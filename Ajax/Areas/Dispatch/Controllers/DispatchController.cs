using Extric.Roadside.API.Models;
using Extric.Roadside.Surveys;
using Extric.Towbook;
using Extric.Towbook.Accounts;
using Extric.Towbook.ActivityLogging;
using Extric.Towbook.API.Controllers;
using Extric.Towbook.API.Models;
using Extric.Towbook.API.Models.Calls;
using Extric.Towbook.Dispatch;
using Extric.Towbook.Dispatch.CallModels;
using Extric.Towbook.Dispatch.QuoteModels;
using Extric.Towbook.Impounds;
using Extric.Towbook.Integration;
using Extric.Towbook.Integrations.MotorClubs.Aaa;
using Extric.Towbook.Integrations.MotorClubs.Nsd;
using Extric.Towbook.Integrations.MotorClubs.Swoop;
using Extric.Towbook.Management.Payments;
using Extric.Towbook.Utility;
using Extric.Towbook.WebShared;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;


namespace Ajax.Areas.Dispatch.Controllers
{
    [Area("Dispatch")]
    public class DispatchController : Controller
    {
        //
        // GET: /Dispatch/Dispatch/

        public ActionResult Index()
        {
            return View();
        }

        public ActionResult View(int id)
        {
            return View();
        }


        public sealed class CheckinStatusModel
        {
            public int Id { get; set; }
            public bool CheckedIn { get; set; }
        }

        public async Task<ActionResult> StatusUpdate(int id)
        {
            var entry = await Entry.GetByIdAsync(id);
            if (entry == null || !(await WebGlobal.CurrentUser.HasAccessToDispatchEntryAsync(entry)))
                throw new TowbookException("The call specified doesn't exist or you don't have access to view it.");

            // Towouts -> consider only second asset
            if (entry.Attributes?.FirstOrDefault(a => a.Key == Extric.Towbook.Dispatch.AttributeValue.BUILTIN_TOWOUT_CALL).Value?.Value == "1")
                entry.Assets = entry.Assets?.Where(w => w == entry.Assets.Last()).ToCollection();

            var cm = await (await CallModel.MapAsync(entry)).FinishMapAsync();

            var statuses = StatusesController.InternalGet().Where(s => s.Id != Status.Cancelled.Id);

            var callStatuses = cm.Statuses.SelectMany(a => statuses.Where(b => b.Id == a)).OrderBy(c => c.DisplayOrder).ToCollection();

            ViewBag.Id = id;
            ViewBag.Statuses = callStatuses;
            ViewBag.StatusesJson = callStatuses.ToJson();
            ViewBag.CallNumber = entry.CallNumber;
            ViewBag.CancelledStatusId = Status.Cancelled.Id;
            ViewBag.CompletedStatusId = Status.Completed.Id;
            ViewBag.Call = await (await CallModel.MapAsync(entry)).FinishMapAsync();
            ViewBag.Message = (await WdnMessageTemplate.GetByTypeIdAsync(1, WebGlobal.CurrentUser.CompanyId)).ParseMessage(entry);
            ViewBag.AssignedDrivers = GetDriversAssignedToEntry(entry).ToJson();
            ViewBag.AssignedTrucks = GetTrucksAssignedToEntry(entry).ToJson();
            ViewBag.AllstateJobType = entry.Attributes?.FirstOrDefault(a => a.Key == Extric.Towbook.Dispatch.AttributeValue.BUILTIN_ALLSTATE_JOB_INFO_PRIMARY_TASK).Value?.Value ?? string.Empty;
            ViewBag.SubcontractorsJson = "[]";
            if (await entry.Company.HasFeatureAsync(Extric.Towbook.Generated.Features.AdvancedBilling_ClosedAccountingPeriod))
            {
                ViewBag.UpdateBlockedByClosedPeriod = Extric.Towbook.Company.Accounting.ClosedPeriodExtensions.IsWithinClosedAccountingPeriod(entry);
            }
            else
            {
                ViewBag.UpdateBlockedByClosedPeriod = false;
            }

            if (await WebGlobal.CurrentUser.Company.HasFeatureAsync(Extric.Towbook.Generated.Features.DispatchToSubcontractors))
            {
                var subs = await Account.GetByCompanyAsync(entry.Company, AccountType.Subcontractor);
                var akvs = AccountKeyValue.GetFirstValueOrNull(entry.CompanyId,
                    entry.AccountId, Provider.Towbook.ProviderId,
                    "DefaultSubcontractorAccountId");

                int defaultSub = 0;
                if (akvs != null)
                    defaultSub = Convert.ToInt32(akvs);
                ViewBag.DefaultSubcontractorAccountId = defaultSub;

                ViewBag.SubcontractorsJson =
                    subs.Select(o => new
                    {
                        o.Id,
                        o.Company
                    }).ToJson();
            }

            decimal latitude = 0;
            decimal longitude = 0;

            var wp = entry.Waypoints.Where(o => o.Title == "Pickup").FirstOrDefault();
            if (wp != null)
            {
                if (wp.Latitude == 0)
                {
                    var f = (await new GeocoderController().Get(wp.Address)).FirstOrDefault();
                    if (f != null)
                    {
                        wp.Latitude = f.Latitude;
                        wp.Longitude = f.Longitude;
                        wp.Save(WebGlobal.CurrentUser.Id);
                    }
                }

                latitude = wp.Latitude;
                longitude = wp.Longitude;
            }

            ViewBag.DTS_HideCompanyDrivers = CompanyKeyValue.GetFirstValueOrNull(entry.CompanyId, Provider.Towbook.ProviderId, "DTS_HideCompanyDrivers") == "1";
            
            try
            {
                ViewBag.NearestDrivers = (await new NearestDriversController().GetAsync(latitude, longitude, id)).ToJson();
            }
            catch
            {
                ViewBag.NearestDrivers = "[]";
            }

            if (await WebGlobal.CurrentUser.Company.HasFeatureAsync(Extric.Towbook.Generated.Features.DriverCheckIn))
            {
                if (await WebGlobal.CurrentUser.Company.HasFeatureAsync(Extric.Towbook.Generated.Features.AutoDispatch))
                {
                    ViewBag.UsersCheckedIn = await getAvailableUsersToDispatchAsync(entry);
                }
                else
                {
                    var users = Extric.Towbook.User.GetByCompanyId(entry.CompanyId);
                    var checkedIn = UserCheckInStatus.GetByUserIds(users.Select(o => o.Id).ToArray());

                    ViewBag.UsersCheckedIn =  users.Select(u => new
                    {
                        Id = u.Id,
                        CheckedIn = checkedIn.Any(o => o.UserId == u.Id && o.IsCheckedIn)                    
                    }).ToJson(); 
                }

                if (string.IsNullOrWhiteSpace(ViewBag.UsersCheckedIn))
                    ViewBag.UsersCheckedIn = "[]";
            }
            else
            {
                ViewBag.UsersCheckedIn = "[]";
            }

            return View(ViewBag.Call);
        }
        
        public async Task<ActionResult> InternalNotes(int id)
        {
            var entry = await Entry.GetByIdAsync(id);
            if (entry == null || !(await WebGlobal.CurrentUser.HasAccessToDispatchEntryAsync(entry)))
                throw new TowbookException("The call specified doesn't exist or you don't have access to view it.");

            ViewBag.Id = id;
            ViewBag.CompanyId = entry.CompanyId;
            ViewBag.CallNumber = entry.CallNumber;
            ViewBag.usersJson = WebGlobal.GetResponseFromUrl($"/api/users");
            ViewBag.IsLockedCall = entry.IsLocked;
            ViewBag.IsAllowedToViewInternalNotes = WebGlobal.CurrentUser.HasPermissionToViewInternalNotes();
            ViewBag.IsAllowedToEditNotes = CallModelExtensions.AccountUserIsAllowedToEdit(WebGlobal.CurrentUser, entry.Status.Id, entry.CompanyId);
            
            var company = CompanyConfigModel.Map(entry.Company);
            if (company.Accounting?.ClosedAccountingPeriod != null && entry.CompletionTime != null)
            {
                ViewBag.EditBlockedByAccountingPeriod = Extric.Towbook.Company.Accounting.ClosedPeriodExtensions.IsWithinClosedAccountingPeriod(entry);
                if (ViewBag.EditBlockedByAccountingPeriod) {
                    ViewBag.IsAllowedToEditNotes = false;
                }
            } else
            {
                ViewBag.EditBlockedByAccountingPeriod = false;
            }

            return View();
        }

        internal class ScheduleItem
        {
            public string DayName { get; set; }
            public TimeSpan Start { get; set; }
            public TimeSpan Stop { get; set; }
        }

        public static async Task<string> getAvailableUsersToDispatchAsync(Entry entry)
        {
            var drivers = (await Driver.GetByExactCompanyIdAsync(entry.CompanyId))
                .Where(o => o.UserId > 0 && o.IsActive() && o.Deleted == false).ToList();

            var keys = DriverKeyValue.GetByDriver(entry.CompanyId,
                drivers.Select(o => o.Id),
                Provider.Towbook.ProviderId,
                new string[] {
                    "Rating",
                    "BlockedZipCodesJson",
                    "ScheduleForceOff",
                    "ScheduleAlwaysOn",
                    "ScheduleJson"
                });

            var locations = UserLocationHistoryItem.GetCurrentByUserIds(
                drivers.Select(o => o.UserId).ToArray(),
                DateTime.Now.AddMinutes(-480), DateTime.Now).ToArray();

            var driverKeys = DriverKey.GetAll().Where(o => o.ProviderId == Provider.Towbook.ProviderId);

            var keyRating = driverKeys.Where(o => o.Name == "Rating").First().Id;
            var keyBlockedZipCodesJson = driverKeys.Where(o => o.Name == "BlockedZipCodesJson").First().Id;
            var keyScheduleForceOff = driverKeys.Where(o => o.Name == "ScheduleForceOff").First().Id;
            var keyScheduleAlwaysOn = driverKeys.Where(o => o.Name == "ScheduleAlwaysOn").First().Id;
            var keyScheduleJson = driverKeys.Where(o => o.Name == "ScheduleJson").First().Id;

            var today = DateTime.Now.DayOfWeek.ToString();

            if (drivers.Count > 1 && false)
                drivers = drivers.Where(d => locations.Where(l => l.UserId == d.UserId).Any()).ToList();

            List<Driver> drivers2 = new List<Driver>();
            foreach (var driver in drivers)
            {
                // step 1: ignore drivers that are forced off
                var forceOff = keys.Where(o => o.DriverId == driver.Id && o.KeyId == keyScheduleForceOff).FirstOrDefault()?.Value;
                if (forceOff == "1")
                {
                    continue;
                }

                // step 2: make sure the call isn't in a zip that the driver won't work in
                var blockedZips = keys.Where(o => o.DriverId == driver.Id && o.KeyId == keyBlockedZipCodesJson).FirstOrDefault()?.Value;
                if (blockedZips != null)
                {
                    // only 5 digit zip codes are valid.
                    var blockedZipList = blockedZips.Replace(",", " ")
                        .Replace(";", "")
                        .Replace("\n", " ")
                        .Replace("\r", " ")
                        .Split(' ')
                        .Where(o => o.Length == 5)
                        .ToCollection();

                    var blocked = false;

                    foreach (var zip in blockedZipList)
                    {
                        if (entry.TowSource.Contains(zip))
                        {
                            blocked = true;
                            break;
                        }
                    }

                    if (blocked)
                    {
                        continue;
                    }
                }

                // step 3: check if they are always on
                var alwaysOn = keys.Where(o => o.DriverId == driver.Id && o.KeyId == keyScheduleAlwaysOn).FirstOrDefault()?.Value;

                if (alwaysOn != "1")
                {
                    var schedule = keys.Where(o => o.DriverId == driver.Id && o.KeyId == keyScheduleJson).FirstOrDefault()?.Value;
                    if (schedule != null)
                    {
                        var sched = JsonConvert.DeserializeObject<IEnumerable<ScheduleItem>>(schedule).Where(o => 
                            string.Equals(o.DayName, today, StringComparison.InvariantCultureIgnoreCase));

                        var foundMatch = false;

                        var timeofday = DateTime.Now.TimeOfDay;
                        foreach (var slot in sched)
                        {
                            // datetime.now.timeofday is eastern
                            // slot.start/slot.stop are user local time, they need to be converted from pacific to eastern, for example. 

                            // handle midnight to midnight
                            if (slot.Start == slot.Stop && slot.Start.Ticks == 0)
                            {
                                foundMatch = true;
                                break;
                            }

                            // slot.start / slot.stop needs to be offset. User is entering it in 'their time' 
                            slot.Start = slot.Start.Add(TimeSpan.FromMinutes(-(long)entry.Company.TimezoneOffset * 60));
                            slot.Stop = slot.Stop.Add(TimeSpan.FromMinutes(-(long)entry.Company.TimezoneOffset * 60));


                            if ((timeofday >= slot.Start && ((slot.Stop.Ticks == 0 || timeofday < slot.Stop || slot.Stop < slot.Start))) ||  // handle stopping at 12:00am which is 0 ticks.
                                ((slot.Stop < slot.Start) ? timeofday < slot.Stop : false) // handle crossover (7pm-7am)
                              )
                            {
                                foundMatch = true;
                                break;
                            }
                        }

                        if (!foundMatch)
                        {
                            //       Console.WriteLine("Removing " + driverName + " because they are not scheduled to work.");
                            continue;
                        }
                    }
                }

                drivers2.Add(driver);
            }

            return drivers2.Select(o => new CheckinStatusModel
            {
                Id = o.UserId,
                CheckedIn = true
            }).ToJson();
        }

        public async Task<ActionResult> StatusUpdateMap(int id)
        {
            var entry = await Entry.GetByIdAsync(id);
            if (entry == null || !(await WebGlobal.CurrentUser.HasAccessToDispatchEntryAsync(entry)))
                throw new TowbookException("The call specified doesn't exist or you don't have access to view it.");
            var cm = await (await CallModel.MapAsync(entry)).FinishMapAsync();

            var statuses = StatusesController.InternalGet().Where(s => s.Id != Status.Cancelled.Id);

            var callStatuses = cm.Statuses
                                    .Where(w => statuses.FirstOrDefault(f => f.Id == w) != null)
                                    .Select(o => statuses.First(s => s.Id == o))
                                    .OrderBy(s => s.DisplayOrder)
                                    .ToCollection();

            ViewBag.Id = id;
            ViewBag.Statuses = callStatuses;
            ViewBag.StatusesJson = callStatuses.ToJson();
            ViewBag.CallNumber = entry.CallNumber;
            ViewBag.CancelledStatusId = Status.Cancelled.Id;
            ViewBag.CompletedStatusId = Status.Completed.Id;
            ViewBag.Call = cm;
            ViewBag.Message = (await WdnMessageTemplate.GetByTypeIdAsync(1, WebGlobal.CurrentUser.CompanyId)).ParseMessage(entry);
            ViewBag.AssignedDrivers = GetDriversAssignedToEntry(entry).ToJson();
            ViewBag.AssignedTrucks = GetTrucksAssignedToEntry(entry).ToJson();

            decimal latitude = 0;
            decimal longitude = 0;

            var wp = entry.Waypoints.Where(o => o.Title == "Pickup").FirstOrDefault();
            if (wp != null)
            {
                if (wp.Latitude == 0)
                {
                    var f = (await new GeocoderController().Get(wp.Address)).FirstOrDefault();
                    if (f != null)
                    {
                        wp.Latitude = f.Latitude;
                        wp.Longitude = f.Longitude;
                        wp.Save(WebGlobal.CurrentUser.Id);
                    }
                }

                latitude = wp.Latitude;
                longitude = wp.Longitude;
            }

            try
            {
                ViewBag.NearestDrivers = (await new NearestDriversController().GetAsync(latitude, longitude, id)).ToJson();
            }
            catch
            {
                ViewBag.NearestDrivers = "[]";
            }

            if (await WebGlobal.CurrentUser.Company.HasFeatureAsync(Extric.Towbook.Generated.Features.DriverCheckIn))
            {
                var users = Extric.Towbook.User.GetByCompanyId(entry.CompanyId);
                var checkedIn = UserCheckInStatus.GetByUserIds(users.Select(o => o.Id).ToArray());

                ViewBag.UsersCheckedIn = users.Select(u => new
                {
                    Id = u.Id,
                    CheckedIn = checkedIn.Any(o => o.UserId == u.Id && o.IsCheckedIn)
                }).ToJson();
            }
            else
            {
                ViewBag.UsersCheckedIn = "[]";
            }

            return View(ViewBag.Call);
        }

        public async Task<ActionResult> Cancel(int id)
        {
            var entry = await Entry.GetByIdAsync(id);

            if (entry == null || !(await WebGlobal.CurrentUser.HasAccessToDispatchEntryAsync(entry)))
                throw new TowbookException("The call specified doesn't exist or you don't have access to view it.");

            ViewBag.Id = id;
            ViewBag.CallNumber = entry.CallNumber;
            ViewBag.Call = entry;

            if (entry.Account?.MasterAccountId == MasterAccountTypes.Nsd)
            {
                var cr = await CallRequest.GetByDispatchEntryId(entry.Id);
                if (cr != null)
                {
                    var nsd = NsdDispatch.GetByCallRequestId(cr.CallRequestId);
                    if (nsd != null)
                    {
                        // new nsd integration, force digital cancel.
                        return await DigitalCancel(id);
                    }
                }
            }

            if (entry.Account?.MasterAccountId == MasterAccountTypes.Swoop)
            {
                var cr = await CallRequest.GetByDispatchEntryId(entry.Id);
                if (cr != null)
                {
                    var swp = SwoopDispatch.GetByCallRequestId(cr.CallRequestId);
                    if (swp != null)
                    {
                        return await DigitalCancel(id);
                    }
                }
            }

            if (entry.Account?.MasterAccountId == MasterAccountTypes.AaaWashington ||
                entry.Account?.MasterAccountId == MasterAccountTypes.AaaNewYork ||
                entry.Account?.MasterAccountId == MasterAccountTypes.AaaAcg ||
                entry.Account?.MasterAccountId == MasterAccountTypes.AaaAce ||
                entry.Account?.MasterAccountId == MasterAccountTypes.AaaNortheast ||
                entry.Account?.MasterAccountId == MasterAccountTypes.AaaAca ||
                entry.Account?.MasterAccountId == MasterAccountTypes.AaaNationalFsl ||
                entry.Account?.MasterAccountId == MasterAccountTypes.Bcaa)
            {
                var cr = await CallRequest.GetByDispatchEntryId(entry.Id);
                if (cr != null)
                {
                    var aa = AaaDispatch.GetByCallRequestId(cr.CallRequestId);
                    if (aa != null)
                    {
                        return await DigitalCancel(id);
                    }
                }
            }


            return View();
        }

        public static int[] GetTrucksAssignedToEntry(Entry entry)
        {
            if (entry == null)
                return Array.Empty<int>();

            var trucks = new List<int>();
            
            var isTowout = entry.Attributes?.FirstOrDefault(f => f.Key == Extric.Towbook.Dispatch.AttributeValue.BUILTIN_TOWOUT_CALL).Value?.Value == "1";

            if (!isTowout && entry.TruckId > 0)
            {
                if (!trucks.Contains(entry.TruckId))
                    trucks.Add(entry.TruckId);
            }

            if (entry.Assets != null)
            {
                foreach (var x in entry.Assets)
                {
                    if (x.Drivers != null)
                    {
                        foreach (var dt in x.Drivers)
                        {
                            if (dt.TruckId != null &&
                                !trucks.Contains(dt.TruckId.Value))
                            {
                                trucks.Add(dt.TruckId.Value);
                            }
                        }
                    }
                }
            }

            return trucks.ToArray();
        }

        public static IEnumerable<int> GetDriversAssignedToEntry(Entry entry)
        {
            if (entry == null)
                return Array.Empty<int>();

            var drivers = new Collection<int>();

            var isTowOut = entry.GetAttribute(Extric.Towbook.Dispatch.AttributeValue.BUILTIN_TOWOUT_CALL) == "1";

            if (!isTowOut && entry.DriverId > 0)
            {
                if (!drivers.Contains(entry.DriverId))
                    drivers.Add(entry.DriverId);
            }

            if (entry.Assets != null)
            {
                foreach (var x in entry.Assets)
                {
                    if (x.Drivers == null)
                        continue;

                    foreach (var dt in x.Drivers)
                    {
                        if (dt.DriverId != null &&
                            !drivers.Contains(dt.DriverId.Value))
                        {
                            drivers.Add(dt.DriverId.Value);
                        }
                    }
                }
            }

            return drivers.ToArray();
        }

        /// <summary>
        /// Get page to delete a call.
        /// </summary>
        /// <param name="id">Dispatch Entry Id</param>
        /// <returns></returns>
        /// <exception cref="TowbookException"></exception>
        public async Task<ActionResult> Delete(int id)
        {
            var entry = await Entry.GetByIdAsync(id);

            if (entry == null || !(await WebGlobal.CurrentUser.HasAccessToCompanyAsync(entry.CompanyId)))
                throw new TowbookException("The call specified doesn't exist or you don't have access to view it.");

            ViewBag.Id = id;
            ViewBag.CallNumber = entry.CallNumber;
            ViewBag.Call = entry;
            var company = CompanyConfigModel.Map(entry.Company);
            if (company.Accounting?.ClosedAccountingPeriod != null && entry.CompletionTime != null)
            {
                ViewBag.DeleteBlockedByAccountingPeriod = Extric.Towbook.Company.Accounting.ClosedPeriodExtensions.IsWithinClosedAccountingPeriod(entry);
            } else
            {
                ViewBag.DeleteBlockedByAccountingPeriod = false;
            }

            if (Request.Query.ContainsKey("title"))
                ViewBag.CustomTitle = Request.Query["title"];

            return View();
        }

        /// <summary>
        /// Displays the Complete Call view for the specified call.
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ActionResult> CompleteAsync(int id)
        {
            var entry = await Entry.GetByIdAsync(id);

            if (entry == null || !(await WebGlobal.CurrentUser.HasAccessToDispatchEntryAsync(entry)))
                throw new TowbookException("The call specified doesn't exist or you don't have access to view it.");

            // Towouts -> consider only second asset
            if (entry.Attributes?.FirstOrDefault(a => a.Key == Extric.Towbook.Dispatch.AttributeValue.BUILTIN_TOWOUT_CALL).Value?.Value == "1")
                entry.Assets = entry.Assets?.Where(w => w == entry.Assets.Last()).ToCollection();

            StatusModel[] statusesList = StatusesController.InternalGet();
            List<Status> statuses = new List<Status>();
            DateTime completionTime = WebGlobal.OffsetDateTime(DateTime.Now);

            String[,] statusesHistory = new String[7, 4] {
                { "create", "Call Received", String.Format("{0:d}", WebGlobal.OffsetDateTime(entry.CreateDate)), String.Format("{0:t}", WebGlobal.OffsetDateTime(entry.CreateDate)) },
                { "dispatch", Status.Dispatched.Name, String.Format("{0:d}", WebGlobal.OffsetDateTime(entry.DispatchTime)), String.Format("{0:t}", WebGlobal.OffsetDateTime(entry.DispatchTime)) },
                { "enroute", Status.EnRoute.Name, String.Format("{0:d}", WebGlobal.OffsetDateTime(entry.EnrouteTime)), String.Format("{0:t}", WebGlobal.OffsetDateTime(entry.EnrouteTime)) },
                { "atSite", Status.AtSite.Name, String.Format("{0:d}", WebGlobal.OffsetDateTime(entry.ArrivalTime)), String.Format("{0:t}", WebGlobal.OffsetDateTime(entry.ArrivalTime)) },
                { "tow", Status.BeingTowed.Name, String.Format("{0:d}", WebGlobal.OffsetDateTime(entry.TowTime)), String.Format("{0:t}", WebGlobal.OffsetDateTime(entry.TowTime)) },
                { "destinationArrival", Status.DestinationArrival.Name, String.Format("{0:d}", WebGlobal.OffsetDateTime(entry.DestinationArrivalTime)), String.Format("{0:t}", WebGlobal.OffsetDateTime(entry.DestinationArrivalTime)) },
                { "complete", Status.Completed.Name, String.Format("{0:d}", completionTime), String.Format("{0:t}", completionTime) },
            };

            var callModel = await (await CallModel.MapAsync(entry)).FinishMapAsync();

            ViewBag.statusesHistory = statusesHistory;
            ViewBag.Statuses = statuses;
            ViewBag.Id = id;
            ViewBag.CallNumber = entry.CallNumber;
            ViewBag.CancelledStatusId = Status.Cancelled.Id;
            ViewBag.Call = callModel;
            ViewBag.userIsDriver = WebGlobal.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Driver ? 1 : 0;
            ViewBag.CompletedStatusId = Status.Completed.Id;
            ViewBag.AssignedDrivers = GetDriversAssignedToEntry(entry).ToJson();
            ViewBag.HasDestinationArrivalStatus = CompanyKeyValue.GetFirstValueOrNull(entry.CompanyId, Provider.Towbook.ProviderId, "EnableDestinationArrivalStatus") == "1";
            ViewBag.ClosedPeriodDate = "";
            if (await entry.Company.HasFeatureAsync(Extric.Towbook.Generated.Features.AdvancedBilling_ClosedAccountingPeriod))
            {
                var settings = await Extric.Towbook.Company.Accounting.ClosedPeriodOption.GetByCompanyIdAsync(callModel.CompanyId);
                if (settings != null && settings.Enabled && settings.ClosedDate != null)
                {
                    ViewBag.ClosedPeriodDate = settings.ClosedDate.ToString();
                }
            }

            if (new int[] { (int)Extric.Towbook.User.TypeEnum.Accountant, (int)Extric.Towbook.User.TypeEnum.Dispatcher, (int)Extric.Towbook.User.TypeEnum.Manager }.Contains((int)Extric.Towbook.WebShared.WebGlobal.CurrentUser.Type))
                ViewBag.CallNotes = callModel.Notes;
            else
                ViewBag.CallNotes = string.Empty;

            ViewBag.BillingNotes = callModel?.Attributes.FirstOrDefault(f => f.AttributeId == 47)?.Value ?? "";

            var xdrivers = new List<int>();

            foreach (var a in entry.Assets)
                xdrivers.AddRange(a.Drivers.Where(o => o.DriverId != null).Select<dynamic, int>(o => o.DriverId));

            ViewBag.HideDriverTruck = (xdrivers.Distinct().ToList().Count > 1);

            if (await WebGlobal.CurrentUser.Company.HasFeatureAsync(Extric.Towbook.Generated.Features.DispatchToSubcontractors))
                ViewBag.HideDriverTruck = true;

            ViewBag.CallCompleteValidationJson = (await new CallValidationController().GetAsync(id, Status.Completed.Id)).ToJson();

            ViewBag.KeysLocation = entry.Assets.FirstOrDefault()?.KeysLocation;

            return View();
        }

        public ActionResult SendMessage()
        {
            var drivers = new List<DriverMessageRecipient>();

            foreach (Driver d in Driver.GetByCompany(WebGlobal.CurrentUser.Company)
                .Where(o => o.IsActive()))
            {
                string type = "Unknown";

                if (!d.IsActive())
                    continue;

                if (d.DispatchingNotificationType != null && d.DispatchingNotificationType != DriverDispatchingNotificationType.None &&
                    !string.IsNullOrWhiteSpace(d.DispatchingNotificationValue))
                {
                    switch (d.DispatchingNotificationType.Value)
                    {
                        case DriverDispatchingNotificationType.SnppSprint: type = "Sprint Text Message"; break;
                        case DriverDispatchingNotificationType.SnppNextel: type = "Nextel Text Message"; break;
                        case DriverDispatchingNotificationType.Email: type = "Email"; break;
                        case DriverDispatchingNotificationType.NativeSmsOneWay: type = "SMS Text Message"; break;
                        default: type = d.DispatchingNotificationType.Value.ToString(); break;
                    }

                    drivers.Add(new DriverMessageRecipient() { DriverId = d.Id, Name = d.Name, Type = type });
                }
                else if (d.UserId > 0)
                {
                    var u = Extric.Towbook.User.GetById(d.UserId);

                    if (u == null)
                        continue;

                    var keys = u.GetKeys();

                    if (keys.Any(o => o.Key == "registration"))
                        type = "Towbook Mobile App";
                    else
                        continue;

                    drivers.Add(new DriverMessageRecipient()
                    {
                        DriverId = d.Id,
                        Name = d.Name,
                        Type = type
                    });
                }
            }

            ViewBag.Drivers = drivers.ToJson();

            return View();
        }


        public class DriverMessageRecipient
        {
            public int DriverId { get; set; }
            public string Name { get; set; }
            public string Type { get; set; }
        }

        /// <summary>
        /// Displays the Receipt view for the specified call to allow the user to view or print the receipt.
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ActionResult> Receipt(int id)
        {
            var entry = await Entry.GetByIdAsync(id);
            var impound = Impound.GetByDispatchEntry(entry);

            if (entry == null || !(await WebGlobal.CurrentUser.HasAccessToDispatchEntryAsync(entry)))
                throw new TowbookException("The call specified doesn't exist or you don't have access to view it.");

            ViewBag.Id = id;
            ViewBag.CallNumber = entry.CallNumber;
            ViewBag.Call = entry;
            ViewBag.InvoiceTotal = entry.InvoiceTotal.ToString("C");
            ViewBag.InvoiceNumber = entry.InvoiceNumber;
            ViewBag.Completed = entry.Status == Entry.EntryStatus.Completed ? 1 : 0;
            ViewBag.Impound = entry.Impound == true ? 1 : 0;
            ViewBag.ImpoundId = impound?.Id;
            ViewBag.ImpoundLotAccountId = impound?.Lot?.AccountId;
            ViewBag.BlockCharges = await (await CallModel.MapAsync(entry)).ShouldBlockChargesAsync();
            ViewBag.HideCharges = false;
            ViewBag.HideDiscounts = false;
            ViewBag.HasPhotos = (await Extric.Towbook.Dispatch.Photo.GetByDispatchEntryIdAsync(id)).Count != 0;
            ViewBag.HidePhotos = Request.Query["hidePhotos"] == "1";
            ViewBag.PrintCall = Request.Query["printcall"] == "1";
            ViewBag.AllowedToEmail = WebGlobal.CurrentUser.HasPermissionToEmailCall(entry);

            if (CompanyKeyValue.GetFirstValueOrNull(entry.CompanyId, Provider.Towbook.ProviderId, "Towbook_Calls_NeverShowPhotoLink") == "1")
                ViewBag.HidePhotos = true;

            var defaultValue = (entry.Account?.Type == AccountType.MotorClub ? "1" : "0");

            // only apply these settings if they weren't overridden in the query string. 
            if (entry.Account?.Type == AccountType.MotorClub) {
                if ((CompanyKeyValue.GetFirstValueOrNull(WebGlobal.CurrentUser.CompanyId,
                    Provider.Towbook.ProviderId, "HideChargesFromMotorClubInvoicesByDefault") ?? defaultValue) == "1")
                    ViewBag.HideCharges = true;
            }

            var hideCharges = AccountKeyValue.GetByAccount(entry.CompanyId, entry.AccountId, Provider.Towbook.ProviderId, "AlwaysHideCharges").FirstOrDefault()?.Value;
            if (hideCharges == "1")
                ViewBag.HideCharges = true;

            var hideDiscounts = AccountKeyValue.GetByAccount(entry.CompanyId, entry.AccountId, Provider.Towbook.ProviderId, "AlwaysHideDiscounts").FirstOrDefault()?.Value;
            if (hideDiscounts == "1")
                ViewBag.HideDiscounts = true;

            var hidePhotos = AccountKeyValue.GetByAccount(entry.CompanyId, entry.AccountId, Provider.Towbook.ProviderId, "AlwaysHidePhotos").FirstOrDefault()?.Value;
            if (hidePhotos == "1")
                ViewBag.HidePhotos = true;


            #region Regulated Charges for MA + impounds
            ViewBag.ShowRegulatedLink = false;
            if (entry.Impound && entry.Company.State.Equals("MA"))
            {
                var regulatedItems = entry.Invoice.GetRegulatedCharges();

                if (regulatedItems?.Count() > 0)
                {
                    ViewBag.ShowRegulatedLink = true;
                }

            }
            #endregion

            return View();
        }

        /// <summary>
        /// Displays the result of a push to the accounting sofware.
        /// </summary>
        /// <param name="id">The dispatch entry id</param>
        /// <returns></returns>
        public async Task<ActionResult> PushToAccountingApp(int id)
        {
            var entry = await Entry.GetByIdAsync(id);

            if (entry == null || !(await WebGlobal.CurrentUser.HasAccessToDispatchEntryAsync(entry)))
                throw new TowbookException("The call specified doesn't exist or you don't have access to view it.");

            if (!(await entry.Company.IsConnectedToAccountingProviderAsync()))
            {
                ViewBag.ErrorMessage = "Your company isn't connected to QuickBooks.";
                return View();
            }


            ViewBag.Id = id;

            int statusCode = 0;

            string baseUrl = "/api/calls/";

            var ag = await Extric.Towbook.Agent.Session.GetByCompanyIdAsync(entry.CompanyId);

            if (ag == null)
            {
                string response = WebGlobal.GetResponseFromUrl(baseUrl + String.Format("{0}/pushToAccountingAppTest", id),
                    out statusCode,
                    true,
                    false);

                if (statusCode != 200)
                {
                    var oj = JsonConvert.DeserializeObject<dynamic>(response);

                    if (oj.exceptionType == "Extric.Towbook.Integrations.Quickbooks.MisconfiguredException")
                    {
                        ViewBag.ErrorMessage = oj.exceptionMessage;
                    }
                    else
                    {
                        ViewBag.ErrorMessage = response;
                    }
                }
            }
            else
            {
                // user is using the towbook agent
                var ags = Extric.Towbook.Agent.Sync.QuickbooksSyncService.Get(ag.Id);
                try
                {
                    var ticketId = await ags.Send(entry);
                    ViewBag.AgentReferenceId = ticketId;

                    ViewBag.Title = "Sent to Towbook Agent...";
                    ViewBag.Description = "If the Towbook Agent is currently running, the invoice will process in the next few seconds. If not, it will be processed the next time the Agent opens.";
                }
                catch (TowbookException te)
                {
                    ViewBag.ErrorMessage = te.Message;

                }

                return View();
            }

            return View();
        }

        public async Task<ActionResult> EmailQuote(Guid id)
        {
            QuoteModel quote = await QuoteModel.GetById(id);
            if (quote == null || !WebGlobal.CurrentUser.HasAccessToCompany(quote.CompanyId))
                throw new Exception("You don't have permission to access the quote or the quote doesn't exist.");

            var entry = new Entry();
            entry.CreateDate = quote.CreateDate;
            entry.Type = Entry.EntryType.Quote;
            entry = await CallModelExtensions.Map(quote.Call, entry);

            foreach (var asset in quote.Call.Assets)
                entry.Assets.Add(await CallAssetModelExtensions.TranslateAsync(asset));

            ViewBag.Id = id;
            ViewBag.CallNumber = entry.CallNumber;
            ViewBag.Call = entry;
            ViewBag.Asset = entry.Assets.FirstOrDefault();
            ViewBag.Impound = 0;
            ViewBag.ImpoundId = 0;
            ViewBag.ImpoundLotAccountId = 0;
            ViewBag.CustomTitle = Request.Query["title"].ToString();
            ViewBag.SuggestedContactsJson = "[]";
            ViewBag.BlockCharges = false;
            ViewBag.HideCharges = false;
            ViewBag.HideDiscounts = false;
            ViewBag.HasPhotos = true;
            ViewBag.HidePhotos = true;
            ViewBag.PrintCall = false;
            ViewBag.usersJson = WebGlobal.GetResponseFromUrl($"/api/users");
            ViewBag.statusesJson = WebGlobal.GetResponseFromUrl($"/api/statuses");
            ViewBag.DamageForms = "[]";
            ViewBag.IncludePaymentLink = Request.Query["includePaymentLink"] == "1";


            ViewBag.Quote = quote;

            return View("Email", quote.Call);
        }

        /// <summary>
        /// Displays the Email view for the specified call, to allow the user to send a copy of the calls invoice to the specified email address(s).
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<IActionResult> Email(int id)
        {
            var entry = await Entry.GetByIdAsync(id);
            
            if (entry == null || !(await WebGlobal.CurrentUser.HasAccessToDispatchEntryAsync(entry)))
                throw new TowbookException("The call specified doesn't exist or you don't have access to view it.");

            var impound = Impound.GetByDispatchEntry(entry);

            ViewBag.Id = id;
            ViewBag.CallNumber = entry.CallNumber;
            ViewBag.Call = entry;
            ViewBag.Asset = entry.Assets.FirstOrDefault();
            ViewBag.Impound = entry.Impound == true ? 1 : 0;
            ViewBag.ImpoundId = impound != null ? (int?)impound.Id : 0;
            ViewBag.ImpoundLotAccountId = impound != null && impound.Lot != null ? impound.Lot.AccountId : 0;
            ViewBag.CustomTitle = Request.Query["title"];
            ViewBag.SuggestedContactsJson = WebGlobal.GetResponseFromUrl($"/api/calls/" + entry.Id  +"/suggestedEmails");
            ViewBag.BlockCharges = await (await CallModel.MapAsync(entry)).ShouldBlockChargesAsync();
            ViewBag.HideCharges = Request.Query["showPrices"] == "0";
            ViewBag.HideDiscounts = Request.Query["hideDiscounts"] == "1";
            ViewBag.HasPhotos = (await Extric.Towbook.Dispatch.Photo.GetByDispatchEntryIdAsync(id)).Any();
            ViewBag.HidePhotos = Request.Query["hidePhotos"] == "1";
            ViewBag.PrintCall = Request.Query["printcall"] == "1";
            ViewBag.HideLineItems = Request.Query["hideLineItems"] == "1";
            ViewBag.usersJson = WebGlobal.GetResponseFromUrl($"/api/users");
            ViewBag.statusesJson = WebGlobal.GetResponseFromUrl($"/api/statuses");
            ViewBag.DamageForms = WebGlobal.GetResponseFromUrl($"/api/calls/" + entry.Id + "/damageforms");
            ViewBag.IncludePaymentLink = Request.Query["includePaymentLink"] == "1" ? true : (bool?)null;

            ViewBag.DefaultSubject = string.Empty;
            ViewBag.DefaultMessageBody = string.Empty;

            var company = (await WebGlobal.GetCompaniesAsync()).FirstOrDefault(w => w.Id == entry.CompanyId);
            if (company != null && await company.HasFeatureAsync(Extric.Towbook.Generated.Features.AdvancedBilling))
            {
                var ieo = await InvoiceEmailOption.GetByCompanyIdAsync(company.Id, entry.AccountId);
                if (ieo != null)
                {
                    ViewBag.DefaultSubject = ieo.Subject;
                    ViewBag.DefaultMessageBody = ieo.Message;
                }
            }


            if (CompanyKeyValue.GetFirstValueOrNull(entry.CompanyId, Provider.Towbook.ProviderId, "Towbook_Calls_NeverShowPhotoLink") == "1")
                ViewBag.HidePhotos = true;

            var defaultValue = (entry.Account?.Type == AccountType.MotorClub ? "1" : "0");

            // only apply these settings if they weren't overridden in the query string. 
            if (entry.Account?.Type == AccountType.MotorClub)
            {
                if ((CompanyKeyValue.GetFirstValueOrNull(WebGlobal.CurrentUser.CompanyId,
                Provider.Towbook.ProviderId, "HideChargesFromMotorClubInvoicesByDefault") ?? defaultValue) == "1")
                    ViewBag.HideCharges = true;
            }

            var hideCharges = AccountKeyValue.GetByAccount(entry.CompanyId, entry.AccountId, Provider.Towbook.ProviderId, "AlwaysHideCharges").FirstOrDefault()?.Value;
            if (hideCharges == "1")
                ViewBag.HideCharges = true;

            var hideDiscounts = AccountKeyValue.GetByAccount(entry.CompanyId, entry.AccountId, Provider.Towbook.ProviderId, "AlwaysHideDiscounts").FirstOrDefault()?.Value;
            if (hideDiscounts == "1")
                ViewBag.HideDiscounts = true;

            var hidePhotos = AccountKeyValue.GetByAccount(entry.CompanyId, entry.AccountId, Provider.Towbook.ProviderId, "AlwaysHidePhotos").FirstOrDefault()?.Value;
            if (hidePhotos == "1")
                ViewBag.HidePhotos = true;

            
            var sqConn = await Extric.Towbook.API.Integration.Square.SquareUtils.GetAuthorizationAsync(entry.CompanyId);
            if (sqConn != null)
            {
                var includePaymentLink = AccountKeyValue.GetFirstValueOrNull(entry.CompanyId, entry.AccountId, Provider.Towbook.ProviderId, "Square_AlwaysIncludePaymentLinkOnInvoices") ??
                                            CompanyKeyValue.GetFirstValueOrNull(entry.CompanyId, Provider.Towbook.ProviderId, "Square_AlwaysIncludePaymentLinkOnInvoices");

                if (includePaymentLink == "1")
                    ViewBag.IncludePaymentLinkAsChecked = true;
                else
                    ViewBag.IncludePaymentLinkAsChecked = false;

            }
            else
                ViewBag.IncludePaymentLinkAsChecked = (bool?)null;


            return View();
        }

        /// <summary>
        /// Displays the Email view for the specified call, to allow the user to send a copy of the calls invoice to the specified email address(s).
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<IActionResult> Fax(int id)
        {
            var entry = await Entry.GetByIdAsync(id);

            if (entry == null || !(await WebGlobal.CurrentUser.HasAccessToDispatchEntryAsync(entry)))
                throw new TowbookException("The call specified doesn't exist or you don't have access to view it.");

            var impound = Impound.GetByDispatchEntry(entry);

            ViewBag.Id = id;
            ViewBag.CallNumber = entry.CallNumber;
            ViewBag.Call = entry;
            ViewBag.Impound = entry.Impound == true ? 1 : 0;
            ViewBag.ImpoundId = impound != null ? (int?)impound.Id : 0;
            ViewBag.ImpoundLotAccountId = impound != null && impound.Lot != null ? impound.Lot.AccountId : 0;

            ViewBag.SuggestedContactsJson = (await (new CallsController()).SuggestedFaxesAsync(entry.Id)).ToJson();

            return View();
        }

        public class SuggestedContact
        {
            public string Type { get; set; }
            public string Name { get; set; }
            public string Address { get; set; }
        }

        /// <summary>
        /// Displays the Photo view to allow the user to view photos for the call and upload new ones.
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<IActionResult> Photos(int id)
        {
            NewRelic.Api.Agent.NewRelic.DisableBrowserMonitoring();

            var entry = await Entry.GetByIdAsync(id);

            if (entry == null || !(await WebGlobal.CurrentUser.HasAccessToDispatchEntryAsync(entry)))
                throw new TowbookException("The call specified doesn't exist or you don't have access to view it.");

            ViewBag.PreventPhotoSharing = false;
            if (entry.Account?.MasterAccountId == MasterAccountTypes.Agero ||
                entry.Account?.MasterAccountId == MasterAccountTypes.Urgently ||
                entry.Account?.MasterAccountId == MasterAccountTypes.Allstate ||
                entry.Account?.MasterAccountId == MasterAccountTypes.Quest ||
                entry.Account?.MasterAccountId == MasterAccountTypes.Fleetnet)
            {
                var cr = await CallRequest.GetByDispatchEntryId(entry.AccountId);

                if (cr == null ||
                    entry.CreateDate < Convert.ToDateTime("9/1/2020") || 
                    CompanyKeyValue.GetFirstValueOrNull(entry.CompanyId, Provider.Towbook.ProviderId, "PreventPhotoSharing") == "1" ||
                    AccountKeyValue.GetFirstValueOrNull(entry.CompanyId, entry.Account.Id, Provider.Towbook.ProviderId, "PreventPhotoSharing") == "1")
                    ViewBag.PreventPhotoSharing = true;
            }

            ViewBag.Id = id;
            ViewBag.CallNumber = entry.CallNumber;
            ViewBag.Call = await (await CallModel.MapAsync(entry)).FinishMapAsync();
            ViewBag.Users = (await new UsersController().Get()).Select(u =>
            {
                u.Name = Core.HtmlEncode(u.Name);
                return u;
            }).ToJson(); 
            ViewBag.MasterAccountId = entry.Account?.MasterAccountId ?? 0;
            ViewBag.StickerNumber = entry.Attributes.Any(f => f.Key == Extric.Towbook.Dispatch.AttributeValue.BUILTIN_DISPATCH_STICKER_NUMBER) ? 
                entry.Attributes[Extric.Towbook.Dispatch.AttributeValue.BUILTIN_DISPATCH_STICKER_NUMBER].Value : string.Empty;

            return View();
        }

        /// <summary>
        /// Displays the File view to allow the user to view Files for the call and upload new ones.
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<IActionResult> Files(int id)
        {
            var entry = await Entry.GetByIdAsync(id);

            if (entry == null || !(await WebGlobal.CurrentUser.HasAccessToDispatchEntryAsync(entry)))
                throw new TowbookException("The call specified doesn't exist or you don't have access to view it.");

            ViewBag.Id = id;
            ViewBag.CallNumber = entry.CallNumber;
            ViewBag.Call = entry;
            ViewBag.Users = (await new UsersController().Get()).ToJson();

            return View();
        }

        /// <summary>
        /// Displays the Activity History view for the call, showing a complete history of actions/events taken place for the call.
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<IActionResult> History(int id)
        {
            var entry = await Entry.GetByIdAsync(id);

            if (entry == null || !(await WebGlobal.CurrentUser.HasAccessToDispatchEntryAsync(entry)))
                throw new TowbookException("The call specified doesn't exist or you don't have access to view it.");

            if (WebGlobal.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Driver)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            ViewBag.Id = id;
            ViewBag.CallNumber = entry.CallNumber;
            ViewBag.Id = entry.Id;
            ViewBag.Call = entry;
            ViewBag.CompanyOffset = WebGlobal.CurrentUser.Company.TimezoneOffset;

            ViewBag.Config = "{}";

            if (WebGlobal.CurrentUser.Type == Extric.Towbook.User.TypeEnum.SystemAdministrator)
            {
                ViewBag.Config = Core.GetRedisValue(entry.CompanyId + "_1_config[" + entry.CompanyId + "]");
                if (ViewBag.Config == null)
                    ViewBag.Config = "{}";
            }

            Collection<User> users = new Collection<User>();
            Collection<Driver> drivers = new Collection<Driver>();

            foreach (var c in (await WebGlobal.GetCompaniesAsync()))
            {
                var cUsers = Extric.Towbook.User.GetByCompanyId(c.Id);
                foreach (var u in cUsers)
                {
                    if (!users.Select(a => a.Id).Contains(u.Id))
                        users.Add(u);
                }

                var cDrivers = (await Driver.GetByExactCompanyIdAsync(c.Id)).Where(o => o.Deleted == false);
                foreach (var d in cDrivers)
                {
                    if (!drivers.Select(a => a.Id).Contains(d.Id))
                        drivers.Add(d);
                }
            }

            ViewBag.Users = users.Select(s => new
            {
                Id = s.Id,
                Name = s.FullName
            }).OrderBy(o => o.Id).ToJson();

            ViewBag.Drivers = drivers.Select(s => DriversController.InternalDriverModel.Map(s)).OrderBy(o => o.Id).ToJson();

            ViewBag.Models = (await Extric.Towbook.Vehicle.Model.GetModelsAsync()).ToJson();
            ViewBag.Clients = Enum.GetValues(typeof(Extric.Towbook.Platform.ClientVersionType))
                                    .Cast<Extric.Towbook.Platform.ClientVersionType>()
                                    .ToDictionary(c => (int)c, c => c.ToString())
                                    .ToCollection()
                                    .ToJson();

            ViewBag.Reasons = (await new ReasonsController().Get())
                                        .Select(a => new { a.Id, a.Reason, a.Active })
                                        .ToCollection()
                                        .OrderBy(o => o.Id)
                                        .ToJson();

            var items = (await ActivityLogCloudService.GetByObject(ActivityLogType.DispatchEntry, id)).ToArray();

            bool hidePrices = false;

            if (await (await CallModel.MapAsync(entry)).ShouldBlockChargesAsync())
                hidePrices = true;

            foreach (var x in items)
            {
                // offset call times to be local to company
                x.OffsetDatesToCompanyLocal(WebGlobal.CurrentUser.Company);
                if (hidePrices)
                    x.HidePrices();
            }


            if (new int[] { 4185, 4189, 4194 }.Contains(WebGlobal.CurrentUser.CompanyId) && entry.CompletionTime != null)
            {
                if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                {
                    // block history after completion time

                    items = items.Where(o => o.CreateDate < entry.CompletionTime.Value).ToArray();
                }
            }

            items = items.OrderBy(o => o.Id).ToArray();

            ViewBag.Items = items.ToJson();

            return View();
        }

        /// <summary>
        /// Displays Update Invoice Status page
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<IActionResult> InvoiceStatus(int id)
        {
            var entry = await Entry.GetByIdAsync(id);

            if (entry == null || !(await WebGlobal.CurrentUser.HasAccessToDispatchEntryAsync(entry)))
                throw new TowbookException("The call specified doesn't exist or you don't have access to view it.");

            ViewBag.Id = id;
            ViewBag.CallNumber = entry.CallNumber;
            ViewBag.Id = entry.Id;
            ViewBag.Call = entry;

            var cm = await (await CallModel.MapAsync(entry)).FinishMapAsync();
            return View(cm);
        }



        /// <summary>
        /// Displays Request GOA UI
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ActionResult> DigitalRequestGoa(int id)
        {
            var entry = await Entry.GetByIdAsync(id);

            if (entry == null || !(await WebGlobal.CurrentUser.HasAccessToDispatchEntryAsync(entry)))
                throw new TowbookException("The call specified doesn't exist or you don't have access to view it.");

            ViewBag.Id = id;
            ViewBag.CallNumber = entry.CallNumber;
            ViewBag.Id = entry.Id;
            ViewBag.Call = entry;

            var cr = await CallRequest.GetByDispatchEntryId(id);
            ViewBag.CallRequest = cr;

            ViewBag.Reasons = MasterAccountReason.GetByMasterAccountId(entry.Account.MasterAccountId)
                .Where(o => o.Type == MasterAccountReasonType.Goa).ToList();

            var cm = await (await CallModel.MapAsync(entry)).FinishMapAsync();
            return View(cm);
        }

        /// <summary>
        /// Displays Request ETA UI
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ActionResult> DigitalExtendEta(int id)
        {
            var entry = await Entry.GetByIdAsync(id);

            if (entry == null || !(await WebGlobal.CurrentUser.HasAccessToDispatchEntryAsync(entry)))
                throw new TowbookException("The call specified doesn't exist or you don't have access to view it.");

            ViewBag.Id = id;
            ViewBag.CallNumber = entry.CallNumber;
            ViewBag.Id = entry.Id;
            ViewBag.Call = entry;
            
            var cr = await CallRequest.GetByDispatchEntryId(id);
            ViewBag.CallRequest = cr;

            ViewBag.Reasons = MasterAccountReason.GetByMasterAccountId(entry.Account.MasterAccountId)
                .Where(o => o.Type == MasterAccountReasonType.ExtendEta).ToList();

            var cm = await (await CallModel.MapAsync(entry)).FinishMapAsync();
            return View(cm);
        }



        /// <summary>
        /// Displays Digital DIspatch Cancel UI
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ActionResult> DigitalCancel(int id)
        {
            var entry = await Entry.GetByIdAsync(id);

            if (entry == null || !(await WebGlobal.CurrentUser.HasAccessToDispatchEntryAsync(entry)))
                throw new TowbookException("The call specified doesn't exist or you don't have access to view it.");


            if (entry.Account.MasterAccountId == MasterAccountTypes.AaaWashington ||
                entry.Account.MasterAccountId == MasterAccountTypes.AaaNewYork)
            {
                if (entry.Status == Status.AtSite)
                {
                    // if the call is on scene
                    return await DigitalRequestGoa(id);
                }
            }
                
            ViewBag.Id = id;
            ViewBag.CallNumber = entry.CallNumber;
            ViewBag.Id = entry.Id;
            ViewBag.Call = entry;

            var cr = await CallRequest.GetByDispatchEntryId(id);
            ViewBag.CallRequest = cr;

            var reasons = MasterAccountReason.GetByMasterAccountId(entry.Account.MasterAccountId)
                .Where(o => o.Type == MasterAccountReasonType.Cancel).ToList();

            if (entry.Account.MasterAccountId == MasterAccountTypes.AaaAcg ||
                entry.Account.MasterAccountId == MasterAccountTypes.AaaNationalFsl)
            {
                if (!entry.Waypoints.Any(o => o?.Title == "Destination"))
                {
                    reasons = reasons.Where(o => o.Type == MasterAccountReasonType.Cancel && o.Code != "DESTINATION_DECLINE")
                        .ToList();
                }

                var acg = AaaContractor.GetByCompanyId(entry.Account.CompanyId)
                    .FirstOrDefault(o => o.AccountId == entry.Account.Id);
                
                if (acg == null)
                    acg = AaaContractor.GetByCompanyId(entry.CompanyId)
                    .FirstOrDefault(o => o.AccountId == entry.Account.Id);

                if (acg != null && acg.EnvironmentId != 2)
                {
                    reasons = reasons.Where(o => o.MasterAccountReasonId != 2367 &&
                        o.MasterAccountReasonId != 2368 &&
                        o.MasterAccountReasonId != 2369 &&
                        o.MasterAccountReasonId != 2370 &&
                        o.MasterAccountReasonId != 2371).ToList();
                }
                else
                {
                    reasons = reasons.Where(o => o.MasterAccountReasonId != 1986 && o.MasterAccountReasonId != 1987).ToList();
                }
            }
            ViewBag.Reasons = reasons;

            var cm = await (await CallModel.MapAsync(entry)).FinishMapAsync();
            return View("DigitalCancel", cm);
        }

        /// <summary>
        /// Displays Digital Dispatch Cancel UI
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ActionResult> DigitalServiceFailure(int id)
        {
            var entry = await Entry.GetByIdAsync(id);

            if (entry == null || !(await WebGlobal.CurrentUser.HasAccessToDispatchEntryAsync(entry)))
                throw new TowbookException("The call specified doesn't exist or you don't have access to view it.");

            ViewBag.Id = id;
            ViewBag.CallNumber = entry.CallNumber;
            ViewBag.Id = entry.Id;
            ViewBag.Call = entry;

            var cr = await CallRequest.GetByDispatchEntryId(id);
            ViewBag.CallRequest = cr;

            ViewBag.Reasons = MasterAccountReason.GetByMasterAccountId(entry.Account.MasterAccountId)
                .Where(o => o.Type == MasterAccountReasonType.ServiceFailure).ToList();

            if (entry.Account.MasterAccountId == MasterAccountTypes.Swoop &&
                !string.IsNullOrWhiteSpace(cr.TowDestination) &&
                (entry.Status == Status.BeingTowed ||
                entry.Status == Status.DestinationArrival))
            {
                var client = entry.CompanyId == 10000 ? SwoopRestClient.Staging() :
                    new SwoopRestClient();

                var swoopCall = SwoopDispatch.GetByCallRequestId(cr.CallRequestId);
                if (swoopCall != null)
                {
                    var xss = SwoopSession.GetById(swoopCall.SwoopSessionId);

                    if (xss != null)
                    {
                        var reasons = await client.UnsuccessfulJobReasons(xss.AccessToken, swoopCall.OfferId);
                        
                        var reasons2 = MasterAccountReason.GetByMasterAccountId(entry.Account.MasterAccountId)
                            .Where(o => o.Type == MasterAccountReasonType.ServiceFailureTowing)
                            .Where(o => reasons.Any(r => r.Id == o.Code))
                            .ToList();

                        if (reasons2.Any())
                            ViewBag.Reasons = reasons2;
                    }
                }
            }

            var cm = await (await CallModel.MapAsync(entry)).FinishMapAsync();
            return View(cm);
        }

        /// <summary>
        /// Displays Digital Dispatch Request Additional Service
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ActionResult> DigitalRequestAdditionalService(int id)
        {
            var entry = await Entry.GetByIdAsync(id);

            if (entry == null || !(await WebGlobal.CurrentUser.HasAccessToDispatchEntryAsync(entry)))
                throw new TowbookException("The call specified doesn't exist or you don't have access to view it.");

            ViewBag.Id = id;
            ViewBag.CallNumber = entry.CallNumber;
            ViewBag.Id = entry.Id;
            ViewBag.Call = entry;

            var cr = await CallRequest.GetByDispatchEntryId(id);
            ViewBag.CallRequest = cr;

            ViewBag.Reasons = MasterAccountReason.GetByMasterAccountId(entry.Account.MasterAccountId)
                .Where(o => o.Type == MasterAccountReasonType.AdditionalService).ToList();

            return View(await (await CallModel.MapAsync(entry)).FinishMapAsync());
        }

        /// <summary>
        /// Displays Digital Dispatch Request Info
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ActionResult> DigitalRequestInfo(int id)
        {
            var entry = await Entry.GetByIdAsync(id);

            if (entry == null || !(await WebGlobal.CurrentUser.HasAccessToDispatchEntryAsync(entry)))
                throw new TowbookException("The call specified doesn't exist or you don't have access to view it.");

            ViewBag.Id = id;
            ViewBag.CallNumber = entry.CallNumber;
            ViewBag.Id = entry.Id;
            ViewBag.Call = entry;

            var cr = await CallRequest.GetByDispatchEntryId(id);
            ViewBag.CallRequest = cr;

            var cm = await (await CallModel.MapAsync(entry)).FinishMapAsync();
            return View(cm);
        }

        /// <summary>
        /// Displays the Payments view for the call, showing payments recorded against the call, current balance, recording additional payments, etc.
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ActionResult> Payments(int id)
        {
            var entry = await Entry.GetByIdAsync(id);
            var payments = new Collection<InvoicePayment>();

            if (entry == null || !(await WebGlobal.CurrentUser.HasAccessToDispatchEntryAsync(entry)))
                throw new TowbookException("The call specified doesn't exist or you don't have access to view it.");

            ViewBag.Id = id;
            ViewBag.CallNumber = entry.CallNumber;
            ViewBag.Company = Extric.Towbook.API.Models.CompanyConfigModel.Map(entry.Company);
            ViewBag.HasClosedPeriodFF = await entry.Company.HasFeatureAsync(
                Extric.Towbook.Generated.Features.AdvancedBilling_ClosedAccountingPeriod
                ) ? "true" : "false";
            ViewBag.Call = await (await CallModel.MapAsync(entry)).FinishMapAsync();

            ViewBag.CanLock = !await entry.IsLockedAsync() && 
                WebGlobal.CurrentUser.HasAccessToLockCalls() &&
                (!entry.Impound || entry.Released) && 
                ((entry.Status?.Id ?? 0) >= 5);
            
            ViewBag.CanAudit = !entry.Attributes.ContainsKey(Extric.Towbook.Dispatch.AttributeValue.BUILTIN_DISPATCH_AUDITED) && 
                WebGlobal.CurrentUser.HasAccessToAuditCalls() &&
                ((entry.Status?.Id ?? 0) >= 5);

            if (await (await CallModel.MapAsync(entry)).ShouldBlockChargesAsync())
            {
                payments = (await InvoicePayment.GetByDispatchEntryIdAsync(entry.Id, true)).Where(o => o.ClassId == 1).ToCollection();
            }
            else
            {
                payments = (await InvoicePayment.GetByDispatchEntryIdAsync(entry.Id, true));
            }

            var accountPayments = (await Payment.GetByAccountPaymentIdsAsync(payments.Select(s => s.AccountPaymentId).ToCollection())).ToList();
            var verifiedPayments = PaymentVerification.GetByIds(payments.Where(w => w.PaymentVerificationId != null && !w.IsVoid).Select(s => s.PaymentVerificationId.Value).ToArray()).ToList();

            var paymentUsers = new List<User>();

            foreach(var p in payments)
            {
                if (!paymentUsers.Any(a => a.Id == p.OwnerUserId))
                    paymentUsers.Add(await Extric.Towbook.User.GetByIdAsync(p.OwnerUserId));

                if (p.VoidedByUserId != null && !paymentUsers.Any(a => a.Id == p.VoidedByUserId.Value))
                    paymentUsers.Add(await Extric.Towbook.User.GetByIdAsync(p.VoidedByUserId.Value));

                var ap = accountPayments.FirstOrDefault(f => f.Id == p.AccountPaymentId);
                if (string.IsNullOrEmpty(p.ReferenceNumber) && ap != null)
                    p.ReferenceNumber = ap.ReferenceNumber;
            }

            foreach (var p in verifiedPayments)
            {
                if (!paymentUsers.Any(a => a.Id == p.UserId))
                    paymentUsers.Add(await Extric.Towbook.User.GetByIdAsync(p.UserId));
            }

            ViewBag.Payments = payments.Select(o => Extric.Towbook.API.Models.PaymentModel.Map(o, WebGlobal.CurrentUser));
            ViewBag.AccountPayments = accountPayments;
            ViewBag.VerifiedPayments = verifiedPayments;
            ViewBag.PaymentUsers = paymentUsers;
            ViewBag.PaymentStatuses = PaymentStatusModel.GetAllStatuses();

            if (await WebGlobal.CurrentUser.Company.HasFeatureAsync(Extric.Towbook.Generated.Features.ClassTracking))
            {
                ViewBag.ChargeClasses = ChargeClass.GetByCompanyId(entry.CompanyId);
            }
            else
            {
                ViewBag.ChargeClasses = null;
            }

            var pairedDevices = "[]";

if (entry.Company.HasFeature(Extric.Towbook.Generated.Features.PaymentIntegrations_Square) && 
                entry.Company.HasFeature(Extric.Towbook.Generated.Features.PaymentIntegrations_Square_SquareTerminal))
            {

                var resp = await WebGlobal.GetResponseFromUrlAsync("/api/integration/square/terminalDevices?companyId=" + entry.CompanyId);

                if (resp.IsSuccessStatusCode)
                {
                    string jsonString = await resp.Content.ReadAsStringAsync();
                    if(!string.IsNullOrEmpty(jsonString))
                        pairedDevices = jsonString;
                }
            }

            ViewBag.PairedDevicesJson = pairedDevices;
            ViewBag.HaveSquareDevicesPaired = pairedDevices != "[]";

            return View();
        }

        public ActionResult DamageForms(int id)
        {
            ViewBag.callId = id;
            ViewBag.damageTypesJson = VehicleDamage.Types.ToJson();
            ViewBag.damageRegionsJson = VehicleDamage.Regions.ToJson();
            ViewBag.usersJson = WebGlobal.GetResponseFromUrl($"/api/users");
            ViewBag.statusesJson = WebGlobal.GetResponseFromUrl($"/api/statuses");

            return View();
        }

        public ActionResult DamageFormRegions()
        {
            return View();
        }

        public async Task<IActionResult> AccidentReports(int id)
        {
            var featureEnabled = await WebGlobal.CurrentUser.Company.HasFeatureAsync(Extric.Towbook.Generated.Features.AccidentReports);
            
            if (id > 0)
            {
                ViewBag.callJson = WebGlobal.GetResponseFromUrl($"/api/calls/{id}");
                ViewBag.usersJson = WebGlobal.GetResponseFromUrl($"/api/users");
                ViewBag.reportsJson = WebGlobal.GetResponseFromUrl($"/api/accidentReports/getAll?dispatchEntryId={id}" + (featureEnabled ? "" : "&sampleOnly=true"));
            }
            else
            {
                ViewBag.callJson = "''";
                ViewBag.usersJson = "''";
                ViewBag.reportsJson = "''";
            }
            
            return View();
        }

        public async Task<IActionResult> Roadside(int id)
        {
            var entry = await Entry.GetByIdAsync(id);

            if (entry == null || !(await WebGlobal.CurrentUser.HasAccessToDispatchEntryAsync(entry)))
                throw new TowbookException("The call specified doesn't exist or you don't have access to view it.");

            ViewBag.callJson = WebGlobal.GetResponseFromUrl($"/api/calls/{id}");
            ViewBag.roadsideUsersJson = WebGlobal.GetResponseFromUrl($"/api/calls/{id}/roadsideUsers");
            ViewBag.roadsideEnabled = (await WebGlobal.CurrentUser.Company.HasFeatureAsync(Extric.Towbook.Generated.Features.Roadside)).ToJson();

            ViewBag.surveysOnlyEnabled = await WebGlobal.CurrentUser.Company.HasFeatureAsync(Extric.Towbook.Generated.Features.Roadside_CustomerSurveysOnly) ? "true" : "false";

            var av = AccountKeyValue.GetByAccount(entry.Account.CompanyId, entry.Account.Id, Provider.Towbook.ProviderId, "RoadsideMotorClubAccountInclude").FirstOrDefault();
            ViewBag.IncludeMotorClubAccount = av != null && av.Value == "1";

            if (await WebGlobal.CurrentUser.Company.HasFeatureAsync(Extric.Towbook.Generated.Features.Roadside) && entry.Account.Type == AccountType.MotorClub)
            {
                ViewBag.roadsideEnabled = "false";
                ViewBag.surveysOnlyEnabled = "true";
            }

            if (entry.Account.MasterAccountId == MasterAccountTypes.Urgently)
            {
                ViewBag.roadsideEnabled = "false";
                ViewBag.surveysOnlyEnabled = "false";
            }

            // Get surveys
            var reviewItems = Survey.Search(new[] { entry.CompanyId }, null, null, null, null, null, entry.Id, null, null).OrderBy(o => o.SurveySubmittedDate);

            var webItems = "[]";
            if (await WebGlobal.CurrentUser.Company.HasFeatureAsync(Extric.Towbook.Generated.Features.Roadside))
            {
                webItems = WebGlobal.GetResponseFromUrl($"/api/roadside/surveyresultswebcomponentitems/?responseIds=" + string.Join(",", reviewItems.Select(s => s.SurveyResponseId)));

                if (webItems == null || webItems == string.Empty)
                    webItems = "[]";
            }

            ViewBag.webComponentItems = webItems;

            var results = new List<object>();
            foreach (var s in reviewItems)
            {
                var row = new ExpandoObject() as IDictionary<string, Object>;

                var questions = new List<object>();

                foreach (var q in SurveyQuestionAnswerModel.TransformAnswers((string)s.AnswersJson))
                {
                    var answer = q.Rating;
                    if(q.Type == SurveyQuestionType.Boolean)
                    {
                        int v = 0;
                        if(int.TryParse(q.Value, out v))
                            answer = v;
                    }

                    questions.Add(new {
                        QuestionId = q.QuestionId,
                        Question = q.Question,
                        Type = q.Type,
                        Answer = answer,
                        Feedback = q.Feedback
                    });
                }

                questions.Add(new {
                    QuestionId = 4,
                    Question = "Review",
                    Type = SurveyQuestionType.Review,
                    Answer = string.Empty,
                    Feedback = s.Review ?? string.Empty
                });

                row["SurveyResponseId"] = s.SurveyResponseId;
                row["Contact"] = s.ContactName;
                row["Phone"] = s.ContactPhone;
                row["Email"] = s.ContactEmail;
                row["SurveySendDate"] = s.SurveySentDate;
                row["SurveySubmissionDate"] = s.SurveySubmittedDate;
                row["AverageRating"] = s.Rating;
                row["Questions"] = questions;

                var surveyResponse = SurveyResponse.GetById(s.SurveyResponseId);
                if(surveyResponse?.Deleted != true)
                    results.Add(row);
            }

            ViewBag.surveys = results.ToJson();

            ViewBag.dispatch = await Extric.Roadside.RoadsideDispatch.GetByDispatchEntryIdAsync(id);

            return View();
        }
    }

}
