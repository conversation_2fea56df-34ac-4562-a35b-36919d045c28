@model Extric.Towbook.Dispatch.CallModels.CallModel
@{
    ViewBag.Title = "Roadside";
}

<style>
    #rs-contents {
        background: #fff !important;
    }

    th {
        padding: 5px 10px;
        text-align: left;
    }

    td {
        padding: 5px 10px;
        vertical-align: middle;
    }

    button {
        margin-top: 0;
    }

    .navigation-row {
        padding-top: 30px;
    }

    .err-msg {
        display: none;
        padding: 10px;
        margin: 10px 0;
        border: 1px solid #d3d3d3;
        background: #ffffe0;
    }

        .err-msg .fa {
            font-size: 20px;
            padding-right: 10px;
        }

    .err-msg-2 {
        color: #2b75be;
    }

    .send-link,
    .new-contact-link {
        width: 150px !important;
    }

    #surveys-lbl {
        margin: 20px 0 10px 10px;
        font-size: 18px;
        color: #2b75be;
    }

    #surveys-none {
        padding: 0 0 15px 15px;
    }

    #surveys-table {
        display: none;
    }

        #surveys-table td {
            cursor: default;
            vertical-align: top;
        }

        #surveys-table .feedback {
            margin-top: 3px;
            font-style: italic;
        }

    .rating-stars {
        text-align: center;
    }

        .rating-stars .answer {
            display: none;
        }

        .rating-stars .feedback {
            margin-top: 3px;
        }

        .rating-stars .fa-star {
            color: #fa0;
        }

    #survey-responses {
        display: flex;
        flex-wrap: wrap;
    }

    #x-survey {
        display: flex;
        flex-wrap: wrap;
    }

        #x-survey li {
            padding: 30px;
            display: flex;
            flex-direction: column;
            border: 1px solid grey;
            margin-right: 15px;
            margin-bottom: 15px;
            max-width: 300px;
            overflow: hidden;
            position: relative;
        }

        #x-survey > div {
            width: 100%;
            display: flex;
        }

        #x-survey .fit-vert {
            flex-grow: 1;
            flex-shrink: 1;
            flex-basis: 100%
        }

        #x-survey .qa-wrapper {
            padding: 10px 0px;
            text-align: center;
        }

        #x-survey .contact {
            font-size: 1.5rem;
            padding: 5px;
            text-align: center;
        }

        #x-survey .question {
            font-style: italic;
            text-transform: uppercase;
            letter-spacing: .1em;
            color: #333;
        }

        #x-survey .answer {
            text-align: center;
            letter-spacing: .1em;
        }

        #x-survey .rating-wrapper {
            display: flex;
            flex-wrap: nowrap;
            padding-bottom: 5px;
            margin-bottom: 5px;
            border-bottom: 1px solid #ccc;
        }

            #x-survey .rating-wrapper > div {
                display: flex;
            }

                #x-survey .rating-wrapper > div.fit {
                    flex-grow: 1;
                    flex-shrink: 1;
                    flex-basis: 100%
                }

            #x-survey .rating-wrapper .rating-num {
                color: #fa0;
                font-size: 18px;
                line-height: 40px;
                padding-right: 10px;
                display: none;
            }

            #x-survey .rating-wrapper .star-ratings-css {
                unicode-bidi: bidi-override;
                color: #c5c5c5;
                font-size: 40px;
                line-height: 40px;
                margin: 0 auto;
                position: relative;
                padding: 0;
                text-shadow: 0px 1px 0 #a2a2a2;
            }


                #x-survey .rating-wrapper .star-ratings-css .star-ratings-css-top {
                    color: #fa0;
                    padding: 0;
                    position: absolute;
                    z-index: 1;
                    display: block;
                    top: 0;
                    left: 0;
                    overflow: hidden;
                }

                #x-survey .rating-wrapper .star-ratings-css .star-ratings-css-top {
                    padding: 0;
                    display: block;
                    z-index: 0;
                }

        #x-survey span.rating {
            color: #fa0;
            font-weight: 600;
            letter-spacing: .1rem;
        }

        #x-survey .web-integration-wrapper {
            border-top: 1px solid #ccc;
            margin-top: 10px;
        }

            #x-survey .web-integration-wrapper h4 {
            }

    .web-integration-wrapper.enabled input.x-add-item {
        display: none;
    }

    .web-integration-wrapper.enabled input.x-remove-item {
        display: inline-block;
    }

    .web-integration-wrapper input.x-add-item {
        display: inline-block;
    }

    .web-integration-wrapper input.x-remove-item {
        display: none;
    }

    .web-integration-wrapper .publish-info {
        color: #777;
    }
</style>

<h2 id="title">Roadside/Survey for Call #</h2>
<div id="rs-contents">
    <div id="is-motorclub" class="err-msg">
        <i class="fa fa-exclamation-circle"></i>
        The account currently selected is <span id="acct-name"></span>. &nbsp;This feature is not available for motor clubs.
    </div>
    <table id="rs-table">
        <thead>
            <tr>
                <th>Name</th>
                <th>Mobile Number</th>
                <th>Last Activity</th>
                <th></th>
            </tr>
        </thead>
        <tbody></tbody>
        <tfoot>
            <tr>
                <td><input type="text" class="new-contact-name" placeholder="New Contact" /></td>
                <td><input type="text" class="new-contact-phone" placeholder="Phone Number" /></td>
                <td>&nbsp;</td>
                <td><button class="new-contact-link flat-blue">send</button></td>
            </tr>
        </tfoot>
    </table>
    <div id="surveys-lbl">Customer Responses</div>
    <div id="surveys-none">(none)</div>

    <div id="survey-responses">
        <ul id="x-survey"></ul>
    </div>

</div>

<div class="navigation-row">
    <input type="button" value="Close" class="standard-button" id="ok-btn" />
</div>

<script type="text/x-jQuery-tmpl" id="t-contact">
    <tr data-contact-id="${id}" data-name="${name}" data-mobile="${phone}" data-email="${email}" data-roadside-user-id="${roadsideUserId}" data-roadside-dispatch-id="${roadsideDispatchId}">
        <td>${name}</td>
        <td>${towbook.formatPhoneNumber(phone)}</td>
        <td>${lastActivity ? towbook.formatDate(lastActivity) + ' ' + towbook.formatAMPM(lastActivity) : 'na'}</td>
        <td>
            {{if phone.length > 0}}
            {{if sending}}
            <button class="send-link flat-blue" disabled>sending</button>
            {{else}}
            <button class="send-link flat-blue">${roadsideUserId ? 're-send' : 'send'}</button>
            {{/if}}
            {{else}}
            <div class="err-msg-2">Phone # required to send Roadside/Survey</div>
            {{/if}}
        </td>
    </tr>
</script>

<script type="text/x-jQuery-tmpl" id="t-survey-list">
    <li>
        <div>
            {{if averageRating}}
            <div class="rating-wrapper">
                <div class="fit"></div>
                <div class="star-ratings-css">
                    <div class="star-ratings-css-top" style="width: ${averageRating * 20}%"><span>★</span><span>★</span><span>★</span><span>★</span><span>★</span></div>
                    <div class="star-ratings-css-bottom"><span>★</span><span>★</span><span>★</span><span>★</span><span>★</span></div>
                </div>
                <div class="fit"></div>
            </div>
            {{/if}}
            <div class="contact">${contact}</div>
            {{each questions}}
            {{if type == 0}}
            <div class="rating-stars qa-wrapper">
                <div class="question">${question}</div>
                <span class="answer">${answer}</span>
                <i class="{{if answer == 0}}far{{else}}fas{{/if}} fa-star"></i>
                <i class="{{if answer <= 1}}far{{else}}fas{{/if}} fa-star"></i>
                <i class="{{if answer <= 2}}far{{else}}fas{{/if}} fa-star"></i>
                <i class="{{if answer <= 3}}far{{else}}fas{{/if}} fa-star"></i>
                <i class="{{if answer <= 4}}far{{else}}fas{{/if}} fa-star"></i>
                <div class="feedback">{{if feedback}}"${feedback}"{{/if}}</div>
            </div>
            {{else}}
            <div class="qa-wrapper">
                <div class="question">${question}</div>
                <div class="answer">${answer}</div>
                <div class="feedback">{{if feedback}}"${feedback}"{{/if}}</div>
            </div>
            {{/if}}
            {{/each}}
            <div class="qa-wrapper">
                Submitted ${towbook.formatDate(surveySubmissionDate)}
                {{if averageRating}}<br />Rating of <span class="rating">${Number(Math.round(averageRating + 'e2') + 'e-2').toFixed(1)}</span>{{/if}}
            </div>
        </div>
        @if (Extric.Towbook.WebShared.WebGlobal.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.Roadside_SocialMediaLinks) && Extric.Towbook.WebShared.WebGlobal.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Manager)
        {
            <text>
                <div class="fit-vert"></div>
                <div class="web-integration-wrapper qa-wrapper {{if slideshowEnabled }}enabled{{/if}}">
                    <h4>Website Integration</h4>
                    <input type="button" value="Remove" class="button-gray x-remove-item" data-response-id="${surveyResponseId}" />
                    <input type="button" value="Add" class="button-gray x-add-item" data-response-id="${surveyResponseId}" />
                    <div class="publish-info">{{if createDate != null }}Added on ${towbook.formatDate(createDate)}{{else}}&nbsp;{{/if}}</div>
                </div>
            </text>
        }
    </li>
</script>




<script type="text/x-jQuery-tmpl" id="t-survey">
    <tr>
        <td>${contact}</td>
        {{each questions}}
        {{if type == 0}}
        <td class="rating-stars no-wrap" title="${question}">
            <span class="answer">${answer}</span>
            <i class="{{if answer == 0}}far{{else}}fas{{/if}} fa-star"></i>
            <i class="{{if answer <= 1}}far{{else}}fas{{/if}} fa-star"></i>
            <i class="{{if answer <= 2}}far{{else}}fas{{/if}} fa-star"></i>
            <i class="{{if answer <= 3}}far{{else}}fas{{/if}} fa-star"></i>
            <i class="{{if answer <= 4}}far{{else}}fas{{/if}} fa-star"></i>
            <div class="feedback">{{if feedback}}"${feedback}"{{/if}}</div>
        </td>
        {{else}}
        <td title="${question}">
            ${answer}
            <div class="feedback">{{if feedback}}"${feedback}"{{/if}}</div>
        </td>
        {{/if}}
        {{/each}}
        {{if questions.length < 3}}
        <td>&nbsp;</td>
        {{/if}}
        {{if questions.length < 2}}
        <td>&nbsp;</td>
        {{/if}}
    </tr>
</script>

<script>
    var call = @Html.Raw(ViewBag.callJson);
    var roadsideUsers = @Html.Raw(ViewBag.roadsideUsersJson);
    var roadsideEnabled = @Html.Raw(ViewBag.roadsideEnabled);
    var surveysOnlyEnabled = @Html.Raw(ViewBag.surveysOnlyEnabled);
    var surveys = @Html.Raw(ViewBag.surveys);
    var slideshowItems = @Html.Raw(ViewBag.webComponentItems);
    var contactTemplate = towbook.compileTemplate('tContact', $("#t-contact"));
    var surveyTemplate = towbook.compileTemplate('tSurvey', $("#t-survey-list"));
    var contacts = call.contacts;

    // start with call contacts
    contacts = contacts.map(function (c) {
        var ru = towbook.get(roadsideUsers, c.id, "callContactId");
        if (ru != null) {
            c.roadsideDispatchId = ru.roadsideDispatchId;
            c.roadsideUserId = ru.id;
            c.deleted = ru.deleted;
            c.lastActivity = ru.lastActivity || ru.createDate;
        }
        else {
            c.roadsideDispatchId = 0;
            c.roadsideUserId = 0;
            c.deleted = false;
            c.lastActivity = null;
        }

        // send only raw digits if the standard 10 digits are entered
        if (c.phone.replace(/[^0-9]+/g, '').length == 10)
            c.phone = c.phone.replace(/[^0-9]+/g, '');

        return c;
    });

    // consider roadside users
    roadsideUsers.map(function (ru) {
        if (towbook.get(contacts, ru.callContactId, "id") == null) {
            contacts.push({
                roadsideDispatchId: ru.roadsideDispatchId,
                roadsideUserId: ru.id,
                deleted: false,
                lastActivity: ru.lastActivity || ru.createDate,
                phone: ru.mobileNumber,
                name: ru.name,
            });
        }
    });

    var acc = towbook.get(towbook.accounts, call.account.id, "id");
    //TODO: Review Serialize response equals to old  Json.Encode
    var allowed = @Json.Serialize(ViewBag.IncludeMotorClubAccount);

    if (acc != null && acc.type == 21 && !allowed) // motor club
    {
        $('#is-motorclub').show();
        $('#acct-name').text(acc.name);
        $('#rs-table').hide();
        $('#surveys-lbl').hide();
        $('#surveys-none').hide();
        $('#surveys-table').hide();

    } else {

        $.each(contacts, function (count, contact) {
            $('#rs-table').append(towbook.applyTemplate(contactTemplate, contact));
        });

        if (surveys && surveys.length > 0) {
            $('#surveys-none').hide();
            $('#surveys-table').show();

            $.each(surveys, function (count, survey) {

                var slideshowItem = towbook.get(slideshowItems, survey.surveyResponseId, "responseId")
                if (slideshowItem != null) {
                    survey.slideshowEnabled = true;
                    survey.createDate = slideshowItem.createDate;
                }
                else {
                    survey.slideshowEnabled = false;
                    survey.createDate = null;
                }

                var obj = towbook.applyTemplate(surveyTemplate, survey);
                $('#x-survey').append(obj);
            });
        }

        console.log('contacts: ', contacts);
        console.log('surveys: ', surveys);
    }

    if (surveysOnlyEnabled || (call.status && call.status.id == 5)) {
        $('#title').text('Survey for Call #' + call.callNumber);
    } else {
        $('#title').text('Roadside/Survey for Call #' + call.callNumber);
    }

    $('#ok-btn').click(function () {
        towbook.views.dispatch.clearDetailView();
    });

    $('.send-link').click(function (event) {
        SendClickEventHandler(this);
    });

    $('.new-contact-link').click(function () {
        var name = $('.new-contact-name').val();
        var phone = $('.new-contact-phone').val();

        if (!name) {
            swal('Please specify a name');
            return;
        } else if (!phone) {
            swal('Please specify a phone number');
            return;
        }

        var self = $(this);
        $(this).text('sending');
        $(this).attr('disabled', 'disabled');

        $.ajax({
            url: '/api/calls/' + call.id + '/roadside/0/users/',
            type: 'POST',
            data: {
                id: 0,
                roadsideDispatchId: 0,
                callContactId: 0, // create new EntryContact
                name: name,
                mobileNumber: phone,
                email: null,
                deleted: false,
            },
        }).done(function (data) {

            var row = towbook.applyTemplate(contactTemplate, {
                roadsideDispatchId: data.dispatchId,
                roadsideUserId: data.id,
                deleted: false,
                lastActivity: moment().format(),
                phone: phone,
                name: name
            });

            row.find('.send-link').on('click', function (event) {
                SendClickEventHandler(this);
            });
            $('#rs-table').append(row);

            $('.new-contact-name').val('');
            $('.new-contact-phone').val('');

            resetSendButton(self, 'send');
        }).fail(function (xhr, status, error) {

            $.when(handleError(xhr, status, error))
                .then(function () {
                    resetSendButton(self, 'send')
                });
        });

    });


    function SendClickEventHandler(obj) {
        var self = obj;
        $(self).text('sending');
        $(self).attr('disabled', 'disabled');

        var data = $(self).closest('tr').data();
        var type = 'POST';
        var url = '/api/calls/' + call.id + '/roadside/' + data.roadsideDispatchId + '/users/';
        if (data.roadsideUserId > 0) {
            type = "PUT";
            url = '/api/calls/' + call.id + '/roadside/' + data.roadsideDispatchId + '/users/' + data.roadsideUserId + '?resend=true';
        }
        $.ajax({
            url: url,
            type: type,
            data: {
                id: data.roadsideUserId,
                roadsideDispatchId: data.roadsideDispatchId,
                callContactId: data.contactId,
                name: data.name,
                mobileNumber: data.mobile + '',
                email: data.email,
                deleted: false,
            }
        }).done(function (response) {
            resetSendButton(self, 're-send')
        }).fail(function (xhr, status, error) {
            $.when(handleError(xhr, status, error))
                .then(function () {
                    resetSendButton(self, 're-send')
                });
        });
    }


    function resetSendButton(obj, text) {
        if (!obj)
            return;

        if (!text)
            text = 'send';

        $(obj).text(text);
        $(obj).removeAttr('disabled');
    }

    function handleError(xhr, status, error) {
        var deferred = new $.Deferred();

        console.log(xhr, status, error);

        var msg = "Server returned status of " + xhr.status;

        if (xhr != null && xhr.responseText.length > 0)
        {
            if (typeof (xhr.responseText) == "string") {

                try {
                    // Is this a Json response
                    var rt = JSON.parse(xhr.responseText);
                    if (rt.hasOwnProperty('exceptionMessage'))
                        msg = rt.exceptionMessage;
                    else
                        msg = xhr.responseText;
                } catch (e) {
                    // Not Json, assume string response
                    msg = xhr.responseText;
                }
            }
        }

        swal({ title: "Error", text: msg, type: "error" })
            .then(function () {
                deferred.resolve();
            });

        return deferred.promise();
    }

    $('.x-remove-item').on('click', function () {
        var obj = $(this).closest('.web-integration-wrapper');
        var id = parseInt($(this).data('responseId'));
        console.log("remove web item", id);

        $(obj).find(".publish-info").html('<i class="fas fa-spinner fa-pulse"></i><span style="padding-left: 6px;">Deleting...</span>');

        $.when(updateWebsiteItem(id, false))
            .done(function (data, id) {
                slideshowItems = slideshowItems.filter(function (f) { return f.responseId != id; });
                $(obj).find(".publish-info").html("&nbsp;");
                $(obj).removeClass('enabled');
            }).fail(handleError);

    });

    $('.x-add-item').on('click', function () {
        var obj = $(this).closest('.web-integration-wrapper');
        var id = parseInt($(this).data('responseId'));
        console.log("add web item", id);

        $(obj).find(".publish-info").html('<i class="fas fa-spinner fa-pulse"></i><span style="padding-left: 6px;">Saving...</span>');

        $.when(updateWebsiteItem(id, true))
            .done(function (data) {
                slideshowItems.push(data);
                $(obj).find(".publish-info").html("Added on " + towbook.formatDate(data.createDate));
                $(obj).addClass('enabled');
            }).fail(handleError);
    });

    function updateWebsiteItem(responseId, isAdd) {
        var deferred = new $.Deferred();

        var item = towbook.get(slideshowItems, responseId, "responseId");

        var data = {
            id: item != null ? item.id : 0,
            responseId: responseId
        }

        var method = isAdd ? 'POST' : 'DELETE';
        var url = "/api/roadside/surveyresultswebcomponentitems/" + data.id;

        $.ajax({
            url: url,
            data: JSON.stringify(data),
            type: method,
            contentType: "application/json; charset=utf-8"
        }).done(function (data) {
            deferred.resolve(data, responseId);
        }).error(function (xhr, status, error) {
            deferred.reject(xhr, status, error, "string");
        });

        return deferred.promise();
    }

</script>
