using System;
using System.Collections.Generic;
using Extric.Towbook.WebShared;
using Extric.Towbook.AutoDataDirect;
using Extric.Towbook;
using Extric.Towbook.Company;
using Extric.Towbook.Integration;
using Microsoft.AspNetCore.Mvc;
using System.Linq;
using System.Configuration;

namespace Ajax.Areas.Settings.Controllers
{
    [Area("Settings")]
    public class AutoDataDirectController : Controller
    {
        [HttpGet]
        public ActionResult Index()
        {
            if (!new[] { Extric.Towbook.User.TypeEnum.Manager, Extric.Towbook.User.TypeEnum.Dispatcher }.Contains(WebGlobal.CurrentUser.Type))
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            return List();
        }

        [HttpGet]
        public PartialViewResult List()
        {
            if (!new[] { Extric.Towbook.User.TypeEnum.Manager, Extric.Towbook.User.TypeEnum.Dispatcher }.Contains(WebGlobal.CurrentUser.Type))
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            Response.Headers.Add("X-Twbk-Title", "AutoDataDirect Integration");

            this.ViewBag.Connection = AutoDataDirectConnection.GetByUserId(
                WebGlobal.CurrentUser.CompanyId, 
                WebGlobal.CurrentUser.Id);

            var webAppUrl = "https://app.towbook.com/autodatadirect/";
            if (Request.Host.Host.ToLowerInvariant().EndsWith("towbook.dev"))
                webAppUrl = "https://app.towbook.dev/autodatadirect/";
            if (Request.Host.Host.ToLowerInvariant().EndsWith("localhost"))
                webAppUrl = "http://localhost/autodatadirect";

            this.ViewBag.WebAppUrl = webAppUrl;

            return PartialView("../impounds/autodatadirect/index");
        }

        [HttpDelete]
        public JsonResult Delete()
        {
            if (!new[] { Extric.Towbook.User.TypeEnum.Manager, Extric.Towbook.User.TypeEnum.Dispatcher }.Contains(WebGlobal.CurrentUser.Type))
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            var connection = AutoDataDirectConnection.GetByUserId(
                WebGlobal.CurrentUser.CompanyId,
                WebGlobal.CurrentUser.Id);

            if(connection == null)
                throw new TowbookException("The AutoDataDirect connection was not found.");

            connection.Delete(WebGlobal.CurrentUser);

            return Json($"Successfully disconnected the AutoDataDirect connection for {WebGlobal.CurrentUser.FullName}.");
        }
    }
}
