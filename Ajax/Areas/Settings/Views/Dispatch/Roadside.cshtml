@using Extric.Towbook.Utility
@{
    ViewBag.Title = "Roadside.io Settings";
}

<style type="text/css">
    div.save .fa-spin, .fa-check, .fa-exclamation {
        display: none;
        margin-left: 10px;
        font-size: 20px
    }

    div.save > span {
        display: none;
        font-size: 1em;
    }

    div.save {
        line-height: 30px;
    }

    div.saving .fa-spin, div.saving .fa-spin + span {
        display: inline-block;
    }

    div.saved .fa-check, div.saved .fa-check + span {
        display: inline-block;
    }

    div.error .fa-exclamation, div.error .fa-exclamation + span {
        display: inline-block;
    }


    .fresh-check + label {
        color: #444;
        font-family: Calibri, Arial;
    }

    .ui-combobox-input {
        margin-right: 0;
    }

    button {
        margin-top: 0;
    }

    .blockUI.blockOverlay {
        background-color: white !important;
    }

    

    .large {
        width: 30rem;
    }

    .med {
        width: 15rem;
    }

    .small {
        width: 10rem;
    }

    .xsmall {
        width: 5rem;
    }

    .tc {
        text-align: center;
        justify-content: center;
    }

    input[type="checkbox"].fresh-check + label {
        padding: 0;
    }

    input[type="checkbox"].fresh-check:disabled + label span {
      border-color: #ccc;
      background-color: #efefef;
      cursor: default;
    }

    .limit-count-wrapper {
        color: #999;
        line-height: 14px;
        font-size: 12px;
        position: absolute;
        right: 0px;
        top: -14px;
    }

    .limit-count {
        color: #2B75BE;
        font-weight: 600;
        padding-right: 3px;
        font-size: 14px;
    }
</style>

<div class="Overview">
    Setup Roadside.io options for your entire company or specific accounts.
</div>
<div class="container">
    <!--
    <label for="ddlAccounts">Apply to:</label><br />
    <select id="ddlAccounts" data-width="100%"></select><br />
    <br />
    -->

    <div class="box-options-wrapper">
        <div class="box-options">
            <label>Visibility Options</label>
            <ul class="display-options" data-setting-id="0">
                <li style="display: none;">
                    <div class="label">Show your company info</div>
                    <div class="fit-auto"></div>
                    <div class="option"><input type="checkbox" class="fresh-check" id="showCompanyName" /><label for="showCompanyName"><span></span></label></div>
                </li>
                <li>
                    <div class="label">Allow customer to see the call ETA</div>
                    <div class="fit-auto"></div>
                    <div class="option"><input type="checkbox" class="fresh-check" id="showCallETA" /><label for="showCallETA"><span></span></label></div>
                </li>
                <li>
                    <div class="label">Show the drivers real-time location</div>
                    <div class="fit-auto"></div>
                    <div class="option"><input type="checkbox" class="fresh-check" id="showDriverLocation" /><label for="showDriverLocation"><span></span></label></div>
                </li>
                <li>
                    <div class="label">Show driver name</div>
                    <div class="fit-auto"></div>
                    <div class="option">
                        <select id="driverNameOption" title="Select a driver name option" data-width="150px">
                            <option value="0">Don't Show</option>
                            <option value="1">Show full name</option>
                            <option value="2">Show first name</option>
                        </select>
                    </div>
                </li>
                <li>
                    <div class="label">Provide a "call us" option?</div>
                    <div class="fit-auto"></div>
                    <div class="option">
                        <select id="showCallOption" title="Select a call option" data-width="150px">
                            <option value="0">Don't Show</option>
                            <option value="1">Call Dispatcher</option>
                            <option value="2">Call Driver</option>
                        </select>
                    </div>
                </li>
                <li>
                    <div class="label">Provide link to view the invoice</div>
                    <div class="fit-auto"></div>
                    <div class="option">
                        <input type="checkbox" class="fresh-check" id="showInvoice" /><label for="showInvoice"><span></span></label>
                    </div>
                </li>
            </ul>
        </div>

        <div class="box-options" id="text-alert-links">
            <label class="row">Job Progress Text Alerts</label>
            <ul class="display-options" data-setting-id="0">
                <li class="header">
                    <div class="fit-auto">Status</div>
                    <div class="option large">Message</div>
                    <div class="option">Enable</div>
                </li>
                <li>
                    <div class="label">Call created</div>
                    <div class="fit-auto"></div>
                    <div class="option large">
                        <input type="text" id="created-text-alert" data-id="0" maxlength="300" placeholder="@Extric.Roadside.JobProgressStatusType.Created.DefaultMessage" />
                        <div class="limit-count-wrapper" style="display: none;"><span class="limit-count"></span>(max 75)</div>
                        <div class="alert-info" style="display: none;">Alert sent during waiting and dispatched statuses</div>
                    </div>
                    <div class="option"><input type="checkbox" class="fresh-check text-alert-item" id="enable-created-text-alert" /><label for="enable-created-text-alert"><span></span></label></div>
                </li>
                <li>
                    <div class="label">En Route</div>
                    <div class="fit-auto"></div>
                    <div class="option large">
                        <input type="text" id="enroute-text-alert" data-id="0" maxlength="75" placeholder="@Extric.Roadside.JobProgressStatusType.Enroute.DefaultMessage" />
                        <div class="limit-count-wrapper" style="display: none;"><span class="limit-count"></span>(max 75)</div>
                        <div class="alert-info" style="display: none;">Alert sent when job is marked en route</div>
                    </div>
                    <div class="option"><input type="checkbox" class="fresh-check text-alert-item" id="enable-enroute-text-alert" /><label for="enable-enroute-text-alert"><span></span></label></div>
                </li>
                <li>
                    <div class="label">Arriving</div>
                    <div class="fit-auto"></div>
                    <div class="option large">
                        <input type="text" id="arriving-text-alert" data-id="0" maxlength="75" placeholder="@Extric.Roadside.JobProgressStatusType.Arriving.DefaultMessage" />
                        <div class="limit-count-wrapper" style="display: none;"><span class="limit-count"></span>(max 75)</div>
                        <div class="alert-info" style="display: none;">Estimated based on driver's real-time predicted arrival</div>
                    </div>
                    <div class="option"><input type="checkbox" class="fresh-check text-alert-item" id="enable-arriving-text-alert" /><label for="enable-arriving-text-alert"><span></span></label></div>
                </li>
            </ul>
            <label class="row">Job Completion Text & Survey Link</label>
            <ul class="display-options" data-setting-id="0">
                <li>
                    <div class="label">Completed</div>
                    <div class="fit-auto"></div>
                    <div class="option large">
                        <input type="text" id="completed-text-alert" data-id="0" maxlength="60" placeholder="@Extric.Roadside.JobProgressStatusType.Completed.DefaultMessage" />
                        <div class="limit-count-wrapper" style="display: none;"><span class="limit-count"></span>(max 60)</div>
                        <div class="alert-info" style="display: none;">This text message includes the link for customer satisfaction surveys</div>
                    </div>
                    <div class="option"><input type="checkbox" class="fresh-check text-alert-item" id="enable-completed-text-alert" /><label for="enable-completed-text-alert"><span></span></label></div>
                </li>
            </ul>

                <label class="row">Text Signature</label>
                <ul>
                    <li>
                        <div class="label">Company Name</div>
                        <div class="fit-auto"></div>
                        <div class="option large">
                            <input type="text" id="company-signature-name" maxlength="30" placeholder="@(Extric.Towbook.WebShared.WebGlobal.CurrentUser.Company.Name.Length <= 30 ? Extric.Towbook.WebShared.WebGlobal.CurrentUser.Company.Name :  "Company alias (less than 30 characters)" )" />
                            <div class="limit-count-wrapper" style="display: none;"><span class="limit-count"></span>(max 30)</div>
                        </div>
                        <div class="option"></div>
                    </li>
                </ul>
</div>

        

        <div class="box-options" id="text-alert-links">
            <label class="row">Preferences</label>
            <ul class="display-options" data-setting-id="0">
                <li class="header">
                    <div>Accounts</div>
                    <div class="fit-auto"></div>
                    <div class="xsmall">Roadside.io</div>
                    <div class="xsmall">Text Alerts</div>
                    <div class="small tc">Send Automatically</div>
                </li>
                <li>
                    <div>Motor Club Calls</div>
                    <div class="fit-auto"></div>
                    <div class="xsmall tc" style="border-left: 1px solid #ccc;"></div>
                    <div class="xsmall tc" style="border-right: 1px solid #ccc;"><input type="checkbox" class="fresh-check" id="enable-mc-text-alerts" /><label for="enable-mc-text-alerts"><span></span></label></div>
                    <div class="small tc">
                        <input type="checkbox" class="fresh-check" id="enable-mc-auto-invite" />
                        <label for="enable-mc-auto-invite" title="Invite the first contact on the call to automatically recieve text alerts. You must have at least one text alert to enable this option."><span></span></label>
                    </div>
                </li>
                <li>
                    <div>Non Motor Club Calls</div>
                    <div class="fit-auto"></div>
                    <div class="xsmall tc" style="border-left: 1px solid #ccc;"><input type="checkbox" class="fresh-check nmc-preferences" group="nmc-preferences" id="enable-nmc-roadside" /><label for="enable-nmc-roadside"><span></span></label></div>
                    <div class="xsmall tc" style="border-right: 1px solid #ccc"><input type="checkbox" class="fresh-check nmc-preferences" group="nmc-preferences" id="enable-nmc-text-alerts" /><label for="enable-nmc-text-alerts"><span></span></label></div>
                    <div class="small tc">
                        <input type="checkbox" class="fresh-check" id="enable-nmc-auto-invite" />
                        <label for="enable-nmc-auto-invite" title="Invite the first contact on the call to automatically recieve text alerts. You must have at least one text alert to enable this option."><span></span></label>
                    </div>
                </li>
            </ul>
        </div>

    </div>

<div class="save">
    <input type="button" class="button save-button" value="Save Options">
    <i class="fa fa-circle-notch fa-spin fa-3x fa-fw"></i>
    <span class="sr-only">Saving...</span>
    <i class="fa fa-check"></i>
    <span>Saved!</span>
    <i class="fa fa-exclamation"></i>
    <span>Error</span>
</div>

<script>
    var companyId = @ViewBag.Company.Id;
    var companySettings = JSON.parse('@Html.Raw(System.Web.HttpUtility.JavaScriptStringEncode(@ViewBag.CompanySettings))');
    var accounts = JSON.parse('@Html.Raw(System.Web.HttpUtility.JavaScriptStringEncode(@ViewBag.Accounts))');
    accounts.splice(0, 0, { id: 0, name: '@ViewBag.Company.Name - Default' });
    var currSettings = null;
    var key = "";
    var defaultSettings = {
        accountId: null,
        settings: []
    };
    var alertTypes = @Html.Raw(@ViewBag.Types);

    function getSettings(accountId) {
        currSettings = {
            accountId: accountId || null,
            settings: []
        };

        // If accountId == 0, we're looking for default settings
        if (accountId == 0) {

            currSettings.settings = companySettings.settings.filter(function (o) {
                return !o.accountId;
            }).sort(function (a, b) {
                return b.companyId - a.companyId; // sort by companyId desc
            }).slice(0, 1);

            defaultSettings = currSettings;

        } else {

            currSettings.settings = companySettings.settings.filter(function (o) {
                return o.accountId == accountId;
            }).slice(0, 1);
        }

        // If we have settings for this company/account
        if (currSettings.settings.length) {

            var compSettings = currSettings.settings[0];

            // Load them
            $('#showCallETA').prop('checked', currSettings.settings[0].showCallETA);

            var driverOption = currSettings.settings[0].showDriverFirstNameOnly ? 2 : currSettings.settings[0].showDriverName ? 1 : 0;
            $('#driverNameOption').val(driverOption);

            $('#showInvoice').prop('checked', currSettings.settings[0].showInvoice);
            $('#showCompanyName').prop('checked', currSettings.settings[0].showCompanyName);
            $('#showDriverLocation').prop('checked', currSettings.settings[0].showDriverLocation);
            $('#showCallOption').val(currSettings.settings[0].showCallOption);
            $('.display-options').data('setting-id', currSettings.settings[0].id);

            $('#enable-mc-text-alerts').prop('checked', currSettings.settings[0].enableMotorClubTextAlerts);
            $('#enable-mc-auto-invite').prop('checked', currSettings.settings[0].enableMotorClubAutoInvite);
            $('#enable-nmc-auto-invite').prop('checked', currSettings.settings[0].enableNonMotorClubAutoInvite);

            $('#company-signature-name').val(currSettings.settings[0].companySignatureName);

            var at = towbook.get(alertTypes, currSettings.settings[0].nonMotorClubTextAlertPreferenceType, "id");
            if (at != null) {
                if (at.name == "Roadside_io")
                    $('#enable-nmc-roadside').prop('checked', 'checked');
                else if (at.name == "TextOnly")
                    $('#enable-nmc-text-alerts').prop('checked', 'checked');
            }

            compSettings.textAlertItems.map(function (item) {
                if (item.name == "Created") {
                    $('#created-text-alert').data('id', item.id);    
                    $('#created-text-alert').attr('placeholder', item.message);

                    if (item.enabled) {
                        $('#enable-created-text-alert').prop('checked', 'checked');
                        $('#created-text-alert').val(item.message);
                    }
                }

                if (item.name == "Enroute") {
                    $('#enroute-text-alert').data('id', item.id);
                    $('#enroute-text-alert').attr('placeholder', item.message);

                    if (item.enabled) {
                        $('#enable-enroute-text-alert').prop('checked', 'checked');
                        $('#enroute-text-alert').val(item.message);
                    }
                }

                if (item.name == "Arriving") {
                    $('#arriving-text-alert').data('id', item.id);
                    $('#arriving-text-alert').attr('placeholder', item.message);

                    if (item.enabled) {
                        $('#enable-arriving-text-alert').prop('checked', 'checked');
                        $('#arriving-text-alert').val(item.message);
                    }
                }

                if (item.name == "Completed") {
                    $('#completed-text-alert').data('id', item.id > 0 ? item.id : 0);
                    $('#completed-text-alert').attr('placeholder', item.message);

                    if (item.enabled || item.id == -1) {
                        $('#enable-completed-text-alert').prop('checked', 'checked');
                        $('#completed-text-alert').val(item.message);
                    }
                }
            });


        } else {

            // Load defaults
            if (defaultSettings.settings.length) {
                $('#showCallETA').prop('checked', defaultSettings.settings[0].showCallETA);

                var driverOption = defaultSettings.settings[0].showDriverFirstNameOnly ? 2 : defaultSettings.settings[0].showDriverName ? 1 : 0;
                $('#driverNameOption').val(driverOption);

                $('#showInvoice').prop('checked', defaultSettings.settings[0].showInvoice);
                $('#showCompanyName').prop('checked', defaultSettings.settings[0].showCompanyName);
                $('#showDriverLocation').prop('checked', defaultSettings.settings[0].showDriverLocation);
                $('#showCallOption').val(defaultSettings.settings[0].showCallOption);

                $('#enable-mc-text-alerts').prop('checked', defaultSettings.settings[0].enableMotorClubTextAlerts);
                $('#enable-mc-auto-invite').prop('checked', defaultSettings.settings[0].enableMotorClubAutoInvite);
                $('#enable-nmc-auto-invite').prop('checked', defaultSettings.settings[0].enableNonMotorClubAutoInvite);

                var at = towbook.get(alertTypes, defaultSettings.settings[0].nonMotorClubTextAlertPreferenceType, "id");
                if (at != null) {
                    if (at.name == "Roadside_io")
                        $('#enable-nmc-roadside').prop('checked', 'checked');
                    else if (at.name == "TextOnly")
                        $('#enable-nmc-text-alerts').prop('checked', 'checked');
                }

            } else {
                $('#showCallETA').prop('checked', false);
                $('#driverNameOption').val(0);
                $('#showInvoice').prop('checked', false);
                $('#showCompanyName').prop('checked', false);
                $('#showDriverLocation').prop('checked', false);
                $('#showCallOption').val(0);

                $('#enable-mc-auto-invite').attr('disabled', 'disabled');
                $('#enable-nmc-auto-invite').attr('disabled', 'disabled');
            }
        }

        refreshOptions();

        if ($('.text-alert-item:checked').length == 0) {
            $('#enable-mc-auto-invite').attr('disabled', 'disabled');
            $('#enable-nmc-auto-invite').attr('disabled', 'disabled');
        }

        $('div.save').removeClass('saving').removeClass('error').removeClass('saved');
    }

    function refreshOptions() {

        if ($('.nmc-preferences').is(':checked'))
            $('#enable-nmc-auto-invite').removeAttr('disabled');
        else
        {
            $('#enable-nmc-auto-invite').prop('checked', '');
            $('#enable-nmc-auto-invite').attr('disabled', 'disabled');
        }

        if ($('#enable-mc-text-alerts').is(':checked'))
            $('#enable-mc-auto-invite').removeAttr('disabled');
        else
        {
            $('#enable-mc-auto-invite').prop('checked', '');
            $('#enable-mc-auto-invite').attr('disabled', 'disabled');
        }

        if ($('#enable-created-text-alert').is(':checked'))
            $('#created-text-alert').removeAttr('disabled');
        else
            $('#created-text-alert').attr('disabled', 'disabled');

        if ($('#enable-enroute-text-alert').is(':checked'))
            $('#enroute-text-alert').removeAttr('disabled');
        else
            $('#enroute-text-alert').attr('disabled', 'disabled');

        if ($('#enable-arriving-text-alert').is(':checked'))
            $('#arriving-text-alert').removeAttr('disabled');
        else
            $('#arriving-text-alert').attr('disabled', 'disabled');

        if ($('#enable-completed-text-alert').is(':checked'))
            $('#completed-text-alert').removeAttr('disabled');
        else
            $('#completed-text-alert').attr('disabled', 'disabled');
    }

    function refreshTextAlertItem(obj) {
        var enabling = $(obj).is(":checked");

        if (obj.id == 'enable-created-text-alert') {
            if (enabling)
                $('#created-text-alert').val($('#created-text-alert').prop('placeholder'));
            else
                $('#created-text-alert').val('');

        }
        else if (obj.id == 'enable-enroute-text-alert') {
            if (enabling)
                $('#enroute-text-alert').val($('#enroute-text-alert').prop('placeholder'));
            else
                $('#enroute-text-alert').val('');
        }
        else if (obj.id == 'enable-arriving-text-alert') {
            if (enabling)
                $('#arriving-text-alert').val($('#arriving-text-alert').prop('placeholder'));
            else
                $('#arriving-text-alert').val('');
        }
        else if (obj.id == 'enable-completed-text-alert') {
            if (enabling)
                $('#completed-text-alert').val($('#completed-text-alert').prop('placeholder'));
            else
                $('#completed-text-alert').val('');
        }
    
    }

    function getTextAlertItems() {
        var items = [];

        items.push({
            id: $('#created-text-alert').data('id'),
            typeId: @Extric.Roadside.JobProgressStatusType.Created.Id,
            name: "Created",
            message: $('#enable-created-text-alert').is(':checked') ? $('#created-text-alert').val() : $('#created-text-alert').attr('placeholder'),
            enabled: $('#enable-created-text-alert').is(':checked')
        });

        items.push({
            id: $('#enroute-text-alert').data('id'),
            typeId: @Extric.Roadside.JobProgressStatusType.Enroute.Id,
            name: "Enroute",
            message: $('#enable-enroute-text-alert').is(':checked') ? $('#enroute-text-alert').val() : $('#enroute-text-alert').attr('placeholder'),
            enabled: $('#enable-enroute-text-alert').is(':checked')
        });
        
        items.push({
            id: $('#arriving-text-alert').data('id'),
            typeId: @Extric.Roadside.JobProgressStatusType.Arriving.Id,
            name: "Arriving",
            message: $('#enable-arriving-text-alert').is(':checked') ? $('#arriving-text-alert').val() : $('#arriving-text-alert').attr('placeholder'),
            enabled: $('#enable-arriving-text-alert').is(':checked')
        });

        items.push({
            id: $('#completed-text-alert').data('id'),
            typeId: @Extric.Roadside.JobProgressStatusType.Completed.Id,
            name: "Completed",
            message: $('#enable-completed-text-alert').is(':checked') ? $('#completed-text-alert').val() : $('#completed-text-alert').attr('placeholder'),
            enabled: $('#enable-completed-text-alert').is(':checked')
        });

        return items;
    }

    function handleError(xhr, status, error) {
        console.log(xhr, status, error);
        swal({ title: "Please try again", text: "Server returned status of " + xhr.status, type: "error" })
            .then(function () {
                $('.save-button').removeAttr('disabled');
                $('.save-button').val('Save Options');
            });

        $('div.save').removeClass('saving').removeClass('saved').addClass('error');
    }

    function saveOptions() {
        $('div.save').removeClass('saved').removeClass('error').addClass('saving');
        $('.save-button').attr('disabled', 'disabled').val('Saving...');
       
        var keep = $.extend({}, currSettings.settings[0]);
        var textAlertItems = getTextAlertItems();

        // don't send text alert items when they haven't changed.
        if (JSON.stringify(keep.textAlertItems) === JSON.stringify(textAlertItems))
            textAlertItems = null;

        
        currSettings.settings = [];
        currSettings.questions = null;

        var driverOption = $('#driverNameOption').val();
        var at = $('#enable-nmc-roadside').is(':checked') ? 1 : $('#enable-nmc-text-alerts').is(':checked') ? 2 : 0;

        

        // Save settings
        currSettings.settings = [{
            id: keep.id,
            companyId: companyId,
            accountId: keep.accountId,
            showCallETA: $('#showCallETA').is(':checked'),
            showDriverName: driverOption == "1" || driverOption == "2" ? true : false,
            showDriverFirstNameOnly: driverOption == "2" ? true : false,
            showInvoice: $('#showInvoice').is(':checked'),
            showCompanyName: $('#showCompanyName').is(':checked'),
            showDriverLocation: $('#showDriverLocation').is(':checked'),
            showCallOption: $('#showCallOption').val(),
            ownerUserId: @Extric.Towbook.WebShared.WebGlobal.CurrentUser.Id,
            socialMediaIntegration: keep.socialMediaIntegration,
            enableMotorClubAutoInvite: $('#enable-mc-auto-invite').is(':checked'),
            enableMotorClubTextAlerts: $('#enable-mc-text-alerts').is(':checked'),
            nonMotorClubTextAlertPreferenceType: at,
            enableNonMotorClubAutoInvite: $('#enable-nmc-auto-invite').is(':checked'),
            textAlertItems: textAlertItems,
            companySignatureName: $('#company-signature-name').val()
        }];

        // If we made changes to the default settings, update them
        if (currSettings.accountId == null)
            defaultSettings = currSettings;

        setLoadingMessage($('#ajaxContent'), "loading");

        // Save settings
        $.ajax({
            url: '/api/roadside/settings',
            data: JSON.stringify(currSettings),
            dataType: 'json',
            type: 'POST',
            contentType: 'application/json; charset=utf-8',
        }).done(function (data) {
            unsetLoadingMessage();
            $('.save-button').removeAttr('disabled').val('Save Options');
            $('div.save').removeClass('saving').removeClass('error').addClass('saved');

            // Refresh company settings
            $.ajax({
                url: '/api/roadside/settings?companyId=' + companyId,
                type: 'GET',
            }).done(function (data) {
                companySettings = data;
                getSettings(currSettings.accountId || 0);
            });

        }).error(function (xhr, status, error) {
            unsetLoadingMessage();
            handleError(xhr, status, error);
        });
    }

    $(function () {
        // Load accounts into 'Apply To' drop down
        $('#ddlAccounts').appendOptions(accounts, false, false, 'id', 'name', null, null, true).setVal(accounts[0]);
        $('#ddlAccounts').combobox({
            selected: function () {
                getSettings(parseInt($(this).getVal()));
            }
        });

        // Get settings for this company
        getSettings(0);

        // If we uncheck 'Show Driver Name' then uncheck 'First Name Only' as well
        $('#showDriverName').change(function () {
            if ($('#showDriverName').prop('checked') == false)
                $('#showDriverFirstNameOnly').prop('checked', false)
        });

        // If we check 'First Name Only' then check 'Show Driver Name' as well
        $('#showDriverFirstNameOnly').change(function () {
            if ($('#showDriverFirstNameOnly').prop('checked') == true)
                $('#showDriverName').prop('checked', true)
        });

        // Save button - click
        $('.save-button').click(function () {
            saveOptions();

            return false;
        });

        $('.nmc-preferences').on('click', function () {
            var self = this;
            var enabling = $(this).is(':checked');

            // make checkboxes act like radio buttons
            $('.nmc-preferences').each(function (i, obj) {
                if (obj.id == self.id) {
                    if (enabling)
                        $(self).prop('checked', 'checked');
                    else
                        $(self).prop('checked', '');
                }
                else {
                    if ($(obj).is(':checked') && enabling)
                        $(obj).prop('checked', '');
                }
            });

            refreshOptions();
        });

        $('#enable-created-text-alert, #enable-enroute-text-alert, #enable-arriving-text-alert, #enable-completed-text-alert').on('click', function () {
            refreshTextAlertItem(this);

            refreshOptions();
        });

        $('.text-alert-item').on('click', function () {

            // uncheck automatic invites if all progress alert texts are unchecked
            if ($('.text-alert-item:checked').length == 0) {
                $('#enable-mc-auto-invite').prop('checked', '');
                $('#enable-nmc-auto-invite').prop('checked', '');
                $('#enable-mc-auto-invite').attr('disabled', 'disabled');
                $('#enable-nmc-auto-invite').attr('disabled', 'disabled');
            }
            else {
                $('#enable-mc-auto-invite').removeAttr('disabled');
                $('#enable-nmc-auto-invite').removeAttr('disabled');
            }

        });

        $('#created-text-alert, #enroute-text-alert, #arriving-text-alert, #completed-text-alert, #company-signature-name')
            .on('focus', function () {
                $(this).closest('.option').find('.limit-count-wrapper').show();
                $(this).closest('.option').find('.alert-info').show();

                $(this).trigger('keyup');
            })
            .on('blur', function () {
                $(this).closest('.option').find('.limit-count-wrapper').hide();
                $(this).closest('.option').find('.alert-info').hide();
            })
            .on('keyup', function () {
                var obj = $(this).closest('.option').find('.limit-count');

                $(obj).html($(this).val().length);
            });

        $('#enable-mc-text-alerts').on('click', function () {
            var enabling = $(this).is(':checked');
            if (enabling)
            {
                $('#enable-mc-auto-invite').removeAttr('disabled');
            }
            else
            {
                $('#enable-mc-auto-invite').prop('checked', '');
                $('#enable-mc-auto-invite').attr('disabled', 'disabled');
            }
        });

    });

</script>

