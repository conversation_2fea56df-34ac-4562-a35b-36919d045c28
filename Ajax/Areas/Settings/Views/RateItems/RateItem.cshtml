@using Extric.Towbook.WebShared
@model Extric.Towbook.API.Models.RateItemModel

@using (Html.BeginForm("Details", "RateItems", FormMethod.Post, new { id = "tbFormCustom" }))
{

<script type="text/javascript">
        var itemsToDelete = [];
        var timeRoundValues = [];
        var limitToAccountPricingOption = '@ViewBag.LimitToAccountPricingOption' === '@true';
        var accountRateItems = @Html.Raw(ViewBag.AccountRateItems);
        var storageItems = @Html.Raw(ViewBag.ChildRateItems);

        function deleteThis()
        {
            if (confirm('Are you sure you want to delete this item?')) {
                $('#tbFormCustom').get(0).setAttribute('action','/ajax/settings/rateitems/delete');
                $('#tbFormCustom').submit();
            }
        }

        function undeleteThis() {
            if (confirm('Are you sure you want to undelete this item?')) {
                $.ajax({
                    url: '/api/rateitems/' + @Model.Id + '/undelete/',
                    type: 'POST',
                }).done(function () {
                    tload(null, null, '/ajax/settings/rateitems');
                }).fail(function () {
                    swal({
                        type: 'error',
                        text: 'We ran into an error undeleting this rate item. Please try again later or contact our support staff.',
                    });
                });
            }
        }

        $(function () {
            towbook.compileTemplate('tieredStorageItem', $('#t-tieredStorageItem'));

            var useStandard = '@ViewBag.MapUsingStandardPricing' === '@true';

            $('#ajaxTitle').html('Modify <span class="rateItemName"></span>');
            var name = $('#Name').val();
            if(name.length != 0)
            {
                $('span.rateItemName').each(function () {
                    $(this).text(name);
                });
            } else {
                $('#ajaxTitle').html('Add New Service Item <span class="rateItemName"></span>');
            }

            $('#Name').on('keyup', function () {
                var name = $(this).val();
                if(name.length == 0)
                    $('span.rateItemName').html("");
                else
                    $('span.rateItemName').each(function () { $(this).text(name); });
            });

            var addToList = function(accountRateItemId, name, accountId, defaultRate, extendedItems, animate, defaultFreeQuantity) {
                var result = $('#account_' + accountId);
                if (animate == null)
                    animate = true;

                if(defaultFreeQuantity === undefined)
                    defaultFreeQuantity = '';

                var finder = function (array, value) {
                    for (var row in array) {
                        if (array[row].bodyType == value){
                            if (array[row].value > 0)
                                return array[row].value.toFixed(2);
                            return "";
                        }
                    }
                    return "";
                };

                defaultRate = parseFloat(defaultRate);
                if (defaultRate > 0)
                    defaultRate = defaultRate.toFixed(2);
                else
                    defaultRate = $('#cost').val();

                if (limitToAccountPricingOption && defaultRate == "")
                    defaultRate = "0.00";

                var txts = '';
                txts += '<td class="crt"><input type="text" id="account_' + accountId + '_any" class="validate-money" autocomplete="off" value="' + defaultRate + '"/><input type="hidden" name="account_' + accountId + '_id" value="' + accountId + '" /></td>';

                $(towbook.vehicle.types).each(function(index, item) {
                    txts += '<td class="crt x-subitem"><input type="text" id="account_' + accountId + '_' + item.id + '" data-bodytype="' + item.id + '" class="validate-money" autocomplete="off" value="' + finder(extendedItems, item.id) + '" /></td>';
                });

                @if (Model.PredefinedId == Extric.Towbook.PredefinedRateItem.BUILTIN_MILEAGE_UNLOADED ||
                    Model.PredefinedId == Extric.Towbook.PredefinedRateItem.BUILTIN_MILEAGE_LOADED ||
                    Model.PredefinedId == Extric.Towbook.PredefinedRateItem.BUILTIN_MILEAGE_DEADHEAD)
                {
                    <text>txts += '<td class="crt x-free-quantity"><input type="text" id="account_' + accountId + '_fq" class="validate-number" autocomplete="off" value="' + defaultFreeQuantity + '" /></td>';</text>
                }
                else
                {
                    <text>txts += '<td class="crt x-free-quantity" style="display: none;"><input type="text" id="account_' + accountId + '_fq" class="validate-number" autocomplete="off" value="' + defaultFreeQuantity + '" /></td>';</text>
                }

                if (result.length == 0) {
                    result = $("<tr id='account_" + accountId + "' class='dataRow' data-account-id='" + accountId + "' data-account-item-id='" + accountRateItemId + "'><td>" + name + '</td><td class="trash"><input type=button class="removeButton remove-account-pricing" title="Remove this account and any custom pricing associated with the account"></td>' + txts + "</tr>")
                        .appendTo('#tAccounts tbody');
                }

                result.find('.remove-account-pricing').on('click', function () {
                    if (confirm('Are you sure you want to remove this account and all the rates associated with this account?')) {
                        var rowObj = $(this).closest('tr.dataRow');
                        var accountId = parseInt(rowObj.data('accountId'));
                        var rateItemId = parseInt(rowObj.data('accountItemId'));
                        if (rateItemId > 0) {
                            itemsToDelete.push({ id: rateItemId, accountId: accountId, delete: true });
                        }

                        rowObj.removeAttr('id').slideUpTableRow(null, function (o) {
                            rowObj.remove();
                        });
                    }
                });

                if ($('#TimeBasedItem').is(':checked')) {
                    $('#tAccounts').find('tbody').find('.x-free-quantity').show();
                }

                if (animate)
                    $('#account_' + accountId).flashRow();
            }


            refreshPlaceholders = function(bodyTypeId) {
                var time = (new Date()).getTime();

                var defaultRow = $('tr.defaultRow:visible');

                defaultRow.each(function(index, item) {
                    var cost = $(item).find('.cost');
                    var blanket = towbook.formatMoney(cost.val()).substring(1);

                    // apply from rateItem.Cost to the extendedRateItems:
                    $(item)
                        .find('input[name^="ExtendedRates"], .x-extended-rate-input, .inherit')
                        .each(function () {
                            $(this).prop('placeholder', blanket);
                        });

                    if (bodyTypeId) {
                        // update account items (down/column) - just one body type
                        refreshBodyTypePlaceholders(bodyTypeId);
                    }
                    else {
                        // update account items (down/column) - All body types
                        $(item)
                            .find('input[name^="ExtendedRates"]:text, .x-extended-rate-input')
                            .each(function () {
                                refreshBodyTypePlaceholders($(this).data("body-type-id"));
                            });
                    }
                });

                // update account sub items... take into account account_any and base extended items.
                $('#tAccounts > tbody > tr > td > input[type=hidden]').each(function () {
                    refreshAccountPlaceholders($(this).val());
                });

                return (new Date()).getTime() - time;
            }


            // copy default value as placeholders to each standard rate extended input
            refreshDefaultRow = function(blanket, bodyTypeId)  {
                var time = (new Date()).getTime();

                // apply from rateItem.Cost to the extendedRateItems:
                if (bodyTypeId) {
                    $('tr.defaultRow:visible, dl.tieredRates')
                        .find('input[name^="ExtendedRates"][data-body-type-id="' + bodyTypeId + '"], .x-extended-rate-input[data-body-type-id="' + bodyTypeId + '"], .inherit')
                        .closest('td, dd')
                        .find('input')
                        .each(function () {
                            $(this).prop('placeholder', blanket);
                        });

                    refreshBodyTypePlaceholders(bodyTypeId);
                } else {
                    $('tr.defaultRow:visible, dl.tieredRates')
                        .find('input[name^="ExtendedRates"], .x-extended-rate-input, .inherit')
                        .each(function () {
                            $(this).prop('placeholder', blanket);
                        });
                }

                return (new Date()).getTime() - time;
            }

            // Column (down) inheritance
            refreshBodyTypePlaceholders = function (bodyTypeId) {
                var time = (new Date()).getTime();
                var blanket = towbook.formatMoney($('tr.defaultRow:visible').find('.cost').val()).substring(1);

                $('input[data-bodyType="' + bodyTypeId || 0 + '"]').each(function (i, s) {
                    var self = $('tr.defaultRow:visible').find('input[data-body-type-id="' + bodyTypeId + '"]');
                    if (useStandard)
                        $(s).prop('placeholder', self.val().length > 0 ? towbook.formatMoney(self.val()).substring(1) : self.prop('placeholder'));
                    else
                        $(s).not('.no-inherit').prop('placeholder', blanket);
                    if (self.val().length > 0 && self.val() != self.prop('placeholder') && !self.hasClass('default'))
                        $(s).data('mainset', 1);
                    else
                        $(s).data('mainset', 0);
                });

                return (new Date()).getTime() - time;
            }

            // Row (across) inheritance
            refreshAccountPlaceholders = function (accountId) {
                var time = (new Date()).getTime();

                var self = $('#account_' + accountId);
                var any = $('#account_' + accountId + '_any');

                var selector = $(any).closest('tr').find(".x-subitem > input");

                selector.each(function (i, c) {
                    c = $(c);

                    if (any.val().length > 0) {
                        if (c.data('mainset') != 1 || !useStandard) {
                            c.prop('placeholder', towbook.formatMoney(any.val()).substring(1));
                            c.addClass('no-inherit');
                        }
                    }
                    else {
                        c.removeClass('no-inherit');
                    }
                });

                return (new Date()).getTime() - time;
            }

            refreshTieredStoragePlaceholders = function(row) {
                var blanket = $(row).find('input.cost:first').prop('placeholder');
                var any = $(row).find('input.cost:first').val()

                $(row)
                    .find('dl.x-extended-rate')
                    .find('input')
                    .each(function () {
                        c = $(this);

                    if (any.length > 0) {
                        if (c.data('mainset') != 1 || !useStandard) {
                            c.prop('placeholder', towbook.formatMoney(any).substring(1));
                            c.addClass('no-inherit');
                        }
                    }
                    else {
                        c.removeClass('no-inherit');
                    }
                    });
            }

            var applyEvents = function(context) {
                if (context == null)
                    context = document;

                $(context).find('.cost').on('keyup', function () {
                    if ($(this).closest('dl.tieredRates').length)
                        refreshTieredStoragePlaceholders($(this).closest('dd.tiered-rate-row'));
                    else
                        refreshDefaultRow(towbook.formatMoney($(this).val()).substring(1));
                });

                $(context).find('input[name^="ExtendedRates"], .x-extended-rate-input').on('keyup', function () {
                    if ($(this).closest('dl.tieredRates').length) {
                        refreshTieredStoragePlaceholders($(this).closest('dd.tiered-rate-row'));
                        return;
                    }

                    var blanket = towbook.formatMoney($('tr.defaultRow:visible').find('.cost').val()).substring(1);
                    var bodyTypeId = $(this).closest('td').find('input[type="hidden"]').val();

                    if ($(this).closest("dl.tieredRates").length)
                        bodyTypeId = $(this).closest('dd').find('input[type="hidden"]').val();

                    refreshDefaultRow(blanket, bodyTypeId)
                });

                $(context).find('.bodyTypes input[name^="ExtendedRates"], .x-extended-rate-input').each(function () {
                    if ($(this).data("inheritApplied") != "1") {
                        $(this).bind('focus',function () {
                            if ($(this).hasClass('inherit')) {
                                $(this).removeClass('inherit');
                                $(this).val('');
                            }
                        });
                        $(this).bind('blur', function () {
                            if ($(this).val().length > 0)
                                $(this).removeClass('inherit');
                            $('.cost').blur();
                        });

                        $(this).data("inheritApplied", "1");
                    }
                });
            };

            var validateDay = function(obj) {
                if (obj == null)
                    obj = $('.tieredRates select:first');

                if (obj['get'] != null)
                    obj = obj.get(0);

                var day = +($(obj).combobox('get').customText);

                if (day == 0)
                    day = 1;

                var found = false;
                var previousE = null;
                $('.tiered-rate-row select').each(function(index,item) {
                    if (item == obj) {
                        found = true;
                    }

                    if (!found || index == 0) {
                        previousE = item;
                        return;
                    }

                    var thisVal = 0, lastVal = 0;
                    if (previousE != null) {
                        thisVal = +($(item).combobox('get').customText) ;
                        lastVal = +($(previousE).combobox('get').customText);
                        day = lastVal;
                    }

                    var days = Array();
                    for(i = day + 1; i < day + 59; i++) {
                        days.push({id: i, name: i + "" });
                    }
                    $(item).appendOptions(days, false, true, "id", "name", null, null, true);
                    previousE = item;

                    if (thisVal <= lastVal)
                        $(item).combobox('force', '');
                });
            };

            var onTieredStorageClick = function(addDefaultItem) {
                if (addDefaultItem == null)
                    addDefaultItem = true;
                if ($('#TieredStorageRates').prop('checked')) {
                    $('#regularRate').hide();
                    $('#tieredRate').show();
                    if ($('.tieredRates').html().length > 0)
                        return;

                    if (addDefaultItem)
                        addTieredItem();

                } else {
                    $('#regularRate').show();
                    $('#tieredRate').hide();
                }

            };
            var addTieredItem = function(data) {
                if (!$('#TieredStorageRates').is(':checked') || !$('#tieredRate').is(":visible") || $('#regularRate').is(":visible")){
                    $('#TieredStorageRates').prop('checked', true);
                    onTieredStorageClick(false);
                }

                if (data == null)
                    data = { id: -1, cost: null, freeQuantity: 1, extendedRates: [] };

                var output = $(towbook.applyTemplate('tieredStorageItem', data));

                output.find('select').combobox({
                    allowNull: false,
                    allowMissing: false,
                    selected: function () {
                        validateDay($(this));
                    }
                });

                output.find('.x-remove-tier').on("click", function() {
                    $(this).closest(".tiered-rate-row").remove();
                    validateDay();
                });

                if (data != null) {
                    output.find(".cost").setVal(data.cost);
                    output.find("select").append('<option value="' + data.freeQuantity + '">' + data.freeQuantity + '</option>').setVal(data.freeQuantity);
                    $(data.extendedRates).each(function(index,item) {
                        output.find('[data-body-type-id=' + item.bodyTypeId + ']').setVal(item.cost);
                    });
                }

                $('.tieredRates').append(output);
                validateDay();
                applyEvents(output);

                refreshTieredStoragePlaceholders(output);
            };

            $("span").tipTip({maxWidth: "auto"});

            $.extend(towbook, @Html.Raw(ViewBag.Config));
            towbook.formatData();

            var accounts = @Html.Raw(ViewBag.Accounts);

           $(accounts).each(function(index,item) {
                item.category = towbook.get(towbook.accountTypesPlural, item.type, 'type').name;
           });

           accounts = $.grep(accounts, function(o) { return o.id != 1; });

            var initialView = function() {
                $('ul.accountRows', '#x-accounts').show().find('li.accountRow').show();
                if(accounts.length > 20)
                    $('ul.accountRows', '#x-accounts').hide().find('li.accountRow').show();

                $.each($('li.groupRow', '#x-accounts'), function (index, group) {
                    var groupId = $(group).data('group-id');
                    if($('ul.accountRows[data-group-id=' + groupId + '] li').length !=0)
                    {
                        $(group).show();
                        if(accounts.length > 20)
                            $(group).addClass('hidden');
                    }
                });
            }

            resetContents = function() {
                refreshPlaceholders();

                $('#x-selected-message').hide();

                initialView();

                $('input[type=checkbox]', '#selectAccount').prop('checked', false);
                $('input[type=text]', '#selectAccount').val("");
                filterResults("");

                closeLightbox();
            }

            var filterResults = function(searchText) {
                if(searchText == "")
                {
                    initialView();
                    return;
                }

                var accountIds = $.map(accounts, function (account) {
                    var name = account.name;
                    var group = account.category;
                    var accountId = account.id;

                    if(name.toLowerCase().indexOf(searchText.toLowerCase()) >= 0 || group.toLowerCase().indexOf(searchText) >= 0)
                        return accountId;
                });

                $('ul.accountRows', '#x-accounts').hide();
                $('li.accountRow', '#x-accounts').hide();
                $('li.groupRow', '#x-accounts').hide();

                if(accountIds.length != 0)
                {
                    $('li.noResults', '#x-accounts').hide();

                    $.each(accountIds, function (index, accountId) {
                        accountRowObj = $('li.accountRow[data-id=' + accountId + ']', '#x-accounts');
                        accountsObj = $(accountRowObj).closest('ul.accountRows');
                        var groupId = $(accountsObj).data('group-id');
                        var groupRowObj = $('li.groupRow[data-group-id=' + groupId + ']');

                        $(accountRowObj).show();
                        $(accountsObj).show();
                        $(groupRowObj).show();

                    });
                }
                else
                {
                    $('li.noResults', '#x-accounts').show();
                }
            }

            $('a.button.selectAccount').on('click', function() {

                // only create once, otherwise let the click event pass.
                if(!$('#x-accounts').hasClass('created'))
                {
                    $('#loadingAccountSelect').show();
                    towbook.compileTemplate('tpl-account-group', $('#tpl-account-group'));
                    towbook.compileTemplate('tpl-account-row', $('#tpl-account-row'));
                    towbook.compileTemplate('tpl-group-row', $('#tpl-group-row'));

                    var categories = towbook.accountTypes;
                    categories.sort(function (a, b) {
                        return (a.name > b.name ? 1 : ((b.name > a.name) ? -1 : 0));
                    });


                    // inline fancybox select account setup
                    var $ui = $('#searchAccounts');
                    var $dd = $('.sb_dropdown', '#searchAccounts');

                    var displayChecked = function() {
                        var checkedObj = $('input[type=checkbox]:checked', '#x-accounts li.accountRow');
                        $.each(checkedObj, function () {
                            var rowObj = $(this).closest('li');
                            var groupId = rowObj.closest('ul').data('group-id');
                            rowObj.show();
                            $('li.groupRow[data-group-id=' + groupId + ']', '#x-accounts').show();
                        });
                    }

                    var countChecked = function () {
                        var amount = $('#x-accounts li.accountRow').find('input[type=checkbox]:checked').length;
                        var amountObj = $('.x-selected-amount', '#x-selected-message').remove();

                        if(amount == 1)
                            $('#x-selected-message').html(' account selected').prepend(amountObj);
                        else
                            $('#x-selected-message').html(' accounts selected').prepend(amountObj);

                        amountObj.html(amount);

                        if(amount)
                            $('#x-selected-message').show();
                        else
                            $('#x-selected-message').hide();
                    }

                    // Setup categories and accounts in list
                    $.each(categories, function (i, item) {
                        // Add category to select list
                        out = towbook.applyTemplate('tpl-group-row', item)
                        $('#x-accounts').append(out);
                        var acc = $.grep(accounts, function (account) {
                            return account.type == item.type;
                        }).sort(function (a, b) {
                            return (a.name > b.name ? 1 : ((b.name > a.name) ? -1 : 0));
                        });
                        $(out).find('span.x-group-count').html("(" + acc.length + ")");

                        var groupRow = $('#x-accounts li[data-group-id=' + item.type + ']');
                        var accountRows = $('#x-accounts ul[data-group-id=' + item.type + ']');

                        // Add accounts under category
                        $.each(acc, function (i, account) {
                            accountRows.append(towbook.applyTemplate('tpl-account-row', account));
                        });

                        // hide category if there are no accounts associated with it
                        if(accountRows.find('li').length == 0)
                        {
                            groupRow.hide();
                        } else {
                            // Add categories to drop down menu of search box
                            var cat = towbook.applyTemplate('tpl-account-group', item);
                            $dd.append(cat);
                        }

                        // apply events
                        groupRow.on('click', function(e) {
                            // Collapse/Uncollapse group sections
                            if(!$(this).hasClass('hidden'))
                            {
                                $(this).addClass('hidden');
                                $('ul.accountRows[data-group-id=' + $(this).data('group-id') + ']', '#x-accounts').slideUp();
                            } else {
                                $(this).removeClass('hidden');
                                $('ul.accountRows[data-group-id=' + $(this).data('group-id') + ']', '#x-accounts').slideDown();
                            }
                        });
                    });

                    // inline fancybox select account events
                    $('#x-accounts :checkbox').on('change', function (e) {

                        if($(this).closest('li').hasClass('groupRow'))
                        {
                            var rowObj = $(this).closest('li')
                            var groupId = rowObj.data('group-id');

                            if($(this).is(':checked'))
                            {
                                if(rowObj.hasClass('hidden'))
                                    $('li.accountRow', 'ul[data-group-id=' + groupId + ']').find('input[type=checkbox]').prop('checked', true);
                                else
                                    $('li.accountRow:visible', 'ul[data-group-id=' + groupId + ']').find('input[type=checkbox]').prop('checked', true);
                            }
                            else
                            {
                                if(rowObj.hasClass('hidden'))
                                    $('li.accountRow', 'ul[data-group-id=' + groupId + ']').find('input[type=checkbox]').prop('checked', false);
                                else
                                    $('li.accountRow:visible', 'ul[data-group-id=' + groupId + ']').find('input[type=checkbox]').prop('checked', false);
                            }
                        }
                        countChecked();
                    });

                    $('#x-accounts :checkbox').on('click', function (e) {
                        e.stopPropagation();
                    });

                    $('#x-accounts :checkbox + span').on('click', function (e) {
                        // move click event to checkbox
                        $(this).closest('li').find('input[type=checkbox]').trigger('click');
                        e.stopPropagation();
                    });

                    $ui.find('input[type=text]').on('keyup', function(e) {
                        var searchText = $(this).val();

                        filterResults(searchText);
                    });

                    $($dd).find(':checkbox').on('click', function() {
                        var groupIds = $.map($('ul.sb_dropdown li', '#searchAccounts'), function (item) {
                            if($(item).find('input[type=checkbox]').is(':checked'))
                                return $(item).data('group-id');
                        });

                        if(groupIds.length == 0)
                        {
                            initialView();
                        }
                        else
                        {
                            $.each($('ul.sb_dropdown li', '#searchAccounts'), function() {
                                var groupId = $(this).data('group-id');

                                if(groupIds.indexOf(groupId) != -1)
                                {
                                    $('li.groupRow[data-group-id=' + groupId + ']', '#x-accounts').show();
                                    $('ul.accountRows[data-group-id=' + groupId + ']', '#x-accounts').find('li.accountRow').show();
                                } else {
                                    $('li.groupRow[data-group-id=' + groupId + ']', '#x-accounts').hide();
                                    $('ul.accountRows[data-group-id=' + groupId + ']', '#x-accounts').find('li.accountRow').hide();
                                }
                            });

                            var text = $('#searchAccounts').find('input[type=text]').val();
                            if(text.length == 0){
                                if(groupIds.length == 0)
                                    $('li.noResults', '#x-accounts').show();
                            }
                            else
                            {
                                filterResults(text);
                            }

                            displayChecked();
                        }
                    });

                    $ui.find('.sb_input').bind('focus click', function() {
                        $ui.find('.sb_down')
                            .addClass('sb_up')
                            .removeClass('sb_down')
                            .andSelf()
                            .find('.sb_dropdown')
                            .show();
                    });

                    $ui.find('.sb_container').find(':nth-child(3)').on('click', function() {
                        if($(this).hasClass('sb_down'))
                        {
                            $(this).removeClass('sb_down').addClass('sb_up');
                            $dd.show();
                        }
                        else
                        {
                            $(this).removeClass('sb_up').addClass('sb_down');
                            $dd.hide();
                        }
                    });

                    $ui.bind('mouseleave', function() {
                        $ui.find('.sb_up')
                            .addClass('sb_down')
                            .removeClass('sb_up')
                            .andSelf()
                            .find('.sb_dropdown')
                            .hide();
                    });

                    $('#loadingAccountSelect').hide();
                    $('#x-accounts').addClass('created');

                    initialView();
                }

            });

            $('.cancel.button').on('click', function() {
                resetContents();
            });

            $('#x-submit').on('click', function() {
                if(!$('#x-selected-message').is(':visible'))
                {
                    alert("There are no accounts selected.  Please check at least one account to continue.");
                    return;
                }

                $('#loadingAccountSelect').show();

                var checkedObj = $('input[type=checkbox]:checked', '#x-accounts li.accountRow');
                $.each(checkedObj, function () {
                    var rowObj = $(this).closest('li');
                    var accountId = rowObj.data('id');
                    var acc = towbook.get(accounts, accountId);

                    if (acc != null) {
                        if ($('#account_' + acc.id).length !== 0) {
                            towbook.log("account " + acc.id + " is already showing.");
                        }
                        else {
                            addToList(0, acc.name, acc.id, 0, acc.extendedItems, true, acc.freeQuantity);
                        }
                    }
                });

                $('#loadingAccountSelect').hide();
                resetContents();
                $('#ajaxContent').trigger('scroll');
            });

            $('#CategoryId').combobox({
                selected: function () {
                    if($(this).val() == 5) {
                        $('.ajaxContent').addClass('z-tiered-storage');
                        $('#time-based-button-container').hide();
                    } else {
                        $('.ajaxContent').removeClass('z-tiered-storage');
                        $('#TieredStorageRates').prop('checked', false).checkbox('refresh');

                        $('#regularRate').show();
                        $('#tieredRate').hide();

                        $('#time-based-button-container').show();
                    }
                }
            }).combobox('selected'); // fire the select event

            $('input[type=checkbox]').checkbox();

        @if (Extric.Towbook.WebShared.WebGlobal.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.TimeBasedRates) ||
            (Extric.Towbook.WebShared.WebGlobal.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.Impounds_HourlyStorage) && Model.PredefinedId == Extric.Towbook.PredefinedRateItem.BUILTIN_STORAGE_HOURLYRATE))
            {<text>
                var onTimeBasedItemClick = function() {

                    if ($('#TimeBasedItem').is (':checked')) {
                            $('#timeBasedRate').show();
                            $('#tPrice').text('Price Per Hour');
                            $('#tAccounts').find('.defaultRow').find('td:eq(0)').html('Standard Rate <span class="extra">(per hour)</span>');
                            $('#tAccounts').find('thead').find('.x-free-quantity').html('Free mins');
                            $('#tAccounts').find('.x-free-quantity').show();
                    } else {
                        $('#timeBasedRate').hide();
                        $('#tPrice').html('Price');
                        $('#tAccounts').find('.defaultRow').find('td:eq(0)').html('Standard Rate');
                        $('#tAccounts').find('thead').find('.x-free-quantity').html('Free miles');
                    @if(Model.PredefinedId != Extric.Towbook.PredefinedRateItem.BUILTIN_MILEAGE_UNLOADED &&
                        Model.PredefinedId != Extric.Towbook.PredefinedRateItem.BUILTIN_MILEAGE_LOADED &&
                        Model.PredefinedId != Extric.Towbook.PredefinedRateItem.BUILTIN_MILEAGE_DEADHEAD &&
                        Model.PredefinedId != Extric.Towbook.PredefinedRateItem.BUILTIN_STORAGE_HOURLYRATE) {
                        <text>
                        $('#tAccounts').find('.x-free-quantity').hide();
                        </text>
                        }
                    }
                }

                @if (Model.PredefinedId != Extric.Towbook.PredefinedRateItem.BUILTIN_STORAGE_HOURLYRATE) {
                <text>$('#TimeBasedItem').on('click', onTimeBasedItemClick);</text>
                }

            </text>
            }
            $('#TieredStorageRates').on('click', onTieredStorageClick);
            $('.x-add-tier').on("click", function () {
                addTieredItem();
            });

            $('#tbFormCustom').on('submit', function (e) {
                e.preventDefault();

                if ($(this).validate != null) {
                    $(this).validate(this.options);
                    if ($(this).valid != null) {
                        if (!$(this).valid())
                            return;
                    }
                }

                var action = $(this).attr('action');
                var postData = $(this).serialize();

                $('input[type="submit"]').prop('disabled', 'disabled');
                $.ajax({
                    url: action,
                    type: 'POST',
                    data: postData,
                    error: function(jqXHR, textStatus, errorThrown) {
                        alert('failed to save: ' + textStatus + ", " + errorThrown);
                    }
                }).done(function (r, s, x) {
                    window.location.hash = tload_activeUrl = curHash = x.getResponseHeader('X-Twbk-Location');

                    var id = x.getResponseHeader('X-Twbk-ResourceId');

                     if (action != '/ajax/settings/rateitems/delete')
                    {
                        (function saveTieredRates() {
                            var rows = $('.tiered-rate-row:visible').map(function(index,item) {
                                return {
                                    id: $(item).data('id') || 0,
                                    name: "-",
                                    freeQuantity: $(item).find('select').combobox('get').customText,
                                    cost: $(item).find('.cost').val(),
                                    extendedRates: $(item).find('.x-extended-rate').map(function(index,item) {
                                        return {
                                            bodyTypeId: $(item).find('.x-bodyTypeId').val(),
                                            cost: $(item).find('.x-cost').val()
                                        };
                                    }).toArray(),
                                    parentRateItemId: id
                                };
                            }).toArray();

                            console.log(rows);

                            if (rows.length == 0) {
                                // force a delete... we could optimize this by checking if there were any prior, this is
                                // a wasted request...
                                // TODO: check if the user removes any from the page/hides them, and only in that case remove them.
                                // TODO/Priority (1-5): 2
                                $.ajax({
                                    url: "/api/rateItems/" + id + "/children",
                                    type: 'DELETE' });
                            } else {
                                $.ajax({
                                    url: "/api/rateItems/" + id + "/children",
                                    type: 'POST',
                                    contentType: 'application/json; charset=utf-8',
                                    data: JSON.stringify(rows),
                                    success: function (data) {
                                        towbook.log('saved:', data, rows);
                                    },
                                    error: function (jqXHR, textStatus, errorThrown) {
                                        alert('failed to save: ' +  textStatus + ", " + errorThrown, row);
                                    }
                                });
                            }
                        })();

                        (function saveAccountRates() {

                            var batchedUpdates = [];

                            $('#tAccounts .dataRow').each(function (e) {
                                var dataRowObj = $(this);
                                var model = new Object();
                                var i = 0;

                                var basePrice =  dataRowObj.is(':visible') ? $(this).find('input[id$="_any"]').val() : '0';
                                var freeQuantity = $(this).find('input[id$="_fq"]').getVal();

                                if (basePrice.length == 0)
                                    basePrice = 0;

                                if (freeQuantity.length == 0)
                                    freeQuantity = 0;

                                model.id = id;
                                model.accountId = $(this).find('input[type=hidden]').val();
                                model.value = basePrice;
                                model.extendedItems = new Array();
                                model.freeQuantity = freeQuantity;

                                dataRowObj.find('input[data-bodytype]').each(function (e) {
                                    model.extendedItems[i++] = {
                                        "bodyType": $(this).data('bodytype'),
                                        "value": (dataRowObj.is(':visible') && $(this).getVal().length > 0 ? $(this).getVal() : "0")
                                    };
                                });
                                batchedUpdates.push(model);
                            });

                            batchedUpdates = batchedUpdates.concat($.grep(itemsToDelete, function (o) {
                                if (towbook.get(batchedUpdates, o.accountId, "accountId")) {
                                    return false;
                                }
                                return true;
                            }));

                            console.log("batched:", batchedUpdates);

                            $.ajax({
                                url: "/api/accountRateItems/" + id,
                                type: 'POST',
                                contentType: 'application/json; charset=utf-8',
                                data: JSON.stringify(batchedUpdates),
                                success: function (data) {
                                    towbook.log('saved');
                                    towbook.log(data);
                                },
                                error: function (jqXHR, textStatus, errorThrown) {
                                    alert('failed to save ' + model.AccountId + ': ' + textStatus + ", " + errorThrown);
                                }
                            });
                        })();

                    }

                    var xRedirect = x.getResponseHeader('X-Twbk-Redirect');

                    if (xRedirect != null) {
                        towbook.log("X-Twbk-Redirect... Destination => " + xRedirect);
                        tload(null, true, xRedirect);
                        $.growlUI('', 'Changes saved successfully.');
                    }
                    else {
                        ajTitle = x.getResponseHeader('X-Twbk-Title');
                        if (ajTitle != null)
                            $('#ajaxTitle').text(ajTitle);

                        $('#ajaxContent').html(r).find("a[href*='/ajax/'], a[href*='/Ajax/']").click(function () {
                            tload(null, true, $(this).attr('href'));
                            return false;
                        });
                    }
                });
            });


            // first initilization of placeholders
            var blanket = towbook.formatMoney($('tr.defaultRow:visible').find('.cost').val()).substring(1);
            refreshDefaultRow(blanket);

            // Add account rateitems and refresh each row
            accountRateItems.map(function (item) {
                if ($('#account_' + item.accountId).length == 0) {
                    addToList(@Html.Raw(Model.Id), item.accountName, item.accountId, item.value.toFixed(2), item.extendedItems, false, item.freeQuantity);
                    refreshAccountPlaceholders(item.accountId);
                };
            });

            if(storageItems.length)
                $('.tieredRates').empty();

            $(storageItems).each(function(index, item) {
                addTieredItem(item);
            });


            @if (Extric.Towbook.WebShared.WebGlobal.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.TimeBasedRates) ||
                (Extric.Towbook.WebShared.WebGlobal.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.Impounds_HourlyStorage) && Model.PredefinedId == Extric.Towbook.PredefinedRateItem.BUILTIN_STORAGE_HOURLYRATE))
            {
                foreach (SelectListItem trv in Model.TimeRoundValues) {
                    <text>timeRoundValues.push({id: @trv.Value, name: "@trv.Text" }); </text>
                }
                <text>$('#TimeRound').appendOptions(timeRoundValues, false, true);</text>

                if (Model.PredefinedId != Extric.Towbook.PredefinedRateItem.BUILTIN_STORAGE_HOURLYRATE)
                {
                    <text>onTimeBasedItemClick();</text>
                }
            }

            @if (Extric.Towbook.WebShared.WebGlobal.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.Impounds_HourlyStorage) && Model.PredefinedId == Extric.Towbook.PredefinedRateItem.BUILTIN_STORAGE_HOURLYRATE)
            {
                <text>
            $('.ajaxContent').removeClass('z-tiered-storage');
            $('#timeStartAt').hide();
            $('#timeStopAt').hide();
            $('#categoryIdWrapper').hide();
            $('#time-based-button-container').show();
            $('#TimeBasedItem').prop('checked', true).checkbox('refresh');
            $('#tAccounts').find('tbody tr.defaultRow:eq(0)').find('input[type="text"]').prop('disabled', true);
            $('#tAccounts').find('.x-free-quantity').hide();
            $('#tPrice').text('Price Per Hour');
            $('#TimeBasedItem').on('click', function () {
                $(this).prop('checked', true).checkbox('refresh');
            });
                </text>
            }

            // apply body type defaults.  Must happen after account rateitems are added.
            $('tr.defaultRow:visible')
                .find('input[name^="ExtendedRates"]:text, .x-extended-rate-input')
                .each(function () {
                var bodyTypeId = $(this).closest('td').find('input[type="hidden"]').val();
                refreshDefaultRow(blanket, bodyTypeId);
            });


                // call this only once
                applyEvents();

            $('.cost').blur();

                initDefaultTitles();
            });
    </script>
    <script type="text/x-jQuery-tmpl" id="t-tieredStorageItem">
        <dd class="tiered-rate-row" data-id="${id}">
            <dl>
                <dt>Day # +</dt>
                <dd>
                    <select data-width="80px" class="x-freeQuantity">
                        <option value="1" selected>1</option>
                    </select>
                </dd>
            </dl>
            <dl>
                <dt><span title="Specify what you charge retail/cash calls, or that you want applied to accounts by default">Default</span></dt>
                <dd><input class="validate-money cost" value="${cost}" type="text" /></dd>
            </dl>

            @for (int i = 0; i < Model.ExtendedRates.Count; i++)
            {
                <dl class="x-extended-rate">

                    <dt>@Model.ExtendedRates[i].Name</dt>
                    <dd>
                        <input class="validate-money x-cost x-extended-rate-input" type="text" data-body-type-id="@Model.ExtendedRates[i].BodyTypeId" />
                        @Html.HiddenFor(model => model.ExtendedRates[i].BodyTypeId, new { @class = "x-bodyTypeId" })
                    </dd>
                </dl>
            }
            <dl>
                <dt>&nbsp;</dt>
                <dd><input type="button" class="standard-button x-remove-tier" style="width: 20px" value="-" /></dd>
            </dl>
        </dd>
    </script>


    @Html.HiddenFor(model => model.Id)
    <style type="text/css">
        table.list tbody tr {
            border-bottom: none;
        }

            table.list tbody tr.data td {
                border-bottom: 1px solid #efefef;
            }

        table.list thead {
            background-color: transparent;
            color: black;
            border-bottom: 1px solid #efefef;
            font-weight: bold;
        }

            table.list thead tr td {
                padding: 5px;
                font-size: 12px;
                padding-left: 10px;
            }

        table.list td.crt {
            padding: 0px;
        }

        td.crt {
            width: 85px;
            text-align: center;
        }

            td.crt input {
                width: 65px;
                margin: 0;
            }

        dl {
            max-width: 800px;
        }

        #tAccounts .HeaderCell {
            vertical-align: bottom;
            cursor: text;
        }

        #tAccounts .defaultRow {
            background-color: #FFF;
            border-top: solid 1px #DFDFDF;
            font-size: 19px;
            font-family: segoe ui light, "Open Sans", ff-meta-web-pro, arial;
        }

        #tAccounts .dataRow .remove-account-pricing {
            width: 20px;
            height: 16px;
            text-decoration: none;
            background-image: url("/ui/images/icons/delete.png");
            background-repeat: no-repeat;
            border: none;
            background-color: transparent;
            margin-top: 4px;
            cursor: pointer;
        }

        #tAccounts .dataRow td.trash {
            width: 20px;
            padding-left: 0px;
            padding-right: 0px;
        }

        #Name {
            width: 680px;
        }

        .HeaderCell {
            min-width: 70px;
        }

        .list .x-subitem:last-child {
            padding-right: 5px;
        }

        .list .defaultRow .extra {
            font-size: 12px;
        }

        .list .dataRow input {
            box-shadow: none;
            border: solid 1px #dfdfdf;
        }

            .list .dataRow input:hover {
                border: solid 1px #afafaf;
            }

        .list .dataRow td:first-child {
            vertical-align: middle;
        }

        .list .defaultRow td:first-child {
            vertical-align: middle;
        }

        .field-validation-error {
            padding-left: 0;
            margin-left: 0;
            margin-bottom: 20px;
            color: red;
            margin-top: -4px;
            font-size: 16px;
            width: auto;
        }

        .sbs .field-validation-error {
            display: block;
        }

        span.sbs {
            font-size: 20px;
            color: #777;
            font-family: segoe ui light, "Open Sans", ff-meta-web-pro, arial;
        }

        #ch .TabContainer h1 span.rateItemName {
            display: inline;
            font-size: 20px;
            color: #888;
        }

        #CategoryId, #DefaultClassId,#LedgerAccountId {
            width: 260px;
        }
        #TimeStartAtStatusId, #TimeStopAtStatusId {
            width: 160px;
        }
        #timeBasedRate label { margin-left: 10px; margin-right: 3px}

        #timeBasedRate label:first-child { margin-left: 0}

        #contents dt {
            display: block;
        }

        #Taxable, #ExcludeFuelSurcharge, #cbEnable, #Enable {
            width: 20px;
        }

        .tiered-rate-row {
            clear: both;
        }

            .tiered-rate-row:not(:first-child) dt {
                display: none !important;
            }

        #tiered-storage-container {
            display: none;
        }

        .z-tiered-storage #tiered-storage-container {
            display: inline;
        }

        input.inherit {
            color: #888888;
        }

        .bodyTypes dl {
            float: left;
            display: inline;
            padding: 0px;
            margin-top: 2px;
            margin-bottom: 2px;
            margin-left: 0;
            margin-right: 0;
            padding-right: 2px !important;
            border-left: none;
            border-right: none;
        }

            .bodyTypes dl dd {
                padding: 0px;
                border: none;
                margin: 0px;
            }

            .bodyTypes dl dt {
                display: table-row;
                padding: 0;
                border: none;
            }

            .bodyTypes dl dd input[type=text] {
                margin: 0px;
                display: inline;
                width: 65px;
                margin-right: 10px;
                font-size: 14px;
            }

        /*elements used in fancybox for selecting one or more accounts*/
        #selectAccount h1 {
            font-size: 20px;
            color: #777;
            font-family: 'segoe ui light, "Open Sans", ff-meta-web-pro, arial';
        }

        #selectAccount p {
            font-family: ff-meta-web-pro, Georgia, helvetica;
            font-size: 14px;
            color: #000;
        }

        #selectAccount h3 {
            margin-top: 0px;
            color: #777;
            font-size: 20px;
            font-family: segoe ui light, "Open Sans", ff-meta-web-pro, arial;
        }

        #searchAccounts {
            position: relative;
        }

        #x-accounts {
            height: 400px;
            width: 655px;
            border: 1px solid #ddd;
            margin-top: 75px;
            overflow-y: scroll;
        }

            #x-accounts li {
                padding: 10px;
                cursor: pointer;
                color: #333;
                border-bottom: 1px solid #ddd;
            }

                #x-accounts li:hover {
                    color: #333;
                    background: #f5f5f5;
                }

                #x-accounts li label {
                    font-weight: normal;
                    display: block;
                }

                #x-accounts li input[type="checkbox"] + span {
                    font-family: ff-meta-web-pro;
                    font-size: 16px;
                    cursor: pointer;
                }

                #x-accounts li.groupRow span.x-group-count {
                    padding-left: 3px;
                }

                #x-accounts li.accountRow {
                    padding-left: 10px;
                    cursor: default;
                }

            #x-accounts ul.accountRows {
                font-family: ff-meta-web-pro;
                font-size: 16px;
                padding-left: 35px;
            }

                #x-accounts ul.accountRows li input[type="checkbox"] + span {
                    font-size: 16px;
                    font-family: ff-meta-web-pro;
                }

        #x-selected-message {
            padding-left: 20px;
            padding-top: 5px;
        }

        /*IE8 fix for checkboxes and jquery 1.8.11*/
        html.ie8 #x-accounts .ui-helper-hidden-accessible,
        html.ie8 #searchAccounts .ui-helper-hidden-accessible {
            position: relative !important;
        }

        /*sb=search bar (with drop down filter selections) based off of http://tympanus.net/codrops/2010/07/14/ui-elements-search-box/ */
        .sb_wrapper {
            margin: 0;
            padding: 0;
            position: absolute;
            display: block;
            top: 0px;
            left: 0px;
            width: 655px;
        }

        .sb_container {
            margin: 0;
            padding: 0;
        }

        .sb_wrapper input[type="text"],
        ul.sb_dropdown {
            border: 1px solid #fff;
            background: #fafafa;
            background: -webkit-gradient(linear, left top, left bottom, from(#f2f2f2), to(#fafafa));
            background: -moz-linear-gradient(top, #f2f2f2, #fafafa);
            border: 1px solid #ddd;
            font-size: 24px;
            font-family: "Myriad Pro", "Trebuchet MS", sans-serif;
            outline: none;
            padding: 6px 5px 6px 20px;
            text-shadow: 1px 1px 1px #fff;
            width: 555px;
            float: left;
            margin: 3px 0px;
            z-index: 100;
            box-shadow: none;
        }

        .sb_wrapper input[type="text"] {
            padding-right: 6px;
            padding-left: 6px;
            height: 43px;
        }

        ul.sb_dropdown {
            float: right;
            list-style: none;
            width: 500px;
            padding: 6px 5px;
            -moz-border-radius: 0px 0px 10px 10px;
            -webkit-border-bottom-right-radius: 10px;
            -webkit-border-bottom-left-radius: 10px;
            border-bottom-right-radius: 10px;
            border-bottom-left-radius: 10px;
        }

            ul.sb_dropdown li {
                font-size: 16px;
                line-height: 32px;
                height: 32px;
                float: left;
                width: 50%;
            }

                ul.sb_dropdown li input[type="checkbox"] + span {
                    font-family: ff-meta-web-pro;
                    font-size: 14px;
                }

                ul.sb_dropdown li.sb_filter {
                    width: 100%;
                    clear: both;
                    background: #ddd;
                    font-size: 12px;
                    text-transform: uppercase;
                    text-align: center;
                    letter-spacing: 1px;
                    color: #444;
                    height: 26px;
                    line-height: 16px;
                    padding: 5px;
                }

        div.sb_up,
        div.sb_down {
            position: relative;
            float: left;
            top: 3px;
            width: 50px;
            height: 43px;
            background-color: #f2f2f2;
            background-position: center center;
            background-repeat: no-repeat;
            z-index: 10;
            border: solid 1px #ddd;
            cursor: pointer;
        }

        div.sb_up {
            background-image: url(/ui/images/icons/up.png);
        }

        div.sb_down {
            background-image: url(/ui/images/icons/down.png);
        }

        div.sb_search {
            background: #f2f2f2 url(/ui/images/icons/search.png) no-repeat center center;
            height: 43px;
            width: 50px;
            float: left;
            border: none;
            margin: 3px 0px;
            border: solid 1px #ddd;
            cursor: default;
            outline: none;
        }

        #loadingAccountSelect {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255,255,255, 0.8);
            z-index: 9999;
        }

            #loadingAccountSelect span {
                position: fixed;
                top: 40%;
                right: 50%;
                background: url(/ui/images/ajax-loading2.gif) center center no-repeat;
                text-align: center;
                padding-top: 90px;
                background-position: 50% 50px;
                color: #000;
            }

        #time-based-button-container, #corporate-rate-button-container {
            display: inline-block;
        }

        .label-inline-block {
          display: inline-block !important;
        }
    </style>

    @Html.ValidationSummary(true)


    <dl>
        <dt class="sbs required">
            @Html.LabelFor(model => Model.Name)

            @if (Model.Predefined)
            {
                @Html.TextBoxFor(model => model.Name, new { @readOnly = true })
                @Html.HiddenFor(model => model.PredefinedId)
            }
            else
            {
                @Html.TextBoxFor(model => model.Name)
            }
            @Html.ValidationMessageFor(model => model.Name)
        </dt>
    </dl>

        <dl>
            <dt class="sbs" style="margin-top: 10px">
                
                @if (Model.PredefinedId != Extric.Towbook.PredefinedRateItem.BUILTIN_AFTER_HOUR_RELEASE_FEE)
                {
            <label for="CategoryId"><span title="Choose which category to place this rate item in. This is used for reporting purposes and when grouping items together for creating letters.">Category</span></label>
            <div id="categoryIdWrapper" style="display-inline">
                @Html.DropDownListFor(model => model.CategoryId, Model.Categories as SelectList)
            </div>
                }
                @Html.LabelFor(model => model.Taxable, new { @class= "label-inline-block" })
            @Html.EditorFor(model => model.Taxable)
                @Html.LabelFor(m => m.ExcludeFuelSurcharge, (string)ViewBag.ExcludeFuelSurcharge, new { @class = "label-inline-block" })
            @Html.EditorFor(m => m.ExcludeFuelSurcharge)

            @if ((Extric.Towbook.WebShared.WebGlobal.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.TimeBasedRates) && Model.Predefined == false) ||
               (Extric.Towbook.WebShared.WebGlobal.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.Impounds_HourlyStorage) && Model.PredefinedId == Extric.Towbook.PredefinedRateItem.BUILTIN_STORAGE_HOURLYRATE))
            {
                <div id="time-based-button-container">
                    @Html.LabelFor(m => m.TimeBasedItem)
                    @Html.EditorFor(m => m.TimeBasedItem)
                </div>
            }

            @if (ViewBag.LimitToAccountPricingOption)
            {
                <div id="corporate-rate-button-container">
                    @Html.LabelFor(m => m.InclusionType)
                    @Html.EditorFor(m => m.InclusionType)
                </div>
            }
                
                
                @if (Extric.Towbook.WebShared.WebGlobal.CurrentUser.Company.State == "MA")
                {
                    <div id="time-based-button-container">
                    @Html.LabelFor(m => m.RegulatedCharge)
                    @Html.EditorFor(m => m.RegulatedCharge)
                    </div>
                }

            </dt>
            <dt class="sbs" style="width: 260px;">
                <span id="tiered-storage-container" class="sbs">
                    Storage Pricing Options
                    @Html.LabelFor(m => m.TieredStorageRates)
                    @Html.EditorFor(m => m.TieredStorageRates)
                </span>
            </dt>
        </dl>
        if (Extric.Towbook.WebShared.WebGlobal.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.TimeBasedRates) || 
            (Extric.Towbook.WebShared.WebGlobal.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.Impounds_HourlyStorage) && Model.PredefinedId == Extric.Towbook.PredefinedRateItem.BUILTIN_STORAGE_HOURLYRATE))
        {
        <div id="timeBasedRate" style="margin-bottom:20px">
            <div id="timeStartAt" style="display: inline">
                @Html.LabelFor(m => m.TimeStartAtStatusId)
                @Html.DropDownListFor(model => model.TimeStartAtStatusId, Model.Statuses as SelectList, new { @data_width = "160" })
            </div>
            <div id="timeStopAt" style="display: inline">
                @Html.LabelFor(m => m.TimeStopAtStatusId)
                @Html.DropDownListFor(model => model.TimeStopAtStatusId, Model.Statuses as SelectList, new { @data_width = "160" })
            </div>
            <div style="display: inline">
                @Html.LabelFor(m => m.TimeRound)
                @Html.DropDownListFor(model => model.TimeRound, Model.TimeRoundValues as SelectList, new { @data_width = "160" })
            </div>
        </div>
    }

    if (Extric.Towbook.WebShared.WebGlobal.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.ClassTracking) && !Model.Predefined)
    {
        <dl id="class-tracking-container">
            <dt class="sbs" style="margin-bottom: 20px;">
                <label for="DefaultClassId"><span title="Choose a class for this rate item for tracking purposes.">Class</span></label>
                @Html.DropDownListFor(m => m.DefaultClassId, new SelectList(ViewBag.ChargeClasses, "Value", "Text"))
            </dt>
        </dl>
    }
    
if (Extric.Towbook.WebShared.WebGlobal.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.LedgerAccounts))
{
        <dl id="class-tracking-container">
            <dt class="sbs" style="margin-bottom: 20px;">
                <label for="LedgerAccountId"><span title="Choose the ledger account associated with this rate item">Ledger Account</span></label>
                @Html.DropDownListFor(m => m.LedgerAccountId, new SelectList(ViewBag.LedgerAccounts, "Value", "Text"))
                </dt>
            </dl>
 }

    <div id="regularRate">
        <div class="TabContainer">
            <span id="tPrice" class="sbs" title="Enter the default price you want to charge customers (example: cash calls). You can also specify a different rate for specific customers.">Price</span>
            <table class="list" id="tAccounts" style="width: 850px">
                <thead>
                    <tr>
                        <td style="width: 50%; padding-left: 10px;" class="HeaderCell"></td>
                        <td></td>
                        <td class="HeaderCell"><span title="Specify what you charge retail/cash calls, or that you want applied to accounts by default">Default</span></td>
                        @for (int i = 0; i < Model.ExtendedRates.Count; i++)
                        {
                            <td class="HeaderCell">@Model.ExtendedRates[i].Name</td>
                        }

                        @if (Model.PredefinedId == Extric.Towbook.PredefinedRateItem.BUILTIN_MILEAGE_UNLOADED ||
                           Model.PredefinedId == Extric.Towbook.PredefinedRateItem.BUILTIN_MILEAGE_LOADED ||
                           Model.PredefinedId == Extric.Towbook.PredefinedRateItem.BUILTIN_MILEAGE_DEADHEAD)
                        {
                            <td style="width: 10%;" class="HeaderCell">Free @(WebGlobal.CurrentUser.Company.LocaleMile + "s")</td>
                        }
                        else
                        {
                            <td style="width: 10%; display: none;" class="HeaderCell x-free-quantity">Free @(WebGlobal.CurrentUser.Company.LocaleMile + "s")</td>
                        }
                    </tr>
                </thead>
                <tbody>
                    <tr class="defaultRow">
                        <td>Standard Rate</td>
                        <td class="trash"></td>
                        <td class="crt">@Html.TextBoxFor(model => model.Cost, new { @class = "validate-money cost", id = "cost" })</td>
                        @for (int i = 0; i < Model.ExtendedRates.Count; i++)
                        {
                            <td class="crt x-subitem">
                                @Html.HiddenFor(model => model.ExtendedRates[i].BodyTypeId)
                                @Html.TextBoxFor(model => model.ExtendedRates[i].Cost, new { @class = "validate-money", @data_body_type_id = Model.ExtendedRates[i].BodyTypeId })
                            </td>
                        }
                        <td class="crt x-free-quantity-standard-row">
                            @if (Model != null && Model.FreeQuantity > 0 &&
                               (Model.PredefinedId == Extric.Towbook.PredefinedRateItem.BUILTIN_MILEAGE_UNLOADED ||
                                   Model.PredefinedId == Extric.Towbook.PredefinedRateItem.BUILTIN_MILEAGE_LOADED ||
                                   Model.PredefinedId == Extric.Towbook.PredefinedRateItem.BUILTIN_MILEAGE_DEADHEAD))
                            {
                                @Html.TextBoxFor(model => model.FreeQuantity, new { @class = "validate-number" })
                            }
                        </td>


                        </tr>
                    </tbody>
                </table>
                <a href="#selectAccount" class="button selectAccount" rel="towbook-dialog-ajax-inline" data-dialog-height="600">Choose Accounts</a>
                <div style="padding-top: 20px">
                    <strong>Tip</strong>: You can set unique prices for each of your accounts if you charge an amount different than the standard rate entered.
                </div>
            </div>
        </div>

        <div id="tieredRate" style="display: none; padding-top: 20px">
            You selected a tiered rate. Setup your tiers and their prices here.
            <dl class="tieredRates bodyTypes"></dl>
            <div style="text-align: right; max-width: 600px">
                <input type="button" value="add another tier" class="standard-button x-add-tier" />
            </div>
        </div>

        <script type='text/x-jQuery-tmpl' id='tpl-account-group'>
            <li data-group-id="${type}"><label><input type="checkbox" /><span>${name}</span></label></li>
        </script>
        <script type='text/x-jQuery-tmpl' id='tpl-group-row'>
            <li data-group-id="${type}" class="groupRow"><input type="checkbox" /><span>${name}<span class="x-group-count"></span></span></li>
            <ul data-group-id="${type}" class="accountRows"></ul>
        </script>
        <script type='text/x-jQuery-tmpl' id='tpl-account-row'>
            <li data-id="${id}" data-name="${name}" data-group="${category}" class="accountRow"><input type="checkbox" /><span>${name}</span></li>
        </script>
        <div style="display: none;">
            <div id="selectAccount" class="towbook-dialog">
                <div class="towbook-dialog-content">
                    <h3>Search Accounts</h3>
                    <div id="searchAccounts">
                        <div class="sb_wrapper">
                            <div class="sb_container">
                                <div class="sb_search"></div>
                                <input class="sb_input" type="text" />
                                <div class="sb_down"></div>
                            </div>
                            <ul class="sb_dropdown css3-checkboxes" style="display: none;"><li class="sb_filter"><span title="Narrow down the list of accounts by checking which category you want to select from.">Filter accounts by category</span></li></ul>
                        </div>
                    </div>
                    <ul id="x-accounts" class="css3-checkboxes"><li class="noResults" style="display: none; border-bottom: none;">No Results Found</li></ul>
                    <div id="x-selected-message" style="display: none"><span class="x-selected-amount"></span> accounts selected</div>
                </div>

                <div class="towbook-dialog-footer" style="padding-bottom: 40px; padding-right: 40px">
                    <ul>
                        <li><input class="button" value="Select" id="x-submit"></li>
                        <li><input class="cancel button" value="Cancel"></li>
                    </ul>
                </div>
            </div>
        </div>

        <div id="formNavigationHolder"></div>
        <ul class="formNavigation bottomStick">
            <li><input type="submit" value="Save" class="button" /></li>
            <li>@Html.ActionLink("Cancel", "Index", null,null, new { @class = "button", @style = "display: inline-block" })</li>
            @if (Model.Id > 0)
            {
                if(Model.Deleted) 
                {
                    <li class="right"><input type="button" value="Undelete" class="button" onclick="undeleteThis()" /></li>
                } 
                else 
                {
                    <li class="right"><input type="button" value="Delete" class="button delete" onclick="deleteThis()" /></li>
                }
            }
        </ul>
        <div id="loadingAccountSelect" style="display: none;"><span>Loading, please wait...</span></div>
}

