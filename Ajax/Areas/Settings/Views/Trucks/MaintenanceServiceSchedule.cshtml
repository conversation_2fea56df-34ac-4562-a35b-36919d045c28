<script type="text/x-jQuery-tmpl" id="t-service-item-row">
    <tr data-item-id="${truckServiceGroupItemId}">
        <td class="CellLeft"><a href="/ajax/settings/trucks/truckServiceGroup/${truckServiceGroupItemId}/details" data-dialog-height="350" rel="towbook-dialog-ajax">${name}</a></td>
        <td class="Cell">${frequency}</td>
    </tr>
</script>

<script type="text/x-jQuery-tmpl" id="t-service-group">
    <div class="x-group-outer" data-group-id="${id}">
        <div class="SubHeading" data-group-id="${id}" id="shGroup${id}">${name}</div>
        <div id="divGroup${id}" class="x-group-item-container" style="display: none;">
            <div>This service schedule contains the following maintenance items:
                <table class="list">
                    <thead>
                            <tr>
                                <th style="width: 80%">Service Task</th>
                                <th style="width: 20%">Frequency</th>
                            </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="CellLeft" colspan="2">There are no maintenance items associated with this group yet.</td>
                        </tr>
                    </tbody>
                    <tfoot>
                        <tr>
                            <td class="CellLeft" style="background-color: #f7f7f7" colspan="2">
                                <a class="button" href="/ajax/settings/trucks/TruckServiceGroup/${id}/new" rel="towbook-dialog-ajax" data-dialog-height="350">Add a New Item</a>
                            </td>
                        </tr>
                    </tfoot>
                </table>
                <div class="x-truck-list" style="display: none">
                    <p>In addition to setting maintenance here for all of your trucks, you can specify maintenance under an individual trucks details for just that truck.</p>
                    
                    <strong>Apply this schedule to the following trucks:</strong><br /><br />
                    <div id="divGroupTrucks${id}">

                        @foreach (Extric.Towbook.Truck truck in Extric.Towbook.WebShared.WebGlobal.CurrentUser.Company.Trucks)
                        {
                            //string chk = "";

                            <input type="checkbox" style="border: none" value="@truck.Id" id="groupTruck$(id}_ @{@truck.Id}" />
                            <label for="groupTruck$(id}_ @{@truck.Id}">@truck.Name</label><br />
                        }

                    </div>
                    <br /><br />
                    <ul class="formNavigation">
                        <li><input type="button" class="sm" value="Apply"/></li>
                    </ul>
                </div>
            </div>

            <div id="group${id}SaveMsg" style="display: none;">
                <div style="padding: 10px; padding-bottom: 50px">
                <strong>Your maintenance changes have been saved succesfully.</strong>
                </div>
            </div>
        </div>
    </div>
</script>

<script type="text/x-jQuery-tmpl" id="t-service-group-trucks">
    <input type="checkbox" style="border: none" value="$(truckId}" id="groupTruck${groupId}_ ${truckId}" />
    <label for="groupTruck${groupId}_ ${truckId}">${truckName}</label><br />
</script>

<style type="text/css">
    div.x-group-item-container {
        padding-left: 20px;
        padding-top: 10px;
        padding-bottom: 50px;
        padding-right: 20px;
    }
</style>

<script type="text/javascript">
        function ToggleFields(header, container, extraHide) {
            
            var containersToClose = $.grep($('.x-group-item-container'), function(item, index) {
                var group = '#' + $(item).attr('id');

                if(group != container && $(group).is(":visible"))
                    return item;
            });

            $(containersToClose).each(function(i, v) {
                $(v).hide();
            });
            
            $(container).toggle('slow');
            if ($(container).css('display') == 'none') {
                $(header).animate('color:#000', 3000);
            } else {
                $(header).animate('color:#555', 3000);
            }

            if (extraHide != null)
                $(extraHide).css('display', 'none');
        }

        function addScheduleItem(form, id) {
            row = $('tbContacts').insertRow($('tbContacts').rows.length);
            cell1 = row.insertCell(0);
            cell2 = row.insertCell(1);
            cell3 = row.insertCell(2);
            cell4 = row.insertCell(3);
            cell5 = row.insertCell(4);
            cell6 = row.insertCell(5);
            a = document.createElement("a");
            a.setAttribute("href", "Address.aspx?id=" + id);
            a.setAttribute("rel", "ybox|320px");
            a.appendChild(document.createTextNode(form.ctl00$Content$name));

            cell1.appendChild(a);
            cell2.appendChild(document.createTextNode(form.ctl00$Content$address));
            cell3.appendChild(document.createTextNode(form.ctl00$Content$city));
            cell4.appendChild(document.createTextNode(form.ctl00$Content$state));
            cell5.appendChild(document.createTextNode(form.ctl00$Content$zip));
            cell6.appendChild(document.createTextNode(form.ctl00$Content$phone));

            cell1.className = "CellLeft";
            cell1.style.fontWeight = 'bold';
            cell2.className = cell3.className = cell4.className = cell5.className = cell6.className = "Cell";
            initLightbox();
        }

        function saveGroup(btn, id) {
            var rootNode = $('#divGroup' + id + ':first-child');
            var truckNodes = $('#divGroupTrucks' + id + ' INPUT');
            var name = $('#divGroup' + id).data('group-name');
            var selectedTrucks = [];

            for (x = 0; x < truckNodes.length; x++) {
                if (truckNodes[x].checked)
                    selectedTrucks.push(truckNodes[x].value);
            }

            $.blockUI();

            console.log("AC: save called.  Sending data = ", JSON.stringify({ Id: id, Name: name, Trucks: selectedTrucks }));
            
            $.ajax({
                url: "/api/truckMaintenanceGroup/" + id,
                data: JSON.stringify({ Id: id, Name: name, Trucks: selectedTrucks }),
                dataType: 'json',
                type: "PUT",
                contentType: "application/json; charset=utf-8",
            }).done(function (data) {
            }).error(function (xhr, status, error) {
                alert("Error" + status + ": " + error);
            }).always(function () {
                $.unblockUI();
            });
        }

        function finishCreateNewGroupRequest(data)
        {
            console.log("AC: finished group create request with this -> ", data);
            towbook.compileTemplate('serviceGroup', $('#t-service-group'));
            towbook.compileTemplate('truckItem', $('#t-service-group-trucks'));

            var groupObj = $('#groupList');
            var outer = towbook.applyTemplate('serviceGroup', data);

            $(groupObj).append(outer);

            $('.SubHeading[data-group-id=' + data.id + ']').on('click', function(){
                ToggleFields('#shGroup'+data.id+'', '#divGroup'+data.id+'',  '#group'+data.id+'SaveMsg');
            });

            load_finish();
        }

        function finishItemCreateRequest(data)
        {
            console.log("AC: finish create request", data);
            towbook.compileTemplate('serviceItemRow', $('#t-service-item-row'));
            
            var groupObj = $('#divGroup' + data.serviceGroupId + '');
            var row = groupObj.find('table.list tbody');
            var frequency = "";

            if(data.reoccurMiles > 0)
                frequency = data.reoccurMiles + " @Extric.Towbook.WebShared.WebGlobal.CurrentUser.Company.LocaleMile.ToLower()s";
            if(data.reoccurMiles > 0 && data.reoccurDays > 0)
                frequency += " or ";
            if(data.reoccurDays > 0)
                frequency += data.reoccurDays + " days";

            var rowData = { 
                "truckServiceGroupItemId": data.id,
                "name": data.name,
                "frequency": frequency
            }

            $(row).append(towbook.applyTemplate('serviceItemRow', rowData)).flashRow();

            if(!$('.x-truck-list', groupObj).is(":visible"))
                $('.x-truck-list', groupObj).show();

            if($('tbody tr:first', groupObj).is(":visible"))
                $('tbody tr:first', groupObj).hide();

            load_finish();
        }
        
        function finishItemUpdateRequest(data)
        {
            console.log("AC: finish update request", data);
            towbook.compileTemplate('serviceItemRow', $('#t-service-item-row'));
            var groupObj = $('#divGroup' + data.serviceGroupId + '');
            var row = groupObj.find('table.list tbody tr[data-item-id=' + data.id + ']');
            var nameObj = row.find('td:first');
            var frequency ="";
            

            if(data.reoccurMiles > 0)
                frequency = data.reoccurMiles + " @Extric.Towbook.WebShared.WebGlobal.CurrentUser.Company.LocaleMile.ToLower()s";
            if(data.reoccurMiles > 0 && data.reoccurDays > 0)
                frequency += " or ";
            if(data.reoccurDays > 0)
                frequency += data.reoccurDays + " days";

            var rowData = { 
                "truckServiceGroupItemId": data.id,
                "name": data.name,
                "frequency": frequency
            }

            $(row).replaceWith(towbook.applyTemplate('serviceItemRow', rowData)).flashRow();
            load_finish();

        }

        function finishItemDeleteRequest(data)
        {
            console.log("AC: finish delete request", data);
            var groupObj = $('#divGroup' + data.serviceGroupId + '');
            var itemObj = groupObj.find('table.list').find('tr[data-item-id=' + data.truckServiceGroupItemId + ']');

            $(itemObj).remove();

            if($('tbody tr:not(:first)', groupObj).length == 0)
            {
                $('tbody tr:first', groupObj).show();
                $('.x-truck-list', groupObj).hide();
            }
        }

</script>
    
    <div id="tabMaintenance" class="TabContainer">
    <div class="Overview">
        Schedule maintenance for your trucks to remind you when to perform oil changes, lubrication, tire rotations, etc.<br /><br />
        Click on one of the names below to expand the schedule.
    </div>
    <a class="button" href="/ajax/settings/trucks/TruckServiceGroup/newScheduleGroup" rel="towbook-dialog-ajax" data-dialog-height="350" style="width: 170px; margin-bottom: 10px">Add a New Group</a>

<div id="groupList">
    @foreach (Extric.Towbook.Company.TruckServiceGroup group in Extric.Towbook.Company.TruckServiceGroup.GetByCompany(Extric.Towbook.WebShared.WebGlobal.CurrentUser.Company))
    {   
        <div class="x-group-outer" data-group-id="@group.Id">    
            <div class="SubHeading" data-group-id="@group.Id" id="shGroup@{@group.Id}" onclick="ToggleFields('#shGroup@{@group.Id}', '#divGroup@{@group.Id}', '#group@{@group.Id}SaveMsg');">
                @group.Name
            </div>
            <div id="divGroup@{@group.Id}" data-group-name="@group.Name" class="x-group-item-container" style="display: @(Context.Request.Query["truckGroupId"] == group.Id.ToString() ? "display" : "none")">
                <div>
                    This service schedule contains the following maintenance items:        
                    <table class="list">
                        <thead>
                            <tr>
                                <th style="width: 80%">Service Task</th>
                                <th style="width: 20%">Frequency</th>
                            </tr>
                        </thead>
                        <tbody>
                            @if (group.Items == null)
                            {
                                <tr><td class="CellLeft" colspan="2">There are no maintenance items associated with this group yet.</td></tr>
                            }
                            else
                            {
                                <tr style="display:none"><td class="CellLeft" colspan="2">There are no maintenance items associated with this group yet.</td></tr>
                            }

                            @if (group.Items != null)
                            {
                                foreach (Extric.Towbook.Company.TruckServiceGroupItem sgi in group.Items)
                                {                    
                                    <tr data-item-id="@sgi.Id">
                                        <td class="CellLeft">
                                            <a href="/ajax/settings/trucks/truckServiceGroup/@sgi.Id/details" rel="towbook-dialog-ajax" data-dialog-height="350">@sgi.Name</a>
                                        </td>
                                        <td class="Cell">
                                            @if (sgi.ReoccurMiles > 0)
                                            {
                                                @Html.Raw(sgi.ReoccurMiles + " " + Extric.Towbook.WebShared.WebGlobal.CurrentUser.Company.LocaleMile.ToLower() + "s");
                                            }                                           

                                            @if (sgi.ReoccurMiles > 0 && sgi.ReoccurDays > 0)
                                            {
                                                @Html.Raw(" or ");
                                            }

                                            @if (sgi.ReoccurDays > 0)
                                            {
                                                @Html.Raw(sgi.ReoccurDays + " days");
                                            }
                                        </td>
                                    </tr>                    
                                }
                            }
                            
                    
                        </tbody>
                        <tfoot>
                            <tr>
                                <td class="CellLeft" style="background-color: #f7f7f7" colspan="2">
                                    <a class="button" href="/ajax/settings/trucks/TruckServiceGroup/@group.Id/new" rel="towbook-dialog-ajax" data-dialog-height="350">Add a New Item</a>
                                </td>
                            </tr>
                        </tfoot>
                    </table>

                    <div class="x-truck-list" @(group.Items == null ? "style=display:none" : "")>

                 
                        <p>In addition to setting maintenance here for all of your trucks, you can specify maintenance under an individual trucks details for just that truck.</p>
                    
                        <strong>Apply this schedule to the following trucks:</strong><br /><br />
                        

                        
                        <div id="divGroupTrucks@{@group.Id}" >
                        @foreach (Extric.Towbook.Truck truck in Extric.Towbook.WebShared.WebGlobal.CurrentUser.Company.Trucks)
                        {
                            string chk = "";

                            if (group.Trucks.Contains(truck))
                            {
                                chk = "checked ";
                            }                        
                        
                            <input type="checkbox" @chk style="border: none" value="@truck.Id" id="groupTruck@{@group.Id}_ @{@truck.Id}" />
                            <label for="groupTruck@{@group.Id}_ @{@truck.Id}">@truck.Name</label><br />
                        }
                        </div>
                    
                        <br /><br />
                        <ul class="formNavigation">
                            <li><input type="button" class="sm" value="Apply" onclick="saveGroup(this, @{@group.Id})"/></li>
                        </ul>

                    </div>
    
                </div>
            </div>
        
            <div id="group@{@group.Id}SaveMsg" style="display: none;">
                <div style="padding: 10px; padding-bottom: 50px">
                <strong>Your maintenance changes have been saved succesfully.</strong>
            </div>
        </div>
        </div>
    }
        
    </div>
</div>




