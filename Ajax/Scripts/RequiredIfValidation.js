jQuery.validator.unobtrusive.adapters.add
    ("requiredif", ['dependencyproperty'],
    function (options) {
        options.rules['requiredif'] = { other: options.params.other,
            dependencyproperty: options.params.dependencyproperty
        };
        options.messages['requiredif'] = options.message;
    }
);

jQuery.validator.addMethod("requiredif", function (value, element, params) {

    if (value != "")
        return true;

    var passwordValidation;

    if (params.dependencyproperty) {
        if ($('#' + params.dependencyproperty)) {
            passwordValidation = $('#' + params.dependencyproperty).val();
        }
    }
        
    if (passwordValidation == "True") {
        return false;
    }

    return true;
}); 