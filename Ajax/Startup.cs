using System;
using System.Globalization;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Net.Http;
using System.Security.Cryptography.X509Certificates;
using Extric.Towbook;
using Extric.Towbook.Configuration;
using Extric.Towbook.Web;
using Extric.Towbook.WebShared;
using Extric.Towbook.WebWrapper;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.ResponseCompression;
using Microsoft.AspNetCore.Server.Kestrel.Core;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.FileProviders;
using Microsoft.Extensions.Hosting;
using Newtonsoft.Json;
using NLog;

namespace Ajax
{
    public class Startup
    {
        public IConfiguration Configuration { get; }

        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
            Configuration.ConfigureHelper();
        }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            Configuration.GetSection("Quickbooks").Bind(new QuickbooksConfiguration());
            Configuration.GetSection("DataProtection").Bind(new DataProtectionConfiguration());

            services.ConfigureCore();

            HttpContextFactory.Instance = new HttpContextNet5();
            
            //single instance of HttpClient
            services.AddSingleton(config =>
            {
                return new HttpClient(
                    new SocketsHttpHandler
                    {
                        PooledConnectionLifetime = TimeSpan.FromMinutes(5),
                        PooledConnectionIdleTimeout = TimeSpan.FromSeconds(30)
                    }
                );
            });
            
            services.AddDataProtection()
                .SetApplicationName("towbook")
                .PersistKeysToStackExchangeRedis(Core.GetRedisConnection())
                .ProtectKeysWithCertificate(
                    X509Certificate2.CreateFromPem(
                        DataProtectionConfiguration.Certificate, DataProtectionConfiguration.PrivateKey));
            
            //init the items request context
            services.AddScoped<IRequestContext, NullSafeConcurrentDictionary>();

            services.AddAntiforgery(options =>
            {
                options.FormFieldName = "RequestVerificationToken";
                options.HeaderName = "RequestVerificationToken";
                //    options.SuppressXFrameOptionsHeader = false;
                options.Cookie.Path = "/";
            });
            services.AddControllersWithViews(ConfigureMvcOptions)
                // Newtonsoft.Json is added for compatibility reasons
                // The recommended approach is to use System.Text.Json for serialization
                // Visit the following link for more guidance about moving away from Newtonsoft.Json to System.Text.Json
                // https://docs.microsoft.com/dotnet/standard/serialization/system-text-json-migrate-from-newtonsoft-how-to
                .AddNewtonsoftJson(options =>
                {
                    options.UseMemberCasing();
                    // Required for master behavior, json objects needs to convert empty string to null
                    options.SerializerSettings.Converters.Add(new EmptyStringToNullJsonConverter());
                });

            services.AddControllers(options => options.Filters.Add(new HttpResponseExceptionFilter()))
                .ConfigureApplicationPartManager(apm =>
                {
                    var originals = apm.FeatureProviders.ToList().OfType<Microsoft.AspNetCore.Mvc.Controllers.ControllerFeatureProvider>();
                    foreach (var original in originals)
                    {
                        apm.FeatureProviders.Remove(original);
                    }
                    apm.FeatureProviders.Add(new AjaxControllerFeatureProvider());
                });

            services.AddHttpContextAccessor();

            // TODO: [CHECK] Set up AllowSynchronousIO to True for HttpResponseException with JsonMediaTypeFormatter (Kestrel and IIS Server)
            services.Configure<KestrelServerOptions>(options =>
            {
                options.AllowSynchronousIO = true;
            });
            services.Configure<IISServerOptions>(options =>
            {
                options.AllowSynchronousIO = true;
            });
            services.Configure<GzipCompressionProviderOptions>(options =>
            {
                options.Level = CompressionLevel.Optimal;
            });
            services.AddResponseCompression(options =>
            {
                options.EnableForHttps = true;
                options.Providers.Add<GzipCompressionProvider>();
            });

            services.Configure<FormOptions>(options =>
            {
                // Maximum default number of form values allowed is 1024.  Increased to 4000.
                options.ValueCountLimit = 4000;
            });
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env, IHostApplicationLifetime appLifetime)
        {
            app.ConfigureHelper();
            ILogger logger = LogManager.GetCurrentClassLogger();

            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }

            app.UsePathBase("/ajax");

            app.Use(async (httpContext, next) =>
            {
                httpContext.Response.Headers.CacheControl = "no-cache, no-store";
                await next();
            });

            app.ConfigureExceptionMiddleware(appLifetime);
            app.ConfigureEndAppMiddleware(logger);

            app.UseRouting();
            app.UseAuthorization();

            app.UseResponseCaching();

            app.UseAjaxAuthentication();

            app.UseBeginRequest();

            app.UseRequestLocalization();

            app.UseResponseCompression();

            app.UseEndpoints(endpoints =>
            {
                // Dispatch
                /*
                 context.MapRoute(
                "Dispatch_none",
                "dispatch/{action}",
                new { controller = "Dispatch" }
                );
                */

                endpoints.MapAreaControllerRoute(
                    name: "Health_default",
                    areaName: "Health",
                    pattern: "Health/{action:alpha}",
                    defaults: new { controller = "Health", action = "Index" });

                endpoints.MapAreaControllerRoute(
                    name: "Dispatch_none",
                    areaName: "Dispatch",
                    pattern: "dispatch/{action:alpha}",
                    defaults: new { controller = "Dispatch" });


                endpoints.MapAreaControllerRoute(
                    name: "Dispatch_Editor",
                    areaName: "Dispatch",
                    pattern: "dispatchEditor/{id?}",
                    defaults: new { controller = "DispatchEditor", action = "Index" });

                /*context.MapRoute(
                "Dispatch_default",
                "dispatch/{id}/{action}/{resourceId}",
                new { controller = "Dispatch", action = "Index", resourceId = UrlParameter.Optional }
                );
                */
                endpoints.MapAreaControllerRoute(
                    name: "Dispatch_default",
                    areaName: "Dispatch",
                    pattern: "dispatch/{id}/{action:alpha}/{resourceId?}",
                    defaults: new { controller = "Dispatch", action = "Index" });

                // Settings

                endpoints.MapAreaControllerRoute(
                    name: "Settings_controller_action_main",
                    areaName: "Settings",
                    pattern: "Settings/{controller:alpha}",
                    defaults: new { routeName = "Settings_controller_action_main", action = "Index" });

                /*context.MapRoute("Settings_actions_any", "Settings/{controller}/{action}", 
                new { action = "Index", controller = "Home" });*/

                endpoints.MapAreaControllerRoute(
                    name: "Settings_actions_any",
                    areaName: "Settings",
                    pattern: "Settings/{controller:alpha}/{action:alpha}",
                    defaults: new { routeName = "Settings_actions_any", action = "Index", controller = "Home" });

                /*
                context.MapRoute("Settings_actions_id_only", "Settings/{controller}/{id}",
                new { action = "Details" },
                new { id = new RestStyleConstraint() });
                */

                endpoints.MapAreaControllerRoute(
                    name: "Settings_actions_id_only",
                    areaName: "Settings",
                    pattern: "Settings/{controller:alpha}/{id:int}",
                    defaults: new { routeName = "Settings_actions_id_only", action = "Details" });

                /*context.MapRoute("Settings_actions_id", "Settings/{controller}/{id}/{action}",
                new { action = "Details" },
                new { id = new RestStyleConstraint() });*/

                endpoints.MapAreaControllerRoute(
                    name: "Settings_actions_id",
                    areaName: "Settings",
                    pattern: "Settings/{controller:alpha}/{id:int}/{action:alpha}",
                    defaults: new { routeName = "Settings_actions_id", action = "Details" });

                /*context.MapRoute("Settings_actions_any_dispatching", "Settings/{folder}/{controller}/{action}", 
                new { action = "Index" });*/

                endpoints.MapAreaControllerRoute(
                    name: "Settings_actions_any_dispatching",
                    areaName: "Settings",
                    pattern: "Settings/{folder}/{controller:alpha}/{action}",
                    defaults: new { routeName = "Settings_actions_any_dispatching", action = "Index" });

                /*context.MapRoute("Settings_actions_id_only_dispatching", "Settings/{folder}/{controller}/{id}",
                new { action = "Details" },
                new { id = new RestStyleConstraint() });*/

                endpoints.MapAreaControllerRoute(
                    name: "Settings_actions_id_only_dispatching",
                    areaName: "Settings",
                    pattern: "Settings/{folder:alpha}/{controller:alpha}/{id:int}",
                    defaults: new { routeName = "Settings_actions_id_only_dispatching", action = "Details" });

                /*context.MapRoute("Settings_actions_dispatching", "Settings/{folder}/{controller}/{id}/{action}",
                new { action = "Details" },
                new { id = new RestStyleConstraint() }); 
                */

                endpoints.MapAreaControllerRoute(
                    name: "Settings_actions_dispatching",
                    areaName: "Settings",
                    pattern: "Settings/{folder:alpha}/{controller:alpha}/{id:int}/{action:alpha}",
                    defaults: new { routeName = "Settings_actions_dispatching", action = "Details" });

                // Default

                endpoints.MapControllerRoute(
                    name: "default",
                    pattern: "{controller=Home}/{action=Index}/{id?}");
            });

            //app.Use((context, next) =>
            //{
            //    context.Request.PathBase = "/ajax";
            //    return next();
            //});

            // This will add "Libs" as another valid static content location
            app.UseStaticFiles(new StaticFileOptions()
            {
                FileProvider = new PhysicalFileProvider(
                     Path.Combine(Directory.GetCurrentDirectory(), @"Scripts")),
                RequestPath = new PathString("/Scripts")
            });
            app.UseStaticFiles(new StaticFileOptions()
            {
                FileProvider = new PhysicalFileProvider(
                     Path.Combine(Directory.GetCurrentDirectory(), @"Styles")),
                RequestPath = new PathString("/Styles")
            });

            appLifetime.ApplicationStopped.Register(async() =>
            {
                await Extric.Towbook.Integration.MotorClubs.Services.ServiceBusHelper.Cleanup();
                await Extric.Towbook.EventNotifications.ENServiceBusHelper.Cleanup();
                Console.WriteLine("Turning off the Log Manager");
                LogManager.Shutdown();
            });


            var cultureInfo = new CultureInfo("en-US");
            cultureInfo.NumberFormat.CurrencySymbol = "$";

            CultureInfo.DefaultThreadCurrentCulture = cultureInfo;
            CultureInfo.DefaultThreadCurrentUICulture = cultureInfo;

            //setup static accessor 
            IHttpContextAccessor httpContextAccessor = app.ApplicationServices.GetRequiredService<IHttpContextAccessor>();
            IWebHostEnvironment webHostEnvironment = app.ApplicationServices.GetRequiredService<IWebHostEnvironment>();

            Extric.Towbook.Web.HttpContext.Configure(httpContextAccessor, webHostEnvironment, app.ApplicationServices);

            MvcApplication.Application_Start();

            //Extric.Towbook.Web.HttpContextFactory.Instance = new HttpContextNet5();

        }

        private void ConfigureMvcOptions(MvcOptions mvcOptions)
        {
            //mvcOptions.Filters.Add(new AutoValidateAntiforgeryTokenAttribute());
            //mvcOptions.Filters.Add(new Ajax.Filters.ValidateAntiForgeryHeaderAttribute());
        }

        public class EmptyStringToNullJsonConverter : JsonConverter
        {
            public override bool CanRead => true;
            public override bool CanWrite => false;

            public override bool CanConvert(Type objectType)
            {
                return typeof(string) == objectType;
            }

            public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
            {
                string value = (string)reader.Value;
                return string.IsNullOrWhiteSpace(value) ? null : value.Trim();
            }

            public override void WriteJson(JsonWriter writer, object value, JsonSerializer serializer)
            {
                throw new NotImplementedException("Unnecessary because CanWrite is false. The type will skip the converter.");
            }
        }
    }
}
