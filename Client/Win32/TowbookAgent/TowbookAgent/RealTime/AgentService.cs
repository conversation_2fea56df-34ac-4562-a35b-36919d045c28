using Extric.Towbook.AgentClient.ApiClient;
using Extric.Towbook.AgentClient.ApiClient.Model;
using Extric.Towbook.AgentClient.Connectors.QuickBooks.Models;
using Extric.Towbook.AgentClient.Utility;
using Extric.Towbook.Utility;
using Newtonsoft.Json;
using PusherClient;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Extric.Towbook.AgentClient.RealTime
{
    public class AgentService
    {
        private Pusher _pusher;
        private Channel _chatChannel;
        private TowbookClient _client;

        public string Password
        {
            get { return _client.Password; }
        }

        public string Username
        {
            get { return _client.Username; }
        }

        public AgentService(TowbookClient client)
        {
            _client = client;
        }

        public bool HasQuickBooks()
        {
            var hasQuickBooks = _client.EnabledFeatures.Where(o => o.Name == "QuickBooks").Any();

            return hasQuickBooks;
        }

        public async Task<bool> PusherInit()
        {
            if (!HasQuickBooks())
                return false;

            _pusher = new Pusher("00d0fb70749a0a4fd6f9", new PusherOptions()
            {
                Authorizer = new TowbookPusherAuthorizer(_client)
            });

            _pusher.ConnectionStateChanged += _pusher_ConnectionStateChanged;
            _pusher.Error += _pusher_Error;

            // Setup private channel
            _chatChannel = await _pusher.SubscribeAsync($"private-agent-{_client.SessionId}-push", _chatChannel_Subscribed);

            // Inline binding!
            _chatChannel.Bind("call_update", (dynamic data) =>
            {
                OnAddLine("[" + data.name + "] " + data.message);
            });

            JsonSerializerSettings jss = new JsonSerializerSettings();
            jss.Converters.Add(new ForceUtcJsonConverter());

            _chatChannel.BindAll((string channel, dynamic data) =>
            {
                SyncEventContainer ex1 = JsonConvert.DeserializeObject<SyncEventContainer>(
                    JsonConvert.SerializeObject(data, jss),
                    jss);
                ex1.Service = this;

                Program.quickbooksQueue.EnqueueTask(ex1);
            });

            await _pusher.ConnectAsync();

            return true;
        }

        internal void Notify(string v)
        {
            OnAddLine(v);
        }

        private void _chatChannel_Subscribed(object sender)
        {
            var channel = sender as PrivateChannel;
            OnAddLine("Subscribed to " + channel.Name);
        }

        private void _pusher_Error(object sender, PusherException error)
        {
            OnAddLine(JsonConvert.SerializeObject(error, Formatting.Indented));
        }

        private void _pusher_ConnectionStateChanged(object sender, ConnectionState state)
        {
            ConnectionStateChanged(sender, state);
        }

        public event ConnectionStateChangedEventHandler ConnectionStateChanged;

        public event DebugEventHandler AddLine;

        protected virtual void OnAddLine(string msg)
        {
            if (AddLine != null)
                AddLine(this, new DebugEventArgs(msg));
        }

        protected virtual void OnConnectionStateChanged(EventArgs e, ConnectionState state)
        {
            if (ConnectionStateChanged != null)
                ConnectionStateChanged(this, state);
        }
        
        public delegate void DebugEventHandler(object sender, DebugEventArgs e);

        public class DebugEventArgs : EventArgs
        {
            public string Message { get; private set; }
            public DebugEventArgs(string message)
            {
                Message = message;
            }
        }

        internal void RecordResponse(SyncEventContainer sec, string xml)
        {
            _client.RecordSyncEventResponse(sec.Event.Id, "response", xml);
            Notify($"Reported result back to Towbook server ({xml.Length} bytes)");
        }



        internal void RecordQuickBooksFilename(string filename)
        {
            _client.RecordSyncEventResponse(-99, "quickBooksFilename", filename);
        }

        internal void SyncQueuedEvents()
        {
            var i = 0;

            foreach (var x in _client.GetEvents())
            {
                x.Service = this;
                Program.quickbooksQueue.EnqueueTask(x);
                i++;
            }

            Notify("Queued " + i + " events that were requested when the Agent wasn't running.");
        }

        public async void RecordStatus(SyncEventContainer sec, SyncEventStatus syncEventStatus, string txnId = null, string editSequence = null)
        {
            await Task.Run(() => {
                sec.Service._client.RecordStatus(sec.Event.Id, sec.Event.Status, syncEventStatus, txnId, editSequence);
            });
        }

        public async void SendCustomer(IEnumerable<CustomerModel> models)
        {
            await Task.Run(() =>
            {
                _client.Send(models);
            });
        }

        public async void SendItems(IEnumerable<ItemModel> items)
        {
            await Task.Run(() => _client.Send(items));
        }

        public async void SendClasses(IEnumerable<ClassModel> classes)
        {
            await Task.Run(() => _client.Send(classes));
        }

        public async void SendTaxCodes(IEnumerable<SalesTaxCodeModel> taxCodes)
        {
            await Task.Run(() => _client.Send(taxCodes));
        }

        internal async Task Disconnect()
        {
            if (_pusher != null && _pusher.State == ConnectionState.Connected)
                await _pusher.DisconnectAsync();
        }

        public async void SendTaxItems(IEnumerable<ItemSalesTaxModel> taxItems)
        {
            await Task.Run(() => _client.Send(taxItems));
        }

        public async void SendPaymentMethods(IEnumerable<PaymentMethodModel> paymentMethods)
        {
            await Task.Run(() => _client.Send(paymentMethods));
        }

        internal async void SendAccounts(IEnumerable<AccountModel> accounts)
        {
            await Task.Run(() => _client.Send(accounts));
        }

        private SyncEventContainer CreateLocalSyncEvent(SyncEventType type, string key, string data)
        {
            SyncEventContainer ex1 = new SyncEventContainer();
            ex1.Service = this;
            ex1.Event = new SyncEvent();
            ex1.Event.Type = type;
            ex1.Request = new SyncEventRequestData[]
            {
                new SyncEventRequestData() { Data = data, Key = key }
            };

            return ex1;
        }

        internal void SyncCustomers(DateTime? from = null)
        {
            var ex1 = CreateLocalSyncEvent(SyncEventType.Customer, "local", 
                $"sync_everything|{from.ToJson()}");
            
            Program.quickbooksQueue.EnqueueTask(ex1);
        }

        internal void SyncItems(DateTime? from = null)
        {
            var ex1 = CreateLocalSyncEvent(SyncEventType.Item, "local",
                $"sync_everything|{from.ToJson()}");

            Program.quickbooksQueue.EnqueueTask(ex1);
        }

        internal void SyncPaymentMethods(DateTime? from = null)
        {
            var ex1 = CreateLocalSyncEvent(SyncEventType.PaymentMethod, "local",
                $"sync_everything|{from.ToJson()}");

            Program.quickbooksQueue.EnqueueTask(ex1);
        }

        internal void SyncTaxCodes(DateTime? from = null)
        {
            var ex1 = CreateLocalSyncEvent(SyncEventType.TaxCode, "local",
                $"sync_everything|{from.ToJson()}");

            Program.quickbooksQueue.EnqueueTask(ex1);
        }

        internal void SyncTaxItems(DateTime? from = null)
        {
            var ex1 = CreateLocalSyncEvent(SyncEventType.TaxItem, "local",
                $"sync_everything|{from.ToJson()}");

            Program.quickbooksQueue.EnqueueTask(ex1);
        }

        internal void SyncClasses(DateTime? from = null)
        {
            var ex1 = CreateLocalSyncEvent(SyncEventType.Class, "local",
                $"sync_everything|{from.ToJson()}");
            Program.quickbooksQueue.EnqueueTask(ex1);
        }

        internal void SyncAccounts(DateTime? from = null)
        {
            var ex1 = CreateLocalSyncEvent(SyncEventType.Account, "local",
                $"sync_everything|{from.ToJson()}");

            Program.quickbooksQueue.EnqueueTask(ex1);
        }
    }
}
