/*
 * Key Update Procedures
 *
 */

IF OBJECT_ID('Integration.[ProviderCompanyKeysUpdateById]') IS NULL EXEC('CREATE PROCEDURE Integration.[ProviderCompanyKeysUpdateById]  AS SET NOCOUNT ON;')
GO
ALTER PROCEDURE [Integration].[ProviderCompanyKeysUpdateById] (
    @ProviderCompanyKeyId   INT,
    @IntegrationProviderId	INT, 
    @Name                   VARCHAR (50) 
)
AS
	UPDATE ProviderCompanyKeys 
		SET		IntegrationProviderId=@IntegrationProviderId, 
				Name=@Name
		WHERE ProviderCompanyKeyId=@ProviderCompanyKeyId
GO

IF OBJECT_ID('Integration.[ProviderDispatchEntryKeysUpdateById]') IS NULL EXEC('CREATE PROCEDURE Integration.[ProviderDispatchEntryKeysUpdateById]  AS SET NOCOUNT ON;')
GO
ALTER PROCEDURE [Integration].[ProviderDispatchEntryKeysUpdateById] (
    @ProviderDispatchEntryKeyId INT, 
    @IntegrationProviderId       INT,
    @Name                        VARCHAR (50)
)
AS
	UPDATE ProviderDispatchEntryKeys 
		SET		IntegrationProviderId=@IntegrationProviderId, 
				Name=@Name
		WHERE ProviderDispatchEntryKeyId=@ProviderDispatchEntryKeyId
GO

IF OBJECT_ID('Integration.[ProviderRateItemKeysUpdateById]') IS NULL EXEC('CREATE PROCEDURE Integration.[ProviderRateItemKeysUpdateById]  AS SET NOCOUNT ON;')
GO
ALTER PROCEDURE [Integration].[ProviderRateItemKeysUpdateById] (
    @ProviderRateItemKeyId INT, 
    @IntegrationProviderId INT, 
    @Name                   VARCHAR (50)
)
AS
	UPDATE ProviderRateItemKeys 
		SET		IntegrationProviderId=@IntegrationProviderId, 
				Name=@Name
		WHERE ProviderRateItemKeyId=@ProviderRateItemKeyId
GO

IF OBJECT_ID('Integration.[ProviderAccountKeysUpdateById]') IS NULL EXEC('CREATE PROCEDURE Integration.[ProviderAccountKeysUpdateById]  AS SET NOCOUNT ON;')
GO
ALTER PROCEDURE [Integration].[ProviderAccountKeysUpdateById] (
    @ProviderAccountKeyId   INT,
    @IntegrationProviderId	INT, 
    @Name                   VARCHAR (50) 
)
AS
	UPDATE ProviderAccountKeys 
		SET		IntegrationProviderId=@IntegrationProviderId, 
				Name=@Name
		WHERE ProviderAccountKeyId=@ProviderAccountKeyId
GO

IF OBJECT_ID('Integration.[ProviderDriverKeysUpdateById]') IS NULL EXEC('CREATE PROCEDURE Integration.[ProviderDriverKeysUpdateById]  AS SET NOCOUNT ON;')
GO
ALTER PROCEDURE [Integration].[ProviderDriverKeysUpdateById] (
	@ProviderDriverKeyId   INT,
    @IntegrationProviderId	 	INT,
    @Name						VARCHAR (50)
)
AS
	UPDATE ProviderDriverKeys 
		SET		IntegrationProviderId=@IntegrationProviderId, 
				Name=@Name
		WHERE ProviderDriverKeyId=@ProviderDriverKeyId
GO

IF OBJECT_ID('Integration.[ProviderTruckKeysUpdateById]') IS NULL EXEC('CREATE PROCEDURE Integration.[ProviderTruckKeysUpdateById]  AS SET NOCOUNT ON;')
GO
ALTER PROCEDURE [Integration].[ProviderTruckKeysUpdateById] (
    @ProviderTruckKeyId		INT,
    @IntegrationProviderId	INT, 
    @Name					VARCHAR (50)
)
AS
	UPDATE ProviderTruckKeys 
		SET		IntegrationProviderId=@IntegrationProviderId, 
				Name=@Name
		WHERE ProviderTruckKeyId=@ProviderTruckKeyId
GO


IF OBJECT_ID('Integration.[ProviderUserKeysUpdateById]') IS NULL EXEC('CREATE PROCEDURE Integration.[ProviderUserKeysUpdateById]  AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE Integration.[ProviderUserKeysUpdateById] (
    @ProviderUserKeyId      INT,
    @IntegrationProviderId  INT, 
    @Name                   VARCHAR (50)
) 
AS
	UPDATE ProviderUserKeys 
		SET		IntegrationProviderId=@IntegrationProviderId, 
				Name=@Name
		WHERE ProviderUserKeyId=@ProviderUserKeyId
GO

/*
 * Key Value Update Procedures
 *
 */

IF OBJECT_ID('Integration.[ProviderUserKeyValuesUpdateById]') IS NULL EXEC('CREATE PROCEDURE [Integration].[ProviderUserKeyValuesUpdateById] AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE [Integration].[ProviderUserKeyValuesUpdateById] (
    @ProviderUserKeyValueId  INT, 
    @UserId                  INT,
    @ProviderUserKeyId       INT,
    @Value                   VARCHAR (256) 
)
AS
	UPDATE ProviderUserKeyValues
		SET		UserId=@UserId,
				ProviderUserKeyId=@ProviderUserKeyId,
				Value=@Value
		WHERE ProviderUserKeyValueId=@ProviderUserKeyValueId
GO

IF OBJECT_ID('Integration.[ProviderDriverKeyValuesUpdateById]') IS NULL EXEC('CREATE PROCEDURE [Integration].[ProviderDriverKeyValuesUpdateById] AS SET NOCOUNT ON;')
GO
ALTER PROCEDURE [Integration].[ProviderDriverKeyValuesUpdateById] (
    @ProviderDriverKeyValueId  INT, 
    @DriverId                  INT,
    @ProviderDriverKeyId       INT,
    @Value                     VARCHAR (256) 
)
AS
	UPDATE ProviderDriverKeyValues
		SET		DriverId=@DriverId,
				ProviderDriverKeyId=@ProviderDriverKeyId,
				Value=@Value
		WHERE ProviderDriverKeyValueId=@ProviderDriverKeyValueId
GO

IF OBJECT_ID('Integration.[ProviderTruckKeyValuesUpdateById]') IS NULL EXEC('CREATE PROCEDURE [Integration].[ProviderTruckKeyValuesUpdateById] AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE [Integration].[ProviderTruckKeyValuesUpdateById] (
    @ProviderTruckKeyValueId  INT, 
    @TruckId                  INT,
    @ProviderTruckKeyId       INT,
    @Value                    VARCHAR (256) 
)
AS
	UPDATE ProviderTruckKeyValues
		SET		TruckId=@TruckId,
				ProviderTruckKeyId=@ProviderTruckKeyId,
				Value=@Value
		WHERE ProviderTruckKeyValueId=@ProviderTruckKeyValueId
GO


IF OBJECT_ID('Integration.[ProviderAccountKeyValuesUpdateById]') IS NULL EXEC('CREATE PROCEDURE [Integration].[ProviderAccountKeyValuesUpdateById] AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE [Integration].[ProviderAccountKeyValuesUpdateById] (
    @ProviderAccountKeyValueId	INT, 
    @AccountId                  INT,
    @ProviderAccountKeyId       INT,
    @Value                      VARCHAR (256)
)
AS
	UPDATE ProviderAccountKeyValues
		SET		AccountId=@AccountId,
				ProviderAccountKeyId=@ProviderAccountKeyId,
				Value=@Value
		WHERE ProviderAccountKeyValueId=@ProviderAccountKeyValueId
GO

IF OBJECT_ID('Integration.[ProviderCompanyKeyValuesUpdateById]') IS NULL EXEC('CREATE PROCEDURE [Integration].[ProviderCompanyKeyValuesUpdateById] AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE [Integration].[ProviderCompanyKeyValuesUpdateById] (
    @ProviderCompanyKeyValueId	INT, 
    @CompanyId                  INT,
    @ProviderCompanyKeyId       INT,
    @Value                      VARCHAR (256)
)
AS
	UPDATE ProviderCompanyKeyValues
		SET		CompanyId=@CompanyId,
				ProviderCompanyKeyId=@ProviderCompanyKeyId,
				Value=@Value
		WHERE ProviderCompanyKeyValueId=@ProviderCompanyKeyValueId
GO

IF OBJECT_ID('Integration.[ProviderDispatchEntryKeyValuesUpdateById]') IS NULL EXEC('CREATE PROCEDURE [Integration].[ProviderDispatchEntryKeyValuesUpdateById] AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE [Integration].[ProviderDispatchEntryKeyValuesUpdateById] (
    @ProviderDispatchEntryKeyValueId INT, 
    @DispatchEntryId                  INT,
    @ProviderDispatchEntryKeyId       INT,
    @Value                            VARCHAR (512)
)
AS
	UPDATE ProviderDispatchEntryKeyValues
		SET		DispatchEntryId=@DispatchEntryId,
				ProviderDispatchEntryKeyId=@ProviderDispatchEntryKeyId,
				Value=@Value
		WHERE ProviderDispatchEntryKeyValueId=@ProviderDispatchEntryKeyValueId
GO

IF OBJECT_ID('Integration.[ProviderRateItemKeyValuesUpdateById]') IS NULL EXEC('CREATE PROCEDURE [Integration].[ProviderRateItemKeyValuesUpdateById] AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE [Integration].[ProviderRateItemKeyValuesUpdateById] (
    @ProviderRateItemKeyValueId  INT, 
    @RateItemId                  INT,
    @ProviderRateItemKeyId       INT,
    @Value                       VARCHAR (512)
)
AS
	UPDATE ProviderRateItemKeyValues
		SET		RateItemId=@RateItemId,
				ProviderRateItemKeyId=@ProviderRateItemKeyId,
				Value=@Value
		WHERE ProviderRateItemKeyValueId=@ProviderRateItemKeyValueId
GO

IF OBJECT_ID('Integration.[ProvidersUpdateById]') IS NULL EXEC('CREATE PROCEDURE [Integration].[ProvidersUpdateById] AS SET NOCOUNT ON;')
GO
ALTER PROCEDURE [Integration].[ProvidersUpdateById] (
	@ProviderId				INT,
    @Name                   VARCHAR (50),
    @TypeId                 TINYINT
)
AS
	UPDATE	Providers
		SET		Name=@Name,
				TypeId=@TypeId
		WHERE IntegrationProviderId=@ProviderId
GO

IF OBJECT_ID('Integration.[ProviderTypesUpdateById]') IS NULL EXEC('CREATE PROCEDURE [Integration].[ProviderTypesUpdateById] AS SET NOCOUNT ON;')
GO
ALTER PROCEDURE [Integration].[ProviderTypesUpdateById] (
    @IntegrationProviderTypeId	TINYINT,
    @Name                       VARCHAR (50)
)
AS
	UPDATE Integration.ProviderTypes
		SET @Name=@Name
		WHERE IntegrationProviderTypeId=@IntegrationProviderTypeId
GO

/*Payments registration*/
IF OBJECT_ID('Integration.[InvoicePaymentKeysUpdateById]') IS NULL EXEC('CREATE PROCEDURE [Integration].[InvoicePaymentKeysUpdateById] AS SET NOCOUNT ON;')
GO
ALTER PROCEDURE [Integration].[InvoicePaymentKeysUpdateById] (
    @InvoicePaymentKeyId	INT, 
    @IntegrationProviderId	INT,
    @Name					VARCHAR (50)
)
AS
	UPDATE InvoicePaymentKeys 
		SET		IntegrationProviderId=@IntegrationProviderId, 
				Name=@Name
		WHERE InvoicePaymentKeyId=@InvoicePaymentKeyId
GO

IF OBJECT_ID('Integration.[InvoicePaymentKeyValuesUpdateById]') IS NULL EXEC('CREATE PROCEDURE [Integration].[InvoicePaymentKeyValuesUpdateById] AS SET NOCOUNT ON;')
GO
ALTER PROCEDURE [Integration].[InvoicePaymentKeyValuesUpdateById] (
    @InvoicePaymentKeyValueId	INT, 
    @DispatchEntryPaymentId		INT,
    @InvoicePaymentKeyId		INT,
    @Value						VARCHAR (512)
)
AS
	UPDATE InvoicePaymentKeyValues
		SET		DispatchEntryPaymentId=@DispatchEntryPaymentId,
				InvoicePaymentKeyId=@InvoicePaymentKeyId,
				Value=@Value
		WHERE InvoicePaymentKeyValueId=@InvoicePaymentKeyValueId
GO
/*End Payments registration*/

/*Invoices and Payments Process Queue*/
IF OBJECT_ID('Integration.[ProviderDispatchEntryProcessingQueueUpdateStatusByDispatchEntryId]') IS NULL EXEC('CREATE PROCEDURE [Integration].[ProviderDispatchEntryProcessingQueueUpdateStatusByDispatchEntryId] AS SET NOCOUNT ON;')
GO
ALTER PROCEDURE [Integration].[ProviderDispatchEntryProcessingQueueUpdateStatusByDispatchEntryId]
	@DispatchEntryId int,
	@RunAfterDate datetime = null,
	@ProcessStart datetime = null,
	@ProcessEnd datetime = null,
	@Status int = null
AS
IF @Status IS NULL
BEGIN
   UPDATE [Integration].[ProviderDispatchEntryProcessingQueue]
   SET [RunAfterDate] = @RunAfterDate
   WHERE [DispatchEntryId] = @DispatchEntryId
END
ELSE IF @Status = 1 
BEGIN
   UPDATE [Integration].[ProviderDispatchEntryProcessingQueue]
   SET [ProcessStart] = GETDATE(),
       [Status] = @Status
   WHERE [DispatchEntryId] = @DispatchEntryId
END
ELSE IF @Status = 2 OR @Status = 3
BEGIN
   UPDATE [Integration].[ProviderDispatchEntryProcessingQueue]
   SET [ProcessEnd] = @ProcessEnd,
       [Status] = @Status
   WHERE [DispatchEntryId] = @DispatchEntryId
END
GO

IF OBJECT_ID('Integration.ProviderInvoicePaymentsProcessingQueueUpdateStatusByInvoicePaymentId') IS NULL EXEC('CREATE PROCEDURE [Integration].[ProviderInvoicePaymentsProcessingQueueUpdateStatusByInvoicePaymentId] AS SET NOCOUNT ON;')
GO
ALTER PROCEDURE [Integration].[ProviderInvoicePaymentsProcessingQueueUpdateStatusByInvoicePaymentId]
	@InvoicePaymentId int,
	@ProcessStart datetime = null,
	@ProcessEnd datetime = null,
	@Status int
AS
IF @Status = 5 
BEGIN
   UPDATE [Integration].[ProviderInvoicePaymentsProcessingQueue]
   SET [ProcessStart] = GETDATE(),
       [Status] = @Status
   WHERE [InvoicePaymentId] = @InvoicePaymentId
END
ELSE IF @Status = 6 OR @Status = 7
BEGIN
   UPDATE [Integration].[ProviderInvoicePaymentsProcessingQueue]
   SET [ProcessEnd] = @ProcessEnd,
       [Status] = @Status
   WHERE [InvoicePaymentId] = @InvoicePaymentId
END
GO
/*End Invoices and Payments Process Queue*/

IF OBJECT_ID('Integration.ProviderPaymentMethodKeysUpdateById') IS NULL EXEC('CREATE PROCEDURE [Integration].[ProviderPaymentMethodKeysUpdateById] AS SET NOCOUNT ON;')
GO
ALTER PROCEDURE [Integration].[ProviderPaymentMethodKeysUpdateById] (
    @ProviderPaymentMethodKeyId   INT,
    @IntegrationProviderId	INT, 
    @Name                   VARCHAR (50) 
)
AS
	UPDATE ProviderPaymentMethodKeys 
		SET		IntegrationProviderId=@IntegrationProviderId, 
				Name=@Name
		WHERE ProviderPaymentMethodKeyId=@ProviderPaymentMethodKeyId
GO

IF OBJECT_ID('Integration.ProviderPaymentMethodKeyValuesUpdateById') IS NULL EXEC('CREATE PROCEDURE [Integration].[ProviderPaymentMethodKeyValuesUpdateById] AS SET NOCOUNT ON;')
GO
ALTER PROCEDURE [Integration].[ProviderPaymentMethodKeyValuesUpdateById] (
    @ProviderPaymentMethodKeyValueId	INT, 
    @PaymentMethodId                  INT,
    @ProviderPaymentMethodKeyId       INT,
    @Value                      VARCHAR (256)
)
AS
	UPDATE ProviderPaymentMethodKeyValues
		SET		PaymentMethodId=@PaymentMethodId,
				ProviderPaymentMethodKeyId=@ProviderPaymentMethodKeyId,
				Value=@Value
		WHERE ProviderPaymentMethodKeyValueId=@ProviderPaymentMethodKeyValueId
GO


IF OBJECT_ID('Integration.ProviderTaxRateKeysUpdateById') IS NULL EXEC('CREATE PROCEDURE [Integration].[ProviderTaxRateKeysUpdateById] AS SET NOCOUNT ON;')
GO
ALTER PROCEDURE [Integration].[ProviderTaxRateKeysUpdateById] (
    @ProviderTaxRateKeyId   INT,
    @IntegrationProviderId	INT, 
    @Name                   VARCHAR (50) 
)
AS
	UPDATE ProviderTaxRateKeys 
		SET		IntegrationProviderId=@IntegrationProviderId, 
				Name=@Name
		WHERE ProviderTaxRateKeyId=@ProviderTaxRateKeyId
GO

IF OBJECT_ID('Integration.ProviderTaxRateKeyValuesUpdateById') IS NULL EXEC('CREATE PROCEDURE [Integration].[ProviderTaxRateKeyValuesUpdateById] AS SET NOCOUNT ON;')
GO
ALTER PROCEDURE [Integration].[ProviderTaxRateKeyValuesUpdateById] (
    @ProviderTaxRateKeyValueId	INT, 
    @TaxRateId                  INT,
    @ProviderTaxRateKeyId       INT,
    @Value                      VARCHAR (256)
)
AS
	UPDATE ProviderTaxRateKeyValues
		SET		TaxRateId=@TaxRateId,
				ProviderTaxRateKeyId=@ProviderTaxRateKeyId,
				Value=@Value
		WHERE ProviderTaxRateKeyValueId=@ProviderTaxRateKeyValueId
GO