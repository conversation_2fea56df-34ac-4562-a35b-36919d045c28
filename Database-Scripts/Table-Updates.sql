 print '**** DATABASE *** ' + DB_NAME()



IF NOT EXISTS (SELECT 1 FROM sys.schemas WHERE name = 'Geo')
BEGIN
    EXEC( 'CREATE SCHEMA Geo' );
END
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='ZipCodes' AND TABLE_SCHEMA='Geo')
    CREATE TABLE Geo.ZipCodes(
	    ZipCode varchar(5) NOT NULL,
	    City varchar(50) NOT NULL,
	    State varchar(2) NULL,
	    Latitude decimal(9, 6) NULL,
	    Longitude decimal(9, 6) NULL,
        CONSTRAINT PK_ZipCodes PRIMARY KEY CLUSTERED  ( ZipCode ASC )
    )
GO

/* ------------------------------------------------------------------------------------------------------------------
 * VehicleBodyTypes
 * ------------------------------------------------------------------------------------------------------------------*/
 
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='VehicleBodyTypes' AND COLUMN_NAME = 'CompanyId')
    ALTER TABLE VehicleBodyTypes ADD CompanyId INT NULL

IF NOT EXISTS(SELECT * from INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS WHERE CONSTRAINT_NAME='FK_VehicleBodyTypes_Companies')
    ALTER TABLE VehicleBodyTypes  ADD CONSTRAINT FK_VehicleBodyTypes_Companies FOREIGN KEY (CompanyId) REFERENCES Companies(CompanyId)

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='VehicleBodyTypeAliases')
    CREATE TABLE [dbo].[VehicleBodyTypeAliases] (
        [VehicleBodyTypeAliasId]	INT IDENTITY (1, 1) NOT NULL,
        [BodyTypeId]			TINYINT NOT NULL,
        [CompanyId]			INT NOT NULL,
        [Name]				VARCHAR(32),
        [Hide]				BIT DEFAULT(0)
        
        CONSTRAINT [PK_VehicleBodyTypeAliases] PRIMARY KEY CLUSTERED ([VehicleBodyTypeAliasId] ASC),
        CONSTRAINT [FK_VehicleBodyTypeAliases_VehicleBodyTypes] FOREIGN KEY ([BodyTypeId]) REFERENCES [dbo].[VehicleBodyTypes] ([BodyTypeId]),    
        CONSTRAINT [FK_VehicleBodyTypeAliases_Companies] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].[Companies] ([CompanyId])
    );

/* 7/22/2013 - add CategoryId to Truck Expense Items */

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='TruckExpenseItems' AND COLUMN_NAME = 'CategoryId')
    ALTER TABLE TruckExpenseItems ADD CategoryId INT NULL

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='TruckExpenseItems' AND COLUMN_NAME = 'Units')
    ALTER TABLE TruckExpenseItems ADD Units float NULL

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='TruckExpenseItems' AND COLUMN_NAME = 'Odometer')
    ALTER TABLE TruckExpenseItems ADD Odometer int NULL

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='TruckExpenseItems' AND COLUMN_NAME = 'Verified')
    ALTER TABLE TruckExpenseItems ADD Verified bit NULL
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='TruckExpenseItems' AND COLUMN_NAME = 'VerifiedUserId')
    ALTER TABLE TruckExpenseItems ADD VerifiedUserId int NULL
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='TruckExpenseItems' AND COLUMN_NAME = 'VerifiedDate')
    ALTER TABLE TruckExpenseItems ADD VerifiedDate datetime NULL
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='TruckExpenseItems' AND COLUMN_NAME = 'PaymentReferenceNumber')
    ALTER TABLE TruckExpenseItems ADD PaymentReferenceNumber varchar(50)

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='RateItems' AND COLUMN_NAME = 'DiscountExempt')

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='TruckExpenseCategories')
    CREATE TABLE [dbo].[TruckExpenseCategories] (
        [TruckExpenseCategoryId]		INT IDENTITY (1, 1) NOT NULL,
        [CompanyId]						INT,
        [Name]							VARCHAR(50) NOT NULL,
        [Description]					VARCHAR(500)
    )
GO

IF (NOT EXISTS(SELECT RateItemPredefinedId FROM RateItemsPredefined WHERE RateItemPredefinedId=6))
INSERT INTO RateItemsPredefined (RateItemPredefinedId, name, locked, hidden)
VALUES(6, 'Discount', 0, 1)

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountRateItemsExtended' AND COLUMN_NAME = 'FreeQuantity')
    BEGIN
    ALTER TABLE AccountRateItemsExtended ADD FreeQuantity int NULL
        END
ELSE
    BEGIN
        ALTER TABLE AccountRateItemsExtended ALTER COLUMN FreeQuantity int NULL
    END
GO

IF(NOT EXISTS(SELECT * FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS WHERE CONSTRAINT_NAME = 'FK_AccountRateItemsExtended_Accounts'))
ALTER TABLE [dbo].[AccountRateItemsExtended] ADD CONSTRAINT [FK_AccountRateItemsExtended_Accounts] FOREIGN KEY ([AccountId]) REFERENCES [dbo].[Accounts] ([AccountId])
GO

IF(NOT EXISTS(SELECT * FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS WHERE CONSTRAINT_NAME = 'FK_AccountRateItemsExtended_RateItems'))
ALTER TABLE [dbo].[AccountRateItemsExtended] ADD CONSTRAINT [FK_AccountRateItemsExtended_RateItems] FOREIGN KEY ([RateItemId]) REFERENCES [dbo].[RateItems] ([RateItemId])
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='RateItemsExtended' AND COLUMN_NAME = 'FreeQuantity')
    ALTER TABLE RateItemsExtended ADD FreeQuantity float NULL
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='RateItems' AND COLUMN_NAME = 'DiscountExempt')
    ALTER TABLE RateItems ADD DiscountExempt BIT DEFAULT(0)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='RateItems' AND COLUMN_NAME = 'LedgerAccountId')
    ALTER TABLE RateItems ADD LedgerAccountId INT
GO



IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='CompaniesShared')
    CREATE TABLE [dbo].[CompaniesShared] (
        [SharedCompanyLinkId]		INT IDENTITY (1, 1) NOT NULL,
        [CompanyId]					INT,
        [SharedCompanyId]			INT,
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='RateItems' AND COLUMN_NAME = 'ParentRateItemId')
    ALTER TABLE RateItems ADD ParentRateItemId INT NULL

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountRateItems' AND COLUMN_NAME = 'ParentRateItemId')
    ALTER TABLE AccountRateItems ADD ParentRateItemId INT NULL

	
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='RateItems' AND COLUMN_NAME = 'TimeStartAtStatusId')
    ALTER TABLE RateItems ADD TimeStartAtStatusId INT NULL
	
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='RateItems' AND COLUMN_NAME = 'TimeStopAtStatusId')
    ALTER TABLE RateItems ADD TimeStopAtStatusId INT NULL
	
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='RateItems' AND COLUMN_NAME = 'TimeRound')
    ALTER TABLE RateItems ADD TimeRound INT NULL

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='RateItems' AND COLUMN_NAME = 'InclusionTypeId')
    ALTER TABLE RateItems ADD InclusionTypeId INT DEFAULT(0) NOT NULL

IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.Columns WHERE TABLE_NAME= 'RateItems' AND COLUMN_NAME = 'Name' AND CHARACTER_MAXIMUM_LENGTH != 100)
    ALTER TABLE RateItems ALTER COLUMN Name VARCHAR(100)

-- This is commented out because DF_RateItems_FreeQuantity is dependent on column FreeQuantity. Not sure how to run this update correctly
-- IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.Columns WHERE TABLE_NAME= 'RateItems' AND COLUMN_NAME = 'FreeQuantity' AND DATA_TYPE = 'smallint')
--     ALTER TABLE RateItems ALTER COLUMN FreeQuantity float 

IF (NOT EXISTS(SELECT 1 FROM sys.indexes WHERE name='IX_RateItems_CompanyId'))
    CREATE NONCLUSTERED INDEX [IX_RateItems_CompanyId] ON [dbo].[RateItems] ([CompanyId], [Deleted])
GO

IF (NOT EXISTS(SELECT 1 FROM sys.indexes WHERE name='IX_RateItems_ParentRateItemId'))
    CREATE NONCLUSTERED INDEX [IX_RateItems_ParentRateItemId] ON [dbo].[RateItems] ([CompanyId], [Deleted])
GO


IF (NOT EXISTS(SELECT 1 FROM sys.indexes WHERE name='IX_RateItemsExtended_RateItemId'))
    CREATE NONCLUSTERED INDEX [IX_RateItemsExtended_RateItemId] ON [dbo].[RateItemsExtended] ([RateItemId])
GO

IF (NOT EXISTS(SELECT 1 FROM sys.indexes WHERE name='UX_RateItemsExtended'))
    CREATE NONCLUSTERED INDEX [UX_RateItemsExtended] ON [dbo].[RateItemsExtended] ([RateItemId], [BodyTypeId])
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='DispatchEntrySequencesShared')
    CREATE TABLE [dbo].[DispatchEntrySequencesShared] (
        [SharedSequenceId]					INT IDENTITY (1, 1) NOT NULL,
        [CompanyId]							INT,
        [SharedSequenceToUseCompanyId]		INT,
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='CompanyAttributes')
    CREATE TABLE [dbo].[CompanyAttributes] (
        [CompanyId] [int] NOT NULL,
        [DispatchEntryAttributeId] [int] NOT NULL
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='CompanyImpoundLots')
    CREATE TABLE [dbo].[CompanyImpoundLots] (
        [CompanyId] [int] NOT NULL,
        [ImpoundLotId] [int] NOT NULL
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='CompanyStorageRates')
    CREATE TABLE [dbo].[CompanyStorageRates] (
        [CompanyId] [int] NOT NULL,
        [StorageRateId] [int] NOT NULL
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='CompanyLetterTemplates')
    CREATE TABLE [dbo].[CompanyLetterTemplates] (
        [CompanyId] [int] NOT NULL,
        [LetterTemplateId] [int] NOT NULL,
        [No10Formatted] [bit] DEFAULT(NULL),
        [Hidden] [bit] NOT NULL DEfAULT(0)
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='CompanyLetterTemplates' AND COLUMN_NAME = 'No10Formatted')
    ALTER TABLE CompanyLetterTemplates ADD No10Formatted bit Default (NULL)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='CompanyLetterTemplates' AND COLUMN_NAME = 'Hidden')
    ALTER TABLE CompanyLetterTemplates ADD Hidden bit NOT NULL Default (0)
GO



IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='CompanyAccounts')
    CREATE TABLE [dbo].[CompanyAccounts](
        [CompanyId] [int] NOT NULL,
        [AccountId] [int] NOT NULL
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryPayments' AND COLUMN_NAME = 'PaymentDate')
    ALTER TABLE DispatchEntryPayments ADD PaymentDate DateTime Default (GETDATE())
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryPayments' AND COLUMN_NAME = 'IsVoid')
BEGIN
    ALTER TABLE DispatchEntryPayments ADD IsVoid bit Default (0)
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryPayments' AND COLUMN_NAME = 'VoidedByUserId')
    ALTER TABLE DispatchEntryPayments ADD VoidedByUserId INT NULL
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryPayments' AND COLUMN_NAME = 'VoidedDate')
    ALTER TABLE DispatchEntryPayments ADD VoidedDate DATETIME NULL
GO



IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountPayments' AND COLUMN_NAME = 'ReferenceNumber')
BEGIN
    ALTER TABLE AccountPayments ADD ReferenceNumber VARCHAR(192) DEFAULT (0)
END
ELSE
BEGIN 
    ALTER TABLE AccountPayments ALTER COLUMN ReferenceNumber VARCHAR(192) 
END
GO


 IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountPayments' AND COLUMN_NAME = 'ReferenceNumber')
     BEGIN
         ALTER TABLE AccountPayments ADD Amount [money] NOT NULL
     END
 ELSE
     BEGIN
         ALTER TABLE AccountPayments ALTER COLUMN Amount [money] NOT NULL
     END
 GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Invoices' AND COLUMN_NAME = 'PaymentsTotal')
BEGIN
    ALTER TABLE Invoices ADD PaymentsTotal [money]
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Invoices' AND COLUMN_NAME = 'AccountId')
BEGIN
    ALTER TABLE Invoices ADD AccountId [int]
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Invoices' AND COLUMN_NAME = 'IsTaxExempt')
BEGIN
    ALTER TABLE Invoices ADD IsTaxExempt bit NOT NULL DEFAULT(0)
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Invoices' AND COLUMN_NAME='NextScheduledRecalculate')
BEGIN
    ALTER TABLE Invoices ADD 
        NextScheduledRecalculate DATETIME DEFAULT NULL,
        LastUpdated DATETIME DEFAULT(getdate())
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Invoices' AND COLUMN_NAME = 'HiddenTotal')
BEGIN
    ALTER TABLE Invoices ADD 
        HiddenTotal money,
        TaxableTotal money,
        TicketValue money
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='AccountBalances')
    CREATE TABLE [dbo].[AccountBalances] (
        [AccountBalanceId]		INT IDENTITY (1, 1) NOT NULL,
        [AccountId]						INT NOT NULL,
        [CompanyId]						INT NOT NULL,
        [Balance]						MONEY NOT NULL,
        [LastUpdatedDate]				DATETIME DEFAULT(getdate()),

        CONSTRAINT [PK_AccountBalances] PRIMARY KEY NONCLUSTERED ([AccountBalanceId] ASC),
        CONSTRAINT [FK_AccountBalances_Companies] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].[Companies] ([CompanyId]),
        CONSTRAINT [FK_AccountBalances_Accounts] FOREIGN KEY ([AccountId]) REFERENCES [dbo].[Accounts] ([AccountId])
    )
GO

-- IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE WHERE CONSTRAINT_NAME = 'PK_AccountBalances')
--     ALTER TABLE [dbo].[AccountBalances] ADD CONSTRAINT [PK_AccountBalances] PRIMARY KEY NONCLUSTERED ([AccountBalanceId])
-- GO

-- IF (NOT EXISTS(SELECT 1 FROM sys.indexes WHERE name='IX_AccountBalances'))
--     CREATE CLUSTERED INDEX [IX_AccountBalances] ON [dbo].[AccountBalances] ([AccountId], [CompanyId])
-- GO
     
IF (NOT EXISTS(SELECT 1 FROM sys.indexes WHERE name='IX_DispatchEntryAccounts_DispatchEntryId'))
BEGIN
    CREATE NONCLUSTERED INDEX [IX_DispatchEntryAccounts_DispatchEntryId]
        ON [dbo].[DispatchEntryAccounts] ([DispatchEntryId])
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='AccountContacts')
    CREATE TABLE [dbo].[AccountContacts] (
        [AccountContactId]	INT IDENTITY (1, 1) NOT NULL,
        [AccountId]			INT NOT NULL,
        [FirstName]			VARCHAR(50),
        [LastName]			VARCHAR(50),
        [Email]				VARCHAR(100),
        [UserId]			int,
        [Birthday]			datetime,
        [Title]				varchar(100),
        [Notes]				varchar(4000),
        [Deleted]			bit default(0) NOT NULL,
        [CreateDate]		datetime default(getdate()) NOT NULL


        CONSTRAINT [FK_AccountContacts_Accounts] FOREIGN KEY ([AccountId]) REFERENCES [dbo].[Accounts] ([AccountId])
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AddressBookEntries' AND COLUMN_NAME = 'AccountId')
BEGIN
    ALTER TABLE AddressBookEntries ADD AccountId [int]
    CONSTRAINT [FK_AddressBookEntries_Accounts] FOREIGN KEY ([AccountId]) REFERENCES [dbo].[Accounts] ([AccountId])
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AddressBookEntries' AND COLUMN_NAME = 'Email')
BEGIN
    ALTER TABLE AddressBookEntries ADD Email varchar(100)
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AddressBookEntries' AND COLUMN_NAME = 'Notes')
BEGIN
    ALTER TABLE AddressBookEntries ADD Notes varchar(8000)
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AddressBookEntries' AND COLUMN_NAME = 'IsProblemCustomer')
BEGIN
    ALTER TABLE AddressBookEntries ADD IsProblemCustomer bit default(0)
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='MotorClubProcessingQueue')
    CREATE TABLE [dbo].[MotorClubProcessingQueue] (
        [QueueItemId]		INT IDENTITY (1, 1) NOT NULL,
        [StatusId]			TINYINT,
        [CompanyId]			INT,
        [TypeId]			TINYINT,
        [MetaDataJson]		varchar(4000),
        [FileId]			INT

        CONSTRAINT [PK_MotorClubProcessingQueue] PRIMARY KEY CLUSTERED ([QueueItemId] ASC),
        CONSTRAINT [FK_MotorClubProcessingQueue_Companies] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].[Companies] ([CompanyId])
    )
GO
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='MotorClubProcessingQueue' AND COLUMN_NAME = 'AccountId')
BEGIN
    ALTER TABLE MotorClubProcessingQueue ADD AccountId [int]
    CONSTRAINT [FK_MotorClubProcessingQueue_Accounts] FOREIGN KEY ([AccountId]) REFERENCES [dbo].[Accounts] ([AccountId])
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='MotorClubProcessingQueue' AND COLUMN_NAME = 'CreateDate')
BEGIN
    ALTER TABLE MotorClubProcessingQueue ADD CreateDate datetime DEFAULT(getdate())
END
GO

IF NOT EXISTS (SELECT 1 FROM sys.schemas WHERE name = 'Faxing')
BEGIN
    EXEC( 'CREATE SCHEMA Faxing' );
END

IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='FaxNumbers' AND TABLE_SCHEMA='Faxing' )
BEGIN
    CREATE TABLE [Faxing].[FaxNumbers] (
        [FaxNumberId]		INT IDENTITY (1, 1) NOT NULL,
        [FaxNumber]			varchar(11),
        [CompanyId]			INT,
        [OwnerUserId]		INT,
        [CreateDate]		datetime default(getdate()) NOT NULL,
        [Deleted]			bit default(0) NOT NULL

        CONSTRAINT [PK_FaxNumbers] PRIMARY KEY CLUSTERED ([FaxNumberId] ASC),
        CONSTRAINT [FK_FaxNumbers_Companies] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].[Companies] ([CompanyId]),
        CONSTRAINT [FK_FaxNumbers_Users] FOREIGN KEY ([OwnerUserId]) REFERENCES [dbo].[Users] ([UserId])
    )

    CREATE UNIQUE NONCLUSTERED INDEX IX_FaxNumbers_NumberUnique ON Faxing.FaxNumbers (FaxNumber) WHERE Deleted=0
END
GO

IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='FaxDestinations' AND TABLE_SCHEMA='Faxing' )
BEGIN
    CREATE TABLE [Faxing].[FaxDestinations] (
        [FaxDestinationId]	INT IDENTITY (1, 1) NOT NULL,
        [FaxNumberId]		INT,
        [Email]				VARCHAR(100),
        [OwnerUserId]		INT,
        [CreateDate]		datetime default(getdate()) NOT NULL,
        [Deleted]			bit default(0) NOT NULL

        CONSTRAINT [PK_FaxDestinations] PRIMARY KEY CLUSTERED ([FaxDestinationId] ASC),
        CONSTRAINT [FK_FaxDestinations_FaxNumbers] FOREIGN KEY ([FaxNumberId]) REFERENCES [Faxing].[FaxNumbers] ([FaxNumberId]),
        CONSTRAINT [FK_FaxDestinations_Users] FOREIGN KEY ([OwnerUserId]) REFERENCES [dbo].[Users] ([UserId])
    )

    CREATE UNIQUE NONCLUSTERED INDEX IX_FaxDestinations_UniqueFaxAndEmail ON [Faxing].[FaxDestinations] (FaxNumberId, Email) WHERE Deleted=0
END
GO

IF OBJECT_ID('[Faxing].[FaxTransactions]') IS NULL
CREATE TABLE [Faxing].[FaxTransactions] (
    [FaxTransactionId]	INT           IDENTITY (1, 1) NOT NULL,
    [JobId]				INT           NOT NULL,
    [PageCount]			INT           NOT NULL,
    [CompanyId]			INT			  NULL,
    [SendingNumber]     VARCHAR(20)   NOT NULL,
    [DestinationNumber]	VARCHAR(20)	  NOT NULL,
    [CreateDate]		datetime default(getdate()) NOT NULL

    CONSTRAINT [PK_FaxTransactions] PRIMARY KEY CLUSTERED ([FaxTransactionId] ASC),
    CONSTRAINT [FK_FaxTransactions_Companies] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].[Companies] ([CompanyId])
);
GO

IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='FaxDirectoryEntries' AND TABLE_SCHEMA='Faxing' )
BEGIN
    CREATE TABLE [Faxing].[FaxDirectoryEntries] (
        [FaxDirectoryEntryId]		INT IDENTITY (1, 1) NOT NULL,
        [FaxNumber]			varchar(11),
        [Name]				varchar(50),
        [OwnerUserId]		INT,
        [CreateDate]		datetime default(getdate()) NOT NULL,
        [Deleted]			bit default(0) NOT NULL

        CONSTRAINT [FK_FaxDirectoryEntries_Users] FOREIGN KEY ([OwnerUserId]) REFERENCES [dbo].[Users] ([UserId])
    )

    CREATE UNIQUE NONCLUSTERED INDEX IX_FaxDirectoryEntries_FaxNumberUnique ON Faxing.FaxDirectoryEntries (FaxNumber) WHERE Deleted=0
END
GO


IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='FaxNumberPool' AND TABLE_SCHEMA='Faxing' )
BEGIN
    CREATE TABLE [Faxing].[FaxNumberPool] (
        [FaxNumberId]		INT IDENTITY (1, 1) NOT NULL,
        [FaxNumber]			varchar(11),
        [CreateDate]		datetime default(getdate()) NOT NULL,
        [Deleted]			bit default(0) NOT NULL

        CONSTRAINT [PK_FaxNumberPool] PRIMARY KEY CLUSTERED ([FaxNumberId] ASC),
    )
    
    CREATE UNIQUE NONCLUSTERED INDEX IX_FaxNumberPool_NumberUnique ON Faxing.FaxNumberPool (FaxNumber) WHERE Deleted=0
END
GO


IF NOT EXISTS (SELECT 1 FROM sys.schemas WHERE name = 'Email')
BEGIN
    EXEC( 'CREATE SCHEMA Email' );
END
GO


IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='EmailDomains' AND TABLE_SCHEMA='Email' )
BEGIN
    CREATE TABLE [Email].[EmailDomains] (
        [EmailDomainId] 	INT IDENTITY (1, 1) NOT NULL,
        [Domain]		VARCHAR(50),
        [OwnerUserId]		INT,
        [CreateDate]		DATETIME DEFAULT(GETDATE()) NOT NULL,
        [Deleted]		BIT DEFAULT(0) NOT NULL

        CONSTRAINT [PK_EmailDomains] PRIMARY KEY CLUSTERED ([EmailDomainId] ASC)
        CONSTRAINT [FK_EmailDomains_Users] FOREIGN KEY ([OwnerUserId]) REFERENCES [dbo].[Users] ([UserId]),
    )
END
GO

IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='EmailAddresses' AND TABLE_SCHEMA='Email' )
BEGIN
    CREATE TABLE [Email].[EmailAddresses] (
        [EmailAddressId]	INT IDENTITY (1, 1) NOT NULL,
        [EmailAddress]		VARCHAR(100),
        [EmailDomainId] INT,
        [CompanyId]		INT,
        [OwnerUserId]		INT,
        [CreateDate]		DATETIME DEFAULT(GETDATE()) NOT NULL,
        [Deleted]		BIT DEFAULT(0) NOT NULL

        CONSTRAINT [PK_EmailAddresses] PRIMARY KEY CLUSTERED ([EmailAddressId] ASC),
        CONSTRAINT [FK_EmailAddresses_Companies] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].[Companies] ([CompanyId]),
        CONSTRAINT [FK_EmailAddresses_Users] FOREIGN KEY ([OwnerUserId]) REFERENCES [dbo].[Users] ([UserId]),
        CONSTRAINT [FK_EmailAddresses_Domains] FOREIGN KEY ([EmailDomainId]) REFERENCES [Email].[EmailDomains] ([EmailDomainId])
    )

    CREATE UNIQUE NONCLUSTERED INDEX IX_EmailAddresses_EmailUnique ON Email.EmailAddresses (EmailAddress) WHERE Deleted=0
END
GO

IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='EmailAddressForwards' AND TABLE_SCHEMA='Email' )
BEGIN
    CREATE TABLE [Email].[EmailAddressForwards] (
        [EmailAddressForwardId]	INT IDENTITY (1, 1) NOT NULL,
        [EmailAddressId]	INT,
        [Email]			VARCHAR(100),
        [OwnerUserId]		INT,
        [CreateDate]		DATETIME DEFAULT(GETDATE()) NOT NULL,
        [Deleted]		BIT DEFAULT(0) NOT NULL

        CONSTRAINT [PK_EmailAddressForwards] PRIMARY KEY CLUSTERED ([EmailAddressForwardId] ASC),
        CONSTRAINT [FK_EmailAddressForwards_EmailAddresses] FOREIGN KEY ([EmailAddressId]) REFERENCES [Email].[EmailAddresses] ([EmailAddressId]),
        CONSTRAINT [FK_EmailAddressForwards_Users] FOREIGN KEY ([OwnerUserId]) REFERENCES [dbo].[Users] ([UserId])
    )

    CREATE UNIQUE NONCLUSTERED INDEX IX_EmailAddressForwards_UniqueEmail ON [Email].[EmailAddressForwards] (EmailAddressId, Email) WHERE Deleted=0
END
GO



IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountInvoiceItems' AND COLUMN_NAME = 'InvoiceId')
    ALTER TABLE AccountInvoiceItems ADD InvoiceId INT NULL


IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='StatementInvoices')
BEGIN
    CREATE TABLE [dbo].[StatementInvoices](
        [StatementInvoiceId] [int] IDENTITY(1,1) NOT NULL,
        [StatementId] [int] NOT NULL,
        [InvoiceId] [int] NOT NULL,
 
        CONSTRAINT [PK_StatementInvoices] PRIMARY KEY CLUSTERED  ( [StatementInvoiceId] ASC ),
        CONSTRAINT [FK_StatementInvoices_Invoices] FOREIGN KEY([InvoiceId]) REFERENCES [dbo].[Invoices] ([InvoiceId]),
        CONSTRAINT [FK_StatementInvoices_Statements] FOREIGN KEY([StatementId]) REFERENCES [dbo].[Statements] ([StatementId])
    )
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='StatementOptions')

    CREATE TABLE [dbo].[StatementOptions](
        [StatementOptionsId] [int] IDENTITY(1,1) NOT NULL,
        [CompanyId] [int] NOT NULL,
        [AccountId] [int] NULL,
        [ShowInvoiceItems] [bit] NOT NULL,
        [ShowReason] [bit] NOT NULL,
        [ShowToFrom] [bit] NOT NULL,
        [ShowDriver] [bit] NOT NULL,
        [ShowOdometer] [bit] NOT NULL,
        [ShowVin] [bit] NOT NULL,
        [ShowInvoiceNumber] [bit] NOT NULL,
        [ShowVinAsLastEight] [bit] NOT NULL,
        [ShowLatest] [bit] NOT NULL,

    CONSTRAINT [PK_StatementOptions] PRIMARY KEY CLUSTERED 
    (
        [StatementOptionsId] ASC
    )WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
    ) ON [PRIMARY]

GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='StatementOptions' AND COLUMN_NAME = 'ShowCallNumber')
BEGIN
    ALTER TABLE StatementOptions ADD ShowCallNumber bit NOT NULL DEFAULT(0)
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='StatementOptions' AND COLUMN_NAME = 'ShowInvoiceLinks')
BEGIN
    ALTER TABLE StatementOptions ADD ShowInvoiceLinks bit NOT NULL DEFAULT(0)
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='StatementOptions' AND COLUMN_NAME = 'ShowAccountContact')
BEGIN
    ALTER TABLE StatementOptions ADD ShowAccountContact bit NOT NULL DEFAULT(1)
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='StatementOptions' AND COLUMN_NAME = 'ShowCompletionDate')
BEGIN
    ALTER TABLE StatementOptions ADD ShowCompletionDate bit NOT NULL DEFAULT(0)
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='StatementOptions' AND COLUMN_NAME = 'ShowPlateNumber')
BEGIN
    ALTER TABLE StatementOptions ADD ShowPlateNumber bit NOT NULL DEFAULT(0)
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='StatementOptions' AND COLUMN_NAME = 'ShowUnitNumber')
BEGIN
    ALTER TABLE StatementOptions ADD ShowUnitNumber bit NOT NULL DEFAULT(1)
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='StatementOptions' AND COLUMN_NAME = 'ShowTruck')
BEGIN
    ALTER TABLE StatementOptions ADD ShowTruck bit NOT NULL DEFAULT(1)
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='StatementOptions' AND COLUMN_NAME = 'ShowBillingNotes')
BEGIN
    ALTER TABLE StatementOptions ADD ShowBillingNotes bit NOT NULL DEFAULT(0)
END
GO


IF NOT EXISTS (SELECT 1 FROM sys.schemas WHERE name = 'Billing')
BEGIN
    -- create a schema to hold all tables related to billing integrations for agero, geico, road america, etc.
    EXEC( 'CREATE SCHEMA Billing' );
END

IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='BillingSystems' AND TABLE_SCHEMA='Billing')
BEGIN
    CREATE TABLE [Billing].[BillingSystems](
        [BillingSystemId] int IDENTITY(1,1) NOT NULL,
        [Name]			varchar(100),
        [CreateDate]	datetime DEFAULT(getdate()) NOT NULL,
        [IsDeleted]		bit DEFAULT(0)
 
        CONSTRAINT [PK_BillingSystems] PRIMARY KEY CLUSTERED  ( [BillingSystemId] ASC ),
    )
END
GO


IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='CompanySubscriptions' AND TABLE_SCHEMA='Billing')
BEGIN
    CREATE TABLE [Billing].[CompanySubscriptions](
        [CompanySubscriptionId] [int] IDENTITY(1,1) NOT NULL,
        [CompanyId] int NOT NULL,
        [BillingSystemId] int NOT NULL,
        [Amount]			smallmoney NOT NULL,
        [BillingDay]		tinyint DEFAULT(1) NOT NULL,
        [CreateDate]		datetime DEFAULT(getdate()) NOT NULL,
        [IsCancelled]		bit DEFAULT(0)
 
        CONSTRAINT [PK_CompanySubscriptions] PRIMARY KEY CLUSTERED  ( [CompanySubscriptionId] ASC ),
        CONSTRAINT [FK_CompanySubscriptions_Companies] FOREIGN KEY([CompanyId]) REFERENCES [dbo].[Companies] ([CompanyId]),
        CONSTRAINT [FK_CompanySubscriptions_Systems] FOREIGN KEY([BillingSystemId]) REFERENCES [Billing].[BillingSystems] ([BillingSystemId]),
    )
END
GO


IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='CompanySubscriptionTransactions' AND TABLE_SCHEMA='Billing')
BEGIN
    CREATE TABLE [Billing].[CompanySubscriptionTransactions](
        [CompanySubscriptionTransactionId] int IDENTITY(1,1) NOT NULL,
        [CompanySubscriptionId] int,
        [Amount]				smallmoney NOT NULL,
        [TransactionDate]		datetime DEFAULT(getdate()) NOT NULL,
        [IsSuccess]				bit
 
        CONSTRAINT [PK_CompanySubscriptionTransactions] PRIMARY KEY CLUSTERED  ( [CompanySubscriptionId] ASC ),
        CONSTRAINT [FK_CompanySubscriptionTransactions_CompanySubscriptions] FOREIGN KEY([CompanySubscriptionId]) REFERENCES [Billing].[CompanySubscriptions] ([CompanySubscriptionId]),
    )
END
GO

IF (NOT EXISTS(SELECT 1 FROM sys.indexes WHERE name='IX_DispatchEntryPayments_PaymentDate'))
BEGIN
    CREATE NONCLUSTERED INDEX IX_DispatchEntryPayments_PaymentDate
    ON [dbo].[DispatchEntryPayments] ([IsVoid],[PaymentDate])
    INCLUDE ([InvoiceId],[Type])

    PRINT 'Created index IX_DispatchEntryPayments_PaymentDate'
END


IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='VehicleColorAliases')
BEGIN
    CREATE TABLE [dbo].[VehicleColorAliases](
        [VehicleColorAliasId] int IDENTITY(1,1) NOT NULL,
        [ColorId] tinyint,
        [Name]				varchar(50) NOT NULL,
        [CreateDate]		datetime DEFAULT(getdate()) NOT NULL
 
        CONSTRAINT [PK_VehicleColorAliases] PRIMARY KEY CLUSTERED  ( [VehicleColorAliasId] ASC ),
        CONSTRAINT [FK_VehicleColorAliases_VehicleColors] FOREIGN KEY([ColorId]) REFERENCES [dbo].[VehicleColors] ([ColorId]),
    )
    /*
    insert into vehiclecoloraliases (colorid, name) 
    values 
    (2, 'wht'),
    (1, 'blk'),
    (6, 'blu'),
    (5, 'jad'),
    (5, 'grn'),
    (3, 'sil'),
    (11, 'gld'),
    (13, 'org'),
    (10, 'bge'),
    (12, 'navy blue'),
    (16, 'mar'),
    (17, 'gry'),
    (17, 'grey'),
    (17, 'dark grey'),
    (19, 'bur'),
    (20, 'chm')
    */
END
GO



IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='VehicleModelAliases')
BEGIN
    CREATE TABLE [dbo].[VehicleModelAliases](
        [VehicleModelAliasId] int IDENTITY(1,1) NOT NULL,
        [ModelId]			smallint,
        [Name]				varchar(50) NOT NULL,
        [CreateDate]		datetime DEFAULT(getdate()) NOT NULL
 
        CONSTRAINT [PK_VehicleModelAliases] PRIMARY KEY CLUSTERED  ( [VehicleModelAliasId] ASC ),
        CONSTRAINT [FK_VehicleModelAliases_VehicleModels] FOREIGN KEY([ModelId]) REFERENCES [dbo].[VehicleModels] ([ModelId]),
    )
END
GO



IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='VehicleManufacturerAliases')
BEGIN
    CREATE TABLE [dbo].[VehicleManufacturerAliases](
        [VehicleManufacturerAliasId] int IDENTITY(1,1) NOT NULL,
        [ManufacturerId]			smallint,
        [Name]				varchar(50) NOT NULL,
        [CreateDate]		datetime DEFAULT(getdate()) NOT NULL
 
        CONSTRAINT [PK_VehicleManufacturerAliases] PRIMARY KEY CLUSTERED  ( [VehicleManufacturerAliasId] ASC ),
        CONSTRAINT [FK_VehicleManufacturerAliases_VehicleManufacturers] FOREIGN KEY([ManufacturerId]) REFERENCES [dbo].[VehicleManufacturers] ([ManufacturerId]),
    )
END
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='DispatchEntryGroups')
    CREATE TABLE [dbo].[DispatchEntryGroups] (
        [DispatchEntryGroupId]			INT IDENTITY (1, 1) NOT NULL,
        [Name]							VARCHAR(50),
        [Description]					VARCHAR(250),
        [CompanyId]						INT default(NULL),
        [Deleted]						bit default(0) NOT NULL,
        [CreateDate]					datetime default(getdate()) NOT NULL

        CONSTRAINT [PK_DispatchEntryGroups] PRIMARY KEY CLUSTERED ([DispatchEntryGroupId] ASC)
    )
GO

IF (NOT EXISTS(SELECT 1 FROM sys.indexes WHERE name='IX_DispatchEntryPayments_PaymentDate'))
BEGIN
    CREATE NONCLUSTERED INDEX IX_DispatchEntryPayments_PaymentDate
    ON [dbo].[DispatchEntryPayments] ([IsVoid],[PaymentDate])
    INCLUDE ([InvoiceId],[Type])

    PRINT 'Created index IX_DispatchEntryPayments_PaymentDate'
END


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='DispatchEntryGroupReasons')
    CREATE TABLE [dbo].[DispatchEntryGroupReasons] (
        [DispatchEntryGroupReasonId]		INT IDENTITY (1, 1) NOT NULL,
        [DispatchEntryGroupId]				INT NOT NULL,
        [ReasonId]							INT NOT NULL

        CONSTRAINT [PK_DispatchEntryGroupReasons] PRIMARY KEY CLUSTERED ([DispatchEntryGroupReasonId] ASC),
        CONSTRAINT [FK_DispatchEntryGroupReasonId_DispatchEntryGroups] FOREIGN KEY ([DispatchEntryGroupId]) REFERENCES [dbo].[DispatchEntryGroups] ([DispatchEntryGroupId])
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='DispatchEntryGroupImpounds')
    CREATE TABLE [dbo].[DispatchEntryGroupImpounds] (
        [DispatchEntryGroupImpoundId]		INT IDENTITY (1, 1) NOT NULL,
        [DispatchEntryGroupId]				INT NOT NULL,
        [ImpoundId]							INT NOT NULL
        
        CONSTRAINT [PK_DispatchEntryGroupImpounds] PRIMARY KEY CLUSTERED ([DispatchEntryGroupImpoundId] ASC),
        CONSTRAINT [FK_DispatchEntryGroupImpoundId_DispatchEntryGroups] FOREIGN KEY ([DispatchEntryGroupId]) REFERENCES [dbo].[DispatchEntryGroups] ([DispatchEntryGroupId])
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='DispatchEntryGroupAccountTypes')
    CREATE TABLE [dbo].[DispatchEntryGroupAccountTypes] (
        [DispatchEntryGroupAccountTypeId]	INT IDENTITY (1, 1) NOT NULL,
        [DispatchEntryGroupId]				INT NOT NULL,
        [AccountTypeId]						INT NOT NULL

        CONSTRAINT [PK_DispatchEntryGroupAccountTypes] PRIMARY KEY CLUSTERED ([DispatchEntryGroupAccountTypeId] ASC),
        CONSTRAINT [FK_DispatchEntryGroupAccountTypeId_DispatchEntryGroups] FOREIGN KEY ([DispatchEntryGroupId]) REFERENCES [dbo].[DispatchEntryGroups] ([DispatchEntryGroupId])
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='DispatchEntryGroupAccounts')
    CREATE TABLE [dbo].[DispatchEntryGroupAccounts] (
        [DispatchEntryGroupAccountId]		INT IDENTITY (1, 1) NOT NULL,
        [DispatchEntryGroupId]				INT NOT NULL,
        [AccountId]							INT NOT NULL

        CONSTRAINT [PK_DispatchEntryGroupAccounts] PRIMARY KEY CLUSTERED ([DispatchEntryGroupAccountId] ASC),
        CONSTRAINT [FK_DispatchEntryGroupAccountId_DispatchEntryGroups] FOREIGN KEY ([DispatchEntryGroupId]) REFERENCES [dbo].[DispatchEntryGroups] ([DispatchEntryGroupId])
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='SurchargeRates' AND COLUMN_NAME = 'ApplyToCustomInvoiceItems')
    ALTER TABLE SurchargeRates ADD ApplyToCustomInvoiceItems BIT NOT NULL DEFAULT(1) 

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='SurchargeRates' AND COLUMN_NAME = 'TreatExclusionsAsInclusions')
    ALTER TABLE SurchargeRates ADD TreatExclusionsAsInclusions BIT NOT NULL DEFAULT(0) 

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='SurchargeRates' AND COLUMN_NAME = 'ReadOnly')
    ALTER TABLE SurchargeRates ADD ReadOnly BIT NOT NULL DEFAULT(1) 


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='ImpoundReleaseDetails' AND COLUMN_NAME = 'LicenseExpirationDate')
    ALTER TABLE ImpoundReleaseDetails ADD LicenseExpirationDate DATETIME

/* production needs this table column updated */
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='ImpoundReleaseDetails' AND COLUMN_NAME = 'OwnerUserId')
    ALTER TABLE ImpoundReleaseDetails ADD OwnerUserId INT NULL

/* production needs this table column updated */
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='ImpoundReleaseDetails' AND COLUMN_NAME = 'CreateDate')
    ALTER TABLE ImpoundReleaseDetails ADD CreateDate DATETIME NULL

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='CompaniesShared' AND COLUMN_NAME = 'ShareAllTrucks')
    ALTER TABLE CompaniesShared ADD ShareAllTrucks BIT default(0)
    
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='CompaniesShared' AND COLUMN_NAME = 'ShareAllDrivers')
    ALTER TABLE CompaniesShared ADD ShareAllDrivers BIT default(0)
    
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='CompaniesShared' AND COLUMN_NAME = 'ShareAllRateItems')
    ALTER TABLE CompaniesShared ADD ShareAllRateItems BIT default(0)

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='CompaniesShared' AND COLUMN_NAME = 'ShareAllAccounts')
    ALTER TABLE CompaniesShared ADD ShareAllAccounts BIT default(0)
    
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='CompaniesShared' AND COLUMN_NAME = 'ShareAllUsers')
    ALTER TABLE CompaniesShared ADD ShareAllUsers BIT default(0)
    
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='CompaniesShared' AND COLUMN_NAME = 'ShareAllLetterTemplates')
    ALTER TABLE CompaniesShared ADD ShareAllLetterTemplates BIT default(0)

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='CompaniesShared' AND COLUMN_NAME = 'Nickname')
    BEGIN
    ALTER TABLE CompaniesShared ADD Nickname varchar(100)
        END
ELSE 
BEGIN
    ALTER TABLE CompaniesShared ALTER COLUMN Nickname varchar(100)
END
GO
    
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='CompaniesShared' AND COLUMN_NAME = 'ShareAllImpoundLots')
    ALTER TABLE CompaniesShared ADD ShareAllImpoundLots BIT default(0)

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='CompaniesShared' AND COLUMN_NAME = 'ShareAllInvoiceStatuses')
    ALTER TABLE CompaniesShared ADD ShareAllInvoiceStatuses BIT default(0)

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='CompaniesShared' AND COLUMN_NAME = 'ShareQuickBooksConnection')
    ALTER TABLE CompaniesShared ADD ShareQuickBooksConnection BIT default(0)

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='CompaniesShared' AND COLUMN_NAME = 'ShareAllDispatchEntryAttributes')
    ALTER TABLE CompaniesShared ADD ShareAllDispatchEntryAttributes BIT default(0)

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='CompaniesShared' AND COLUMN_NAME = 'ShareAllReasons')
    ALTER TABLE CompaniesShared ADD ShareAllReasons BIT default(0)

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='CompaniesShared' AND COLUMN_NAME = 'ForceUsersToParent')
    ALTER TABLE CompaniesShared ADD ForceUsersToParent BIT default(0)

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='CompaniesShared' AND COLUMN_NAME = 'ShareAddressBookEntries')
    ALTER TABLE CompaniesShared ADD ShareAddressBookEntries INT default(0)

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE WHERE  CONSTRAINT_NAME = 'PK_CompaniesShared')
    ALTER TABLE [dbo].[CompaniesShared] ADD CONSTRAINT [PK_CompaniesShared] PRIMARY KEY CLUSTERED ([SharedCompanyLinkId])
GO

IF (NOT EXISTS(SELECT 1 FROM sys.indexes WHERE name='IX_CompaniesShared_CompanyId'))
    CREATE NONCLUSTERED INDEX [IX_CompaniesShared_CompanyId] ON [dbo].[CompaniesShared] ([CompanyId]) INCLUDE ([SharedCompanyId])
GO

IF (NOT EXISTS(SELECT 1 FROM sys.indexes WHERE name='IX_CompaniesShared_SharedCompanyId'))
    CREATE NONCLUSTERED INDEX [IX_CompaniesShared_SharedCompanyId] ON [dbo].[CompaniesShared] ([SharedCompanyId]) INCLUDE ([CompanyId], [Nickname])
GO



-- IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='CompaniesShared' AND COLUMN_NAME = 'ShareAddressBookEntries' AND DATA_TYPE = 'BIT')
--         ALTER TABLE CompaniesShared ALTER COLUMN ShareAddressBookEntries INT 



IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='DriverTruckDefaults')
    CREATE TABLE [dbo].[DriverTruckDefaults] (
        [DriverTruckDefaultId]			INT IDENTITY (1, 1) NOT NULL,
        [DriverId]				INT NOT NULL,
        [TruckId]				INT NOT NULL,
        [Deleted]				BIT DEFAULT(0) NOT NULL,
        [CreateDate]			DATETIME default(getdate()) NOT NULL,
        [ModifiedDate]			DATETIME default(getdate()) NOT NULL,
        [ModifiedUserId]		INT,

        CONSTRAINT [PK_DriverTruckDefaults] PRIMARY KEY CLUSTERED ([DriverTruckDefaultId] ASC),
        CONSTRAINT [FK_DriverTruckDefaults_Users] FOREIGN KEY ([ModifiedUserId]) REFERENCES [dbo].[Users] ([UserId]),
        CONSTRAINT [FK_DriverTruckDefaults_Trucks] FOREIGN KEY ([TruckId]) REFERENCES [dbo].[Trucks] ([TruckId]),
        CONSTRAINT [FK_DriverTruckDefaults_Drivers] FOREIGN KEY ([DriverId]) REFERENCES [dbo].[Drivers] ([DriverId])	
    )
GO
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DriverTruckDefaults' AND COLUMN_NAME = 'SourceId')
    ALTER TABLE DriverTruckDefaults ADD SourceId int default(0) NOT NULL

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='StorageRates' AND COLUMN_NAME = 'FreeSaturdays')
    ALTER TABLE StorageRates ADD FreeSaturdays bit default(0) NOT NULL

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='StorageRates' AND COLUMN_NAME = 'FreeSundays')
    ALTER TABLE StorageRates ADD FreeSundays bit default(0) NOT NULL

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='SurchargeAccountRates' AND COLUMN_NAME = 'OverrideCompanyInclusions')
    ALTER TABLE SurchargeAccountRates ADD OverrideCompanyInclusions bit default(0) NOT NULL

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Trucks' AND COLUMN_NAME = 'ReferenceNumber')
    ALTER TABLE Trucks ADD ReferenceNumber VARCHAR(30) NULL

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Drivers' AND COLUMN_NAME = 'ReferenceNumber')
    ALTER TABLE Drivers ADD ReferenceNumber VARCHAR(30) NULL

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='DispatchEntryNotes')
    CREATE TABLE [dbo].[DispatchEntryNotes](
        [DispatchEntryNoteId] [int] IDENTITY(1,1) NOT NULL,
        [DispatchEntryId] [int] NOT NULL,
        [Content] [varchar](2000) NOT NULL,
        [OwnerUserId] [int] NOT NULL,
        [CreateDate] [datetime] NOT NULL DEFAULT(getdate()),
        [Deleted] [bit] NOT NULL DEFAULT(0)
        CONSTRAINT [PK_DispatchEntrysNotes] PRIMARY KEY CLUSTERED ( [DispatchEntryNoteId] ASC )
    ) 
GO

-- Motorclubs

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='MotorClubTasks')
    CREATE TABLE [dbo].[MotorClubTasks](
        [MotorClubTaskId] [int] IDENTITY(1,1) NOT NULL,
        [Type] [int] NOT NULL,
        [Username] [varchar](100) NOT NULL,
        [Password] [varchar](100) NOT NULL,
        [CompanyId] [int] NOT NULL,
        [AccountId] [int] NULL,
        [Status] [int] NOT NULL,
        [StatusDate] [datetime] NOT NULL,
        [Error] [varchar](1000) NULL,
        CONSTRAINT [PK_MotorClubTasks] PRIMARY KEY CLUSTERED ( [MotorClubTaskId] ASC ),
        CONSTRAINT [FK_MotorClubTasks_Accounts] FOREIGN KEY([AccountId]) REFERENCES [dbo].[Accounts] ([AccountId]),
        CONSTRAINT [FK_MotorClubTasks_Companies] FOREIGN KEY([CompanyId]) REFERENCES [dbo].[Companies] ([CompanyId])
    ) 
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='MotorClubTaskErrors')
    CREATE TABLE [dbo].[MotorClubTaskErrors](
        MotorClubTaskErrorId int IDENTITY(1,1) NOT NULL,
        MotorClubTaskId int NOT NULL,
        Error text NULL,
        CreateDate datetime NOT NULL DEFAULT(getdate()),

        CONSTRAINT PK_MotorClubTaskErrors PRIMARY KEY CLUSTERED (MotorClubTaskErrorId ASC),
        CONSTRAINT FK_MotorClubTaskErrors_MotorClubTasks FOREIGN KEY(MotorClubTaskId) REFERENCES dbo.MotorClubTasks (MotorClubTaskId) ON UPDATE CASCADE ON DELETE CASCADE
    ) 
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='MotorClubConnectionLimits')
    CREATE TABLE [dbo].[MotorClubConnectionLimits](
        [MotorClubConnectionLimitId] [int] IDENTITY(1,1) NOT NULL,
        [Type] [int] NOT NULL,
        [Description] [varchar](100) NOT NULL,
        [ConnectionLimit] [int] NOT NULL,
        CONSTRAINT [PK_MotorClubConnectionLimit] PRIMARY KEY CLUSTERED ( [MotorClubConnectionLimitId] ASC )
    )
GO

-- End of Motorclubs

-- Entry Commissions
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='DispatchEntryCommissionDrivers')
    CREATE TABLE [dbo].[DispatchEntryCommissionDrivers](
        [EntryCommissionDriverId] int IDENTITY(1,1) NOT NULL,
        [DispatchEntryId] int NOT NULL,
        [DriverId] int NOT NULL,
        [FlatAmount] decimal(16,2) NOT NULL,
        [Percentage] decimal(16,2) NOT NULL,
        [Type] tinyint,
        [OwnerUserId] [int] NOT NULL,
        [CreateDate] datetime NOT NULL DEFAULT(getdate()),
        [Deleted] bit NOT NULL DEFAULT(0)
        CONSTRAINT [PK_DispatchEntryCommissionDrivers] PRIMARY KEY CLUSTERED ( [EntryCommissionDriverId] ASC )
        CONSTRAINT [FK_EntryCommissionDrivers_DispatchEntries] FOREIGN KEY ([DispatchEntryId]) REFERENCES [dbo].[DispatchEntries]([DispatchEntryId]),
        CONSTRAINT [FK_EntryCommissionDrivers_Users] FOREIGN KEY ([OwnerUserId]) REFERENCES [dbo].[Users]([UserId]),
    ) 
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='DispatchEntryCommissionDriverExtendedCommissions')
    CREATE TABLE [dbo].[DispatchEntryCommissionDriverExtendedCommissions](
        [EntryCommissionDriverExtendedCommissionId] int IDENTITY(1,1) NOT NULL,
        [EntryCommissionDriverId] int NOT NULL,
        [InvoiceItemId] int NOT NULL,
        [Amount] decimal NOT NULL,
        [Type] tinyint,
        [OwnerUserId] [int] NOT NULL,
        [CreateDate] datetime NOT NULL DEFAULT(getdate()),
        [Deleted] bit NOT NULL DEFAULT(0)
        CONSTRAINT [PK_DispatchEntryCommissionDriverExtendedCommissions] PRIMARY KEY CLUSTERED ( [EntryCommissionDriverExtendedCommissionId] ASC )
        CONSTRAINT [FK_EntryCommissionDriversExtendedCommissions_Users] FOREIGN KEY ([OwnerUserId]) REFERENCES [dbo].[Users]([UserId]),
    ) 
GO

-- End of Entry Commissions



IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='AccountRateItemRules')
    CREATE TABLE [dbo].[AccountRateItemRules](
        [AccountRateItemRuleId] int IDENTITY(1,1) NOT NULL,
        [AccountId] int NULL,
        [RateItemId] int NOT NULL,
        [ReasonId] int NULL, 
        [BodyTypeId] int NULL, 
        [OwnerUserId] int NOT NULL,
        [CreateDate] datetime NOT NULL DEFAULT(getdate()),
        [Deleted] bit NOT NULL DEFAULT(0)
        CONSTRAINT [PK_AccountRateItemRules] PRIMARY KEY CLUSTERED ( [AccountRateItemRuleId] ASC )
        CONSTRAINT [FK_AccountRateItemRules_Accounts] FOREIGN KEY ([AccountId]) REFERENCES [dbo].[Accounts]([AccountId]),
        CONSTRAINT [FK_AccountRateItemRules_RateItems] FOREIGN KEY ([RateItemId]) REFERENCES [dbo].[RateItems]([RateItemId]),
        CONSTRAINT [FK_AccountRateItemRules_Reasons] FOREIGN KEY ([ReasonId]) REFERENCES [dbo].[DispatchReasons]([DispatchReasonId]),
        CONSTRAINT [FK_AccountRateItemRules_Users] FOREIGN KEY ([OwnerUserId]) REFERENCES [dbo].[Users]([UserId]),
    ) 
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountRateItemRules' AND COLUMN_NAME = 'DefaultQuantity')
    ALTER TABLE AccountRateItemRules ADD DefaultQuantity float NULL

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountRateItemRules' AND COLUMN_NAME = 'ImpoundAddAfterThisManyDaysHeld')
    ALTER TABLE AccountRateItemRules ADD ImpoundAddAfterThisManyDaysHeld float NULL

ALTER TABLE AccountRateItemRules ALTER COLUMN AccountId INT NULL

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='InvoiceItemRulesApplied')
    CREATE TABLE [dbo].[InvoiceItemRulesApplied](
        [InvoiceItemRulesAppliedId] int IDENTITY(1,1) NOT NULL,
        [AccountRateItemRuleId] int NOT NULL,
        [DispatchEntryId] int NOT NULL,
        [InvoiceItemId] int NULL,
        [CreateDate] datetime NOT NULL DEFAULT(getdate()),
        CONSTRAINT [PK_InvoiceItemRulesApplied] PRIMARY KEY CLUSTERED ( [InvoiceItemRulesAppliedId] ASC ),
        CONSTRAINT [FK_AccountRateItemRule_Id] FOREIGN KEY ([AccountRateItemRuleId]) REFERENCES [dbo].[AccountRateItemRules]([AccountRateItemRuleId]),
        CONSTRAINT [FK_AccountRateItemRules_InvoiceItems] FOREIGN KEY ([InvoiceItemId]) REFERENCES [dbo].[InvoiceItems]([InvoiceItemId]) ON DELETE SET NULL,
        CONSTRAINT [FK_AccountRateItemRules_DispatchEntry] FOREIGN KEY ([DispatchEntryId]) REFERENCES [dbo].[DispatchEntries]([DispatchEntryId]),
    ) 
GO



IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=23)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (23, 1, 1, 'Cancellation Reason', 0)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END

IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=22)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (22, 1, 1, 'Commission Ticket Value', 0)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END

IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=24)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (24, 1, 1, 'SourceLatLong', 0)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END

IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=25)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (25, 1, 1, 'DestLatLong', 0)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END

IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=26)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (26, 1, 1, 'Convert From Quote Id', 0)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END



IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=30)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (30, 1, 1, 'Customer Covered Miles', 2)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END

IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=31)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (31, 1, 1, 'Customer Paid Miles', 2)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END

IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=32)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (32, 1, 1, 'Deadhead Miles', 2)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END


IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=32)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (33, 1, 1, 'Toll Roads', 3)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END



IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=33)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (33, 1, 1, 'Geico CCRF', 3)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END

IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=34)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (34, 1, 1, 'Toll Road', 1)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END

IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=35)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (35, 1, 1, 'Applied PricingRuleIds', 3)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END



-- Washington Master Report built-in attributes
IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=36)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (36, 1, 1, 'Call Enforcement Date', 4)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END
IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=37)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (37, 1, 1, 'Write Enforcement Date', 4)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END
IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=38)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (38, 1, 1, '24 Hour Notice Date', 4)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END
IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=39)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (39, 1, 1, 'AVR Mailed Date', 4)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END
IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=40)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (40, 1, 1, 'AVR Returned Date', 4)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END
IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=41)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (41, 1, 1, 'Notice Mailed Date', 4)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END
IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=42)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (42, 1, 1, 'Auction Advertised Date', 4)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END
IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=43)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (43, 1, 1, 'Auction Date', 5)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END
IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=44)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (44, 1, 1, 'Amount of Lien', 1)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END
IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=45)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (45, 1, 1, 'Sale Amount', 1)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END
IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=46)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (46, 1, 1, 'Sale Surplus Amount', 1)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END
-- End Washington Master Log attributes
IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=47)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (47, 1, 1, 'Billing Notes', 1)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END

-- External dispatch import built-in attributes
IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=48)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (48, 1, 1, 'Call Import Id', 0)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END
-- End External dispatch import attributes

IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=49)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (49, 1, 1, 'Impound Reason', 1)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END


IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=50)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (50, 1, 1, 'Reference URL', 1)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END

IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=51)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (51, 1, 1, 'Calculated Mileage Automatically', 1)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END



IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=52)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (52, 1, 1, 'Audited', 1)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END


IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=53)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (53, 1, 1, 'Tow Out Call', 1)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END


IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=54)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (54, 1, 1, 'Tow Out Linked Asset Id', 1)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END

IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=55)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (55, 1, 1, 'Sticker Number', 1)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END




IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=57)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (57, 1, 1, 'Cancellation Acknowledgement JSON', 0)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END


IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=58)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (58, 1, 1, 'Completion Acknowledgement JSON', 0)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END


IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=59)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (59, 1, 1, 'Cancel Recommended', 3)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END

IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=60)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (60, 1, 1, 'AutoDispatch Managed', 3)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END

IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=67)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (67, 1, 1, 'Subcontractor AccountId', 0)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END



IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=68)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (68, 1, 1, 'Subcontractor Response JSON', 0)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END

IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=69)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (69, 1, 1, 'Subcontractor Rotation Managed', 0)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END

IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=71)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (71, 1, 1, 'Property Release Description', 0)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END

IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=72)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (72, 1, 1, 'Linked Auction Invoice CallId', 0)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END

IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=73)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (73, 1, 1, 'Police Hold JSON', 0)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END

IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=74)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (74, 1, 1, 'Linked Account Manager User Id', 0)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END

IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=75)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (75, 1, 1, 'Vehicle Value', 2)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END

IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=76)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (76, 1, 1, 'AAA Trouble Problem Code', 2)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END

IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=77)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (77, 1, 1, 'Resolution Code', 2)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END


IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=79)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (79, 1, 1, 'Current Call Acknowledgement JSON', 0)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END

IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=80)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (80, 1, 1, 'Stolen Record JSON', 0)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END

IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=81)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (81, 1, 1, 'ACG Super7', 0)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END

IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=82)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (82, 1, 1, 'Skipped Photo Requirement', 0)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END

IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=84)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (84, 1, 1, 'Lien Perfected Date', 4)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END

IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=85)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (85, 1, 1, 'External Chat URL', 4)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END

IF NOT EXISTS( SELECT 1 FROM DispatchEntryAttributes WHERE DispatchEntryAttributeId=86)
BEGIN
    SET IDENTITY_INSERT DispatchEntryAttributes ON
    insert into dispatchentryattributes (DispatchEntryAttributeId, companyid, BuiltIn, name, type)
    values (86, 1, 1, 'Terms of Service URL', 4)
    SET IDENTITY_INSERT DispatchEntryAttributes OFF
END

-- Stickers



IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='StickerSequences')
    CREATE TABLE [dbo].[StickerSequences](
        [CompanyId] [int] NOT NULL,
        [CurrentValue] [int] NOT NULL,
        
        CONSTRAINT [PK_StickerSequences] PRIMARY KEY CLUSTERED ([CompanyId] ASC),
        CONSTRAINT [FK_StickerSequences_Companies] FOREIGN KEY([CompanyId]) REFERENCES [dbo].[Companies] ([CompanyId])
    )
GO





IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='Stickers')
    CREATE TABLE [dbo].[Stickers](
        [StickerId] [int] IDENTITY(1,1) NOT NULL,
        [CompanyId] [int] NOT NULL,
        [CustomNumber] varchar(30) NULL,
        [StickerNumber] [int] NOT NULL,
        [AccountId] [int] NULL,
        [Notes] [varchar](1000) NULL,
        [CreateDate] [datetime] DEFAULT (getdate()) NOT NULL,
        [Deleted] [bit] DEFAULT ((0)) NOT NULL,
        [ColorId] [int] NULL,
        [BodyTypeId] [int] NULL,
        [Make] [varchar](50) NULL,
        [Model] [varchar](50) NULL,
        [VIN] [varchar](17) NULL,
        [ModelYear] [smallint] NULL,
        [LicenseNumber] [varchar](50) NULL,
        [LicenseState] [varchar](30) NULL,
    
        CONSTRAINT [PK_Stickers] PRIMARY KEY CLUSTERED ([StickerId] ASC),
        CONSTRAINT [FK_Stickers_Companies] FOREIGN KEY([CompanyId]) REFERENCES [dbo].[Companies] ([CompanyId])
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='StickerReasons')
    CREATE TABLE [dbo].[StickerReasons](
        [StickerReasonId] [int] IDENTITY(1,1)  NOT NULL,
        [CompanyId] [int] NULL,
        [Name] [varchar](32) NOT NULL,
        [Description] [varchar](100) NULL,
        [Impound] [bit] DEFAULT ((0)) NOT NULL,
        [Deleted] [bit] DEFAULT ((0)) NOT NULL,
    
        CONSTRAINT [PK_StickerReasons] PRIMARY KEY CLUSTERED ([StickerReasonId] ASC),
        CONSTRAINT [FK_StickerReasons_Companies] FOREIGN KEY([CompanyId]) REFERENCES [dbo].[Companies] ([CompanyId])
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='StickerReasonLinks')
    CREATE TABLE [dbo].[StickerReasonLinks](
        [StickerReasonId] [int] NOT NULL,
        [StickerId] [int] NOT NULL,
        [CreateDate] [datetime] NOT NULL,
    
        CONSTRAINT [FK_StickerReasonLinks_StickerReasons] FOREIGN KEY([StickerReasonId]) REFERENCES [dbo].[StickerReasons] ([StickerReasonId]),
        CONSTRAINT [FK_StickerReasonLinks_Stickers] FOREIGN KEY([StickerId]) REFERENCES [dbo].[Stickers] ([StickerId])
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='StickerStatuses')
    CREATE TABLE [dbo].[StickerStatuses](
        [StickerStatusId] [int] IDENTITY(1,1) NOT NULL,
        [CompanyId] [int] NULL,
        [Name] [varchar](50) NOT NULL,
        [ExtendedName] [varchar](100) NULL,
        [BuiltIn] [bit] NOT NULL,
        [HtmlColor] [varchar](50) NULL,
        [DisplayOrder] [int] DEFAULT ((0)) NOT NULL,
        [Deleted] [bit] DEFAULT ((0)) NOT NULL,
        
        CONSTRAINT [PK_StickerStatuses] PRIMARY KEY CLUSTERED ([StickerStatusId] ASC),
        CONSTRAINT [FK_StickerStatuses_Companies] FOREIGN KEY([CompanyId]) REFERENCES [dbo].[Companies] ([CompanyId])
    )
GO

IF (NOT EXISTS (SELECT 1 FROM StickerStatuses WHERE StickerStatusId=1))
BEGIN
    SET IDENTITY_INSERT StickerStatuses ON
        INSERT INTO StickerStatuses (StickerStatusId, Name, BuiltIn, HtmlColor, DisplayOrder, Deleted) VALUES (1, 'Waiting', 1, '#FF9900', 0, 0)
    SET IDENTITY_INSERT StickerStatuses OFF
END

IF (NOT EXISTS (SELECT 1 FROM StickerStatuses WHERE StickerStatusId=2))
BEGIN
    SET IDENTITY_INSERT StickerStatuses ON
        INSERT INTO StickerStatuses (StickerStatusId, Name, BuiltIn, HtmlColor, DisplayOrder, Deleted) VALUES (2, 'Rejected', 1, '#ff0000', 0, 0)
    SET IDENTITY_INSERT StickerStatuses OFF
END

IF (NOT EXISTS (SELECT 1 FROM StickerStatuses WHERE StickerStatusId=3))
BEGIN
    SET IDENTITY_INSERT StickerStatuses ON
        INSERT INTO StickerStatuses (StickerStatusId, Name, BuiltIn, HtmlColor, DisplayOrder, Deleted) VALUES (3, 'Approved', 1, '#2850ab', 0, 0)
    SET IDENTITY_INSERT StickerStatuses OFF
END

IF (NOT EXISTS (SELECT 1 FROM StickerStatuses WHERE StickerStatusId=4))
BEGIN
    SET IDENTITY_INSERT StickerStatuses ON
        INSERT INTO StickerStatuses (StickerStatusId, Name, BuiltIn, HtmlColor, DisplayOrder, Deleted) VALUES (4, 'Resolved', 1, '#3294F5', 0, 0)
    SET IDENTITY_INSERT StickerStatuses OFF
END

IF (NOT EXISTS (SELECT 1 FROM StickerStatuses WHERE StickerStatusId=5))
BEGIN
    SET IDENTITY_INSERT StickerStatuses ON
        INSERT INTO StickerStatuses (StickerStatusId, Name, BuiltIn, HtmlColor, DisplayOrder, Deleted) VALUES (5, 'Towable', 1, '#90ee90', 0, 0)
    SET IDENTITY_INSERT StickerStatuses OFF
END

IF (NOT EXISTS (SELECT 1 FROM StickerStatuses WHERE StickerStatusId=6))
BEGIN
    SET IDENTITY_INSERT StickerStatuses ON
        INSERT INTO StickerStatuses (StickerStatusId, Name, BuiltIn, HtmlColor, DisplayOrder, Deleted) VALUES (6, 'Towed', 1, '#008000', 0, 0)
    SET IDENTITY_INSERT StickerStatuses OFF
END

IF (NOT EXISTS (SELECT 1 FROM StickerStatuses WHERE StickerStatusId=7))
BEGIN
    SET IDENTITY_INSERT StickerStatuses ON
        INSERT INTO StickerStatuses (StickerStatusId, Name, BuiltIn, HtmlColor, DisplayOrder, Deleted) VALUES (7, 'Converted', 1, '#98f0f9', 0, 0)
    SET IDENTITY_INSERT StickerStatuses OFF
END

IF (EXISTS (SELECT 1 FROM StickerStatuses WHERE StickerStatusId=7 and HtmlColor = '#98f0f9'))
BEGIN
    UPDATE StickerStatuses SET HtmlColor='#008000' WHERE StickerStatusId=7 and HtmlColor = '#98f0f9'
END

IF (NOT EXISTS (SELECT 1 FROM StickerStatuses WHERE StickerStatusId=8))
BEGIN
    SET IDENTITY_INSERT StickerStatuses ON
        INSERT INTO StickerStatuses (StickerStatusId, Name, BuiltIn, HtmlColor, DisplayOrder, Deleted) VALUES (8, 'Expired', 1, '#000000', 0, 0)
    SET IDENTITY_INSERT StickerStatuses OFF
END

IF (NOT EXISTS (SELECT 1 FROM StickerStatuses WHERE StickerStatusId=9))
BEGIN
    SET IDENTITY_INSERT StickerStatuses ON
        INSERT INTO StickerStatuses (StickerStatusId, Name, BuiltIn, HtmlColor, DisplayOrder, Deleted) VALUES (9, 'Unapproved', 1, '', 0, 0)
    SET IDENTITY_INSERT StickerStatuses OFF
END

IF (NOT EXISTS (SELECT 1 FROM StickerStatuses WHERE StickerStatusId=10))
BEGIN
    SET IDENTITY_INSERT StickerStatuses ON
        INSERT INTO StickerStatuses (StickerStatusId, Name, BuiltIn, HtmlColor, DisplayOrder, Deleted) VALUES (10, 'Unresolved', 1, '', 0, 0)
    SET IDENTITY_INSERT StickerStatuses OFF
END

IF (NOT EXISTS (SELECT 1 FROM StickerStatuses WHERE StickerStatusId=11))
BEGIN
    SET IDENTITY_INSERT StickerStatuses ON
        INSERT INTO StickerStatuses (StickerStatusId, Name, BuiltIn, HtmlColor, DisplayOrder, Deleted) VALUES (11, 'Unrejected', 1, '', 0, 0)
    SET IDENTITY_INSERT StickerStatuses OFF
END

IF (NOT EXISTS (SELECT 1 FROM StickerStatuses WHERE StickerStatusId=12))
BEGIN
    SET IDENTITY_INSERT StickerStatuses ON
        INSERT INTO StickerStatuses (StickerStatusId, Name, BuiltIn, HtmlColor, DisplayOrder, Deleted) VALUES (12, 'Extended', 1, '', 0, 0)
    SET IDENTITY_INSERT StickerStatuses OFF
END

IF (NOT EXISTS (SELECT 1 FROM StickerStatuses WHERE StickerStatusId=13))
BEGIN
    SET IDENTITY_INSERT StickerStatuses ON
        INSERT INTO StickerStatuses (StickerStatusId, Name, BuiltIn, HtmlColor, DisplayOrder, Deleted) VALUES (13, 'Matured', 1, '', 0, 0)
    SET IDENTITY_INSERT StickerStatuses OFF
END



IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='StickerStatusEvents')
    CREATE TABLE [dbo].[StickerStatusEvents](
        [StickerStatusEventId] [int] IDENTITY(1,1) NOT NULL,
        [StickerId] [int] NOT NULL,
        [StickerStatusId] [int] NOT NULL,
        [UserId] [int] NOT NULL,
        [CreateDate] [datetime] NOT NULL,
        
        CONSTRAINT [PK_StickerStatusEvents] PRIMARY KEY CLUSTERED ([StickerStatusEventId] ASC),
        CONSTRAINT [FK_StickerStatusEvents_Stickers] FOREIGN KEY([StickerId]) REFERENCES [dbo].[Stickers] ([StickerId]),
        CONSTRAINT [FK_StickerStatusEvents_StickerStatuses] FOREIGN KEY([StickerStatusId]) REFERENCES [dbo].[StickerStatuses] ([StickerStatusId]),
        CONSTRAINT [FK_StickerStatusEvents_Users] FOREIGN KEY([UserId]) REFERENCES [dbo].[Users] ([UserId])
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='StickerPhotos')
    CREATE TABLE [dbo].[StickerPhotos](
        [StickerPhotoId] [int] IDENTITY(1,1) NOT NULL,
        [StickerId] [int] NOT NULL,
        [ContentType] [varchar](50) NOT NULL,
        [Description] [varchar](100) NULL,
        [CreateDate] [datetime] DEFAULT (getdate()) NOT NULL,
        [RemoteIp] [varchar](46) NULL,
        [OwnerUserId] [int] NULL,
        [Deleted] [bit] DEFAULT ((0)) NOT NULL,
        
        CONSTRAINT [PK_StickerPhotos] PRIMARY KEY CLUSTERED ([StickerPhotoId] ASC),
        CONSTRAINT [FK_StickerPhotos_Stickers] FOREIGN KEY([StickerId]) REFERENCES [dbo].[Stickers] ([StickerId])
    )
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='StickerSessions')
    CREATE TABLE dbo.StickerSessions(
        StickerSessionId int identity(1,1) NOT NULL, 
		CompanyId int NOT NULL,
		AccountId int NOT NULL,
		OwnerUserId int NOT NULL,
		CreateDate datetime default(getdate()), 
		CompletionDate datetime,

        
        CONSTRAINT PK_StickerSessions PRIMARY KEY CLUSTERED (StickerSessionId ASC),
        CONSTRAINT FK_StickerSessions_Companies FOREIGN KEY(CompanyId) REFERENCES dbo.Companies (CompanyId),
		CONSTRAINT FK_StickerSessions_Accounts FOREIGN KEY(AccountId) REFERENCES dbo.Accounts (AccountId)
    )
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='StickerApprovalNotifications')
    CREATE TABLE dbo.StickerApprovalNotifications (
        StickerApprovalNotificationId int identity(1,1) NOT NULL, 
		AccountId int NOT NULL,
		StickerId int NOT NULL,
		SentToUserId int NOT NULL,
		CreateDate datetime default(getdate()), 
        
        CONSTRAINT PK_StickerApprovalNotifications PRIMARY KEY CLUSTERED (StickerApprovalNotificationId ASC),
    )
GO



IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='AccountStickerReasonLinks')
    CREATE TABLE [dbo].[AccountStickerReasonLinks](
        [AccountId] [int] NOT NULL,
        [StickerReasonId] [int] NOT NULL,
        [CreateDate] [datetime] NOT NULL,
        [UserId] [int] NOT NULL,
        
        CONSTRAINT [FK_AccountStickerReasonLinks_Accounts] FOREIGN KEY([AccountId]) REFERENCES [dbo].[Accounts] ([AccountId]),
        CONSTRAINT [FK_AccountStickerReasonLinks_StickerReasons] FOREIGN KEY([StickerReasonId]) REFERENCES [dbo].[StickerReasons] ([StickerReasonId]),
        CONSTRAINT [FK_AccountStickerReasonLinks_Users] FOREIGN KEY([UserId]) REFERENCES [dbo].[Users] ([UserId])
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountStickerReasonLinks' AND COLUMN_NAME = 'StartFromType')
    ALTER TABLE AccountStickerReasonLinks ADD StartFromType int Default(0)


IF OBJECT_ID('dbo.[vwStickers]') IS NULL EXEC ('CREATE VIEW dbo.[vwStickers] AS SELECT 1 as Temp')
GO


ALTER VIEW [dbo].[vwStickers]
AS 
SELECT DISTINCT 
	S.StickerId,
	S.CustomNumber,
	S.StickerNumber,
	S.AccountId,
	S.Notes,
	S.CompanyId,
	S.CreateDate,
	S.Deleted,
	S.ColorId,
	S.BodyTypeId,
	S.Make,
	S.Model,
	S.VIN,
	S.ModelYear,
	S.LicenseNumber,
	S.LicenseState,
	S.DispatchEntryId,
	S.GracePeriodType,
	S.GracePeriodExpirationDate,
	S.AuthorizationDate,
	S.AuthorizationUserId,
    S.AuthorizationUserSignatureId,
	S.VehicleLatitude,
	S.VehicleLongitude,
	S.VehicleLocation,
	S.ExpirationDate,
	S.StickerSessionId,
	S.OwnerUserId,
	S.ExtendedExpirationDate,
	S.ResolvedDate,
	S.ResolvedUserId,
	S.RejectedDate,
	S.RejectedUserId,
	S.StatusId,
	SS.Name AS StatusName,
	SS.HtmlColor AS StatusColor
FROM 
	dbo.Stickers S
		INNER JOIN dbo.StickerStatuses AS SS ON SS.StickerStatusId = S.StatusId
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='StickerNotes')
    CREATE TABLE [dbo].[StickerNotes](
        [StickerNoteId] [int] IDENTITY(1,1) NOT NULL,
        [StickerId] [int] NOT NULL,
        [UserId] [int] NOT NULL,
        [CreateDate] [datetime] NOT NULL,
        [Message] [varchar](1000) NOT NULL,
        
        CONSTRAINT [PK_StickerNotes] PRIMARY KEY CLUSTERED ([StickerNoteId] ASC),
        CONSTRAINT [FK_StickerNotes_Stickers] FOREIGN KEY([StickerId]) REFERENCES [dbo].[Stickers] ([StickerId]),
        CONSTRAINT [FK_StickerNotes_Users] FOREIGN KEY([UserId]) REFERENCES [dbo].[Users] ([UserId])
    )
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='StickerReasonTimes')
    CREATE TABLE [dbo].[StickerReasonTimes](
        [StickerReasonTimeId] [int] IDENTITY(1,1) NOT NULL,
        [AccountId] [int] NOT NULL,
        [StickerReasonId] [int] NULL,
        [Time] [decimal] NULL,
        [CreateDate] [datetime] NOT NULL,
        
        CONSTRAINT [PK_StickerReasonTimes] PRIMARY KEY CLUSTERED ([StickerReasonTimeId] ASC),
        CONSTRAINT [FK_StickerReasonTimes_Accounts] FOREIGN KEY([AccountId]) REFERENCES [dbo].[Accounts] ([AccountId]),
        CONSTRAINT [FK_StickerReasonTimes_StickerReasons] FOREIGN KEY([StickerReasonId]) REFERENCES [dbo].[StickerReasons] ([StickerReasonId])
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='StickerRequests')
    CREATE TABLE dbo.StickerRequests (
        StickerRequestId int IDENTITY(1,1) NOT NULL,
        CompanyId int NOT NULL,
        AccountId int NOT NULL,
        StatusId int NOT NULL default(0),
        CreateDate datetime NOT NULL default(getdate()),
        DueDate datetime NOT NULL default(getdate()),
        IsDeleted bit NOT NULL default(0),
        OwnerUserId int NOT NULL
    
        CONSTRAINT [PK_StickerRequests] PRIMARY KEY CLUSTERED ([StickerRequestId] ASC),
        CONSTRAINT [FK_StickerRequests_Companies] FOREIGN KEY([CompanyId]) REFERENCES [dbo].[Companies] ([CompanyId]),
        CONSTRAINT [FK_StickerRequests_Accounts] FOREIGN KEY([AccountId]) REFERENCES [dbo].[Accounts] ([AccountId]),
        CONSTRAINT [FK_StickerRequests_Users] FOREIGN KEY(OwnerUserId) REFERENCES [dbo].[Users] (UserId)
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='StickerSettings')
    CREATE TABLE dbo.StickerSettings (
        StickerSettingId int IDENTITY(1,1) NOT NULL,
        CompanyId int NOT NULL,
        AccountId int,
        ExpirationHours decimal,
        PropertyApprovalRequired bit NOT NULL DEFAULT(0),
        TowManagerApprovalRequired bit NOT NULL DEFAULT(0),
        AllowExtensions bit NOT NULL DEFAULT(0),
        CreateDate DATETIME NOT NULL DEFAULT getdate(),
        OwnerUserId INT NOT NULL,
        Deleted bit NOT NULL DEFAULT(0),
        DeleteDate DATETIME NULL,
        DeletedByUserId INT NULL

        CONSTRAINT [PK_StickerSettings] PRIMARY KEY CLUSTERED ([StickerSettingId] ASC),
        CONSTRAINT [FK_StickerSettings_Companies] FOREIGN KEY([CompanyId]) REFERENCES [dbo].[Companies] ([CompanyId]),
        CONSTRAINT [FK_StickerSettings_Accounts] FOREIGN KEY([AccountId]) REFERENCES [dbo].[Accounts] ([AccountId]),
        CONSTRAINT [FK_StickerSettings_Users] FOREIGN KEY(OwnerUserId) REFERENCES [dbo].[Users] (UserId)
    )
GO
GRANT SELECT ON StickerSettings TO public
GRANT INSERT ON StickerSettings TO public
GRANT UPDATE ON StickerSettings TO public




-- End of Stickers


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryAttributes' AND COLUMN_NAME = 'PrintOnTowInvoice')
    ALTER TABLE DispatchEntryAttributes ADD PrintOnTowInvoice bit default(0) NOT NULL

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryAttributes' AND COLUMN_NAME = 'PrintOnImpoundInvoice')
    ALTER TABLE DispatchEntryAttributes ADD PrintOnImpoundInvoice bit default(0) NOT NULL

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryAttributes' AND COLUMN_NAME = 'PrintOnStatement')
    ALTER TABLE DispatchEntryAttributes ADD PrintOnStatement bit default(0) NOT NULL

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryAttributes' AND COLUMN_NAME = 'AllowDriversToView')
    ALTER TABLE DispatchEntryAttributes ADD AllowDriversToView bit default(0) NOT NULL

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryAttributes' AND COLUMN_NAME = 'AllowDispatchersToView')
    ALTER TABLE DispatchEntryAttributes ADD AllowDispatchersToView bit default(0) NOT NULL

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryAttributes' AND COLUMN_NAME = 'AllowAccountUsersToView')
    ALTER TABLE DispatchEntryAttributes ADD AllowAccountUsersToView bit default(0) NOT NULL
	
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryAttributes' AND COLUMN_NAME = 'ImpoundsOnly')
    ALTER TABLE DispatchEntryAttributes ADD ImpoundsOnly BIT NULL


-- Sendgrid

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='SendgridBounces')
    CREATE TABLE [dbo].[SendgridBounces](
        [SendgridBounceId] [int] IDENTITY(1,1) NOT NULL,
        [Status] [varchar](50) NOT NULL,
        [Created] [datetime] default(getdate()) NOT NULL,
        [Reason] [varchar](1000) NULL,
        [EmailAddress] [varchar](255) NOT NULL,
        
        CONSTRAINT [PK_SendgridBounces] PRIMARY KEY CLUSTERED ( [SendgridBounceId] ASC )
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='SendgridUnbounces')
    CREATE TABLE [dbo].[SendgridUnbounces](
        [SendgridUnbounceId] [int] IDENTITY(1,1) NOT NULL,
        [EmailAddress] [varchar](255) NOT NULL,
        [Created] [datetime] NOT NULL,
        [Warning] [varchar](1000) NULL,
        
        CONSTRAINT [PK_SendgridUnblocks] PRIMARY KEY CLUSTERED ( [SendgridUnbounceId] ASC )
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='EmailTransactions')
    CREATE TABLE [dbo].[EmailTransactions](
        [EmailTransactionId] [int] IDENTITY(1,1) NOT NULL,
        [EmailAddress] [varchar](255) NOT NULL,
        [UserId] [int] NULL,
        [CreateDate] [datetime] NOT NULL,
        [Status] [int] NOT NULL,
        [CompanyId] [int] NULL,
        [Type] [int] NOT NULL,
        
        CONSTRAINT [PK_EmailTransactions] PRIMARY KEY CLUSTERED ( [EmailTransactionId] ASC ),
        CONSTRAINT [FK_EmailTransactions_Companies] FOREIGN KEY([CompanyId]) REFERENCES [dbo].[Companies] ([CompanyId]),
        CONSTRAINT [FK_EmailTransactions_Users] FOREIGN KEY([UserId]) REFERENCES [dbo].[Users] ([UserId])
    )
GO

IF (NOT EXISTS(SELECT * FROM sys.indexes  WHERE name='IX_EmailTransactions_ByCompanyAndType'))
    CREATE INDEX [IX_EmailTransactions_ByCompanyAndType] ON [dbo].[EmailTransactions] (CompanyId, Type)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='EmailTransactionDispatchEntries')
    CREATE TABLE [dbo].[EmailTransactionDispatchEntries](
        [EmailTransactionId] [int] NOT NULL,
        [DispatchEntryId] [int] NOT NULL,
        
        CONSTRAINT [FK_EmailTransactionDispatchEntries_DispatchEntries] FOREIGN KEY([DispatchEntryId]) REFERENCES [dbo].[DispatchEntries] ([DispatchEntryId]),
        CONSTRAINT [FK_EmailTransactionDispatchEntries_EmailTransactions] FOREIGN KEY([EmailTransactionId]) REFERENCES [dbo].[EmailTransactions] ([EmailTransactionId])
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='EmailTransactionStatements')
    CREATE TABLE [dbo].[EmailTransactionStatements](
        [EmailTransactionId] [int] NOT NULL,
        [StatementId] [int] NOT NULL,
        
        CONSTRAINT [FK_EmailTransactionStatements_EmailTransactions] FOREIGN KEY([EmailTransactionId]) REFERENCES [dbo].[EmailTransactions] ([EmailTransactionId]),
        CONSTRAINT [FK_EmailTransactionStatements_Statements] FOREIGN KEY([StatementId]) REFERENCES [dbo].[Statements] ([StatementId])
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='EmailTransactionImpounds')
    CREATE TABLE [dbo].[EmailTransactionImpounds](
        [EmailTransactionId] [int] NOT NULL,
        [ImpoundId] [int] NOT NULL,
        
        CONSTRAINT [FK_EmailTransactionImpounds_EmailTransactions] FOREIGN KEY([EmailTransactionId]) REFERENCES [dbo].[EmailTransactions] ([EmailTransactionId]),
        CONSTRAINT [FK_EmailTransactionImpounds_Impounds] FOREIGN KEY([ImpoundId]) REFERENCES [dbo].[Impounds] ([ImpoundId])
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='EmailTransactionAccounts')
    CREATE TABLE [dbo].[EmailTransactionAccounts](
        [EmailTransactionId] [int] NOT NULL,
        [AccountId] [int] NOT NULL,
        
        CONSTRAINT [FK_EmailTransactionAccounts_EmailTransactions] FOREIGN KEY([EmailTransactionId]) REFERENCES dbo.EmailTransactions (EmailTransactionId),
        CONSTRAINT [FK_EmailTransactionAccounts_Accounts] FOREIGN KEY(AccountId) REFERENCES dbo.Accounts (AccountId)
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='EmailTransactionQuotes')
    CREATE TABLE [dbo].[EmailTransactionQuotes](
        [EmailTransactionId] [int] NOT NULL,
        [QuoteId] varchar(50) NOT NULL,
        
        CONSTRAINT [FK_EmailTransactionQuotes_EmailTransactions] FOREIGN KEY([EmailTransactionId]) REFERENCES [dbo].[EmailTransactions] ([EmailTransactionId])
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='EmailTransactionPropertyReleaseForms')
    CREATE TABLE [dbo].[EmailTransactionPropertyReleaseForms](
        [EmailTransactionId] [int] NOT NULL,
        [PropertyReleaseFormId] [int] NOT NULL,
        
        CONSTRAINT [FK_EmailTransactionsPropertyReleaseForms_EmailTransactions] FOREIGN KEY([EmailTransactionId]) REFERENCES dbo.EmailTransactions (EmailTransactionId),
        CONSTRAINT [FK_EmailTransactionsPropertyReleaseForms_PropertyReleaseForms] FOREIGN KEY(PropertyReleaseFormId) REFERENCES dbo.PropertyReleaseForms (PropertyReleaseFormId)
    )
GO
GRANT SELECT, UPDATE, INSERT on EmailTransactionPropertyReleaseForms to public
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='EmailTransactionEventStatuses' AND TABLE_SCHEMA='dbo')
    CREATE TABLE [dbo].[EmailTransactionEventStatuses](
        [EmailTransactionEventStatusId] INT IDENTITY(1,1) NOT NULL,
        [EmailTransactionId] INT NOT NULL,
        [Status] INT NOT NULL,
        [CreateDate] datetime NOT NULL DEFAULT(GetDate()),
        [LastUpdatedAt] datetime NULL,
        [LastSendGridDeliveryEventId] INT NULL,
        [DispatchEntryId] [int] NULL,
        [StatementId] [int] NULL
        
        CONSTRAINT [PK_EmailTransactionEventStatuses] PRIMARY KEY CLUSTERED ( [EmailTransactionId] ASC ),
        CONSTRAINT [FK_EmailTransactionEventStatuses_EmailTransactions] FOREIGN KEY([EmailTransactionId]) REFERENCES [dbo].[EmailTransactions] ([EmailTransactionId]),
        CONSTRAINT [FK_EmailTransactions_DispatchEntries] FOREIGN KEY([DispatchEntryId]) REFERENCES [dbo].[DispatchEntries] ([DispatchEntryId]),
        CONSTRAINT [FK_EmailTransactions_Statements] FOREIGN KEY([StatementId]) REFERENCES [dbo].[Statements] ([StatementId])
    );

    CREATE NONCLUSTERED INDEX IK_EmailTransactionEventStatuses_TransactionId ON dbo.EmailTransactionEventStatuses (EmailTransactionId, DispatchEntryId, StatementId)
GO
GRANT SELECT ON EmailTransactionEventStatuses TO PUBLIC
GRANT UPDATE ON EmailTransactionEventStatuses TO PUBLIC
GRANT INSERT ON EmailTransactionEventStatuses TO PUBLIC
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='EmailTransactionEventStatuses' AND COLUMN_NAME = 'SmtpId')
    ALTER TABLE EmailTransactionEventStatuses ADD SmtpId varchar(256)


-- sendgrid webhook event logs
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='SendGridDeliveryEvents' AND TABLE_SCHEMA='dbo')
    CREATE TABLE [dbo].[SendGridDeliveryEvents](
        SendGridDeliveryEventId INT IDENTITY(1,1) NOT NULL,
        Email VARCHAR(128) NOT NULL,
        SmtpId VARCHAR(256) NULL,
        CreateDate datetime NOT NULL,
        EventType VARCHAR(100) NOT NULL,
        EventId VARCHAR(100) NULL,
        MessageId VARCHAR(500) NULL,
        Tls VARCHAR(100) NULL,
        EmailTransactionIdsJson VARCHAR(2000) NULL,
        UniqueArgsJson VARCHAR(MAX) NULL,
        CategoryJson VARCHAR(MAX) NULL,
        Reason VARCHAR(MAX) NULL,
        StatusCode VARCHAR(100) NULL,
        Response VARCHAR(1000) NULL,
        Attempt INT NULL,
        BounceType VARCHAR(100) NULL,
        BounceClassification VARCHAR(100) NULL

        CONSTRAINT [PK_SendGridDeliveryEvents] PRIMARY KEY CLUSTERED ( [SendGridDeliveryEventId] ASC ),
    )
GO
GRANT SELECT ON SendGridDeliveryEvents TO PUBLIC
GRANT UPDATE ON SendGridDeliveryEvents TO PUBLIC
GRANT INSERT ON SendGridDeliveryEvents TO PUBLIC
GO

IF (NOT EXISTS(SELECT * FROM sys.indexes  WHERE name='IX_SendGridDeliveryEventId_SmtpId_And_EventType'))
    CREATE INDEX [IX_SendGridDeliveryEventId_SmtpId_And_EventType] ON [dbo].[SendGridDeliveryEvents] (SmtpId, EventType)
GO

-- End of Sendgrid


--
-- Table to hold GPS location system sources. TomTom, DriverLocate, etc.
--
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='LocationSources')
    CREATE TABLE [dbo].[LocationSources] (
        SourceId int NOT NULL,
        Name varchar(50),
        Notes varchar(5000),
        CreateDate datetime default(getdate())
        
        CONSTRAINT PK_LocationSources PRIMARY KEY CLUSTERED ( SourceId ASC )
    )
GO


IF (NOT EXISTS(SELECT 1 FROM LocationSources WHERE Name='Samsara'))
INSERT INTO LocationSources (SourceId, Name)
VALUES(6, 'Samsara')
GO

IF (NOT EXISTS(SELECT 1 FROM LocationSources WHERE Name='Fleetmatics'))
INSERT INTO LocationSources (SourceId, Name)
VALUES(8, 'Fleetmatics')
GO

--
-- Towbook Android/iOS User Locations
-- 

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='UserLocationHistoryItems')
    CREATE TABLE [dbo].[UserLocationHistoryItems] (
        [UserLocationHistoryItemId] [int] IDENTITY(1,1) NOT NULL,
        [UserId] int NOT NULL,
        [Latitude] decimal(9,6) NOT NULL,
        [Longitude] decimal(9,6) NOT NULL,
        [Timestamp] datetime default(getdate())
        
        CONSTRAINT [PK_UserLocationHistoryItems] PRIMARY KEY CLUSTERED ( [UserLocationHistoryItemId] ASC )
        CONSTRAINT [FK_UserLocationHistoryItems_Users] FOREIGN KEY([UserId]) REFERENCES [dbo].[Users] ([UserId]),
    )
GO



--
-- Users Current Location
-- 

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='UserLocationsCurrent')
    CREATE TABLE [dbo].[UserLocationsCurrent] (
        [UserId] int NOT NULL,
        [Latitude] decimal(9,6) NOT NULL,
        [Longitude] decimal(9,6) NOT NULL,
        [Timestamp] datetime default(getdate())
        
        CONSTRAINT PK_UserLocationsCurrent PRIMARY KEY CLUSTERED ( UserId ASC )
        
        CONSTRAINT PK_UserLocationsCurrent_Users FOREIGN KEY([UserId]) REFERENCES [dbo].[Users] ([UserId]),
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='TruckLocationsCurrent')
    CREATE TABLE [dbo].[TruckLocationsCurrent] (
        TruckId int NOT NULL,
        SourceId int NOT NULL,
        Latitude decimal(9,6) NOT NULL,
        Longitude decimal(9,6) NOT NULL,
        Timestamp datetime default(getdate())
        
        CONSTRAINT PK_TruckLocationsCurrent PRIMARY KEY CLUSTERED ( TruckId ASC, SourceId ASC )

        CONSTRAINT PK_TruckLocationsCurrent_Trucks FOREIGN KEY(TruckId) REFERENCES dbo.Trucks (TruckId),
        CONSTRAINT PK_TruckLocationsCurrent_LocationSources FOREIGN KEY(SourceId) REFERENCES dbo.LocationSources (SourceId)
    )
GO




--
-- External GPS locations for devices
--

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='ExternalLocationHistoryItems')
    CREATE TABLE [dbo].[ExternalLocationHistoryItems] (
        [ExternalLocationHistoryItemId] [int] IDENTITY(1,1) NOT NULL,
        [CompanyId] int NOT NULL,
        [DeviceId] varchar(50) NOT NULL,
        [Latitude] decimal(9,6) NOT NULL,
        [Longitude] decimal(9,6) NOT NULL,
        [Timestamp] datetime default(getdate())
        
        CONSTRAINT [PK_ExternalLocationHistoryItems] PRIMARY KEY CLUSTERED ( [ExternalLocationHistoryItemId] ASC )
        
        CONSTRAINT [FK_ExternalLocationHistoryItems_Companies] FOREIGN KEY(CompanyId) REFERENCES [dbo].[Companies] (CompanyId),
    )
GO

--
-- Truck GPS Location History (for when data comes from an external tracking system such as DriverLocate, etc)
-- SourceId describes what system the GPS data came from. 
--
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='TruckLocationHistoryItems')
    CREATE TABLE [dbo].[TruckLocationHistoryItems] (
        TruckLocationHistoryItemId int IDENTITY(1,1) NOT NULL,
        TruckId int NOT NULL,
        SourceId int NOT NULL,
        Latitude decimal(9,6) NOT NULL,
        Longitude decimal(9,6) NOT NULL,
        [Timestamp] datetime default(getdate())
        
        CONSTRAINT PK_TruckLocationHistoryItems PRIMARY KEY CLUSTERED ( TruckLocationHistoryItemId ASC )
        CONSTRAINT FK_TruckLocationHistoryItems_Trucks FOREIGN KEY(TruckId) REFERENCES dbo.Trucks (TruckId)
    )
GO




-- Payment verifications

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryPayments' AND COLUMN_NAME = 'PaymentVerificationId')
    ALTER TABLE DispatchEntryPayments ADD PaymentVerificationId int

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='PaymentVerifications')
    CREATE TABLE [dbo].[PaymentVerifications] (
        [PaymentVerificationId] [int] IDENTITY(1,1) NOT NULL,
        [UserId] [int] NOT NULL,
        [CompanyId] [int] NOT NULL,
        [CreateDate] [datetime] NOT NULL,
        [IPAddress] [varchar](255) NOT NULL,
        [Status] [int] NOT NULL,
        [Deleted] [bit] DEFAULT(0) NOT NULL,
        [Notes] [varchar](1000) NULL,
        
        CONSTRAINT [PK_PaymentVerifications] PRIMARY KEY CLUSTERED ( [PaymentVerificationId] ASC ),
        CONSTRAINT [FK_PaymentVerifications_Users] FOREIGN KEY([UserId]) REFERENCES [dbo].[Users] ([UserId])
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='PaymentVerifications' AND COLUMN_NAME = 'EntryPaymentId')
    ALTER TABLE PaymentVerifications ADD EntryPaymentId int NULL

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='PaymentVerifications' AND COLUMN_NAME = 'DeletedByUserId')
    ALTER TABLE PaymentVerifications ADD DeletedByUserId int NULL

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='PaymentVerifications' AND COLUMN_NAME = 'DeletedDate')
    ALTER TABLE PaymentVerifications ADD DeletedDate DateTime NULL

GRANT SELECT ON PaymentVerifications TO public
GRANT INSERT ON PaymentVerifications TO public
GRANT UPDATE ON PaymentVerifications TO public

-- End of Payment verifications



--
/*************************************************************************************************
 ** ACTIVITY LOGGING
 *************************************************************************************************/

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='ActivityLogActions')
    CREATE TABLE [dbo].[ActivityLogActions] (
        [ActionId] smallint NOT NULL,
        [Name] varchar(50),
        [Description] varchar(500),
        [CreateDate] datetime NOT NULL DEFAULT (getdate())
        
        CONSTRAINT PK_ActivityLogActions PRIMARY KEY CLUSTERED (ActionId ASC)
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='ActivityLogObjectTypes')
    CREATE TABLE [dbo].[ActivityLogObjectTypes] (
        [ObjectTypeId] smallint NOT NULL,
        [Name] varchar(50),
        [Description] varchar(500),
        [CreateDate] datetime NOT NULL DEFAULT (getdate())
        
        CONSTRAINT PK_ActivityLogObjectTypes PRIMARY KEY CLUSTERED (ObjectTypeId ASC)
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='ActivityLogItems')
    CREATE TABLE [dbo].[ActivityLogItems] (
        [ActivityLogItemId] bigint NOT NULL,
        [UserId] int,
        [ObjectTypeId] smallint NOT NULL,
        [ActionId] smallint NOT NULL,
        [CreateDate] datetime NOT NULL,
        [IpAddress] varchar(255) NOT NULL,
        [ObjectId] int, 

        CONSTRAINT PK_ActivityLog PRIMARY KEY CLUSTERED (ActivityLogItemId ASC),
        CONSTRAINT FK_ActivityLog_Users FOREIGN KEY(UserId) REFERENCES dbo.Users (UserId),
        CONSTRAINT FK_ActivityLog_Actions FOREIGN KEY(ActionId) REFERENCES dbo.ActivityLogActions (ActionId),
        CONSTRAINT FK_ActivityLog_ObjectTypes FOREIGN KEY(ObjectTypeId) REFERENCES dbo.ActivityLogObjectTypes (ObjectTypeId)
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='ActivityLogItemDetailTypes')
    CREATE TABLE [dbo].[ActivityLogItemDetailTypes] (
        [ActivityLogItemDetailTypeId] smallint,
        [ObjectTypeId] smallint,
        [Name] varchar(50), 
        [Description] varchar(500)
        
        CONSTRAINT PK_ActivityLogItemDetailTypes PRIMARY KEY CLUSTERED ([ActivityLogItemDetailTypeId] ASC),
        CONSTRAINT FK_ActivityLogItemDetailTypes_ObjectTypes FOREIGN KEY(ObjectTypeId) REFERENCES dbo.ActivityLogObjectTypes (ObjectTypeId)
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='ActivityLogItemDetails')
    CREATE TABLE [dbo].[ActivityLogItemDetails] (
        [ActivityLogItemDetailId] bigint IDENTITY(1,1) NOT NULL,
        [ActivityLogItemId] bigint,
        [ActivityLogItemDetailTypeId] smallint,
        [Data] varchar(7500)
        
        CONSTRAINT PK_ActivityLogItemDetails PRIMARY KEY CLUSTERED (ActivityLogItemDetailId ASC),
        CONSTRAINT FK_ActivityLogItemDetails_ActivityLogItems FOREIGN KEY(ActivityLogItemId) REFERENCES dbo.ActivityLogItems (ActivityLogItemId),
        CONSTRAINT FK_ActivityLogItemDetails_ActivityLogItemDetailTypes FOREIGN KEY(ActivityLogItemDetailTypeId) REFERENCES dbo.ActivityLogItemDetailTypes (ActivityLogItemDetailTypeId)
    )
GO



/*************************************************************************************************
 ** ENTRY WAYPOINTS
 *************************************************************************************************/

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='DispatchEntryWaypoints')
BEGIN
    CREATE TABLE [dbo].[DispatchEntryWaypoints] (
        [WaypointId] int IDENTITY(1,1) NOT NULL,
        [DispatchEntryId] int NOT NULL,
        [Position] smallint DEFAULT(1), 
        [Title] varchar(50),
        [Address] varchar(200),
        [Latitude] decimal(9,6) NOT NULL DEFAULT(0),
        [Longitude] decimal(9,6) NOT NULL DEFAULT(0),		
        [OwnerUserId] int NOT NULL,
        [CreateDate] datetime NOT NULL DEFAULT(GETDATE()),
        
        CONSTRAINT PK_DispatchEntryWaypoints PRIMARY KEY CLUSTERED ([WaypointId] ASC),
        CONSTRAINT FK_DispatchEntryWaypoints_Users FOREIGN KEY(OwnerUserId) REFERENCES dbo.Users (UserId),
        CONSTRAINT FK_DispatchEntryWaypoints_DispatchEntries FOREIGN KEY(DispatchEntryId) REFERENCES dbo.DispatchEntries (DispatchEntryId),
    )
    CREATE INDEX [IX_DispatchEntryWaypoints_DispatchEntryId] ON [dbo].[DispatchEntryWaypoints] ([DispatchEntryId])
END

GO



IF NOT EXISTS (SELECT * from INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryWaypoints' AND COLUMN_NAME = 'EnrouteTime')
    ALTER TABLE DispatchEntryWaypoints ADD EnrouteTime DATETIME

IF NOT EXISTS (SELECT * from INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryWaypoints' AND COLUMN_NAME = 'ArrivalTime')
    ALTER TABLE DispatchEntryWaypoints ADD ArrivalTime DATETIME

IF NOT EXISTS (SELECT * from INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryWaypoints' AND COLUMN_NAME = 'HasToll')
    ALTER TABLE DispatchEntryWaypoints ADD HasToll bit default(0) NOT NULL




/*************************************************************************************************
 ** DISPATCH ENTRY TAGS (Unpaid, Paid, Open Dispute, etc)
 *************************************************************************************************/

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='DispatchEntryTagDefinitions')
BEGIN
    CREATE TABLE [dbo].[DispatchEntryTagDefinitions] (
        [TagId] int IDENTITY(1,1) NOT NULL,
        [CompanyId] int,
        [Name] varchar(50),
        [Description] varchar(500),		
        [HtmlColor] varchar(10),		
        [CreateDate] datetime NOT NULL DEFAULT(GETDATE()),
        [Deleted] bit NOT NULL DEFAULT(0)
        CONSTRAINT PK_DispatchEntryTagDefinitions PRIMARY KEY CLUSTERED ([TagId] ASC),
        CONSTRAINT FK_DispatchEntryTagDefinitions_CompanyId FOREIGN KEY(CompanyId) REFERENCES dbo.Companies (CompanyId),
    )
    CREATE INDEX [IX_DispatchEntryTags_CompanyId] ON [dbo].[DispatchEntryTagDefinitions] (CompanyId, Deleted)
END

GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='DispatchEntryTags')
BEGIN
    CREATE TABLE [dbo].[DispatchEntryTags] (
        [DispatchEntryTagId] int IDENTITY(1,1) NOT NULL,
        [DispatchEntryId] int NOT NULL,
        [TagId] int NOT NULL,
        [CreateDate] datetime NOT NULL DEFAULT(GETDATE())
        CONSTRAINT PK_DispatchEntryTags PRIMARY KEY CLUSTERED ([DispatchEntryTagId] ASC),
        CONSTRAINT FK_DispatchEntryTags_DispatchEntries FOREIGN KEY(DispatchEntryId) REFERENCES dbo.DispatchEntries (DispatchEntryId),
        CONSTRAINT FK_DispatchEntryTags_Tags FOREIGN KEY(TagId) REFERENCES dbo.DispatchEntryTagDefinitions (TagId),
    )
    CREATE INDEX [IX_DispatchEntryTags_DispatchEntryId] ON [dbo].[DispatchEntryTags] ([DispatchEntryId])
    CREATE INDEX [IX_DispatchEntryTags_TagId] ON [dbo].[DispatchEntryTags] ([TagId])
END

GO

IF (NOT EXISTS(SELECT * FROM sys.indexes  WHERE name='IX_AccountRateItemRules_AccountId'))
    CREATE INDEX [IX_AccountRateItemRules_AccountId] ON [dbo].[AccountRateItemRules] (AccountId, Deleted)
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='ServiceNotifications')
    CREATE TABLE [dbo].[ServiceNotifications] (
        [NotificationId]		INT IDENTITY (1, 1) NOT NULL,
        [Type]					TINYINT default(1),
        [Title]					VARCHAR(250),
        [Message]				VARCHAR(2000) NOT NULL,
        [ExtLink]				VARCHAR(250),
        [CreateDate]			DATETIME NOT NULL DEFAULT(GetUTCDate()),
        [EndDate]				DATETIME NOT NULL DEFAULT(DATEADD(day, 14, GetUTCDate())),
        [ActionType]			TINYINT,
        [Deleted]				bit default(0) NOT NULL		
    
        CONSTRAINT PK_ServiceNotifications PRIMARY KEY CLUSTERED ([NotificationId] ASC)
    )
    
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='ServiceNotificationsUserTypeDelivery')
    CREATE TABLE [dbo].[ServiceNotificationsUserTypeDelivery] (
        [UserTypeDeliveryId]	INT IDENTITY (1, 1) NOT NULL,
        [NotificationId]		INT NOT NULL,
        [UserTypeId]			INT NOT NULL

        CONSTRAINT PK_ServiceNotificationsUserTypeDelivery PRIMARY KEY CLUSTERED ([UserTypeDeliveryId] ASC),
        CONSTRAINT FK_ServiceNotificationsUserTypeDelivery_NotificationId FOREIGN KEY(NotificationId) REFERENCES dbo.ServiceNotifications (NotificationId)
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='ServiceNotificationsClosedByUserId')
    CREATE TABLE [dbo].[ServiceNotificationsClosedByUserId] (
        [ClosedByUserId]		INT IDENTITY (1, 1) NOT NULL,
        [NotificationId]		INT NOT NULL,
        [UserId]				INT NOT NULL

        CONSTRAINT PK_ServiceNotificationsClosedByUserId PRIMARY KEY CLUSTERED ([ClosedByUserId] ASC),
        CONSTRAINT FK_ServiceNotificationsClosedByUserId_NotificationId FOREIGN KEY(NotificationId) REFERENCES dbo.ServiceNotifications (NotificationId)
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='TruckExpenseFiles')
    CREATE TABLE [dbo].[TruckExpenseFiles] (
        [TruckExpenseFileId]	INT IDENTITY (1, 1) NOT NULL,
        [TruckExpenseItemId]	INT NOT NULL,
        [FileId]				INT NOT NULL

        CONSTRAINT PK_TruckExpenseFiles PRIMARY KEY CLUSTERED ([TruckExpenseFileId] ASC),
        CONSTRAINT FK_TruckExpenseFiles_TruckExpenseItems FOREIGN KEY(TruckExpenseItemId) REFERENCES dbo.TruckExpenseItems (TruckExpenseItemId),
        CONSTRAINT FK_TruckExpenseFiles_Files FOREIGN KEY(FileId) REFERENCES dbo.Files (FileId)
    )
GO

IF NOT EXISTS (SELECT * from INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='TruckExpenseItems' AND COLUMN_NAME = 'Deleted')
    ALTER TABLE TruckExpenseItems ADD Deleted bit DEFAULT((0)) NOT NULL

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='StorageRates' AND COLUMN_NAME = 'MidnightWaitForGracePeriod')
    ALTER TABLE StorageRates ADD MidnightWaitForGracePeriod bit DEFAULT((0)) NOT NULL

    
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='AccountTypes')
BEGIN
    CREATE TABLE [dbo].[AccountTypes] (
        [AccountTypeId]		INT IDENTITY (0, 1) NOT NULL,
        [Name]				VARCHAR(50) NOT NULL,
        [IsDeleted]			BIT DEFAULT(0) NOT NULL

        CONSTRAINT PK_AccountTypes PRIMARY KEY CLUSTERED (AccountTypeId ASC),
    )
    
    SET IDENTITY_INSERT AccountTypes ON
    INSERT INTO AccountTypes (AccountTypeId, Name)
    VALUES 
    (0,'Other'), 
    (1,'Police'),
    (2,'Individual'), 
    (3,'Body Shop'), 
    (4,'Insurance'), 
    (5,'Motor Club'), 
    (6,'Municipality'), 
    (7,'Service Shop'),
    (8,'Storage Facility'),
    (9,'Private Property'),
    (10,'Repossession Agency'), 
    (11,'Dealership'),
    (12,'Heavy Equipment'), 
    (13,'Fleet'),
    (14,'Broker'),
    (15,'Transport')
    SET IDENTITY_INSERT AccountTypes OFF
END
GO

IF (NOT EXISTS (SELECT 1 FROM AccountTypes WHERE AccountTypeId=14))
BEGIN
    SET IDENTITY_INSERT AccountTypes ON
        INSERT INTO AccountTypes (AccountTypeId, Name) VALUES (14,'Broker')
    SET IDENTITY_INSERT AccountTypes OFF
END

IF (NOT EXISTS (SELECT 1 FROM AccountTypes WHERE AccountTypeId=15))
BEGIN
    SET IDENTITY_INSERT AccountTypes ON
        INSERT INTO AccountTypes (AccountTypeId, Name) VALUES (15,'Transport')
    SET IDENTITY_INSERT AccountTypes OFF
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='MasterAccountRelationships')
BEGIN
    CREATE TABLE [dbo].[MasterAccountRelationships] (
        [MasterAccountRelationshipId]	INT IDENTITY (0, 1) NOT NULL,
        [Name]				VARCHAR(50) NOT NULL,
        [IsDeleted]			BIT DEFAULT(0) NOT NULL

        CONSTRAINT PK_MasterAccountRelationships PRIMARY KEY CLUSTERED (MasterAccountRelationshipId ASC),
    )

    SET IDENTITY_INSERT MasterAccountRelationships ON
    INSERT INTO MasterAccountRelationships (MasterAccountRelationshipId, Name)
    VALUES (1, 'No Integration'), 
           (2, 'Fax/Email Interation - No Official'),
           (3, 'API based'),
           (4, 'Towbook')

    SET IDENTITY_INSERT  MasterAccountRelationships OFF
END
GO




IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='MasterAccounts')
BEGIN

    CREATE TABLE [dbo].[MasterAccounts] (
        [MasterAccountId]	INT IDENTITY (1, 1) NOT NULL,
        [AccountTypeId]		INT NOT NULL,
        [CompanyId]			INT,
        [Name]				VARCHAR(50) NOT NULL,
        [Address]			VARCHAR(100),
        [City]				VARCHAR(50),
        [State]				VARCHAR(32),
        [Zip]				VARCHAR(32),
        [Phone]				VARCHAR(32),
        [Fax]				VARCHAR(32),
        [Email]				VARCHAR(100),
        [Country]			VARCHAR(50),
        [Latitude]			DECIMAL(9,6),
        [Longitude]			DECIMAL(9,6),
        [RelationshipId]	INT DEFAULT(0) NOT NULL,
        [IsDeleted]			BIT DEFAULT(0) NOT NULL

        CONSTRAINT PK_MasterAccounts PRIMARY KEY CLUSTERED ([MasterAccountId] ASC),
        CONSTRAINT FK_MasterAccounts_AccountTypes FOREIGN KEY(AccountTypeId) REFERENCES dbo.AccountTypes (AccountTypeId),
        CONSTRAINT FK_MasterAccounts_MasterAccountRelationships FOREIGN KEY(RelationshipId) REFERENCES dbo.MasterAccountRelationships (MasterAccountRelationshipId),
        CONSTRAINT FK_MasterAccounts_Companies  FOREIGN KEY(CompanyId) REFERENCES dbo.Companies (CompanyId)
    )

    SET IDENTITY_INSERT MasterAccounts ON 
    INSERT INTO MasterAccounts (MasterAccountId, AccountTypeId, Name, RelationshipId, Country, Address, City, State, Zip) values (1,5, 'Road America', 2, 'USA', 'PO BOX 528024', 'Miami', 'FL', '33152')
    INSERT INTO MasterAccounts (MasterAccountId, AccountTypeId, Name, RelationshipId, Country, Address, City, State, Zip) values (2,5, 'Allstate', 2, 'USA','PO Box 3094','Arlington Heights','IL','60006')
    INSERT INTO MasterAccounts (MasterAccountId, AccountTypeId, Name, RelationshipId, Country, Address, City, State, Zip) values (3,5, 'Agero', 2, 'USA', '1 Cabot Rd', 'Medford', 'MA', '02155')
    INSERT INTO MasterAccounts (MasterAccountId, AccountTypeId, Name, RelationshipId, Country, Address, City, State, Zip) values (4,5, 'Quest', 2, 'USA', '106 W. Tolles Dr.', 'St. Johns', 'MI',  '48879')
    INSERT INTO MasterAccounts (MasterAccountId, AccountTypeId, Name, RelationshipId, Country, Address, City, State, Zip) values (5,5, 'Geico', 2, 'USA', 'PO BOX 8075','Macon', 'GA', '31208-8075')
    INSERT INTO MasterAccounts (MasterAccountId, AccountTypeId, Name, RelationshipId, Country, Address, City, State, Zip) values (6,5, 'Auto Help Line of America',2, 'USA', '7007 13th Avenue', 'Brooklyn', 'NY', '11228')
    INSERT INTO MasterAccounts (MasterAccountId, AccountTypeId, Name, RelationshipId, Country, Address, City, State, Zip) values (7,5, 'NSD', 2, 'USA', '800 Yamato Road STE 100', 'Boca Raton', 'FL', '33431')
    INSERT INTO MasterAccounts (MasterAccountId, AccountTypeId, Name, RelationshipId, Country, Address, City, State, Zip) values (8,5, 'Net Cost', 2, 'USA', 'P.O. Box 681068', 'Schaumburg', 'IL', '60168')
    INSERT INTO MasterAccounts (MasterAccountId, AccountTypeId, Name, RelationshipId, Country, Address, City, State, Zip) values (9,5, 'AAA', 2, 'USA', '', '', '', '')
    INSERT INTO MasterAccounts (MasterAccountId, AccountTypeId, Name, RelationshipId, Country, Address, City, State, Zip, latitude, longitude) values (10,1, 'Marysville Police Department', 4, 'USA', '1355 Delaware Ave.', 'Marysville', 'MI', '48040', 42.913846, -82.474159)
    INSERT INTO MasterAccounts (MasterAccountId, AccountTypeId, Name, RelationshipId, Country, Address, City, State, Zip, latitude, longitude) values (11,8, 'KTL Auto Storage', 4, 'USA', '1700 Brittmoore', 'Houston', 'TX', '77043', 29.806245,-82.474159)
    SET IDENTITY_INSERT MasterAccounts OFF
END


GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='StatementBatches')
BEGIN
    CREATE TABLE [dbo].[StatementBatches] (
        [StatementBatchId]	INT IDENTITY (1, 1) NOT NULL,
        [CompanyId]				INT NOT NULL,
        [OwnerUserId]			INT NOT NULL,
        [CreateDate]	DATETIME DEFAULT(getdate()),
        [Deleted]		BIT default(0) NOT NULL

        CONSTRAINT PK_StatementBatches PRIMARY KEY CLUSTERED (StatementBatchId ASC),
        CONSTRAINT FK_StatementBatches_Companies  FOREIGN KEY(CompanyId) REFERENCES dbo.Companies (CompanyId),
        CONSTRAINT FK_StatementBatches_Users  FOREIGN KEY(OwnerUserId) REFERENCES dbo.Users (UserId)
    )

    CREATE NONCLUSTERED INDEX IX_StatementBatches_CompanyId ON StatementBatches (CompanyId, Deleted)
END
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='StatementBatchStatements')
BEGIN
    CREATE TABLE [dbo].[StatementBatchStatements] (
        [StatementBatchId]	INT NOT NULL,
        [StatementId]		INT NOT NULL,

        CONSTRAINT PK_StatementBatchStatements PRIMARY KEY CLUSTERED (StatementBatchId ASC, StatementId),
        CONSTRAINT FK_StatementBatches_Statements FOREIGN KEY(StatementId) REFERENCES dbo.Statements (StatementId),
    )

    CREATE NONCLUSTERED INDEX IX_StatementBatchStatements_StatementId ON StatementBatchStatements (StatementId)
END
GO

GRANT INSERT ON StatementBatchStatements TO public
GRANT INSERT ON StatementBatches TO public
GRANT UPDATE ON StatementBatches TO public


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='UserAuthenticationTokens' AND COLUMN_NAME = 'ClientVersionId')
    ALTER TABLE UserAuthenticationTokens ADD ClientVersionId int

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='UserAuthenticationTokens' AND COLUMN_NAME = 'RegistrationHandle')
    ALTER TABLE UserAuthenticationTokens ADD RegistrationHandle varchar(255)

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='UserAuthenticationTokens' AND COLUMN_NAME = 'CompanyId')
    ALTER TABLE UserAuthenticationTokens ADD CompanyId int


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='MasterAccounts' AND COLUMN_NAME = 'SendDispatchDocumentEvents')
    ALTER TABLE MasterAccounts ADD SendDispatchDocumentEvents bit default(0)
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='MasterAccounts' AND COLUMN_NAME = 'SendDispatchSignatureEvents')
    ALTER TABLE MasterAccounts ADD SendDispatchSignatureEvents bit default(0)
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='MasterAccounts' AND COLUMN_NAME = 'SendDriverEquipmentPictureEvents')
    ALTER TABLE MasterAccounts ADD SendDriverEquipmentPictureEvents bit default(0)
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='MasterAccounts' AND COLUMN_NAME = 'SendDriverEvents')
    ALTER TABLE MasterAccounts ADD SendDriverEvents bit default(0)
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='MasterAccounts' AND COLUMN_NAME = 'SendDriverLocationEvents')
    ALTER TABLE MasterAccounts ADD SendDriverLocationEvents bit default(0)
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='MasterAccounts' AND COLUMN_NAME = 'SendDriverPictureEvents')
    ALTER TABLE MasterAccounts ADD SendDriverPictureEvents bit default(0)
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='MasterAccounts' AND COLUMN_NAME = 'SendHeatMapEvents')
    ALTER TABLE MasterAccounts ADD SendHeatMapEvents bit default(0)





    
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='ActivityLogItems' AND COLUMN_NAME = 'ClientVersionId')
    ALTER TABLE ActivityLogItems ADD ClientVersionId int

GO
    
IF NOT EXISTS (SELECT 1 FROM sys.schemas WHERE name = 'Platform')
BEGIN
    EXEC( 'CREATE SCHEMA Platform' );
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='ClientVersions' AND TABLE_SCHEMA='Platform')
BEGIN
    CREATE TABLE [Platform].[ClientVersions] (
        [ClientVersionId]	INT NOT NULL IDENTITY(1,1),
        [Version]			varchar(50) NOT NULL,
        [GitHash]			varchar(50), 
        [Notes]				varchar(500),
        [CreateDate]		datetime default(getdate()),
        [ReleaseDate]		datetime default(getdate())

        CONSTRAINT PK_ClientVersions PRIMARY KEY CLUSTERED ([ClientVersionId] ASC),
        CONSTRAINT UC_ClientVersions_GitHash UNIQUE (GitHash)
    )

    CREATE NONCLUSTERED INDEX IX_ClientVersions_GitHash ON Platform.ClientVersions (GitHash)
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='ClientVersions' AND COLUMN_NAME='Type' AND TABLE_SCHEMA='Platform')
    ALTER TABLE Platform.ClientVersions ADD Type INT DEFAULT '0' NOT NULL
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Accounts' AND COLUMN_NAME = 'MasterAccountId')
BEGIN
    ALTER TABLE Accounts ADD MasterAccountId int
    
    CONSTRAINT [FK_Accounts_MasterAccounts] FOREIGN KEY ([MasterAccountId]) REFERENCES [dbo].[MasterAccounts] ([MasterAccountId])
    CREATE NONCLUSTERED INDEX IX_Accounts_MasterAccountId ON Accounts (MasterAccountId)
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Accounts' AND COLUMN_NAME = 'DefaultPriority')
    ALTER TABLE Accounts ADD DefaultPriority int

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Accounts' AND COLUMN_NAME = 'DefaultPO')
    ALTER TABLE Accounts ADD DefaultPO varchar(50)

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Accounts' AND COLUMN_NAME = 'CreditBalance')
    ALTER TABLE Accounts ADD CreditBalance money

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Accounts' AND COLUMN_NAME = 'CreditHold')
    ALTER TABLE Accounts ADD CreditHold bit default (0) NOT NULL


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Accounts' AND COLUMN_NAME = 'DefaultStorageRateItemId')
BEGIN
    ALTER TABLE Accounts ADD DefaultStorageRateItemId int

    CONSTRAINT [FK_Accounts_DefaultStorageRateItemId] FOREIGN KEY ([DefaultStorageRateItemId]) REFERENCES [dbo].[RateItems] ([RateItemId])
END


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Accounts' AND COLUMN_NAME = 'Latitude')
    ALTER TABLE Accounts ADD [Latitude] [decimal](9, 6) NULL

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Accounts' AND COLUMN_NAME = 'Longitude')
    ALTER TABLE Accounts ADD [Longitude] [decimal](9, 6) NULL


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='DispatchReasonGroups')
BEGIN
    CREATE TABLE dbo.DispatchReasonGroups  (
        [DispatchReasonGroupId]	INT NOT NULL IDENTITY(1,1),
        [Name]		varchar(50) NOT NULL,
        [Description] varchar(500),
        [CreateDate]		datetime default(getdate()),

        CONSTRAINT PK_DispatchReasonGroups PRIMARY KEY CLUSTERED (DispatchReasonGroupId ASC)
    )
END
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='DispatchReasonGroupReasons')
BEGIN
    CREATE TABLE dbo.DispatchReasonGroupReasons  (
        [DispatchReasonGroupId]	INT,
        [DispatchReasonId] INT

        CONSTRAINT PK_DispatchReasonGroupReasons PRIMARY KEY CLUSTERED (DispatchReasonGroupId, DispatchReasonId ASC)
    )

    CREATE NONCLUSTERED INDEX IK_DispatchReasonGroupReasons_DispatchReasonGroupId ON dbo.DispatchReasonGroupReasons (DispatchReasonGroupId)
END
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='CompanyDispatchGroups')
BEGIN
    CREATE TABLE dbo.CompanyDispatchGroups  (
        [CompanyDispatchReasonGroupId]	INT NOT NULL IDENTITY(1,1),
        [DispatchReasonGroupId]	INT,
        [CompanyId] INT

        CONSTRAINT PK_CompanyDispatchGroups PRIMARY KEY CLUSTERED ([CompanyDispatchReasonGroupId]),
        
        CONSTRAINT FK_CompanyDispatchGroups_Companies FOREIGN KEY(CompanyId) REFERENCES dbo.Companies (CompanyId),
        CONSTRAINT FK_CompanyDispatchGroups_DispatchReasonGroups FOREIGN KEY(DispatchReasonGroupId) REFERENCES dbo.DispatchReasonGroups (DispatchReasonGroupId)
    )

    CREATE NONCLUSTERED INDEX IK_CompanyDispatchGroups_CompanyId ON dbo.CompanyDispatchGroups (CompanyId)
END
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='Disclaimers')
BEGIN
    CREATE TABLE dbo.Disclaimers  (
        [DisclaimerId]	INT NOT NULL IDENTITY(1,1),
        [CompanyId]	INT NOT NULL,
        [DispatchReasonId] INT,
        [Content] VARCHAR(4000),
        [Deleted] bit NOT NULL DEFAULT(0)

        CONSTRAINT PK_Disclaimers PRIMARY KEY CLUSTERED ([DisclaimerId] ASC),
        
        CONSTRAINT FK_Disclaimers_Companies FOREIGN KEY(CompanyId) REFERENCES dbo.Companies (CompanyId),
        CONSTRAINT FK_Disclaimers_DispatchReasons FOREIGN KEY(DispatchReasonId) REFERENCES dbo.DispatchReasons (DispatchReasonId)
    )

    CREATE NONCLUSTERED INDEX IK_Disclaimers_CompanyId ON dbo.Disclaimers (CompanyId, Deleted)
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Disclaimers' AND COLUMN_NAME = 'Impound')
    ALTER TABLE [dbo].[Disclaimers] 
        ADD Impound bit NOT NULL DEFAULT(0)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Disclaimers' AND COLUMN_NAME = 'AccountId')
    ALTER TABLE [dbo].[Disclaimers] 
        ADD AccountId int NULL
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='ElectronicConsentDisclaimers')
    CREATE TABLE dbo.ElectronicConsentDisclaimers (  
        DisclaimerId int IDENTITY(1,1) NOT NULL,
        CompanyId INT,
        AccountId INT,
        Content varchar(max) NULL,
        CreateDate datetime NOT NULL,
        OwnerUserId int NOT NULL,
        Deleted bit DEFAULT(0),
        DeleteDate datetime NULL,
        DeletedByUserId int NULL,

        CONSTRAINT PK_ElectronicConsentDisclaimers PRIMARY KEY CLUSTERED ([DisclaimerId] ASC),
        
        CONSTRAINT FK_ElectronicConsentDisclaimers_Companies FOREIGN KEY (CompanyId) REFERENCES dbo.Companies (CompanyId),
        CONSTRAINT FK_ElectronicConsentDisclaimers_Accounts FOREIGN KEY (AccountId) REFERENCES dbo.Accounts (AccountId),
        CONSTRAINT FK_ElectronicConsentDisclaimers_Users FOREIGN KEY (OwnerUserId) REFERENCES dbo.Users (UserId)
    )
GO

IF NOT EXISTS(SELECT * FROM sys.indexes WHERE object_id = object_id('dbo.ElectronicConsentDisclaimers') AND NAME ='IK_ElectronicConsentDisclaimers_CompanyId')
    CREATE NONCLUSTERED INDEX IK_ElectronicConsentDisclaimers_CompanyId ON dbo.ElectronicConsentDisclaimers (CompanyId, Deleted)
GO

/* Default consent agreement */
IF (SELECT COUNT(1) FROM dbo.ElectronicConsentDisclaimers where DisclaimerId=1) = 0
BEGIN
    SET IDENTITY_INSERT dbo.ElectronicConsentDisclaimers ON
    insert into dbo.ElectronicConsentDisclaimers (DisclaimerId, CompanyId, AccountId, Content, CreateDate, OwnerUserId) values (1, NULL, NULL, 'Default consent agreement to do business electronically.  THIS NEEDS TO BE UPDATED. This is just a placeholder.', GetDate(), 1)
    SET IDENTITY_INSERT dbo.ElectronicConsentDisclaimers OFF
END
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='CompanyDispatchReasonGroups')
BEGIN
    CREATE TABLE dbo.CompanyDispatchReasonGroups  (
        [CompanyId]	INT NOT NULL,
        [DispatchReasonGroupId] INT NOT NULL
        
        CONSTRAINT PK_CompanyDispatchReasonGroups PRIMARY KEY CLUSTERED (DispatchReasonGroupId, CompanyId ASC)

        CONSTRAINT FK_CompanyDispatchReasonGroups_Companies FOREIGN KEY(CompanyId) REFERENCES dbo.Companies (CompanyId),
        CONSTRAINT FK_CompanyDispatchReasonGroups_DispatchReasonGroups FOREIGN KEY(DispatchReasonGroupId) REFERENCES dbo.DispatchReasonGroups (DispatchReasonGroupId)
    )

    CREATE NONCLUSTERED INDEX IK_CompanyDispatchReasonGroups_CompanyId ON dbo.CompanyDispatchReasonGroups (CompanyId)
END
GO

IF (EXISTS(select 1 from sys.indexes where name = 'IX_DispatchEntryAssetDrivers_Drivers'
    AND object_id = OBJECT_ID('dbo.DispatchEntryAssetDrivers')))
BEGIN
    CREATE NONCLUSTERED INDEX IX_DispatchEntryAssetDrivers_Drivers ON dbo.DispatchEntryAssetDrivers (
        DriverId ASC, AssetId ASC )
        WITH ( DROP_EXISTING=ON, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON )
END
ELSE
BEGIN
    CREATE NONCLUSTERED INDEX IX_DispatchEntryAssetDrivers_Drivers ON dbo.DispatchEntryAssetDrivers (
        DriverId ASC, AssetId ASC )
        WITH ( ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON )
END


IF (EXISTS(select 1 from sys.indexes where name = 'IX_DispatchEntryAssetDrivers_Trucks'
    AND object_id = OBJECT_ID('dbo.DispatchEntryAssetDrivers')))
BEGIN
    CREATE NONCLUSTERED INDEX IX_DispatchEntryAssetDrivers_Trucks ON dbo.DispatchEntryAssetDrivers (
        TruckId ASC, AssetId ASC )
        WITH ( DROP_EXISTING=ON, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON )
END
ELSE
BEGIN
    CREATE NONCLUSTERED INDEX IX_DispatchEntryAssetDrivers_Trucks ON dbo.DispatchEntryAssetDrivers (
        TruckId ASC, AssetId ASC )
        WITH ( ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON )
END


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='ImpoundLots' AND COLUMN_NAME = 'DefaultTaxRateId')
    ALTER TABLE ImpoundLots ADD DefaultTaxRateId int
GO

IF OBJECT_ID('dbo.[vwImpounds]') IS NULL EXEC ('CREATE VIEW dbo.[vwImpounds] AS SELECT 1 as Temp')
GO
ALTER VIEW [dbo].[vwImpounds]
AS  
SELECT     ImpoundId, CompanyId, DispatchEntryId, PropertyNumber, OwnerUserId, ImpoundDate, ReleaseDate, ReleaseReason, ReleaseNotes, Hold, CreateDate,   
                      CAST(CASE WHEN (dbo.Impounds.ReleaseDate IS NOT NULL) THEN 1 ELSE 0 END AS bit) AS Released, WinchingFee, CleanupFee, Miles, TowTypeId, AccountId,   
                      MilePrice, Deleted, ReleasePickupDate, ImpoundLotId, ImpoundTypeId, Reason, OwnerName, OwnerAddress, OwnerCity, OwnerState, OwnerZip, OwnerPhone,   
                      LienholderName, LienholderAddress, LienholderState, LienholderCity, LienholderZip, LienholderPhone, HasKeys, IsTaxExempt, MotorVehicleReportDate, Auction  
,StatusId  
FROM         dbo.Impounds  
GO

IF NOT EXISTS (SELECT * FROM SYS.EXTENDED_PROPERTIES WHERE 
    [major_id] = OBJECT_ID('dbo.vwImpounds') AND [name] = N'MS_DiagramPane1' AND [minor_id] = 0)
EXEC sp_addextendedproperty N'MS_DiagramPane1', N'[0E232FF0-B466-11cf-A24F-00AA00A3EFFF, 1.00]
Begin DesignProperties = 
   Begin PaneConfigurations = 
      Begin PaneConfiguration = 0
         NumPanes = 4
         Configuration = "(H (1[40] 4[20] 2[20] 3) )"
      End
      Begin PaneConfiguration = 1
         NumPanes = 3
         Configuration = "(H (1 [50] 4 [25] 3))"
      End
      Begin PaneConfiguration = 2
         NumPanes = 3
         Configuration = "(H (1 [50] 2 [25] 3))"
      End
      Begin PaneConfiguration = 3
         NumPanes = 3
         Configuration = "(H (4 [30] 2 [40] 3))"
      End
      Begin PaneConfiguration = 4
         NumPanes = 2
         Configuration = "(H (1 [56] 3))"
      End
      Begin PaneConfiguration = 5
         NumPanes = 2
         Configuration = "(H (2 [66] 3))"
      End
      Begin PaneConfiguration = 6
         NumPanes = 2
         Configuration = "(H (4 [50] 3))"
      End
      Begin PaneConfiguration = 7
         NumPanes = 1
         Configuration = "(V (3))"
      End
      Begin PaneConfiguration = 8
         NumPanes = 3
         Configuration = "(H (1[56] 4[18] 2) )"
      End
      Begin PaneConfiguration = 9
         NumPanes = 2
         Configuration = "(H (1 [75] 4))"
      End
      Begin PaneConfiguration = 10
         NumPanes = 2
         Configuration = "(H (1[66] 2) )"
      End
      Begin PaneConfiguration = 11
         NumPanes = 2
         Configuration = "(H (4 [60] 2))"
      End
      Begin PaneConfiguration = 12
         NumPanes = 1
         Configuration = "(H (1) )"
      End
      Begin PaneConfiguration = 13
         NumPanes = 1
         Configuration = "(V (4))"
      End
      Begin PaneConfiguration = 14
         NumPanes = 1
         Configuration = "(V (2))"
      End
      ActivePaneConfig = 0
   End
   Begin DiagramPane = 
      Begin Origin = 
         Top = 0
         Left = 0
      End
      Begin Tables = 
         Begin Table = "Impounds"
            Begin Extent = 
               Top = 6
               Left = 38
               Bottom = 273
               Right = 215
            End
            DisplayFlags = 280
            TopColumn = 24
         End
      End
   End
   Begin SQLPane = 
   End
   Begin DataPane = 
      Begin ParameterDefaults = ""
      End
      Begin ColumnWidths = 16
         Width = 284
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
      End
   End
   Begin CriteriaPane = 
      Begin ColumnWidths = 11
         Column = 1440
         Alias = 900
         Table = 1170
         Output = 720
         Append = 1400
         NewValue = 1170
         SortType = 1350
         SortOrder = 1410
         GroupBy = 1350
         Filter = 1350
         Or = 1350
         Or = 1350
         Or = 1350
      End
   End
End
', 'SCHEMA', N'dbo', 'VIEW', N'vwImpounds', NULL, NULL
GO
DECLARE @xp int
SELECT @xp=1
IF NOT EXISTS (SELECT * FROM SYS.EXTENDED_PROPERTIES WHERE
        [major_id] = OBJECT_ID('dbo.vwImpounds') AND [name] = N'MS_DiagramPaneCount' AND [minor_id] = 0)
EXEC sp_addextendedproperty N'MS_DiagramPaneCount', @xp, 'SCHEMA', N'dbo', 'VIEW', N'vwImpounds', NULL, NULL
GO


IF OBJECT_ID('dbo.[vwImpoundLots]') IS NULL EXEC ('CREATE VIEW dbo.[vwImpoundLots] AS SELECT 1 as Temp')
GO

ALTER VIEW [dbo].[vwImpoundLots]
AS
    SELECT     ImpoundLotId, CompanyId, Name, ContactPerson, Address, City, State, Zip, Phone, Email, Deleted, CreateDate, Capacity, Notes, AccountId,
          nullif(replace(replace(cast((
        select * from(select cast(ca.CompanyId as varchar) + ',' as [text()]
        from CompanyImpoundLots ca
        where ca.ImpoundLotid = ImpoundLots.ImpoundLotId
        union 
        select cast(sharedcompanyid as varchar) + ','  FROM CompaniesShared CS WHERE CS.CompanyId=ImpoundLots.CompanyId AND CS.ShareAllImpoundLots=1) x
        for xml path( '' ), root( 'X' ), type) as varchar(500)), '<X>', ''),'</X>',''), '') as Companies, DefaultTaxRateId, Latitude, Longitude
FROM 
    ImpoundLots
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='ActivityLogItems' AND COLUMN_NAME = 'ParentObjectId')
    ALTER TABLE ActivityLogItems 
        ADD [ParentObjectId] int, 
         [ParentObjectTypeId] smallint,
         CONSTRAINT FK_ActivityLogItems_ParentObjectTypes FOREIGN KEY(ParentObjectTypeId) REFERENCES dbo.ActivityLogObjectTypes (ObjectTypeId)
GO



IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='ProviderPaymentMethodKeyValues' AND COLUMN_NAME = 'CompanyId')
    ALTER TABLE Integration.ProviderPaymentMethodKeyValues 
        ADD CompanyId int, 
         CONSTRAINT FK_ProviderPaymentMethodKeyValues_CompanyId FOREIGN KEY(CompanyId) 
            REFERENCES dbo.Companies (CompanyId)
GO





/*************************************************************************************************
 ** MOTOR CLUB BILLING
 *************************************************************************************************/

IF NOT EXISTS (SELECT 1 FROM sys.schemas WHERE name = 'MCBilling')
BEGIN
    -- create a schema to hold all tables related to billing integrations for agero, geico, road america, etc.
    EXEC( 'CREATE SCHEMA MCBilling' );
END

 
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='MotorClubBillingDispatchEntryQueue' AND TABLE_SCHEMA='MCBilling')
    CREATE TABLE MCBilling.MotorClubBillingDispatchEntryQueue (
        QueueItemId		INT IDENTITY (1, 1) NOT NULL,
        StatusId		TINYINT,
        CompanyId		INT,
        DispatchEntryId	INT,
        MetaDataJson	VARCHAR(4000),
        CreateDate		datetime default(getdate())

        CONSTRAINT PK_MotorClubBillingDispatchEntryQueue PRIMARY KEY CLUSTERED (QueueItemId ASC),
        CONSTRAINT FK_MotorClubBillingDispatchEntryQueue_Companies FOREIGN KEY (CompanyId) REFERENCES dbo.Companies (CompanyId),
        CONSTRAINT FK_MotorClubBillingDispatchEntryQueue_DispatchEntries FOREIGN KEY (DispatchEntryId) REFERENCES dbo.DispatchEntries (DispatchEntryId)
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='MotorClubBillingAuthenticationQueue' AND TABLE_SCHEMA='MCBilling')
    CREATE TABLE MCBilling.MotorClubBillingAuthenticationQueue (
        QueueItemId		INT IDENTITY (1, 1) NOT NULL,
        StatusId		TINYINT,
        CompanyId		INT,
        AccountId		INT,
        MetaDataJson		VARCHAR(4000),
        CreateDate		datetime default(getdate())

        CONSTRAINT PK_MotorClubBillingAuthenticationQueue PRIMARY KEY CLUSTERED (QueueItemId ASC),
        CONSTRAINT FK_MotorClubBillingAuthenticationQueue_Companies FOREIGN KEY (CompanyId) REFERENCES dbo.Companies (CompanyId),
        CONSTRAINT FK_MotorClubBillingAuthenticationQueue_Accounts FOREIGN KEY (AccountId) REFERENCES dbo.Accounts (AccountId)
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='MotorClubBillingPaymentQueue' AND TABLE_SCHEMA='MCBilling')
    CREATE TABLE MCBilling.MotorClubBillingPaymentQueue (
        QueueItemId		INT IDENTITY (1, 1) NOT NULL,
        StatusId		TINYINT,
        CompanyId		INT,
        AccountId		INT,
        MetaDataJson		VARCHAR(4000),
        CreateDate		datetime default(getdate())

        CONSTRAINT PK_MotorClubBillingPaymentQueue PRIMARY KEY CLUSTERED (QueueItemId ASC),
        CONSTRAINT FK_MotorClubBillingPaymentQueue_Companies FOREIGN KEY (CompanyId) REFERENCES dbo.Companies (CompanyId),
        CONSTRAINT FK_MotorClubBillingPaymentQueue_Accounts FOREIGN KEY (AccountId) REFERENCES dbo.Accounts (AccountId)
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='MotorClubBillingPaymentQueue' AND COLUMN_NAME = 'OwnerUserId' AND TABLE_SCHEMA='MCBilling')
    ALTER TABLE MCBilling.MotorClubBillingPaymentQueue
        ADD OwnerUserId int, 
        CONSTRAINT FK_MotorClubBillingPaymentQueue_Users FOREIGN KEY(OwnerUserId) REFERENCES dbo.Users (UserId)

GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='MotorClubBillingDispatchEntryQueue' AND COLUMN_NAME = 'OwnerUserId' AND TABLE_SCHEMA='MCBilling')
    ALTER TABLE MCBilling.MotorClubBillingDispatchEntryQueue
        ADD OwnerUserId int, 
        CONSTRAINT FK_MotorClubBillingDispatchEntryQueue_Users FOREIGN KEY(OwnerUserId) REFERENCES dbo.Users (UserId)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='MotorClubBillingAuthenticationQueue' AND COLUMN_NAME = 'OwnerUserId' AND TABLE_SCHEMA='MCBilling')
    ALTER TABLE MCBilling.MotorClubBillingAuthenticationQueue
        ADD OwnerUserId int, 
        CONSTRAINT FK_MotorClubBillingAuthenticationQueue_Users FOREIGN KEY(OwnerUserId) REFERENCES dbo.Users (UserId)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='MotorClubBillingAuthenticationQueue' AND COLUMN_NAME = 'Username' AND TABLE_SCHEMA='MCBilling')
    ALTER TABLE MCBilling.MotorClubBillingAuthenticationQueue
        ADD Username varchar(100),
        Password varchar(50) 
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='MotorClubBillingDispatchEntryDetailsQueue' AND TABLE_SCHEMA='MCBilling')
    CREATE TABLE MCBilling.MotorClubBillingDispatchEntryDetailsQueue (
        QueueItemId int IDENTITY(1,1) NOT NULL,
        StatusId tinyint NULL,
        CompanyId int NOT NULL,
        AccountId int NOT NULL,
        CreateDate datetime default(getdate()) NOT NULL,
        PurchaseOrderNumbers varchar(1000) NULL

        CONSTRAINT PK_MotorClubBillingDispatchEntryDetailsQueue PRIMARY KEY CLUSTERED (QueueItemId ASC),
        CONSTRAINT FK_MotorClubBillingDispatchEntryDetailsQueue_Accounts FOREIGN KEY(AccountId) REFERENCES dbo.Accounts (AccountId),
        CONSTRAINT FK_MotorClubBillingDispatchEntryDetailsQueue_Companies FOREIGN KEY(CompanyId) REFERENCES dbo.Companies (CompanyId)
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='MotorClubBillingDispatchEntryQueue' AND COLUMN_NAME = 'Message' AND TABLE_SCHEMA='MCBilling')
    ALTER TABLE MCBilling.MotorClubBillingDispatchEntryQueue
        ADD Message varchar(50) 
    
    -- Increase Message size
    ALTER TABLE MCBilling.MotorClubBillingDispatchEntryQueue 
        ALTER COLUMN Message varchar(200) 

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='MotorClubBillingDispatchEntryQueue' AND COLUMN_NAME = 'TestMode' AND TABLE_SCHEMA='MCBilling')
    ALTER TABLE MCBilling.MotorClubBillingDispatchEntryQueue 
        ADD TestMode bit NOT NULL default 0
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='MotorClubBillingDispatchEntryQueue' AND COLUMN_NAME = 'Hidden' AND TABLE_SCHEMA='MCBilling')
    ALTER TABLE MCBilling.MotorClubBillingDispatchEntryQueue 
        ADD [Hidden] bit NOT NULL default 0
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='MotorClubBillingDispatchEntryQueue' AND COLUMN_NAME = 'ResultId' AND TABLE_SCHEMA='MCBilling')
    ALTER TABLE MCBilling.MotorClubBillingDispatchEntryQueue 
        ADD ResultId tinyint NOT NULL default 0
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='MotorClubBillingPurchaseOrderListQueue' AND TABLE_SCHEMA='MCBilling')
    CREATE TABLE MCBilling.MotorClubBillingPurchaseOrderListQueue (
        QueueItemId int IDENTITY(1,1) NOT NULL,
        StatusId int NOT NULL,
        CompanyId int NOT NULL,
        AccountId int NOT NULL,
        CreateDate datetime default(getdate()),
        ScheduleDate datetime NOT NULL

        CONSTRAINT PK_MotorClubBillingPurchaseOrderListQueue PRIMARY KEY CLUSTERED (QueueItemId ASC),
        CONSTRAINT FK_MotorClubBillingPurchaseOrderListQueue_Accounts FOREIGN KEY(AccountId) REFERENCES dbo.Accounts (AccountId),
        CONSTRAINT FK_MotorClubBillingPurchaseOrderListQueue_Companies FOREIGN KEY(CompanyId) REFERENCES dbo.Companies (CompanyId),
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='AvailablePurchaseOrders' AND TABLE_SCHEMA='MCBilling')
    CREATE TABLE MCBilling.AvailablePurchaseOrders (
        AvailablePurchaseOrderId int IDENTITY(1,1) NOT NULL,
        PurchaseOrderNumber varchar(50) NOT NULL,
        AccountId int NOT NULL,
        [Type] int NOT NULL

        CONSTRAINT PK_AvailablePurchaseOrders PRIMARY KEY CLUSTERED (AvailablePurchaseOrderId ASC),
        CONSTRAINT FK_AvailablePurchaseOrders_Accounts FOREIGN KEY(AccountId) REFERENCES dbo.Accounts (AccountId)
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='BillingAccountStatus' AND TABLE_SCHEMA='MCBilling')
    CREATE TABLE MCBilling.BillingAccountStatus (
        BillingAccountStatusId int IDENTITY(1,1) NOT NULL,
        AccountId int NOT NULL,
        LastPurchaseOrderRefreshDate datetime NULL,

        CONSTRAINT PK_BillingAccountStatus PRIMARY KEY CLUSTERED (BillingAccountStatusId ASC),
        CONSTRAINT FK_BillingAccountStatus_Accounts FOREIGN KEY(AccountId) REFERENCES dbo.Accounts (AccountId)
    )
GO

/*************************************************************************************************
 ** MOTOR CLUB DIGITAL DISPATCH
 *************************************************************************************************/

IF NOT EXISTS (SELECT 1 FROM sys.schemas WHERE name = 'MCDispatch')
BEGIN
    -- create a schema to hold all tables related to digitial dispatching integrations for agero, geico, road america, etc.
    EXEC( 'CREATE SCHEMA MCDispatch' );
END
GO

 
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='ActionQueue' AND TABLE_SCHEMA='MCDispatch')
    --
    -- Holds events to be processed by our backend queue processors (service bus is the actual queue, this just holds a long-term history of the events)
    --
    CREATE TABLE MCDispatch.ActionQueue (
        QueueItemId		INT IDENTITY (1, 1) NOT NULL,
        CompanyId		INT,
        Status		    INT,
        JsonObject		VARCHAR(MAX),
        Type			INT,
        CreateDate		DATETIME DEFAULT(GETDATE()),
        OwnerUserId     INT

        CONSTRAINT PK_ActionQueue PRIMARY KEY CLUSTERED (QueueItemId ASC),
        CONSTRAINT FK_ActionQueue_Companies FOREIGN KEY (CompanyId) REFERENCES dbo.Companies (CompanyId)
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='ActionQueue' AND COLUMN_NAME = 'AccountId' AND TABLE_SCHEMA = 'MCDispatch')
    ALTER TABLE MCDispatch.ActionQueue
        ADD AccountId int
        CONSTRAINT FK_ActionQueue_Accounts FOREIGN KEY(AccountId) REFERENCES dbo.Accounts (AccountId)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='ActionQueue' AND COLUMN_NAME = 'ScheduledDate' AND TABLE_SCHEMA = 'MCDispatch')
    ALTER TABLE MCDispatch.ActionQueue
        ADD ScheduledDate Datetime
GO

/*************************************************************************************************
 ** Customer Location Tracking
 *************************************************************************************************/

 IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='DispatchEntryLocationRequests')
    CREATE TABLE [dbo].[DispatchEntryLocationRequests](
        [DispatchEntryLocationRequestId] [int] IDENTITY(1,1) NOT NULL,
        [DispatchEntryId] [int] NOT NULL,
        [MobileNumber] [varchar](50) NOT NULL,
        [Latitude] [decimal](9, 6) NULL,
        [Longitude] [decimal](9, 6) NULL,
        [GpsAccuracy] [int] NULL,
        [LocationDate] [datetime] NULL,
        [IpAddress] [varchar](255) NULL,
        [OwnerUserId] [int] NOT NULL,	
        [CreateDate] [datetime] NOT NULL DEFAULT(getdate()),

        CONSTRAINT [PK_DispatchEntryLocationRequests] PRIMARY KEY CLUSTERED ([DispatchEntryLocationRequestId] ASC),
        CONSTRAINT [FK_DispatchEntryLocationRequests_DispatchEntries] FOREIGN KEY([DispatchEntryId]) REFERENCES [dbo].[DispatchEntries] ([DispatchEntryId]),
        CONSTRAINT [FK_DispatchEntryLocationRequests_Users] FOREIGN KEY([OwnerUserId])  REFERENCES [dbo].[Users] ([UserId])
    )

GO


 IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='DispatchEntryRequests')
    CREATE TABLE dbo.DispatchEntryRequests (
        CallRequestId int IDENTITY(1,1) NOT NULL,
        CompanyId int NOT NULL,
        AccountId int NOT NULL,
        StartingLocation varchar(255),
        Reason varchar(50),
        ServiceNeeded varchar(50),
        Vehicle varchar(50),
        RequestDate datetime NOT NULL,
        ExpirationDate datetime,
        CreateDate datetime default(getdate()) NOT NULL,
        [Status] int, 
        DispatchEntryId int,
        OwnerUserId int

        CONSTRAINT [PK_DispatchEntryRequests] PRIMARY KEY CLUSTERED (CallRequestId ASC),
        CONSTRAINT [FK_DispatchEntryRequests_DispatchEntries] FOREIGN KEY(DispatchEntryId) REFERENCES dbo.DispatchEntries (DispatchEntryId),
        CONSTRAINT [FK_DispatchEntryRequests_Companies] FOREIGN KEY(CompanyId) REFERENCES dbo.Companies (CompanyId),
        CONSTRAINT [FK_DispatchEntryRequests_Accounts] FOREIGN KEY(AccountId)  REFERENCES dbo.Accounts (AccountId)
    )
GO

grant insert on DispatchEntryRequests to public
grant update on DispatchEntryRequests to public
grant select on DispatchEntryRequests to public
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryRequests' AND COLUMN_NAME = 'PurchaseOrderNumber')
    ALTER TABLE [dbo].[DispatchEntryRequests]
        ADD PurchaseOrderNumber varchar(50)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryRequests' AND COLUMN_NAME = 'TowDestination')
    ALTER TABLE [dbo].[DispatchEntryRequests]
        ADD TowDestination varchar(255)
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryRequests' AND COLUMN_NAME = 'MaxEta')
    ALTER TABLE [dbo].[DispatchEntryRequests]
        ADD MaxEta int 
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryRequests' AND COLUMN_NAME = 'ProviderId')
    ALTER TABLE [dbo].[DispatchEntryRequests]
        ADD ProviderId varchar(50)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryRequests' AND COLUMN_NAME = 'ResponseReasonId')
    ALTER TABLE [dbo].[DispatchEntryRequests]
        ADD ResponseReasonId int
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryRequests' AND COLUMN_NAME = 'Drivers')
    ALTER TABLE [dbo].[DispatchEntryRequests]
        ADD Drivers varchar(500) -- comma delimited list of DriverIds that are preferred for the request - user can pick any of them.
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryRequests' AND COLUMN_NAME = 'Trucks')
    ALTER TABLE [dbo].[DispatchEntryRequests]
        ADD Trucks varchar(500) -- comma delimited list of TruckIds that are preferred for the request - user can pick any of them.
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryRequests' AND COLUMN_NAME = 'Distance')
    ALTER TABLE [dbo].[DispatchEntryRequests]
        ADD Distance float
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryRequests' AND COLUMN_NAME = 'Eta')
    ALTER TABLE [dbo].[DispatchEntryRequests]
        ADD Eta int null
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryRequests' AND COLUMN_NAME = 'LoadedDistance')
    ALTER TABLE [dbo].[DispatchEntryRequests]
        ADD LoadedDistance float
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryRequests' AND COLUMN_NAME = 'StartLocationLatitude')
    ALTER TABLE [dbo].[DispatchEntryRequests]
        ADD StartLocationLatitude decimal(9,6), StartLocationLongitude decimal(9,6)
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='DispatchEntryRequestEvents')
    CREATE TABLE dbo.DispatchEntryRequestEvents (
        DispatchEntryRequestEventId int IDENTITY(1,1) NOT NULL,
        CallRequestId int NOT NULL,
        UserId int NULL,
        ActionId int NOT NULL,
        [Message] varchar(255) NULL,
        JsonData varchar(max) NULL,
        [Source] int NOT NULL,
        CreateDate datetime NOT NULL

        CONSTRAINT [PK_DispatchEntryRequestEvents] PRIMARY KEY CLUSTERED (DispatchEntryRequestEventId ASC),
        CONSTRAINT [FK_DispatchEntryRequestEvents_DispatchEntryRequests] FOREIGN KEY(CallRequestId) REFERENCES dbo.DispatchEntryRequests (CallRequestId),
        CONSTRAINT [FK_DispatchEntryRequestEvents_Users] FOREIGN KEY(UserId) REFERENCES dbo.Users (UserId)
    )
GO

grant insert on DispatchEntryRequestEvents to public
grant update on DispatchEntryRequestEvents to public
grant select on DispatchEntryRequestEvents to public
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='DispatchEntryRequestsMetrics')
    CREATE TABLE dbo.DispatchEntryRequestsMetrics (
        DispatchEntryRequestsMetricId int IDENTITY(1,1) NOT NULL,
        AccountId int NOT NULL,
        ActionId int NOT NULL,
        [Count] int NOT NULL,
        CreateDate datetime NOT NULL,
        Interval int NOT NULL,

        CONSTRAINT [PK_DispatchEntryRequestsMetrics] PRIMARY KEY CLUSTERED (DispatchEntryRequestsMetricId ASC),
        CONSTRAINT [FK_DispatchEntryRequestsMetrics_Accounts] FOREIGN KEY(AccountId) REFERENCES dbo.Accounts (AccountId)
    )
GO

grant insert on DispatchEntryRequestsMetrics to public
grant update on DispatchEntryRequestsMetrics to public
grant select on DispatchEntryRequestsMetrics to public
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='DispatchEntryRequestsMetricsHistory')
    CREATE TABLE dbo.DispatchEntryRequestsMetricsHistory (
        DispatchEntryRequestsMetricsHistoryId int IDENTITY(1,1) NOT NULL,
        AccountId int NOT NULL,
        LastDispatchEntryRequestEventId int,

        CONSTRAINT [PK_DispatchEntryRequestsMetricsHistory] PRIMARY KEY CLUSTERED (DispatchEntryRequestsMetricsHistoryId ASC),
        CONSTRAINT [FK_DispatchEntryRequestsMetricsHistory_Accounts] FOREIGN KEY(AccountId) REFERENCES dbo.Accounts (AccountId),
        CONSTRAINT [FK_DispatchEntryRequestsMetricsHistory_DispatchEntryRequestEvents] FOREIGN KEY(LastDispatchEntryRequestEventId) REFERENCES dbo.DispatchEntryRequestEvents (DispatchEntryRequestEventId)
    )
GO

grant insert on DispatchEntryRequestsMetricsHistory to public
grant update on DispatchEntryRequestsMetricsHistory to public
grant select on DispatchEntryRequestsMetricsHistory to public
GO


/*************************************************************************************************
 ** Multiple Signatures
 *************************************************************************************************/

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='SignatureTypes')
    CREATE TABLE dbo.SignatureTypes (
        SignatureTypeId int IDENTITY(1,1) NOT NULL,
        [Description] varchar(100) NULL,
        IsDeleted bit NOT NULL,
        Name varchar(100) NULL,
        CompanyId int NULL,

        CONSTRAINT [PK_SignatureTypes] PRIMARY KEY CLUSTERED (SignatureTypeId ASC),
        CONSTRAINT [FK_SignatureTypes_Companies] FOREIGN KEY(CompanyId) REFERENCES dbo.Companies (CompanyId)
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='DispatchEntrySignatures')
    CREATE TABLE dbo.DispatchEntrySignatures (
        DispatchEntrySignatureId int IDENTITY(1,1) NOT NULL,
        DispatchEntryId int NOT NULL,
        SignatureTypeId int NOT NULL,
        CreateDate datetime NOT NULL,
        RemoteIp varchar(46) NULL,
        [Description] varchar(100) NULL,
        OwnerUserId int NOT NULL,
        FileSize bigint NULL,
        IsDeleted bit NOT NULL,
        ContentType varchar(50) NOT NULL,

        CONSTRAINT [PK_DispatchEntrySignatures] PRIMARY KEY CLUSTERED (DispatchEntrySignatureId ASC),
        CONSTRAINT [FK_DispatchEntrySignatures_DispatchEntries] FOREIGN KEY(DispatchEntryId) REFERENCES dbo.DispatchEntries (DispatchEntryId),
        CONSTRAINT [FK_DispatchEntrySignatures_SignatureTypes] FOREIGN KEY(SignatureTypeId) REFERENCES dbo.SignatureTypes (SignatureTypeId),
        CONSTRAINT [FK_DispatchEntrySignatures_Users] FOREIGN KEY(OwnerUserId) REFERENCES dbo.Users (UserId)
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntrySignatures' AND COLUMN_NAME = 'Latitude')
    ALTER TABLE DispatchEntrySignatures ADD Latitude decimal(9,6), Longitude decimal(9,6)



IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='UserSignatures')
    CREATE TABLE dbo.UserSignatures (
        UserSignatureId int IDENTITY(1,1) NOT NULL,
        UserId int NOT NULL,
        ElectronicConsentDisclaimerId int NULL,
        ContentType varchar(50) NOT NULL,
        FileSize bigint NULL,
        Latitude decimal(9,6),
        Longitude decimal(9,6),
        RemoteIp varchar(46) NULL,
        CreateDate datetime NOT NULL,
        IsDeleted bit NOT NULL,

        CONSTRAINT [PK_UserSignatures] PRIMARY KEY CLUSTERED (UserSignatureId ASC),
        CONSTRAINT [FK_UserSignatures_Users] FOREIGN KEY(UserId) REFERENCES dbo.Users (UserId),
        CONSTRAINT [FK_UserSignatures_ElectronicConsentDisclaimers] FOREIGN KEY(ElectronicConsentDisclaimerId) REFERENCES dbo.ElectronicConsentDisclaimers (DisclaimerId),
    )

IF NOT EXISTS(SELECT * FROM sys.indexes WHERE object_id = object_id('dbo.UserSignatures') AND NAME ='IK_UserSignatures_UserId')
    CREATE NONCLUSTERED INDEX IK_UserSignatures_UserId ON dbo.UserSignatures (UserId, IsDeleted)
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='UserSignatureAgreements')
    CREATE TABLE dbo.UserSignatureAgreements ( 
        UserSignatureAgreementId int IDENTITY(1,1) NOT NULL,
        UserId int NOT NULL,
        UserSignatureId int NOT NULL,
        SignatureTypeId int NOT NULL,
        CreateDate datetime NOT NULL,
        IsVoid bit DEFAULT(0),
        VoidedByUserId int,
        VoidedDate datetime

        CONSTRAINT [PK_UserSignatureAgreements] PRIMARY KEY CLUSTERED (UserSignatureAgreementId ASC),
        CONSTRAINT [FK_UserSignaturesAgreements_Users] FOREIGN KEY (UserId) REFERENCES dbo.Users (UserId),
        CONSTRAINT [FK_UserSignaturesAgreements_UserSignatures] FOREIGN KEY(UserSignatureId) REFERENCES dbo.UserSignatures (UserSignatureId),
        CONSTRAINT [FK_UserSignaturesAgreements_SignatureTypes] FOREIGN KEY(SignatureTypeId) REFERENCES dbo.SignatureTypes (SignatureTypeId),
    )
GO

IF NOT EXISTS(SELECT * FROM sys.indexes WHERE object_id = object_id('dbo.UserSignatureAgreements') AND NAME ='IK_UserSignatureAgreements_UserId')
    CREATE NONCLUSTERED INDEX IK_UserSignatureAgreements_UserId ON dbo.UserSignatureAgreements (UserId, IsVoid)
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='CompanySignatureTypes')
    CREATE TABLE dbo.CompanySignatureTypes (
        CompanyId int NOT NULL,
        SignatureTypeId int NOT NULL,

        CONSTRAINT [FK_CompanySignatureTypes_Companies] FOREIGN KEY(CompanyId) REFERENCES dbo.Companies (CompanyId),
        CONSTRAINT [FK_CompanySignatureTypes_SignatureTypes] FOREIGN KEY(SignatureTypeId) REFERENCES dbo.SignatureTypes (SignatureTypeId)
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='AccountSignatureTypes')
    CREATE TABLE dbo.AccountSignatureTypes (
        AccountId int NOT NULL,
        SignatureTypeId int NOT NULL,

        CONSTRAINT [FK_AccountSignatureTypes_Accounts] FOREIGN KEY(AccountId) REFERENCES dbo.Accounts (AccountId),
        CONSTRAINT [FK_AccountSignatureTypes_SignatureTypes] FOREIGN KEY(SignatureTypeId) REFERENCES dbo.SignatureTypes (SignatureTypeId)
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='MasterAccountSignatureTypes')
    CREATE TABLE dbo.MasterAccountSignatureTypes (
        MasterAccountId int NOT NULL,
        SignatureTypeId int NOT NULL,

        CONSTRAINT [FK_MasterAccountSignatureTypes_MasterAccounts] FOREIGN KEY(MasterAccountId) REFERENCES dbo.MasterAccounts (MasterAccountId),
        CONSTRAINT [FK_MasterAccountSignatureTypes_SignatureTypes] FOREIGN KEY(SignatureTypeId) REFERENCES dbo.SignatureTypes (SignatureTypeId)
    )
GO

IF OBJECT_ID('dbo.[vwSurchargeAccountRates]') IS NULL EXEC ('CREATE VIEW dbo.[vwSurchargeAccountRates] AS SELECT 1 as Temp')
GO
ALTER VIEW [dbo].[vwSurchargeAccountRates]
AS
SELECT    SAR.SurchargeAccountRateId,     
   SAR.SurchargeId,     
   SAR.AccountId,     
   SAR.Rate,     
   SR.Taxable,     
   SAR.OverrideCompanyInclusions    
FROM dbo.SurchargeAccountRates SAR   with (nolock)     
 INNER JOIN Accounts A with (nolock) on A.AccountId=SAR.AccountId    
 LEFT OUTER JOIN dbo.SurchargeRates SR with (nolock) ON SR.CompanyId=A.CompanyId    

GO


IF NOT EXISTS (SELECT * FROM SYS.EXTENDED_PROPERTIES WHERE 
    [major_id] = OBJECT_ID('dbo.vwSurchargeAccountRates') AND [name] = N'MS_DiagramPane1' AND [minor_id] = 0)
EXEC sp_addextendedproperty N'MS_DiagramPane1', N'[0E232FF0-B466-11cf-A24F-00AA00A3EFFF, 1.00]
Begin DesignProperties = 
   Begin PaneConfigurations = 
      Begin PaneConfiguration = 0
         NumPanes = 4
         Configuration = "(H (1[40] 4[20] 2[20] 3) )"
      End
      Begin PaneConfiguration = 1
         NumPanes = 3
         Configuration = "(H (1 [50] 4 [25] 3))"
      End
      Begin PaneConfiguration = 2
         NumPanes = 3
         Configuration = "(H (1 [50] 2 [25] 3))"
      End
      Begin PaneConfiguration = 3
         NumPanes = 3
         Configuration = "(H (4 [30] 2 [40] 3))"
      End
      Begin PaneConfiguration = 4
         NumPanes = 2
         Configuration = "(H (1 [56] 3))"
      End
      Begin PaneConfiguration = 5
         NumPanes = 2
         Configuration = "(H (2 [66] 3))"
      End
      Begin PaneConfiguration = 6
         NumPanes = 2
         Configuration = "(H (4 [50] 3))"
      End
      Begin PaneConfiguration = 7
         NumPanes = 1
         Configuration = "(V (3))"
      End
      Begin PaneConfiguration = 8
         NumPanes = 3
         Configuration = "(H (1[56] 4[18] 2) )"
      End
      Begin PaneConfiguration = 9
         NumPanes = 2
         Configuration = "(H (1 [75] 4))"
      End
      Begin PaneConfiguration = 10
         NumPanes = 2
         Configuration = "(H (1[66] 2) )"
      End
      Begin PaneConfiguration = 11
         NumPanes = 2
         Configuration = "(H (4 [60] 2))"
      End
      Begin PaneConfiguration = 12
         NumPanes = 1
         Configuration = "(H (1) )"
      End
      Begin PaneConfiguration = 13
         NumPanes = 1
         Configuration = "(V (4))"
      End
      Begin PaneConfiguration = 14
         NumPanes = 1
         Configuration = "(V (2))"
      End
      ActivePaneConfig = 0
   End
   Begin DiagramPane = 
      Begin Origin = 
         Top = 0
         Left = 0
      End
      Begin Tables = 
         Begin Table = "SurchargeAccountRates"
            Begin Extent = 
               Top = 6
               Left = 38
               Bottom = 123
               Right = 248
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "SurchargeRates"
            Begin Extent = 
               Top = 120
               Left = 561
               Bottom = 291
               Right = 732
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "Surcharges"
            Begin Extent = 
               Top = 135
               Left = 369
               Bottom = 237
               Right = 529
            End
            DisplayFlags = 280
            TopColumn = 0
         End
      End
   End
   Begin SQLPane = 
   End
   Begin DataPane = 
      Begin ParameterDefaults = ""
      End
      Begin ColumnWidths = 9
         Width = 284
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 2370
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
      End
   End
   Begin CriteriaPane = 
      Begin ColumnWidths = 11
         Column = 1440
         Alias = 900
         Table = 1170
         Output = 720
         Append = 1400
         NewValue = 1170
         SortType = 1350
         SortOrder = 1410
         GroupBy = 1350
         Filter = 1350
         Or = 1350
         Or = 1350
         Or = 1350
      End
   End
End
', 'SCHEMA', N'dbo', 'VIEW', N'vwSurchargeAccountRates', NULL, NULL
GO
DECLARE @xp int
SELECT @xp=1
IF NOT EXISTS (SELECT * FROM SYS.EXTENDED_PROPERTIES WHERE
        [major_id] = OBJECT_ID('dbo.vwSurchargeAccountRates') AND [name] = N'MS_DiagramPaneCount' AND [minor_id] = 0)
EXEC sp_addextendedproperty N'MS_DiagramPaneCount', @xp, 'SCHEMA', N'dbo', 'VIEW', N'vwSurchargeAccountRates', NULL, NULL
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchReasons' AND COLUMN_NAME = 'IsActive')
    ALTER TABLE [dbo].[DispatchReasons]
        ADD [IsActive] BIT NOT NULL DEFAULT(1)
GO

grant UPDATE on DispatchReasons to public
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='DispatchEntryInvoiceOptions')

    CREATE TABLE [dbo].[DispatchEntryInvoiceOptions](
        [DispatchEntryInvoiceOptionsId] [int] IDENTITY(1,1) NOT NULL,
        [CompanyId] [int] NOT NULL,
        [AccountId] [int] NULL,
        [ShowCompletionDateTime] [bit] NOT NULL,
        [HideAccountContact] [bit] NOT NULL,
        [HideInvoiceNumber] [bit] NOT NULL,
        [HideTowDestination] [bit] NOT NULL,
        [HidePickupLocation] [bit] NOT NULL,
        [HideAccountAddress] [bit] NOT NULL,
        [WindowEnvelopeFormat] [bit] NOT NULL,
        [ShowPriority] [bit] NOT NULL,
        [HideReason] [bit] NOT NULL,
        [HideNotes] [bit] NOT NULL,
        [HidecallNumber] [bit] NOT NULL,
        [ShowDriver] [bit] NOT NULL,
        [ShowTruck] [bit] NOT NULL,
    CONSTRAINT [PK_DispatchEntryInvoiceOptions] PRIMARY KEY CLUSTERED 
    (
        [DispatchEntryInvoiceOptionsId] ASC
    )WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
    ) ON [PRIMARY]

GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryInvoiceOptions' AND COLUMN_NAME = 'ShowCompletionDateTime')
    ALTER TABLE [dbo].[DispatchEntryInvoiceOptions] 
        ADD ShowCompletionDateTime BIT NOT NULL DEFAULT(1)
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryInvoiceOptions' AND COLUMN_NAME = 'HideAccountContact')
    ALTER TABLE [dbo].[DispatchEntryInvoiceOptions]
        ADD HideAccountContact BIT NOT NULL DEFAULT(0)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryInvoiceOptions' AND COLUMN_NAME = 'HideInvoiceNumber')
    ALTER TABLE [dbo].[DispatchEntryInvoiceOptions]
        ADD HideInvoiceNumber BIT NOT NULL DEFAULT(0)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryInvoiceOptions' AND COLUMN_NAME = 'HideInvoiceNumber')
    ALTER TABLE [dbo].[DispatchEntryInvoiceOptions]
        ADD HideCallNumber BIT NOT NULL DEFAULT(0)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryInvoiceOptions' AND COLUMN_NAME = 'HideTowDestination')
    ALTER TABLE [dbo].[DispatchEntryInvoiceOptions]
        ADD HideTowDestination BIT NOT NULL DEFAULT(0)
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryInvoiceOptions' AND COLUMN_NAME = 'HidePickupLocation')
    ALTER TABLE [dbo].[DispatchEntryInvoiceOptions]
        ADD HidePickupLocation BIT NOT NULL DEFAULT(0)
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryInvoiceOptions' AND COLUMN_NAME = 'HideAccountAddress')
    ALTER TABLE [dbo].[DispatchEntryInvoiceOptions]
        ADD HideAccountAddress BIT NOT NULL DEFAULT(0)
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryInvoiceOptions' AND COLUMN_NAME = 'WindowEnvelopeFormat')
    ALTER TABLE [dbo].[DispatchEntryInvoiceOptions]
        ADD WindowEnvelopeFormat BIT NOT NULL DEFAULT(0)
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryInvoiceOptions' AND COLUMN_NAME = 'ShowPriority')
    ALTER TABLE [dbo].[DispatchEntryInvoiceOptions]
        ADD ShowPriority BIT NOT NULL DEFAULT(0)
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryInvoiceOptions' AND COLUMN_NAME = 'HideReason')
    ALTER TABLE [dbo].[DispatchEntryInvoiceOptions]
        ADD HideReason BIT NOT NULL DEFAULT(0)
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryInvoiceOptions' AND COLUMN_NAME = 'HideNotes')
    ALTER TABLE [dbo].[DispatchEntryInvoiceOptions]
        ADD HideNotes BIT NOT NULL DEFAULT(0)
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryInvoiceOptions' AND COLUMN_NAME = 'HideCallNumber')
    ALTER TABLE [dbo].[DispatchEntryInvoiceOptions]
        ADD HideCallNumber BIT NOT NULL DEFAULT(0)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryInvoiceOptions' AND COLUMN_NAME = 'ShowDriver')
    ALTER TABLE [dbo].[DispatchEntryInvoiceOptions]
        ADD ShowDriver BIT NOT NULL DEFAULT(0)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryInvoiceOptions' AND COLUMN_NAME = 'ShowTruck')
    ALTER TABLE [dbo].[DispatchEntryInvoiceOptions]
        ADD ShowTruck BIT NOT NULL DEFAULT(0)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryInvoiceOptions' AND COLUMN_NAME = 'ShowDispatchedDateTime')
    ALTER TABLE [dbo].[DispatchEntryInvoiceOptions]
        ADD ShowDispatchedDateTime BIT NOT NULL DEFAULT(0)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryInvoiceOptions' AND COLUMN_NAME = 'ShowEnrouteDateTime')
    ALTER TABLE [dbo].[DispatchEntryInvoiceOptions]
        ADD ShowEnrouteDateTime BIT NOT NULL DEFAULT(0)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryInvoiceOptions' AND COLUMN_NAME = 'ShowOnSceneDateTime')
    ALTER TABLE [dbo].[DispatchEntryInvoiceOptions]
        ADD ShowOnSceneDateTime BIT NOT NULL DEFAULT(0)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryInvoiceOptions' AND COLUMN_NAME = 'ShowTowingDateTime')
    ALTER TABLE [dbo].[DispatchEntryInvoiceOptions]
        ADD ShowTowingDateTime BIT NOT NULL DEFAULT(0)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryInvoiceOptions' AND COLUMN_NAME = 'DefaultInvoiceNumber')
    ALTER TABLE [dbo].[DispatchEntryInvoiceOptions]
        ADD DefaultInvoiceNumber int NOT NULL DEFAULT(0),
			DefaultInvoiceNumberLocked bit NOT NULL DEFAULT(0)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryInvoiceOptions' AND COLUMN_NAME = 'HidePrintDate')
    ALTER TABLE [dbo].[DispatchEntryInvoiceOptions]
        ADD HidePrintDate BIT NOT NULL DEFAULT(0)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryInvoiceOptions' AND COLUMN_NAME = 'DefaultInvoiceNumberPrefix')
    ALTER TABLE [dbo].[DispatchEntryInvoiceOptions]
        ADD DefaultInvoiceNumberPrefix varchar(20)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryInvoiceOptions' AND COLUMN_NAME = 'ShowDestinationArrivalDateTime')
    ALTER TABLE [dbo].[DispatchEntryInvoiceOptions]
        ADD ShowDestinationArrivalDateTime BIT NOT NULL DEFAULT 0
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryInvoiceOptions' AND COLUMN_NAME = 'HideCreationTime')
    ALTER TABLE [dbo].[DispatchEntryInvoiceOptions]
        ADD HideCreationTime BIT NOT NULL DEFAULT(0)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryInvoiceOptions' AND COLUMN_NAME = 'IncludeDriverSignature')
    ALTER TABLE [dbo].[DispatchEntryInvoiceOptions]
        ADD IncludeDriverSignature BIT NOT NULL DEFAULT(0)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryInvoiceOptions' AND COLUMN_NAME = 'ShowDispatcher')
    ALTER TABLE [dbo].[DispatchEntryInvoiceOptions]
        ADD ShowDispatcher BIT NOT NULL DEFAULT(0)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryInvoiceOptions' AND COLUMN_NAME = 'ShowCompanyEmail')
    ALTER TABLE [dbo].[DispatchEntryInvoiceOptions]
        ADD ShowCompanyEmail BIT NOT NULL DEFAULT(0)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='InvoiceStatuses')
    CREATE TABLE dbo.InvoiceStatuses (
        InvoiceStatusId int IDENTITY(1,1) NOT NULL,
        CompanyId int, 
        Name varchar(50) NOT NULL, 
        Description varchar(50), 
        StatusOrder int NOT NULL DEFAULT(1),
        OwnerUserId int NOT NULL,
        IsSystemOnly bit default(0),
        Deleted bit DEFAULT(0),
        
        
        CONSTRAINT PK_InvoiceStatuses PRIMARY KEY CLUSTERED (InvoiceStatusId ASC),
        CONSTRAINT FK_InvoiceStatuses_Users FOREIGN KEY (OwnerUserId) REFERENCES dbo.Users (UserId),    
        CONSTRAINT FK_InvoiceStatuses_Companies FOREIGN KEY (CompanyId) REFERENCES dbo.Companies (CompanyId)
    );
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntries' AND COLUMN_NAME = 'InvoiceStatusId')
    ALTER TABLE [dbo].[DispatchEntries]
        ADD InvoiceStatusId int
        CONSTRAINT FK_DispatchEntries_InvoiceStatuses FOREIGN KEY(InvoiceStatusId) REFERENCES dbo.InvoiceStatuses (InvoiceStatusId)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='MasterAccountReasons')
    CREATE TABLE dbo.MasterAccountReasons (
        MasterAccountReasonId int IDENTITY(1,1) NOT NULL,
        MasterAccountId int NOT NULL,
        [Type] int NOT NULL,
        Name varchar(50) NOT NULL,
        Code varchar(255) NULL,
        
        CONSTRAINT PK_MasterAccountReasons PRIMARY KEY CLUSTERED (MasterAccountReasonId ASC),
        CONSTRAINT FK_MasterAccountReasons_MasterAccounts FOREIGN KEY(MasterAccountId) REFERENCES dbo.MasterAccounts (MasterAccountId)
    );
GO

/*************************************************************************************************
 ** Commissions Changes
 *************************************************************************************************/
 IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='CommissionScheduleGroups')
    CREATE TABLE dbo.CommissionScheduleGroups (
        CommissionScheduleGroupId int IDENTITY(1,1) NOT NULL,
        CompanyId int NOT NULL,
        [Description] varchar(50) NOT NULL,
        OwnerUserId int NOT NULL,
        CreateDate datetime default(getdate()) NOT NULL,
        
        CONSTRAINT PK_CommissionScheduleTimes PRIMARY KEY CLUSTERED (CommissionScheduleGroupId ASC),
        CONSTRAINT FK_CommissionScheduleGroups_Companies FOREIGN KEY (CompanyId) REFERENCES dbo.Companies (CompanyId),
        CONSTRAINT FK_CommissionScheduleGroups_Users FOREIGN KEY (OwnerUserId) REFERENCES dbo.Users (UserId)
    );
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='CommissionScheduleGroupTimes')
    CREATE TABLE dbo.CommissionScheduleGroupTimes (
        CommissionScheduleGroupTimeId int IDENTITY(1,1) NOT NULL,
        CommissionScheduleGroupId int NOT NULL,
        StartTime time(0) NULL,
        EndTime time(0) NULL,
        [WeekDay] int NULL,
        
        CONSTRAINT PK_CommissionScheduleGroupTimeId PRIMARY KEY CLUSTERED  (CommissionScheduleGroupTimeId ASC),
        CONSTRAINT FK_CommissionScheduleGroupTimes_CommissionScheduleGroups FOREIGN KEY(CommissionScheduleGroupId) REFERENCES dbo.CommissionScheduleGroups (CommissionScheduleGroupId)
    );
GO


ALTER TABLE dbo.CommissionScheduleGroupTimes
    DROP CONSTRAINT FK_CommissionScheduleGroupTimes_CommissionScheduleGroups

ALTER TABLE dbo.CommissionScheduleGroupTimes
    ADD CONSTRAINT FK_CommissionScheduleGroupTimes_CommissionScheduleGroups FOREIGN KEY(CommissionScheduleGroupId) REFERENCES dbo.CommissionScheduleGroups (CommissionScheduleGroupId) ON DELETE CASCADE

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='CommissionRateItems' AND COLUMN_NAME = 'CommissionScheduleGroupId')
    ALTER TABLE [dbo].[CommissionRateItems]
        ADD CommissionScheduleGroupId int null
        CONSTRAINT FK_CommissionRateItems_CommissionScheduleGroups FOREIGN KEY(CommissionScheduleGroupId) REFERENCES dbo.CommissionScheduleGroups (CommissionScheduleGroupId)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='CommissionRateItemBodyTypes' AND COLUMN_NAME = 'CommissionScheduleGroupId')
    ALTER TABLE [dbo].[CommissionRateItemBodyTypes]
        ADD CommissionScheduleGroupId int null
        CONSTRAINT FK_CommissionRateItemBodyTypes_CommissionScheduleGroups FOREIGN KEY(CommissionScheduleGroupId) REFERENCES dbo.CommissionScheduleGroups (CommissionScheduleGroupId)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='CommissionRateItemBodyTypes' AND COLUMN_NAME = 'CompanyId')
    ALTER TABLE CommissionRateItemBodyTypes ADD CompanyId int DEFAULT NULL
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='CommissionScheduleGroupStandardValues')
    CREATE TABLE dbo.CommissionScheduleGroupStandardValues (
        CommissionScheduleGroupStandardValueId int IDENTITY(1,1) NOT NULL,
        CommissionScheduleGroupId int NOT NULL,
        StandardDriverCommission float NOT NULL,
        DriverId int NULL,
        
        CONSTRAINT PK_CommissionScheduleGroupStandardValues PRIMARY KEY CLUSTERED (CommissionScheduleGroupStandardValueId ASC),
        CONSTRAINT FK_CommissionScheduleGroupStandardValues_CommissionScheduleGroups FOREIGN KEY(CommissionScheduleGroupId) REFERENCES dbo.CommissionScheduleGroups (CommissionScheduleGroupId),
        CONSTRAINT FK_CommissionScheduleGroupStandardValues_Drivers FOREIGN KEY(DriverId) REFERENCES dbo.Drivers (DriverId)
    );
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='CommissionScheduleGroupRateItemValues')
    CREATE TABLE dbo.CommissionScheduleGroupRateItemValues (
        CommissionScheduleGroupRateItemValueId int IDENTITY(1,1) NOT NULL,
        CommissionScheduleGroupId int NOT NULL,
        BodyTypeId tinyint NULL,
        DriverId int NULL,
        Value float NULL,
        RateItemId int NULL,
        
        CONSTRAINT PK_CommissionScheduleGroupRateItemValues PRIMARY KEY CLUSTERED (CommissionScheduleGroupRateItemValueId ASC),
        CONSTRAINT FK_CommissionScheduleGroupRateItemValues_CommissionScheduleGroups FOREIGN KEY(CommissionScheduleGroupId) REFERENCES dbo.CommissionScheduleGroups (CommissionScheduleGroupId)
    );
GO

ALTER TABLE CommissionScheduleGroupRateItemValues 
ALTER COLUMN RateItemId INT NULL
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='DistributedLocks')
BEGIN
    CREATE TABLE [dbo].[DistributedLocks] (
        [DistributedLockId] bigint IDENTITY(1,1) NOT NULL,
        [Key] varchar(32) NOT NULL,
        [Value] varchar(32) NOT NULL,
        [Code] uniqueidentifier NOT NULL default(newid()),
        [CreateDate] datetime NOT NULL default(getutcdate()),		
        CONSTRAINT PK_DistributedLocks PRIMARY KEY CLUSTERED (DistributedLockId ASC),
    )

    CREATE UNIQUE NONCLUSTERED INDEX IX_DistributedLocks ON dbo.DistributedLocks ([Key], [Value])
END
GO

IF (SELECT [CHARACTER_MAXIMUM_LENGTH] FROM INFORMATION_SCHEMA.COLUMNS WHERE [COLUMN_NAME] = 'Value' AND [TABLE_NAME] = 'DistributedLocks') < 100
BEGIN
    ALTER TABLE DistributedLocks
    ALTER COLUMN [Value] VARCHAR(100) NOT NULL
END
GO

IF OBJECT_ID('dbo.[vwDriverStatus]') IS NULL EXEC ('CREATE VIEW dbo.[vwDriverStatus] AS SELECT 1 as Temp')
GO

ALTER VIEW [dbo].[vwDriverStatus] AS
    SELECT DE.CompanyId, 
        D.DriverId, 
        DE.DispatchEntryId, 
        DE.CallNumber, 
        DE.Status as StatusId, 
        CASE DE.Status 
        WHEN 0 THEN DE.CreateDate
        WHEN 1 THEN DE.DispatchTime
        WHEN 2 THEN DE.EnRouteTime
        WHEN 3 THEN DE.ArrivalTime
        WHEN 4 THEN DE.TowTime 
        ELSE NULL
        END AS StatusTime
 FROM Drivers D  with (nolock)                          
 INNER JOIN DispatchEntryAssetDrivers ASR with (nolock) on ASR.DriverId=D.DriverId  and asr.AssetDriverId > 92049713                          
 INNER JOIN DispatchEntryAssets A  with (nolock) on a.AssetId > 97048187              and A.AssetId=ASR.AssetId  AND A.Deleted=0             
 INNER JOIN DispatchEntries DE  with (nolock) on de.DispatchEntryId > ********* and DE.DispatchEntryId=A.DispatchEntryId and           
 DE.Status NOT In (5, 255, 0) and DE.Deleted=0      

GO


GRANT SELECT ON [dbo].[vwDriverStatus] TO public
GO


    
IF (NOT EXISTS(SELECT 1 FROM sys.indexes WHERE name='IX_Drivers_CompanyId'))
    CREATE NONCLUSTERED INDEX [IX_Drivers_CompanyId] ON [dbo].[Drivers] ([CompanyId])
GO

IF (NOT EXISTS(SELECT 1 FROM sys.indexes WHERE name='IX_Drivers_UserId'))
    CREATE NONCLUSTERED INDEX [IX_Drivers_UserId] ON [dbo].[Drivers] ([UserId], [Deleted])
GO
    
    
IF OBJECT_ID('dbo.[vwDrivers]') IS NULL EXEC ('CREATE VIEW dbo.[vwDrivers] AS SELECT 1 as Temp')
GO

    
ALTER VIEW [dbo].[vwDrivers]    
AS    
SELECT     DriverId, CompanyId, Name, Address, City, State, Zip, HomePhone, MobilePhone, WorkPhone, Email, LicenseNumber, LicenseExpirationDate, LicenseClass,     
                      BirthDate, StartDate, EndDate, Notes, UserId, Deleted, Active, OperateHeavyEquipment, CreateDate, CommissionRate, DispatchingNotificationTypeId,     
                      DispatchingNotificationValue, EmergencyContactName, EmergencyContactPhone, MedicalExamExpirationDate,
    
       nullif(replace(replace(cast((    
 select * from(select cast(ca.CompanyId as varchar) + ',' as [text()]    
 from CompanyDrivers ca  WITH (NOLOCK)    
 where ca.DriverId = Drivers.DriverId    
 union     
 select cast(sharedcompanyid as varchar) + ','  FROM CompaniesShared CS WITH (NOLOCK) WHERE CS.CompanyId=Drivers.CompanyId AND CS.ShareAllDrivers=1    
  union    
SELECT cast(CompanyId as varchar) + ',' FROM CompanyUsers WITH (NOLOCK) WHERE UserId=Drivers.UserId    
 ) x    
    
 -- get all companies that a driver should show under    
 -- first lookup the     
    
    
 for xml path( '' ), root( 'X' ), type    
 ) as varchar(8000)), '<X>', ''),'</X>',''), '') as Companies, ReferenceNumber      
FROM Drivers with (nolock)    

GO    
grant select on [dbo].[vwDrivers]  to public
GO

    
    


IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='CompanyContracts' AND TABLE_SCHEMA='Billing')
BEGIN
    CREATE TABLE [Billing].[CompanyContracts](
        CompanyContractId	int IDENTITY(1,1) NOT NULL,
        CompanyId			int NOT NULL,
        OwnerUserId			int NOT NULL,
        SalesUserId			int,
        IsPaying			bit DEFAULT(0),
        IsCancelled			bit DEFAULT(0),
        BillingAmount		smallmoney NOT NULL,
        BillingDay			int DEFAULT(1) NOT NULL,
        BillingSystemId		int NOT NULL,		
        CreateDate			datetime DEFAULT(getdate()) NOT NULL
 
        CONSTRAINT [PK_CompanyContracts] PRIMARY KEY CLUSTERED  ( CompanyContractId ASC ),
        CONSTRAINT [FK_CompanyContracts_Companies] FOREIGN KEY([CompanyId]) REFERENCES [dbo].[Companies] ([CompanyId]),
        CONSTRAINT [FK_CompanyContracts_OwnerUserId] FOREIGN KEY(OwnerUserId) REFERENCES [dbo].[Users] (UserId),
        CONSTRAINT [FK_CompanyContracts_SalesUserId] FOREIGN KEY(SalesUserId) REFERENCES [dbo].[Users] (UserId),
        CONSTRAINT [FK_CompanyContracts_Systems] FOREIGN KEY([BillingSystemId]) REFERENCES [Billing].[BillingSystems] ([BillingSystemId]),
    )
END
GO


IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='CompanyContractTransactions' AND TABLE_SCHEMA='Billing')
BEGIN
    CREATE TABLE [Billing].[CompanyContractTransactions] (
        [CompanyContractTransactionId] int IDENTITY(1,1) NOT NULL,
        [CompanyContractId]		int,
        [TransactionKey]		varchar(100),
        [Amount]				smallmoney NOT NULL,
        [TransactionDate]		datetime DEFAULT(getdate()) NOT NULL,
        [IsSuccess]				bit
 
        CONSTRAINT [PK_CompanyContractTransactions] PRIMARY KEY CLUSTERED  ( [CompanyContractTransactionId] ASC ),
        CONSTRAINT [FK_CompanyContractTransactions_CompanyContracts] FOREIGN KEY([CompanyContractId]) REFERENCES [Billing].[CompanyContracts] ([CompanyContractId]),
    )
END
GO

IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='Features' AND TABLE_SCHEMA='Billing')
BEGIN
    CREATE TABLE [Billing].[Features](
        FeatureId int IDENTITY(1,1) NOT NULL,
        Name varchar(50) NOT NULL,
        [Description] varchar(500) NOT NULL default(''),
        MonthlyAmount decimal DEFAULT(0),
        DependsOnFeatureId int NULL,
        Billable bit DEFAULT(0),
        Deleted bit DEFAULT(0)
 
        CONSTRAINT [PK_Features] PRIMARY KEY CLUSTERED  ( FeatureId ASC )
    )

    CREATE INDEX IX_Features_Deleted ON Billing.Features (Deleted)
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Features' AND COLUMN_NAME = 'Internal')
    ALTER TABLE [Billing].[Features] ADD Internal bit DEFAULT(0)
GO



IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='Packages' AND TABLE_SCHEMA='Billing')
BEGIN
    CREATE TABLE [Billing].[Packages] (
        PackageId int IDENTITY(1,1) NOT NULL,
        Name varchar(50) NOT NULL,
        [Description] varchar(500) NOT NULL default(''),
        MonthlyAmount decimal DEFAULT(0),
        Deleted bit DEFAULT(0)
 
        CONSTRAINT [PK_Packages] PRIMARY KEY CLUSTERED  ( PackageId ASC )
    )

    CREATE INDEX IX_Packages_Deleted ON Billing.Packages (Deleted)
END
GO


IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='Packages' AND TABLE_SCHEMA='Billing')
BEGIN
    CREATE TABLE [Billing].[PackageFeatures] (
        PackageFeatureId int IDENTITY(1,1) NOT NULL,
        PackageId int NOT NULL,
        FeatureId int NOT NULL
 
        CONSTRAINT [PK_PackageFeatures] PRIMARY KEY CLUSTERED  ( PackageFeatureId ASC ),
        CONSTRAINT [FK_PackageFeatures_Packages] FOREIGN KEY(PackageId) REFERENCES Billing.Packages (PackageId),
        CONSTRAINT [FK_PackageFeatures_Features] FOREIGN KEY(FeatureId) REFERENCES Billing.Features (FeatureId),
    )

    CREATE INDEX IX_PackageFeatures_PackageId ON Billing.PackageFeatures (PackageId) 
END
GO




IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='PackageFeatures' AND TABLE_SCHEMA='Billing')
BEGIN
    --
    -- Holds the features for the specified packages. 
    --
    CREATE TABLE [Billing].[PackageFeatures] (
        PackageFeatureId int IDENTITY(1,1) NOT NULL,
        FeatureId int NOT NULL,
        CreateDate datetime DEFAULT(getdate()) NOT NULL,
 
        CONSTRAINT [PK_PackageFeatures] PRIMARY KEY CLUSTERED  (PackageFeatureId ASC ),
        CONSTRAINT [FK_PackageFeatures_Features] FOREIGN KEY(FeatureId) REFERENCES Billing.Features (FeatureId),
    )
END
GO

IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='CompanyContractFeatures' AND TABLE_SCHEMA='Billing')
BEGIN
    --
    -- Holds any features that were manually added to the account irrespective of the package chosen.
    --
    CREATE TABLE [Billing].[CompanyContractFeatures] (
        [CompanyContractFeatureId] int IDENTITY(1,1) NOT NULL,
        [CompanyContractId] int NOT NULL,
        [FeatureId]			int NOT NULL,
        [MonthlyAmount]		smallmoney DEFAULT(0) NOT NULL,
        [CreateDate]		datetime DEFAULT(getdate()) NOT NULL,
        [ExpirationDate]	datetime,
        [IsDeleted]			bit DEFAULT(0)
 
        CONSTRAINT [PK_CompanyContractFeatures] PRIMARY KEY CLUSTERED  ( [CompanyContractFeatureId] ASC ),
        CONSTRAINT [FK_CompanyContractFeatures_CompanyContracts] FOREIGN KEY([CompanyContractId]) REFERENCES Billing.CompanyContracts (CompanyContractId),
        CONSTRAINT [FK_CompanyContractFeatures_Features] FOREIGN KEY(FeatureId) REFERENCES Billing.Features (FeatureId),

    )
END
GO

GRANT SELECT ON Billing.CompanyContracts TO public
GRANT SELECT ON Billing.CompanyContractFeatures TO public
GRANT SELECT ON Billing.Packages TO public
GRANT SELECT ON Billing.PackageFeatures TO public
GRANT SELECT ON Billing.Packages TO public



IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='DispatchEntrySources' AND TABLE_SCHEMA='dbo')
BEGIN
    CREATE TABLE dbo.[DispatchEntrySources] (
        DispatchEntrySourceId int IDENTITY(1,1) NOT NULL,
        Name varchar(50) NOT NULL,
        [Description] varchar(500) NOT NULL default(''),
        Deleted bit DEFAULT(0)
 
        CONSTRAINT [PK_DispatchEntrySources] PRIMARY KEY CLUSTERED  ( DispatchEntrySourceId ASC ),
        CONSTRAINT [UX_DispatchEntrySources_Name] UNIQUE ( Name)
    )

    CREATE INDEX IX_DispatchEntrySources_Deleted ON dbo.DispatchEntrySources (Deleted)

    select 1 as success
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='AccountPublicRequestLinks')
    CREATE TABLE [dbo].[AccountPublicRequestLinks] (
        [AccountPublicRequestLinkId]	INT IDENTITY (1, 1) NOT NULL,
        [AccountId]						INT NOT NULL,
        [CompanyId]						INT NOT NULL,
        [CreateDate]					DATETIME NOT NULL DEFAULT(GETDATE()),
        [OwnerUserId]					INT NOT NULL,
        [Deleted]						BIT DEFAULT(0) NOT NULL
        
        CONSTRAINT [PK_AccountPublicRequestLinkId] PRIMARY KEY CLUSTERED ([AccountPublicRequestLinkId] ASC),
        CONSTRAINT [FK_AccountPublicRequestLinks_AccountId] FOREIGN KEY ([AccountId]) REFERENCES [dbo].[Accounts] ([AccountId]),
        CONSTRAINT [FK_AccountPublicRequestLinks_CompanyId] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].[Companies] ([CompanyId]),
        CONSTRAINT [FK_AccountPublicRequestLinks_Users] FOREIGN KEY([OwnerUserId]) REFERENCES [dbo].[Users] ([UserId])
    );

GO
GRANT SELECT ON AccountPublicRequestLinks TO PUBLIC
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountPublicRequestLinks' AND COLUMN_NAME = 'GUID')
    ALTER TABLE AccountPublicRequestLinks ADD GUID varchar(128) DEFAULT NULL
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountPublicRequestLinks' AND COLUMN_NAME = 'UseGUID')
    ALTER TABLE AccountPublicRequestLinks ADD UseGUID bit DEFAULT 0
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountPublicRequestLinks' AND COLUMN_NAME = 'RequireAuthentication')
    ALTER TABLE AccountPublicRequestLinks ADD RequireAuthentication bit NOT NULL DEFAULT 0
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='AccountPublicRequests')
    CREATE TABLE [dbo].[AccountPublicRequests] (
        [AccountPublicRequestId]		INT IDENTITY (1, 1) NOT NULL,
        [AccountPublicRequestLinkId]	INT NOT NULL,
        [AccountId]						INT NOT NULL,
        [CreateDate]					DATETIME NOT NULL DEFAULT(GETDATE()),
        [IpAddress]						varchar(255),
        [DispatchEntryId]				INT NOT NULL

        CONSTRAINT [PK_AccountPublicRequests_AccountPublicRequestId] PRIMARY KEY CLUSTERED ([AccountPublicRequestId] ASC),
        CONSTRAINT [FK_AccountPublicRequests_AccountPublicRequestLinkId] FOREIGN KEY ([AccountPublicRequestLinkId]) REFERENCES [dbo].[AccountPublicRequestLinks] ([AccountPublicRequestLinkId]),
        CONSTRAINT [FK_AccountPublicRequests_AccountId] FOREIGN KEY ([AccountId]) REFERENCES [dbo].[Accounts] ([AccountId]),
        CONSTRAINT [FK_AccountPublicRequests_DispatchEntryId] FOREIGN KEY ([DispatchEntryId]) REFERENCES [dbo].[DispatchEntries] ([DispatchEntryId])
    );

GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='AccountPublicRequestFormFields')
    CREATE TABLE [dbo].[AccountPublicRequestFormFields] (
        [AccountPublicRequestFormFieldId]	INT IDENTITY (1, 1) NOT NULL,
        [CompanyId]							INT,
        [AccountId]							INT NOT NULL,
        [DispatchEntryAttributeId]			INT,
        [Required]							BIT DEFAULT(0) NOT NULL,
        [Deleted]							BIT DEFAULT(0) NOT NULL

        CONSTRAINT [FK_AccountPublicRequestFormFields_CompanyId] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].[Companies] ([CompanyId]),
        CONSTRAINT [FK_AccountPublicRequestFormFields_AccountId] FOREIGN KEY ([AccountId]) REFERENCES [dbo].[Accounts] ([AccountId]),
        CONSTRAINT [FK_AccountPublicRequestFormFields_AttributeId] FOREIGN KEY ([DispatchEntryAttributeId]) REFERENCES [dbo].[DispatchEntryAttributes] ([DispatchEntryAttributeId])
    );

GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountPublicRequestFormFields' AND COLUMN_NAME = 'AccountPublicRequestFormTemplateId')
BEGIN
    ALTER TABLE  AccountPublicRequestFormFields ADD AccountPublicRequestFormTemplateId INT NULL DEFAULT (NULL)
END 
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountPublicRequestFormFields' AND COLUMN_NAME = 'IncludedOption')
     BEGIN
         ALTER TABLE  AccountPublicRequestFormFields ADD IncludedOption INT NULL DEFAULT (NULL)
     END
GO
 
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountPublicRequestFormFields' AND COLUMN_NAME = 'DefaultValue')
     BEGIN
         ALTER TABLE  AccountPublicRequestFormFields ADD DefaultValue VARCHAR(500) NULL
     END
GO


 IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountPublicRequestFormFields' AND COLUMN_NAME = 'IncludeOption')
     BEGIN
         ALTER TABLE  AccountPublicRequestFormFields ADD IncludeOption INT NULL DEFAULT (NULL)
     END
 GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='AccountPublicRequestLinkAccounts')
    CREATE TABLE [dbo].[AccountPublicRequestLinkAccounts] (
        [AccountPublicRequestLinkId]	INT NOT NULL,
        [AccountId]						INT NOT NULL
                
        CONSTRAINT [FK_AccountPublicRequestLinkAccounts_AccountPublicRequestLinkId] FOREIGN KEY ([AccountPublicRequestLinkId]) REFERENCES [dbo].[AccountPublicRequestLinks] (AccountPublicRequestLinkId),
        CONSTRAINT [FK_AccountPublicRequestLinkAccounts_AccountId] FOREIGN KEY ([AccountId]) REFERENCES [dbo].[Accounts] ([AccountId])
    );

GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='AccountPublicRequestLinkBillToAccounts')
    CREATE TABLE [dbo].[AccountPublicRequestLinkBillToAccounts] (
        [AccountPublicRequestLinkId]	INT NOT NULL,
        [AccountId]						INT NOT NULL
                
        CONSTRAINT [FK_AccountPublicRequestLinkBillToAccounts_AccountPublicRequestLinkId] FOREIGN KEY ([AccountPublicRequestLinkId]) REFERENCES [dbo].[AccountPublicRequestLinks] (AccountPublicRequestLinkId),
        CONSTRAINT [FK_AccountPublicRequestLinkBillToAccounts_AccountId] FOREIGN KEY ([AccountId]) REFERENCES [dbo].[Accounts] ([AccountId])
    );

GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='DispatchEntryCancellations')

    CREATE TABLE [dbo].[DispatchEntryCancellations](
        [DispatchEntryCancellationId]	INT IDENTITY(1,1) NOT NULL,
        [DispatchEntryId]				int NOT NULL,
        [Reason]						varchar(255),
        [CreateDate]					DATETIME DEFAULT(getdate()),
        [OwnerUserId]					INT NOT NULL,
        [IpAddress]						varchar(255),
        [Deleted]						BIT NOT NULL DEFAULT(0)

        CONSTRAINT [PK_DispatchEntryCancellations_DispatchEntryCancellationId] PRIMARY KEY CLUSTERED ([DispatchEntryCancellationId] ASC),
        CONSTRAINT [FK_DispatchEntryCancellations_DispatchEntries] FOREIGN KEY ([DispatchEntryId]) REFERENCES [dbo].[DispatchEntries]([DispatchEntryId]),
        CONSTRAINT [FK_DispatchEntryCancellations_Users] FOREIGN KEY ([OwnerUserId]) REFERENCES [dbo].[Users] ([UserId])
    );

IF (NOT EXISTS(SELECT 1 FROM sys.indexes WHERE name='IX_DispatchEntryCancellations_DispatchEntryId'))
    CREATE NONCLUSTERED INDEX [IX_DispatchEntryCancellations_DispatchEntryId] ON [dbo].[DispatchEntryCancellations] ([DispatchEntryId])
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryCancellations' AND COLUMN_NAME = 'Notes')
    ALTER TABLE dbo.DispatchEntryCancellations ADD Notes varchar(4000)
GO

ALTER TABLE dbo.DispatchEntryCancellations ALTER COLUMN Reason varchar(512)
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='LetterTemplateAssociations')
    CREATE TABLE [dbo].[LetterTemplateAssociations](
        [LetterTemplateAssociationId] [int] IDENTITY(1,1) NOT NULL,
        [LetterTemplateId] [int] NOT NULL,
        [Field] [varchar](50) NOT NULL,
        [Value] [varchar](50) NOT NULL,
        CONSTRAINT [PK_LicenseTemplateAssociations] PRIMARY KEY CLUSTERED  ([LetterTemplateAssociationId] ASC )
    );

IF (NOT EXISTS(SELECT 1 FROM sys.indexes WHERE name='IX_LetterTemplateAssociations_FieldValue'))
    CREATE NONCLUSTERED INDEX [IX_LetterTemplateAssociations_FieldValue] ON [dbo].[LetterTemplateAssociations] (Field, Value)

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='LetterTemplates' AND COLUMN_NAME = 'Deleted')
    ALTER TABLE LetterTemplates ADD Deleted bit DEFAULT((0)) NOT NULL

GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='LetterTemplates' AND COLUMN_NAME = 'No10Formatted')
    ALTER TABLE LetterTemplates ADD No10Formatted bit DEFAULT((0)) NOT NULL

GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='LetterTemplates' AND COLUMN_NAME = 'Type')
    ALTER TABLE LetterTemplates ADD Type INT
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='CompanyCalculatedStatistics')
CREATE TABLE [dbo].[CompanyCalculatedStatistics](
    [CompanyId] [int] NOT NULL, 
    [TruckCount] [int] NOT NULL DEFAULT(0),
    [UserCount] [int] NOT NULL DEFAULT(0),
    [DriverCount] [int] NOT NULL DEFAULT(0),
    [RateItemCount] [int] NOT NULL DEFAULT(0),
    [CallCountForever] [int] NOT NULL DEFAULT(0),
    [CallCount365] [int] NOT NULL DEFAULT(0),
    [CallCount60] [int] NOT NULL DEFAULT(0),
    [CallCount30] [int] NOT NULL DEFAULT(0),
    [FirstLogin] [datetime] NULL,
    [LastLogin] [datetime] NULL,
    CONSTRAINT [PK_CompanyCalculatedStatistics] PRIMARY KEY CLUSTERED ( [CompanyId] ASC )
)
GO


IF OBJECT_ID('dbo.[vwCompanies]') IS NULL EXEC ('CREATE VIEW dbo.[vwCompanies] AS SELECT 1 as Temp')
GO

ALTER  VIEW [dbo].[vwCompanies]
AS
SELECT        C.CompanyId, Name, Phone, Fax, Email, Website, Address, City, State, Zip, Country, C.CreateDate, AccountingMethod, InvoicingSystemId, TimezoneOffset, 
                         TimezoneUseDST, TaxMode, InvoicingTagline, InvoicingLateFlatAmount, InvoicingLateAPR, InvoicingLateGracePeriod, StandardDriverCommission, Type, 
                         VinRequiredLength, News, ParentCompanyId,
                            COALESCE(CCS.TruckCount, 0) as TruckCount,
                            COALESCE(CCS.UserCount, 0) as UserCount,
                            COALESCE(CCS.DriverCount, 0) as DriverCount, 
                            COALESCE(CCS.RateItemCount, 0) as RateItemCount,
                            COALESCE(CCS.CallCountForever, 0) as CallCountForever,
                            COALESCE(CCS.CallCount365, 0) as CallCount365,
                            COALESCE(CCS.CallCount60, 0) as CallCount60,
                            COALESCE(CCS.CallCount30, 0) as CallCount30,
                            CCS.FirstLogin,
                            CCS.LastLogin,
                             (SELECT        CASE WHEN C.CreateDate IS NULL OR Type = 2 THEN 0 
                                                WHEN DATEDIFF(DAY, GETDATE(), C.CreateDate + 30) < 0 THEN 0 
                                                ELSE DATEDIFF(DAY, GETDATE(), C.CreateDate + 30) 
                                                END AS Expr1) AS DaysLeft, 
                         COALESCE(CC.BillingAmount, 0) AS MonthlyFee,
                         ShortName=coalesce((select top 1 Nickname from CompaniesShared WHERE SharedCompanyId=C.CompanyId), Name),
                         Latitude, 
                         Longitude
FROM            dbo.Companies  C
LEFT OUTER JOIN dbo.CompanyCalculatedStatistics CCS on CCS.CompanyId=C.CompanyId
LEFT OUTER JOIN billing.CompanyContracts cc on CC.CompanyId=C.CompanyId and CC.IsCancelled=0
where C.Type not in (102,103)

GO

IF NOT EXISTS (SELECT * FROM SYS.EXTENDED_PROPERTIES WHERE 
    [major_id] = OBJECT_ID('dbo.vwCompanies') AND [name] = N'MS_DiagramPane1' AND [minor_id] = 0)
EXEC sp_addextendedproperty N'MS_DiagramPane1', N'[0E232FF0-B466-11cf-A24F-00AA00A3EFFF, 1.00]
Begin DesignProperties = 
   Begin PaneConfigurations = 
      Begin PaneConfiguration = 0
         NumPanes = 4
         Configuration = "(H (1[40] 4[20] 2[20] 3) )"
      End
      Begin PaneConfiguration = 1
         NumPanes = 3
         Configuration = "(H (1 [50] 4 [25] 3))"
      End
      Begin PaneConfiguration = 2
         NumPanes = 3
         Configuration = "(H (1 [50] 2 [25] 3))"
      End
      Begin PaneConfiguration = 3
         NumPanes = 3
         Configuration = "(H (4 [30] 2 [40] 3))"
      End
      Begin PaneConfiguration = 4
         NumPanes = 2
         Configuration = "(H (1 [56] 3))"
      End
      Begin PaneConfiguration = 5
         NumPanes = 2
         Configuration = "(H (2 [66] 3))"
      End
      Begin PaneConfiguration = 6
         NumPanes = 2
         Configuration = "(H (4 [50] 3))"
      End
      Begin PaneConfiguration = 7
         NumPanes = 1
         Configuration = "(V (3))"
      End
      Begin PaneConfiguration = 8
         NumPanes = 3
         Configuration = "(H (1[56] 4[18] 2) )"
      End
      Begin PaneConfiguration = 9
         NumPanes = 2
         Configuration = "(H (1 [75] 4))"
      End
      Begin PaneConfiguration = 10
         NumPanes = 2
         Configuration = "(H (1[66] 2) )"
      End
      Begin PaneConfiguration = 11
         NumPanes = 2
         Configuration = "(H (4 [60] 2))"
      End
      Begin PaneConfiguration = 12
         NumPanes = 1
         Configuration = "(H (1) )"
      End
      Begin PaneConfiguration = 13
         NumPanes = 1
         Configuration = "(V (4))"
      End
      Begin PaneConfiguration = 14
         NumPanes = 1
         Configuration = "(V (2))"
      End
      ActivePaneConfig = 0
   End
   Begin DiagramPane = 
      Begin Origin = 
         Top = 0
         Left = 0
      End
      Begin Tables = 
         Begin Table = "Companies"
            Begin Extent = 
               Top = 6
               Left = 38
               Bottom = 313
               Right = 246
            End
            DisplayFlags = 280
            TopColumn = 0
         End
      End
   End
   Begin SQLPane = 
   End
   Begin DataPane = 
      Begin ParameterDefaults = ""
      End
      Begin ColumnWidths = 31
         Width = 284
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
      End
   End
   Begin CriteriaPane = 
      Begin ColumnWidths = 11
         Column = 1440
         Alias = 900
         Table = 1170
         Output = 720
         Append = 1400
         NewValue = 1170
         SortType = 1350
         SortOrder = 1410
         GroupBy = 1350
         Filter = 1350
         Or = 1350
         Or = 1350
         Or = 1350
      End
   End
End
', 'SCHEMA', N'dbo', 'VIEW', N'vwCompanies', NULL, NULL
GO
DECLARE @xp int
SELECT @xp=1
IF NOT EXISTS (SELECT * FROM SYS.EXTENDED_PROPERTIES WHERE
        [major_id] = OBJECT_ID('dbo.vwCompanies') AND [name] = N'MS_DiagramPaneCount' AND [minor_id] = 0)
EXEC sp_addextendedproperty N'MS_DiagramPaneCount', @xp, 'SCHEMA', N'dbo', 'VIEW', N'vwCompanies', NULL, NULL
GO
    

IF OBJECT_ID('dbo.vwCompanies_Dynamic') IS NULL EXEC ('CREATE VIEW dbo.[vwCompanies_Dynamic] AS SELECT 1 as Temp')
GO

ALTER  VIEW [dbo].[vwCompanies_Dynamic]
AS
SELECT        CompanyId, Name, Phone, Fax, Email, Website, Address, City, State, Zip, Country, CreateDate, AccountingMethod, InvoicingSystemId, TimezoneOffset, 
                         TimezoneUseDST, TaxMode, InvoicingTagline, InvoicingLateFlatAmount, InvoicingLateAPR, InvoicingLateGracePeriod, StandardDriverCommission, Type, 
                         VinRequiredLength, News, ParentCompanyId,
                             (SELECT        COUNT(*) AS Expr1
                               FROM            dbo.Trucks
                               WHERE        (CompanyId = dbo.Companies.CompanyId) AND (Deleted = 0)) AS TruckCount,
                             (SELECT        COUNT(*) AS Expr1
                               FROM            dbo.Users
                               WHERE        (CompanyId = dbo.Companies.CompanyId) AND (Deleted = 0)) AS UserCount,
                             (SELECT        COUNT(*) AS Expr1
                               FROM            dbo.Drivers
                               WHERE        (CompanyId = dbo.Companies.CompanyId) AND (Deleted = 0)) AS DriverCount,
                             (SELECT        COUNT(*) AS Expr1
                               FROM            dbo.RateItems
                               WHERE        (CompanyId = dbo.Companies.CompanyId) AND (Deleted = 0)) AS RateItemCount,
                             (SELECT        COUNT(*) AS Expr1
                               FROM            dbo.DispatchEntries
                               WHERE        (CompanyId = dbo.Companies.CompanyId) AND (Deleted = 0)) AS CallCountForever,
                             (SELECT        COUNT(*) AS Expr1
                               FROM            dbo.DispatchEntries AS DispatchEntries_3
                               WHERE        (CompanyId = dbo.Companies.CompanyId) AND (CreateDate > DATEADD(day, - 365, GETDATE())) AND (Deleted = 0 and Status not in(255))) AS CallCount365,    
                             (SELECT        COUNT(*) AS Expr1    
                               FROM            dbo.DispatchEntries AS DispatchEntries_2    
                               WHERE        (CompanyId = dbo.Companies.CompanyId) AND (CreateDate > DATEADD(day, - 60, GETDATE())) AND (Deleted = 0 and Status not in(255))) AS CallCount60,    
                             (SELECT        COUNT(*) AS Expr1    
                               FROM            dbo.DispatchEntries AS DispatchEntries_1    
                               WHERE        (CompanyId = dbo.Companies.CompanyId) AND (CreateDate > DATEADD(day, - 30, GETDATE())) AND (Deleted = 0 and Status not in(255))) AS CallCount30,    
                             (SELECT        TOP (1) uh.CreateDate    
                               FROM            dbo.Users AS u INNER JOIN    
                                                         dbo.UserHistory AS uh ON u.UserId = uh.UserId    
                               WHERE        (uh.ActionId = 1) AND (u.CompanyId = dbo.Companies.CompanyId)    
                               ORDER BY uh.CreateDate) AS FirstLogin,    

        (SELECT        TOP (1)     

        (select max(v) from (    
        VALUES (LastLoginDate), (LastLoginDateAndroid), (LastLoginDateIOS)) as value(v)) as LastLogin    
                               FROM            dbo.Users AS Users_1    
                               WHERE        (CompanyId = dbo.Companies.CompanyId)    
                               ORDER BY LastLogin DESC) AS LastLogin,    

                             (SELECT        CASE WHEN CreateDate IS NULL OR Type = 2 THEN 0     
            WHEN DATEDIFF(DAY, GETDATE(), CreateDate + 30) < 0 THEN 0     
            ELSE DATEDIFF(DAY, GETDATE(), CreateDate + 30)     
            END AS Expr1)     
                         AS DaysLeft,     

       CASE WHEN Type=2 THEN 49     
       ELSE    
       0     
       END AS MonthlyFee,    
       ShortName=coalesce((select top 1 Nickname from CompaniesShared WITH (nolock) WHERE SharedCompanyId=dbo.Companies.CompanyId), Name)    
FROM            dbo.Companies    

    WHERE Type not in (102)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Features' AND TABLE_SCHEMA='Billing' AND COLUMN_NAME = 'DependsOnFeatureId')
BEGIN
    ALTER TABLE Billing.Features ADD DependsOnFeatureId int NULL
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='MotorClubProviderNumberHistory')
CREATE TABLE dbo.MotorClubProviderNumberHistory(
    ProviderNumberHistoryId int identity (1, 1) NOT NULL,
    CompanyId int,
    AccountId int,
    ProviderId varchar(50),
    LocationId varchar(50),
    Parser varchar(30),
    CreateDate datetime not null default(getdate()),
    LastActivity datetime not null default(getdate())

    CONSTRAINT [PK_MotorClubProviderIdentificationHistory] PRIMARY KEY CLUSTERED ( [ProviderNumberHistoryId] ASC )
    CONSTRAINT [FK_MotorClubProviderIdentificationHistory_Companies] FOREIGN KEY (CompanyId) REFERENCES [dbo].[Companies] (CompanyId),
    CONSTRAINT [FK_MotorClubProviderIdentificationHistory_Accounts] FOREIGN KEY (AccountId) REFERENCES [dbo].[Accounts] (AccountId),
)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='RateItems' AND COLUMN_NAME = 'ClassId')
    ALTER TABLE RateItems ADD ClassId INT NOT NULL DEFAULT(0)

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='InvoiceItems' AND COLUMN_NAME = 'ClassId')
    ALTER TABLE InvoiceItems ADD ClassId INT DEFAULT(0)


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='ChargeClasses')
BEGIN
    CREATE TABLE [dbo].[ChargeClasses] (
        [ClassId]		INT IDENTITY (1, 1) NOT NULL,
        [CompanyId]		INT NULL,
        [Name]			VARCHAR(32),
        [Description]	VARCHAR(100),
        
        CONSTRAINT [PK_ChargeClasses] PRIMARY KEY CLUSTERED ([ClassId] ASC),
        CONSTRAINT [FK_ChargeClasses_Companies] FOREIGN KEY (CompanyId) REFERENCES [dbo].[Companies] ([CompanyId]),    
    );

    SET IDENTITY_INSERT ChargeClasses ON
    INSERT INTO ChargeClasses (ClassId, CompanyId, Name, Description) VALUES
    (0, null, 'None', 'Class not specified'),
    (1, null, 'Cash/Retail', 'Charge is to be paid by the customer at the time of the job'),
    (2, null, 'Account', 'Charge is to be paid by the account'),
    (3, null, 'Payout', 'Used to track payouts'),
    (4, null, 'Reimbursement', 'Used to track money the company needs to reimburse to driver. Dont include in invoice total'),
    (5, null, 'Billable Reimbursement', 'Reimburse the driver for this amount, but include as part of invoice total')
    SET IDENTITY_INSERT ChargeClasses OFF
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryPayments' AND COLUMN_NAME = 'ClassId')
    ALTER TABLE DispatchEntryPayments ADD 
        ClassId int Default (0)
        CONSTRAINT FK_InvoicePayments_ChargeClasses FOREIGN KEY (ClassId) REFERENCES ChargeClasses(ClassId)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Statements' AND COLUMN_NAME = 'Subtotal')
    ALTER TABLE Statements ADD Subtotal money NULL, Tax money null, Balance money NULL

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Statements' AND COLUMN_NAME = 'BalanceNow')
    ALTER TABLE Statements ADD BalanceNow money NULL
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Statements' AND COLUMN_NAME = 'SourceId')
    ALTER TABLE Statements ADD SourceId int NOT NULL DEFAULT(0)
GO

IF OBJECT_ID('dbo.[vwCompanyFeatures]') IS NULL EXEC ('CREATE VIEW dbo.[vwCompanyFeatures] AS SELECT 1 as Temp')
GO
ALTER VIEW [dbo].[vwCompanyFeatures] as  
select * from (  
select CompanyId, FeatureId FROM Billing.CompanyContractFeatures CCF  
 INNER JOIN Billing.CompanyContracts CC on   
  CC.IsCancelled=0 AND   
  CC.CompanyContractId=CCF.CompanyContractId AND CCF.IsDeleted=0  
union  
select CS.SharedCompanyId, FeatureId FROM Billing.CompanyContractFeatures CCF  
 INNER JOIN Billing.CompanyContracts CC on CC.IsCancelled=0 AND CC.CompanyContractId=CCF.CompanyContractId AND CCF.IsDeleted=0  
 INNER JOIN CompaniesShared CS on CS.CompanyId=CC.CompanyId  
 union  
select companyid, 12 from Companies c where c.companyid not in (select companyid from billing.CompanyContracts where billingamount > 0 and BillingAmount < 99) 
/*union   
select companyid, 7 from Companies c where c.CompanyId >16012  
union  
select companyid, 8 from Companies c where c.CompanyId >16012  
union  
select companyid, 9 from Companies c where c.CompanyId >16012  
  
union  
select companyid, 37 from Companies c where c.CompanyId >16012 -- agero direct billing */  
) x  
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryPhotos' AND COLUMN_NAME = 'FileSize')
    ALTER TABLE DispatchEntryPhotos ADD 
        FileSize bigint Default (0)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryPhotos' AND COLUMN_NAME = 'Latitude')
    ALTER TABLE DispatchEntryPhotos ADD 
        Latitude decimal(9,6) DEFAULT(0),
        Longitude decimal(9,6) DEFAULT(0)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryPhotos' AND COLUMN_NAME = 'ClientVersionId')
    ALTER TABLE DispatchEntryPhotos ADD 
        ClientVersionId int,
        [Checksum] varchar(64)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryPhotos' AND COLUMN_NAME = 'DispatchEntryStatusId')
    ALTER TABLE DispatchEntryPhotos ADD 
        DispatchEntryStatusId int
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryPhotos' AND COLUMN_NAME = 'CameraLatitude')
    ALTER TABLE DispatchEntryPhotos ADD 
        CameraLatitude decimal(9,6) DEFAULT(NULL),
        CameraLongitude decimal(9,6) DEFAULT(NULL)
GO



 IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryRequestEtas')
    CREATE TABLE [dbo].[DispatchEntryRequestEtas]
    (
        [CallRequestId] [int] NOT NULL,
        [Eta] [int] NULL
    )
 GO
-- Constraints and Indexes

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE WHERE  CONSTRAINT_NAME = 'PK_DispatchEntryRequestEtas')
 ALTER TABLE [dbo].[DispatchEntryRequestEtas] ADD CONSTRAINT [PK_DispatchEntryRequestEtas] PRIMARY KEY CLUSTERED ([CallRequestId])
GO


IF OBJECT_ID('dbo.vwGetDigitalDispatchCallRequests') IS NULL EXEC ('CREATE VIEW dbo.vwGetDigitalDispatchCallRequests AS SELECT 1 as Temp')
GO

ALTER VIEW [dbo].[vwGetDigitalDispatchCallRequests] AS                              
  SELECT                              
   DR.CallRequestId,   
   MA.MasterAccountId,   
   MA.Name as ProviderName,   
   DR.CompanyId,
   DR.ProviderId as ContractorId,
   C.Name as CompanyName,
   DR.AccountId,
   DE.CallNumber,
   DR.StartingLocation,
   DR.Reason,
   DR.ServiceNeeded,
   DR.Vehicle,
   DR.RequestDate,
   DR.ExpirationDate,
   DR.CreateDate,
   DR.Status,
   DR.DispatchEntryId,
   DR.OwnerUserId,
   DR.ResponseReasonId,
   MR.Name as ResponseReasonName,
   U.FullName as OwnerUserName,
   DE.PurchaseOrderNumber,
   DR.TowDestination,
   COALESCE(DR.Eta, ETA.Eta) as EtaGiven
  FROM                              
   DispatchEntryRequests AS DR WITH (nolock)                             
   INNER JOIN Accounts   AS A WITH (nolock) ON A.AccountId = DR.AccountId  
   INNER JOIN MasterAccounts AS MA WITH (nolock) ON MA.MasterAccountId = A.MasterAccountId  
   INNER JOIN Companies AS C  WITH (nolock)ON C.CompanyId = DR.CompanyId  
   LEFT OUTER JOIN Users U WITH (nolock) on U.UserId=DR.OwnerUserId  
   LEFT OUTER JOIN DispatchEntryRequestEtas ETA WITH (nolock)  on ETA.CallRequestId=DR.CallRequestId                              
   LEFT OUTER JOIN Dispatchentries DE WITH (nolock)  on DE.DispatchEntryId=DR.DispatchEntryId                              
   LEFT OUTER JOIN MasterAccountReasons MR  WITH (nolock) on MR.MasterAccountId = A.MasterAccountId and MR.MasterAccountReasonId = DR.ResponseReasonId                         
   WHERE DR.CallRequestId > ********  
GO

IF OBJECT_ID('dbo.vwInternalMotorClubSummary') IS NULL EXEC ('CREATE VIEW dbo.vwInternalMotorClubSummary AS SELECT 1 as Temp')
GO

ALTER VIEW dbo.vwInternalMotorClubSummary AS 
SELECT
    *,
    RecId = ROW_NUMBER() OVER(ORDER BY client, motorclub, callnumber, dispatchentryid, companyid, fileid, statusid, createdate)
    FROM 
    (
        SELECT TOP 10000
            c.name as Client, 
            a.Company as MotorClub, 
            d.CallNumber, 
            d.DispatchEntryId, 
            q.CompanyId,
            df.FileId, 
            q.StatusId, 
            q.CreateDate,
            (select TOP 1 Value FROM DispatchEntryAttributeValues WHERE DispatchEntryId=d.DispatchEntryId and DispatchEntryAttributeId in (1,4)) as PONumber
            from MotorClubProcessingQueue q 
        inner join companies c on c.companyid=q.companyid
        left outer join dispatchentryfiles df  on q.fileid=df.FileId
        left outer join dispatchentries d on d.DispatchEntryId=df.DispatchEntryId
        left outer join accounts a on a.accountid=d.accountid
            order by q.QueueItemId DESC

        union
        select TOP 10000 c.name as Client, 
            a.Company as MotorClub, 
            d.CallNumber, 
            d.DispatchEntryId, 
            q.CompanyId,
            q.FileId, 
            StatusId = 5, 
            q.CreateDate,
            (select TOP 1 Value FROM DispatchEntryAttributeValues WHERE DispatchEntryId=d.DispatchEntryId and DispatchEntryAttributeId in (1,4)) as PONumber
            from Files q 
        inner join companies c on c.companyid=q.companyid
        left outer join dispatchentryfiles df  on q.fileid=df.FileId
        left outer join dispatchentries d on d.DispatchEntryId=df.DispatchEntryId
        left outer join accounts a on a.accountid=d.accountid  

        where q.StorageKey='ImportedJob_Email.eml'
        order by Q.FileId desc
    ) v1
GO

IF OBJECT_ID('dbo.internalDigitalMotorClubAccounts') IS NULL EXEC ('CREATE VIEW dbo.internalDigitalMotorClubAccounts AS SELECT 1 as Temp')
GO

ALTER view [dbo].[internalDigitalMotorClubAccounts] AS
    select InternalId, CompanyId, Name, ContractorId, AccountId, AccountName, MasterAccountId, MasterAccountName, LocationId, LoginStatus, choose(LoginStatus, 'Logged out', 'Logging In', 'Logged in') as LoginStatusName, LastLoginDate, row_number() OVER (ORDER BY CompanyId, MasterAccountId) AS recid FROM (
    select AG.AgeroSessionId as InternalId, AG.CompanyId, C.Name, cast(AG.VendorId as varchar) as ContractorId, a.AccountId, a.company as AccountName, MA.MasterAccountId, MA.Name as MasterAccountName, '' as LocationId, 3 as LoginStatus, AG.CreateDate as LastLoginDate from mcdispatch.AgeroSessions AG 
    inner join accounts a on a.accountid=ag.accountid
    inner join companies C on C.CompanyId=AG.CompanyId
    inner join MasterAccounts MA on MA.MasterAccountId=3

    union
    select AC.AllstateContractorId, AC.CompanyId, C.Name, AC.ContractorId, a.AccountId, A.company as AccountName, AC.MasterAccountId, MA.Name, null, AC.LoginStatus, AC.LastLoginDate from mcdispatch.AllstateContractors AC
        INNER JOIN Accounts A on A.AccountId=AC.AccountId 
        INNER JOIN Companies C on C.CompanyId=AC.CompanyId
        inner join MasterAccounts MA on MA.MasterAccountId=AC.MasterAccountId
        where AC.IsDeleted=0
    union

    select IC.IsscProviderId, IC.CompanyId, C.Name, IC.ContractorId, a.AccountId, A.company as AccountName, A.MasterAccountId, MA.Name, IC.LocationId, IC.LoginStatus, IC.LastLoginDate from mcdispatch.IsscProviders IC
        INNER JOIN Accounts A on A.AccountId=IC.AccountId 
        INNER JOIN Companies C on C.CompanyId=IC.CompanyId
        inner join MasterAccounts MA on MA.MasterAccountId=A.MasterAccountId
    where IC.IsDeleted=0
    )
    z
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='ChatTypes')
    CREATE TABLE [dbo].[ChatTypes] (
        ChatTypeId int NOT NULL,
        Name varchar(50) NOT NULL

        CONSTRAINT [PK_ChatTypes] PRIMARY KEY CLUSTERED (ChatTypeId ASC),
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='ChatDispatchEntries')
    CREATE TABLE [dbo].[ChatDispatchEntries] (
        ChatDispatchEntryId int IDENTITY(1,1) NOT NULL,
        ChatId bigint NOT NULL,
        DispatchEntryId int NOT NULL,
        CreateDate datetime NOT NULL default(getdate()),
        
        CONSTRAINT [PK_ChatDispatchEntries] PRIMARY KEY CLUSTERED (ChatDispatchEntryId ASC),
        CONSTRAINT [FK_ChatDispatchEntries_Chats] FOREIGN KEY(ChatId) REFERENCES dbo.Chats (ChatId),
        CONSTRAINT [FK_ChatDispatchEntries_DispatchEntries] FOREIGN KEY(DispatchEntryId) REFERENCES [dbo].[DispatchEntries] (DispatchEntryId),
    )
GO



IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='ChatMembers')
    CREATE TABLE [dbo].[ChatMembers] (
        ChatMemberId bigint IDENTITY(1,1) NOT NULL,
        ChatId bigint NOT NULL,
        OwnerUserId int NOT NULL,
        UserId int NOT NULL,
        RoleId int NOT NULL DEFAULT(1), 		
        Muted bit NOT NULL DEFAULT(0),
        CreateDate datetime NOT NULL default(getdate()),
        
        CONSTRAINT [PK_ChatMembers] PRIMARY KEY CLUSTERED (ChatMemberId ASC),
        CONSTRAINT [FK_ChatMembers_Chats] FOREIGN KEY(ChatId) REFERENCES dbo.Chats (ChatId),
        CONSTRAINT [FK_ChatMembers_Users] FOREIGN KEY(UserId) REFERENCES [dbo].[Users] (UserId),
        CONSTRAINT [FK_ChatMembers_OwnerUsers] FOREIGN KEY(OwnerUserId) REFERENCES [dbo].[Users] (UserId)
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='ChatMembers' AND COLUMN_NAME = 'Deleted')
    ALTER TABLE ChatMembers ADD 
        Deleted bit NOT NULL DEFAULT(0)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='ChatMembers' AND COLUMN_NAME = 'LastReadMessageId')
    ALTER TABLE ChatMembers ADD 
        LastReadMessageId bigint DEFAULT(null),
        CONSTRAINT [FK_ChatMembers_ChatMessages] FOREIGN KEY(LastReadMessageId) REFERENCES dbo.ChatMessages (ChatMessageId)
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='Chats')
    CREATE TABLE [dbo].[Chats] (
        ChatId bigint IDENTITY(1,1) NOT NULL,
        CompanyId int NOT NULL,
        OwnerUserId int NOT NULL,
        ChatTypeId int NOT NULL DEFAULT(1),
        Status int NOT NULL DEFAULT(0),
        ReadOnly bit NOT NULL DEFAULT(0),
        CreateDate datetime NOT NULL default(getdate())
        
        CONSTRAINT [PK_Chats] PRIMARY KEY CLUSTERED (ChatId ASC),
        CONSTRAINT [FK_Chats_Users] FOREIGN KEY(OwnerUserId) REFERENCES [dbo].[Users] (UserId),
        CONSTRAINT [FK_Chats_Companies] FOREIGN KEY(CompanyId) REFERENCES [dbo].[Companies] (CompanyId),
        CONSTRAINT [FK_Chats_ChatTypes] FOREIGN KEY(ChatTypeId) REFERENCES [dbo].[ChatTypes] (ChatTypeId)
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Chats' AND COLUMN_NAME = 'Name')
    ALTER TABLE Chats ADD 
        Name varchar(100)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Chats' AND COLUMN_NAME = 'Deleted')
    ALTER TABLE Chats ADD 
        Deleted BIT NOT NULL DEFAULT 0
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Chats' AND COLUMN_NAME = 'DeletedByUserId')
    ALTER TABLE Chats ADD 
        DeletedByUserId INT
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Chats' AND COLUMN_NAME = 'DeleteDate')
    ALTER TABLE Chats ADD 
        DeleteDate DATETIME
GO




IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='ChatMessages')
    CREATE TABLE [dbo].[ChatMessages] (
        ChatMessageId bigint IDENTITY(1,1) NOT NULL,
        ChatId bigint NOT NULL,
        CreateDate datetime NOT NULL default(getdate()),
        SenderUserId int NOT NULL,
        Latitude decimal(9,6) NOT NULL DEFAULT(0),
        Longitude decimal(9,6) NOT NULL DEFAULT(0),
        PriorityId int NOT NULL DEFAULT(0),
        Message varchar(1000) NOT NULL,
        
        CONSTRAINT [PK_ChatMessages] PRIMARY KEY CLUSTERED (ChatMessageId ASC),
        CONSTRAINT [FK_ChatMessages_Chats] FOREIGN KEY(ChatId) REFERENCES [dbo].[Chats] (ChatId),
        CONSTRAINT [FK_ChatMessages_Users] FOREIGN KEY(SenderUserId) REFERENCES [dbo].[Users] (UserId)
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='ChatMessages' AND COLUMN_NAME = 'Nonce')
    ALTER TABLE ChatMessages ADD 
        Nonce bigint
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='ChatMessageDeliveries')
    CREATE TABLE [dbo].[ChatMessageDeliveries] (
        ChatMessageDeliveryId bigint IDENTITY(1,1) NOT NULL,
        ChatMessageId bigint NOT NULL,
        UserId int NOT NULL,
        DeliveredDate datetime,
        ReadDate datetime,

        CONSTRAINT [PK_ChatMessageDeliveries] PRIMARY KEY CLUSTERED (ChatMessageDeliveryId ASC),
        CONSTRAINT [FK_ChatMessageDeliveries_ChatMessages] FOREIGN KEY(ChatMessageId) REFERENCES dbo.ChatMessages (ChatMessageId),
        CONSTRAINT [FK_ChatMessageDeliveries_Users] FOREIGN KEY(UserId) REFERENCES dbo.Users (UserId)
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='ChatUserStatuses')
    CREATE TABLE [dbo].[ChatUserStatuses] (
        ChatUserStatusId int IDENTITY(1,1) NOT NULL,
        UserId int NOT NULL,
        StatusId int NOT NULL,
        LastChangeDate datetime NOT NULL,
        
        CONSTRAINT [PK_ChatUserStatuses] PRIMARY KEY CLUSTERED (ChatUserStatusId ASC),
        CONSTRAINT [FK_ChatUserStatuses_Users] FOREIGN KEY(UserId) REFERENCES dbo.Users (UserId)
    )
GO


/*************************************************************************************************
 ** Dispatch Layouts
 *************************************************************************************************/

IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='DispatchLayouts' )
BEGIN
    CREATE TABLE dbo.[DispatchLayouts] (
        [DispatchLayoutId]      INT IDENTITY (1, 1) NOT NULL,
        [CompanyId]     INT,
        [OneLine]       BIT DEFAULT(0) NOT NULL

        CONSTRAINT [PK_DispatchLayouts] PRIMARY KEY CLUSTERED ([DispatchLayoutId] ASC),
        CONSTRAINT [FK_DispatchLayouts_Companies] FOREIGN KEY ([CompanyId]) REFERENCES dbo.[Companies] ([CompanyId])
    )

    SET IDENTITY_INSERT [DispatchLayouts] ON
    INSERT INTO [DispatchLayouts] (DispatchLayoutId, CompanyId, OneLine) VALUES
    (0, null, 0)
    SET IDENTITY_INSERT [DispatchLayouts] OFF

    CREATE UNIQUE NONCLUSTERED INDEX IX_DispatchLayouts_CompanyId ON dbo.[DispatchLayouts] (CompanyId)
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchLayouts' AND COLUMN_NAME = 'UserId')
    
    ALTER TABLE DispatchLayouts ADD UserId int
        CONSTRAINT [FK_DispatchLayouts_Users] FOREIGN KEY ([UserId]) REFERENCES dbo.[Users] ([UserId])
    GO
    
    DROP INDEX IX_DispatchLayouts_CompanyId on dbo.[DispatchLayouts]
    GO
	
    CREATE NONCLUSTERED INDEX IX_DispatchLayouts_CompanyId ON dbo.[DispatchLayouts] (CompanyId)
GO



IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='DispatchLayoutColumns' )
BEGIN
    CREATE TABLE dbo.[DispatchLayoutColumns] (
        [DispatchLayoutColumnId] INT IDENTITY (1, 1) NOT NULL,
        [DispatchLayoutId] INT NOT NULL,
        [Section]        INT NOT NULL,
        [ColumnId]       INT NOT NULL,
        [Width]          INT NOT NULL,
        [Order]          INT NOT NULL,
        [Color]          VARCHAR(10) NOT NULL,
        [Weight]         INT NOT NULL,
        [AttributeId]    INT NOT NULL DEFAULT(0)

        CONSTRAINT [PK_DispatchLayoutColumns] PRIMARY KEY CLUSTERED ([DispatchLayoutColumnId] ASC),
        CONSTRAINT [FK_DispatchLayoutColumns_DispatchLayouts] FOREIGN KEY ([DispatchLayoutId]) REFERENCES dbo.[DispatchLayouts] ([DispatchLayoutId])
    )

    CREATE NONCLUSTERED INDEX IX_DispatchLayoutColumns_DispatchLayoutId_Order ON dbo.[DispatchLayoutColumns] (DispatchLayoutId, [Order])
END
GO

IF (SELECT COUNT(1) FROM DispatchLayoutColumns) = 0 
BEGIN
    DELETE FROM DispatchLayoutColumns WHERE DispatchLayoutId = 0
    SET IDENTITY_INSERT [DispatchLayoutColumns] ON
    INSERT INTO [DispatchLayoutColumns] ([DispatchLayoutColumnId], [DispatchLayoutId], [Section], [ColumnId], [Width], [Order], [Color], [Weight], [AttributeId]) VALUES
        ( 0, 0, 1, 2, 4, 0, '#005EB1', 0, 0 ),
        ( 1, 0, 1, 14, 1, 1, '#555555', 0, 0 ),
        ( 2, 0, 1, 3, 2, 2, '#555555', 0, 0 ),
        ( 3, 0, 1, 4, 2, 3, '#555555', 0, 0 ),
        ( 4, 0, 2, 7, 3, 0, '#005EB1', 0, 0 ),
        ( 5, 0, 2, 15, 1, 1, '#555555', 0, 0 ),
        ( 6, 0, 2, 5, 1, 2, '#555555', 0, 0 ),
        ( 7, 0, 2, 6, 2, 3, '#555555', 0, 0 ),
        ( 8, 0, 2, 8, 2, 4, '#555555', 0, 0 ),
        ( 9, 0, 2, 9, 1, 5, '#555555', 0, 0 ),
        ( 10, 0, 2, 13, 1, 6, '#555555', 0, 0 ),
        ( 11, 0, 3, 16, 2, 0, '#555555', 0, 0 ),
        ( 12, 0, 3, 11, 2, 1, '#555555', 0, 0 ),
        ( 13, 0, 3, 17, 2, 2, '#555555', 0, 0 ),
        ( 14, 0, 3, 18, 2, 3, '#555555', 0, 0 ),
        ( 15, 0, 3, 19, 2, 4, '#555555', 0, 0 ),
        ( 16, 0, 3, 20, 2, 5, '#555555', 0, 0 ),
        ( 17, 0, 3, 21, 2, 6, '#555555', 0, 0 ),
        ( 18, 0, 3, 22, 2, 7, '#555555', 0, 0 ),
        ( 19, 0, 3, 23, 2, 8, '#555555', 0, 0 ),
        ( 20, 0, 3, 24, 2, 9, '#555555', 0, 0 )
    SET IDENTITY_INSERT [DispatchLayoutColumns] OFF
END
GO

/*************************************************************************************************
 ** Invoice Status Workflow
 *************************************************************************************************/

IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='InvoiceStatusWorkflows' )
BEGIN
    CREATE TABLE dbo.InvoiceStatusWorkflows (
        InvoiceStatusWorkflowId INT IDENTITY (1, 1) NOT NULL,
        Name                    VARCHAR(100) NOT NULL,
        CompanyId               INT NULL

        CONSTRAINT [PK_InvoiceStatusWorkflows] PRIMARY KEY CLUSTERED (InvoiceStatusWorkflowId),
        CONSTRAINT [FK_InvoiceStatusWorkflows_CompanyId] FOREIGN KEY (CompanyId) REFERENCES dbo.Companies (CompanyId)
    )

    CREATE NONCLUSTERED INDEX IX_InvoiceStatusWorkflows_CompanyId
    ON dbo.InvoiceStatusWorkflows (CompanyId)
END
GO

IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='InvoiceStatusWorkflowStatuses' )
BEGIN
    CREATE TABLE dbo.InvoiceStatusWorkflowStatuses (
        InvoiceStatusWorkflowStatusId INT IDENTITY (1, 1) NOT NULL,
        InvoiceStatusWorkflowId INT NOT NULL,
        InvoiceStatusId         INT NOT NULL,
        DisplayOrder            INT NOT NULL,
        Locked                  BIT NOT NULL

        CONSTRAINT [PK_InvoiceStatusWorkflowStatuses] PRIMARY KEY CLUSTERED (InvoiceStatusWorkflowStatusId),
        CONSTRAINT [FK_InvoiceStatusWorkflowStatuses_InvoiceStatuses] FOREIGN KEY (InvoiceStatusId) REFERENCES dbo.InvoiceStatuses (InvoiceStatusId),
        CONSTRAINT [FK_InvoiceStatusWorkflowStatuses_InvoiceStatusWorkflows] FOREIGN KEY (InvoiceStatusWorkflowId) REFERENCES dbo.InvoiceStatusWorkflows (InvoiceStatusWorkflowId),
    )

    CREATE NONCLUSTERED INDEX IX_InvoiceStatusWorkflowStatuses_InvoiceStatusWorkflowId 
    ON dbo.InvoiceStatusWorkflowStatuses (InvoiceStatusWorkflowId)
END
GO

IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='InvoiceStatusWorkflowAccountTypes' )
BEGIN
    CREATE TABLE dbo.InvoiceStatusWorkflowAccountTypes (
        AccountTypeId           INT NOT NULL,
        InvoiceStatusWorkflowId INT NOT NULL

        CONSTRAINT [PK_InvoiceStatusWorkflowAccountTypes] PRIMARY KEY CLUSTERED (AccountTypeId, InvoiceStatusWorkflowId),
        CONSTRAINT [FK_InvoiceStatusWorkflowAccountTypes_AccountTypes] FOREIGN KEY (AccountTypeId) REFERENCES dbo.AccountTypes (AccountTypeId),
        CONSTRAINT [FK_InvoiceStatusWorkflowAccountTypes_InvoiceStatusWorkflows] FOREIGN KEY (InvoiceStatusWorkflowId) REFERENCES dbo.InvoiceStatusWorkflows (InvoiceStatusWorkflowId)
    )
END
GO

IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='InvoiceStatusWorkflowAccounts' )
BEGIN
    CREATE TABLE dbo.InvoiceStatusWorkflowAccounts (
        AccountId               INT NOT NULL,
        InvoiceStatusWorkflowId INT NOT NULL

        CONSTRAINT [PK_InvoiceStatusWorkflowAccounts] PRIMARY KEY CLUSTERED (AccountId, InvoiceStatusWorkflowId),
        CONSTRAINT [FK_InvoiceStatusWorkflowAccounts_Accounts] FOREIGN KEY (AccountId) REFERENCES dbo.Accounts (AccountId),
        CONSTRAINT [FK_InvoiceStatusWorkflowAccounts_InvoiceStatusWorkflows] FOREIGN KEY (InvoiceStatusWorkflowId) REFERENCES dbo.InvoiceStatusWorkflows (InvoiceStatusWorkflowId)
    )
END
GO

/*************************************************************************************************
 ** Invoice Status Workflow - Initial Population
 *************************************************************************************************/
 
-- Invoice Statuses
IF (SELECT COUNT(1) FROM InvoiceStatuses) = 0 
BEGIN
    SET IDENTITY_INSERT InvoiceStatuses ON
    INSERT INTO InvoiceStatuses (InvoiceStatusId, CompanyId, Name, StatusOrder, OwnerUserId, IsSystemOnly, Deleted) VALUES
        (1, null, 'Completed',               1, 1, null, 0),
        (2, null, 'Ready to Bill',           2, 1, null, 0),
        (3, null, 'Submitted',               3, 1, null, 0),
        (4, null, 'Failed to Submit',        4, 1, null, 0),
        (5, null, 'Billed',                  5, 1, null, 0),
        (8, null, 'Dispute With Motor Club', 8, 1, null, 0),
        (9, null, 'Sent Notification',       9, 1, null, 0),
        (10, null, 'Sent to Police Dept',    10, 1, null, 0),
        (11, null, 'TR-52 Requested',        11, 1, null, 0),
        (12, null, 'Closed',                 12, 1, null, 0)
    SET IDENTITY_INSERT InvoiceStatuses OFF
END
GO
-- Edit name
IF EXISTS(SELECT * FROM InvoiceStatuses WHERE Name = 'Dispute With Motor Club')
	UPDATE InvoiceStatuses SET Name = 'Disputed' WHERE Name = 'Dispute With Motor Club'

-- insert system only status of 'Unpaid'
IF (SELECT COUNT(1) FROM InvoiceStatuses where InvoiceStatusId=13) = 0
BEGIN
    SET IDENTITY_INSERT InvoiceStatuses ON
    insert into InvoiceStatuses (InvoiceStatusId, Name, Description, StatusOrder, OwnerUserId, IsSystemOnly) values (13, 'Unpaid', 'Filtered view of invoices that have ANY balance', 13, 1, 1)
    SET IDENTITY_INSERT InvoiceStatuses OFF
END
GO

-- Invoice Status Workflows
IF (SELECT COUNT(1) FROM InvoiceStatusWorkflows) = 0 
BEGIN
    SET IDENTITY_INSERT InvoiceStatusWorkflows ON
    INSERT INTO InvoiceStatusWorkflows (InvoiceStatusWorkflowId, Name) VALUES
        (1, 'Motor Club'),
        (2, 'Motor Club with Direct Billing'),
        (3, 'Generic'),
        (4, 'Police Department'),
        (5, 'Storage Lot'),
        (6, 'Private Property')
    SET IDENTITY_INSERT InvoiceStatusWorkflows OFF
END
GO

-- Invoice Status Workflow Statuses
IF (SELECT COUNT(1) FROM InvoiceStatusWorkflowStatuses) = 0 
BEGIN
    SET IDENTITY_INSERT InvoiceStatusWorkflowStatuses ON
    INSERT INTO InvoiceStatusWorkflowStatuses (InvoiceStatusWorkflowStatusId, InvoiceStatusWorkflowId, InvoiceStatusId, DisplayOrder, Locked) VALUES
        (1, 1, 1, 1, 0),  -- Motor Club
        (2, 1, 2, 2, 0),
        (5, 1, 8, 5, 0),
        (6, 1, 12, 6, 0),
        (7, 2, 1, 1, 0), -- Motor Club with Direct Billing
        (8, 2, 2, 2, 0),
        (9, 2, 3, 3, 1),
        (10, 2, 4, 4, 1),
        (13, 2, 8, 7, 0),
        (14, 2, 12, 8, 0),
        (15, 3, 1, 1, 0), -- Generic Account
        (16, 3, 2, 2, 0),
        (17, 3, 5, 3, 0),
        (20, 3, 12, 6, 0),
        (21, 4, 1, 1, 0), -- Police Department
        (22, 4, 10, 2, 0),
        (23, 4, 11, 3, 0),
        (24, 4, 12, 4, 0),
        (25, 5, 1, 1, 0), -- Storage Lot
        (26, 5, 2, 2, 0),
        (27, 5, 5, 3, 0),
        (30, 5, 12, 6, 0),
        (31, 6, 1, 1, 0), -- Private Property
        (32, 6, 10, 2, 0),
        (33, 6, 12, 3, 0)
    SET IDENTITY_INSERT InvoiceStatusWorkflowStatuses OFF
END
GO

-- Add 'billed' tab option to 'Motor Club' workflow
IF (SELECT COUNT(1) FROM InvoiceStatusWorkflowStatuses where InvoiceStatusWorkflowId=1 and invoiceStatusId=5 ) = 0
BEGIN
    insert into InvoiceStatusWorkFlowStatuses (InvoiceStatusWorkflowId, InvoiceStatusId, DisplayOrder, Locked) values (1, 5, 4, 0)
END
GO

-- Remove Closed tab option from workflows
IF (SELECT COUNT(1) FROM InvoiceStatusWorkflowStatuses where invoiceStatusId=12) > 0
BEGIN
    delete from InvoiceStatusWorkflowStatuses where invoiceStatusId=12
END
GO

-- Remove "Disputed", "Sent to Police Dept", "TR-52 Requested" options from any workflows
IF (SELECT COUNT(1) FROM InvoiceStatusWorkflowStatuses where invoiceStatusId in (8,9,10,11)) > 0
BEGIN
    delete from InvoiceStatusWorkflowStatuses where invoiceStatusId in (8,9,10,11)
END
GO

-- Remove Partially Paid / Paid invoice statuses from workflows
IF (SELECT COUNT(1) FROM InvoiceStatusWorkflowStatuses where InvoiceStatusId IN (6,7)) > 0
BEGIN
	UPDATE DispatchEntries SET InvoiceStatusId = NULL WHERE InvoiceStatusId IN (6,7)
	DELETE FROM InvoiceStatuses WHERE InvoiceStatusId IN (6,7)
	DELETE FROM InvoiceStatusWorkflowStatuses WHERE InvoiceStatusId IN (6,7)
END
GO

-- Invoice Status Workflow AccountTypes
IF (SELECT COUNT(1) FROM InvoiceStatusWorkflowAccountTypes) = 0 
BEGIN
    INSERT INTO InvoiceStatusWorkflowAccountTypes (AccountTypeId, InvoiceStatusWorkflowId) VALUES
    (0, 3),   -- Other,              Generic
    (1, 4),   -- Police,             Police
    (2, 3),   -- Individual,         Generic
    (3, 3),   -- Body Shop,          Generic
    (4, 3),   -- Insurance,          Generic
    (5, 1),   -- Motor Club,         Motor Club
    (6, 3),   -- Municipality,       Generic
    (7, 3),   -- Service Shop,       Generic
    (8, 5),   -- Storage Facility,   Storage Lot
    (9, 6),   -- Private Property,   Private Property
    (10, 3),  -- Reposession Agency, Generic
    (11, 3),  -- Dealership,         Generic
    (12, 3),  -- Heavy Equipment,    Generic
    (13, 3),  -- Fleet,              Generic
    (14, 3),  -- Broker,             Generic
    (15, 3)   -- Transport,          Generic
END
GO

-- Add Invoice status workflow for AccountType 'Transport'
IF (SELECT COUNT(1) FROM InvoiceStatusWorkflowAccountTypes WHERE AccountTypeId=15) = 0 
BEGIN
    INSERT INTO InvoiceStatusWorkflowAccountTypes (AccountTypeId, InvoiceStatusWorkflowId) values (15, 3)
END
GO


---- Invoice Status Workflow Accounts
--IF (SELECT COUNT(1) FROM InvoiceStatusWorkflowAccounts) = 0 
--BEGIN
--	INSERT INTO InvoiceStatusWorkflowAccounts (AccountId, InvoiceStatusWorkflowId) VALUES
--	(11359, 5)   -- AC Individual
--END
--GO


/*************************************************************************************************
 ** Dispatch Entry - Alterations
 *************************************************************************************************/

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntries' AND COLUMN_NAME = 'InvoiceNumber')
    ALTER TABLE DispatchEntries ADD 
        InvoiceNumber varchar(50), 
        PurchaseOrderNumber varchar(50)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntries' AND COLUMN_NAME = 'CallRequestId')
    ALTER TABLE DispatchEntries ADD 
        CallRequestId int
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntries' AND COLUMN_NAME = 'DestinationArrivalTime')
    ALTER TABLE DispatchEntries ADD 
        DestinationArrivalTime smalldatetime
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntries' AND COLUMN_NAME = 'DispatchEntryLockId')
    ALTER TABLE DispatchEntries ADD 
        DispatchEntryLockId int
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='DispatchEntryStatusRecords')
    CREATE TABLE dbo.DispatchEntryStatusRecords (
        DispatchEntryStatusRecordId int IDENTITY(1,1) NOT NULL,
        DispatchEntryId		int,
        OwnerUserId			int,
        IpAddress			varchar(255),
        ClientVersionId		int,
        PreviousStatusId	int,
        NewStatusId			int,
        Latitude			decimal(9,6),
        Longitude			decimal(9,6),
        DriverId			int,
        CreateDate			datetime DEFAULT(getdate()) NOT NULL

        CONSTRAINT PK_DispatchEntryStatusRecords PRIMARY KEY CLUSTERED ( DispatchEntryStatusRecordId ASC ),
        CONSTRAINT FK_DispatchEntryStatusRecords_DispatchEntries FOREIGN KEY(DispatchEntryId) REFERENCES dbo.DispatchEntries (DispatchEntryId),
        CONSTRAINT FK_DispatchEntryStatusRecords_Users FOREIGN KEY(OwnerUserId) REFERENCES dbo.Users (UserId),
        CONSTRAINT FK_DispatchEntryStatusRecords_PrevStatus FOREIGN KEY(PreviousStatusId) REFERENCES dbo.DispatchStatuses (DispatchStatusId),
        CONSTRAINT FK_DispatchEntryStatusRecords_NewStatus FOREIGN KEY(NewStatusId) REFERENCES dbo.DispatchStatuses (DispatchStatusId)
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryStatusRecords' AND COLUMN_NAME = 'IpAddress')
    ALTER TABLE DispatchEntryStatusRecords ADD IpAddress varchar(255) NULL
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryStatusRecords' AND COLUMN_NAME = 'ClientVersionId')
    ALTER TABLE DispatchEntryStatusRecords ADD ClientVersionId int null
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryStatusRecords' AND COLUMN_NAME = 'QueueItemId')
    ALTER TABLE DispatchEntryStatusRecords ADD  QueueItemId bigint null
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryPhotos' AND COLUMN_NAME = 'IsDeleted')
    ALTER TABLE DispatchEntryPhotos ADD IsDeleted BIT DEFAULT(0) NOT NULL

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='NotificationMessages' AND COLUMN_NAME = 'StatusId')
    ALTER TABLE NotificationMessages ADD 
        StatusId int, 
        StatusTime datetime,
        StatusResponse varchar(4000)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='NotificationMessages' AND COLUMN_NAME = 'ReadDate')
    ALTER TABLE NotificationMessages ADD 
        ReadDate datetime
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='NotificationMessages' AND COLUMN_NAME = 'SenderUserId')
    ALTER TABLE NotificationMessages ADD 
        SenderUserId int,

        CONSTRAINT FK_NotificationMessages_SenderUsers FOREIGN KEY(SenderUserId) REFERENCES dbo.Users (UserId)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='DispatchEntryDamages')
BEGIN
    CREATE TABLE [dbo].[DispatchEntryDamages] (
        DispatchEntryDamageId	int identity (1, 1) not null,
        DispatchEntryId	int not null,
        DispatchEntryStatusId int,
        AssetId					int not null,
        OwnerUserId			int,
        Latitude 			  decimal(9,6),
        Longitude 			decimal(9,6),
        Description			varchar(300),
        CreateDate		  datetime not null default(getdate()),

        CONSTRAINT [PK_DispatchEntryDamages] PRIMARY KEY CLUSTERED (DispatchEntryDamageId ASC),
        CONSTRAINT [FK_DispatchEntryDamages_DispatchEntries] FOREIGN KEY (DispatchEntryId) REFERENCES dbo.DispatchEntries (DispatchEntryId),
        CONSTRAINT [FK_DispatchEntryDamages_DispatchEntryAssets] FOREIGN KEY (AssetId) REFERENCES dbo.DispatchEntryAssets (AssetId),
        CONSTRAINT [FK_DispatchEntryDamages_Users_Owner] FOREIGN KEY (OwnerUserId) REFERENCES dbo.Users (UserId)
    );

    GRANT INSERT ON DispatchEntryDamages to public
    GRANT SELECT ON DispatchEntryDamages to public
    GRANT UPDATE ON DispatchEntryDamages to public
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryDamages' AND COLUMN_NAME = 'ClassId')
    ALTER TABLE DispatchEntryDamages
        ADD ClassId int NULL
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryDamages' AND COLUMN_NAME = 'Location')
    ALTER TABLE DispatchEntryDamages
        ADD Location varchar(300) NULL
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='DispatchEntryDamageRegions')
BEGIN
    CREATE TABLE [dbo].[DispatchEntryDamageRegions] (
        DispatchEntryDamageRegionId	int identity (1, 1) not null,
        DispatchEntryId int not null,
        DispatchEntryDamageId int not null,
        AssetId					int not null,
        RegionId				int not null,
        TypeId					int not null,
        Description			varchar(300),
        CreateDate			datetime not null default(getdate()),

        CONSTRAINT [PK_DispatchEntryDamageRegions] PRIMARY KEY CLUSTERED (DispatchEntryDamageRegionId ASC),
        CONSTRAINT [FK_DispatchEntryDamageRegions_DispatchEntries] FOREIGN KEY (DispatchEntryId) REFERENCES dbo.DispatchEntries (DispatchEntryId),
        CONSTRAINT [FK_DispatchEntryDamageRegions_DispatchEntryAssets] FOREIGN KEY (AssetId) REFERENCES dbo.DispatchEntryAssets (AssetId),
        CONSTRAINT [FK_DispatchEntryDamageRegions_DispatchEntryDamages] FOREIGN KEY (DispatchEntryDamageId) REFERENCES dbo.DispatchEntryDamages (DispatchEntryDamageId),
    );

    GRANT INSERT ON DispatchEntryDamages to public
    GRANT SELECT ON DispatchEntryDamages to public
    GRANT UPDATE ON DispatchEntryDamages to public
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='DispatchEntryDamageRel')
BEGIN
    CREATE TABLE [dbo].[DispatchEntryDamageRel] (
        DispatchEntryDamageId       int not null,
        DispatchEntryDamageRegionId	int not null,

        CONSTRAINT [PK_DispatchEntryDamageRel] PRIMARY KEY CLUSTERED (DispatchEntryDamageId, DispatchEntryDamageRegionId),
        CONSTRAINT [FK_DispatchEntryDamageRel_DispatchEntryDamages] FOREIGN KEY (DispatchEntryDamageId) REFERENCES dbo.DispatchEntryDamages (DispatchEntryDamageId),
        CONSTRAINT [FK_DispatchEntryDamageRel_DispatchEntryDamageRegions] FOREIGN KEY (DispatchEntryDamageRegionId) REFERENCES dbo.DispatchEntryDamageRegions (DispatchEntryDamageRegionId),
    );

    GRANT INSERT ON DispatchEntryDamages to public
    GRANT SELECT ON DispatchEntryDamages to public
END
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='DispatchEntryDamagePhotos')
BEGIN
    CREATE TABLE [dbo].[DispatchEntryDamagePhotos] (
        DispatchEntryDamagePhotoId	int identity (1, 1) not null,
        DispatchEntryId 						int not null,
        DispatchEntryDamageId				int not null,
        DispatchEntryDamageRegionId	int,
        PhotoType					varchar(10) check (PhotoType IN('PHOTO', 'SIGNATURE')),
        ContentType				varchar(50),
        OwnerUserId				int,
        CreateDate				datetime not null default(getdate()),

        CONSTRAINT [PK_DispatchEntryDamagePhotos] PRIMARY KEY CLUSTERED (DispatchEntryDamagePhotoId ASC),
        CONSTRAINT [FK_DispatchEntryDamagePhotos_DispatchEntryRegions] FOREIGN KEY (DispatchEntryDamageRegionId) REFERENCES dbo.DispatchEntryDamageRegions (DispatchEntryDamageRegionId),
        CONSTRAINT [FK_DispatchEntryDamagePhotos_DispatchEntryDamages] FOREIGN KEY (DispatchEntryDamageId) REFERENCES dbo.DispatchEntryDamages (DispatchEntryDamageId),
        CONSTRAINT [FK_DispatchEntryDamagePhotos_Users_Owner] FOREIGN KEY (OwnerUserId) REFERENCES dbo.Users (UserId)
    );

    GRANT INSERT ON DispatchEntryDamagePhotos to public
    GRANT SELECT ON DispatchEntryDamagePhotos to public
    GRANT UPDATE ON DispatchEntryDamagePhotos to public
END
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchStatuses' AND COLUMN_NAME = 'HexColor')
    ALTER TABLE DispatchStatuses ADD HexColor VARCHAR(7)
GO

SET IDENTITY_INSERT CompanyDispatchStatuses ON

IF NOT EXISTS(SELECT 1 FROM CompanyDispatchStatuses WHERE CompanyId IS NULL AND DispatchStatusId = 0)
	INSERT INTO CompanyDispatchStatuses (CompanyId, DispatchStatusId) VALUES (NULL, 0)
GO
IF NOT EXISTS(SELECT 1 FROM CompanyDispatchStatuses WHERE CompanyId IS NULL AND DispatchStatusId = 1)
	INSERT INTO CompanyDispatchStatuses (CompanyId, DispatchStatusId) VALUES (NULL, 1)
GO
IF NOT EXISTS(SELECT 1 FROM CompanyDispatchStatuses WHERE CompanyId IS NULL AND DispatchStatusId = 2)
	INSERT INTO CompanyDispatchStatuses (CompanyId, DispatchStatusId) VALUES (NULL, 2)
GO
IF NOT EXISTS(SELECT 1 FROM CompanyDispatchStatuses WHERE CompanyId IS NULL AND DispatchStatusId = 3)
	INSERT INTO CompanyDispatchStatuses (CompanyId, DispatchStatusId) VALUES (NULL, 3)
GO
IF NOT EXISTS(SELECT 1 FROM CompanyDispatchStatuses WHERE CompanyId IS NULL AND DispatchStatusId = 4)
	INSERT INTO CompanyDispatchStatuses (CompanyId, DispatchStatusId) VALUES (NULL, 4)
GO
IF NOT EXISTS(SELECT 1 FROM CompanyDispatchStatuses WHERE CompanyId IS NULL AND DispatchStatusId = 7)
	INSERT INTO CompanyDispatchStatuses (CompanyId, DispatchStatusId) VALUES (NULL, 7)
GO
IF NOT EXISTS(SELECT 1 FROM CompanyDispatchStatuses WHERE CompanyId IS NULL AND DispatchStatusId = 5)
	INSERT INTO CompanyDispatchStatuses (CompanyId, DispatchStatusId) VALUES (NULL, 5)
GO
IF NOT EXISTS(SELECT 1 FROM CompanyDispatchStatuses WHERE CompanyId IS NULL AND DispatchStatusId = 255)
	INSERT INTO CompanyDispatchStatuses (CompanyId, DispatchStatusId) VALUES (NULL, 255)
GO

SET IDENTITY_INSERT CompanyDispatchStatuses OFF

IF EXISTS(SELECT * FROM CompanyDispatchStatuses WHERE DisplayOrder != 0 AND CompanyId IS NULL AND DispatchStatusId = 0)
                  UPDATE CompanyDispatchStatuses SET DisplayOrder = 0 WHERE CompanyId IS NULL AND DispatchStatusId = 0

IF EXISTS(SELECT * FROM CompanyDispatchStatuses WHERE DisplayOrder != 1 AND CompanyId IS NULL AND DispatchStatusId = 1)
                  UPDATE CompanyDispatchStatuses SET DisplayOrder = 1 WHERE CompanyId IS NULL AND DispatchStatusId = 1

IF EXISTS(SELECT * FROM CompanyDispatchStatuses WHERE DisplayOrder != 2 AND CompanyId IS NULL AND DispatchStatusId = 2)
                  UPDATE CompanyDispatchStatuses SET DisplayOrder = 2 WHERE CompanyId IS NULL AND DispatchStatusId = 2

IF EXISTS(SELECT * FROM CompanyDispatchStatuses WHERE DisplayOrder != 3 AND CompanyId IS NULL AND DispatchStatusId = 3)
                  UPDATE CompanyDispatchStatuses SET DisplayOrder = 3 WHERE CompanyId IS NULL AND DispatchStatusId = 3

IF EXISTS(SELECT * FROM CompanyDispatchStatuses WHERE DisplayOrder != 4 AND CompanyId IS NULL AND DispatchStatusId = 4)
                  UPDATE CompanyDispatchStatuses SET DisplayOrder = 4 WHERE CompanyId IS NULL AND DispatchStatusId = 4

IF EXISTS(SELECT * FROM CompanyDispatchStatuses WHERE DisplayOrder != 5 AND CompanyId IS NULL AND DispatchStatusId = 7)
                  UPDATE CompanyDispatchStatuses SET DisplayOrder = 5 WHERE CompanyId IS NULL AND DispatchStatusId = 7

IF EXISTS(SELECT * FROM CompanyDispatchStatuses WHERE DisplayOrder != 6 AND CompanyId IS NULL AND DispatchStatusId = 5)
                  UPDATE CompanyDispatchStatuses SET DisplayOrder = 6 WHERE CompanyId IS NULL AND DispatchStatusId = 5

IF EXISTS(SELECT * FROM CompanyDispatchStatuses WHERE DisplayOrder != 2147483647 AND CompanyId IS NULL AND DispatchStatusId = 255)
                  UPDATE CompanyDispatchStatuses SET DisplayOrder = 2147483647 WHERE CompanyId IS NULL AND DispatchStatusId = 255

SET IDENTITY_INSERT DispatchStatuses ON

IF NOT EXISTS(SELECT 1 FROM DispatchStatuses WHERE DispatchStatusId = 0)
	INSERT INTO DispatchStatuses (DispatchStatusId, CompanyId, Name, ExtendedName, System, Type)  VALUES (0, NULL, 'Waiting', 'Waiting', 1, 0)
GO
IF NOT EXISTS(SELECT 1 FROM DispatchStatuses WHERE DispatchStatusId = 1)
	INSERT INTO DispatchStatuses (DispatchStatusId, CompanyId, Name, ExtendedName, System, Type)  VALUES (1, NULL, 'Dispatched', 'Dispatched to driver', 1, 0)
GO
IF NOT EXISTS(SELECT 1 FROM DispatchStatuses WHERE DispatchStatusId = 2)
	INSERT INTO DispatchStatuses (DispatchStatusId, CompanyId, Name, ExtendedName, System, Type)  VALUES (2, NULL, 'Enroute', 'Enroute to pickup location', 1, 0)
GO
IF NOT EXISTS(SELECT 1 FROM DispatchStatuses WHERE DispatchStatusId = 3)
	INSERT INTO DispatchStatuses (DispatchStatusId, CompanyId, Name, ExtendedName, System, Type)  VALUES (3, NULL, 'On Scene', 'On Scene', 1, 0)
GO
IF NOT EXISTS(SELECT 1 FROM DispatchStatuses WHERE DispatchStatusId = 4)
	INSERT INTO DispatchStatuses (DispatchStatusId, CompanyId, Name, ExtendedName, System, Type)  VALUES (4, NULL, 'Towing', 'Towing', 1, 0)
GO
IF NOT EXISTS(SELECT 1 FROM DispatchStatuses WHERE DispatchStatusId = 7)
	INSERT INTO DispatchStatuses (DispatchStatusId, CompanyId, Name, ExtendedName, System, Type)  VALUES (7, NULL, 'Destination Arrival', 'Destination Arrival', 1, 0)
GO
IF NOT EXISTS(SELECT 1 FROM DispatchStatuses WHERE DispatchStatusId = 5)
	INSERT INTO DispatchStatuses (DispatchStatusId, CompanyId, Name, ExtendedName, System, Type)  VALUES (5, NULL, 'Completed', 'Completed', 1, 0)
GO
IF NOT EXISTS(SELECT 1 FROM DispatchStatuses WHERE DispatchStatusId = 255)
	INSERT INTO DispatchStatuses (DispatchStatusId, CompanyId, Name, ExtendedName, System, Type)  VALUES (255, NULL, 'Cancelled', 'Cancelled', 1, 0)
GO

SET IDENTITY_INSERT DispatchStatuses OFF

IF EXISTS(SELECT * FROM DispatchStatuses WHERE HexColor != '#ff9900' AND DispatchStatusId = 0)
                 UPDATE DispatchStatuses SET HexColor  = '#ff9900', HtmlColor = '#ff9900' WHERE DispatchStatusId = 0
IF EXISTS(SELECT * FROM DispatchStatuses WHERE HexColor != '#2850ab' AND DispatchStatusId = 1)
                 UPDATE DispatchStatuses SET HexColor  = '#2850ab', HtmlColor = '#2850ab' WHERE DispatchStatusId = 1
IF EXISTS(SELECT * FROM DispatchStatuses WHERE HexColor != '#90ee90' AND DispatchStatusId = 2)
                 UPDATE DispatchStatuses SET HexColor  = '#90ee90', HtmlColor = '#90ee90' WHERE DispatchStatusId = 2
IF EXISTS(SELECT * FROM DispatchStatuses WHERE HexColor != '#008000' AND DispatchStatusId = 3)
                 UPDATE DispatchStatuses SET HexColor  = '#008000', HtmlColor = '#008000' WHERE DispatchStatusId = 3
IF EXISTS(SELECT * FROM DispatchStatuses WHERE HexColor != '#3294f5' AND DispatchStatusId = 4)
                 UPDATE DispatchStatuses SET HexColor  = '#3294f5', HtmlColor = '#3294f5' WHERE DispatchStatusId = 4
IF EXISTS(SELECT * FROM DispatchStatuses WHERE HexColor != '#d700c9' AND DispatchStatusId = 7)
                 UPDATE DispatchStatuses SET HexColor  = '#d700c9', HtmlColor = '#d700c9' WHERE DispatchStatusId = 7
IF EXISTS(SELECT * FROM DispatchStatuses WHERE HexColor != '#eaeaea' AND DispatchStatusId = 5)
                 UPDATE DispatchStatuses SET HexColor  = '#eaeaea', HtmlColor = '#eaeaea' WHERE DispatchStatusId = 5
IF EXISTS(SELECT * FROM DispatchStatuses WHERE HexColor != '#808080' AND DispatchStatusId = 255)
                 UPDATE DispatchStatuses SET HexColor  = '#808080', HtmlColor = '#808080' WHERE DispatchStatusId = 255

IF NOT EXISTS (SELECT 1 FROM sys.schemas WHERE name = 'Agent')
BEGIN
    EXEC( 'CREATE SCHEMA Agent' );
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='AgentSessions' AND TABLE_SCHEMA='Agent')
BEGIN
    CREATE TABLE Agent.AgentSessions (
        AgentSessionId int IDENTITY(1,1) NOT NULL,
        CompanyId int NOT NULL,
        OwnerUserId int NOT NULL,
        CreateDate datetime NOT NULL default(getdate()),
        IsActive bit NOT NULL default(1),

        CONSTRAINT PK_Agent_AgentSesions_ PRIMARY KEY CLUSTERED (AgentSessionId ASC),
        CONSTRAINT FK_Agent_AgentSessions_CompanyId FOREIGN KEY (CompanyId) REFERENCES dbo.Companies (CompanyId)
    )
END


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AgentSessions' AND TABLE_SCHEMA='Agent' AND COLUMN_NAME = 'QuickBooksFilename')
    ALTER TABLE Agent.AgentSessions
        ADD QuickBooksFilename varchar(512) NULL
GO




IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='AgentSessionCompanies' AND TABLE_SCHEMA='Agent')
BEGIN
    CREATE TABLE Agent.AgentSessionCompanies (
        AgentSessionId int NOT NULL,
        CompanyId int NOT NULL,

        CONSTRAINT PK_Agent_AgentSesionCompanies_AgentSessions FOREIGN KEY (AgentSessionId) REFERENCES Agent.AgentSessions (AgentSessionId),
        CONSTRAINT FK_Agent_AgentSessionCompanes_CompanyId FOREIGN KEY (CompanyId) REFERENCES dbo.Companies (CompanyId)
    )
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='QBAccounts' AND TABLE_SCHEMA='Agent')
BEGIN
    CREATE TABLE Agent.QBAccounts (
        AccountId bigint IDENTITY(1,1) NOT NULL,
        AgentSessionId int NOT NULL,
        ObjectId varchar(50),
        Name varchar(31),
        FullName varchar(50),
        AccountNumber varchar(7),
        Type int NOT NULL,

        CONSTRAINT PK_Agent_QBAccounts_ PRIMARY KEY CLUSTERED (AccountId ASC),
        CONSTRAINT FK_Agent_QBAccounts_Sessions FOREIGN KEY (AgentSessionId) REFERENCES Agent.AgentSessions (AgentSessionId)
    )
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='QBClasses' AND TABLE_SCHEMA='Agent')
BEGIN
    CREATE TABLE Agent.QBClasses (
        ClassId int IDENTITY(1,1) NOT NULL,
        AgentSessionId int NOT NULL,
        ObjectId varchar(36) NOT NULL,
        Name varchar(31) NULL,
        IsActive bit NOT NULL,

        CONSTRAINT PK_Agent_Classes PRIMARY KEY CLUSTERED ( ClassId ASC),
        CONSTRAINT FK_Agent_QBClasses_Sessions FOREIGN KEY (AgentSessionId) REFERENCES Agent.AgentSessions (AgentSessionId)
    )
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='QBCustomers' AND TABLE_SCHEMA='Agent')
BEGIN
    CREATE TABLE Agent.QBCustomers (
        CustomerId int IDENTITY(1,1) NOT NULL,
        AgentSessionId int NOT NULL,
        ObjectId varchar(36) NOT NULL,
        EditSequence varchar(16) NOT NULL,
        Name varchar(41) NOT NULL,
        Company varchar(41),
        Notes varchar(4000),
        Fax varchar(31),
        Phone varchar(21),
        Email varchar(1023),
        LastName varchar(25),
        FirstName varchar(25),
        AccountNumber varchar(99),

        CONSTRAINT PK_Agent_Customers PRIMARY KEY CLUSTERED ( CustomerId ASC),
        CONSTRAINT FK_Agent_QBCustomers_Sessions FOREIGN KEY (AgentSessionId) REFERENCES Agent.AgentSessions (AgentSessionId)
    )
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='QBInvoices' AND TABLE_SCHEMA='Agent')
BEGIN
    CREATE TABLE Agent.QBInvoices (
        InvoiceId int IDENTITY(1,1) NOT NULL,
        AgentSessionId int NOT NULL,
        ObjectId varchar(36) NOT NULL,
        EditSequence varchar(16) NOT NULL,
        TimeModified datetime,
        TimeCreated datetime,
        TxnDate datetime,
        RefNumber varchar(11),
        PoNumber varchar(13),
        TxnNumber int NOT NULL,
        ClassName varchar(41),
        CustomerId varchar(36) NULL,
        CustomerName varchar(41) NOT NULL,
        ArAccountId varchar(36) NULL,
        ArAccountName varchar(41) NOT NULL,
        TermsRefId varchar(36) NULL,
        TermsRefName varchar(41) NULL,
        Subtotal decimal(9,2) NOT NULL,
        SalesTaxPercentage decimal(9,2) NOT NULL,
        ItemSalesTaxRefId varchar(36) NULL,
        ItemSalesTaxRefName varchar(41) NULL,
        SalesTaxTotal decimal(9,2) NOT NULL,
        TotalAmount decimal(9,2) NOT NULL,
        BalanceRemaining decimal(9,2) NOT NULL,
        ToBeEmailed bit NOT NULL,
        BillingAddressLine1 varchar(41), 
        BillingAddressLine2 varchar(41), 
        BillingAddressLine3 varchar(41), 
        BillingAddressLine4 varchar(41), 
        BillingAddressLine5 varchar(41), 
        BillingAddressCity varchar(41), 
        BillingAddressState varchar(41), 
        BillingAddressPostalCode varchar(13), 
        BillingAddressCountry varchar(41), 
        BillingAddressNote varchar(41), 
        ShippingAddressLine1 varchar(41), 
        ShippingAddressLine2 varchar(41), 
        ShippingAddressLine3 varchar(41), 
        ShippingAddressLine4 varchar(41), 
        ShippingAddressLine5 varchar(41), 
        ShippingAddressCity varchar(41), 
        ShippingAddressState varchar(41), 
        ShippingAddressPostalCode varchar(13), 
        ShippingAddressCountry varchar(41), 
        ShippingAddressNote varchar(41), 
        BillingEmail varchar(1023),
        DiscountAmount decimal(9,2),
        DiscountRate decimal(9,2),
        SaleTaxCodeName varchar(41), 
        Memo varchar(4095),
        ShipDate datetime,
        DueDate datetime,

        CONSTRAINT PK_Agent_Invoices PRIMARY KEY CLUSTERED (InvoiceId ASC),
        CONSTRAINT FK_Agent_QBInvoices_Sessions FOREIGN KEY (AgentSessionId) REFERENCES Agent.AgentSessions (AgentSessionId)
    )
END
GO



IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='QBInvoices' AND TABLE_SCHEMA='Agent' AND COLUMN_NAME = 'ClassId')
    ALTER TABLE Agent.QBInvoices
        ADD ClassId varchar(36) NULL
GO



IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='QBInvoiceLines' AND TABLE_SCHEMA='Agent')
BEGIN
    CREATE TABLE Agent.QBInvoiceLines (
        InvoiceLineId bigint IDENTITY(1,1) NOT NULL,
        InvoiceId int NOT NULL,
        AgentSessionId int NOT NULL,
        ObjectId varchar(36) NOT NULL,
        ItemId varchar(36),
        ItemName varchar(31) NOT NULL, 
        Quantity decimal(9,2) NOT NULL DEFAULT(1),
        Amount decimal(9,2) NOT NULL,
        Rate decimal(9,2) NOT NULL,
        Description varchar(4095),
        ClassRefId varchar(32), 
        ClassRefName varchar(31),
        SalesTaxCodeRefId varchar(36),
        SalesTaxCodeRefName varchar(41),
        CONSTRAINT PK_Agent_QBInvoiceLines PRIMARY KEY CLUSTERED (InvoiceLineId ASC),
        CONSTRAINT FK_Agent_QBInvoiceLines_Sessions FOREIGN KEY (AgentSessionId) REFERENCES Agent.AgentSessions (AgentSessionId),
        CONSTRAINT FK_Agent_QBInvoiceLines_Invoices FOREIGN KEY (InvoiceId) REFERENCES Agent.QBInvoices (InvoiceId)
    )
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='QBPaymentMethods' AND TABLE_SCHEMA='Agent')
BEGIN
    CREATE TABLE Agent.QBPaymentMethods (
        PaymentMethodId int IDENTITY(1,1) NOT NULL,
        AgentSessionId int NOT NULL,
        ObjectId varchar(36) NOT NULL,
        name varchar(31) NOT NULL,
        isActive bit NOT NULL,

        CONSTRAINT PK_Agent_PaymentMethods PRIMARY KEY CLUSTERED ( PaymentMethodId ASC),
        CONSTRAINT FK_Agent_QBPaymentMethods_Sessions FOREIGN KEY (AgentSessionId) REFERENCES Agent.AgentSessions (AgentSessionId)
    )
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='QBTaxCodes' AND TABLE_SCHEMA='Agent')
BEGIN
    CREATE TABLE Agent.QBTaxCodes (
        TaxCodeId int IDENTITY(1,1) NOT NULL,
        AgentSessionId int NOT NULL,
        ObjectId varchar(36) NOT NULL,
        Name varchar(31) NULL,
        IsActive bit NOT NULL,
        IsTaxable bit NOT NULL,

        CONSTRAINT PK_Agent_TaxCodes PRIMARY KEY CLUSTERED ( TaxCodeId ASC),
        CONSTRAINT FK_Agent_QBTaxCodes_Sessions FOREIGN KEY (AgentSessionId) REFERENCES Agent.AgentSessions (AgentSessionId)
    )
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='QBTaxItems' AND TABLE_SCHEMA='Agent')
BEGIN
    CREATE TABLE Agent.QBTaxItems (
        TaxItemId int IDENTITY(1,1) NOT NULL,
        AgentSessionId int NOT NULL,
        ObjectId varchar(36) NOT NULL,
        EditSequence varchar(16) NOT NULL,
        Name varchar(31),
        TaxRate decimal(9,2) NOT NULL,
        Description varchar(4000),
        AccountName varchar(41),

        CONSTRAINT PK_Agent_TaxItems PRIMARY KEY CLUSTERED ( TaxItemId ASC),
        CONSTRAINT FK_Agent_QBTaxItems_Sessions FOREIGN KEY (AgentSessionId) REFERENCES Agent.AgentSessions (AgentSessionId)
    )
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='ClientLocks' AND TABLE_SCHEMA='Agent')
BEGIN
    CREATE TABLE Agent.ClientLocks (
        ClientLockId bigint IDENTITY(1,1) NOT NULL,
        AgentSessionId int NOT NULL,
        [Key] varchar(32) NOT NULL,
        Value varchar(32) NOT NULL,
        Code uniqueidentifier NOT NULL default(newid()),
        CreateDate datetime NOT NULL default(getutcdate()),		

        CONSTRAINT PK_DistributedLocks PRIMARY KEY CLUSTERED (ClientLockId ASC),
    )

    CREATE UNIQUE NONCLUSTERED INDEX IX_ClientLocks ON Agent.ClientLocks (AgentSessionId, [Key], Value)
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='QBItems' AND TABLE_SCHEMA='Agent')
BEGIN
    CREATE TABLE Agent.QBItems (
        ItemId int IDENTITY(1,1) NOT NULL,
        AgentSessionId int NOT NULL,
        ObjectId varchar(36) NOT NULL,
        EditSequence varchar(16) NOT NULL,
        Name varchar(31) NULL,
        Price decimal(9,2) NOT NULL,
        Description varchar(4095) NULL,
        AccountName varchar(31) NULL,
        IsActive bit NOT NULL DEFAULT(1)

        CONSTRAINT PK_Agent_QBItems PRIMARY KEY CLUSTERED ( ItemId ASC),
        CONSTRAINT FK_Agent_QBItems_Sessions FOREIGN KEY (AgentSessionId) REFERENCES Agent.AgentSessions (AgentSessionId)
    )
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='QBItems' AND TABLE_SCHEMA='Agent' AND COLUMN_NAME = 'IsActive')
    ALTER TABLE Agent.QBItems
        ADD IsActive BIT NOT NULL DEFAULT(1)
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='QBCustomers' AND TABLE_SCHEMA='Agent' AND COLUMN_NAME = 'IsActive')
    ALTER TABLE Agent.QBCustomers
        ADD IsActive BIT NOT NULL DEFAULT(1)
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='QBPayments' AND TABLE_SCHEMA='Agent')
BEGIN
    CREATE TABLE Agent.QBPayments (
        PaymentId bigint IDENTITY(1,1) NOT NULL,
        AgentSessionId int NOT NULL,
        ObjectId varchar(36),
        EditSequence varchar(16),
        RefNumber varchar(50),
        Amount decimal(9,2) NOT NULL,
        Date datetime NOT NULL default(getdate()),
        CustomerRefName varchar(50),
        PaymentMethodRefName varchar(31),
        ARAccountRefName varchar(31),
        Memo varchar(4095),
        TransactionNumber bigint

        CONSTRAINT PK_Agent_Payments PRIMARY KEY CLUSTERED(PaymentId asc),
        CONSTRAINT FK_Agent_Payments_Sessions FOREIGN KEY (AgentSessionId) REFERENCES Agent.AgentSessions (AgentSessionId)
    )
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='QBPayments' AND TABLE_SCHEMA='Agent' AND COLUMN_NAME = 'RefNumber')
    ALTER TABLE Agent.QBPayments
        ADD RefNumber varchar(50) NULL
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='QBPaymentLines' AND TABLE_SCHEMA='Agent')
BEGIN
    CREATE TABLE Agent.QBPaymentLines (
        PaymentLineId bigint IDENTITY(1,1) NOT NULL,
        PaymentId bigint,
        RefNumber varchar(50),
        ObjectId varchar(36),
        Amount decimal(9,2) NOT NULL,
        CONSTRAINT PK_Agent_PaymentLines PRIMARY KEY CLUSTERED ( PaymentLineId asc ),
        CONSTRAINT FK_Agent_PaymentLines_Payments FOREIGN KEY (PaymentId) REFERENCES Agent.QBPayments (PaymentId)
    )
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='SyncEvents' AND TABLE_SCHEMA='Agent')
BEGIN 
    CREATE TABLE Agent.SyncEvents (
        SyncEventId bigint IDENTITY(1,1) NOT NULL,
        AgentSessionId int NOT NULL,
        Type int NOT NULL,
        CreateDate datetime,
        LastUpdated datetime,
        Status int NOT NULL,
        SourceIdType int NOT NULL,
        SourceId int NOT NULL,
        ObjectId int NOT NULL,

        CONSTRAINT PK_Agent_SyncEvent PRIMARY KEY CLUSTERED ( SyncEventId asc )
    )
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='SyncEventRequestData' AND TABLE_SCHEMA='Agent')
BEGIN
    CREATE TABLE Agent.SyncEventRequestData (
        SyncRequestDataId bigint IDENTITY(1,1) NOT NULL,
        SyncEventId bigint NOT NULL,
        [Key] varchar(48) NULL,
        Data varchar(max) NULL,
        CreateDate datetime,

        CONSTRAINT PK_Agent_SyncEventRequestData PRIMARY KEY CLUSTERED ( SyncRequestDataId asc ),
        CONSTRAINT FK_Agent_SyncEventRequestData_SyncEvents FOREIGN KEY (SyncEventId) REFERENCES Agent.SyncEvents (SyncEventId)
    )
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='SyncEventResponseData' AND TABLE_SCHEMA='Agent')
BEGIN
    CREATE TABLE Agent.SyncEventResponseData (
        SyncEventResponseDataId bigint IDENTITY(1,1) NOT NULL,
        SyncEventId bigint NOT NULL,
        [Key] varchar(48) NULL,
        Data varchar(max) NULL,
        CreateDate datetime,
        CONSTRAINT PK_Agent_EventResponseData PRIMARY KEY CLUSTERED (SyncEventResponseDataId asc),
        CONSTRAINT FK_Agent_SyncEventResponseData_SyncEvents FOREIGN KEY (SyncEventId) REFERENCES Agent.SyncEvents (SyncEventId)    
    )
END
GO


/*************************************************************************************************
 ** Grid layout (w2ui)
 *************************************************************************************************/

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='Grids')
    CREATE TABLE dbo.Grids (
        GridId			int IDENTITY(1,1) NOT NULL,
        Url				varchar(1000), /* pathname of page that hosts the grid */
        Name			varchar(100), /* used for multiple grids on one page */
        Description		varchar(1000), /* internal description only */
        CreateDate		datetime DEFAULT(getdate()) NOT NULL

        CONSTRAINT PK_Grids PRIMARY KEY CLUSTERED (GridId ASC)
    )
GO

GRANT SELECT ON dbo.Grids TO PUBLIC
GO

IF (SELECT COUNT(1) FROM dbo.Grids) = 0 
BEGIN
    SET IDENTITY_INSERT dbo.Grids ON
    INSERT INTO Grids (GridId, Url, Name, Description) VALUES
        (1, '/impounds/', NULL, 'Impound list')
    INSERT INTO Grids (GridId, Url, Name, Description) VALUES
        (2, '/impounds/', 'Impound Grid', 'User defined columns')
    SET IDENTITY_INSERT dbo.Grids OFF
END
GO

IF (SELECT COUNT(1) FROM dbo.Grids) = 0 
BEGIN
    SET IDENTITY_INSERT dbo.Grids ON
    INSERT INTO Grids (GridId, Url, Name, Description) VALUES
        (1, '/impounds/', NULL, 'Impound list')
    SET IDENTITY_INSERT dbo.Grids OFF
END
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='GridLayouts')
    CREATE TABLE dbo.GridLayouts (
        Id					int IDENTITY(1,1) NOT NULL,	
        GridId				int NOT NULL,
        CompanyId			int NOT NULL,
        UserId				int,
        TabName				varchar(100),
        Columns				varchar(max),
        FilterJson			varchar(max),
        CreateDate			datetime DEFAULT(getdate()) NOT NULL,
        Deleted				bit DEFAULT(0) NOT NULL

        CONSTRAINT PK_GridLayout PRIMARY KEY CLUSTERED ( Id ASC),
        CONSTRAINT FK_GridLayout_GridId FOREIGN KEY(GridId) REFERENCES dbo.Grids (GridId),
        CONSTRAINT FK_GridLayout_CompanyId FOREIGN KEY(CompanyId) REFERENCES dbo.Companies (CompanyId),
        CONSTRAINT FK_GridLayout_OwnerUserId FOREIGN KEY(UserId) REFERENCES dbo.Users (UserId)
    )
GO

GRANT SELECT ON GridLayouts TO public
GRANT INSERT ON GridLayouts TO public
GRANT UPDATE ON GridLayouts TO public


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='GridLayoutDetailTypes')
    CREATE TABLE dbo.GridLayoutDetailTypes (
        [GridLayoutDetailTypeId] int IDENTITY(1,1) NOT NULL,
        [Name] varchar(50), 
        [Description] varchar(500)

        CONSTRAINT PK_GridLayoutDetailTypeId PRIMARY KEY CLUSTERED ( [GridLayoutDetailTypeId] ASC),
    )
GO

IF (SELECT COUNT(1) FROM dbo.GridLayoutDetailTypes) = 0 
BEGIN
    SET IDENTITY_INSERT dbo.GridLayoutDetailTypes ON
    INSERT INTO GridLayoutDetailTypes (GridLayoutDetailTypeId, Name, Description) VALUES
        (1, 'RawJson', 'raw JSON data')
    SET IDENTITY_INSERT dbo.GridLayoutDetailTypes OFF
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='GridLayoutDetails')
    CREATE TABLE dbo.GridLayoutDetails (
        GridLayoutDetailId		int IDENTITY(1,1) NOT NULL,	
        GridLayoutId			int NOT NULL,
        GridLayoutDetailTypeId	int NOT NULL,
        OwnerUserId				int,
        LastSaveDate			datetime,
        Data				    varchar(7500) 

        CONSTRAINT FK_GridLayoutItemDetails_GridLayoutId FOREIGN KEY(GridLayoutId) REFERENCES dbo.GridLayouts (Id),
        CONSTRAINT FK_GridLayoutItemDetails_GridLayoutDetailTypeId FOREIGN KEY (GridLayoutDetailTypeId) REFERENCES dbo.GridLayoutDetailTypes (GridLayoutDetailTypeId),
        CONSTRAINT FK_GridLayoutItemDetails_GridLayoutDetailOwnerUserId FOREIGN KEY (OwnerUserId) REFERENCES dbo.Users (UserId)
    )
GO

ALTER VIEW [dbo].[vwAccounts]
AS
    SELECT     
        A.AccountId, A.CompanyId, ReferenceNumber, Company, FullName, Address, City, State, Zip, Country, Email, Website, Notes, TypeId, InvoiceTermId, CreditLimit,     
        InvoiceDeliveryFax, InvoiceDeliveryMail, InvoiceDeliveryEmail, StatementEndDate, Phone, Fax, DiscountRate, Deleted, CreateDate,    
        null AS LastActivity, TaxExempt, ParentAccountId, Billable, ImpoundDestinationTypeId,    
        ImpoundDestinationStorageLotId,    
        Status,     
        (select STRING_AGG(  AccountTagId, ',') from (
            select soi.AccountTagId    
            from vwAccountTags soi WITH (NOLOCK)     
            where soi.AccountId = A.Accountid) r) as TagsSummary,    
        AB.Balance,    
        AB.CreditLimitUtilized,    
        A.CreditHold,    
        StatementPaymentsCount=0,    
            (
	        select STRING_AGG(CompanyId,',') from(
	        SELECT ca.CompanyId    
            from CompanyAccounts ca WITH (NOLOCK)     
            where ca.AccountId = A.AccountId    
            UNION ALL
            select sharedcompanyid as CompanyId FROM CompaniesShared CS WITH (NOLOCK) 
	        WHERE CS.CompanyId=A.CompanyId AND CS.ShareAllAccounts=1)r)as Companies,     
        MasterAccountId,    
        DefaultPriority,    
        DefaultPO,    
        DefaultStorageRateItemId,
        Latitude,
        Longitude    

        FROM         dbo.Accounts A WITH (NOLOCK)    
            LEFT OUTER JOIN dbo.AccountBalances AB WITH (NOLOCK) ON AB.AccountId=A.AccountId AND AB.CompanyId=A.CompanyId     
            WHERE A.Deleted=0 
GO
-- Extended Properties

IF NOT EXISTS (SELECT * FROM SYS.EXTENDED_PROPERTIES WHERE 
    [major_id] = OBJECT_ID('dbo.vwAccounts') AND [name] = N'MS_DiagramPane1' AND [minor_id] = 0)
EXEC sp_addextendedproperty N'MS_DiagramPane1', N'[0E232FF0-B466-11cf-A24F-00AA00A3EFFF, 1.00]
Begin DesignProperties = 
   Begin PaneConfigurations = 
      Begin PaneConfiguration = 0
         NumPanes = 4
         Configuration = "(H (1[40] 4[20] 2[20] 3) )"
      End
      Begin PaneConfiguration = 1
         NumPanes = 3
         Configuration = "(H (1 [50] 4 [25] 3))"
      End
      Begin PaneConfiguration = 2
         NumPanes = 3
         Configuration = "(H (1 [50] 2 [25] 3))"
      End
      Begin PaneConfiguration = 3
         NumPanes = 3
         Configuration = "(H (4 [30] 2 [40] 3))"
      End
      Begin PaneConfiguration = 4
         NumPanes = 2
         Configuration = "(H (1 [56] 3))"
      End
      Begin PaneConfiguration = 5
         NumPanes = 2
         Configuration = "(H (2 [66] 3))"
      End
      Begin PaneConfiguration = 6
         NumPanes = 2
         Configuration = "(H (4 [50] 3))"
      End
      Begin PaneConfiguration = 7
         NumPanes = 1
         Configuration = "(V (3))"
      End
      Begin PaneConfiguration = 8
         NumPanes = 3
         Configuration = "(H (1[56] 4[18] 2) )"
      End
      Begin PaneConfiguration = 9
         NumPanes = 2
         Configuration = "(H (1 [75] 4))"
      End
      Begin PaneConfiguration = 10
         NumPanes = 2
         Configuration = "(H (1[66] 2) )"
      End
      Begin PaneConfiguration = 11
         NumPanes = 2
         Configuration = "(H (4 [60] 2))"
      End
      Begin PaneConfiguration = 12
         NumPanes = 1
         Configuration = "(H (1) )"
      End
      Begin PaneConfiguration = 13
         NumPanes = 1
         Configuration = "(V (4))"
      End
      Begin PaneConfiguration = 14
         NumPanes = 1
         Configuration = "(V (2))"
      End
      ActivePaneConfig = 0
   End
   Begin DiagramPane = 
      Begin Origin = 
         Top = 0
         Left = 0
      End
      Begin Tables = 
         Begin Table = "Accounts"
            Begin Extent = 
               Top = 6
               Left = 38
               Bottom = 386
               Right = 216
            End
            DisplayFlags = 280
            TopColumn = 8
         End
      End
   End
   Begin SQLPane = 
   End
   Begin DataPane = 
      Begin ParameterDefaults = ""
      End
      Begin ColumnWidths = 26
         Width = 284
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
      End
   End
   Begin CriteriaPane = 
      Begin ColumnWidths = 11
         Column = 1440
         Alias = 900
         Table = 1170
         Output = 720
         Append = 1400
         NewValue = 1170
         SortType = 1350
         SortOrder = 1410
         GroupBy = 1350
         Filter = 1350
         Or = 1350
         Or = 1350
         Or = 1350
      End
   End
End
', 'SCHEMA', N'dbo', 'VIEW', N'vwAccounts', NULL, NULL
GO
DECLARE @xp int
SELECT @xp=1
IF NOT EXISTS (SELECT * FROM SYS.EXTENDED_PROPERTIES WHERE
        [major_id] = OBJECT_ID('dbo.vwAccounts') AND [name] = N'MS_DiagramPaneCount' AND [minor_id] = 0)
EXEC sp_addextendedproperty N'MS_DiagramPaneCount', @xp, 'SCHEMA', N'dbo', 'VIEW', N'vwAccounts', NULL, NULL
GO

ALTER VIEW [dbo].[vwDispatchEntries]
AS
SELECT     D.DispatchEntryId, CallNumber, D.CompanyId, D.AccountId, Type, DispatchReasonId, ReceiveTime, DispatchTime, EnRouteTime, ArrivalTime, TowTime, DestinationArrivalTime,CompletionTime, TowSource, TowDestination, MilesSource, MilesDestination, DriverId, TruckId, Notes, VehicleVIN, VehicleBodyTypeId, VehicleColorId, 
                      VehicleManufacturerId, VehicleModelId, VehicleYear, VehicleLicenseNumber, VehicleLicenseState, VehicleLicenseYear, coalesce(VehicleDrivable, 0) as VehicleDrivable, 
                      VehicleOdometer, Priority, Status, D.OwnerUserId, D.CreateDate, Created, Impound, ArrivalETA,
                        (SELECT SUM(PaymentsTotal) FROM dbo.Invoices inv WITH (NOLOCK) WHERE inv.DispatchEntryId=D.DispatchEntryId) AS PaymentsApplied, 

							Version, COALESCE(CAST(CASE WHEN (i.ReleaseDate IS NOT NULL) THEN 1 ELSE 0 END AS bit), 0) as Released,
(SELECT TOP 1 Reason FROM dbo.DispatchEntryCancellations WITH (NOLOCK) WHERE (D.DispatchEntryId=dbo.DispatchEntryCancellations.DispatchEntryId AND Deleted=0)) /* null */ AS CancellationReason,

							InvoiceStatusId,
							InvoiceNumber,
							PurchaseOrderNumber,
							/*nullif(replace(replace(cast((
select cast(soi.StatementId as varchar) + ', ' as [text()]
from StatementDispatchEntries soi WITH (NOLOCK) 
	inner join statements S on s.statementid=soi.statementid and s.deleted=0
where soi.DispatchEntryId =D.DispatchEntryId
for xml path( '' ), root( 'X' ), type
) as varchar(max)), '<X>', ''),'</X>',''), '') */ null as Statements,
DispatchEntryLockId
FROM         dbo.DispatchEntries D
	LEFT OUTER JOIN Impounds I WITH (NOLOCK) on I.DispatchEntryId=D.DispatchEntryId AND D.Impound=1 and I.Deleted=0

    WHERE D.Deleted=0
GO

IF NOT EXISTS (SELECT * FROM SYS.EXTENDED_PROPERTIES WHERE 
    [major_id] = OBJECT_ID('dbo.vwDispatchEntries') AND [name] = N'MS_DiagramPane1' AND [minor_id] = 0)
EXEC sp_addextendedproperty N'MS_DiagramPane1', N'[0E232FF0-B466-11cf-A24F-00AA00A3EFFF, 1.00]
Begin DesignProperties = 
   Begin PaneConfigurations = 
      Begin PaneConfiguration = 0
         NumPanes = 4
         Configuration = "(H (1[40] 4[20] 2[20] 3) )"
      End
      Begin PaneConfiguration = 1
         NumPanes = 3
         Configuration = "(H (1 [50] 4 [25] 3))"
      End
      Begin PaneConfiguration = 2
         NumPanes = 3
         Configuration = "(H (1 [50] 2 [25] 3))"
      End
      Begin PaneConfiguration = 3
         NumPanes = 3
         Configuration = "(H (4 [30] 2 [40] 3))"
      End
      Begin PaneConfiguration = 4
         NumPanes = 2
         Configuration = "(H (1 [56] 3))"
      End
      Begin PaneConfiguration = 5
         NumPanes = 2
         Configuration = "(H (2 [66] 3))"
      End
      Begin PaneConfiguration = 6
         NumPanes = 2
         Configuration = "(H (4 [50] 3))"
      End
      Begin PaneConfiguration = 7
         NumPanes = 1
         Configuration = "(V (3))"
      End
      Begin PaneConfiguration = 8
         NumPanes = 3
         Configuration = "(H (1[56] 4[18] 2) )"
      End
      Begin PaneConfiguration = 9
         NumPanes = 2
         Configuration = "(H (1 [75] 4))"
      End
      Begin PaneConfiguration = 10
         NumPanes = 2
         Configuration = "(H (1[66] 2) )"
      End
      Begin PaneConfiguration = 11
         NumPanes = 2
         Configuration = "(H (4 [60] 2))"
      End
      Begin PaneConfiguration = 12
         NumPanes = 1
         Configuration = "(H (1) )"
      End
      Begin PaneConfiguration = 13
         NumPanes = 1
         Configuration = "(V (4))"
      End
      Begin PaneConfiguration = 14
         NumPanes = 1
         Configuration = "(V (2))"
      End
      ActivePaneConfig = 0
   End
   Begin DiagramPane = 
      Begin Origin = 
         Top = 0
         Left = 0
      End
      Begin Tables = 
         Begin Table = "DispatchEntries"
            Begin Extent = 
               Top = 6
               Left = 38
               Bottom = 114
               Right = 226
            End
            DisplayFlags = 280
            TopColumn = 33
         End
      End
   End
   Begin SQLPane = 
   End
   Begin DataPane = 
      Begin ParameterDefaults = ""
      End
      Begin ColumnWidths = 39
         Width = 284
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
      End
   End
   Begin CriteriaPane = 
      Begin ColumnWidths = 11
         Column = 1440
         Alias = 900
         Table = 1170
         Output = 720
         Append = 1400
         NewValue = 1170
         SortType = 1350
         SortOrder = 1410
         GroupBy = 1350
         Filter = 1350
         Or = 1350
         Or = 1350
         Or = 1350
      End
   End
End
', 'SCHEMA', N'dbo', 'VIEW', N'vwDispatchEntries', NULL, NULL
GO
DECLARE @xp int
SELECT @xp=1
IF NOT EXISTS (SELECT * FROM SYS.EXTENDED_PROPERTIES WHERE
        [major_id] = OBJECT_ID('dbo.vwDispatchEntries') AND [name] = N'MS_DiagramPaneCount' AND [minor_id] = 0)
EXEC sp_addextendedproperty N'MS_DiagramPaneCount', @xp, 'SCHEMA', N'dbo', 'VIEW', N'vwDispatchEntries', NULL, NULL
GO


IF OBJECT_ID('dbo.[vwInvoices]') IS NULL EXEC ('CREATE VIEW dbo.[vwInvoices] AS SELECT 1 as Temp')
GO
        
ALTER VIEW [dbo].[vwInvoices]
AS 
SELECT        InvoiceId, CompanyId, CreateDate, DispatchEntryId, ImpoundId, SubTotal, Tax, CASE WHEN (I.PaymentsTotal IS NULL OR
            I.PaymentsTotal = 0) THEN
                (SELECT        COALESCE (SUM(Amount), 0)
                FROM            DispatchEntryPayments
                WHERE        InvoiceId = I.InvoiceId AND IsVoid = 0) ELSE I.PaymentsTotal END AS PaymentsTotal, 
                AccountId, 
                IsTaxExempt, 
                HiddenTotal, 
                TaxableTotal, 
                TicketValue,
                NextScheduledRecalculate, 
                LastUpdated
FROM            dbo.Invoices AS I
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='ImpoundNotes' AND COLUMN_NAME = 'IsDeleted')
    ALTER TABLE ImpoundNotes ADD IsDeleted BIT DEFAULT(0) NOT NULL
GO


IF NOT EXISTS (SELECT 1 FROM sys.schemas WHERE name = 'InternalVoice')
BEGIN
    EXEC( 'CREATE SCHEMA InternalVoice' );
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='PhoneCalls' AND TABLE_SCHEMA='InternalVoice')
BEGIN
    CREATE TABLE InternalVoice.PhoneCalls(
        PhoneCallId int IDENTITY(1,1) NOT NULL,	
        UniversalId uniqueidentifier,
        Direction int, 
        CallerNumber varchar(40),
        CallerName varchar(50),
        ForwardedFrom varchar(40),
        DestinationNumber varchar(40),
        StartTime datetime,
        EndTime datetime,
        Duration float,
        BillingDuration int,
        BillingRate smallmoney,
        CompanyId int,
        CompanyUserId int,
        AnsweredByNumber varchar(40),
        AnsweredByUserId int,
        CreateDate datetime NOT NULL default(getdate()),
    
        CONSTRAINT PK_InternalVoice_PhoneCalls PRIMARY KEY CLUSTERED ( PhoneCallId ASC)
    )
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Users' AND COLUMN_NAME = 'LastLoginDateAndroid')
    ALTER TABLE Users ADD LastLoginDateAndroid datetime
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Users' AND COLUMN_NAME = 'LastLoginDateIOS')
    ALTER TABLE Users ADD LastLoginDateIOS datetime
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Users' AND COLUMN_NAME = 'OfficePhone')
    ALTER TABLE Users ADD OfficePhone varchar(32),
		MobilePhone varchar(32),
		MobilePhoneConfirmed datetime
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Users' AND COLUMN_NAME = 'RequireGPSCheckin')
    ALTER TABLE Users ADD RequireGPSCheckin bit DEFAULT(1) NOT NULL
GO

IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Users')
    ALTER TABLE Users ALTER COLUMN [Username] varchar(100) NOT NULL
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Users' AND COLUMN_NAME = 'BirthDate')
    ALTER TABLE Users ADD BirthDate DATE NULL
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Users' AND COLUMN_NAME = 'EmergencyContactName')
    ALTER TABLE Users ADD EmergencyContactName [varchar](100) NULL

GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Users' AND COLUMN_NAME = 'EmergencyContactPhone')
    ALTER TABLE Users ADD EmergencyContactPhone [varchar](32) NULL	

GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Users' AND COLUMN_NAME = 'HasProfilePhoto')
    ALTER TABLE Users ADD HasProfilePhoto BIT NULL
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Drivers' AND COLUMN_NAME = 'EmergencyContactName')
    ALTER TABLE Drivers ADD EmergencyContactName [varchar](100) NULL

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Drivers' AND COLUMN_NAME = 'EmergencyContactPhone')
    ALTER TABLE Drivers ADD EmergencyContactPhone [varchar](32) NULL

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Drivers' AND COLUMN_NAME = 'MedicalExamExpirationDate')
	ALTER TABLE Drivers ADD MedicalExamExpirationDate DATE NULL	
	

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountBalances' AND COLUMN_NAME = 'IsLocked')
    ALTER TABLE AccountBalances ADD IsLocked bit DEFAULT(0) NOT NULL
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='AccountBalanceUpdateQueue')
BEGIN
    CREATE TABLE dbo.AccountBalanceUpdateQueue (
        BalanceUpdateId int IDENTITY(1,1) NOT NULL,	
    
        InvoiceId int,
        OldAccountId int, 
        NewAccountId int,
        CompanyId int,
        BalanceDue money,
        OldBalanceDue money,

        CreateDate datetime NOT NULL default(getdate()),
    
        CONSTRAINT PK_AccountBalanceUdpateQueue PRIMARY KEY CLUSTERED ( BalanceUpdateId ASC)
    )
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountBalances' AND COLUMN_NAME = 'CreditLimitUtilized')
    ALTER TABLE AccountBalances ADD CreditLimitUtilized decimal(15,1) DEFAULT(0) NOT NULL
GO



IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='GeotabVehicleDispatchUpdateQueue')
BEGIN
    CREATE TABLE dbo.GeotabVehicleDispatchUpdateQueue (
        UpdateQueueItemId int IDENTITY(1,1) NOT NULL,	
        CompanyId int,
        MessageId varchar(50),
        DestinationId varchar(50),
        TruckId int,
        OwnerUserId int,
        DispatchEntryId int,
        StartTime datetime,
        FinishTime datetime,
        CreateDate datetime NOT NULL default(getdate()),	
        IsDone bit DEFAULT (0)
        CONSTRAINT PK_GeotabVehicleDispatchUpdateQueue PRIMARY KEY CLUSTERED ( UpdateQueueItemId ASC)
    )
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Companies' AND COLUMN_NAME = 'Latitude')
    ALTER TABLE Companies ADD Latitude decimal(9,6) DEFAULT(0) NOT NULL
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Companies' AND COLUMN_NAME = 'Longitude')
    ALTER TABLE Companies ADD Longitude decimal(9,6) DEFAULT(0) NOT NULL
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='ImpoundLots' AND COLUMN_NAME = 'Latitude')
    ALTER TABLE ImpoundLots ADD Latitude decimal(9,6) DEFAULT(0) NOT NULL
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='ImpoundLots' AND COLUMN_NAME = 'Longitude')
    ALTER TABLE ImpoundLots ADD Longitude decimal(9,6) DEFAULT(0) NOT NULL
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Impounds' AND COLUMN_NAME = 'Auction')
    ALTER TABLE Impounds ADD Auction bit DEFAULT(0) NOT NULL
GO

/*************************************************************************************************
 ** Security Questions
 *************************************************************************************************/

 IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='SecurityQuestions')
    CREATE TABLE [dbo].[SecurityQuestions] (
        SecurityQuestionId int IDENTITY(1,1) NOT NULL,
        Question varchar(500) NOT NULL,
        IsDeleted bit NOT NULL,
        CompanyId int NULL,
        
        CONSTRAINT [PK_SecurityQuestions] PRIMARY KEY CLUSTERED (SecurityQuestionId ASC),
        CONSTRAINT [FK_SecurityQuestions_Companies] FOREIGN KEY(CompanyId) REFERENCES dbo.Companies (CompanyId)
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='UserSecurityQuestions')
    CREATE TABLE [dbo].[UserSecurityQuestions] (
        UserSecurityQuestionId int IDENTITY(1,1) NOT NULL,
        UserId int NOT NULL,
        SecurityQuestionId int NOT NULL,
        Answer varchar(500) NOT NULL,
        CreateDate datetime NOT NULL default(getdate()),
        LastAttempt datetime NULL,

        CONSTRAINT [PK_UserSecurityQuestions] PRIMARY KEY CLUSTERED (UserSecurityQuestionId ASC),
        CONSTRAINT [FK_UserSecurityQuestions_SecurityQuestions] FOREIGN KEY(SecurityQuestionId) REFERENCES dbo.SecurityQuestions (SecurityQuestionId),
        CONSTRAINT [FK_UserSecurityQuestions_Users] FOREIGN KEY(UserId) REFERENCES dbo.Users (UserId)
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='UsersPasswordResetTokens')
    CREATE TABLE [dbo].[UsersPasswordResetTokens] (
        UserId int NOT NULL,
        Token uniqueidentifier NOT NULL,
        
        CONSTRAINT [FK_UsersPasswordResetTokens_Users] FOREIGN KEY(UserId) REFERENCES dbo.Users (UserId)
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='CommissionRateItems' AND COLUMN_NAME = 'CompanyId')
    ALTER TABLE [dbo].[CommissionRateItems]
        ADD CompanyId int null
        CONSTRAINT FK_CommissionRateItems_CompanyId FOREIGN KEY(CompanyId) REFERENCES dbo.Companies (CompanyId)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='CommissionRateItemBodyTypes' AND COLUMN_NAME = 'CompanyId')
    ALTER TABLE [dbo].[CommissionRateItemBodyTypes]
        ADD CompanyId int null
        CONSTRAINT FK_CommissionRateItemBodyTypes_CompanyId FOREIGN KEY(CompanyId) REFERENCES dbo.Companies (CompanyId)
GO


/*************************************************************************************************
 ** Pre-Trip Inspections
 *************************************************************************************************/

--drop table PreTripInspectionPhotos
--drop table PreTripInspectionItems
--drop table PreTripInspectionItemTypes
--drop table PreTripInspectionItemCategories
--drop table PreTripInspections

IF OBJECT_ID('dbo.PreTripInspectionItemCategories', 'U') IS NULL
    CREATE TABLE dbo.PreTripInspectionItemCategories (
        PreTripInspectionItemCategoryId INT IDENTITY (1, 1) NOT NULL,
        Name                            VARCHAR(50) NOT NULL,

        CONSTRAINT PK_PreTripInspectionItemCategories PRIMARY KEY CLUSTERED (PreTripInspectionItemCategoryId)
    )
GO

/* Use companyId of NULL to indicate 'for everyone'.  Otherwise a custom category can be assigned to a companyId. */
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='PreTripInspectionItemCategories' AND COLUMN_NAME = 'CompanyId')
    ALTER TABLE [dbo].[PreTripInspectionItemCategories]
        ADD CompanyId int null
        CONSTRAINT FK_PreTripInspectionItemCategories_CompanyId FOREIGN KEY(CompanyId) REFERENCES dbo.Companies (CompanyId)
GO

/* setup default categories for all companies */
BEGIN
SET IDENTITY_INSERT PreTripInspectionItemCategories ON

IF NOT EXISTS (SELECT * FROM PreTripInspectionItemCategories WHERE PreTripInspectionItemCategoryId = 1)
    BEGIN INSERT INTO PreTripInspectionItemCategories (PreTripInspectionItemCategoryId, NAME) values (1, 'Underhood/Engine Compartment') END

IF NOT EXISTS (SELECT * FROM PreTripInspectionItemCategories WHERE PreTripInspectionItemCategoryId = 2)
    BEGIN INSERT INTO PreTripInspectionItemCategories (PreTripInspectionItemCategoryId, NAME) values (2, 'Interior/Cab') END

IF NOT EXISTS (SELECT * FROM PreTripInspectionItemCategories WHERE PreTripInspectionItemCategoryId = 3)
    BEGIN INSERT INTO PreTripInspectionItemCategories (PreTripInspectionItemCategoryId, NAME) values (3, 'Exterior') END

IF NOT EXISTS (SELECT * FROM PreTripInspectionItemCategories WHERE PreTripInspectionItemCategoryId = 4)
    BEGIN INSERT INTO PreTripInspectionItemCategories (PreTripInspectionItemCategoryId, NAME) values (4, 'Lift/Boom Equipment') END

IF NOT EXISTS (SELECT * FROM PreTripInspectionItemCategories WHERE PreTripInspectionItemCategoryId = 5)
    BEGIN INSERT INTO PreTripInspectionItemCategories (PreTripInspectionItemCategoryId, NAME) values (5, 'Safety Equipment') END

IF NOT EXISTS (SELECT * FROM PreTripInspectionItemCategories WHERE PreTripInspectionItemCategoryId = 6)
    BEGIN INSERT INTO PreTripInspectionItemCategories (PreTripInspectionItemCategoryId, NAME) values (6, 'Other') END

SET IDENTITY_INSERT PreTripInspectionItemCategories OFF
END
GO


IF OBJECT_ID('dbo.PreTripInspectionItemTypes', 'U') IS NULL
    CREATE TABLE dbo.PreTripInspectionItemTypes (
        PreTripInspectionItemTypeId     INT IDENTITY (1, 1) NOT NULL,
        CategoryId                      INT NOT NULL,
        Name                            VARCHAR(50) NOT NULL,

        CONSTRAINT PK_PreTripInspectionItemTypes PRIMARY KEY CLUSTERED (PreTripInspectionItemTypeId),
        CONSTRAINT FK_PreTripInspectionItemTypes_PreTripInspectionItemCategories FOREIGN KEY (CategoryId) 
            REFERENCES dbo.PreTripInspectionItemCategories (PreTripInspectionItemCategoryId)
    )
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='PreTripInspectionItemTypes' AND COLUMN_NAME = 'CompanyId')
    ALTER TABLE [dbo].[PreTripInspectionItemTypes]
        ADD CompanyId int 
        CONSTRAINT FK_PreTripInspectionItemTypes_CompanyId FOREIGN KEY(CompanyId) REFERENCES dbo.Companies (CompanyId)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='PreTripInspectionItemTypes' AND COLUMN_NAME = 'TruckTypeId')
    ALTER TABLE [dbo].[PreTripInspectionItemTypes]
        ADD TruckTypeId tinyint NOT NULL DEFAULT(0)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='PreTripInspectionItemTypes' AND COLUMN_NAME = 'Deleted')
    ALTER TABLE [dbo].[PreTripInspectionItemTypes]
        ADD deleted BIT NOT NULL DEFAULT(0)
GO

IF (NOT EXISTS(SELECT * FROM sys.indexes  WHERE name='IX_PreTripInspectionItemTypes_CompanyTypes'))
CREATE INDEX IX_PreTripInspectionItemTypes_CompanyTypes ON dbo.PreTripInspectionItemTypes (CompanyId, TruckTypeId) 
GO



IF OBJECT_ID('dbo.PreTripInspections', 'U') IS NULL
    CREATE TABLE dbo.PreTripInspections (
        PreTripInspectionId INT IDENTITY (1, 1) NOT NULL,
        CompanyId           INT NOT NULL,
        InspectionDate      DATETIME NOT NULL DEFAULT(getdate()),
        OwnerUserId         INT NOT NULL,
        TruckId             INT NOT NULL,
        Odometer            INT NULL,
        Notes               VARCHAR(1000) NULL,

        CONSTRAINT PK_PreTripInspections PRIMARY KEY CLUSTERED (PreTripInspectionId),
        CONSTRAINT FK_PreTripInspections_Companies FOREIGN KEY (CompanyId) 
            REFERENCES dbo.Companies (CompanyId),
        CONSTRAINT FK_PreTripInspections_Users FOREIGN KEY (OwnerUserId) 
            REFERENCES dbo.Users (UserId),
        CONSTRAINT FK_PreTripInspections_Trucks FOREIGN KEY (TruckId) 
            REFERENCES dbo.Trucks (TruckId)
    )
GO

IF OBJECT_ID('dbo.PreTripInspectionItems', 'U') IS NULL
    CREATE TABLE dbo.PreTripInspectionItems (
        PreTripInspectionItemId INT IDENTITY (1, 1) NOT NULL,
        PreTripInspectionId     INT NOT NULL,
        ItemTypeId              INT NOT NULL,
        Checked                 BIT,
        Comments                VARCHAR(200) NULL,

        CONSTRAINT PK_PreTripInspectionItems PRIMARY KEY CLUSTERED (PreTripInspectionItemId),
        CONSTRAINT FK_PreTripInspectionItems_PreTripInspections FOREIGN KEY (PreTripInspectionId) 
            REFERENCES dbo.PreTripInspections (PreTripInspectionId),
        CONSTRAINT FK_PreTripInspectionItems_PreTripInspectionItemTypes FOREIGN KEY (ItemTypeId) 
            REFERENCES dbo.PreTripInspectionItemTypes (PreTripInspectionItemTypeId)
    )
GO

IF OBJECT_ID('dbo.PreTripInspectionPhotoTypes', 'U') IS NULL
    CREATE TABLE dbo.PreTripInspectionPhotoTypes (
        PreTripInspectionPhotoTypeId INT IDENTITY (1, 1) NOT NULL,
        Name                         VARCHAR(50) NOT NULL,

        CONSTRAINT PK_PreTripInspectionPhotoTypes PRIMARY KEY CLUSTERED (PreTripInspectionPhotoTypeId)
    )
GO

IF (SELECT COUNT(1) FROM PreTripInspectionPhotoTypes) = 0 
BEGIN
    SET IDENTITY_INSERT PreTripInspectionPhotoTypes ON
    INSERT INTO PreTripInspectionPhotoTypes (PreTripInspectionPhotoTypeId, Name) VALUES
        (1, 'Item'),
        (2, 'Odometer'),
        (3, 'Signature')
    SET IDENTITY_INSERT PreTripInspectionPhotoTypes OFF
END
GO

IF OBJECT_ID('dbo.PreTripInspectionPhotos', 'U') IS NULL
    CREATE TABLE [dbo].PreTripInspectionPhotos(
        PreTripInspectionPhotoId INT IDENTITY (1, 1) NOT NULL,
        PreTripInspectionId      INT NOT NULL,
        PhotoType                INT NOT NULL,
        ItemType                 INT NULL,
        ContentType              VARCHAR(50) NOT NULL,
        CreateDate               DATETIME DEFAULT GETDATE() NOT NULL,
        OwnerUserId              INT NULL,

        CONSTRAINT PK_PreTripInspectionPhotos PRIMARY KEY CLUSTERED (PreTripInspectionPhotoId),
        CONSTRAINT FK_PreTripInspectionPhotos_PreTripInspections FOREIGN KEY (PreTripInspectionId) 
            REFERENCES dbo.PreTripInspections (PreTripInspectionId),
        CONSTRAINT FK_PreTripInspectionPhotos_PreTripInspectionPhotoTypes FOREIGN KEY (PhotoType) 
            REFERENCES dbo.PreTripInspectionPhotoTypes (PreTripInspectionPhotoTypeId),
        CONSTRAINT FK_PreTripInspectionPhotos_PreTripInspectionItemTypes FOREIGN KEY (ItemType) 
            REFERENCES dbo.PreTripInspectionItemTypes (PreTripInspectionItemTypeId)
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='PreTripInspections' AND COLUMN_NAME = 'Latitude')
    ALTER TABLE PreTripInspections ADD 
        Latitude decimal(9,6) NOT NULL DEFAULT(0),
        Longitude decimal(9,6) NOT NULL DEFAULT(0)
GO


ALTER TABLE dbo.PreTripInspectionItems ALTER COLUMN Checked BIT NULL


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='PreTripInspectionItemTypeDetails' AND TABLE_SCHEMA='dbo')
BEGIN

CREATE TABLE dbo.PreTripInspectionItemTypeDetails (
        PreTripInspectionItemTypeDetailId	INT IDENTITY (1, 1) NOT NULL,
        CompanyId			INT NOT NULL,
        Name                varchar(200),
        CreateDate          DATETIME NOT NULL DEFAULT(GetDate()),
        OwnerUserId         INT NOT NULL,
        Deleted             BIT NOT NULL DEFAULT(0),
        DeletedDate         DATETIME NULL,
        DeletedByUserId     INT NULL

        CONSTRAINT PK_PreTripInspectionItemTypeDetails PRIMARY KEY CLUSTERED (PreTripInspectionItemTypeDetailId),
        CONSTRAINT FK_PreTripInspectionItemTypeDetails_Companies FOREIGN KEY (CompanyId) REFERENCES dbo.Companies (CompanyId),
        CONSTRAINT FK_PreTripInspectionItemTypeDetails_Users FOREIGN KEY (UserId) REFERENCES dbo.Users (UserId),
    )

    CREATE INDEX IX_PreTripInspectionItemTypeDetails_Companies ON dbo.PreTripInspectionItemTypeDetails (CompanyId)
    
    GRANT INSERT ON dbo.PreTripInspectionItemTypeDetails to public
    GRANT SELECT ON dbo.PreTripInspectionItemTypeDetails to public
	GRANT UPDATE ON dbo.PreTripInspectionItemTypeDetails to public
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='PreTripInspectionItemTypes' AND COLUMN_NAME = 'PreTripInspectionItemTypeDetailId')
    ALTER TABLE PreTripInspectionItemTypes ADD 
        PreTripInspectionItemTypeDetailId INT

    CONSTRAINT FK_PreTripInspectionItemTypes_Details FOREIGN KEY (PreTripInspectionItemTypeDetailId) REFERENCES dbo.PreTripInspectionItemTypeDetails (PreTripInspectionItemTypeDetailId)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='PreTripInspections' AND COLUMN_NAME = 'PreTripInspectionItemTypeDetailId')
    ALTER TABLE PreTripInspections ADD 
        PreTripInspectionItemTypeDetailId INT

    CONSTRAINT FK_PreTripInspections_Details FOREIGN KEY (PreTripInspectionItemTypeDetailId) REFERENCES dbo.PreTripInspectionItemTypeDetails (PreTripInspectionItemTypeDetailId)
GO

IF (NOT EXISTS(SELECT * FROM sys.indexes  WHERE name='IX_PreTripInspectionItemTypes_ItemTypeDetailId'))
    CREATE INDEX IX_PreTripInspectionItemTypes_ItemTypeDetailId ON dbo.PreTripInspectionItemTypes (PreTripInspectionItemTypeDetailId)

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='PreTripInspectionItemCategoryInclusions' AND TABLE_SCHEMA='dbo')
BEGIN

CREATE TABLE dbo.PreTripInspectionItemCategoryInclusions (
        PreTripInspectionItemCategoryInclusionId	INT IDENTITY (1, 1) NOT NULL,
        CategoryId          INT NOT NULL,
        TruckTypeId			INT

        CONSTRAINT PK_PreTripInspectionItemCategoryInclusionId PRIMARY KEY CLUSTERED (PreTripInspectionItemCategoryInclusionId),
        CONSTRAINT FK_PreTripInspectionItemCategoryInclusions_Categories FOREIGN KEY (CategoryId) REFERENCES dbo.PreTripInspectionItemCategories (PreTripInspectionItemCategoryId)
    )
    
    GRANT SELECT ON PreTripInspectionItemCategoryInclusions to public
END
GO

IF (SELECT COUNT(1) FROM PreTripInspectionItemCategories Where Name='Recovery/Deck Equipment') = 0 
BEGIN
    SET IDENTITY_INSERT PreTripInspectionItemCategories ON
    INSERT INTO PreTripInspectionItemCategories (PreTripInspectionItemCategoryId, Name) values (7, 'Recovery/Deck Equipment')
    SET IDENTITY_INSERT PreTripInspectionItemCategories OFF
END
GO

IF (SELECT COUNT(1) FROM PreTripInspectionItemCategories Where Name='Inspection Items') = 0 
BEGIN
    SET IDENTITY_INSERT PreTripInspectionItemCategories ON
    INSERT INTO PreTripInspectionItemCategories (PreTripInspectionItemCategoryId, Name) values (8, 'Inspection Items')
    SET IDENTITY_INSERT PreTripInspectionItemCategories OFF
END
GO

IF (SELECT COUNT(1) FROM PreTripInspectionItemCategoryInclusions) = 0 
BEGIN
    SET IDENTITY_INSERT PreTripInspectionItemCategoryInclusions ON
    INSERT INTO PreTripInspectionItemCategoryInclusions (PreTripInspectionItemCategoryInclusionId, CategoryId, TruckTypeId) VALUES
        -- Wrecker, truckTypeId=1
        (1, 1, 1), --Underhood/Engine Compartment
        (2, 2, 1), --Interior/Cab
        (3, 3, 1), --Exterior
        (4, 4, 1), --Lift/Boom Equipment
        (5, 5, 1), --Safety Equipment
        (6, 6, 1), --Other

        -- FlatBed, truckTypeId=2
        (7, 1, 2), --Underhood/Engine Compartment
        (8, 2, 2), --Interior/Cab
        (9, 3, 2), --Exterior
        (10, 7, 2), --Recovery/Deck Equipment
        (11, 5, 2), --Safety Equipment
        (12, 6, 2), --Other

        -- Service Vehicle
        (13, 1, 4), --Underhood/Engine Compartment
        (14, 2, 4), --Interior/Cab
        (15, 3, 4), --Exterior
        (16, 5, 4), --Safety Equipment
        (17, 6, 4), --Other

        -- Rotator
        (18, 1, 5), --Underhood/Engine Compartment
        (19, 2, 5), --Interior/Cab
        (20, 3, 5), --Exterior
        (21, 4, 5), --Lift/Boom Equipment
        (22, 5, 5), --Safety Equipment
        (23, 6, 5), --Other


        -- Tractor
        (24, 1, 6), --Underhood/Engine Compartment
        (25, 2, 6), --Interior/Cab
        (26, 3, 6), --Exterior
        (27, 5, 6), --Safety Equipment
        (28, 6, 6), --Other

        -- Trailer
        (29, 8, 7), -- Inspection Items

        -- Container
        (30, 8, 8), -- Inspection Items

        -- Hazmat
        (31, 1, 9), --Underhood/Engine Compartment
        (32, 2, 9), --Interior/Cab
        (33, 3, 9), --Exterior
        (34, 5, 9), --Safety Equipment
        (35, 6, 9)  --Other
    SET IDENTITY_INSERT PreTripInspectionItemCategoryInclusions OFF
END
GO

/*************************************************************************************************
 ** Indicate User Edit
 *************************************************************************************************/

 IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='DispatchEntryEditLogs')
    CREATE TABLE dbo.DispatchEntryEditLogs (
        DispatchEntryEditLogId int IDENTITY (1, 1) NOT NULL,
        DispatchEntryId int NOT NULL,
        UserId int NOT NULL,
        CreateDate datetime NOT NULL default (GETDATE()),
        IsActive bit NOT NULL default ((0)),
        
        CONSTRAINT [PK_DispatchEntryEditLog] PRIMARY KEY CLUSTERED (DispatchEntryEditLogId ASC),
        CONSTRAINT [FK_DispatchEntryEditLog_DispatchEntries] FOREIGN KEY(DispatchEntryId) REFERENCES dbo.DispatchEntries (DispatchEntryId),
        CONSTRAINT [FK_DispatchEntryEditLog_Users] FOREIGN KEY(UserId) REFERENCES dbo.Users (UserId)
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryEditLogs' AND COLUMN_NAME = 'IpAddress')
    ALTER TABLE DispatchEntryEditLogs ADD IpAddress varchar(255) NULL
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryEditLogs' AND COLUMN_NAME = 'ClientVersionId')
    ALTER TABLE [dbo].DispatchEntryEditLogs ADD ClientVersionId int null
GO

IF OBJECT_ID('MCDispatch.[vwAgeroGpsBreadcrumbExport]') IS NULL EXEC ('CREATE VIEW MCDispatch.[vwAgeroGpsBreadcrumbExport] AS SELECT 1 as Temp')
GO

ALTER VIEW [MCDispatch].[vwAgeroGpsBreadcrumbExport] AS
select distinct D.DispatchEntryId,     
 ISC.VendorId,     
 AGS.AccessToken,    
 D.DriverId as ExternalDriverId,     
 ULC.Latitude,     
 ULC.Longitude,     
 ULC.Timestamp, D.CompanyId     

from DispatchEntryRequests DR   with (nolock)   
 INNER JOIN DispatchEntries D  with (nolock) on D.DispatchEntryId=DR.DispatchEntryId and D.Status > 1 AND D.Status < 5    
 INNER JOIN MCDispatch.AgeroDispatches ISC with (nolock) on ISC.CallRequestId=DR.CallRequestId    
 INNER JOIN MCDispatch.AgeroSessions AGS  with (nolock) on AGS.VendorId=ISC.VendorId    
 INNER JOIN Drivers DRV  with (nolock) on DRV.DriverId=D.DriverId    
 INNER JOIN UserLocationsCurrent ULC WITH (nolock) on ULC.UserId=DRV.UserId    
 where DR.CreateDate > DATEADD(hour, -4, getdate()) and DR.status=1 and DR.CompanyId not in (2887,4550,6304,9783,9760)    
 and ulc.Timestamp > dateadd(second, -60, getdate())    
 and DR.CallRequestId > IDENT_CURRENT('DispatchEntryRequests')-50000    
  -- added 7/2/19  
  UNION  



  select distinct D.DispatchEntryId,     
 ISC.VendorId,     
 AGS.AccessToken,    
 D.DriverId as ExternalDriverId,     
 ULC.Latitude,     
 ULC.Longitude,     
 ULC.Timestamp, D.CompanyId     

from DispatchEntryRequests DR  with (nolock)  
 INNER JOIN DispatchEntries D  with (nolock) on D.DispatchEntryId=DR.DispatchEntryId and D.Status > 1 AND D.Status < 5    
 INNER JOIN MCDispatch.AgeroDispatches ISC with (nolock) on ISC.CallRequestId=DR.CallRequestId    
 INNER JOIN MCDispatch.AgeroSessions AGS  with (nolock)on AGS.VendorId=ISC.VendorId    
 INNER JOIN Trucks TCK  with (nolock) on TCK.TruckId=D.TruckId  
 INNER JOIN TruckLocationsCurrent ULC WITH (nolock) on ULC.TruckId=TCK.TruckId  
 INNER JOIN Drivers DRV  with (nolock) on DRV.DriverId=D.DriverId    
 where DR.CreateDate > DATEADD(hour, -4, getdate()) and DR.status=1 and DR.CompanyId not in (2887,4550,6304,9783,9760)    
 and ulc.Timestamp > dateadd(second, -60, getdate())    
 and DR.CallRequestId > IDENT_CURRENT('DispatchEntryRequests')-50000    

GO


    
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Stickers' AND COLUMN_NAME = 'DispatchEntryId')
    ALTER TABLE [dbo].Stickers ADD DispatchEntryId int null
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Stickers' AND COLUMN_NAME = 'GracePeriodType')
    ALTER TABLE [dbo].Stickers ADD GracePeriodType int not null default(0)

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Stickers' AND COLUMN_NAME = 'GracePeriodExpirationDate')
    ALTER TABLE [dbo].Stickers ADD GracePeriodExpirationDate datetime not null default(getdate())
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Stickers' AND COLUMN_NAME = 'ExtendedExpirationDate')
    ALTER TABLE [dbo].Stickers ADD ExtendedExpirationDate datetime null
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Stickers' AND COLUMN_NAME = 'AuthorizationDate')
    ALTER TABLE [dbo].Stickers ADD AuthorizationDate datetime

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Stickers' AND COLUMN_NAME = 'AuthorizationUserId')
    ALTER TABLE [dbo].Stickers ADD AuthorizationUserId int
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Stickers' AND COLUMN_NAME = 'AuthorizationUserSignatureId')
    ALTER TABLE [dbo].Stickers ADD AuthorizationUserSignatureId int
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Stickers' AND COLUMN_NAME = 'AuthorizationRequired')
    ALTER TABLE [dbo].Stickers ADD AuthorizationRequired int
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Stickers' AND COLUMN_NAME = 'ResolvedDate')
    ALTER TABLE [dbo].Stickers ADD ResolvedDate datetime
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Stickers' AND COLUMN_NAME = 'ResolvedUserId')
    ALTER TABLE [dbo].Stickers ADD ResolvedUserId int
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Stickers' AND COLUMN_NAME = 'RejectedDate')
    ALTER TABLE [dbo].Stickers ADD RejectedDate datetime
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Stickers' AND COLUMN_NAME = 'RejectedUserId')
    ALTER TABLE [dbo].Stickers ADD RejectedUserId int
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Stickers' AND COLUMN_NAME = 'VehicleLatitude')
    ALTER TABLE [dbo].Stickers ADD VehicleLatitude decimal(9,6)  not null default(0)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Stickers' AND COLUMN_NAME = 'VehicleLongitude')
    ALTER TABLE [dbo].Stickers ADD VehicleLongitude decimal(9,6)  not null default(0)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Stickers' AND COLUMN_NAME = 'VehicleLocation')
    ALTER TABLE [dbo].Stickers ADD VehicleLocation varchar(100)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Stickers' AND COLUMN_NAME = 'ExpirationDate')
    ALTER TABLE [dbo].Stickers ADD ExpirationDate datetime
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Stickers' AND COLUMN_NAME = 'StatusId')
    ALTER TABLE [dbo].Stickers ADD StatusId int
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Stickers' AND COLUMN_NAME = 'StickerSessionId')
    ALTER TABLE [dbo].Stickers ADD StickerSessionId int
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Stickers' AND COLUMN_NAME = 'OwnerUserId')
    ALTER TABLE [dbo].Stickers ADD OwnerUserId int
GO




IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='StickerGracePeriodSettings')
    CREATE TABLE dbo.StickerGracePeriodSettings (
        StickerGracePeriodSettingId int IDENTITY (1, 1) NOT NULL,
        CompanyId int,
        AccountId int,
        ReasonId int,
        Hours int,
        StartFromType int,
        OwnerUserId int ,
        CreateDate datetime NOT NULL default(GETDATE()),
        IsDeleted bit NOT NULL default ((0)),
        DeletedTime datetime,
        DeletedUserId int

        CONSTRAINT [PK_StickerGracePeriodSettings] PRIMARY KEY CLUSTERED (StickerGracePeriodSettingId ASC),
        CONSTRAINT [FK_StickerGracePeriodSettings_Users] FOREIGN KEY(OwnerUserId) REFERENCES dbo.Users (UserId),
        CONSTRAINT [FK_StickerGracePeriodSettings_UserDeleter] FOREIGN KEY(DeletedUserId) REFERENCES dbo.Users (UserId),
        CONSTRAINT [FK_StickerGracePeriodSettings_Accounts] FOREIGN KEY(AccountId) REFERENCES dbo.Accounts (AccountId),
        CONSTRAINT [FK_StickerGracePeriodSettings_Companies] FOREIGN KEY(CompanyId) REFERENCES dbo.Companies (CompanyId)
    )
GO

GRANT SELECT ON StickerGracePeriodSettings TO public
GRANT INSERT ON StickerGracePeriodSettings TO public
GRANT UPDATE ON StickerGracePeriodSettings TO public



IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='DispatchEntryValidationRuleSets')
    CREATE TABLE dbo.DispatchEntryValidationRuleSets (
        DispatchEntryValidationRuleSetId int IDENTITY (1, 1) NOT NULL,
        CompanyId  int NOT NULL DEFAULT(0),
        MasterAccountId int NOT NULL DEFAULT(0),
        AccountId int NOT NULL DEFAULT(0),
        RequirePlateNumber int NOT NULL DEFAULT(0),
        RequirePlateState int NOT NULL DEFAULT(0),
        RequireVin int NOT NULL DEFAULT(0),
        RequireOdometer int NOT NULL DEFAULT(0),
        RequireVehicleColor int NOT NULL DEFAULT(0),
        RequireVehicleYear int NOT NULL DEFAULT(0),
        RequireCharges int NOT NULL DEFAULT(0),
        RequireOnSceneStatus int NOT NULL DEFAULT(0),
        RequireReason int NOT NULL DEFAULT(0),
        RequirePurchaseOrderNumber int NOT NULL DEFAULT(0),
        RequireInvoiceNumber int NOT NULL DEFAULT(0),
        RequireUnloadedMiles int NOT NULL DEFAULT(0),
        RequireSignature int NOT NULL DEFAULT(0),
        RequirePhotos int NOT NULL DEFAULT(0),
        RequireDamageForm int NOT NULL DEFAULT(0),
        RequiredBeforeStatusId  int NOT NULL DEFAULT(5),
        OwnerUserId int ,
        CreateDate datetime NOT NULL default(GETDATE()),
        IsDeleted bit NOT NULL default ((0)),
        DeletedTime datetime,
        DeletedUserId int

        CONSTRAINT [PK_DispatchEntryValidationRuleSets] PRIMARY KEY CLUSTERED (DispatchEntryValidationRuleSetId ASC),
        CONSTRAINT [FK_DispatchEntryValidationRuleSets_Users] FOREIGN KEY(OwnerUserId) REFERENCES dbo.Users (UserId),
        CONSTRAINT [FK_DispatchEntryValidationRuleSets_UserDeleter] FOREIGN KEY(DeletedUserId) REFERENCES dbo.Users (UserId),
        CONSTRAINT [FK_DispatchEntryValidationRuleSets_Accounts] FOREIGN KEY(AccountId) REFERENCES dbo.Accounts (AccountId),
        CONSTRAINT [FK_DispatchEntryValidationRuleSets_Companies] FOREIGN KEY(CompanyId) REFERENCES dbo.Companies (CompanyId)
    )
GO

GRANT SELECT ON DispatchEntryValidationRuleSets TO public
GRANT INSERT ON DispatchEntryValidationRuleSets TO public
GRANT UPDATE ON DispatchEntryValidationRuleSets TO public
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryValidationRuleSets' AND COLUMN_NAME = 'RequireInvoicePaid')
	alter table DispatchEntryValidationRuleSets
		add RequireInvoicePaid int NOT NULL default(0)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryValidationRuleSets' AND COLUMN_NAME = 'RequireOveragesPaid')
	alter table DispatchEntryValidationRuleSets
		add RequireOveragesPaid int NOT NULL default(0)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryValidationRuleSets' AND COLUMN_NAME = 'RequireContactName')
	alter table DispatchEntryValidationRuleSets
		add RequireContactName int NOT NULL default(0)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryValidationRuleSets' AND COLUMN_NAME = 'RequireContactPhone')
	alter table DispatchEntryValidationRuleSets
		add RequireContactPhone int NOT NULL default(0)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryValidationRuleSets' AND COLUMN_NAME = 'AccountTypeId')
	alter table DispatchEntryValidationRuleSets
		add AccountTypeId int NULL
GO

IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = 'dbo'AND TABLE_NAME = 'DispatchEntryValidationRuleSets' AND COLUMN_NAME = 'AccountId' AND DATA_TYPE = 'INT' AND IS_NULLABLE = 'NO')
BEGIN

	ALTER TABLE DispatchEntryValidationRuleSets
	ALTER COLUMN AccountId INT NULL

	ALTER TABLE DispatchEntryValidationRuleSets
	ALTER COLUMN MasterAccountId INT NULL

	ALTER TABLE DispatchEntryValidationRuleSets
	ALTER COLUMN RequirePlateNumber INT NULL

	ALTER TABLE DispatchEntryValidationRuleSets
	ALTER COLUMN RequirePlateState INT NULL

	ALTER TABLE DispatchEntryValidationRuleSets
	ALTER COLUMN RequireVin INT NULL

	ALTER TABLE DispatchEntryValidationRuleSets
	ALTER COLUMN RequireOdometer INT NULL

	ALTER TABLE DispatchEntryValidationRuleSets
	ALTER COLUMN RequireVehicleColor INT NULL

	ALTER TABLE DispatchEntryValidationRuleSets
	ALTER COLUMN RequireVehicleYear INT NULL

	ALTER TABLE DispatchEntryValidationRuleSets
	ALTER COLUMN RequireCharges INT NULL

	ALTER TABLE DispatchEntryValidationRuleSets
	ALTER COLUMN RequireOveragesPaid INT NULL

	ALTER TABLE DispatchEntryValidationRuleSets
	ALTER COLUMN RequireOnSceneStatus INT NULL

	ALTER TABLE DispatchEntryValidationRuleSets
	ALTER COLUMN RequireReason INT NULL

	ALTER TABLE DispatchEntryValidationRuleSets
	ALTER COLUMN RequirePurchaseOrderNumber INT NULL

	ALTER TABLE DispatchEntryValidationRuleSets
	ALTER COLUMN RequireInvoiceNumber INT NULL

	ALTER TABLE DispatchEntryValidationRuleSets
	ALTER COLUMN RequireUnloadedMiles INT NULL

	ALTER TABLE DispatchEntryValidationRuleSets
	ALTER COLUMN RequireSignature INT NULL

	ALTER TABLE DispatchEntryValidationRuleSets
	ALTER COLUMN RequirePhotos INT NULL

	ALTER TABLE DispatchEntryValidationRuleSets
	ALTER COLUMN RequireDamageForm INT NULL

	ALTER TABLE DispatchEntryValidationRuleSets
	ALTER COLUMN RequireInvoicePaid INT NULL

	ALTER TABLE DispatchEntryValidationRuleSets
	ALTER COLUMN RequireContactName INT NULL

	ALTER TABLE DispatchEntryValidationRuleSets
	ALTER COLUMN RequireContactPhone INT NULL

	ALTER TABLE DispatchEntryValidationRuleSets
	ALTER COLUMN RequireVehicleMake INT NULL

	ALTER TABLE DispatchEntryValidationRuleSets
	ALTER COLUMN RequireVehicleModel INT NULL

	ALTER TABLE DispatchEntryValidationRuleSets
	ALTER COLUMN RequireVehicleDestination INT NULL

	ALTER TABLE DispatchEntryValidationRuleSets
	ALTER COLUMN RequireVehicleKeysLocation INT NULL

END
ELSE
BEGIN
	PRINT 'AccountId column is already a nullable INT column data type.'
END



IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='ImpoundInvoiceOptions')
    CREATE TABLE dbo.ImpoundInvoiceOptions(
        ImpoundInvoiceOptionsId int IDENTITY(1,1) NOT NULL,
        CompanyId int NOT NULL,
        AccountId int NULL,
        ShowInvoiceNumber int NULL,
        ShowWindowEnvelope bit NOT NULL,
        ShowTruck bit NOT NULL,
        ShowDriver bit NOT NULL,
        ShowNotes bit NOT NULL,
        ShowCreateDate bit NOT NULL,
        ShowArrivalTime bit NOT NULL,
        ShowEnrouteTime bit NOT NULL,
        ShowCompletionTime bit NOT NULL,
        ShowDispatchTime bit NOT NULL,
		ShowDestinationArrivalTime bit NOT NULL,
        HideReferenceNumber bit NOT NULL,
        HideTowSource bit NOT NULL,
        HideTowDestination bit NOT NULL,
        HidePrintDate bit NOT NULL,
        HideReason bit NOT NULL,
        HideDrivable bit NOT NULL,
        HideDriverLicense bit NOT NULL,
        HideKeys bit NOT NULL,
        HideStockNumber bit NOT NULL,
        HideImpoundDate bit NOT NULL,
        HideCompletionTime bit NOT NULL,
        HideAccountContact bit NOT NULL,
        
        CONSTRAINT PK_ImpoundInvoiceOptions PRIMARY KEY CLUSTERED  (ImpoundInvoiceOptionsId ASC)
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='ImpoundInvoiceOptions' AND COLUMN_NAME = 'ShowCallNumber')
	ALTER TABLE ImpoundInvoiceOptions ADD ShowCallNumber bit DEFAULT 1 NOT NULL;

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='ImpoundInvoiceOptions' AND COLUMN_NAME = 'ShowDestinationArrivalTime')
	ALTER TABLE ImpoundInvoiceOptions ADD ShowDestinationArrivalTime BIT NOT NULL DEFAULT 0

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='ImpoundInvoiceOptions' AND COLUMN_NAME = 'ShowPoliceHold')
	ALTER TABLE ImpoundInvoiceOptions ADD ShowPoliceHold BIT NOT NULL DEFAULT 0

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='ImpoundInvoiceOptions' AND COLUMN_NAME = 'ShowReleaseNotes')
	ALTER TABLE ImpoundInvoiceOptions ADD ShowReleaseNotes BIT NOT NULL DEFAULT 0

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='ImpoundInvoiceOptions' AND COLUMN_NAME = 'ShowTowingTime')
	ALTER TABLE ImpoundInvoiceOptions ADD ShowTowingTime BIT NOT NULL DEFAULT 0

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='ImpoundInvoiceOptions' AND COLUMN_NAME = 'ShowCallContact')
	ALTER TABLE ImpoundInvoiceOptions ADD ShowCallContact BIT NOT NULL DEFAULT (1)
	
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='ImpoundInvoiceOptions' AND COLUMN_NAME = 'IncludeDriverSignature')
    ALTER TABLE [dbo].[ImpoundInvoiceOptions]
        ADD IncludeDriverSignature BIT NOT NULL DEFAULT(0)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='ImpoundInvoiceOptions' AND COLUMN_NAME = 'HideRateItemCategoryNames')
    ALTER TABLE [dbo].[ImpoundInvoiceOptions]
        ADD HideRateItemCategoryNames BIT NOT NULL DEFAULT(0)
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='ImpoundInvoiceOptions' AND COLUMN_NAME = 'ShowDispatcher')
    ALTER TABLE [dbo].[ImpoundInvoiceOptions]
        ADD ShowDispatcher BIT NOT NULL DEFAULT(0)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='ImpoundInvoiceOptions' AND COLUMN_NAME = 'ShowCompanyEmail')
    ALTER TABLE [dbo].[ImpoundInvoiceOptions]
        ADD ShowCompanyEmail BIT NOT NULL DEFAULT(0)
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='UserImpersonationSessions')
    CREATE TABLE dbo.UserImpersonationSessions (
        UserImpersonationSessionId int IDENTITY(1,1) NOT NULL,
        OwnerUserId int NOT NULL,
        CompanyId int NOT NULL,
        CreateDate datetime not null default(getdate()),
        IpAddress varchar(50) NOT NULL,
        UserAgent varchar(50) NOT NULL,
        UserTypeId int NOT NULL,
        TerminationDate datetime,
        ExpirationDate datetime NOT NULL default(dateadd(hour,2, getdate())),
        UrlHash varchar(32) NOT NULL,

        CONSTRAINT PK_UserImpersonationSessions PRIMARY KEY CLUSTERED  (UserImpersonationSessionId ASC)
    )
GO

GRANT SELECT ON UserImpersonationSessions to public
GRANT INSERT ON UserImpersonationSessions to public
GRANT DELETE ON UserImpersonationSessions to public


/*************************************************************************************************
 ** Accident Reports
 *************************************************************************************************/

--drop table AccidentReportPhotos
--drop table AccidentReportItems
--drop table AccidentReports

IF OBJECT_ID('dbo.AccidentReports', 'U') IS NULL
    CREATE TABLE dbo.AccidentReports (
        AccidentReportId    INT IDENTITY (1, 1) NOT NULL,
        CompanyId           INT NOT NULL,
        DispatchEntryId     INT NOT NULL,
        AttachInvoice       BIT NOT NULL,
        OwnerUserId         INT NOT NULL,
        CreateDate          DATETIME NOT NULL DEFAULT GETDATE(),
        IsDeleted           BIT NOT NULL DEFAULT 0,

        CONSTRAINT PK_AccidentReports PRIMARY KEY CLUSTERED (AccidentReportId),
        CONSTRAINT FK_AccidentReports_Companies FOREIGN KEY (CompanyId) 
            REFERENCES dbo.Companies (CompanyId),
        CONSTRAINT FK_AccidentReports_DispatchEntries FOREIGN KEY (DispatchEntryId) 
            REFERENCES dbo.DispatchEntries (DispatchEntryId),
        CONSTRAINT FK_AccidentReports_Users FOREIGN KEY (OwnerUserId) 
            REFERENCES dbo.Users (UserId)
    )
GO

IF OBJECT_ID('dbo.AccidentReportContacts', 'U') IS NULL
    CREATE TABLE dbo.AccidentReportContacts (
        AccidentReportContactId INT IDENTITY (1, 1) NOT NULL,
        AccidentReportId     INT NOT NULL,
        ContactId INT NOT NULL,

        CONSTRAINT PK_AccidentReportContacts PRIMARY KEY CLUSTERED (AccidentReportContactId),
        CONSTRAINT FK_AccidentReportContacts_AccidentReports FOREIGN KEY (AccidentReportId) 
            REFERENCES dbo.AccidentReports (AccidentReportId),
        CONSTRAINT FK_AccidentReportContacts_DispatchEntryContacts FOREIGN KEY (ContactId) 
            REFERENCES dbo.DispatchEntryContacts (DispatchEntryContactId)
    )
GO

IF OBJECT_ID('dbo.AccidentReportItems', 'U') IS NULL
    CREATE TABLE dbo.AccidentReportItems (
        AccidentReportItemId INT IDENTITY (1, 1) NOT NULL,
        AccidentReportId     INT NOT NULL,
        ItemType             INT NOT NULL,
        Name                 VARCHAR(100) NULL,
        Value                VARCHAR(8000) NULL,
        OrderNum             INT NULL,
        SectionEnd           BIT NULL,

        CONSTRAINT PK_AccidentReportItems PRIMARY KEY CLUSTERED (AccidentReportItemId),
        CONSTRAINT FK_AccidentReportItems_AccidentReports FOREIGN KEY (AccidentReportId) 
            REFERENCES dbo.AccidentReports (AccidentReportId)
    )
GO

IF OBJECT_ID('dbo.AccidentReportPhotos', 'U') IS NULL

	CREATE TABLE [dbo].AccidentReportPhotos(
		AccidentReportPhotoId INT IDENTITY (1, 1) NOT NULL,
		AccidentReportId      INT NOT NULL,
		DispatchEntryPhotoId  INT NULL,

		CONSTRAINT PK_AccidentReportPhotos PRIMARY KEY CLUSTERED (AccidentReportPhotoId),
		CONSTRAINT FK_AccidentReportPhotos_AccidentReports FOREIGN KEY (AccidentReportId) 
			REFERENCES dbo.AccidentReports (AccidentReportId),
		CONSTRAINT FK_AccidentReportPhotos_DispatchEntryPhotos FOREIGN KEY (DispatchEntryPhotoId) 
			REFERENCES dbo.DispatchEntryPhotos (DispatchEntryPhotoId)
	)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccidentReportPhotos' AND COLUMN_NAME = 'PhotoType')
	ALTER TABLE AccidentReportPhotos ADD PhotoType INT DEFAULT 1 NOT NULL;

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccidentReportPhotos' AND COLUMN_NAME = 'ContentType')
	ALTER TABLE AccidentReportPhotos ADD ContentType varchar(50);

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccidentReportPhotos' AND COLUMN_NAME = 'CreateDate')
	ALTER TABLE AccidentReportPhotos ADD CreateDate datetime DEFAULT (getdate()) NOT NULL;

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccidentReportPhotos' AND COLUMN_NAME = 'OwnerUserId')
	ALTER TABLE AccidentReportPhotos ADD OwnerUserId INT NULL

ALTER TABLE dbo.AccidentReportPhotos ALTER COLUMN DispatchEntryPhotoId INT NULL

IF ((SELECT COUNT(1) FROM dbo.AccidentReports where AccidentReportId=1) = 0 AND
	EXISTS(select 1 from DispatchEntries where DispatchEntryId=********))
BEGIN
    SET IDENTITY_INSERT AccidentReports ON
    INSERT INTO AccidentReports (AccidentReportId, CompanyId, DispatchEntryId, AttachInvoice, OwnerUserId) VALUES
        (1, 2, ********, 1, 10)
    SET IDENTITY_INSERT AccidentReports OFF
END
GO

IF (NOT EXISTS(SELECT 1 FROM masteraccounts WHERE masteraccountid=21))
BEGIN
	SET IDENTITY_INSERT MasterAccounts ON
	INSERT INTO MasterAccounts (MasterAccountId, AccountTypeId, Name, RelationshipId, Country, Address, City, State, Zip) 
	values (21,5, 'Urgent.ly', 2, 'USA', '', '', 'VA', ''),
	(22,5, 'USAC', 2, 'USA', '', '', 'TX', '')
	SET IDENTITY_INSERT MasterAccounts OFF
END


IF (NOT EXISTS(SELECT 1 FROM masteraccounts WHERE masteraccountid=26))
BEGIN
	SET IDENTITY_INSERT MasterAccounts ON
	INSERT INTO MasterAccounts (MasterAccountId, AccountTypeId, Name, RelationshipId, Country, Address, City, State, Zip) 
	values (26,5, 'Sykes', 2, 'Canada', '', '', 'ON', '')
	SET IDENTITY_INSERT MasterAccounts OFF
END



IF (NOT EXISTS(SELECT 1 FROM masteraccounts WHERE masteraccountid=27))
BEGIN
	SET IDENTITY_INSERT MasterAccounts ON
	INSERT INTO MasterAccounts (MasterAccountId, AccountTypeId, Name, RelationshipId, Country, Address, City, State, Zip) 
	values (27,5, 'Fleetnet', 2, 'USA', '', '', 'TX', '')
	SET IDENTITY_INSERT MasterAccounts OFF
END

IF (NOT EXISTS(SELECT 1 FROM masteraccounts WHERE masteraccountid=28))
BEGIN
	SET IDENTITY_INSERT MasterAccounts ON
	INSERT INTO MasterAccounts (MasterAccountId, AccountTypeId, Name, RelationshipId, Country, Address, City, State, Zip) 
	values (28,5, 'Agero Spark', 2, 'USA', '', '', 'MA', '')
	SET IDENTITY_INSERT MasterAccounts OFF
END


IF (NOT EXISTS(SELECT 1 FROM masteraccounts WHERE masteraccountid=29))
BEGIN
	SET IDENTITY_INSERT MasterAccounts ON
	INSERT INTO MasterAccounts (MasterAccountId, AccountTypeId, Name, RelationshipId, Country, Address, City, State, Zip) 
	values (29,5, 'Swoop', 2, 'USA', '', 'San Francisco', 'CA', '')
	SET IDENTITY_INSERT MasterAccounts OFF
END

IF (NOT EXISTS(SELECT 1 FROM masteraccounts WHERE masteraccountid=30))
BEGIN
	SET IDENTITY_INSERT MasterAccounts ON
	INSERT INTO MasterAccounts (MasterAccountId, AccountTypeId, Name, RelationshipId, Country, Address, City, State, Zip) 
	values (30,5, 'Out of Network - Agero', 2, 'USA', '', 'Medford', 'MA', '')
	SET IDENTITY_INSERT MasterAccounts OFF
END

IF (NOT EXISTS(SELECT 1 FROM masteraccounts WHERE masteraccountid=31))
BEGIN
	SET IDENTITY_INSERT MasterAccounts ON
	INSERT INTO MasterAccounts (MasterAccountId, AccountTypeId, Name, RelationshipId, Country, Address, City, State, Zip) 
	values (31,5, 'Out of Network - Allstate', 2, 'USA', '', '', 'IL', '')
	SET IDENTITY_INSERT MasterAccounts OFF
END

IF (NOT EXISTS(SELECT 1 FROM masteraccounts WHERE masteraccountid=32))
BEGIN
	SET IDENTITY_INSERT MasterAccounts ON
	INSERT INTO MasterAccounts (MasterAccountId, AccountTypeId, Name, RelationshipId, Country, Address, City, State, Zip) 
	values (32,5, 'Honk', 2, 'USA', '', '', 'IL', '')
	SET IDENTITY_INSERT MasterAccounts OFF
END
IF (NOT EXISTS(SELECT 1 FROM masteraccounts WHERE masteraccountid=33))
BEGIN
	SET IDENTITY_INSERT MasterAccounts ON
	INSERT INTO MasterAccounts (MasterAccountId, AccountTypeId, Name, RelationshipId, Country, Address, City, State, Zip) 
	values (33,5, 'Out of Network - Quest', 2, 'USA', '', '', 'IL', '')
	SET IDENTITY_INSERT MasterAccounts OFF
END
IF (NOT EXISTS(SELECT 1 FROM masteraccounts WHERE masteraccountid=34))
BEGIN
	SET IDENTITY_INSERT MasterAccounts ON
	INSERT INTO MasterAccounts (MasterAccountId, AccountTypeId, Name, RelationshipId, Country, Address, City, State, Zip) 
	values (34,5, 'Out of Network - Urgently', 2, 'USA', '', '', 'IL', '')
	SET IDENTITY_INSERT MasterAccounts OFF
END

IF (NOT EXISTS(SELECT 1 FROM masteraccounts WHERE masteraccountid=35))
BEGIN
	SET IDENTITY_INSERT MasterAccounts ON
	INSERT INTO MasterAccounts (MasterAccountId, AccountTypeId, Name, RelationshipId, Country, Address, City, State, Zip) 
	values (35,5, 'Servicase', 2, 'USA', '', '', 'IL', '')
	SET IDENTITY_INSERT MasterAccounts OFF
END

IF (NOT EXISTS(SELECT 1 FROM masteraccounts WHERE masteraccountid=36))
BEGIN
	SET IDENTITY_INSERT MasterAccounts ON
	INSERT INTO MasterAccounts (MasterAccountId, AccountTypeId, Name, RelationshipId, Country, Address, City, State, Zip) 
	values (36,5, 'Out of Network - Agero (Swoop)', 2, 'USA', '', 'Medford', 'MA', '')
	SET IDENTITY_INSERT MasterAccounts OFF
END



IF (NOT EXISTS(SELECT 1 FROM masteraccounts WHERE masteraccountid=37))
BEGIN
	SET IDENTITY_INSERT MasterAccounts ON
	INSERT INTO MasterAccounts (MasterAccountId, AccountTypeId, Name, RelationshipId, Country, Address, City, State, Zip) 
	values (37,5, 'Carvana', 1, 'USA', '', '', 'AZ', '')
	SET IDENTITY_INSERT MasterAccounts OFF
END


IF (NOT EXISTS(SELECT 1 FROM masteraccounts WHERE masteraccountid=38))
BEGIN
	SET IDENTITY_INSERT MasterAccounts ON
	INSERT INTO MasterAccounts (MasterAccountId, AccountTypeId, Name, RelationshipId, Country, Address, City, State, Zip) 
	values (38,5, 'AAA ACE', 2, 'USA', '', '', 'AZ', '')
	SET IDENTITY_INSERT MasterAccounts OFF
END
GO

IF (NOT EXISTS(SELECT 1 FROM masteraccounts WHERE masteraccountid=39))
BEGIN
	SET IDENTITY_INSERT MasterAccounts ON
	INSERT INTO MasterAccounts (MasterAccountId, AccountTypeId, Name, RelationshipId, Country, Address, City, State, Zip) 
	values (39,5, 'Driven Solutions', 2, 'USA', '', '', '', '')
	SET IDENTITY_INSERT MasterAccounts OFF
END


IF (NOT EXISTS(SELECT 1 FROM masteraccounts WHERE masteraccountid=40))
BEGIN
	SET IDENTITY_INSERT MasterAccounts ON
	INSERT INTO MasterAccounts (MasterAccountId, AccountTypeId, Name, RelationshipId, Country, Address, City, State, Zip) 
	values (40,5, 'Stack Three Atlas', 2, 'USA', '', '', 'FL', '')
	SET IDENTITY_INSERT MasterAccounts OFF
END


IF (NOT EXISTS(SELECT 1 FROM masteraccounts WHERE masteraccountid=41))
BEGIN
	SET IDENTITY_INSERT MasterAccounts ON
	INSERT INTO MasterAccounts (MasterAccountId, AccountTypeId, Name, RelationshipId, Country, Address, City, State, Zip) 
	values (41,5, 'Out of Network - Honk', 2, 'USA', '', '', 'AZ', '')
	SET IDENTITY_INSERT MasterAccounts OFF
END

IF (NOT EXISTS(SELECT 1 FROM masteraccounts WHERE masteraccountid=42))
BEGIN
	SET IDENTITY_INSERT MasterAccounts ON
	INSERT INTO MasterAccounts (MasterAccountId, AccountTypeId, Name, RelationshipId, Country, Address, City, State, Zip) 
	values (42,5, 'Out of Network - Roadside Protect', 2, 'USA', '', '', 'AZ', '')
	SET IDENTITY_INSERT MasterAccounts OFF
END

IF (NOT EXISTS(SELECT 1 FROM masteraccounts WHERE masteraccountid=43))
BEGIN
	SET IDENTITY_INSERT MasterAccounts ON
	INSERT INTO MasterAccounts (MasterAccountId, AccountTypeId, Name, RelationshipId, Country, Address, City, State, Zip) 
	values (43,5, 'Out of Network - TrxNow', 2, 'USA', '', '', 'AZ', '')
	SET IDENTITY_INSERT MasterAccounts OFF
END


IF (NOT EXISTS(SELECT 1 FROM masteraccounts WHERE masteraccountid=44))
BEGIN
	SET IDENTITY_INSERT MasterAccounts ON
	INSERT INTO MasterAccounts (MasterAccountId, AccountTypeId, Name, RelationshipId, Country, Address, City, State, Zip) 
	values (44,5, 'Out of Network - Driven Solutions', 2, 'USA', '', '', 'AZ', '')
	SET IDENTITY_INSERT MasterAccounts OFF
END





IF (NOT EXISTS(SELECT 1 FROM masteraccounts WHERE masteraccountid=45))
BEGIN
	SET IDENTITY_INSERT MasterAccounts ON
	INSERT INTO MasterAccounts (MasterAccountId, AccountTypeId, Name, RelationshipId, Country, Address, City, State, Zip) 
	values (45,5, 'AAA - ACG', 2, 'USA', '', '', 'AZ', '')
	SET IDENTITY_INSERT MasterAccounts OFF
END

IF (NOT EXISTS(SELECT 1 FROM masteraccounts WHERE masteraccountid=46))
BEGIN
	SET IDENTITY_INSERT MasterAccounts ON
	INSERT INTO MasterAccounts (MasterAccountId, AccountTypeId, Name, RelationshipId, Country, Address, City, State, Zip) 
	values (46,5, 'AAA - National', 2, 'USA', '', '', 'AZ', '')
	SET IDENTITY_INSERT MasterAccounts OFF
END


IF (NOT EXISTS(SELECT 1 FROM masteraccounts WHERE masteraccountid=47))
BEGIN
	SET IDENTITY_INSERT MasterAccounts ON
	INSERT INTO MasterAccounts (MasterAccountId, AccountTypeId, Name, RelationshipId, Country, Address, City, State, Zip) 
	values (47,5, 'AAA - National FSL', 2, 'USA', '', '', 'FL', '')
	SET IDENTITY_INSERT MasterAccounts OFF
END

IF (NOT EXISTS(SELECT 1 FROM masteraccounts WHERE masteraccountid=48))
BEGIN
	SET IDENTITY_INSERT MasterAccounts ON
	INSERT INTO MasterAccounts (MasterAccountId, AccountTypeId, Name, RelationshipId, Country, Address, City, State, Zip) 
	values (48,5, 'AAA - Northeast', 2, 'USA', '', '', 'RI', '')
	SET IDENTITY_INSERT MasterAccounts OFF
END


IF (NOT EXISTS(SELECT 1 FROM masteraccounts WHERE masteraccountid=49))
BEGIN
	SET IDENTITY_INSERT MasterAccounts ON
	INSERT INTO MasterAccounts (MasterAccountId, AccountTypeId, Name, RelationshipId, Country, Address, City, State, Zip) 
	values (49,5, 'AAA - ACA', 2, 'USA', '', '', '', '')
	SET IDENTITY_INSERT MasterAccounts OFF
END

IF (NOT EXISTS(SELECT 1 FROM masteraccounts WHERE masteraccountid=50))
BEGIN
	SET IDENTITY_INSERT MasterAccounts ON
	INSERT INTO MasterAccounts (MasterAccountId, AccountTypeId, Name, RelationshipId, Country, Address, City, State, Zip) 
	values (50,5, 'AAA Washington', 2, 'USA', '', '', 'WA', '')
	SET IDENTITY_INSERT MasterAccounts OFF
END

IF (NOT EXISTS(SELECT 1 FROM masteraccounts WHERE masteraccountid=51))
BEGIN
	SET IDENTITY_INSERT MasterAccounts ON
	INSERT INTO MasterAccounts (MasterAccountId, AccountTypeId, Name, RelationshipId, Country, Address, City, State, Zip) 
	values (51,5, 'AAA New York', 2, 'USA', '', '', 'NY', '')
	SET IDENTITY_INSERT MasterAccounts OFF
END






/*************************************************************************************************
** Payment Types
*************************************************************************************************/

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='PaymentTypes')
    CREATE TABLE dbo.PaymentTypes (
		PaymentTypeId int IDENTITY NOT NULL,
        Name VARCHAR(50),
        CompanyId int NULL,
        CreateDate datetime not null default(getdate()),
        OwnerUserId int NULL,
        IsActive bit not null default(0)

        CONSTRAINT FK_PaymentTypes_CompanyId FOREIGN KEY (CompanyId) REFERENCES dbo.Companies (CompanyId)
    )
GO 

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='PaymentTypes' AND COLUMN_NAME = 'OwnerUserId')
	ALTER TABLE PaymentTypes ADD OwnerUserId INT NULL

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='PaymentTypes' AND COLUMN_NAME = 'IsActive')
	ALTER TABLE PaymentTypes ADD IsActive bit DEFAULT (0) NOT NULL;

GO

SET IDENTITY_INSERT paymentTypes ON

IF (NOT EXISTS(SELECT 1 FROM PaymentTypes WHERE PaymentTypeId=0))
	insert into PaymentTypes (PaymentTypeId, Name, CompanyId) values (0, 'Cash', NULL)
GO

IF (NOT EXISTS(SELECT 1 FROM PaymentTypes WHERE PaymentTypeId=1))
	insert into PaymentTypes (PaymentTypeId, Name, CompanyId) values (1, 'Check', NULL)
GO

IF (NOT EXISTS(SELECT 1 FROM PaymentTypes WHERE PaymentTypeId=2))
	insert into PaymentTypes (PaymentTypeId, Name, CompanyId) values (2, 'Visa', NULL)
GO

IF (NOT EXISTS(SELECT 1 FROM PaymentTypes WHERE PaymentTypeId=3))
insert into PaymentTypes (PaymentTypeId, Name, CompanyId) values (3, 'MasterCard', NULL)
GO

IF (NOT EXISTS(SELECT 1 FROM PaymentTypes WHERE PaymentTypeId=4))
	insert into PaymentTypes (PaymentTypeId, Name, CompanyId) values (4, 'Discover', NULL)
GO


IF (NOT EXISTS(SELECT 1 FROM PaymentTypes WHERE PaymentTypeId=5))
	insert into PaymentTypes (PaymentTypeId, Name, CompanyId) values (5, 'American Express', NULL)
GO

IF (NOT EXISTS(SELECT 1 FROM PaymentTypes WHERE PaymentTypeId=6))
	insert into PaymentTypes (PaymentTypeId, Name, CompanyId) values (6, 'Other', NULL)
GO

IF (NOT EXISTS(SELECT 1 FROM PaymentTypes WHERE PaymentTypeId=7))
	insert into PaymentTypes (PaymentTypeId, Name, CompanyId) values (7, 'Debit', NULL)
GO

IF (NOT EXISTS(SELECT 1 FROM PaymentTypes WHERE PaymentTypeId=8))
	insert into PaymentTypes (PaymentTypeId, Name, CompanyId) values (8, 'Account', NULL)
GO

IF (NOT EXISTS(SELECT 1 FROM PaymentTypes WHERE PaymentTypeId=10))
	insert into PaymentTypes (PaymentTypeId, Name, CompanyId) values (10, 'EFT', NULL)
GO

IF (NOT EXISTS(SELECT 1 FROM PaymentTypes WHERE PaymentTypeId=11))
	insert into PaymentTypes (PaymentTypeId, Name, CompanyId) values (11, 'Uncollectable Bad Debt', NULL)
GO

IF (NOT EXISTS(SELECT 1 FROM PaymentTypes WHERE PaymentTypeId=98))
	insert into PaymentTypes (PaymentTypeId, Name, CreateDate, CompanyId, OwnerUserId, IsActive)
    values (98, 'RoadSync', GetDate(), NULL, NULL, 0)
GO

IF (NOT EXISTS(SELECT 1 FROM PaymentTypes WHERE PaymentTypeId=99))
	insert into PaymentTypes (PaymentTypeId, Name, CompanyId) values (99, 'Credit Card', NULL)
GO

IF (NOT EXISTS(SELECT 1 FROM PaymentTypes WHERE PaymentTypeId=100))
	insert into PaymentTypes (PaymentTypeId, Name, CreateDate, CompanyId, OwnerUserId, IsActive)
    values (100, 'Square', GetDate(), NULL, NULL, 0)
GO

SET IDENTITY_INSERT paymentTypes OFF
GO


GRANT SELECT ON PaymentTypes to public
GRANT INSERT ON PaymentTypes to public
GRANT DELETE ON PaymentTypes to public

IF (OBJECT_ID('dbo.CompanySuspensions', 'U') IS NULL)
	CREATE TABLE dbo.CompanySuspensions(
		CompanySuspensionId INT IDENTITY (1, 1) NOT NULL,
		CompanyId int not null, 
		OwnerUserId int not null,
		OwnerUserIpAddress varchar(50) not null, 
		ReasonId int,
		Description varchar(500), 
		CreateDate datetime not null default(Getdate()) ,
		Deleted bit not null default(0),
		DeletedDate datetime,
		DeletedIpAddress varchar(50),
		DeletedUserId int,
		DeletedReasonId int,
		DeletedDescription varchar(500)

		CONSTRAINT PK_CompanySuspensions PRIMARY KEY CLUSTERED (CompanySuspensionId),
		CONSTRAINT FK_CompanySuspensions_Companies FOREIGN KEY (CompanyId) 
			REFERENCES dbo.Companies (CompanyId),
		CONSTRAINT FK_CompanySuspensions_Users FOREIGN KEY (OwnerUserId) 
			REFERENCES dbo.Users (UserId)
	)
GO

/*************************************************************************************************
 ** GPS Input Events
 *************************************************************************************************/

DROP TABLE Integration.GpsInputEvents

IF OBJECT_ID('Integration.GpsInputEvents', 'U') IS NULL
    CREATE TABLE Integration.GpsInputEvents (
        GpsInputEventId    INT IDENTITY (1, 1) NOT NULL,
        ProviderId         INT NOT NULL,
        CompanyId          INT NOT NULL,
        DriverId           INT NULL,
        TruckId            INT NULL,
        EventNum           INT NULL,
        EventString        VARCHAR(200) NULL, 
        EventDate          DATETIME NULL,
        CreateDate         DATETIME NOT NULL DEFAULT GETDATE(),

        CONSTRAINT PK_GpsInputEvents PRIMARY KEY CLUSTERED (GpsInputEventId),
        CONSTRAINT FK_GpsInputEvents_Companies FOREIGN KEY (CompanyId) 
            REFERENCES dbo.Companies (CompanyId),
        CONSTRAINT FK_GpsInputEvents_Trucks FOREIGN KEY (TruckId) 
            REFERENCES dbo.Trucks (TruckId)
    )
GO


-- Create view internalGpsReport
IF OBJECT_ID('internalGpsReport', 'v') IS NULL EXEC('CREATE VIEW internalGpsReport AS SELECT 1 as Temp')
GO
ALTER view [dbo].[internalGpsReport] AS
    select distinct
                 c.Name as CompanyName,
                 u.CompanyId, u.UserId, u.FullName,
     Latitude = COALESCE(uc.Latitude, ca1.Latitude),
     Longitude = COALESCE(uc.Longitude, ca1.Longitude),
     Timestamp = COALESCE(uc.Timestamp, ca1.Timestamp),
                 Coalesce(kv.Value,0) as DisableGps,
                 (select top 1 type from UserAuthenticationTokens ut with (nolock)
                    inner join platform.ClientVersions cv with (nolock) on cv.ClientVersionId=ut.ClientVersionId
                  where ut.userid=u.userid and ExpirationDate > getdate() order by 1 desc) as PlatformType  ,
                 coalesce(forceOff.Value, 0) as ForceOff,
                 coalesce(alwayson.Value, 0) as AlwaysOn
    from users u
        left join UserLocationsCurrent uc WITH (nolock) on uc.UserId = u.UserId
        left join integration.ProviderUserKeyValues kv on kv.UserId = u.UserId and kv.ProviderUserKeyId = 1
        outer apply
        (
        select
                tc.Latitude, tc.Longitude, tc.Timestamp
            from TruckLocationsCurrent tc WITH (nolock)
                join DriverTruckDefaults dfd on dfd.TruckId = tc.TruckId
                join Drivers d on d.DriverId = dfd.DriverId
                join users u2 on u2.UserId = d.UserId
            where u2.UserId = u.UserId
                and dfd.Deleted = 0
        ) ca1
          inner join companies c on c.companyid=u.CompanyId
          left outer join drivers d on d.userid=u.UserId
          left outer join Integration.ProviderDriverKeyValues alwaysOn on alwaysOn.DriverId=d.DriverId and alwaysOn.ProviderDriverKeyId=15
          left outer join Integration.ProviderDriverKeyValues forceOff on forceOff.DriverId=d.DriverId and forceOff.ProviderDriverKeyId=16
 where (u.UserTypeId = 3 or u.notes is not null and u.notes like '%grantsupervisor%') and u.Disabled = 0 and u.Deleted = 0
go


-- Create view vwGetDigitalDispatchCallRequests
IF OBJECT_ID('vwGetDigitalDispatchCallRequests', 'v') IS NULL EXEC('CREATE VIEW vwGetDigitalDispatchCallRequests AS SET NOCOUNT ON;')
GO
ALTER VIEW [dbo].[vwGetDigitalDispatchCallRequests] AS
	SELECT
        DR.CallRequestId,   
        MA.MasterAccountId,   
        MA.Name as ProviderName,   
        DR.CompanyId,
        DR.ProviderId as ContractorId,
		C.Name as CompanyName, 
		DR.AccountId, 
		DE.CallNumber, 
		DR.StartingLocation, 
		DR.Reason, 
		DR.ServiceNeeded, 
		DR.Vehicle, 
		DR.RequestDate, 
		DR.ExpirationDate, 
		DR.CreateDate, 
		DR.Status, 
		DR.DispatchEntryId, 
		DR.OwnerUserId, 
		DR.ResponseReasonId, 
		MR.Name as ResponseReasonName,
		U.FullName as OwnerUserName,
        DE.PurchaseOrderNumber,
        DR.TowDestination,
        COALESCE(DR.ETA, ETA.Eta) as EtaGiven
  FROM                              
   DispatchEntryRequests AS DR WITH (nolock)                             
   INNER JOIN Accounts   AS A WITH (nolock) ON A.AccountId = DR.AccountId  
   INNER JOIN MasterAccounts AS MA WITH (nolock) ON MA.MasterAccountId = A.MasterAccountId  
   INNER JOIN Companies AS C  WITH (nolock)ON C.CompanyId = DR.CompanyId  
   LEFT OUTER JOIN Users U WITH (nolock) on U.UserId=DR.OwnerUserId  
   LEFT OUTER JOIN DispatchEntryRequestEtas ETA WITH (nolock)  on ETA.CallRequestId=DR.CallRequestId                              
   LEFT OUTER JOIN Dispatchentries DE WITH (nolock)  on DE.DispatchEntryId=DR.DispatchEntryId                              
   LEFT OUTER JOIN MasterAccountReasons MR  WITH (nolock) on MR.MasterAccountId = A.MasterAccountId and MR.MasterAccountReasonId = DR.ResponseReasonId                         
   WHERE DR.CallRequestId > ********  

GO


/*************************************************************************************************
 ** EVENT NOTIFICATIONS
 *************************************************************************************************/

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='EventNotificationGroups')
BEGIN
    CREATE TABLE [dbo].[EventNotificationGroups] (
        [GroupId] int IDENTITY(1,1) NOT NULL,
        [Name] varchar(100) NOT NULL

        CONSTRAINT PK_EventNotificationGroups PRIMARY KEY CLUSTERED ([GroupId] ASC),
    )

    SET IDENTITY_INSERT EventNotificationGroups ON
        INSERT INTO EventNotificationGroups (GroupId, Name) VALUES (1, 'System')
        INSERT INTO EventNotificationGroups (GroupId, Name) VALUES (2, 'Dispatching')
        INSERT INTO EventNotificationGroups (GroupId, Name) VALUES (3, 'Digital Dispatch')
        INSERT INTO EventNotificationGroups (GroupId, Name) VALUES (4, 'Billing')
        INSERT INTO EventNotificationGroups (GroupId, Name) VALUES (5, 'Equipment Inspection')
        INSERT INTO EventNotificationGroups (GroupId, Name) VALUES (6, 'Stickering')
        INSERT INTO EventNotificationGroups (GroupId, Name) VALUES (7, 'Private Property')
    SET IDENTITY_INSERT EventNotificationGroups OFF
END

GRANT SELECT ON EventNotificationGroups to public

IF (NOT EXISTS(SELECT 1 FROM EventNotificationGroups WHERE GroupId=8))
BEGIN
  SET IDENTITY_INSERT EventNotificationGroups ON
  INSERT INTO EventNotificationGroups (GroupId, Name) VALUES (8, 'Roadside/Surveys')
  SET IDENTITY_INSERT EventNotificationGroups OFF
END
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='EventNotifications')
BEGIN
    CREATE TABLE [dbo].[EventNotifications] (
        [EventNotificationId] int IDENTITY(1,1) NOT NULL,
        [Name] varchar(100) NOT NULL,
        [Description] varchar(300), 
        [GroupId] INT NOT NULL,
        [EventNotificationConditionKeyId] INT NULL,
        [FeatureId] INT NULL,
        [IsActive] BIT NOT NULL DEFAULT(0),
        [EmailDefaultType] INT NOT NULL DEFAULT(0),
        [EmailManagerRequired] BIT NOT NULL DEFAULT(0),
        [EmailDispatcherRequired] BIT NOT NULL DEFAULT(0),
        [EmailDriverRequired] BIT NOT NULL DEFAULT(0),
        [TextDefaultType] INT NOT NULL DEFAULT(0),	
        [TextManagerRequired] BIT NOT NULL DEFAULT(0),
        [TextDispatcherRequired] BIT NOT NULL DEFAULT(0),
        [TextDriverRequired] BIT NOT NULL DEFAULT(0),
        [PushNotificationDefaultType] INT NOT NULL DEFAULT(0),
        [PushNotificationManagerRequired] BIT NOT NULL DEFAULT(0),
        [PushNotificationDispatcherRequired] BIT NOT NULL DEFAULT(0),
        [PushNotificationDriverRequired] BIT NOT NULL DEFAULT(0),
        [WebNotificationDefaultType] INT NOT NULL DEFAULT(0),
        [WebNotificationManagerRequired] BIT NOT NULL DEFAULT(0),
        [WebNotificationDispatcherRequired] BIT NOT NULL DEFAULT(0),
        [WebNotificationDriverRequired] BIT NOT NULL DEFAULT(0)

        CONSTRAINT PK_EventNotifications PRIMARY KEY CLUSTERED ([EventNotificationId] ASC),
        CONSTRAINT FK_EventNotifications_Groups FOREIGN KEY(GroupId) REFERENCES dbo.EventNotificationGroups (GroupId),
        CONSTRAINT FK_EventNotifications_Features FOREIGN KEY(FeatureId) REFERENCES billing.features (FeatureId),
    )
    CREATE INDEX [IX_EventNotifications_EventNotificationId] ON [dbo].[EventNotifications] ([EventNotificationId])

END

GRANT SELECT ON EventNotifications to public

-- Dispatching ------------------------------------------------------------------

SET IDENTITY_INSERT EventNotifications ON

IF (NOT EXISTS(SELECT 1 FROM EventNotifications WHERE EventNotificationId = 200))
    INSERT INTO EventNotifications (
        EventNotificationId, 
        [Name], 
        [Description], 
        GroupId, IsActive, EmailDefaultType, TextDefaulttype, PushNotificationDefaultType, WebNotificationDefaultType, 
        PushNotificationDispatcherRequired, WebNotificationDispatcherRequired) 
    VALUES (
        200,
        'When driver accepts a call', 
        'Notification will be sent when a driver gives their approval and accepts a dispatched job.',
        2, 1, 0, 0, 0, 0, 
        1, 1)

IF (NOT EXISTS(SELECT 1 FROM EventNotifications WHERE EventNotificationId = 201))
    INSERT INTO EventNotifications (
        EventNotificationId, 
        [Name], 
        [Description], 
        GroupId, IsActive, EmailDefaultType, TextDefaulttype, PushNotificationDefaultType, WebNotificationDefaultType, 
        PushNotificationManagerRequired, PushNotificationDispatcherRequired, WebNotificationManagerRequired, WebNotificationDispatcherRequired) 
    VALUES (
        201,
        'When a call is sent by email', 
        'Notification will be sent when a call is automatically created from an email from supported providers.',
        2, 1, 0, 0, 0, 1, 
        1, 1, 1, 1)

IF (NOT EXISTS(SELECT 1 FROM EventNotifications WHERE EventNotificationId = 202))
    INSERT INTO EventNotifications (
        EventNotificationId, 
        [Name], 
        [Description], 
        GroupId, IsActive, EmailDefaultType, TextDefaulttype, PushNotificationDefaultType, WebNotificationDefaultType, 
        PushNotificationManagerRequired, PushNotificationDispatcherRequired, WebNotificationManagerRequired, WebNotificationDispatcherRequired) 
    VALUES (
        202,
        'When a call is sent via web request form',
        'Notification will be sent when a call is submitted from a web request form.',
        2, 1, 0, 0, 0, 1, 
        1, 1, 1, 1)

IF (NOT EXISTS(SELECT 1 FROM EventNotifications WHERE EventNotificationId = 203))
    INSERT INTO EventNotifications (
        EventNotificationId, 
        [Name], 
        [Description], 
        GroupId, IsActive, EmailDefaultType, TextDefaulttype, PushNotificationDefaultType, WebNotificationDefaultType, 
        PushNotificationManagerRequired, PushNotificationDispatcherRequired, WebNotificationManagerRequired, WebNotificationDispatcherRequired) 
    VALUES (
        203,
        'When a call is sent by an account user',
        'Notification will be sent when a call is created by an account user profile.',
        2, 1, 0, 0, 0, 1, 
        1, 1, 1, 1)

IF (NOT EXISTS(SELECT 1 FROM EventNotifications WHERE EventNotificationId = 204))
    INSERT INTO EventNotifications (
        EventNotificationId, 
        [Name], 
        [Description], 
        GroupId, IsActive, EmailDefaultType, TextDefaulttype, PushNotificationDefaultType, WebNotificationDefaultType) 
    VALUES (
        204,
        'Driver Clear (call completed)',
        'Notification will be sent when a driver completes a call.',
        2, 1, 0, 0, 0, 0)

IF (NOT EXISTS(SELECT 1 FROM EventNotifications WHERE EventNotificationId = 205))
    INSERT INTO EventNotifications (
        EventNotificationId, 
        [Name], 
        [Description], 
        GroupId, IsActive, EmailDefaultType, TextDefaulttype, PushNotificationDefaultType, WebNotificationDefaultType) 
    VALUES (
        205,
        'When a call''s status changes to "Dispatched"',
        'Notification will be sent when call status changes to "Dispatched".',
        2, 0, 0, 0, 0, 0)

IF (NOT EXISTS(SELECT 1 FROM EventNotifications WHERE EventNotificationId = 206))
    INSERT INTO EventNotifications (
        EventNotificationId, 
        [Name], 
        [Description], 
        GroupId, IsActive, EmailDefaultType, TextDefaulttype, PushNotificationDefaultType, WebNotificationDefaultType) 
    VALUES (
        206,
        'When a call''s status changes to "Enroute"',
        'Notification will be sent when call status changes to "Enroute".',
        2, 0, 0, 0, 0, 0)

IF (NOT EXISTS(SELECT 1 FROM EventNotifications WHERE EventNotificationId = 207))
    INSERT INTO EventNotifications (
        EventNotificationId, 
        [Name], 
        [Description], 
        GroupId, IsActive, EmailDefaultType, TextDefaulttype, PushNotificationDefaultType, WebNotificationDefaultType) 
    VALUES (
        207,
        'When a call''s status changes to "On Scene"',
        'Notification will be sent when call status changes to "On Scene".',
        2, 1, 0, 0, 0, 0)

IF (NOT EXISTS(SELECT 1 FROM EventNotifications WHERE EventNotificationId = 208))
    INSERT INTO EventNotifications (
        EventNotificationId, 
        [Name], 
        [Description], 
        GroupId, IsActive, EmailDefaultType, TextDefaulttype, PushNotificationDefaultType, WebNotificationDefaultType) 
    VALUES (
        208,
        'When a call''s status changes to "Towing"',
        'Notification will be sent when call status changes to "Towing".',
        2, 1, 0, 0, 0, 0)

IF (NOT EXISTS(SELECT 1 FROM EventNotifications WHERE EventNotificationId = 209))
    INSERT INTO EventNotifications (
        EventNotificationId, 
        [Name], 
        [Description], 
        GroupId, IsActive, EmailDefaultType, TextDefaulttype, PushNotificationDefaultType, WebNotificationDefaultType) 
    VALUES (
        209,
        'When a call''s status changes to "Destination Arrival"',
        'Notification will be sent when call status changes to "Destination Arrival".',
        2, 1, 0, 0, 0, 0)

IF (NOT EXISTS(SELECT 1 FROM EventNotifications WHERE EventNotificationId = 210))
BEGIN
SET IDENTITY_INSERT EventNotifications ON
    INSERT INTO EventNotifications (
        EventNotificationId, 
        [Name], 
        [Description], 
        GroupId, IsActive, EmailDefaultType, TextDefaulttype, PushNotificationDefaultType, WebNotificationDefaultType) 
    VALUES (
        210,
        'When a call moves from Scheduled to Waiting',
        'Notification will be sent when call is moved from the scheduled call list to the waiting call list.',
        2, 1, 0, 0, 0, 0)
SET IDENTITY_INSERT EventNotifications OFF
END

-- Digital Dispatch -------------------------------------------------------------

IF (NOT EXISTS(SELECT 1 FROM EventNotifications WHERE EventNotificationId = 300))
    INSERT INTO EventNotifications (
        EventNotificationId, 
        [Name], 
        [Description], 
        GroupId, IsActive, EmailDefaultType, TextDefaulttype, PushNotificationDefaultType, WebNotificationDefaultType, 
        PushNotificationManagerRequired, PushNotificationDispatcherRequired, WebNotificationManagerRequired, WebNotificationDispatcherRequired) 
    VALUES (
        300,
        'When a digital offer is missed',
        'Notification will be sent when a digital offer is missed.',
        3, 1, 0, 0, 0, 0, 
        1, 1, 1, 1)

IF (NOT EXISTS(SELECT 1 FROM EventNotifications WHERE EventNotificationId = 301))
    INSERT INTO EventNotifications (
        EventNotificationId, 
        [Name], 
        [Description], 
        GroupId, IsActive, EmailDefaultType, TextDefaulttype, PushNotificationDefaultType, WebNotificationDefaultType, 
        PushNotificationManagerRequired, PushNotificationDispatcherRequired, WebNotificationManagerRequired, WebNotificationDispatcherRequired) 
    VALUES (
        301,
        'When a digital dispatch is refused',
        'Notification will be sent when a digital dispatch is refused (by an employee).',
        3, 1, 0, 0, 0, 0, 
        1, 1, 1, 1)

IF (NOT EXISTS(SELECT 1 FROM EventNotifications WHERE EventNotificationId = 301
	AND [Name] = 'When a digital dispatch is rejected (by a user)'))
	UPDATE EventNotifications SET
		[Name] = 'When a digital dispatch is rejected (by a user)',
		[Description] = 'Notification will be sent when a digital dispatch is rejected (by a user).'
	WHERE EventNotificationId = 301

IF (NOT EXISTS(SELECT 1 FROM EventNotifications WHERE EventNotificationId = 302))
    INSERT INTO EventNotifications (
        EventNotificationId, 
        [Name], 
        [Description], 
        GroupId, IsActive, EmailDefaultType, TextDefaulttype, PushNotificationDefaultType, WebNotificationDefaultType) 
    VALUES (
        302,
        'When a digital dispatch is awarded',
        'Notification will be sent when a digital dispatch is awarded.',
        3, 1, 0, 0, 0, 0)

IF (NOT EXISTS(SELECT 1 FROM EventNotifications WHERE EventNotificationId = 303))
    INSERT INTO EventNotifications (
        EventNotificationId, 
        [Name], 
        [Description], 
        GroupId, IsActive, EmailDefaultType, TextDefaulttype, PushNotificationDefaultType, WebNotificationDefaultType, 
        PushNotificationManagerRequired, PushNotificationDispatcherRequired, WebNotificationManagerRequired, WebNotificationDispatcherRequired) 
    VALUES (
        303,
        'When a digital dispatch is rejected',
        'Notification will be sent when a digital dispatch is rejected (by a motor club).',
        3, 1, 0, 0, 0, 0, 
        1, 1, 1, 1)

IF (NOT EXISTS(SELECT 1 FROM EventNotifications WHERE EventNotificationId = 303
	AND [Name] = 'When a digital dispatch is rejected (by a motor club)'))
	UPDATE EventNotifications SET
		[Name] = 'When a digital dispatch is rejected (by a motor club)',
		[Description] = 'Notification will be sent when a digital dispatch is rejected (by a motor club).'
	WHERE EventNotificationId = 303

IF (NOT EXISTS(SELECT 1 FROM EventNotifications WHERE EventNotificationId = 304))
    INSERT INTO EventNotifications (
        EventNotificationId, 
        [Name], 
        [Description], 
        GroupId, IsActive, EmailDefaultType, TextDefaulttype, PushNotificationDefaultType, WebNotificationDefaultType, 
        PushNotificationManagerRequired, PushNotificationDispatcherRequired, WebNotificationManagerRequired, WebNotificationDispatcherRequired) 
    VALUES (
        304,
        'When a call gets cancelled by a motor club',
        'Notification will be sent when a call gets cancelled by a motor club.',
        3, 1, 0, 0, 0, 0, 
        1, 1, 1, 1)

IF (NOT EXISTS(SELECT 1 FROM EventNotifications WHERE EventNotificationId = 305))
    INSERT INTO EventNotifications (
        EventNotificationId, 
        [Name], 
        [Description], 
        GroupId, IsActive, EmailDefaultType, TextDefaulttype, PushNotificationDefaultType, WebNotificationDefaultType) 
    VALUES (
        305,
        'When a call gets modified by a motor club',
        'Notification will be sent when a call gets modified by a motor club (via email).',
        3, 1, 0, 0, 0, 0)

IF (NOT EXISTS(SELECT 1 FROM EventNotifications WHERE EventNotificationId = 305 AND IsActive = 0))
	UPDATE EventNotifications SET
		IsActive = 0
	WHERE EventNotificationId = 305

-- Equipment Inspection ---------------------------------------------------------

IF (NOT EXISTS(SELECT 1 FROM EventNotifications WHERE EventNotificationId = 500))
    INSERT INTO EventNotifications (
        EventNotificationId, 
        [Name], 
        [Description], 
        GroupId, IsActive, EmailDefaultType, TextDefaulttype, PushNotificationDefaultType, WebNotificationDefaultType, 
        PushNotificationManagerRequired, WebNotificationManagerRequired) 
    VALUES (
        500,
        'Equipment Inspection failure',
        'Notification will be sent when an Equipment Inspection form is submitted with one or more failures.',
        5, 1, 0, 0, 0, 0, 
        1, 1)

SET IDENTITY_INSERT EventNotifications OFF
GO

-- Roadside/Surveys ---------------------------------------------------------

IF (NOT EXISTS(SELECT 1 FROM EventNotifications WHERE EventNotificationId = 800))
BEGIN
  SET IDENTITY_INSERT EventNotifications ON
    INSERT INTO EventNotifications (
        EventNotificationId, 
        [Name], 
        [Description], 
        GroupId, IsActive, EmailDefaultType, TextDefaulttype, PushNotificationDefaultType, WebNotificationDefaultType, 
        EmailManagerRequired, EmailDispatcherRequired, WebNotificationManagerRequired, WebNotificationDispatcherRequired) 
    VALUES (
        800,
        'When a customer survey rating is two or less', 
        'Send a notification when a customer submits a survey with a low rating.',
        8, 1, 0, 0, 0, 0, 
        1, 0, 1, 0)

  SET IDENTITY_INSERT EventNotifications OFF
END
GO





IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='EventUserTypeNotifications')
BEGIN
    CREATE TABLE [dbo].[EventUserTypeNotifications] (
        [EventUserTypeNotificationId] int IDENTITY(1,1) NOT NULL,
        [EventNotificationId] int NOT NULL,
        [UserTypeId] SMALLINT NULL,        
        [CompanyId] INT NOT NULL,
        [RequireEmail] BIT NULL,
        [RequireText] BIT NULL,
        [RequirePushNotification] BIT NULL,
        [RequireWebNotification] BIT NULL,
        [OwnerUserId] INT NOT NULL,
        [CreateDate] datetime NOT NULL,
        [Deleted] BIT NOT NULL DEFAULT(0),
        [DeletedByUserId] INT NULL,
        [DeleteDate] datetime NULL

        CONSTRAINT PK_EventUserTypeNotifications PRIMARY KEY CLUSTERED ([EventUserTypeNotificationId] ASC),
        CONSTRAINT FK_EventUserTypeNotifications_EventNotifications FOREIGN KEY(EventNotificationId) REFERENCES dbo.EventNotifications (EventNotificationId),
        CONSTRAINT FK_EventUserTypeNotifications_Company FOREIGN KEY(CompanyId) REFERENCES Companies (CompanyId),
        
    )
    CREATE INDEX [IX_EventUserTypeNotifications_EventUserTypeNotificationId] ON [dbo].[EventUserTypeNotifications] ([EventUserTypeNotificationId])
END

GRANT SELECT ON EventUserTypeNotifications to public
GRANT INSERT ON EventUserTypeNotifications to public
GRANT UPDATE ON EventUserTypeNotifications to public

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='EventUserNotifications')
BEGIN
    CREATE TABLE [dbo].[EventUserNotifications] (
        [EventUserNotificationId] int IDENTITY(1,1) NOT NULL,
        [EventNotificationId] int NOT NULL,
        [UserId] INT NULL,
        [CompanyId] INT NOT NULL,
        [RequireEmail] BIT NULL,
        [RequireText] BIT NULL,
        [RequirePushNotification] BIT NULL,
        [RequireWebNotification] BIT NULL,
        [OwnerUserId] INT NOT NULL,
        [CreateDate] datetime NOT NULL,
        [Deleted] BIT NOT NULL DEFAULT(0),
        [DeletedByUserId] INT NULL,
        [DeleteDate] datetime NULL

        CONSTRAINT PK_EventUserNotifications PRIMARY KEY CLUSTERED ([EventUserNotificationId] ASC),
        CONSTRAINT FK_EventUserNotifications_EventNotifications FOREIGN KEY(EventNotificationId) REFERENCES dbo.EventNotifications (EventNotificationId),
        CONSTRAINT FK_EventUserNotifications_User FOREIGN KEY(UserId) REFERENCES Users (UserId),        
        CONSTRAINT FK_EventUserNotifications_Company FOREIGN KEY(CompanyId) REFERENCES Companies (CompanyId),       
    )
    CREATE INDEX [IX_EventUserNotifications_EventUserNotificationId] ON [dbo].[EventUserNotifications] ([EventUserNotificationId])
END
GO

GRANT SELECT ON EventUserNotifications to public
GRANT INSERT ON EventUserNotifications to public
GRANT UPDATE ON EventUserNotifications to public




IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='EventNotificationAlertSounds')
BEGIN
    CREATE TABLE [dbo].[EventNotificationAlertSounds] (
        [EventNotificationAlertSoundId] int IDENTITY(1,1) NOT NULL,
        [EventNotificationId] int NOT NULL,
        [CompanyId] INT NOT NULL,
        [UserId] INT,
        [EventNotificationSoundId] INT NOT NULL,
        [IsDeleted] BIT NOT NULL DEFAULT(0)
    )
    CREATE INDEX [IX_EventNotificationAlertSounds_CompanyUser] ON [dbo].[EventNotificationAlertSounds] ([CompanyId], [UserId])
END
GO

GRANT SELECT ON EventNotificationAlertSounds to public
GRANT INSERT ON EventNotificationAlertSounds to public
GRANT UPDATE ON EventNotificationAlertSounds to public






IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='DispatchEntryVideos')
BEGIN

CREATE TABLE dbo.DispatchEntryVideos(
	DispatchEntryVideoId int IDENTITY(1,1) NOT NULL,
	DispatchEntryId int NOT NULL,
	ContentType varchar(50) NOT NULL,
	Description varchar(100) NULL,
	CreateDate datetime NOT NULL DEFAULT(getdate()),
	RemoteIp varchar(46) NULL,
	OwnerUserId int NULL,
	FileSize bigint NULL DEFAULT(0),
	IsDeleted bit NOT NULL DEFAULT(0),
	Latitude decimal(9, 6) NULL,
	Longitude decimal(9, 6) NULL,

	CONSTRAINT PK_DispatchEntryVideos PRIMARY KEY CLUSTERED (DispatchEntryVideoId ASC)
)
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='DispatchEntryDamageVideos')
BEGIN

CREATE TABLE dbo.DispatchEntryDamageVideos(
	DispatchEntryDamageVideoId int IDENTITY(1,1) NOT NULL,
	DispatchEntryDamageId int NOT NULL,
    DispatchEntryDamageRegionId int NULL,
	ContentType varchar(50) NOT NULL,
	Description varchar(100) NULL,
	CreateDate datetime NOT NULL DEFAULT(getdate()),
	RemoteIp varchar(46) NULL,
	OwnerUserId int NULL,
	FileSize bigint NULL DEFAULT(0),
	IsDeleted bit NOT NULL DEFAULT(0),
	Latitude decimal(9, 6) NULL,
	Longitude decimal(9, 6) NULL,

	CONSTRAINT PK_DispatchEntryDamageVideos PRIMARY KEY CLUSTERED (DispatchEntryDamageVideoId ASC)
)
END
GO

/*************************************************************************************************
 ** USER CHECKIN
 *************************************************************************************************/
 

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='UserCheckInActivityLog')
BEGIN

	CREATE TABLE dbo.UserCheckInActivityLog(
		UserCheckInActivityLogId int IDENTITY(1,1) NOT NULL,
		UserId int NOT NULL,
		IsCheckedIn bit NOT NULL DEFAULT(0),
		CreateDate datetime NOT NULL DEFAULT(getdate()),
		RemoteIp varchar(46) NULL,
		Latitude decimal(9, 6) NULL,
		Longitude decimal(9, 6) NULL,

		CONSTRAINT PK_UserCheckInActivityLog PRIMARY KEY CLUSTERED (UserCheckInActivityLogId ASC),
		CONSTRAINT FK_UserCheckInActivityLog_User FOREIGN KEY(UserId) REFERENCES Users (UserId)
	)

	CREATE INDEX [IX_UserCheckInActivityLog_UserId] ON [dbo].[UserCheckInActivityLog] ([UserId])

END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='UserCheckInStatuses')
BEGIN

	CREATE TABLE dbo.UserCheckInStatuses(
		UserCheckInStatusId int IDENTITY(1,1) NOT NULL,
		UserId int NOT NULL,
		CompanyId int NOT NULL,
		IsCheckedIn bit NOT NULL DEFAULT(0),
		LastUpdateDate datetime NOT NULL DEFAULT(getdate()),
	
		CONSTRAINT PK_UserCheckInStatuses PRIMARY KEY CLUSTERED (UserCheckInStatusId ASC),
		CONSTRAINT FK_UserCheckInStatuses_User FOREIGN KEY(UserId) REFERENCES Users (UserId),
		CONSTRAINT FK_UserCheckInStatuses_Company FOREIGN KEY(CompanyId) REFERENCES Companies (CompanyId)
	)

	CREATE INDEX [IX_UserCheckInStatuses_UserId] ON [dbo].[UserCheckInStatuses] ([UserId])
END
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='CompanyLogos')
BEGIN

CREATE TABLE dbo.CompanyLogos (
	CompanyLogoId int IDENTITY(1,1) NOT NULL,
	CompanyId int NOT NULL,
        OwnerUserId int NOT NULL,
	CreateDate datetime NOT NULL DEFAULT(getdate()),
	RemoteIp varchar(46) NULL,
	IsUploaded bit NOT NULL DEFAULT(0),
	IsDeleted bit NOT NULL DEFAULT(0),

	CONSTRAINT PK_CompanyLogos PRIMARY KEY CLUSTERED (CompanyLogoId ASC)
)

CREATE INDEX [IX_CompanyLogos_CompanyId] ON [dbo].[CompanyLogos] (CompanyId)
CREATE INDEX [IX_CompanyLogos_Active] ON [dbo].[CompanyLogos] (CompanyId, IsUploaded, IsDeleted)
GRANT INSERT ON CompanyLogos to public
GRANT SELECT ON CompanyLogos to public
GRANT UPDATE ON CompanyLogos to public

END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='InvoiceItemDriverCommissions')
BEGIN

CREATE TABLE dbo.InvoiceItemDriverCommissions (
	InvoiceItemDriverCommissionId int IDENTITY(1,1) NOT NULL,
	InvoiceId int NOT NULL,
	InvoiceItemId int NOT NULL,
	DriverId int NOT NULL,
	Amount money NOT NULL default(0),
	CreateDate datetime NOT NULL DEFAULT(getdate()),

	CONSTRAINT PK_InvoiceItemDriverCommissions PRIMARY KEY CLUSTERED (InvoiceItemDriverCommissionId ASC)
)


CREATE INDEX IX_InvoiceItemDriverCommissions_InvoiceItemId ON dbo.InvoiceItemDriverCommissions (InvoiceItemId)
CREATE INDEX IX_InvoiceItemDriverCommissions_InvoiceId ON dbo.InvoiceItemDriverCommissions (InvoiceId)
CREATE INDEX IX_InvoiceItemDriverCommissions_DriverId ON dbo.InvoiceItemDriverCommissions (DriverId)

CREATE UNIQUE NONCLUSTERED INDEX UX_InvoiceItemDriverCommissions_UniqueDriver ON  dbo.InvoiceItemDriverCommissions (InvoiceId, InvoiceItemId, DriverId)

GRANT INSERT ON InvoiceItemDriverCommissions to public
GRANT SELECT ON InvoiceItemDriverCommissions to public
GRANT UPDATE ON InvoiceItemDriverCommissions to public


END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='InvoiceItemDriverCommissions' AND COLUMN_NAME = 'DriverBase')
	alter table InvoiceItemDriverCommissions
		add DriverBase money NOT NULL default(0)



------------------------------------------------------------*/
 


/*************************************************************************************************
 ** Stripe Payments/Connect integrations
 *************************************************************************************************/

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='StripeConnectSessions' AND TABLE_SCHEMA='Integration')
BEGIN

    CREATE TABLE Integration.StripeConnectSessions (
        ConnectSessionId	INT IDENTITY (1, 1) NOT NULL,
        CompanyId			INT NOT NULL,
		AccountId			INT NULL,
		StripeUserId		VARCHAR(75) NOT NULL,
		AccessToken			VARCHAR(75) NOT NULL,
		RefreshToken		VARCHAR(75) NULL,
        PublishableKey      VARCHAR(75) NULL,
		OwnerUserId			INT NOT NULL,
        CreateDate			DATETIME NOT NULL DEFAULT GETDATE(),
		Revoked				BIT NOT NULL DEFAULT(0),
		RevokedByUserId		INT NULL,
		RevokedDate			DATETIME NULL,

        CONSTRAINT PK_StripeConnectSessions PRIMARY KEY CLUSTERED (ConnectSessionId),
        CONSTRAINT FK_StripeConnectSessions_Companies FOREIGN KEY (CompanyId) REFERENCES dbo.Companies (CompanyId),
        CONSTRAINT FK_StripeConnectSessions_Accounts FOREIGN KEY (AccountId) REFERENCES dbo.Accounts (AccountId)
    )


	CREATE INDEX IX_StripeConnectSessions_CompanyId ON Integration.StripeConnectSessions (CompanyId)

	GRANT INSERT ON Integration.StripeConnectSessions to public
	GRANT SELECT ON Integration.StripeConnectSessions to public
	GRANT UPDATE ON Integration.StripeConnectSessions to public
END
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='StripeTransactions' AND TABLE_SCHEMA='Integration')
BEGIN

CREATE TABLE Integration.StripeTransactions (
        StripeTransactionId	INT IDENTITY (1, 1) NOT NULL,
        CompanyId			INT NOT NULL,
        AccountId			INT NOT NULL,
        StripeConnectSessionId INT NOT NULL,
        StripePaymentIntentId VARCHAR(256) NOT NULL,
        StripePaymentIntentClientSecret VARCHAR(256) NOT NULL,
        IdempotencyKey      VARCHAR(256) NOT NULL,
        RequestId           INT NULL,
        PermitUserId        INT NULL,
        TenantName          VARCHAR(150) NULL,
        UnitNumber          VARCHAR(150) NULL,
        CaptureMethod       VARCHAR(25) NOT NULL,
        PreAuthAmount       INT NULL, --in cents
        CaptureAmount       INT NULL, --in cents
        AccountParkingPermitApprovalSessionOrderId INT NULL,
        CreateDate			DATETIME NOT NULL,
        UpdateDate			DATETIME NOT NULL,
        Deleted				bit NOT NULL Default(0)

        CONSTRAINT PK_StripeTransactions PRIMARY KEY CLUSTERED (StripeTransactionId),
        CONSTRAINT FK_StripeTransactions_Companies FOREIGN KEY (CompanyId) REFERENCES dbo.Companies (CompanyId),
        CONSTRAINT FK_StripeTransactions_Accounts FOREIGN KEY (AccountId) REFERENCES dbo.Accounts (AccountId),
        CONSTRAINT FK_StripeTransactions_PermitUsers FOREIGN KEY (PermitUserId) REFERENCES PPIO.PermitUsers (UserId),
        CONSTRAINT FK_StripeTransactions_ConnectSessions FOREIGN KEY (StripeConnectSessionId) REFERENCES integration.StripeConnectSessions (ConnectSessionId),
        CONSTRAINT FK_StripeTransactions_Requests FOREIGN KEY (RequestId) REFERENCES dbo.AccountParkingPermitRequests (ParkingPermitRequestId),
        CONSTRAINT FK_StripeTransactions_SessionOrders FOREIGN KEY (AccountParkingPermitApprovalSessionOrderId) REFERENCES dbo.AccountParkingPermitApprovalSessionOrders (ApprovalSessionOrderId)
    )

    CREATE INDEX IX_Transactions_ByParkingPermitUser ON Integration.StripeTransactions (AccountId, PermitUserId)
    CREATE INDEX IX_Transactions_ByParkingPermitVisitor ON Integration.StripeTransactions (AccountId, TenantName, UnitNumber)
    CREATE INDEX IX_Transactions_ByPaymentIntents ON Integration.StripeTransactions (StripePaymentIntentId)

	GRANT INSERT ON Integration.StripeTransactions to public
	GRANT SELECT ON Integration.StripeTransactions to public
	GRANT UPDATE ON Integration.StripeTransactions to public
END
GO



IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='VoiceDispatches' AND TABLE_SCHEMA='dbo')
BEGIN

CREATE TABLE dbo.VoiceDispatches (
	VoiceDispatchId	bigint IDENTITY(1,1) NOT NULL,
	PublicId		uniqueidentifier NOT NULL,
	Type			int NOT NULL,
	CompanyId		int NOT NULL,
	UserId			int,
	PhoneNumber		varchar(21),
	StatusId  		int,
	ReferenceId		bigint,
	ReferenceData	varchar(8000),
	CreateDate		datetime NOT NULL default(getdate()),
	
    CONSTRAINT PK_VoiceDispatches PRIMARY KEY CLUSTERED (VoiceDispatchId),
	CONSTRAINT FK_VoiceDispatches_Companies FOREIGN KEY (CompanyId) REFERENCES dbo.Companies (CompanyId)
)

GRANT SELECT ON VoiceDispatches TO public
GRANT INSERT ON VoiceDispatches TO public
GRANT UPDATE ON VoiceDispatches TO public

END




IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='DispatchEntryLocks')
BEGIN
	CREATE TABLE dbo.DispatchEntryLocks (
		DispatchEntryLockId int IDENTITY(1,1) NOT NULL,
		DispatchEntryId int NOT NULL,
		LockedByUserId int NOT NULL,
		LockDate datetime NOT NULL DEFAULT(getdate()),
		UnlockedByUserId int,
		UnlockDate datetime,

		CONSTRAINT PK_DispatchEntryLocks PRIMARY KEY CLUSTERED (DispatchEntryLockId ASC)
	)

	CREATE INDEX [IX_DispatchEntryLocks_DispatchEntryId] ON [dbo].[DispatchEntryLocks] (DispatchEntryId)
END
GO



IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='CompanyDispatchStatusesCurrent')
BEGIN
	CREATE TABLE dbo.CompanyDispatchStatusesCurrent (
		CompanyDispatchStatusCurrentId int IDENTITY(1,1) NOT NULL,
		CompanyId int NOT NULL,
		IsActive bit NOT NULL default(0),
		LastChanged datetime default(getdate()),
		Reasons varchar(8000)

		CONSTRAINT PK_CompanyDispatchStatusesCurrent PRIMARY KEY CLUSTERED (CompanyDispatchStatusCurrentId ASC),
		CONSTRAINT [UX_CompanyDispathStatusesCurrent_CompanyId] UNIQUE (CompanyId)
	)

	GRANT SELECT on CompanyDispatchStatusesCurrent to public
	GRANT UPDATE on CompanyDispatchStatusesCurrent to public
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='CompanyDispatchStatusesCurrent' AND COLUMN_NAME = 'ForceDown')
    ALTER TABLE CompanyDispatchStatusesCurrent 
        ADD ForceDown bit NOT NULL default(0) 


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='CompanyMessagingServices')
BEGIN
    CREATE TABLE dbo.CompanyMessagingServices (
        CompanyMessagingServiceId int IDENTITY(1,1) NOT NULL,
        CompanyId       int NOT NULL,
        ParentCompanyId int,
        ServiceSid      varchar(40),
        IsActive        bit NOT NULL default(1),
        CreateDate      datetime default(getdate())

        CONSTRAINT PK_CompaniesMessagingServices PRIMARY KEY CLUSTERED (CompanyMessagingServiceId ASC),
        CONSTRAINT [UX_CompaniesMessagingServices_CompanyId] UNIQUE (CompanyId)        
    )

    GRANT SELECT on CompanyMessagingServices to public
    GRANT UPDATE on CompanyMessagingServices to public
END
GO

IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS WHERE CONSTRAINT_NAME ='FK_ChatDispatchEntries_Chats')
    ALTER TABLE ChatDispatchEntries DROP CONSTRAINT FK_ChatDispatchEntries_Chats


	
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='AutoAcceptRules')
BEGIN
	CREATE TABLE MCDispatch.AutoAcceptRules (
		AutoAcceptRuleId int IDENTITY(1,1) NOT NULL,
		CompanyId int NULL,
		AccountId int NULL,
		Zip varchar(5) NOT NULL, 
		Eta int NOT NULL, 
		Type int NOT NULL DEFAULT(0),
		OwnerUserId int, 
		CreateDate datetime DEFAULT(getdate()),
		IsDeleted bit NOT NULL default(0),

		CONSTRAINT PK_AutoAcceptRules PRIMARY KEY CLUSTERED (AutoAcceptRuleId ASC)
	)

	GRANT SELECT on MCDispatch.AutoAcceptRules to public
	GRANT UPDATE on MCDispatch.AutoAcceptRules to public
	GRANT INSERT on MCDispatch.AutoAcceptRules to public
END
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE WHERE  CONSTRAINT_NAME = 'PK_RoadsideDispatchId')
 ALTER TABLE [Roadside].[Dispatches] ADD CONSTRAINT [PK_RoadsideDispatchId] PRIMARY KEY CLUSTERED ([RoadsideDispatchId])
GO


IF (NOT EXISTS(SELECT 1 FROM sys.indexes WHERE name='IX_ChatMembers_ChatId'))
BEGIN			
	CREATE NONCLUSTERED INDEX IX_ChatMembers_ChatId ON dbo.[ChatMembers] (ChatId)
END


IF (NOT EXISTS(SELECT 1 FROM sys.indexes WHERE name='IX_ChatMessageDeliveries_MessageId_UserId'))
BEGIN			
	CREATE NONCLUSTERED INDEX IX_ChatMessageDeliveries_MessageId_UserId ON dbo.[ChatMessageDeliveries] (ChatMessageId, UserId)
END


IF (NOT EXISTS(SELECT 1 FROM sys.indexes WHERE name='IX_RoadsideDispatches_CompanyId'))
BEGIN			
	CREATE NONCLUSTERED INDEX IX_RoadsideDispatches_CompanyId ON [Roadside].[Dispatches] (CompanyId)
END

IF (NOT EXISTS(SELECT 1 FROM sys.indexes WHERE name='IX_RoadsideDispatches_DispatchEntryId'))
BEGIN			
	CREATE NONCLUSTERED INDEX IX_RoadsideDispatches_DispatchEntryId ON [Roadside].[Dispatches] (DispatchEntryId)
END

IF (NOT EXISTS(SELECT 1 FROM sys.indexes WHERE name='IX_RoadsideDispatchMessages_UserId_CallStatus'))
BEGIN			
	CREATE NONCLUSTERED INDEX IX_RoadsideDispatchMessages_UserId_CallStatus ON [Roadside].[DispatchMessages] (RoadsideDispatchUserId, CallStatus)
END

IF (NOT EXISTS(SELECT 1 FROM sys.indexes WHERE name='IX_RoadsideDispatchMessages_UserId_TextAlertItemId'))
BEGIN			
	CREATE NONCLUSTERED INDEX IX_RoadsideDispatchMessages_UserId_TextAlertItemId ON [Roadside].[DispatchMessages] (RoadsideDispatchUserId, JobProgressTextAlertItemId)
END	

IF (NOT EXISTS(SELECT 1 FROM sys.indexes WHERE name='IX_RoadsideSurveyResponses_DispatchId'))
BEGIN			
	CREATE NONCLUSTERED INDEX IX_RoadsideSurveyResponses_DispatchId ON [Roadside].[SurveyResponses] (RoadsideDispatchId)
END	


IF (NOT EXISTS(SELECT 1 FROM sys.indexes WHERE name='IX_RoadsideDispatchUsers_DispatchId'))
BEGIN			
	CREATE NONCLUSTERED INDEX IX_RoadsideDispatchUsers_DispatchId ON [Roadside].[DispatchUsers] (RoadsideDispatchId)
END

IF (NOT EXISTS(SELECT 1 FROM sys.indexes WHERE name='IX_RoadsideDispatchUsers_ContactId'))
BEGIN			
	CREATE NONCLUSTERED INDEX IX_RoadsideDispatchUsers_ContactId ON [Roadside].[DispatchUsers] (DispatchEntryContactId)
END

IF (NOT EXISTS(SELECT 1 FROM sys.indexes WHERE name='IX_RoadsideJobProgressTextAlertItems_CompanyId_StatusTypeId'))
BEGIN			
	CREATE NONCLUSTERED INDEX IX_RoadsideJobProgressTextAlertItems_CompanyId_StatusTypeId ON [Roadside].[JobProgressTextAlertItems] (CompanyId, StatusTypeId)
END

IF (NOT EXISTS(SELECT 1 FROM sys.indexes WHERE name='IX_RoadsideSettings_CompanyId'))
BEGIN			
	CREATE NONCLUSTERED INDEX IX_RoadsideSettings_CompanyId ON [Roadside].[Settings] (CompanyId)
END

IF (NOT EXISTS(SELECT 1 FROM sys.indexes WHERE name='IX_RoadsideSurveyQuestions_CompanyId'))
BEGIN			
	CREATE NONCLUSTERED INDEX IX_RoadsideSurveyQuestions_CompanyId ON [Roadside].[SurveyQuestions] (CompanyId)
END

IF (NOT EXISTS(SELECT 1 FROM sys.indexes WHERE name='IX_RoadsideSurveyResponses_DispatchId'))
BEGIN			
	CREATE NONCLUSTERED INDEX IX_RoadsideSurveyResponses_DispatchId ON [Roadside].[SurveyResponses] (RoadsideDispatchId)
END

IF (NOT EXISTS(SELECT 1 FROM sys.indexes WHERE name='IX_RoadsideSurveyResponses_UserId'))
BEGIN			
	CREATE NONCLUSTERED INDEX IX_RoadsideSurveyResponses_UserId ON [Roadside].[SurveyResponses] (RoadsideDispatchUserId)
END

IF (NOT EXISTS(SELECT 1 FROM sys.indexes WHERE name='IX_SurveyResponseWebComponentItems_CompanyId'))
BEGIN			
	CREATE NONCLUSTERED INDEX IX_SurveyResponseWebComponentItems_CompanyId ON [Roadside].[SurveyResponseWebComponentItems] (CompanyId)
END

IF (NOT EXISTS(SELECT 1 FROM sys.indexes WHERE name='IX_SurveyResponseWebComponentItems_SurveyResponseId'))
BEGIN			
	CREATE NONCLUSTERED INDEX IX_SurveyResponseWebComponentItems_SurveyResponseId ON [Roadside].[SurveyResponseWebComponentItems] (SurveyResponseId)
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='DispatchEntryMessagingChannels')
BEGIN
    CREATE TABLE [dbo].[DispatchEntryMessagingChannels] (
        [DispatchEntryMessagingChannelId] INT IDENTITY (1, 1) NOT NULL,
        [CompanyId]                 INT NOT NULL,
        [DispatchEntryId]           INT NOT NULL,
        [ChannelSid]                VARCHAR(40) NOT NULL,
        [UniqueName]                VARCHAR(120) NOT NULL,
        [FriendlyName]              VARCHAR(50),
        [CreateDate]                DATETIME,
        [CreatedBy]                 VARCHAR(32),
        [Members]                   VARCHAR(2000),

        CONSTRAINT PK_DispatchEntryMessagingChannels PRIMARY KEY CLUSTERED (DispatchEntryMessagingChannelId ASC)
    )
END
GO

IF (NOT EXISTS(SELECT 1 FROM sys.indexes WHERE name='IX_DispatchEntryMessagingChannels_DispatchEntryId'))
BEGIN
    CREATE NONCLUSTERED INDEX [IX_DispatchEntryMessagingChannels_DispatchEntryId]
        ON [dbo].[DispatchEntryMessagingChannels] ([DispatchEntryId])

END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryAssetDrivers' AND COLUMN_NAME = 'CurrentWaypointId')
    ALTER TABLE DispatchEntryAssetDrivers ADD CurrentWaypointId int
GO

/***************************
// Impound Release rules
***************************/


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='ImpoundReleaseValidationRuleSets')
    CREATE TABLE dbo.ImpoundReleaseValidationRuleSets (
        ImpoundReleaseValidationRuleSetId int IDENTITY (1, 1) NOT NULL,
        CompanyId  int NOT NULL,
        MasterAccountId int,
        AccountId int,
		RequireReleaseMethod int NOT NULL DEFAULT(0),
		RequireName int NOT NULL DEFAULT(0),
		RequirePhone int NOT NULL DEFAULT(0),
		RequireAddress int NOT NULL DEFAULT(0),
		RequireDriversLicense int NOT NULL DEFAULT(0),
		RequireDriversLicenseExpiration int NOT NULL DEFAULT(0),
		RequireBirthDate int NOT NULL DEFAULT(0),
		RequirePaymentInFull int NOT NULL DEFAULT(0),
        OwnerUserId int,
        CreateDate datetime NOT NULL default(GETDATE()),
        IsDeleted bit NOT NULL default (0),
        DeletedTime datetime,
        DeletedUserId int

        CONSTRAINT [PK_ImpoundReleaseValidationRuleSets] PRIMARY KEY CLUSTERED (ImpoundReleaseValidationRuleSetId ASC),
        CONSTRAINT [FK_ImpoundReleaseValidationRuleSets_Users] FOREIGN KEY(OwnerUserId) REFERENCES dbo.Users (UserId),
        CONSTRAINT [FK_ImpoundReleaseValidationRuleSets_UserDeleter] FOREIGN KEY(DeletedUserId) REFERENCES dbo.Users (UserId),
        CONSTRAINT [FK_ImpoundReleaseValidationRuleSets_Accounts] FOREIGN KEY(AccountId) REFERENCES dbo.Accounts (AccountId),
        CONSTRAINT [FK_ImpoundReleaseValidationRuleSets_Companies] FOREIGN KEY(CompanyId) REFERENCES dbo.Companies (CompanyId)
    )
GO

GRANT SELECT ON ImpoundReleaseValidationRuleSets TO public
GRANT INSERT ON ImpoundReleaseValidationRuleSets TO public
GRANT UPDATE ON ImpoundReleaseValidationRuleSets TO public
GO



IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='SquareCompanyAuthorizations' AND COLUMN_NAME = 'RefreshToken')
    ALTER TABLE Integration.SquareCompanyAuthorizations ADD RefreshToken VARCHAR (256)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='SquareCompanyAuthorizations' AND COLUMN_NAME = 'InvalidationDate')
    ALTER TABLE Integration.SquareCompanyAuthorizations ADD InvalidationDate DATETIME
GO
      
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='AutoDataDirectConnections')
    CREATE TABLE Integration.AutoDataDirectConnections (
        AutoDataDirectConnectionId int IDENTITY (1, 1) NOT NULL,
        CompanyId  int NOT NULL,
		OwnerUserId int NOT NULL,
		IsDeleted bit NOT NULL DEFAULT(0),
		ExpiresNext datetime,
		CreateDate datetime default(getdate()),
		LastUpdated datetime default(getdate()),

        CONSTRAINT [FK_AutoDataDirectConnections_Users] FOREIGN KEY(OwnerUserId) REFERENCES dbo.Users (UserId),
        CONSTRAINT [FK_AutoDataDirectConnections_Companies] FOREIGN KEY(CompanyId) REFERENCES dbo.Companies (CompanyId)
    )
GO

GRANT SELECT ON  Integration.AutoDataDirectConnections TO public
GRANT INSERT ON  Integration.AutoDataDirectConnections TO public
GRANT UPDATE ON  Integration.AutoDataDirectConnections TO public
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='DispatchEntryTowouts')
    CREATE TABLE dbo.DispatchEntryTowouts (
		DispatchEntryTowoutId int IDENTITY (1, 1) NOT NULL,
        DispatchEntryId  int NOT NULL,
		TowInCompletionTime datetime NOT NULL,
		TowInDispatchTime datetime,
		TowInEnrouteTime datetime,
		TowInArrivalTime datetime,
		TowInTowTime datetime,
		TowInDestinationArrivalTime datetime

        CONSTRAINT [FK_DispatchEntries] FOREIGN KEY(DispatchEntryId) REFERENCES dbo.DispatchEntries (DispatchEntryId),
    )
GO

GRANT SELECT ON  dbo.DispatchEntryTowouts TO public
GRANT INSERT ON  dbo.DispatchEntryTowouts TO public
GRANT UPDATE ON  dbo.DispatchEntryTowouts TO public

GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryTowouts' AND COLUMN_NAME = 'AssetId')
    ALTER TABLE DispatchEntryTowouts ADD AssetId int

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryTowouts' AND COLUMN_NAME = 'IsDeleted')
    ALTER TABLE DispatchEntryTowouts ADD IsDeleted BIT DEFAULT(0)

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryTowouts' AND COLUMN_NAME = 'DeletedByUserId')
    ALTER TABLE DispatchEntryTowouts ADD DeletedByUserId INT

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryTowouts' AND COLUMN_NAME = 'DeletedDate')
    ALTER TABLE DispatchEntryTowouts ADD DeletedDate DATETIME

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryTowouts' AND COLUMN_NAME = 'CreateDate')
    ALTER TABLE DispatchEntryTowouts ADD CreateDate DATETIME NOT NULL DEFAULT(GetDate())

IF (NOT EXISTS(SELECT 1 FROM sys.indexes WHERE name='IX_DispatchEntryTowouts_DispatchEntryId'))
BEGIN
    CREATE NONCLUSTERED INDEX [IX_DispatchEntryTowouts_DispatchEntryId]
        ON [dbo].[DispatchEntryTowouts] ([DispatchEntryId])

END

IF (NOT EXISTS(SELECT 1 FROM sys.indexes WHERE name='IX_DispatchEntryTowouts_AssetId'))
BEGIN
    CREATE NONCLUSTERED INDEX [IX_DispatchEntryTowouts_AssetId]
        ON [dbo].[DispatchEntryTowouts] ([AssetId])
END


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='AccountUsers')
    CREATE TABLE dbo.AccountUsers (
        AccountUserId int IDENTITY (1, 1) NOT NULL,
        UserId  int NOT NULL,
		AccountId int NOT NULL,
		CreateDate datetime default(getdate())
    )
GO
GRANT SELECT ON AccountUsers TO public

IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='SquareCompanyAuthorizations' AND TABLE_SCHEMA='Integration' AND COLUMN_NAME = 'LocationId')
ALTER TABLE [Integration].[SquareCompanyAuthorizations] DROP COLUMN LocationId;

IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='SquarePayments' AND TABLE_SCHEMA='Integration' AND COLUMN_NAME = 'ReferenceType')
ALTER TABLE [Integration].[SquarePayments] DROP COLUMN ReferenceType;

IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='SquarePayments' AND TABLE_SCHEMA='Integration' AND COLUMN_NAME = 'ReferenceId')
ALTER TABLE [Integration].[SquarePayments] DROP COLUMN ReferenceId;

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='SquarePayments' AND COLUMN_NAME = 'InvoiceId')
ALTER TABLE [Integration].[SquarePayments] ADD InvoiceId INT

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='SquarePayments' AND COLUMN_NAME = 'InvoicePaymentId')
ALTER TABLE [Integration].[SquarePayments] ADD InvoicePaymentId INT

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='SquarePayments' AND COLUMN_NAME = 'PaymentLinkId')
ALTER TABLE [Integration].[SquarePayments] ADD PaymentLinkId INT

IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='SquareRefunds' AND TABLE_SCHEMA='Integration' AND COLUMN_NAME = 'ReferenceType')
ALTER TABLE [Integration].[SquareRefunds] DROP COLUMN ReferenceType;

IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='SquareRefunds' AND TABLE_SCHEMA='Integration' AND COLUMN_NAME = 'ReferenceId')
ALTER TABLE [Integration].[SquareRefunds] DROP COLUMN ReferenceId;

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='SquareRefunds' AND COLUMN_NAME = 'InvoiceId')
ALTER TABLE [Integration].[SquareRefunds] ADD InvoiceId INT

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='SquareRefunds' AND COLUMN_NAME = 'InvoicePaymentId')
ALTER TABLE [Integration].[SquareRefunds] ADD InvoicePaymentId INT

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='SquareRefunds' AND COLUMN_NAME = 'PaymentLinkId')
ALTER TABLE [Integration].[SquareRefunds] ADD PaymentLinkId INT

/*
 * OON - Agero
 */

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='OonAgeroDispatches')
    CREATE TABLE MCDispatch.OonAgeroDispatches (
        OonAgeroDispatchId int IDENTITY (1, 1) NOT NULL,
        DispatchId varchar(50), 
        CompanyId int,
        CallRequestId int,
        DispatchJson varchar(max), 
        Eta int null,
		CreateDate datetime default(getdate())
    )
GO
GRANT SELECT ON  MCDispatch.OonAgeroDispatches  TO public
GRANT INSERT ON  MCDispatch.OonAgeroDispatches  TO public
GRANT UPDATE ON  MCDispatch.OonAgeroDispatches  TO public


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='OonAgeroDispatches' AND COLUMN_NAME = 'Eta')
    ALTER TABLE MCDispatch.OonAgeroDispatches ADD Eta int null
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='OonAgeroDispatches' AND COLUMN_NAME = 'ClosestDriverDistanceToDisablement')
    ALTER TABLE MCDispatch.OonAgeroDispatches ADD 
        ClosestDriverDistanceToDisablement float,
        ClosestDriverDistanceToDestination float, 
        CompanyDistanceToDisablement  float,
        CompanyDistanceToDestination  float,
        DisablementToDestinationDistance  float
GO   


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='OonAgeroDispatches' AND COLUMN_NAME = 'OfferPrice')
    ALTER TABLE MCDispatch.OonAgeroDispatches ADD OfferPrice money
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AllstateDispatches' AND COLUMN_NAME = 'ClosestDriverDistanceToDisablement')
    ALTER TABLE MCDispatch.AllstateDispatches ADD 
        ClosestDriverDistanceToDisablement float,
        ClosestDriverDistanceToDestination float, 
        CompanyDistanceToDisablement  float,
        CompanyDistanceToDestination  float,
        DisablementToDestinationDistance  float,
        OfferPrice money
GO   



IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='OonAgeroBlacklisted')
BEGIN
    CREATE TABLE mcdispatch.OonAgeroBlacklisted
    (
	    BlacklistId int  IDENTITY(1,1) NOT NULL,
	    Companyid int null,
	    Name varchar(100),
	    Phone varchar(20), 
	
	    AgeroDate datetime, 
	    AgeroVendorId int, 

	    CreateDate datetime not null default(getdate())
    )
  

  grant select on mcdispatch.OonAgeroBlacklisted to public
END
  
GO


/*
 * Police Rotation
 */

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='RotationSubcontractors')
    CREATE TABLE dbo.RotationSubcontractors (
        RotationSubcontractorId int IDENTITY (1, 1) NOT NULL,
        CompanyId int NOT NULL,
        AccountId int NOT NULL,
        BodyTypeId int NOT NULL,
        SubcontractorAccountId int NOT NULL,
        OwnerUserId int NOT NULL, 
		CreateDate datetime default(getdate())
        CONSTRAINT [PK_RotationSubcontractors] PRIMARY KEY CLUSTERED (RotationSubcontractorId ASC),
    )
GO
GRANT SELECT ON  dbo.RotationSubcontractors  TO public
GRANT INSERT ON  dbo.RotationSubcontractors  TO public
GRANT UPDATE ON  dbo.RotationSubcontractors  TO public
GRANT DELETE ON  dbo.RotationSubcontractors  TO public


/***************************
// Payment Links 
***************************/

IF OBJECT_ID('[dbo].[PaymentLinks]') IS NULL
BEGIN
CREATE TABLE [dbo].[PaymentLinks] (
    [PaymentLinkId]   INT           NOT NULL IDENTITY,
    [Vendor]          INT           NOT NULL, /* SQUARE, STRIPE, PAYPAL, ETC */
    [TransactionId]   INT           NULL,     /* reference constraint to the vendor transaction table */
    [Status]          INT           NOT NULL, /* 0=created, 1=processing, 2=preapproved, 3=captured/success, 4=error */
    [CompanyId]       INT           NOT NULL,
    [DispatchEntryId] INT           NULL,
    [StatementId]     INT           NULL,
    [CreateDate]      DATETIME      NOT NULL,
    [OwnerUserId]     INT           NULL,
    [StatusMessage]   VARCHAR(200)  NULL

    CONSTRAINT [PK_PaymentLinks] PRIMARY KEY CLUSTERED (PaymentLinkId ASC),
    CONSTRAINT [FK_PaymentLinks_DispatchEntries] FOREIGN KEY(DispatchEntryId) REFERENCES dbo.DispatchEntries (DispatchEntryId),
    CONSTRAINT [FK_PaymentLinks_Statements] FOREIGN KEY(StatementId) REFERENCES dbo.Statements (StatementId),
    CONSTRAINT [FK_PaymentLinks_Company] FOREIGN KEY(CompanyId) REFERENCES dbo.Companies (CompanyId),
    CONSTRAINT [FK_PaymentLinks_OwnerUserId] FOREIGN KEY(OwnerUserId) REFERENCES dbo.Users (UserId),
);

    GRANT SELECT ON  dbo.PaymentLinks TO public
    GRANT INSERT ON  dbo.PaymentLinks TO public
    GRANT UPDATE ON  dbo.PaymentLinks TO public

END
GO



/***************************
// Web Links 
***************************/

IF OBJECT_ID('[dbo].[DispatchEntryWebLinks]') IS NULL
BEGIN
CREATE TABLE [dbo].[DispatchEntryWebLinks] (
    [DispatchEntryWebLinkId]    INT         NOT NULL IDENTITY,
    [DispatchEntryId]           INT         NOT NULL,
    [CompanyId]                 INT         NOT NULL,
    [UrlHash]                   VARCHAR(20) NOT NULL,
    [CreateDate]                DATETIME    NOT NULL,
    [OwnerUserId]               INT         NULL,
    [EmailTransactionId]        INT         NULL,
    [PaymentLinkId]             INT         NULL,
    [ShowCharges]               INT         NULL,
    [HideDiscount]              INT         NULL,
    [HidePhotos]                INT         NULL

    CONSTRAINT [PK_DispatchEntryWebLinks] PRIMARY KEY CLUSTERED (DispatchEntryWebLinkId ASC),
    CONSTRAINT [FK_DispatchEntryWebLinks_DispatchEntries] FOREIGN KEY(DispatchEntryId) REFERENCES dbo.DispatchEntries (DispatchEntryId),
    CONSTRAINT [FK_DispatchEntryWebLinks_Company] FOREIGN KEY(CompanyId) REFERENCES dbo.Companies (CompanyId),
    CONSTRAINT [FK_DispatchEntryWebLinks_OwnerUserId] FOREIGN KEY(OwnerUserId) REFERENCES dbo.Users (UserId),
);

    CREATE INDEX [IX_DispatchEntryWebLinks_UrlHash] ON [dbo].[DispatchEntryWebLinks] (UrlHash)

    GRANT SELECT ON  dbo.DispatchEntryWebLinks TO public
    GRANT INSERT ON  dbo.DispatchEntryWebLinks TO public
    GRANT UPDATE ON  dbo.DispatchEntryWebLinks TO public

END
GO




IF OBJECT_ID('[dbo].[StatementWebLinks]') IS NULL
BEGIN
CREATE TABLE [dbo].[StatementWebLinks] (
    [StatementWebLinkId]        INT         NOT NULL IDENTITY,
    [StatementId]               INT         NOT NULL,
    [CompanyId]                 INT         NOT NULL,
    [UrlHash]                   VARCHAR(20) NOT NULL,
    [CreateDate]                DATETIME    NOT NULL,
    [OwnerUserId]               INT         NULL,
    [EmailTransactionId]        INT         NULL,
    [PaymentLinkId]             INT         NULL

    CONSTRAINT [PK_StatementWebLinks] PRIMARY KEY CLUSTERED (StatementWebLinkId ASC),
    CONSTRAINT [FK_StatementWebLinks_Statements] FOREIGN KEY(StatementId) REFERENCES dbo.Statements (StatementId),
    CONSTRAINT [FK_StatementWebLinks_Company] FOREIGN KEY(CompanyId) REFERENCES dbo.Companies (CompanyId),
    CONSTRAINT [FK_StatementWebLinks_OwnerUserId] FOREIGN KEY(OwnerUserId) REFERENCES dbo.Users (UserId),
);

    CREATE INDEX [IX_StatementWebLinks_UrlHash] ON [dbo].[StatementWebLinks] (UrlHash)

    GRANT SELECT ON  dbo.StatementWebLinks TO public
    GRANT INSERT ON  dbo.StatementWebLinks TO public
    GRANT UPDATE ON  dbo.StatementWebLinks TO public
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='DispatchEntryPaymentStates')
BEGIN
    CREATE TABLE [dbo].[DispatchEntryPaymentStates](
        [DispatchEntryPaymentStateId] INT NOT NULL IDENTITY,
        [CompanyId]                 INT NOT NULL,
        [DispatchEntryPaymentId]    INT NOT NULL,
        [StatusId]                  INT NOT NULL,
        [Timestamp]                 DATETIME default(getdate()),
        [SourceName]                VARCHAR(40) NOT NULL,
        [UserId]                    INT NULL,

        CONSTRAINT [PK_DispatchEntryPaymentStates] PRIMARY KEY CLUSTERED (DispatchEntryPaymentStateId ASC),
        CONSTRAINT [FK_DispatchEntryPaymentStates_Companies] FOREIGN KEY([CompanyId]) REFERENCES [dbo].[Companies] ([CompanyId]),
        CONSTRAINT [FK_DispatchEntryPaymentStates_DispatchEntryPayments] FOREIGN KEY([DispatchEntryPaymentId]) REFERENCES [dbo].[DispatchEntryPayments] ([DispatchEntryPaymentId])
    );

    GRANT SELECT ON  dbo.DispatchEntryPaymentStates TO public
    GRANT INSERT ON  dbo.DispatchEntryPaymentStates TO public
    GRANT UPDATE ON  dbo.DispatchEntryPaymentStates TO public
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryPaymentStates' AND COLUMN_NAME = 'UserId')
ALTER TABLE DispatchEntryPaymentStates ADD UserId INT NULL

IF EXISTS (SELECT 1 FROM sys.columns WHERE name = 'CreateDate' AND object_name(object_id) = 'DispatchEntryPaymentStates') AND
  NOT EXISTS (SELECT 1 FROM sys.columns WHERE name = 'Timestamp' AND object_name(object_id) = 'DispatchEntryPaymentStates')
    EXEC sp_RENAME 'dbo.DispatchEntryPaymentStates.CreateDate', 'Timestamp', 'COLUMN';

IF EXISTS (SELECT 1 FROM sys.columns WHERE name = 'Status' AND object_name(object_id) = 'DispatchEntryPaymentStates') AND
   NOT EXISTS (SELECT 1 FROM sys.columns WHERE name = 'StatusId' AND object_name(object_id) = 'DispatchEntryPaymentStates')
    EXEC sp_RENAME 'dbo.DispatchEntryPaymentStates.Status', 'StatusId', 'COLUMN';


IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='SquarePayments' AND COLUMN_NAME = 'Note')
ALTER TABLE [Integration].[SquarePayments] ALTER COLUMN Note VARCHAR(256);


IF OBJECT_ID('[Integration].[SquareTerminalCodes]') IS NULL
CREATE TABLE [Integration].[SquareTerminalCodes] (
   [SquareTerminalCodeId]  INT     NOT NULL IDENTITY,
   [CompanyId]             INT     NOT NULL,
   [AuthorizationId]       INT     NOT NULL,
   [LocationId]      VARCHAR (256) NOT NULL,
   [LocationName]    VARCHAR (256),
   [Name]            VARCHAR (64),
   [DeviceCodeId]    VARCHAR (96),
   [Code]            VARCHAR (32),
   [Status]          VARCHAR (32),
   [DeviceId]        VARCHAR (96),
   [PairDate]        DATETIME,
   [Hidden]          BIT DEFAULT(0) NOT NULL,
   [CreateDate]      DATETIME,
   [UpdateDate]      DATETIME,
   
   CONSTRAINT [PK_Integration.SquareTerminalCodes] PRIMARY KEY CLUSTERED ([SquareTerminalCodeId] ASC),
   CONSTRAINT [FK_Integration_SquareTerminalCodes_Companies] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].[Companies] ([CompanyId]),
   CONSTRAINT [FK_Integration_SquareTerminalCodes_Authorizations] FOREIGN KEY ([AuthorizationId]) REFERENCES [Integration].[SquareCompanyAuthorizations] ([AuthorizationId])
);
GO

IF OBJECT_ID('[Integration].[SquareTerminalCheckouts]') IS NULL
CREATE TABLE [Integration].SquareTerminalCheckouts (
   [SquareTerminalCheckoutId]  INT     NOT NULL IDENTITY,
   [CompanyId]                 INT     NOT NULL,
   [AuthorizationId]           INT     NOT NULL,
   [DispatchEntryId]           INT     NOT NULL,
   [AmountInCents]             MONEY    NOT NULL,
   [Amount]           MONEY    NOT NULL,
   [CheckoutId]       VARCHAR (256),
   [LocationId]       VARCHAR (256),
   [ReferenceId]      VARCHAR (64),
   [Note]             VARCHAR (96),
   [AppId]            VARCHAR (64),
   [PaymentType]      VARCHAR (64),
   [DeadlineDuration] VARCHAR (64),
   [DeviceId]         VARCHAR (96),
   [Status]           VARCHAR(60),
   [UserId]           INT,
   [CreateDate]       DATETIME,
   [UpdateDate]       DATETIME,
   
   CONSTRAINT [PK_Integration.SquareTerminalCheckouts] PRIMARY KEY CLUSTERED ([SquareTerminalCheckoutId] ASC),
   CONSTRAINT [FK_Integration_SquareTerminalCheckouts_Companies] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].[Companies] ([CompanyId]),
   CONSTRAINT [FK_Integration_SquareTerminalCheckouts_Authorizations] FOREIGN KEY ([AuthorizationId]) REFERENCES [Integration].[SquareCompanyAuthorizations] ([AuthorizationId])
);
GO

IF (NOT EXISTS(SELECT * FROM sys.indexes  WHERE name='IX_Integration_SquareTerminalCodes_DeviceCodeId'))
    CREATE INDEX [IX_Integration_SquareTerminalCodes_DeviceCodeId] ON Integration.SquareTerminalCodes ([DeviceCodeId])
GO
IF (NOT EXISTS(SELECT * FROM sys.indexes  WHERE name='IX_Integration_SquareTerminalCodes_CompanyId_Status'))
    CREATE INDEX [IX_Integration_SquareTerminalCodes_CompanyId_Status] ON Integration.SquareTerminalCodes ([CompanyId], [Status])
GO
IF (NOT EXISTS(SELECT * FROM sys.indexes  WHERE name='IX_Integration_SquareTerminalCheckouts_CheckoutId'))
    CREATE INDEX [IX_Integration_SquareTerminalCheckouts_CheckoutId] ON Integration.SquareTerminalCheckouts ([CheckoutId])
GO

GRANT SELECT,INSERT,UPDATE ON [Integration].[SquareTerminalCodes] TO public
GRANT SELECT,INSERT,UPDATE ON [Integration].[SquareTerminalCheckouts] TO public

IF NOT EXISTS (SELECT * from INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='SquareTerminalCodes' AND COLUMN_NAME = 'Hidden')
    ALTER TABLE Integration.SquareTerminalCodes ADD Hidden bit default(0) NOT NULL




/***************************
// Quotes
***************************/
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='QuoteSequences')
BEGIN
    CREATE TABLE [dbo].[QuoteSequences](
        [CompanyId] INT NOT NULL,
        [CurrentValue] INT NOT NULL,
        
        CONSTRAINT PK_QuoteSequences PRIMARY KEY CLUSTERED  ( CompanyId ASC )
    );

    CREATE INDEX [IX_QuoteSequences_Companies] ON [dbo].[QuoteSequences] (CompanyId)

    GRANT SELECT ON  dbo.QuoteSequences TO public
    GRANT INSERT ON  dbo.QuoteSequences TO public
    GRANT UPDATE ON  dbo.QuoteSequences TO public
END
GO


IF OBJECT_ID('[dbo].[QuoteWebLinks]') IS NULL
BEGIN
CREATE TABLE [dbo].[QuoteWebLinks] (
    [QuoteWebLinkId]            INT         NOT NULL IDENTITY,
    [QuoteId]                   VARCHAR(50) NOT NULL,
    [CompanyId]                 INT         NOT NULL,
    [UrlHash]                   VARCHAR(20) NOT NULL,
    [CreateDate]                DATETIME    NOT NULL,
    [OwnerUserId]               INT         NULL,
    [EmailTransactionId]        INT         NULL,

    CONSTRAINT [PK_QuoteWebLinks] PRIMARY KEY CLUSTERED (QuoteWebLinkId ASC),
    CONSTRAINT [FK_QuoteWebLinks_Company] FOREIGN KEY(CompanyId) REFERENCES dbo.Companies (CompanyId),
    CONSTRAINT [FK_QuoteWebLinks_OwnerUserId] FOREIGN KEY(OwnerUserId) REFERENCES dbo.Users (UserId),
);

    CREATE INDEX [IX_QuoteWebLinks_UrlHash] ON [dbo].[QuoteWebLinks] (UrlHash)

    GRANT SELECT ON  dbo.QuoteWebLinks TO public
    GRANT INSERT ON  dbo.QuoteWebLinks TO public
    GRANT UPDATE ON  dbo.QuoteWebLinks TO public

END
GO

IF OBJECT_ID('[dbo].[DispatchEntryDeleteLogs]') IS NULL
BEGIN
CREATE TABLE [dbo].DispatchEntryDeleteLogs (
    [DispatchEntryDeleteLogId]    INT         NOT NULL IDENTITY,
    [DispatchEntryId]             INT         NOT NULL,
    [OwnerUserId]                 INT         NOT NULL,
    [DeleteDate]                  DATETIME    NOT NULL,


    CONSTRAINT [PK_DispatchEntryDeleteLogs] PRIMARY KEY CLUSTERED (DispatchEntryDeleteLogId ASC),
    CONSTRAINT [FK_DispatchEntryDeleteLogs_DispatchEntryId] FOREIGN KEY(DispatchEntryId) REFERENCES dbo.DispatchEntries (DispatchEntryId),
    CONSTRAINT [FK_DispatchEntryDeleteLogs_OwnerUserId] FOREIGN KEY(OwnerUserId) REFERENCES dbo.Users (UserId),
);

    CREATE INDEX [IX_DispatchEntryDeleteLogs_DispatchEntryId] ON [dbo].[DispatchEntryDeleteLogs] (DispatchEntryId)

    GRANT SELECT ON  dbo.DispatchEntryDeleteLogs TO public
    GRANT INSERT ON  dbo.DispatchEntryDeleteLogs TO public
    GRANT UPDATE ON  dbo.DispatchEntryDeleteLogs TO public

END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='UserLocationsCurrent' AND COLUMN_NAME = 'ClientVersionId')
ALTER TABLE UserLocationsCurrent
 ADD ClientVersionId int not null default(0) 
GO


/***************************
// Account Managers
***************************/

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='AccountManagers')
BEGIN
    CREATE TABLE [dbo].[AccountManagers] (
        [AccountManagerId]	INT IDENTITY (1, 1) NOT NULL,
        [AccountId]			INT NOT NULL,
        [UserId]			INT NOT NULL,
        [OwnerUserId]       INT NOT NULL,
        [CreateDate]		datetime default(getdate()) NOT NULL,
        [IsDeleted]			bit default(0) NOT NULL,
        [DeletedByUserId]   INT NULL,
        [DeleteDate]		datetime NULL,


        CONSTRAINT [FK_AccountManagers_Accounts] FOREIGN KEY ([AccountId]) REFERENCES [dbo].[Accounts] ([AccountId]),
        CONSTRAINT [FK_AccountManagers_UserId] FOREIGN KEY ([UserId]) REFERENCES [dbo].[Users] ([UserId])
    );

    CREATE INDEX [IX_AccountManagers_AccountId] ON [dbo].[AccountManagers] (AccountId)

    GRANT SELECT ON  dbo.AccountManagers TO public
    GRANT INSERT ON  dbo.AccountManagers TO public
    GRANT UPDATE ON  dbo.AccountManagers TO public
END
GO

/***************************
// Release Reasons
***************************/

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='ImpoundReleaseReasons')
BEGIN
    CREATE TABLE [dbo].[ImpoundReleaseReasons] (
        [ImpoundReleaseReasonId]	INT IDENTITY (1, 1) NOT NULL,
        [CompanyId]			        INT NULL,
        [Name]			            VARCHAR(100),
        [Description]               VARCHAR(1000),
        [Internal]                  bit default(0) NOT NULL,
        [CreateDate]		        datetime default(getdate()) NOT NULL,
        [Deleted]			        bit default(0) NOT NULL,

        CONSTRAINT [FK_ImpoundReleaseReasons_Company] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].[Companies] ([CompanyId]),
    );

    CREATE INDEX [IX_ImpoundReleaseReasons_CompanyId] ON [dbo].[ImpoundReleaseReasons] (CompanyId)

    GRANT SELECT ON  dbo.ImpoundReleaseReasons TO public


    SET IDENTITY_INSERT ImpoundReleaseReasons ON
        INSERT INTO ImpoundReleaseReasons (ImpoundReleaseReasonId, CompanyId, Name, Description, Internal) VALUES (1, NULL, 'ReleaseWithPayment', 'Released - with Payment', 0)
        INSERT INTO ImpoundReleaseReasons (ImpoundReleaseReasonId, CompanyId, Name, Description, Internal) VALUES (2, NULL, 'ReleaseNewOwner', 'Released - to New Owner', 0)
        INSERT INTO ImpoundReleaseReasons (ImpoundReleaseReasonId, CompanyId, Name, Description, Internal) VALUES (3, NULL, 'Scrapped', 'Vehicle was scrapped', 0)
        INSERT INTO ImpoundReleaseReasons (ImpoundReleaseReasonId, CompanyId, Name, Description, Internal) VALUES (4, NULL, 'Other', 'Other', 0)
        INSERT INTO ImpoundReleaseReasons (ImpoundReleaseReasonId, CompanyId, Name, Description, Internal) VALUES (5, NULL, 'TowOut', 'Released from storage - Tow Out', 1)
        INSERT INTO ImpoundReleaseReasons (ImpoundReleaseReasonId, CompanyId, Name, Description, Internal) VALUES (6, NULL, 'ReleaseToInsurance', 'Release - to Insurance', 0)
        INSERT INTO ImpoundReleaseReasons (ImpoundReleaseReasonId, CompanyId, Name, Description, Internal) VALUES (7, NULL, 'ReleaseTitleObtained', 'Released - Title Obtained', 0)
        INSERT INTO ImpoundReleaseReasons (ImpoundReleaseReasonId, CompanyId, Name, Description, Internal) VALUES (8, NULL, 'ReleaseTitleSurrendered', 'Released - Title Surrendered', 0)
        INSERT INTO ImpoundReleaseReasons (ImpoundReleaseReasonId, CompanyId, Name, Description, Internal) VALUES (9, NULL, 'PromiseToPay', 'Released - Promise to Pay', 0)
        INSERT INTO ImpoundReleaseReasons (ImpoundReleaseReasonId, CompanyId, Name, Description, Internal) VALUES (10, NULL, 'RefusalToPay', 'Released - Refusal to Pay', 0)
    SET IDENTITY_INSERT ImpoundReleaseReasons OFF

END
GO



IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='DispatchEntryVehicleTitles')
BEGIN
    CREATE TABLE [dbo].[DispatchEntryVehicleTitles] (
        [DispatchEntryVehicleTitleId]	INT IDENTITY (1, 1) NOT NULL,
        [DispatchEntryId]			    INT NOT NULL,
        [ImpoundId]			            INT NULL,
        [TitleReceived]                 INT NULL,  
        [TitleIssueDate]		        DATETIME,
        [TitleNumber]		            VARCHAR(50),
        [TitleBrand]		            VARCHAR(100),
        [TitleJurisdiction]             VARCHAR(50),
        [Deleted]			            BIT DEFAULT(0) NOT NULL,

        CONSTRAINT [PK_DispatchEntryVehicleTitles] PRIMARY KEY CLUSTERED (DispatchEntryVehicleTitleId ASC),
        CONSTRAINT [FK_DispatchEntryVehicleTitles_Entry] FOREIGN KEY ([DispatchEntryId]) REFERENCES [dbo].[DispatchEntries] ([DispatchEntryId]),
    );

    CREATE INDEX [IX_DispatchEntryVehicleTitles_DispatchEntry] ON [dbo].[DispatchEntryVehicleTitles] (DispatchEntryId)
    CREATE INDEX [IX_DispatchEntryVehicleTitles_Impound] ON [dbo].[DispatchEntryVehicleTitles] (ImpoundId)

    GRANT SELECT ON  dbo.DispatchEntryVehicleTitles TO public
    GRANT INSERT ON  dbo.DispatchEntryVehicleTitles TO public
    GRANT UPDATE ON  dbo.DispatchEntryVehicleTitles TO public
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryVehicleTitles' AND 
    COLUMN_NAME = 'TitleJurisdiction')
  ALTER TABLE [dbo].[DispatchEntryVehicleTitles]
    ADD TitleJurisdiction VARCHAR(50)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='DispatchEntryVehicleStolenRecords')
BEGIN
    CREATE TABLE [dbo].[DispatchEntryVehicleStolenRecords] (
        [DispatchEntryVehicleStolenRecordId]	INT IDENTITY (1, 1) NOT NULL,
        [DispatchEntryId]			    INT NOT NULL,
        [ImpoundId]			            INT NOT NULL,
		[AssetId]						INT NOT NULL,
        [Source]                        VARCHAR(50),
        [SourceId]                      VARCHAR(128),
        [CreateDate]                    DATETIME NOT NULL DEFAULT(getdate()),
        [LastUpdateDate]                DATETIME NULL,
        [IncidentDate]					DATETIME NOT NULL,  
        [Status]						int NOT NULL DEFAULT(0),
		[State]							VARCHAR(15),
        [Vin]                           VARCHAR(21),
		[Deleted]                       BIT NOT NULL DEFAULT(0)

        CONSTRAINT [PK_DispatchEntryVehicleStolenRecords] PRIMARY KEY CLUSTERED (DispatchEntryVehicleStolenRecordId ASC),
        CONSTRAINT [FK_DispatchEntryVehicleStolenRecords_Entry] FOREIGN KEY ([DispatchEntryId]) REFERENCES [dbo].[DispatchEntries] ([DispatchEntryId]),
		CONSTRAINT [FK_DispatchEntryVehicleStolenRecords_Impound] FOREIGN KEY ([ImpoundId]) REFERENCES [dbo].[Impounds] ([ImpoundId]),
		CONSTRAINT [FK_DispatchEntryVehicleStolenRecords_Asset] FOREIGN KEY ([AssetId]) REFERENCES [dbo].[DispatchEntryAssets] ([AssetId])
    );

    CREATE INDEX [IX_DispatchEntryVehicleStolenRecords_DispatchEntry] ON [dbo].[DispatchEntryVehicleStolenRecords] (DispatchEntryId)
    CREATE INDEX [IX_DispatchEntryVehicleStolenRecords_Impound] ON [dbo].[DispatchEntryVehicleStolenRecords] (ImpoundId)

    GRANT SELECT ON  dbo.DispatchEntryVehicleStolenRecords TO public
    GRANT INSERT ON  dbo.DispatchEntryVehicleStolenRecords TO public
	GRANT UPDATE ON  dbo.DispatchEntryVehicleStolenRecords TO public
END
GO



IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='ImpoundTasks' AND COLUMN_NAME = 'LetterTemplateId')
ALTER TABLE ImpoundTasks
 ADD LetterTemplateId int null

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Tasks' AND COLUMN_NAME = 'Hidden')
ALTER TABLE Tasks
 ADD Hidden bit default(0)

IF EXISTS(SELECT * FROM sys.indexes WHERE name = '<IX_Tasks_CompanyId_ParentTaskId_TypeId>')
    BEGIN
        --drop old
        DROP INDEX [<IX_Tasks_CompanyId_ParentTaskId_TypeId>] on dbo.Tasks
    END

IF NOT EXISTS(SELECT * FROM sys.indexes WHERE name = 'IX_Tasks_CompanyId_ParentTaskId_TypeId_Hidden')
    BEGIN
        -- create new
        CREATE NONCLUSTERED INDEX [IX_Tasks_CompanyId_ParentTaskId_TypeId_Hidden] ON [dbo].[Tasks]
        (
	        [CompanyId] ASC,
            [Hidden] ASC,
	        [Deleted] ASC,
	        [ParentTaskId] ASC,
	        [TypeId] ASC
        )
        INCLUDE (
            [Title],
	        [DueDate]) 
            WITH (
                PAD_INDEX = OFF, 
                STATISTICS_NORECOMPUTE = OFF, 
                SORT_IN_TEMPDB = OFF, 
                DROP_EXISTING = OFF, 
                ONLINE = OFF, 
                ALLOW_ROW_LOCKS = ON, 
                ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
        

    END




IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='TaskHistory')
BEGIN
    CREATE TABLE [dbo].[TaskHistory] (
        [TaskHistoryId]	                INT IDENTITY (1, 1) NOT NULL,
        [TaskId]			            INT NOT NULL,
        [ActionId]			            INT NOT NULL,
        [CreateDate]                    DATETIME NOT NULL,  
        [OwnerUserId]		            INT NOT NULL,
        [Deleted]		                BIT NULL,
        [DeletedByUserId]		        INT NULL,
        [DeletedDate]			        DATETIME NULL,

        CONSTRAINT [PK_TaskHistory] PRIMARY KEY CLUSTERED (TaskHistoryId ASC),
        CONSTRAINT [FK_TaskHistory_TaskId] FOREIGN KEY ([TaskId]) REFERENCES [dbo].[Tasks] ([TaskId]),
    );

    CREATE INDEX [IX_TaskHistory_TaskId] ON [dbo].[TaskHistory] (TaskId)

    GRANT SELECT ON  dbo.TaskHistory TO public
    GRANT INSERT ON  dbo.TaskHistory TO public
    GRANT UPDATE ON  dbo.TaskHistory TO public
END
GO


/***************************
// User Details
***************************/

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='UserDetails')
BEGIN
    CREATE TABLE [dbo].[UserDetails] (
        [UserId]			        INT NOT NULL,
        [Address]			        VARCHAR(200),
        [City]                      VARCHAR(50),
        [State]		                VARCHAR(32),
        [Zip]			            VARCHAR(32),

	CONSTRAINT PK_UserDetails PRIMARY KEY CLUSTERED  ( UserId ASC )
    );

    CREATE INDEX [IX_UserDetails_UserId] ON [dbo].[UserDetails] (UserId)

    GRANT SELECT ON  dbo.UserDetails TO public
    GRANT UPDATE ON  dbo.UserDetails TO public
    GRANT INSERT ON  dbo.UserDetails TO public
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='UserLocationsCurrent' AND COLUMN_NAME = 'Speed')
ALTER TABLE UserLocationsCurrent
    ADD  Speed decimal(9,6), 
        Bearing decimal(9,6), 
        Battery int
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='DispatchEntryYearlySequences')
BEGIN
    CREATE TABLE dbo.DispatchEntryYearlySequences(
	    CompanyId int NOT NULL,
	    SequenceYear INT NOT NULL,
	    CurrentValue int NOT NULL,
	    CONSTRAINT PK_DispatchEntryYearlySequences PRIMARY KEY CLUSTERED (CompanyId ASC, SequenceYear ASC)
    )

    ALTER TABLE dbo.DispatchEntryYearlySequences ADD  CONSTRAINT DF_DispatchEntryYearlySequences_CurrentValue  DEFAULT ((1)) FOR CurrentValue
END

GO

/***************************
// Vehicle Auctions
***************************/

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='Auctions')
BEGIN
    CREATE TABLE [dbo].[Auctions] (
        [AuctionId]	            INT IDENTITY (1, 1) NOT NULL,
        [CompanyId]             INT NOT NULL,
        [ImpoundLotId]          INT NULL,
        [CreateDate]            DATETIME NOT NULL,
        [OwnerUserId]           INT NOT NULL,
        [StartDate]             DATETIME NULL,
        [EndDate]               DATETIME NULL,
        [ItemCount]             INT NOT NULL DEFAULT(0),
        [Description]           VARCHAR(MAX) DEFAULT(NULL),
        [Deleted]               BIT DEFAULT(NULL),
        [DeleteDate]            DATETIME NULL,
        [DeletedByUserId]       INT NULL

        CONSTRAINT [PK_Auctions] PRIMARY KEY CLUSTERED (AuctionId ASC),
        CONSTRAINT [FK_Auctions_CompanyId] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].[Companies] ([CompanyId]),
    );

    CREATE INDEX [IX_Auctions_AuctionId] ON [dbo].[Auctions] (AuctionId)
    CREATE INDEX [IX_Auctions_CompanyId_LotId_StartDate] ON [dbo].[Auctions] (CompanyId, ImpoundLotId, StartDate)

    GRANT SELECT ON  dbo.Auctions TO public
    GRANT INSERT ON  dbo.Auctions TO public
    GRANT UPDATE ON  dbo.Auctions TO public
END
GO



IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Auctions' AND COLUMN_NAME = 'ItemCount')
  ALTER TABLE Auctions
    ADD ItemCount INT NOT NULL DEFAULT(0)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Auctions' AND COLUMN_NAME = 'PhotoCount')
  ALTER TABLE Auctions
    ADD PhotoCount INT NOT NULL DEFAULT(0)
GO

IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Auctions' AND COLUMN_NAME = 'AssetCount')
  ALTER TABLE Auctions
    Drop Column AssetCount 

    -- may need to drop default contraint
    --ex: ALTER TABLE Auctions Drop Constraint DF__Auctions__AssetC__6AA2861B
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Auctions' AND COLUMN_NAME = 'Description')
  ALTER TABLE Auctions
    ADD Description VARCHAR(MAX) DEFAULT(NULL)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Auctions' AND COLUMN_NAME = 'Name')
  ALTER TABLE Auctions
    ADD Name VARCHAR(250) DEFAULT(NULL)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Auctions' AND COLUMN_NAME = 'RemoteId')
  ALTER TABLE Auctions
    ADD RemoteId VARCHAR(250) DEFAULT(NULL)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Auctions' AND COLUMN_NAME = 'RemoteSystem')
  ALTER TABLE Auctions
    ADD RemoteSystem VARCHAR(250) DEFAULT(NULL)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryAuctionDetails' AND COLUMN_NAME = 'RemoteId')
  ALTER TABLE DispatchEntryAuctionDetails
    ADD RemoteId VARCHAR(250) DEFAULT(NULL)
GO




IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='DispatchEntryAuctionDetails')
BEGIN
    CREATE TABLE [dbo].[DispatchEntryAuctionDetails] (
        [DispatchEntryAuctionDetailId]	INT IDENTITY (1, 1) NOT NULL,
        [DispatchEntryId]               INT NOT NULL,
        [AuctionId]                     INT NULL,
        [HasCatalyticConverter]         BIT DEFAULT(NULL),
        [StartPrice]                    DECIMAL(9,2) NULL,
        [ReservePrice]                  DECIMAL(9,2) NULL,
        [SalePrice]                     DECIMAL(9,2) NULL,
        [Notes]                         VARCHAR (8000)

        CONSTRAINT [PK_DispatchEntryAuctionDetails] PRIMARY KEY CLUSTERED (DispatchEntryAuctionDetailId ASC),
        CONSTRAINT [FK_DispatchEntryAuctionDetails_DispatchEntryId] FOREIGN KEY ([DispatchEntryId]) REFERENCES [dbo].[DispatchEntries] ([DispatchEntryId]),
    );

    CREATE INDEX [IX_DispatchEntryAuctionDetails_DispatchEntryAuctionDetailId] ON [dbo].[DispatchEntryAuctionDetails] (DispatchEntryAuctionDetailId)
    CREATE INDEX [IX_DispatchEntryAuctionDetails_DispatchEntryId] ON [dbo].[DispatchEntryAuctionDetails] (DispatchEntryId)

    GRANT SELECT ON  dbo.DispatchEntryAuctionDetails TO public
    GRANT INSERT ON  dbo.DispatchEntryAuctionDetails TO public
    GRANT UPDATE ON  dbo.DispatchEntryAuctionDetails TO public
END
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryAuctionDetails' AND COLUMN_NAME = 'AuctionId')
ALTER TABLE DispatchEntryAuctionDetails
    ADD AuctionId INT
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryAuctionDetails' AND COLUMN_NAME = 'LotNumber')
ALTER TABLE DispatchEntryAuctionDetails
    ADD LotNumber VARCHAR(250) DEFAULT(NULL)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryAuctionDetails' AND COLUMN_NAME = 'RowNumber')
ALTER TABLE DispatchEntryAuctionDetails
    ADD RowNumber VARCHAR(250) DEFAULT(NULL)
GO

IF (NOT EXISTS(SELECT * FROM sys.indexes  WHERE name='IX_DispatchEntryAuctionDetails_AuctionId'))
    CREATE INDEX [IX_DispatchEntryAuctionDetails_AuctionId] ON [dbo].[DispatchEntryAuctionDetails] (AuctionId)
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='AuctionItems')
BEGIN
    CREATE TABLE [dbo].[AuctionItems] (
        [AuctionItemId]	        INT IDENTITY (1, 1) NOT NULL,
        [AuctionId]	            INT NOT NULL,
        [DispatchEntryId]       INT NOT NULL,
        [StartPrice]            DECIMAL(9,2) NULL,
        [ReservePrice]          DECIMAL(9,2) NULL,
        [SalePrice]             DECIMAL(9,2) NULL,
		[RemoteId]				VARCHAR(250) DEFAULT(NULL),
		[Deleted]				BIT NOT NULL DEFAULT(0)

        CONSTRAINT [PK_AuctionItems] PRIMARY KEY CLUSTERED (AuctionItemId ASC),
        CONSTRAINT [FK_AuctionItems_DispatchEntryId] FOREIGN KEY ([DispatchEntryId]) REFERENCES [dbo].[DispatchEntries] ([DispatchEntryId]),
    );


    CREATE INDEX [IX_Auctions_AuctionId_DispatchEntryId] ON [dbo].[AuctionItems] (AuctionId, DispatchEntryId)

    GRANT SELECT ON  dbo.AuctionItems TO public
    GRANT INSERT ON  dbo.AuctionItems TO public
    GRANT UPDATE ON  dbo.AuctionItems TO public
END
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AuctionItems' AND COLUMN_NAME = 'RemoteId')
  ALTER TABLE AuctionItems
    ADD RemoteId VARCHAR(250) DEFAULT(NULL)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AuctionItems' AND COLUMN_NAME = 'Deleted')
ALTER TABLE AuctionItems
    ADD Deleted BIT NOT NULL DEFAULT(0)
GO



IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='AuctionPhotos')
BEGIN
    CREATE TABLE dbo.AuctionPhotos  (
        [AuctionPhotoId]                INT IDENTITY (1,1) NOT NULL,
        [AuctionId]                     INT NOT NULL,
        [DispatchEntryAuctionDetailId]  INT NOT NULL,
        [DispatchEntryPhotoId]          INT NOT NULL,
        [Deleted]                       BIT NOT NULL DEFAULT(0)

        CONSTRAINT [PK_AuctionPhotos] PRIMARY KEY CLUSTERED (AuctionPhotoId ASC),
    );
    
    CREATE NONCLUSTERED INDEX IK_AuctionPhotos_AuctionId ON dbo.AuctionPhotos (AuctionId)
    CREATE NONCLUSTERED INDEX IK_AuctionPhotos_EntryAuctionDetailId ON dbo.AuctionPhotos (DispatchEntryAuctionDetailId)

    GRANT SELECT ON  dbo.AuctionPhotos TO public
    GRANT INSERT ON  dbo.AuctionPhotos TO public
    GRANT UPDATE ON  dbo.AuctionPhotos TO public
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='GatewayConnections')
BEGIN

create table MCDispatch.GatewayConnections (
	ConnectionId INT IDENTITY (1, 1) NOT NULL,
    ConnectionGuid uniqueidentifier,
	Provider varchar(50), 
	ContractorId varchar(100),
	LocationId varchar(100),
	Timestamp datetime,
	LoggedIn int,
	Status varchar(50),
    IsDeleted bit not null default(0)

	CONSTRAINT [PK_GatewayConnections] PRIMARY KEY CLUSTERED (ConnectionId ASC)
)

grant select on MCDispatch.GatewayConnections to public
grant update on MCDispatch.GatewayConnections to public
grant insert on MCDispatch.GatewayConnections to public
END
GO


/*
 * SSO Table. 
 */
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='SSOProviders')
BEGIN
    CREATE TABLE [dbo].[SSOProviders](
    	[SSOProviderId] [int] IDENTITY(1,1) NOT NULL,
        [Name] [varchar](255) NOT NULL,
    	[SSOType] [varchar](32) NOT NULL,
    	[UrlEntityId] [varchar](255) NOT NULL,
	    CONSTRAINT [PK_SSOProviders] PRIMARY KEY CLUSTERED ( [SSOProviderId] ASC )
    )

    GRANT SELECT ON  dbo.SSOProviders TO public
    GRANT INSERT ON  dbo.SSOProviders TO public
    GRANT UPDATE ON  dbo.SSOProviders TO public

    SET IDENTITY_INSERT SSOProviders ON
        INSERT INTO SSOProviders (SSOProviderId, Name, SSOType, UrlEntityId) 
        VALUES (1, 'okta', 'SAML', 'http://www.okta.com/')
        INSERT INTO SSOProviders (SSOProviderId, Name, SSOType, UrlEntityId) 
        VALUES (2, 'azure', 'SAML', 'https://sts.windows.net/')
        INSERT INTO SSOProviders (SSOProviderId, Name, SSOType, UrlEntityId) 
        VALUES (3, 'azure OIDC', 'OIDC', 'https://login.microsoftonline.com/')
        INSERT INTO SSOProviders (SSOProviderId, Name, SSOType, UrlEntityId) 
        VALUES (4, 'okta OIDC', 'OIDC', '')
    SET IDENTITY_INSERT SSOProviders OFF
END
ELSE
BEGIN
    SET IDENTITY_INSERT SSOProviders ON
    IF NOT EXISTS (SELECT 1 FROM SSOProviders WHERE SSOProviderId = 3)
    BEGIN
        INSERT INTO SSOProviders (SSOProviderId, Name, SSOType, UrlEntityId) 
        VALUES (3, 'azure OIDC', 'OIDC', 'https://login.microsoftonline.com/')
    END
    
    IF NOT EXISTS (SELECT 1 FROM SSOProviders WHERE SSOProviderId = 4)
    BEGIN
        INSERT INTO SSOProviders (SSOProviderId, Name, SSOType, UrlEntityId) 
        VALUES (4, 'okta OIDC', 'OIDC', '')
    END
    SET IDENTITY_INSERT SSOProviders OFF
END
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='CompanySecuritySettings')
BEGIN
    CREATE TABLE [dbo].[CompanySecuritySettings](
    	[CompanySecuritySettingId] [int] IDENTITY(1,1) NOT NULL,
    	[CompanyId] [int] NOT NULL,
        [SSOProviderId] [int] NOT NULL,
    	[CompanyDomain] [varchar](255) NOT NULL,
    	[SessionTimeOut] [int],
        [SSOEnable] [bit] NOT NULL,
    	[DefaultUserType] [int] NOT NULL,
    	[MetadataURL] [varchar](255) NOT NULL,
    	[Idp] [varchar](255) NOT NULL,
    	CONSTRAINT [PK_CompanySecuritySettings] PRIMARY KEY CLUSTERED ( [CompanySecuritySettingId] ASC )
    )

    ALTER TABLE [dbo].[CompanySecuritySettings]  WITH CHECK ADD  CONSTRAINT [FK_CompanySecuritySettings_Companies] FOREIGN KEY([CompanyId])
    REFERENCES [dbo].[Companies] ([CompanyId])

    ALTER TABLE [dbo].[CompanySecuritySettings]  WITH CHECK ADD  CONSTRAINT [FK_CompanySecuritySettings_SSOProviders] FOREIGN KEY([SSOProviderId])
    REFERENCES [dbo].[SSOProviders] ([SSOProviderId])

    ALTER TABLE [dbo].[CompanySecuritySettings]
    ADD CONSTRAINT [UQ_CompanySecuritySettings_CompanyDomain] UNIQUE (CompanyDomain);   

    GRANT SELECT ON  dbo.CompanySecuritySettings TO public
    GRANT INSERT ON  dbo.CompanySecuritySettings TO public
    GRANT UPDATE ON  dbo.CompanySecuritySettings TO public
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='CompanySecuritySettings' AND COLUMN_NAME = 'Secret')
    ALTER TABLE CompanySecuritySettings ADD Secret VARCHAR(255) default('') NULL
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='GatewayConnections' AND COLUMN_NAME = 'EnvironmentId')
ALTER TABLE MCDispatch.GatewayConnections
    ADD EnvironmentId int NOT NULL DEFAULT(1)
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='GatewayMessages' AND TABLE_SCHEMA='MCDispatch')
	CREATE TABLE MCDispatch.GatewayMessages (
		GatewayMessageId int IDENTITY(1,1) NOT NULL,
	        EnvironmentId int NOT NULL DEFAULT(1),
		MessageType int NOT NULL,
		DispatchId varchar(50) NULL,
		JsonData varchar(max) NULL,

		CONSTRAINT PK_GatewayMessages PRIMARY KEY CLUSTERED (GatewayMessageId ASC)
	)
GO

grant select on MCDispatch.GatewayMessages to public
grant update on MCDispatch.GatewayMessages to public
grant insert on MCDispatch.GatewayMessages to public





/* 
// Short Links 
*/
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='ShortLinks' AND TABLE_SCHEMA='dbo')
CREATE TABLE dbo.ShortLinks
(
	ShortLinkId int IDENTITY(1,1) NOT NULL,
	ShortToken varchar(20) NOT NULL, -- Short token name e.g. AbCdE will be peresent in site as https://twbk.co/AbCdE
	ShortOrigin varchar(100) NOT NULL, -- Short origin url e.g. https://twbk.co
	OriginLink varchar(500) NOT NULL, -- Origin url that is represented by the short link url
	CompanyId int NULL,
	OwnerUserId int NULL,
	ObjectId int NULL,
	ObjectType int NULL,
	ParentObjectId int NULL,
	ParentObjectType int NULL,
	CreateDate DATETIME NOT NULL DEFAULT(getdate()),
	Deleted bit NOT NULL DEFAULT(0)

	CONSTRAINT PK_ShortLinks PRIMARY KEY CLUSTERED (ShortLinkId ASC)
);

IF NOT EXISTS (select * from sys.indexes where name = 'IK_ShortLinks_ShortLink')
CREATE NONCLUSTERED INDEX IK_ShortLinks_ShortLink ON dbo.ShortLinks (ShortToken, ShortOrigin)
GO
IF NOT EXISTS (select * from sys.indexes where name = 'IK_ShortLinks_OriginLink')
CREATE NONCLUSTERED INDEX IK_ShortLinks_OriginLink ON dbo.ShortLinks (OriginLink)
GO

grant select on dbo.ShortLinks to public
grant update on dbo.ShortLinks to public
grant insert on dbo.ShortLinks to public
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='ShortLinkVisitDetails' AND TABLE_SCHEMA='dbo')
CREATE TABLE dbo.ShortLinkVisitDetails
(
	ShortLinkVisitDetailId int IDENTITY(1,1) NOT NULL,
	ShortLinkId int NOT NULL,
	VisitDate DateTime NOT NULL DEFAULT(getdate()),
	IpAddress varchar(255) NULL,
	OS varchar(50) NULL,
	Browser varchar(50) NULL

	CONSTRAINT PK_ShortLinkVisitDetails_Detail PRIMARY KEY CLUSTERED (ShortLinkVisitDetailId ASC),
	CONSTRAINT [FK_ShortLinkVisitDetails_ShortLinkId] FOREIGN KEY ([ShortLinkId]) REFERENCES [dbo].[ShortLinks] ([ShortLinkId]),
);
GO
IF NOT EXISTS (select * from sys.indexes WHERE name = 'IK_ShortLinkVisitDetails_ShortLinkId')
CREATE NONCLUSTERED INDEX IK_ShortLinkVisitDetails_ShortLinkId ON dbo.ShortLinkVisitDetails (ShortLinkId)
GO

grant select on dbo.ShortLinkVisitDetails to public
grant insert on dbo.ShortLinkVisitDetails to public
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='VehicleManufacturers' AND COLUMN_NAME = 'NcicName')
ALTER TABLE dbo.VehicleManufacturers
    ADD NcicName varchar(50)
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='GpsCollectorRuns' AND TABLE_SCHEMA='Integration')
CREATE TABLE Integration.GpsCollectorRuns (
  CompanySourceId INT IDENTITY (1, 1) NOT NULL,
  CompanyId int,
  SourceId int NOT NULL, 
  LastRan datetime NOT NULL default('1/1/2022'),

  CONSTRAINT [PK_GpsCollectorRuns] PRIMARY KEY CLUSTERED (CompanySourceId ASC),
)
GO

IF (NOT EXISTS(SELECT * FROM sys.indexes  WHERE name='IX_GpsCollectorRuns_CompanySource'))
CREATE INDEX IX_GpsCollectorRuns_CompanySource ON Integration.GpsCollectorRuns (CompanyId, SourceId) 
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='JoyrideConnections')
    CREATE TABLE Integration.JoyrideConnections (
		JoyrideConnectionId int IDENTITY (1, 1) NOT NULL,
		CompanyId  int NOT NULL,
		OwnerUserId int NOT NULL,
		IsDeleted bit NOT NULL DEFAULT(0),
		ExpiresNext datetime,
		CreateDate datetime default(getdate()),
		LastUpdated datetime default(getdate()),

        CONSTRAINT [FK_JoyrideConnections_Companies] FOREIGN KEY(CompanyId) REFERENCES dbo.Companies (CompanyId)
    )
GO

GRANT SELECT ON  Integration.JoyrideConnections TO public
GRANT INSERT ON  Integration.JoyrideConnections TO public
GRANT UPDATE ON  Integration.JoyrideConnections TO public
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='ImpoundReminderDefinitions' AND COLUMN_NAME = 'LetterTemplateId')
     ALTER TABLE ImpoundReminderDefinitions
         ADD LetterTemplateId INT NULL
 GO



/*
Advanced Web Forms Templates and Settings
*/


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='AccountPublicRequestFormTemplates' AND TABLE_SCHEMA='dbo' )
BEGIN
    CREATE TABLE dbo.AccountPublicRequestFormTemplates (
		AccountPublicRequestFormTemplateId          INT IDENTITY (1, 1) NOT NULL,
		CompanyId                                   INT NOT NULL,
        AccountPublicRequestFormTemplateSettingId   INT,
		Name                                        VARCHAR(255) NOT NULL,
		OwnerUserId                                 INT NOT NULL,
		CreateDate                                  DATETIME default(getdate()),
		Deleted                                     BIT NOT NULL DEFAULT(0),
		DeletedByUserId                             INT,
		DeletedDate                                 DATETIME

		CONSTRAINT [PK_AccountPublicRequestFormTemplates_Id] PRIMARY KEY CLUSTERED (AccountPublicRequestFormTemplateId ASC),
        CONSTRAINT [FK_AccountPublicRequestFormTemplates_Companies] FOREIGN KEY(CompanyId) REFERENCES dbo.Companies (CompanyId),
		CONSTRAINT [FK_AccountPublicRequestFormTemplates_Users] FOREIGN KEY(OwnerUserId) REFERENCES dbo.Users (UserId)
    );
	
	CREATE NONCLUSTERED INDEX IK_AccountPublicRequestFormTemplates_CompanyId ON dbo.AccountPublicRequestFormTemplates (CompanyId)
END
GO

GRANT SELECT ON  dbo.AccountPublicRequestFormTemplates TO public
GRANT INSERT ON  dbo.AccountPublicRequestFormTemplates TO public
GRANT UPDATE ON  dbo.AccountPublicRequestFormTemplates TO public
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountPublicRequestFormTemplates' AND COLUMN_NAME = 'LayoutId')
     ALTER TABLE AccountPublicRequestFormTemplates
         ADD LayoutId INT DEFAULT(0) NOT NULL
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountPublicRequestFormTemplates' AND COLUMN_NAME = 'IsEnabled')
     ALTER TABLE AccountPublicRequestFormTemplates
         ADD IsEnabled BIT DEFAULT(1) NOT NULL
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='AccountPublicRequestFormTemplateSettings' AND TABLE_SCHEMA='dbo')
    CREATE TABLE dbo.AccountPublicRequestFormTemplateSettings (
		AccountPublicRequestFormTemplateSettingId 	INT IDENTITY (1, 1) NOT NULL,
        AccountPublicRequestFormTemplateId          INT NOT NULL,
        VINOption                                   INT,
		VehicleMakeOption 							INT,
		VehicleModelOption 							INT,
		VehicleYearOption 							INT,
		VehicleColorOption 							INT,
		LicensePlateOption 							INT,
		LicensePlateStateOption 					INT,
		PickupLocationOption 						INT,
		PickupLocationDefaultOption					INT,
		PickupLocationDefaultValue					VARCHAR(100),
		DestinationLocationOption					INT,
		DestinationLocationDefaultOption			INT,
		DestinationImpoundLotId						INT,
		DestinationLocationDefaultValue				VARCHAR(100),
        ContactNameOption							INT,
        ContactNameDefaultOption                    INT,
        ContactNameDefaultValue                     VARCHAR(100),
		ContactPhoneNumberOption					INT,
        ContactPhoneNumberDefaultOption             INT,
        ContactPhoneNumberDefaultValue              VARCHAR(100),
		ContactEmailOption							INT,
        ContactEmailDefaultOption                   INT,
        ContactEmailDefaultValue                    VARCHAR(100),
		ReasonOption								INT,
        ReasonDefaultOption                         INT,
        ReasonDefaultValueId                        INT,
		PurchaseOrderOption							INT,
        PurchaseOrderDefaultOption					INT,
        PurchaseOrderDefaultValue                   VARCHAR(100),
		PhotoOption									INT,
		NotesOption									INT,
		NotesDefaultValue							VARCHAR(2000),
		WelcomeEmailBody							VARCHAR(2000)

		CONSTRAINT [PK_AccountPublicRequestFormTemplateSettings_Id] PRIMARY KEY CLUSTERED (AccountPublicRequestFormTemplateSettingId ASC),
        CONSTRAINT [FK_AccountPublicRequestFormTemplateSettings_TemplateId] FOREIGN KEY(AccountPublicRequestFormTemplateId) REFERENCES dbo.AccountPublicRequestFormTemplates (AccountPublicRequestFormTemplateId),
        CONSTRAINT [FK_AccountPublicRequestFormTemplateSettings_ImpoundLotId] FOREIGN KEY(DestinationImpoundLotId) REFERENCES dbo.ImpoundLots (ImpoundLotId),
    );
GO

GRANT SELECT ON  dbo.AccountPublicRequestFormTemplateSettings TO public
GRANT INSERT ON  dbo.AccountPublicRequestFormTemplateSettings TO public
GRANT UPDATE ON  dbo.AccountPublicRequestFormTemplateSettings TO public
GO



IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='AccountPublicRequestFormTemplateOfficeHours' AND TABLE_SCHEMA='dbo')
BEGIN
    CREATE TABLE dbo.AccountPublicRequestFormTemplateOfficeHours (
        AccountPublicRequestFormOfficeHourId INT IDENTITY(1,1) NOT NULL,
        AccountPublicRequestFormTemplateId INT NOT NULL,
        DayOfWeek   INT NOT NULL,
        StartTime   TIME NOT NULL,
        EndTime     TIME NOT NULL,
        
        CONSTRAINT [PK_AccountPublicRequestFormTemplateOfficeHours_Id] PRIMARY KEY CLUSTERED (AccountPublicRequestFormOfficeHourId ASC),
        CONSTRAINT [FK_AccountPublicRequestFormTemplateOfficeHours_TemplateId] FOREIGN KEY(AccountPublicRequestFormTemplateId) REFERENCES dbo.AccountPublicRequestFormTemplates (AccountPublicRequestFormTemplateId),
    )


END
GO

GRANT SELECT ON  dbo.AccountPublicRequestFormTemplateOfficeHours TO public
GRANT INSERT ON  dbo.AccountPublicRequestFormTemplateOfficeHours TO public
GRANT DELETE ON  dbo.AccountPublicRequestFormTemplateOfficeHours TO public
GO




IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='AccountPublicRequestFormTemplateDisclaimers' AND TABLE_SCHEMA='dbo')
BEGIN
    CREATE TABLE dbo.AccountPublicRequestFormTemplateDisclaimers (
        AccountPublicRequestFormTemplateDisclaimerId INT IDENTITY (1,1) NOT NULL,
		AccountPublicRequestFormTemplateId INT NOT NULL,
		Disclaimer VARCHAR(max) NOT NULL,
        CreateDate DATETIME NOT NULL DEFAULT(getDate()),
        OwnerUserId INT NOT NULL,
        Deleted BIT DEFAULT(0) NOT NULL,
        DeletedByUserId INT NULL,
        DeleteDate DATETIME NULL

        CONSTRAINT [PK_AccountPublicRequestFormTemplateDisclaimers_Id] PRIMARY KEY CLUSTERED (AccountPublicRequestFormTemplateDisclaimerId ASC),
        CONSTRAINT [FK_AccountPublicRequestFormTemplateDisclaimers_TemplateId] FOREIGN KEY(AccountPublicRequestFormTemplateId) REFERENCES dbo.AccountPublicRequestFormTemplates (AccountPublicRequestFormTemplateId),
        CONSTRAINT [FK_AccountPublicRequestFormTemplateDisclaimers_UserId] FOREIGN KEY(OwneruserId) REFERENCES dbo.Users (UserId),
    );

    CREATE NONCLUSTERED INDEX IK_AccountPublicRequestFormTemplatedisclaimers_ByTemplate ON dbo.AccountPublicRequestFormTemplateDisclaimers (AccountPublicRequestFormTemplateId)
END
GO

GRANT SELECT ON  dbo.AccountPublicRequestFormTemplateDisclaimers TO public
GRANT INSERT ON  dbo.AccountPublicRequestFormTemplateDisclaimers TO public
GRANT UPDATE ON  dbo.AccountPublicRequestFormTemplateDisclaimers TO public
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='AccountPublicRequestFormTemplateReasons' AND TABLE_SCHEMA='dbo')
BEGIN
    CREATE TABLE dbo.AccountPublicRequestFormTemplateReasons (
        AccountPublicRequestFormTemplateReasonId INT IDENTITY (1,1) NOT NULL,
		TemplateId INT NOT NULL,
		ReasonId INT NOT NULL
    );

    CREATE NONCLUSTERED INDEX IK_AccountPublicRequestFormTemplateReasons_ByTemplate ON dbo.AccountPublicRequestFormTemplateReasons (TemplateId)
END
GO

GRANT SELECT ON  dbo.AccountPublicRequestFormTemplateReasons TO public
GRANT INSERT ON  dbo.AccountPublicRequestFormTemplateReasons TO public
GRANT DELETE ON  dbo.AccountPublicRequestFormTemplateReasons TO public
GO



IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='AccountPublicRequestFormTemplateCustomFields' AND TABLE_SCHEMA='dbo')
BEGIN
    CREATE TABLE dbo.AccountPublicRequestFormTemplateCustomFields (
        AccountPublicRequestFormTemplateCustomFieldId INT IDENTITY (1,1) NOT NULL,
		AccountPublicRequestFormTemplateId INT NOT NULL,
		DispatchEntryAttributeId INT NOT NULL,
        IncludeOption INT,
        DefaultOption INT,
        DefaultValue VARCHAR(500)
    );

    CREATE NONCLUSTERED INDEX IK_AccountPublicRequestFormTemplateCustomFields_ByTemplate ON dbo.AccountPublicRequestFormTemplateCustomFields (AccountPublicRequestFormTemplateId)
END
GO

GRANT SELECT ON  dbo.AccountPublicRequestFormTemplateCustomFields TO public
GRANT INSERT ON  dbo.AccountPublicRequestFormTemplateCustomFields TO public
GRANT DELETE ON  dbo.AccountPublicRequestFormTemplateCustomFields TO public
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountPublicRequestLinks' AND COLUMN_NAME = 'AccountPublicRequestFormTemplateId')
	ALTER TABLE 
		dbo.AccountPublicRequestLinks
	ADD 
		AccountPublicRequestFormTemplateId INT DEFAULT(NULL)
GO





IF OBJECT_ID('dbo.[vwRateItems]') IS NULL EXEC ('CREATE VIEW dbo.[vwRateItems] AS SELECT 1 as Temp')
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

 ALTER view [dbo].[vwRateItems] AS
 SELECT  R.RateItemId, R.CompanyId, R.RateTypeId, R.MeasurementId, R.Name, R.Description, R.Cost, R.MinimumQuantity, R.MaximumQuantity, R.DefaultQuantity,
         R.LockCost, R.LockQuantity, R.Taxable, R.PayCommission, R.Deleted, R.CreateDate, R.RateItemPredefinedId, R.FreeQuantity, R.RateItemCategoryId,
         R.ParentRateItemId, R.DiscountExempt, R.ClassId FROM RateItems R
 UNION
 SELECT        R.RateItemId, CS.CompanyId, R.RateTypeId, R.MeasurementId, R.Name, R.Description, R.Cost, R.MinimumQuantity, R.MaximumQuantity, R.DefaultQuantity,
               R.LockCost, R.LockQuantity, R.Taxable, R.PayCommission, R.Deleted, R.CreateDate, R.RateItemPredefinedId, R.FreeQuantity, R.RateItemCategoryId,
               R.ParentRateItemId, R.DiscountExempt, R.ClassId
 FROM            RateItems AS R INNER JOIN
                 CompaniesShared AS CS ON CS.ShareAllRateItems = 1 AND (CS.SharedCompanyId = R.CompanyId OR
                                                                        CS.CompanyId = R.CompanyId)
 UNION

 SELECT        R.RateItemId, CS.SharedCompanyId, R.RateTypeId, R.MeasurementId, R.Name, R.Description, R.Cost, R.MinimumQuantity, R.MaximumQuantity, R.DefaultQuantity,
               R.LockCost, R.LockQuantity, R.Taxable, R.PayCommission, R.Deleted, R.CreateDate, R.RateItemPredefinedId, R.FreeQuantity, R.RateItemCategoryId,
               R.ParentRateItemId, R.DiscountExempt, R.ClassId
 FROM            RateItems AS R INNER JOIN
                 CompaniesShared AS CS ON CS.ShareAllRateItems = 1 AND (CS.SharedCompanyId = R.CompanyId OR
                                                                        CS.CompanyId = R.CompanyId)
GO


ALTER TABLE [dbo].[CommissionRateItemBodyTypes]
    ALTER COLUMN RateItemId [int] NULL
GO



IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='StatementEmailOptions' AND TABLE_SCHEMA='dbo')
BEGIN
	CREATE TABLE StatementEmailOptions (
	  StatementEmailOptionId INT IDENTITY(1,1) NOT NULL,
	  CompanyId INT NOT NULL,
	  AccountId INT,
	  Subject VARCHAR(500),
	  Message VARCHAR(MAX),
      OwnerUserId INT NOT NULL,
	  Deleted bit Default(0),
	  DeletedByUserId int,
	  DeletedDate DateTime NULL
	  
	  CONSTRAINT PK_StatementEmailOptions PRIMARY KEY CLUSTERED ([StatementEmailOptionId] ASC),
	  CONSTRAINT [FK_StatementEmailOptions_Companies] FOREIGN KEY([CompanyId]) REFERENCES [dbo].[Companies] ([CompanyId]),
	)
END
GO
    GRANT SELECT ON StatementEmailOptions to Public
    GRANT INSERT ON StatementEmailOptions to Public
    GRANT UPDATE ON StatementEmailOptions to Public
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='InvoiceEmailOptions' AND TABLE_SCHEMA='dbo')
BEGIN
	CREATE TABLE InvoiceEmailOptions (
	  InvoiceEmailOptionId INT IDENTITY(1,1) NOT NULL,
	  CompanyId INT NOT NULL,
	  AccountId INT,
	  Subject VARCHAR(500),
	  Message VARCHAR(MAX),
      OwnerUserId INT NOT NULL,
	  Deleted bit Default(0),
	  DeletedByUserId int,
	  DeletedDate DateTime NULL
	  
	  CONSTRAINT PK_InvoiceEmailOptions PRIMARY KEY CLUSTERED ([InvoiceEmailOptionId] ASC),
	  CONSTRAINT [FK_InvoiceEmailOptions_Companies] FOREIGN KEY([CompanyId]) REFERENCES [dbo].[Companies] ([CompanyId]),
	)
END
GO
    GRANT SELECT ON InvoiceEmailOptions to Public
    GRANT INSERT ON InvoiceEmailOptions to Public
    GRANT UPDATE ON InvoiceEmailOptions to Public
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='StatementOptions' AND COLUMN_NAME = 'DefaultDueDateType')
    ALTER TABLE StatementOptions ADD DefaultDueDateType int default(0) NOT NULL


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Disclaimers' AND COLUMN_NAME = 'Statement')
    ALTER TABLE Disclaimers ADD Statement bit default(0) NOT NULL


ALTER TABLE [dbo].[CommissionRateItemBodyTypes]
    ALTER COLUMN RateItemId [int] NULL
GO

 

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='FleetnetProviders' AND COLUMN_NAME = 'ContractorId')
 ALTER TABLE mcdispatch.FleetnetProviders
     ADD ContractorId varchar(50)
GO

ALTER TABLE [dbo].[AccountTags]
    ALTER COLUMN ShortName VarChar(30) NULL
GO

ALTER TABLE [dbo].[AccountTags]
    ADD IsEnabled BIT DEFAULT 1 NOT NULL
GO

IF OBJECT_ID('dbo.[vwCarfax]') IS NULL EXEC ('CREATE VIEW dbo.[vwCarfax] AS SELECT 1 as Temp')
GO
ALTER VIEW [dbo].[vwCarfax] AS 
SELECT                 
de.DispatchEntryId,              
VIN=REPLACE(upper(VehicleVIN), ',', ''),                
PlateNumber=DE.VehicleLicenseNumber,                
PlateState=DE.VehicleLicenseState,                
VehicleMake=aa.make,              
VehicleModel=aa.model,              
AirbagStatus=aa.AirbagStatus,              
AreaOfDamage='',                
Status='Completed',                
City=replace(C.City,',','/'),                 
State=upper(C.State),                 
Date=DE.CreateDate,                
Reason=DR.Name,                
Drivable=de.VehicleDrivable,                
Odometer=de.VehicleOdometer,                
Impound=de.Impound                
 FROM DispatchEntries  DE with (nolock)                
 INNER JOIN Companies C with (nolock) on C.CompanyId=DE.CompanyId                
 INNER JOIN DispatchReasons DR with (nolock) on DR.DispatchReasonId=DE.DispatchReasonId          
 inner join DispatchEntryAssets AA on AA.DispatchEntryId=DE.DispatchEntryId and AA.Deleted=0           
 left outer join accounts a on a.accountid=de.AccountId and a.MasterAccountId is not null and a.MasterAccountId > 0          
  WHERE  DE.Deleted=0 and a.accountid is null and     
  de.VehicleVIN is not null          
  and len(de.vehiclevin)=17    
  AND de.companyid not in               
 (49039 ,2, 1061, 3,1, 27, 7,800,3981,5056,4182,4185,4189,4082,20005,10000,7124,36671,        
36672, 36673,  36674, 36675,   36676,  36677,  36678,  36679,  36680, 36681,  36682,  36683,  36684, 
36685,  36686,   36687,   36688,   36689,   36690,   36691,  35211,  35212,  35213,  35214,    
35215, 35216, 35217, 35218, 35219, 38966, 35210, 31069, 31070,    
31071,  30008,  30088,  30818,  31032,  31069,  31558,  32980,  33294,  33881,  33882,  33947,  36649,  37527,  39952,  39971,  40539,  40896,  40897,    
17609, 4399   
) and           
de.impound=0          
and de.companyid not in (select companyid From companies with (nolock) where name like '%transport%')    
and (a.accountid is null)               
and de.DispatchEntryId > ********     
and (dr.Name like '%police%' or dr.Name like '%accident%')    
and  aa.make not in ('bmw', 'ferrari', 'mclaren', 'lamborghini', 'porsche')  
GO



IF OBJECT_ID('dbo.[vwDeletedCalls]') IS NULL EXEC ('CREATE VIEW dbo.[vwDeletedCalls] AS SELECT 1 as Temp')
GO
ALTER VIEW [dbo].[vwDeletedCalls] AS
select 
	DE.CompanyId,
	DE.DispatchEntryId, 
	A.AccountId,
	CallNumber, 
	DS.Name as Status,
	A.Company as AccountName,
	cast(DE.CreateDate as varchar) as CreateDate,
	cast(DE.CompletionTime as varchar) as CompletionTime,
	U.FullName as Dispatcher_Creator,
	D.Name as DriverName,

	DE.TowSource as IncidentLocation,
	DE.TowDestination AS TowDestination,
	EA.ModelYear,
	EA.Make, 
	EA.Model,
	EA.VIN,
	EA.LicenseNumber,
	EA.LicenseState,
	(select sum(amount) from DispatchEntryPayments  where invoiceid=(select invoiceid from invoices where DispatchEntryId=de.DispatchEntryId)) as PaymentsTotal
	from dispatchentries DE 
LEFT OUTER JOIN Accounts A on A.AccountId=DE.AccountId
LEFT OUTER JOIN Users U on U.UserId=DE.OwnerUserId
LEFT OUTER JOIN Drivers D on D.DriverId=DE.DriverId
LEFT OUTER JOIN  DispatchStatuses DS ON DS.DispatchStatusId=DE.Status
LEFT OUTER JOIN DispatchEntryAssets EA on EA.DispatchEntryId=DE.DispatchEntryId
where DE.deleted=1

GO


IF OBJECT_ID('dbo.[vwDispatchEntries2]') IS NULL EXEC ('CREATE VIEW dbo.[vwDispatchEntries2] AS SELECT 1 as Temp')
GO
ALTER VIEW [dbo].[vwDispatchEntries2]
AS
SELECT     D.DispatchEntryId, CallNumber, D.CompanyId, D.AccountId, Type, DispatchReasonId, ReceiveTime, DispatchTime, EnRouteTime, ArrivalTime, TowTime,DestinationArrivalTime, 
                      CompletionTime, TowSource, TowDestination, MilesSource, MilesDestination, DriverId, TruckId, Notes, VehicleVIN, VehicleBodyTypeId, VehicleColorId, 
                      VehicleManufacturerId, VehicleModelId, VehicleYear, VehicleLicenseNumber, VehicleLicenseState, VehicleLicenseYear, coalesce(VehicleDrivable, 0) as VehicleDrivable, 
                      VehicleOdometer, Priority, Status, D.OwnerUserId, D.CreateDate, Created, Impound, ArrivalETA,
                        (SELECT SUM(PaymentsTotal) FROM dbo.Invoices inv WITH (NOLOCK) WHERE inv.DispatchEntryId=D.DispatchEntryId) AS PaymentsApplied, 

							Version, COALESCE(CAST(CASE WHEN (i.ReleaseDate IS NOT NULL) THEN 1 ELSE 0 END AS bit), 0) as Released,
'' AS CancellationReason,
							InvoiceStatusId,
							InvoiceNumber,
							PurchaseOrderNumber,
							/*nullif(replace(replace(cast((
select cast(soi.StatementId as varchar) + ', ' as [text()]
from StatementDispatchEntries soi WITH (NOLOCK) 
	inner join statements S on s.statementid=soi.statementid and s.deleted=0
where soi.DispatchEntryId =D.DispatchEntryId
for xml path( '' ), root( 'X' ), type
) as varchar(max)), '<X>', ''),'</X>',''), '') */ null as Statements
FROM         dbo.DispatchEntries D
	LEFT OUTER JOIN Impounds I WITH (NOLOCK) on I.DispatchEntryId=D.DispatchEntryId AND D.Impound=1 and I.Deleted=0
WHERE D.Deleted=0
GO

IF OBJECT_ID('dbo.[vwInternalActivityLogs]') IS NULL EXEC ('CREATE VIEW dbo.[vwInternalActivityLogs] AS SELECT 1 as Temp')
GO
ALTER VIEW [dbo].[vwInternalActivityLogs] AS
select ALOT.Name as ObjectType, ALI.*, ALID.Data from ActivityLogItems ALI 
	INNER JOIN ActivityLogItemDetails ALID ON ALID.ActivityLogItemId=ALI.ActivityLogItemId
	INNER JOIN ActivityLogObjectTypes ALOT ON ALOT.ObjectTypeId=ALI.ObjectTypeId
	INNER JOIN ActivityLogObjectTypes POT ON POT.ObjectTypeId=ALI.ParentObjectTypeId
GO


IF OBJECT_ID('dbo.[vwInternalAllstateCallsByCompany]') IS NULL EXEC ('CREATE VIEW dbo.[vwInternalAllstateCallsByCompany] AS SELECT 1 as Temp')
GO
ALTER view [dbo].[vwInternalAllstateCallsByCompany] as 
  select A.AccountId, C.CompanyId, C.Name, C.LastLogin, 
  (select count(*) from dispatchentries WITH (NOLOCK) where accountid=a.accountid and CreateDate > dateadd(day, -30, getdate())) as  AllstateCalls from accounts A  WITH (NOLOCK)
	inner join vwcompanies C WITH (NOLOCK) on C.CompanyId=A.CompanyId
	where company like '%allstate%' and C.Companyid not in (2)
GO


IF OBJECT_ID('dbo.[vwInternalCallRequests]') IS NULL EXEC ('CREATE VIEW dbo.[vwInternalCallRequests] AS SELECT 1 as Temp')
GO

ALTER VIEW [dbo].[vwInternalCallRequests] AS    

SELECT        DR.CallRequestId, MA.MasterAccountId, MA.Name as ProviderName, DR.CompanyId, DR.ProviderId as ContractorId, C.Name as CompanyName,
DR.AccountId, DE.CallNumber, DR.StartingLocation, DR.Reason,     
DR.ServiceNeeded, DR.Vehicle, DR.RequestDate, DR.ExpirationDate, DR.CreateDate, DR.Status,    
       case DR.Status    
       when 1 then 'Accepted'    
       when 2 then 'Rejected'    
       when 3 then 'Phone call Requested'    
       when 4 then 'Cancelled'    
       when 5 then 'Expired'    
       when 6 then 'Accepting (no response)'    
       when 40 then 'Rejected by Motor Club (Eta?)'    
       when 9 then 'Auto Phone Call Requested - no response from you'    
       when 0 then 'None'    
       else 'Other'    
      end as StatusName,    
        DR.DispatchEntryId, DR.OwnerUserId, COALESCE(DE.PurchaseOrderNumber, DR.PurchaseOrderNumber) AS PurchaseOrderNumber, DR.TowDestination, DR.ResponseReasonId ,
	coalesce(DR.Eta, ETA.Eta) as EtaGiven
FROM            DispatchEntryRequests AS DR     
 INNER JOIN Accounts AS A ON A.AccountId = DR.AccountId     
 INNER JOIN MasterAccounts AS MA ON MA.MasterAccountId = A.MasterAccountId     
 INNER JOIN Companies AS C ON C.CompanyId = DR.CompanyId    
 LEFT OUTER JOIN DispatchEntryRequestEtas ETA on ETA.CallRequestId=DR.CallRequestId    
 LEFT OUTER JOIN Dispatchentries DE on DE.DispatchEntryId=DR.DispatchEntryId    
GO

IF OBJECT_ID('dbo.[vwInternalGeicoCallsByCompany]') IS NULL EXEC ('CREATE VIEW dbo.[vwInternalGeicoCallsByCompany] AS SELECT 1 as Temp')
GO

alter view [dbo].[vwInternalGeicoCallsByCompany] as 
  select A.AccountId, C.CompanyId, C.Name, C.LastLogin, 
  (select count(*) from dispatchentries WITH (NOLOCK) where accountid=a.accountid and CreateDate > dateadd(day, -30, getdate())) as  GeicoCalls from accounts A  WITH (NOLOCK)
	inner join vwcompanies C WITH (NOLOCK) on C.CompanyId=A.CompanyId
GO


IF OBJECT_ID('dbo.[vwInternalRDAMRegistrations]') IS NULL EXEC ('CREATE VIEW dbo.[vwInternalRDAMRegistrations] AS SELECT 1 as Temp')
GO
ALTER view [dbo].[vwInternalRDAMRegistrations] as

select 'isc.RegisterProvider("RDAM", '+ cast (CompanyId as varchar) +', ' + cast( Z.AccountId as varchar) + ', "' + z.Value + '", "' + KV.Value + '", "' + z.X + '");' as CSharp,  KV.Value as TaxId, z.Value as ContractorId, z.X as LocationId, Z.AccountId, Z.CompanyId from

(
	select A.CompanyId, PKV.AccountId, PKV.Value, COALESCE(substring(MH.LocationId, 1, 3), substring(LID.Value, 1, 3)) as X from Integration.ProviderAccountKeyValues PKV 
		inner join Accounts A on A.AccountId=PKV.AccountId and A.MasterAccountId=1
		left outer join MotorClubProviderNumberHistory MH on MH.AccountId=A.AccountId
		left outer join Integration.ProviderAccountKeyValues LID on LID.AccountId=PKV.AccountId and LID.ProviderAccountKeyId=17
where PKV.ProviderAccountKeyId=3 and len(PKV.value ) > 1 ) z
inner join Integration.ProviderAccountKeyValues KV ON KV.ProviderAccountKeyId=15 and KV.AccountId=Z.AccountId
where z.Value not in (select contractorid from mcdispatch.vwIsscProviders WHERE IsDeleted=0) and KV.Value not in ('12-520250')
GO


IF OBJECT_ID('dbo.[vwInternalReportsCallsByMonth]') IS NULL EXEC ('CREATE VIEW dbo.[vwInternalReportsCallsByMonth] AS SELECT 1 as Temp')
GO
ALTER view [dbo].[vwInternalReportsCallsByMonth] AS
select CreateDate=cast(cast(month(createdate) as varchar)+ '/1/' + cast(year(createdate) as varchar) as date),
Calls=count(*) from DispatchEntries WHERE CreateDate > '1/1/2009' and createdate < getdate()

group by month(createdate), year(createdate)
GO


IF OBJECT_ID('dbo.[vwInternalReportsTrialsByMonth]') IS NULL EXEC ('CREATE VIEW dbo.[vwInternalReportsTrialsByMonth] AS SELECT 1 as Temp')
GO
alter view [dbo].[vwInternalReportsTrialsByMonth] AS
select Trials=count(*), Date=cast(cast(month(createdate) as varchar)+ '/1/' + cast(year(createdate) as varchar) as datetime) from vwcompanies 

group by month(createdate), year(createdate)
GO



IF OBJECT_ID('dbo.[vwInvoiceItems]') IS NULL EXEC ('CREATE VIEW dbo.[vwInvoiceItems] AS SELECT 1 as Temp')
GO
ALTER VIEW [dbo].[vwInvoiceItems]  
AS   
SELECT     II.InvoiceItemId, II.InvoiceId, D.CallNumber, D.Impound, D.CompanyId, D.AccountId As CallAccountId,  
  II.RateItemId,   
  II.CustomName, II.CustomPrice, II.Quantity, II.RelatedInvoiceItemId, II.AssetId, I.DispatchEntryId,   
                      I.ImpoundId,  
       PaymentsTotal=CASE   
   WHEN (I.PaymentsTotal IS NULL OR I.PaymentsTotal=0) THEN (SELECT COALESCE(SUM(Amount),0) FROM DispatchEntryPayments WHERE InvoiceId=I.InvoiceId AND IsVoid=0)  
   ELSE I.PaymentsTotal  
  END,  
  D.AccountId,  
  II.Taxable,  
  CC.ClassId,   
  CC.Name as ClassName,  
  D.CreateDate as CallDate,  
  coalesce(RI.Name, rip.name) as RateItemName  ,
  II.Hidden
FROM         InvoiceItems AS II   
 INNER JOIN Invoices AS I ON I.InvoiceId = II.InvoiceId  
 INNER JOIN DispatchEntries D on D.DispatchEntryid=I.DispatchEntryId  
 LEFT OUTER JOIN ChargeClasses CC on CC.ClassId=II.ClassId  
 LEFT OUTER JOIN RateItems RI on RI.RateItemId=II.RateItemId   
 left outer join RateItemsPredefined rip on rip.RateItemPredefinedId=ri.RateItemPredefinedId  
GO

IF OBJECT_ID('dbo.[vwVINDecoder1]') IS NULL EXEC ('CREATE VIEW dbo.[vwVINDecoder1] AS SELECT 1 as Temp')
GO

ALTER view [dbo].[vwVINDecoder1] as
select distinct VIN_Make=substring(upper(vehiclevin), 1, 3), 
VIN_Model = substring(upper(vehiclevin), 4, 3), 
VM.name as Make, M.Name as Model, M.ModelId, M.ManufacturerId as MakeId,
upper(vehicleVIN) as VIN,
VehicleYear
 from dispatchentries DE

 inner join VehicleManufacturers VM on DE.VehicleManufacturerId=VM.ManufacturerId
 inner join VehicleModels M on M.ModelId=DE.VehicleModelId
 where len(vehiclevin)=17 and vehiclevin not in('*****************','*****************',
'19UU1111111111111',
'*****************',
'*****************') AND VehicleVin not like '% %' and vehicleyear > 1980 --and VehicleManufacturerId=1
and VehicleModelId not in(32767)
and VehicleManufacturerId is not null
--AND substring(upper(vehiclevin), 1, 3)
GO


IF OBJECT_ID('dbo.[vwInternalReportsCallsByMonth]') IS NULL EXEC ('CREATE VIEW dbo.[vwInternalReportsCallsByMonth] AS SELECT 1 as Temp')
GO
ALTER view [dbo].[vwInternalReportsCallsByMonth] AS
select CreateDate=cast(cast(month(createdate) as varchar)+ '/1/' + cast(year(createdate) as varchar) as date),
Calls=count(*) from DispatchEntries WHERE CreateDate > '1/1/2009' and createdate < getdate()

group by month(createdate), year(createdate)
GO

IF OBJECT_ID('dbo.[vwInternalReportsTrialsByMonth]') IS NULL EXEC ('CREATE VIEW dbo.[vwInternalReportsTrialsByMonth] AS SELECT 1 as Temp')
GO
alter view [dbo].[vwInternalReportsTrialsByMonth] AS
select Trials=count(*), Date=cast(cast(month(createdate) as varchar)+ '/1/' + cast(year(createdate) as varchar) as datetime) from vwcompanies 

group by month(createdate), year(createdate)
GO



IF OBJECT_ID('dbo.[vwInvoiceItems]') IS NULL EXEC ('CREATE VIEW dbo.[vwInvoiceItems] AS SELECT 1 as Temp')
GO
ALTER VIEW [dbo].[vwInvoiceItems]  
AS   
SELECT     II.InvoiceItemId, II.InvoiceId, D.CallNumber, D.Impound, D.CompanyId, D.AccountId As CallAccountId,  
  II.RateItemId,   
  II.CustomName, II.CustomPrice, II.Quantity, II.RelatedInvoiceItemId, II.AssetId, I.DispatchEntryId,   
                      I.ImpoundId,  
       PaymentsTotal=CASE   
   WHEN (I.PaymentsTotal IS NULL OR I.PaymentsTotal=0) THEN (SELECT COALESCE(SUM(Amount),0) FROM DispatchEntryPayments WHERE InvoiceId=I.InvoiceId AND IsVoid=0)  
   ELSE I.PaymentsTotal  
  END,  
  D.AccountId,  
  II.Taxable,  
  CC.ClassId,   
  CC.Name as ClassName,  
  D.CreateDate as CallDate,  
  coalesce(RI.Name, rip.name) as RateItemName  ,
  II.Hidden
FROM         InvoiceItems AS II   
 INNER JOIN Invoices AS I ON I.InvoiceId = II.InvoiceId  
 INNER JOIN DispatchEntries D on D.DispatchEntryid=I.DispatchEntryId  
 LEFT OUTER JOIN ChargeClasses CC on CC.ClassId=II.ClassId  
 LEFT OUTER JOIN RateItems RI on RI.RateItemId=II.RateItemId   
 left outer join RateItemsPredefined rip on rip.RateItemPredefinedId=ri.RateItemPredefinedId  
GO


IF OBJECT_ID('dbo.[vwVINDecoder1]') IS NULL EXEC ('CREATE VIEW dbo.[vwVINDecoder1] AS SELECT 1 as Temp')
GO
ALTER view [dbo].[vwVINDecoder1] as

select distinct VIN_Make=substring(upper(vehiclevin), 1, 3), 
VIN_Model = substring(upper(vehiclevin), 4, 3), 
VM.name as Make, M.Name as Model, M.ModelId, M.ManufacturerId as MakeId,
upper(vehicleVIN) as VIN,
VehicleYear
 from dispatchentries DE

 inner join VehicleManufacturers VM on DE.VehicleManufacturerId=VM.ManufacturerId
 inner join VehicleModels M on M.ModelId=DE.VehicleModelId
 where len(vehiclevin)=17 and vehiclevin not in('*****************','*****************',
'19UU1111111111111',
'*****************',
'*****************') AND VehicleVin not like '% %' and vehicleyear > 1980 --and VehicleManufacturerId=1
and VehicleModelId not in(32767)
and VehicleManufacturerId is not null
--AND substring(upper(vehiclevin), 1, 3)
GO


IF OBJECT_ID('dbo.[vwAccountTags]') IS NULL EXEC ('CREATE VIEW dbo.[vwAccountTags] AS SELECT 1 as Temp')
GO
ALTER VIEW [dbo].[vwAccountTags]  
AS  
SELECT     atl.AccountId, at.AccountTagId, at.Name, at.ShortName  
FROM         dbo.AccountTagLinks AS atl with (nolock)
INNER JOIN   dbo.AccountTags  AS at with (nolock) ON at.AccountTagId = atl.AccountTagId 
WHERE at.IsEnabled = 1

GO
-- Extended Properties

IF NOT EXISTS (SELECT * FROM SYS.EXTENDED_PROPERTIES WHERE 
    [major_id] = OBJECT_ID('dbo.vwAccountTags') AND [name] = N'MS_DiagramPane1' AND [minor_id] = 0)
EXEC sp_addextendedproperty N'MS_DiagramPane1', N'[0E232FF0-B466-11cf-A24F-00AA00A3EFFF, 1.00]
Begin DesignProperties = 
   Begin PaneConfigurations = 
      Begin PaneConfiguration = 0
         NumPanes = 4
         Configuration = "(H (1[40] 4[20] 2[20] 3) )"
      End
      Begin PaneConfiguration = 1
         NumPanes = 3
         Configuration = "(H (1 [50] 4 [25] 3))"
      End
      Begin PaneConfiguration = 2
         NumPanes = 3
         Configuration = "(H (1 [50] 2 [25] 3))"
      End
      Begin PaneConfiguration = 3
         NumPanes = 3
         Configuration = "(H (4 [30] 2 [40] 3))"
      End
      Begin PaneConfiguration = 4
         NumPanes = 2
         Configuration = "(H (1 [56] 3))"
      End
      Begin PaneConfiguration = 5
         NumPanes = 2
         Configuration = "(H (2 [66] 3))"
      End
      Begin PaneConfiguration = 6
         NumPanes = 2
         Configuration = "(H (4 [50] 3))"
      End
      Begin PaneConfiguration = 7
         NumPanes = 1
         Configuration = "(V (3))"
      End
      Begin PaneConfiguration = 8
         NumPanes = 3
         Configuration = "(H (1[56] 4[18] 2) )"
      End
      Begin PaneConfiguration = 9
         NumPanes = 2
         Configuration = "(H (1 [75] 4))"
      End
      Begin PaneConfiguration = 10
         NumPanes = 2
         Configuration = "(H (1[66] 2) )"
      End
      Begin PaneConfiguration = 11
         NumPanes = 2
         Configuration = "(H (4 [60] 2))"
      End
      Begin PaneConfiguration = 12
         NumPanes = 1
         Configuration = "(H (1) )"
      End
      Begin PaneConfiguration = 13
         NumPanes = 1
         Configuration = "(V (4))"
      End
      Begin PaneConfiguration = 14
         NumPanes = 1
         Configuration = "(V (2))"
      End
      ActivePaneConfig = 0
   End
   Begin DiagramPane = 
      Begin Origin = 
         Top = 0
         Left = 0
      End
      Begin Tables = 
         Begin Table = "atl"
            Begin Extent = 
               Top = 6
               Left = 38
               Bottom = 114
               Right = 189
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "at"
            Begin Extent = 
               Top = 6
               Left = 227
               Bottom = 184
               Right = 378
            End
            DisplayFlags = 280
            TopColumn = 0
         End
      End
   End
   Begin SQLPane = 
   End
   Begin DataPane = 
      Begin ParameterDefaults = ""
      End
   End
   Begin CriteriaPane = 
      Begin ColumnWidths = 11
         Column = 1440
         Alias = 900
         Table = 1170
         Output = 720
         Append = 1400
         NewValue = 1170
         SortType = 1350
         SortOrder = 1410
         GroupBy = 1350
         Filter = 1350
         Or = 1350
         Or = 1350
         Or = 1350
      End
   End
End
', 'SCHEMA', N'dbo', 'VIEW', N'vwAccountTags', NULL, NULL
GO
DECLARE @xp int
SELECT @xp=1
IF NOT EXISTS (SELECT * FROM SYS.EXTENDED_PROPERTIES WHERE
        [major_id] = OBJECT_ID('dbo.vwAccountTags') AND [name] = N'MS_DiagramPaneCount' AND [minor_id] = 0)
EXEC sp_addextendedproperty N'MS_DiagramPaneCount', @xp, 'SCHEMA', N'dbo', 'VIEW', N'vwAccountTags', NULL, NULL
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='ScheduleAbsences' AND TABLE_SCHEMA='dbo')
BEGIN

    CREATE TABLE ScheduleAbsences (
        ScheduleAbsenceId INT IDENTITY (1, 1) NOT NULL,
        DriverId int not null,
        StartDate datetime not null,
        EndDate datetime not null,
        Type int not null,
        CreateDate datetime not null,
        OwnerUserId int not null,
        ReferenceId varchar(50)

        CONSTRAINT PK_ScheduleAbsences PRIMARY KEY CLUSTERED  ( ScheduleAbsenceId ASC )
)
END
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='ScheduleAbsences' AND COLUMN_NAME = 'IsDeleted')
    ALTER TABLE ScheduleAbsences 
        ADD IsDeleted BIT NOT NULL DEFAULT(0),
            DeletedByUserId int,
            DeletedDate datetime
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='DomainSsoExceptions' AND TABLE_SCHEMA='dbo')
BEGIN

    CREATE TABLE DomainSsoExceptions (
        Domain varchar(255)
        CONSTRAINT Domains PRIMARY KEY CLUSTERED  
)
END
GO



IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='ImpoundReleasePayments' AND TABLE_SCHEMA='dbo')
BEGIN
    CREATE TABLE ImpoundReleasePayments (
        ImpoundReleasePaymentId INT IDENTITY (1, 1) NOT NULL,
        ImpoundId int not null,
        DispatchEntryPaymentId int not null,
        Deleted bit Default(0),
	    DeletedByUserId int,
	    DeletedDate DateTime NULL
        
        CONSTRAINT [PK_ImpoundReleasePayments] PRIMARY KEY CLUSTERED  ( ImpoundReleasePaymentId ASC ),
        CONSTRAINT [FK_ImpoundReleasePayments_DispatchEntryPayments] FOREIGN KEY([DispatchEntryPaymentId]) REFERENCES [dbo].[DispatchEntryPayments] ([DispatchEntryPaymentId]),
)
END
GO

    GRANT SELECT ON ImpoundReleasePayments to Public
    GRANT INSERT ON ImpoundReleasePayments to Public
    GRANT UPDATE ON ImpoundReleasePayments to Public
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryAttributeSelectorOptions' AND TABLE_SCHEMA='dbo')
BEGIN
    CREATE TABLE DispatchEntryAttributeSelectorOptions (
		DispatchEntryAttributeSelectorOptionId INT IDENTITY (1,1) NOT NULL,
		DispatchEntryAttributeId INT NOT NULL,
		OptionValue VARCHAR(100) NOT NULL,
        CreateDate DateTime NOT NULL DEFAULT(getDate()),
        OwnerUserId INT NOT NULL,
		Deleted BIT DEFAULT(0),
		DeletedByUserId int,
		DeletedDate DateTime NULL

		CONSTRAINT [PK_DispatchEntryAttributeSelectorOptions] PRIMARY KEY CLUSTERED  ( DispatchEntryAttributeSelectorOptionId ASC ),
        CONSTRAINT [FK_DispatchEntryAttributeSelectorOptions_DispatchEntryAttributeId] FOREIGN KEY([DispatchEntryAttributeId]) REFERENCES [dbo].[DispatchEntryAttributes] ([DispatchEntryAttributeId]),
	)
END
GO

    GRANT SELECT ON DispatchEntryAttributeSelectorOptions to Public
    GRANT INSERT ON DispatchEntryAttributeSelectorOptions to Public
    GRANT UPDATE ON DispatchEntryAttributeSelectorOptions to Public
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='CommissionDriverReportOptions')
 CREATE TABLE dbo.CommissionDriverReportOptions(
                                             CommissionDriverReportOptionId int IDENTITY(1,1) NOT NULL,
                                             CompanyId int NOT NULL,
                                             ShowDriverBaseExport bit NOT NULL,
                                             ShowCallTotalExport bit NOT NULL,
                                             ShowTruckExport bit NOT NULL,
                                             ShowPONumberExport bit NOT NULL,
                                             ShowWeightClassExport bit NOT NULL,
                                             ShowDriversLicenseNumExport bit NOT NULL,
                                             HideAllButCommissionExport bit NOT NULL,
                                             ShowDriverBaseWeb bit NOT NULL,
                                             ShowCallTotalWeb bit NOT NULL,
                                             ShowTruckWeb bit NOT NULL,
                                             ShowPONumberWeb bit NOT NULL,
                                             ShowWeightClassWeb bit NOT NULL,
                                             ShowDriversLicenseNumWeb bit NOT NULL,
                                             HideAllButCommissionWeb bit NOT NULL,
                                             CreateDate datetime default(getdate()) NOT NULL,
                                             OwnerUserId int NOT NULL, 

                                             CONSTRAINT PK_CommissionDriverReportOptions PRIMARY KEY CLUSTERED  (CommissionDriverReportOptionId ASC),
                                             CONSTRAINT fk_CommissionDriverReportOptions_Companies FOREIGN KEY (CompanyId) REFERENCES Companies(CompanyId))
GO

 GRANT SELECT ON CommissionDriverReportOptions to Public
 GRANT INSERT ON CommissionDriverReportOptions to Public
 GRANT UPDATE ON CommissionDriverReportOptions to Public
 GO



IF EXISTS (SELECT * FROM Billing.Features WHERE Name='Closing Calls' and FeatureId=133)
BEGIN
    Update Billing.Features Set Name='Closed Accounting Period' WHERE FeatureId=133
END


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='ClosedAccountingPeriodOptions' AND TABLE_SCHEMA='dbo') 
BEGIN
  CREATE TABLE dbo.ClosedAccountingPeriodOptions (
    ClosedAccountingPeriodOptionId int IDENTITY(1,1) NOT NULL,
    CompanyId INT NOT NULL,
    OwnerUserId INT NOT NULL,
    CreateDate DATETIME NOT NULL,
    Enabled BIT NOT NULL Default(0),
    ClosedDate DATETIME NULL,
    ModificationMethod INT NULL,
    ModificationPassword VARCHAR(128) NULL,
    Deleted BIT NOT NULL DEFAULT(0),
    DeletedDate DATETIME NULL,
    DeletedByUserId INT NULL

    CONSTRAINT PK_ClosedAccountingPeriodOptions PRIMARY KEY CLUSTERED (ClosedAccountingPeriodOptionId ASC),
    CONSTRAINT FK_ClosedAccountingPeriodOptions_Companies FOREIGN KEY (CompanyId) REFERENCES Companies(CompanyId),
    CONSTRAINT FK_ClosedAccountingPeriodOptions_Users FOREIGN KEY (OwnerUserId) REFERENCES Users(UserId)
  )
  CREATE INDEX [IX_ClosedAccountingPeriodOptions_ByCompany] ON [dbo].[ClosedAccountingPeriodOptions] (CompanyId)
END
GO
 GRANT SELECT ON ClosedAccountingPeriodOptions to Public
 GRANT INSERT ON ClosedAccountingPeriodOptions to Public
 GRANT UPDATE ON ClosedAccountingPeriodOptions to Public
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='ClosedAccountingPeriodOptionHistory' AND TABLE_SCHEMA='dbo') 
BEGIN
  CREATE TABLE dbo.ClosedAccountingPeriodOptionHistory (
    ClosedAccountingPeriodOptionHistoryId int IDENTITY(1,1) NOT NULL,
    ClosedAccountingPeriodOptionId INT NOT NULL,
    CompanyId INT NOT NULL,
    OwnerUserId INT NOT NULL,
    CreateDate DATETIME NOT NULL,
    Description VARCHAR(128) NOT NULL,
    IpAddress VARCHAR(255) NULL,

    CONSTRAINT PK_ClosedAccountingPeriodOptionHistory PRIMARY KEY CLUSTERED (ClosedAccountingPeriodOptionHistoryId ASC),
    CONSTRAINT FK_ClosedAccountingPeriodOptionHistory_Option FOREIGN KEY (ClosedAccountingPeriodOptionId) REFERENCES ClosedAccountingPeriodOptions(ClosedAccountingPeriodOptionId),
    CONSTRAINT FK_ClosedAccountingPeriodOptionHistory_Companies FOREIGN KEY (CompanyId) REFERENCES Companies(CompanyId),
    CONSTRAINT FK_ClosedAccountingPeriodOptionHistory_Users FOREIGN KEY (OwnerUserId) REFERENCES Users(UserId)
  )
  CREATE INDEX [IX_ClosedAccountingPeriodOptionHistory_ByCompany] ON [dbo].[ClosedAccountingPeriodOptions] (CompanyId)
END
GO
 GRANT SELECT ON ClosedAccountingPeriodOptionHistory to Public
 GRANT INSERT ON ClosedAccountingPeriodOptionHistory to Public
GO




IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='DispatchEntryTempTokens' AND TABLE_SCHEMA='dbo') 
BEGIN
  CREATE TABLE dbo.DispatchEntryTempTokens (
    DispatchEntryTempTokenId INT IDENTITY(1,1) NOT NULL,
    UserId INT NOT NULL,
    DispatchEntryId INT NULL,
    Type INT NOT NULL,
    CompanyId INT NULL,
    Token VARCHAR(36) NOT NULL,
    CreateDate DATETIME NOT NULL,
    ExpirationDate DATETIME NOT NULL,
    IpAddress VARCHAR(64) NULL,
    Deleted BIT NOT NULL DEFAULT(0),
    DeletedDate DATETIME NULL

    CONSTRAINT PK_DispatchEntryTempTokens PRIMARY KEY CLUSTERED (DispatchEntryTempTokenId ASC),
    CONSTRAINT FK_DispatchEntryTempTokens_Companies FOREIGN KEY (CompanyId) REFERENCES Companies(CompanyId),
    CONSTRAINT FK_DispatchEntryTempTokens_Entries FOREIGN KEY (DispatchEntryId) REFERENCES DispatchEntries(DispatchEntryId),
    CONSTRAINT FK_DispatchEntryTempTokens_Users FOREIGN KEY (UserId) REFERENCES Users(UserId)
  )
  CREATE INDEX [IX_DispatchEntryTempTokens_ByToken] ON [dbo].[DispatchEntryTempTokens] (Token)
  CREATE INDEX [IX_DispatchEntryTempTokens_ByUser] ON [dbo].[DispatchEntryTempTokens] (UserId, Type)
END
GO
 GRANT SELECT ON DispatchEntryTempTokens to Public
 GRANT INSERT ON DispatchEntryTempTokens to Public
 GRANT UPDATE ON DispatchEntryTempTokens to Public
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='ClosedAccountingPeriodCallActivityLogs' AND TABLE_SCHEMA='dbo') 
BEGIN
  CREATE TABLE dbo.ClosedAccountingPeriodCallActivityLogs (
    ClosedAccountingPeriodCallActivityLogId INT IDENTITY(1,1) NOT NULL,
	CompanyId INT NOT NULL,
    DispatchEntryId INT NOT NULL,
	DispatchEntryPaymentId INT NULL,
	ActionType INT NOT NULL,
    OwnerUserId INT NOT NULL,
	DispatchEntryTempTokenId INT NULL,
    CreateDate DATETIME NOT NULL,
    IpAddress VARCHAR(255) NULL
    

    CONSTRAINT PK_ClosedAccountingPeriodCallActivityLogs PRIMARY KEY CLUSTERED (ClosedAccountingPeriodCallActivityLogId ASC),
    CONSTRAINT FK_ClosedAccountingPeriodCallActivityLogs_Entries FOREIGN KEY (DispatchEntryId) REFERENCES DispatchEntries(DispatchEntryId),
	CONSTRAINT FK_ClosedAccountingPeriodCallActivityLogs_Companies FOREIGN KEY (CompanyId) REFERENCES Companies(CompanyId),
	CONSTRAINT FK_ClosedAccountingPeriodCallActivityLogs_Payments FOREIGN KEY (DispatchEntryPaymentId) REFERENCES DispatchEntryPayments(DispatchEntryPaymentId),
    CONSTRAINT FK_ClosedAccountingPeriodCallActivityLogs_Users FOREIGN KEY (OwnerUserId) REFERENCES Users(UserId),
	CONSTRAINT FK_ClosedAccountingPeriodCallActivityLogs_TempTokens FOREIGN KEY (DispatchEntryTempTokenId) REFERENCES DispatchEntryTempTokens(DispatchEntryTempTokenId),
  )
  CREATE INDEX [IX_ClosedAccountingPeriodCallActivityLogs_ByCompany] ON [dbo].[ClosedAccountingPeriodCallActivityLogs] (CompanyId)
END
GO
 GRANT SELECT ON ClosedAccountingPeriodCallActivityLogs to Public
 GRANT INSERT ON ClosedAccountingPeriodCallActivityLogs to Public
GO



IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='dbo.ImpoundLotLicenseKeys')
    CREATE TABLE dbo.ImpoundLotLicenseKeys(
        ImpoundLotLicenseKeyId int IDENTITY(1,1) NOT NULL,
        Name VARCHAR(50) NOT NULL,
        Required BIT DEFAULT(0),
        ShowValueOnPrintables BIT DEFAULT(0),
        ShowExpirationOnPrintables BIT DEFAULT(0)

        CONSTRAINT PK_ImpoundLotLicenseKeys PRIMARY KEY CLUSTERED  (ImpoundLotLicenseKeyId ASC)
    )
GO

 GRANT SELECT ON ImpoundLotLicenseKeys to Public
 GRANT INSERT ON ImpoundLotLicenseKeys to Public
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='dbo.ImpoundLotLicenseKeyValues')
    CREATE TABLE dbo.ImpoundLotLicenseKeyValues(
        ImpoundLotLicenseKeyValueId INT IDENTITY(1,1) NOT NULL,
        ImpoundLotLicenseKeyId INT NOT NULL,
        ImpoundLotId INT NOT NULL,
        Value VARCHAR(256) NOT NULL,
        ExpirationDate DATETIME NULL,
        LastUpdatedByUserId INT NULL,
        LastUpdatedDate DATETIME NULL,
        CreateDate DATETIME Default(GetDate())

        CONSTRAINT PK_ImpoundLotLicenseKeyValues PRIMARY KEY CLUSTERED  (ImpoundLotLicenseKeyValueId ASC),
        CONSTRAINT FK_ImpoundLotLicenseKeyValues_Lots FOREIGN KEY (ImpoundLotId) REFERENCES ImpoundLots(ImpoundLotId)
    )
GO

 GRANT SELECT ON ImpoundLotLicenseKeyValues to Public
 GRANT INSERT ON ImpoundLotLicenseKeyValues to Public
 GRANT UPDATE ON ImpoundLotLicenseKeyValues to Public
 GRANT DELETE ON ImpoundLotLicenseKeyValues to Public
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='dbo.ImpoundLotLicenseKeyAssociations')
    CREATE TABLE dbo.ImpoundLotLicenseKeyAssociations(
        ImpoundLotLicenseKeyAssociationId int IDENTITY(1,1) NOT NULL,
        ImpoundLotLicenseKeyId INT NOT NULL,
        Field VARCHAR(50) NOT NULL,
        Value VARCHAR(50) NOT NULL

        CONSTRAINT PK_ImpoundLotLicenseKeyAssociations PRIMARY KEY CLUSTERED  (ImpoundLotLicenseKeyAssociationId ASC)
    )
GO

 GRANT SELECT ON ImpoundLotLicenseKeyAssociations to Public
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='SecurityToolSettings')
    BEGIN
        CREATE TABLE dbo.SecurityToolSettings(
            SecurityToolSettingsId int IDENTITY(1,1) NOT NULL,
            CompanyId int NOT NULL,
            OwnerUserId int NOT NULL,
            CreateDate datetime2 default(getdate()) NOT NULL,
            Deleted BIT NOT NULL DEFAULT 0,
            DeletedDate datetime2 NULL,
            DeletedByUserId int NULL,
            PreventDriversFromSigningInOnWeb BIT NOT NULL,
            PreventDispatchersSigningInOnMobile BIT NOT NULL,
            RestrictManagersBasedOnIpAddress BIT NOT NULL,
            RestrictAccountantsBasedOnIpAddress BIT NOT NULL,
            RestrictDispatchersBasedOnIpAddress BIT NOT NULL,
            RestrictDriversBasedOnIpAddress BIT NOT NULL,

            Constraint PK_SecurityToolSettings PRIMARY KEY CLUSTERED (SecurityToolSettingsId ASC),
            Constraint FK_SecurityToolSettings_Company FOREIGN KEY (CompanyId) REFERENCES Companies(CompanyId),
            Constraint FK_SecurityToolSettings_OwnerUser FOREIGN KEY (OwnerUserId) REFERENCES Users(UserId),
            Constraint FK_SecurityToolSettings_DeletedByUser FOREIGN KEY (DeletedByUserId) REFERENCES Users(UserId),
        )
        CREATE INDEX [IX_SecurityToolSettings_CompanyId] ON [dbo].[SecurityToolSettings] (CompanyId)

    END
GO
GRANT SELECT, INSERT, UPDATE ON SecurityToolSettings to public
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='SecurityToolSettingsHistory' AND TABLE_SCHEMA='dbo')
    BEGIN
        CREATE TABLE dbo.SecurityToolSettingsHistory (
            SecurityToolSettingsHistoryId int IDENTITY(1,1) NOT NULL,
            SecurityToolSettingsId INT NULL,
            CompanyId INT NOT NULL,
            OwnerUserId INT NOT NULL,
            CreateDate DATETIME2 NOT NULL,
            Description VARCHAR(128) NOT NULL,
            IpAddress VARCHAR(255) NULL,

            CONSTRAINT PK_SecurityToolSettingsHistory PRIMARY KEY CLUSTERED (SecurityToolSettingsHistoryId ASC),
            CONSTRAINT FK_SecurityToolSettingsHistory_Settings FOREIGN KEY (SecurityToolSettingsId) REFERENCES SecurityToolSettings(SecurityToolSettingsId),
            CONSTRAINT FK_SecurityToolSettingsHistory_Companies FOREIGN KEY (CompanyId) REFERENCES Companies(CompanyId),
            CONSTRAINT FK_SecurityToolSettingsHistory_Users FOREIGN KEY (OwnerUserId) REFERENCES Users(UserId)
        )
        CREATE INDEX [IX_SecurityToolSettingsHistory_ByCompany] ON [dbo].[SecurityToolSettingsHistory] (CompanyId)
    END
GO
GRANT SELECT ON SecurityToolSettingsHistory to Public
GRANT INSERT ON SecurityToolSettingsHistory to Public
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='CompanyApprovedIpAddresses' AND TABLE_SCHEMA='dbo')
    BEGIN
        CREATE TABLE dbo.CompanyApprovedIpAddresses(
            CompanyApprovedIpAddressId int IDENTITY(1,1) NOT NULL,
            CompanyId int NOT NULL,
            OwnerUserId int NOT NULL,
            EditedByIpAddress [varchar](255) NOT NULL,
            CreateDate DateTime2 NOT NULL,
            Deleted BIT NOT NULL DEFAULT 0,
            DeletedDate DateTime2 NULL,
            DeletedByUserId int NULL,
            Name [varchar](255) NOT NULL,
            IpAddress [varchar](255) NOT NULL,

            Constraint PK_CompanyApprovedIpAddresses PRIMARY KEY CLUSTERED (CompanyApprovedIpAddressId ASC),
            Constraint FK_CompanyApprovedIpAddresses_Company FOREIGN KEY (CompanyId) REFERENCES Companies(CompanyId),
            Constraint FK_CompanyApprovedIpAddresses_OwnerUser FOREIGN KEY (OwnerUserId) REFERENCES Users(UserId),
            Constraint FK_CompanyApprovedIpAddresses_DeletedByUser FOREIGN KEY (DeletedByUserId) REFERENCES Users(UserId),
)

CREATE INDEX [IX_CompanyApprovedIpAddresses_CompanyId] ON [dbo].CompanyApprovedIpAddresses (CompanyId)
END
GO
GRANT SELECT ON CompanyApprovedIpAddresses to Public
GRANT INSERT ON CompanyApprovedIpAddresses to Public
GRANT UPDATE ON CompanyApprovedIpAddresses to Public
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='LedgerAccounts' AND TABLE_SCHEMA='dbo')
    CREATE TABLE dbo.LedgerAccounts(
	    LedgerAccountId int IDENTITY (1,1) NOT NULL,
        Name varchar(100) NOT NULL,
        Description varchar(100),
        CompanyId int,
        ParentId int,
        CreateDate datetime2 not null default(getdate()),
        CONSTRAINT PK_LedgerAccounts PRIMARY KEY CLUSTERED  (LedgerAccountId ASC)
    )
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='dbo.PropertyReleaseForms')
    CREATE TABLE dbo.PropertyReleaseForms(
        PropertyReleaseFormId int IDENTITY(1,1) NOT NULL,
        ImpoundId INT NOT NULL,
        Description varchar(500) NULL,
        Status INT NOT NULL,
        OwnerUserId INT NOT NULL,
        CreateDate DATETIME2 DEFAULT(GETDATE()) NOT NULL,
        IsDeleted BIT DEFAULT(0),
        DeletedByUserId INT NULL,
        DeleteDate DATETIME2 NULL

        CONSTRAINT PK_PropertyReleaseForms PRIMARY KEY CLUSTERED  (PropertyReleaseFormId ASC),
        CONSTRAINT FK_PropertyReleaseForms_Impounds FOREIGN KEY (ImpoundId) REFERENCES Impounds(ImpoundId),
        CONSTRAINT FK_PropertyReleaseForms_Users FOREIGN KEY (OwnerUserId) REFERENCES Users(UserId),
        CONSTRAINT FK_PropertyReleaseForms_Users_deletedBy FOREIGN KEY (DeletedByUserId) REFERENCES Users(UserId)
    )
    GO
    GRANT SELECT, INSERT, UPDATE ON PropertyReleaseForms to Public
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='DriverReplays' AND TABLE_SCHEMA='dbo')
BEGIN
        CREATE TABLE DriverReplays (
            DriverReplayId INT PRIMARY KEY IDENTITY(1,1),
            DriverReplayGuid UNIQUEIDENTIFIER NOT NULL,
            DispatchEntryId INT NOT NULL,
            DriverId INT NOT NULL,
            CompanyId INT NOT NULL,
            CreateDate DATETIME NOT NULL,
            OwnerUserId INT NOT NULL,
            IsDisabled BIT NOT NULL,
            DisabledDate DATETIME NULL
            
            Constraint FK_DriverReplays_DispatchEntries FOREIGN KEY (DispatchEntryId) REFERENCES DispatchEntries(DispatchEntryId),
            Constraint FK_DriverReplays_Drivers FOREIGN KEY (DriverId) REFERENCES Drivers(DriverId),
            Constraint FK_DriverReplays_Companies FOREIGN KEY (CompanyId) REFERENCES Companies(CompanyId),
            Constraint FK_DriverReplays_Users FOREIGN KEY (OwnerUserId) REFERENCES Users(UserId),
        )
        CREATE INDEX [IX_DriverReplay_DriverReplay_Guid] ON [dbo].[DriverReplays] ([DriverReplayGuid])
END
GO
GRANT SELECT, INSERT, UPDATE to public
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchEntryPaymentTips' AND TABLE_SCHEMA='dbo')
BEGIN
    CREATE TABLE DispatchEntryPaymentTips (
        DispatchEntryPaymentTipId INT IDENTITY (1,1) NOT NULL,
        DispatchEntryId INT NOT NULL,
        DispatchEntryPaymentId INT,
        Amount SMALLMONEY NOT NULL,
        DriverId INT,
        CreateDate DateTime2 NOT NULL DEFAULT(getDate()),
        OwnerUserId INT NOT NULL,
        IsVoided BIT DEFAULT(0),
        VoidedByUserId INT,
        VoidedDate DateTime

        CONSTRAINT [PK_DispatchEntryPaymentTips] PRIMARY KEY CLUSTERED  ( DispatchEntryPaymentTipId ASC ),
        CONSTRAINT [FK_DispatchEntryPaymentTips_DispatchEntryId] FOREIGN KEY([DispatchEntryId]) REFERENCES [dbo].[DispatchEntries] ([DispatchEntryId]),
        CONSTRAINT [FK_DispatchEntryPaymentTips_DispatchEntryPaymentId] FOREIGN KEY([DispatchEntryPaymentId]) REFERENCES [dbo].[DispatchEntryPayments] ([DispatchEntryPaymentId]),
        CONSTRAINT [FK_DispatchEntryPaymentTips_DriverId] FOREIGN KEY([DriverId]) REFERENCES [dbo].[Drivers] ([DriverId]),
        CONSTRAINT [FK_DispatchEntryPaymentTips_UserId] FOREIGN KEY([OwnerUserId]) REFERENCES [dbo].[Users] ([UserId]),
	)

    CREATE NONCLUSTERED INDEX IK_DispatchEntryPaymentTips_byDispatchEntryId ON dbo.DispatchEntryPaymentTips (DispatchEntryId)
    CREATE NONCLUSTERED INDEX IK_DispatchEntryPaymentTips_byDriverId ON dbo.DispatchEntryPaymentTips (DriverId)
END
GO

    GRANT SELECT ON DispatchEntryPaymentTips to Public
    GRANT INSERT ON DispatchEntryPaymentTips to Public
    GRANT UPDATE ON DispatchEntryPaymentTips to Public
GO

