using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using Extric.Towbook.Accounts;

namespace Extric.Towbook.API.Models.Accounts
{
    public class AccountPublicRequestFormTemplateOfficeHourModel
    {
        public string Day { get; set; }
        public int DayOfWeekId { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public bool AllDay { get; set; }


        public static AccountPublicRequestFormTemplateOfficeHourModel Map(AccountPublicRequestFormTemplateOfficeHour item)
        {
            if (item == null)
                return null;

            var today = DateTime.Today;
            var startTime = today.Add(item.StartTime);
            var endTime = today.Add(item.EndTime);

            // if storing times in server time (EST) has caused a day crossover
            // when offsetted to server time and has made the end time less than
            // the start time, then add one day to compensate for the offset.
            if (item.EndTime < item.StartTime)
                endTime = endTime.AddDays(1); 

            return new AccountPublicRequestFormTemplateOfficeHourModel()
            {
                Day = item.DayOfWeek.ToString(),
                DayOfWeekId = (int)item.DayOfWeek,
                StartTime = startTime,
                EndTime = endTime,
                AllDay = (endTime - startTime).TotalSeconds > (23*59*60)
            };
        }

        public static AccountPublicRequestFormTemplateOfficeHourModel[] Map(IEnumerable<AccountPublicRequestFormTemplateOfficeHour> items)
        {
            var models = Array.Empty<AccountPublicRequestFormTemplateOfficeHourModel>();

            if (items == null)
                return models;

            foreach(var i in items)
            {
                var ret = Map(i);

                if (ret != null)
                    models = models.Union(new[] { ret }).ToArray();

            }

            return models;
        }
    }
}
