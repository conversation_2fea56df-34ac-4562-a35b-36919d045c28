using System;
using System.Collections.Generic;
using System.Linq;

namespace Extric.Towbook.Api.Models
{
    /// <summary>
    /// Represents a Driver/Truck assignment pair
    /// </summary>
    public sealed class DriverTruckAssignmentModel
    {
        public int TruckId { get; set; }
        public int DriverId { get; set; }
        public int SourceId { get; set; }

        public DateTime? ModifiedDate { get; set; }

        public long? QueueItemId { get; set; }

        public static DriverTruckAssignmentModel Map(DriverTruckDefault m)
        {
            if (m == null)
                return null;

            var r = new DriverTruckAssignmentModel();

            r.TruckId = m.TruckId;
            r.DriverId = m.DriverId;
            r.SourceId = (int)m.SourceId;
            r.ModifiedDate = m.ModifiedDate;

            return r;
        }
    }
}
