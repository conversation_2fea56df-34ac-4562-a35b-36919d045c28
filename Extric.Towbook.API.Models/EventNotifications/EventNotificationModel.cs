using Extric.Towbook.EventNotifications;
using Extric.Towbook.Utility;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;

namespace Extric.Towbook.API.Models
{
    public class EventNotificationTypeModel
    {
        public DeliveryMethodType Id { get; set; }
        public string Name { get; set; }
        public bool Readonly { get; set; }
        public Collection<RequiredUserType> DefaultUserTypeItems { get; set; }

        public Collection<RequiredUserType> OptOutUserTypeItems { get; set; }
        public Collection<int> OptOutUserItems { get; set; }

        public Collection<RequiredUserType> UserTypeItems { get; set; }
        public Collection<int> UserItems { get; set; }

        public EventNotificationTypeModel()
        {
        }
    }

    public class EventNotificationAlertSoundModel
    {
        public int Id { get; set; }
        public string Name { get; set; }
        
    }


    public class EventNotificationModel
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public EventNotificationGroup Group { get; set; }

        public Collection<EventNotificationTypeModel> Types { get; set; }
        public EventNotificationAlertSoundModel AlertSound { get; set; }


        public static EventNotificationModel Map(EventNotification o)
        {
            var model = new EventNotificationModel();

            model.Id = o.Id;
            model.Name = o.Name;
            model.Description = o.Description;
            model.Group = o.Group;

            model.Types = new Collection<EventNotificationTypeModel>();
            var methods = Enum.GetValues(typeof(DeliveryMethodType))
                                    .Cast<DeliveryMethodType>()
                                    .ToDictionary(c => (int)c, c => c.ToString())
                                    .ToCollection();

            foreach (var method in methods)
            {

                EventNotificationTypeModel typeModel = new EventNotificationTypeModel();
                typeModel.DefaultUserTypeItems = new Collection<RequiredUserType>();

                typeModel.Id = (DeliveryMethodType)method.Key;
                typeModel.Name = method.Value;

                switch (method.Value) {
                    case "Email":
                        typeModel.Readonly = o.EmailDefaultType != EventNotificationDefaultType.Public;
                        if (o.EmailManagerRequired)
                            typeModel.DefaultUserTypeItems.Add(RequiredUserType.Managers);
                        if (o.EmailDispatcherRequired)
                            typeModel.DefaultUserTypeItems.Add(RequiredUserType.Dispatchers);
                        if (o.EmailDriverRequired)
                            typeModel.DefaultUserTypeItems.Add(RequiredUserType.Drivers);
                        break;

                    case "Text":
                        typeModel.Readonly = o.TextDefaultType != EventNotificationDefaultType.Public;
                        if (o.TextManagerRequired)
                            typeModel.DefaultUserTypeItems.Add(RequiredUserType.Managers);
                        if (o.TextDispatcherRequired)
                            typeModel.DefaultUserTypeItems.Add(RequiredUserType.Dispatchers);
                        if (o.TextDriverRequired)
                            typeModel.DefaultUserTypeItems.Add(RequiredUserType.Drivers);
                        break;

                    case "Mobile":
                        typeModel.Readonly = o.PushNotificationDefaultType != EventNotificationDefaultType.Public;
                        if (o.PushNotificationManagerRequired)
                            typeModel.DefaultUserTypeItems.Add(RequiredUserType.Managers);
                        if (o.PushNotificationDispatcherRequired)
                            typeModel.DefaultUserTypeItems.Add(RequiredUserType.Dispatchers);
                        if (o.PushNotificationDriverRequired)
                            typeModel.DefaultUserTypeItems.Add(RequiredUserType.Drivers);
                        break;

                    case "Desktop":
                        typeModel.Readonly = o.WebNotificationDefaultType != EventNotificationDefaultType.Public;
                        if (o.WebNotificationManagerRequired)
                            typeModel.DefaultUserTypeItems.Add(RequiredUserType.Managers);
                        if (o.WebNotificationDispatcherRequired)
                            typeModel.DefaultUserTypeItems.Add(RequiredUserType.Dispatchers);
                        if (o.WebNotificationDriverRequired)
                            typeModel.DefaultUserTypeItems.Add(RequiredUserType.Drivers);
                        break;
                }

                model.Types.Add(typeModel);
            }

            return model;
        }


        public static EventNotificationModel Map(EventNotification o,
            Collection<EventUserTypeNotification> userTypeItems,
            Collection<EventUserNotification> userItems, 
            IEnumerable<EventNotificationAlertSound> savedSounds = null)
        {
            var model = Map(o);

            var soundId = savedSounds?.FirstOrDefault(w => w.EventNotificationId == o.Id && w.UserId.GetValueOrDefault() == 0)?.EventNotificationSoundId ?? EventNotificationSound.Alert.Id;
            var sound = EventNotificationSound.GetById(soundId);

            model.AlertSound = new EventNotificationAlertSoundModel
            {
                Id = sound.Id,
                Name = sound.Name
            };

            var types = Enum.GetValues(typeof(DeliveryMethodType))
                                    .Cast<DeliveryMethodType>()
                                    .ToDictionary(c => (int)c, c => c.ToString())
                                    .ToCollection();


            foreach (var type in model.Types)
            {
                if (type.Id == DeliveryMethodType.Internal)
                    continue;

                // instantiate collections
                if (type.UserTypeItems == null)
                    type.UserTypeItems = new Collection<RequiredUserType>();
                if (type.UserItems == null)
                    type.UserItems = new Collection<int>();
                if (type.OptOutUserTypeItems == null)
                    type.OptOutUserTypeItems = new Collection<RequiredUserType>();
                if (type.OptOutUserItems == null)
                    type.OptOutUserItems = new Collection<int>();


                // userTypeItems
                foreach (var userTypeItem in userTypeItems)
                {
                    if (userTypeItem.EventNotificationId == model.Id)
                    {
                        if (type.Id == DeliveryMethodType.Email)
                        {
                            if (userTypeItem.RequireEmail != null)
                            {
                                if (userTypeItem.RequireEmail.Value)
                                    type.UserTypeItems.Add((RequiredUserType)userTypeItem.UserTypeId);
                                else
                                    type.OptOutUserTypeItems.Add((RequiredUserType)userTypeItem.UserTypeId);
                            }
                        }
                        else if (type.Id == DeliveryMethodType.Text)
                        {
                            if (userTypeItem.RequireText != null)
                            {
                                if (userTypeItem.RequireText.Value)
                                    type.UserTypeItems.Add((RequiredUserType)userTypeItem.UserTypeId);
                                else
                                    type.OptOutUserTypeItems.Add((RequiredUserType)userTypeItem.UserTypeId);
                            }
                        }
                        else if (type.Id == DeliveryMethodType.Mobile)
                        {
                            if (userTypeItem.RequirePushNotification != null)
                            {
                                if (userTypeItem.RequirePushNotification.Value)
                                    type.UserTypeItems.Add((RequiredUserType)userTypeItem.UserTypeId);
                                else
                                    type.OptOutUserTypeItems.Add((RequiredUserType)userTypeItem.UserTypeId);
                            }
                        }
                        else if (type.Id == DeliveryMethodType.Desktop)
                        {
                            if (userTypeItem.RequireWebNotification != null)
                            {
                                if (userTypeItem.RequireWebNotification.Value)
                                    type.UserTypeItems.Add((RequiredUserType)userTypeItem.UserTypeId);
                                else
                                    type.OptOutUserTypeItems.Add((RequiredUserType)userTypeItem.UserTypeId);
                            }
                        }
                    }
                }


                // userItems
                foreach (var userItem in userItems ?? new Collection<EventUserNotification>())
                {
                    if (userItem.EventNotificationId == model.Id)
                    {
                        if (type.Id == DeliveryMethodType.Email)
                        {
                            if (userItem.RequireEmail != null)
                            {
                                if (userItem.RequireEmail.Value)
                                    type.UserItems.Add(userItem.UserId);
                                else
                                    type.OptOutUserItems.Add(userItem.UserId);
                            }

                        }
                        else if (type.Id == DeliveryMethodType.Text)
                        {
                            if (userItem.RequireText != null)
                            {
                                if (userItem.RequireText.Value)
                                    type.UserItems.Add(userItem.UserId);
                                else
                                    type.OptOutUserItems.Add(userItem.UserId);
                            }
                        }
                        else if (type.Id == DeliveryMethodType.Mobile)
                        {
                            if (userItem.RequirePushNotification != null)
                            {
                                if (userItem.RequirePushNotification.Value)
                                    type.UserItems.Add(userItem.UserId);
                                else
                                    type.OptOutUserItems.Add(userItem.UserId);
                            }
                        }
                        else if (type.Id == DeliveryMethodType.Desktop)
                        {
                            if (userItem.RequireWebNotification != null)
                            {
                                if (userItem.RequireWebNotification.Value)
                                    type.UserItems.Add(userItem.UserId);
                                else
                                    type.OptOutUserItems.Add(userItem.UserId);
                            }
                        }
                    }
                }
            }

            return model;
        }

    }
}