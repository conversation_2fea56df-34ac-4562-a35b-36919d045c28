using Extric.Towbook.API.Utils.Models;
using SkiaSharp;
using System.Reflection;
using BitMiracle.LibTiff.Classic;
using IronPdf.Editing;
using IronSoftware.Drawing;

namespace Extric.Towbook.API.Utils.Services
{
    public class PdfService
    {
        private readonly ILogger<PdfService> _logger;

        public static readonly string basePath = 
            Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location)!;
        public static readonly string TIF_TEMPLATE_PATH =
            Path.Combine(
            basePath,
            "Assets",
            "tif-template-html.html");

        public PdfService(ILogger<PdfService> logger)
        {
            _logger = logger;
        }

        public async Task<MemoryStream> GenerateDummyPdfAsync()
        {
            var renderer = new ChromePdfRenderer(); // Instantiates Chrome Renderer
            var pdf = await renderer.RenderHtmlAsPdfAsync(" <h1> ~Hello World~ </h1> Made with IronPDF!");
            var document = pdf.SaveAs($"{basePath}\\html_saved.pdf"); // Saves our PdfDocument object as a PDF

            File.Delete($"{basePath}\\html_saved.pdf");
            return document.Stream;
        }
        #region GenerateComplexHtmlToPdf
        public async Task<MemoryStream> GenerateComplexHtmlToPdfAsync()
        {
            var millisDelay = 500;

            var renderer = new ChromePdfRenderer
            {
                RenderingOptions = new ChromePdfRenderOptions()
                {
                    EnableJavaScript = true,
                }
            };
            renderer.RenderingOptions.WaitFor.RenderDelay(millisDelay);

            var pdf = await renderer.RenderHtmlAsPdfAsync("<html itemscope=\"\" itemtype=\"http://schema.org/WebPage\" lang=\"en-BO\"><head><meta charset=\"UTF-8\"><meta content=\"origin\" name=\"referrer\"><meta content=\"/images/branding/googleg/1x/googleg_standard_color_128dp.png\" itemprop=\"image\"><title>Google</title><script src=\"https://apis.google.com/_/scs/abc-static/_/js/k=gapi.gapi.en.SGzW6IeCawI.O/m=gapi_iframes,googleapis_client/rt=j/sv=1/d=1/ed=1/am=AACA/rs=AHpOoo-5biO9jua-6zCEovdoDJ8SLzd6sw/cb=gapi.loaded_0\" nonce=\"CP311SD5BVmtqozsXvXMVQ\" async=\"\"></script><script nonce=\"CP311SD5BVmtqozsXvXMVQ\">window._hst=Date.now();performance&&performance.mark&&performance.mark(\"SearchHeadStart\");</script><script nonce=\"CP311SD5BVmtqozsXvXMVQ\">(function(){var _g={kEI:'sNsXZ8SAEsff5OUPju6tyAE',kEXPI:'31',u:'5d2feb90',kBL:'W56S',kOPI:********};(function(){var a;((a=window.google)==null?0:a.stvsc)?google.kEI=_g.kEI:window.google=_g;}).call(this);})();(function(){google.sn='webhp';google.kHL='en-BO';})();(function(){\nvar h=this||self;function l(){return window.google!==void 0&&window.google.kOPI!==void 0&&window.google.kOPI!==0?window.google.kOPI:null};var m,n=[];function p(a){for(var b;a&&(!a.getAttribute||!(b=a.getAttribute(\"eid\")));)a=a.parentNode;return b||m}function q(a){for(var b=null;a&&(!a.getAttribute||!(b=a.getAttribute(\"leid\")));)a=a.parentNode;return b}function r(a){/^http:/i.test(a)&&window.location.protocol===\"https:\"&&(google.ml&&google.ml(Error(\"a\"),!1,{src:a,glmm:1}),a=\"\");return a}\nfunction t(a,b,c,d,k){var e=\"\";b.search(\"&ei=\")===-1&&(e=\"&ei=\"+p(d),b.search(\"&lei=\")===-1&&(d=q(d))&&(e+=\"&lei=\"+d));d=\"\";var g=b.search(\"&cshid=\")===-1&&a!==\"slh\",f=[];f.push([\"zx\",Date.now().toString()]);h._cshid&&g&&f.push([\"cshid\",h._cshid]);c=c();c!=null&&f.push([\"opi\",c.toString()]);for(c=0;c<f.length;c++){if(c===0||c>0)d+=\"&\";d+=f[c][0]+\"=\"+f[c][1]}return\"/\"+(k||\"gen_204\")+\"?atyp=i&ct=\"+String(a)+\"&cad=\"+(b+e+d)};m=google.kEI;google.getEI=p;google.getLEI=q;google.ml=function(){return null};google.log=function(a,b,c,d,k,e){e=e===void 0?l:e;c||(c=t(a,b,e,d,k));if(c=r(c)){a=new Image;var g=n.length;n[g]=a;a.onerror=a.onload=a.onabort=function(){delete n[g]};a.src=c}};google.logUrl=function(a,b){b=b===void 0?l:b;return t(\"\",a,b)};}).call(this);(function(){google.y={};google.sy=[];var d;(d=google).x||(d.x=function(a,b){if(a)var c=a.id;else{do c=Math.random();while(google.y[c])}google.y[c]=[a,b];return!1});var e;(e=google).sx||(e.sx=function(a){google.sy.push(a)});google.lm=[];var f;(f=google).plm||(f.plm=function(a){google.lm.push.apply(google.lm,a)});google.lq=[];var g;(g=google).load||(g.load=function(a,b,c){google.lq.push([[a],b,c])});var h;(h=google).loadAll||(h.loadAll=function(a,b){google.lq.push([a,b])});google.bx=!1;var k;(k=google).lx||(k.lx=function(){});var l=[],m;(m=google).fce||(m.fce=function(a,b,c,n){l.push([a,b,c,n])});google.qce=l;}).call(this);google.f={};(function(){\ndocument.documentElement.addEventListener(\"submit\",function(b){var a;if(a=b.target){var c=a.getAttribute(\"data-submitfalse\");a=c===\"1\"||c===\"q\"&&!a.elements.q.value?!0:!1}else a=!1;a&&(b.preventDefault(),b.stopPropagation())},!0);document.documentElement.addEventListener(\"click\",function(b){var a;a:{for(a=b.target;a&&a!==document.documentElement;a=a.parentElement)if(a.tagName===\"A\"){a=a.getAttribute(\"data-nohref\")===\"1\";break a}a=!1}a&&b.preventDefault()},!0);}).call(this);(function(){google.hs={h:true,nhs:false,sie:false};})();(function(){google.c={btfi:false,c4t:false,caf:false,cap:2000,cfr:false,cli:true,csp:false,di:false,doiu:0,fla:false,fli:false,gl:true,irsf:false,lhc:false,marb:true,mcc:false,pci:true,raf:false,taf:true,timl:false,tprc:false,vis:true,wh0:false,whu:false};})();(function(){\nvar p=this||self;window.google=window.google||{};var r=window.performance&&window.performance.timing&&\"navigationStart\"in window.performance.timing,aa=google.stvsc&&google.stvsc.ns,t=r?aa||window.performance.timing.navigationStart:void 0;function u(){return window.performance.now()-(google.stvsc&&google.stvsc.pno||0)}var ba=google.stvsc&&google.stvsc.rs,v=r?ba||window.performance.timing.responseStart:void 0;function ca(a,b,c){a:{for(var d=a;d&&d!==b;d=d.parentElement)if(d.style.overflow===\"hidden\"||d.tagName===\"G-EXPANDABLE-CONTENT\"&&getComputedStyle(d).getPropertyValue(\"overflow\")===\"hidden\"){b=d;break a}b=null}if(!b)return!1;a=c(a);c=c(b);return a.bottom<c.top||a.top>=c.bottom||a.right<c.left||a.left>=c.right}\nfunction da(a){return a.style.display===\"none\"?!0:document.defaultView&&document.defaultView.getComputedStyle?(a=document.defaultView.getComputedStyle(a),!!a&&(a.visibility===\"hidden\"||a.height===\"0px\"&&a.width===\"0px\")):!1}\nfunction ea(a,b,c,d,e){var h=e(a),k=h.left+(c?0:window.pageXOffset),m=h.top+(c?0:window.pageYOffset),n=h.width,f=h.height,g=0;if(!b&&f<=0&&n<=0)return g;b=window.innerHeight||document.documentElement.clientHeight;m+f<0?g=2:m>=b&&(g=4);if(k+n<0||k>=(window.innerWidth||document.documentElement.clientWidth))g|=8;else if(d){k=h.left;if(!c)for(;a&&a!==d;a=a.parentElement)k+=a.scrollLeft;d=e(d);if(k+n<d.left||k>=d.right)g|=8;h.top>=d.bottom&&(g|=4)}g||(g=1,m+f>b&&(g|=4));return g};var fa=google.c.cap,ha=google.c.csp,ia=google.c.cli,ja=google.c.doiu,w=google.c.vis,x=google.c.gl,ka=google.c.marb,la=google.c.taf,ma=google.c.wh0,y=google.c.timl,na=google.c.whu,oa=google.c.pci;function z(a,b){google.tick(\"load\",a,b)}function A(a,b){google.c.e(\"load\",a,String(b))}function B(a,b,c,d){a.addEventListener?a.addEventListener(b,c,d||!1):a.attachEvent&&a.attachEvent(\"on\"+b,c)}function C(a,b,c,d){\"addEventListener\"in a?a.removeEventListener(b,c,d||!1):a.attachEvent&&a.detachEvent(\"on\"+b,c)};var pa=function(a,b,c){this.g=a;this.v=[];this.B=this.g.hasAttribute(\"data-noaft\");this.j=!!this.g.getAttribute(\"data-deferred\");var d;if(d=!this.j)a:{for(d=0;d<D.length;++d)if(a.getAttribute(\"data-\"+D[d])){d=!0;break a}d=!1}this.l=d;this.F=this.g.hasAttribute(\"data-bsrc\");(a=this.g.src)&&this.l&&(this.D=a);!this.l&&typeof a===\"string\"&&a||this.g.setAttribute(\"data-lzy_\",\"1\");this.B?b=!0:b||x&&this.i||this.j||this.l?b=!1:(b=this.g.src,b=typeof b!==\"string\"||!b,a=this.g.getAttribute(\"data-cmp\"),b=\na!==null?a===\"1\":b||this.g.complete);this.A=b;x||this.A||this.i||E(this);w&&!c&&F(this)},E=function(a){google.rll(a.g,!0,function(){G(a,Date.now())})},F=function(a){if(a.C===void 0){var b=a.g;var c;a:{for(c=b;c;c=c.parentElement)if(c.tagName===\"G-SCROLLING-CAROUSEL\"||ha&&c.classList.contains(\"XNfAUb\"))break a;c=null}var d=b.parentElement;if(d&&(d.tagName===\"G-IMG\"||d.classList.contains(\"uhHOwf\"))&&(d.style.height||d.style.width)){var e=d.getBoundingClientRect(),h=b.getBoundingClientRect();if(ia){if(e.height<\nh.height||e.width<h.width)b=d}else if(e.height<=h.height||e.width<=h.width)b=d}b=google.cv(b,!1,void 0,c);a.C=b}return a.C},G=function(a,b){if(a.D&&a.g.src===a.D||a.g.getAttribute(\"data-deferred\")===\"1\")x||E(a);else if(!a.i){a.j&&a.g.setAttribute(\"data-deferred\",\"3\");a.i=b;b=a.i;for(var c=0;c<a.v.length;++c)a.v[c](b,a.g);a.v.length=0}},D=\"src bsrc url ll image img-url lioi\".split(\" \");google.c.iim=google.c.iim||{};var H=google.c.iim,qa=0;function I(a,b,c,d){var e=a.getAttribute(\"data-csiid\");e||(e=String(++qa),oa&&(e=google.kEI+\"_\"+e));H[e]||(a.setAttribute(\"data-csiid\",e),H[e]=b?b(a):new pa(a,c,d));return H[e]}function J(a){for(var b=document.getElementsByTagName(\"img\"),c=0,d=b.length;c<d;++c)a(I(b[c]))};function ra(a){if(a&&(a=a.target,a.tagName===\"IMG\")){var b=Date.now();G(I(a,void 0,!0,!0),b)}}function K(a){google.c.oil(a)};google.timers={};google.startTick=function(a){google.timers[a]={t:{start:Date.now()},e:{},m:{}}};google.tick=function(a,b,c){google.timers[a]||google.startTick(a);c=c!==void 0?c:Date.now();b instanceof Array||(b=[b]);for(var d=0,e;e=b[d++];)google.timers[a].t[e]=c};google.c.e=function(a,b,c){google.timers[a].e[b]=c};google.c.b=function(a,b){b=google.timers[b||\"load\"].m;b[a]&&google.ml(Error(\"a\"),!1,{m:a});b[a]=!0};google.c.u=function(a,b){var c=google.timers[b||\"load\"];b=c.m;if(b[a]){b[a]=!1;for(a in b)if(b[a])return!1;x&&(C(document.documentElement,\"load\",K,!0),C(document.documentElement,\"error\",K,!0));google.csiReport(c,\"all\");return!0}c=\"\";for(var d in b)c+=d+\":\"+b[d]+\";\";google.ml(Error(\"b\"),!1,{m:a,b:b[a]===!1,s:c});return!1};google.rll=function(a,b,c){function d(e){c(e);C(a,\"load\",d);C(a,\"error\",d)}B(a,\"load\",d);b&&B(a,\"error\",d)};p.google.aft=function(a){a.setAttribute(\"data-iml\",String(Date.now()))};google.startTick(\"load\");google.tick(\"load\",\"hst\",window._hst);var L=google.timers.load;if(!google.stvsc||google.stvsc.sw){var M=L.t,N=window.performance;N&&(t&&v&&v>t&&v<=M.start?(M.start=v,L.wsrt=v-t):N.now&&(L.wsrt=Math.floor(u())))}google.c.b(\"xe\",\"load\");!window._hst&&performance&&performance.mark&&performance.mark(\"SearchHeadStart\");var O;if((O=google.stvsc)==null?0:O.start)google.timers.load.t.start=google.stvsc.start;function P(a){if(document.visibilityState===\"hidden\"){google.c.fh=a;var b;t&&(b=Math.floor(t+a));google.tick(\"load\",\"fht\",b);return!0}return!1}function Q(a){P(a.timeStamp)&&C(document,\"visibilitychange\",Q,!0)}google.c.fh=Infinity;B(document,\"visibilitychange\",Q,!0);P(0);x&&(google.c.oil=ra,B(document.documentElement,\"load\",K,!0),B(document.documentElement,\"error\",K,!0));google.cv=function(a,b,c,d){if(!a||!b&&da(a))return 0;if(!a.getBoundingClientRect)return 1;var e=function(h){return h.getBoundingClientRect()};return!b&&ca(a,d,e)?0:ea(a,b,c,d,e)};function R(a){try{a()}catch(b){google.ml(b,!1)}}function sa(){if(google.aftq!==null){google.tick(\"load\",\"aftqf\",Date.now());for(var a,b=0,c;c=(a=google.aftq)==null?void 0:a[b++];)R(c);google.aftq=null}}google.caft=function(a){google.aftq===null?R(a):(google.aftq=google.aftq||[],google.aftq.push(a))};function S(){return window.performance&&window.performance.navigation&&window.performance.navigation.type};var ta=window.location,ua=\"aft afti aftr afts cbs cbt fht frts frvt hct hst prt prs sct\".split(\" \");function T(a){return(a=ta.search.match(new RegExp(\"[?&]\"+a+\"=(\\\\d+)\")))?Number(a[1]):-1}\nfunction U(a){var b=google.timers.load,c=b.m;if(!c||!c.prs){c=window._csc===\"agsa\"&&window._cshid;var d=S()||c?0:T(\"qsubts\");d>0&&(c=T(\"fbts\"),c>0&&(b.t.start=Math.max(d,c)));var e=b.t,h=e.start;c={};b.wsrt!==void 0&&(c.wsrt=b.wsrt);if(h)for(var k=0,m;m=ua[k++];){var n=e[m];n&&(c[m]=Math.max(n-h,0))}d>0&&(c.gsasrt=b.t.start-d);b=b.e;a=\"/gen_204?s=\"+google.sn+\"&t=\"+a+\"&atyp=csi&ei=\"+google.kEI+\"&rt=\";d=\"\";for(var f in c)a+=\"\"+d+f+\".\"+c[f],d=\",\";for(var g in b)a+=\"&\"+g+\"=\"+b[g];f=a;g=\"\";a=[];p._cshid&&\na.push([\"cshid\",p._cshid]);b=window.google!==void 0&&window.google.kOPI!==void 0&&window.google.kOPI!==0?window.google.kOPI:null;b!=null&&a.push([\"opi\",b.toString()]);for(b=0;b<a.length;b++){if(b===0||b>0)g+=\"&\";g+=a[b][0]+\"=\"+a[b][1]}a=f+g;(f=google.stvsc)&&(a+=\"&ssr=1\");if(f?f.isBF:S()===2)a+=\"&bb=1\";S()===1&&(a+=\"&r=1\");\"gsasrt\"in c&&(c=T(\"qsd\"),c>0&&(a+=\"&qsd=\"+c));a:{if(window.performance&&window.performance.getEntriesByType&&(c=window.performance.getEntriesByType(\"navigation\"),c.length!==0)){c=\nc[0];break a}c=void 0}c&&(f=c.deliveryType,typeof f===\"string\"&&(a+=\"&dt=\"+f),c=c.transferSize,typeof c===\"number\"&&(a+=\"&ts=\"+c));c=a;typeof navigator.sendBeacon===\"function\"?navigator.sendBeacon(c,\"\"):google.log(\"\",\"\",c)}};function V(a){a&&google.tick(\"load\",\"cbs\",a);google.tick(\"load\",\"cbt\");U(\"cap\")};var wa=function(a){var b=va;b.g=a;b.g&&(b.g(),b.g=null)},va=new function(){this.g=null};function xa(a,b,c){function d(){h||k!==m||c(f,n,g)}function e(l,q){l=Math.max(f,l);f!==l&&(n=f,g=q);f=l;++m;d()}var h=!0,k=0,m=0,n=0,f=0,g;J(function(l){a(l)&&(++k,l.i||l.A?e(l.i||0,l.g):l.v.push(e))});b();h=!1;d()};var W=window.performance;function ya(){if(google.c.c4t&&W&&W.mark&&W.timing){var a=google.timers.load,b=a.wsrt;a=a.t.aft;b&&b>0&&a&&a>0&&(a-=W.timing.navigationStart,a>0&&(W.mark(\"SearchAFTStart\",{startTime:b}),W.mark(\"trigger:SearchAFTEnd\",{startTime:a})))}};var za=!1,X=0,Y=0,Z;function Aa(a,b){na&&!google.c.wh&&(google.c.wh=Math.floor(window.innerHeight||document.documentElement.clientHeight),google.c.wh&&A(\"whu\",\"1\"));var c=google.c.wh,d=!b;b=b?Math.floor(b.getBoundingClientRect().top+window.pageYOffset):-1;var e=ma&&!c?!1:b>=c;Y||!d&&!e||(Y=a,X=b);if(Y){var h=0,k=0,m=0,n=!1;xa(function(f){if(!(F(f)&1))return!1;if(f.A)return++m,!f.B;F(f)&4&&(n=!0);f.j&&++k;++h;return!0},function(){A(\"ima\",h);A(\"imad\",k);A(\"imac\",m);document.getElementsByClassName(\"Ib7Efc\").length&&A(\"ddl\",1);A(\"wh\",c)},function(f,g,l){f&&z(\"afti\",f);g&&z(\"aftip\",g);X>0&&z(\"afts\",Y);if(l){var q;g=((q=l.closest(\"[data-ved]\"))==null?void 0:q.getAttribute(\"data-ved\"))||\"NF\";A(\"aftie\",g)}q=Y;g=X;if(0>q||ka&&c&&X<c&&0>=c)g=q=0;f&&(!la||f>q||n)&&(q=f,g=c);z(\"aft\",q);A(\"aft\",1);A(\"aftp\",g);wa(function(){Z&&clearTimeout(Z);U(\"aft\")});ya();document.visibilityState===\"hidden\"&&A(\"hddn\",1);google.c.u(\"aft\");sa()})}};var Ba=!1;function Ca(a){a=I(a);return x&&w||ja!==0?F(a):0};google.c.wh=Math.floor(window.innerHeight||document.documentElement.clientHeight);google.c.b(\"prt\");var Da=fa||0;if(Da>0)a:{if(t!==void 0){var Ea=u(),Fa=Da-Ea;if(Fa>0){Z=setTimeout(V,Fa,Math.floor(t+Ea));break a}V()}Z=void 0}google.c.maft=function(a,b){x||J(function(){});za||(google.c.b(\"aft\"),za=!0);Y||Aa(a,b)};google.c.miml=function(a){function b(d){var e=F(d);d.g.setAttribute(\"data-atf\",String(e));return y&&!d.B&&(!d.l||d.F||!!(F(d)&1))}function c(d){y&&z(\"iml\",d||a);google.c.u(\"iml\")}Ba||(google.c.b(\"iml\"),function(){xa(b,function(){},c)}(0),Ba=!0)};google.c.ub=function(){};if(!x||w)google.c.setup=Ca;}).call(this);(function(){function b(){for(var a=google.drc.shift();a;)a(),a=google.drc.shift()};google.drc=[function(){google.tick&&google.tick(\"load\",\"dcl\")}];google.dclc=function(a){google.drc.length?google.drc.push(a):a()};window.addEventListener?(document.addEventListener(\"DOMContentLoaded\",b,!1),window.addEventListener(\"load\",b,!1)):window.attachEvent&&window.attachEvent(\"onload\",b);}).call(this);(function(){var b=[];google.jsc={xx:b,x:function(a){b.push(a)},mm:[],m:function(a){google.jsc.mm.length||(google.jsc.mm=a)}};}).call(this);(function(){var f=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},h=typeof Object.defineProperties==\"function\"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},k=function(a){a=[\"object\"==typeof globalThis&&globalThis,a,\"object\"==typeof window&&window,\"object\"==typeof self&&self,\"object\"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error(\"a\");},l=k(this),m=function(a,b){if(b)a:{var c=l;a=a.split(\".\");for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&b!=null&&h(c,a,{configurable:!0,writable:!0,value:b})}},n=function(a){if(!(a instanceof Array)){var b=typeof Symbol!=\"undefined\"&&Symbol.iterator&&a[Symbol.iterator];if(b)a=b.call(a);else if(typeof a.length==\"number\")a={next:f(a)};else throw Error(\"b`\"+String(a));for(var c=[];!(b=a.next()).done;)c.push(b.value);a=c}return a},p=typeof Object.assign==\n\"function\"?Object.assign:function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)Object.prototype.hasOwnProperty.call(d,e)&&(a[e]=d[e])}return a};m(\"Object.assign\",function(a){return a||p});\nvar r=this||self;var t=\"click focusin focusout auxclick change copy dblclick beforeinput input keyup keydown keypress mousedown mouseenter mouseleave mouseout mouseover mouseup paste pointerdown pointerenter pointerleave pointerup touchstart touchmove touchend touchcancel transitioncancel transitionend transitionrun transitionstart dragover dragenter dragleave drop dragstart dragend speech\".split(\" \").concat([\"focus\",\"blur\",\"error\",\"load\"]);\nvar u=[\"focus\",\"blur\",\"error\",\"load\",\"toggle\"];function v(a){return a===\"mouseenter\"?\"mouseover\":a===\"mouseleave\"?\"mouseout\":a===\"pointerenter\"?\"pointerover\":a===\"pointerleave\"?\"pointerout\":a};var x=function(){var a=w;this.v={};this.A={};this.j=null;this.g=[];this.i=a};x.prototype.handleEvent=function(a,b,c){y(this,{eventType:a,event:b,targetElement:b.target,eic:c,timeStamp:Date.now(),eia:void 0,eirp:void 0,eiack:void 0})};var y=function(a,b){if(a.j)a.j(b);else{b.eirp=!0;var c;(c=a.g)==null||c.push(b)}};x.prototype.addEvent=function(a,b){var c=this;if(!(a in this.v)&&this.i){var d=function(g,q,T){c.handleEvent(g,q,T)};this.v[a]=d;b=v(b||a);if(b!==a){var e=this.A[b]||[];e.push(a);this.A[b]=e}this.i.addEventListener(b,function(g){return function(q){d(a,q,g)}})}};x.prototype.s=function(a){return this.v[a]};x.prototype.l=function(){this.i.l();this.i=null;this.v={};this.A={};this.j=null;this.g=[]};x.prototype.ecrd=function(a){this.j=a;var b;if((b=this.g)==null?0:b.length){for(a=0;a<this.g.length;a++)y(this,this.g[a]);this.g=null}};var z=typeof navigator!==\"undefined\"&&/iPhone|iPad|iPod/.test(navigator.userAgent),B=function(){this.g=A;this.i=[]};B.prototype.addEventListener=function(a,b){z&&(this.g.style.cursor=\"pointer\");var c=this.i,d=c.push,e=this.g;b=b(this.g);var g=!1;u.indexOf(a)>=0&&(g=!0);e.addEventListener(a,b,g);d.call(c,{eventType:a,s:b,capture:g})};B.prototype.l=function(){for(var a=0;a<this.i.length;a++){var b=this.g,c=this.i[a];b.removeEventListener?b.removeEventListener(c.eventType,c.s,c.capture):b.detachEvent&&b.detachEvent(\"on\"+c.eventType,c.s)}this.i=[]};var C=function(){this.g=[];this.i=[];this.j=[]};C.prototype.addEventListener=function(a,b){for(var c=function(e){e.addEventListener(a,b)},d=0;d<this.g.length;d++)c(this.g[d]);this.j.push(c)};C.prototype.l=function(){for(var a=[].concat(n(this.g),n(this.i)),b=0;b<a.length;b++)a[b].l();this.g=[];this.i=[];this.j=[]};var E=function(a){for(var b=D,c=0;c<b.j.length;c++)b.j[c](a)};function F(a){for(var b=G,c=0;c<b.length;++c)if(H(b[c].g,a.g))return!0;return!1}\nfunction H(a,b){if(a===b)return!1;for(;a!==b&&b.parentNode;)b=b.parentNode;return a===b};var w=new C;a:{for(var D=w,A=window.document.documentElement,I=0;I<D.g.length;I++)if(A===D.g[I].g)break a;var J=new B,K;b:{for(var L=0;L<D.g.length;L++)if(H(D.g[L].g,J.g)){K=!0;break b}K=!1}if(K)D.i.push(J);else{E(J);D.g.push(J);for(var G=[].concat(n(D.i),n(D.g)),M=[],N=[],O=0;O<D.g.length;++O){var P=D.g[O];F(P)?(M.push(P),P.l()):N.push(P)}for(var Q=0;Q<D.i.length;++Q){var R=D.i[Q];F(R)?M.push(R):(N.push(R),E(R))}D.g=N;D.i=M}}for(var S=new x,U=0;U<t.length;U++)S.addEvent(t[U]);(function(a){google.jsad=function(b){b(a)}})(S);var V=Object.assign({},function(a){return{trigger:function(b){var c=a.s(b.type);c||(a.addEvent(b.type),c=a.s(b.type));var d=b.target||b.srcElement;c&&c(b.type,b,d.ownerDocument.documentElement)},configure:function(b){b(a)}}}(S),{addEvent:function(a){S.addEvent(a)}});r.gws_wizbind=V;(function(a,b){var c=function(d){var e=d.detail;e&&e._type&&b({type:e._type,target:d.target,bubbles:!0,detail:e})};a.addEventListener(\"_custom\",c);return function(){a.removeEventListener(\"_custom\",c)}})(window.document.documentElement,V.trigger);}).call(this);(function(){\nvar c=/^\\s*(?!javascript:)(?:[\\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;window.document.documentElement.addEventListener(\"mousedown\",function(b){var a=b.target;if(!a)return!0;a=a.closest(\"a[data-sb]\");if(!a)return!0;b.button!==0?(b=a.getAttribute(\"data-sb\"),c.test(b)&&(a.href=b),a.removeAttribute(\"data-sb\")):typeof navigator.sendBeacon===\"function\"?navigator.sendBeacon(a.getAttribute(\"data-sb\"),\"\"):google.log(\"\",\"\",a.getAttribute(\"data-sb\"));return!0},!0);}).call(this);</script>  <script nonce=\"CP311SD5BVmtqozsXvXMVQ\">(function(){google.xjs={basecomb:'/xjs/_/js/k\\x3dxjs.hd.en.0mZg4onwq2I.es5.O/ck\\x3dxjs.hd.3osHYkwsEnY.L.F4.O/am\\x3dJFUAAAAAAAAAAGAAAAAAAAAAAAAAAAAAAAAAAAAQAAAAAgAAAAAAAAAEBWhAAgAAjAKebACAjgEAAAAIAAAMABBICAAAGgAAJAAAAcACAACBAAAoAAAEFBECAAVNAHiUCSDACBASQAAFEAIEkIAANATxKEQAAAAGAAAIAWAEwwAEFQCMAgQAAAAAAAAgACEAAAQQIQACAAB-BADAAgDSAgCAINADgAAAAAAAAQAUAAIgSEwA7JABCAAAAAAAANAHAMEDYEjhAQAAAAAAAAAAAAAAApggmAsJCAhAAAAAAAAAAAAAAAAAAEhJExcm/d\\x3d1/ed\\x3d1/dg\\x3d0/ujg\\x3d1/rs\\x3dACT90oEFokshLJTGBEuzmJ4fqUwMoWpnfg',basecss:'/xjs/_/ss/k\\x3dxjs.hd.3osHYkwsEnY.L.F4.O/am\\x3dJFUAAAAAAAAAAGAAAAAAAAAAAAAAAAAAAAAAAAAQAAAAAgAAAAAAAAAABUBAAgAAjACebACAjgEAAAAIAAAMAABAAAAAGgAAIAAAAYACAAAAAAAgAAAEBBECAABFAAAAACBACBAAQAAFEAIEkIAANATxKAQAAAAGAAAIAWAEwwAEFQCMAgQAAAAAAAAgACEAAAAQAQACAAB-BADAAgDSAgCAINADgAAAAAAAAQAQAAIAQEwA7JABCAAAAAAAAEAGAAAAAACAAAAAAAAAAAAAAAAAAAggAAgACAAAAAAAAAAAAAAAAAAAAAAB/rs\\x3dACT90oFgzNy81TfImOPzePJpkFp_tYRRrg',basejs:'/xjs/_/js/k\\x3dxjs.hd.en.0mZg4onwq2I.es5.O/am\\x3dAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgAAAAAAAAAEBCgAAAAAgAIeAAAAgAAAAAAAAAAAABBICAAAGgAABAAAAMACAACBAAAIAAAAFBAAAAVIAHiUCQCACAASAAAAAAAEAIAANAQAAEAAAAAGAAAAAQAEAAAAFAAAAAAAAAAAAAAAAAAAAAQQIAAAAAAAAAAAAABAAgAAANADAAAAAAAAAAAEAAAgCAAA7JABCAAAAAAAANAHAMEDYEjhAQAAAAAAAAAAAAAAApggmAsJCAhAAAAAAAAAAAAAAAAAAEhJExcm/dg\\x3d0/rs\\x3dACT90oFkMsRkHqYDY758QJVs9U3tVH4BiA',excm:[]};})();</script> <script nonce=\"CP311SD5BVmtqozsXvXMVQ\">(function(){var f=\"src href async nonce type charset crossorigin onload rel\".split(\" \");window._rtf=function(b,c){var a=document.createElement(b.tagName);f.forEach(function(e){a[e]=b[e]});a.onerror=function(){window._rtf(a,c)};var d=b.dataset.rtc===void 0?0:Number(b.dataset.rtc);d>=4?c&&c():(a.dataset.rtc=String(d+1),setTimeout(function(){document.body.appendChild(a)},50*Math.pow(2,d)+Math.random()*50))};}).call(this);</script> <link href=\"/xjs/_/ss/k=xjs.hd.3osHYkwsEnY.L.F4.O/am=JFUAAAAAAAAAAGAAAAAAAAAAAAAAAAAAAAAAAAAQAAAAAgAAAAAAAAAABUBAAgAAjACebACAjgEAAAAIAAAMAABAAAAAGgAAIAAAAYACAAAAAAAgAAAEBBECAABFAAAAACBACBAAQAAFEAIEkIAANATxKAQAAAAGAAAIAWAEwwAEFQCMAgQAAAAAAAAgACEAAAAQAQACAAB-BADAAgDSAgCAINADgAAAAAAAAQAQAAIAQEwA7JABCAAAAAAAAEAGAAAAAACAAAAAAAAAAAAAAAAAAAggAAgACAAAAAAAAAAAAAAAAAAAAAAB/d=1/ed=1/rs=ACT90oFgzNy81TfImOPzePJpkFp_tYRRrg/m=cdos,hsm,jsa,mb4ZUb,cEt90b,SNUn3,qddgKe,sTsDMc,dtl0hd,eHDfl,d,csi\" onerror=\"_rtf(this)\" rel=\"stylesheet\" nonce=\"CP311SD5BVmtqozsXvXMVQ\"> <script nonce=\"CP311SD5BVmtqozsXvXMVQ\">(function(){google.kEXPI='0,3700273,1111,544986,72488,94323,527343,51059,5208501,6230,44,4620,62036985,24008,9295,17921,65745,7,5176,2442,10906,33671,41482,4190,7556,2482,58854,15868,14617,700,12588,4191,10256,5692,5237,8741,5198,13472,462,3039,7005,8915,3725,7606,15454,3926,9837,2736,3435,4775,8143,3381,968,718,456,8617,644,1026,3218,4462,2295,1744,1653,68,3576,1042,635,235,69,1322,551,722,520,1970,441,4,220,484,2274,755,1216,280,801,2955,380,825,1332,1471,25,61,1240,353,2987,823,8,970,1607,869,839,414,1115,231,1449,57,446,536,1491,1552,257,2499,35,519,22,1510,102,8,464,414,504,752,730,2015,260,382,44,2277,21050438,397131,3241,2853,36,251,1533,7407306,209178';})();window._ = window._ || {};window._DumpException = _._DumpException = function(e){throw e;};window._s = window._s || {};_s._DumpException = _._DumpException;window._qs = window._qs || {};_qs._DumpException = _._DumpException;(function(){var t=[21796,0,100663296,0,0,0,0,67108864,33554432,0,0,270147905,201326594,28473866,605874176,33619968,786432,69279811,134324224,2314,180225,198024,1019520,42222849,5048068,2511328,553684546,22024196,268698384,282067858,88248080,27263524,19398656,51188112,86592,67174579,135004160,67141968,5243138,25167940,75366400,269156352,11552,284428320,128,2359296,2130272,1249800,134320364,0,1023410176,15745025,31541344,0,0,0,404789250,538977326,1024,0,0,536870912,561591444,9];window._F_toggles = window._xjs_toggles = t;})();window._F_installCss = window._F_installCss || function(css){};(function(){window.google.xjsu='/xjs/_/js/k\\x3dxjs.hd.en.0mZg4onwq2I.es5.O/am\\x3dAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgAAAAAAAAAEBCgAAAAAgAIeAAAAgAAAAAAAAAAAABBICAAAGgAABAAAAMACAACBAAAIAAAAFBAAAAVIAHiUCQCACAASAAAAAAAEAIAANAQAAEAAAAAGAAAAAQAEAAAAFAAAAAAAAAAAAAAAAAAAAAQQIAAAAAAAAAAAAABAAgAAANADAAAAAAAAAAAEAAAgCAAA7JABCAAAAAAAANAHAMEDYEjhAQAAAAAAAAAAAAAAApggmAsJCAhAAAAAAAAAAAAAAAAAAEhJExcm/d\\x3d1/ed\\x3d1/dg\\x3d3/rs\\x3dACT90oFkMsRkHqYDY758QJVs9U3tVH4BiA/ee\\x3dALeJib:B8gLwd;AfeaP:TkrAjf;BMxAGc:E5bFse;BgS6mb:fidj5d;BjwMce:cXX2Wb;CxXAWb:YyRLvc;DULqB:RKfG5c;Dkk6ge:JZmW9e;DpcR3d:zL72xf;EABSZ:MXZt9d;ESrPQc:mNTJvc;EVNhjf:pw70Gc;EmZ2Bf:zr1jrb;EnlcNd:WeHg4;F9mqte:UoRcbe;Fmv9Nc:O1Tzwc;G0KhTb:LIaoZ;G6wU6e:hezEbd;GleZL:J1A7Od;HMDDWe:G8QUdb;HoYVKb:PkDN7e;HqeXPd:cmbnH;IBADCc:RYquRb;IoGlCf:b5lhvb;IsdWVc:qzxzOb;JXS8fb:Qj0suc;JbMT3:M25sS;JsbNhc:Xd8iUd;K5nYTd:ZDZcre;KOxcK:OZqGte;KQzWid:ZMKkN;KcokUb:KiuZBf;KpRAue:Tia57b;LBgRLc:SdcwHb,XVMNvd;LEikZe:byfTOb,lsjVmc;LXA8b:q7OdKd;LsNahb:ucGLNb;Me32dd:MEeYgc;NPKaK:SdcwHb;NSEoX:lazG7b;Np8Qkd:Dpx6qc;Nyt6ic:jn2sGd;OgagBe:cNTe0;OohIYe:mpEAQb;Pjplud:EEDORb,PoEs9b;Q1Ow7b:x5CSu;Q6C5kf:pfdZCe;QGR0gd:Mlhmy;R2kc8b:ALJqWb;R4IIIb:QWfeKf;R9Ulx:CR7Ufe;RDNBlf:zPRCJb;SLtqO:Kh1xYe;SMDL4c:fTfGO,fTfGO;SNUn3:ZwDk9d,x8cHvb;ScI3Yc:e7Hzgb,e7Hzgb;ShpF6e:N0pvGc;SzQQ3e:dNhofb;TxfV6d:YORN0b;U96pRd:FsR04;UBKJZ:LGDJGb;UDrY1c:eps46d;UVmjEd:EesRsb;UVzb9c:IvPZ6d;UyG7Kb:wQd0G;V2HTTe:RolTY;VGRfx:VFqbr;VN6jIc:ddQyuf;VOcgDe:YquhTb;VsAqSb:PGf2Re;VxQ32b:k0XsBb;WCEKNd:I46Hvd;WDGyFe:jcVOxd;Wfmdue:g3MJlb;XUezZ:sa7lqb;YIZmRd:A1yn5d;YV5bee:IvPZ6d;YkQtAf:rx8ur;ZMvdv:PHFPjb;ZSH6tc:QAvyLe;ZWEUA:afR4Cf;ZlOOMb:P0I0Ec;a56pNe:JEfCwb;aAJE9c:WHW6Ef;aCJ9tf:qKftvc;aZ61od:arTwJ;af0EJf:ghinId;bDXwRe:UsyOtc;bcPXSc:gSZLJb;cEt90b:ws9Tlc;cFTWae:gT8qnd;coJ8e:KvoW8;dIoSBb:ZgGg9b;dLlj2:Qqt3Gf;daB6be:lMxGPd;dowIGb:ebZ3mb,ebZ3mb;dtl0hd:lLQWFe;eBAeSb:Ck63tb;eBZ5Nd:audvde;eHDfl:ofjVkb;eO3lse:nFClrf;euOXY:OZjbQ;g8nkx:U4MzKc;gaub4:TN6bMe;gtVSi:ekUOYd;h3MYod:cEt90b;hK67qb:QWEO5b;heHB1:EtZ8Cd;hjRo6e:F62sG;hsLsYc:Vl118;iFQyKf:QIhFr,vfuNJf;imqimf:jKGL2e;jY0zg:Q6tNgc;k2Qxcb:XY51pe;kCQyJ:ueyPK;kMFpHd:OTA3Ae;kbAm9d:MkHyGd;lOO0Vd:OTA3Ae;lkq0A:JyBE3e;nAFL3:NTMZac,s39S4;nJw4Gd:dPFZH;oGtAuc:sOXFj;oSUNyd:fTfGO,fTfGO;oUlnpc:RagDlc;okUaUd:wItadb;pKJiXd:VCenhc;pNsl2d:j9Yuyc;pXdRYb:JKoKVe;pj82le:ww04Df;qZx2Fc:j0xrE;qaS3gd:yiLg6e;qafBPd:sgY6Zb;qavrXe:zQzcXe;qddgKe:d7YSfd,x4FYXe;rQSrae:C6D5Fc;sTsDMc:kHVSUb;sZmdvc:rdGEfc;tH4IIe:Ymry6;tosKvd:ZCqP3;trZL0b:qY8PFe;uuQkY:u2V3ud;vEYCNb:FaqsVd;vGrMZ:lPJJ0c;vfVwPd:lcrkwe;w3bZCb:ZPGaIb;w4rSdf:XKiZ9;w9w86d:dt4g2b;wQlYve:aLUfP;wR5FRb:O1Gjze,TtcOte;wV5Pjc:L8KGxe;xBbsrc:NEW1Qc;ysNiMc:CpIBjd;yxTchf:KUM7Z;z97YGf:oug9te;zOsCQe:Ko78Df;zaIgPb:Qtpxbd/m\\x3dcdos,hsm,jsa,mb4ZUb,cEt90b,SNUn3,qddgKe,sTsDMc,dtl0hd,eHDfl,d,csi';window._F_jsUrl='/xjs/_/js/k\\x3dxjs.hd.en.0mZg4onwq2I.es5.O/am\\x3dAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgAAAAAAAAAEBCgAAAAAgAIeAAAAgAAAAAAAAAAAABBICAAAGgAABAAAAMACAACBAAAIAAAAFBAAAAVIAHiUCQCACAASAAAAAAAEAIAANAQAAEAAAAAGAAAAAQAEAAAAFAAAAAAAAAAAAAAAAAAAAAQQIAAAAAAAAAAAAABAAgAAANADAAAAAAAAAAAEAAAgCAAA7JABCAAAAAAAANAHAMEDYEjhAQAAAAAAAAAAAAAAApggmAsJCAhAAAAAAAAAAAAAAAAAAEhJExcm/d\\x3d1/ed\\x3d1/dg\\x3d3/rs\\x3dACT90oFkMsRkHqYDY758QJVs9U3tVH4BiA/ee\\x3dALeJib:B8gLwd;AfeaP:TkrAjf;BMxAGc:E5bFse;BgS6mb:fidj5d;BjwMce:cXX2Wb;CxXAWb:YyRLvc;DULqB:RKfG5c;Dkk6ge:JZmW9e;DpcR3d:zL72xf;EABSZ:MXZt9d;ESrPQc:mNTJvc;EVNhjf:pw70Gc;EmZ2Bf:zr1jrb;EnlcNd:WeHg4;F9mqte:UoRcbe;Fmv9Nc:O1Tzwc;G0KhTb:LIaoZ;G6wU6e:hezEbd;GleZL:J1A7Od;HMDDWe:G8QUdb;HoYVKb:PkDN7e;HqeXPd:cmbnH;IBADCc:RYquRb;IoGlCf:b5lhvb;IsdWVc:qzxzOb;JXS8fb:Qj0suc;JbMT3:M25sS;JsbNhc:Xd8iUd;K5nYTd:ZDZcre;KOxcK:OZqGte;KQzWid:ZMKkN;KcokUb:KiuZBf;KpRAue:Tia57b;LBgRLc:SdcwHb,XVMNvd;LEikZe:byfTOb,lsjVmc;LXA8b:q7OdKd;LsNahb:ucGLNb;Me32dd:MEeYgc;NPKaK:SdcwHb;NSEoX:lazG7b;Np8Qkd:Dpx6qc;Nyt6ic:jn2sGd;OgagBe:cNTe0;OohIYe:mpEAQb;Pjplud:EEDORb,PoEs9b;Q1Ow7b:x5CSu;Q6C5kf:pfdZCe;QGR0gd:Mlhmy;R2kc8b:ALJqWb;R4IIIb:QWfeKf;R9Ulx:CR7Ufe;RDNBlf:zPRCJb;SLtqO:Kh1xYe;SMDL4c:fTfGO,fTfGO;SNUn3:ZwDk9d,x8cHvb;ScI3Yc:e7Hzgb,e7Hzgb;ShpF6e:N0pvGc;SzQQ3e:dNhofb;TxfV6d:YORN0b;U96pRd:FsR04;UBKJZ:LGDJGb;UDrY1c:eps46d;UVmjEd:EesRsb;UVzb9c:IvPZ6d;UyG7Kb:wQd0G;V2HTTe:RolTY;VGRfx:VFqbr;VN6jIc:ddQyuf;VOcgDe:YquhTb;VsAqSb:PGf2Re;VxQ32b:k0XsBb;WCEKNd:I46Hvd;WDGyFe:jcVOxd;Wfmdue:g3MJlb;XUezZ:sa7lqb;YIZmRd:A1yn5d;YV5bee:IvPZ6d;YkQtAf:rx8ur;ZMvdv:PHFPjb;ZSH6tc:QAvyLe;ZWEUA:afR4Cf;ZlOOMb:P0I0Ec;a56pNe:JEfCwb;aAJE9c:WHW6Ef;aCJ9tf:qKftvc;aZ61od:arTwJ;af0EJf:ghinId;bDXwRe:UsyOtc;bcPXSc:gSZLJb;cEt90b:ws9Tlc;cFTWae:gT8qnd;coJ8e:KvoW8;dIoSBb:ZgGg9b;dLlj2:Qqt3Gf;daB6be:lMxGPd;dowIGb:ebZ3mb,ebZ3mb;dtl0hd:lLQWFe;eBAeSb:Ck63tb;eBZ5Nd:audvde;eHDfl:ofjVkb;eO3lse:nFClrf;euOXY:OZjbQ;g8nkx:U4MzKc;gaub4:TN6bMe;gtVSi:ekUOYd;h3MYod:cEt90b;hK67qb:QWEO5b;heHB1:EtZ8Cd;hjRo6e:F62sG;hsLsYc:Vl118;iFQyKf:QIhFr,vfuNJf;imqimf:jKGL2e;jY0zg:Q6tNgc;k2Qxcb:XY51pe;kCQyJ:ueyPK;kMFpHd:OTA3Ae;kbAm9d:MkHyGd;lOO0Vd:OTA3Ae;lkq0A:JyBE3e;nAFL3:NTMZac,s39S4;nJw4Gd:dPFZH;oGtAuc:sOXFj;oSUNyd:fTfGO,fTfGO;oUlnpc:RagDlc;okUaUd:wItadb;pKJiXd:VCenhc;pNsl2d:j9Yuyc;pXdRYb:JKoKVe;pj82le:ww04Df;qZx2Fc:j0xrE;qaS3gd:yiLg6e;qafBPd:sgY6Zb;qavrXe:zQzcXe;qddgKe:d7YSfd,x4FYXe;rQSrae:C6D5Fc;sTsDMc:kHVSUb;sZmdvc:rdGEfc;tH4IIe:Ymry6;tosKvd:ZCqP3;trZL0b:qY8PFe;uuQkY:u2V3ud;vEYCNb:FaqsVd;vGrMZ:lPJJ0c;vfVwPd:lcrkwe;w3bZCb:ZPGaIb;w4rSdf:XKiZ9;w9w86d:dt4g2b;wQlYve:aLUfP;wR5FRb:O1Gjze,TtcOte;wV5Pjc:L8KGxe;xBbsrc:NEW1Qc;ysNiMc:CpIBjd;yxTchf:KUM7Z;z97YGf:oug9te;zOsCQe:Ko78Df;zaIgPb:Qtpxbd/m\\x3dcdos,hsm,jsa,mb4ZUb,cEt90b,SNUn3,qddgKe,sTsDMc,dtl0hd,eHDfl,d,csi';})();</script> <script defer=\"\" src=\"/xjs/_/js/k=xjs.hd.en.0mZg4onwq2I.es5.O/am=AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgAAAAAAAAAEBCgAAAAAgAIeAAAAgAAAAAAAAAAAABBICAAAGgAABAAAAMACAACBAAAIAAAAFBAAAAVIAHiUCQCACAASAAAAAAAEAIAANAQAAEAAAAAGAAAAAQAEAAAAFAAAAAAAAAAAAAAAAAAAAAQQIAAAAAAAAAAAAABAAgAAANADAAAAAAAAAAAEAAAgCAAA7JABCAAAAAAAANAHAMEDYEjhAQAAAAAAAAAAAAAAApggmAsJCAhAAAAAAAAAAAAAAAAAAEhJExcm/d=1/ed=1/dg=3/rs=ACT90oFkMsRkHqYDY758QJVs9U3tVH4BiA/ee=ALeJib:B8gLwd;AfeaP:TkrAjf;BMxAGc:E5bFse;BgS6mb:fidj5d;BjwMce:cXX2Wb;CxXAWb:YyRLvc;DULqB:RKfG5c;Dkk6ge:JZmW9e;DpcR3d:zL72xf;EABSZ:MXZt9d;ESrPQc:mNTJvc;EVNhjf:pw70Gc;EmZ2Bf:zr1jrb;EnlcNd:WeHg4;F9mqte:UoRcbe;Fmv9Nc:O1Tzwc;G0KhTb:LIaoZ;G6wU6e:hezEbd;GleZL:J1A7Od;HMDDWe:G8QUdb;HoYVKb:PkDN7e;HqeXPd:cmbnH;IBADCc:RYquRb;IoGlCf:b5lhvb;IsdWVc:qzxzOb;JXS8fb:Qj0suc;JbMT3:M25sS;JsbNhc:Xd8iUd;K5nYTd:ZDZcre;KOxcK:OZqGte;KQzWid:ZMKkN;KcokUb:KiuZBf;KpRAue:Tia57b;LBgRLc:SdcwHb,XVMNvd;LEikZe:byfTOb,lsjVmc;LXA8b:q7OdKd;LsNahb:ucGLNb;Me32dd:MEeYgc;NPKaK:SdcwHb;NSEoX:lazG7b;Np8Qkd:Dpx6qc;Nyt6ic:jn2sGd;OgagBe:cNTe0;OohIYe:mpEAQb;Pjplud:EEDORb,PoEs9b;Q1Ow7b:x5CSu;Q6C5kf:pfdZCe;QGR0gd:Mlhmy;R2kc8b:ALJqWb;R4IIIb:QWfeKf;R9Ulx:CR7Ufe;RDNBlf:zPRCJb;SLtqO:Kh1xYe;SMDL4c:fTfGO,fTfGO;SNUn3:ZwDk9d,x8cHvb;ScI3Yc:e7Hzgb,e7Hzgb;ShpF6e:N0pvGc;SzQQ3e:dNhofb;TxfV6d:YORN0b;U96pRd:FsR04;UBKJZ:LGDJGb;UDrY1c:eps46d;UVmjEd:EesRsb;UVzb9c:IvPZ6d;UyG7Kb:wQd0G;V2HTTe:RolTY;VGRfx:VFqbr;VN6jIc:ddQyuf;VOcgDe:YquhTb;VsAqSb:PGf2Re;VxQ32b:k0XsBb;WCEKNd:I46Hvd;WDGyFe:jcVOxd;Wfmdue:g3MJlb;XUezZ:sa7lqb;YIZmRd:A1yn5d;YV5bee:IvPZ6d;YkQtAf:rx8ur;ZMvdv:PHFPjb;ZSH6tc:QAvyLe;ZWEUA:afR4Cf;ZlOOMb:P0I0Ec;a56pNe:JEfCwb;aAJE9c:WHW6Ef;aCJ9tf:qKftvc;aZ61od:arTwJ;af0EJf:ghinId;bDXwRe:UsyOtc;bcPXSc:gSZLJb;cEt90b:ws9Tlc;cFTWae:gT8qnd;coJ8e:KvoW8;dIoSBb:ZgGg9b;dLlj2:Qqt3Gf;daB6be:lMxGPd;dowIGb:ebZ3mb,ebZ3mb;dtl0hd:lLQWFe;eBAeSb:Ck63tb;eBZ5Nd:audvde;eHDfl:ofjVkb;eO3lse:nFClrf;euOXY:OZjbQ;g8nkx:U4MzKc;gaub4:TN6bMe;gtVSi:ekUOYd;h3MYod:cEt90b;hK67qb:QWEO5b;heHB1:EtZ8Cd;hjRo6e:F62sG;hsLsYc:Vl118;iFQyKf:QIhFr,vfuNJf;imqimf:jKGL2e;jY0zg:Q6tNgc;k2Qxcb:XY51pe;kCQyJ:ueyPK;kMFpHd:OTA3Ae;kbAm9d:MkHyGd;lOO0Vd:OTA3Ae;lkq0A:JyBE3e;nAFL3:NTMZac,s39S4;nJw4Gd:dPFZH;oGtAuc:sOXFj;oSUNyd:fTfGO,fTfGO;oUlnpc:RagDlc;okUaUd:wItadb;pKJiXd:VCenhc;pNsl2d:j9Yuyc;pXdRYb:JKoKVe;pj82le:ww04Df;qZx2Fc:j0xrE;qaS3gd:yiLg6e;qafBPd:sgY6Zb;qavrXe:zQzcXe;qddgKe:d7YSfd,x4FYXe;rQSrae:C6D5Fc;sTsDMc:kHVSUb;sZmdvc:rdGEfc;tH4IIe:Ymry6;tosKvd:ZCqP3;trZL0b:qY8PFe;uuQkY:u2V3ud;vEYCNb:FaqsVd;vGrMZ:lPJJ0c;vfVwPd:lcrkwe;w3bZCb:ZPGaIb;w4rSdf:XKiZ9;w9w86d:dt4g2b;wQlYve:aLUfP;wR5FRb:O1Gjze,TtcOte;wV5Pjc:L8KGxe;xBbsrc:NEW1Qc;ysNiMc:CpIBjd;yxTchf:KUM7Z;z97YGf:oug9te;zOsCQe:Ko78Df;zaIgPb:Qtpxbd/m=cdos,hsm,jsa,mb4ZUb,cEt90b,SNUn3,qddgKe,sTsDMc,dtl0hd,eHDfl,d,csi\" nonce=\"CP311SD5BVmtqozsXvXMVQ\"></script>        <script nonce=\"CP311SD5BVmtqozsXvXMVQ\">(function(){window.google.erd={jsr:1,bv:2101,sd:true,de:true,dpf:'yvsZ6ppcv-vgWkDLrjZQaT_lAVODVAy_D9JA02tzdlg'};})();(function(){var sdo=false;var mei=10;\nvar g=this||self;var k,l=(k=g.mei)!=null?k:1,n,p=(n=g.sdo)!=null?n:!0,q=0,r,t=google.erd,v=t.jsr;google.ml=function(a,b,d,m,e){e=e===void 0?2:e;b&&(r=a&&a.message);d===void 0&&(d={});d.cad=\"ple_\"+google.ple+\".aple_\"+google.aple;if(google.dl)return google.dl(a,e,d,!0),null;b=d;if(v<0){window.console&&console.error(a,b);if(v===-2)throw a;b=!1}else b=!a||!a.message||a.message===\"Error loading script\"||q>=l&&!m?!1:!0;if(!b)return null;q++;d=d||{};b=encodeURIComponent;var c=\"/gen_204?atyp=i&ei=\"+b(google.kEI);google.kEXPI&&(c+=\"&jexpid=\"+b(google.kEXPI));c+=\"&srcpg=\"+b(google.sn)+\"&jsr=\"+b(t.jsr)+\n\"&bver=\"+b(t.bv);t.dpf&&(c+=\"&dpf=\"+b(t.dpf));var f=a.lineNumber;f!==void 0&&(c+=\"&line=\"+f);var h=a.fileName;h&&(h.indexOf(\"-extension:/\")>0&&(e=3),c+=\"&script=\"+b(h),f&&h===window.location.href&&(f=document.documentElement.outerHTML.split(\"\\n\")[f],c+=\"&cad=\"+b(f?f.substring(0,300):\"No script found.\")));google.ple&&google.ple===1&&(e=2);c+=\"&jsel=\"+e;for(var u in d)c+=\"&\",c+=b(u),c+=\"=\",c+=b(d[u]);c=c+\"&emsg=\"+b(a.name+\": \"+a.message);c=c+\"&jsst=\"+b(a.stack||\"N/A\");c.length>=12288&&(c=c.substr(0,12288));a=c;m||google.log(0,\"\",a);return a};window.onerror=function(a,b,d,m,e){r!==a&&(a=e instanceof Error?e:Error(a),d===void 0||\"lineNumber\"in a||(a.lineNumber=d),b===void 0||\"fileName\"in a||(a.fileName=b),google.ml(a,!1,void 0,!1,a.name===\"SyntaxError\"||a.message.substring(0,11)===\"SyntaxError\"||a.message.indexOf(\"Script error\")!==-1?3:0));r=null;p&&q>=l&&(window.onerror=null)};})();;this.gbar_={CONFIG:[[[0,\"www.gstatic.com\",\"og.qtm.en_US.JsvYdB1VlTQ.2019.O\",\"com.bo\",\"en\",\"538\",0,[4,2,\"\",\"\",\"\",\"*********\",\"0\"],null,\"sNsXZ-SLFM6z4dUPwumX6AI\",null,0,\"og.qtm.wW8O5Lw5T1g.L.F4.O\",\"AA2YrTt6VjuqvFHGTQ7vz8QgRv0QbbEJTQ\",\"AA2YrTuFwfpUCTC88n_H9FoSYz-cjRhDFQ\",\"\",2,1,200,\"BOL\",null,null,\"1\",\"538\",1,null,null,********,1,0],null,[1,0.****************,2,1],null,[1,0,1,null,\"0\",\"<EMAIL>\",\"\",\"AK5n21pUUQUwtnwr9v0iZUF8kNLLYhq9uvtClz6uhbH9Uv55lX8M0NjFsvNvS5CHmzeH68wjHGS1NhfJEzbuVgzyEjn-0hOWBQ\",0,0,0,\"\"],[0,0,\"\",1,0,0,0,0,0,0,null,0,0,null,0,0,null,null,0,0,0,\"\",\"\",\"\",\"\",\"\",\"\",null,0,0,0,0,0,null,null,null,\"rgba(32,33,36,1)\",\"rgba(255,255,255,1)\",0,0,1,null,null,null,0],[\"%1$s (default)\",\"Brand account\",0,\"%1$s (delegated)\",1,null,83,\"https://www.google.com/webhp?authuser=$authuser\",null,null,null,1,\"https://accounts.google.com/ListAccounts?listPages=0\\u0026pid=538\\u0026gpsia=1\\u0026source=ogb\\u0026atic=1\\u0026mo=1\\u0026mn=1\\u0026hl=en\\u0026ts=157\",0,\"dashboard\",null,null,null,null,\"Profile\",\"\",0,null,\"Signed out\",\"https://accounts.google.com/AccountChooser?source=ogb\\u0026continue=$continue\\u0026Email=$email\\u0026ec=GAhAmgQ\",\"https://accounts.google.com/RemoveLocalAccount?source=ogb\",\"Remove\",\"Sign in\",0,1,1,0,1,1,0,null,null,null,\"Session expired\",null,null,null,\"Visitor\",null,\"Default\",\"Delegated\",\"Sign out of all accounts\",0,null,null,0,null,null,\"myaccount.google.com\",\"https\",0,1,0],null,[\"1\",\"gci_91f30755d6a6b787dcc2a4062e6e9824.js\",\"googleapis.client:gapi.iframes\",\"0\",\"en\"],null,null,null,null,[\"m;/_/scs/abc-static/_/js/k=gapi.gapi.en.SGzW6IeCawI.O/am=AACA/d=1/rs=AHpOoo-5biO9jua-6zCEovdoDJ8SLzd6sw/m=__features__\",\"https://apis.google.com\",\"\",\"\",\"1\",\"\",null,1,\"es_plusone_gc_20241007.0_p2\",\"en\",null,0],[0.009999999776482582,\"com.bo\",\"538\",[null,\"\",\"0\",null,1,5184000,null,null,\"\",null,null,null,null,null,0,null,0,null,1,0,0,0,null,null,0,0,null,0,0,0,0,0],null,null,null,0],[1,null,null,40400,538,\"BOL\",\"en\",\"*********.0\",8,null,1,0,null,null,null,null,\"3701384,*********,*********\",null,null,null,\"sNsXZ-SLFM6z4dUPwumX6AI\",0,0,0,null,2,5,\"cg\",74,0,0,0,0,1,********,0,0],[[null,null,null,\"https://www.gstatic.com/og/_/js/k=og.qtm.en_US.JsvYdB1VlTQ.2019.O/rt=j/m=qabr,qgl,q_dnp,qcwid,qbd,qapid,qald,qads,qrcd,q_dg,qrbg/exm=qaaw,qadd,qaid,qein,qhaw,qhba,qhbr,qhch,qhga,qhid,qhin/d=1/ed=1/rs=AA2YrTt6VjuqvFHGTQ7vz8QgRv0QbbEJTQ\"],[null,null,null,\"https://www.gstatic.com/og/_/ss/k=og.qtm.wW8O5Lw5T1g.L.F4.O/m=qcwid,d_b_gm3,d_wi_gm3,d_lo_gm3/excm=qaaw,qadd,qaid,qein,qhaw,qhba,qhbr,qhch,qhga,qhid,qhin/d=1/ed=1/ct=zgms/rs=AA2YrTuFwfpUCTC88n_H9FoSYz-cjRhDFQ\"]],null,null,null,[[[null,null,[null,null,null,\"https://ogs.google.com/u/0/widget/app?eom=1\\u0026awwd=1\\u0026alb=1\"],0,470,370,49,4,1,0,0,63,64,8000,\"https://www.google.com.bo/intl/en/about/products\",67,1,69,null,1,70,\"Can't seem to load the app launcher right now. Try again or go to the %1$sGoogle Products%2$s page.\",3,0,0,74,4000,null,null,null,null,null,null,null,\"/widget/app\",null,null,null,null,null,null,null,0,null,null,null,null,null,null,null,null,null,null,1,null,144,null,null,0,0],[null,null,[null,null,null,\"https://ogs.google.com/u/0/widget/account?eom=1\\u0026yac=1\\u0026amb=1\"],0,414,436,49,4,1,0,0,65,66,8000,\"https://accounts.google.com/SignOutOptions?hl=en\\u0026continue=https://www.google.com/\\u0026ec=GBRAmgQ\",68,2,null,null,1,113,\"Something went wrong.%1$s Refresh to try again or %2$schoose another account%3$s.\",3,null,null,75,0,null,null,null,null,null,null,null,\"/widget/account\",[\"https\",\"myaccount.google.com\",0,32,83,0],0,0,1,[\"Critical security alert\",\"Important account alert\",\"Storage usage alert\",0],0,1,null,1,1,1,1,null,null,0,0,0,null,1,0],[null,null,[null,null,null,\"https://ogs.google.com/u/0/widget/callout/sid?eom=1\\u0026dc=1\"],null,280,420,70,25,0,null,0,null,null,8000,null,71,4,null,null,null,null,null,null,null,null,76,null,null,null,107,108,109,\"\",null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,1]],null,null,\"1\",\"538\",1,0,null,\"en\",1,[\"https://www.google.com/webhp?authuser=$authuser\",\"https://accounts.google.com/AddSession?hl=en\\u0026continue=https://www.google.com/\\u0026ec=GAlAmgQ\",\"https://accounts.google.com/Logout?hl=en\\u0026continue=https://www.google.com/\\u0026timeStmp=**********\\u0026secTok=.AG5fkS-5cHCGIDd2d33wmUsU3q4ehYOH4Q\\u0026ec=GAdAmgQ\",\"https://accounts.google.com/ListAccounts?listPages=0\\u0026pid=538\\u0026gpsia=1\\u0026source=ogb\\u0026atic=1\\u0026mo=1\\u0026mn=1\\u0026hl=en\\u0026ts=157\",0,0,\"\",0,0,[\"Google Search\",[[null,[\"og-am-qdc\",0,\"https://myactivity.google.com/product/search?utm_source=web_search\\u0026utm_medium=mavatar\\u0026hl=en\",\"https://support.google.com/accounts/answer/465#other-places\\u0026zippy=a%2Cdelete-browser-activity\"]],[[[[\"og-am-cp-int\",\"Interests\",[\"interests\",0,0],1,\"https://myactivity.google.com/personal-results?utm_source=web_search\\u0026utm_medium=web\\u0026utm_campaign=mavatar\\u0026hl=en\",1,\"\",\"\"]]]],[[[[\"og-am-ps\",\"Search personalization\",[\"personalized_recommendations\",0,0],1,\"/setting/search/privateresults?hl=en\",1,\"\",\"\"]],[[\"og-am-ss\",\"SafeSearch\",[\"safesearch_logo\",0,0],1,\"/safesearch?prev=https://www.google.com/\",1,\"\",\"Blurring on\"]],[[\"og-am-lg\",\"Language\",[\"language\",0,0],1,\"/preferences?lang=1\\u0026hl=en\\u0026prev=https://www.google.com/#languages\",1,\"\",\"English\"]],[null,[[\"og-am-ms\",\"More settings\",[\"settings\",0,0],1,\"/preferences?hl=en\\u0026prev=https://www.google.com/\",1,\"\",\"\"],[\"og-am-hf\",\"Help\",[\"help_outline\",0,0],1,\"https://support.google.com/websearch/?p=dsrp_search_hc\\u0026hl=en\",1,\"\",\"\"]]]]]],null,null,\"\"],0,0,\"https://accounts.google.com/ServiceLogin?hl=en\\u0026passive=true\\u0026continue=https://www.google.com/\\u0026ec=GAZAmgQ\",1,1,0,0,null,0],0,0,0,[null,\"\",null,null,null,1,null,0,0,\"\",\"\",\"\",\"https://ogads-pa.clients6.google.com\",0,0,0,\"\",\"\",0,0,null,86400,null,0,1,null,0,0,0],0,null,null,null,0],null,[[\"mousedown\",\"touchstart\",\"touchmove\",\"wheel\",\"keydown\"],300000],[[null,null,null,\"https://accounts.google.com/RotateCookiesPage\"],3,null,null,null,0,1],[300000,\"/u/0\",\"/u/0/_/gog/get\",\"AK5n21pUUQUwtnwr9v0iZUF8kNLLYhq9uvtClz6uhbH9Uv55lX8M0NjFsvNvS5CHmzeH68wjHGS1NhfJEzbuVgzyEjn-0hOWBQ\",\"https\",0,\"aa.google.com\",\"rt=j\\u0026sourceid=538\",\"\",\"CP311SD5BVmtqozsXvXMVQ\",null,0,0,null,0,null,1,1,\"https://waa-pa.clients6.google.com\",\"AIzaSyBGb5fGAyC-pRcRU6MUHb__b_vKha71HRE\",\"/JR8jsAkqotcKsEKhXic\",null,1,0,\"https://waa-pa.googleapis.com\"]]],};this.gbar_=this.gbar_||{};(function(_){var window=this;\ntry{\n_._F_toggles_initialize=function(a){(typeof globalThis!==\"undefined\"?globalThis:typeof self!==\"undefined\"?self:this)._F_toggles=a||[]};(0,_._F_toggles_initialize)([]);\n/*\n\n Copyright The Closure Library Authors.\n SPDX-License-Identifier: Apache-2.0\n*/\nvar ca,ja,ka,oa,qa,ra,Aa,Ba,Da,Fa,Ia,Na,Za,Ya,eb,gb,fb,hb,ib,nb,ob,sb,vb,pb,ub,tb,rb,qb,wb,xb,Eb,Fb,Ib,Jb,Kb,Lb;_.aa=function(a,b){if(Error.captureStackTrace)Error.captureStackTrace(this,_.aa);else{const c=Error().stack;c&&(this.stack=c)}a&&(this.message=String(a));b!==void 0&&(this.cause=b)};_.ba=function(a){a.oj=!0;return a};ca=function(a,b){if(a.length>b.length)return!1;if(a.length<b.length||a===b)return!0;for(let c=0;c<a.length;c++){const d=a[c],e=b[c];if(d>e)return!1;if(d<e)return!0}};\n_.ea=function(a){_.t.setTimeout(()=>{throw a;},0)};_.fa=function(){var a=_.t.navigator;return a&&(a=a.userAgent)?a:\"\"};ja=function(a){return ha?ia?ia.brands.some(({brand:b})=>b&&b.indexOf(a)!=-1):!1:!1};_.u=function(a){return _.fa().indexOf(a)!=-1};ka=function(){return ha?!!ia&&ia.brands.length>0:!1};_.la=function(){return ka()?!1:_.u(\"Opera\")};_.ma=function(){return ka()?!1:_.u(\"Trident\")||_.u(\"MSIE\")};_.na=function(){return _.u(\"Firefox\")||_.u(\"FxiOS\")};\n_.pa=function(){return _.u(\"Safari\")&&!(oa()||(ka()?0:_.u(\"Coast\"))||_.la()||(ka()?0:_.u(\"Edge\"))||(ka()?ja(\"Microsoft Edge\"):_.u(\"Edg/\"))||(ka()?ja(\"Opera\"):_.u(\"OPR\"))||_.na()||_.u(\"Silk\")||_.u(\"Android\"))};oa=function(){return ka()?ja(\"Chromium\"):(_.u(\"Chrome\")||_.u(\"CriOS\"))&&!(ka()?0:_.u(\"Edge\"))||_.u(\"Silk\")};qa=function(){return ha?!!ia&&!!ia.platform:!1};ra=function(){return _.u(\"iPhone\")&&!_.u(\"iPod\")&&!_.u(\"iPad\")};_.sa=function(){return ra()||_.u(\"iPad\")||_.u(\"iPod\")};\n_.ta=function(){return qa()?ia.platform===\"macOS\":_.u(\"Macintosh\")};_.va=function(a,b){return _.ua(a,b)>=0};_.wa=function(a){let b=\"\",c=0;const d=a.length-10240;for(;c<d;)b+=String.fromCharCode.apply(null,a.subarray(c,c+=10240));b+=String.fromCharCode.apply(null,c?a.subarray(c):a);return btoa(b)};_.xa=function(a){return a!=null&&a instanceof Uint8Array};_.ya=function(a){return Array.prototype.slice.call(a)};_.za=function(a){return!!((a[_.v]|0)&2)};Aa=function(a,b){b[_.v]=(a|0)&-30975};\nBa=function(a,b){b[_.v]=(a|34)&-30941};Da=function(a){return!(!a||typeof a!==\"object\"||a.i!==Ca)};_.Ea=function(a){return a!==null&&typeof a===\"object\"&&!Array.isArray(a)&&a.constructor===Object};Fa=function(a){return!Array.isArray(a)||a.length?!1:(a[_.v]|0)&1?!0:!1};_.Ga=function(a){if(a&2)throw Error();};Ia=function(a,b){(b=_.Ha?b[_.Ha]:void 0)&&(a[_.Ha]=_.ya(b))};_.Ka=function(){const a=Error();Ja(a,\"incident\");_.ea(a)};_.La=function(a){a=Error(a);Ja(a,\"warning\");return a};\n_.Ma=function(a,b){if(a.length!==b.length)return!1;for(const e in b){var c=Number(e),d;if(d=Number.isInteger(c))d=a[c],c=b[c],d=!(Number.isNaN(d)?Number.isNaN(c):d===c);if(d)return!1}return!0};Na=function(){_.Ka()};_.Pa=function(a,b){let c,d;return(c=_.Oa)==null?void 0:(d=c.get(b))==null?void 0:d.get(a)};_.Ra=function(a){if(typeof a!==\"boolean\")throw Error(\"s`\"+_.Qa(a)+\"`\"+a);return a};_.Sa=function(a){if(!Number.isFinite(a))throw _.La(\"enum\");return a|0};\n_.Ta=function(a){if(typeof a!==\"number\")throw _.La(\"int32\");if(!Number.isFinite(a))throw _.La(\"int32\");return a|0};_.Ua=function(a){if(a!=null&&typeof a!==\"string\")throw Error();return a};_.Va=function(a){return a==null||typeof a===\"string\"?a:void 0};_.Xa=function(a,b,c){if(a!=null&&typeof a===\"object\"&&a.zd===_.Wa)return a;if(Array.isArray(a)){var d=a[_.v]|0,e=d;e===0&&(e|=c&32);e|=c&2;e!==d&&(a[_.v]=e);return new b(a)}};Za=function(a,b){return Ya(b)};\nYa=function(a){switch(typeof a){case \"number\":return isFinite(a)?a:String(a);case \"bigint\":return(0,_.$a)(a)?Number(a):String(a);case \"boolean\":return a?1:0;case \"object\":if(a)if(Array.isArray(a)){if(Fa(a))return}else{if(_.xa(a))return _.wa(a);if(\"function\"==typeof _.ab&&a instanceof _.ab)return a.j()}}return a};_.cb=function(a,b){bb=b;a=new a(b);bb=void 0;return a};\n_.db=function(a,b,c){a==null&&(a=bb);bb=void 0;if(a==null){var d=96;c?(a=[c],d|=512):a=[];b&&(d=d&-33521665|(b&1023)<<15)}else{if(!Array.isArray(a))throw Error(\"t\");d=a[_.v]|0;if(d&2048)throw Error(\"w\");if(d&64)return a;d|=64;if(c&&(d|=512,c!==a[0]))throw Error(\"x\");a:{c=a;const e=c.length;if(e){const f=e-1;if(_.Ea(c[f])){d|=256;b=f-(+!!(d&512)-1);if(b>=1024)throw Error(\"y\");d=d&-33521665|(b&1023)<<15;break a}}if(b){b=Math.max(b,e-(+!!(d&512)-1));if(b>1024)throw Error(\"z\");d=d&-33521665|(b&1023)<<\n15}}}a[_.v]=d;return a};eb=function(a,b,c){const d=_.ya(a);var e=d.length;const f=b&256?d[e-1]:void 0;e+=f?-1:0;for(b=b&512?1:0;b<e;b++)d[b]=c(d[b]);if(f){b=d[b]={};for(const g in f)b[g]=c(f[g])}Ia(d,a);return d};gb=function(a,b,c,d,e){if(a!=null){if(Array.isArray(a))a=Fa(a)?void 0:e&&(a[_.v]|0)&2?a:fb(a,b,c,d!==void 0,e);else if(_.Ea(a)){const f={};for(let g in a)f[g]=gb(a[g],b,c,d,e);a=f}else a=b(a,d);return a}};\nfb=function(a,b,c,d,e){const f=d||c?a[_.v]|0:0;d=d?!!(f&32):void 0;const g=_.ya(a);for(let h=0;h<g.length;h++)g[h]=gb(g[h],b,c,d,e);c&&(Ia(g,a),c(f,g));return g};hb=function(a){return a.zd===_.Wa?a.toJSON():Ya(a)};\nib=function(a,b,c=Ba){if(a!=null){if(a instanceof Uint8Array)return b?a:new Uint8Array(a);if(Array.isArray(a)){var d=a[_.v]|0;if(d&2)return a;b&&(b=d===0||!!(d&32)&&!(d&64||!(d&16)));return b?(a[_.v]=(d|34)&-12293,a):fb(a,ib,d&4?Ba:c,!0,!0)}a.zd===_.Wa&&(c=a.fa,d=c[_.v],a=d&2?a:_.jb(a,c,d,!0));return a}};_.jb=function(a,b,c,d){_.kb(a);return _.cb(a.constructor,_.lb(b,c,d))};_.lb=function(a,b,c){const d=c||b&2?Ba:Aa,e=!!(b&32);a=eb(a,b,f=>ib(f,e,d));a[_.v]=a[_.v]|32|(c?2:0);return a};\n_.mb=function(a){const b=a.fa,c=b[_.v];return c&2?_.jb(a,b,c,!1):a};nb=function(a){return a};ob=function(a){return a};sb=function(a,b,c,d){return pb(a,b,c,d,qb,rb)};vb=function(a,b,c,d){return pb(a,b,c,d,tb,ub)};\npb=function(a,b,c,d,e,f){if(!c.length&&!d)return 0;var g=0;let h=0,k=0;var l=0;let m=0;for(var p=c.length-1;p>=0;p--){var r=c[p];d&&p===c.length-1&&r===d||(l++,r!=null&&k++)}if(d)for(var q in d)p=+q,isNaN(p)||(m+=wb(p),h++,p>g&&(g=p));l=e(l,k)+f(h,g,m);q=k;p=h;r=g;let w=m;for(let C=c.length-1;C>=0;C--){var D=c[C];if(D==null||d&&C===c.length-1&&D===d)continue;D=C-b;const H=e(D,q)+f(p,r,w);H<l&&(a=1+D,l=H);p++;q--;w+=wb(D);r=Math.max(r,D)}b=e(0,0)+f(p,r,w);b<l&&(a=0,l=b);if(d){p=h;r=g;w=m;q=k;for(const C in d)d=\n+C,isNaN(d)||d>=1024||(p--,q++,w-=C.length,g=e(d,q)+f(p,r,w),g<l&&(a=1+d,l=g))}return a};ub=function(a,b,c){return c+a*3+(a>1?a-1:0)};tb=function(a,b){return(a>1?a-1:0)+(a-b)*4};rb=function(a,b){return a==0?0:9*Math.max(1<<32-Math.clz32(a+a/2-1),4)<=b?a==0?0:a<4?100+(a-1)*16:a<6?148+(a-4)*16:a<12?244+(a-6)*16:a<22?436+(a-12)*19:a<44?820+(a-22)*17:52+32*a:40+4*b};qb=function(a){return 40+4*a};wb=function(a){return a>=100?a>=1E4?Math.ceil(Math.log10(1+a)):a<1E3?3:4:a<10?1:2};\nxb=function(a,b,c,d){b=d+(+!!(b&512)-1);if(!(b<0||b>=a.length||b>=c))return a[b]};_.yb=function(a,b,c,d){const e=b>>15&1023||536870912;if(c>=e){let f,g=b;if(b&256)f=a[a.length-1];else{if(d==null)return g;f=a[e+(+!!(b&512)-1)]={};g|=256}f[c]=d;c<e&&(a[c+(+!!(b&512)-1)]=void 0);g!==b&&(a[_.v]=g);return g}a[c+(+!!(b&512)-1)]=d;b&256&&(a=a[a.length-1],c in a&&delete a[c]);return b};_.Ab=function(a,b,c,d){a=a.fa;let e=a[_.v];d=_.zb(a,e,c,d);b=_.Xa(d,b,e);b!==d&&b!=null&&_.yb(a,e,c,b);return b};\n_.Bb=function(a,b){return a!=null?a:b};\nEb=function(a){_.kb(a);var b=Cb?a.fa:fb(a.fa,hb,void 0,void 0,!1);var c=!Cb,d=(c?a.fa:b)[_.v];if(a=b.length){var e=b[a-1],f=_.Ea(e);f?a--:e=void 0;var g=+!!(d&512)-1,h=a-g;d=!!Db&&!(d&512);var k,l=(k=Db)!=null?k:ob;k=d?l(h,g,b,e):h;d=(h=d&&h!==k)?Array.prototype.slice.call(b,0,a):b;if(f||h){b:{var m=d;var p=e;var r;f=!1;if(h)for(l=Math.max(0,k+g);l<m.length;l++){var q=m[l];const C=l-g;if(!(q==null||Fa(q)||Da(q)&&q.size===0)){var w=m[l]=void 0;((w=r)!=null?w:r={})[C]=q;f=!0}}if(p)for(let C in p)if(w=\n+C,isNaN(w)){let H;((H=r)!=null?H:r={})[C]=p[C]}else if(l=p[C],Array.isArray(l)&&(Fa(l)||Da(l)&&l.size===0)&&(l=null),l==null&&(f=!0),h&&w<k){f=!0;l=w+g;for(q=m.length;q<=l;q++)m.push(void 0);m[l]=p[w]}else if(l!=null){let H;((H=r)!=null?H:r={})[C]=l}f||(r=p);if(r)for(let C in r){p=r;break b}p=null}m=p==null?e!=null:p!==e}h&&(a=d.length);for(var D;a>0;a--){r=d[a-1];if(!(r==null||Fa(r)||Da(r)&&r.size===0))break;D=!0}if(d!==b||m||D){if(!h&&!c)d=Array.prototype.slice.call(d,0,a);else if(D||m||p)d.length=\na;p&&d.push(p)}b=d}return b};Fb=function(){const a=class{constructor(){throw Error();}};Object.setPrototypeOf(a,a.prototype);return a};_.x=function(a,b){return a!=null?!!a:!!b};_.y=function(a,b){b==void 0&&(b=\"\");return a!=null?a:b};_.Gb=function(a,b,c){for(const d in a)b.call(c,a[d],d,a)};_.Hb=function(a){for(const b in a)return!1;return!0};Ib=typeof Object.defineProperties==\"function\"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a};\nJb=function(a){a=[\"object\"==typeof globalThis&&globalThis,a,\"object\"==typeof window&&window,\"object\"==typeof self&&self,\"object\"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error(\"a\");};Kb=Jb(this);Lb=function(a,b){if(b)a:{var c=Kb;a=a.split(\".\");for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&b!=null&&Ib(c,a,{configurable:!0,writable:!0,value:b})}};\nLb(\"Symbol.dispose\",function(a){return a?a:Symbol(\"b\")});Lb(\"globalThis\",function(a){return a||Kb});Lb(\"Promise.prototype.finally\",function(a){return a?a:function(b){return this.then(function(c){return Promise.resolve(b()).then(function(){return c})},function(c){return Promise.resolve(b()).then(function(){throw c;})})}});var Ob,Pb,Sb;_.Mb=_.Mb||{};_.t=this||self;Ob=function(a,b){var c=_.Nb(\"WIZ_global_data.oxN3nb\");a=c&&c[a];return a!=null?a:b};Pb=_.t._F_toggles||[];_.Nb=function(a,b){a=a.split(\".\");b=b||_.t;for(var c=0;c<a.length;c++)if(b=b[a[c]],b==null)return null;return b};_.Qa=function(a){var b=typeof a;return b!=\"object\"?b:a?Array.isArray(a)?\"array\":b:\"null\"};_.Qb=function(a){var b=typeof a;return b==\"object\"&&a!=null||b==\"function\"};_.Rb=\"closure_uid_\"+(Math.random()*1E9>>>0);\nSb=function(a,b,c){return a.call.apply(a.bind,arguments)};_.z=function(a,b,c){_.z=Sb;return _.z.apply(null,arguments)};_.Tb=function(a,b){var c=Array.prototype.slice.call(arguments,1);return function(){var d=c.slice();d.push.apply(d,arguments);return a.apply(this,d)}};_.A=function(a,b){a=a.split(\".\");var c=_.t;a[0]in c||typeof c.execScript==\"undefined\"||c.execScript(\"var \"+a[0]);for(var d;a.length&&(d=a.shift());)a.length||b===void 0?c[d]&&c[d]!==Object.prototype[d]?c=c[d]:c=c[d]={}:c[d]=b};\n_.B=function(a,b){function c(){}c.prototype=b.prototype;a.X=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.ij=function(d,e,f){for(var g=Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}};_.B(_.aa,Error);_.aa.prototype.name=\"CustomError\";var Ub=!!(Pb[0]&1024),Vb=!!(Pb[0]&32),Wb=!!(Pb[0]&2048),Xb=!!(Pb[0]&8);var Yb,ha;Yb=Ob(1,!0);ha=Ub?Wb:Ob(610401301,!1);_.Zb=Ub?Vb||!Xb:Ob(653718497,Yb);_.$b=_.ba(a=>typeof a===\"number\");_.ac=_.ba(a=>typeof a===\"string\");_.bc=_.ba(a=>typeof a===\"boolean\");_.cc=typeof _.t.BigInt===\"function\"&&typeof _.t.BigInt(0)===\"bigint\";var fc,dc,gc,ec;_.$a=_.ba(a=>_.cc?a>=dc&&a<=ec:a[0]===\"-\"?ca(a,fc):ca(a,gc));fc=Number.MIN_SAFE_INTEGER.toString();dc=_.cc?BigInt(Number.MIN_SAFE_INTEGER):void 0;gc=Number.MAX_SAFE_INTEGER.toString();ec=_.cc?BigInt(Number.MAX_SAFE_INTEGER):void 0;_.hc=typeof TextDecoder!==\"undefined\";_.ic=typeof TextEncoder!==\"undefined\";_.jc=function(){return _.fa().toLowerCase().indexOf(\"webkit\")!=-1};var ia,kc=_.t.navigator;ia=kc?kc.userAgentData||null:null;_.ua=function(a,b){return Array.prototype.indexOf.call(a,b,void 0)};_.lc=function(a,b,c){Array.prototype.forEach.call(a,b,c)};_.mc=function(a,b){return Array.prototype.some.call(a,b,void 0)};_.nc=function(a){_.nc[\" \"](a);return a};_.nc[\" \"]=function(){};var Bc;_.oc=_.la();_.pc=_.ma();_.qc=_.u(\"Edge\");_.rc=_.u(\"Gecko\")&&!(_.jc()&&!_.u(\"Edge\"))&&!(_.u(\"Trident\")||_.u(\"MSIE\"))&&!_.u(\"Edge\");_.sc=_.jc()&&!_.u(\"Edge\");_.tc=_.ta();_.uc=qa()?ia.platform===\"Windows\":_.u(\"Windows\");_.vc=qa()?ia.platform===\"Android\":_.u(\"Android\");_.wc=ra();_.xc=_.u(\"iPad\");_.yc=_.u(\"iPod\");_.zc=_.sa();\na:{var Cc=\"\",Dc=function(){var a=_.fa();if(_.rc)return/rv:([^\\);]+)(\\)|;)/.exec(a);if(_.qc)return/Edge\\/([\\d\\.]+)/.exec(a);if(_.pc)return/\\b(?:MSIE|rv)[: ]([^\\);]+)(\\)|;)/.exec(a);if(_.sc)return/WebKit\\/(\\S+)/.exec(a);if(_.oc)return/(?:Version)[ \\/]?(\\S+)/.exec(a)}();Dc&&(Cc=Dc?Dc[1]:\"\");if(_.pc){var Ec,Fc=_.t.document;Ec=Fc?Fc.documentMode:void 0;if(Ec!=null&&Ec>parseFloat(Cc)){Bc=String(Ec);break a}}Bc=Cc}_.Gc=Bc;_.Hc=_.na();_.Ic=ra()||_.u(\"iPod\");_.Jc=_.u(\"iPad\");_.Kc=_.u(\"Android\")&&!(oa()||_.na()||_.la()||_.u(\"Silk\"));_.Lc=oa();_.Mc=_.pa()&&!_.sa();var Nc;_.v=Symbol();Nc=Symbol();_.Oc=Symbol();_.Pc=Symbol();var Ca,Rc;_.Wa={};Ca={};Rc=[];Rc[_.v]=55;_.Qc=Object.freeze(Rc);_.Sc=Object.freeze({});_.Tc=Object.freeze({});_.Uc=Object.freeze({});var Ja=function(a,b){a.__closure__error__context__984382||(a.__closure__error__context__984382={});a.__closure__error__context__984382.severity=b};var Vc;_.Xc=function(a,b){const c=_.Pa(a,b);c&&!_.Ma(a,c)&&(Na(),_.Wc(a,b))};_.kb=function(a){var b;if(a&&(b=_.Oa)!=null&&b.has(a)&&(b=a.fa))for(let c=0;c<b.length;c++){const d=b[c];if(c===b.length-1&&_.Ea(d))for(const e in d){const f=d[e];Array.isArray(f)&&_.Xc(f,a)}else Array.isArray(d)&&_.Xc(d,a)}};_.Oa=void 0;_.Wc=function(a,b){let c,d;(c=_.Oa)==null||(d=c.get(b))==null||d.delete(a)};var bb;_.Yc=function(a,b){a=a.fa;return _.zb(a,a[_.v],b)};_.zb=function(a,b,c,d){if(c===-1)return null;const e=b>>15&1023||536870912;if(c>=e){if(b&256)return a[a.length-1][c]}else{var f=a.length;if(d&&b&256&&(d=a[f-1][c],d!=null)){if(xb(a,b,e,c)&&Nc!=null){var g;a=(g=Vc)!=null?g:Vc={};g=a[Nc]||0;g>=4||(a[Nc]=g+1,_.Ka())}return d}return xb(a,b,e,c)}};_.Zc=function(a,b,c){const d=a.fa;let e=d[_.v];_.Ga(e);_.yb(d,e,b,c);return a};\n_.E=function(a,b,c,d=!1){b=_.Ab(a,b,c,d);if(b==null)return b;a=a.fa;d=a[_.v];if(!(d&2)){const e=_.mb(b);e!==b&&(b=e,_.yb(a,d,c,b))}return b};_.F=function(a,b,c){c==null&&(c=void 0);return _.Zc(a,b,c)};_.G=function(a,b){a=_.Yc(a,b);return a==null||typeof a===\"boolean\"?a:typeof a===\"number\"?!!a:void 0};_.I=function(a,b){return _.Va(_.Yc(a,b))};_.J=function(a,b,c=!1){return _.Bb(_.G(a,b),c)};_.K=function(a,b){return _.Bb(_.I(a,b),\"\")};_.M=function(a,b,c){return _.Zc(a,b,c==null?c:_.Ra(c))};\n_.N=function(a,b,c){return _.Zc(a,b,c==null?c:_.Ta(c))};_.O=function(a,b,c){return _.Zc(a,b,_.Ua(c))};_.P=function(a,b,c){return _.Zc(a,b,c==null?c:_.Sa(c))};var Db,Cb;_.Q=class{constructor(a,b,c){this.fa=_.db(a,b,c)}toJSON(){return Eb(this)}Aa(a){try{return Cb=!0,a&&(Db=a===ob||a!==nb&&a!==sb&&a!==vb?ob:a),JSON.stringify(Eb(this),Za)}finally{a&&(Db=void 0),Cb=!1}}qc(){return _.za(this.fa)}};_.Q.prototype.zd=_.Wa;_.Q.prototype.toString=function(){try{return Cb=!0,Eb(this).toString()}finally{Cb=!1}};_.$c=Fb();_.ad=Fb();_.bd=Fb();var cd=class extends _.Q{constructor(){super()}};_.dd=class extends _.Q{constructor(){super()}D(a){return _.N(this,3,a)}};var ed=class extends _.Q{constructor(a){super(a)}Ic(a){return _.O(this,24,a)}};_.fd=class extends _.Q{constructor(a){super(a)}};_.R=function(){this.qa=this.qa;this.Y=this.Y};_.R.prototype.qa=!1;_.R.prototype.isDisposed=function(){return this.qa};_.R.prototype.dispose=function(){this.qa||(this.qa=!0,this.P())};_.R.prototype[Symbol.dispose]=function(){this.dispose()};_.R.prototype.P=function(){if(this.Y)for(;this.Y.length;)this.Y.shift()()};var gd=class extends _.R{constructor(){var a=window;super();this.o=a;this.i=[];this.j={}}resolve(a){var b=this.o;a=a.split(\".\");for(var c=a.length,d=0;d<c;++d)if(b[a[d]])b=b[a[d]];else return null;return b instanceof Function?b:null}ob(){for(var a=this.i.length,b=this.i,c=[],d=0;d<a;++d){var e=b[d].i(),f=this.resolve(e);if(f&&f!=this.j[e])try{b[d].ob(f)}catch(g){}else c.push(b[d])}this.i=c.concat(b.slice(a))}};var id=class extends _.R{constructor(){var a=_.hd;super();this.o=a;this.A=this.i=null;this.v=0;this.B={};this.j=!1;a=window.navigator.userAgent;a.indexOf(\"MSIE\")>=0&&a.indexOf(\"Trident\")>=0&&(a=/\\b(?:MSIE|rv)[: ]([^\\);]+)(\\)|;)/.exec(a))&&a[1]&&parseFloat(a[1])<9&&(this.j=!0)}C(a,b){this.i=b;this.A=a;b.preventDefault?b.preventDefault():b.returnValue=!1}};_.jd=class extends _.Q{constructor(a){super(a)}};var kd=class extends _.Q{constructor(a){super(a)}};var nd;_.ld=function(a,b,c=98,d=new _.dd){if(a.i){const e=new cd;_.O(e,1,b.message);_.O(e,2,b.stack);_.N(e,3,b.lineNumber);_.P(e,5,1);_.F(d,40,e);a.i.log(c,d)}};nd=class{constructor(){var a=md;this.i=null;_.J(a,4,!0)}log(a,b,c=new _.dd){_.ld(this,a,98,c)}};var od,pd;od=function(a){if(a.o.length>0){var b=a.i!==void 0,c=a.j!==void 0;if(b||c){b=b?a.v:a.A;c=a.o;a.o=[];try{_.lc(c,b,a)}catch(d){console.error(d)}}}};_.qd=class{constructor(a){this.i=a;this.j=void 0;this.o=[]}then(a,b,c){this.o.push(new pd(a,b,c));od(this)}resolve(a){if(this.i!==void 0||this.j!==void 0)throw Error(\"D\");this.i=a;od(this)}reject(a){if(this.i!==void 0||this.j!==void 0)throw Error(\"D\");this.j=a;od(this)}v(a){a.j&&a.j.call(a.i,this.i)}A(a){a.o&&a.o.call(a.i,this.j)}};\npd=class{constructor(a,b,c){this.j=a;this.o=b;this.i=c}};_.rd=a=>{var b=\"nc\";if(a.nc&&a.hasOwnProperty(b))return a.nc;b=new a;return a.nc=b};_.sd=class{constructor(){this.v=new _.qd;this.i=new _.qd;this.D=new _.qd;this.B=new _.qd;this.C=new _.qd;this.A=new _.qd;this.o=new _.qd;this.j=new _.qd;this.F=new _.qd}Y(){return this.v}M(){return this.i}N(){return this.D}L(){return this.B}qa(){return this.C}K(){return this.A}J(){return this.o}G(){return this.j}static i(){return _.rd(_.sd)}};var xd;_.ud=function(){return _.E(_.td,ed,1)};_.vd=function(){return _.E(_.td,_.fd,5)};xd=class extends _.Q{constructor(){super(wd)}};var wd;window.gbar_&&window.gbar_.CONFIG?wd=window.gbar_.CONFIG[0]||{}:wd=[];_.td=new xd;var md=_.E(_.td,kd,3)||new kd;_.ud()||new ed;_.hd=new nd;_.A(\"gbar_._DumpException\",function(a){_.hd?_.hd.log(a):console.error(a)});_.yd=new id;var Ad;_.Bd=function(a,b){var c=_.zd.i();if(a in c.i){if(c.i[a]!=b)throw new Ad;}else{c.i[a]=b;const h=c.j[a];if(h)for(let k=0,l=h.length;k<l;k++){b=h[k];var d=c.i;delete b.i[a];if(_.Hb(b.i)){for(var e=b.j.length,f=Array(e),g=0;g<e;g++)f[g]=d[b.j[g]];b.o.apply(b.v,f)}}delete c.j[a]}};_.zd=class{constructor(){this.i={};this.j={}}static i(){return _.rd(_.zd)}};_.Cd=class extends _.aa{constructor(){super()}};Ad=class extends _.Cd{};_.A(\"gbar.A\",_.qd);_.qd.prototype.aa=_.qd.prototype.then;_.A(\"gbar.B\",_.sd);_.sd.prototype.ba=_.sd.prototype.M;_.sd.prototype.bb=_.sd.prototype.N;_.sd.prototype.bd=_.sd.prototype.qa;_.sd.prototype.bf=_.sd.prototype.Y;_.sd.prototype.bg=_.sd.prototype.L;_.sd.prototype.bh=_.sd.prototype.K;_.sd.prototype.bj=_.sd.prototype.J;_.sd.prototype.bk=_.sd.prototype.G;_.A(\"gbar.a\",_.sd.i());window.gbar&&window.gbar.ap&&window.gbar.ap(window.gbar.a);var Dd=new gd;_.Bd(\"api\",Dd);\nvar Ed=_.vd()||new _.fd,Fd=window,Gd=_.y(_.I(Ed,8));Fd.__PVT=Gd;_.Bd(\"eq\",_.yd);\n}catch(e){_._DumpException(e)}\ntry{\n_.Hd=class extends _.Q{constructor(a){super(a)}};\n}catch(e){_._DumpException(e)}\ntry{\nvar Id=class extends _.Q{constructor(){super()}};var Jd=class extends _.R{constructor(){super();this.j=[];this.i=[]}o(a,b){this.j.push({features:a,options:b})}init(a,b,c){window.gapi={};var d=window.___jsl={};d.h=_.y(_.I(a,1));_.G(a,12)!=null&&(d.dpo=_.x(_.J(a,12)));d.ms=_.y(_.I(a,2));d.m=_.y(_.I(a,3));d.l=[];_.K(b,1)&&(a=_.I(b,3))&&this.i.push(a);_.K(c,1)&&(c=_.I(c,2))&&this.i.push(c);_.A(\"gapi.load\",(0,_.z)(this.o,this));return this}};var Kd=_.E(_.td,_.jd,14);if(Kd){var Ld=_.E(_.td,_.Hd,9)||new _.Hd,Md=new Id,Nd=new Jd;Nd.init(Kd,Ld,Md);_.Bd(\"gs\",Nd)};\n}catch(e){_._DumpException(e)}\n})(this.gbar_);\n// Google Inc.\n</script><style>h1,ol,ul,li,button{margin:0;padding:0}button{border:none;background:none}body{background:#202124}body,input,button{font-size:14px;font-family:Arial,sans-serif;color:var(--COEmY)}a{color:var(--JKqx2);text-decoration:none}a:hover,a:active{text-decoration:underline}a:visited{color:#c58af9}html,body{min-width:400px}body,html{height:100%;margin:0;padding:0}.gb_1d:not(.gb_oe){font:13px/27px Roboto,Arial,sans-serif;z-index:986}.gb_P{display:none}.gb_Ea,.gb_Hd{font-family:\"Google Sans Text\",Roboto,Helvetica,Arial,sans-serif;font-style:normal}a.gb_Ta{-moz-border-radius:100px;border-radius:100px;background:#0b57d0;background:var(--gm3-sys-color-primary,#0b57d0);-moz-box-sizing:border-box;box-sizing:border-box;color:#fff;color:var(--gm3-sys-color-on-primary,#fff);display:inline-block;font-size:14px;font-weight:500;min-height:40px;outline:none;padding:10px 24px;text-align:center;text-decoration:none;white-space:normal;line-height:18px;position:relative}a.gb_Ua{-moz-border-radius:100px;border-radius:100px;border:1px solid;border-color:#747775;border-color:var(--gm3-sys-color-outline,#747775);background:none;-moz-box-sizing:border-box;box-sizing:border-box;color:#0b57d0;color:var(--gm3-sys-color-primary,#0b57d0);display:inline-block;font-size:14px;font-weight:500;min-height:40px;outline:none;padding:10px 24px;text-align:center;text-decoration:none;white-space:normal;line-height:18px;position:relative}.gb_Za.gb_F a.gb_Ta,.gb_0a.gb_F a.gb_Ta,.gb_1a.gb_F a.gb_Ta{background:#c2e7ff;background:var(--gm3-sys-color-secondary-container,#c2e7ff);color:#001d35;color:var(--gm3-sys-color-on-secondary-container,#001d35)}.gb_Ea.gb_F a.gb_Ua{color:#a8c7fa;color:var(--gm3-sys-color-primary,#a8c7fa)}a.gb_od{padding:10px 12px;margin:12px 16px 12px 10px;min-width:85px}@media (max-width:640px){a.gb_od{min-width:75px}}.gb_Ea.gb_Za{color:#1f1f1f;color:var(--gm3-sys-color-on-surface,#1f1f1f)}.gb_Ea.gb_Za.gb_pd{background:#fff;background:var(--gm3-sys-color-background,#fff)}.gb_Ea.gb_Za .gb_8c.gb_9c,.gb_Ea.gb_Za a.gb_W,.gb_Ea.gb_Za span.gb_W,.gb_Ea.gb_Za .gb_qd .gb_rd,.gb_Ea.gb_Za .gb_1c .gb_rd{color:#1f1f1f;color:var(--gm3-sys-color-on-surface,#1f1f1f)}.gb_Ea.gb_Za svg{color:#444746;color:var(--gm3-sys-color-on-surface-variant,#444746)}.gb_Ea.gb_F.gb_Za{color:#e3e3e3;color:var(--gm3-sys-color-on-surface,#e3e3e3)}.gb_Ea.gb_F.gb_Za.gb_pd{background:transparent}.gb_Ea.gb_F.gb_Za .gb_8c.gb_9c,.gb_Ea.gb_F.gb_Za a.gb_W,.gb_Ea.gb_F.gb_Za span.gb_W,.gb_Ea.gb_F.gb_Za .gb_qd .gb_rd,.gb_Ea.gb_F.gb_Za .gb_1c .gb_rd{color:#e3e3e3;color:var(--gm3-sys-color-on-surface,#e3e3e3)}.gb_Ea.gb_F.gb_Za svg{color:#c4c7c5;color:var(--gm3-sys-color-on-surface-variant,#c4c7c5)}.gb_Ea.gb_F.gb_Za.gb_pd{background:#1f1f1f;background:var(--gm3-sys-color-background,#1f1f1f)}.gb_Ea.gb_0a{color:#1f1f1f;color:var(--gm3-sys-color-on-surface,#1f1f1f)}.gb_Ea.gb_0a.gb_pd{background:#e9eef6;background:var(--gm3-sys-color-surface-container-high,#e9eef6)}.gb_Ea.gb_0a .gb_8c.gb_9c,.gb_Ea.gb_0a a.gb_W,.gb_Ea.gb_0a span.gb_W,.gb_Ea.gb_0a .gb_qd .gb_rd,.gb_Ea.gb_0a .gb_1c .gb_rd{color:#1f1f1f;color:var(--gm3-sys-color-on-surface,#1f1f1f)}.gb_Ea.gb_0a svg{color:#444746;color:var(--gm3-sys-color-on-surface-variant,#444746)}.gb_Ea.gb_F.gb_0a{color:#e3e3e3;color:var(--gm3-sys-color-on-surface,#e3e3e3)}.gb_Ea.gb_F.gb_0a.gb_pd{background:#282a2c;background:var(--gm3-sys-color-surface-container-high,#282a2c)}.gb_Ea.gb_F.gb_0a .gb_8c.gb_9c,.gb_Ea.gb_F.gb_0a a.gb_W,.gb_Ea.gb_F.gb_0a span.gb_W,.gb_Ea.gb_F.gb_0a .gb_qd .gb_rd,.gb_Ea.gb_F.gb_0a .gb_1c .gb_rd{color:#e3e3e3;color:var(--gm3-sys-color-on-surface,#e3e3e3)}.gb_Ea.gb_F.gb_0a svg{color:#c4c7c5;color:var(--gm3-sys-color-on-surface-variant,#c4c7c5)}.gb_Ea.gb_1a{color:#1f1f1f;color:var(--gm3-sys-color-on-surface,#1f1f1f)}.gb_Ea.gb_1a.gb_pd{background:transparent}.gb_Ea.gb_1a .gb_8c.gb_9c,.gb_Ea.gb_1a a.gb_W,.gb_Ea.gb_1a span.gb_W,.gb_Ea.gb_1a .gb_qd .gb_rd,.gb_Ea.gb_1a .gb_1c .gb_rd{color:#1f1f1f;color:var(--gm3-sys-color-on-surface,#1f1f1f)}.gb_Ea.gb_1a svg{color:#444746;color:var(--gm3-sys-color-on-surface-variant,#444746)}.gb_Ea.gb_1a.gb_F.gb_pd{background:transparent}.gb_Ea.gb_1a.gb_F .gb_8c.gb_9c,.gb_Ea.gb_1a.gb_F a.gb_W,.gb_Ea.gb_1a.gb_F span.gb_W,.gb_Ea.gb_1a.gb_F .gb_qd .gb_rd,.gb_Ea.gb_1a.gb_F .gb_1c .gb_rd,.gb_Ea.gb_1a.gb_F svg{color:white;color:var(--og-theme-color,white)}.gb_Ea a.gb_W,.gb_Ea span.gb_W{text-decoration:none}.gb_8c{font-family:Google Sans,Roboto,Helvetica,Arial,sans-serif;font-size:20px;font-weight:400;letter-spacing:.25px;line-height:48px;margin-bottom:2px;opacity:1;overflow:hidden;padding-left:16px;position:relative;text-overflow:ellipsis;vertical-align:middle;top:2px;white-space:nowrap;-moz-box-flex:1;flex:1 1 auto}.gb_Ea.gb_bc .gb_8c{margin-bottom:0}.gb_qd.gb_sd .gb_8c{padding-left:4px}.gb_Ea.gb_bc .gb_td{position:relative;top:-2px}.gb_Ea{min-width:160px;position:relative}.gb_Ea.gb_Rc{min-width:120px}.gb_Ea.gb_ud .gb_vd{display:none}.gb_Ea.gb_ud .gb_jd{height:56px}header.gb_Ea{display:block}.gb_Ea svg{fill:currentColor}.gb_Bd{position:fixed;top:0;width:100%}.gb_wd{-moz-box-shadow:0 4px 5px 0 rgba(0,0,0,.14),0 1px 10px 0 rgba(0,0,0,.12),0 2px 4px -1px rgba(0,0,0,.2);box-shadow:0 4px 5px 0 rgba(0,0,0,.14),0 1px 10px 0 rgba(0,0,0,.12),0 2px 4px -1px rgba(0,0,0,.2)}.gb_Cd{height:64px}.gb_jd{-moz-box-sizing:border-box;box-sizing:border-box;position:relative;width:100%;display:-webkit-box;display:-moz-box;display:-ms-flexbox;display:-webkit-flex;display:flex;-moz-box-pack:space-between;justify-content:space-between;min-width:-webkit-min-content;min-width:-moz-min-content;min-width:-ms-min-content;min-width:min-content}.gb_Ea:not(.gb_bc) .gb_jd{padding:8px}.gb_Ea:not(.gb_bc) .gb_jd a.gb_xd{margin:12px 8px 12px 10px}.gb_Ea.gb_Dd .gb_jd{-moz-box-flex:1;flex:1 0 auto}.gb_Ea .gb_jd.gb_kd.gb_Ed{min-width:0}.gb_Ea.gb_bc .gb_jd{padding:4px;padding-left:8px;min-width:0}.gb_Ea.gb_bc .gb_jd a.gb_xd{margin:12px 8px 12px 10px}.gb_vd{height:48px;vertical-align:middle;white-space:nowrap;-moz-box-align:center;align-items:center;display:-webkit-box;display:-moz-box;display:-ms-flexbox;display:-webkit-flex;display:flex;-moz-user-select:-moz-none}.gb_yd>.gb_vd{display:table-cell;width:100%}.gb_qd{padding-right:25px;-moz-box-sizing:border-box;box-sizing:border-box;-moz-box-flex:1;flex:1 0 auto}.gb_Ea.gb_bc .gb_qd{padding-right:14px}.gb_zd{-moz-box-flex:1;flex:1 1 100%}.gb_zd>:only-child{display:inline-block}.gb_Ad.gb_2c{padding-left:4px}.gb_Ad.gb_Fd,.gb_Ea.gb_Dd .gb_Ad,.gb_Ea.gb_bc:not(.gb_Hd) .gb_Ad{padding-left:0}.gb_Ea.gb_bc .gb_Ad.gb_Fd{padding-right:0}.gb_Ea.gb_bc .gb_Ad.gb_Fd .gb_Va{margin-left:10px}.gb_2c{display:inline}.gb_Ea.gb_Vc .gb_Ad.gb_Id,.gb_Ea.gb_Hd .gb_Ad.gb_Id{padding-left:2px}.gb_8c{display:inline-block}.gb_Ad{-moz-box-sizing:border-box;box-sizing:border-box;height:48px;padding:0 4px;padding-left:5px;-moz-box-flex:0;flex:0 0 auto;-moz-box-pack:flex-end;justify-content:flex-end}.gb_Hd{height:48px}.gb_Ea.gb_Hd{min-width:auto}.gb_Hd .gb_Ad{float:right;padding-left:32px;padding-left:var(--og-bar-parts-side-padding,32px)}.gb_Hd .gb_Ad.gb_Jd{padding-left:0}.gb_Kd{font-size:14px;max-width:200px;overflow:hidden;padding:0 12px;text-overflow:ellipsis;white-space:nowrap;-moz-user-select:text}.gb_Oc a{color:inherit}.gb_9c{text-rendering:optimizeLegibility;-moz-osx-font-smoothing:grayscale;opacity:1}.gb_Od{position:relative}.gb_L{font-family:arial,sans-serif;line-height:normal;padding-right:15px}.gb_X{display:inline-block;padding-left:15px}.gb_X .gb_W{display:inline-block;line-height:24px;vertical-align:middle}.gb_Pd{text-align:left}.gb_J{display:none}@media screen and (max-width:319px){.gb_jd .gb_I{display:none;visibility:hidden}}.gb_I .gb_A,.gb_I .gb_A:hover,.gb_I .gb_A:focus{opacity:1}.gb_K{display:none}.gb_Q{display:none!important}.gb_ld{visibility:hidden}@media screen and (max-width:319px){.gb_jd:not(.gb_kd) .gb_I{display:none;visibility:hidden}}.gb_ad{display:inline-block;vertical-align:middle}.gb_Ne .gb_P{bottom:-3px;right:-5px}.gb_C{position:relative}.gb_A{display:inline-block;outline:none;vertical-align:middle;-moz-border-radius:50%;border-radius:50%;-moz-box-sizing:border-box;box-sizing:border-box;height:40px;width:40px;cursor:pointer;text-decoration:none}#gb#gb a.gb_A{cursor:pointer;text-decoration:none}.gb_A,a.gb_A{color:#000}x:-o-prefocus{border-bottom-color:#ccc}.gb_ka{background:#fff;border:1px solid #ccc;border-color:rgba(0,0,0,.2);color:#000;-moz-box-shadow:0 2px 10px rgba(0,0,0,.2);box-shadow:0 2px 10px rgba(0,0,0,.2);display:none;outline:none;overflow:hidden;position:absolute;right:0;top:54px;animation:gb__a .2s;-moz-border-radius:2px;border-radius:2px;-moz-user-select:text}.gb_ad.gb_Sc .gb_ka,.gb_Sc.gb_ka{display:block}.gb_Oe{position:absolute;right:0;top:54px;z-index:-1}.gb_ed .gb_ka{margin-top:-10px}.gb_ad:first-child{padding-left:4px}.gb_Ea.gb_Pe .gb_ad:first-child{padding-left:0}.gb_Qe{position:relative}.gb_1c .gb_Qe,.gb_Hd .gb_Qe{float:right}.gb_A{padding:8px;cursor:pointer}.gb_gd button svg,.gb_A{-moz-border-radius:50%;border-radius:50%}.gb_ad{padding:4px}.gb_Ea.gb_Pe .gb_ad{padding:4px 2px}.gb_Ea.gb_Pe .gb_y.gb_ad{padding-left:6px}.gb_ka{z-index:991;line-height:normal}.gb_ka.gb_id{left:0;right:auto}@media (max-width:350px){.gb_ka.gb_id{left:0}}.gb_Re .gb_ka{top:56px}.gb_O{background-size:32px 32px;border:0;-moz-border-radius:50%;border-radius:50%;display:block;margin:0px;position:relative;height:32px;width:32px;z-index:0}.gb_db{background-color:#e8f0fe;border:1px solid rgba(32,33,36,.08);position:relative}.gb_db.gb_O{height:30px;width:30px}.gb_db.gb_O:hover,.gb_db.gb_O:active{-moz-box-shadow:none;box-shadow:none}.gb_eb{background:#fff;border:none;-moz-border-radius:50%;border-radius:50%;bottom:2px;-moz-box-shadow:0px 1px 2px 0px rgba(60,64,67,.30),0px 1px 3px 1px rgba(60,64,67,.15);box-shadow:0px 1px 2px 0px rgba(60,64,67,.30),0px 1px 3px 1px rgba(60,64,67,.15);height:14px;margin:2px;position:absolute;right:0;width:14px}.gb_vc{color:#1f71e7;font:400 22px/32px Google Sans,Roboto,Helvetica,Arial,sans-serif;text-align:center;text-transform:uppercase}@media (-webkit-min-device-pixel-ratio:1.25),(min-resolution:1.25dppx),(min-device-pixel-ratio:1.25){.gb_O::before,.gb_fb::before{display:inline-block;transform:scale(0.5);transform-origin:left 0}.gb_2 .gb_fb::before{transform:scale(scale(0.416666667))}}.gb_O:hover,.gb_O:focus{-moz-box-shadow:0 1px 0 rgba(0,0,0,.15);box-shadow:0 1px 0 rgba(0,0,0,.15)}.gb_O:active{-moz-box-shadow:inset 0 2px 0 rgba(0,0,0,.15);box-shadow:inset 0 2px 0 rgba(0,0,0,.15)}.gb_O:active::after{background:rgba(0,0,0,.1);-moz-border-radius:50%;border-radius:50%;content:\"\";display:block;height:100%}.gb_gb{cursor:pointer;line-height:40px;min-width:30px;opacity:.75;overflow:hidden;vertical-align:middle;text-overflow:ellipsis}.gb_A.gb_gb{width:auto}.gb_gb:hover,.gb_gb:focus{opacity:.85}.gb_ed .gb_gb,.gb_ed .gb_Td{line-height:26px}#gb#gb.gb_ed a.gb_gb,.gb_ed .gb_Td{font-size:11px;height:auto}.gb_hb{border-top:4px solid #000;border-left:4px dashed transparent;border-right:4px dashed transparent;display:inline-block;margin-left:6px;opacity:.75;vertical-align:middle}.gb_Xa:hover .gb_hb{opacity:.85}.gb_Va>.gb_y{padding:3px 3px 3px 4px}.gb_Ud.gb_ld{color:#fff}.gb_0 .gb_gb,.gb_0 .gb_hb{opacity:1}#gb#gb.gb_0.gb_0 a.gb_gb,#gb#gb .gb_0.gb_0 a.gb_gb{color:#fff}.gb_0.gb_0 .gb_hb{border-top-color:#fff;opacity:1}.gb_ja .gb_O:hover,.gb_0 .gb_O:hover,.gb_ja .gb_O:focus,.gb_0 .gb_O:focus{-moz-box-shadow:0 1px 0 rgba(0,0,0,.15),0 1px 2px rgba(0,0,0,.2);box-shadow:0 1px 0 rgba(0,0,0,.15),0 1px 2px rgba(0,0,0,.2)}.gb_Vd .gb_y,.gb_Wd .gb_y{position:absolute;right:1px}.gb_y.gb_Z,.gb_ib.gb_Z,.gb_Xa.gb_Z{-moz-box-flex:0;flex:0 1 auto}.gb_Xd.gb_Zd .gb_gb{width:30px!important}.gb_0d{height:40px;position:absolute;right:-5px;top:-5px;width:40px}.gb_1d .gb_0d,.gb_2d .gb_0d{right:0;top:0}.gb_y .gb_A{padding:4px}.gb_R{display:none}.gb_Xa:not(.gb_xd){position:relative}.gb_Xa:not(.gb_xd)::after{content:\"\";border:1px solid #202124;opacity:.13;position:absolute;top:4px;left:4px;-moz-border-radius:50%;border-radius:50%;width:30px;height:30px}.gb_Va{-moz-box-sizing:border-box;box-sizing:border-box;cursor:pointer;display:inline-block;height:48px;overflow:hidden;outline:none;padding:7px 0 0 16px;vertical-align:middle;width:142px;-moz-border-radius:28px;border-radius:28px;background-color:transparent;border:1px solid;position:relative}.gb_Va .gb_Xa{width:32px;height:32px;padding:0}.gb_Za .gb_Va,.gb_0a .gb_Va{border-color:#747775;border-color:var(--gm3-sys-color-outline,#747775)}.gb_Za.gb_F .gb_Va,.gb_0a.gb_F .gb_Va{border-color:#8e918f;border-color:var(--gm3-sys-color-outline,#8e918f)}.gb_1a .gb_Va{border-color:#747775;border-color:var(--gm3-sys-color-outline,#747775)}.gb_1a.gb_F .gb_Va{border-color:#e3e3e3;border-color:var(--gm3-sys-color-on-surface,#e3e3e3)}.gb_2a{display:inherit}.gb_Va .gb_2a{background:#fff;-moz-border-radius:6px;border-radius:6px;display:inline-block;left:15px;position:initial;padding:2px;top:-1px;height:32px;-moz-box-sizing:border-box;box-sizing:border-box;width:78px}.gb_3a{text-align:center}.gb_3a.gb_4a{background-color:#f1f3f4}.gb_3a .gb_Hc{vertical-align:middle;max-height:28px;max-width:74px}.gb_Ea .gb_Va .gb_y.gb_ad{padding:0;margin-right:9px;float:right}.gb_Ea:not(.gb_bc) .gb_Va{margin-left:10px;margin-right:4px}sentinel{}:root{--COEmY:#e8e8e8;--xhUGwc:#1f1f1f}:root{--vZe0jb:#757fa4;--nwXobb:#757fa4;--VuZXBd:#eef0ff;--uLz37c:#c3c6d6;--jINu6c:#eef0ff;--TyVYld:#8ab4f8;--ZEpPmd:#424654;--QWaaaf:#757fa4;--DEeStf:#242832;--TSWZIb:#2c303d;--BRLwE:#3a3f50;--gS5jXb:#313335;--Aqn7xd:#444746;--EpFNW:#1f1f1f;--IXoxUe:#9e9e9e;--bbQxAb:#bfbfbf;--YLNNHc:#e8e8e8;--TMYS9:#a8c7fa;--JKqx2:#99c3ff;--rrJJUc:#a8c7fa;--mXZkqc:#444746;--Nsm0ce:#a8c7fa;--XKMDxc:#28292a;--aYn2S:#333438;--Lm570b:#3a3f50}.IiOSLb .rsGxI.Ww4FFb,.Ww4FFb{background-color:var(--xhUGwc);border-radius:0px;border-width:0}.IiOSLb .rsGxI.Ww4FFb,.Ww4FFb{box-shadow:none}.Ww4FFb .mnr-c:not(:empty),.mnr-c:not(:empty) .Ww4FFb,.Ww4FFb .Ww4FFb{box-shadow:none;margin-bottom:0px}.vt6azd{margin:0px 0px 8px}.vt6azd{margin:0px 0px 30px}.jbBItf{display:block;position:relative}.DU0NJ{bottom:0;left:0;position:absolute;right:0;top:0}.lP3Jof{display:inline-block;position:relative}.nNMuOd{animation:qli-container-rotate 1568.2352941176ms linear infinite}@keyframes qli-container-rotate{from{transform:rotate(0)}to{transform:rotate(1turn)}}.RoKmhb{height:100%;opacity:0;position:absolute;width:100%}.nNMuOd .VQdeab{animation:qli-fill-unfill-rotate 5332ms cubic-bezier(0.4,0,0.2,1) infinite both,qli-blue-fade-in-out 5332ms cubic-bezier(0.4,0,0.2,1) infinite both}.nNMuOd .IEqiAf{animation:qli-fill-unfill-rotate 5332ms cubic-bezier(0.4,0,0.2,1) infinite both,qli-red-fade-in-out 5332ms cubic-bezier(0.4,0,0.2,1) infinite both}.nNMuOd .smocse{animation:qli-fill-unfill-rotate 5332ms cubic-bezier(0.4,0,0.2,1) infinite both,qli-yellow-fade-in-out 5332ms cubic-bezier(0.4,0,0.2,1) infinite both}.nNMuOd .FlKbCe{animation:qli-fill-unfill-rotate 5332ms cubic-bezier(0.4,0,0.2,1) infinite both,qli-green-fade-in-out 5332ms cubic-bezier(0.4,0,0.2,1) infinite both}.BSnLb .nNMuOd .RoKmhb{animation:qli-fill-unfill-rotate 5332ms cubic-bezier(0.4,0,0.2,1) infinite both;opacity:0.99}@keyframes qli-fill-unfill-rotate{0%{transform:rotate(0)}12.5%{transform:rotate(135deg)}25%{transform:rotate(270deg)}37.5%{transform:rotate(405deg)}50%{transform:rotate(540deg)}62.5%{transform:rotate(675deg)}75%{transform:rotate(810deg)}87.5%{transform:rotate(945deg)}100%{transform:rotate(3turn)}}@keyframes qli-blue-fade-in-out{0%{opacity:0.99}25%{opacity:0.99}26%{opacity:0}89%{opacity:0}90%{opacity:0.99}100%{opacity:0.99}}@keyframes qli-red-fade-in-out{0%{opacity:0}15%{opacity:0}25%{opacity:0.99}50%{opacity:0.99}51%{opacity:0}}@keyframes qli-yellow-fade-in-out{0%{opacity:0}40%{opacity:0}50%{opacity:0.99}75%{opacity:0.99}76%{opacity:0}}@keyframes qli-green-fade-in-out{0%{opacity:0}65%{opacity:0}75%{opacity:0.99}90%{opacity:0.99}100%{opacity:0}}.beDQP{display:inline-block;height:100%;overflow:hidden;position:relative;width:50%}.FcXfi{-moz-box-sizing:border-box;box-sizing:border-box;height:100%;left:45%;overflow:hidden;position:absolute;top:0;width:10%}.SPKFmc{border-radius:50%;border:3px solid transparent;-moz-box-sizing:border-box;box-sizing:border-box}@media (forced-colors:active),(prefers-contrast:more){.beDQP:last-child .SPKFmc{border:none}}.x3SdXd{width:200%}.J7uuUe{transform:rotate(129deg)}.sDPIC{left:-100%;transform:rotate(-129deg)}.tS3P5{left:-450%;width:1000%}.VQdeab .SPKFmc{border-color:#4487f6}.IEqiAf .SPKFmc{border-color:#ff7769}.smocse .SPKFmc{border-color:#824300}.FlKbCe .SPKFmc{border-color:#219540}.RoKmhb .J7uuUe{border-bottom-color:transparent;border-right-color:transparent}.RoKmhb .sDPIC{border-bottom-color:transparent;border-left-color:transparent}.RoKmhb .tS3P5{border-bottom-color:transparent}.GgTJWe .nNMuOd .J7uuUe{animation:qli-left-spin 1333ms cubic-bezier(0.4,0,0.2,1) infinite both}.GgTJWe .nNMuOd .sDPIC{animation:qli-right-spin 1333ms cubic-bezier(0.4,0,0.2,1) infinite both}.BSnLb .nNMuOd .J7uuUe{animation:qli-left-spin 1333ms cubic-bezier(0.4,0,0.2,1) infinite both;border-left-color:var(--EpFNW);border-top-color:var(--EpFNW)}.BSnLb .nNMuOd .sDPIC{animation:qli-right-spin 1333ms cubic-bezier(0.4,0,0.2,1) infinite both;border-right-color:var(--EpFNW);border-top-color:var(--EpFNW)}.BSnLb .nNMuOd .tS3P5{border-color:var(--EpFNW);border-bottom-color:transparent}@keyframes qli-left-spin{0%{transform:rotate(130deg)}50%{transform:rotate(-5deg)}100%{transform:rotate(130deg)}}@keyframes qli-right-spin{0%{transform:rotate(-130deg)}50%{transform:rotate(5deg)}100%{transform:rotate(-130deg)}}.OhScic{margin:0px}.zsYMMe{padding:0px}.bNg8Rb{clip:rect(1px,1px,1px,1px);height:1px;overflow:hidden;position:absolute;white-space:nowrap;width:1px;z-index:-1000;user-select:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none}.TBC9ub{margin-left:0px;margin-right:0px}.OZ5bRd{margin-bottom:auto;margin-top:auto}.wgbRNb{cursor:pointer;height:72px;position:absolute;display:block;visibility:inherit;width:36px;bottom:0;opacity:.8;top:0;z-index:101}.wgbRNb.tHT0l{transition:opacity .5s,visibility .5s}.wgbRNb:hover{opacity:.9}.wgbRNb.pQXcHc,.wgbRNb.pQXcHc:hover{cursor:default;opacity:0;visibility:hidden}.b5K9zd{bottom:0;display:block;position:absolute!important;top:0}.wgbRNb.ENJHPd:hover g-fab{color:#dadce0!important}.bCwlI.ENJHPd g-fab,.VdehBf.ENJHPd g-fab{box-shadow:0 7px 15px rgba(0,0,0,0.8);}.wgbRNb.ENJHPd{height:50px;width:50px;opacity:1}.wgbRNb.ENJHPd.pQXcHc,.wgbRNb.ENJHPd.pQXcHc:hover{opacity:0}.bCwlI.ENJHPd g-fab,.VdehBf.ENJHPd g-fab{cursor:pointer;height:50px;width:50px}.bCwlI.ENJHPd{left:-25px}.VdehBf.ENJHPd{right:-25px}.wgbRNb.HEeAqe:hover g-fab{color:#dadce0!important}.wgbRNb.HEeAqe{height:48px;width:48px;opacity:.9}.wgbRNb.HEeAqe:hover{opacity:1}.wgbRNb.HEeAqe.pQXcHc,.wgbRNb.HEeAqe.pQXcHc:hover{opacity:0}.bCwlI.HEeAqe g-fab,.VdehBf.HEeAqe g-fab{box-shadow:0 7px 15px rgba(0,0,0,0.2);cursor:pointer;height:48px;width:48px}.bCwlI.HEeAqe{left:20px}.VdehBf.HEeAqe{right:20px}.wgbRNb.TVgFVb:hover g-fab{color:#dadce0!important}.wgbRNb.TVgFVb{height:40px;width:40px}.bCwlI.TVgFVb g-fab,.VdehBf.TVgFVb g-fab{box-shadow:0 4px 4px rgba(0,0,0,0.3),0 0 4px rgba(0,0,0,0.2);cursor:pointer}.bCwlI.TVgFVb{left:-58px}.VdehBf.TVgFVb{right:-58px}.bCwlI.Yze26d .vWtmKf,.VdehBf.Yze26d .PFY4o{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAuCAYAAAAcEfjaAAABV0lEQVRIx+2VsW7CQBBEDwTpIkXICMuyJdtfQsGXQUVFlSpVmjTESOn4BAoXLigsueAvaM2MBAht7g6v06ZYwNK8893ezGLatjV5ni9QO2PMC599ZdI0nWdZdgbQ4vsH0NgLQLSn+FZ4/gY0cgJBELxCdHiEUF+AhlaAH9jWG0SleNOnDbr/iON4AlEloA9AAyvAiqIogPAooHcnwIJghqrFmTZOgJUkSQRRI6C1E7huL8GbTmJ7Ky2w/PuWVIcOw3Daua2qi1NZQ20+i723XnurA/QQ0aJTRJ8J/oEuAFvNqcjWPwV4ibzM66Weeck+8YhTUNhm7xIPaUAhPtCoVjGtLdxbMgK/zsCwMDRi5YrhsnaJcRQrHzkNrW1l0MXKNQeCy95rsXLDUeNK3EqsfOIQ8/0DLVWAeku9Du1rK6ehE1BfnNoavcwn7L3tZO9eARIRLW4RvQA0+6DNwTHW6QAAAABJRU5ErkJggg==);background-repeat:no-repeat;height:20px;opacity:.56;width:12px}.bCwlI.Yze26d .vWtmKf,.VdehBf.Yze26d .PFY4o{filter:invert(1)}.bCwlI.Yze26d,.VdehBf.Yze26d{opacity:.9;width:80px}.bCwlI.Yze26d::after,.VdehBf.Yze26d::after{content:\"\";min-height:48px;height:100%;left:50%;transform:translate(-50%,-50%);position:absolute;top:50%;min-width:48px;width:100%}.bCwlI.Yze26d .vWtmKf{background-position:0 -26px;left:2px}.VdehBf.Yze26d .PFY4o{background-position:0 0;right:2px}.bCwlI.Yze26d{background:linear-gradient(90deg,#202124 50%,rgba(48,49,52,.09) 100%);left:0}.VdehBf.Yze26d{background:linear-gradient(270deg,#202124 50%,rgba(48,49,52,.09) 100%);right:0}@media (forced-colors:active){.bCwlI.Yze26d,.VdehBf.Yze26d{background:#202124;opacity:1;width:48px}.bCwlI.Yze26d .vWtmKf,.VdehBf.Yze26d .PFY4o{opacity:1}}@media (forced-colors:active) and (prefers-color-scheme:dark){.bCwlI.Yze26d .vWtmKf,.VdehBf.Yze26d .PFY4o{filter:invert(1) contrast(200%)}}@media (forced-colors:active) and (prefers-color-scheme:light){.bCwlI.Yze26d .vWtmKf,.VdehBf.Yze26d .PFY4o{filter:contrast(200%)}}.wgbRNb.T9Wh5:hover g-fab{color:#dadce0!important;box-shadow:0 0 0 1px rgba(0,0,0,0.04),0 4px 8px 0 rgba(0,0,0,0.2)}.wgbRNb.T9Wh5{height:36px;width:36px;opacity:1}.wgbRNb.T9Wh5.pQXcHc,.wgbRNb.T9Wh5.pQXcHc:hover{opacity:0}.bCwlI.T9Wh5 g-fab,.VdehBf.T9Wh5 g-fab{cursor:pointer;height:36px;width:36px}.bCwlI.T9Wh5{left:-18px}.VdehBf.T9Wh5{right:-18px}.bCwlI.SL0Gp .vWtmKf,.VdehBf.SL0Gp .PFY4o{background-repeat:no-repeat;opacity:.54;width:24px}.bCwlI.SL0Gp .vWtmKf{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAQAAABKfvVzAAAAQ0lEQVR4AWNABaNAAQhJUv4ACGVJUf6f4TADDw2VHyFN+VEGXmJdfwCo/AeDNIhJqQ2E/UBLLYiApVFMI9ISWWAUAAAy1x3G3j1wxQAAAABJRU5ErkJggg==)}.bCwlI.SL0Gp .vWtmKf{filter:invert(1)}.VdehBf.SL0Gp .PFY4o{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAQAAABKfvVzAAAAUElEQVR4AWOAgFHAAYQkKd8BhCRoUWZ4wfCfNC1aDC/poEWb4RWpWnQYfgK1bCBWOSPDFKDyHwwexCqfTFPlkyDKiQ/SH2DlJAAPIKQIjAIADAMd5ce1j0IAAAAASUVORK5CYII=)}.VdehBf.SL0Gp .PFY4o{filter:invert(1)}.VdehBf.SL0Gp,.bCwlI.SL0Gp{width:24px}.wgbRNb.SL0Gp.pQXcHc,.wgbRNb.SL0Gp.pQXcHc:hover{cursor:default;opacity:1;visibility:inherit}.wgbRNb.SL0Gp.pQXcHc .vWtmKf,.wgbRNb.SL0Gp.pQXcHc .PFY4o{opacity:.2}.wgbRNb.SL0Gp{height:24px;margin:0}.wgbRNb.SL0Gp:hover{opacity:1}.wgbRNb.zfpUke:hover g-fab{color:#dadce0!important}.wgbRNb.zfpUke{height:36px;width:36px;opacity:0}.z1Mm0e:hover .wgbRNb.zfpUke{opacity:.9}.z1Mm0e .wgbRNb.zfpUke:hover,.z1Mm0e .wgbRNb.zfpUke:focus-visible{opacity:1}.wgbRNb.zfpUke.pQXcHc,.wgbRNb.zfpUke.pQXcHc:hover{opacity:0}.bCwlI.zfpUke g-fab,.VdehBf.zfpUke g-fab{box-shadow:0 0 0 1px rgba(0,0,0,0.04),0 4px 8px 0 rgba(0,0,0,0.2);cursor:pointer;height:36px;width:36px}.bCwlI.zfpUke{left:16px}.VdehBf.zfpUke{right:16px}.bCwlI.ENpXyb .vWtmKf,.VdehBf.ENpXyb .PFY4o{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAuCAYAAAAcEfjaAAABV0lEQVRIx+2VsW7CQBBEDwTpIkXICMuyJdtfQsGXQUVFlSpVmjTESOn4BAoXLigsueAvaM2MBAht7g6v06ZYwNK8893ezGLatjV5ni9QO2PMC599ZdI0nWdZdgbQ4vsH0NgLQLSn+FZ4/gY0cgJBELxCdHiEUF+AhlaAH9jWG0SleNOnDbr/iON4AlEloA9AAyvAiqIogPAooHcnwIJghqrFmTZOgJUkSQRRI6C1E7huL8GbTmJ7Ky2w/PuWVIcOw3Daua2qi1NZQ20+i723XnurA/QQ0aJTRJ8J/oEuAFvNqcjWPwV4ibzM66Weeck+8YhTUNhm7xIPaUAhPtCoVjGtLdxbMgK/zsCwMDRi5YrhsnaJcRQrHzkNrW1l0MXKNQeCy95rsXLDUeNK3EqsfOIQ8/0DLVWAeku9Du1rK6ehE1BfnNoavcwn7L3tZO9eARIRLW4RvQA0+6DNwTHW6QAAAABJRU5ErkJggg==);background-repeat:no-repeat;height:20px;width:12px}.bCwlI.ENpXyb .vWtmKf,.VdehBf.ENpXyb .PFY4o{filter:invert(1)}.bCwlI.ENpXyb .vWtmKf{background-position:0 -26px;left:8px}.VdehBf.ENpXyb .PFY4o{background-position:0 0;right:8px}.bCwlI.ENpXyb{left:0}.VdehBf.ENpXyb{right:0}.bCwlI.ENpXyb{border-bottom-right-radius:36px;border-top-right-radius:36px;box-shadow:1px 0 2px rgba(0,0,0,0.5)}.VdehBf.ENpXyb{border-bottom-left-radius:36px;border-top-left-radius:36px;box-shadow:-1px 0 2px rgba(0,0,0,0.5)}.bCwlI.AoT6sc .vWtmKf{left:2px}.VdehBf.AoT6sc .PFY4o{right:2px}.bCwlI.AoT6sc{left:-32px;border:1px solid #f8f9fa;border-radius:2px 0 0 2px}.VdehBf.AoT6sc{right:-32px;border:1px solid #f8f9fa;border-radius:0 2px 2px 0}.wgbRNb.AoT6sc{background-image:linear-gradient(top,#0a0a0a,#f8f9fa);height:50px;width:30px}.wgbRNb.AoT6sc:hover{background-image:linear-gradient(top,#050505,#f8f9fa)}.wgbRNb.btpNFe:hover g-fab{color:#dadce0!important}.wgbRNb.btpNFe{height:36px;width:36px;opacity:.9}.wgbRNb.btpNFe:hover{opacity:1}.wgbRNb.btpNFe.pQXcHc,.wgbRNb.btpNFe.pQXcHc:hover{opacity:0}.bCwlI.btpNFe g-fab,.VdehBf.btpNFe g-fab{box-shadow:0 0 0 1px rgba(0,0,0,0.04),0 4px 8px 0 rgba(0,0,0,0.2);cursor:pointer;height:36px;width:36px}.bCwlI.btpNFe{left:-18px}.VdehBf.btpNFe{right:-18px}.wgbRNb.jSZMre:hover g-fab{color:#dadce0!important}.wgbRNb.jSZMre{height:32px;width:32px;opacity:.9;padding:8px}.wgbRNb.jSZMre:hover{opacity:1}.wgbRNb.jSZMre.pQXcHc,.wgbRNb.jSZMre.pQXcHc:hover{opacity:0}.bCwlI.jSZMre g-fab,.VdehBf.jSZMre g-fab{box-shadow:0 7px 15px rgba(0,0,0,0.2);cursor:pointer;height:32px;width:32px}.bCwlI.jSZMre{left:20px}.VdehBf.jSZMre{right:20px}.wgbRNb.vkcLib{height:40px;width:40px;opacity:0}.z1Mm0e:hover .wgbRNb.vkcLib,.z1Mm0e .wgbRNb.vkcLib:hover,.z1Mm0e .wgbRNb.vkcLib:focus-visible{opacity:1}.wgbRNb.vkcLib.pQXcHc,.wgbRNb.vkcLib.pQXcHc:hover{opacity:0}.bCwlI.vkcLib g-fab,.VdehBf.vkcLib g-fab{box-shadow:0 0 0 1px rgba(0,0,0,0.04),0 4px 8px 0 rgba(0,0,0,0.2);cursor:pointer;height:24px;width:24px;position:relative;top:8px;left:8px}.bCwlI.vkcLib{left:4px}.VdehBf.vkcLib{right:4px}.OvQkSb{border-radius:9999px}.S3PB2d{margin:auto}.sr9hec{display:block;position:relative;z-index:0}.sr9hec{cursor:pointer}.sr9hec{box-shadow:0,0,2,0 rgba(0,0,0,0.5)}.sr9hec:focus{outline:none}.sr9hec .U8v51e{position:absolute;left:0;right:0;top:0;bottom:0;width:24px;height:24px}.s3IB3{width:40px;height:40px}.a11Pr{width:56px;height:56px}.MKCV1b{width:28px;height:28px}.sr9hec.MKCV1b .U8v51e{width:22px;height:22px}.OZQDWd{width:18px;height:18px}.sr9hec.OZQDWd .U8v51e{width:12px;height:12px}.e5KZJf{display:none;position:absolute;width:100%;height:100%;opacity:.1;left:0;top:0}.e5KZJf:active{animation-duration:.4s;animation-name:shift}@keyframes shift{25%{background:var(--YLNNHc)}}.Xx7Mif{contain:style;position:relative}.E5eFb{width:100%;contain:size style}.LoygGf{width:100%;display:block}.CTOaxb{position:fixed;top:0;left:0;contain:size layout style}.OMqmfd{position:sticky;top:0}.zLSRge{border-bottom:1px solid var(--gS5jXb)}.AX1Qf{position:absolute;left:50vw;width:1px;height:2px;visibility:hidden;pointer-events:none}.KLEmSd{border-bottom:1px solid #313335}.v0rrvd{padding-bottom:16px}.GUHazd{padding-bottom:12px}.bvSTKc{padding:8px}.cB4NFc{padding-top:16px}.OdBhM{padding-top:8px}.wok5vf{padding:24px}@media (min-height:576px){.uSolm .qk7LXc{height:100%}.mcPPZ.uSolm{padding:64px 0px}.qk7LXc.uSolm{height:calc(100% - 128px)}}@media (max-height:575px){.uSolm .qk7LXc,.qk7LXc.uSolm{height:100%;max-height:448px}}@media (min-height:496px){.GeOznc .qk7LXc{height:100%}.mcPPZ.GeOznc{padding:24px 0px}.qk7LXc.GeOznc{height:calc(100% - 48px)}}@media (max-height:495px){.GeOznc .qk7LXc,.qk7LXc.GeOznc{height:100%;max-height:448px}}.TUOsUe{text-align:left}.kJFf0c.ivkdbf{filter:none}.KUf18.ivkdbf{background-color:rgba(0,0,0,0.6);opacity:1;visibility:inherit}.VfsLpf.ivkdbf{background-color:#000;opacity:.4;visibility:inherit}.J3Hnlf.ivkdbf{background-color:#202124;opacity:.7;visibility:inherit}.X46m8.ivkdbf{background-color:#000;opacity:.8;visibility:inherit}.cBoDed.ivkdbf{background-color:#303134;opacity:.85;visibility:inherit}.kyk7qb.ivkdbf{background-color:#202124;opacity:.6;visibility:inherit}.qk7LXc.ivkdbf{opacity:1}.mcPPZ.ivkdbf{opacity:1;visibility:inherit}.mcPPZ.nP0TDe{cursor:pointer}.mcPPZ.nP0TDe .qk7LXc{cursor:default}.kJFf0c{position:fixed;z-index:9997;right:0;bottom:-200px;top:0;left:0;transition:opacity .25s;opacity:0;visibility:hidden}.qk7LXc{display:inline-block;z-index:9997;background-color:#202124;opacity:0;white-space:normal;overflow:hidden}.qk7LXc{border-radius:8px}.qk7LXc{box-shadow:0px 5px 26px 0px rgba(0,0,0,0.5),0px 20px 28px 0px rgba(0,0,0,0.5)}.qk7LXc.DJEOfc{background-color:transparent}.qk7LXc.DJEOfc{box-shadow:none}.qk7LXc.Fb1AKc{position:relative;vertical-align:middle}.qk7LXc.By9mMc{position:fixed}.qk7LXc.W6Z5of{top:50%;left:50%;transform:translate(-50%,-50%)}.qk7LXc.ulWzbd{position:absolute}.qk7LXc.P1WYLb{border:1px solid var(--mXZkqc);box-shadow:#dadce0}.mcPPZ{position:fixed;right:0;bottom:0;top:0;left:0;z-index:9997;vertical-align:middle;visibility:hidden;white-space:nowrap;max-height:100%;max-width:100%;overflow-x:hidden;overflow-y:auto}.mcPPZ.xg7rAe{text-align:center}.mcPPZ::after{content:\"\";display:inline-block;height:100%;vertical-align:middle}.LjfRsf{height:0;opacity:0;position:absolute;width:0}.VH47ed{visibility:hidden}.TaoyYc{overflow:hidden}.TaoyYc{position:fixed;width:100%}.qk7LXc.aJPx6e{overflow:visible}.vAJJzd{opacity:.999}.yMNJR .qk7LXc,.qk7LXc.yMNJR{max-width:100%}.cJFqsd .qk7LXc,.qk7LXc.cJFqsd{height:100%}.rfx2Y .qk7LXc,.qk7LXc.rfx2Y{width:100%}.BhUHze .qk7LXc,.qk7LXc.BhUHze{width:75%}.dgVGnc .qk7LXc,.qk7LXc.dgVGnc{width:90%}.GmoL0c{margin-right:12px}.xTWltf{margin-right:24px}.Wt5Tfe{padding-left:0px;padding-right:0px}.eJtrMc{padding-bottom:8px;padding-top:8px}.MTIaKb,.LwDUdc,.FAoEle,.RlTCPd,.wPNfjb,.caNvfd,.Vnob4b,.bbxTBb,.DpgmK,.YKUhfb,.uNnvb,.aVsZpf,.RoOVmf,.dIfvQd,.V3Ezn,.Enb9pe,.mYuoaf,.kJSB8,.tUr4Kc,.iQMtqe{--Yi4Nb:var(--mXZkqc);--pEa0Bc:var(--bbQxAb);--kloG3:var(--mXZkqc);--YaIeMb:var(--XKMDxc);--Pa8Wlb:var(--Nsm0ce);--izGsqb:var(--Nsm0ce);--todMNcl:var(--EpFNW);--p9J9c:var(--Nsm0ce)}:root{--KIZPne:#34517d;--xPpiM:#eef0ff;--Ehh4mf:var(--Nsm0ce)}:root{--Yi4Nb:#444746;--pEa0Bc:#bfbfbf;--kloG3:#444746;--YaIeMb:#28292a;--Pa8Wlb:#a8c7fa;--izGsqb:#a8c7fa;--todMNcl:#202124;--p9J9c:#a8c7fa}.btku5b{display:inline-block;vertical-align:middle;cursor:pointer}.k0Jjg,.brKmxb:focus-visible{outline:0;-webkit-tap-highlight-color:transparent}.k0Jjg:focus-visible .niO4u,.brKmxb:focus-visible .niO4u{outline:2px solid var(--Pa8Wlb);outline-offset:1px}.niO4u{display:-moz-box;display:flex;-moz-box-pack:center;justify-content:center;position:relative;-moz-box-align:stretch;align-items:stretch;width:100%}.niO4u::before{content:\"\";height:48px;left:0;margin-top:-24px;margin-left:-1px;margin-right:-1px;position:absolute;right:0;top:50%}.kHtcsd{display:-moz-box;display:flex;-moz-box-align:center;align-items:center;-moz-box-pack:center;justify-content:center;width:100%}.d3o3Ad,.clOx1e{display:-moz-box;display:flex;-moz-box-align:center;align-items:center;margin:6px 16px}.QuU3Wb{margin-top:6px}.pdJ1Ff{height:20px}.mh3zcf.mh3zcf.mh3zcf,.btku5b[selected] .KAe3ie{margin-left:0}.pdJ1Ff.pdJ1Ff.pdJ1Ff,.R04TOd.R04TOd.R04TOd{margin-left:12px;margin-right:8px}.VDgVie{text-align:center}.SlP8xc{text-transform:none}.A29zgf .niO4u{border-radius:8px}.A29zgf .kHtcsd{border-radius:7px}.pAn7ne{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.fCrZyc .niO4u,.fCrZyc .kHtcsd{border-radius:9999px}.LwdV0e.OJeuxf .niO4u{width:36px;height:36px}.gUAcff.OJeuxf .niO4u{width:32px;height:32px}.NQYJvc.OJeuxf .niO4u{width:44px;height:44px}.OJeuxf .niO4u{margin:0 auto}.OJeuxf .kHtcsd{height:100%}.gJdC8e.gJdC8e.gJdC8e{margin:0}.OJeuxf .niO4u::before{width:48px;margin-left:-24px;left:50%}.k1U36b{font-size:12px;font-family:Arial,sans-serif-medium,sans-serif;font-weight:500;line-height:16px}.gUAcff .clOx1e,.gUAcff .d3o3Ad{margin:8px 16px}.gUAcff .d3o3Ad{height:16px}.LwdV0e .clOx1e,.LwdV0e .d3o3Ad{margin:8px 16px}.NQYJvc .clOx1e,.NQYJvc .d3o3Ad{margin:12px 16px}.NQYJvc .d3o3Ad{height:20px}.FR7ZSc{color:var(--rrJJUc)}.FR7ZSc .niO4u{background-color:transparent;outline:1px solid var(--Yi4Nb);outline-offset:-1px}.FR7ZSc:not([selected]):not([disabled]) .d3o3Ad{color:#b1c5ff}.FR7ZSc .k0Jjg:hover .niO4u,.FR7ZSc.k0Jjg:hover .niO4u{color:var(--jINu6c)}.FR7ZSc:not([selected]) .k0Jjg:hover .d3o3Ad,.FR7ZSc.k0Jjg:not([selected]):hover .d3o3Ad{color:var(--jINu6c)}.FR7ZSc .k0Jjg:hover .kHtcsd,.FR7ZSc.k0Jjg:hover .kHtcsd{background-color:#a8c7fa15}.brKmxb:active .FR7ZSc .niO4u,.brKmxb:focus-visible .FR7ZSc .niO4u,.FR7ZSc.k0Jjg:active .niO4u,.FR7ZSc .k0Jjg:active .niO4u,.FR7ZSc.k0Jjg:focus-visible .niO4u,.FR7ZSc .k0Jjg:focus-visible .niO4u{color:var(--jINu6c)}.brKmxb:focus-visible .FR7ZSc:not([selected]):not([disabled]) .d3o3Ad,.FR7ZSc:not([selected]):not([disabled]) .k0Jjg:focus-visible .d3o3Ad,.FR7ZSc.k0Jjg:not([selected]):not([disabled]):focus-visible .d3o3Ad{color:#eef0ff}.brKmxb:focus-visible .FR7ZSc .kHtcsd,.FR7ZSc .k0Jjg:focus-visible .kHtcsd,.FR7ZSc.k0Jjg:focus-visible .kHtcsd{background-color:#a8c7fa15}.brKmxb:active .FR7ZSc:not([selected]):not([disabled]) .d3o3Ad,.FR7ZSc:not([selected]):not([disabled]) .k0Jjg:active .d3o3Ad,.FR7ZSc.k0Jjg:not([selected]):not([disabled]):active .d3o3Ad{color:var(--IXoxUe)}.brKmxb:active .FR7ZSc .kHtcsd,.FR7ZSc .k0Jjg:active .kHtcsd,.FR7ZSc.k0Jjg:active .kHtcsd{background-color:#a8c7fa39}.expUPd{color:var(--todMNcl)}.expUPd .niO4u{background-color:var(--izGsqb)}.expUPd.k0Jjg:hover .kHtcsd,.expUPd .k0Jjg:hover .kHtcsd{background-color:#eef0ff39}.brKmxb:focus-visible .expUPd .kHtcsd,.expUPd.k0Jjg:focus-visible .kHtcsd,.expUPd .k0Jjg:focus-visible .kHtcsd{background-color:#eef0ff33}.brKmxb:active .expUPd .kHtcsd,.expUPd .k0Jjg:active .kHtcsd,.expUPd.k0Jjg:active .kHtcsd{background-color:#eef0ff66}@media (prefers-contrast:more){.expUPd .niO4u{outline:1px solid #202124;outline-offset:-1px}}.zqrO0{color:#dadce0}.zqrO0 .niO4u{background-color:var(--xhUGwc);outline:1px solid #5f6368;outline-offset:-1px}.brKmxb:active .zqrO0,.brKmxb:focus-visible .zqrO0,.zqrO0 .k0Jjg:hover,.zqrO0 .k0Jjg:active,.zqrO0 .k0Jjg:focus-visible,.zqrO0.k0Jjg:hover,.zqrO0.k0Jjg:focus-visible,.zqrO0.k0Jjg:active{color:var(--bbQxAb)}.zqrO0 .k0Jjg:hover .kHtcsd,.zqrO0.k0Jjg:hover .kHtcsd{background-color:#a8c7fa15}.brKmxb:focus-visible .zqrO0 .kHtcsd,.zqrO0 .k0Jjg:focus-visible .kHtcsd,.zqrO0.k0Jjg:focus-visible .kHtcsd{background-color:#a8c7fa15}.brKmxb:active .zqrO0 .kHtcsd,.zqrO0 .k0Jjg:active .kHtcsd,.zqrO0.k0Jjg:active .kHtcsd{background-color:#a8c7fa39}.xab99e .niO4u{z-index:2;background-color:var(--xhUGwc);outline:1px solid var(--mXZkqc)}.xab99e.xab99e.OJeuxf .niO4u{width:40px;height:40px}.xab99e.k0Jjg:hover .kHtcsd,.xab99e .k0Jjg:hover .kHtcsd{outline:0;outline-offset:-1px;box-shadow:0px 4px 12px rgba(23,23,23,0.9)}.NtaMpb{color:var(--bbQxAb)}.NtaMpb .niO4u{background-color:var(--xhUGwc)}.NtaMpb .k0Jjg:hover .kHtcsd,.NtaMpb.k0Jjg:hover .kHtcsd{background-color:#a8c7fa15}.brKmxb:focus-visible .NtaMpb .kHtcsd,.NtaMpb .k0Jjg:focus-visible .kHtcsd,.NtaMpb.k0Jjg:focus-visible .kHtcsd{background-color:#a8c7fa15}.brKmxb:active .NtaMpb .kHtcsd,.NtaMpb .k0Jjg:active .kHtcsd,.NtaMpb.k0Jjg:active .kHtcsd{background-color:#a8c7fa39}.Bmegof{color:#eef0ff}.Bmegof .niO4u{background-color:var(--BRLwE)}.Bmegof .k0Jjg:hover .kHtcsd,.Bmegof.k0Jjg:hover .kHtcsd{background-color:#a8c7fa15}.brKmxb:focus-visible .Bmegof .kHtcsd,.Bmegof .k0Jjg:focus-visible .kHtcsd,.Bmegof.k0Jjg:focus-visible .kHtcsd{background-color:#a8c7fa15}.brKmxb:active .Bmegof .kHtcsd,.Bmegof .k0Jjg:active .kHtcsd,.Bmegof.k0Jjg:active .kHtcsd{background-color:#a8c7fa39}.r69FZ{color:#eef0ff}.r69FZ .niO4u{background-color:#2c303d}@media (prefers-contrast:more){.cD6kyf .niO4u{outline:1px solid #202124}}.r69FZ .k0Jjg:hover .kHtcsd,.r69FZ.k0Jjg:hover .kHtcsd{background-color:#a8c7fa15}.brKmxb:focus-visible .r69FZ .kHtcsd,.r69FZ .k0Jjg:focus-visible .kHtcsd,.r69FZ.k0Jjg:focus-visible .kHtcsd{background-color:#a8c7fa15}.brKmxb:active .r69FZ .kHtcsd,.r69FZ .k0Jjg:active .kHtcsd,.r69FZ.k0Jjg:active .kHtcsd{background-color:#a8c7fa39}.rXoM2c{color:#9e9e9e}.rXoM2c .niO4u{background-color:var(--XKMDxc)}.rXoM2c:not([disabled]) .k0Jjg:hover .kHtcsd,.rXoM2c.k0Jjg:not([disabled]):hover .kHtcsd{background-color:rgba(232,232,232,0.08);color:var(--YLNNHc)}.brKmxb:focus-visible .rXoM2c:not([disabled]) .kHtcsd,.rXoM2c:not([disabled]) .k0Jjg:focus-visible .kHtcsd,.rXoM2c.k0Jjg:not([disabled]):focus-visible .kHtcsd{background-color:rgba(232,232,232,0.08)}.brKmxb:active .rXoM2c:not([disabled]) .kHtcsd,.rXoM2c:not([disabled]) .k0Jjg:active .kHtcsd,.rXoM2c.k0Jjg:active:not([disabled]) .kHtcsd{background-color:rgba(232,232,232,0.24);color:var(--YLNNHc)}.jdOrZc{color:var(--rrJJUc)}.jdOrZc .niO4u{background-color:transparent}.zqrO0 .k0Jjg:hover .kHtcsd,.zqrO0.k0Jjg:hover .niO4u{color:var(--jINu6c)}.jdOrZc .k0Jjg:hover .kHtcsd,.jdOrZc.k0Jjg:hover .kHtcsd{background-color:#a8c7fa15}.brKmxb:active .jdOrZc .niO4u,.brKmxb:focus-visible .jdOrZc .niO4u,.zqrO0 .k0Jjg:active .niO4u,.zqrO0 .k0Jjg:focus-visible .niO4u,.zqrO0.k0Jjg:active .niO4u,.zqrO0.k0Jjg:focus-visible .niO4u{color:var(--jINu6c)}.brKmxb:focus-visible .jdOrZc .kHtcsd,.jdOrZc .k0Jjg:focus-visible .kHtcsd,.jdOrZc.k0Jjg:focus-visible .kHtcsd{background-color:#a8c7fa15}.brKmxb:active .jdOrZc .kHtcsd,.jdOrZc .k0Jjg:active .kHtcsd,.jdOrZc.k0Jjg:active .kHtcsd{background-color:#a8c7fa39}.ogBxF{color:var(--JKqx2)}.ogBxF .niO4u{background-color:transparent;outline:1px solid var(--mXZkqc);outline-offset:-1px}.ogBxF .k0Jjg:hover .kHtcsd,.ogBxF.k0Jjg:hover .kHtcsd{background-color:#a8c7fa15}.brKmxb:focus-visible .ogBxF .kHtcsd,.ogBxF .k0Jjg:focus-visible .kHtcsd,.ogBxF.k0Jjg:focus-visible .kHtcsd{background-color:#a8c7fa15}.brKmxb:active .ogBxF .kHtcsd,.ogBxF .k0Jjg:active .kHtcsd,.ogBxF.k0Jjg:active .kHtcsd{background-color:#a8c7fa39}.x2GJWb,.x2GJWb .QuU3Wb{color:#e8e8e8}.x2GJWb .niO4u{background-color:#28292a}.x2GJWb .k0Jjg:focus-visible .niO4u,.x2GJWb.k0Jjg:focus-visible .niO4u,.brKmxb:focus-visible .x2GJWb .niO4u{outline:2px solid var(--rrJJUc);outline-offset:2px}.x2GJWb:active .kHtcsd{background-color:rgba(189,193,198,0.24);}.x2GJWb .k0Jjg:hover .kHtcsd,.x2GJWb.k0Jjg:hover .kHtcsd{background-color:#a8c7fa15}.brKmxb:focus-visible .x2GJWb .kHtcsd,.x2GJWb .k0Jjg:focus-visible .kHtcsd,.x2GJWb.k0Jjg:focus-visible .kHtcsd{background-color:#a8c7fa15}.brKmxb:active .x2GJWb .kHtcsd,.x2GJWb .k0Jjg:active .kHtcsd,.x2GJWb.k0Jjg:active .kHtcsd{background-color:#a8c7fa39}.fXVarf{color:#fff}.fXVarf .niO4u{background-color:rgba(0,0,0,0.6);backdrop-filter:blur(8px)}.fXVarf .k0Jjg:hover .kHtcsd,.fXVarf.k0Jjg:hover .kHtcsd{background-color:#20212433}.brKmxb:focus-visible .fXVarf .kHtcsd,.fXVarf .k0Jjg:focus-visible .kHtcsd,.fXVarf.k0Jjg:focus-visible .kHtcsd{background-color:#20212433}.brKmxb:active .fXVarf .kHtcsd,.fXVarf .k0Jjg:active .kHtcsd,.fXVarf.k0Jjg:active .kHtcsd{background-color:#20212466}.qVhvac[selected]{color:var(--xPpiM)}.qVhvac[selected] .niO4u{background-color:var(--KIZPne)}.qVhvac[selected] .k0Jjg:hover .kHtcsd,.qVhvac.k0Jjg[selected]:hover .kHtcsd{background-color:rgba(255,255,255,0.08)}.W68A0[selected] .niO4u{outline:none}.r69FZ .d3o3Ad{color:#b1c5ff}.r69FZ:not([disabled]) .k0Jjg:hover .d3o3Ad,.r69FZ.k0Jjg:not([disabled]):hover .d3o3Ad{color:#eef0ff}.brKmxb:focus-visible .r69FZ:not([disabled]) .d3o3Ad,.brKmxb:active .r69FZ:not([disabled]) .d3o3Ad,.r69FZ:not([disabled]) .k0Jjg:focus-visible .d3o3Ad,.r69FZ:not([disabled]) .k0Jjg:active .d3o3Ad,.r69FZ.k0Jjg:not([disabled]):focus-visible .d3o3Ad,.r69FZ.k0Jjg:not([disabled]):active .d3o3Ad{color:#eef0ff}.Bmegof .d3o3Ad{color:#b1c5ff}.Bmegof:not([disabled]) .k0Jjg:hover .d3o3Ad,.Bmegof.k0Jjg:not([disabled]):hover .d3o3Ad{color:#eef0ff}.brKmxb:focus-visible .Bmegof:not([disabled]) .d3o3Ad,.brKmxb:active .Bmegof:not([disabled]) .d3o3Ad,.Bmegof:not([disabled]) .k0Jjg:focus-visible .d3o3Ad,.Bmegof:not([disabled]) .k0Jjg:active .d3o3Ad,.Bmegof.k0Jjg:not([disabled]):focus-visible .d3o3Ad,.Bmegof.k0Jjg:not([disabled]):active .d3o3Ad{color:#eef0ff}.sLl7de[selected] .niO4u{outline-color:transparent}.d2D5h[selected]{color:var(--rrJJUc)}.d2D5h[selected] .niO4u{background-color:var(--xhUGwc)}.d2D5h[selected] .k0Jjg:hover,.d2D5h.k0Jjg[selected]:hover{color:var(--rrJJUc)}.d2D5h[selected] .k0Jjg:hover .kHtcsd,.d2D5h.k0Jjg[selected]:hover .kHtcsd{background-color:rgba(138,180,248,.24)}.brKmxb:focus-visible .d2D5h[selected],.d2D5h.k0Jjg[selected]:focus-visible,.d2D5h[selected] .k0Jjg:focus-visible{color:var(--rrJJUc)}.fCrZyc[disabled]{color:#bfbfbf61}.fCrZyc.fCrZyc.fCrZyc.fCrZyc.fCrZyc.fCrZyc[disabled] .kHtcsd{background:transparent}.fCrZyc.fCrZyc.fCrZyc.fCrZyc.fCrZyc.fCrZyc[disabled] .niO4u{background:var(--YaIeMb);outline-color:#44474661;cursor:auto;color:#bfbfbf61}@media (forced-colors:active){.fCrZyc[disabled] .niO4u{outline-color:GrayText;cursor:auto}.fCrZyc[disabled]{color:GrayText}}.btku5b:not([selected]) .R04TOd,.btku5b[selected] .BGVjpf{display:none}.btku5b[selected] .k0Jjg:hover .kHtcsd,.btku5b.k0Jjg[selected]:hover .kHtcsd{background-color:#a8c7fa15}.clOx1e.aqBay{margin-right:4px}.WoA9Zd.WoA9Zd.WoA9Zd{margin-right:8px;margin-left:0}.btku5b:not([selected]) .RwPSab{display:none}.btku5b[selected] .rDI98b{display:none}.sjVJQd{font-family:Google Sans,Arial,sans-serif;font-size:14px;font-weight:400;line-height:20px}.btku5b.Tw6Cme{display:-moz-inline-box;display:inline-flex;-moz-box-align:center;align-items:center}.btku5b.Tw6Cme[selected] .iY6rMb .kHtcsd,.btku5b.Tw6Cme[selected] .iY6rMb .niO4u{border-top-left-radius:0;border-bottom-left-radius:0}.btku5b.Tw6Cme[selected] .EDvPNc .kHtcsd,.btku5b.Tw6Cme[selected] .EDvPNc .niO4u{border-top-right-radius:0;border-bottom-right-radius:0}.btku5b:not([selected]) .EDvPNc{display:none}.btku5b.Tw6Cme[selected] .niO4u{outline:none}.zJUuqf{margin-bottom:4px}.AB4Wff{margin-left:16px}.wHYlTd{font-family:Arial,sans-serif;font-size:14px;line-height:22px}.yUTMj{font-family:Arial,sans-serif;font-weight:400}@keyframes g-snackbar-show{from{pointer-events:none;transform:translateY(0)}to{transform:translateY(-100%)}}@keyframes g-snackbar-hide{from{transform:translateY(-100%)}to{transform:translateY(0)}}@keyframes g-snackbar-show-content{from{opacity:0}}@keyframes g-snackbar-hide-content{to{opacity:0}}.LH3wG,.jhZvod{bottom:0;height:0;position:fixed;z-index:999}.Ox8Cyd{height:0;position:fixed;z-index:999}.E7Hdgb{-moz-box-sizing:border-box;box-sizing:border-box;visibility:hidden;display:inline-block}.yK6jqe,.Wu0v9b{-moz-box-sizing:border-box;box-sizing:border-box;visibility:hidden}.rTYTNb{animation:g-snackbar-hide .4s cubic-bezier(0.4,0,0.2,1) both;visibility:inherit}.UewPMd{animation:g-snackbar-show .5s cubic-bezier(0.4,0,0.2,1) both;visibility:inherit}.b77HKf{background-color:#3c4043;padding:0 24px}.rIxsve{-moz-box-align:center;align-items:center;display:box;display:-moz-box;display:flex}.rTYTNb .rIxsve{animation:g-snackbar-hide-content .35s cubic-bezier(0.4,0,0.2,1) both}.UewPMd .rIxsve{animation:g-snackbar-show-content .35s cubic-bezier(0.4,0,0.2,1) .15s both}.Txngnb.Txngnb{line-height:20px}.Txngnb{color:#fff;-moz-box-flex:1;flex:1 1 auto;margin:14px 0;word-break:break-word}.sHFNYd{margin-right:-8px}@media (min-width:569px) and (min-height:569px){.LH3wG,.jhZvod{text-align:center}.Wu0v9b,.yK6jqe{display:inline-block;max-width:568px;min-width:288px;text-align:left}.b77HKf{border-radius:8px}.sHFNYd{margin-left:40px}}.V9O1Yd .rIxsve{display:block;padding:8px 0}.V9O1Yd .sHFNYd{margin-left:0}.V9O1Yd .sHFNYd g-flat-button{padding-left:0}.jhZvod{left:16px;right:auto}.LH3wG,.Ox8Cyd{left:0;right:0}.yK6jqe,.Wu0v9b,.E7Hdgb{position:relative}.G9jore{position:absolute;top:-24px;bottom:-24px;left:-24px;right:-24px}.zUdppc{padding-bottom:4px}.TkZZsf{padding-bottom:4px;padding-top:4px}.MdFDgb.HUQNIe{color:rgba(255,255,255,.26)}.czUY2e{background-color:var(--Pa8Wlb)}g-img{display:block}g-img{height:100%}.YQ4gaf{display:block;border:0}.u9wH7d .YQ4gaf{object-fit:fill}.mNsIhb .YQ4gaf{object-fit:cover}.tb08Pd .YQ4gaf{object-fit:contain}.wA1Bge{position:relative}.hhtjrc{-moz-box-flex:0;flex:none}.ZGomKf{overflow:hidden}.LLO8yd{background-color:rgba(0,0,0,.03);position:absolute;top:0;bottom:0;pointer-events:none;left:0;right:0}.LqkKtf{-moz-box-align:center;align-items:center;display:none;height:100%;-moz-box-pack:center;justify-content:center;left:0;opacity:.999;overflow:hidden;position:absolute;top:0;width:100%}.Iz740d.LqkKtf{border-radius:50%}.LqkKtf.DngrPc{left:50%;margin-left:-50vw;margin-right:-50vw;right:50%;width:100vw}.amp_re{position:relative}.QyJI3d{background-color:var(--xhUGwc);color:#dadce0;box-shadow:1px 1px 15px 0px #171717}.oQcPt{background-color:var(--xhUGwc)}.QyJI3d{border:1px solid rgba(0,0,0,.2);position:absolute;z-index:9120}.nFdFHf{animation:g-bubble-show .2s forwards}.bE3Kif{animation:g-bubble-hide .2s forwards}@keyframes g-bubble-show{from{opacity:0}to{opacity:1}}@keyframes g-bubble-hide{from{opacity:1}to{opacity:0}}.QyJI3d.QJnoze{border-radius:12px}.QyJI3d.SiOjJb{border-left-width:0;border-right-width:0;width:100%}.QyJI3d.PnQMie{background-color:#202124;border:1px solid rgba(0,0,0,0.5);color:#dadce0}.QyJI3d.LWen5c{background-color:#1a73e8;border:none;color:#fff;z-index:9100}.tYmfxe{transform:translate(2.5px,1.8px) rotateZ(45deg);position:absolute;z-index:9121}[dir=rtl] .tYmfxe{transform:translate(-2.5px,1.8px) rotateZ(45deg)}.IBPZu.tYmfxe{transform:translate(2.5px,-5.7px) rotateZ(45deg)}[dir=rtl] .IBPZu.tYmfxe{transform:translate(-2.5px,-5.7px) rotateZ(45deg)}.oQcPt{border-bottom:none;border-left:1px solid rgba(0,0,0,.2);border-right:none;border-top:1px solid rgba(0,0,0,.2);-moz-box-sizing:border-box;box-sizing:border-box;height:13.435px;width:13.435px}.IBPZu .oQcPt{border-bottom:1px solid rgba(0,0,0,.2);border-left:none;border-right:1px solid rgba(0,0,0,.2);border-top:none}.PnQMie .oQcPt{background-color:#202124;border-color:rgba(0,0,0,0.5)}.LWen5c .oQcPt{background-color:#1a73e8;border:none}.nnFGuf{display:none}.c5aZPb{cursor:pointer}.Dcltre{pointer-events:none}.TYQ8Af{clip:rect(1px,1px,1px,1px);height:1px;overflow:hidden;position:absolute;white-space:nowrap;width:1px;z-index:-1000;top:3px;right:3px}.JgzqYd{font-family:Google Sans,Arial,sans-serif;font-size:28px;line-height:36px}.Pqkn2e{font-family:Arial,sans-serif;font-size:16px;line-height:24px}.ZwRhJd{font-family:Arial,sans-serif;font-size:14px;line-height:18px}.MBeuO{font-family:Arial,sans-serif;font-size:20px;font-weight:400}.MBeuO{line-height:24px}.RES9jf{color:var(--YLNNHc)}.ZYHQ7e{color:var(--IXoxUe)}.GS5rRd{color:var(--JKqx2)}.GS5rRd:visited{color:#c58af9}.iwY1Mb{height:0;opacity:0;display:block}.V8fWH{border:0;clip:rect(0 0 0 0);clip-path:polygon(0 0,0 0,0 0);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px;white-space:nowrap;-moz-appearance:none;appearance:none;z-index:-1000;-moz-user-select:none;user-select:none}.gW7zSc{display:block}.CqmPRe:active .aVlTpc span{animation-timing-function:cubic-bezier(.2,.2,0,1);animation-duration:.5s}@keyframes shape-tween-right{50%{transform:scaleY(.9) translateX(8%)}100%{transform:none}}@keyframes shape-tween-left{50%{transform:scaleY(.9) translateX(-8%)}100%{transform:none}}.CqmPRe:active .KArJuc span{animation-name:shape-tween-right}.CqmPRe:active .YbCrzd span{animation-name:shape-tween-left}@keyframes shape-tween-up{50%{transform:scaleX(.9) translateY(-8%)}100%{transform:none}}.CqmPRe:active .oXqZxc span{animation-name:shape-tween-up}@keyframes shape-tween-down{50%{transform:scaleX(.9) translateY(8%)}100%{transform:none}}.CqmPRe:active .TD5FQe span{animation-name:shape-tween-down}.Aajd3{padding-left:16px}.U09Jxd{padding-right:4px}.gxMdVd{padding-right:8px}.mWcf0e{cursor:pointer}@media (forced-colors:active){.CNbPnc{background-color:ButtonFace;border:1px solid transparent;border-color:ButtonBorder;color:ButtonText}}.VfL2Y{position:relative;}.YQpX9d{cursor:pointer;position:relative;}.LRZwuc{display:inline-block}.nKHyTc{color:var(--IXoxUe);float:right;font-size:12px;line-height:16px;padding-bottom:4px}.eDGqNd{color:var(--IXoxUe);float:right;font-size:12px;line-height:16px}.k62gjb:hover,.k62gjb .W7GCoc:hover{text-decoration:underline}.r2fjmd{margin-bottom:0px;margin-top:0px}g-dropdown-menu{display:inline-block;position:relative}.Jb0Zif g-dropdown-menu{vertical-align:middle}.WNN1b{background-color:var(--xhUGwc)}.W4XqN{cursor:pointer;background-color:var(--xhUGwc)}.GKXWV{border-top:1px solid #5f6368;height:0;margin-left:5px;margin-right:5px}.eNRwyf{height:100%;width:100%}.adGN6[disabled]{pointer-events:none;cursor:default;background-color:var(--YaIeMb)}.pkWBse{box-shadow:1px 1px 15px 0px #171717}.pkWBse{border-radius:8px}.UjBGL{display:block}.CcNe6e{cursor:pointer;display:inline-block}.iRQHZe{position:absolute}.Qaqu5{position:relative}.shnMoc.CcNe6e{display:block}.v4Zpbe.CcNe6e{display:-moz-box;display:flex;height:100%;width:100%}.PBn44e{border-radius:8px}.yKUyj .EpPYLd:first-of-type{border-top-left-radius:8px;border-top-right-radius:8px}.yKUyj .EpPYLd:last-of-type{border-bottom-left-radius:8px;border-bottom-right-radius:8px}.yTik0{border:none;display:block;outline:none}.wplJBd{white-space:nowrap}.iQXTJe{padding:5px 0}.Zt0a5e.LGiluc{border-top-color:var(--gS5jXb)}.Zt0a5e.LGiluc,.Zt0a5e.EpPYLd[disabled]{color:rgba(255,255,255,0.26)!important}.CjiZvb,.GZnQqe.EpPYLd:active{background-color:rgba(255,255,255,.1)}.EpPYLd{display:block;position:relative}.YpcDnf{padding:0 16px;vertical-align:middle}.YpcDnf.HG1dvd{padding:0}.HG1dvd>*{padding:0 16px}.WtV5nd .YpcDnf{padding-left:28px}.Zt0a5e .YpcDnf{line-height:48px}.GZnQqe .YpcDnf{line-height:23px}.EpPYLd:hover{cursor:pointer}.EpPYLd,.CB8nDe:hover{cursor:default}.LGiluc,.EpPYLd[disabled]{pointer-events:none;cursor:default}@media (forced-colors:active){.EpPYLd[disabled]{color:GrayText}}.LGiluc{border-top:1px solid;height:0;margin:5px 0}.Zt0a5e.CB8nDe{background:no-repeat left 8px center}.Zt0a5e.CB8nDe{background-image:url(https://ssl.gstatic.com/images/icons/material/system/1x/done_white_16dp.png)}@media (forced-colors:active){.Zt0a5e.CB8nDe{background-image:url(https://ssl.gstatic.com/images/icons/material/system/1x/done_white_16dp.png)}}.GZnQqe.CB8nDe{background:no-repeat left center}.GZnQqe.CB8nDe{background-image:url(https://ssl.gstatic.com/ui/v1/menu/checkmark2-light.png)}@media (forced-colors:active){.GZnQqe.CB8nDe{background-image:url(https://ssl.gstatic.com/ui/v1/menu/checkmark2-light.png)}}.GZnQqe.LGiluc,.GZnQqe.EpPYLd[disabled]{opacity:61%}.GZnQqe.LGiluc{border-top-color:var(--gS5jXb)}.eCcUA,.cDnxO{pointer-events:none;position:absolute;top:0;left:0;width:100%;height:100%;border-radius:50%}.rLdWfe{position:absolute;border-radius:inherit;top:0;left:0;width:100%;height:100%}.MDfoTd,.hdqIFe{pointer-events:none;position:absolute;top:0;left:0;width:100%;height:100%}.MDfoTd,.cDnxO{opacity:0}.hdqIFe,.cDnxO{overflow:hidden}.bKvogb .MDfoTd,.bKvogb .hdqIFe{border-radius:50%}.bKvogb .eCcUA{overflow:hidden}.LwVyHd{transition:max-height .3s;overflow:hidden}.Yh3tf{pointer-events:none;position:absolute!important;right:0;color:#9e9e9e;top:50%;margin-top:-12px;display:-moz-box;display:flex;-moz-box-align:center;align-items:center}.FXMOpb .Yh3tf{margin-top:-14px}.FXMOpb .MLgx0e{transform:rotateX(180deg)}.ydSoC{padding-left:8px}.kGfs{position:relative}.Yh3tf.chvYFc{background:var(--XKMDxc);border-radius:25px;margin-top:-18px;padding:6px}.SpKsUc{margin:0 -10px;padding:0 10px}.V1sL5c{overflow:visible}.S8PBwe{max-height:0;display:none}.hObAcc{margin-left:4px;margin-right:4px}.gTewb{padding-left:8px;padding-right:8px}.thsZXc{background:#8ab4f8}.hiQRQc{background:#303134}.Bb1JKe{padding-bottom:8px}.ouy7Mc{padding-left:16px;padding-right:16px}.M8CEed{padding-top:12px}.z1asCe{display:inline-block;fill:currentColor;height:24px;line-height:24px;position:relative;width:24px}.z1asCe svg{display:block;height:100%;width:100%}</style><script async=\"\" type=\"text/javascript\" charset=\"UTF-8\" src=\"https://www.gstatic.com/og/_/js/k=og.qtm.en_US.JsvYdB1VlTQ.2019.O/rt=j/m=qabr,qgl,q_dnp,qcwid,qbd,qapid,qald,qads,qrcd,q_dg,qrbg/exm=qaaw,qadd,qaid,qein,qhaw,qhba,qhbr,qhch,qhga,qhid,qhin/d=1/ed=1/rs=AA2YrTt6VjuqvFHGTQ7vz8QgRv0QbbEJTQ\" nonce=\"CP311SD5BVmtqozsXvXMVQ\"></script><link type=\"text/css\" href=\"https://www.gstatic.com/og/_/ss/k=og.qtm.wW8O5Lw5T1g.L.F4.O/m=qcwid,d_b_gm3,d_wi_gm3,d_lo_gm3/excm=qaaw,qadd,qaid,qein,qhaw,qhba,qhbr,qhch,qhga,qhid,qhin/d=1/ed=1/ct=zgms/rs=AA2YrTuFwfpUCTC88n_H9FoSYz-cjRhDFQ\" rel=\"stylesheet\"><link href=\"data:text/css,%3Ais(div%3Ahas(%3E%20iframe%5Bsrc*%3D'prid%3D19026802'%5D)%2Cdiv%3Ahas(%3E%20iframe%5Bsrc*%3D'prid%3D19015398'%5D)%2Cdiv%3Ahas(%3E%20iframe%5Bsrc*%3D'prid%3D19026796'%5D)%2Cdiv%3Ahas(%3E%20iframe%5Bsrc*%3D'prid%3D19018053'%5D)%2Cdiv%3Ahas(%3E%20iframe%5Bsrc*%3D'prid%3D19018054'%5D)%2Cdiv%3Ahas(%3E%20iframe%5Bsrc*%3D'prid%3D19016403'%5D)%2Cdiv%3Ahas(%3E%20iframe%5Bsrc*%3D'prid%3D19015972'%5D)%2Cdiv%3Ahas(%3E%20iframe%5Bsrc*%3D'prid%3D19016223'%5D)%2Cdiv%3Ahas(%3E%20iframe%5Bsrc*%3D'prid%3D19015952'%5D)%2Cdiv%3Ahas(%3E%20iframe%5Bsrc*%3D'prid%3D19030391'%5D)%2Cdiv%3Ahas(%3E%20iframe%5Bsrc*%3D'prid%3D19030389'%5D)%2Cdiv%3Ahas(%3E%20iframe%5Bsrc*%3D'prid%3D19030167'%5D)%2Cdiv%3Ahas(%3E%20iframe%5Bsrc*%3D'prid%3D19031496'%5D)%2Cdiv%3Ahas(%3E%20iframe%5Bsrc*%3D'prid%3D19043282'%5D)%2Cdiv%3Ahas(%3E%20iframe%5Bsrc*%3D'prid%3D19043280'%5D)%2Cdiv%3Ahas(%3E%20iframe%5Bsrc*%3D'prid%3D19031780'%5D)%2C%5Baria-labelledby%3D'promo-header'%5D%2C%5Bid*%3D'google_ads_iframe'%5D%2C%5Bid*%3D'taboola-'%5D%2C.taboolaHeight%2C.taboola-placeholder%2C%23credential_picker_container%2C%23credentials-picker-container%2C%23credential_picker_iframe%2C%5Bid*%3D'google-one-tap-iframe'%5D%2C%23google-one-tap-popup-container%2C.google-one-tap-modal-div%2C%23amp_floatingAdDiv%2C%23ez-content-blocker-container)%20%7Bdisplay%3Anone!important%3Bmin-height%3A0!important%3Bheight%3A0!important%3B%7D\" rel=\"stylesheet\" type=\"text/css\"><style data-late-css=\"\">c-wiz{contain:style}c-wiz>c-data{display:none}c-wiz.rETSD{contain:none}c-wiz.Ubi8Z{contain:layout style}.jbBItf{display:block;position:relative}.DU0NJ{bottom:0;left:0;position:absolute;right:0;top:0}.lP3Jof{display:inline-block;position:relative}.nNMuOd{animation:qli-container-rotate 1568.2352941176ms linear infinite}@keyframes qli-container-rotate{from{transform:rotate(0)}to{transform:rotate(1turn)}}.RoKmhb{height:100%;opacity:0;position:absolute;width:100%}.nNMuOd .VQdeab{animation:qli-fill-unfill-rotate 5332ms cubic-bezier(0.4,0,0.2,1) infinite both,qli-blue-fade-in-out 5332ms cubic-bezier(0.4,0,0.2,1) infinite both}.nNMuOd .IEqiAf{animation:qli-fill-unfill-rotate 5332ms cubic-bezier(0.4,0,0.2,1) infinite both,qli-red-fade-in-out 5332ms cubic-bezier(0.4,0,0.2,1) infinite both}.nNMuOd .smocse{animation:qli-fill-unfill-rotate 5332ms cubic-bezier(0.4,0,0.2,1) infinite both,qli-yellow-fade-in-out 5332ms cubic-bezier(0.4,0,0.2,1) infinite both}.nNMuOd .FlKbCe{animation:qli-fill-unfill-rotate 5332ms cubic-bezier(0.4,0,0.2,1) infinite both,qli-green-fade-in-out 5332ms cubic-bezier(0.4,0,0.2,1) infinite both}.BSnLb .nNMuOd .RoKmhb{animation:qli-fill-unfill-rotate 5332ms cubic-bezier(0.4,0,0.2,1) infinite both;opacity:0.99}@keyframes qli-fill-unfill-rotate{0%{transform:rotate(0)}12.5%{transform:rotate(135deg)}25%{transform:rotate(270deg)}37.5%{transform:rotate(405deg)}50%{transform:rotate(540deg)}62.5%{transform:rotate(675deg)}75%{transform:rotate(810deg)}87.5%{transform:rotate(945deg)}100%{transform:rotate(3turn)}}@keyframes qli-blue-fade-in-out{0%{opacity:0.99}25%{opacity:0.99}26%{opacity:0}89%{opacity:0}90%{opacity:0.99}100%{opacity:0.99}}@keyframes qli-red-fade-in-out{0%{opacity:0}15%{opacity:0}25%{opacity:0.99}50%{opacity:0.99}51%{opacity:0}}@keyframes qli-yellow-fade-in-out{0%{opacity:0}40%{opacity:0}50%{opacity:0.99}75%{opacity:0.99}76%{opacity:0}}@keyframes qli-green-fade-in-out{0%{opacity:0}65%{opacity:0}75%{opacity:0.99}90%{opacity:0.99}100%{opacity:0}}.beDQP{display:inline-block;height:100%;overflow:hidden;position:relative;width:50%}.FcXfi{-moz-box-sizing:border-box;box-sizing:border-box;height:100%;left:45%;overflow:hidden;position:absolute;top:0;width:10%}.SPKFmc{border-radius:50%;border:3px solid transparent;-moz-box-sizing:border-box;box-sizing:border-box}@media (forced-colors:active),(prefers-contrast:more){.beDQP:last-child .SPKFmc{border:none}}.x3SdXd{width:200%}.J7uuUe{transform:rotate(129deg)}.sDPIC{left:-100%;transform:rotate(-129deg)}.tS3P5{left:-450%;width:1000%}.VQdeab .SPKFmc{border-color:#4487f6}.IEqiAf .SPKFmc{border-color:#ff7769}.smocse .SPKFmc{border-color:#824300}.FlKbCe .SPKFmc{border-color:#219540}.RoKmhb .J7uuUe{border-bottom-color:transparent;border-right-color:transparent}.RoKmhb .sDPIC{border-bottom-color:transparent;border-left-color:transparent}.RoKmhb .tS3P5{border-bottom-color:transparent}.GgTJWe .nNMuOd .J7uuUe{animation:qli-left-spin 1333ms cubic-bezier(0.4,0,0.2,1) infinite both}.GgTJWe .nNMuOd .sDPIC{animation:qli-right-spin 1333ms cubic-bezier(0.4,0,0.2,1) infinite both}.BSnLb .nNMuOd .J7uuUe{animation:qli-left-spin 1333ms cubic-bezier(0.4,0,0.2,1) infinite both;border-left-color:var(--EpFNW);border-top-color:var(--EpFNW)}.BSnLb .nNMuOd .sDPIC{animation:qli-right-spin 1333ms cubic-bezier(0.4,0,0.2,1) infinite both;border-right-color:var(--EpFNW);border-top-color:var(--EpFNW)}.BSnLb .nNMuOd .tS3P5{border-color:var(--EpFNW);border-bottom-color:transparent}@keyframes qli-left-spin{0%{transform:rotate(130deg)}50%{transform:rotate(-5deg)}100%{transform:rotate(130deg)}}@keyframes qli-right-spin{0%{transform:rotate(-130deg)}50%{transform:rotate(5deg)}100%{transform:rotate(-130deg)}}.ea0Lbe{background:#303134;border-radius:24px;box-shadow:0px 4px 6px rgba(32,33,36,0.28);margin-left:-4px;margin-top:0;position:absolute;top:-4px;width:calc(100% + 8px);z-index:989}.KoWHpd{margin:20px}.BiKNf{align-self:flex-end;cursor:pointer;display:-moz-box;display:flex;padding:14px;position:absolute;right:6px;top:6px}.p4pvTd{color:rgb(241,243,244);font-family:\"Google Sans Display\",Roboto,Arial,sans-serif;font-size:16px;line-height:28px;margin-bottom:14px;text-align:center;letter-spacing:.1px}.BH9rn{-moz-box-align:center;align-items:center;display:-moz-inline-box;display:inline-flex;-moz-box-orient:horizontal;-moz-box-direction:normal;flex-direction:row;-moz-box-flex:1;flex-grow:1;-moz-box-pack:initial;justify-content:normal;padding-top:16px}.gIYJUc{background:rgb(32,33,36);border:1px dashed rgb(60,64,67);border-radius:8px;-moz-box-sizing:border-box;box-sizing:border-box;display:-moz-box;display:flex;-moz-box-orient:vertical;-moz-box-direction:normal;flex-direction:column;-moz-box-flex:1;flex-grow:1;height:280px;position:relative;width:100%}.Ndj4R{border:1px dashed rgb(138,180,248)}.id5vMb{border:1px dashed rgb(138,180,248)}.f6GA0{height:100%;-moz-box-pack:center;justify-content:center;margin-top:0}.f6GA0,.CacfB{-moz-box-align:center;align-items:center;display:-moz-box;display:flex;-moz-box-orient:vertical;-moz-box-direction:normal;flex-direction:column}.f6GA0,.CacfB,.Ua7Yuf{border-radius:8px;-moz-box-flex:1;flex-grow:1}.CacfB{background:linear-gradient(0deg,rgba(138,180,248,0.24),rgba(138,180,248,0.24)),#202124;-moz-box-pack:center;justify-content:center;height:100%;width:100%}.ZeVBtc{color:#93969b;font-family:\"Google Sans\",Roboto,Arial,sans-serif;font-size:16px;line-height:25px;max-width:300px}.alTBQe{-moz-box-align:center;align-items:center;background-color:rgb(242,139,130);border-top-left-radius:8px;border-top-right-radius:8px;-moz-box-pack:justify;justify-content:space-between;left:0;position:absolute;right:0;top:0}.OHzWjb{color:rgb(32,33,36);-moz-box-flex:1;flex:1;font-family:\"Google Sans\",Roboto,Arial,sans-serif;font-size:12px;padding:5px;text-align:center}.Ua7Yuf{-moz-box-align:center;align-items:center;align-self:center;background:linear-gradient(0deg,rgba(138,180,248,0.24),rgba(138,180,248,0.24)),#202124;display:-moz-box;display:flex;-moz-box-flex:1;flex:1;-moz-box-orient:vertical;-moz-box-direction:normal;flex-direction:column;height:100%;-moz-box-pack:center;justify-content:center;width:100%}.wHH8af{color:rgb(138,180,248);font-family:\"Google Sans\",Roboto,Arial,sans-serif;font-size:16px;line-height:25px;margin-top:12px}.DV7the{color:rgb(138,180,248);cursor:pointer;white-space:nowrap}.DV7the.RiECff:focus{outline:none}.DV7the:hover,.DV7the:hover{text-decoration:underline}.DV7the:focus{text-decoration:underline}.ArIAXb{fill:rgb(60,64,67)}.qOFLsb{fill:rgb(60,64,67)}.Aye1k{width:inherit;position:relative;display:block}.RaoUUe{display:-moz-inline-box;display:inline-flex;margin-right:18px}.e8Eule{-moz-box-sizing:border-box;box-sizing:border-box;display:-moz-box;display:flex;-moz-box-orient:vertical;-moz-box-direction:normal;flex-direction:column;padding:0 20px 20px;width:100%}.YJx25{-moz-box-align:center;align-items:center;display:-moz-box;display:flex}.diOlIe{border-top:1px solid rgb(60,64,67);-moz-box-flex:1;flex-grow:1;height:0}.aHK1bd{color:rgb(154,160,166);cursor:default;flex-shrink:0;font-family:\"Google Sans Display\",Roboto,Arial,sans-serif;font-size:14px;margin-left:20px;margin-right:20px}.PXT6cd{display:-moz-box;display:flex;margin-top:14px}.cB9M7{background-color:#303134;border:1px solid rgb(60,64,67);color:rgb(241,243,244);border-radius:36px;display:-moz-inline-box;display:inline-flex;-moz-box-flex:1;flex-grow:1;font-size:14px;font-family:\"Google Sans Display\",Roboto,Arial,sans-serif;height:40px;padding:0 24px;width:100%;outline:none}.lensUploadWizwebUploadDialogUrlInputInputBox ::-moz-placeholder{color:rgb(128,134,139)}.cB9M7 ::placeholder{color:rgb(128,134,139)}.cB9M7:hover{border:1px solid rgb(95,99,104)}.cB9M7:focus{border:1px solid rgb(138,180,248)}.cB9M7:active{border:1px solid rgb(138,180,248)}.Qwbd3{-moz-box-align:center;align-items:center;background:#303134;border-radius:32px;border:1px solid rgb(60,64,67);color:rgb(138,180,248);cursor:pointer;display:-moz-inline-box;display:inline-flex;flex-shrink:0;font-family:\"Google Sans\",Roboto,Arial,sans-serif;font-size:14px;-moz-box-pack:center;justify-content:center;letter-spacing:.25px;margin-left:8px;outline:0;padding:8px 24px}.Qwbd3:hover{background:rgba(136,170,187,0.04);color:rgb(210,227,252);border:1px solid rgb(60,64,67)}.Qwbd3:focus{background:rgba(138,180,248,0.12);border:1px solid rgb(210,227,252);color:rgb(210,227,252)}.Qwbd3:active{background:rgba(136,170,187,0.1);border:1px solid rgb(60,64,67);color:rgb(210,227,252)}sentinel{}</style><style data-late-css=\"\">.MTIaKb,.LwDUdc,.FAoEle,.RlTCPd,.wPNfjb,.caNvfd,.Vnob4b,.bbxTBb,.DpgmK,.YKUhfb,.uNnvb,.aVsZpf,.RoOVmf,.dIfvQd,.V3Ezn,.Enb9pe,.mYuoaf,.kJSB8,.tUr4Kc,.iQMtqe{--Yi4Nb:var(--mXZkqc);--pEa0Bc:var(--bbQxAb);--kloG3:var(--mXZkqc);--YaIeMb:var(--XKMDxc);--Pa8Wlb:var(--Nsm0ce);--izGsqb:var(--Nsm0ce);--todMNcl:var(--EpFNW);--p9J9c:var(--Nsm0ce)}:root{--KIZPne:#34517d;--xPpiM:#eef0ff;--Ehh4mf:var(--Nsm0ce)}:root{--Yi4Nb:#444746;--pEa0Bc:#bfbfbf;--kloG3:#444746;--YaIeMb:#28292a;--Pa8Wlb:#a8c7fa;--izGsqb:#a8c7fa;--todMNcl:#202124;--p9J9c:#a8c7fa}.EpPYLd{display:block;position:relative}.YpcDnf{padding:0 16px;vertical-align:middle}.YpcDnf.HG1dvd{padding:0}.HG1dvd>*{padding:0 16px}.WtV5nd .YpcDnf{padding-left:28px}.Zt0a5e .YpcDnf{line-height:48px}.GZnQqe .YpcDnf{line-height:23px}.EpPYLd:hover{cursor:pointer}.EpPYLd,.CB8nDe:hover{cursor:default}.LGiluc,.EpPYLd[disabled]{pointer-events:none;cursor:default}@media (forced-colors:active){.EpPYLd[disabled]{color:GrayText}}.LGiluc{border-top:1px solid;height:0;margin:5px 0}.Zt0a5e.CB8nDe{background:no-repeat left 8px center}.Zt0a5e.CB8nDe{background-image:url(https://ssl.gstatic.com/images/icons/material/system/1x/done_white_16dp.png)}@media (forced-colors:active){.Zt0a5e.CB8nDe{background-image:url(https://ssl.gstatic.com/images/icons/material/system/1x/done_white_16dp.png)}}.GZnQqe.CB8nDe{background:no-repeat left center}.GZnQqe.CB8nDe{background-image:url(https://ssl.gstatic.com/ui/v1/menu/checkmark2-light.png)}@media (forced-colors:active){.GZnQqe.CB8nDe{background-image:url(https://ssl.gstatic.com/ui/v1/menu/checkmark2-light.png)}}.GZnQqe.LGiluc,.GZnQqe.EpPYLd[disabled]{opacity:61%}.GZnQqe.LGiluc{border-top-color:var(--gS5jXb)}sentinel{}</style></head><body jsmodel=\"hspDDf \" data-dt=\"1\" jsaction=\"xjhTIf:.CLIENT;O2vyse:.CLIENT;IVKTfe:.CLIENT;Ez7VMc:.CLIENT;R6Slyc:.CLIENT;hWT9Jb:.CLIENT;WCulWe:.CLIENT;VM8bg:.CLIENT;qqf0n:.CLIENT;A8708b:.CLIENT;YcfJ:.CLIENT;szjOR:.CLIENT;JL9QDc:.CLIENT;kWlxhc:.CLIENT;ydZCDf:.CLIENT\"><style>.L3eUgb{display:flex;flex-direction:column;height:100%}.o3j99{flex-shrink:0;box-sizing:border-box}.n1xJcf{height:60px}.LLD4me{min-height:150px;height:calc(100% - 560px);max-height:290px}.yr19Zb{min-height:92px}.ikrT4e{max-height:160px}.mwht9d{display:none}.ADHj4e{padding-top:0px;padding-bottom:85px}.oWyZre{width:100%;height:500px;border-width:0}.qarstb{flex-grow:1}</style><div class=\"L3eUgb\" data-hveid=\"1\"><div class=\"o3j99 n1xJcf Ne6nSd\" role=\"navigation\"><style>.Ne6nSd{display:flex;align-items:center;padding:6px}.LX3sZb{display:inline-block;flex-grow:1}</style><div class=\"LX3sZb\"><div jscontroller=\"SJpD2c\" jsname=\"uZkjhb\" jsaction=\"rcuQ6b:npT2md;YQNWc:MMAZpe;zWO23b:FPeyVe\"><div><div class=\"gb_Ea gb_Hd gb_1d gb_F gb_Kc gb_e gb_1a gb_Vc\" id=\"gb\" style=\"background-color:rgba(32,33,36,1)\"><div class=\"gb_Ad gb_Xd gb_vd\" data-ogsr-up=\"\"><div><div class=\"gb_L gb_Z gb_Mf gb_Tf\" data-ogbl=\"\"><div class=\"gb_X gb_Z\"><a class=\"gb_W\" aria-label=\"Gmail \" data-pid=\"23\" href=\"https://mail.google.com/mail/&amp;ogbl\" target=\"_top\">Gmail</a></div><div class=\"gb_X gb_Z\"><a class=\"gb_W\" aria-label=\"Search for Images \" data-pid=\"2\" href=\"https://www.google.com/imghp?hl=en&amp;ogbl\" target=\"_top\">Images</a></div></div></div><div class=\"gb_Qe\"><div class=\"gb_2c\"><div class=\"gb_I gb_ad gb_Z\" data-ogsr-fb=\"true\" data-ogsr-alt=\"\" id=\"gbwa\"><div class=\"gb_C\"><a class=\"gb_A\" aria-label=\"Google apps\" href=\"https://www.google.com.bo/intl/en/about/products\" aria-expanded=\"false\" role=\"button\" tabindex=\"0\"><svg class=\"gb_E\" focusable=\"false\" viewBox=\"0 0 24 24\"><path d=\"M6,8c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM12,20c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM6,20c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM6,14c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM12,14c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM16,6c0,1.1 0.9,2 2,2s2,-0.9 2,-2 -0.9,-2 -2,-2 -2,0.9 -2,2zM12,8c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM18,14c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM18,20c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2z\"></path><image src=\"https://ssl.gstatic.com/gb/images/bar/al-icon.png\" alt=\"\" height=\"24\" width=\"24\" style=\"border:none;display:none \\9\"></image></svg></a></div></div></div><div class=\"gb_y gb_ad gb_Mf gb_Z\"><div class=\"gb_C gb_ib gb_Mf gb_Z\"><a class=\"gb_A gb_Xa gb_Z\" aria-label=\"Google Account: Diego Balderrama  \n(<EMAIL>)\" href=\"https://accounts.google.com/SignOutOptions?hl=en&amp;continue=https://www.google.com/&amp;ec=GBRAmgQ\" tabindex=\"0\" role=\"button\"><img class=\"gb_O gbii\" src=\"https://lh3.google.com/u/0/ogw/AF2bZyhWCpTTH-rEi87cmz_xrMz5Se2l5drt0aJ9E_BmcxI8bw=s32-c-mo\" srcset=\"https://lh3.google.com/u/0/ogw/AF2bZyhWCpTTH-rEi87cmz_xrMz5Se2l5drt0aJ9E_BmcxI8bw=s32-c-mo 1x, https://lh3.google.com/u/0/ogw/AF2bZyhWCpTTH-rEi87cmz_xrMz5Se2l5drt0aJ9E_BmcxI8bw=s64-c-mo 2x \" alt=\"\" aria-hidden=\"true\" data-noaft=\"\" data-csiid=\"sNsXZ8SAEsff5OUPju6tyAE_1\" data-atf=\"1\"></a></div></div></div><div style=\"overflow: hidden; position: absolute; top: 0px; visibility: hidden; width: 370px; z-index: 991; height: 0px; margin-top: 49px; transition: height 0.3s ease-in-out; right: 0px; margin-right: 4px;\" aria-hidden=\"true\"><iframe role=\"presentation\" style=\"height: 100%; width: 100%; color-scheme: light; visibility: hidden;\" frameborder=\"0\" scrolling=\"no\" name=\"app\" src=\"https://ogs.google.com/u/0/widget/app?eom=1&amp;awwd=1&amp;alb=1&amp;origin=https%3A%2F%2Fwww.google.com&amp;cn=app&amp;pid=1&amp;spid=538&amp;hl=en&amp;dm=\"></iframe></div><div style=\"overflow: hidden; position: absolute; top: 0px; visibility: hidden; width: 436px; z-index: 991; height: 0px; margin-top: 49px; right: 0px; margin-right: 4px;\"></div><div style=\"overflow: hidden; position: absolute; top: 0px; visibility: hidden; width: 420px; z-index: 991; height: 280px; margin-top: 70px; right: 0px; margin-right: 25px;\"></div></div></div></div><div jsname=\"hJoEPd\" style=\"display:none\" data-ved=\"0ahUKEwjEmLvfvKKJAxXHL7kGHQ53CxkQxMIJCAI\"></div><div jsname=\"micRse\" style=\"display:none\" data-ved=\"0ahUKEwjEmLvfvKKJAxXHL7kGHQ53CxkQ7JsICAM\"></div><div jsname=\"t5CbBf\" style=\"display:none\" data-ved=\"0ahUKEwjEmLvfvKKJAxXHL7kGHQ53CxkQraQMCAQ\"></div><div jsname=\"Du1Fsb\" style=\"display:none\" data-ved=\"0ahUKEwjEmLvfvKKJAxXHL7kGHQ53CxkQzegNCAU\"></div><div jsname=\"NG1V7d\" style=\"display:none\" data-ved=\"0ahUKEwjEmLvfvKKJAxXHL7kGHQ53CxkQ8JsICAY\"></div><div jsname=\"IZKNDc\" style=\"display:none\" data-ved=\"0ahUKEwjEmLvfvKKJAxXHL7kGHQ53CxkQ8ZsICAc\"></div><div jsname=\"tP7ayb\" style=\"display:none\" data-ved=\"0ahUKEwjEmLvfvKKJAxXHL7kGHQ53CxkQ65sICAg\"></div><div jsname=\"uO6mde\" style=\"display:none\" data-ved=\"0ahUKEwjEmLvfvKKJAxXHL7kGHQ53CxkQ8psICAk\"></div><g-snackbar jsname=\"PWj1Zb\" jscontroller=\"OZLguc\" style=\"display:none\" jsshadow=\"\" jsaction=\"rcuQ6b:npT2md\" id=\"ow12\" __is_owner=\"true\"><div jsname=\"Ng57nc\" class=\"yK6jqe\" data-ved=\"0ahUKEwjEmLvfvKKJAxXHL7kGHQ53CxkQ4G8ICg\" jsowner=\"ow12\"><div class=\"b77HKf\"><div class=\"rIxsve\" jsslot=\"\"><span class=\"Txngnb wHYlTd yUTMj\">Something went wrong. Your history wasn't deleted.</span></div></div></div><div jsname=\"sM5MNb\" aria-live=\"polite\" class=\"LH3wG\"></div></g-snackbar></div></div></div><div class=\"o3j99 LLD4me yr19Zb LS8OJ\"><style>.LS8OJ{display:flex;flex-direction:column;align-items:center}.k1zIA{height:100%;margin-top:auto}</style><div class=\"k1zIA rSk4se\"><style>.rSk4se{max-height:92px;position:relative}.lnXdpd{max-height:100%;max-width:100%;object-fit:contain;object-position:center bottom;width:auto}</style><img class=\"lnXdpd\" alt=\"Google\" height=\"92\" src=\"/images/branding/googlelogo/2x/googlelogo_light_color_272x92dp.png\" srcset=\"/images/branding/googlelogo/1x/googlelogo_light_color_272x92dp.png 1x, /images/branding/googlelogo/2x/googlelogo_light_color_272x92dp.png 2x\" width=\"272\" data-csiid=\"sNsXZ8SAEsff5OUPju6tyAE_2\" data-atf=\"1\"></div></div><div class=\"o3j99 ikrT4e om7nvf\"><style>.om7nvf{padding:20px}</style><dialog class=\"spch-dlg\" id=\"spch-dlg\"><div class=\"spch\" style=\"display:none\" id=\"spch\"><style>.spch-dlg{background:transparent;border:none}.spch{background:var(--xhUGwc);height:100%;left:0;opacity:0;overflow:hidden;position:fixed;text-align:left;top:0;visibility:hidden;width:100%;z-index:10000;transition:visibility 0s linear 0.218s,background-color 0.218s;}.s2fp.spch{opacity:1;transition-delay:0s;visibility:visible;}.pz5bj{background:none;border:none;color:var(--IXoxUe);cursor:pointer;font-size:26px;right:0;line-height:15px;opacity:.6;margin:-1px -1px 0 0;padding:0 0 2px 0;height:48px;width:48px;position:absolute;top:0;z-index:10}.pz5bj:hover{opacity:.8}.pz5bj:active{opacity:1}.spchc{display:block;height:42px;pointer-events:none;margin:auto;position:relative;top:0;margin-top:312px;max-width:572px;min-width:534px;padding:0 223px}.inner-container{height:100%;opacity:.1;pointer-events:none;width:100%;transition:opacity .318s ease-in;}.s2ml .inner-container,.s2ra .inner-container,.s2er .inner-container,.OJaju .inner-container{opacity:1;transition:opacity 0s}</style><style>.google-logo{background:url(data:image/png;base64,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) no-repeat center;background-size:94px 32px;height:32px;width:94px;top:8px;opacity:0;float:right;left:255px;pointer-events:none;position:relative;transition:opacity .5s ease-in,left .5s ease-in}</style><button class=\"pz5bj\" id=\"spchx\" aria-label=\"close\"><span style=\"height:16px;line-height:16px;width:16px\" class=\"z1asCe wuXmqc\"><svg focusable=\"false\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\"><path d=\"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"></path></svg></span></button><div class=\"spchc\" id=\"spchc\"><div class=\"inner-container\"><div class=\"button-container\"><style>.LgbsSe{background-color:#fff;border:1px solid #f8f9fa;border-radius:100%;bottom:0;box-shadow:0 2px 5px rgba(0,0,0,.1);cursor:pointer;display:inline-block;opacity:0;pointer-events:none;position:absolute;right:0;transition:background-color 0.218s,border 0.218s,box-shadow 0.218s;transition-delay:0;position:absolute;opacity:0;left:0;top:0}.s2fp .LgbsSe{opacity:1;pointer-events:auto;transform:scale(1);}.s2ra .LgbsSe{background-color:#ea4335;border:0;box-shadow:none}.r8s4j{background-color:#dadce0;border-radius:100%;display:inline-block;opacity:1;pointer-events:none;position:absolute;transform:scale(.01);transition:opacity 0.218s;height:301px;left:-69px;top:-69px;width:301px;}.button-container{pointer-events:none;position:relative;transition:transform 0.218s,opacity 0.218s ease-in;transform:scale(.1);height:165px;width:165px;right:-70px;top:-70px;float:right;}.s2fp .button-container{transform:scale(1)}.s2ra .LgbsSe:active{background-color:#c5221f}.LgbsSe:active{background-color:#f8f9fa}</style><span class=\"r8s4j\" id=\"spchl\"></span><span class=\"LgbsSe\" id=\"spchb\"><div class=\"microphone\"><style>.microphone{height:87px;pointer-events:none;position:absolute;width:42px;top:47px;transform:scale(1);left:43px;}.receiver{background-color:#999;border-radius:30px;height:46px;left:25px;pointer-events:none;position:absolute;width:24px}.wrapper{bottom:0;height:53px;left:11px;overflow:hidden;pointer-events:none;position:absolute;width:52px}.stem{background-color:#999;bottom:14px;height:14px;left:22px;pointer-events:none;position:absolute;width:9px;z-index:1}.shell{border:7px solid #999;border-radius:28px;bottom:27px;height:57px;pointer-events:none;position:absolute;width:38px;z-index:0;left:0px}.s2ml .receiver,.s2ml .stem{background-color:#f44}.s2ml .shell{border-color:#f44}.s2ra .receiver,.s2ra .stem{background-color:#fff}.s2ra .shell{border-color:#fff}</style><span class=\"receiver\"></span><div class=\"wrapper\"><span class=\"stem\"></span><span class=\"shell\"></span></div></div></span></div><div class=\"text-container\"><style>.text-container{pointer-events:none;position:absolute;}.spcht{font-weight:normal;line-height:1.2;opacity:0;pointer-events:none;position:absolute;text-align:left;font-smoothing:antialiased;transition:opacity .1s ease-in,margin-left .5s ease-in,top 0s linear 0.218s;left:-44px;top:-.2em;margin-left:44px;font-size:32px;width:460px;}.s2fp .spcht{margin-left:0;opacity:1;transition:opacity .5s ease-out,margin-left .5s ease-out}.spchta{color:var(--JKqx2);cursor:pointer;font-size:18px;font-weight:500;pointer-events:auto;text-decoration:underline}.spch-2l.spcht,.spch-3l.spcht,.spch-4l.spcht{transition:top 0.218s ease-out}.spch-2l.spcht{top:-.6em}.spch-3l.spcht{top:-1.3em}.spch-4l.spcht{top:-1.7em}.s2fp .spch-5l.spcht{top:-2.5em;}</style><span class=\"spcht\" style=\"color:#9aa0a6\" id=\"spchi\"></span><span class=\"spcht\" style=\"color:#bdc1c6\" id=\"spchf\"></span></div><div class=\"google-logo\"></div></div><div class=\"permission-bar\"><style>.permission-bar{margin-top:-100px;opacity:0;pointer-events:none;position:absolute;width:500px;transition:opacity 0.218s ease-in,margin-top .4s ease-in}.s2wfp .permission-bar{margin-top:-300px;opacity:1;transition:opacity .5s ease-out 0.218s,margin-top 0.218s ease-out 0.218s}.permission-bar-gradient{box-shadow:0 1px 0px #4285f4;height:80px;left:0;margin:0;opacity:0;pointer-events:none;position:fixed;right:0;top:-80px;transition:opacity 0.218s,box-shadow 0.218s}.s2wfp .permission-bar-gradient{box-shadow:0 1px 80px #4285f4;opacity:1;pointer-events:none;animation:allow-alert .75s 0 infinite;animation-direction:alternate;animation-timing-function:ease-out;transition:opacity 0.218s,box-shadow 0.218s}@-webkit-keyframes allow-alert {from{opacity:1}to{opacity:.35}}</style><div class=\"permission-bar-gradient\"></div></div></div></div></dialog><form action=\"/search\" autocomplete=\"off\" method=\"GET\" role=\"search\"> <div jsmodel=\"b5W85 vNzKHd\" jsdata=\"MuIEvd;_;AT2l+E\"> <div jscontroller=\"cnjECf\" jsmodel=\"VYkzu kjkykd EPRt9d LM7wx Qlyryb EtCx8b Ip3Erc L97mud           \" class=\"A8SBwf\" data-alt=\"false\" data-biboe=\"false\" data-efaql=\"false\" data-hp=\"true\" jsdata=\"LVplcb;_;\" jsaction=\"lX6RWd:w3Wsmc;aaFXSd:k0wtTd;ocDSvd:duwfG;XmGRxb:mVw6nb;DkpM0b:d3sQLd;IQOavd:dFyQEf;XzZZPe:jI3wzf;Aghsf:AVsnlb;iHd9U:Q7Cnrc;f5hEHe:G0jgYd;vmxUb:j3bJnb;XBqW7:ihYaWc;UkQk6c:VSb4De;nTzfpf:YPRawb;R2c5O:LuRugf;qiCkJd:ANdidc;Q3vWPd:FtWxqb;htNNz:SNIJTd;NOg9L:HLgh3;uGoIkd:epUokb;zLdLw:eaGBS;H9muVd:J4e6lb;djyPCf:nMeUJf;hBEIVb:nUZ9le;CudXPd:iu9yrc;TsIFpc:aGAD7e;rcuQ6b:npT2md\"><style>.A8SBwf,.IormK{width:640px;}.A8SBwf{margin:0 auto;width:auto;max-width:584px;padding-top:6px;position:relative}.RNNXgb{display:flex;z-index:3;position:relative;min-height:44px;border:1px solid transparent;background:#4d5156;box-shadow:none;border-radius:24px;margin:0 auto;width:638px;width:auto;max-width:584px}.emcav .RNNXgb,.BgPPrc .RNNXgb{border-bottom-left-radius:0;border-bottom-right-radius:0;box-shadow:0 2px 8px 1px rgba(64,60,67,.24);background:#303134;border-color:transparent;background:#303134;box-shadow:0 2px 8px 1px rgba(64,60,67,.24);}.emcav.emcat .RNNXgb{border-bottom-left-radius:24px;border-bottom-right-radius:24px}.RNNXgb:hover,.sbfc .RNNXgb{background:#303134;box-shadow:0 2px 8px 1px rgba(64,60,67,.24);border-color:transparent}.RNNXgb:hover{background:#5f6368;box-shadow:none}.emcav .RNNXgb:hover,.sbfc.emcav .RNNXgb{background:#303134;box-shadow:0 2px 8px 1px rgba(64,60,67,.24);border-color:transparent}.SDkEP{flex:1;display:flex;padding:0 8px 0 0;}.FPdoLc{padding-top:18px}.emcav.A8SBwf.pD4qTd{z-index:989;}.iblpc{display:flex;align-items:center;padding-right:13px;padding-left:14px;height:46px}.M8H8pb{position:absolute;top:0;left:0;right:0;padding:inherit;width:inherit}</style><div jsname=\"RNNXgb\" class=\"RNNXgb\"><div class=\"SDkEP\"><div jsname=\"uFMOof\" class=\"iblpc\"><style>.CcAdNb{margin:auto}.QCzoEc{margin-top:3px;color:#9aa0a6}</style><div class=\"CcAdNb\"><span class=\"QCzoEc z1asCe MZy1Rb\" style=\"height:20px;line-height:20px;width:20px\"><svg focusable=\"false\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\"><path d=\"M15.5 14h-.79l-.28-.27A6.471 6.471 0 0 0 16 9.5 6.5 6.5 0 1 0 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z\"></path></svg></span></div></div><div jscontroller=\"vZr2rb\" jsname=\"gLFyf\" class=\"a4bIc\" data-hpmde=\"false\" data-mnr=\"10\" jsaction=\"h5M12e;input:d3sQLd;blur:jI3wzf\"><style>.gLFyf,.YacQv{line-height:34px;font-size:16px;flex:100%;}textarea.gLFyf,.YacQv{font-family:Arial,sans-serif;line-height:22px;border-bottom:8px solid transparent;padding-top:11px;overflow-x:hidden}textarea.gLFyf{}.sbfc textarea.gLFyf{white-space:pre-line;overflow-y:auto}.gLFyf{resize:none;background-color:transparent;border:none;margin:0;padding:0;color:#e8eaed;word-wrap:break-word;outline:none;display:flex;tap-highlight-color:transparent}.a4bIc{display:flex;flex-wrap:wrap;flex:1;}.YacQv{color:transparent;white-space:pre;position:absolute;pointer-events:none}.YacQv span{text-decoration:#f28b82 dotted underline}</style><div jsname=\"vdLsw\" class=\"YacQv\"></div><textarea class=\"gLFyf\" aria-controls=\"Alh6id\" aria-owns=\"Alh6id\" autofocus=\"\" title=\"Search\" value=\"\" jsaction=\"paste:puy29d;\" aria-label=\"Search\" aria-autocomplete=\"both\" aria-expanded=\"false\" aria-haspopup=\"false\" autocapitalize=\"none\" autocomplete=\"off\" autocorrect=\"off\" id=\"APjFqb\" maxlength=\"2048\" name=\"q\" role=\"combobox\" rows=\"1\" spellcheck=\"false\" data-ved=\"0ahUKEwjEmLvfvKKJAxXHL7kGHQ53CxkQ39UDCA0\"></textarea></div><div class=\"fM33ce dRYYxd\"><style>.dRYYxd{display:flex;flex:0 0 auto;align-items:stretch;flex-direction:row;height:44px}</style> <style>.BKRPef{background:transparent;align-items:center;flex:1 0 auto;flex-direction:row;display:flex;cursor:pointer}.vOY7J{background:transparent;border:0;align-items:center;flex:1 0 auto;cursor:pointer;display:none;height:100%;line-height:44px;outline:none;padding:0 12px}.M2vV3{display:flex}.ExCKkf{height:100%;color:#e8eaed;vertical-align:middle;outline:none}</style> <style>.BKRPef{padding-right:4px}.ACRAdd{border-left:1px solid rgba(248, 249, 250, 0.25);height:65%}.ACRAdd{display:none}.ACRAdd.M2vV3{display:block}</style> <div jscontroller=\"PymCCe\" jsname=\"RP0xob\" class=\"BKRPef\"> <div class=\"vOY7J\" tabindex=\"0\" jsname=\"pkjasb\" aria-label=\"Clear\" role=\"button\" jsaction=\"AVsnlb;rcuQ6b:npT2md\" data-ved=\"0ahUKEwjEmLvfvKKJAxXHL7kGHQ53CxkQ05YFCA4\">  <span jsname=\"itVqKe\" class=\"ExCKkf z1asCe rzyADb\"><svg focusable=\"false\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\"><path d=\"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"></path></svg></span>   </div> <span jsname=\"s1VaRe\" class=\"ACRAdd\"></span> </div> <style>.XDyW0e{flex:1 0 auto;display:flex;cursor:pointer;align-items:center;border:0;background:transparent;outline:none;padding:0 8px;width:24px;line-height:44px}.goxjub{height:24px;width:24px;vertical-align:middle}</style><div jscontroller=\"unV4T\" jsname=\"F7uqIe\" class=\"XDyW0e\" aria-label=\"Search by voice\" role=\"button\" tabindex=\"0\" jsaction=\"h5M12e;rcuQ6b:npT2md\" data-ved=\"0ahUKEwjEmLvfvKKJAxXHL7kGHQ53CxkQvs8DCA8\"><svg class=\"goxjub\" focusable=\"false\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\"><path fill=\"#4285f4\" d=\"m12 15c1.66 0 3-1.31 3-2.97v-7.02c0-1.66-1.34-3.01-3-3.01s-3 1.34-3 3.01v7.02c0 1.66 1.34 2.97 3 2.97z\"></path><path fill=\"#34a853\" d=\"m11 18.08h2v3.92h-2z\"></path><path fill=\"#fbbc04\" d=\"m7.05 16.87c-1.27-1.33-2.05-2.83-2.05-4.87h2c0 1.45 0.56 2.42 1.47 3.38v0.32l-1.15 1.18z\"></path><path fill=\"#ea4335\" d=\"m12 16.93a4.97 5.25 0 0 1 -3.54 -1.55l-1.41 1.49c1.26 1.34 3.02 2.13 4.95 2.13 3.87 0 6.99-2.92 6.99-7h-1.99c0 2.92-2.24 4.93-5 4.93z\"></path></svg></div><style>.nDcEnd{flex:1 0 auto;display:flex;cursor:pointer;align-items:center;border:0;background:transparent;outline:none;padding:0 8px;width:24px;line-height:44px}.Gdd5U{height:24px;width:24px;vertical-align:middle}</style><div jscontroller=\"lpsUAf\" class=\"nDcEnd\" data-base-lens-url=\"https://lens.google.com\" data-image-processor-enabled=\"true\" data-is-images-mode=\"false\" data-preferred-mime-type=\"image/jpeg\" data-propagated-experiment-ids=\"\" jsname=\"R5mgy\" aria-label=\"Search by image\" role=\"button\" tabindex=\"0\" jsaction=\"rcuQ6b:npT2md;h5M12e\" data-ved=\"0ahUKEwjEmLvfvKKJAxXHL7kGHQ53CxkQhqEICBA\"><svg class=\"Gdd5U\" focusable=\"false\" viewBox=\"0 0 192 192\" xmlns=\"http://www.w3.org/2000/svg\"><rect fill=\"none\" height=\"192\" width=\"192\"></rect><g><circle fill=\"#34a853\" cx=\"144.07\" cy=\"144\" r=\"16\"></circle><circle fill=\"#4285f4\" cx=\"96.07\" cy=\"104\" r=\"24\"></circle><path fill=\"#ea4335\" d=\"M24,135.2c0,18.11,14.69,32.8,32.8,32.8H96v-16l-40.1-0.1c-8.8,0-15.9-8.19-15.9-17.9v-18H24V135.2z\"></path><path fill=\"#fbbc04\" d=\"M168,72.8c0-18.11-14.69-32.8-32.8-32.8H116l20,16c8.8,0,16,8.29,16,18v30h16V72.8z\"></path><path fill=\"#4285f4\" d=\"M112,24l-32,0L68,40H56.8C38.69,40,24,54.69,24,72.8V92h16V74c0-9.71,7.2-18,16-18h80L112,24z\"></path></g></svg></div></div></div></div><div jscontroller=\"Dvn7fe\" jsname=\"UUbT9\" class=\"UUbT9 EyBRub\" style=\"display:none\" jsaction=\"mouseout:ItzDCd;mouseleave:MWfikb;hBEIVb:nUZ9le;YMFC3:VKssTb;vklu5c:k02QY;ldyIye:CmVOgc\" data-ved=\"0ahUKEwjEmLvfvKKJAxXHL7kGHQ53CxkQ4tUDCBE\"><style>.UUbT9{position:absolute;text-align:left;z-index:3;cursor:default;user-select:none;width:100%;margin-top:-1px;}.aajZCb{display:flex;flex-direction:column;margin:0;padding:0;overflow:hidden;background:#303134;border-radius:0 0 24px 24px;box-shadow:0 4px 6px 0 #171717;border:0;padding-bottom:4px}.minidiv .aajZCb{border-radius:0 0 16px 16px}.mkHrUc{display:flex}.pD4qTd .rLrQHf{padding-bottom:16px}.pD4qTd .rLrQHf{min-width:47%;width:47%;margin:8px 16px 0}.pD4qTd .rLrQHf:empty{display:none}.erkvQe{padding-bottom:8px;flex:auto;overflow-x:hidden}.RjPuVb{height:1px;margin:0 26px 0 0}.S3nFnd{display:flex}.S3nFnd .RjPuVb,.S3nFnd .aajZCb{flex:0 0 auto}.lh87ke:link,.lh87ke:visited{color:var(--JKqx2);cursor:pointer;font:11px arial,sans-serif;padding:0 5px;text-decoration:none;flex:auto;align-self:flex-end;margin:0 16px 5px 0}.lh87ke:hover{text-decoration:underline}.xtSCL{border-top:1px solid #5f6368;margin:0 20px 0 14px;padding-bottom:4px}.sb27{background:url(/images/searchbox/desktop_searchbox_sprites318_hr.webp) no-repeat 0 -21px;background-size:20px;min-height:20px;min-width:20px;height:20px;width:20px}.sb43{background:url(/images/searchbox/desktop_searchbox_sprites318_hr.webp) no-repeat 0 0;background-size:20px;min-height:20px;min-width:20px;height:20px;width:20px}.sb53.sb53{padding:0 4px;margin:0}.sb33{background:url(/images/searchbox/desktop_searchbox_sprites318_hr.webp) no-repeat 0 -42px;background-size:20px;height:20px;width:20px;}</style><div jscontroller=\"Wo3n8\" jsname=\"aadvhe\" jsmodel=\"d5EhJe\" data-bkt=\"searchbox\" data-eas=\"\" data-fhs=\"\" data-maindata=\"[null,null,null,&quot;autocomplete_user_feedback_kp_id&quot;,null,11,null,null,null,null,null,5010715,&quot;searchbox&quot;,null,&quot;AutocompletePrediction&quot;,null,null,null,null,11]\" data-pid=\"5010715\" jsdata=\"vST7rb;_;AT2l+I zEIyGd;_;\" jsaction=\"kPzEO:MlP2je;qjLxRc:FbhRG;w8f1fc:hRwSgb;kq2wxf:s5CUif;aIJAdf:UhDUnd;BqbTbe:naa5ve;kYAKrf:CqUGrf;hwhRRe:KyxjCd;rcuQ6b:npT2md\"><div id=\"_sNsXZ8SAEsff5OUPju6tyAE_1\"><style>.z8gr9e{color:var(--bbQxAb)}</style><style>.i1eWpb .GTERze{display:none}.ky4hfd{display:none}.i1eWpb .ky4hfd{display:block}</style><div jsname=\"GkjeIf\" id=\"_sNsXZ8SAEsff5OUPju6tyAE_4\" data-jiis=\"up\" data-async-type=\"kp_feedback\" class=\"yp\" data-ved=\"0ahUKEwjEmLvfvKKJAxXHL7kGHQ53CxkQ68cECBI\"></div></div></div><div jscontroller=\"P10Owf\" class=\"YB4h9 ky4hfd\" aria-label=\"Choose what you’re giving feedback on\" jsdata=\"vST7rb;_;AT2l+I\" role=\"dialog\" jsaction=\"kPzEO:MlP2je;qjLxRc:MlP2je;w8f1fc:hRwSgb;kq2wxf:s5CUif\" data-ved=\"0ahUKEwjEmLvfvKKJAxXHL7kGHQ53CxkQlokGCBM\"><div id=\"_sNsXZ8SAEsff5OUPju6tyAE_6\"><style>.YB4h9{background-color:var(--TMYS9);color:var(--EpFNW);padding:18px 60px 18px 12px;position:relative}.C85rO{font-size:20px}.Gtr0ne{padding-top:10px}.twTT9c{}.YB4h9 .Gtr0ne .twTT9c{color:var(--EpFNW);text-decoration:underline;padding:0;background:none;border:none;font:inherit;outline:inherit}.YB4h9 .Job8vb{padding:20px;position:absolute;right:0;top:0}.YB4h9.rPPJbd .Job8vb{padding-top:24px;padding-right:8px;position:absolute;right:0;top:0}.YB4h9.q7XNbb{margin-bottom:44px}.YB4h9.JF7fk{border-radius:16px;border-style:solid;border-color:var(--gS5jXb)}.YB4h9.IPINXd{border-bottom-left-radius:16px;border-bottom-right-radius:16px;border-color:var(--gS5jXb);border-style:solid;border-top:none}.YB4h9.rPPJbd{background-color:var(--xhUGwc);color:var(--bbQxAb)}.YB4h9.rPPJbd .twTT9c{color:var(--bbQxAb);text-decoration:underline;padding:0;background:none;border:none;font:inherit;outline:inherit}.R4GmFb{align-items:center;display:flex;flex-direction:row;margin-bottom:8px}.R4GmFb svg{display:block}.JrWcR{margin-left:10px}</style><span class=\"Job8vb z1asCe wuXmqc\" aria-label=\"Close Choose what you’re giving feedback on\" role=\"button\" tabindex=\"0\" jsaction=\"kEOk4d\" style=\"height:20px;line-height:20px;width:20px\" data-ved=\"0ahUKEwjEmLvfvKKJAxXHL7kGHQ53CxkQmIkGCBQ\"><svg focusable=\"false\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\"><path d=\"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"></path></svg></span><div class=\"C85rO\" aria-level=\"1\" role=\"heading\">Choose what you’re giving feedback on</div></div></div><div jsname=\"RjPuVb\" class=\"RjPuVb\"></div><div jsname=\"aajZCb\" class=\"aajZCb\"><div class=\"xtSCL\"></div><div class=\"mkHrUc\" id=\"Alh6id\" role=\"presentation\"><div jsname=\"erkvQe\" class=\"erkvQe\"></div><div jsname=\"tovEib\" class=\"rLrQHf\" role=\"presentation\"></div></div><style>#shJ2Vb{display:none}.OBMEnb{padding:0;margin:0}.G43f7e{display:flex;flex-direction:column;min-width:0;padding:0;margin:0;list-style:none}.Ye4jfc{flex-direction:row;flex-wrap:wrap}</style><div jsname=\"E80e9e\" class=\"OBMEnb\" id=\"shJ2Vb\" role=\"presentation\"><ul jsname=\"bw4e9b\" class=\"G43f7e\" role=\"listbox\"></ul></div><style>#ynRric{display:none}.ynRric{list-style-type:none;flex-direction:column;color:var(--IXoxUe);font-family:Google Sans,Arial,sans-serif-medium,sans-serif;font-size:14px;margin:0 20px 0 16px;padding:8px 0 8px 0;line-height:16px;width:100%}</style><div class=\"ynRric\" id=\"ynRric\" role=\"presentation\"></div><style>.sbct{display:flex;flex-direction:column;min-width:0;overflow:hidden;padding:0}.eIPGRd{flex:auto;display:flex;align-items:center;margin:0 20px 0 14px}.pcTkSc{display:flex;flex:auto;flex-direction:column;min-width:0;padding:6px 0}.sbic{display:flex;align-items:center;margin:0 13px 0 1px;}.sbic.vYOkbe{background:center/contain no-repeat;border-radius:4px;min-height:32px;min-width:32px;margin:4px 7px 4px -5px}.sbre .wM6W7d{line-height:18px}.ClJ9Yb{line-height:12px;font-size:13px;color:#9aa0a6;margin-top:2px;padding-right:8px}.wM6W7d{display:flex;font-size:16px;color:#e8eaed;flex:auto;align-items:center;word-break:break-all;padding-right:8px}.WggQGd{color:#c58af9}.wM6W7d span{flex:auto}.AQZ9Vd{display:flex;align-self:stretch}.sbhl{border-radius:4px;background:#3c4043}.UUbT9.i1eWpb .PZPZlf.sbhl{background:none}.UUbT9.i1eWpb .PZPZlf.sbhl .gmlSVb{background:rgba(255,119,105,.12)}@media (forced-colors:active){.sbhl{background-color:highlight}}.mus_pc{display:block;margin:6px 0}.mus_il{font-family:Arial,Helvetica Neue Light,Helvetica Neue,Helvetica;padding-top:7px;position:relative}.mus_il:first-child{padding-top:0}.mus_il_at{margin-left:10px}.mus_il_st{right:52px;position:absolute}.mus_il_i{align:left;margin-right:10px}.mus_it3{margin-bottom:3px;max-height:24px;vertical-align:bottom}.mus_tt3{color:#9aa0a6;font-size:12px;vertical-align:top}.mus_tt5{color:#f28b82;font-size:14px}.mus_tt6{color:#81c995;font-size:14px}.mus_tt8{font-size:16px;font-family:Arial,sans-serif}.mus_tt17{color:#e8eaed;font-size:20px}.mus_tt18{color:#e8eaed;font-size:28px}.mus_tt19{color:#9aa0a6;font-size:12px}.mus_tt20{color:#9aa0a6;font-size:14px}.mus_tt23{color:#9aa0a6;font-size:18px}.TfeWfb{display:none}.xAmryf{display:none}.DJbVFb .TfeWfb{display:flex;flex-wrap:wrap;overflow-x:hidden;width:100%;height:52px}.DJbVFb .AQZ9Vd{display:none}.DJbVFb .xAmryf{border-radius:100px;background-color:#28292a}.DJbVFb .TfeWfb{display:inherit}.DJbVFb .xAmryf .eL7oAc{display:none}.DJbVFb{background:#3c4043;border-radius:20px}.DJbVFb:hover{background:rgba(95,99,104,0.6)}.DJbVFb .vYOkbe{height:-1px;width:-1px;flex-shrink:0;margin:20px 0 20px 8px;float:right;border-radius:16px;background-color:#fff}.DJbVFb.sbhl{background:rgba(95,99,104,0.6)}.DJbVFb .ClJ9Yb{display:none}.DJbVFb .wM6W7d{flex:initial}.DJbVFb .wM6W7d span{text-overflow:ellipsis;-webkit-box-orient:vertical;display:-webkit-box;-webkit-line-clamp:2;overflow:hidden}.DJbVFb .eIPGRd{display:flex;flex-direction:row-reverse;align-items:stretch;margin:0 20px 0 14px}.DJbVFb .a5RLac{line-height:24px;font-size:20px;font-family:Arial,sans-serif;padding-top:16px;color:var(--bbQxAb);margin-bottom:auto}.DJbVFb .kzCE2{font-size:16px}.DJbVFb .wM6W7d span{color:var(--YLNNHc);line-height:36px;font-weight:400;font-size:28px;font-family:Google Sans,Arial,sans-serif}.DJbVFb .pcTkSc{margin:20px 6px;padding:0}.DJbVFb .vYOkbe{margin:20px 0 20px 18px;background-color:#fff;border-radius:20px}.DJbVFb .EOLKOc{width:calc(50% - 1px)}.iQxPRb{display:flex;gap:2px}.DJbVFb .EOLKOc:first-child{border-bottom-left-radius:20px}.DJbVFb .EOLKOc:last-child{border-bottom-right-radius:20px}.DJbVFb .AZNDm{border-top-right-radius:20px;border-top-left-radius:20px}.DJbVFb .a5RLac.kzCE2 span{-webkit-line-clamp:3}.DJbVFb .lnnVSe{margin-bottom:auto}.DJbVFb .a5RLac span{text-overflow:ellipsis;-webkit-box-orient:vertical;display:-webkit-box;-webkit-line-clamp:2;overflow:hidden;margin-right:10px}#bgeLZd{display:none}.xAmryf{box-sizing:border-box;align-items:center;height:40px;border-radius:8px;display:flex;color:var(--bbQxAb);border:1px solid var(--mXZkqc);background-color:var(--xhUGwc);line-height:22px}.xAmryf .eL7oAc{fill:var(--bbQxAb);padding-top:1px}.xAmryf.LvqzR{background-color:#394457;cursor:pointer;color:var(--TMYS9)}.xAmryf.LvqzR .eL7oAc{fill:var(--TMYS9)}.jtAOgd{white-space:nowrap;font-family:Google Sans,Arial,sans-serif;font-size:14px;margin:0 14px}.TfeWfb{gap:12px 6px;overflow-x:auto;-ms-overflow-style:none;scrollbar-width:none}.TfeWfb::-webkit-scrollbar{display:none}.uhebGb{font-style:italic}#YMXe{display:none}</style><li data-view-type=\"1\" class=\"sbct PZPZlf\" id=\"YMXe\" role=\"presentation\" data-attrid=\"AutocompletePrediction\" data-entityid=\"autocomplete_user_feedback_kp_id\"><div class=\"eIPGRd\"><div class=\"sbic\"><div class=\"j0GJWd\" style=\"display:none\"><div><img class=\"uHGFVd AZNDm\" alt=\"\" style=\"display:none\" data-csiid=\"sNsXZ8SAEsff5OUPju6tyAE_3\" data-atf=\"0\"></div><div class=\"iQxPRb\"><img class=\"uHGFVd EOLKOc\" alt=\"\" style=\"display:none\" data-csiid=\"sNsXZ8SAEsff5OUPju6tyAE_4\" data-atf=\"0\"><img class=\"uHGFVd EOLKOc\" alt=\"\" style=\"display:none\" data-csiid=\"sNsXZ8SAEsff5OUPju6tyAE_5\" data-atf=\"0\"></div></div></div><div class=\"pcTkSc\"><div class=\"lnnVSe\" aria-atomic=\"true\" role=\"option\"><div class=\"wM6W7d\"><span></span></div><div class=\"ClJ9Yb\"><span></span></div><div class=\"a5RLac\"><span></span></div></div><style>.MagqMc .ZFiwCf{background-color:#28292a;border:1px solid var(--gS5jXb);width:100%}.MagqMc.U48fD{padding:0;margin-top:16px}.MagqMc .Bi9oQd{display:none}.MagqMc{padding:0}.MagqMc:hover .LGwnxb{color:var(--YLNNHc)}.sOmPcf .ZFiwCf{background-color:#303134}</style><div class=\"Sz7Lee MagqMc U48fD\" style=\"display:none\" aria-label=\"See more\" role=\"button\" tabindex=\"0\"><style>.U48fD{-webkit-tap-highlight-color:transparent;cursor:pointer;display:block;line-height:18px;text-overflow:ellipsis;white-space:nowrap;padding:16px;padding-top:0;margin-top:24px;position:relative}.TQc1id .U48fD{margin-top:16px}.U48fD.df13ud{margin-top:16px}.U48fD.TOQyFc{margin-top:0}.U48fD.p8FEIf{padding-bottom:0}.U48fD.ke7M4{padding-left:0;padding-right:0}.jRKCUd::before{bottom:12px;content:'';left:16px;position:absolute;right:16px;top:-4px}a.jRKCUd:hover{text-decoration:none}</style><style>.ZFiwCf{display:flex;align-items:center;justify-content:center;position:relative;margin:0 auto;width:100%;border-radius:18px;outline:1px solid transparent;background-color:var(--XKMDxc);font-size:14px;font-family:Arial,sans-serif;font-weight:400;max-width:300px;height:36px}@media (forced-colors:active){.ZFiwCf{border:1px solid transparent}}.TQc1id .ZFiwCf{max-width:unset}.Zjtggb .ZFiwCf{max-width:unset}.ZFiwCf:hover{background-color:#303134}.nCFUpc .ZFiwCf{width:100%}.Bi9oQd{background-color:var(--gS5jXb);margin-top:18px;position:absolute;border:0;height:1px;left:0;width:100%;}.TQc1id .Bi9oQd{display:none}.kC8B4e .Bi9oQd{display:none}.w2fKdd svg{width:auto}.w2fKdd{color:#9e9e9e}.LGwnxb{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;width:auto;padding-left:0;padding-right:8px;color:#e8eaed;max-width:calc(100% - 64px)}.LGwnxb:empty{padding-right:0}.LGwnxb span,.LGwnxb div{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;width:auto}</style><hr class=\"Bi9oQd\" aria-hidden=\"true\"><div class=\"ZFiwCf\"><span class=\"LGwnxb\">See more</span><span class=\"w2fKdd z1asCe\" aria-hidden=\"true\" style=\"height:20px;line-height:20px;width:20px\"><svg focusable=\"false\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\"><path d=\"M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8z\"></path></svg></span></div></div></div><div class=\"AQZ9Vd\" aria-atomic=\"true\" role=\"button\"><style>.JCHpcb:hover,.LvqzR .JCHpcb{color:#8ab4f8;text-decoration:underline}.JCHpcb{color:#aaadb2;font:13px arial,sans-serif;cursor:pointer;align-self:center}@media (hover:hover){.sbai{visibility:hidden}.sbhl .sbai{visibility:inherit}}</style><div class=\"sbai\" role=\"presentation\">Delete</div></div></div></li><style>#d6ItKb{display:none}.AB2Fdd{display:flex}</style><li class=\"AB2Fdd\" data-view-type=\"9\" id=\"d6ItKb\" role=\"presentation\"><div class=\"eIPGRd\"><div class=\"ZDHp\" style=\"display:none\" id=\"fU0xAb\" role=\"presentation\"><div class=\"kZtr1b\"><style>.ZDHp{position:relative;margin:20px;display:flex}.DJbVFb,.o6OF0{background:#414143;border-radius:20px}.o6OF0:hover,.o6OF0.LvqzR{background:#515254}.o6OF0 .eIPGRd{display:block}@media (forced-colors:none){.o6OF0.sbhl{background:#515254}}@media (forced-colors:active){.o6OF0.sbhl{background-color:highlight}}.o6OF0 .AQZ9Vd{display:none}.o6OF0 .sbic{display:none}.o6OF0 .pcTkSc{display:none}.o6OF0 .wM6W7d{display:none}.o6OF0 .eIPGRd{max-width:100%;margin:0}.az9Ajc{padding_top:0px}.ZDHp .SHFPkb{margin-bottom:12px}.o6OF0 .SHFPkb{line-height:48px;font-family:Google Sans,Arial,sans-serif;font-size:36px;font-weight:400;color:var(--YLNNHc);display:-webkit-box;overflow:hidden;-webkit-line-clamp:2;-webkit-box-orient:vertical}.lQoozf{font-size:28px}.o6OF0 .HrUlUc,.o6OF0 .PnfqLc{font-family:Arial,sans-serif;font-weight:400;max-height:72px;color:var(--bbQxAb)}.ZDHp .HrUlUc,.ZDHp .PnfqLc{font-size:18px;line-height:24px}.o6OF0 .bTSf5c{font-family:Arial,sans-serif;font-weight:400;color:var(--bbQxAb)}.ZDHp .bTSf5c{line-height:22px;font-size:14px;margin-bottom:6px}.ZDHp .HrUlUc,.ZDHp .PnfqLc{overflow:hidden;display:-webkit-box;-webkit-line-clamp:3;-webkit-box-orient:vertical}.Vlt3wb{font-style:normal;font-family:Arial,sans-serif;font-weight:400;font-size:14px;line-height:22px;padding-top:8px;margin-top:12px;color:var(--bbQxAb);border-top:1px solid #5f6368;display:flex;width:100%}.Tnv2td{position:absolute;top:0;right:0}.z76Rnb{padding:6px;width:24px;height:24px;background-color:#28292a;color:#9e9e9e;border-radius:9999px;border:1px solid var(--mXZkqc);cursor:pointer}.z76Rnb.LvqzR{color:var(--YLNNHc);background-color:#303134}.kZtr1b{display:flex;flex-direction:column;flex-grow:1;min-width:0}.XAFD5c{width:200px;height:200px;background-color:#28292a;border-radius:20px;margin-left:20px;flex-shrink:0;background-position:center;background-repeat:no-repeat;background-size:contain}.XAFD5c.iNF0Vd{background-size:136px}.ZDHp .lnnVSe{font-size:18px;flex-grow:1}.ZDHp .HrUlUc,.ZDHp .PnfqLc{display:flex;flex-direction:column}.rnAixd{color:#f28b82}.izxCJf{color:#81c995}</style><div class=\"lnnVSe\" aria-atomic=\"true\" role=\"option\"><div class=\"SHFPkb\"></div><div class=\"bTSf5c\"></div><div class=\"PnfqLc\"></div><div class=\"HrUlUc\"></div></div><div class=\"Tnv2td\" style=\"display:none\" aria-label=\"Word pronunciation\" aria-atomic=\"true\" role=\"button\"><span class=\"z76Rnb z1asCe JKu1je\"><svg focusable=\"false\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\"><path d=\"M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z\"></path></svg></span></div><div class=\"Vlt3wb\" style=\"display:none\"></div></div><span class=\"XAFD5c\" style=\"display:none\"></span><div class=\"j0GJWd\" style=\"display:none\"><div><img class=\"uHGFVd AZNDm\" alt=\"\" style=\"display:none\" data-csiid=\"sNsXZ8SAEsff5OUPju6tyAE_6\" data-atf=\"0\"></div><div class=\"iQxPRb\"><img class=\"uHGFVd EOLKOc\" alt=\"\" style=\"display:none\" data-csiid=\"sNsXZ8SAEsff5OUPju6tyAE_7\" data-atf=\"0\"><img class=\"uHGFVd EOLKOc\" alt=\"\" style=\"display:none\" data-csiid=\"sNsXZ8SAEsff5OUPju6tyAE_8\" data-atf=\"0\"></div></div></div></div></li><style>#mitGyb{display:none}.s2Wjec{display:block}.Q82Okf{font-size:16px;font-family:Arial,sans-serif}</style><li data-view-type=\"8\" class=\"sbct PZPZlf\" id=\"mitGyb\" role=\"presentation\" data-attrid=\"AutocompletePrediction\" data-entityid=\"autocomplete_user_feedback_kp_id\"><div class=\"eIPGRd hdt0ld\"><div class=\"sbic\"></div><div class=\"pcTkSc\"><div aria-atomic=\"true\" class=\"lnnVSe\" role=\"option\"><div class=\"wM6W7d\"><span></span></div><div class=\"ClJ9Yb\"><span></span></div></div></div><div class=\"AQZ9Vd\" aria-atomic=\"true\" role=\"button\"><div class=\"sbai\" role=\"presentation\">Delete</div></div></div></li><div class=\"ZDHp\" style=\"display:none\" id=\"fU0xAb\" role=\"presentation\"><div class=\"kZtr1b\"><div class=\"lnnVSe\" aria-atomic=\"true\" role=\"option\"><div class=\"SHFPkb\"></div><div class=\"bTSf5c\"></div><div class=\"PnfqLc\"></div><div class=\"HrUlUc\"></div></div><div class=\"Tnv2td\" style=\"display:none\" aria-label=\"Word pronunciation\" aria-atomic=\"true\" role=\"button\"><span class=\"z76Rnb z1asCe JKu1je\"><svg focusable=\"false\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\"><path d=\"M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z\"></path></svg></span></div><div class=\"Vlt3wb\" style=\"display:none\"></div></div><span class=\"XAFD5c\" style=\"display:none\"></span><div class=\"j0GJWd\" style=\"display:none\"><div><img class=\"uHGFVd AZNDm\" alt=\"\" style=\"display:none\" data-csiid=\"sNsXZ8SAEsff5OUPju6tyAE_9\" data-atf=\"0\"></div><div class=\"iQxPRb\"><img class=\"uHGFVd EOLKOc\" alt=\"\" style=\"display:none\" data-csiid=\"sNsXZ8SAEsff5OUPju6tyAE_10\" data-atf=\"0\"><img class=\"uHGFVd EOLKOc\" alt=\"\" style=\"display:none\" data-csiid=\"sNsXZ8SAEsff5OUPju6tyAE_11\" data-atf=\"0\"></div></div></div><style>#TN4rFf{display:none}.IDVnvc{display:inline-block;overflow:hidden;max-width:223px;border-radius:16px;height:178px;margin:-2px -10px 2px 10px}.IDVnvc.sbhl{border-radius:16px}.OBMEnb:only-child .IDVnvc{margin-right:calc(25% - 113px)}.cRV9hb{width:90px;padding:6px}.cRV9hb .pcTkSc{font-family:Arial,sans-serif;overflow:hidden;margin-top:4px;padding:0}.cRV9hb .pcTkSc .wM6W7d{font-size:14px;line-height:18px;padding:0;color:var(--COEmY)}.cRV9hb .pcTkSc .ClJ9Yb{line-height:16px;font-size:12px;display:none;display:flex}.cRV9hb .pcTkSc .wM6W7d span,.cRV9hb .pcTkSc .ClJ9Yb span{overflow:hidden;text-overflow:ellipsis;-webkit-box-orient:vertical;display:-webkit-box;white-space:normal}.cRV9hb .pcTkSc .wM6W7d span{-webkit-line-clamp:2}.cRV9hb .pcTkSc .ClJ9Yb span{-webkit-line-clamp:2}.aVbWac{background:#fff;border-radius:12px;height:90px}.aVbWac .sbic.vYOkbe{height:90px;width:90px;border-radius:12px;margin:0}</style><li class=\"IDVnvc PZPZlf\" data-view-type=\"6\" id=\"TN4rFf\" role=\"presentation\" data-attrid=\"AutocompletePrediction\" data-entityid=\"autocomplete_user_feedback_kp_id\"><div class=\"cRV9hb\"><div class=\"aVbWac\"><div class=\"sbic\"></div></div><div class=\"pcTkSc\" role=\"presentation\"><div class=\"lnnVSe\" aria-atomic=\"true\" role=\"option\"><div class=\"wM6W7d\"><span></span></div><div class=\"ClJ9Yb\"><span></span></div></div></div></div></li><div jsname=\"VlcLAe\" class=\"lJ9FBc\"> <style>.lJ9FBc{height:70px}.lJ9FBc input[type=\"submit\"],.gbqfba{background-color:#303134;border:1px solid #303134;border-radius:4px;color:#e8eaed;font-family:Arial,sans-serif;font-size:14px;margin:11px 4px;padding:0 16px;line-height:27px;height:36px;min-width:54px;text-align:center;cursor:pointer;user-select:none}.lJ9FBc input[type=\"submit\"]:hover{box-shadow:0 1px 3px rgba(23,23,23,0.24);background-color:#303134;border:1px solid #5f6368;color:#e8eaed}.lJ9FBc input[type=\"submit\"]:focus{border:1px solid #5f6368;outline:none}.aajZCb .lJ9FBc input,.aajZCb .lJ9FBc input:hover{background:#3c4043}input:focus{outline:none}input::-moz-focus-inner{border:0}</style> <center> <input class=\"gNO89b\" value=\"Google Search\" aria-label=\"Google Search\" name=\"btnK\" role=\"button\" tabindex=\"0\" type=\"submit\" data-ved=\"0ahUKEwjEmLvfvKKJAxXHL7kGHQ53CxkQ4dUDCBY\"> <input class=\"RNmpXc\" value=\"I'm Feeling Lucky\" aria-label=\"I'm Feeling Lucky\" name=\"btnI\" type=\"submit\" jsaction=\"trigger.kWlxhc\" data-ved=\"0ahUKEwjEmLvfvKKJAxXHL7kGHQ53CxkQ19QECBc\">  </center> </div></div><div jsname=\"JUypV\"><style>.WzNHm{font-size:11px;font-style:italic;margin-top:-16px;position:absolute;right:20px;color:var(--IXoxUe)}</style><div class=\"WzNHm mWcf0e\" jscontroller=\"gSZvdb\" data-dccl=\"false\" role=\"button\" tabindex=\"0\" jsdata=\"vST7rb;_;AT2l+I\" jsaction=\"i5KCU;kVBCVd:yM1YJe\" data-ved=\"0ahUKEwjEmLvfvKKJAxXHL7kGHQ53CxkQ6scECBg\"><div class=\"VfL2Y LRZwuc\">Report inappropriate predictions</div></div></div></div><div jsname=\"mvaK7d\" class=\"M8H8pb\" data-ved=\"0ahUKEwjEmLvfvKKJAxXHL7kGHQ53CxkQ4d8ICBk\"></div><div class=\"FPdoLc lJ9FBc\">  <center> <input class=\"gNO89b\" value=\"Google Search\" aria-label=\"Google Search\" name=\"btnK\" role=\"button\" tabindex=\"0\" type=\"submit\" data-ved=\"0ahUKEwjEmLvfvKKJAxXHL7kGHQ53CxkQ4dUDCBo\"> <input class=\"RNmpXc\" value=\"I'm Feeling Lucky\" aria-label=\"I'm Feeling Lucky\" name=\"btnI\" type=\"submit\" jsaction=\"trigger.kWlxhc\" data-ved=\"0ahUKEwjEmLvfvKKJAxXHL7kGHQ53CxkQ19QECBs\">  </center> </div></div> <div style=\"background:url(/images/searchbox/desktop_searchbox_sprites318_hr.webp)\"> </div> <script nonce=\"CP311SD5BVmtqozsXvXMVQ\">(function(){\nvar a=this||self;var c=document.querySelector(\"form\");if(c){var d=function(b){b.key!==\"Enter\"||b.shiftKey||(b.preventDefault(),c.submit&&c.submit())};c.addEventListener(\"keydown\",d);a.sbmlhf=d};}).call(this);</script> </div> <div id=\"tophf\"><input name=\"sca_esv\" value=\"88f3ba43b8b69cfe\" type=\"hidden\"><input name=\"source\" type=\"hidden\" value=\"hp\"><input value=\"sNsXZ8SAEsff5OUPju6tyAE\" name=\"ei\" type=\"hidden\"><input value=\"AL9hbdgAAAAAZxfpwKZKuSaJrnJHWyvWW4tVpFgNIQhW\" name=\"iflsig\" type=\"hidden\"></div></form></div><div class=\"o3j99 qarstb\"><style>.vcVZ7d{text-align:center}</style><div><div jscontroller=\"ms4mZb\" jsname=\"FAeNCb\" data-aipm=\"1\" data-eaip=\"true\" data-endpoint=\"2\" data-oaipm=\"0\" id=\"_sNsXZ8SAEsff5OUPju6tyAE_8\" data-jiis=\"up\" data-async-type=\"hpba\" class=\"yp\" jsaction=\"rcuQ6b:npT2md\" data-ved=\"0ahUKEwjEmLvfvKKJAxXHL7kGHQ53CxkQj-0KCBw\" eid=\"sNsXZ6qtJInD5OUPg9-6iQ8\"><div jsname=\"Nll0ne\"></div></div>  <script nonce=\"CP311SD5BVmtqozsXvXMVQ\">(function(){'use strict';const d=[],e={},f=[],g=[],h=[];function k(a,b){if(a.wfpe&&document.prerendering)document.addEventListener(\"prerenderingchange\",()=>{b(a)},{once:!0,passive:!0});else if(a.wfve&&document.hidden){function c(){document.hidden||(b(a),document.removeEventListener(\"visibilitychange\",c))}document.addEventListener(\"visibilitychange\",c,{passive:!0})}else b(a)}\nfunction l(a){a.ets={};var b=document.getElementById(a.id);a.t=b;if(b=b==null?void 0:b.dataset.ved)a.url+=`&vet=${`1${b}..i`}`;for(const c of f)c&&c(a);e[a.id]=a;d[a.m](a)}google.ia==null&&(google.ia={q:l,r:e,mi:d,rf:f,was:k,dq:g,pf:h});}).call(this);</script>     <script nonce=\"CP311SD5BVmtqozsXvXMVQ\">(function(){'use strict';var h={push:a=>void a()};var k=function(a){if(Array.isArray(a.g.cbvi))for(const b of a.g.cbvi)b();a.g.cbvi=h},l=function(a){k(a);var b,c;let d;const e=((b=google.stvsc)==null?void 0:b.ns)||((c=window.performance)==null?void 0:(d=c.timing)==null?void 0:d.navigationStart);e&&(a.i.ns=e);b=a.g.t.getAttribute(\"eid\");c=[];for(var f of Object.keys(a.i))c.push(`${f}.${a.i[f]-a.L}`);for(const g of Object.keys(a.j))c.push(`${g}.${a.j[g]-a.L}`);f=`/gen_204?s=async&astyp=${a.g.astyp}&atyp=csi&ei=${b}&rt=${c.join(\",\")}`;e&&(f+=`&ns=${e}`);a.v&&(f+=`&twt=${a.v}&mwt=${a.D}`);a.C&&(f+=`&lvhr=${a.C}`);a.A&&(f+=`&${\"imn\"}=${a.A}`);a.B&&(f+=`&${\"ima\"}=${a.B}`);google.log(\"\",\"\",f)},m=function(a){a.A!==a.H||a.I||(a.i.art=Date.now(),l(a))},n=function(a){a.B===a.M&&k(a)},p=function(a,b,c){var d=a.g.t.querySelectorAll(\"[data-subtree]\");for(const f of d){var e=f.dataset.subtree;e&&(d=`${\"saft\"}-${e}`,e=`${\"sart\"}-${e}`,f.contains(b)||b.contains(f))&&(a.j[d]=c,a.j[e]=c)}},q=function(a,b,c){var d=google.cv(b,!1,!0,a.g.t);d&1?(d=Date.now(),c&&(a.C=\nc),a.i.aaft=d,a.i.aafct=d,p(a,b,d)):d&4&&(a.N=!0,n(a))},r=function(a,b,c,d=!1){b.getAttribute(\"data-deferred\")===\"1\"?google.rll(b,!0,()=>{r(a,b,c,d)}):d||(b.getAttribute(\"data-deferred\")===\"2\"&&b.setAttribute(\"data-deferred\",\"3\"),d=!0,t(a,b,c))},u=function(a,b,c){q(a,b,c);b=b.getElementsByTagName(\"img\");for(c=0;c<b.length;c++){const d=b[c];++a.A;const e=d.complete&&!d.getAttribute(\"data-deferred\"),f=d.hasAttribute(\"data-noaft\");if(e||f){++a.H;continue}const g=!!(google.cv(d,!1,!0,a.g.t)&1);g&&++a.B;google.rll(d,!0,()=>{r(a,d,g)});a.i.irfi||(a.i.irfi=Date.now());a.i.irli=Date.now()}},t=function(a,b,c){const d=Date.now();a.i.irfie||(a.i.irfie=d);++a.H;c&&(++a.M,a.i.aaft=d,a.i.aafit=d,p(a,b,d));a.N&&n(a);a.S&&m(a)},v=function(a,b,c){var d=a.g.t.querySelectorAll(\"[data-subtree]\");for(const f of d){var e=f.dataset.subtree;if(!e)continue;d=`${\"sirt\"}-${e}`;const g=`${\"sart\"}-${e}`;e=`${\"scrt\"}-${e}`;b.contains(f)?(a.j[d]||(a.j[d]=c),a.j[g]=c,a.j[e]=c):f.contains(b)&&(a.j[g]=c,a.j[e]=c)}},w=class{constructor(a){this.g=\na;this.L=Date.now();this.j={};this.i={};this.M=this.B=this.v=this.H=this.A=this.G=this.D=this.C=0}};\nfunction x(a){a.req=new XMLHttpRequest;a.cbfd=[];a.cbvi=[]}function y(a){x(a);const b=new z(a);google.ia.was(a,()=>{b.g.req.addEventListener(\"readystatechange\",b.J);b.g.req.open(\"GET\",b.g.url);b.i.ipf=Date.now();b.g.req.send()})}\nvar A=function(a){a.s===0&&a.R&&google.drty&&google.drty()},B=function(a){if(!a.O){a.O=!0;if(Array.isArray(a.g.cbfd))for(const b of a.g.cbfd)b();a.g.cbfd=h}},D=function(a,b){a.I=!0;a.i.ft=Date.now();a.v=0;a.g.req.removeEventListener(\"readystatechange\",a.J);a.g.req.abort();l(a);google.fce(a.g.t,a.g.cee,b);B(a)},K=function(a,b,c){const d=b[0],e=b[1];if(!a.T&&d===2&&e==null)return E(a,c),a.T=!0,a.g.t;switch(d){case 1:D(a,Error(c));break;case 2:return F(a,e,c);case 6:return G(a,e,c);case 3:google.ia.dq.push({astyp:a.g.astyp,cb:()=>{H(a,e,c,!0)}});break;case 4:a.s++;google.ia.dq.push({astyp:a.g.astyp,cb:()=>{I(c);a.s--;A(a)}});break;case 7:J(a,c);break;case 5:google.ia.dq.push({astyp:a.g.astyp,md:c})}},E=function(a,b){const c=Date.now();a.i.st=c;a.g.t.innerHTML=b;v(a,a.g.t,c);L(a.g.t);a.g.t.setAttribute(\"eid\",a.K[0]);u(a,a.g.t,++a.l)},F=function(a,b,c){b=document.getElementById(b);b.innerHTML=c;a.l++;for(const d of b.children)q(a,d,a.l);v(a,b,Date.now());L(b);return b},G=function(a,b,c){const d=a.g.t.querySelector(`[data-async-ph=\"${b}\"]`);if(d)return M(a,d,c);a.s++;google.ia.dq.push({astyp:a.g.astyp,cb:()=>{M(a,a.g.t.querySelector(`[data-async-ph=\"${b}\"]`),c);a.s--;A(a)}})},H=function(a,b,c,d){let e;const f=(e=document.getElementById(b))!=null?e:a.g.t.querySelector(`img[data-iid=\"${b}\"]`);!f&&d?a.g.cbfd.push(()=>{google.ia.dq.push({astyp:a.g.astyp,cb:()=>{H(a,b,c,!1)}})}):(f.setAttribute(\"data-deferred\",\"2\"),f.src=c)},I=function(a){const b=document.createElement(\"script\");b.textContent=a;document.body.appendChild(b)},J=function(a,b){const c=document.createElement(\"style\");c.appendChild(document.createTextNode(b));a.g.t.appendChild(c)},L=function(a){a=a.querySelectorAll('script:not([type]),script[type=\"text/javascript\"]');for(const b of a)I(b.text)},M=function(a,b,c){var d=document.createElement(\"div\");d.innerHTML=c;c=Array.from(d.getElementsByTagName(\"script\"),f=>f.text);for(var e=document.createDocumentFragment();d.firstChild;)e.appendChild(d.firstChild);d=Array.from(e.children);b.parentElement.replaceChild(e,b);e=Date.now();a.l++;for(const f of c)I(f);for(const f of d)v(a,f,e),u(a,f,a.l);return b},z=class extends w{constructor(a){super(a);this.J=this.P.bind(this);this.s=this.l=this.cursor=0;a.cbvi.push(()=>{this.R=!0;A(this)})}P(){const a=this.g.req.readyState;try{if(a>=2&&(this.i.ipfr||(this.i.ttfb=this.i.ipfr=Date.now()),this.g.req.status!==200)){D(this,Error(`${this.g.req.status}`));return}if(a<3)return;this.G=performance.now();const c=this.g.req.responseText,d=c.length;if(d>=5){if(this.cursor===0){if(c.substring(0,5)!==\")]}'\\n\"){D(this,Error(\"a\"));return}this.cursor=5}let e;for(;!this.I;){e=c.indexOf(\";\",this.cursor);if(e===-1)break;const g=parseInt(c.substring(this.cursor,e),16);if(isNaN(g))break;if(d<e+g)break;var b=c.substring(e+1,e+g+1);if(this.K)if(this.F){const N=this.F;this.F=void 0;const C=K(this,N,b);C&&google.drty&&google.drty(C)}else this.F=JSON.parse(b);else this.K=JSON.parse(b);this.cursor=e+g+1}const f=performance.now()-this.G;f>this.D&&(this.D=f);this.v+=f;this.G=0}}catch(c){D(this,c);return}a===\n4&&(this.i.ipfrl=this.i.acrt=Date.now(),this.i.aaft||(this.i.aaft=this.i.acrt),this.S=!0,m(this),B(this),n(this))}};google.ia.mi[1]==null&&(google.ia.mi[1]=y);}).call(this);</script>     <script nonce=\"CP311SD5BVmtqozsXvXMVQ\">(function(){var p={astyp:'hpba',cee:'kvZGKe',ck:'_sNsXZ8SAEsff5OUPju6tyAE_8sNsXZ8SAEsff5OUPju6tyAE',cp:'x',id:'_sNsXZ8SAEsff5OUPju6tyAE_8',m:1,url:'/async/hpba?yv\\x3d3\\x26cs\\x3d1\\x26ei\\x3dsNsXZ8SAEsff5OUPju6tyAE\\x26async\\x3d_basejs:/xjs/_/js/k%3Dxjs.hd.en.0mZg4onwq2I.es5.O/am%3DAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgAAAAAAAAAEBCgAAAAAgAIeAAAAgAAAAAAAAAAAABBICAAAGgAABAAAAMACAACBAAAIAAAAFBAAAAVIAHiUCQCACAASAAAAAAAEAIAANAQAAEAAAAAGAAAAAQAEAAAAFAAAAAAAAAAAAAAAAAAAAAQQIAAAAAAAAAAAAABAAgAAANADAAAAAAAAAAAEAAAgCAAA7JABCAAAAAAAANAHAMEDYEjhAQAAAAAAAAAAAAAAApggmAsJCAhAAAAAAAAAAAAAAAAAAEhJExcm/dg%3D0/rs%3DACT90oFkMsRkHqYDY758QJVs9U3tVH4BiA,_basecss:/xjs/_/ss/k%3Dxjs.hd.3osHYkwsEnY.L.F4.O/am%3DJFUAAAAAAAAAAGAAAAAAAAAAAAAAAAAAAAAAAAAQAAAAAgAAAAAAAAAABUBAAgAAjACebACAjgEAAAAIAAAMAABAAAAAGgAAIAAAAYACAAAAAAAgAAAEBBECAABFAAAAACBACBAAQAAFEAIEkIAANATxKAQAAAAGAAAIAWAEwwAEFQCMAgQAAAAAAAAgACEAAAAQAQACAAB-BADAAgDSAgCAINADgAAAAAAAAQAQAAIAQEwA7JABCAAAAAAAAEAGAAAAAACAAAAAAAAAAAAAAAAAAAggAAgACAAAAAAAAAAAAAAAAAAAAAAB/rs%3DACT90oFgzNy81TfImOPzePJpkFp_tYRRrg,_basecomb:/xjs/_/js/k%3Dxjs.hd.en.0mZg4onwq2I.es5.O/ck%3Dxjs.hd.3osHYkwsEnY.L.F4.O/am%3DJFUAAAAAAAAAAGAAAAAAAAAAAAAAAAAAAAAAAAAQAAAAAgAAAAAAAAAEBWhAAgAAjAKebACAjgEAAAAIAAAMABBICAAAGgAAJAAAAcACAACBAAAoAAAEFBECAAVNAHiUCSDACBASQAAFEAIEkIAANATxKEQAAAAGAAAIAWAEwwAEFQCMAgQAAAAAAAAgACEAAAQQIQACAAB-BADAAgDSAgCAINADgAAAAAAAAQAUAAIgSEwA7JABCAAAAAAAANAHAMEDYEjhAQAAAAAAAAAAAAAAApggmAsJCAhAAAAAAAAAAAAAAAAAAEhJExcm/d%3D1/ed%3D1/dg%3D0/ujg%3D1/rs%3DACT90oEFokshLJTGBEuzmJ4fqUwMoWpnfg,_fmt:prog,_id:_sNsXZ8SAEsff5OUPju6tyAE_8\\x26sp_imghp\\x3dfalse\\x26sp_hpep\\x3d2\\x26sp_hpte\\x3d0',wfpe:false,wfve:false};(function(){'use strict';google.ia.q(p);}).call(this);})();</script>    </div><div><div jscontroller=\"ms4mZb\" jsname=\"FAeNCb\" data-aipm=\"1\" data-eaip=\"true\" data-endpoint=\"1\" data-istweed=\"true\" data-oaipm=\"0\" id=\"_sNsXZ8SAEsff5OUPju6tyAE_9\" data-jiis=\"up\" data-async-type=\"hpba\" class=\"yf\" jsaction=\"rcuQ6b:npT2md\" data-ved=\"0ahUKEwjEmLvfvKKJAxXHL7kGHQ53CxkQj-0KCB0\" eid=\"sdsXZ4rxA5O85OUP8IWesQc\"><div jsname=\"Nll0ne\"></div></div></div><div class=\"vcVZ7d\" id=\"gws-output-pages-elements-homepage_additional_languages__als\"><style>#gws-output-pages-elements-homepage_additional_languages__als{font-size:small;margin-bottom:24px}#SIvCob{color:var(--bbQxAb);display:inline-block;line-height:28px;}#SIvCob a{padding:0 3px;}.H6sW5{display:inline-block;margin:0 2px;white-space:nowrap}.z4hgWe{display:inline-block;margin:0 2px}</style><div id=\"SIvCob\">Google offered in:  <a href=\"https://www.google.com/setprefs?sig=0_BbXVXwFAWv0qjN_np0VJ7-2S8yw%3D&amp;hl=es-419&amp;source=homepage&amp;sa=X&amp;ved=0ahUKEwjEmLvfvKKJAxXHL7kGHQ53CxkQ2ZgBCB8\">Español (Latinoamérica)</a>    <a href=\"https://www.google.com/setprefs?sig=0_BbXVXwFAWv0qjN_np0VJ7-2S8yw%3D&amp;hl=qu&amp;source=homepage&amp;sa=X&amp;ved=0ahUKEwjEmLvfvKKJAxXHL7kGHQ53CxkQ2ZgBCCA\">Quechua</a>  </div></div></div><div jscontroller=\"B2qlPe\" jsname=\"cgsKlb\" jsaction=\"rcuQ6b:npT2md\"><style>.ceE3R{opacity:0.06;pointer-events:none}</style></div><div class=\"o3j99 c93Gbe\" role=\"contentinfo\"><style>.c93Gbe{background:#171717}.uU7dJb{padding:15px 30px;border-bottom:1px solid var(--gS5jXb);font-size:15px;color:var(--YLNNHc);display:flex;flex-wrap:wrap;justify-content:space-between}.Wymece{justify-content:flex-end}.SSwjIe{padding:0 20px}.KxwPGc{display:flex;flex-wrap:wrap;justify-content:space-between}@media only screen and (max-width:1200px){.KxwPGc{justify-content:space-evenly}}.pHiOh{display:block;padding:15px;white-space:nowrap}.pHiOh,a.pHiOh{color:var(--YLNNHc)}</style><div class=\"uU7dJb\">Bolivia</div><div jscontroller=\"NzU6V\" class=\"KxwPGc SSwjIe\" data-sfe=\"false\" data-sfsw=\"1200\" jsaction=\"rcuQ6b:npT2md\"><div class=\"KxwPGc AghGtd\"><a class=\"pHiOh\" href=\"https://about.google/?utm_source=google-BO&amp;utm_medium=referral&amp;utm_campaign=hp-footer&amp;fg=1\" data-sb=\"/url?sa=t&amp;rct=j&amp;source=webhp&amp;url=https://about.google/%3Futm_source%3Dgoogle-BO%26utm_medium%3Dreferral%26utm_campaign%3Dhp-footer%26fg%3D1&amp;ved=0ahUKEwjEmLvfvKKJAxXHL7kGHQ53CxkQkNQCCCE&amp;bl=W56S&amp;opi=********&amp;usg=AOvVaw1thtAlUx14KPcwQ0HEZ_nE\">About</a><a class=\"pHiOh\" href=\"https://www.google.com/intl/en_bo/ads/?subid=ww-ww-et-g-awa-a-g_hpafoot1_1!o2&amp;utm_source=google.com&amp;utm_medium=referral&amp;utm_campaign=google_hpafooter&amp;fg=1\" data-sb=\"/url?sa=t&amp;rct=j&amp;source=webhp&amp;url=https://www.google.com/intl/en_bo/ads/%3Fsubid%3Dww-ww-et-g-awa-a-g_hpafoot1_1!o2%26utm_source%3Dgoogle.com%26utm_medium%3Dreferral%26utm_campaign%3Dgoogle_hpafooter%26fg%3D1&amp;ved=0ahUKEwjEmLvfvKKJAxXHL7kGHQ53CxkQkdQCCCI&amp;bl=W56S&amp;opi=********&amp;usg=AOvVaw0YLA7eKOo8do0eXAdgE3_l\">Advertising</a><a class=\"pHiOh\" href=\"https://www.google.com/services/?subid=ww-ww-et-g-awa-a-g_hpbfoot1_1!o2&amp;utm_source=google.com&amp;utm_medium=referral&amp;utm_campaign=google_hpbfooter&amp;fg=1\" data-sb=\"/url?sa=t&amp;rct=j&amp;source=webhp&amp;url=https://www.google.com/services/%3Fsubid%3Dww-ww-et-g-awa-a-g_hpbfoot1_1!o2%26utm_source%3Dgoogle.com%26utm_medium%3Dreferral%26utm_campaign%3Dgoogle_hpbfooter%26fg%3D1&amp;ved=0ahUKEwjEmLvfvKKJAxXHL7kGHQ53CxkQktQCCCM&amp;bl=W56S&amp;opi=********&amp;usg=AOvVaw2SJ4zwRVXKyZr53qG9dm4K\">Business</a><a class=\"pHiOh\" href=\"https://google.com/search/howsearchworks/?fg=1\"> How Search works </a></div><div class=\"KxwPGc iTjxkf\"><a class=\"pHiOh\" href=\"https://policies.google.com/privacy?hl=en-BO&amp;fg=1\" data-sb=\"/url?sa=t&amp;rct=j&amp;source=webhp&amp;url=https://policies.google.com/privacy%3Fhl%3Den-BO%26fg%3D1&amp;ved=0ahUKEwjEmLvfvKKJAxXHL7kGHQ53CxkQ8awCCCQ&amp;bl=W56S&amp;opi=********&amp;usg=AOvVaw0XiLy9-raNWmpfU0aiTsKh\">Privacy</a><a class=\"pHiOh\" href=\"https://policies.google.com/terms?hl=en-BO&amp;fg=1\" data-sb=\"/url?sa=t&amp;rct=j&amp;source=webhp&amp;url=https://policies.google.com/terms%3Fhl%3Den-BO%26fg%3D1&amp;ved=0ahUKEwjEmLvfvKKJAxXHL7kGHQ53CxkQ8qwCCCU&amp;bl=W56S&amp;opi=********&amp;usg=AOvVaw2M1dJzNpp_z0c-BggyGQim\">Terms</a><span><style>.ayzqOc:hover{text-decoration:underline}</style><span jscontroller=\"nabPbb\" data-ffp=\"false\" jsaction=\"KyPa0e:Y0y4c;BVfjhf:VFzweb;wjOG7e:gDkf4c;\"><style>.cF4V5c{background-color:var(--xhUGwc)}.cF4V5c g-menu-item{display:block;font-size:14px;line-height:23px;white-space:nowrap}.cF4V5c g-menu-item a,.cF4V5c .y0fQ9c{display:block;padding-top:4px;padding-bottom:4px;cursor:pointer}.cF4V5c g-menu-item a,.cF4V5c g-menu-item a:visited,.cF4V5c g-menu-item a:hover{text-decoration:inherit;color:inherit}</style><g-popup jsname=\"V68bde\" jscontroller=\"DPreE\" jsaction=\"A05xBd:IYtByb;EOZ57e:WFrRFb;\" jsdata=\"mVjAjf;_;AT2l+M\"><div jsname=\"oYxtQd\" class=\"CcNe6e\" aria-expanded=\"false\" aria-haspopup=\"true\" role=\"button\" tabindex=\"0\" jsaction=\"WFrRFb;keydown:uYT2Vb\"><div jsname=\"LgbsSe\" class=\"ayzqOc pHiOh\" aria-controls=\"_sNsXZ8SAEsff5OUPju6tyAE_10\" aria-haspopup=\"true\">Settings</div></div><div jsname=\"V68bde\" class=\"UjBGL pkWBse iRQHZe\" style=\"display:none;z-index:200\"><g-menu jsname=\"xl07Ob\" class=\"cF4V5c yTik0 PBn44e iQXTJe wplJBd\" jscontroller=\"WlNQGd\" role=\"menu\" tabindex=\"-1\" jsaction=\"PSl28c;focus:h06R8;keydown:uYT2Vb;mouseenter:WOQqYb;mouseleave:Tx5Rb;mouseover:IgJl9c\"><g-menu-item jsname=\"NNJLud\" jscontroller=\"CnSW2d\" class=\"EpPYLd GZnQqe\" role=\"none\" data-short-label=\"\" jsdata=\"zPXzie;_;AT2l+Q\"><div jsname=\"ibnC6b\" class=\"YpcDnf OSrXXb HG1dvd\" role=\"none\"><a href=\"https://www.google.com/preferences?hl=en-BO&amp;fg=1\" role=\"menuitem\" tabindex=\"-1\" data-sb=\"/url?sa=t&amp;rct=j&amp;source=webhp&amp;url=https://www.google.com/preferences%3Fhl%3Den-BO%26fg%3D1&amp;ved=0ahUKEwjEmLvfvKKJAxXHL7kGHQ53CxkQs4gOCCg&amp;bl=W56S&amp;opi=********&amp;usg=AOvVaw1sBzhpL0_fSwTLumgCKNte\">Search settings</a></div></g-menu-item><g-menu-item jsname=\"NNJLud\" jscontroller=\"CnSW2d\" class=\"EpPYLd GZnQqe\" role=\"none\" data-short-label=\"\" jsdata=\"zPXzie;_;AT2l+Q\"><div jsname=\"ibnC6b\" class=\"YpcDnf OSrXXb HG1dvd\" role=\"none\"><a href=\"/advanced_search?hl=en-BO&amp;fg=1\" role=\"menuitem\" tabindex=\"-1\" data-sb=\"/url?sa=t&amp;rct=j&amp;source=webhp&amp;url=/advanced_search%3Fhl%3Den-BO%26fg%3D1&amp;ved=0ahUKEwjEmLvfvKKJAxXHL7kGHQ53CxkQ3ogOCCk&amp;bl=W56S&amp;opi=********&amp;usg=AOvVaw14HAGJlkBWHvbSYWJMrX8I\">Advanced search</a></div></g-menu-item><g-menu-item jsname=\"NNJLud\" jscontroller=\"CnSW2d\" class=\"EpPYLd GZnQqe\" role=\"none\" data-short-label=\"\" jsdata=\"zPXzie;_;AT2l+Q\"><div jsname=\"ibnC6b\" class=\"YpcDnf OSrXXb HG1dvd\" role=\"none\"><a href=\"https://myactivity.google.com/privacyadvisor/search?utm_source=googlemenu&amp;fg=1\" role=\"menuitem\" tabindex=\"-1\" data-sb=\"/url?sa=t&amp;rct=j&amp;source=webhp&amp;url=https://myactivity.google.com/privacyadvisor/search%3Futm_source%3Dgooglemenu%26fg%3D1&amp;ved=0ahUKEwjEmLvfvKKJAxXHL7kGHQ53CxkQgoYOCCo&amp;bl=W56S&amp;opi=********&amp;usg=AOvVaw22bX-TAn8QNT52lDsC8IUa\">Your data in Search</a></div></g-menu-item><g-menu-item jsname=\"NNJLud\" jscontroller=\"CnSW2d\" class=\"EpPYLd GZnQqe\" role=\"none\" data-short-label=\"\" jsdata=\"zPXzie;_;AT2l+Q\"><div jsname=\"ibnC6b\" class=\"YpcDnf OSrXXb HG1dvd\" role=\"none\"><a href=\"https://myactivity.google.com/product/search?utm_source=google&amp;hl=en-BO&amp;fg=1\" role=\"menuitem\" tabindex=\"-1\" data-sb=\"/url?sa=t&amp;rct=j&amp;source=webhp&amp;url=https://myactivity.google.com/product/search%3Futm_source%3Dgoogle%26hl%3Den-BO%26fg%3D1&amp;ved=0ahUKEwjEmLvfvKKJAxXHL7kGHQ53CxkQ4YgOCCs&amp;bl=W56S&amp;opi=********&amp;usg=AOvVaw3GRMoTc543RgncVdbvX2Mi\">Search history</a></div></g-menu-item><g-menu-item jsname=\"NNJLud\" jscontroller=\"CnSW2d\" class=\"EpPYLd GZnQqe\" role=\"none\" data-short-label=\"\" jsdata=\"zPXzie;_;AT2l+Q\"><div jsname=\"ibnC6b\" class=\"YpcDnf OSrXXb HG1dvd\" role=\"none\"><a href=\"https://support.google.com/websearch/?p=ws_results_help&amp;hl=en-BO&amp;fg=1\" role=\"menuitem\" tabindex=\"-1\" data-sb=\"/url?sa=t&amp;rct=j&amp;source=webhp&amp;url=https://support.google.com/websearch/%3Fp%3Dws_results_help%26hl%3Den-BO%26fg%3D1&amp;ved=0ahUKEwjEmLvfvKKJAxXHL7kGHQ53CxkQ4IgOCCw&amp;bl=W56S&amp;opi=********&amp;usg=AOvVaw2UOORHitqZlfJ5ECKX4leV\">Search help</a></div></g-menu-item><g-menu-item jsname=\"NNJLud\" jscontroller=\"CnSW2d\" class=\"EpPYLd GZnQqe\" role=\"none\" data-short-label=\"\" jsdata=\"zPXzie;_;AT2l+Q\"><div jsname=\"ibnC6b\" class=\"YpcDnf OSrXXb HG1dvd\" role=\"none\"><span data-bucket=\"websearch\" role=\"menuitem\" tabindex=\"-1\" jsaction=\"trigger.YcfJ\" data-ved=\"0ahUKEwjEmLvfvKKJAxXHL7kGHQ53CxkQ4ogOCC0\">Send feedback</span></div></g-menu-item><g-menu-item jsname=\"NNJLud\" jscontroller=\"CnSW2d\" class=\"EpPYLd GZnQqe LGiluc\" aria-hidden=\"true\" role=\"separator\" data-short-label=\"\" jsdata=\"zPXzie;_;AT2l+U\"></g-menu-item><g-menu-item jsname=\"NNJLud\" jscontroller=\"CnSW2d\" class=\"EpPYLd GZnQqe\" role=\"none\" data-short-label=\"\" jsdata=\"zPXzie;_;AT2l+Q\"><div jsname=\"ibnC6b\" class=\"YpcDnf OSrXXb HG1dvd\" role=\"none\"><div class=\"y0fQ9c\" data-spl=\"/setprefs?hl=en&amp;prev=https://www.google.com/?pccc%3D1&amp;sig=0_BbXVXwFAWv0qjN_np0VJ7-2S8yw%3D&amp;cs=1\" id=\"YUIDDb\" role=\"menuitem\" tabindex=\"-1\"><style>.tFYjZe{align-items:center;display:flex;justify-content:space-between;padding-bottom:4px;padding-top:4px}.tFYjZe:hover .iOHNLb,.tFYjZe:focus .iOHNLb{opacity:1}.iOHNLb{color:#9e9e9e;height:20px;margin-top:-2px;opacity:0;width:20px}</style><div jscontroller=\"fXO0xe\" class=\"tFYjZe\" data-bsdm=\"0\" data-btf=\"0\" data-hbc=\"#8ab4f8\" data-htc=\"#202124\" data-spt=\"1\" data-tsdm=\"0\" role=\"link\" tabindex=\"0\" jsaction=\"ok5gFc;x6BCfb:ggFCce;w3Ukrf:aelxJb\" data-ved=\"0ahUKEwjEmLvfvKKJAxXHL7kGHQ53CxkQqsEHCC4\"><div>Dark theme: On</div><div class=\"iOHNLb\"><span style=\"height:20px;line-height:20px;width:20px\" class=\"z1asCe tSAV7\"><svg focusable=\"false\" xmlns=\"http://www.w3.org/2000/svg\" enable-background=\"new 0 0 24 24\" height=\"24\" viewBox=\"0 0 24 24\" width=\"24\"><rect fill=\"none\" height=\"24\" width=\"24\"></rect><path d=\"M12,3c-4.97,0-9,4.03-9,9s4.03,9,9,9s9-4.03,9-9c0-0.46-0.04-0.92-0.1-1.36c-0.98,1.37-2.58,2.26-4.4,2.26 c-2.98,0-5.4-2.42-5.4-5.4c0-1.81,0.89-3.42,2.26-4.4C12.92,3.04,12.46,3,12,3L12,3z\"></path></svg></span></div></div></div></div></g-menu-item></g-menu></div></g-popup></span></span></div></div><div jscontroller=\"zGLm3b\" style=\"display:none\" data-pcs=\"2\" jsaction=\"rcuQ6b:npT2md\"></div></div></div><div class=\"Fgvgjc\"><style>.Fgvgjc{height:0;overflow:hidden}</style><div class=\"gTMtLb fp-nh\" id=\"lb\"><style>.gTMtLb{z-index:1001;position:absolute;top:-1000px}</style></div><span style=\"display:none\"><span jscontroller=\"DhPYme\" style=\"display:none\" data-atsd=\"10\" data-db=\"1\" data-du=\"1\" data-mmcnt=\"100\" jsaction=\"rcuQ6b:npT2md\" data-ei=\"sNsXZ8SAEsff5OUPju6tyAE\"></span></span><script nonce=\"CP311SD5BVmtqozsXvXMVQ\">this.gbar_=this.gbar_||{};(function(_){var window=this;\ntry{\n_.Od=function(a,b,c){if(!a.j)if(c instanceof Array)for(var d of c)_.Od(a,b,d);else{d=(0,_.z)(a.C,a,b);const e=a.v+c;a.v++;b.dataset.eqid=e;a.B[e]=d;b&&b.addEventListener?b.addEventListener(c,d,!1):b&&b.attachEvent?b.attachEvent(\"on\"+c,d):a.o.log(Error(\"B`\"+b))}};\n}catch(e){_._DumpException(e)}\ntry{\nvar Pd=document.querySelector(\".gb_I .gb_A\"),Qd=document.querySelector(\"#gb.gb_Rc\");Pd&&!Qd&&_.Od(_.yd,Pd,\"click\");\n}catch(e){_._DumpException(e)}\ntry{\n_.uh=function(a){const b=[];let c=0;for(const d in a)b[c++]=a[d];return b};_.vh=function(a){if(a.v)return a.v;for(const b in a.i)if(a.i[b].ma()&&a.i[b].B())return a.i[b];return null};_.wh=function(a,b){a.i[b.K()]=b};var xh=new class extends _.R{constructor(){var a=_.hd;super();this.B=a;this.v=null;this.o={};this.C={};this.i={};this.j=null}A(a){this.i[a]&&(_.vh(this)&&_.vh(this).K()==a||this.i[a].R(!0))}Ra(a){this.j=a;for(const b in this.i)this.i[b].ma()&&this.i[b].Ra(a)}jc(a){return a in this.i?this.i[a]:null}};_.Bd(\"dd\",xh);\n}catch(e){_._DumpException(e)}\ntry{\n_.Oi=function(a,b){return _.M(a,36,b)};\n}catch(e){_._DumpException(e)}\ntry{\nvar Pi=document.querySelector(\".gb_y .gb_A\"),Qi=document.querySelector(\"#gb.gb_Rc\");Pi&&!Qi&&_.Od(_.yd,Pi,\"click\");\n}catch(e){_._DumpException(e)}\n})(this.gbar_);\n// Google Inc.\nthis.gbar_=this.gbar_||{};(function(_){var window=this;\ntry{\nvar Rd;Rd=class extends _.Cd{};_.Sd=function(a,b){if(b in a.i)return a.i[b];throw new Rd;};_.Td=function(a){return _.Sd(_.zd.i(),a)};\n}catch(e){_._DumpException(e)}\ntry{\n/*\n\n Copyright Google LLC\n SPDX-License-Identifier: Apache-2.0\n*/\nvar Wd;_.Ud=function(a){const b=a.length;if(b>0){const c=Array(b);for(let d=0;d<b;d++)c[d]=a[d];return c}return[]};Wd=function(a){return new _.Vd(b=>b.substr(0,a.length+1).toLowerCase()===a+\":\")};_.Xd=globalThis.trustedTypes;_.Yd=class{constructor(a){this.i=a}toString(){return this.i}};_.Zd=new _.Yd(\"about:invalid#zClosurez\");_.Vd=class{constructor(a){this.Wg=a}};_.$d=[Wd(\"data\"),Wd(\"http\"),Wd(\"https\"),Wd(\"mailto\"),Wd(\"ftp\"),new _.Vd(a=>/^[^:]*([/?#]|$)/.test(a))];_.ae=class{constructor(a){this.i=a}toString(){return this.i+\"\"}};_.be=new _.ae(_.Xd?_.Xd.emptyHTML:\"\");\n}catch(e){_._DumpException(e)}\ntry{\nvar fe,te,ee,ge,le;_.ce=function(a){return a==null?a:Number.isFinite(a)?a|0:void 0};_.de=function(a){if(a==null)return a;if(typeof a===\"string\"){if(!a)return;a=+a}if(typeof a===\"number\")return Number.isFinite(a)?a|0:void 0};fe=function(){let a=null;if(!ee)return a;try{const b=c=>c;a=ee.createPolicy(\"ogb-qtm#html\",{createHTML:b,createScript:b,createScriptURL:b})}catch(b){}return a};_.he=function(){ge===void 0&&(ge=fe());return ge};\n_.je=function(a){const b=_.he();return new _.ie(b?b.createScriptURL(a):a)};_.ke=function(a){if(a instanceof _.ie)return a.i;throw Error(\"F\");};_.me=function(a){if(le.test(a))return a};_.ne=function(a){if(a instanceof _.Yd)if(a instanceof _.Yd)a=a.i;else throw Error(\"F\");else a=_.me(a);return a};_.oe=function(a,b=document){let c,d;b=(d=(c=\"document\"in b?b.document:b).querySelector)==null?void 0:d.call(c,`${a}[nonce]`);return b==null?\"\":b.nonce||b.getAttribute(\"nonce\")||\"\"};\n_.pe=function(a){var b=_.Qa(a);return b==\"array\"||b==\"object\"&&typeof a.length==\"number\"};_.qe=function(a,b,c){return _.Ab(a,b,c,!1)!==void 0};_.re=function(a,b){return _.de(_.Yc(a,b))};_.S=function(a,b){return _.ce(_.Yc(a,b))};_.T=function(a,b,c=0){return _.Bb(_.re(a,b),c)};_.se=function(a,b,c=0){return _.Bb(_.S(a,b),c)};_.ue=function(a,b){return a.lastIndexOf(b,0)==0};ee=_.Xd;_.ie=class{constructor(a){this.i=a}toString(){return this.i+\"\"}};le=/^\\s*(?!javascript:)(?:[\\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;var Ae,Ee,ve;_.xe=function(a){return a?new ve(_.we(a)):te||(te=new ve)};_.ye=function(a,b){return typeof b===\"string\"?a.getElementById(b):b};_.U=function(a,b){var c=b||document;if(c.getElementsByClassName)a=c.getElementsByClassName(a)[0];else{c=document;var d=b||c;a=d.querySelectorAll&&d.querySelector&&a?d.querySelector(a?\".\"+a:\"\"):_.ze(c,a,b)[0]||null}return a||null};\n_.ze=function(a,b,c){var d;a=c||a;if(a.querySelectorAll&&a.querySelector&&b)return a.querySelectorAll(b?\".\"+b:\"\");if(b&&a.getElementsByClassName){var e=a.getElementsByClassName(b);return e}e=a.getElementsByTagName(\"*\");if(b){var f={};for(c=d=0;a=e[c];c++){var g=a.className;typeof g.split==\"function\"&&_.va(g.split(/\\s+/),b)&&(f[d++]=a)}f.length=d;return f}return e};\n_.Be=function(a,b){_.Gb(b,function(c,d){d==\"style\"?a.style.cssText=c:d==\"class\"?a.className=c:d==\"for\"?a.htmlFor=c:Ae.hasOwnProperty(d)?a.setAttribute(Ae[d],c):_.ue(d,\"aria-\")||_.ue(d,\"data-\")?a.setAttribute(d,c):a[d]=c})};Ae={cellpadding:\"cellPadding\",cellspacing:\"cellSpacing\",colspan:\"colSpan\",frameborder:\"frameBorder\",height:\"height\",maxlength:\"maxLength\",nonce:\"nonce\",role:\"role\",rowspan:\"rowSpan\",type:\"type\",usemap:\"useMap\",valign:\"vAlign\",width:\"width\"};\n_.Ce=function(a){return a?a.defaultView:window};_.Fe=function(a,b){var c=b[1],d=_.De(a,String(b[0]));c&&(typeof c===\"string\"?d.className=c:Array.isArray(c)?d.className=c.join(\" \"):_.Be(d,c));b.length>2&&Ee(a,d,b);return d};Ee=function(a,b,c){function d(g){g&&b.appendChild(typeof g===\"string\"?a.createTextNode(g):g)}for(var e=2;e<c.length;e++){var f=c[e];!_.pe(f)||_.Qb(f)&&f.nodeType>0?d(f):_.lc(f&&typeof f.length==\"number\"&&typeof f.item==\"function\"?_.Ud(f):f,d)}};\n_.Ge=function(a){return _.De(document,a)};_.De=function(a,b){b=String(b);a.contentType===\"application/xhtml+xml\"&&(b=b.toLowerCase());return a.createElement(b)};_.He=function(a){for(var b;b=a.firstChild;)a.removeChild(b)};_.Ie=function(a){return a&&a.parentNode?a.parentNode.removeChild(a):null};_.Je=function(a,b){return a&&b?a==b||a.contains(b):!1};_.we=function(a){return a.nodeType==9?a:a.ownerDocument||a.document};ve=function(a){this.i=a||_.t.document||document};_.n=ve.prototype;\n_.n.H=function(a){return _.ye(this.i,a)};_.n.Xa=function(a,b,c){return _.Fe(this.i,arguments)};_.n.appendChild=function(a,b){a.appendChild(b)};_.n.oe=_.He;_.n.Nf=_.Ie;_.n.Mf=_.Je;\n}catch(e){_._DumpException(e)}\ntry{\n_.Ui=function(a){const b=_.oe(\"script\",a.ownerDocument&&a.ownerDocument.defaultView||window);b&&a.setAttribute(\"nonce\",b)};_.Vi=function(a){if(!a)return null;a=_.I(a,4);var b;a===null||a===void 0?b=null:b=_.je(a);return b};_.Wi=class extends _.Q{constructor(a){super(a)}};_.Xi=function(a,b){return(b||document).getElementsByTagName(String(a))};\n}catch(e){_._DumpException(e)}\ntry{\nvar Zi=function(a,b,c){a<b?Yi(a+1,b):_.hd.log(Error(\"ea`\"+a+\"`\"+b),{url:c})},Yi=function(a,b){if($i){const c=_.Ge(\"SCRIPT\");c.async=!0;c.type=\"text/javascript\";c.charset=\"UTF-8\";c.src=_.ke($i);_.Ui(c);c.onerror=_.Tb(Zi,a,b,c.src);_.Xi(\"HEAD\")[0].appendChild(c)}},aj=class extends _.Q{constructor(a){super(a)}};var bj=_.E(_.td,aj,17)||new aj,cj,$i=(cj=_.E(bj,_.Wi,1))?_.Vi(cj):null,dj,ej=(dj=_.E(bj,_.Wi,2))?_.Vi(dj):null,fj=function(){Yi(1,2);if(ej){const a=_.Ge(\"LINK\");a.setAttribute(\"type\",\"text/css\");a.href=_.ke(ej).toString();a.rel=\"stylesheet\";let b=_.oe(\"style\",window);b&&a.setAttribute(\"nonce\",b);_.Xi(\"HEAD\")[0].appendChild(a)}};(function(){const a=_.ud();if(_.G(a,18))fj();else{const b=_.re(a,19)||0;window.addEventListener(\"load\",()=>{window.setTimeout(fj,b)})}})();\n}catch(e){_._DumpException(e)}\n})(this.gbar_);\n// Google Inc.\n</script><div><div></div></div><div style=\"display:'none'\" jscontroller=\"KHourd\" jsdata=\"C4mkuf;_;AT2l+Y\" jsaction=\"rcuQ6b:npT2md\" data-ved=\"0ahUKEwjEmLvfvKKJAxXHL7kGHQ53CxkQuqMJCC8\"></div></div><textarea class=\"csi\" name=\"csi\" style=\"display:none\"></textarea><script nonce=\"CP311SD5BVmtqozsXvXMVQ\">(function(){google.caft=function(a){if(google.aftq===null)try{a()}catch(b){google.ml(b,!1)}else google.aftq=google.aftq||[],google.aftq.push(a)};window.google=window.google||{};google.c.iim=google.c.iim||{};(function(){var a=Date.now();google.tick(\"load\",\"prt\",a);performance&&performance.mark&&performance.mark(\"SearchBodyEnd\");google.c.e(\"load\",\"imn\",String(document.getElementsByTagName(\"img\").length));google.c.ub();google.c.cae||google.c.maft(a,null);google.c.miml(a);google.rll(window,!1,function(){google.tick(\"load\",\"old\")});google.c.u(\"prt\")})();}).call(this);(function(){window.google=window.google||{};window.google.ishk=[];function a(){return window.scrollY+window.document.documentElement.clientHeight>=Math.max(document.body.scrollHeight,document.body.offsetHeight)}function b(){a()&&window.google.ishk.length===0&&(window.google.bs=!0,window.removeEventListener(\"scroll\",b))}a()?window.google.bs=!0:(window.google.bs=!1,window.addEventListener(\"scroll\",b));}).call(this);(function(){google.jl={bfl:0,dw:false,ine:false,ubm:false,uwp:true,vs:false};})();(function(){var pmc='{\\x22aa\\x22:{},\\x22abd\\x22:{\\x22abd\\x22:false,\\x22deb\\x22:false,\\x22det\\x22:false},\\x22async\\x22:{},\\x22cdos\\x22:{\\x22cdobsel\\x22:false},\\x22csi\\x22:{},\\x22d\\x22:{},\\x22gf\\x22:{\\x22pid\\x22:196,\\x22si\\x22:true},\\x22hsm\\x22:{},\\x22jsa\\x22:{\\x22csi\\x22:true,\\x22csir\\x22:100},\\x22mb4ZUb\\x22:{},\\x22pHXghd\\x22:{},\\x22sb_wiz\\x22:{\\x22rfs\\x22:[],\\x22scq\\x22:\\x22\\x22,\\x22stok\\x22:\\x22sgKP3ud2-JKvl5gf_4EsV_GpyKQ\\x22,\\x22ueh\\x22:\\x223cc5bbba_25d23ec3_17c26104_50658fc4_e8346b67\\x22},\\x22sf\\x22:{},\\x22spch\\x22:{\\x22ae\\x22:\\x22Please check your microphone.  \\\\u003Ca href\\x3d\\\\\\x22https://support.google.com/chrome/?p\\x3dui_voice_search\\\\\\x22 target\\x3d\\\\\\x22_blank\\\\\\x22\\\\u003ELearn more\\\\u003C/a\\\\u003E\\x22,\\x22ak\\x22:\\x22AIzaSyBm7NubC-Swn1nt2nhYfxb58eCdmL2vCVU\\x22,\\x22ao\\x22:true,\\x22cd\\x22:0,\\x22fp\\x22:true,\\x22hl\\x22:\\x22en-BO\\x22,\\x22im\\x22:\\x22Click \\\\u003Cb\\\\u003EAllow\\\\u003C/b\\\\u003E to start voice search\\x22,\\x22iw\\x22:\\x22Waiting...\\x22,\\x22lm\\x22:\\x22Listening...\\x22,\\x22lu\\x22:\\x22%1$s voice search not available\\x22,\\x22mb\\x22:false,\\x22mc\\x22:false,\\x22ne\\x22:\\x22No Internet connection\\x22,\\x22nt\\x22:\\x22Didn\\x27t get that. \\\\u003Cspan\\\\u003ETry again\\\\u003C/span\\\\u003E\\x22,\\x22nv\\x22:\\x22Please check your microphone and audio levels.  \\\\u003Ca href\\x3d\\\\\\x22https://support.google.com/chrome/?p\\x3dui_voice_search\\\\\\x22 target\\x3d\\\\\\x22_blank\\\\\\x22\\\\u003ELearn more\\\\u003C/a\\\\u003E\\x22,\\x22pe\\x22:\\x22Voice search has been turned off.  \\\\u003Ca href\\x3d\\\\\\x22https://support.google.com/chrome/?p\\x3dui_voice_search\\\\\\x22 target\\x3d\\\\\\x22_blank\\\\\\x22\\\\u003EDetails\\\\u003C/a\\\\u003E\\x22,\\x22ri\\x22:false,\\x22rm\\x22:\\x22Speak now\\x22,\\x22s3\\x22:true,\\x22sa\\x22:false}}';google.pmc=JSON.parse(pmc);})();(function(){var r=['sb_wiz','aa','abd','async','pHXghd','sf','spch'];google.plm(r);})();(function(){var m={\"AT2l+E\":[\"gws-wiz\",\"\",\"\",\"\",null,1,0,0,13,\"en\",null,\"3cc5bbba25d23ec317c2610450658fc4e8346b67\",\"sNsXZ8SAEsff5OUPju6tyAE\",0,\"en-BO\",null,null,null,3,5,8,null,\"********\",null,1,0,1800000,1,-1,null,1.15,1,0,0,null,1,1,0,null,null,null,null,0,0,null,\"\",0,1,null,null,null,0,0,null,null,null,null,\"\",1,null,1,-1,-1,null,1,0,1,1000,1,[\"gws-wiz\",\"\",\"\"],null,[\"gws-wiz-modeless-local\",\"\",\"\"],null,[\"img\",\"gws-wiz-img\",\"i\"],null,[\"gws-wiz-modeless-products\",\"\",\"sh\"],null,[\"gws-wiz-modeless\",\"gws-wiz-perspectives\",\"\"],null,[\"gws-wiz-modeless-lodging\",\"\",\"\"],null,[\"gws-wiz-modeless-flights\",\"\",\"\"],1,null,0,1,null,null,null,[\"gws-wiz-modeless-vss-products\",\"\",\"\"],null,[\"gws-wiz-modeless-vss-jobs\",\"\",\"\"],null,[\"gws-wiz-modeless-vss-local-travel\",\"\",\"\"],1,null,null,[\"gws-wiz-modeless-video\",\"\",\"v\"],null,null,null,[\"gws-wiz-modeless-jobs\",\"\",\"\"],null,[\"gws-wiz-modeless-vss-flights\",\"\",\"\"],null,[\"gws-wiz-modeless\",\"\",\"\"],null,null,null,[\"gws-wiz-modeless-shopping\",\"\",\"sh\"],null,null,null,0,[\"multimodal-lens-web\",\"\",\"\"],[\"gws-wiz-modeless\",\"\",\"\"],0,0,[\"gws-wiz-modeless-forums\",\"\",\"\"],[\"gws-wiz-modeless-short-videos\",\"\",\"\"],20000000],\"AT2l+I\":[null,null,null,\"autocomplete_user_feedback_kp_id\",null,11,null,null,null,null,null,5010715,\"searchbox\",null,\"AutocompletePrediction\",null,null,null,null,11],\"AT2l+M\":[3,6,null,null,1,1,0,0,0,0,0,0,0,0],\"AT2l+Q\":[\"\",6,0],\"AT2l+U\":[\"\",4,0],\"AT2l+Y\":[null,null,1,null,null,null,null,null,null,null,3,null,null,null,null,null,null,null,null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,1,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,1,1,null,null,null,null,null,null,null,null,1,\"UNKNOWN_BROWSER\"]};var a=m;if(window.W_jd)for(var b in a)window.W_jd[b]=a[b];else window.W_jd=a;})();(function(){window.WIZ_global_data={\"QrtxK\":\"0\",\"oxN3nb\":{\"1\":false},\"SNlM0e\":\"AKlEn5jNEK3jTjVGto5C0W5vy4V0:**********367\",\"w2btAe\":\"%.@.\\\"113591880695572659218\\\",\\\"113591880695572659218\\\",\\\"0\\\",null,null,null,1]\",\"LVIXXb\":\"1\",\"Im6cmf\":\"/wizrpcui/_/WizRpcUi\",\"STfaRd\":\"{}\",\"eptZe\":\"/wizrpcui/_/WizRpcUi/\",\"Yllh3e\":\"%.@.**********294980,112799687,420181774]\",\"S6lZl\":\"********\",\"S06Grb\":\"113591880695572659218\",\"GWsdKe\":\"en-BO\",\"zChJod\":\"%.@.]\"};window.IJ_values={\"vSjUZd\":24,\"bs2drc\":false,\"yys2yc\":false,\"toVELc\":\"#f8f9fa\",\"XuC5Td\":24,\"ivyWed\":28,\"UlHllb\":1.0,\"osNyZ\":1.0,\"mtmrtb\":\"1px 1px 15px 0px #171717\",\"U2GTk\":\"#202124\",\"WgRLme\":\"#3c4043\",\"I6R5lf\":\"#303134\",\"vzRvgb\":\"#8ab4f8\",\"uD3Lwc\":\"#aecbfa\",\"m7EnTc\":\"#d2e3fc\",\"wFGKdc\":true,\"klgere\":\"none\",\"VBSc8c\":true,\"zsYZK\":\"#3c4043\",\"wsRfI\":false,\"QOuvIc\":\"#8ab4f8\",\"SOm4o\":false,\"U6xP0\":\"#4285f4\",\"A5tF3b\":false,\"TxsTcf\":\"#fff\",\"v4iQCe\":\"#4487f6\",\"QbZklb\":\"#394457\",\"Fcb4A\":\"#8ab4f8\",\"QDXUyc\":\"#81c995\",\"JQWqub\":\"#ff7d70\",\"rZLJJb\":\"#202124\",\"GJQmmf\":\"#1aa863\",\"hETIfb\":\"#3c4043\",\"NtNjtd\":\"#3c4043\",\"vCsrw\":\"#3c4043\",\"p9416c\":\"#050607\",\"toQ7tf\":\"#0b0d0e\",\"xgY1nc\":\"#050505\",\"p1ocJb\":\"#0a0a0a\",\"FCLfBe\":\"#111\",\"fP2Yo\":\"#9aa0a6\",\"mknyu\":\"#9aa0a6\",\"PUenT\":\"#bdc1c6\",\"Z0DEKf\":\"#bdc1c6\",\"oHHKwf\":\"#bdc1c6\",\"xNPzic\":\"#202124\",\"uiKEV\":\"#e8eaed\",\"BPltf\":\"#3c4043\",\"m8l8td\":\"CARET\",\"qdoinb\":\"#9aa0a6\",\"Fpi7Rc\":\"Arial,sans-serif-medium,sans-serif\",\"a2ykac\":\"Arial,sans-serif\",\"ME4NMc\":\"#bdc1c6\",\"BpPAcd\":\"#292e36\",\"N0wyZ\":\"#bdc1c6\",\"CQvMbe\":\"#48a1ff\",\"PngPbb\":\"#bdc1c6\",\"esUgv\":\"#fff\",\"MoAfyf\":\"#000\",\"n4eEIc\":true,\"tqmosb\":\"#202124\",\"HjM8R\":\"#303134\",\"SATNMc\":\"1px solid #3c4043\",\"X1bUEc\":\"Arial,sans-serif-medium,sans-serif\",\"QZheGe\":\"Google Sans,Arial,sans-serif-medium,sans-serif\",\"LIYDac\":\"Arial,sans-serif\",\"mNmrAb\":\"#3c4043\",\"x0VCkc\":\"1px solid #3c4043\",\"JuqxTb\":\"#bdc1c6\",\"E6Gkjd\":\"0 2px 10px 0 rgba(0,0,0,0.2)\",\"V6eh7c\":16,\"sKPNrc\":\"#3c4043\",\"AgJzQ\":\"#3c4043\",\"FagChc\":\"#171717\",\"tCGJz\":\"#303134\",\"oqx7yb\":\"#9aa0a6\",\"khoEPb\":\"#8ab4f8\",\"SfSmD\":\"#3c4043\",\"auaxA\":\"#bdc1c6\",\"qtDmFc\":\"#3c4043\",\"v44rSc\":\"#9aa0a6\",\"d1ULv\":\"#3c4043\",\"NNBneb\":\"#9aa0a6\",\"VXIo7d\":\"8px\",\"EiEfXb\":\"#3c4043\",\"zhkRO\":\"%.@.0,0,0,0,1,0,0,0,null,1,null,0,0,null,0,1,0,\\\"/setprefs?sig\\\\u003d0_BbXVXwFAWv0qjN_np0VJ7-2S8yw%3D\\\\u0026szl\\\\u003d0\\\",0,1]\",\"w2btAe\":\"%.@.\\\"113591880695572659218\\\",\\\"113591880695572659218\\\",\\\"0\\\",null,null,null,1]\",\"pxO4Zd\":\"0\",\"mXOY5d\":\"%.@.null,1,1,null,[null,757,1440]]\",\"SsQ4x\":\"CP311SD5BVmtqozsXvXMVQ\",\"IYFWl\":\"%.@.\\\"#424548\\\"]\",\"Ht1O2b\":\"%.@.0]\",\"d6J1ld\":\"%.@.0]\",\"Oo3dKf\":\"%.@.\\\"0px 5px 26px 0px rgba(0,0,0,0.5),0px 20px 28px 0px rgba(0,0,0,0.5)\\\",\\\"#202124\\\"]\",\"uUBnEb\":\"\",\"nfxEDe\":\"%.@.[],0,null,1,1]\",\"auIt8\":\"%.@.0,0]\",\"YPqjbf\":\"%.@.\\\"#bdc1c6\\\",\\\"#202124\\\",\\\"1px 1px 15px 0px #171717\\\",\\\"1px solid #5f6368\\\",\\\"#9aa0a6\\\"]\",\"MuJWjd\":false,\"GWsdKe\":\"en-BO\",\"frJqAd\":\"%.@.\\\"13px\\\",\\\"16px\\\",\\\"11px\\\",13,16,11,\\\"8px\\\",8,20]\",\"N1ycab\":\"en_BO\",\"AB5Xwb\":\"%.@.\\\"10px\\\",10,\\\"16px\\\",16,\\\"18px\\\"]\",\"Z8HLFf\":\"%.@.\\\"14px\\\",14]\",\"ymaOI\":\"%.@.40,32,14,\\\"\\\\\\\"#dadce0\\\\\\\"\\\"]\",\"fNpQmb\":\"\",\"aMI2mb\":\"%.@.\\\"1px 1px 15px 0px #171717\\\"]\",\"BZUDzc\":\"%.@.0,\\\"14px\\\",\\\"500\\\",\\\"500\\\",\\\"0 1px 1px rgba(0,0,0,.16)\\\",\\\"pointer\\\",\\\"#fff\\\",null,\\\"#9aa0a6\\\",\\\"#bdc1c6\\\",null,null,null,\\\"rgba(102,102,102,.4)\\\",\\\"#1aa863\\\",\\\"#4487f6\\\",\\\"#8ab4f8\\\",\\\"#ff7d70\\\",\\\"#8a4a00\\\",\\\"#111\\\",\\\"#050505\\\",\\\"#bdc1c6\\\",\\\"#4f861f\\\",\\\"rgba(255,255,255,.12)\\\",null,\\\"#202124\\\",null,\\\"#000\\\",\\\"#bdc1c6\\\",null,null,0]\",\"v7Qvdc\":\"%.@.\\\"20px\\\",\\\"500\\\",\\\"400\\\",\\\"13px\\\",\\\"15px\\\",\\\"15px\\\",\\\"Roboto,RobotoDraft,Helvetica,Arial,sans-serif\\\",\\\"24px\\\",\\\"400\\\",\\\"32px\\\",\\\"24px\\\"]\",\"MgUcDb\":\"BO\",\"SIsrTd\":false,\"fyLpDc\":\"\",\"JPnTp\":\"%.@.\\\"#3c4043\\\",\\\"36px\\\"]\",\"ZxtPCd\":\"%.@.{\\\"100\\\":\\\"12px\\\",\\\"101\\\":\\\"8px\\\",\\\"102\\\":\\\"8px\\\",\\\"103\\\":\\\"10px\\\",\\\"104\\\":\\\"9px\\\"}]\",\"DwYRY\":\"%.@.\\\"var(--rrJJUc)\\\",\\\"32px\\\",null,null,\\\"#dadce0\\\",\\\"#5f6368\\\",\\\"transparent\\\",\\\"transparent\\\",\\\"#d2e3fc\\\",\\\"#44474661\\\",\\\"#bfbfbf61\\\",\\\"#20212433\\\",\\\"#20212433\\\",\\\"#20212466\\\",\\\"#a8c7fa15\\\",\\\"#a8c7fa15\\\",\\\"#a8c7fa39\\\",\\\"#a8c7fa15\\\",\\\"#a8c7fa15\\\",\\\"#a8c7fa39\\\",\\\"#a8c7fa15\\\",\\\"#a8c7fa15\\\",\\\"#a8c7fa39\\\",\\\"#eef0ff33\\\",\\\"#eef0ff39\\\",\\\"#eef0ff66\\\",\\\"rgba(232,232,232,0.08)\\\",\\\"rgba(232,232,232,0.08)\\\",\\\"rgba(232,232,232,0.24)\\\"]\",\"NyzCwe\":\"%.@.null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\\\"18px\\\",\\\"20px\\\",\\\"18px\\\",\\\"#dadce0\\\",\\\"16px\\\",\\\"var(--IXoxUe)\\\",\\\"var(--YLNNHc)\\\"]\",\"spz2q\":\"%.@.\\\"var(--xhUGwc)\\\",null,null,null,null,\\\"0px\\\"]\",\"geiQxc\":\"%.@.\\\"234px\\\",\\\"176px\\\",\\\"204px\\\",\\\"172px\\\",\\\"128px\\\",\\\"148px\\\",\\\"111px\\\",\\\"83px\\\",\\\"92px\\\"]\",\"xFmcof\":\"%.@.null,null,null,\\\"20px\\\",null,\\\"12px\\\"]\",\"lDqiof\":\"%.@.\\\"var(--YLNNHc)\\\",\\\"var(--bbQxAb)\\\",\\\"var(--rrJJUc)\\\",null,\\\"var(--IXoxUe)\\\",\\\"var(--JKqx2)\\\",\\\"#c58af9\\\",null,null,\\\"var(--xhUGwc)\\\",\\\"var(--Nsm0ce)\\\",\\\"var(--EpFNW)\\\",\\\"#394457\\\",\\\"#eef0ff\\\",\\\"#28292a\\\",\\\"#e8e8e8\\\",\\\"#fff\\\",\\\"#3c4043\\\",\\\"#202124\\\",\\\"#fff\\\",\\\"#202124\\\",\\\"#fff\\\",\\\"#81c995\\\",\\\"#f28b82\\\",\\\"#fdd663\\\",\\\"var(--mXZkqc)\\\",\\\"#1f1f1f\\\",\\\"rgba(0,0,0,0.6)\\\",\\\"var(--COEmY)\\\",\\\"var(--gS5jXb)\\\",\\\"#8ab4f8\\\",null,\\\"var(--TMYS9)\\\",\\\"#9e9e9e\\\",null,\\\"transparent\\\",\\\"#313335\\\",\\\"rgba(0,0,0,0.03)\\\",null,null,null,null,null,null,null,null,null,\\\"#ea4335\\\",\\\"#34a853\\\",\\\"#4285f4\\\",\\\"#fbbc04\\\",\\\"#fdd663\\\",\\\"#80868b\\\",\\\"#171717\\\",null,null,null,null,\\\"#394457\\\",\\\"var(--XKMDxc)\\\",\\\"var(--aYn2S)\\\",null,\\\"var(--Aqn7xd)\\\",null,\\\"#f2b8b5\\\",\\\"#8c1d18\\\",\\\"#f4bf00\\\",\\\"#ffdf92\\\",\\\"#601410\\\",\\\"#f9dedc\\\",\\\"#241a00\\\",\\\"#241a00\\\",\\\"#0a3818\\\",\\\"#c4eed0\\\",\\\"#f2b8b5\\\",\\\"#6dd58c\\\",\\\"#6dd58c\\\",\\\"#0f5223\\\",\\\"var(--nwXobb)\\\",\\\"var(--vZe0jb)\\\",\\\"var(--QWaaaf)\\\",\\\"var(--ZEpPmd)\\\",\\\"#5c5f5e\\\",\\\"var(--Lm570b)\\\",\\\"#303134\\\",\\\"var(--jINu6c)\\\",\\\"var(--BRLwE)\\\",\\\"var(--DEeStf)\\\",\\\"var(--TSWZIb)\\\",\\\"var(--uLz37c)\\\",null,\\\"#c3c6d6\\\",\\\"var(--TyVYld)\\\",null,null,null,null,\\\"var(--VuZXBd)\\\",\\\"rgba(23,23,23,0.75)\\\",{\\\"100\\\":\\\"rgba(48,49,52,0.94)\\\",\\\"101\\\":\\\"rgba(255,255,255,0.8)\\\",\\\"102\\\":\\\"rgba(23,23,23,0.6)\\\",\\\"103\\\":\\\"#d2e3fc\\\",\\\"104\\\":\\\"#bdc1c6\\\",\\\"105\\\":\\\"#303134\\\"}]\",\"Gpnz4c\":\"%.@.\\\"#3c4665\\\",\\\"#2d2f31\\\",\\\"#444746\\\",\\\"#b1c5ff\\\",\\\"#c3c6d6\\\",\\\"#eef0ff\\\",null,\\\"#2c303d\\\",\\\"#eef0ff\\\",\\\"#3a3f50\\\",\\\"#202124\\\",\\\"#9e9e9e\\\",\\\"#bfbfbf\\\",\\\"#e8e8e8\\\",\\\"#a6c9fc\\\",\\\"#444746\\\",null,\\\"#a8c7fa\\\",\\\"#34517d\\\",\\\"#eef0ff\\\",\\\"#333438\\\",\\\"#28292a\\\",\\\"#1f1f1f\\\",null,\\\"#303134\\\",\\\"#3c4665\\\",\\\"#f5f8ff\\\",\\\"#424654\\\",\\\"#424654\\\",null,\\\"#242832\\\",\\\"#b1c5ff\\\",\\\"#c3c6d6\\\",\\\"#eef0ff\\\",null,\\\"#2c303d\\\",\\\"#eef0ff\\\",\\\"#3a3f50\\\",\\\"#202124\\\",\\\"#fff\\\",\\\"#c3c6d6\\\",\\\"#eef0ff\\\",\\\"#eef0ff\\\",\\\"#b1c5ff\\\",\\\"#424654\\\",\\\"#a8c7fa\\\",null,\\\"rgba(0,0,0,0.6)\\\",\\\"#34517d\\\",\\\"#eef0ff\\\",\\\"#1f1f1f\\\",\\\"#242832\\\",\\\"#3a3f50\\\",\\\"#3a3f50\\\",\\\"#446eff\\\",\\\"#b1c5ff\\\",\\\"#779ad2\\\"]\",\"kXVUzd\":\"%.@.\\\"40px\\\",\\\"48px\\\"]\",\"DT34Qc\":\"%.@.\\\"44px\\\",\\\"rgba(138,180,248,0.24)\\\",\\\"36px\\\",\\\"#3c4043\\\",20,18,16,\\\"#e8eaed\\\"]\",\"sCU50d\":\"%.@.null,\\\"none\\\",null,\\\"0px 1px 3px rgba(23,23,23,0.24)\\\",null,\\\"0px 2px 6px rgba(23,23,23,0.32)\\\",null,\\\"0px 4px 12px rgba(23,23,23,0.9)\\\",null,null,\\\"1px solid #5f6368\\\",\\\"0\\\",\\\"0\\\",\\\"0\\\",null,\\\"0px 1px 3px rgba(95,99,104,0.64)\\\",\\\"0\\\"]\",\"w9Zicc\":\"%.@.\\\"#fff\\\",\\\"26px\\\",\\\"#2a4165\\\",\\\"#e2eeff\\\",\\\"#2a4165\\\",\\\"1px\\\",\\\"#9aa0a6\\\",\\\"1px\\\",\\\"#fff\\\",\\\"#9aa0a6\\\",null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\\\"28px\\\",\\\"10px\\\",\\\"8px\\\",\\\"20px\\\",\\\"10px\\\",\\\"#fff\\\",\\\"#5c5f5e\\\",\\\"#fff\\\",\\\"#fff\\\",\\\"#5e5e5e\\\",\\\"var(--TSWZIb)\\\",\\\"var(--TMYS9)\\\"]\",\"IkSsrf\":\"%.@.\\\"Google Sans,Arial,sans-serif\\\",\\\"Google Sans,Arial,sans-serif-medium,sans-serif\\\",\\\"Arial,sans-serif\\\",\\\"Arial,sans-serif-medium,sans-serif\\\",\\\"Arial,sans-serif-light,sans-serif\\\"]\",\"MR0w4\":\"%.@.\\\"var(--google-fs,1)\\\",\\\"var(--google-screen-scale-ratio,1)\\\"]\",\"OItNqf\":\"%.@.null,\\\"24px\\\"]\",\"JMyuH\":\"%.@.]\",\"j2FoS\":\"%.@.null,null,null,null,null,null,null,null,\\\"20px\\\"]\",\"e2zoW\":\"%.@.\\\"16px\\\",\\\"12px\\\",\\\"0px\\\",\\\"8px\\\",\\\"4px\\\",\\\"2px\\\",\\\"20px\\\",\\\"24px\\\",\\\"48px\\\",\\\"20px\\\",null,null,\\\"0px\\\",\\\"20px\\\",\\\"36px\\\",\\\"20px\\\",null,\\\"83px\\\",\\\"52px\\\",\\\"6px\\\",\\\"20px\\\",\\\"18px\\\",\\\"16px\\\",\\\"24px\\\",\\\"12px\\\",\\\"6px\\\",\\\"75px\\\",\\\"52px\\\",\\\"940.98px\\\",\\\"684px\\\",\\\"683.98px\\\",\\\"1163.98px\\\",\\\"941px\\\",\\\"1560px\\\",\\\"1164px\\\",\\\"940.98px\\\",\\\"800px\\\"]\",\"W1Bte\":\"%.@.\\\"cubic-bezier(0.1,1,0.2,1)\\\",\\\"cubic-bezier(0.8,0,1,0.8)\\\",\\\"cubic-bezier(0.2,0.6,0.2,1)\\\",\\\"cubic-bezier(0.4,0,1,0.8)\\\",\\\"300ms\\\",\\\"100ms\\\",\\\"200ms\\\",\\\"250ms\\\",\\\"cubic-bezier(0.4,0,0.2,1)\\\",\\\"cubic-bezier(0.4,0,0.6,1)\\\",\\\"cubic-bezier(0.6,0,0,1)\\\",\\\"cubic-bezier(0,0,1,1)\\\",\\\"cubic-bezier(0.2,0,0,1)\\\",\\\"800ms\\\",\\\"1000ms\\\",\\\"400ms\\\",\\\"500ms\\\",\\\"600ms\\\",\\\"50ms\\\",\\\"400ms\\\",\\\"300ms\\\",\\\"250ms\\\",\\\"150ms\\\",\\\"250ms\\\",\\\"200ms\\\",\\\"150ms\\\",\\\"150ms\\\",\\\"300ms\\\",\\\"250ms\\\",\\\"200ms\\\",\\\"150ms\\\",\\\"450ms\\\",\\\"450ms\\\",\\\"300ms\\\",\\\"150ms\\\",\\\"300ms\\\",\\\"250ms\\\",\\\"200ms\\\",\\\"100ms\\\",\\\"250ms\\\",\\\"200ms\\\",\\\"150ms\\\",\\\"100ms\\\",\\\"250ms\\\",\\\"200ms\\\",\\\"150ms\\\",\\\"100ms\\\",\\\"300ms\\\",\\\"250ms\\\",\\\"200ms\\\",\\\"100ms\\\",\\\"null\\\",\\\"cubic-bezier(0.3,0,0.8,0.15)\\\",\\\"cubic-bezier(0.05,0.7,0.1,1)\\\",\\\"cubic-bezier(0,0,1,1)\\\",\\\"cubic-bezier(0.2,0,0,1)\\\",\\\"cubic-bezier(0.3,0,1,1)\\\",\\\"cubic-bezier(0,0,0,1)\\\",\\\"250ms\\\",\\\"200ms\\\",\\\"150ms\\\",\\\"50ms\\\",\\\"50ms\\\",\\\"50ms\\\",\\\"400ms\\\",\\\"350ms\\\",\\\"250ms\\\",\\\"50ms\\\",\\\"50ms\\\",\\\"50ms\\\",\\\"200ms\\\",\\\"150ms\\\",\\\"100ms\\\",\\\"50ms\\\",\\\"200ms\\\",\\\"150ms\\\",\\\"100ms\\\",\\\"50ms\\\",\\\"50ms\\\",\\\"50ms\\\",\\\"250ms\\\",\\\"200ms\\\",\\\"150ms\\\",\\\"50ms\\\",\\\"50ms\\\",\\\"50ms\\\",\\\"cubic-bezier(0.05,0.7,0.1,1)\\\",\\\"cubic-bezier(0.3,0,0.8,0.15)\\\"]\",\"pbvshf\":\"%.@.\\\"48px\\\"]\",\"u9mep\":\"%.@.\\\"#8ab4f8\\\",null,\\\"#e8e8e8\\\",\\\"var(--JKqx2)\\\"]\",\"mrqaQb\":\"%.@.null,null,null,null,\\\"2px\\\",\\\"12px\\\"]\",\"k7Tqye\":\"%.@.null,null,null,null,null,null,null,\\\"16px\\\",\\\"12px\\\",\\\"8px\\\",\\\"20px\\\",\\\"4px\\\",\\\"9999px\\\",\\\"0px\\\",\\\"2px\\\"]\",\"y50LC\":\"%.@.null,null,\\\"#bdc1c6\\\",null,\\\"#bcc0c3\\\"]\",\"jfSEkd\":\"%.@.\\\"var(--Pa8Wlb)\\\",\\\"var(--KIZPne)\\\",\\\"var(--xPpiM)\\\",null,\\\"rgba(255,255,255,0.08)\\\",\\\"rgba(255,255,255,0.08)\\\",\\\"rgba(255,255,255,0.24)\\\",\\\"var(--pEa0Bc)\\\",\\\"var(--Yi4Nb)\\\",\\\"var(--kloG3)\\\",\\\"var(--YaIeMb)\\\",\\\"var(--izGsqb)\\\",\\\"var(--todMNcl)\\\",\\\"rgba(255,255,255,0.16)\\\",null,\\\"rgba(255,255,255,0.4)\\\",\\\"#2c303d\\\",\\\"#eef0ff\\\",\\\"rgba(189,193,198,0.08)\\\",\\\"rgba(189,193,198,0.08)\\\",\\\"rgba(189,193,198,0.24)\\\",\\\"transparent\\\",\\\"var(--rrJJUc)\\\",null,\\\"rgba(138,180,248,0.08)\\\",\\\"rgba(138,180,248,0.08)\\\",\\\"rgba(138,180,248,0.24)\\\",null,null,null,\\\"rgba(189,193,198,0.08)\\\",\\\"rgba(189,193,198,0.08)\\\",\\\"rgba(189,193,198,0.24)\\\",\\\"2px\\\",\\\"2px\\\",\\\"61\\\",\\\"var(--bbQxAb)\\\",\\\"var(--BRLwE)\\\",null,\\\"#b1c5ff\\\",\\\"var(--XKMDxc)\\\",\\\"var(--YLNNHc)\\\",\\\"#9e9e9e\\\",\\\"var(--YLNNHc)\\\",\\\"var(--YLNNHc)\\\",\\\"15\\\",\\\"0\\\",\\\"39\\\",\\\"var(--jINu6c)\\\",\\\"rgba(255,255,255,0.16)\\\",\\\"33\\\",\\\"39\\\",\\\"var(--EpFNW)\\\",\\\"66\\\",\\\"#eef0ff\\\",\\\"#9e9e9e\\\",\\\"0.08\\\",\\\"var(--bbQxAb)\\\",\\\"0.08\\\",\\\"#9e9e9e\\\",\\\"var(--bbQxAb)\\\",\\\"0.24\\\",\\\"var(--IXoxUe)\\\",\\\"transparent\\\",\\\"0.08\\\",\\\"0.08\\\",\\\"var(--IXoxUe)\\\",\\\"0.24\\\",\\\"var(--IXoxUe)\\\",\\\"transparent\\\",\\\"0.08\\\",\\\"0.08\\\",\\\"var(--uLz37c)\\\",\\\"0.24\\\",\\\"var(--uLz37c)\\\",\\\"var(--jINu6c)\\\",\\\"var(--jINu6c)\\\",\\\"15\\\",\\\"#8ab4f8\\\",\\\"15\\\",\\\"#b1c5ff\\\",\\\"39\\\",\\\"#a8c7fa\\\",\\\"15\\\",\\\"15\\\",\\\"#b1c5ff\\\",\\\"#eef0ff\\\",\\\"39\\\",\\\"var(--p9J9c)\\\",\\\"var(--jINu6c)\\\",\\\"15\\\",\\\"15\\\",\\\"39\\\",\\\"#a8c7fa\\\",\\\"rgba(0,0,0,0.6)\\\",\\\"33\\\",\\\"33\\\",\\\"#fff\\\",\\\"66\\\",{\\\"100\\\":\\\"#fff\\\",\\\"101\\\":\\\"0.08\\\",\\\"102\\\":\\\"0.08\\\",\\\"103\\\":\\\"#3c4043\\\",\\\"104\\\":\\\"0.24\\\",\\\"105\\\":\\\"#202124\\\",\\\"106\\\":\\\"rgba(255,255,255,0.1)\\\",\\\"107\\\":\\\"0.08\\\",\\\"108\\\":\\\"#d2e3fc\\\",\\\"109\\\":\\\"0.08\\\",\\\"110\\\":\\\"0.24\\\",\\\"111\\\":\\\"var(--Nsm0ce)\\\",\\\"112\\\":\\\"0\\\",\\\"113\\\":\\\"transparent\\\",\\\"114\\\":\\\"0.08\\\",\\\"115\\\":\\\"0.08\\\",\\\"116\\\":\\\"var(--YLNNHc)\\\",\\\"117\\\":\\\"0.24\\\",\\\"118\\\":\\\"var(--YLNNHc)\\\",\\\"119\\\":\\\"0.75\\\",\\\"120\\\":\\\"0.6\\\",\\\"121\\\":\\\"rgba(232,232,232,0.08)\\\",\\\"122\\\":\\\"rgba(232,232,232,0.08)\\\",\\\"123\\\":\\\"rgba(232,232,232,0.24)\\\",\\\"124\\\":\\\"rgba(11,87,208,0.078431375)\\\",\\\"125\\\":\\\"rgba(11,87,208,0.078431375)\\\",\\\"126\\\":\\\"rgba(11,87,208,0.23921569)\\\",\\\"127\\\":\\\"rgba(32,33,36,0.2)\\\",\\\"128\\\":\\\"rgba(32,33,36,0.2)\\\",\\\"129\\\":\\\"rgba(32,33,36,0.4)\\\",\\\"130\\\":\\\"rgba(32,33,36,0.078431375)\\\",\\\"131\\\":\\\"rgba(32,33,36,0.078431375)\\\",\\\"132\\\":\\\"rgba(32,33,36,0.23921569)\\\",\\\"133\\\":\\\"var(--Ehh4mf)\\\"}]\",\"GVtPm\":\"%.@.null,null,null,null,null,\\\"0 0 0 1px var(--mXZkqc)\\\",\\\"1px solid var(--mXZkqc)\\\",null,null,null,null,null,null,null,null,\\\"transparent\\\",\\\"rgba(23,23,23,0.3)\\\",null,null,\\\"16px\\\"]\",\"MexNte\":\"%.@.\\\"700\\\",\\\"400\\\",\\\"underline\\\",\\\"none\\\",\\\"capitalize\\\",\\\"none\\\",\\\"uppercase\\\",\\\"none\\\",\\\"500\\\",\\\"lowercase\\\",\\\"italic\\\",null,null,\\\"-1px\\\",\\\"0.3px\\\",\\\"20px\\\",\\\"12px\\\",null,\\\"12px\\\",\\\"14px\\\",\\\"16px\\\",\\\"18px\\\",\\\"22px\\\",\\\"24px\\\",\\\"26px\\\",\\\"28px\\\",\\\"32px\\\",\\\"36px\\\",\\\"40px\\\",\\\"48px\\\",\\\"52px\\\",\\\"56px\\\",\\\"60px\\\",\\\"14px\\\",\\\"16px\\\",\\\"18px\\\",\\\"20px\\\",\\\"22px\\\",\\\"24px\\\",\\\"28px\\\",\\\"36px\\\",\\\"40px\\\",\\\"45px\\\",\\\"48px\\\",\\\"56px\\\",\\\"0\\\",\\\"0.1px\\\"]\",\"Aahcnf\":\"%.@.\\\"28px\\\",\\\"36px\\\",\\\"400\\\",\\\"Google Sans,Arial,sans-serif\\\",null,\\\"Arial,sans-serif\\\",\\\"14px\\\",\\\"400\\\",\\\"22px\\\",null,\\\"18px\\\",\\\"24px\\\",\\\"400\\\",\\\"Google Sans,Arial,sans-serif\\\",null,\\\"Google Sans,Arial,sans-serif\\\",\\\"56px\\\",\\\"48px\\\",\\\"0\\\",null,\\\"400\\\",\\\"Google Sans,Arial,sans-serif\\\",\\\"36px\\\",\\\"400\\\",\\\"48px\\\",null,\\\"Google Sans,Arial,sans-serif\\\",\\\"36px\\\",\\\"28px\\\",null,\\\"400\\\",null,\\\"Arial,sans-serif\\\",\\\"24px\\\",\\\"18px\\\",null,\\\"400\\\",\\\"Arial,sans-serif\\\",\\\"16px\\\",\\\"12px\\\",null,\\\"400\\\",\\\"Arial,sans-serif\\\",\\\"24px\\\",\\\"16px\\\",null,\\\"400\\\",\\\"Arial,sans-serif\\\",\\\"24px\\\",\\\"20px\\\",null,\\\"400\\\",\\\"Arial,sans-serif\\\",\\\"24px\\\",\\\"16px\\\",null,\\\"400\\\",\\\"Arial,sans-serif\\\",\\\"18px\\\",\\\"14px\\\",null,\\\"400\\\",null,null,null,null,null,\\\"14px\\\",\\\"Google Sans,Arial,sans-serif\\\",\\\"20px\\\",\\\"400\\\",\\\"Google Sans,Arial,sans-serif\\\",\\\"28px\\\",\\\"22px\\\",\\\"400\\\",\\\"Google Sans,Arial,sans-serif\\\",\\\"24px\\\",\\\"16px\\\",\\\"400\\\",\\\"Arial,sans-serif-medium,sans-serif\\\",\\\"16px\\\",\\\"12px\\\",\\\"Google Sans,Arial,sans-serif\\\",\\\"28px\\\",\\\"22px\\\",\\\"400\\\",null,null,null,null,\\\"500\\\",\\\"0px\\\",\\\"0px\\\",\\\"0\\\"]\",\"PFhmed\":\"%.@.\\\"rgba(31,31,31,0)\\\",\\\"rgba(31,31,31,0.9)\\\",null,\\\"rgba(168,199,250,0.08)\\\",\\\"var(--XKMDxc)\\\",\\\"rgba(232,234,237,0.5)\\\",\\\"#333438\\\",\\\"#3a3f50\\\"]\",\"mf1yif\":\"%.@.4]\",\"B4pZbd\":\"BO\",\"aKXqGc\":\"%.@.\\\"14px\\\",14,\\\"16px\\\",16,\\\"0\\\",0,null,632,\\\"1px solid #3c4043\\\",null,\\\"normal\\\",\\\"#9aa0a6\\\",\\\"12px\\\",\\\"1.34\\\",\\\"1px solid #3c4043\\\",\\\"none\\\",\\\"0\\\",\\\"none\\\",\\\"none\\\",\\\"none\\\",\\\"none\\\",\\\"6px\\\",\\\"632px\\\"]\",\"ZP0oif\":\"%.@.\\\"16px\\\",\\\"#303134\\\"]\",\"o0P8Hf\":\"%.@.\\\"rgba(255,255,255,.0)\\\",\\\"#000\\\",\\\"rgba(0,0,0,.0)\\\",null,null,null,null,null,null,\\\"#303134\\\",\\\"#fff\\\",\\\"#48a1ff\\\",\\\"#212327\\\",\\\"#000\\\",\\\"#000\\\",null,\\\"#868b90\\\",\\\"rgba(255,255,255,.26)\\\",\\\"rgba(255,255,255,.2)\\\",null,null,null,\\\"rgba(0,0,0,.1)\\\",\\\"#fff\\\",null,null,\\\"#fff\\\",null,null,null,\\\"rgba(255,255,255,.5)\\\",null,\\\"rgba(0,0,0,.3)\\\",\\\"rgba(0,0,0,.2)\\\",null,null,\\\"rgba(0,0,0,.04)\\\",null,null,\\\"#868b90\\\",\\\"#868b90\\\",\\\"rgba(0,0,0,.22)\\\",\\\"rgba(0,0,0,.30)\\\",\\\"rgba(0,0,0,.06)\\\",null,\\\"#0f2039\\\",null,\\\"rgba(221,222,225,.7)\\\",null,null,null,\\\"rgba(255,255,255,.8)\\\",\\\"rgba(60,64,67,.15)\\\",null,\\\"rgba(0,0,0,.16)\\\",null,\\\"rgba(0,0,0,.14)\\\",\\\"rgba(0,0,0,.12)\\\",null,null,\\\"rgba(0,0,0,.24)\\\",\\\"rgba(0,0,0,.05)\\\",\\\"rgba(0,0,0,.13)\\\",\\\"rgba(60,64,67,.3)\\\",\\\"rgba(0,0,0,.36)\\\",\\\"rgba(0,0,0,.15)\\\",\\\"rgba(32,33,36,.28)\\\",\\\"rgba(218,220,224,.7)\\\",\\\"#dadce0\\\",\\\"#fff\\\",\\\"#fff\\\",\\\"#1a73e8\\\",\\\"#000\\\",\\\"rgba(0,0,0,.0)\\\",\\\"#202124\\\",\\\"rgba(0,0,0,.8)\\\",\\\"rgba(26,115,232,.0)\\\",\\\"rgba(26,115,232,.7)\\\",null,\\\"rgba(32,33,36,.7)\\\",\\\"rgba(255,255,255,.8)\\\",null,null,null,null,\\\"rgba(255,255,255,.54)\\\",null,\\\"rgba(60,64,67,.38)\\\",\\\"rgba(255,255,255,.3)\\\",\\\"rgba(0,0,0,0.54)\\\",\\\"rgba(0,0,0,0.8)\\\",\\\"rgba(248,249,250,0.85)\\\",\\\"#212327\\\",\\\"#ff7769\\\",\\\"#219540\\\",null,null,\\\"#bcc0c3\\\",\\\"#dddee1\\\",{\\\"100\\\":\\\"#050607\\\",\\\"101\\\":\\\"#212327\\\",\\\"102\\\":\\\"#bcc0c3\\\",\\\"106\\\":\\\"#868b90\\\",\\\"108\\\":\\\"#050607\\\",\\\"112\\\":\\\"#dadce0\\\",\\\"113\\\":\\\"#08101e\\\",\\\"114\\\":\\\"#4487f6\\\",\\\"117\\\":\\\"#4487f6\\\",\\\"118\\\":\\\"#48a1ff\\\",\\\"121\\\":\\\"#4487f6\\\",\\\"126\\\":\\\"#08101e\\\",\\\"127\\\":\\\"#0f2039\\\",\\\"128\\\":\\\"#4487f6\\\",\\\"129\\\":\\\"#48a1ff\\\",\\\"130\\\":\\\"#230f0d\\\",\\\"131\\\":\\\"#441c19\\\",\\\"134\\\":\\\"#ff897e\\\",\\\"140\\\":\\\"#ff897e\\\",\\\"144\\\":\\\"#ff897e\\\",\\\"147\\\":\\\"#ff7769\\\",\\\"149\\\":\\\"#ffb1b1\\\",\\\"150\\\":\\\"#0f0800\\\",\\\"166\\\":\\\"#824300\\\",\\\"169\\\":\\\"#c66200\\\",\\\"171\\\":\\\"#041208\\\",\\\"180\\\":\\\"#4eb66e\\\",\\\"187\\\":\\\"#219540\\\",\\\"188\\\":\\\"#3cac5c\\\",\\\"189\\\":\\\"#4eb66e\\\",\\\"190\\\":\\\"#5ebe7e\\\",\\\"191\\\":\\\"#70c890\\\",\\\"192\\\":\\\"rgba(255,255,255,.1)\\\",\\\"193\\\":\\\"rgba(255,255,255,.2)\\\",\\\"196\\\":\\\"rgba(0,0,0,.0)\\\",\\\"197\\\":\\\"rgba(255,255,255,.12)\\\",\\\"198\\\":\\\"rgba(32,33,36,.0)\\\",\\\"199\\\":\\\"rgba(32,33,36,.1)\\\",\\\"200\\\":\\\"rgba(255,255,255,.12)\\\",\\\"201\\\":\\\"rgba(255,255,255,.5)\\\",\\\"203\\\":\\\"#fff\\\",\\\"204\\\":\\\"rgba(0,0,0,.5)\\\",\\\"205\\\":\\\"#6eb1ff\\\",\\\"207\\\":\\\"rgba(0,0,0,.24)\\\",\\\"208\\\":\\\"#f8f9fa\\\",\\\"209\\\":\\\"rgba(255,255,255,.6)\\\",\\\"210\\\":\\\"#1e8e3e\\\",\\\"211\\\":\\\"rgba(0,0,0,.02)\\\",\\\"212\\\":\\\"#000\\\",\\\"214\\\":\\\"rgba(0,0,0,.7)\\\",\\\"215\\\":\\\"#1a73e8\\\",\\\"216\\\":\\\"#d93025\\\",\\\"217\\\":\\\"#4285f4\\\",\\\"218\\\":\\\"rgba(255,255,255,.15)\\\",\\\"219\\\":\\\"rgba(255,255,255,.05)\\\",\\\"220\\\":\\\"#70757a\\\",\\\"221\\\":\\\"#dadce0\\\",\\\"222\\\":\\\"#188038\\\",\\\"223\\\":\\\"rgba(0,0,0,.6)\\\",\\\"224\\\":\\\"#34a853\\\",\\\"225\\\":\\\"rgba(255,255,255,.3)\\\",\\\"226\\\":\\\"rgba(0,0,0,.05)\\\",\\\"227\\\":\\\"rgba(0,0,0,.05)\\\",\\\"228\\\":\\\"rgba(32,33,36,.9)\\\",\\\"229\\\":\\\"rgba(0,0,0,.6)\\\",\\\"230\\\":\\\"rgba(255,255,255,.08)\\\",\\\"231\\\":\\\"rgba(0,0,0,.8)\\\",\\\"232\\\":\\\"rgba(255,255,255,.05)\\\",\\\"233\\\":\\\"#4285f4\\\",\\\"234\\\":\\\"rgba(255,255,255,.16)\\\",\\\"235\\\":\\\"#000\\\",\\\"236\\\":\\\"rgba(0,0,0,.87)\\\",\\\"238\\\":\\\"#fdd663\\\",\\\"239\\\":\\\"#fdd663\\\",\\\"243\\\":\\\"#fdd663\\\",\\\"244\\\":\\\"rgba(255,255,255,.54)\\\",\\\"246\\\":\\\"rgba(0,0,0,.26)\\\",\\\"247\\\":\\\"rgba(0,0,0,.26)\\\",\\\"248\\\":\\\"rgba(0,0,0,.38)\\\",\\\"249\\\":\\\"rgba(0,0,0,.03)\\\",\\\"250\\\":\\\"#4285f4\\\",\\\"251\\\":\\\"rgba(60,64,67,.12)\\\",\\\"252\\\":\\\"rgba(255,255,255,.0)\\\",\\\"253\\\":\\\"rgba(0,0,0,.0)\\\",\\\"256\\\":\\\"#3c4043\\\",\\\"257\\\":\\\"#d2e3fc\\\",\\\"258\\\":\\\"#d2e3fc\\\",\\\"259\\\":\\\"#4285f4\\\",\\\"261\\\":\\\"rgba(0,0,0,.16)\\\",\\\"262\\\":\\\"rgba(255,255,255,.3)\\\",\\\"263\\\":\\\"rgba(255,255,255,.0)\\\",\\\"264\\\":\\\"#c5221f\\\",\\\"265\\\":\\\"#dadce0\\\",\\\"266\\\":\\\"#ea4335\\\",\\\"267\\\":\\\"#34a853\\\",\\\"268\\\":\\\"rgba(188,192,195,.15)\\\",\\\"269\\\":\\\"rgba(94,190,126,.15)\\\",\\\"270\\\":\\\"rgba(255,255,255,.15)\\\",\\\"271\\\":\\\"rgba(255,255,255,.18)\\\",\\\"272\\\":\\\"rgba(255,255,255,.28)\\\",\\\"273\\\":\\\"rgba(188,192,195,.3)\\\",\\\"274\\\":\\\"#1558d6\\\",\\\"275\\\":\\\"rgba(33,35,39,0.0)\\\",\\\"276\\\":\\\"rgba(33,35,39,0.5)\\\",\\\"277\\\":\\\"rgba(255,255,255,.26)\\\",\\\"278\\\":\\\"#145c28\\\"}]\",\"rkD25\":\"%.@.[[\\\"hl\\\",\\\"en-BO\\\"]]]\",\"WiLzZe\":\"%.@.\\\"#dddee1\\\",\\\"#868b90\\\",null,null,\\\"#000\\\",\\\"rgba(0,0,0,.7)\\\",28,24,26,20,16,-2,0,-4,2,0,0,24,20,20,14,12]\",\"AYkLRe\":\"%.@.\\\"20px\\\",20,\\\"14px\\\",14,\\\"#e8eaed\\\"]\",\"rNyuJc\":\"<EMAIL>\",\"LU5fGb\":true,\"gXkHoe\":\"113591880695572659218\",\"hevonc\":\"%.@.1]\",\"sBvdwb\":\"%.@.\\\"rgba(26,73,232,0.1)\\\",\\\"rgba(26,73,232,0.03)\\\",\\\"rgba(0,0,0,0.05)\\\",\\\"1.3\\\"]\",\"xcljyb\":\"%.@.\\\"8px\\\",8]\"};})();(function(){google.ldi={};google.pim={};google.ldie=[];(function(){var a=google.ldi||{},b=google.pim||{},c;for(c in a)if(a.hasOwnProperty(c)){c in b&&(a[c]=b[c]);var d=document.getElementById(c)||document.documentElement.querySelector('img[data-iid=\"'+c+'\"]');d&&Number(d.getAttribute(\"data-atf\"))&1&&(d.setAttribute(\"data-deferred\",\"2\"),d.src=a[c])};}).call(this);})();(function(){for(var a=document.getElementsByTagName(\"img\"),b=0,c=a.length;b<c;++b){var d=a[b],e;if(e=Number(d.getAttribute(\"data-atf\"))&1){var f=d.getAttribute(\"jscontroller\");e=(f===\"UBXHI\"||f===\"R3fhkb\"||f===\"TSZEqd\")&&d.hasAttribute(\"data-src\")}e&&(d.src=d.getAttribute(\"data-src\"))};}).call(this);(function(){var b=function(a){var c=0;return function(){return c<a.length?{done:!1,value:a[c++]}:{done:!0}}};\nvar e=this||self;var g,h;a:{for(var k=[\"CLOSURE_FLAGS\"],l=e,n=0;n<k.length;n++)if(l=l[k[n]],l==null){h=null;break a}h=l}var p=h&&h[610401301];g=p!=null?p:!1;var q,r=e.navigator;q=r?r.userAgentData||null:null;function t(a){return g?q?q.brands.some(function(c){return(c=c.brand)&&c.indexOf(a)!=-1}):!1:!1}function u(a){var c;a:{if(c=e.navigator)if(c=c.userAgent)break a;c=\"\"}return c.indexOf(a)!=-1};function v(){return g?!!q&&q.brands.length>0:!1}function w(){return u(\"Safari\")&&!(x()||(v()?0:u(\"Coast\"))||(v()?0:u(\"Opera\"))||(v()?0:u(\"Edge\"))||(v()?t(\"Microsoft Edge\"):u(\"Edg/\"))||(v()?t(\"Opera\"):u(\"OPR\"))||u(\"Firefox\")||u(\"FxiOS\")||u(\"Silk\")||u(\"Android\"))}function x(){return v()?t(\"Chromium\"):(u(\"Chrome\")||u(\"CriOS\"))&&!(v()?0:u(\"Edge\"))||u(\"Silk\")}function y(){return u(\"Android\")&&!(x()||u(\"Firefox\")||u(\"FxiOS\")||(v()?0:u(\"Opera\"))||u(\"Silk\"))};var z=v()?!1:u(\"Trident\")||u(\"MSIE\");y();x();w();var A=!z&&!w(),D=function(a){if(/-[a-z]/.test(\"ved\"))return null;if(A&&a.dataset){if(y()&&!(\"ved\"in a.dataset))return null;a=a.dataset.ved;return a===void 0?null:a}return a.getAttribute(\"data-\"+\"ved\".replace(/([A-Z])/g,\"-$1\").toLowerCase())};var E=[],F=null;function G(a){a=a.target;var c=performance.now(),f=[],H=f.concat,d=E;if(!(d instanceof Array)){var m=typeof Symbol!=\"undefined\"&&Symbol.iterator&&d[Symbol.iterator];if(m)d=m.call(d);else if(typeof d.length==\"number\")d={next:b(d)};else throw Error(\"b`\"+String(d));for(var B=[];!(m=d.next()).done;)B.push(m.value);d=B}E=H.call(f,d,[c]);if(a&&a instanceof HTMLElement)if(a===F){if(c=E.length>=4)c=(E[E.length-1]-E[E.length-4])/1E3<5;if(c){c=google.getEI(a);a.hasAttribute(\"data-ved\")?f=a?D(a)||\"\":\"\":f=(f=\na.closest(\"[data-ved]\"))?D(f)||\"\":\"\";f=f||\"\";if(a.hasAttribute(\"jsname\"))a=a.getAttribute(\"jsname\");else{var C;a=(C=a.closest(\"[jsname]\"))==null?void 0:C.getAttribute(\"jsname\")}google.log(\"rcm\",\"&ei=\"+c+\"&tgtved=\"+f+\"&jsname=\"+(a||\"\"))}}else F=a,E=[c]}window.document.addEventListener(\"DOMContentLoaded\",function(){document.body.addEventListener(\"click\",G)});}).call(this);var w=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}};window.jsl=window.jsl||{};window.jsl.dh=function(a,b,m){try{var h=document.getElementById(a),e;if(!h&&((e=google.stvsc)==null?0:e.dds)){e=[];var f=e.concat,c=google.stvsc.dds;if(c instanceof Array)var n=c;else{var p=typeof Symbol!=\"undefined\"&&Symbol.iterator&&c[Symbol.iterator];if(p)var g=p.call(c);else if(typeof c.length==\"number\")g={next:w(c)};else throw Error(String(c)+\" is not an iterable or ArrayLike\");c=g;var q;for(g=[];!(q=c.next()).done;)g.push(q.value);n=g}var r=f.call(e,n);for(f=0;f<r.length&&!(h=r[f].getElementById(a));f++);}if(h)h.innerHTML=b,m&&m();else{var d={id:a,script:String(!!m),milestone:String(google.jslm||0)};google.jsla&&(d.async=google.jsla);var t=a.indexOf(\"_\"),k=t>0?a.substring(0,t):\"\",u=document.createElement(\"div\");u.innerHTML=b;var l=u.children[0];if(l&&(d.tag=l.tagName,d[\"class\"]=String(l.className||null),d.name=String(l.getAttribute(\"jsname\")),k)){a=[];var v=document.querySelectorAll('[id^=\"'+k+'_\"]');for(b=0;b<v.length;++b)a.push(v[b].id);d.ids=a.join(\",\")}google.ml(Error(k?\"Missing ID with prefix \"+\nk:\"Missing ID\"),!1,d)}}catch(x){google.ml(x,!0,{\"jsl.dh\":!0})}};(function(){var x=true;google.jslm=x?2:1;})();google.x(null, function(){(function(){(function(){google.csct={};google.csct.ps='AOvVaw1krpqIqcoZ7inVGXro5zDY\\x26ust\\x3d1729703216358063';})();})();(function(){(function(){google.csct.sb=true;})();})();(function(){window.jsl=window.jsl||{};window.jsl.dh=window.jsl.dh||function(i,c,d){try{var e=document.getElementById(i);if(e){e.innerHTML=c;if(d){d();}}else{if(window.jsl.el){window.jsl.el(new Error('Missing ID.'),{'id':i});}}}catch(e){if(window.jsl.el){window.jsl.el(new Error('jsl.dh'));}}};})();(function(){window.jsl.dh('spch','\\x3cstyle\\x3e.spch-dlg{background:transparent;border:none}.spch{background:var(--xhUGwc);height:100%;left:0;opacity:0;overflow:hidden;position:fixed;text-align:left;top:0;visibility:hidden;width:100%;z-index:10000;transition:visibility 0s linear 0.218s,background-color 0.218s;}.s2fp.spch{opacity:1;transition-delay:0s;visibility:visible;}.pz5bj{background:none;border:none;color:var(--IXoxUe);cursor:pointer;font-size:26px;right:0;line-height:15px;opacity:.6;margin:-1px -1px 0 0;padding:0 0 2px 0;height:48px;width:48px;position:absolute;top:0;z-index:10}.pz5bj:hover{opacity:.8}.pz5bj:active{opacity:1}.spchc{display:block;height:42px;pointer-events:none;margin:auto;position:relative;top:0;margin-top:312px;max-width:572px;min-width:534px;padding:0 223px}.inner-container{height:100%;opacity:.1;pointer-events:none;width:100%;transition:opacity .318s ease-in;}.s2ml .inner-container,.s2ra .inner-container,.s2er .inner-container,.OJaju .inner-container{opacity:1;transition:opacity 0s}\\x3c/style\\x3e\\x3cstyle\\x3e.google-logo{background:url(data:image/png;base64,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) no-repeat center;background-size:94px 32px;height:32px;width:94px;top:8px;opacity:0;float:right;left:255px;pointer-events:none;position:relative;transition:opacity .5s ease-in,left .5s ease-in}\\x3c/style\\x3e\\x3cbutton class\\x3d\\x22pz5bj\\x22 id\\x3d\\x22spchx\\x22 aria-label\\x3d\\x22close\\x22\\x3e\\x3cspan style\\x3d\\x22height:16px;line-height:16px;width:16px\\x22 class\\x3d\\x22z1asCe wuXmqc\\x22\\x3e\\x3csvg focusable\\x3d\\x22false\\x22 xmlns\\x3d\\x22http://www.w3.org/2000/svg\\x22 viewBox\\x3d\\x220 0 24 24\\x22\\x3e\\x3cpath d\\x3d\\x22M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\\x22\\x3e\\x3c/path\\x3e\\x3c/svg\\x3e\\x3c/span\\x3e\\x3c/button\\x3e\\x3cdiv class\\x3d\\x22spchc\\x22 id\\x3d\\x22spchc\\x22\\x3e\\x3cdiv class\\x3d\\x22inner-container\\x22\\x3e\\x3cdiv class\\x3d\\x22button-container\\x22\\x3e\\x3cstyle\\x3e.LgbsSe{background-color:#fff;border:1px solid #f8f9fa;border-radius:100%;bottom:0;box-shadow:0 2px 5px rgba(0,0,0,.1);cursor:pointer;display:inline-block;opacity:0;pointer-events:none;position:absolute;right:0;transition:background-color 0.218s,border 0.218s,box-shadow 0.218s;transition-delay:0;position:absolute;opacity:0;left:0;top:0}.s2fp .LgbsSe{opacity:1;pointer-events:auto;transform:scale(1);}.s2ra .LgbsSe{background-color:#ea4335;border:0;box-shadow:none}.r8s4j{background-color:#dadce0;border-radius:100%;display:inline-block;opacity:1;pointer-events:none;position:absolute;transform:scale(.01);transition:opacity 0.218s;height:301px;left:-69px;top:-69px;width:301px;}.button-container{pointer-events:none;position:relative;transition:transform 0.218s,opacity 0.218s ease-in;transform:scale(.1);height:165px;width:165px;right:-70px;top:-70px;float:right;}.s2fp .button-container{transform:scale(1)}.s2ra .LgbsSe:active{background-color:#c5221f}.LgbsSe:active{background-color:#f8f9fa}\\x3c/style\\x3e\\x3cspan class\\x3d\\x22r8s4j\\x22 id\\x3d\\x22spchl\\x22\\x3e\\x3c/span\\x3e\\x3cspan class\\x3d\\x22LgbsSe\\x22 id\\x3d\\x22spchb\\x22\\x3e\\x3cdiv class\\x3d\\x22microphone\\x22\\x3e\\x3cstyle\\x3e.microphone{height:87px;pointer-events:none;position:absolute;width:42px;top:47px;transform:scale(1);left:43px;}.receiver{background-color:#999;border-radius:30px;height:46px;left:25px;pointer-events:none;position:absolute;width:24px}.wrapper{bottom:0;height:53px;left:11px;overflow:hidden;pointer-events:none;position:absolute;width:52px}.stem{background-color:#999;bottom:14px;height:14px;left:22px;pointer-events:none;position:absolute;width:9px;z-index:1}.shell{border:7px solid #999;border-radius:28px;bottom:27px;height:57px;pointer-events:none;position:absolute;width:38px;z-index:0;left:0px}.s2ml .receiver,.s2ml .stem{background-color:#f44}.s2ml .shell{border-color:#f44}.s2ra .receiver,.s2ra .stem{background-color:#fff}.s2ra .shell{border-color:#fff}\\x3c/style\\x3e\\x3cspan class\\x3d\\x22receiver\\x22\\x3e\\x3c/span\\x3e\\x3cdiv class\\x3d\\x22wrapper\\x22\\x3e\\x3cspan class\\x3d\\x22stem\\x22\\x3e\\x3c/span\\x3e\\x3cspan class\\x3d\\x22shell\\x22\\x3e\\x3c/span\\x3e\\x3c/div\\x3e\\x3c/div\\x3e\\x3c/span\\x3e\\x3c/div\\x3e\\x3cdiv class\\x3d\\x22text-container\\x22\\x3e\\x3cstyle\\x3e.text-container{pointer-events:none;position:absolute;}.spcht{font-weight:normal;line-height:1.2;opacity:0;pointer-events:none;position:absolute;text-align:left;font-smoothing:antialiased;transition:opacity .1s ease-in,margin-left .5s ease-in,top 0s linear 0.218s;left:-44px;top:-.2em;margin-left:44px;font-size:32px;width:460px;}.s2fp .spcht{margin-left:0;opacity:1;transition:opacity .5s ease-out,margin-left .5s ease-out}.spchta{color:var(--JKqx2);cursor:pointer;font-size:18px;font-weight:500;pointer-events:auto;text-decoration:underline}.spch-2l.spcht,.spch-3l.spcht,.spch-4l.spcht{transition:top 0.218s ease-out}.spch-2l.spcht{top:-.6em}.spch-3l.spcht{top:-1.3em}.spch-4l.spcht{top:-1.7em}.s2fp .spch-5l.spcht{top:-2.5em;}\\x3c/style\\x3e\\x3cspan class\\x3d\\x22spcht\\x22 style\\x3d\\x22color:#9aa0a6\\x22 id\\x3d\\x22spchi\\x22\\x3e\\x3c/span\\x3e\\x3cspan class\\x3d\\x22spcht\\x22 style\\x3d\\x22color:#bdc1c6\\x22 id\\x3d\\x22spchf\\x22\\x3e\\x3c/span\\x3e\\x3c/div\\x3e\\x3cdiv class\\x3d\\x22google-logo\\x22\\x3e\\x3c/div\\x3e\\x3c/div\\x3e\\x3cdiv class\\x3d\\x22permission-bar\\x22\\x3e\\x3cstyle\\x3e.permission-bar{margin-top:-100px;opacity:0;pointer-events:none;position:absolute;width:500px;transition:opacity 0.218s ease-in,margin-top .4s ease-in}.s2wfp .permission-bar{margin-top:-300px;opacity:1;transition:opacity .5s ease-out 0.218s,margin-top 0.218s ease-out 0.218s}.permission-bar-gradient{box-shadow:0 1px 0px #4285f4;height:80px;left:0;margin:0;opacity:0;pointer-events:none;position:fixed;right:0;top:-80px;transition:opacity 0.218s,box-shadow 0.218s}.s2wfp .permission-bar-gradient{box-shadow:0 1px 80px #4285f4;opacity:1;pointer-events:none;animation:allow-alert .75s 0 infinite;animation-direction:alternate;animation-timing-function:ease-out;transition:opacity 0.218s,box-shadow 0.218s}@-webkit-keyframes allow-alert {from{opacity:1}to{opacity:.35}}\\x3c/style\\x3e\\x3cdiv class\\x3d\\x22permission-bar-gradient\\x22\\x3e\\x3c/div\\x3e\\x3c/div\\x3e\\x3c/div\\x3e');})();(function(){window.jsl.dh('_sNsXZ8SAEsff5OUPju6tyAE_6','\\x3cstyle\\x3e.YB4h9{background-color:var(--TMYS9);color:var(--EpFNW);padding:18px 60px 18px 12px;position:relative}.C85rO{font-size:20px}.Gtr0ne{padding-top:10px}.twTT9c{}.YB4h9 .Gtr0ne .twTT9c{color:var(--EpFNW);text-decoration:underline;padding:0;background:none;border:none;font:inherit;outline:inherit}.YB4h9 .Job8vb{padding:20px;position:absolute;right:0;top:0}.YB4h9.rPPJbd .Job8vb{padding-top:24px;padding-right:8px;position:absolute;right:0;top:0}.YB4h9.q7XNbb{margin-bottom:44px}.YB4h9.JF7fk{border-radius:16px;border-style:solid;border-color:var(--gS5jXb)}.YB4h9.IPINXd{border-bottom-left-radius:16px;border-bottom-right-radius:16px;border-color:var(--gS5jXb);border-style:solid;border-top:none}.YB4h9.rPPJbd{background-color:var(--xhUGwc);color:var(--bbQxAb)}.YB4h9.rPPJbd .twTT9c{color:var(--bbQxAb);text-decoration:underline;padding:0;background:none;border:none;font:inherit;outline:inherit}.R4GmFb{align-items:center;display:flex;flex-direction:row;margin-bottom:8px}.R4GmFb svg{display:block}.JrWcR{margin-left:10px}\\x3c/style\\x3e\\x3cspan class\\x3d\\x22Job8vb z1asCe wuXmqc\\x22 aria-label\\x3d\\x22Close Choose what you\\u2019re giving feedback on\\x22 role\\x3d\\x22button\\x22 tabindex\\x3d\\x220\\x22 jsaction\\x3d\\x22kEOk4d\\x22 style\\x3d\\x22height:20px;line-height:20px;width:20px\\x22 data-ved\\x3d\\x220ahUKEwjEmLvfvKKJAxXHL7kGHQ53CxkQmIkGCBQ\\x22\\x3e\\x3csvg focusable\\x3d\\x22false\\x22 xmlns\\x3d\\x22http://www.w3.org/2000/svg\\x22 viewBox\\x3d\\x220 0 24 24\\x22\\x3e\\x3cpath d\\x3d\\x22M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\\x22\\x3e\\x3c/path\\x3e\\x3c/svg\\x3e\\x3c/span\\x3e\\x3cdiv class\\x3d\\x22C85rO\\x22 aria-level\\x3d\\x221\\x22 role\\x3d\\x22heading\\x22\\x3eChoose what you\\u2019re giving feedback on\\x3c/div\\x3e');})();(function(){google.drty&&google.drty(undefined,true);})();});if (!google.stvsc){google.drty && google.drty(undefined,true);}\n</script><div id=\"sZmt3b\" class=\"fp-nh\"><div id=\"i58Mw\" class=\"ABMFZ\"></div></div><script src=\"/xjs/_/js/k=xjs.hd.en.0mZg4onwq2I.es5.O/ck=xjs.hd.3osHYkwsEnY.L.F4.O/am=JFUAAAAAAAAAAGAAAAAAAAAAAAAAAAAAAAAAAAAQAAAAAgAAAAAAAAAEBWhAAgAAjAKebACAjgEAAAAIAAAMABBICAAAGgAAJAAAAcACAACBAAAoAAAEFBECAAVNAHiUCSDACBASQAAFEAIEkIAANATxKEQAAAAGAAAIAWAEwwAEFQCMAgQAAAAAAAAgACEAAAQQIQACAAB-BADAAgDSAgCAINADgAAAAAAAAQAUAAIgSEwA7JABCAAAAAAAANAHAMEDYEjhAQAAAAAAAAAAAAAAApggmAsJCAhAAAAAAAAAAAAAAAAAAEhJExcm/d=0/dg=0/ujg=1/rs=ACT90oEFokshLJTGBEuzmJ4fqUwMoWpnfg/m=sb_wiz,aa,abd,syrn,syrm,syrh,syf2,syrl,syr7,syzj,syyr,syrc,syyq,sysd,syri,syrk,syrg,sys1,syr4,sys2,sys3,syrx,syrt,syrd,syrr,syru,syrv,syqx,syrp,syr8,syr9,syr2,syql,syqj,syqi,syrb,syyp,sysc,syqv,sysb,async,pHXghd,sf,TxCJfd,sy45m,qzxzOb,IsdWVc,sy45o,sy1e2,sy1ai,sy1ae,syqh,syqf,syqg,syqe,syqd,sy44v,sy44y,sy29g,sy16h,sy11f,syqr,syq9,syeg,syb9,syb8,syca,spch,sy8f,syfk,syfl,syfj,syft,syfr,syfp,syfi,sybw,sybr,sybu,syao,syag,syaf,syap,syae,syad,syac,sya4,sy9o,sybs,sybb,sybc,sybi,syak,sybh,syba,syb4,syb3,syaa,syai,sybd,syb1,syax,syay,syaz,syan,syau,syas,syat,syav,sycb,sybn,sybo,sy9z,sya1,sya6,sya5,syal,sya3,syc0,syc1,sy9r,sy9u,sy9t,sy9n,sy9l,sy9m,sy9w,sybe,syf8,syfh,syfg,syfd,syfb,sy7y,sy7v,sy7x,syfa,syff,syf9,syf7,syf4,syf3,sy81,uxMpU,syez,syce,syc8,syc2,sycc,syc5,syaw,syc6,sybx,sy8x,sy8w,sy8v,Mlhmy,QGR0gd,aurFic,sy96,fKUV3e,OTA3Ae,sy8g,OmgaI,EEDORb,PoEs9b,Pjplud,sy8r,sy8n,sy8l,A1yn5d,YIZmRd,uY49fb,sy7s,sy7q,sy7r,sy7p,sy7o,byfTOb,lsjVmc,LEikZe,kWgXee,Ug7Xab,U0aPgd,ovKuLd,sgY6Zb,qafBPd,ebZ3mb,dowIGb,syv0,syuz,syuy,sys6,SJpD2c,sytr,sytq,rtH1bd,sy1bm,sy17h,sy169,sy11k,sy1bl,SMquOb,sy1br,sy1bn,sytw,sy1bq,syy5,d5EhJe,sy1c6,fCxEDd,syvc,sy1c5,sy1c4,sy1c3,sy1bz,sy1bv,sy1bw,sy1by,sy19c,sy195,syvb,syxq,syxp,T1HOxc,sy1bx,sy1bu,zx30Y,sy1c7,sy1c1,sy17s,Wo3n8,syr3,loL8vb,sys5,sys4,ms4mZb,syp9,B2qlPe,syuq,NzU6V,syzv,syv6,zGLm3b,sywl,sywm,sywc,DhPYme,syz2,syyx,syz0,syyz,syx4,syx5,syyy,syyv,syyw,KHourd,MpJwZc,UUJqVe,sy7l,sOXFj,sy7k,s39S4,oGtAuc,NTMZac,nAFL3,sy8d,sy8c,q0xTif,y05UD,syxo,sy1aq,sy1b5,sy1ay,sy13e,syxn,syxm,syxl,syxr,sy1ax,sy1b6,sy11v,sy1b4,sy136,sy1am,sy13b,sy1aw,sy11q,sy1ar,sy1an,sy13c,sy13d,sy1az,sy11h,sy1av,sy1au,sy1as,symo,sy1at,sy1b1,sy1ag,sy1ao,sy1af,sy1al,sy1ah,sy149,sy1ap,sy1ab,sy13g,sy13h,syxt,syxu,epYOx?xjs=s3\" nonce=\"CP311SD5BVmtqozsXvXMVQ\" async=\"\" gapi_processed=\"true\"></script><div class=\"gb_K\" ng-non-bindable=\"\">Google apps</div><div class=\"gb_R\" ng-non-bindable=\"\"><div class=\"gb_Ac\"><div>Google Account</div><div class=\"gb_g\">Diego Balderrama</div><div><EMAIL></div></div></div><div id=\"snbc\"><div jsname=\"sM5MNb\" aria-live=\"polite\" class=\"LH3wG\"></div></div><link href=\"/xjs/_/js/k=xjs.hd.en.0mZg4onwq2I.es5.O/am=AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgAAAAAAAAAEBCgAAAAAgAIeAAAAgAAAAAAAAAAAABBICAAAGgAABAAAAMACAACBAAAIAAAAFBAAAAVIAHiUCQCACAASAAAAAAAEAIAANAQAAEAAAAAGAAAAAQAEAAAAFAAAAAAAAAAAAAAAAAAAAAQQIAAAAAAAAAAAAABAAgAAANADAAAAAAAAAAAEAAAgCAAA7JABCAAAAAAAANAHAMEDYEjhAQAAAAAAAAAAAAAAApggmAsJCAhAAAAAAAAAAAAAAAAAAEhJExcm/d=0/dg=0/rs=ACT90oFkMsRkHqYDY758QJVs9U3tVH4BiA/m=sy1d9,P10Owf,sy1c2,sy1c0,syq1,gSZvdb,syze,syzd,WlNQGd,syq6,syq3,syq2,syq0,DPreE,syzq,syzo,nabPbb,syz8,syz6,syiy,syn3,CnSW2d,kQvlef,syzp,fXO0xe?xjs=s4\" rel=\"preload\" as=\"script\"><script src=\"/xjs/_/js/k=xjs.hd.en.0mZg4onwq2I.es5.O/am=AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgAAAAAAAAAEBCgAAAAAgAIeAAAAgAAAAAAAAAAAABBICAAAGgAABAAAAMACAACBAAAIAAAAFBAAAAVIAHiUCQCACAASAAAAAAAEAIAANAQAAEAAAAAGAAAAAQAEAAAAFAAAAAAAAAAAAAAAAAAAAAQQIAAAAAAAAAAAAABAAgAAANADAAAAAAAAAAAEAAAgCAAA7JABCAAAAAAAANAHAMEDYEjhAQAAAAAAAAAAAAAAApggmAsJCAhAAAAAAAAAAAAAAAAAAEhJExcm/d=0/dg=0/rs=ACT90oFkMsRkHqYDY758QJVs9U3tVH4BiA/m=sy1d9,P10Owf,sy1c2,sy1c0,syq1,gSZvdb,syze,syzd,WlNQGd,syq6,syq3,syq2,syq0,DPreE,syzq,syzo,nabPbb,syz8,syz6,syiy,syn3,CnSW2d,kQvlef,syzp,fXO0xe?xjs=s4\" nonce=\"CP311SD5BVmtqozsXvXMVQ\" async=\"\"></script><link href=\"/xjs/_/js/k=xjs.hd.en.0mZg4onwq2I.es5.O/am=AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgAAAAAAAAAEBCgAAAAAgAIeAAAAgAAAAAAAAAAAABBICAAAGgAABAAAAMACAACBAAAIAAAAFBAAAAVIAHiUCQCACAASAAAAAAAEAIAANAQAAEAAAAAGAAAAAQAEAAAAFAAAAAAAAAAAAAAAAAAAAAQQIAAAAAAAAAAAAABAAgAAANADAAAAAAAAAAAEAAAgCAAA7JABCAAAAAAAANAHAMEDYEjhAQAAAAAAAAAAAAAAApggmAsJCAhAAAAAAAAAAAAAAAAAAEhJExcm/d=0/dg=0/rs=ACT90oFkMsRkHqYDY758QJVs9U3tVH4BiA/m=lOO0Vd,sy8s,P6sQOc?xjs=s4\" rel=\"preload\" as=\"script\"><script src=\"/xjs/_/js/k=xjs.hd.en.0mZg4onwq2I.es5.O/am=AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgAAAAAAAAAEBCgAAAAAgAIeAAAAgAAAAAAAAAAAABBICAAAGgAABAAAAMACAACBAAAIAAAAFBAAAAVIAHiUCQCACAASAAAAAAAEAIAANAQAAEAAAAAGAAAAAQAEAAAAFAAAAAAAAAAAAAAAAAAAAAQQIAAAAAAAAAAAAABAAgAAANADAAAAAAAAAAAEAAAgCAAA7JABCAAAAAAAANAHAMEDYEjhAQAAAAAAAAAAAAAAApggmAsJCAhAAAAAAAAAAAAAAAAAAEhJExcm/d=0/dg=0/rs=ACT90oFkMsRkHqYDY758QJVs9U3tVH4BiA/m=lOO0Vd,sy8s,P6sQOc?xjs=s4\" nonce=\"CP311SD5BVmtqozsXvXMVQ\" async=\"\"></script><iframe id=\"hfcr\" style=\"display: none;\" src=\"https://accounts.google.com/RotateCookiesPage?og_pid=538&amp;rot=3&amp;origin=https%3A%2F%2Fwww.google.com&amp;exp_id=0\"></iframe></body></html>");
            var document = pdf.SaveAs($"{basePath}\\google.pdf"); // Saves our PdfDocument object as a PDF

            return document.Stream;
        }
        #endregion GenerateComplexHtmlToPdf

        public async Task<MemoryStream> GeneratePdfAsync(HtmlToPdfRequest request)
        {
            _logger.LogDebug("Html to convert Pdf: {Html}", request.Html);

            var millisDelay = 500;

            var renderer = new ChromePdfRenderer
            {
                RenderingOptions = GetHtmlToPdfOptions(request),
            };
            renderer.RenderingOptions.WaitFor.RenderDelay(millisDelay);

            var baseUrl = string.IsNullOrEmpty(request.BaseUrl) ? null : new Uri(request.BaseUrl);

            var document = await renderer.RenderHtmlAsPdfAsync(request.Html, baseUrl);

            try
            {
                switch (request.Type)
                {
                    case Models.Type.PNG:
                        break;
                    case Models.Type.JPEG:
                        document.CompressImages(Quality: 100);
                        break;
                    case Models.Type.PDF:
                        break;
                }

                if (!string.IsNullOrEmpty(request.Title))
                    document.MetaData.Creator = request.Creator;

                if (!string.IsNullOrEmpty(request.Creator))
                    document.MetaData.Title = request.Title;

                if (!string.IsNullOrEmpty(request.PdfToMergeBase64))
                {
                    using var pdfFileStream = new MemoryStream(
                        Convert.FromBase64String(request.PdfToMergeBase64));
                    using var pdfToMerge = new PdfDocument(pdfFileStream);
                    document = PdfDocument.Merge(document, pdfToMerge);
                }

                var memoryStream = await SaveToStreamAsync(request.Type, document);

                _logger.LogInformation(
                    "Stream length generated ({TypeFile}): {FileLength}",
                    request.Type,
                    memoryStream.Length);

                memoryStream.Position = 0;

                return memoryStream;

            }
            finally
            {
                document?.Dispose();
            }
        }

        public async Task<MemoryStream> GeneratePdfAsync(HtmlListToPdfRequest request)
        {
            _logger.LogDebug("# List of Htmls to convert Pdf ({Count})", request.Htmls.Count);

            var millisDelay = 500;

            var renderer = new ChromePdfRenderer
            {
                RenderingOptions = GetHtmlToPdfOptions(request),
            };
            renderer.RenderingOptions.WaitFor.RenderDelay(millisDelay);

            using var document = await GeneratePdfDocumentsMergedAsync(request.Htmls, renderer);

            switch (request.Type)
            {
                case Models.Type.PNG:
                    break;
                case Models.Type.JPEG:
                    document.CompressImages(Quality: 100);
                    break;
                case Models.Type.PDF:
                    break;
            }

            if (!string.IsNullOrEmpty(request.Title))
                document.MetaData.Creator = request.Creator;

            if (!string.IsNullOrEmpty(request.Creator))
                document.MetaData.Title = request.Title;

            var memoryStream = await SaveToStreamAsync(request.Type, document);

            _logger.LogInformation(
                "Stream length generated ({TypeFile}): {FileLength}",
                request.Type,
                memoryStream.Length);
           
            memoryStream.Position = 0;
            
            return memoryStream;
        }

        public async Task<MemoryStream> GeneratePdfAsync(TifToPdfResquest request)
        {
            var dpi = 300;
            var pageWidth = 8.5f;
            var pageHeight = 11.0f;
            var millimetersConversion = 25.4d;

            _logger.LogDebug(
                "Tif(base64) to convert Pdf: {FileDataBase64}", request.FileDataBase64);

            var options = new ChromePdfRenderOptions
            {
                MarginBottom = 0,
                MarginLeft = 0,
                MarginRight = 0,
                MarginTop = 0,
            };

            options.SetCustomPaperSizeinPixelsOrPoints(
                        width: pageWidth * dpi,
                        height: pageHeight * dpi,
                        dpi);

            var pdfDocument = new PdfDocument(
                Width: pageWidth * millimetersConversion,
                Height: pageHeight * millimetersConversion);

            var renderer = new ChromePdfRenderer
            {
                RenderingOptions = options
            };

            using (var tifFileStream = new MemoryStream(
                Convert.FromBase64String(request.FileDataBase64)))
            {
                using (Tiff image = Tiff.ClientOpen("in-memory", "r", tifFileStream, new TiffStream()))
                {
                    int numberOfDirectories = image.NumberOfDirectories();
                    for (int i = 0; i < numberOfDirectories; i++)
                    {
                        image.SetDirectory((short)i);
                        int width = image.GetField(TiffTag.IMAGEWIDTH)[0].ToInt();
                        int height = image.GetField(TiffTag.IMAGELENGTH)[0].ToInt();

                        // Process the frame as needed
                        // For example, read the raster data
                        int[] raster = new int[width * height];
                        if (!image.ReadRGBAImage(width, height, raster))
                        {
                            Console.WriteLine($"Could not read frame {i + 1}");
                        }
                        else
                        {
                            // Convert raster to SKBitmap
                            using (var skBitmap = new SKBitmap(
                                width, height, SKColorType.Bgra8888, SKAlphaType.Premul))
                            {
                                for (int y = 0; y < height; y++)
                                {
                                    for (int x = 0; x < width; x++)
                                    {
                                        int pixel = raster[y * width + x];
                                        var color = new SKColor(
                                            (byte)((pixel >> 16) & 0xff), // red
                                            (byte)((pixel >> 8) & 0xff),  // green
                                            (byte)(pixel & 0xff),         // blue
                                            (byte)((pixel >> 24) & 0xff)); // alpha
                                        skBitmap.SetPixel(x, y, color);
                                    }
                                }

                                // Save SKBitmap to MemoryStream
                                using (var imageStream = new MemoryStream())
                                {
                                    using (var skImage = SKImage.FromBitmap(skBitmap))
                                    using (var skData = skImage.Encode(SKEncodedImageFormat.Png, 100))
                                    {
                                        skData.SaveTo(imageStream);
                                    }
                                    imageStream.Position = 0;

                                    var base64 = Convert.ToBase64String(imageStream.ToArray());
                                    
                                    var usingTifTemplate = File.OpenText(TIF_TEMPLATE_PATH).ReadToEnd();
                                    var html = usingTifTemplate.Replace("{{IMAGE}}", base64);

                                    var newPagePdf = await renderer.RenderHtmlAsPdfAsync(html);

                                    pdfDocument.AppendPdf(newPagePdf);
                                }
                            }
                        }
                    }
                }
            }

            _logger.LogInformation(
                "Stream length generated ({FileName}): {FileLength}",
                request.FileName,
                pdfDocument.Stream.Length);
            
            pdfDocument.Stream.Position = 0;
            
            return pdfDocument.Stream;
        }

        public GetFieldsOfPdfResponse GetFields(GetFieldsOfPdfRequest request)
        {
            using var pdfStream = new MemoryStream(
                Convert.FromBase64String(request.PdfFileBase64));
            var pdfDocument = new PdfDocument(pdfStream);

            var fields = pdfDocument.Form.ToDictionary(f => f.Name, f => f.Value);
            return new GetFieldsOfPdfResponse
            {
                Fields = fields
            };
        }

        public async Task<MemoryStream> FillOutPdfFormAsync(FillOutPdfRequest request)
        {
            using var pdfFile = new MemoryStream(Convert.FromBase64String(request.PdfFileBase64));
            var pdfDocument = new PdfDocument(pdfFile);

            foreach (var item in request.Fields)
            {
                var tempField = pdfDocument.Form.FindFormField(item.Key)?.Value;
                if (tempField is not null)
                {
                    pdfDocument.Form.FindFormField(item.Key).Value = item.Value;
                }
            }

            await AddSignaturesToDocAsync(pdfDocument, request.Signatures);

            CloneDoc(ref pdfDocument, request.Cloning);

            await AddImageToDocAsync(pdfDocument, request.Image);

            pdfDocument.Stream.Position = 0;

            return pdfDocument.Stream;
        }

        private static async Task AddImageToDocAsync(
            PdfDocument pdfDocument,
            Models.ImageToPdf imageToPdf)
        {
            if (imageToPdf == null)
            {
                return;
            }

            //Load the image
            using var imageFile = new MemoryStream(Convert.FromBase64String(imageToPdf.Url));

            //Create a new PdfImage object
            var imageStamper = new ImageStamper(new AnyBitmap(imageFile))
            {
                HorizontalOffset = new Length(imageToPdf.X, MeasurementUnit.Pixel),
                VerticalOffset = new Length(imageToPdf.Y, MeasurementUnit.Pixel)
            };

            if (imageToPdf.ScaleX.HasValue && imageToPdf.ScaleY.HasValue)
            {
                double averageScaleFactor =
                    (imageToPdf.ScaleX.Value + imageToPdf.ScaleY.Value) / 2d;

                double scaleFactorPercentage = averageScaleFactor * 100d;

                imageStamper.Scale = scaleFactorPercentage;
            }

            //Place the image on the page
            await pdfDocument.ApplyStampAsync(imageStamper, PageIndexesToStamp: [0]);
        }

        private static void CloneDoc(ref PdfDocument pdfDocument, Models.CloningPdf cloning)
        {
            if (cloning == null || cloning.Fields.Count <= 0)
            {
                return;
            }

            var extras = new List<PdfDocument>();
            var i = 0;
            foreach (var fields in cloning.Fields)
            {
                var tempDoc = new PdfDocument(pdfDocument);
                foreach (var key in new List<string>(fields.Select(k => k.Key)))
                {
                    var tempField = tempDoc.Form.FindFormField(key)?.Value;
                    if (tempField is not null)
                    {
                        tempDoc.Form.FindFormField(key).Value = fields[key];
                    }
                }

                if (cloning.RenameFields)
                {
                    // Need unique field names or users can't edit field values
                    foreach (var field in tempDoc.Form)
                    {
                        field.Name = $"{field.Name}_{i}_{cloning.TemplateId}";
                    }
                }
                extras.Add(tempDoc);
            }

            pdfDocument = PdfDocument.Merge([.. extras]);
        }

        private static async Task AddSignaturesToDocAsync(
            PdfDocument pdfDocument, List<Models.SignaturePdf> signatures)
        {
            if (signatures == null || signatures.Count <= 0)
            {
                return;
            }
            // Images...including notary seal, signatures
            foreach (var signature in signatures)
            {
                //Load the image
                using var imageFile = new MemoryStream(Convert.FromBase64String(signature.SignatureBase64));
                //Create a new PdfImage object
                var imageStamper = new ImageStamper(new AnyBitmap(imageFile));

                if (signature.TranslateX.HasValue && signature.TranslateY.HasValue)
                {
                    imageStamper.HorizontalOffset = new Length(signature.TranslateX.Value, MeasurementUnit.Pixel);
                    imageStamper.VerticalOffset = new Length(signature.TranslateY.Value, MeasurementUnit.Pixel);
                }

                //Place the image on the page
                await pdfDocument.ApplyStampAsync(imageStamper, PageIndexesToStamp: [0]);
            }
        }

        private static async Task<MemoryStream> SaveToStreamAsync(
            Models.Type type,
            PdfDocument document)
        {
            if ((type is Models.Type.PNG || type is Models.Type.JPEG) && document.Stream.Length == 0)
            {
                throw new BadHttpRequestException($"The method does not support different for {type} type");
            }

            if (type.Equals(Models.Type.PNG))
            {
                // Create a temporary directory-safe filename pattern
                string tempDir = Path.GetTempPath();
                string fileGuid = Guid.NewGuid().ToString();
                string tempFilenamePattern = Path.Combine(tempDir, $"{fileGuid}-{{0}}.png");

                try
                {
                    // Use ToPngImages with the filename pattern
                    var imageFiles = document.ToPngImages(tempFilenamePattern, 100, true);

                    if (imageFiles != null && imageFiles.Length > 0)
                    {
                        // Read the first image file into memory
                        byte[] pngData = await File.ReadAllBytesAsync(imageFiles[0]);
                        // Apply autocropping to the PNG image
                        using (var inputStream = new MemoryStream(pngData))
                        {
                            // Decode the PNG into an SKBitmap
                            var bitmap = SKBitmap.Decode(inputStream);

                            // Apply autocropping
                            var croppedBitmap = AutoCrop(bitmap);

                            // Encode the cropped bitmap to a new memory stream
                            var outputStream = new MemoryStream();
                            croppedBitmap.Encode(outputStream, SKEncodedImageFormat.Png, 100);
                            outputStream.Position = 0;
                            return outputStream;
                        }
                    }
                }
                finally
                {
                    // Clean up any temporary files created with this pattern
                    string searchPattern = $"{fileGuid}-*.png";
                    foreach (var file in Directory.GetFiles(tempDir, searchPattern))
                    {
                        try { File.Delete(file); } catch { /* Ignore cleanup errors */ }
                    }
                }
            }
            else if (type.Equals(Models.Type.JPEG))
            {
                // Create a temporary directory-safe filename pattern
                string tempDir = Path.GetTempPath();
                string fileGuid = Guid.NewGuid().ToString();
                string tempFilenamePattern = Path.Combine(tempDir, $"{fileGuid}-{{0}}.jpg");

                try
                {
                    // Use ToJpegImages with the filename pattern
                    var imageFiles = document.ToJpegImages(tempFilenamePattern, 300, true);

                    if (imageFiles != null && imageFiles.Length > 0)
                    {
                        // Read the first image file into memory
                        byte[] jpegData = await File.ReadAllBytesAsync(imageFiles[0]);
                        return new MemoryStream(jpegData);
                    }
                }
                finally
                {
                    // Clean up any temporary files created with this pattern
                    string searchPattern = $"{fileGuid}-*.jpg";
                    foreach (var file in Directory.GetFiles(tempDir, searchPattern))
                    {
                        try { File.Delete(file); } catch { /* Ignore cleanup errors */ }
                    }
                }
            }
            return document.Stream;
        }

        //-- Obsolete
        private static PdfDocument GeneratePdfInStream(
            List<string> htmls, ChromePdfRenderer renderer)
        {
            PdfDocument document = null;
            
            foreach (var html in htmls)
            {
                document = renderer.RenderHtmlAsPdf(html);
            }

            return document;
        }

        private static async Task<PdfDocument> GeneratePdfDocumentsMergedAsync(
            List<string> htmls, ChromePdfRenderer renderer)
        {
            var documents = new List<PdfDocument>();
            var tasks = new List<Task<PdfDocument>>();
            foreach (var html in htmls)
            {
                var result =  renderer.RenderHtmlAsPdfAsync(html);
                tasks.Add(result);
            }

            return PdfDocument.Merge(await Task.WhenAll(tasks));
        }

        private static ChromePdfRenderOptions GetHtmlToPdfOptions(
            BaseOptionsRequest baseOptions)
        {
            var options = new ChromePdfRenderOptions
            {
                HtmlFooter = new HtmlHeaderFooter
                {
                    HtmlFragment = baseOptions.Footer
                },
                EnableJavaScript = true,
            };
            var dpi = 300;
            var millimitersConversion = 25.4d;

            // pixels = inches * DPI
            // 300 DPI is a consistent and professional quality
            // 1 inch = 25.4 millimeters
            // Rect dimensions should be in inches by default
            if (baseOptions.Type.Equals(Models.Type.PNG))
            {
                var pageSize = baseOptions.PageSize != null && baseOptions.PageSize.IsValid
                    ? baseOptions.PageSize.GetSizeF
                    : new SizeF(3.5f, 10.9f);

                options.SetCustomPaperSizeInInches((double)pageSize.Width, (double)pageSize.Height);

                var outputArea = baseOptions.OutputArea != null && baseOptions.OutputArea.IsValid
                    ? baseOptions.OutputArea.GetRectangleF
                    : new RectangleF(0.2f, 0.2f, 3.3f, 10.7f);
                var leftMargin = outputArea.X;
                var rightMargin = pageSize.Width - (outputArea.X + outputArea.Width);
                var topMargin = outputArea.Y;
                var bottomMargin = pageSize.Height - (outputArea.Y + outputArea.Height);

                options.MarginTop = topMargin * millimitersConversion;
                options.MarginBottom = bottomMargin * millimitersConversion;
                options.MarginLeft = leftMargin * millimitersConversion;
                options.MarginRight = rightMargin * millimitersConversion;
                options.Zoom = 140;
            }
            else if (baseOptions.Type.Equals(Models.Type.JPEG))
            {
                if (baseOptions.PageSize != null && baseOptions.PageSize.IsValid)
                {
                    options.SetCustomPaperSizeinPixelsOrPoints(
                        width: baseOptions.PageSize.Width * dpi,
                        height: baseOptions.PageSize.Height * dpi,
                        dpi);

                    var outputArea = baseOptions.OutputArea != null && baseOptions.OutputArea.IsValid
                        ? baseOptions.OutputArea.GetRectangleF
                        : new RectangleF(0.3f, 0.3f, 7.8f, 10.3f);
                    
                    var leftMargin = outputArea.X;
                    var rightMargin = baseOptions.PageSize.Width - (outputArea.X + outputArea.Width);
                    var topMargin = outputArea.Y;
                    var bottomMargin = baseOptions.PageSize.Height - (outputArea.Y + outputArea.Height);

                    options.MarginTop = topMargin * millimitersConversion;
                    options.MarginBottom = bottomMargin * millimitersConversion;
                    options.MarginLeft = leftMargin * millimitersConversion;
                    options.MarginRight = rightMargin * millimitersConversion;
                }
            }
            else if (baseOptions.Type.Equals(Models.Type.PDF))
            {
                var pageSize = baseOptions.PageSize != null && baseOptions.PageSize.IsValid
                    ? baseOptions.PageSize.GetSizeF
                    : new SizeF(8.50f, 11.0f);

                options.SetCustomPaperSizeinPixelsOrPoints(
                        width: pageSize.Width * dpi,
                        height: pageSize.Height * dpi,
                        dpi);

                if (!baseOptions.WithDefault && baseOptions.OutputArea is null)
                {
                    baseOptions.WithDefault = true;
                }

                if (baseOptions.WithDefault || (baseOptions.OutputArea != null && baseOptions.OutputArea.IsValid))
                {
                    var outputArea = baseOptions.OutputArea != null && baseOptions.OutputArea.IsValid
                        ? baseOptions.OutputArea.GetRectangleF
                        : new RectangleF(0.3f, 0.3f, 7.8f, 10.3f);

                    var leftMargin = outputArea.X;
                    var rightMargin = pageSize.Width - (outputArea.X + outputArea.Width);
                    var topMargin = outputArea.Y;
                    var bottomMargin = pageSize.Height - (outputArea.Y + outputArea.Height);

                    options.MarginTop = topMargin * millimitersConversion;
                    options.MarginBottom = baseOptions.NoMarginBottom ? 0 : bottomMargin * millimitersConversion;
                    options.MarginLeft = leftMargin * millimitersConversion;
                    options.MarginRight = rightMargin * millimitersConversion;
                }
            }

            return options;
        }

        private static SKBitmap AutoCrop(SKBitmap bmp)
        {
            // Find the bounds of non-transparent/non-white pixels
            int minX = bmp.Width - 1;
            int minY = bmp.Height - 1;
            int maxX = 0;
            int maxY = 0;
            bool foundPixel = false;

            for (int y = 0; y < bmp.Height; y++)
            {
                for (int x = 0; x < bmp.Width; x++)
                {
                    SKColor pixel = bmp.GetPixel(x, y);
                    // Check if pixel is not white and not transparent
                    if (pixel.Alpha > 10 && !(pixel.Red > 245 && pixel.Green > 245 && pixel.Blue > 245))
                    {
                        if (x < minX) minX = x;
                        if (y < minY) minY = y;
                        if (x > maxX) maxX = x;
                        if (y > maxY) maxY = y;
                        foundPixel = true;
                    }
                }
            }

            if (!foundPixel)
                return bmp; // Return original if no non-white pixels

            // Create a cropped bitmap
            int width = maxX - minX + 1;
            int height = maxY - minY + 1;

            // Add some padding
            int padding = 2;
            minX = Math.Max(0, minX - padding);
            minY = Math.Max(0, minY - padding);
            width = Math.Min(bmp.Width - minX, width + 2 * padding);
            height = Math.Min(bmp.Height - minY, height + 2 * padding);

            SKBitmap croppedBitmap = new SKBitmap(width, height);
            using (SKCanvas canvas = new SKCanvas(croppedBitmap))
            {
                SKRect destRect = new SKRect(0, 0, width, height);
                SKRect srcRect = new SKRect(minX, minY, minX + width, minY + height);
                canvas.DrawBitmap(bmp, srcRect, destRect);
            }

            return croppedBitmap;
        }
    }
}
