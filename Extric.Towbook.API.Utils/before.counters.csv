Timestamp,Provider,Counter Name,Counter Type,Mean/Increment
05/08/2025 15:10:02,System.Runtime,CPU Usage (%),<PERSON><PERSON>,0.9671179883945842
05/08/2025 15:10:02,System.Runtime,Working Set (MB),Metric,111.06304
05/08/2025 15:10:02,System.Runtime,GC Heap Size (MB),Metric,8.408448
05/08/2025 15:10:02,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:02,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:02,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:02,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:10:02,System.Runtime,ThreadPool Thread Count,Metric,2
05/08/2025 15:10:02,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:02,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:02,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:10:02,System.Runtime,Allocation Rate (B / 1 sec),Rate,0
05/08/2025 15:10:02,System.Runtime,Number of Active Timers,Metric,3
05/08/2025 15:10:02,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:10:02,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:10:02,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:02,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:02,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:02,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:10:02,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:10:02,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:10:02,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:10:02,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:10:02,System.Runtime,Number of Assemblies Loaded,Metric,144
05/08/2025 15:10:02,System.Runtime,IL Bytes Jitted (B),Metric,338384
05/08/2025 15:10:02,System.Runtime,Number of Methods Jitted,Metric,4670
05/08/2025 15:10:02,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,62.12950000000001
05/08/2025 15:10:01,System.Runtime,CPU Usage (%),Metric,0
05/08/2025 15:10:01,System.Runtime,Working Set (MB),Metric,110.821376
05/08/2025 15:10:01,System.Runtime,GC Heap Size (MB),Metric,8.383776
05/08/2025 15:10:01,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:01,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:01,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:01,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:10:01,System.Runtime,ThreadPool Thread Count,Metric,2
05/08/2025 15:10:01,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:01,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:01,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:10:01,System.Runtime,Allocation Rate (B / 1 sec),Rate,276096
05/08/2025 15:10:01,System.Runtime,Number of Active Timers,Metric,3
05/08/2025 15:10:01,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:10:01,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:10:01,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:01,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:01,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:01,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:10:01,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:10:01,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:10:01,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:10:01,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:10:01,System.Runtime,Number of Assemblies Loaded,Metric,144
05/08/2025 15:10:01,System.Runtime,IL Bytes Jitted (B),Metric,331850
05/08/2025 15:10:01,System.Runtime,Number of Methods Jitted,Metric,4624
05/08/2025 15:10:01,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,188.06130000000007
05/08/2025 15:10:03,System.Runtime,CPU Usage (%),Metric,0.1968503937007874
05/08/2025 15:10:03,System.Runtime,Working Set (MB),Metric,111.104
05/08/2025 15:10:03,System.Runtime,GC Heap Size (MB),Metric,8.416672
05/08/2025 15:10:03,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:03,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:03,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:03,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:10:03,System.Runtime,ThreadPool Thread Count,Metric,2
05/08/2025 15:10:03,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:03,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:03,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:10:03,System.Runtime,Allocation Rate (B / 1 sec),Rate,8200
05/08/2025 15:10:03,System.Runtime,Number of Active Timers,Metric,3
05/08/2025 15:10:03,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:10:03,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:10:03,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:03,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:03,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:03,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:10:03,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:10:03,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:10:03,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:10:03,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:10:03,System.Runtime,Number of Assemblies Loaded,Metric,144
05/08/2025 15:10:03,System.Runtime,IL Bytes Jitted (B),Metric,341131
05/08/2025 15:10:03,System.Runtime,Number of Methods Jitted,Metric,4693
05/08/2025 15:10:03,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,18.1262999999999
05/08/2025 15:10:04,System.Runtime,CPU Usage (%),Metric,0.5847953216374269
05/08/2025 15:10:04,System.Runtime,Working Set (MB),Metric,111.214592
05/08/2025 15:10:04,System.Runtime,GC Heap Size (MB),Metric,8.42464
05/08/2025 15:10:04,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:04,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:04,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:04,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:10:04,System.Runtime,ThreadPool Thread Count,Metric,2
05/08/2025 15:10:04,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:04,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:04,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:10:04,System.Runtime,Allocation Rate (B / 1 sec),Rate,7944
05/08/2025 15:10:04,System.Runtime,Number of Active Timers,Metric,3
05/08/2025 15:10:04,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:10:04,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:10:04,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:04,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:04,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:04,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:10:04,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:10:04,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:10:04,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:10:04,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:10:04,System.Runtime,Number of Assemblies Loaded,Metric,144
05/08/2025 15:10:04,System.Runtime,IL Bytes Jitted (B),Metric,345015
05/08/2025 15:10:04,System.Runtime,Number of Methods Jitted,Metric,4744
05/08/2025 15:10:04,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,41.33180000000016
05/08/2025 15:10:05,System.Runtime,CPU Usage (%),Metric,0
05/08/2025 15:10:05,System.Runtime,Working Set (MB),Metric,111.214592
05/08/2025 15:10:05,System.Runtime,GC Heap Size (MB),Metric,8.42464
05/08/2025 15:10:05,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:05,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:05,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:05,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:10:05,System.Runtime,ThreadPool Thread Count,Metric,2
05/08/2025 15:10:05,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:05,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:05,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:10:05,System.Runtime,Allocation Rate (B / 1 sec),Rate,8200
05/08/2025 15:10:05,System.Runtime,Number of Active Timers,Metric,3
05/08/2025 15:10:05,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:10:05,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:10:05,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:05,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:05,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:05,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:10:05,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:10:05,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:10:05,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:10:05,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:10:05,System.Runtime,Number of Assemblies Loaded,Metric,144
05/08/2025 15:10:05,System.Runtime,IL Bytes Jitted (B),Metric,345549
05/08/2025 15:10:05,System.Runtime,Number of Methods Jitted,Metric,4750
05/08/2025 15:10:05,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,8.054499999999962
05/08/2025 15:10:06,System.Runtime,CPU Usage (%),Metric,0.5952380952380952
05/08/2025 15:10:06,System.Runtime,Working Set (MB),Metric,111.3088
05/08/2025 15:10:06,System.Runtime,GC Heap Size (MB),Metric,8.432864
05/08/2025 15:10:06,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:06,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:06,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:06,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:10:06,System.Runtime,ThreadPool Thread Count,Metric,2
05/08/2025 15:10:06,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:06,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:06,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:10:06,System.Runtime,Allocation Rate (B / 1 sec),Rate,0
05/08/2025 15:10:06,System.Runtime,Number of Active Timers,Metric,3
05/08/2025 15:10:06,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:10:06,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:10:06,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:06,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:06,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:06,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:10:06,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:10:06,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:10:06,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:10:06,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:10:06,System.Runtime,Number of Assemblies Loaded,Metric,144
05/08/2025 15:10:06,System.Runtime,IL Bytes Jitted (B),Metric,349381
05/08/2025 15:10:06,System.Runtime,Number of Methods Jitted,Metric,4794
05/08/2025 15:10:06,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,53.27629999999999
05/08/2025 15:10:07,System.Runtime,CPU Usage (%),Metric,0.19230769230769232
05/08/2025 15:10:07,System.Runtime,Working Set (MB),Metric,111.333376
05/08/2025 15:10:07,System.Runtime,GC Heap Size (MB),Metric,8.441088
05/08/2025 15:10:07,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:07,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:07,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:07,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:10:07,System.Runtime,ThreadPool Thread Count,Metric,2
05/08/2025 15:10:07,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:07,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:07,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:10:07,System.Runtime,Allocation Rate (B / 1 sec),Rate,8200
05/08/2025 15:10:07,System.Runtime,Number of Active Timers,Metric,3
05/08/2025 15:10:07,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:10:07,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:10:07,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:07,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:07,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:07,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:10:07,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:10:07,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:10:07,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:10:07,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:10:07,System.Runtime,Number of Assemblies Loaded,Metric,144
05/08/2025 15:10:07,System.Runtime,IL Bytes Jitted (B),Metric,350495
05/08/2025 15:10:07,System.Runtime,Number of Methods Jitted,Metric,4795
05/08/2025 15:10:07,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,8.435699999999997
05/08/2025 15:10:08,System.Runtime,CPU Usage (%),Metric,0
05/08/2025 15:10:08,System.Runtime,Working Set (MB),Metric,111.341568
05/08/2025 15:10:08,System.Runtime,GC Heap Size (MB),Metric,8.441088
05/08/2025 15:10:08,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:08,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:08,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:08,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:10:08,System.Runtime,ThreadPool Thread Count,Metric,2
05/08/2025 15:10:08,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:08,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:08,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:10:08,System.Runtime,Allocation Rate (B / 1 sec),Rate,8200
05/08/2025 15:10:08,System.Runtime,Number of Active Timers,Metric,3
05/08/2025 15:10:08,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:10:08,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:10:08,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:08,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:08,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:08,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:10:08,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:10:08,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:10:08,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:10:08,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:10:08,System.Runtime,Number of Assemblies Loaded,Metric,144
05/08/2025 15:10:08,System.Runtime,IL Bytes Jitted (B),Metric,351152
05/08/2025 15:10:08,System.Runtime,Number of Methods Jitted,Metric,4799
05/08/2025 15:10:08,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,8.878799999999956
05/08/2025 15:10:09,System.Runtime,CPU Usage (%),Metric,0
05/08/2025 15:10:09,System.Runtime,Working Set (MB),Metric,111.34976
05/08/2025 15:10:09,System.Runtime,GC Heap Size (MB),Metric,8.449312
05/08/2025 15:10:09,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:09,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:09,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:09,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:10:09,System.Runtime,ThreadPool Thread Count,Metric,2
05/08/2025 15:10:09,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:09,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:09,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:10:09,System.Runtime,Allocation Rate (B / 1 sec),Rate,0
05/08/2025 15:10:09,System.Runtime,Number of Active Timers,Metric,3
05/08/2025 15:10:09,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:10:09,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:10:09,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:09,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:09,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:09,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:10:09,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:10:09,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:10:09,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:10:09,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:10:09,System.Runtime,Number of Assemblies Loaded,Metric,144
05/08/2025 15:10:09,System.Runtime,IL Bytes Jitted (B),Metric,351265
05/08/2025 15:10:09,System.Runtime,Number of Methods Jitted,Metric,4801
05/08/2025 15:10:09,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,1.5876000000000658
05/08/2025 15:10:10,System.Runtime,CPU Usage (%),Metric,0.390625
05/08/2025 15:10:10,System.Runtime,Working Set (MB),Metric,111.788032
05/08/2025 15:10:10,System.Runtime,GC Heap Size (MB),Metric,8.457536
05/08/2025 15:10:10,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:10,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:10,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:10,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:10:10,System.Runtime,ThreadPool Thread Count,Metric,2
05/08/2025 15:10:10,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:10,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:10,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:10:10,System.Runtime,Allocation Rate (B / 1 sec),Rate,8200
05/08/2025 15:10:10,System.Runtime,Number of Active Timers,Metric,3
05/08/2025 15:10:10,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:10:10,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:10:10,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:10,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:10,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:10,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:10:10,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:10:10,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:10:10,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:10:10,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:10:10,System.Runtime,Number of Assemblies Loaded,Metric,144
05/08/2025 15:10:10,System.Runtime,IL Bytes Jitted (B),Metric,354197
05/08/2025 15:10:10,System.Runtime,Number of Methods Jitted,Metric,4819
05/08/2025 15:10:10,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,42.103200000000015
05/08/2025 15:10:11,System.Runtime,CPU Usage (%),Metric,0.5769230769230769
05/08/2025 15:10:11,System.Runtime,Working Set (MB),Metric,111.80032
05/08/2025 15:10:11,System.Runtime,GC Heap Size (MB),Metric,8.46576
05/08/2025 15:10:11,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:11,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:11,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:11,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:10:11,System.Runtime,ThreadPool Thread Count,Metric,2
05/08/2025 15:10:11,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:11,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:11,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:10:11,System.Runtime,Allocation Rate (B / 1 sec),Rate,8200
05/08/2025 15:10:11,System.Runtime,Number of Active Timers,Metric,3
05/08/2025 15:10:11,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:10:11,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:10:11,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:11,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:11,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:11,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:10:11,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:10:11,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:10:11,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:10:11,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:10:11,System.Runtime,Number of Assemblies Loaded,Metric,144
05/08/2025 15:10:11,System.Runtime,IL Bytes Jitted (B),Metric,354393
05/08/2025 15:10:11,System.Runtime,Number of Methods Jitted,Metric,4821
05/08/2025 15:10:11,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,4.189300000000003
05/08/2025 15:10:12,System.Runtime,CPU Usage (%),Metric,0.1953125
05/08/2025 15:10:12,System.Runtime,Working Set (MB),Metric,111.812608
05/08/2025 15:10:12,System.Runtime,GC Heap Size (MB),Metric,8.46576
05/08/2025 15:10:12,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:12,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:12,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:12,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:10:12,System.Runtime,ThreadPool Thread Count,Metric,2
05/08/2025 15:10:12,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:12,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:12,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:10:12,System.Runtime,Allocation Rate (B / 1 sec),Rate,8200
05/08/2025 15:10:12,System.Runtime,Number of Active Timers,Metric,3
05/08/2025 15:10:12,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:10:12,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:10:12,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:12,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:12,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:12,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:10:12,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:10:12,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:10:12,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:10:12,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:10:12,System.Runtime,Number of Assemblies Loaded,Metric,144
05/08/2025 15:10:12,System.Runtime,IL Bytes Jitted (B),Metric,355589
05/08/2025 15:10:12,System.Runtime,Number of Methods Jitted,Metric,4824
05/08/2025 15:10:12,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,9.337600000000066
05/08/2025 15:10:13,System.Runtime,CPU Usage (%),Metric,0
05/08/2025 15:10:13,System.Runtime,Working Set (MB),Metric,111.824896
05/08/2025 15:10:13,System.Runtime,GC Heap Size (MB),Metric,8.473984
05/08/2025 15:10:13,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:13,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:13,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:13,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:10:13,System.Runtime,ThreadPool Thread Count,Metric,2
05/08/2025 15:10:13,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:13,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:13,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:10:13,System.Runtime,Allocation Rate (B / 1 sec),Rate,0
05/08/2025 15:10:13,System.Runtime,Number of Active Timers,Metric,3
05/08/2025 15:10:13,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:10:13,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:10:13,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:13,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:13,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:13,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:10:13,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:10:13,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:10:13,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:10:13,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:10:13,System.Runtime,Number of Assemblies Loaded,Metric,144
05/08/2025 15:10:13,System.Runtime,IL Bytes Jitted (B),Metric,355589
05/08/2025 15:10:13,System.Runtime,Number of Methods Jitted,Metric,4824
05/08/2025 15:10:13,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:10:14,System.Runtime,CPU Usage (%),Metric,0.19569471624266144
05/08/2025 15:10:14,System.Runtime,Working Set (MB),Metric,111.833088
05/08/2025 15:10:14,System.Runtime,GC Heap Size (MB),Metric,8.482208
05/08/2025 15:10:14,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:14,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:14,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:14,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:10:14,System.Runtime,ThreadPool Thread Count,Metric,2
05/08/2025 15:10:14,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:14,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:14,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:10:14,System.Runtime,Allocation Rate (B / 1 sec),Rate,8200
05/08/2025 15:10:14,System.Runtime,Number of Active Timers,Metric,3
05/08/2025 15:10:14,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:10:14,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:10:14,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:14,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:14,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:14,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:10:14,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:10:14,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:10:14,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:10:14,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:10:14,System.Runtime,Number of Assemblies Loaded,Metric,144
05/08/2025 15:10:14,System.Runtime,IL Bytes Jitted (B),Metric,355589
05/08/2025 15:10:14,System.Runtime,Number of Methods Jitted,Metric,4824
05/08/2025 15:10:14,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:10:15,System.Runtime,CPU Usage (%),Metric,6.896551724137931
05/08/2025 15:10:15,System.Runtime,Working Set (MB),Metric,119.074816
05/08/2025 15:10:15,System.Runtime,GC Heap Size (MB),Metric,9.521576
05/08/2025 15:10:15,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:15,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:15,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:15,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:10:15,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:10:15,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:15,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:15,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,5
05/08/2025 15:10:15,System.Runtime,Allocation Rate (B / 1 sec),Rate,1044808
05/08/2025 15:10:15,System.Runtime,Number of Active Timers,Metric,3
05/08/2025 15:10:15,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:10:15,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:10:15,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:15,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:15,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:15,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:10:15,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:10:15,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:10:15,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:10:15,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:10:15,System.Runtime,Number of Assemblies Loaded,Metric,149
05/08/2025 15:10:15,System.Runtime,IL Bytes Jitted (B),Metric,439653
05/08/2025 15:10:15,System.Runtime,Number of Methods Jitted,Metric,6283
05/08/2025 15:10:15,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,337.1678999999999
05/08/2025 15:10:16,System.Runtime,CPU Usage (%),Metric,14.84375
05/08/2025 15:10:16,System.Runtime,Working Set (MB),Metric,159.17056
05/08/2025 15:10:16,System.Runtime,GC Heap Size (MB),Metric,11.02636
05/08/2025 15:10:16,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:16,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:16,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:16,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:10:16,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:10:16,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:16,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:16,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:10:16,System.Runtime,Allocation Rate (B / 1 sec),Rate,1492456
05/08/2025 15:10:16,System.Runtime,Number of Active Timers,Metric,3
05/08/2025 15:10:16,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:10:16,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:10:16,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:16,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:16,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:16,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:10:16,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:10:16,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:10:16,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:10:16,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:10:16,System.Runtime,Number of Assemblies Loaded,Metric,149
05/08/2025 15:10:16,System.Runtime,IL Bytes Jitted (B),Metric,520715
05/08/2025 15:10:16,System.Runtime,Number of Methods Jitted,Metric,7166
05/08/2025 15:10:16,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,419.17650000000003
05/08/2025 15:10:17,System.Runtime,CPU Usage (%),Metric,9.9609375
05/08/2025 15:10:17,System.Runtime,Working Set (MB),Metric,164.155392
05/08/2025 15:10:17,System.Runtime,GC Heap Size (MB),Metric,11.102656
05/08/2025 15:10:17,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:17,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:17,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:17,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:10:17,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:10:17,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:17,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:17,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:10:17,System.Runtime,Allocation Rate (B / 1 sec),Rate,76056
05/08/2025 15:10:17,System.Runtime,Number of Active Timers,Metric,3
05/08/2025 15:10:17,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:10:17,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:10:17,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:17,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:17,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:17,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:10:17,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:10:17,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:10:17,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:10:17,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:10:17,System.Runtime,Number of Assemblies Loaded,Metric,149
05/08/2025 15:10:17,System.Runtime,IL Bytes Jitted (B),Metric,598788
05/08/2025 15:10:17,System.Runtime,Number of Methods Jitted,Metric,7734
05/08/2025 15:10:17,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,803.9335000000001
05/08/2025 15:10:18,System.Runtime,CPU Usage (%),Metric,0.3968253968253968
05/08/2025 15:10:18,System.Runtime,Working Set (MB),Metric,166.232064
05/08/2025 15:10:18,System.Runtime,GC Heap Size (MB),Metric,11.182704
05/08/2025 15:10:18,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:18,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:18,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:18,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:10:18,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:10:18,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:18,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:18,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:10:18,System.Runtime,Allocation Rate (B / 1 sec),Rate,73040
05/08/2025 15:10:18,System.Runtime,Number of Active Timers,Metric,3
05/08/2025 15:10:18,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:10:18,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:10:18,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:18,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:18,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:18,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:10:18,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:10:18,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:10:18,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:10:18,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:10:18,System.Runtime,Number of Assemblies Loaded,Metric,149
05/08/2025 15:10:18,System.Runtime,IL Bytes Jitted (B),Metric,601012
05/08/2025 15:10:18,System.Runtime,Number of Methods Jitted,Metric,7750
05/08/2025 15:10:18,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,21.099099999999908
05/08/2025 15:10:19,System.Runtime,CPU Usage (%),Metric,12.6953125
05/08/2025 15:10:19,System.Runtime,Working Set (MB),Metric,219.922432
05/08/2025 15:10:19,System.Runtime,GC Heap Size (MB),Metric,54.25744
05/08/2025 15:10:19,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:19,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:19,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:19,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:10:19,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:10:19,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:19,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:19,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,3
05/08/2025 15:10:19,System.Runtime,Allocation Rate (B / 1 sec),Rate,42938968
05/08/2025 15:10:19,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:10:19,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:10:19,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:10:19,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:19,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:19,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:19,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:10:19,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:10:19,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:10:19,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:10:19,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:10:19,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:10:19,System.Runtime,IL Bytes Jitted (B),Metric,720531
05/08/2025 15:10:19,System.Runtime,Number of Methods Jitted,Metric,9319
05/08/2025 15:10:19,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,474.4045000000001
05/08/2025 15:10:20,System.Runtime,CPU Usage (%),Metric,11.328125
05/08/2025 15:10:20,System.Runtime,Working Set (MB),Metric,227.274752
05/08/2025 15:10:20,System.Runtime,GC Heap Size (MB),Metric,55.63892
05/08/2025 15:10:20,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:20,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:20,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:20,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:10:20,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:10:20,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,1
05/08/2025 15:10:20,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:20,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,18
05/08/2025 15:10:20,System.Runtime,Allocation Rate (B / 1 sec),Rate,1380664
05/08/2025 15:10:20,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:10:20,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:10:20,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:10:20,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:20,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:20,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:20,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:10:20,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:10:20,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:10:20,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:10:20,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:10:20,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:10:20,System.Runtime,IL Bytes Jitted (B),Metric,755838
05/08/2025 15:10:20,System.Runtime,Number of Methods Jitted,Metric,9848
05/08/2025 15:10:20,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,248.37069999999994
05/08/2025 15:10:21,System.Runtime,CPU Usage (%),Metric,5.019305019305019
05/08/2025 15:10:21,System.Runtime,Working Set (MB),Metric,227.84
05/08/2025 15:10:21,System.Runtime,GC Heap Size (MB),Metric,55.647112
05/08/2025 15:10:21,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:21,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:21,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:21,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:10:21,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:10:21,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:21,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:21,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:10:21,System.Runtime,Allocation Rate (B / 1 sec),Rate,8192
05/08/2025 15:10:21,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:10:21,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:10:21,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:10:21,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:21,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:21,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:21,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:10:21,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:10:21,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:10:21,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:10:21,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:10:21,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:10:21,System.Runtime,IL Bytes Jitted (B),Metric,794068
05/08/2025 15:10:21,System.Runtime,Number of Methods Jitted,Metric,10352
05/08/2025 15:10:21,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,395.1358
05/08/2025 15:10:22,System.Runtime,CPU Usage (%),Metric,5.8
05/08/2025 15:10:22,System.Runtime,Working Set (MB),Metric,230.121472
05/08/2025 15:10:22,System.Runtime,GC Heap Size (MB),Metric,56.874408
05/08/2025 15:10:22,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:22,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:22,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:22,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:10:22,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:10:22,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:22,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:22,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,4
05/08/2025 15:10:22,System.Runtime,Allocation Rate (B / 1 sec),Rate,1232208
05/08/2025 15:10:22,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:10:22,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:10:22,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:10:22,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:22,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:22,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:22,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:10:22,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:10:22,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:10:22,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:10:22,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:10:22,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:10:22,System.Runtime,IL Bytes Jitted (B),Metric,833492
05/08/2025 15:10:22,System.Runtime,Number of Methods Jitted,Metric,10558
05/08/2025 15:10:22,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,287.6769999999997
05/08/2025 15:10:23,System.Runtime,CPU Usage (%),Metric,0
05/08/2025 15:10:23,System.Runtime,Working Set (MB),Metric,230.137856
05/08/2025 15:10:23,System.Runtime,GC Heap Size (MB),Metric,56.890832
05/08/2025 15:10:23,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:23,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:23,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:23,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:10:23,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:10:23,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:23,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:23,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:10:23,System.Runtime,Allocation Rate (B / 1 sec),Rate,8200
05/08/2025 15:10:23,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:10:23,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:10:23,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:10:23,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:23,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:23,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:23,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:10:23,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:10:23,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:10:23,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:10:23,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:10:23,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:10:23,System.Runtime,IL Bytes Jitted (B),Metric,833492
05/08/2025 15:10:23,System.Runtime,Number of Methods Jitted,Metric,10558
05/08/2025 15:10:23,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:10:24,System.Runtime,CPU Usage (%),Metric,0.38910505836575876
05/08/2025 15:10:24,System.Runtime,Working Set (MB),Metric,230.158336
05/08/2025 15:10:24,System.Runtime,GC Heap Size (MB),Metric,56.907
05/08/2025 15:10:24,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:24,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:24,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:24,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:10:24,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:10:24,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:24,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:24,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:10:24,System.Runtime,Allocation Rate (B / 1 sec),Rate,8560
05/08/2025 15:10:24,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:10:24,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:10:24,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:10:24,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:24,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:24,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:24,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:10:24,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:10:24,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:10:24,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:10:24,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:10:24,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:10:24,System.Runtime,IL Bytes Jitted (B),Metric,833492
05/08/2025 15:10:24,System.Runtime,Number of Methods Jitted,Metric,10558
05/08/2025 15:10:24,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:10:25,System.Runtime,CPU Usage (%),Metric,18.59099804305284
05/08/2025 15:10:25,System.Runtime,Working Set (MB),Metric,243.5072
05/08/2025 15:10:25,System.Runtime,GC Heap Size (MB),Metric,42.5772
05/08/2025 15:10:25,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,1
05/08/2025 15:10:25,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:25,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:25,System.Runtime,Gen 0 GC Budget (MB),Metric,158
05/08/2025 15:10:25,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:10:25,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:25,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:25,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:10:25,System.Runtime,Allocation Rate (B / 1 sec),Rate,42856608
05/08/2025 15:10:25,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:10:25,System.Runtime,GC Fragmentation (%),Metric,4.011201972001151
05/08/2025 15:10:25,System.Runtime,GC Committed Bytes (MB),Metric,69.783552
05/08/2025 15:10:25,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:25,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:25,System.Runtime,Time paused by GC (ms / 1 sec),Rate,6.278
05/08/2025 15:10:25,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:10:25,System.Runtime,Gen 1 Size (B),Metric,4037688
05/08/2025 15:10:25,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:10:25,System.Runtime,LOH Size (B),Metric,3107632
05/08/2025 15:10:25,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:10:25,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:10:25,System.Runtime,IL Bytes Jitted (B),Metric,862287
05/08/2025 15:10:25,System.Runtime,Number of Methods Jitted,Metric,10981
05/08/2025 15:10:25,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,396.04129999999986
05/08/2025 15:10:26,System.Runtime,CPU Usage (%),Metric,5.544554455445544
05/08/2025 15:10:26,System.Runtime,Working Set (MB),Metric,245.645312
05/08/2025 15:10:26,System.Runtime,GC Heap Size (MB),Metric,43.124816
05/08/2025 15:10:26,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:26,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:26,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:26,System.Runtime,Gen 0 GC Budget (MB),Metric,158
05/08/2025 15:10:26,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:10:26,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:26,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:26,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,17
05/08/2025 15:10:26,System.Runtime,Allocation Rate (B / 1 sec),Rate,547472
05/08/2025 15:10:26,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:10:26,System.Runtime,GC Fragmentation (%),Metric,4.011201972001151
05/08/2025 15:10:26,System.Runtime,GC Committed Bytes (MB),Metric,69.783552
05/08/2025 15:10:26,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:26,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:26,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:26,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:10:26,System.Runtime,Gen 1 Size (B),Metric,4037688
05/08/2025 15:10:26,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:10:26,System.Runtime,LOH Size (B),Metric,3107632
05/08/2025 15:10:26,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:10:26,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:10:26,System.Runtime,IL Bytes Jitted (B),Metric,873122
05/08/2025 15:10:26,System.Runtime,Number of Methods Jitted,Metric,11219
05/08/2025 15:10:26,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,122.21610000000055
05/08/2025 15:10:27,System.Runtime,CPU Usage (%),Metric,4.117647058823529
05/08/2025 15:10:27,System.Runtime,Working Set (MB),Metric,246.263808
05/08/2025 15:10:27,System.Runtime,GC Heap Size (MB),Metric,44.382264
05/08/2025 15:10:27,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:27,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:27,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:27,System.Runtime,Gen 0 GC Budget (MB),Metric,158
05/08/2025 15:10:27,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:10:27,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:27,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:27,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,5
05/08/2025 15:10:27,System.Runtime,Allocation Rate (B / 1 sec),Rate,1254016
05/08/2025 15:10:27,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:10:27,System.Runtime,GC Fragmentation (%),Metric,4.011201972001151
05/08/2025 15:10:27,System.Runtime,GC Committed Bytes (MB),Metric,69.783552
05/08/2025 15:10:27,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:27,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:27,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:27,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:10:27,System.Runtime,Gen 1 Size (B),Metric,4037688
05/08/2025 15:10:27,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:10:27,System.Runtime,LOH Size (B),Metric,3107632
05/08/2025 15:10:27,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:10:27,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:10:27,System.Runtime,IL Bytes Jitted (B),Metric,890738
05/08/2025 15:10:27,System.Runtime,Number of Methods Jitted,Metric,11404
05/08/2025 15:10:27,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,176.0636999999997
05/08/2025 15:10:28,System.Runtime,CPU Usage (%),Metric,0
05/08/2025 15:10:28,System.Runtime,Working Set (MB),Metric,245.112832
05/08/2025 15:10:28,System.Runtime,GC Heap Size (MB),Metric,44.406792
05/08/2025 15:10:28,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:28,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:28,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:28,System.Runtime,Gen 0 GC Budget (MB),Metric,158
05/08/2025 15:10:28,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:10:28,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:28,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:28,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:10:28,System.Runtime,Allocation Rate (B / 1 sec),Rate,16920
05/08/2025 15:10:28,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:10:28,System.Runtime,GC Fragmentation (%),Metric,4.011201972001151
05/08/2025 15:10:28,System.Runtime,GC Committed Bytes (MB),Metric,69.783552
05/08/2025 15:10:28,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:28,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:28,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:28,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:10:28,System.Runtime,Gen 1 Size (B),Metric,4037688
05/08/2025 15:10:28,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:10:28,System.Runtime,LOH Size (B),Metric,3107632
05/08/2025 15:10:28,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:10:28,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:10:28,System.Runtime,IL Bytes Jitted (B),Metric,890760
05/08/2025 15:10:28,System.Runtime,Number of Methods Jitted,Metric,11405
05/08/2025 15:10:28,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0.751299999999901
05/08/2025 15:10:29,System.Runtime,CPU Usage (%),Metric,12.922465208747514
05/08/2025 15:10:29,System.Runtime,Working Set (MB),Metric,278.028288
05/08/2025 15:10:29,System.Runtime,GC Heap Size (MB),Metric,87.86548
05/08/2025 15:10:29,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:29,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:29,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:29,System.Runtime,Gen 0 GC Budget (MB),Metric,158
05/08/2025 15:10:29,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:10:29,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:29,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:29,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:10:29,System.Runtime,Allocation Rate (B / 1 sec),Rate,43331640
05/08/2025 15:10:29,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:10:29,System.Runtime,GC Fragmentation (%),Metric,4.011201972001151
05/08/2025 15:10:29,System.Runtime,GC Committed Bytes (MB),Metric,69.783552
05/08/2025 15:10:29,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:29,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:29,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:29,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:10:29,System.Runtime,Gen 1 Size (B),Metric,4037688
05/08/2025 15:10:29,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:10:29,System.Runtime,LOH Size (B),Metric,3107632
05/08/2025 15:10:29,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:10:29,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:10:29,System.Runtime,IL Bytes Jitted (B),Metric,901098
05/08/2025 15:10:29,System.Runtime,Number of Methods Jitted,Metric,11538
05/08/2025 15:10:29,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,123
05/08/2025 15:10:30,System.Runtime,CPU Usage (%),Metric,1.147227533460803
05/08/2025 15:10:30,System.Runtime,Working Set (MB),Metric,277.909504
05/08/2025 15:10:30,System.Runtime,GC Heap Size (MB),Metric,87.890096
05/08/2025 15:10:30,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:30,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:30,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:30,System.Runtime,Gen 0 GC Budget (MB),Metric,158
05/08/2025 15:10:30,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:10:30,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:30,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:30,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,12
05/08/2025 15:10:30,System.Runtime,Allocation Rate (B / 1 sec),Rate,16400
05/08/2025 15:10:30,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:10:30,System.Runtime,GC Fragmentation (%),Metric,4.011201972001151
05/08/2025 15:10:30,System.Runtime,GC Committed Bytes (MB),Metric,69.783552
05/08/2025 15:10:30,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:30,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:30,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:30,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:10:30,System.Runtime,Gen 1 Size (B),Metric,4037688
05/08/2025 15:10:30,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:10:30,System.Runtime,LOH Size (B),Metric,3107632
05/08/2025 15:10:30,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:10:30,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:10:30,System.Runtime,IL Bytes Jitted (B),Metric,905220
05/08/2025 15:10:30,System.Runtime,Number of Methods Jitted,Metric,11604
05/08/2025 15:10:30,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,149.60630000000037
05/08/2025 15:10:31,System.Runtime,CPU Usage (%),Metric,3.4136546184738954
05/08/2025 15:10:31,System.Runtime,Working Set (MB),Metric,279.289856
05/08/2025 15:10:31,System.Runtime,GC Heap Size (MB),Metric,89.078632
05/08/2025 15:10:31,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:31,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:31,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:31,System.Runtime,Gen 0 GC Budget (MB),Metric,158
05/08/2025 15:10:31,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:10:31,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:31,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:31,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,3
05/08/2025 15:10:31,System.Runtime,Allocation Rate (B / 1 sec),Rate,1185392
05/08/2025 15:10:31,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:10:31,System.Runtime,GC Fragmentation (%),Metric,4.011201972001151
05/08/2025 15:10:31,System.Runtime,GC Committed Bytes (MB),Metric,69.783552
05/08/2025 15:10:31,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:31,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:31,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:31,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:10:31,System.Runtime,Gen 1 Size (B),Metric,4037688
05/08/2025 15:10:31,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:10:31,System.Runtime,LOH Size (B),Metric,3107632
05/08/2025 15:10:31,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:10:31,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:10:31,System.Runtime,IL Bytes Jitted (B),Metric,919780
05/08/2025 15:10:31,System.Runtime,Number of Methods Jitted,Metric,11731
05/08/2025 15:10:31,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,122.01069999999982
05/08/2025 15:10:32,System.Runtime,CPU Usage (%),Metric,8.123791102514506
05/08/2025 15:10:32,System.Runtime,Working Set (MB),Metric,317.014016
05/08/2025 15:10:32,System.Runtime,GC Heap Size (MB),Metric,131.397528
05/08/2025 15:10:32,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:32,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:32,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:32,System.Runtime,Gen 0 GC Budget (MB),Metric,158
05/08/2025 15:10:32,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:10:32,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:32,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:32,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:10:32,System.Runtime,Allocation Rate (B / 1 sec),Rate,42186104
05/08/2025 15:10:32,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:10:32,System.Runtime,GC Fragmentation (%),Metric,4.011201972001151
05/08/2025 15:10:32,System.Runtime,GC Committed Bytes (MB),Metric,69.783552
05/08/2025 15:10:32,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:32,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:32,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:32,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:10:32,System.Runtime,Gen 1 Size (B),Metric,4037688
05/08/2025 15:10:32,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:10:32,System.Runtime,LOH Size (B),Metric,3107632
05/08/2025 15:10:32,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:10:32,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:10:32,System.Runtime,IL Bytes Jitted (B),Metric,932936
05/08/2025 15:10:32,System.Runtime,Number of Methods Jitted,Metric,11823
05/08/2025 15:10:32,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,155.9377999999997
05/08/2025 15:10:33,System.Runtime,CPU Usage (%),Metric,6.11439842209073
05/08/2025 15:10:33,System.Runtime,Working Set (MB),Metric,320.59392
05/08/2025 15:10:33,System.Runtime,GC Heap Size (MB),Metric,132.623664
05/08/2025 15:10:33,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:33,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:33,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:33,System.Runtime,Gen 0 GC Budget (MB),Metric,158
05/08/2025 15:10:33,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:10:33,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:33,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:33,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,12
05/08/2025 15:10:33,System.Runtime,Allocation Rate (B / 1 sec),Rate,1217544
05/08/2025 15:10:33,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:10:33,System.Runtime,GC Fragmentation (%),Metric,4.011201972001151
05/08/2025 15:10:33,System.Runtime,GC Committed Bytes (MB),Metric,69.783552
05/08/2025 15:10:33,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:33,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:33,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:33,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:10:33,System.Runtime,Gen 1 Size (B),Metric,4037688
05/08/2025 15:10:33,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:10:33,System.Runtime,LOH Size (B),Metric,3107632
05/08/2025 15:10:33,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:10:33,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:10:33,System.Runtime,IL Bytes Jitted (B),Metric,936594
05/08/2025 15:10:33,System.Runtime,Number of Methods Jitted,Metric,11893
05/08/2025 15:10:33,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,50.041299999999865
05/08/2025 15:10:34,System.Runtime,CPU Usage (%),Metric,2.4714828897338403
05/08/2025 15:10:34,System.Runtime,Working Set (MB),Metric,322.084864
05/08/2025 15:10:34,System.Runtime,GC Heap Size (MB),Metric,133.85212
05/08/2025 15:10:34,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:34,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:34,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:34,System.Runtime,Gen 0 GC Budget (MB),Metric,158
05/08/2025 15:10:34,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:10:34,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:34,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:34,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,4
05/08/2025 15:10:34,System.Runtime,Allocation Rate (B / 1 sec),Rate,1225144
05/08/2025 15:10:34,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:10:34,System.Runtime,GC Fragmentation (%),Metric,4.011201972001151
05/08/2025 15:10:34,System.Runtime,GC Committed Bytes (MB),Metric,69.783552
05/08/2025 15:10:34,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:34,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:34,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:34,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:10:34,System.Runtime,Gen 1 Size (B),Metric,4037688
05/08/2025 15:10:34,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:10:34,System.Runtime,LOH Size (B),Metric,3107632
05/08/2025 15:10:34,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:10:34,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:10:34,System.Runtime,IL Bytes Jitted (B),Metric,945547
05/08/2025 15:10:34,System.Runtime,Number of Methods Jitted,Metric,11955
05/08/2025 15:10:34,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,51.76860000000033
05/08/2025 15:10:35,System.Runtime,CPU Usage (%),Metric,0
05/08/2025 15:10:35,System.Runtime,Working Set (MB),Metric,322.10944
05/08/2025 15:10:35,System.Runtime,GC Heap Size (MB),Metric,133.868568
05/08/2025 15:10:35,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:35,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:35,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:35,System.Runtime,Gen 0 GC Budget (MB),Metric,158
05/08/2025 15:10:35,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:10:35,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:35,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:35,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:10:35,System.Runtime,Allocation Rate (B / 1 sec),Rate,16400
05/08/2025 15:10:35,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:10:35,System.Runtime,GC Fragmentation (%),Metric,4.011201972001151
05/08/2025 15:10:35,System.Runtime,GC Committed Bytes (MB),Metric,69.783552
05/08/2025 15:10:35,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:35,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:35,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:35,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:10:35,System.Runtime,Gen 1 Size (B),Metric,4037688
05/08/2025 15:10:35,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:10:35,System.Runtime,LOH Size (B),Metric,3107632
05/08/2025 15:10:35,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:10:35,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:10:35,System.Runtime,IL Bytes Jitted (B),Metric,945547
05/08/2025 15:10:35,System.Runtime,Number of Methods Jitted,Metric,11955
05/08/2025 15:10:35,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:10:36,System.Runtime,CPU Usage (%),Metric,7.8277886497064575
05/08/2025 15:10:36,System.Runtime,Working Set (MB),Metric,319.504384
05/08/2025 15:10:36,System.Runtime,GC Heap Size (MB),Metric,20.853928
05/08/2025 15:10:36,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,1
05/08/2025 15:10:36,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,1
05/08/2025 15:10:36,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:36,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:10:36,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:10:36,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:36,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:36,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:10:36,System.Runtime,Allocation Rate (B / 1 sec),Rate,42163320
05/08/2025 15:10:36,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:10:36,System.Runtime,GC Fragmentation (%),Metric,2.2085988252630706
05/08/2025 15:10:36,System.Runtime,GC Committed Bytes (MB),Metric,81.911808
05/08/2025 15:10:36,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:36,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:36,System.Runtime,Time paused by GC (ms / 1 sec),Rate,4.211000000000001
05/08/2025 15:10:36,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:10:36,System.Runtime,Gen 1 Size (B),Metric,305776
05/08/2025 15:10:36,System.Runtime,Gen 2 Size (B),Metric,3888416
05/08/2025 15:10:36,System.Runtime,LOH Size (B),Metric,7392568
05/08/2025 15:10:36,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:10:36,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:10:36,System.Runtime,IL Bytes Jitted (B),Metric,948323
05/08/2025 15:10:36,System.Runtime,Number of Methods Jitted,Metric,11984
05/08/2025 15:10:36,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,34.64930000000004
05/08/2025 15:10:37,System.Runtime,CPU Usage (%),Metric,7.869481765834933
05/08/2025 15:10:37,System.Runtime,Working Set (MB),Metric,265.703424
05/08/2025 15:10:37,System.Runtime,GC Heap Size (MB),Metric,22.054416
05/08/2025 15:10:37,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:37,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:37,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:37,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:10:37,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:10:37,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:37,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:37,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,12
05/08/2025 15:10:37,System.Runtime,Allocation Rate (B / 1 sec),Rate,1199984
05/08/2025 15:10:37,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:10:37,System.Runtime,GC Fragmentation (%),Metric,2.2085988252630706
05/08/2025 15:10:37,System.Runtime,GC Committed Bytes (MB),Metric,81.911808
05/08/2025 15:10:37,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:37,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:37,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:37,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:10:37,System.Runtime,Gen 1 Size (B),Metric,305776
05/08/2025 15:10:37,System.Runtime,Gen 2 Size (B),Metric,3888416
05/08/2025 15:10:37,System.Runtime,LOH Size (B),Metric,7392568
05/08/2025 15:10:37,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:10:37,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:10:37,System.Runtime,IL Bytes Jitted (B),Metric,958697
05/08/2025 15:10:37,System.Runtime,Number of Methods Jitted,Metric,12095
05/08/2025 15:10:37,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,174.9980000000005
05/08/2025 15:10:38,System.Runtime,CPU Usage (%),Metric,1.953125
05/08/2025 15:10:38,System.Runtime,Working Set (MB),Metric,266.072064
05/08/2025 15:10:38,System.Runtime,GC Heap Size (MB),Metric,23.249656
05/08/2025 15:10:38,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:38,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:38,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:38,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:10:38,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:10:38,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:38,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:38,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,3
05/08/2025 15:10:38,System.Runtime,Allocation Rate (B / 1 sec),Rate,1192048
05/08/2025 15:10:38,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:10:38,System.Runtime,GC Fragmentation (%),Metric,2.2085988252630706
05/08/2025 15:10:38,System.Runtime,GC Committed Bytes (MB),Metric,81.911808
05/08/2025 15:10:38,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:38,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:38,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:38,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:10:38,System.Runtime,Gen 1 Size (B),Metric,305776
05/08/2025 15:10:38,System.Runtime,Gen 2 Size (B),Metric,3888416
05/08/2025 15:10:38,System.Runtime,LOH Size (B),Metric,7392568
05/08/2025 15:10:38,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:10:38,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:10:38,System.Runtime,IL Bytes Jitted (B),Metric,968986
05/08/2025 15:10:38,System.Runtime,Number of Methods Jitted,Metric,12171
05/08/2025 15:10:38,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,83.08599999999933
05/08/2025 15:10:39,System.Runtime,CPU Usage (%),Metric,0.8032128514056225
05/08/2025 15:10:39,System.Runtime,Working Set (MB),Metric,266.092544
05/08/2025 15:10:39,System.Runtime,GC Heap Size (MB),Metric,23.25788
05/08/2025 15:10:39,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:39,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:39,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:39,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:10:39,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:10:39,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:39,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:39,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:10:39,System.Runtime,Allocation Rate (B / 1 sec),Rate,8200
05/08/2025 15:10:39,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:10:39,System.Runtime,GC Fragmentation (%),Metric,2.2085988252630706
05/08/2025 15:10:39,System.Runtime,GC Committed Bytes (MB),Metric,81.911808
05/08/2025 15:10:39,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:39,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:39,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:39,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:10:39,System.Runtime,Gen 1 Size (B),Metric,305776
05/08/2025 15:10:39,System.Runtime,Gen 2 Size (B),Metric,3888416
05/08/2025 15:10:39,System.Runtime,LOH Size (B),Metric,7392568
05/08/2025 15:10:39,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:10:39,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:10:39,System.Runtime,IL Bytes Jitted (B),Metric,968986
05/08/2025 15:10:39,System.Runtime,Number of Methods Jitted,Metric,12171
05/08/2025 15:10:39,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:10:40,System.Runtime,CPU Usage (%),Metric,0.1941747572815534
05/08/2025 15:10:40,System.Runtime,Working Set (MB),Metric,266.088448
05/08/2025 15:10:40,System.Runtime,GC Heap Size (MB),Metric,23.274328
05/08/2025 15:10:40,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:40,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:40,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:40,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:10:40,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:10:40,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:40,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:40,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:10:40,System.Runtime,Allocation Rate (B / 1 sec),Rate,24600
05/08/2025 15:10:40,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:10:40,System.Runtime,GC Fragmentation (%),Metric,2.2085988252630706
05/08/2025 15:10:40,System.Runtime,GC Committed Bytes (MB),Metric,81.911808
05/08/2025 15:10:40,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:40,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:40,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:40,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:10:40,System.Runtime,Gen 1 Size (B),Metric,305776
05/08/2025 15:10:40,System.Runtime,Gen 2 Size (B),Metric,3888416
05/08/2025 15:10:40,System.Runtime,LOH Size (B),Metric,7392568
05/08/2025 15:10:40,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:10:40,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:10:40,System.Runtime,IL Bytes Jitted (B),Metric,968986
05/08/2025 15:10:40,System.Runtime,Number of Methods Jitted,Metric,12171
05/08/2025 15:10:40,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:10:41,System.Runtime,CPU Usage (%),Metric,9.369024856596559
05/08/2025 15:10:41,System.Runtime,Working Set (MB),Metric,267.276288
05/08/2025 15:10:41,System.Runtime,GC Heap Size (MB),Metric,66.223672
05/08/2025 15:10:41,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:41,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:41,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:41,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:10:41,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:10:41,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:41,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:41,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:10:41,System.Runtime,Allocation Rate (B / 1 sec),Rate,42799616
05/08/2025 15:10:41,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:10:41,System.Runtime,GC Fragmentation (%),Metric,2.2085988252630706
05/08/2025 15:10:41,System.Runtime,GC Committed Bytes (MB),Metric,81.911808
05/08/2025 15:10:41,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:41,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:41,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:41,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:10:41,System.Runtime,Gen 1 Size (B),Metric,305776
05/08/2025 15:10:41,System.Runtime,Gen 2 Size (B),Metric,3888416
05/08/2025 15:10:41,System.Runtime,LOH Size (B),Metric,7392568
05/08/2025 15:10:41,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:10:41,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:10:41,System.Runtime,IL Bytes Jitted (B),Metric,974495
05/08/2025 15:10:41,System.Runtime,Number of Methods Jitted,Metric,12240
05/08/2025 15:10:41,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,78.19740000000002
05/08/2025 15:10:42,System.Runtime,CPU Usage (%),Metric,2.7559055118110236
05/08/2025 15:10:42,System.Runtime,Working Set (MB),Metric,269.172736
05/08/2025 15:10:42,System.Runtime,GC Heap Size (MB),Metric,66.77108
05/08/2025 15:10:42,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:42,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:42,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:42,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:10:42,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:10:42,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:42,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:42,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,12
05/08/2025 15:10:42,System.Runtime,Allocation Rate (B / 1 sec),Rate,547264
05/08/2025 15:10:42,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:10:42,System.Runtime,GC Fragmentation (%),Metric,2.2085988252630706
05/08/2025 15:10:42,System.Runtime,GC Committed Bytes (MB),Metric,81.911808
05/08/2025 15:10:42,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:42,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:42,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:42,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:10:42,System.Runtime,Gen 1 Size (B),Metric,305776
05/08/2025 15:10:42,System.Runtime,Gen 2 Size (B),Metric,3888416
05/08/2025 15:10:42,System.Runtime,LOH Size (B),Metric,7392568
05/08/2025 15:10:42,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:10:42,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:10:42,System.Runtime,IL Bytes Jitted (B),Metric,978950
05/08/2025 15:10:42,System.Runtime,Number of Methods Jitted,Metric,12299
05/08/2025 15:10:42,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,46.22350000000006
05/08/2025 15:10:43,System.Runtime,CPU Usage (%),Metric,3.762376237623762
05/08/2025 15:10:43,System.Runtime,Working Set (MB),Metric,269.570048
05/08/2025 15:10:43,System.Runtime,GC Heap Size (MB),Metric,67.98424
05/08/2025 15:10:43,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:43,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:43,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:43,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:10:43,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:10:43,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:43,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:43,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,3
05/08/2025 15:10:43,System.Runtime,Allocation Rate (B / 1 sec),Rate,1218120
05/08/2025 15:10:43,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:10:43,System.Runtime,GC Fragmentation (%),Metric,2.2085988252630706
05/08/2025 15:10:43,System.Runtime,GC Committed Bytes (MB),Metric,81.911808
05/08/2025 15:10:43,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:43,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:43,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:43,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:10:43,System.Runtime,Gen 1 Size (B),Metric,305776
05/08/2025 15:10:43,System.Runtime,Gen 2 Size (B),Metric,3888416
05/08/2025 15:10:43,System.Runtime,LOH Size (B),Metric,7392568
05/08/2025 15:10:43,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:10:43,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:10:43,System.Runtime,IL Bytes Jitted (B),Metric,985984
05/08/2025 15:10:43,System.Runtime,Number of Methods Jitted,Metric,12417
05/08/2025 15:10:43,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,117.64969999999994
05/08/2025 15:10:44,System.Runtime,CPU Usage (%),Metric,0.3824091778202677
05/08/2025 15:10:44,System.Runtime,Working Set (MB),Metric,269.565952
05/08/2025 15:10:44,System.Runtime,GC Heap Size (MB),Metric,68.008792
05/08/2025 15:10:44,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:44,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:44,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:44,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:10:44,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:10:44,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:44,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:44,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:10:44,System.Runtime,Allocation Rate (B / 1 sec),Rate,8816
05/08/2025 15:10:44,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:10:44,System.Runtime,GC Fragmentation (%),Metric,2.2085988252630706
05/08/2025 15:10:44,System.Runtime,GC Committed Bytes (MB),Metric,81.911808
05/08/2025 15:10:44,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:44,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:44,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:44,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:10:44,System.Runtime,Gen 1 Size (B),Metric,305776
05/08/2025 15:10:44,System.Runtime,Gen 2 Size (B),Metric,3888416
05/08/2025 15:10:44,System.Runtime,LOH Size (B),Metric,7392568
05/08/2025 15:10:44,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:10:44,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:10:44,System.Runtime,IL Bytes Jitted (B),Metric,985984
05/08/2025 15:10:44,System.Runtime,Number of Methods Jitted,Metric,12417
05/08/2025 15:10:44,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:10:45,System.Runtime,CPU Usage (%),Metric,10.216110019646365
05/08/2025 15:10:45,System.Runtime,Working Set (MB),Metric,246.706176
05/08/2025 15:10:45,System.Runtime,GC Heap Size (MB),Metric,52.849224
05/08/2025 15:10:45,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,1
05/08/2025 15:10:45,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:45,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:45,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:10:45,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:10:45,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,1
05/08/2025 15:10:45,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:45,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,12
05/08/2025 15:10:45,System.Runtime,Allocation Rate (B / 1 sec),Rate,43333320
05/08/2025 15:10:45,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:10:45,System.Runtime,GC Fragmentation (%),Metric,1.7834571526712504
05/08/2025 15:10:45,System.Runtime,GC Committed Bytes (MB),Metric,61.325312
05/08/2025 15:10:45,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:45,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:45,System.Runtime,Time paused by GC (ms / 1 sec),Rate,2.0299999999999994
05/08/2025 15:10:45,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:10:45,System.Runtime,Gen 1 Size (B),Metric,462984
05/08/2025 15:10:45,System.Runtime,Gen 2 Size (B),Metric,3888416
05/08/2025 15:10:45,System.Runtime,LOH Size (B),Metric,10249192
05/08/2025 15:10:45,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:10:45,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:10:45,System.Runtime,IL Bytes Jitted (B),Metric,992985
05/08/2025 15:10:45,System.Runtime,Number of Methods Jitted,Metric,12496
05/08/2025 15:10:45,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,73.45750000000044
05/08/2025 15:10:46,System.Runtime,CPU Usage (%),Metric,0
05/08/2025 15:10:46,System.Runtime,Working Set (MB),Metric,246.673408
05/08/2025 15:10:46,System.Runtime,GC Heap Size (MB),Metric,52.857448
05/08/2025 15:10:46,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:46,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:46,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:46,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:10:46,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:10:46,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:46,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:46,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:10:46,System.Runtime,Allocation Rate (B / 1 sec),Rate,8200
05/08/2025 15:10:46,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:10:46,System.Runtime,GC Fragmentation (%),Metric,1.7834571526712504
05/08/2025 15:10:46,System.Runtime,GC Committed Bytes (MB),Metric,61.325312
05/08/2025 15:10:46,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:46,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:46,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:46,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:10:46,System.Runtime,Gen 1 Size (B),Metric,462984
05/08/2025 15:10:46,System.Runtime,Gen 2 Size (B),Metric,3888416
05/08/2025 15:10:46,System.Runtime,LOH Size (B),Metric,10249192
05/08/2025 15:10:46,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:10:46,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:10:46,System.Runtime,IL Bytes Jitted (B),Metric,992985
05/08/2025 15:10:46,System.Runtime,Number of Methods Jitted,Metric,12496
05/08/2025 15:10:46,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:10:47,System.Runtime,CPU Usage (%),Metric,2.3210831721470018
05/08/2025 15:10:47,System.Runtime,Working Set (MB),Metric,248.070144
05/08/2025 15:10:47,System.Runtime,GC Heap Size (MB),Metric,54.054664
05/08/2025 15:10:47,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:47,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:47,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:47,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:10:47,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:10:47,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:47,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:47,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,3
05/08/2025 15:10:47,System.Runtime,Allocation Rate (B / 1 sec),Rate,1194000
05/08/2025 15:10:47,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:10:47,System.Runtime,GC Fragmentation (%),Metric,1.7834571526712504
05/08/2025 15:10:47,System.Runtime,GC Committed Bytes (MB),Metric,61.325312
05/08/2025 15:10:47,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:47,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:47,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:47,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:10:47,System.Runtime,Gen 1 Size (B),Metric,462984
05/08/2025 15:10:47,System.Runtime,Gen 2 Size (B),Metric,3888416
05/08/2025 15:10:47,System.Runtime,LOH Size (B),Metric,10249192
05/08/2025 15:10:47,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:10:47,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:10:47,System.Runtime,IL Bytes Jitted (B),Metric,999266
05/08/2025 15:10:47,System.Runtime,Number of Methods Jitted,Metric,12529
05/08/2025 15:10:47,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,58.74290000000019
05/08/2025 15:10:48,System.Runtime,CPU Usage (%),Metric,0
05/08/2025 15:10:48,System.Runtime,Working Set (MB),Metric,248.102912
05/08/2025 15:10:48,System.Runtime,GC Heap Size (MB),Metric,54.079336
05/08/2025 15:10:48,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:48,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:48,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:48,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:10:48,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:10:48,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:48,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:48,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:10:48,System.Runtime,Allocation Rate (B / 1 sec),Rate,17016
05/08/2025 15:10:48,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:10:48,System.Runtime,GC Fragmentation (%),Metric,1.7834571526712504
05/08/2025 15:10:48,System.Runtime,GC Committed Bytes (MB),Metric,61.325312
05/08/2025 15:10:48,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:48,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:48,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:48,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:10:48,System.Runtime,Gen 1 Size (B),Metric,462984
05/08/2025 15:10:48,System.Runtime,Gen 2 Size (B),Metric,3888416
05/08/2025 15:10:48,System.Runtime,LOH Size (B),Metric,10249192
05/08/2025 15:10:48,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:10:48,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:10:48,System.Runtime,IL Bytes Jitted (B),Metric,999266
05/08/2025 15:10:48,System.Runtime,Number of Methods Jitted,Metric,12529
05/08/2025 15:10:48,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:10:49,System.Runtime,CPU Usage (%),Metric,12.5
05/08/2025 15:10:49,System.Runtime,Working Set (MB),Metric,260.788224
05/08/2025 15:10:49,System.Runtime,GC Heap Size (MB),Metric,39.353408
05/08/2025 15:10:49,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,1
05/08/2025 15:10:49,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:49,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:49,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:10:49,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:10:49,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:49,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:49,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,1
05/08/2025 15:10:49,System.Runtime,Allocation Rate (B / 1 sec),Rate,42843704
05/08/2025 15:10:49,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:10:49,System.Runtime,GC Fragmentation (%),Metric,1.6313457128939897
05/08/2025 15:10:49,System.Runtime,GC Committed Bytes (MB),Metric,80.113664
05/08/2025 15:10:49,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:49,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:49,System.Runtime,Time paused by GC (ms / 1 sec),Rate,1.048
05/08/2025 15:10:49,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:10:49,System.Runtime,Gen 1 Size (B),Metric,520704
05/08/2025 15:10:49,System.Runtime,Gen 2 Size (B),Metric,3888416
05/08/2025 15:10:49,System.Runtime,LOH Size (B),Metric,11677504
05/08/2025 15:10:49,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:10:49,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:10:49,System.Runtime,IL Bytes Jitted (B),Metric,1003942
05/08/2025 15:10:49,System.Runtime,Number of Methods Jitted,Metric,12566
05/08/2025 15:10:49,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,75.33119999999963
05/08/2025 15:10:50,System.Runtime,CPU Usage (%),Metric,2.734375
05/08/2025 15:10:50,System.Runtime,Working Set (MB),Metric,262.631424
05/08/2025 15:10:50,System.Runtime,GC Heap Size (MB),Metric,39.8898
05/08/2025 15:10:50,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:50,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:50,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:50,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:10:50,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:10:50,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:50,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:50,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,11
05/08/2025 15:10:50,System.Runtime,Allocation Rate (B / 1 sec),Rate,536248
05/08/2025 15:10:50,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:10:50,System.Runtime,GC Fragmentation (%),Metric,1.6313457128939897
05/08/2025 15:10:50,System.Runtime,GC Committed Bytes (MB),Metric,80.113664
05/08/2025 15:10:50,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:50,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:50,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:50,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:10:50,System.Runtime,Gen 1 Size (B),Metric,520704
05/08/2025 15:10:50,System.Runtime,Gen 2 Size (B),Metric,3888416
05/08/2025 15:10:50,System.Runtime,LOH Size (B),Metric,11677504
05/08/2025 15:10:50,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:10:50,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:10:50,System.Runtime,IL Bytes Jitted (B),Metric,1007378
05/08/2025 15:10:50,System.Runtime,Number of Methods Jitted,Metric,12642
05/08/2025 15:10:50,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,47.53269999999975
05/08/2025 15:10:51,System.Runtime,CPU Usage (%),Metric,2.357563850687623
05/08/2025 15:10:51,System.Runtime,Working Set (MB),Metric,263.028736
05/08/2025 15:10:51,System.Runtime,GC Heap Size (MB),Metric,41.084736
05/08/2025 15:10:51,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:51,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:51,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:51,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:10:51,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:10:51,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:51,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:51,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,3
05/08/2025 15:10:51,System.Runtime,Allocation Rate (B / 1 sec),Rate,1191672
05/08/2025 15:10:51,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:10:51,System.Runtime,GC Fragmentation (%),Metric,1.6313457128939897
05/08/2025 15:10:51,System.Runtime,GC Committed Bytes (MB),Metric,80.113664
05/08/2025 15:10:51,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:51,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:51,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:51,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:10:51,System.Runtime,Gen 1 Size (B),Metric,520704
05/08/2025 15:10:51,System.Runtime,Gen 2 Size (B),Metric,3888416
05/08/2025 15:10:51,System.Runtime,LOH Size (B),Metric,11677504
05/08/2025 15:10:51,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:10:51,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:10:51,System.Runtime,IL Bytes Jitted (B),Metric,1021065
05/08/2025 15:10:51,System.Runtime,Number of Methods Jitted,Metric,12730
05/08/2025 15:10:51,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,87.98260000000028
05/08/2025 15:10:52,System.Runtime,CPU Usage (%),Metric,0.19193857965451055
05/08/2025 15:10:52,System.Runtime,Working Set (MB),Metric,263.02464
05/08/2025 15:10:52,System.Runtime,GC Heap Size (MB),Metric,41.109408
05/08/2025 15:10:52,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:52,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:52,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:52,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:10:52,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:10:52,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:52,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:52,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:10:52,System.Runtime,Allocation Rate (B / 1 sec),Rate,17016
05/08/2025 15:10:52,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:10:52,System.Runtime,GC Fragmentation (%),Metric,1.6313457128939897
05/08/2025 15:10:52,System.Runtime,GC Committed Bytes (MB),Metric,80.113664
05/08/2025 15:10:52,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:52,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:52,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:52,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:10:52,System.Runtime,Gen 1 Size (B),Metric,520704
05/08/2025 15:10:52,System.Runtime,Gen 2 Size (B),Metric,3888416
05/08/2025 15:10:52,System.Runtime,LOH Size (B),Metric,11677504
05/08/2025 15:10:52,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:10:52,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:10:52,System.Runtime,IL Bytes Jitted (B),Metric,1021065
05/08/2025 15:10:52,System.Runtime,Number of Methods Jitted,Metric,12730
05/08/2025 15:10:52,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:10:53,System.Runtime,CPU Usage (%),Metric,11.354581673306773
05/08/2025 15:10:53,System.Runtime,Working Set (MB),Metric,268.025856
05/08/2025 15:10:53,System.Runtime,GC Heap Size (MB),Metric,24.949512
05/08/2025 15:10:53,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,1
05/08/2025 15:10:53,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:53,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:53,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:10:53,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:10:53,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:53,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:53,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,9
05/08/2025 15:10:53,System.Runtime,Allocation Rate (B / 1 sec),Rate,43321160
05/08/2025 15:10:53,System.Runtime,Number of Active Timers,Metric,5
05/08/2025 15:10:53,System.Runtime,GC Fragmentation (%),Metric,1.4987010125875733
05/08/2025 15:10:53,System.Runtime,GC Committed Bytes (MB),Metric,85.01248
05/08/2025 15:10:53,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:53,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:53,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0.8919999999999995
05/08/2025 15:10:53,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:10:53,System.Runtime,Gen 1 Size (B),Metric,632504
05/08/2025 15:10:53,System.Runtime,Gen 2 Size (B),Metric,3888416
05/08/2025 15:10:53,System.Runtime,LOH Size (B),Metric,13105816
05/08/2025 15:10:53,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:10:53,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:10:53,System.Runtime,IL Bytes Jitted (B),Metric,1033223
05/08/2025 15:10:53,System.Runtime,Number of Methods Jitted,Metric,12845
05/08/2025 15:10:53,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,122.61819999999989
05/08/2025 15:10:54,System.Runtime,CPU Usage (%),Metric,0
05/08/2025 15:10:54,System.Runtime,Working Set (MB),Metric,268.017664
05/08/2025 15:10:54,System.Runtime,GC Heap Size (MB),Metric,24.957736
05/08/2025 15:10:54,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:54,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:54,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:54,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:10:54,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:10:54,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:54,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:54,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,3
05/08/2025 15:10:54,System.Runtime,Allocation Rate (B / 1 sec),Rate,8200
05/08/2025 15:10:54,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:10:54,System.Runtime,GC Fragmentation (%),Metric,1.4987010125875733
05/08/2025 15:10:54,System.Runtime,GC Committed Bytes (MB),Metric,85.01248
05/08/2025 15:10:54,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:54,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:54,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:54,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:10:54,System.Runtime,Gen 1 Size (B),Metric,632504
05/08/2025 15:10:54,System.Runtime,Gen 2 Size (B),Metric,3888416
05/08/2025 15:10:54,System.Runtime,LOH Size (B),Metric,13105816
05/08/2025 15:10:54,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:10:54,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:10:54,System.Runtime,IL Bytes Jitted (B),Metric,1033894
05/08/2025 15:10:54,System.Runtime,Number of Methods Jitted,Metric,12854
05/08/2025 15:10:54,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,2.8918000000003303
05/08/2025 15:10:55,System.Runtime,CPU Usage (%),Metric,1.941747572815534
05/08/2025 15:10:55,System.Runtime,Working Set (MB),Metric,268.263424
05/08/2025 15:10:55,System.Runtime,GC Heap Size (MB),Metric,26.15256
05/08/2025 15:10:55,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:55,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:55,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:55,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:10:55,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:10:55,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,1
05/08/2025 15:10:55,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:55,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,3
05/08/2025 15:10:55,System.Runtime,Allocation Rate (B / 1 sec),Rate,1191680
05/08/2025 15:10:55,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:10:55,System.Runtime,GC Fragmentation (%),Metric,1.4987010125875733
05/08/2025 15:10:55,System.Runtime,GC Committed Bytes (MB),Metric,85.01248
05/08/2025 15:10:55,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:55,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:55,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:55,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:10:55,System.Runtime,Gen 1 Size (B),Metric,632504
05/08/2025 15:10:55,System.Runtime,Gen 2 Size (B),Metric,3888416
05/08/2025 15:10:55,System.Runtime,LOH Size (B),Metric,13105816
05/08/2025 15:10:55,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:10:55,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:10:55,System.Runtime,IL Bytes Jitted (B),Metric,1035319
05/08/2025 15:10:55,System.Runtime,Number of Methods Jitted,Metric,12879
05/08/2025 15:10:55,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,21.957899999999427
05/08/2025 15:10:56,System.Runtime,CPU Usage (%),Metric,0.19342359767891681
05/08/2025 15:10:56,System.Runtime,Working Set (MB),Metric,268.288
05/08/2025 15:10:56,System.Runtime,GC Heap Size (MB),Metric,26.177232
05/08/2025 15:10:56,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:56,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:56,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:56,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:10:56,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:10:56,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:56,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:56,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:10:56,System.Runtime,Allocation Rate (B / 1 sec),Rate,24600
05/08/2025 15:10:56,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:10:56,System.Runtime,GC Fragmentation (%),Metric,1.4987010125875733
05/08/2025 15:10:56,System.Runtime,GC Committed Bytes (MB),Metric,85.01248
05/08/2025 15:10:56,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:56,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:56,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:56,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:10:56,System.Runtime,Gen 1 Size (B),Metric,632504
05/08/2025 15:10:56,System.Runtime,Gen 2 Size (B),Metric,3888416
05/08/2025 15:10:56,System.Runtime,LOH Size (B),Metric,13105816
05/08/2025 15:10:56,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:10:56,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:10:56,System.Runtime,IL Bytes Jitted (B),Metric,1035337
05/08/2025 15:10:56,System.Runtime,Number of Methods Jitted,Metric,12880
05/08/2025 15:10:56,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0.266500000000633
05/08/2025 15:10:57,System.Runtime,CPU Usage (%),Metric,5.158730158730159
05/08/2025 15:10:57,System.Runtime,Working Set (MB),Metric,270.983168
05/08/2025 15:10:57,System.Runtime,GC Heap Size (MB),Metric,68.540072
05/08/2025 15:10:57,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:57,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:57,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:57,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:10:57,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:10:57,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:57,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:57,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,2
05/08/2025 15:10:57,System.Runtime,Allocation Rate (B / 1 sec),Rate,42230088
05/08/2025 15:10:57,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:10:57,System.Runtime,GC Fragmentation (%),Metric,1.4987010125875733
05/08/2025 15:10:57,System.Runtime,GC Committed Bytes (MB),Metric,85.01248
05/08/2025 15:10:57,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:57,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:57,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:57,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:10:57,System.Runtime,Gen 1 Size (B),Metric,632504
05/08/2025 15:10:57,System.Runtime,Gen 2 Size (B),Metric,3888416
05/08/2025 15:10:57,System.Runtime,LOH Size (B),Metric,13105816
05/08/2025 15:10:57,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:10:57,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:10:57,System.Runtime,IL Bytes Jitted (B),Metric,1039658
05/08/2025 15:10:57,System.Runtime,Number of Methods Jitted,Metric,12913
05/08/2025 15:10:57,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,42.04079999999976
05/08/2025 15:10:58,System.Runtime,CPU Usage (%),Metric,6.496062992125984
05/08/2025 15:10:58,System.Runtime,Working Set (MB),Metric,272.945152
05/08/2025 15:10:58,System.Runtime,GC Heap Size (MB),Metric,69.693656
05/08/2025 15:10:58,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:58,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:58,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:58,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:10:58,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:10:58,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:58,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:58,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,12
05/08/2025 15:10:58,System.Runtime,Allocation Rate (B / 1 sec),Rate,1145096
05/08/2025 15:10:58,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:10:58,System.Runtime,GC Fragmentation (%),Metric,1.4987010125875733
05/08/2025 15:10:58,System.Runtime,GC Committed Bytes (MB),Metric,85.01248
05/08/2025 15:10:58,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:58,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:58,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:58,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:10:58,System.Runtime,Gen 1 Size (B),Metric,632504
05/08/2025 15:10:58,System.Runtime,Gen 2 Size (B),Metric,3888416
05/08/2025 15:10:58,System.Runtime,LOH Size (B),Metric,13105816
05/08/2025 15:10:58,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:10:58,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:10:58,System.Runtime,IL Bytes Jitted (B),Metric,1044904
05/08/2025 15:10:58,System.Runtime,Number of Methods Jitted,Metric,12987
05/08/2025 15:10:58,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,55.21770000000015
05/08/2025 15:10:59,System.Runtime,CPU Usage (%),Metric,3.6259541984732824
05/08/2025 15:10:59,System.Runtime,Working Set (MB),Metric,275.349504
05/08/2025 15:10:59,System.Runtime,GC Heap Size (MB),Metric,70.8702
05/08/2025 15:10:59,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:59,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:59,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:10:59,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:10:59,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:10:59,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:10:59,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:10:59,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,3
05/08/2025 15:10:59,System.Runtime,Allocation Rate (B / 1 sec),Rate,1173400
05/08/2025 15:10:59,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:10:59,System.Runtime,GC Fragmentation (%),Metric,1.4987010125875733
05/08/2025 15:10:59,System.Runtime,GC Committed Bytes (MB),Metric,85.01248
05/08/2025 15:10:59,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:10:59,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:10:59,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:10:59,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:10:59,System.Runtime,Gen 1 Size (B),Metric,632504
05/08/2025 15:10:59,System.Runtime,Gen 2 Size (B),Metric,3888416
05/08/2025 15:10:59,System.Runtime,LOH Size (B),Metric,13105816
05/08/2025 15:10:59,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:10:59,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:10:59,System.Runtime,IL Bytes Jitted (B),Metric,1057973
05/08/2025 15:10:59,System.Runtime,Number of Methods Jitted,Metric,13090
05/08/2025 15:10:59,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,203.16709999999966
05/08/2025 15:11:00,System.Runtime,CPU Usage (%),Metric,0
05/08/2025 15:11:00,System.Runtime,Working Set (MB),Metric,275.382272
05/08/2025 15:11:00,System.Runtime,GC Heap Size (MB),Metric,70.8702
05/08/2025 15:11:00,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:00,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:00,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:00,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:00,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:11:00,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:00,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:00,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:11:00,System.Runtime,Allocation Rate (B / 1 sec),Rate,8184
05/08/2025 15:11:00,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:00,System.Runtime,GC Fragmentation (%),Metric,1.4987010125875733
05/08/2025 15:11:00,System.Runtime,GC Committed Bytes (MB),Metric,85.01248
05/08/2025 15:11:00,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:00,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:00,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:11:00,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:00,System.Runtime,Gen 1 Size (B),Metric,632504
05/08/2025 15:11:00,System.Runtime,Gen 2 Size (B),Metric,3888416
05/08/2025 15:11:00,System.Runtime,LOH Size (B),Metric,13105816
05/08/2025 15:11:00,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:00,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:00,System.Runtime,IL Bytes Jitted (B),Metric,1058239
05/08/2025 15:11:00,System.Runtime,Number of Methods Jitted,Metric,13093
05/08/2025 15:11:00,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,1.8649999999997817
05/08/2025 15:11:01,System.Runtime,CPU Usage (%),Metric,3.515625
05/08/2025 15:11:01,System.Runtime,Working Set (MB),Metric,271.101952
05/08/2025 15:11:01,System.Runtime,GC Heap Size (MB),Metric,40.54412
05/08/2025 15:11:01,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,1
05/08/2025 15:11:01,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:01,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:01,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:01,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:11:01,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:01,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:01,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:11:01,System.Runtime,Allocation Rate (B / 1 sec),Rate,30211016
05/08/2025 15:11:01,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:01,System.Runtime,GC Fragmentation (%),Metric,1.298949716572291
05/08/2025 15:11:01,System.Runtime,GC Committed Bytes (MB),Metric,88.854528
05/08/2025 15:11:01,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:01,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:01,System.Runtime,Time paused by GC (ms / 1 sec),Rate,1.3840000000000003
05/08/2025 15:11:01,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:01,System.Runtime,Gen 1 Size (B),Metric,689536
05/08/2025 15:11:01,System.Runtime,Gen 2 Size (B),Metric,3888416
05/08/2025 15:11:01,System.Runtime,LOH Size (B),Metric,15962440
05/08/2025 15:11:01,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:01,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:01,System.Runtime,IL Bytes Jitted (B),Metric,1061649
05/08/2025 15:11:01,System.Runtime,Number of Methods Jitted,Metric,13127
05/08/2025 15:11:01,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,47.264900000000125
05/08/2025 15:11:02,System.Runtime,CPU Usage (%),Metric,9.807692307692308
05/08/2025 15:11:02,System.Runtime,Working Set (MB),Metric,275.984384
05/08/2025 15:11:02,System.Runtime,GC Heap Size (MB),Metric,53.704984
05/08/2025 15:11:02,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:02,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:02,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:02,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:02,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:11:02,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:02,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:02,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,12
05/08/2025 15:11:02,System.Runtime,Allocation Rate (B / 1 sec),Rate,13122224
05/08/2025 15:11:02,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:02,System.Runtime,GC Fragmentation (%),Metric,1.298949716572291
05/08/2025 15:11:02,System.Runtime,GC Committed Bytes (MB),Metric,88.854528
05/08/2025 15:11:02,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:02,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:02,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:11:02,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:02,System.Runtime,Gen 1 Size (B),Metric,689536
05/08/2025 15:11:02,System.Runtime,Gen 2 Size (B),Metric,3888416
05/08/2025 15:11:02,System.Runtime,LOH Size (B),Metric,15962440
05/08/2025 15:11:02,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:02,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:02,System.Runtime,IL Bytes Jitted (B),Metric,1069049
05/08/2025 15:11:02,System.Runtime,Number of Methods Jitted,Metric,13243
05/08/2025 15:11:02,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,126.71870000000035
05/08/2025 15:11:03,System.Runtime,CPU Usage (%),Metric,0
05/08/2025 15:11:03,System.Runtime,Working Set (MB),Metric,275.984384
05/08/2025 15:11:03,System.Runtime,GC Heap Size (MB),Metric,53.713208
05/08/2025 15:11:03,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:03,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:03,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:03,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:03,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:11:03,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:03,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:03,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:11:03,System.Runtime,Allocation Rate (B / 1 sec),Rate,8200
05/08/2025 15:11:03,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:03,System.Runtime,GC Fragmentation (%),Metric,1.298949716572291
05/08/2025 15:11:03,System.Runtime,GC Committed Bytes (MB),Metric,88.854528
05/08/2025 15:11:03,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:03,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:03,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:11:03,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:03,System.Runtime,Gen 1 Size (B),Metric,689536
05/08/2025 15:11:03,System.Runtime,Gen 2 Size (B),Metric,3888416
05/08/2025 15:11:03,System.Runtime,LOH Size (B),Metric,15962440
05/08/2025 15:11:03,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:03,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:03,System.Runtime,IL Bytes Jitted (B),Metric,1069049
05/08/2025 15:11:03,System.Runtime,Number of Methods Jitted,Metric,13243
05/08/2025 15:11:03,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:11:04,System.Runtime,CPU Usage (%),Metric,1.3779527559055118
05/08/2025 15:11:04,System.Runtime,Working Set (MB),Metric,276.291584
05/08/2025 15:11:04,System.Runtime,GC Heap Size (MB),Metric,54.909944
05/08/2025 15:11:04,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:04,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:04,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:04,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:04,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:11:04,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:04,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:04,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,4
05/08/2025 15:11:04,System.Runtime,Allocation Rate (B / 1 sec),Rate,1193568
05/08/2025 15:11:04,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:04,System.Runtime,GC Fragmentation (%),Metric,1.298949716572291
05/08/2025 15:11:04,System.Runtime,GC Committed Bytes (MB),Metric,88.854528
05/08/2025 15:11:04,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:04,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:04,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:11:04,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:04,System.Runtime,Gen 1 Size (B),Metric,689536
05/08/2025 15:11:04,System.Runtime,Gen 2 Size (B),Metric,3888416
05/08/2025 15:11:04,System.Runtime,LOH Size (B),Metric,15962440
05/08/2025 15:11:04,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:04,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:04,System.Runtime,IL Bytes Jitted (B),Metric,1071591
05/08/2025 15:11:04,System.Runtime,Number of Methods Jitted,Metric,13255
05/08/2025 15:11:04,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,11.575399999999718
05/08/2025 15:11:05,System.Runtime,CPU Usage (%),Metric,4.770992366412214
05/08/2025 15:11:05,System.Runtime,Working Set (MB),Metric,272.338944
05/08/2025 15:11:05,System.Runtime,GC Heap Size (MB),Metric,35.506896
05/08/2025 15:11:05,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,1
05/08/2025 15:11:05,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:05,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:05,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:05,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:11:05,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:05,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:05,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:11:05,System.Runtime,Allocation Rate (B / 1 sec),Rate,42072784
05/08/2025 15:11:05,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:05,System.Runtime,GC Fragmentation (%),Metric,1.2179216148137058
05/08/2025 15:11:05,System.Runtime,GC Committed Bytes (MB),Metric,91.19744
05/08/2025 15:11:05,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:05,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:05,System.Runtime,Time paused by GC (ms / 1 sec),Rate,1.8590000000000018
05/08/2025 15:11:05,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:05,System.Runtime,Gen 1 Size (B),Metric,735312
05/08/2025 15:11:05,System.Runtime,Gen 2 Size (B),Metric,3888416
05/08/2025 15:11:05,System.Runtime,LOH Size (B),Metric,17390752
05/08/2025 15:11:05,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:05,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:05,System.Runtime,IL Bytes Jitted (B),Metric,1073368
05/08/2025 15:11:05,System.Runtime,Number of Methods Jitted,Metric,13264
05/08/2025 15:11:05,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,20.378499999999804
05/08/2025 15:11:06,System.Runtime,CPU Usage (%),Metric,6.0546875
05/08/2025 15:11:06,System.Runtime,Working Set (MB),Metric,275.82464
05/08/2025 15:11:06,System.Runtime,GC Heap Size (MB),Metric,36.762816
05/08/2025 15:11:06,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:06,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:06,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:06,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:06,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:11:06,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:06,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:06,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,12
05/08/2025 15:11:06,System.Runtime,Allocation Rate (B / 1 sec),Rate,1255368
05/08/2025 15:11:06,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:06,System.Runtime,GC Fragmentation (%),Metric,1.2179216148137058
05/08/2025 15:11:06,System.Runtime,GC Committed Bytes (MB),Metric,91.19744
05/08/2025 15:11:06,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:06,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:06,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:11:06,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:06,System.Runtime,Gen 1 Size (B),Metric,735312
05/08/2025 15:11:06,System.Runtime,Gen 2 Size (B),Metric,3888416
05/08/2025 15:11:06,System.Runtime,LOH Size (B),Metric,17390752
05/08/2025 15:11:06,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:06,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:06,System.Runtime,IL Bytes Jitted (B),Metric,1076351
05/08/2025 15:11:06,System.Runtime,Number of Methods Jitted,Metric,13311
05/08/2025 15:11:06,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,36.233900000000176
05/08/2025 15:11:07,System.Runtime,CPU Usage (%),Metric,1.953125
05/08/2025 15:11:07,System.Runtime,Working Set (MB),Metric,276.33664
05/08/2025 15:11:07,System.Runtime,GC Heap Size (MB),Metric,37.953056
05/08/2025 15:11:07,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:07,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:07,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:07,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:07,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:11:07,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:07,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:07,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,3
05/08/2025 15:11:07,System.Runtime,Allocation Rate (B / 1 sec),Rate,1187072
05/08/2025 15:11:07,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:07,System.Runtime,GC Fragmentation (%),Metric,1.2179216148137058
05/08/2025 15:11:07,System.Runtime,GC Committed Bytes (MB),Metric,91.19744
05/08/2025 15:11:07,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:07,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:07,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:11:07,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:07,System.Runtime,Gen 1 Size (B),Metric,735312
05/08/2025 15:11:07,System.Runtime,Gen 2 Size (B),Metric,3888416
05/08/2025 15:11:07,System.Runtime,LOH Size (B),Metric,17390752
05/08/2025 15:11:07,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:07,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:07,System.Runtime,IL Bytes Jitted (B),Metric,1079798
05/08/2025 15:11:07,System.Runtime,Number of Methods Jitted,Metric,13354
05/08/2025 15:11:07,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,56.49319999999989
05/08/2025 15:11:08,System.Runtime,CPU Usage (%),Metric,0.8032128514056225
05/08/2025 15:11:08,System.Runtime,Working Set (MB),Metric,276.373504
05/08/2025 15:11:08,System.Runtime,GC Heap Size (MB),Metric,37.977728
05/08/2025 15:11:08,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:08,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:08,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:08,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:08,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:11:08,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:08,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:08,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:11:08,System.Runtime,Allocation Rate (B / 1 sec),Rate,24600
05/08/2025 15:11:08,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:08,System.Runtime,GC Fragmentation (%),Metric,1.2179216148137058
05/08/2025 15:11:08,System.Runtime,GC Committed Bytes (MB),Metric,91.19744
05/08/2025 15:11:08,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:08,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:08,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:11:08,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:08,System.Runtime,Gen 1 Size (B),Metric,735312
05/08/2025 15:11:08,System.Runtime,Gen 2 Size (B),Metric,3888416
05/08/2025 15:11:08,System.Runtime,LOH Size (B),Metric,17390752
05/08/2025 15:11:08,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:08,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:08,System.Runtime,IL Bytes Jitted (B),Metric,1079798
05/08/2025 15:11:08,System.Runtime,Number of Methods Jitted,Metric,13354
05/08/2025 15:11:08,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:11:09,System.Runtime,CPU Usage (%),Metric,10.424710424710424
05/08/2025 15:11:09,System.Runtime,Working Set (MB),Metric,283.52512
05/08/2025 15:11:09,System.Runtime,GC Heap Size (MB),Metric,80.970472
05/08/2025 15:11:09,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:09,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:09,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:09,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:09,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:11:09,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:09,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:09,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:11:09,System.Runtime,Allocation Rate (B / 1 sec),Rate,42859720
05/08/2025 15:11:09,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:09,System.Runtime,GC Fragmentation (%),Metric,1.2179216148137058
05/08/2025 15:11:09,System.Runtime,GC Committed Bytes (MB),Metric,91.19744
05/08/2025 15:11:09,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:09,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:09,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:11:09,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:09,System.Runtime,Gen 1 Size (B),Metric,735312
05/08/2025 15:11:09,System.Runtime,Gen 2 Size (B),Metric,3888416
05/08/2025 15:11:09,System.Runtime,LOH Size (B),Metric,17390752
05/08/2025 15:11:09,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:09,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:09,System.Runtime,IL Bytes Jitted (B),Metric,1082539
05/08/2025 15:11:09,System.Runtime,Number of Methods Jitted,Metric,13386
05/08/2025 15:11:09,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,62.82290000000012
05/08/2025 15:11:10,System.Runtime,CPU Usage (%),Metric,2.3076923076923075
05/08/2025 15:11:10,System.Runtime,Working Set (MB),Metric,269.0048
05/08/2025 15:11:10,System.Runtime,GC Heap Size (MB),Metric,38.918544
05/08/2025 15:11:10,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,1
05/08/2025 15:11:10,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,1
05/08/2025 15:11:10,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,1
05/08/2025 15:11:10,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:10,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:11:10,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,1
05/08/2025 15:11:10,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:10,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,12
05/08/2025 15:11:10,System.Runtime,Allocation Rate (B / 1 sec),Rate,469440
05/08/2025 15:11:10,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:10,System.Runtime,GC Fragmentation (%),Metric,43.13297456366001
05/08/2025 15:11:10,System.Runtime,GC Committed Bytes (MB),Metric,81.24416
05/08/2025 15:11:10,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:10,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:10,System.Runtime,Time paused by GC (ms / 1 sec),Rate,13.741
05/08/2025 15:11:10,System.Runtime,Gen 0 Size (B),Metric,56804816
05/08/2025 15:11:10,System.Runtime,Gen 1 Size (B),Metric,735312
05/08/2025 15:11:10,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:11:10,System.Runtime,LOH Size (B),Metric,6471040
05/08/2025 15:11:10,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:10,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:10,System.Runtime,IL Bytes Jitted (B),Metric,1084249
05/08/2025 15:11:10,System.Runtime,Number of Methods Jitted,Metric,13413
05/08/2025 15:11:10,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,29.404500000000553
05/08/2025 15:11:11,System.Runtime,CPU Usage (%),Metric,1.9841269841269842
05/08/2025 15:11:11,System.Runtime,Working Set (MB),Metric,265.408512
05/08/2025 15:11:11,System.Runtime,GC Heap Size (MB),Metric,6.676056
05/08/2025 15:11:11,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,1
05/08/2025 15:11:11,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:11,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:11,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:11,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:11:11,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:11,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:11,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,3
05/08/2025 15:11:11,System.Runtime,Allocation Rate (B / 1 sec),Rate,1173880
05/08/2025 15:11:11,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:11,System.Runtime,GC Fragmentation (%),Metric,53.001653785142935
05/08/2025 15:11:11,System.Runtime,GC Committed Bytes (MB),Metric,81.211392
05/08/2025 15:11:11,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:11,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:11,System.Runtime,Time paused by GC (ms / 1 sec),Rate,1.1679999999999957
05/08/2025 15:11:11,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:11,System.Runtime,Gen 1 Size (B),Metric,747752
05/08/2025 15:11:11,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:11:11,System.Runtime,LOH Size (B),Metric,6471040
05/08/2025 15:11:11,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:11,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:11,System.Runtime,IL Bytes Jitted (B),Metric,1085097
05/08/2025 15:11:11,System.Runtime,Number of Methods Jitted,Metric,13422
05/08/2025 15:11:11,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,20.213199999999233
05/08/2025 15:11:12,System.Runtime,CPU Usage (%),Metric,4.038461538461538
05/08/2025 15:11:12,System.Runtime,Working Set (MB),Metric,265.85088
05/08/2025 15:11:12,System.Runtime,GC Heap Size (MB),Metric,46.834504
05/08/2025 15:11:12,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:12,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:12,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:12,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:12,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:11:12,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:12,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:12,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:11:12,System.Runtime,Allocation Rate (B / 1 sec),Rate,40025296
05/08/2025 15:11:12,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:12,System.Runtime,GC Fragmentation (%),Metric,53.001653785142935
05/08/2025 15:11:12,System.Runtime,GC Committed Bytes (MB),Metric,81.211392
05/08/2025 15:11:12,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:12,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:12,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:11:12,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:12,System.Runtime,Gen 1 Size (B),Metric,747752
05/08/2025 15:11:12,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:11:12,System.Runtime,LOH Size (B),Metric,6471040
05/08/2025 15:11:12,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:12,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:12,System.Runtime,IL Bytes Jitted (B),Metric,1085609
05/08/2025 15:11:12,System.Runtime,Number of Methods Jitted,Metric,13434
05/08/2025 15:11:12,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,11.457500000000437
05/08/2025 15:11:13,System.Runtime,CPU Usage (%),Metric,9.325396825396826
05/08/2025 15:11:13,System.Runtime,Working Set (MB),Metric,270.0288
05/08/2025 15:11:13,System.Runtime,GC Heap Size (MB),Metric,50.197336
05/08/2025 15:11:13,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:13,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:13,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:13,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:13,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:11:13,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:13,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:13,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,12
05/08/2025 15:11:13,System.Runtime,Allocation Rate (B / 1 sec),Rate,3355968
05/08/2025 15:11:13,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:13,System.Runtime,GC Fragmentation (%),Metric,53.001653785142935
05/08/2025 15:11:13,System.Runtime,GC Committed Bytes (MB),Metric,81.211392
05/08/2025 15:11:13,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:13,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:13,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:11:13,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:13,System.Runtime,Gen 1 Size (B),Metric,747752
05/08/2025 15:11:13,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:11:13,System.Runtime,LOH Size (B),Metric,6471040
05/08/2025 15:11:13,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:13,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:13,System.Runtime,IL Bytes Jitted (B),Metric,1090389
05/08/2025 15:11:13,System.Runtime,Number of Methods Jitted,Metric,13474
05/08/2025 15:11:13,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,65.44980000000032
05/08/2025 15:11:14,System.Runtime,CPU Usage (%),Metric,0
05/08/2025 15:11:14,System.Runtime,Working Set (MB),Metric,270.0288
05/08/2025 15:11:14,System.Runtime,GC Heap Size (MB),Metric,50.205496
05/08/2025 15:11:14,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:14,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:14,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:14,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:14,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:11:14,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:14,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:14,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:11:14,System.Runtime,Allocation Rate (B / 1 sec),Rate,8160
05/08/2025 15:11:14,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:14,System.Runtime,GC Fragmentation (%),Metric,53.001653785142935
05/08/2025 15:11:14,System.Runtime,GC Committed Bytes (MB),Metric,81.211392
05/08/2025 15:11:14,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:14,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:14,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:11:14,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:14,System.Runtime,Gen 1 Size (B),Metric,747752
05/08/2025 15:11:14,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:11:14,System.Runtime,LOH Size (B),Metric,6471040
05/08/2025 15:11:14,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:14,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:14,System.Runtime,IL Bytes Jitted (B),Metric,1090389
05/08/2025 15:11:14,System.Runtime,Number of Methods Jitted,Metric,13474
05/08/2025 15:11:14,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:11:15,System.Runtime,CPU Usage (%),Metric,0
05/08/2025 15:11:15,System.Runtime,Working Set (MB),Metric,270.0288
05/08/2025 15:11:15,System.Runtime,GC Heap Size (MB),Metric,50.205496
05/08/2025 15:11:15,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:15,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:15,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:15,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:15,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:11:15,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:15,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:15,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:11:15,System.Runtime,Allocation Rate (B / 1 sec),Rate,8200
05/08/2025 15:11:15,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:15,System.Runtime,GC Fragmentation (%),Metric,53.001653785142935
05/08/2025 15:11:15,System.Runtime,GC Committed Bytes (MB),Metric,81.211392
05/08/2025 15:11:15,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:15,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:15,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:11:15,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:15,System.Runtime,Gen 1 Size (B),Metric,747752
05/08/2025 15:11:15,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:11:15,System.Runtime,LOH Size (B),Metric,6471040
05/08/2025 15:11:15,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:15,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:15,System.Runtime,IL Bytes Jitted (B),Metric,1090389
05/08/2025 15:11:15,System.Runtime,Number of Methods Jitted,Metric,13474
05/08/2025 15:11:15,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:11:16,System.Runtime,CPU Usage (%),Metric,0.3838771593090211
05/08/2025 15:11:16,System.Runtime,Working Set (MB),Metric,270.0288
05/08/2025 15:11:16,System.Runtime,GC Heap Size (MB),Metric,50.21372
05/08/2025 15:11:16,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:16,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:16,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:16,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:16,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:11:16,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:16,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:16,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:11:16,System.Runtime,Allocation Rate (B / 1 sec),Rate,0
05/08/2025 15:11:16,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:16,System.Runtime,GC Fragmentation (%),Metric,53.001653785142935
05/08/2025 15:11:16,System.Runtime,GC Committed Bytes (MB),Metric,81.211392
05/08/2025 15:11:16,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:16,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:16,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:11:16,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:16,System.Runtime,Gen 1 Size (B),Metric,747752
05/08/2025 15:11:16,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:11:16,System.Runtime,LOH Size (B),Metric,6471040
05/08/2025 15:11:16,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:16,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:16,System.Runtime,IL Bytes Jitted (B),Metric,1091016
05/08/2025 15:11:16,System.Runtime,Number of Methods Jitted,Metric,13482
05/08/2025 15:11:16,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,12.207099999999627
05/08/2025 15:11:17,System.Runtime,CPU Usage (%),Metric,0
05/08/2025 15:11:17,System.Runtime,Working Set (MB),Metric,270.0288
05/08/2025 15:11:17,System.Runtime,GC Heap Size (MB),Metric,50.221944
05/08/2025 15:11:17,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:17,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:17,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:17,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:17,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:11:17,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:17,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:17,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:11:17,System.Runtime,Allocation Rate (B / 1 sec),Rate,8200
05/08/2025 15:11:17,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:17,System.Runtime,GC Fragmentation (%),Metric,53.001653785142935
05/08/2025 15:11:17,System.Runtime,GC Committed Bytes (MB),Metric,81.211392
05/08/2025 15:11:17,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:17,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:17,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:11:17,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:17,System.Runtime,Gen 1 Size (B),Metric,747752
05/08/2025 15:11:17,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:11:17,System.Runtime,LOH Size (B),Metric,6471040
05/08/2025 15:11:17,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:17,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:17,System.Runtime,IL Bytes Jitted (B),Metric,1091016
05/08/2025 15:11:17,System.Runtime,Number of Methods Jitted,Metric,13482
05/08/2025 15:11:17,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:11:18,System.Runtime,CPU Usage (%),Metric,0.9615384615384616
05/08/2025 15:11:18,System.Runtime,Working Set (MB),Metric,266.846208
05/08/2025 15:11:18,System.Runtime,GC Heap Size (MB),Metric,51.385952
05/08/2025 15:11:18,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:18,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:18,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:18,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:18,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:11:18,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:18,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:18,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,3
05/08/2025 15:11:18,System.Runtime,Allocation Rate (B / 1 sec),Rate,1169112
05/08/2025 15:11:18,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:18,System.Runtime,GC Fragmentation (%),Metric,53.001653785142935
05/08/2025 15:11:18,System.Runtime,GC Committed Bytes (MB),Metric,81.211392
05/08/2025 15:11:18,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:18,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:18,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:11:18,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:18,System.Runtime,Gen 1 Size (B),Metric,747752
05/08/2025 15:11:18,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:11:18,System.Runtime,LOH Size (B),Metric,6471040
05/08/2025 15:11:18,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:18,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:18,System.Runtime,IL Bytes Jitted (B),Metric,1091983
05/08/2025 15:11:18,System.Runtime,Number of Methods Jitted,Metric,13500
05/08/2025 15:11:18,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,9.127400000000307
05/08/2025 15:11:19,System.Runtime,CPU Usage (%),Metric,0.9881422924901185
05/08/2025 15:11:19,System.Runtime,Working Set (MB),Metric,263.80288
05/08/2025 15:11:19,System.Runtime,GC Heap Size (MB),Metric,51.394128
05/08/2025 15:11:19,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:19,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:19,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:19,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:19,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:11:19,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:19,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:19,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,1
05/08/2025 15:11:19,System.Runtime,Allocation Rate (B / 1 sec),Rate,0
05/08/2025 15:11:19,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:19,System.Runtime,GC Fragmentation (%),Metric,53.001653785142935
05/08/2025 15:11:19,System.Runtime,GC Committed Bytes (MB),Metric,81.211392
05/08/2025 15:11:19,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:19,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:19,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:11:19,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:19,System.Runtime,Gen 1 Size (B),Metric,747752
05/08/2025 15:11:19,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:11:19,System.Runtime,LOH Size (B),Metric,6471040
05/08/2025 15:11:19,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:19,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:19,System.Runtime,IL Bytes Jitted (B),Metric,1092261
05/08/2025 15:11:19,System.Runtime,Number of Methods Jitted,Metric,13502
05/08/2025 15:11:19,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,1.3359999999993306
05/08/2025 15:11:20,System.Runtime,CPU Usage (%),Metric,0.38684719535783363
05/08/2025 15:11:20,System.Runtime,Working Set (MB),Metric,263.831552
05/08/2025 15:11:20,System.Runtime,GC Heap Size (MB),Metric,51.4188
05/08/2025 15:11:20,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:20,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:20,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:20,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:20,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:11:20,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:20,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:20,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:11:20,System.Runtime,Allocation Rate (B / 1 sec),Rate,24600
05/08/2025 15:11:20,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:20,System.Runtime,GC Fragmentation (%),Metric,53.001653785142935
05/08/2025 15:11:20,System.Runtime,GC Committed Bytes (MB),Metric,81.211392
05/08/2025 15:11:20,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:20,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:20,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:11:20,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:20,System.Runtime,Gen 1 Size (B),Metric,747752
05/08/2025 15:11:20,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:11:20,System.Runtime,LOH Size (B),Metric,6471040
05/08/2025 15:11:20,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:20,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:20,System.Runtime,IL Bytes Jitted (B),Metric,1092261
05/08/2025 15:11:20,System.Runtime,Number of Methods Jitted,Metric,13502
05/08/2025 15:11:20,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:11:21,System.Runtime,CPU Usage (%),Metric,9.941520467836257
05/08/2025 15:11:21,System.Runtime,Working Set (MB),Metric,265.060352
05/08/2025 15:11:21,System.Runtime,GC Heap Size (MB),Metric,34.879584
05/08/2025 15:11:21,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,1
05/08/2025 15:11:21,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:21,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:21,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:21,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:11:21,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:21,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:21,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,9
05/08/2025 15:11:21,System.Runtime,Allocation Rate (B / 1 sec),Rate,43296856
05/08/2025 15:11:21,System.Runtime,Number of Active Timers,Metric,5
05/08/2025 15:11:21,System.Runtime,GC Fragmentation (%),Metric,42.90900052866289
05/08/2025 15:11:21,System.Runtime,GC Committed Bytes (MB),Metric,83.29216
05/08/2025 15:11:21,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:21,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:21,System.Runtime,Time paused by GC (ms / 1 sec),Rate,2.1650000000000063
05/08/2025 15:11:21,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:21,System.Runtime,Gen 1 Size (B),Metric,1009824
05/08/2025 15:11:21,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:11:21,System.Runtime,LOH Size (B),Metric,8294416
05/08/2025 15:11:21,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:21,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:21,System.Runtime,IL Bytes Jitted (B),Metric,1098021
05/08/2025 15:11:21,System.Runtime,Number of Methods Jitted,Metric,13596
05/08/2025 15:11:21,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,68.59970000000067
05/08/2025 15:11:22,System.Runtime,CPU Usage (%),Metric,0.998003992015968
05/08/2025 15:11:22,System.Runtime,Working Set (MB),Metric,267.780096
05/08/2025 15:11:22,System.Runtime,GC Heap Size (MB),Metric,34.896032
05/08/2025 15:11:22,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:22,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:22,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:22,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:22,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:11:22,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:22,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:22,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,3
05/08/2025 15:11:22,System.Runtime,Allocation Rate (B / 1 sec),Rate,16400
05/08/2025 15:11:22,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:22,System.Runtime,GC Fragmentation (%),Metric,42.90900052866289
05/08/2025 15:11:22,System.Runtime,GC Committed Bytes (MB),Metric,83.29216
05/08/2025 15:11:22,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:22,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:22,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:11:22,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:22,System.Runtime,Gen 1 Size (B),Metric,1009824
05/08/2025 15:11:22,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:11:22,System.Runtime,LOH Size (B),Metric,8294416
05/08/2025 15:11:22,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:22,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:22,System.Runtime,IL Bytes Jitted (B),Metric,1105219
05/08/2025 15:11:22,System.Runtime,Number of Methods Jitted,Metric,13672
05/08/2025 15:11:22,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,82.21479999999974
05/08/2025 15:11:23,System.Runtime,CPU Usage (%),Metric,3.495145631067961
05/08/2025 15:11:23,System.Runtime,Working Set (MB),Metric,268.53376
05/08/2025 15:11:23,System.Runtime,GC Heap Size (MB),Metric,36.106344
05/08/2025 15:11:23,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:23,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:23,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:23,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:23,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:11:23,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:23,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:23,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,3
05/08/2025 15:11:23,System.Runtime,Allocation Rate (B / 1 sec),Rate,1207024
05/08/2025 15:11:23,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:23,System.Runtime,GC Fragmentation (%),Metric,42.90900052866289
05/08/2025 15:11:23,System.Runtime,GC Committed Bytes (MB),Metric,83.29216
05/08/2025 15:11:23,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:23,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:23,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:11:23,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:23,System.Runtime,Gen 1 Size (B),Metric,1009824
05/08/2025 15:11:23,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:11:23,System.Runtime,LOH Size (B),Metric,8294416
05/08/2025 15:11:23,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:23,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:23,System.Runtime,IL Bytes Jitted (B),Metric,1114295
05/08/2025 15:11:23,System.Runtime,Number of Methods Jitted,Metric,13793
05/08/2025 15:11:23,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,153.41399999999976
05/08/2025 15:11:24,System.Runtime,CPU Usage (%),Metric,7.115384615384615
05/08/2025 15:11:24,System.Runtime,Working Set (MB),Metric,266.641408
05/08/2025 15:11:24,System.Runtime,GC Heap Size (MB),Metric,18.6566
05/08/2025 15:11:24,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,1
05/08/2025 15:11:24,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:24,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:24,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:24,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:11:24,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:24,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:24,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:11:24,System.Runtime,Allocation Rate (B / 1 sec),Rate,42285472
05/08/2025 15:11:24,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:24,System.Runtime,GC Fragmentation (%),Metric,38.76087952597112
05/08/2025 15:11:24,System.Runtime,GC Committed Bytes (MB),Metric,84.963328
05/08/2025 15:11:24,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:24,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:24,System.Runtime,Time paused by GC (ms / 1 sec),Rate,1.0899999999999963
05/08/2025 15:11:24,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:24,System.Runtime,Gen 1 Size (B),Metric,1044032
05/08/2025 15:11:24,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:11:24,System.Runtime,LOH Size (B),Metric,9722728
05/08/2025 15:11:24,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:24,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:24,System.Runtime,IL Bytes Jitted (B),Metric,1123245
05/08/2025 15:11:24,System.Runtime,Number of Methods Jitted,Metric,13901
05/08/2025 15:11:24,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,90.63259999999991
05/08/2025 15:11:25,System.Runtime,CPU Usage (%),Metric,7.480314960629921
05/08/2025 15:11:25,System.Runtime,Working Set (MB),Metric,270.954496
05/08/2025 15:11:25,System.Runtime,GC Heap Size (MB),Metric,19.692408
05/08/2025 15:11:25,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:25,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:25,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:25,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:25,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:11:25,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:25,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:25,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,12
05/08/2025 15:11:25,System.Runtime,Allocation Rate (B / 1 sec),Rate,1035520
05/08/2025 15:11:25,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:25,System.Runtime,GC Fragmentation (%),Metric,38.76087952597112
05/08/2025 15:11:25,System.Runtime,GC Committed Bytes (MB),Metric,84.963328
05/08/2025 15:11:25,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:25,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:25,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:11:25,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:25,System.Runtime,Gen 1 Size (B),Metric,1044032
05/08/2025 15:11:25,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:11:25,System.Runtime,LOH Size (B),Metric,9722728
05/08/2025 15:11:25,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:25,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:25,System.Runtime,IL Bytes Jitted (B),Metric,1156850
05/08/2025 15:11:25,System.Runtime,Number of Methods Jitted,Metric,14377
05/08/2025 15:11:25,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,247.67849999999999
05/08/2025 15:11:26,System.Runtime,CPU Usage (%),Metric,2.1653543307086616
05/08/2025 15:11:26,System.Runtime,Working Set (MB),Metric,271.269888
05/08/2025 15:11:26,System.Runtime,GC Heap Size (MB),Metric,20.881624
05/08/2025 15:11:26,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:26,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:26,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:26,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:26,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:11:26,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:26,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:26,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,3
05/08/2025 15:11:26,System.Runtime,Allocation Rate (B / 1 sec),Rate,1186024
05/08/2025 15:11:26,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:26,System.Runtime,GC Fragmentation (%),Metric,38.76087952597112
05/08/2025 15:11:26,System.Runtime,GC Committed Bytes (MB),Metric,84.963328
05/08/2025 15:11:26,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:26,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:26,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:11:26,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:26,System.Runtime,Gen 1 Size (B),Metric,1044032
05/08/2025 15:11:26,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:11:26,System.Runtime,LOH Size (B),Metric,9722728
05/08/2025 15:11:26,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:26,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:26,System.Runtime,IL Bytes Jitted (B),Metric,1158277
05/08/2025 15:11:26,System.Runtime,Number of Methods Jitted,Metric,14416
05/08/2025 15:11:26,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,56.334100000000035
05/08/2025 15:11:27,System.Runtime,CPU Usage (%),Metric,0.1926782273603083
05/08/2025 15:11:27,System.Runtime,Working Set (MB),Metric,271.306752
05/08/2025 15:11:27,System.Runtime,GC Heap Size (MB),Metric,20.927792
05/08/2025 15:11:27,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:27,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:27,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:27,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:27,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:11:27,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:27,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:27,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,2
05/08/2025 15:11:27,System.Runtime,Allocation Rate (B / 1 sec),Rate,46000
05/08/2025 15:11:27,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:27,System.Runtime,GC Fragmentation (%),Metric,38.76087952597112
05/08/2025 15:11:27,System.Runtime,GC Committed Bytes (MB),Metric,84.963328
05/08/2025 15:11:27,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:27,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:27,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:11:27,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:27,System.Runtime,Gen 1 Size (B),Metric,1044032
05/08/2025 15:11:27,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:11:27,System.Runtime,LOH Size (B),Metric,9722728
05/08/2025 15:11:27,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:27,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:27,System.Runtime,IL Bytes Jitted (B),Metric,1158891
05/08/2025 15:11:27,System.Runtime,Number of Methods Jitted,Metric,14424
05/08/2025 15:11:27,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,9.79780000000028
05/08/2025 15:11:28,System.Runtime,CPU Usage (%),Metric,2.772277227722772
05/08/2025 15:11:28,System.Runtime,Working Set (MB),Metric,271.745024
05/08/2025 15:11:28,System.Runtime,GC Heap Size (MB),Metric,50.009616
05/08/2025 15:11:28,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:28,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:28,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:28,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:28,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:11:28,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:28,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:28,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:11:28,System.Runtime,Allocation Rate (B / 1 sec),Rate,29016792
05/08/2025 15:11:28,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:28,System.Runtime,GC Fragmentation (%),Metric,38.76087952597112
05/08/2025 15:11:28,System.Runtime,GC Committed Bytes (MB),Metric,84.963328
05/08/2025 15:11:28,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:28,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:28,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:11:28,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:28,System.Runtime,Gen 1 Size (B),Metric,1044032
05/08/2025 15:11:28,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:11:28,System.Runtime,LOH Size (B),Metric,9722728
05/08/2025 15:11:28,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:28,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:28,System.Runtime,IL Bytes Jitted (B),Metric,1158927
05/08/2025 15:11:28,System.Runtime,Number of Methods Jitted,Metric,14425
05/08/2025 15:11:28,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,1.1090000000003783
05/08/2025 15:11:29,System.Runtime,CPU Usage (%),Metric,8.687258687258687
05/08/2025 15:11:29,System.Runtime,Working Set (MB),Metric,275.173376
05/08/2025 15:11:29,System.Runtime,GC Heap Size (MB),Metric,64.415848
05/08/2025 15:11:29,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:29,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:29,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:29,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:29,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:11:29,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:29,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:29,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,12
05/08/2025 15:11:29,System.Runtime,Allocation Rate (B / 1 sec),Rate,14330864
05/08/2025 15:11:29,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:29,System.Runtime,GC Fragmentation (%),Metric,38.76087952597112
05/08/2025 15:11:29,System.Runtime,GC Committed Bytes (MB),Metric,84.963328
05/08/2025 15:11:29,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:29,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:29,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:11:29,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:29,System.Runtime,Gen 1 Size (B),Metric,1044032
05/08/2025 15:11:29,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:11:29,System.Runtime,LOH Size (B),Metric,9722728
05/08/2025 15:11:29,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:29,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:29,System.Runtime,IL Bytes Jitted (B),Metric,1162661
05/08/2025 15:11:29,System.Runtime,Number of Methods Jitted,Metric,14447
05/08/2025 15:11:29,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,52.573999999998705
05/08/2025 15:11:30,System.Runtime,CPU Usage (%),Metric,0.38910505836575876
05/08/2025 15:11:30,System.Runtime,Working Set (MB),Metric,275.173376
05/08/2025 15:11:30,System.Runtime,GC Heap Size (MB),Metric,64.424024
05/08/2025 15:11:30,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:30,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:30,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:30,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:30,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:11:30,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:30,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:30,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:11:30,System.Runtime,Allocation Rate (B / 1 sec),Rate,8176
05/08/2025 15:11:30,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:30,System.Runtime,GC Fragmentation (%),Metric,38.76087952597112
05/08/2025 15:11:30,System.Runtime,GC Committed Bytes (MB),Metric,84.963328
05/08/2025 15:11:30,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:30,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:30,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:11:30,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:30,System.Runtime,Gen 1 Size (B),Metric,1044032
05/08/2025 15:11:30,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:11:30,System.Runtime,LOH Size (B),Metric,9722728
05/08/2025 15:11:30,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:30,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:30,System.Runtime,IL Bytes Jitted (B),Metric,1162661
05/08/2025 15:11:30,System.Runtime,Number of Methods Jitted,Metric,14447
05/08/2025 15:11:30,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:11:31,System.Runtime,CPU Usage (%),Metric,3.515625
05/08/2025 15:11:31,System.Runtime,Working Set (MB),Metric,275.492864
05/08/2025 15:11:31,System.Runtime,GC Heap Size (MB),Metric,65.592032
05/08/2025 15:11:31,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:31,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:31,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:31,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:31,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:11:31,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:31,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:31,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,3
05/08/2025 15:11:31,System.Runtime,Allocation Rate (B / 1 sec),Rate,1172920
05/08/2025 15:11:31,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:31,System.Runtime,GC Fragmentation (%),Metric,38.76087952597112
05/08/2025 15:11:31,System.Runtime,GC Committed Bytes (MB),Metric,84.963328
05/08/2025 15:11:31,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:31,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:31,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:11:31,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:31,System.Runtime,Gen 1 Size (B),Metric,1044032
05/08/2025 15:11:31,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:11:31,System.Runtime,LOH Size (B),Metric,9722728
05/08/2025 15:11:31,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:31,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:31,System.Runtime,IL Bytes Jitted (B),Metric,1166465
05/08/2025 15:11:31,System.Runtime,Number of Methods Jitted,Metric,14457
05/08/2025 15:11:31,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,26.662000000000262
05/08/2025 15:11:32,System.Runtime,CPU Usage (%),Metric,1.5873015873015872
05/08/2025 15:11:32,System.Runtime,Working Set (MB),Metric,268.111872
05/08/2025 15:11:32,System.Runtime,GC Heap Size (MB),Metric,16.28264
05/08/2025 15:11:32,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,1
05/08/2025 15:11:32,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:32,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:32,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:32,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:11:32,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:32,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:32,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:11:32,System.Runtime,Allocation Rate (B / 1 sec),Rate,11903704
05/08/2025 15:11:32,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:32,System.Runtime,GC Fragmentation (%),Metric,25.625554980288257
05/08/2025 15:11:32,System.Runtime,GC Committed Bytes (MB),Metric,86.638592
05/08/2025 15:11:32,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:32,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:32,System.Runtime,Time paused by GC (ms / 1 sec),Rate,1.2830000000000013
05/08/2025 15:11:32,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:32,System.Runtime,Gen 1 Size (B),Metric,1085592
05/08/2025 15:11:32,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:11:32,System.Runtime,LOH Size (B),Metric,10876784
05/08/2025 15:11:32,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:32,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:32,System.Runtime,IL Bytes Jitted (B),Metric,1166895
05/08/2025 15:11:32,System.Runtime,Number of Methods Jitted,Metric,14464
05/08/2025 15:11:32,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,4.680599999999686
05/08/2025 15:11:33,System.Runtime,CPU Usage (%),Metric,8.98661567877629
05/08/2025 15:11:33,System.Runtime,Working Set (MB),Metric,273.063936
05/08/2025 15:11:33,System.Runtime,GC Heap Size (MB),Metric,47.83972
05/08/2025 15:11:33,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:33,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:33,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:33,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:33,System.Runtime,ThreadPool Thread Count,Metric,5
05/08/2025 15:11:33,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:33,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:33,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,12
05/08/2025 15:11:33,System.Runtime,Allocation Rate (B / 1 sec),Rate,31451608
05/08/2025 15:11:33,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:33,System.Runtime,GC Fragmentation (%),Metric,25.625554980288257
05/08/2025 15:11:33,System.Runtime,GC Committed Bytes (MB),Metric,86.638592
05/08/2025 15:11:33,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:33,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:33,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:11:33,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:33,System.Runtime,Gen 1 Size (B),Metric,1085592
05/08/2025 15:11:33,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:11:33,System.Runtime,LOH Size (B),Metric,10876784
05/08/2025 15:11:33,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:33,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:33,System.Runtime,IL Bytes Jitted (B),Metric,1169187
05/08/2025 15:11:33,System.Runtime,Number of Methods Jitted,Metric,14475
05/08/2025 15:11:33,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,18.37840000000142
05/08/2025 15:11:34,System.Runtime,CPU Usage (%),Metric,0
05/08/2025 15:11:34,System.Runtime,Working Set (MB),Metric,273.043456
05/08/2025 15:11:34,System.Runtime,GC Heap Size (MB),Metric,47.847888
05/08/2025 15:11:34,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:34,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:34,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:34,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:34,System.Runtime,ThreadPool Thread Count,Metric,5
05/08/2025 15:11:34,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:34,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:34,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,1
05/08/2025 15:11:34,System.Runtime,Allocation Rate (B / 1 sec),Rate,8168
05/08/2025 15:11:34,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:34,System.Runtime,GC Fragmentation (%),Metric,25.625554980288257
05/08/2025 15:11:34,System.Runtime,GC Committed Bytes (MB),Metric,86.638592
05/08/2025 15:11:34,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:34,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:34,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:11:34,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:34,System.Runtime,Gen 1 Size (B),Metric,1085592
05/08/2025 15:11:34,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:11:34,System.Runtime,LOH Size (B),Metric,10876784
05/08/2025 15:11:34,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:34,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:34,System.Runtime,IL Bytes Jitted (B),Metric,1169187
05/08/2025 15:11:34,System.Runtime,Number of Methods Jitted,Metric,14475
05/08/2025 15:11:34,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:11:35,System.Runtime,CPU Usage (%),Metric,1.7964071856287425
05/08/2025 15:11:35,System.Runtime,Working Set (MB),Metric,273.342464
05/08/2025 15:11:35,System.Runtime,GC Heap Size (MB),Metric,49.090312
05/08/2025 15:11:35,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:35,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:35,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:35,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:35,System.Runtime,ThreadPool Thread Count,Metric,5
05/08/2025 15:11:35,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:35,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:35,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,3
05/08/2025 15:11:35,System.Runtime,Allocation Rate (B / 1 sec),Rate,1239160
05/08/2025 15:11:35,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:35,System.Runtime,GC Fragmentation (%),Metric,25.625554980288257
05/08/2025 15:11:35,System.Runtime,GC Committed Bytes (MB),Metric,86.638592
05/08/2025 15:11:35,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:35,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:35,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:11:35,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:35,System.Runtime,Gen 1 Size (B),Metric,1085592
05/08/2025 15:11:35,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:11:35,System.Runtime,LOH Size (B),Metric,10876784
05/08/2025 15:11:35,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:35,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:35,System.Runtime,IL Bytes Jitted (B),Metric,1169237
05/08/2025 15:11:35,System.Runtime,Number of Methods Jitted,Metric,14476
05/08/2025 15:11:35,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0.903599999999642
05/08/2025 15:11:36,System.Runtime,CPU Usage (%),Metric,0.3838771593090211
05/08/2025 15:11:36,System.Runtime,Working Set (MB),Metric,273.371136
05/08/2025 15:11:36,System.Runtime,GC Heap Size (MB),Metric,49.10676
05/08/2025 15:11:36,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:36,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:36,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:36,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:36,System.Runtime,ThreadPool Thread Count,Metric,5
05/08/2025 15:11:36,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:36,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:36,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:11:36,System.Runtime,Allocation Rate (B / 1 sec),Rate,24600
05/08/2025 15:11:36,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:36,System.Runtime,GC Fragmentation (%),Metric,25.625554980288257
05/08/2025 15:11:36,System.Runtime,GC Committed Bytes (MB),Metric,86.638592
05/08/2025 15:11:36,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:36,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:36,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:11:36,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:36,System.Runtime,Gen 1 Size (B),Metric,1085592
05/08/2025 15:11:36,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:11:36,System.Runtime,LOH Size (B),Metric,10876784
05/08/2025 15:11:36,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:36,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:36,System.Runtime,IL Bytes Jitted (B),Metric,1169237
05/08/2025 15:11:36,System.Runtime,Number of Methods Jitted,Metric,14476
05/08/2025 15:11:36,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:11:37,System.Runtime,CPU Usage (%),Metric,7.662082514734774
05/08/2025 15:11:37,System.Runtime,Working Set (MB),Metric,271.314944
05/08/2025 15:11:37,System.Runtime,GC Heap Size (MB),Metric,33.333896
05/08/2025 15:11:37,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,1
05/08/2025 15:11:37,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:37,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:37,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:37,System.Runtime,ThreadPool Thread Count,Metric,5
05/08/2025 15:11:37,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:37,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:37,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:11:37,System.Runtime,Allocation Rate (B / 1 sec),Rate,42795848
05/08/2025 15:11:37,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:37,System.Runtime,GC Fragmentation (%),Metric,22.49418531345654
05/08/2025 15:11:37,System.Runtime,GC Committed Bytes (MB),Metric,88.301568
05/08/2025 15:11:37,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:37,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:37,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0.847999999999999
05/08/2025 15:11:37,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:37,System.Runtime,Gen 1 Size (B),Metric,1174440
05/08/2025 15:11:37,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:11:37,System.Runtime,LOH Size (B),Metric,12085376
05/08/2025 15:11:37,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:37,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:37,System.Runtime,IL Bytes Jitted (B),Metric,1170154
05/08/2025 15:11:37,System.Runtime,Number of Methods Jitted,Metric,14487
05/08/2025 15:11:37,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,16.950199999999313
05/08/2025 15:11:38,System.Runtime,CPU Usage (%),Metric,1.7307692307692308
05/08/2025 15:11:38,System.Runtime,Working Set (MB),Metric,273.444864
05/08/2025 15:11:38,System.Runtime,GC Heap Size (MB),Metric,33.886472
05/08/2025 15:11:38,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:38,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:38,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:38,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:38,System.Runtime,ThreadPool Thread Count,Metric,5
05/08/2025 15:11:38,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:38,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:38,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,12
05/08/2025 15:11:38,System.Runtime,Allocation Rate (B / 1 sec),Rate,552408
05/08/2025 15:11:38,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:38,System.Runtime,GC Fragmentation (%),Metric,22.49418531345654
05/08/2025 15:11:38,System.Runtime,GC Committed Bytes (MB),Metric,88.301568
05/08/2025 15:11:38,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:38,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:38,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:11:38,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:38,System.Runtime,Gen 1 Size (B),Metric,1174440
05/08/2025 15:11:38,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:11:38,System.Runtime,LOH Size (B),Metric,12085376
05/08/2025 15:11:38,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:38,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:38,System.Runtime,IL Bytes Jitted (B),Metric,1170915
05/08/2025 15:11:38,System.Runtime,Number of Methods Jitted,Metric,14491
05/08/2025 15:11:38,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,12.316500000000815
05/08/2025 15:11:39,System.Runtime,CPU Usage (%),Metric,1.9607843137254901
05/08/2025 15:11:39,System.Runtime,Working Set (MB),Metric,273.575936
05/08/2025 15:11:39,System.Runtime,GC Heap Size (MB),Metric,35.082952
05/08/2025 15:11:39,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:39,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:39,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:39,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:39,System.Runtime,ThreadPool Thread Count,Metric,5
05/08/2025 15:11:39,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:39,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:39,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,3
05/08/2025 15:11:39,System.Runtime,Allocation Rate (B / 1 sec),Rate,1193264
05/08/2025 15:11:39,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:39,System.Runtime,GC Fragmentation (%),Metric,22.49418531345654
05/08/2025 15:11:39,System.Runtime,GC Committed Bytes (MB),Metric,88.301568
05/08/2025 15:11:39,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:39,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:39,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:11:39,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:39,System.Runtime,Gen 1 Size (B),Metric,1174440
05/08/2025 15:11:39,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:11:39,System.Runtime,LOH Size (B),Metric,12085376
05/08/2025 15:11:39,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:39,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:39,System.Runtime,IL Bytes Jitted (B),Metric,1171318
05/08/2025 15:11:39,System.Runtime,Number of Methods Jitted,Metric,14494
05/08/2025 15:11:39,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,4.27639999999883
05/08/2025 15:11:40,System.Runtime,CPU Usage (%),Metric,0.19801980198019803
05/08/2025 15:11:40,System.Runtime,Working Set (MB),Metric,273.580032
05/08/2025 15:11:40,System.Runtime,GC Heap Size (MB),Metric,35.09108
05/08/2025 15:11:40,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:40,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:40,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:40,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:40,System.Runtime,ThreadPool Thread Count,Metric,5
05/08/2025 15:11:40,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:40,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:40,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:11:40,System.Runtime,Allocation Rate (B / 1 sec),Rate,8128
05/08/2025 15:11:40,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:40,System.Runtime,GC Fragmentation (%),Metric,22.49418531345654
05/08/2025 15:11:40,System.Runtime,GC Committed Bytes (MB),Metric,88.301568
05/08/2025 15:11:40,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:40,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:40,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:11:40,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:40,System.Runtime,Gen 1 Size (B),Metric,1174440
05/08/2025 15:11:40,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:11:40,System.Runtime,LOH Size (B),Metric,12085376
05/08/2025 15:11:40,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:40,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:40,System.Runtime,IL Bytes Jitted (B),Metric,1171318
05/08/2025 15:11:40,System.Runtime,Number of Methods Jitted,Metric,14494
05/08/2025 15:11:40,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:11:41,System.Runtime,CPU Usage (%),Metric,0.19569471624266144
05/08/2025 15:11:41,System.Runtime,Working Set (MB),Metric,273.563648
05/08/2025 15:11:41,System.Runtime,GC Heap Size (MB),Metric,35.107528
05/08/2025 15:11:41,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:41,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:41,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:41,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:41,System.Runtime,ThreadPool Thread Count,Metric,5
05/08/2025 15:11:41,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:41,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:41,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:11:41,System.Runtime,Allocation Rate (B / 1 sec),Rate,17016
05/08/2025 15:11:41,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:41,System.Runtime,GC Fragmentation (%),Metric,22.49418531345654
05/08/2025 15:11:41,System.Runtime,GC Committed Bytes (MB),Metric,88.301568
05/08/2025 15:11:41,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:41,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:41,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:11:41,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:41,System.Runtime,Gen 1 Size (B),Metric,1174440
05/08/2025 15:11:41,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:11:41,System.Runtime,LOH Size (B),Metric,12085376
05/08/2025 15:11:41,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:41,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:41,System.Runtime,IL Bytes Jitted (B),Metric,1171318
05/08/2025 15:11:41,System.Runtime,Number of Methods Jitted,Metric,14494
05/08/2025 15:11:41,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:11:42,System.Runtime,CPU Usage (%),Metric,10.1364522417154
05/08/2025 15:11:42,System.Runtime,Working Set (MB),Metric,276.660224
05/08/2025 15:11:42,System.Runtime,GC Heap Size (MB),Metric,19.675088
05/08/2025 15:11:42,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,1
05/08/2025 15:11:42,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:42,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:42,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:42,System.Runtime,ThreadPool Thread Count,Metric,5
05/08/2025 15:11:42,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:42,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:42,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,12
05/08/2025 15:11:42,System.Runtime,Allocation Rate (B / 1 sec),Rate,43306176
05/08/2025 15:11:42,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:42,System.Runtime,GC Fragmentation (%),Metric,19.093206048657954
05/08/2025 15:11:42,System.Runtime,GC Committed Bytes (MB),Metric,89.341952
05/08/2025 15:11:42,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:42,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:42,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0.963000000000001
05/08/2025 15:11:42,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:42,System.Runtime,Gen 1 Size (B),Metric,1257960
05/08/2025 15:11:42,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:11:42,System.Runtime,LOH Size (B),Metric,13128856
05/08/2025 15:11:42,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:42,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:42,System.Runtime,IL Bytes Jitted (B),Metric,1174420
05/08/2025 15:11:42,System.Runtime,Number of Methods Jitted,Metric,14534
05/08/2025 15:11:42,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,61.52360000000044
05/08/2025 15:11:43,System.Runtime,CPU Usage (%),Metric,0.19569471624266144
05/08/2025 15:11:43,System.Runtime,Working Set (MB),Metric,276.697088
05/08/2025 15:11:43,System.Runtime,GC Heap Size (MB),Metric,19.683312
05/08/2025 15:11:43,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:43,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:43,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:43,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:43,System.Runtime,ThreadPool Thread Count,Metric,5
05/08/2025 15:11:43,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:43,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:43,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:11:43,System.Runtime,Allocation Rate (B / 1 sec),Rate,8200
05/08/2025 15:11:43,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:43,System.Runtime,GC Fragmentation (%),Metric,19.093206048657954
05/08/2025 15:11:43,System.Runtime,GC Committed Bytes (MB),Metric,89.341952
05/08/2025 15:11:43,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:43,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:43,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:11:43,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:43,System.Runtime,Gen 1 Size (B),Metric,1257960
05/08/2025 15:11:43,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:11:43,System.Runtime,LOH Size (B),Metric,13128856
05/08/2025 15:11:43,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:43,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:43,System.Runtime,IL Bytes Jitted (B),Metric,1175398
05/08/2025 15:11:43,System.Runtime,Number of Methods Jitted,Metric,14552
05/08/2025 15:11:43,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,20.061900000000605
05/08/2025 15:11:44,System.Runtime,CPU Usage (%),Metric,3.088803088803089
05/08/2025 15:11:44,System.Runtime,Working Set (MB),Metric,276.97152
05/08/2025 15:11:44,System.Runtime,GC Heap Size (MB),Metric,20.929456
05/08/2025 15:11:44,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:44,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:44,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:44,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:44,System.Runtime,ThreadPool Thread Count,Metric,5
05/08/2025 15:11:44,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:44,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:44,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,3
05/08/2025 15:11:44,System.Runtime,Allocation Rate (B / 1 sec),Rate,1242832
05/08/2025 15:11:44,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:44,System.Runtime,GC Fragmentation (%),Metric,19.093206048657954
05/08/2025 15:11:44,System.Runtime,GC Committed Bytes (MB),Metric,89.341952
05/08/2025 15:11:44,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:44,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:44,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:11:44,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:44,System.Runtime,Gen 1 Size (B),Metric,1257960
05/08/2025 15:11:44,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:11:44,System.Runtime,LOH Size (B),Metric,13128856
05/08/2025 15:11:44,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:44,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:44,System.Runtime,IL Bytes Jitted (B),Metric,1179710
05/08/2025 15:11:44,System.Runtime,Number of Methods Jitted,Metric,14596
05/08/2025 15:11:44,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,99.47159999999894
05/08/2025 15:11:45,System.Runtime,CPU Usage (%),Metric,0
05/08/2025 15:11:45,System.Runtime,Working Set (MB),Metric,277.000192
05/08/2025 15:11:45,System.Runtime,GC Heap Size (MB),Metric,20.954128
05/08/2025 15:11:45,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:45,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:45,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:45,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:45,System.Runtime,ThreadPool Thread Count,Metric,5
05/08/2025 15:11:45,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:45,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:45,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:11:45,System.Runtime,Allocation Rate (B / 1 sec),Rate,24600
05/08/2025 15:11:45,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:45,System.Runtime,GC Fragmentation (%),Metric,19.093206048657954
05/08/2025 15:11:45,System.Runtime,GC Committed Bytes (MB),Metric,89.341952
05/08/2025 15:11:45,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:45,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:45,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:11:45,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:45,System.Runtime,Gen 1 Size (B),Metric,1257960
05/08/2025 15:11:45,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:11:45,System.Runtime,LOH Size (B),Metric,13128856
05/08/2025 15:11:45,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:45,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:45,System.Runtime,IL Bytes Jitted (B),Metric,1179710
05/08/2025 15:11:45,System.Runtime,Number of Methods Jitted,Metric,14596
05/08/2025 15:11:45,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:11:46,System.Runtime,CPU Usage (%),Metric,6.213592233009709
05/08/2025 15:11:46,System.Runtime,Working Set (MB),Metric,281.284608
05/08/2025 15:11:46,System.Runtime,GC Heap Size (MB),Metric,63.285264
05/08/2025 15:11:46,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:46,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:46,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:46,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:46,System.Runtime,ThreadPool Thread Count,Metric,5
05/08/2025 15:11:46,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:46,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:46,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:11:46,System.Runtime,Allocation Rate (B / 1 sec),Rate,42198472
05/08/2025 15:11:46,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:46,System.Runtime,GC Fragmentation (%),Metric,19.093206048657954
05/08/2025 15:11:46,System.Runtime,GC Committed Bytes (MB),Metric,89.341952
05/08/2025 15:11:46,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:46,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:46,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:11:46,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:46,System.Runtime,Gen 1 Size (B),Metric,1257960
05/08/2025 15:11:46,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:11:46,System.Runtime,LOH Size (B),Metric,13128856
05/08/2025 15:11:46,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:46,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:46,System.Runtime,IL Bytes Jitted (B),Metric,1182414
05/08/2025 15:11:46,System.Runtime,Number of Methods Jitted,Metric,14612
05/08/2025 15:11:46,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,23.256500000001324
05/08/2025 15:11:47,System.Runtime,CPU Usage (%),Metric,5.642023346303502
05/08/2025 15:11:47,System.Runtime,Working Set (MB),Metric,283.168768
05/08/2025 15:11:47,System.Runtime,GC Heap Size (MB),Metric,64.485832
05/08/2025 15:11:47,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:47,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:47,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:47,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:47,System.Runtime,ThreadPool Thread Count,Metric,5
05/08/2025 15:11:47,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:47,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:47,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,12
05/08/2025 15:11:47,System.Runtime,Allocation Rate (B / 1 sec),Rate,1192040
05/08/2025 15:11:47,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:47,System.Runtime,GC Fragmentation (%),Metric,19.093206048657954
05/08/2025 15:11:47,System.Runtime,GC Committed Bytes (MB),Metric,89.341952
05/08/2025 15:11:47,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:47,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:47,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:11:47,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:47,System.Runtime,Gen 1 Size (B),Metric,1257960
05/08/2025 15:11:47,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:11:47,System.Runtime,LOH Size (B),Metric,13128856
05/08/2025 15:11:47,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:47,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:47,System.Runtime,IL Bytes Jitted (B),Metric,1185789
05/08/2025 15:11:47,System.Runtime,Number of Methods Jitted,Metric,14646
05/08/2025 15:11:47,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,95.48639999999978
05/08/2025 15:11:48,System.Runtime,CPU Usage (%),Metric,1.3671875
05/08/2025 15:11:48,System.Runtime,Working Set (MB),Metric,283.29984
05/08/2025 15:11:48,System.Runtime,GC Heap Size (MB),Metric,65.661568
05/08/2025 15:11:48,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:48,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:48,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:48,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:48,System.Runtime,ThreadPool Thread Count,Metric,5
05/08/2025 15:11:48,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:48,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:48,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,3
05/08/2025 15:11:48,System.Runtime,Allocation Rate (B / 1 sec),Rate,1172544
05/08/2025 15:11:48,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:48,System.Runtime,GC Fragmentation (%),Metric,19.093206048657954
05/08/2025 15:11:48,System.Runtime,GC Committed Bytes (MB),Metric,89.341952
05/08/2025 15:11:48,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:48,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:48,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:11:48,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:48,System.Runtime,Gen 1 Size (B),Metric,1257960
05/08/2025 15:11:48,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:11:48,System.Runtime,LOH Size (B),Metric,13128856
05/08/2025 15:11:48,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:48,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:48,System.Runtime,IL Bytes Jitted (B),Metric,1186113
05/08/2025 15:11:48,System.Runtime,Number of Methods Jitted,Metric,14656
05/08/2025 15:11:48,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,5.143299999999726
05/08/2025 15:11:49,System.Runtime,CPU Usage (%),Metric,0.19305019305019305
05/08/2025 15:11:49,System.Runtime,Working Set (MB),Metric,283.29984
05/08/2025 15:11:49,System.Runtime,GC Heap Size (MB),Metric,65.661568
05/08/2025 15:11:49,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:49,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:49,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:49,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:49,System.Runtime,ThreadPool Thread Count,Metric,5
05/08/2025 15:11:49,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:49,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:49,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,1
05/08/2025 15:11:49,System.Runtime,Allocation Rate (B / 1 sec),Rate,8200
05/08/2025 15:11:49,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:49,System.Runtime,GC Fragmentation (%),Metric,19.093206048657954
05/08/2025 15:11:49,System.Runtime,GC Committed Bytes (MB),Metric,89.341952
05/08/2025 15:11:49,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:49,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:49,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:11:49,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:49,System.Runtime,Gen 1 Size (B),Metric,1257960
05/08/2025 15:11:49,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:11:49,System.Runtime,LOH Size (B),Metric,13128856
05/08/2025 15:11:49,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:49,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:49,System.Runtime,IL Bytes Jitted (B),Metric,1186553
05/08/2025 15:11:49,System.Runtime,Number of Methods Jitted,Metric,14658
05/08/2025 15:11:49,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,4.897999999999229
05/08/2025 15:11:50,System.Runtime,CPU Usage (%),Metric,0.1976284584980237
05/08/2025 15:11:50,System.Runtime,Working Set (MB),Metric,283.328512
05/08/2025 15:11:50,System.Runtime,GC Heap Size (MB),Metric,65.685704
05/08/2025 15:11:50,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:50,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:50,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:50,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:50,System.Runtime,ThreadPool Thread Count,Metric,5
05/08/2025 15:11:50,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:50,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:50,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:11:50,System.Runtime,Allocation Rate (B / 1 sec),Rate,15888
05/08/2025 15:11:50,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:50,System.Runtime,GC Fragmentation (%),Metric,19.093206048657954
05/08/2025 15:11:50,System.Runtime,GC Committed Bytes (MB),Metric,89.341952
05/08/2025 15:11:50,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:50,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:50,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:11:50,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:50,System.Runtime,Gen 1 Size (B),Metric,1257960
05/08/2025 15:11:50,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:11:50,System.Runtime,LOH Size (B),Metric,13128856
05/08/2025 15:11:50,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:50,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:50,System.Runtime,IL Bytes Jitted (B),Metric,1186553
05/08/2025 15:11:50,System.Runtime,Number of Methods Jitted,Metric,14658
05/08/2025 15:11:50,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:11:51,System.Runtime,CPU Usage (%),Metric,10.961538461538462
05/08/2025 15:11:51,System.Runtime,Working Set (MB),Metric,279.740416
05/08/2025 15:11:51,System.Runtime,GC Heap Size (MB),Metric,49.460896
05/08/2025 15:11:51,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,1
05/08/2025 15:11:51,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:51,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:51,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:51,System.Runtime,ThreadPool Thread Count,Metric,5
05/08/2025 15:11:51,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:51,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:51,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:11:51,System.Runtime,Allocation Rate (B / 1 sec),Rate,42787040
05/08/2025 15:11:51,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:51,System.Runtime,GC Fragmentation (%),Metric,12.248687956668713
05/08/2025 15:11:51,System.Runtime,GC Committed Bytes (MB),Metric,91.15648
05/08/2025 15:11:51,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:51,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:51,System.Runtime,Time paused by GC (ms / 1 sec),Rate,1.3500000000000014
05/08/2025 15:11:51,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:51,System.Runtime,Gen 1 Size (B),Metric,1395632
05/08/2025 15:11:51,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:11:51,System.Runtime,LOH Size (B),Metric,14941904
05/08/2025 15:11:51,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:51,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:51,System.Runtime,IL Bytes Jitted (B),Metric,1186767
05/08/2025 15:11:51,System.Runtime,Number of Methods Jitted,Metric,14665
05/08/2025 15:11:51,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,1.4439000000002125
05/08/2025 15:11:52,System.Runtime,CPU Usage (%),Metric,1.3725490196078431
05/08/2025 15:11:52,System.Runtime,Working Set (MB),Metric,279.969792
05/08/2025 15:11:52,System.Runtime,GC Heap Size (MB),Metric,50.000128
05/08/2025 15:11:52,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:52,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:52,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:52,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:52,System.Runtime,ThreadPool Thread Count,Metric,5
05/08/2025 15:11:52,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,1
05/08/2025 15:11:52,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:52,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,12
05/08/2025 15:11:52,System.Runtime,Allocation Rate (B / 1 sec),Rate,539136
05/08/2025 15:11:52,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:52,System.Runtime,GC Fragmentation (%),Metric,12.248687956668713
05/08/2025 15:11:52,System.Runtime,GC Committed Bytes (MB),Metric,91.15648
05/08/2025 15:11:52,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:52,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:52,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:11:52,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:52,System.Runtime,Gen 1 Size (B),Metric,1395632
05/08/2025 15:11:52,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:11:52,System.Runtime,LOH Size (B),Metric,14941904
05/08/2025 15:11:52,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:52,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:52,System.Runtime,IL Bytes Jitted (B),Metric,1187320
05/08/2025 15:11:52,System.Runtime,Number of Methods Jitted,Metric,14678
05/08/2025 15:11:52,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,11.35950000000048
05/08/2025 15:11:53,System.Runtime,CPU Usage (%),Metric,1.761252446183953
05/08/2025 15:11:53,System.Runtime,Working Set (MB),Metric,280.219648
05/08/2025 15:11:53,System.Runtime,GC Heap Size (MB),Metric,51.22128
05/08/2025 15:11:53,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:53,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:53,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:53,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:53,System.Runtime,ThreadPool Thread Count,Metric,3
05/08/2025 15:11:53,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:53,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:53,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,3
05/08/2025 15:11:53,System.Runtime,Allocation Rate (B / 1 sec),Rate,1209728
05/08/2025 15:11:53,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:53,System.Runtime,GC Fragmentation (%),Metric,12.248687956668713
05/08/2025 15:11:53,System.Runtime,GC Committed Bytes (MB),Metric,91.15648
05/08/2025 15:11:53,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:53,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:53,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:11:53,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:53,System.Runtime,Gen 1 Size (B),Metric,1395632
05/08/2025 15:11:53,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:11:53,System.Runtime,LOH Size (B),Metric,14941904
05/08/2025 15:11:53,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:53,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:53,System.Runtime,IL Bytes Jitted (B),Metric,1187471
05/08/2025 15:11:53,System.Runtime,Number of Methods Jitted,Metric,14679
05/08/2025 15:11:53,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,1.5316000000002532
05/08/2025 15:11:54,System.Runtime,CPU Usage (%),Metric,4.930966469428008
05/08/2025 15:11:54,System.Runtime,Working Set (MB),Metric,274.972672
05/08/2025 15:11:54,System.Runtime,GC Heap Size (MB),Metric,32.893656
05/08/2025 15:11:54,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,1
05/08/2025 15:11:54,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:54,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:54,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:54,System.Runtime,ThreadPool Thread Count,Metric,3
05/08/2025 15:11:54,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:54,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:54,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:11:54,System.Runtime,Allocation Rate (B / 1 sec),Rate,42143416
05/08/2025 15:11:54,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:54,System.Runtime,GC Fragmentation (%),Metric,9.886098570455994
05/08/2025 15:11:54,System.Runtime,GC Committed Bytes (MB),Metric,92.196864
05/08/2025 15:11:54,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:54,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:54,System.Runtime,Time paused by GC (ms / 1 sec),Rate,1.1439999999999984
05/08/2025 15:11:54,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:54,System.Runtime,Gen 1 Size (B),Metric,1437728
05/08/2025 15:11:54,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:11:54,System.Runtime,LOH Size (B),Metric,15985728
05/08/2025 15:11:54,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:54,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:54,System.Runtime,IL Bytes Jitted (B),Metric,1187553
05/08/2025 15:11:54,System.Runtime,Number of Methods Jitted,Metric,14682
05/08/2025 15:11:54,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,1.5138999999999214
05/08/2025 15:11:55,System.Runtime,CPU Usage (%),Metric,5.335968379446641
05/08/2025 15:11:55,System.Runtime,Working Set (MB),Metric,278.863872
05/08/2025 15:11:55,System.Runtime,GC Heap Size (MB),Metric,34.069256
05/08/2025 15:11:55,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:55,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:55,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:55,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:55,System.Runtime,ThreadPool Thread Count,Metric,3
05/08/2025 15:11:55,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:55,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:55,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,12
05/08/2025 15:11:55,System.Runtime,Allocation Rate (B / 1 sec),Rate,1175192
05/08/2025 15:11:55,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:55,System.Runtime,GC Fragmentation (%),Metric,9.886098570455994
05/08/2025 15:11:55,System.Runtime,GC Committed Bytes (MB),Metric,92.196864
05/08/2025 15:11:55,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:55,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:55,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:11:55,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:55,System.Runtime,Gen 1 Size (B),Metric,1437728
05/08/2025 15:11:55,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:11:55,System.Runtime,LOH Size (B),Metric,15985728
05/08/2025 15:11:55,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:55,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:55,System.Runtime,IL Bytes Jitted (B),Metric,1187826
05/08/2025 15:11:55,System.Runtime,Number of Methods Jitted,Metric,14685
05/08/2025 15:11:55,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,3.256600000000617
05/08/2025 15:11:56,System.Runtime,CPU Usage (%),Metric,2.4714828897338403
05/08/2025 15:11:56,System.Runtime,Working Set (MB),Metric,279.416832
05/08/2025 15:11:56,System.Runtime,GC Heap Size (MB),Metric,35.26576
05/08/2025 15:11:56,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:56,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:56,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:56,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:56,System.Runtime,ThreadPool Thread Count,Metric,3
05/08/2025 15:11:56,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:56,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:56,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,3
05/08/2025 15:11:56,System.Runtime,Allocation Rate (B / 1 sec),Rate,1193264
05/08/2025 15:11:56,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:56,System.Runtime,GC Fragmentation (%),Metric,9.886098570455994
05/08/2025 15:11:56,System.Runtime,GC Committed Bytes (MB),Metric,92.196864
05/08/2025 15:11:56,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:56,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:56,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:11:56,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:56,System.Runtime,Gen 1 Size (B),Metric,1437728
05/08/2025 15:11:56,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:11:56,System.Runtime,LOH Size (B),Metric,15985728
05/08/2025 15:11:56,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:56,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:56,System.Runtime,IL Bytes Jitted (B),Metric,1189487
05/08/2025 15:11:56,System.Runtime,Number of Methods Jitted,Metric,14695
05/08/2025 15:11:56,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,12.812399999998888
05/08/2025 15:11:57,System.Runtime,CPU Usage (%),Metric,0.1976284584980237
05/08/2025 15:11:57,System.Runtime,Working Set (MB),Metric,279.461888
05/08/2025 15:11:57,System.Runtime,GC Heap Size (MB),Metric,35.31172
05/08/2025 15:11:57,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:57,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:57,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:57,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:57,System.Runtime,ThreadPool Thread Count,Metric,3
05/08/2025 15:11:57,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:57,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:57,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,2
05/08/2025 15:11:57,System.Runtime,Allocation Rate (B / 1 sec),Rate,45792
05/08/2025 15:11:57,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:57,System.Runtime,GC Fragmentation (%),Metric,9.886098570455994
05/08/2025 15:11:57,System.Runtime,GC Committed Bytes (MB),Metric,92.196864
05/08/2025 15:11:57,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:57,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:57,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:11:57,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:57,System.Runtime,Gen 1 Size (B),Metric,1437728
05/08/2025 15:11:57,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:11:57,System.Runtime,LOH Size (B),Metric,15985728
05/08/2025 15:11:57,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:57,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:57,System.Runtime,IL Bytes Jitted (B),Metric,1189579
05/08/2025 15:11:57,System.Runtime,Number of Methods Jitted,Metric,14700
05/08/2025 15:11:57,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,1.6985999999997148
05/08/2025 15:11:58,System.Runtime,CPU Usage (%),Metric,0.1937984496124031
05/08/2025 15:11:58,System.Runtime,Working Set (MB),Metric,279.465984
05/08/2025 15:11:58,System.Runtime,GC Heap Size (MB),Metric,35.328168
05/08/2025 15:11:58,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:58,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:58,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:58,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:58,System.Runtime,ThreadPool Thread Count,Metric,3
05/08/2025 15:11:58,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:11:58,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:58,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:11:58,System.Runtime,Allocation Rate (B / 1 sec),Rate,17016
05/08/2025 15:11:58,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:58,System.Runtime,GC Fragmentation (%),Metric,9.886098570455994
05/08/2025 15:11:58,System.Runtime,GC Committed Bytes (MB),Metric,92.196864
05/08/2025 15:11:58,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:58,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:58,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:11:58,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:58,System.Runtime,Gen 1 Size (B),Metric,1437728
05/08/2025 15:11:58,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:11:58,System.Runtime,LOH Size (B),Metric,15985728
05/08/2025 15:11:58,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:58,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:58,System.Runtime,IL Bytes Jitted (B),Metric,1189579
05/08/2025 15:11:58,System.Runtime,Number of Methods Jitted,Metric,14700
05/08/2025 15:11:58,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:11:59,System.Runtime,CPU Usage (%),Metric,10.474308300395258
05/08/2025 15:11:59,System.Runtime,Working Set (MB),Metric,284.172288
05/08/2025 15:11:59,System.Runtime,GC Heap Size (MB),Metric,78.80888
05/08/2025 15:11:59,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:59,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:59,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:11:59,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:11:59,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:11:59,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,1
05/08/2025 15:11:59,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:11:59,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,12
05/08/2025 15:11:59,System.Runtime,Allocation Rate (B / 1 sec),Rate,43339016
05/08/2025 15:11:59,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:11:59,System.Runtime,GC Fragmentation (%),Metric,9.886098570455994
05/08/2025 15:11:59,System.Runtime,GC Committed Bytes (MB),Metric,92.196864
05/08/2025 15:11:59,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:11:59,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:11:59,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:11:59,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:11:59,System.Runtime,Gen 1 Size (B),Metric,1437728
05/08/2025 15:11:59,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:11:59,System.Runtime,LOH Size (B),Metric,15985728
05/08/2025 15:11:59,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:11:59,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:11:59,System.Runtime,IL Bytes Jitted (B),Metric,1191897
05/08/2025 15:11:59,System.Runtime,Number of Methods Jitted,Metric,14718
05/08/2025 15:11:59,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,31.499300000001313
05/08/2025 15:12:00,System.Runtime,CPU Usage (%),Metric,0
05/08/2025 15:12:00,System.Runtime,Working Set (MB),Metric,284.172288
05/08/2025 15:12:00,System.Runtime,GC Heap Size (MB),Metric,78.817064
05/08/2025 15:12:00,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:12:00,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:12:00,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:12:00,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:12:00,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:12:00,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:12:00,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:12:00,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:12:00,System.Runtime,Allocation Rate (B / 1 sec),Rate,8184
05/08/2025 15:12:00,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:12:00,System.Runtime,GC Fragmentation (%),Metric,9.886098570455994
05/08/2025 15:12:00,System.Runtime,GC Committed Bytes (MB),Metric,92.196864
05/08/2025 15:12:00,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:12:00,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:12:00,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:12:00,System.Runtime,Gen 0 Size (B),Metric,278480
05/08/2025 15:12:00,System.Runtime,Gen 1 Size (B),Metric,1437728
05/08/2025 15:12:00,System.Runtime,Gen 2 Size (B),Metric,3885232
05/08/2025 15:12:00,System.Runtime,LOH Size (B),Metric,15985728
05/08/2025 15:12:00,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:12:00,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:12:00,System.Runtime,IL Bytes Jitted (B),Metric,1191897
05/08/2025 15:12:00,System.Runtime,Number of Methods Jitted,Metric,14718
05/08/2025 15:12:00,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
