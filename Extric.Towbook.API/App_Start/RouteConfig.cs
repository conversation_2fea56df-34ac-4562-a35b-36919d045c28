using System;
using System.Linq;
using System.Net.Http;
using System.Reflection;
using Microsoft.AspNetCore.Routing;

namespace Extric.Towbook.API
{
    public class RouteConfig
    {
        /// <summary>
        /// Invokes the RegisterRoutes method on any provider assemblies that have a class named ProviderRegistration. 
        /// </summary>
        /// <param name="routes"></param>
        public static void RegisterProviderRoutes(RouteCollection routes)
        {
            const string @namespace = "Extric.Towbook.API.Integration";
            const string @roadsideNamespace = "Extric.Roadside.API";
            const string @permitNamespace = "Extric.Towbook.PermitRequests.API";

            var x = AppDomain.CurrentDomain.GetAssemblies().Where(t => 
                t.FullName.StartsWith(@namespace) || /*t.FullName.StartsWith(roadsideNamespace) ||*/ t.FullName.StartsWith(permitNamespace));

            foreach (var y in x)
            {
                var types = from d in y.GetTypes()
                        where d.IsClass && d.Namespace != null && (d.Namespace.StartsWith(@namespace) || d.Namespace.StartsWith(roadsideNamespace) || d.Namespace.StartsWith(permitNamespace)) && d.Name == "ProviderRegistration"
                        select d;

                foreach(var t in types) 
                { 
                    foreach(var o in t.GetMembers().Where(c => c.Name == "RegisterRoutes"))
                    {
                        try
                        {
                            t.InvokeMember(o.Name,
                               BindingFlags.Default | BindingFlags.InvokeMethod,
                               null, null, new object[] { routes });

                            //System.Diagnostics.Debug.WriteLine("Register: " + o.Name + " from " + t.Name + ", " + t.Namespace);
                        }
                        catch (Exception e)
                        {
                            throw new Exception("error invoking " + o.Name, e);
                        }
                    }
                }
            }
        }
        public static void RegisterReceivers(RouteCollection routes, string @namespace)
        {
            var list = AppDomain.CurrentDomain.GetAssemblies();
            var x = list.Where(t => 
                t.FullName.StartsWith(@namespace) ||
                t.FullName.Contains("ReceiverApi"));

            foreach (var y in x)
            {
                var types = from d in y.GetTypes()
                            where d.IsClass && d.Namespace != null && d.Namespace.StartsWith(@namespace) && d.Name == "ProviderRegistration"
                            select d;

                foreach (var t in types)
                {
                    foreach (var o in t.GetMembers().Where(c => c.Name == "RegisterRoutes"))
                    {
                        try
                        {
                            t.InvokeMember(o.Name,
                               BindingFlags.Default | BindingFlags.InvokeMethod,
                               null, null, new object[] { routes });

                            //System.Diagnostics.Debug.WriteLine("Register: " + o.Name + " from " + t.Name + ", " + t.Namespace);
                        }
                        catch (Exception e)
                        {
                            throw new Exception("error invoking " + o.Name, e);
                        }
                    }
                }
            }
        }

        public static void RegisterRoutes(RouteCollection routes)
        {
            //const string myNamespace = "Extric.Towbook.API.Controllers";

            //routes.IgnoreRoute("{resource}.axd/{*pathInfo}");

            //var constraintsResolver = new System.Web.Mvc.Routing.DefaultInlineConstraintResolver();
            //constraintsResolver.ConstraintMap.Add("GuidConstraint", typeof(Extric.Towbook.API.Integration.MotorClubs.Issc.GuidConstraint));
            //routes.MapMvcAttributeRoutes(constraintsResolver);

            // Register Provider assembly routes for providers such as quickbooks, fleetmatics, etc. 
            RegisterProviderRoutes(routes);

            // Register receivers for motor clubs and others (agero, geico, etc)
            RegisterReceivers(routes, "Extric.Towbook.API.Integration.MotorClubs");
            RegisterReceivers(routes, "Extric.Towbook.API.Integration.Gateways");

            #region Attribute selection options (custom fields)
            //routes.MapHttpRoute(
            //    name: "AttributeSelectionOptionDefault",
            //    routeTemplate: "attributes/{attributeId}/selectionOptions/{id}",
            //    defaults: new { controller = "AttributeSelectionOptions", id = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
            #endregion

            #region Admin routes

            //routes.MapHttpRoute(
            //    name: "Admin_Statistics_Apis",
            //    routeTemplate: "admin/statistics/{controller}")
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Controllers.Admin.Statistics" } } };


            //routes.MapHttpRoute(
            //    name: "Admin_Api_Post",
            //    routeTemplate: "admin/{controller}/{action}")
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Admin.Controllers" } } };

            //routes.MapHttpRoute(
            //    name: "Admin_Api_Get",
            //    routeTemplate: "admin/{controller}/{action}")
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Admin.Controllers" } } };

            //routes.MapHttpRoute(
            //    name: "Admin_Companies_Api_withId",
            //    routeTemplate: "admin/companies/{companyId}/{controller}/{id}")
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Admin.Controllers.Companies" } } };

            //routes.MapHttpRoute(
            //    name: "Admin_Companies_Api",
            //    routeTemplate: "admin/companies/{companyId}/{controller}")
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Admin.Controllers.Companies" } } };

            #endregion

            #region Integration Route Definitions (integration/controller/action/id [GET, POST, DELETE] 
            //routes.MapHttpRoute(
            //     name: "IntegrationCompanyKeyValue",
            //     routeTemplate: "integration/{controller}/firstkeyvalue/{providerId}/{keyName}",
            //     defaults: new { controller = "Company", action = "FirstKeyValue" },
            //     constraints: new
            //     {
            //         httpMethod = new HttpMethodConstraint(new string[] { "GET" })
            //     }
            // ).DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Integration" } } };

            //routes.MapHttpRoute(
            //    name: "Integration",
            //    routeTemplate: "integration/{controller}/{action}/{id}",
            //    defaults: new { action = "Get", id = RouteParameter.Optional },
            //    constraints: new
            //    {
            //        httpMethod = new HttpMethodConstraint(new string[] { "GET" }),
            //        managersOnly = new ApiPermissionConstraint(User.TypeEnum.Manager)
            //    }
            //    ).DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Integration" } } };
            //routes.MapHttpRoute(
            //  name: "IntegrationUserKeyValue",
            //  routeTemplate: "integration/{controller}/keyvalue/{keyName}",
            //  defaults: new { controller = "User", action = "KeyValue" },
            //  constraints: new
            //  {
            //      httpMethod = new HttpMethodConstraint(new string[] { "GET" })
            //  }
            //  ).DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Integration" } } };

            //routes.MapHttpRoute(
            //    name: "IntegrationNoActionGet",
            //    routeTemplate: "integration/{controller}/{id}",
            //    defaults: new { action = "Get", id = RouteParameter.Optional },
            //    constraints: new
            //    {
            //        httpMethod = new HttpMethodConstraint(new string[] { "GET" }),
            //        managersOnly = new ApiPermissionConstraint(User.TypeEnum.Manager)
            //    }
            // ).DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Integration" } } };    

            //routes.MapHttpRoute(
            //    name: "Integration",
            //    routeTemplate: "integration/{controller}/{action}/{id}",
            //    defaults: new { action = "Get", id = RouteParameter.Optional},
            //    constraints: new
            //    {
            //        httpMethod = new HttpMethodConstraint(new string[] { "GET" }),
            //managersOnly = new ApiPermissionConstraint(User.TypeEnum.Manager)
            //    }
            //    ).DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Integration" } } };

            //routes.MapHttpRoute(
            //  name: "Integration_Post",
            //  routeTemplate: "integration/{controller}/{action}/{id}",
            //  defaults: new { action = "Post", id = RouteParameter.Optional },
            //  constraints: new
            //  {
            //      httpMethod = new HttpMethodConstraint(new string[] { "POST" }),
            //      managersOnly = new ApiPermissionConstraint(User.TypeEnum.Manager)
            //  }).DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Integration" } } };

            //routes.MapHttpRoute(
            //  name: "Integration_Delete",
            //  routeTemplate: "integration/{controller}/{action}/{id}",
            //  defaults: new { action = "Delete", id = RouteParameter.Optional },
            //  constraints: new
            //  {
            //      httpMethod = new HttpMethodConstraint(new string[] { "DELETE" }),
            //      managersOnly = new ApiPermissionConstraint(User.TypeEnum.Manager)
            //  }).DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Integration" } } }; 

            #endregion

            #region Impound Search Definition (impounds/search) 

            //routes.MapHttpRoute(
            //    name: "ImpoundSearch",
            //    routeTemplate: "impounds/search",
            //    defaults: new { controller = "Impounds", action = "Search" })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
            #endregion

            #region Impound Generate Report (impounds/report) 

            //routes.MapHttpRoute(
            //    name: "GenerateReport",
            //    routeTemplate: "impounds/generateReport",
            //    defaults: new { controller = "Impounds", action = "generateReport" })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };

            //routes.MapHttpRoute(
            //    name: "DeleteImpoundReport",
            //    routeTemplate: "impounds/deleteReport",
            //    defaults: new { controller = "Impounds", action = "deleteReport",  },
            //        constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "DELETE" }) })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
            #endregion

            #region Digital Dispatch Status

            //routes.MapHttpRoute(
            //    name: "DDStatus",
            //    routeTemplate: "accounts/digitalstatus",
            //    defaults: new { controller = "Accounts", action = "DigitalStatus" })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };

            #endregion

            #region Impound Property Release (impounds/{impoundId}/propertyReleases)
            //routes.MapHttpRoute(
            //    name: "ImpoundPropertyRelease",
            //    routeTemplate: "impounds/{impoundId}/propertyReleases/",
            //    defaults: new { controller = "PropertyReleases", action = new string[] { "POST", "GET" } },
            //    constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "POST", "GET" }) })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Impounds.Controllers" } } };

            //routes.MapHttpRoute(
            //    name: "ImpoundPropertyReleaseActions",
            //    routeTemplate: "impounds/{impoundId}/propertyReleases/{formId}/{action}",
            //    defaults: new { controller = "PropertyReleases", action = new string[] {  "Complete", "Email", "Get", "Pdf", "Photos" } },
            //    constraints: new { httpMethod = new HttpMethodConstraint(new string[] {  "GET", "POST" }) })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Impounds.Controllers" } } };
            #endregion


            #region Impound Calculate Days Held (impound/[id]/calculate)

            //routes.MapHttpRoute(
            //    name: "CalculateById",
            //    routeTemplate: "impounds/{id}/calculate",
            //    defaults: new { controller = "Impounds", action = "CalculateDaysHeldBillable", id = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };

            //routes.MapHttpRoute(
            //    name: "Calculate",
            //    routeTemplate: "impounds/calculate",
            //    defaults: new { controller = "Impounds", action = "CalculateDaysHeldBillable" })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
            #endregion

            #region Impound Tasks (impounds/{impoundId}/tasks/{id}/{action})
            //routes.MapHttpRoute(
            //    name: "ImpoundTasks",
            //    routeTemplate: "impounds/{impoundId}/tasks/{id}",
            //    defaults: new { controller = "Tasks", id = RouteParameter.Optional, action = "Get" })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Impounds.Controllers" } } };

            //routes.MapHttpRoute(
            //    name: "ImpoundTasksPost",
            //    routeTemplate: "impounds/{impoundId}/tasks/",
            //    defaults: new { controller = "Tasks", action = "Post" },
            //    constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "POST" }) })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Impounds.Controllers" } } };

            //routes.MapHttpRoute(
            //    name: "ImpoundTasksPut",
            //    routeTemplate: "impounds/{impoundId}/tasks/{id}",
            //    defaults: new { controller = "Tasks", action = "Put" },
            //    constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "PUT" }) })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Impounds.Controllers" } } };

            //routes.MapHttpRoute(
            //    name: "ImpoundTasksDelete",
            //    routeTemplate: "impounds/{impoundId}/tasks/{id}",
            //    defaults: new { controller = "Tasks", action = "Delete"},
            //    constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "DELETE" }) })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Impounds.Controllers" } } };

            //routes.MapHttpRoute(
            //    name: "ImpoundTasksActions",
            //    routeTemplate: "impounds/{impoundId}/tasks/{id}/{action}",
            //    defaults: new { controller = "Tasks" },
            //    constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "GET", "POST" }) })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Impounds.Controllers" } } };
            #endregion

            #region Hide Impound Letters (letterTemplates/HideImpoundLetters)
            //routes.MapHttpRoute(
            //    name: "HideImpoundLetters",
            //    routeTemplate: "letterTemplates/HideImpoundLetters",
            //    defaults: new { controller = "LetterTemplates" },
            //    constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "POST" }) })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Impounds.Controllers" } } };
            #endregion

            #region Impound Letter Templates (impounds/{impoundId}/letters/{id})
            //routes.MapHttpRoute(
            //    name: "ImpoundLetterTemplates",
            //    routeTemplate: "impounds/{impoundId}/letters/{id}",
            //    defaults: new { controller = "LetterTemplates", id = RouteParameter.Optional, action = "Get" },
            //    constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "GET" }) })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Impounds.Controllers" } } };
            #endregion

            #region Impound Notes (impounds/{impoundId}/notes/{id})
            //routes.MapHttpRoute(
            //    name: "ImpoundNotes",
            //    routeTemplate: "impounds/{impoundId}/notes/{id}",
            //    defaults: new { controller = "Notes", id = RouteParameter.Optional },
            //    constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "GET", "POST", "PUT", "DELETE" }) })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Impounds.Controllers" } } };
            #endregion

            #region Impound Files (impounds/{impoundId}/files/{id})
            //routes.MapHttpRoute(
            //    name: "ImpoundFiles",
            //    routeTemplate: "impounds/{impoundId}/files/{id}",
            //    defaults: new { controller = "Files", id = RouteParameter.Optional },
            //    constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "GET", "POST", "PUT", "DELETE" }) })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Impounds.Controllers" } } };
            #endregion

            #region Impound History (impounds/{impoundId}/history/{id})
            //routes.MapHttpRoute(
            //    name: "ImpoundHistoryGet",
            //    routeTemplate: "impounds/{impoundId}/history/{id}",
            //    defaults: new { controller = "History", id = RouteParameter.Optional, action = "GET" })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Impounds.Controllers" } } };

            //routes.MapHttpRoute(
            //    name: "ImpoundHistoryDefaults",
            //    routeTemplate: "impounds/{impoundId}/history/{id}",
            //    defaults: new { controller = "History" },
            //    constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "POST", "DELETE" }) })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Impounds.Controllers" } } };
            #endregion

            #region Impound Calculate Storage Rate Amount (impound/storage)

            //routes.MapHttpRoute(
            //    name: "Storage",
            //    routeTemplate: "impounds/storage",
            //    defaults: new { controller = "Impounds", action = "CalculateStorageRateAmount" })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
            #endregion

            #region Impound Batch (impounds/batch/action)
            //routes.MapHttpRoute(
            //    name: "ImpoundsBatch",
            //    routeTemplate: "impounds/batch/{action}",
            //    defaults: new { controller = "Batch" })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Impounds.Controllers" } } };
            #endregion

            #region Impound Layouts (impounds/layouts/{action})
            //routes.MapHttpRoute(
            //    name: "ImpoundLayouts",
            //    routeTemplate: "impounds/layouts/{action}/{id}",
            //    defaults: new { controller = "Layouts", id = RouteParameter.Optional, action = "Get" },
            //    constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "GET", "POST", "DELETE" }) })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Impounds.Controllers" } } };
            #endregion

            #region Impound Layouts (impounds/reports/{action})
            //routes.MapHttpRoute(
            //    name: "ImpoundReportManagement",
            //    routeTemplate: "impounds/reports/{action}",
            //    defaults: new { controller = "Reports", action = "Get" },
            //    constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "DELETE", "POST", "GET" }) })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Impounds.Controllers" } } };
            #endregion

            #region Impound Release Reasons (impounds/releasereasons/{id}/{action})
            //routes.MapHttpRoute(
            //    name: "ImpoundReleaseReasons",
            //    routeTemplate: "impounds/releasereasons/{id}/{action}",
            //    defaults: new { controller = "ReleaseReasons", id = RouteParameter.Optional, action = "Get" },
            //    constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "GET" }) })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Impounds.Controllers" } } };

            //routes.MapHttpRoute(
            //    name: "ImpoundReleaseReasonsByImpound",
            //    routeTemplate: "impounds/{id}/releasereasons/{action}",
            //    defaults: new { controller = "ReleaseReasons", action = "GetByImpound" },
            //    constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "GET" }) })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Impounds.Controllers" } } };
            #endregion

            #region Auctions
            //routes.MapHttpRoute(
            //    name: "AuctionItemPhotos",
            //    routeTemplate: "auctionItems/{itemId}/photos/{id}/{action}",
            //    defaults: new { controller = "AuctionItemPhotos", id = RouteParameter.Optional, action = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Auctions.Controllers" } } };

            //routes.MapHttpRoute(
            //    name: "AuctionItems",
            //    routeTemplate: "auctions/{auctionId}/items/{id}/{action}",
            //    defaults: new { controller = "AuctionItems", id = RouteParameter.Optional, action = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Auctions.Controllers" } } };

            //routes.MapHttpRoute(
            //    name: "Auction_default_POST",
            //    routeTemplate: "auctions/{action}",
            //    defaults: new { controller = "Auctions", id = RouteParameter.Optional, action = "Post" },
            //        constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "POST" }) })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Auctions.Controllers" } } };

            //routes.MapHttpRoute(
            //    name: "Auctions_Default_actions",
            //    routeTemplate: "auctions/{id}/{action}",
            //    defaults: new { controller = "Auctions", id = RouteParameter.Optional, action = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Auctions.Controllers" } } };

            //routes.MapHttpRoute(
            //    name: "AuctionItems_defaults",
            //    routeTemplate: "auctionItems/{id}",
            //    defaults: new { controller = "auctionItems", id = RouteParameter.Optional },
            //        constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "GET", "PUT", "DELETE", "POST" }) })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Auctions.Controllers" } } };
            #endregion


            #region Remote Auctions
            //routes.MapHttpRoute(
            //    name: "RemoteAuctions_default_POST",
            //    routeTemplate: "remoteauctions/{action}",
            //    defaults: new { controller = "RemoteAuctions", id = RouteParameter.Optional, action = "Post" },
            //        constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "POST" }) })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Auctions.Controllers" } } };

            //routes.MapHttpRoute(
            //    name: "RemoteAuctions_Default_actions",
            //    routeTemplate: "remoteauctions/{id}/{action}",
            //    defaults: new { controller = "RemoteAuctions", id = RouteParameter.Optional, action = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Auctions.Controllers" } } };

            #endregion

            #region API Documentation (docs/controller/action/id)
            //routes.MapRoute(
            //    "ApiDocumentation", // Route name
            //    "docs/{controller}/{action}/{id}",
            //    new { controller = "Home", action = "Index", id = UrlParameter.Optional } )
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
            #endregion

            #region quotes (calls/quotes)
            //routes.MapHttpRoute(
            //    name: "QuoteEmailHistory",
            //    routeTemplate: "quotes/{quoteId}/emailHistory",
            //    defaults: new { controller = "EmailHistory" })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Quotes.Controllers" } } };

            //routes.MapHttpRoute(
            //    name: "Quote_Duplicate_GET",
            //    routeTemplate: "quotes/{id}/duplicate",
            //    defaults: new { controller = "Quotes", action = "DuplicateQuote" },
            //        constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "GET" }) })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Quotes.Controllers" } } };

            //routes.MapHttpRoute(
            //    name: "Quote_GET",
            //    routeTemplate: "quotes/{id}",
            //    defaults: new { controller = "Quotes", action = "Get" },
            //        constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "GET" }) })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Quotes.Controllers" } } };

            //routes.MapHttpRoute(
            //    name: "Quotes Default actions",
            //    routeTemplate: "quotes/{id}/{action}",
            //    defaults: new { controller = "Quotes", id = RouteParameter.Optional, action = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Quotes.Controllers" } } };
            #endregion

            #region Dispatch Batch (calls/batch/action)
            //routes.MapHttpRoute(
            //name: "Batch",
            //routeTemplate: "calls/batch/{action}",
            //defaults: new { controller = "Batch" })
            //.DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
            #endregion

            #region Driver Replay
            //routes.MapHttpRoute(
            //    name: "DriverReplay",
            //    routeTemplate: "calls/{callId}/driverReplay/{driverId}",
            //    defaults: new { controller = "DriverReplay", id = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
            #endregion

            #region Digital Dispatch History

            //routes.MapHttpRoute(
            //    name: "CallRequestsHistory",
            //    routeTemplate: "callrequests/history/{id}",
            //    defaults: new { controller = "CallRequestsHistory", id = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
            #endregion


            #region impound release validation rule sets(impounds/releaseValidationRuleSets) 
            //routes.MapHttpRoute(
            //    name: "ReleaseValidationRuleSetsDefault",
            //    routeTemplate: "impounds/releaseValidationRuleSets",
            //    defaults: new { controller = "ReleaseValidationRuleSets", statusId = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Impounds.Controllers" } } };
            #endregion


            #region Dispatch Undelete Call (calls/undeleteByCallNumber)
            //routes.MapHttpRoute(
            //    name: "Calls_Undelete",
            //    routeTemplate: "calls/undeleteByCallNumber",
            //    defaults: new { controller = "Calls", action = "UndeleteByCallNumber" })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
            #endregion

            #region Dispatch Call Photos (calls/callId/photos/id) 
            //routes.MapHttpRoute(
            //    name: "CallPhotos",
            //    routeTemplate: "calls/{callId}/photos/{id}",
            //    defaults: new { controller = "Photos", id = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };

            //routes.MapHttpRoute(
            //        name: "CallPhotoActions",
            //        routeTemplate: "calls/{callId}/photos/{id}/{action}",
            //        defaults: new { controller = "PhotoActions", id = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
            #endregion

            #region Dispatch Call Media (calls/calId/media)
            //routes.MapHttpRoute(
            //    name: "CallMedia",
            //    routeTemplate: "calls/{callId}/media/{action}",
            //    defaults: new { controller = "Media",  action = "Download" })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
            #endregion


            #region Dispatch Call Videos (calls/callId/photos/id) 
            //routes.MapHttpRoute(
            //    name: "CallVideos",
            //    routeTemplate: "calls/{callId}/videos/{id}",
            //    defaults: new { controller = "Videos", id = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
            #endregion

            #region call disclaimer (calls/{callId}/disclaimer) 
            //routes.MapHttpRoute(
            //    name: "CallDisclaimer",
            //    routeTemplate: "calls/{callId}/disclaimer",
            //    defaults: new { controller = "Disclaimer" })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
            #endregion

            #region call disclaimer (calls/{callId}/duplicate) 
            //routes.MapHttpRoute(
            //    name: "CallDuplicate",
            //    routeTemplate: "calls/{callId}/duplicate",
            //    defaults: new { controller = "Calls", action = "Duplicate" })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
            #endregion


            #region CommissionDriverReportOptions (commissionDriverReportOptions)
            //routes.MapHttpRoute(
            //    name: "CommissionDriverReportOptionsPut",
            //    routeTemplate: "commissionDriverReportOptions/{id}",
            //    defaults: new { controller = "CommissionDriverReportOptions", action = "Put" },
            //    constraints: new { httpMethod = new HttpMethodConstraint(new [] { "PUT" }) })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new[] { myNamespace } } };

            //routes.MapHttpRoute(
            //    name: "CommissionDriverReportOptionsPost",
            //    routeTemplate: "commissionDriverReportOptions/",
            //    defaults: new { controller = "CommissionDriverReportOptions", action = "Post" },
            //    constraints: new { httpMethod = new HttpMethodConstraint(new [] { "POST" }) })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new[] { myNamespace } } };
            #endregion

            #region Reports
            //routes.MapHttpRoute(
            //    name: "ReportyHistoryGet",
            //    routeTemplate: "reports/history",
            //    defaults: new { controller = "Reports", action = "History" },
            //    constraints: new { httpMethod = new HttpMethodConstraint(new [] { "GET" }) })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new[] { myNamespace } } };
            #endregion

            #region Dispatch Roadside Contacts (call/callId/roadside)
            //routes.MapHttpRoute(
            //    name: "RoadsideContacts",
            //    routeTemplate: "calls/{callId}/roadsideUsers/{id}",
            //    defaults: new { controller = "Roadside", id = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };

            //routes.MapHttpRoute(
            //    name: "RoadsideContact",
            //    routeTemplate: "calls/{callId}/roadside/{id}/users/{userId}",
            //    defaults: new { controller = "Roadside", id = RouteParameter.Optional, userId = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
            #endregion

            #region Dispatch Deleted Calls (calls/deleted/id/action) 
            //routes.MapHttpRoute(
            //    name: "CallsDeleted",
            //    routeTemplate: "calls/deleted/{callId}",
            //    defaults: new { controller = "DeletedCalls", callId = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
            #endregion


            #region Dispatch Internal Notes (calls/callId/notes/id)
            //routes.MapHttpRoute(
            //    name: "InternalNotes",
            //    routeTemplate: "calls/{callId}/notes/{id}",
            //    defaults: new { controller = "Notes", id = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
            #endregion

            #region Dispatch Call Signatures (calls/callId/signatures/id) 
            //routes.MapHttpRoute(
            //    name: "CallSignatures",
            //    routeTemplate: "calls/{callId}/signatures/{id}",
            //    defaults: new { controller = "Signatures", id = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
            #endregion

            #region User 
            // Signature (user/signature/id) 
            //routes.MapHttpRoute(
            //    name: "UserSignatureDefault",
            //    routeTemplate: "user/signature/{id}",
            //    defaults: new { controller = "UserSignature", id = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };

            // Signature Agreements (user/signature/{signatureId}/agreements/{id}) 
            //routes.MapHttpRoute(
            //    name: "UserSignatureAgreementsDefault",
            //    routeTemplate: "user/signature/{signatureId}/agreements/{id}",
            //    defaults: new { controller = "UserSignatureAgreements", id = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };

            //routes.MapHttpRoute(
            //        name: "UserChangeCompany",
            //        routeTemplate: "user/changeCompany",
            //        defaults: new { controller = "User", actions = "Put" },
            //        constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "PUT" }) })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new[] { myNamespace } } };
            //=======
            //routes.MapHttpRoute(
            // name: "UserNameValidations",
            // routeTemplate: "user/{action}/{userName}",
            // defaults: new { controller = "User", id = RouteParameter.Optional })
            // .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };

            //routes.MapHttpRoute(
            //        name: "UserChangeCompany",
            //        routeTemplate: "user/changeCompany",
            //        defaults: new { controller = "User", actions = "Put" },
            //        constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "PUT" }) })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new[] { myNamespace } } };

            //routes.MapHttpRoute(
            //       name: "UserHistory",
            //       routeTemplate: "users/history/{id}",
            //       defaults: new { controller = "Users", action = "History", id = RouteParameter.Optional })
            //   .DataTokens = new RouteValueDictionary { { "Namespaces", new[] { myNamespace } } };

            //routes.MapHttpRoute(
            //        name: "UserFiles",
            //        routeTemplate: "users/{userId}/files/{id}",
            //        defaults: new { controller = "Files", actions = "POST", id = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new[] { "Extric.Towbook.API.Controllers.Users" } } };

            //routes.MapHttpRoute(
            //        name: "UserProfilePicUpload",
            //        routeTemplate: "users/{userId}/photo",
            //        defaults: new { controller = "Users", action = "Photo" })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new[] { "Extric.Towbook.API.Controllers" } } };

            //routes.MapHttpRoute(
            //        name: "UserProfileImageGet",
            //        routeTemplate: "users/{userId}/profileImage",
            //        defaults: new { controller = "Users", action = "ProfileImage" })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new[] { "Extric.Towbook.API.Controllers" } } };

            //routes.MapHttpRoute(
            //        name: "UserProfileImageDelete",
            //        routeTemplate: "users/{userId}/deleteprofileimage",
            //        defaults: new { controller = "Users", action = "DeleteProfileImage" })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new[] { "Extric.Towbook.API.Controllers" } } };

            // Signature (user/tempToken) 
            //routes.MapHttpRoute(
            //    name: "UserTempTokenDefault",
            //    routeTemplate: "user/tempToken",
            //    defaults: new { controller = "UserTempToken" },
            //        constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "POST" }) })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
            #endregion

            #region Dispatch Call Payments (calls/callId/payments/id)
            //routes.MapHttpRoute(
            //    name: "CallPayments",
            //    routeTemplate: "calls/{callId}/payments/{paymentId}",
            //    defaults: new { paymentId = RouteParameter.Optional, controller = "Payments" },
            //    constraints: new { paymentId = @"\d*" })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };

            //routes.MapHttpRoute(
            //    name: "CallPaymentActions",
            //    routeTemplate: "calls/{callId}/payments/{action}",
            //    defaults: new { controller = "PaymentActions" })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };

            //routes.MapHttpRoute(
            //        name: "CallPaymentActionsAlt",
            //        routeTemplate: "calls/{callId}/payments/{paymentId}/{action}",
            //        defaults: new { controller = "PaymentActions" })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };

            #endregion

            #region Dispatch Email History (calls/callId/emailHistory)
            //routes.MapHttpRoute(
            //    name: "CallEmailHistory",
            //    routeTemplate: "calls/{callId}/emailHistory",
            //    defaults: new { controller = "EmailHistory", action = "Get" })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
            #endregion

            #region Dispatch Call Payments (calls/callId/activityLog/id)
            //routes.MapHttpRoute(
            //    name: "CallActivityLog",
            //    routeTemplate: "calls/{callId}/activityLog/{id}",
            //    defaults: new { controller = "ActivityLog", id = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
            #endregion


            #region Dispatch Call Damages (calls/callId/damages/id) 
            //routes.MapHttpRoute(
            //    name: "CallDamages",
            //    routeTemplate: "calls/{callId}/damageForms/{id}",
            //    defaults: new { controller = "DamageForms", id = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };

            //routes.MapHttpRoute(
            //    name: "CallDamageFormPhotos",
            //    routeTemplate: "calls/{callId}/damageForms/{damageFormId}/photos/{photoId}",
            //    defaults: new { controller = "DamageFormPhotos", photoId = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };

            //routes.MapHttpRoute(
            //    name: "CallDamageFormDamagePhotos",
            //    routeTemplate: "calls/{callId}/damageForms/{damageFormId}/damages/{damageId}/photos/{photoId}",
            //    defaults: new { controller = "DamageFormDamagePhotos", photoId = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };

            //routes.MapHttpRoute(
            //    name: "CallDamageFormVideos",
            //    routeTemplate: "calls/{callId}/damageForms/{damageId}/videos/{videoId}",
            //    defaults: new { controller = "DamageFormVideos", videoId = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };

            //routes.MapHttpRoute(
            //    name: "CallDamageFormDamageVideos",
            //    routeTemplate: "calls/{callId}/damageForms/{damageId}/damages/{regionId}/videos/{videoId}",
            //    defaults: new { controller = "DamageFormVideos", videoId = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
            #endregion

            #region Dispatch Call Damages Signature (calls/callId/damages/id) 
            //routes.MapHttpRoute(
            //    name: "CallDamageForms.signature",
            //    routeTemplate: "calls/{callId}/damageForms/{damageFormId}/signature",
            //    defaults: new { controller = "DamageFormSignature" })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
            #endregion

            #region Dispatch Call Damages Email (calls/callId/damages/id) 
            //routes.MapHttpRoute(
            //    name: "CallDamagesEmail",
            //    routeTemplate: "calls/{callId}/damages/{damageReportId}/email",
            //    defaults: new { controller = "DamageEmail" })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
            #endregion

            #region Reviews
            //routes.MapHttpRoute(
            //    name: "Reviews_Stats_default",
            //    routeTemplate: "reviews/stats/{Id}",
            //    defaults: new { controller = "Stats", id = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Reviews.Controllers" } } };

            //routes.MapHttpRoute(
            //    name: "Reviews_deleteByAdmin",
            //    routeTemplate: "reviews/{Id}",
            //    defaults: new { controller = "Reviews", id = RouteParameter.Optional, action = "delete" },
            //        constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "DELETE" }) })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Controllers" } } };

            //routes.MapHttpRoute(
            //    name: "Reviews_default",
            //    routeTemplate: "reviews/{Id}/{action}",
            //    defaults: new { controller = "Reviews" })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Controllers" } } };
            #endregion

            #region Accident Reports Email/Photo (calls/callId/accidentReports/id) 
            //routes.MapHttpRoute(
            //    name: "AccidentReportsEmail",
            //    routeTemplate: "calls/{callId}/accidentReports/{reportId}/email",
            //    defaults: new { controller = "AccidentReportsEmail" })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };

            //routes.MapHttpRoute(
            //    name: "AccidentReportsPhoto",
            //    routeTemplate: "calls/{callId}/accidentReports/{reportId}/photos/{photoId}",
            //    defaults: new { controller = "AccidentReportPhotos", photoId = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
            #endregion

            #region Impound Photos (impounds/impoundId/photos/id)
            //routes.MapHttpRoute(
            //    name: "ImpoundPhotos",
            //    routeTemplate: "impounds/{impoundId}/photos/{id}",
            //    defaults: new { controller = "Photos", id = RouteParameter.Optional })
            //   .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Impounds.Controllers" } } };
            #endregion

            #region Account Statements (accounts/accountId/statements)
            //routes.MapHttpRoute(
            //	name: "AccountStatements",
            //	routeTemplate: "accounts/{accountId}/statements/{statementId}",
            //	defaults: new { controller = "Statements", statementId = RouteParameter.Optional })
            //	.DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
            #endregion

            #region Account Digital Connections (accounts/accountId/locations/referenceId)
            //routes.MapHttpRoute(
            //    name: "DDMoveConnection",
            //    routeTemplate: "accounts/{id}/locations/{locationId}",
            //    defaults: new { controller = "Accounts", action = "LocationsPut" },
            //        constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "PUT" }) })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
            #endregion

            #region Account Parking Permits (accounts/accountId/parkingPermits/id)
            //routes.MapHttpRoute(
            //    name: "AccountParkingPermitsSearch",
            //    routeTemplate: "accounts/{accountId}/parkingpermits/search",
            //    defaults: new { controller = "ParkingPermits", action = "Search" })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Accounts.Controllers" } } };

            //routes.MapHttpRoute(
            //    name: "AccountParkingPermitsFind",
            //    routeTemplate: "accounts/{accountId}/parkingpermits/find",
            //    defaults: new { controller = "ParkingPermits", action = "Find" })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Accounts.Controllers" } } };

            //routes.MapHttpRoute(
            //    name: "AccountParkingPermitPhotos",
            //    routeTemplate: "accounts/{accountId}/parkingPermits/{id}/photos",
            //    defaults: new { controller = "ParkingPermitPhotos", id = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Accounts.Controllers" } } };

            //routes.MapHttpRoute(
            //    name: "AccountParkingPermitHistory",
            //    routeTemplate: "accounts/{accountId}/parkingPermits/{id}/history",
            //    defaults: new { controller = "ParkingPermits", id = RouteParameter.Optional, action = "History" })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Accounts.Controllers" } } };

            //routes.MapHttpRoute(
            //    name: "AccountParkingPermitPhotos_Photo",
            //    routeTemplate: "accounts/{accountId}/parkingPermits/{permitId}/photos/{id}",
            //    defaults: new { controller = "ParkingPermitPhotos", id = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Accounts.Controllers" } } };

            //routes.MapHttpRoute(
            //    name: "AccountParkingPermits_POST",
            //    routeTemplate: "accounts/{accountId}/parkingPermits/{id}/{action}",
            //    defaults: new { controller = "ParkingPermits", id = RouteParameter.Optional, action = "Post" },
            //        constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "POST" }) })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Accounts.Controllers" } } };

            //routes.MapHttpRoute(
            //    name: "AccountParkingPermits_PUT",
            //    routeTemplate: "accounts/{accountId}/parkingPermits/{id}",
            //    defaults: new { controller = "ParkingPermits", id = RouteParameter.Optional, action = "Put" },
            //        constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "PUT" }) })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Accounts.Controllers" } } };

            //routes.MapHttpRoute(
            //    name: "AccountParkingPermits_DELETE",
            //    routeTemplate: "accounts/{accountId}/parkingPermits/{id}",
            //    defaults: new { controller = "ParkingPermits", id = RouteParameter.Optional, action = "Delete" },
            //        constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "Delete" }) })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Accounts.Controllers" } } };

            //routes.MapHttpRoute(
            //    name: "AccountParkingPermits_actions",
            //    routeTemplate: "accounts/{accountId}/parkingPermits/{id}/{action}",
            //    defaults: new { controller = "ParkingPermits", id = RouteParameter.Optional, action = "Get" },
            //        constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "GET" }) })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Accounts.Controllers" } } };

            //routes.MapHttpRoute(
            //    name: "AccountParkingPermitFees",
            //    routeTemplate: "accounts/{accountId}/parkingPermitFees/{id}",
            //    defaults: new { controller = "ParkingPermitFees", id = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Accounts.Controllers" } } };

            //routes.MapHttpRoute(
            //    name: "AccountParkingPermitPublicLinks",
            //    routeTemplate: "accounts/{accountId}/parkingPermitPublicLinks/{id}",
            //    defaults: new { controller = "ParkingPermitPublicLinks", id = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Accounts.Controllers" } } };
            #endregion

            #region Account Parking Permit Requests (accounts/accountId/parkingPermitRequests/id)
            //routes.MapHttpRoute(
            //    name: "AccountParkingPermitRequests",
            //    routeTemplate: "accounts/{accountId}/parkingPermitRequests/{id}",
            //    defaults: new { controller = "ParkingPermitRequests", id = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Accounts.Controllers" } } };

            //routes.MapHttpRoute(
            //    name: "AccountParkingPermitRequests_actions",
            //    routeTemplate: "accounts/{accountId}/parkingPermitRequests/{id}/{action}",
            //    defaults: new { controller = "ParkingPermitRequests" })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Accounts.Controllers" } } };
            #endregion

            #region Account Parking Permit Approval Sessions (accounts/accountId/parkingPermitApprovalSessions/id)
            //routes.MapHttpRoute(
            //    name: "AccountParkingPermitApprovalSessions",
            //    routeTemplate: "accounts/{accountId}/parkingPermitApprovalSessions/{id}",
            //    defaults: new { controller = "ParkingPermitApprovalSessions", id = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Accounts.Controllers" } } };
            #endregion

            #region Account Payments (accounts/accountId/payments/id)
            //routes.MapHttpRoute(
            //    name: "AccountPayments",
            //    routeTemplate: "accounts/{accountId}/payments/{id}/{action}",
            //    defaults: new { controller = "Payments", id = RouteParameter.Optional, action = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Accounts.Controllers" } } };
            #endregion


            #region Account Statements (accounts/accountId/photos)
            //routes.MapHttpRoute(
            //    name: "AccountPhotos",
            //    routeTemplate: "accounts/{accountId}/photos/{photoId}",
            //    defaults: new { controller = "AccountPhotos", photoId = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Accounts.Controllers" } } };
            #endregion

            #region Account Statements (accounts/accountId/file)
            //routes.MapHttpRoute(
            //    name: "AccountFileUpload",
            //    routeTemplate: "accounts/{accountId}/file",
            //    defaults: new { controller = "Accounts", id = RouteParameter.Optional, action = "File" })
            //        .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Controllers" } } };
            #endregion

            #region Account Statements Options (accounts/accountId/options)
            //routes.MapHttpRoute(
            //    name: "AccountStatementOptions",
            //    routeTemplate: "accounts/{accountId}/statementOptions/{statementOptionsId}",
            //    defaults: new { controller = "StatementOptions", statementOptionsId = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
            #endregion

            #region Account Notes
            //routes.MapHttpRoute(
            //  name: "Account_Note_Delete",
            //  routeTemplate: "accounts/note/{id}",
            //  defaults: new { controller = "Accounts", action = "Note" },
            //  constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "DELETE" }) })
            //  .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
            #endregion

            #region AccountPublicRequestFormTemplates
            //routes.MapHttpRoute(
            //  name: "Account_PublicRequestForm_Templates_Defaults",
            //  routeTemplate: "accountpublicrequestformtemplates/{id}/{action}",
            //  defaults: new { controller = "AccountPublicRequestFormTemplates", id = RouteParameter.Optional })
            //  .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
            #endregion

            #region Stickering
            //routes.MapHttpRoute(
            //    name: "Stickering_Stickers",
            //    routeTemplate: "stickering/stickers/{id}",
            //    defaults: new { controller = "Stickers", id = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Stickering.Controllers" } } };

            //routes.MapHttpRoute(
            //    name: "Stickering_Stickers_CreateCall",
            //    routeTemplate: "stickering/stickers/{id}/createCall",
            //    defaults: new { controller = "Stickers", Action = "CreateCall" })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Stickering.Controllers" } } };

            //routes.MapHttpRoute(
            //    name: "Stickering_Requests",
            //    routeTemplate: "stickering/requests/{id}",
            //    defaults: new { controller = "Requests", id = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Stickering.Controllers" } } };

            //routes.MapHttpRoute(
            //    name: "Stickering_Reasons",
            //    routeTemplate: "stickering/reasons/{id}",
            //    defaults: new { controller = "Reasons", id = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Stickering.Controllers" } } };

            //routes.MapHttpRoute(
            //    name: "Stickering_Accounts",
            //    routeTemplate: "stickering/accounts/{id}",
            //    defaults: new { controller = "Accounts", id = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Stickering.Controllers" } } };

            //routes.MapHttpRoute(
            //    name: "Stickering_Statuses",
            //    routeTemplate: "stickering/statuses/{id}",
            //    defaults: new { controller = "Statuses", id = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Stickering.Controllers" } } };

            //routes.MapHttpRoute(
            //    name: "Stickering_StickerPhoto",
            //    routeTemplate: "stickering/stickers/{stickerId}/photos/{id}",
            //    defaults: new { controller = "Photos", id = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Stickering.Controllers" } } };

            //routes.MapHttpRoute(
            //    name: "Stickering_StickerPhotos",
            //    routeTemplate: "stickering/stickers/{stickerId}/photos",
            //    defaults: new { controller = "Photos", id = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Stickering.Controllers" } } };

            //routes.MapHttpRoute(
            //    name: "Stickering_StickerNote",
            //    routeTemplate: "stickering/stickers/{stickerId}/notes/{id}",
            //    defaults: new { controller = "Notes", id = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Stickering.Controllers" } } };

            //routes.MapHttpRoute(
            //    name: "StickerReasonTimes",
            //    routeTemplate: "stickering/reasonTimes/{id}",
            //    defaults: new { controller = "ReasonTimes", id = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Stickering.Controllers" } } };


            //routes.MapHttpRoute(
            //    name: "Stickering_Stickers_Actions",
            //    routeTemplate: "stickering/stickers/{id}/{action}",
            //    defaults: new { controller = "Stickers" })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Stickering.Controllers" } } };

            //routes.MapHttpRoute(
            //    name: "Stickering",
            //    routeTemplate: "stickering/",
            //    defaults: new { controller = "Stickering" })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Stickering.Controllers" } } };

            //routes.MapHttpRoute(
            //    name: "Stickering_Settings_Actions",
            //    routeTemplate: "stickering/settings/{id}",
            //    defaults: new { controller = "Settings", id = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Stickering.Controllers" } } };

            #endregion

            #region ParkingPermits

            //routes.MapHttpRoute(
            //    name: "ParkingPermits_default_",
            //    routeTemplate: "parkingpermits/{controller}/{id}",
            //    defaults: new { id = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Permits.Controllers" } } };

            #endregion

            #region Pre-Trip Inspections

            //routes.MapHttpRoute(
            //    name: "PreTripInspectionPhoto",
            //   routeTemplate: "pretripinspections/{preTripInspectionId}/photos/{id}",
            //    defaults: new { controller = "Photos", id = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.PreTripInspections.Controllers" } } };

            //routes.MapHttpRoute(
            //    name: "PreTripInspectionPhotos",
            //    routeTemplate: "pretripinspections/{preTripInspectionId}/photos",
            //    defaults: new { controller = "Photos", id = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.PreTripInspections.Controllers" } } };

            //routes.MapHttpRoute(
            //        name: "DefaultPass",
            //        routeTemplate: "PreTripInspections/DefaultPass",
            //        defaults: new { controller = "PreTripInspections", actions = "Put" },
            //        constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "PUT" }) })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new[] { "Extric.Towbook.API.Controllers" } } };

            #endregion

            #region Payment Verifications
            //routes.MapHttpRoute(
            //    name: "PaymentVerifications",
            //    routeTemplate: "management/{userId}/paymentVerifications/{id}",
            //    defaults: new { controller = "PaymentVerifications", id = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Management.Controllers" } } };
            #endregion

            #region Truck Odometer  (trucks/truckId/odometer/id)
            //routes.MapHttpRoute(
            //    name: "TruckOdometer",
            //    routeTemplate: "trucks/{truckId}/odometer/{id}",
            //    defaults: new { controller = "Odometer", id = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Trucks.Controllers" } } };
            #endregion

            #region Truck Expenses  (trucks/{truckId}/expenses/{id})
            //routes.MapHttpRoute(
            //    name: "TruckExpenses_Files_Upload",
            //    routeTemplate: "trucks/{truckId}/expenses/{expenseId}/files",
            //    defaults: new { controller = "Expenses", action = "Files" })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Trucks.Controllers" } } };
            #endregion

            #region Truck Expenses  (trucks/{truckId}/expenses/{id}/verify)
            //routes.MapHttpRoute(
            //    name: "TruckExpenses_Verify",
            //    routeTemplate: "trucks/{truckId}/expenses/{expenseId}/verify",
            //    defaults: new { controller = "Expenses", action = "Verify" })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Trucks.Controllers" } } };
            #endregion

            #region Truck Expenses  (trucks/{truckId}/expenses/{id}) POST
            //routes.MapHttpRoute(
            //    name: "TruckExpensesPost",
            //    routeTemplate: "trucks/{truckId}/expenses/{id}",
            //    defaults: new { controller = "Expenses", id = RouteParameter.Optional, action = "Post" },
            //    constraints: new
            //    {
            //        httpMethod = new HttpMethodConstraint(new string[] { "POST" }),
            //    })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Trucks.Controllers" } } };
            #endregion


            #region Truck Expenses  (trucks/{truckId}/expenses/{id})
            //routes.MapHttpRoute(
            //    name: "TruckExpenses",
            //    routeTemplate: "trucks/{truckId}/expenses/{id}",
            //    defaults: new { controller = "Expenses", id = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Trucks.Controllers" } } };
            #endregion

            #region Truck Expense Files (trucks/expenses/files/{id}) GET
            //routes.MapHttpRoute(
            //    name: "TruckExpensesFilesGet",
            //    routeTemplate: "trucks/expenses/files/{id}",
            //    defaults: new { controller = "Expenses", id = RouteParameter.Optional, action = "Files" },
            //    constraints: new
            //    {
            //        httpMethod = new HttpMethodConstraint(new [] { "GET" }),
            //    })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new [] { "Extric.Towbook.API.Trucks.Controllers" } } };
            #endregion


            //routes.MapHttpRoute(
            //    name: "TruckExpensesAddNewExpense_Photo_Workaround",
            //    routeTemplate: "trucks/expenses/{truckId}/addNewExpense",
            //    defaults: new { controller = "Expenses", action = "AddNewExpense" })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Trucks.Controllers" } } };


            #region Truck Odometer  (drivers/driverId/absences/id)
            //routes.MapHttpRoute(
            //    name: "DriverAbsences",
            //    routeTemplate: "drivers/{driverId}/absences/{absenceId}",
            //    defaults: new { controller = "Absences", absenceId = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Drivers.Controllers" } } };
            #endregion

            #region Drivers
            //routes.MapHttpRoute(
            //    name: "DriverAbsences",
            //    routeTemplate: "drivers/{driverId}/absences/{absenceId}",
            //    defaults: new { controller = "Absences", absenceId = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Drivers.Controllers" } } };

            //routes.MapHttpRoute(
            //    name: "Driver_Notification",
            //    routeTemplate: "drivers/useraccountnotificaiton/{driverId}",
            //    defaults: new { controller = "Drivers", action = "UserAccountNotificaiton" },
            //    constraints: new
            //    {
            //        httpMethod = new HttpMethodConstraint(new[] { "POST" }),
            //    }).DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Controllers" } } };

            //routes.MapHttpRoute(
            //  name: "DriverFiles",
            //  routeTemplate: "drivers/files/{Id}",
            //  defaults: new { controller = "Files" })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Controllers.Drivers" } } };

            #endregion

            #region Chat Messages

            //routes.MapHttpRoute(
            //    name: "ChatMessages",
            //    routeTemplate: "chats/{chatId}/messages/{id}/{action}",
            //    defaults: new { controller = "ChatMessages", id = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace} } };

            //routes.MapHttpRoute(
            //    name: "ChatMessagesActionOnly",
            //    routeTemplate: "chats/{chatId}/messages",
            //    defaults: new { controller = "ChatMessages" })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };

            #endregion

            #region Private Property Account Tags

            //routes.MapHttpRoute(
            //    name: "Tags_Put",
            //    routeTemplate: "propertyManagement/tags/{id}/{action}",
            //    defaults: new { controller = "Tags", id = RouteParameter.Optional, action = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace + ".PropertyManagement"} } };

            #endregion

            #region User Security Questions (user/securityQuestions) 

            //routes.MapHttpRoute(
            //    name: "SecurityQuestions",
            //    routeTemplate: "user/securityQuestions",
            //    defaults: new { controller = "SecurityQuestions" })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };

            #endregion

            #region User Pusher Event Notifications (notifications) 

            //routes.MapHttpRoute(
            //    name: "EventNotificationSoundDelete",
            //    routeTemplate: "eventnotifications/{itemId}/sounds/{id}",
            //    defaults: new { controller = "EventNotificationSounds", id = RouteParameter.Optional, action = "Delete" },
            //    constraints: new
            //    {
            //        HttpMethod = new HttpMethodConstraint(new string[] { "DELETE" })
            //    })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };

            //routes.MapHttpRoute(
            //    name: "UserEventNotificationsMarkRead",
            //    routeTemplate: "eventNotifications/read",
            //    defaults: new { controller = "EventNotifications" },
            //    constraints: new
            //    {
            //        httpMethod = new HttpMethodConstraint(new string[] { "PUT" }),
            //    })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };

            //routes.MapHttpRoute(
            //    name: "UserEventNotifications",
            //    routeTemplate: "eventNotifications/{id}/{action}",
            //    defaults: new { controller = "EventNotifications" })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };

            // api/eventnotfications/:itemId/sounds/:id/test
            // routes.MapHttpRoute(
            //     name: "EventNotificationSound_actions",
            //     routeTemplate: "eventNotifications/{itemId}/sounds/{id}/{action}",
            //     defaults: new { controller = "EventNotificationSounds", id = RouteParameter.Optional, action = RouteParameter.Optional },
            //     constraints: new
            //     {
            //         HttpMethod = new HttpMethodConstraint(new string[] { "POST" })
            //     })
            //     .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
            //
            // routes.MapHttpRoute(
            //     name: "EventNotificationSound",
            //     routeTemplate: "eventNotifications/{itemId}/sounds/{id}",
            //     defaults: new { controller = "EventNotificationSounds" },
            //     constraints: new { 
            //         HttpMethod = new HttpMethodConstraint(new string[] { "PUT", "DELETE" })
            //     })
            //     .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
            //
            #endregion

            #region Reporting (reporting/{controller}/{id}/{pageNumber})

            //routes.MapHttpRoute(
            //    name: "Reporting_Query",
            //    routeTemplate: "reporting/{controller}/{id}/{pageNumber}",
            //    defaults: new { id = RouteParameter.Optional, pageNumber = RouteParameter.Optional }
            //    ).DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Reporting.Controllers" } } };

            //routes.MapHttpRoute(
            //    name: "Reporting_NoPage",
            //    routeTemplate: "reporting/{controller}/{id}",
            //    defaults: new { action = "Get" }
            //    ).DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Reporting.Controllers" } } };

            #endregion

            #region Mapping API controllers (mapping/{controller}/{id}/{action})
            //routes.MapHttpRoute(
            //    name: "MappingApi",
            //    routeTemplate: "mapping/{controller}/{id}/{action}",
            //    defaults: new { id = RouteParameter.Optional, action = "Get" })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace + ".Mapping" } } };
            #endregion

            #region Directions API (/directions) - iOS uses this instead of /mapping/directions
            //routes.MapHttpRoute(
            //    name: "MappingApiDirections",
            //    routeTemplate: "directions",
            //    defaults: new { controller = "Directions",  action = "Get" })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace + ".Mapping" } } };
            #endregion

            #region Company Accounting API default
            //routes.MapHttpRoute(
            //    name: "Company_Accounting_Post",
            //    routeTemplate: "company/accounting/{controller}/{id}",
            //    defaults: new { id = RouteParameter.Optional, action = "Post" },
            //        constraints: new { httpMethod = new HttpMethodConstraint(new [] { "POST" }) })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new [] { myNamespace + ".Accounting" } } };

            //routes.MapHttpRoute(
            //    name: "Company_Accounting_Defaults",
            //    routeTemplate: "company/accounting/{controller}/{id}/{action}",
            //    defaults: new { id = RouteParameter.Optional, action = "get" })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new [] { myNamespace + ".Accounting" } } };
            #endregion

            #region Security Tools (company/security/)
            //routes.MapHttpRoute(
            //    name: "Security Tools Settings",
            //    routeTemplate: "company/security/{controller}/{action}",
            //    defaults: new { id = RouteParameter.Optional, action = new [] { "GET", "POST" }, controller = "SecurityToolSettings" })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new[] { myNamespace + ".Security" } } };

            //routes.MapHttpRoute(
            //    name: "Security Tools Settings Approved IP Addresses",
            //    routeTemplate: "company/security/{controller}/{action}",
            //    defaults: new {  action = new [] { "get", "post", "delete" }, controller = "ApprovedIpAddress" },
            //    constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "GET", "DELETE", "POST" }) })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new[] { myNamespace + ".Security" } } };
            #endregion

            #region Default API (controller/id/action)
            //routes.MapHttpRoute(
            //    name: "DefaultApi",
            //    routeTemplate: "{controller}/{id}/{action}")
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
            #endregion

            #region VIN decoder (vin/{id})

            //routes.MapHttpRoute(
            //    name: "VinDecoderGet",
            //    routeTemplate: "vin/{id}",
            //    defaults: new { action = "Get", controller = "Vin" }
            //    ).DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };

            #endregion

            #region Location Services (location/{action})
            //routes.MapHttpRoute(
            //    name: "Location Services",
            //    routeTemplate: "locaton/{action}",
            //    defaults: new { action = "Get", controller = "Location" }
            //    ).DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
            #endregion

            #region call validation (calls/{callId}/validate/{statusId}) 
            //routes.MapHttpRoute(
            //    name: "CallValidationDefault",
            //    routeTemplate: "calls/{callId}/validate/{statusId}",
            //    defaults: new { controller = "CallValidation", statusId = RouteParameter.Optional })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
            #endregion

            #region Dispatch Batch (calls/batch/action)
            //routes.MapHttpRoute(
            //    name: "Website",
            //    routeTemplate: "website/{controller}")
            //    .DataTokens = new RouteValueDictionary { 
            //        { "Namespaces", new string[] {
            //            myNamespace + ".Website" } } };
            #endregion

            #region Named actions like (api/widgets/123/doSomething) [POST]
            //routes.MapHttpRoute(
            //    name: "DefaultApiActionPost",
            //    routeTemplate: "{controller}/{id}/{action}",
            //    defaults: new { },
            //    constraints: new
            //    {
            //        httpMethod = new HttpMethodConstraint(new string[] { "POST" }),
            //        id = new RestStyleConstraint()
            //    }
            //    ).DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
            #endregion


            #region Default API (controller/id) [GET, PUT, POST, DELETE]

            //routes.MapHttpRoute(
            //    name: "SSO",
            //    routeTemplate: "sso",
            //    defaults: new { action = "Post", Controller = "Sso" },
            //    constraints: new
            //    {
            //        httpMethod = new HttpMethodConstraint(new string[] { "POST" })
            //    }
            //    ).DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Sso.Controllers" } } };

            //routes.MapHttpRoute(
            //    name: "SsoServices",
            //    routeTemplate: "sso/{action}",
            //    defaults: new { action = "Get", Controller = "Sso" },
            //    constraints: new
            //    {
            //        httpMethod = new HttpMethodConstraint(new string[] { "GET", "POST" })
            //    }
            //    ).DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Sso.Controllers" } } };

            //routes.MapHttpRoute(
            //    name: "CallsList",
            //    routeTemplate: "calls",
            //    defaults: new { action = "List", Controller = "Calls" },
            //    constraints: new
            //    {
            //        httpMethod = new HttpMethodConstraint(new string[] { "GET" })
            //    }
            //    ).DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };


            //routes.MapHttpRoute(
            //    name: "DefaultApiGet",
            //    routeTemplate: "{controller}/{id}",
            //    defaults: new { id = RouteParameter.Optional, action = "Get" },
            //    constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "GET" }),
            //                       id = new RestStyleConstraint() }
            //    ).DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };

            //routes.MapHttpRoute(
            //    name: "DefaultApiGetSingle",
            //    routeTemplate: "{controller}/{action}",
            //    defaults: new { action = "Get" },
            //    constraints: new
            //    {
            //        httpMethod = new HttpMethodConstraint(new string[] { "GET" }),
            //    }
            //    ).DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };

            //routes.MapHttpRoute(
            //    name: "DefaultApiPut",
            //    routeTemplate: "{controller}/{id}",
            //    defaults: new { id = RouteParameter.Optional, action = "Put" },
            //    constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "PUT" }),
            //                       id = new RestStyleConstraint() })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };

            //routes.MapHttpRoute(
            //    name: "DefaultApiPost",
            //    routeTemplate: "{controller}/{id}",
            //    defaults: new { id = RouteParameter.Optional, action = "Post" },
            //    constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "POST" }),
            //                       id = new RestStyleConstraint() })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };

            //routes.MapHttpRoute(
            //    name: "DefaultApiDelete",
            //    routeTemplate: "{controller}/{id}",
            //    defaults: new { id = RouteParameter.Optional, action = "Delete" },
            //    constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "DELETE" }),
            //                       id = new RestStyleConstraint(true) })
            //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };

            //routes.MapHttpRoute(
            //    name: "DefaultApiPostSingle",
            //    routeTemplate: "{controller}/{action}",
            //    defaults: new { action = "Post" },
            //    constraints: new
            //    {
            //        httpMethod = new HttpMethodConstraint(new string[] { "POST" }),
            //    }
            //    ).DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };


            #endregion


        }
    }

    //public class RestStyleConstraint : IRouteConstraint
    //{
    //    private bool allowEmpty;

    //    public RestStyleConstraint()
    //    {
    //        allowEmpty = false;
    //    }

    //    public RestStyleConstraint(bool allowEmpty)
    //    {
    //        this.allowEmpty = allowEmpty;
    //    }

    //    public bool Match(System.Web.HttpContextBase httpContext, Route route, string parameterName, RouteValueDictionary values, RouteDirection routeDirection)
    //    {
    //        if (values["id"] != null)
    //        {
    //            //System.Diagnostics.Debug.WriteLine("ZZZZZ --- ID: " + values["id"]);
    //            int x = 0;
    //            if (allowEmpty &&
    //                string.IsNullOrWhiteSpace(values["id"].ToString()))
    //            {
    //                return true;
    //            }

    //            if (int.TryParse(values["id"].ToString(), out x))
    //            {
    //                return true;
    //            }
    //        }

    //        return false;
    //    }
    //}

}
