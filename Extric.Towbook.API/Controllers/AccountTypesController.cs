using System;
using System.Collections.Generic;
using System.Linq;
using Extric.Towbook.Accounts;
using Extric.Towbook.Utility;
using Microsoft.AspNetCore.Mvc;

namespace Extric.Towbook.API.Controllers
{
    [Route("accounttypes")]
    public class AccountTypesController : ControllerBase
    {
        [HttpGet]
        [Route("")]
        public IEnumerable<AccountTypeModel> Get()
        {
            var types = new List<int>();
            foreach (var x in Enum.GetValues(typeof(AccountType)))
            {
                types.Add(Convert.ToInt32(x));
            }

            return types.Select(o => new AccountTypeModel() { Type = o, Name = GetFriendlyName((AccountType)o) });
        }

        [HttpPost]
        [Route("PluralCombined")]
        public IEnumerable<AccountTypeModel> PluralCombined()
        {
            var types = new List<int>();
            foreach (var x in Enum.GetValues(typeof(AccountType)))
            {
                types.Add(Convert.ToInt32(x));
            }

            return types.Select(o => new AccountTypeModel() { Type = o, Name = GetFriendlyNamesCombined((AccountType)o) }).ToCollection();
        }

        private static string GetFriendlyName(AccountType x)
        {
            switch (x)
            {
                case AccountType.Other:
                    return "Other";
                case AccountType.PoliceDepartment:
                    return "Police Department";
                case AccountType.Individual:
                    return "Individual";
                case AccountType.BodyShop:
                    return "Body Shop";
                case AccountType.InsuranceCompany:
                    return "Insurance";
                case AccountType.MotorClub:
                    return "Motor Club";
                case AccountType.Municipality:
                    return "Municipality";
                case AccountType.ServiceShop:
                    return "Service Shop";
                case AccountType.StorageFacility:
                    return "Storage Facility";
                case AccountType.PrivateProperty:
                    return "Private Property";
                case AccountType.ReposessionAgency:
                    return "Repossession Agency";
                case AccountType.Dealership:
                    return "Dealership";
                case AccountType.HeavyEquipment:
                    return "Heavy Equipment";
                case AccountType.Fleet:
                    return "Fleet";
                case AccountType.Transport:
                    return "Transport";
                default:
                    return Enum.GetName(typeof(AccountType), x).ToString();
            }
        }

        public static string GetFriendlyNamesCombined(AccountType x)
        {
            switch (x)
            {
                case AccountType.Other:
                    return "Others";

                case AccountType.Individual:
                    return "Individuals";

                case AccountType.PoliceDepartment:
                case AccountType.Municipality:
                    return "Police Departments & Municipalities";

                case AccountType.ServiceShop:
                case AccountType.BodyShop:
                    return "Service & Body Shops";

                case AccountType.InsuranceCompany:
                    return "Insurance Companies";

                case AccountType.MotorClub:
                    return "Motor Clubs";

                case AccountType.StorageFacility:
                    return "Storage Facilities";

                case AccountType.PrivateProperty:
                    return "Private Properties";

                case AccountType.ReposessionAgency:
                    return "Repossession Agencies";

                case AccountType.Dealership:
                    return "Dealerships";

                case AccountType.HeavyEquipment:
                    return "Heavy Equipment";

                case AccountType.Fleet:
                    return "Fleets";

                case AccountType.Transport:
                    return "Transports";

                default:
                    return Enum.GetName(typeof(AccountType), x).ToString();

            }
        }
    }

    public class AccountTypeModel
    {
        public int Type { get; set; }
        public string Name { get; set; }
    }
}
