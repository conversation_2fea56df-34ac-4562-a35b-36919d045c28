using Microsoft.AspNetCore.Mvc;
using System;
using System.Linq;
using Extric.Towbook.Dispatch;
using Newtonsoft.Json;
using System.Threading.Tasks;

namespace Extric.Towbook.API.Controllers
{
    [Route("companydispatchstatuses")]
    public class CompanyDispatchStatusesController : ControllerBase
    {
        [HttpGet]
        [Route("")]
        public async Task<object> Get()
        {
            return (await CompanyDispatchStatusCurrent.GetByCompanyIdsAsync((await this.GetCompaniesForRequestAsync()).Select(o => o.Id).ToArray()))
                .Select(o => new
                {
                    CompanyId = o.CompanyId,
                    IsActive = o.IsActive,
                    Reasons = o.Reasons != null ? JsonConvert.DeserializeObject<string[]>(o.Reasons) : Array.Empty<string>(),
                    UnavailableZips = o.BlockedZips != null ? JsonConvert.DeserializeObject<string[]>(o.BlockedZips) : Array.Empty<string>()
                });
        }
    }
}
