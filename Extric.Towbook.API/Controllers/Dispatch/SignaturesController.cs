using System;
using Extric.Towbook.API.Models;
using Extric.Towbook.Dispatch;
using Extric.Towbook.Storage;
using Extric.Towbook.Utility;
using SkiaSharp;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Extric.Towbook.Web;
using Extric.Towbook.WebShared;
using Extric.Towbook.WebShared.Multipart;
using Microsoft.AspNetCore.Mvc;
using static Extric.Towbook.API.ApiUtility;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.Net.Http.Headers;

namespace Extric.Towbook.API.Controllers
{

    // calls/{callId}/signatures/{id}
    [Route("calls/{callId}/signatures")]
    public class SignaturesController : ControllerBase
    {
        /// <summary>
        /// Retrieve a list of signatures associated with <paramref name="callId"/>.
        /// </summary>
        /// <param name="callId">The call/entry that the signature is associated with.</param>
        /// <returns>List of Signatures for the specified call</returns>
        [HttpGet]
        public async Task<IEnumerable<SignatureModel>> Get(int callId)
        {
            var d = await Entry.GetByIdAsync(callId);
            await ThrowIfNoCompanyAccessAsync(d?.CompanyId);

            var signatures = await Signature.GetByDispatchEntryIdAsync(callId);
            var result = new Collection<SignatureModel>();

            foreach (var s in signatures)
            {
                string path = await FileUtility.GetFileAsync(s.Location.Replace("%1", d.CompanyId.ToString()));
                if (!string.IsNullOrEmpty(path))
                    result.Add(SignatureModel.Map(s));
            }

            return result;
        }

        [HttpGet("{id}")]
        public async Task<HttpResponseMessage> Get(int callId, int id)
        {
            var x = await Signature.GetByIdAsync(id);

            if (x?.DispatchEntryId != callId)
                await ThrowIfNoCompanyAccessAsync((int?)null, "call");

            var d = await Entry.GetByIdAsync(x?.DispatchEntryId ?? 0);

            await ThrowIfNoCompanyAccessAsync(d?.CompanyId, "call");
            if (x?.DispatchEntryId != d.Id)
                x = null;

            ThrowIfNotFound(x, "signature");

            var result = new HttpResponseMessage(HttpStatusCode.OK);

            // this may be causing issues on iphone
            if (!Web.HttpContext.Current.IsAppleDevice()) //  && !HttpContext.Current.IsAndroidDevice())
            {
                //var response = Request.CreateResponse(HttpStatusCode.MovedPermanently);
                //res.Headers.Location = new Uri(FileUtility.GetPresignedUrlForDownloadFromClient(x.Location.Replace("%1", d.CompanyId.ToString()), x.ContentType, 30));
                Web.HttpContext.Current.Response.Headers["Location"] = new Uri(FileUtility.GetPresignedUrlForDownloadFromClient(x.Location.Replace("%1", d.CompanyId.ToString()), x.ContentType, 30)).ToString();
                return new HttpResponseMessage(HttpStatusCode.MovedPermanently);
            }


            var fileName = await FileUtility.GetFileAsync(x.Location.Replace("%1", d.CompanyId.ToString()));
            if (!System.IO.File.Exists(fileName) ||
                new FileInfo(fileName).Length == 0)
            {
                int trackingID = id;
                const string one = @"R0lGODlhAQABAPcAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACH5BAEAAP8ALAAAAAABAAEAAAgEAP8FBAA7";
                result.Content = new StreamContent(new MemoryStream(System.Convert.FromBase64String(one)));
                result.Content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("image/gif");
                return result;
            }
            else
            {
                result.Content = new StreamContent(new FileStream(fileName, FileMode.Open, FileAccess.Read, FileShare.ReadWrite));
                result.Content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue(x.ContentType);
            }

            return result;
        }

        /// <summary>
        /// Upload a signature for a call
        /// </summary>
        /// <param name="callId">The call/entry that the signature is associated with.</param>
        /// <param name="type">The type of signature</param>
        /// <param name="description"></param>
        /// <returns>If successful, returns HTTP Status 201. 
        /// 
        /// Returns a JSON object equilivant to calling SignaturesController.Get(callId, id). 
        /// </returns>
        [HttpPost]
        [DisableFormValueModelBinding]
        public async Task<ObjectResult> Post(int callId,
            [FromQuery] int type = 0,
            [FromQuery] string description = "")
        {
            if (type == 0)
            {
                // default it to 1
                type = 1;
            }

            //if (!Request.Content.IsMimeMultipartContent())
            if (!Web.HttpContext.Current.Request.HasFormContentType)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.UnsupportedMediaType));
            }

            var u = WebGlobal.CurrentUser;
            var d = await Entry.GetByIdAsync(callId);
            await ThrowIfNoCompanyAccessAsync(d?.CompanyId, "call");

            // validates the type of signature if its supported by this company calls

            // Save file
            string path = Path.GetTempPath();

            var provider = new MultipartFormDataStreamProvider(path);
            
            FileInfo fileInfo = null;

            FormOptions _defaultFormOptions = new FormOptions();

            var boundary = MultipartRequestHelper.GetBoundary(
                MediaTypeHeaderValue.Parse(Web.HttpContext.Current.Request.ContentType),
                _defaultFormOptions.MultipartBoundaryLengthLimit);

            var reader = new MultipartReader(boundary, Web.HttpContext.Current.Request.Body);

            var section = await reader.ReadNextSectionAsync();

            string targetFilePath = "";

            if (section != null)
            {
                var hasContentDispositionHeader =
                    ContentDispositionHeaderValue.TryParse(
                        section.ContentDisposition, out var contentDisposition);

                if (hasContentDispositionHeader)
                {
                    if (MultipartRequestHelper.HasFileContentDisposition(contentDisposition))
                    {
                        targetFilePath = Path.GetTempFileName();
                        using (var targetStream = System.IO.File.Create(targetFilePath))
                        {
                            await section.Body.CopyToAsync(targetStream);
                            fileInfo = new FileInfo(targetFilePath);
                            Console.WriteLine($"Copied the uploaded file '{targetFilePath}'");
                        }
                    }
                }
            }



            if (fileInfo == null || !fileInfo.Exists)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.InternalServerError)
                {
                    Content = new StringContent("Request is missing file data.")
                });
            }

            // Copy file and rename with new file name and correct extension
            FileInfo file = new FileInfo(targetFilePath);

            try
            {
                string fn = "";
                int photoId = -1;
                Signature s = new Signature();

                using (var i = SKBitmap.Decode(file.FullName))
                {
                    s.DispatchEntryId = callId;
                    s.ContentType = "image/jpg";
                    s.Description = description;
                    s.OwnerUserId = u.Id;
                    s.RemoteIp = this.GetRequestingIp();
                    s.SignatureTypeId = type;
                    s.Latitude = RecentLatitude();
                    s.Longitude = RecentLongitude();
                    s.Save();

                    await PhotosController.AutoAdvanceStatus(d, s.Latitude, s.Longitude, this.GetCurrentToken(), this.GetRequestingIp());

                    fn = s.Location.Replace("%1", d.CompanyId.ToString());

                    if (!Directory.Exists(Path.GetDirectoryName(fn)))
                        Directory.CreateDirectory(Path.GetDirectoryName(fn));

                    // keep it proportionately correct, don't allow any dimension to exceed 1920. 
                    using (var resized = i.ResizeProportionately(1920)) 
                        resized.Save(fn, SKEncodedImageFormat.Jpeg);

                    photoId = s.DispatchEntrySignatureId;

                    s.FileSize = new FileInfo(fn).Length;
                    s.Save();

                    await FileUtility.SendFileAsync(fn);

                    await Towbook.Integration.MotorClubs.Services.DigitalDispatchService.ShareSignatureAsync(d, s, WebGlobal.CurrentUser.Id, RecentLatitude(), RecentLongitude(),
                        WebGlobal.CurrentUser.FullName, d.Status?.Id ?? 0);
                }

                return StatusCode((int)HttpStatusCode.Created, SignatureModel.Map(s));
                //return this.Request.CreateResponse(HttpStatusCode.Created, SignatureModel.Map(s), PerRequestJsonSettingsFormatter.Instance);
            }
            catch
            {
                throw;
            }
            finally
            {
                file.Delete();
            }
            
        }

        /// <summary>
        /// Deletes a signature associated with a call.
        /// </summary>
        /// <param name="callId">The call/entry that the signature is associated with.</param>
        /// <param name="id">The ID of the signature to delete</param>
        /// <returns></returns>
        [ApiPermission(Towbook.User.TypeEnum.Manager)]
        [HttpDelete("{id}")]
        public async Task<HttpResponseMessage> Delete(int callId, int id)
        {
            var e = await Entry.GetByIdAsync(callId);
            var s = await Signature.GetByIdAsync(id);

            await ThrowIfNoCompanyAccessAsync(e?.CompanyId, "call");
            if (s.DispatchEntryId != e.Id)
                s = null;

            if (s != null)
            {
                s.Delete();
            }

            return new HttpResponseMessage(HttpStatusCode.NoContent);
        }
    }
}
