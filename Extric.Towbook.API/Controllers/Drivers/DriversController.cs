using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using Extric.Towbook.EventNotifications;
using Extric.Towbook.API.Models;
using Extric.Towbook.Web;
using Extric.Towbook.Company;
using Extric.Towbook.Dispatch;
using Extric.Towbook.Integration;
using Extric.Towbook.Integration.MotorClubs.Services;
using Extric.Towbook.Integrations.MotorClubs.Aaa;
using Extric.Towbook.Integrations.MotorClubs.Urgently;
using Extric.Towbook.Integrations.TomTom;
using Extric.Towbook.Licenses;
using Extric.Towbook.Utility;
using Extric.Towbook.WebShared;
using Newtonsoft.Json;
using NLog;
using static Extric.Towbook.API.ApiUtility;
using Async = System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using OfficeOpenXml;
using System.IO;
using System.Net.Http.Headers;
using Extric.Towbook.Accounts;
using Extric.Towbook.Api.Models;
using Extric.Towbook.Caching;

namespace Extric.Towbook.API.Controllers
{
    [Route("drivers")]
    public class DriversController : ControllerBase
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        /// <summary>
        /// Retrieve a list of drivers with minimal information by default. Specify full=true to return full details.
        /// </summary>
        /// <param name="full"></param>
        /// <returns></returns>
        ///
        [HttpGet]
        [Route("")]
        public async Task<IActionResult> GetAsync(bool full = false)
        {
            var companies = await this.GetCompaniesForRequestAsync();

            if (full)
            {
                var allDrivers = FinishGet(await Driver.GetByCompaniesAsync(companies));
                var allLicenseKeyValues = await DriverLicenseKeyValue.GetByDriverIdsAsync(allDrivers.Select(s => s.Id).ToArray());

                var fullDrivers = await Task.WhenAll(allDrivers.Select(async s => await DriverModel.MapAsync(s, allLicenseKeyValues.Where(w => w.DriverId == s.Id))));

                return Ok(fullDrivers);
            }

            var drivers = FinishGet(await InternalDriverModel.GetByCompaniesAsync(companies));
            return Ok(drivers);
        }


        /// <summary>
        /// Get request used by settings/ajax 
        /// </summary>
        /// <param name="includeDeleted"></param>
        /// <returns></returns>
        /// <exception cref="TowbookException"></exception>
        [HttpGet]
        [Route("full")]
        public async Task<IEnumerable<DriverModel>> FullAsync(bool? includeDeleted = false)
        {
            // We don't use the ApiPermission attribute because of these notes based overrides
            if (!(WebGlobal.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Manager ||
                    WebGlobal.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Accountant ||
                (WebGlobal.CurrentUser.Notes ?? "").Contains("AllowSettingsForTrucksDrivers") ||
                (WebGlobal.CurrentUser.Notes ?? "").Contains("GrantSupervisorRole")))
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            var companiesToConsider = new List<Company.Company> { WebGlobal.CurrentUser.Company };

            // Check for driver sharing setup and share all company drivers in the multi-company setup
            // TODO: Can be made async
            var sc = await SharedCompany.GetByCompanyIdAsync(WebGlobal.CurrentUser.CompanyId);
            if (sc?.FirstOrDefault(f => f.SharedCompanyId == WebGlobal.CurrentUser.CompanyId)?.ShareAllDrivers == true)
            {
                var parentCompanyId = sc.FirstOrDefault(f => f.SharedCompanyId == WebGlobal.CurrentUser.CompanyId).CompanyId;
                
                if (!companiesToConsider.Any(a => a.Id == parentCompanyId))
                    // TODO: Can be made async
                    companiesToConsider.Add(await Company.Company.GetByIdAsync(parentCompanyId));
            }

            var models = new List<DriverModel>();
            var allDrivers = new List<Driver>();

            foreach (var c in companiesToConsider)
            {
                if (includeDeleted == true)
                    // TODO: Can be made async
                    allDrivers.AddRange(Driver.GetByCompany(c, includeDeleted));
                else
                    allDrivers.AddRange(await Driver.GetByExactCompanyIdAsync(c.Id)); // use redis
            }

            // get all license key values for all drivers at one time
            var allLicenseKeyValues = await DriverLicenseKeyValue.GetByDriverIdsAsync(allDrivers.Select(s => s.Id).ToArray());

            foreach (var d in allDrivers)
            {
                if (!models.Any(a => a.Id == d.Id))
                    models.Add(DriverModel.Map(d, allLicenseKeyValues.Where(w => w.DriverId == d.Id)));
            }

            return models.OrderBy(o => o.Name);
        }

        [HttpGet]
        [Route("driverbar")]
        public async Task<IActionResult> DriverBar()
        {
            var res = FinishGet(await InternalDriverModel.GetByCompaniesAsync(await this.GetCompaniesForRequestAsync(), true)) as object;
            return Ok(res);
            //return Request.CreateResponse<dynamic>(HttpStatusCode.OK, FinishGet(InternalDriverModel.GetByCompanies(this.GetCompaniesForRequest5(), true)) as object);
        }

        public class InternalDriverModel
        {
            [JsonProperty(PropertyName = "id")]
            public int Id { get; set; }
            [JsonProperty(PropertyName = "name")]
            public string Name { get; set; }
            [JsonProperty(PropertyName = "linkedUserId")]
            public int LinkedUserId { get; set; }
            [JsonProperty(PropertyName = "operateHeavy")]
            public bool OperateHeavy { get; set; }
            [JsonProperty(PropertyName = "endDate")]
            public DateTime? EndDate { get; set; }
            [JsonProperty(PropertyName = "companyId")]
            public int CompanyId { get; set; }

            [JsonProperty(PropertyName = "companies")]
            public int[] Companies { get; set; }

            [JsonProperty(PropertyName = "createDate")]
            public DateTime? CreateDate { get; set; }

            [JsonProperty(PropertyName = "phone")]
            public string MobilePhone { get; set; }

            [JsonProperty(PropertyName = "profilePhotoUrl")]
            public string ProfilePhotoUrl { get; set; }

            public bool IsActive() => EndDate == null || EndDate > DateTime.Now;

            public static InternalDriverModel Map(Driver o)
            {
                return new InternalDriverModel
                {
                    Id = o.Id,
                    Name = o.Name,
                    LinkedUserId = o.UserId,
                    OperateHeavy = o.OperateHeavyEquipment,
                    EndDate = o.EndDate,
                    CompanyId = o.CompanyId,
                    Companies = o.Companies,
                    CreateDate = o.CreateDate,
                    MobilePhone = o.MobilePhone,
                    // TODO: Can be made async
                    ProfilePhotoUrl = InternalGetProfilePhotoUrl(o.UserId)
                };
            }

            public static async Task<IEnumerable<InternalDriverModel>> GetByCompaniesAsync(IEnumerable<Company.Company> companies = null,
                bool forBar = false)
            {
                var drivers = await Driver.GetByCompaniesAsync(companies);
                return drivers.Select(Map);
            }

        }

        public static IEnumerable<InternalDriverModel> FinishGet(IEnumerable<InternalDriverModel> drivers)
        {
            return Web.HttpContext.Current.IsMobileAppRequest()
                ? drivers.Where(o => o.IsActive())
                : drivers;
        }

        public static IEnumerable<Driver> FinishGet(IEnumerable<Driver> drivers)
        {
            return Web.HttpContext.Current.IsMobileAppRequest()
                ? drivers.Where(o => o.IsActive())
                : drivers;
        }


        /// <summary>
        /// Retrieve a specific driver by their unique ID.
        /// </summary>
        /// <param name="id"></param>
        /// <returns>DriverModel representing the driver</returns>
        /// 
        [HttpGet]
        [Route("{id}")]
        public async Task<DriverModel> GetAsync(int id)
        {
            var model = await InternalGetAsync(id);

            await ThrowIfNoCompanyAccessAsync(model?.Companies);

            return model;
        }

        /// <summary>
        /// Used by AJAX
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [NonAction]
        public async Task<DriverModel> InternalGetAsync(int id)
        {
            var driver = await Driver.GetByIdAsync(id);
            
            if (driver == null)
                throw new Exception("Driver not found for ID " + id);

            // TODO: Can be made async
            var model = DriverModel.Map(driver);

            return await FinishMapAsync(driver.CompanyId, model);
        }

        /// <summary>
        /// Create a new driver
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [ApiPermission(Towbook.User.TypeEnum.Manager)]
        [HttpPost]
        [Route("")]
        public async Task<DriverModel> Post(DriverModel model)
        {
            if (model == null)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                {
                    Content = new StringContent("Your request body is empty. Did you forget to include the JSON or set the content-type to application/json?")
                });
            }

            if (model.Id > 0)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                {
                    Content = new StringContent("You shouldn't set the ID in a POST request. If you're trying to update an existing object, use PUT method instead")
                });
            }

            var driver = new Driver();

            // IDOR check on UserId
            if (model.UserId > 0)
            {
                var user = await Towbook.User.GetByIdAsync(model.UserId);
                if (user?.Id > 0 && !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(user.CompanyId))
                    throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden) { Content = new StringContent("The specified user either doesn't exist or you don't have access to it.") });
            }

            await DriverModel.MapAsync(model, driver);

            driver.CompanyId = WebGlobal.CurrentUser.CompanyId;
            await driver.Save(WebGlobal.CurrentUser, WebGlobal.GetRequestingIp());

            if (await (await Account.GetByIdAsync(WebGlobal.CurrentUser.AccountId)).ShouldSendDriverEventsAsync())
                await DigitalDispatchService.SendExternalSyncDriversEvent(WebGlobal.CurrentUser.CompanyId, WebGlobal.CurrentUser.AccountId);            

            await SaveDriverFeatures(driver.Id, model);

            // TODO: Can be made async
            SaveLicenseInformation(driver, model);

            // TODO: Can be made async
            var up = UrgentlyProvider.GetByCompanyId(driver.CompanyId).FirstOrDefault();
            if (up != null && up.IsEnabled == true)
            {
                await DigitalDispatchService.SyncCompanyResources(up.CompanyId, up.AccountId, WebGlobal.CurrentUser.Id);
            }

            await Driver.UpdateGlobalDriverCacheAsync(driver, await WebGlobal.GetCompaniesAsync());

            await UpdateUserContactInformation(driver);

            return await FinishMapAsync(driver.CompanyId, DriverModel.Map(driver));
        }

        /// <summary>
        /// Update an existing driver
        /// </summary>
        /// <param name="id"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        [ApiPermission(Extric.Towbook.User.TypeEnum.Manager)]
        [HttpPut]
        [Route("{id}")]
        public async Task<DriverModel> Put(int id, DriverModel model)
        {
            if (model == null)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                {
                    Content = new StringContent("Your request body is empty. Did you forget to include the JSON or set the content-type to application/json?")
                });
            }

            if (model.Id < 1)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.InternalServerError)
                {
                    Content = new StringContent("You must set the Id in a POST request. If you're trying to create a new driver, use the POST method instead.")
                });
            }

            if (model.Id != id)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.InternalServerError)
                {
                    Content = new StringContent("The ID in the body of the request doesn't match the ID passed via the incoming URL.")
                });
            }

            var driver = await Driver.GetByIdAsync(id);

            await ThrowIfNoCompanyAccessAsync(driver?.Companies);

            if (driver.Deleted)
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound) { Content = new StringContent("Cannot update a deleted driver.") });

            // IDOR check on UserId
            if (model.UserId > 0)
            {
                var user = await Towbook.User.GetByIdAsync(model.UserId);
                if (user?.Id > 0 && !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(user.CompanyId))
                    throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden) { Content = new StringContent("The specified user either doesn't exist or you don't have access to it.") });
            }

            await DriverModel.MapAsync(model, driver);
            await driver.Save(WebGlobal.CurrentUser, WebGlobal.GetRequestingIp());

            await SaveDriverFeatures(driver.Id,model);

            if (await (await Account.GetByIdAsync(WebGlobal.CurrentUser.AccountId)).ShouldSendDriverEventsAsync())
                await DigitalDispatchService.SendExternalSyncDriversEvent(WebGlobal.CurrentUser.CompanyId, WebGlobal.CurrentUser.AccountId);

            // TODO: Can be made async
            SaveLicenseInformation(driver, model);

            var up = (await UrgentlyProvider.GetByCompanyIdAsync(driver.CompanyId)).FirstOrDefault();
            if (up != null && up.IsEnabled == true)
            {
                await DigitalDispatchService.SyncCompanyResources(up.CompanyId, up.AccountId, WebGlobal.CurrentUser.Id);
            }

            await Driver.UpdateGlobalDriverCacheAsync(driver, await WebGlobal.GetCompaniesAsync());

            await UpdateUserContactInformation(driver);

            return await FinishMapAsync(driver.CompanyId, DriverModel.Map(driver));
        }

        /// <summary>
        /// Deletes the specified driver
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [ApiPermission(Towbook.User.TypeEnum.Manager)]
        [HttpDelete]
        [Route("{id}")]
        public async Task<HttpResponseMessage> Delete(int id)
        {
            var driver = await Driver.GetByIdAsync(id);

            if (driver == null || driver.Deleted)
                return new HttpResponseMessage(HttpStatusCode.NoContent);

            await ThrowIfNoCompanyAccessAsync(driver?.Companies);

            await driver.DeleteAsync();

            if (await (await Account.GetByIdAsync(WebGlobal.CurrentUser.AccountId)).ShouldSendDriverEventsAsync())
            {
                await DigitalDispatchService.SendExternalSyncDriversEvent(WebGlobal.CurrentUser.CompanyId, WebGlobal.CurrentUser.AccountId);
            }

            var up = (await UrgentlyProvider.GetByCompanyIdAsync(driver.CompanyId)).FirstOrDefault();
            if (up != null && up.IsEnabled == true)
            {
                await DigitalDispatchService.SyncCompanyResources(up.CompanyId, up.AccountId, WebGlobal.CurrentUser.Id);
            }

            return new HttpResponseMessage(HttpStatusCode.Accepted);
        }

        /// <summary>
        /// Undelete the specified driver
        /// </summary>
        /// <param name="id">The driver's id</param>
        /// <returns></returns>
        [HttpPost]
        [HttpPut]
        [Route("{id}/undelete")]
        [ApiPermission(Towbook.User.TypeEnum.Manager)]
        public async Task<HttpResponseMessage> Undelete(int id)
        {
            var driver = await Driver.GetByIdAsync(id);

            await ThrowIfNoCompanyAccessAsync(driver?.Companies);

            if (driver.Deleted == false)
                return new HttpResponseMessage(HttpStatusCode.OK);
    
            await driver.UndeleteAsync();

            if (await (await Account.GetByIdAsync(WebGlobal.CurrentUser.AccountId)).ShouldSendDriverEventsAsync())
                await DigitalDispatchService.SendExternalSyncDriversEvent(WebGlobal.CurrentUser.CompanyId, WebGlobal.CurrentUser.AccountId);

            return new HttpResponseMessage(HttpStatusCode.Accepted);
        }

        /// <summary>
        /// Disable the specified driver
        /// </summary>
        /// <param name="id">The driver's id</param>
        /// <returns></returns>
        [HttpPost]
        [ApiPermission(Towbook.User.TypeEnum.Manager)]
        [Route("{id}/disable")]
        public async Task<HttpResponseMessage> Disable(int id)
        {
            var driver = await Driver.GetByIdAsync(id);

            await ThrowIfNoCompanyAccessAsync(driver?.Companies);

            driver.EndDate = DateTime.Today;
            await driver.Save();
            
            if (await (await Account.GetByIdAsync(WebGlobal.CurrentUser.AccountId)).ShouldSendDriverEventsAsync())
                await DigitalDispatchService.SendExternalSyncDriversEvent(WebGlobal.CurrentUser.CompanyId, WebGlobal.CurrentUser.AccountId);

            var up = (await UrgentlyProvider.GetByCompanyIdAsync(driver.CompanyId)).FirstOrDefault();
            if (up != null && up.IsEnabled == true)
            {
                await DigitalDispatchService.SyncCompanyResources(up.CompanyId, up.AccountId, WebGlobal.CurrentUser.Id);
            }

            return new HttpResponseMessage(HttpStatusCode.Accepted);
        }

        /// <summary>
        /// Send message to a driver
        /// </summary>
        /// <param name="id">The id of the driver</param>
        /// <param name="model">The DriverMessageModel that contains the message to send</param>
        /// <returns></returns>
        [HttpPost("{id}/SendMessage")]
        public async Task<IActionResult> SendMessage(int id, DriverMessageModel model)
        {
            if (model == null)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("Your request body is empty. Did you forget to include the JSON or set the content-type to application/json?")
                });
            }

            Entry entry = null;
            if (model.CallId.HasValue)
            {
                entry = await Entry.GetByIdAsync(model.CallId.Value);
                await ThrowIfNoCompanyAccessAsync(entry?.CompanyId);
            }

            if (string.IsNullOrWhiteSpace(model.Message))
            {
                if (entry != null)
                {
                    // TODO: Can be made async
                    model.Message = (await WdnMessageTemplate.GetByTypeIdAsync(1, WebGlobal.CurrentUser.CompanyId)).ParseMessage(entry);
                }
                else
                {
                    throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                    {
                        Content = new StringContent("Your request contains an empty message. Empty messages are not allowed to be sent.")
                    });
                }
            }

            if (model.Id != id)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.InternalServerError)
                {
                    Content = new StringContent("The ID in the body of the request doesn't match the ID passed via the incoming URL.")
                });
            }

            var drivers = new List<int>();

            if (model.Id > 0)
                drivers.Add(model.Id);


            if (model.Drivers != null)
            {
                foreach (int driverId in model.Drivers)
                {
                    if (!drivers.Contains(driverId))
                        drivers.Add(driverId);
                }
            }

            var currentUser = WebGlobal.CurrentUser;

            if (drivers.Count == 0)
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.InternalServerError)
                {
                    Content = new StringContent("To send a message, you have to specify at least one driver.")
                });

            #region Towouts - remove first asset driver(s)
            if (entry != null && CallsController.IsTowoutCall(entry))
            {
                // remove first asset's driver(s)
                var firstAssetDrivers = entry.Assets?.FirstOrDefault()?.Drivers;
                if(firstAssetDrivers != null)
                {
                    foreach(var dId in firstAssetDrivers.Select(s => s.DriverId.GetValueOrDefault()))
                    {
                        if (dId > 0 && drivers.Contains(dId))
                            drivers.Remove(dId);
                    }
                }
            }
            #endregion

            foreach (int driverId in drivers)
            {
                var driver = await Driver.GetByIdAsync(driverId);

                if (driver == null ||
                    driver.Deleted == true ||
                    // TODO: This is not performant. We are making n calls through the network on a loop.
                    !(await currentUser.HasAccessToCompanyAsync(driver.Companies)))
                {
                    // the driver 

                    throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                    {
                        Content = new StringContent("The specified driver either doesn't exist or you don't have access to it.")
                    });
                }

                #region send via text message
                int[] premiumTextingCompanies = Array.Empty<int>();

                int companyId = currentUser.PrimaryCompanyId;

                // TODO: Can be made async
                var kv = CompanyKeyValue.GetFirstValueOrNull(companyId, Provider.Towbook.ProviderId, "Premium Texting");

                if (kv == "1")
                    premiumTextingCompanies = new int[] { currentUser.PrimaryCompanyId };

                if (driver.DispatchingNotificationType != null && !string.IsNullOrWhiteSpace(driver.DispatchingNotificationValue))
                {
                    int msgType = (int)driver.DispatchingNotificationType.Value;
                    string dest = driver.DispatchingNotificationValue;

                    if (premiumTextingCompanies.Where(o => o == companyId).Any())
                    {
                        // if it is an email address, strip away the phone number from the email
                        msgType = 100;

                        if (dest.Contains("@"))
                        {
                            dest = dest.Substring(0, dest.IndexOf('@'));
                        }
                    }

                    var dnm = new DispatchNotificationMessage(currentUser.Id,
                        driver.Id, (DriverDispatchingNotificationType)msgType, dest);

                    dnm.CompanyId = currentUser.CompanyId;
                    dnm.Message = model.Message;

                    // TODO: Can be made async, also this isn't performant (n database/network calls on a loop)
                    if (CompanyKeyValue.GetFirstValueOrNull(driver.CompanyId, Provider.Towbook.ProviderId, "Towbook_Calls_HideDispatcherFromDrivers") == "1")
                        dnm.Message = dnm.Message.Replace(currentUser.FullName, currentUser.Company.Name);

                    // TODO: Can be made async, also this isn't performant (n database/network calls on a loop)
                    await DispatchNotificationMessage.Send(dnm);
                }
                #endregion

                var u = await Towbook.User.GetByIdAsync(driver.UserId);

                #region Send Push Notification

                if (u != null)
                {
                    var values = new Dictionary<string, string>();
                    values.Add("Message", model.Message);
                    values.Add("Time", Core.OffsetDateTime(currentUser.Company, DateTime.Now).ToShortTowbookTimeString());
                    values.Add("senderUserId", currentUser.Id.ToString());
                    var keys = u.GetKeys();

                    if (model.CallId != null)
                    {
                        var call = await Entry.GetByIdNoCacheAsync(model.CallId.Value);

                        if (call == null || ! (await currentUser.HasAccessToCompanyAsync(call.CompanyId)))
                            throw new TowbookException("invalid callId");

                        string vehicle = "";
                        string reason = "";

                        if (!string.IsNullOrWhiteSpace(call.MakeModelFormatted))
                        {
                            vehicle = " for a " + (call.Year > 0 ? call.Year + " " : "") + call.MakeModelFormatted;
                        }
                        else
                            vehicle = ".";

                        if (entry.ReasonId != 1635 && entry.Reason != null)
                            reason = " a " + entry.Reason.Name;

                        values.Add("callId", model.CallId.Value.ToString());

                        // TODO: Can be made async
                        var hide = CompanyKeyValue.GetFirstValueOrNull(call.CompanyId, Provider.Towbook.ProviderId, "HideAccountDetailsFromDrivers");
                        var hideAccount = (hide == "2" || hide == "3" || hide == "4");

                        if (!hideAccount && call.Account != null)
                            values.Add("Account", call.Account.Company);

                        var sender = currentUser.FullName;

                        // TODO: Can be made async
                        if (CompanyKeyValue.GetFirstValueOrNull(driver.CompanyId, Provider.Towbook.ProviderId, "Towbook_Calls_HideDispatcherFromDrivers") == "1")
                            sender = call.Company.Name;

                        values["Message"] = (sender + " has assigned " + reason + " Call #" + call.CallNumber + " to you" + vehicle).Replace("  ", " ");

                        // Check for and handle multiple addresses
                        if (call.Waypoints.Count() > 2)
                        {
                            foreach(var wp in call.Waypoints)
                            {
                                values[wp.Title] = wp.Address;
                            }
                        }
                        else
                        {
                            if (!string.IsNullOrWhiteSpace(call.TowSource))
                                values["Location"] = call.TowSource;

                            if (!string.IsNullOrWhiteSpace(call.TowDestination))
                                values["Destination"] = call.TowDestination;
                        }

                        if (call.Reason != null)
                            values["Reason"] = call.Reason.Name;


                        if (keys.Any(o => o.Key == "notificationhub_registration_id"))
                        {
                            await NotificationHubHelper.GeneralChannelInstance.SendNotificationMessage(u,
                                "New Call assigned to you",
                                values, true, "Call Dispatched");
                        }
                    }
                    else
                    {
                        try
                        {
                            if (keys.Any(o => o.Key == "notificationhub_registration_id"))
                            {
                                await NotificationHubHelper.GeneralChannelInstance.SendNotificationMessage(u,
                                    "Message from " + currentUser.FullName,
                                    values, true, "Message");
                            }
                        }
                        catch (Exception)
                        {
                            // todo: log this problem... 
                        }
                    }
                }
                #endregion

                #region send via TomTom unit

                // TODO: Can be made async
                var tomtoms = DriverKeyValue.GetByDriver(driver.CompanyId, driver.Id).Where(o => o.KeyId ==
                // TODO: Can be made async
                    Provider.TomTom.GetKey(KeyType.Driver, "DriverId").Id);
                try
                {
                    if (tomtoms.Any())
                    {
                        var ttdid = tomtoms.FirstOrDefault();

                        var ttu = TomTomUtility.GetInstance(driver.CompanyId);

                        if (ttu != null)
                        {
                            // TODO: Can be made async
                            var ttdriver = ttu.GetDrivers().Where(o => o.Id == ttdid.Value).FirstOrDefault();
                            if (ttdriver != null)
                            {
                                if (model.CallId != null)
                                {
                                    var call = await Entry.GetByIdAsync(model.CallId.Value);

                                    if (call == null || !(await currentUser.HasAccessToCompanyAsync(call.CompanyId)))
                                        throw new TowbookException("invalid callid");

                                    long lat = 0;
                                    long longitude = 0;

                                    if (call.Waypoints.Any())
                                    {
                                        var wpDest = call.Waypoints.Where(o => o.Title == "Pickup").FirstOrDefault();

                                        if (wpDest?.Latitude != 0 &&
                                            wpDest?.Longitude != 0)
                                        {
                                            lat = (long)(wpDest.Latitude * 1000000);
                                            longitude = (long)(wpDest.Longitude * 1000000);
                                        }
                                    }

                                    var address = call.TowSource;

                                    if (lat == 0 && longitude == 0)
                                    {
                                        if (call.Attributes.ContainsKey(Dispatch.AttributeValue.BUILTIN_DISPATCH_SOURCE_LATLONG) &&
                                            call.Attributes[Dispatch.AttributeValue.BUILTIN_DISPATCH_SOURCE_LATLONG].Value.Contains(','))
                                        {
                                            string[] latLong = call.Attributes[Dispatch.AttributeValue.BUILTIN_DISPATCH_SOURCE_LATLONG].Value.Split(',');
                                            lat = Convert.ToInt64(latLong[0]);
                                            longitude = Convert.ToInt64(latLong[1]);
                                        }
                                        else
                                        {
                                            var lm = LocationModel.MapFromGoogle(await Mapping.GMapUtil.Geocode(address)).FirstOrDefault();

                                            if (lm != null)
                                            {
                                                lat = (long)(lm.Latitude * 1000000);
                                                longitude = (long)(lm.Longitude * 1000000);
                                                address = $"{lm.Address}, {lm.City} {lm.State} {lm.Zip}";

                                                var av = new Dispatch.AttributeValue()
                                                {
                                                    DispatchEntryId = call.Id,
                                                    DispatchEntryAttributeId = Dispatch.AttributeValue.BUILTIN_DISPATCH_SOURCE_LATLONG
                                                };

                                                av.Value = lat + "," + longitude;
                                                await av.SaveAsync(this.GetCurrentToken(), this.GetRequestingIp());
                                            }
                                        }
                                    }

                                    StringBuilder sb = new StringBuilder();
                                    if (!string.IsNullOrWhiteSpace(call.TowDestination))
                                    {
                                        sb.Append("Pickup Address: " + call.TowSource);
                                        sb.AppendLine();
                                        sb.Append($"Destination Address: " + call.TowDestination);
                                    }
                                    else
                                        sb.Append("Service Location: " + call.TowSource);

                                    sb.AppendLine();
                                    sb.Append(call.Reason);
                                    sb.AppendLine();
                                    sb.AppendFormat("{0} {1} {2} {3}", call.Year, call.VehicleMake, call.VehicleModel, call.Color);
                                    if (call.LicenseNumber != null)
                                    {
                                        sb.Append(", ");
                                        sb.Append(call.LicenseNumber);
                                    }
                                    sb.AppendLine();

                                    if (call.Account != null)
                                    {
                                        sb.Append(call.Account.Company);
                                        sb.AppendLine();
                                    }

                                    if (!String.IsNullOrWhiteSpace(call.PurchaseOrderNumber))
                                    {
                                        sb.Append(call.PurchaseOrderNumber);
                                        sb.AppendLine();
                                    }

                                    EntryContact contact = null;

                                    if (call.Contacts.Any())
                                    {
                                        contact = call.Contacts.First();
                                        sb.Append(contact.Name);

                                        if (!String.IsNullOrWhiteSpace(contact.Phone))
                                        {
                                            sb.Append(", Phone # ");
                                            sb.Append(Core.FormatPhone(contact.Phone));
                                        }
                                        sb.AppendLine();
                                    }

                                    if (!String.IsNullOrWhiteSpace(call.Notes))
                                    {
                                        sb.Append("Notes: ");
                                        sb.Append(call.Notes);
                                        sb.AppendLine();
                                    }

                                    string orderId = TomTomUtility.ConvertToBase(call.CompanyId, 36) + "_" + call.CallNumber.ToString();
                                    try
                                    {
                                        // TODO: Can be made async
                                        ttu.SendCall(ttdriver.CurrentUnitId,
                                            OrderType.PickupOrder,
                                            longitude,
                                            lat,
                                            orderId,
                                            sb.ToString(),
                                            address,
                                            contact?.Name,
                                            contact?.Phone);

                                        var dekv = new DispatchEntryKeyValue();
                                        // TODO: Can be made async
                                        dekv.KeyId = Provider.TomTom.GetKey(KeyType.DispatchEntry, "OrderId").Id;
                                        dekv.Value = orderId;
                                        dekv.DispatchEntryId = call.Id;
                                        // TODO: Can be made async
                                        dekv.Save();
                                    }
                                    catch (TomTomException tte)
                                    {
                                        object data = new
                                        {
                                            type = "tomtom",
                                            code = tte.ErrorCode,
                                            tomtomMessage = tte.Message
                                        };

                                        if (tte.ErrorCode == "2515")
                                        {
                                            // TODO: Can be made async
                                            ttu.UpdateCall(ttdriver.CurrentUnitId, OrderType.PickupOrder, longitude, lat, orderId,
                                                sb.ToString(), address, contact?.Name, contact?.Phone);

                                            // TODO: Can be made async
                                            ttu.ReassignOrder(orderId, ttdriver.CurrentUnitId);
                                        }
                                        else if (tte.ErrorCode == "9500")
                                        {
                                            return BadRequest(data as object);
                                            //return this.Request.CreateResponse<dynamic>(HttpStatusCode.BadRequest, data as object, PerRequestJsonSettingsFormatter.Instance);
                                        }
                                        else
                                        {
                                            if (this.Request != null)
                                                return Ok("FAILED SILENTLY; error from tomtom:" + tte.ErrorCode + ", " + tte.Message);
                                            //return this.Request.CreateResponse<dynamic>(HttpStatusCode.OK, "FAILED SILENTLY; error from tomtom:" + tte.ErrorCode + ", " + tte.Message, PerRequestJsonSettingsFormatter.Instance);
                                            else
                                                return null;
                                        }
                                    }
                                    catch (Exception e)
                                    {
                                        if (this.Request != null)
                                            return Ok("FAILED SILENTLY; erro:" + e.ToString());
                                            //return this.Request.CreateResponse<dynamic>(HttpStatusCode.OK, "FAILED SILENTLY; erro:" + e.ToString(), PerRequestJsonSettingsFormatter.Instance);
                                        else
                                            return null;
                                    }

                                    if (this.Request != null)
                                        return Ok("OK; Sent to TomTom!");
                                        //return this.Request.CreateResponse<dynamic>(HttpStatusCode.OK, "OK; Sent to TomTom!", PerRequestJsonSettingsFormatter.Instance);
                                    else
                                        return null;
                                }
                            }
                        }
                    }
                }
                catch
                {

                }

                #endregion


                await GeotabDeliver(driver, model);

                if (entry != null)
                {
                    await SendExpiration(entry, id);
                }
            }

            if (this.Request != null)
                return Ok("OK".ToJson());
            else
                return null;
        }

        /// <summary>
        /// Excel Export Drivers
        /// </summary>
        /// <returns></returns>
        [ApiPermission(Towbook.User.TypeEnum.Manager, Towbook.User.TypeEnum.Accountant)]
        [HttpGet]
        [Route("export")]
        public async Task<HttpResponseMessage> ExportAsync()
        {
            // TODO: Can be made async
            var model = DriverModel.Map(Driver.GetByCompany(WebGlobal.CurrentUser.Company, false));
            var exportData = new List<IDictionary<string, object>>();

            ExcelPackage.LicenseContext = LicenseContext.Commercial;

            // Create a new Excel package
            using (var excelPackage = new ExcelPackage())
            {
                // Add a worksheet to the workbook
                var worksheet = excelPackage.Workbook.Worksheets.Add("Drivers");

                // Define header names and corresponding property names
                var headers = new string[]
                { "Id", "Name", "Address", "City", "State", "Zip", "Mobile Phone","Work Phone", "Home Phone", 
                    "Email", "License Number", "License Class","License Exp", "Heavy Equip Operator", "Start Date", "Notes"
                };

                //Make the header row bold
                using (var range = worksheet.Cells["A1:P1"])
                {
                    range.Style.Font.Bold = true;
                }

                // Add headers to the first row of the worksheet
                for (int i = 0; i < headers.Length; i++)
                {
                    worksheet.Cells[1, i + 1].Value = headers[i];
                }

                // Populate data rows
                int rowId = 0;
                foreach (var driver in model.Where(x => x.IsActive()))
                {
                    worksheet.Cells[rowId + 2, 1].Value = driver.Id;
                    worksheet.Cells[rowId + 2, 2].Value = driver.Name;
                    worksheet.Cells[rowId + 2, 3].Value = driver.Address;
                    worksheet.Cells[rowId + 2, 4].Value = driver.City;
                    worksheet.Cells[rowId + 2, 5].Value = driver.State;
                    worksheet.Cells[rowId + 2, 6].Value = driver.Zip;
                    worksheet.Cells[rowId + 2, 7].Value = Core.FormatPhone(driver.MobilePhone, WebGlobal.CurrentUser.Company);
                    worksheet.Cells[rowId + 2, 8].Value = Core.FormatPhone(driver.WorkPhone, WebGlobal.CurrentUser.Company);
                    worksheet.Cells[rowId + 2, 9].Value = Core.FormatPhone(driver.HomePhone, WebGlobal.CurrentUser.Company);
                    worksheet.Cells[rowId + 2, 10].Value = driver.Email;
                    worksheet.Cells[rowId + 2, 11].Value = driver.LicenseNumber;
                    worksheet.Cells[rowId + 2, 12].Value = driver.LicenseClass;
                    worksheet.Cells[rowId + 2, 13].Value = (driver.LicenseExpirationDate != null ? String.Format("{0:d}", 
                        Core.OffsetDateTime(WebGlobal.CurrentUser.Company, driver.LicenseExpirationDate.Value, false)) : string.Empty);
                    worksheet.Cells[rowId + 2, 14].Value = driver.OperateHeavyEquipment ? "Yes" : string.Empty;
                    worksheet.Cells[rowId + 2, 15].Value = driver.StartDate != null ? String.Format("{0:d}", 
                        Core.OffsetDateTime(WebGlobal.CurrentUser.Company, driver.StartDate.Value, false)) : string.Empty;
                    worksheet.Cells[rowId + 2, 16].Value = driver.Notes;

                    rowId++;
                }

                // Auto fit columns for better readability
                worksheet.Cells.AutoFitColumns();

                //Prepare to export
                using var stream = new MemoryStream();
                // TODO: Can be made async
                await excelPackage.SaveAsAsync(stream);
                stream.Position = 0;

                var result = new HttpResponseMessage(HttpStatusCode.OK)
                {
                    Content = new ByteArrayContent(stream.ToArray())
                };

                result.Content.Headers.ContentDisposition = new ContentDispositionHeaderValue("attachment")
                {
                    FileName = "Export-Drivers.xlsx"
                };

                result.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

                return result;
            }
        }


        public static async Task SendExpiration(Entry entry, int driverId)
        {
            var logEvent = new LogEventInfo();
            logEvent.LoggerName = logger.Name;
            logEvent.Message = "AutoDispatch";
            logEvent.Level = LogLevel.Info;
            logEvent.TimeStamp = DateTime.Now;

            try
            {
                // TODO: Can be made async
                if (!await entry.Company.HasFeatureAsync(Generated.Features.AutoDispatch))
                    return;
                // this code wont work because the driver gets added in a different api call... the driver isnt set on the call yet.
                /*
                if (entry.Drivers.Where(o => o > 0).Count() == 0 ||
                    entry.Drivers.Where(o => o > 0).Count() > 1)
                    return;
                    */

                var qc =  await ServiceBusHelper.CreateProducerQueueAsync("autodispatch");
                var bm = new BrokeredMessage(new
                {
                    callId = entry.Id,
                    companyId = entry.CompanyId, 
                    driverId = driverId,
                    type = "expired"
                }.ToJson());

                bm.TimeToLive = TimeSpan.FromMinutes(10);

                if (entry.CompanyId == 10000)
                    bm.ScheduledEnqueueTime = DateTime.UtcNow.AddSeconds(120);
                else
                    bm.ScheduledEnqueueTime = DateTime.UtcNow.AddMinutes(8);

                await qc.SendAsync(bm);


                var bm3 = new BrokeredMessage(new
                {
                    callId = entry.Id,
                    companyId = entry.CompanyId,
                    driverId = driverId,
                    type = "textReminder"
                }.ToJson());

                bm3.TimeToLive = TimeSpan.FromMinutes(3);
                bm3.ScheduledEnqueueTime = entry.CompanyId == 10000 ? DateTime.UtcNow.AddSeconds(30) : DateTime.UtcNow.AddMinutes(2);

                await qc.SendAsync(bm3);
                
                var bm2 = new BrokeredMessage(new
                {
                    callId = entry.Id,
                    companyId = entry.CompanyId, 
                    driverId = driverId,
                    type = "voiceDispatch"
                }.ToJson());

                bm2.TimeToLive = TimeSpan.FromMinutes(5);
                bm2.ScheduledEnqueueTime = entry.CompanyId == 10000 ? DateTime.UtcNow.AddSeconds(60) : DateTime.UtcNow.AddMinutes(4);

                await qc.SendAsync(bm2);

                #region recommend cancellation at 15 minutes
                var bm4 = new BrokeredMessage(new
                {
                    callId = entry.Id,
                    companyId = entry.CompanyId,
                    type = "recommendCancel"
                }.ToJson());

                bm4.TimeToLive = TimeSpan.FromMinutes(16);
                bm4.ScheduledEnqueueTime = entry.CompanyId == 10000 ? DateTime.UtcNow.AddSeconds(120) : DateTime.UtcNow.AddMinutes(15);

                await qc.SendAsync(bm4);
                #endregion


                #region warn dispatchers no response received yet - 5 minutes
                var bm5 = new BrokeredMessage(new
                {
                    callId = entry.Id,
                    companyId = entry.CompanyId,
                    driverid = entry.DriverId,
                    type = "warning_alert"
                }.ToJson());

                bm5.TimeToLive = TimeSpan.FromMinutes(6);
                bm5.ScheduledEnqueueTime = entry.CompanyId == 10000 ? DateTime.UtcNow.AddSeconds(80) : DateTime.UtcNow.AddMinutes(5);

                await qc.SendAsync(bm5);

                #endregion

                logEvent.Properties["messageId"] = bm.MessageId;

                logger.Log(logEvent);
            }
            catch (Exception e)
            {
                logEvent.Message += " - FAILED";
                logEvent.Level = LogLevel.Fatal;
                logEvent.Exception = e;

                logger.Log(logEvent);
            }
        }
        private async Task GeotabDeliver(Driver driver, DriverMessageModel model)
        {
            // TODO: In general, use the primitive `string`, not the class `String`, that generates even more garbage.
            var logEvent = new LogEventInfo();
            logEvent.LoggerName = logger.Name;
            logEvent.Message = "Geotab Delivery Event";
            logEvent.Level = LogLevel.Info;
            logEvent.TimeStamp = DateTime.Now;

            // find a geotab vehicle for this driver.

            var currentUser = WebGlobal.CurrentUser;

            // find a truck associated with this driver...
            var defaultTruck = await DriverTruckDefault.GetByDriverIdAsync(driver.Id);

            if (defaultTruck == null)
                return;

            // TODO: Can be made async
            var an = CompanyKeyValue.GetFirstValueOrNew(driver.CompanyId, Provider.GeoTab.ProviderId, "AccountName")?.Value;
            // TODO: Can be made async
            var u = CompanyKeyValue.GetFirstValueOrNew(driver.CompanyId, Provider.GeoTab.ProviderId, "Username")?.Value;
            // TODO: Can be made async
            var p = CompanyKeyValue.GetFirstValueOrNew(driver.CompanyId, Provider.GeoTab.ProviderId, "Password")?.Value;

            try
            {
                var ttu = await Integrations.GeoTab.GeoTabConnection.Login(an, u, p);

                if (ttu == null)
                    return;

                // TODO: Can be made async
                var linkedTruck = TruckKeyValue.GetByTruck(driver.CompanyId, defaultTruck.TruckId).FirstOrDefault();

                if (linkedTruck != null)
                {
                    if (model.CallId != null)
                    {
                        var call = await Entry.GetByIdAsync(model.CallId.Value);

                        if (call == null)
                            throw new TowbookException("invalid callid");

                        decimal lat = 0;
                        decimal longitude = 0;

                        if (call.Attributes.ContainsKey(Dispatch.AttributeValue.BUILTIN_DISPATCH_SOURCE_LATLONG) &&
                            call.Attributes[Dispatch.AttributeValue.BUILTIN_DISPATCH_SOURCE_LATLONG].Value.Contains(','))
                        {
                            string[] latLong = call.Attributes[Extric.Towbook.Dispatch.AttributeValue.BUILTIN_DISPATCH_SOURCE_LATLONG].Value.Split(',');
                            lat = Convert.ToInt64(latLong[0]);
                            longitude = Convert.ToInt64(latLong[1]);
                        }
                        else if (call.Waypoints.Where(o => o.Title == "Pickup" && o.Latitude != 0).Any())
                        {
                            var o = call.Waypoints.Where(rs => rs.Title == "Pickup" && rs.Latitude != 0).FirstOrDefault();

                            lat = o.Latitude;
                            longitude = o.Longitude;
                        }

                        StringBuilder sb = new StringBuilder();

                        sb.Append(call.Reason);
                        sb.AppendLine();
                        sb.AppendFormat("{0} {1} {2} {3}", call.Year, call.VehicleMake, call.VehicleModel, call.Color);
                        if (call.LicenseNumber != null)
                        {
                            sb.Append(", ");
                            sb.Append(call.LicenseNumber);
                        }
                        sb.AppendLine();

                        if (call.Account != null)
                        {
                            sb.Append(call.Account.Company);
                            sb.AppendLine();
                        }

                        if (!String.IsNullOrWhiteSpace(call.PurchaseOrderNumber))
                        {
                            sb.Append(call.PurchaseOrderNumber);
                            sb.AppendLine();
                        }

                        EntryContact contact = null;

                        if (call.Contacts.Any())
                        {
                            contact = call.Contacts.First();
                            sb.Append(contact.Name);

                            if (!String.IsNullOrWhiteSpace(contact.Phone))
                            {
                                sb.Append(", Phone # ");
                                sb.Append(Core.FormatPhone(contact.Phone));
                            }
                            sb.AppendLine();
                        }

                        if (!String.IsNullOrWhiteSpace(call.Notes))
                        {
                            sb.Append("Notes: ");
                            sb.Append(call.Notes);
                            sb.AppendLine();
                        }

                        // send to geotab unit.. if there is one.


                        var id = await ttu.SendLocationMessage(
                            call.CompanyId,
                            WebGlobal.CurrentUser != null ? WebGlobal.CurrentUser.Id : 1,
                            call.Id,
                            linkedTruck.Value,
                            sb.ToString(),
                            call.TowSource,
                            lat,
                            longitude);

                        //ttu.SendCall(ttdriver.CurrentUnitId, OrderType.PickupOrder, longitude, lat, TomTomUtility.ConvertToBase(call.CompanyId, 36) + "_" + call.CallNumber.ToString(),
                        //    sb.ToString(), call.TowSource, (contact != null ? contact.Name : null), (contact != null ? Core.FormatPhone(contact.Phone) : null));

                        logEvent.Properties.Add("message", sb.ToString());
                        logEvent.Properties.Add("source", call.TowSource);
                        logEvent.Properties.Add("latitude", lat);
                        logEvent.Properties.Add("longitude", longitude);
                        logEvent.Properties.Add("responseId", id);

                        logger.Log(logEvent);
                    }
                }
            }
            catch (Exception e)
            {
                logEvent.Message += " - FAILED";
                logEvent.Level = LogLevel.Fatal;
                logEvent.Exception = e;

                logger.Log(logEvent);
            }
        }

        [HttpPut]
        [Route("{id}/DefaultTruck")]
        public async Task<object> DefaultTruck(int id, DriverTruckAssignmentModel model)
        {
            var d = await Driver.GetByIdAsync(id);
            var t = await Truck.GetByIdAsync(model.TruckId);

            await ThrowIfNoCompanyAccessAsync(d?.Companies, "driver");

            if (model.TruckId != 0)
                await ThrowIfNoCompanyAccessAsync(t?.CompanyId, "truck");

            if (WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.Driver && d.UserId != WebGlobal.CurrentUser.Id)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                {
                    Content = new StringContent("You don't have permission to modify that drivers default truck.")
                });
            }
            
            var x = await DriverTruckDefault.GetByDriverIdAsync(d.Id) ?? new DriverTruckDefault();

            if (x.Id > 0 && model.TruckId != 0)
            {
                if (x.TruckId == model.TruckId)
                {
                    // the driver/truck pair isn't changing, so let's not do anything with it.
                    return DriverTruckAssignmentModel.Map(x);
                }
            }

            if (t != null)
            {
                // unassign all other drivers from this truck (trucks can only be assigned to one driver at a time)
                var otherDrivers = (await DriverTruckDefault.GetByTruckIdAsync(t.Id)).Where(o => o.DriverId != d.Id);
                foreach (var otherDriver in otherDrivers)
                {
                    // TODO: Not performant (n database/network calls on a loop)
                    await InternalDefaultTruck(otherDriver.DriverId, new DriverTruckAssignmentModel() { TruckId = 0 }, otherDriver.TruckId);
                }
            }

            return await InternalDefaultTruck(id, model, x.TruckId);
        }

        private async Task<object> InternalDefaultTruck(int id, DriverTruckAssignmentModel model, int originalTruckId)
        {
            var d = await Driver.GetByIdAsync(id);
            var t = await Truck.GetByIdAsync(model.TruckId);
            var x = await DriverTruckDefault.GetByDriverIdAsync(d.Id) ?? new DriverTruckDefault();

            x.DriverId = d.Id;
            x.SourceId = DriverTruckDefaultSource.DefaultTruckAPI;
            if (t != null)
            {
                x.TruckId = t.Id;
                await x.Save(WebGlobal.CurrentUser.Id);
            }
            else
            {
                await x.Delete(WebGlobal.CurrentUser.Id);
            }

            long queueItemId = 0;

            var aaaDrivers = await AaaDriver.GetByDriverIdAsync(d.Id);
            if (aaaDrivers.Any())
            {
                foreach (var each in aaaDrivers)
                {
                    // TODO: Not performant (n database/network calls on a loop)
                    var aac = await AaaContractor.GetByIdAsync(each.AaaContractorId);
                    if (aac != null)
                    {
                        // TODO: Not performant (n database/network calls on a loop)
                        queueItemId = await DigitalDispatchService.ShareDriverVehicleAssignment(aac.CompanyId, d.Id, t?.Id ?? 0, 
                            WebGlobal.CurrentUser.Id, originalTruckId);
                    }
                }
            }
            else
            {
                queueItemId = await DigitalDispatchService.ShareDriverVehicleAssignment(d.CompanyId, d.Id, t?.Id ?? 0, 
                    WebGlobal.CurrentUser.Id, originalTruckId);
            }

            if (t != null)
            {
                // TODO: move this to background service

                var up = (await UrgentlyProvider.GetByCompanyIdAsync(d.CompanyId)).FirstOrDefault();
                // TODO: Can be made async
                var urgently = Provider.GetByName("Urgent.ly").ProviderId;
                // TODO: Can be made async
                var truckKey = TruckKey.GetByProviderId(urgently, "Isregistered").Id;

                if (up != null && up.IsEnabled == true)
                {
                    // TODO: Can be made async
                    if (DriverKeyValue.GetByDriver(d.CompanyId, d.Id, urgently, "IsRegistered").FirstOrDefault()?.Value == "1")
                    {
                        // TODO: Can be made async
                        if (TruckKeyValue.GetByTruck(t.CompanyId, t.Id).Where(o => o.KeyId == truckKey)
                            .FirstOrDefault()?.Value == "1")
                        {
                            UrgentlyRestClient client = null;
                            if (new int[] { 10000, 100769, 100770, 100771, 154042 }.Contains(t.CompanyId))
                                client = UrgentlyRestClient.Get();
                            else
                                client = UrgentlyRestClient.GetProduction();
                            try
                            {
                                await client.TruckUpdateAsync(up.ProviderId, t.Id.ToString(),
                                    new Integrations.MotorClubs.Urgently.UrgentlyRestClient.Vehicle()
                                    {
                                        driverId = d.Id.ToString()
                                    });
                            }
                            catch
                            {

                            }
                        }
                    }
                }
            }

            var dtam = DriverTruckAssignmentModel.Map(x);

            // if we have a queue item id, attach it to the model so the client can listen 
            // for the completion of the operation or get notified of it's failure by a pusher event.
            if (queueItemId > 0)
                dtam.QueueItemId = queueItemId;

            return dtam;
        }

        private static void SaveLicenseInformation(Driver driver, DriverModel model)
        {
            if (model == null || model.Licenses == null || driver == null)
                return;

            if (driver.Id < 1)
                throw new TowbookException("Cannot save license information when driver.Id is less than 1.");

            foreach (var x in model.Licenses)
            {
                var tlkv = new DriverLicenseKeyValue();

                if (x.KeyId > 0)
                {
                    // TODO: Can be made async
                    var c = DriverLicenseKeyValue.GetByDriverId(model.Id, x.KeyId);

                    if (c != null)
                        tlkv = c;
                }
                tlkv.DriverId = driver.Id;
                tlkv.KeyId = x.KeyId;
                tlkv.Value = x.Value;

                if (string.IsNullOrWhiteSpace(tlkv.Value))
                    // TODO: Can be made async
                    tlkv.Delete(WebGlobal.CurrentUser);
                else
                    // TODO: Can be made async
                    tlkv.Save(WebGlobal.CurrentUser);
            }
        }

        /// <summary>
        /// Driver Features including GPS logic migrated from AJAX
        /// </summary>
        /// <param name="id"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        private async Task SaveDriverFeatures(int id, DriverModel model)
        {
            var driver = await Driver.GetByIdAsync(id);
            var companyId = driver.CompanyId;

            //Hamdle GPS Driver 
            if (model.GpsDriverId != null)
            {
                // Link Towbook driver to TomTom driver
                // TODO: Can be made async
                var keyDriverId = DriverKey.GetByProviderId(Provider.TomTom.ProviderId, "DriverId");

                // TODO: Can be made async
                var driverKeys = DriverKeyValue.GetByDriver(driver.CompanyId, driver.Id);

                var gpsId = driverKeys.FirstOrDefault(o => o.KeyId == keyDriverId.Id);

                if (gpsId == null)
                    gpsId = new DriverKeyValue() { KeyId = keyDriverId.Id, DriverId = driver.Id };

                gpsId.Value = model.GpsDriverId;

                // TODO: Can be made async
                gpsId.Save();

                // Get TomTom driver
                var ttu = TomTomUtility.GetInstance(WebGlobal.CurrentUser);
                if (ttu != null)
                {
                    // TODO: Can be made async
                    var gpsDriver = ttu.GetDrivers().FirstOrDefault(o => o.Id == model.GpsDriverId);
                    
                    if (gpsDriver != null)
                    {
                        gpsDriver.MobilePhone = driver.MobilePhone ?? "";
                        gpsDriver.Email = driver.Email ?? "";
                        // TODO: Can be made async
                        ttu.AddUpdateDriver(gpsDriver);
                    }
                }
            }

            //Features: AutoDispatch or DriverSchedules
            // TODO: Can be made async
            if (await WebGlobal.CurrentUser.Company.HasFeatureAsync(Extric.Towbook.Generated.Features.AutoDispatch) ||
            // TODO: Can be made async
              await WebGlobal.CurrentUser.Company.HasFeatureAsync(Extric.Towbook.Generated.Features.DriverSchedules))
            {
                if (model.ScheduleJson == null)
                    model.ScheduleJson = "[]";

                if (model.ScheduleJson != null)
                {
                    try
                    {
                        var schedule = Newtonsoft.Json.JsonConvert.DeserializeObject<DriverModel.ScheduleItem[]>(model.ScheduleJson);

                        // User-Local to Server(EST)
                        var swapped = DriverModel.ScheduleItem.OffsetDateTimes(schedule, true);

                        model.ScheduleJson = swapped.ToJson();

                        // take it from company-local to est
                        // TODO: Can be made async
                        SaveDriverKeyValue(driver.CompanyId, driver.Id, Provider.Towbook.ProviderId, "ScheduleJson", model.ScheduleJson);

                        var acc = (await Account.GetByCompanyAsync(await Company.Company.GetByIdAsync(WebGlobal.CurrentUser.CompanyId), AccountType.MotorClub))
                            .FirstOrDefault(o => o.MasterAccountId == MasterAccountTypes.AaaAcg ||
                                                 o.MasterAccountId == MasterAccountTypes.AaaNationalFsl);

                        if (acc == null)
                        {
                            acc = (await Account.GetByCompanyAsync(await Company.Company.GetByIdAsync(companyId), AccountType.MotorClub))
                                .FirstOrDefault(o => o.MasterAccountId == MasterAccountTypes.AaaAcg ||
                                                     o.MasterAccountId == MasterAccountTypes.AaaNationalFsl);
                        }

                        if (acc != null)
                        {
                            var aaaDrivers = await AaaDriver.GetByDriverIdAsync(driver.Id);
                            if (aaaDrivers.Any())
                            {
                                foreach (var each in aaaDrivers)
                                {
                                    var aac = await AaaContractor.GetByIdAsync(each.AaaContractorId);
                                    if (aac != null)
                                    {
                                        await DigitalDispatchService.HandleOutgoingEventAsync(
                                            aac.CompanyId,
                                            aac.AccountId,
                                            new DriverModel.ScheduleContainer()
                                            {
                                                DriverId = driver.Id,
                                                Json = model.ScheduleJson
                                            }.ToJson(),
                                            DigitalDispatchService.CallEventType.ShareSchedule,
                                            WebGlobal.CurrentUser.Id);
                                    }
                                }
                            }
                            else
                            {
                                await DigitalDispatchService.HandleOutgoingEventAsync(driver.CompanyId, acc.Id,
                                    new DriverModel.ScheduleContainer() { DriverId = driver.Id, Json = model.ScheduleJson }.ToJson(),
                                    DigitalDispatchService.CallEventType.ShareSchedule, WebGlobal.CurrentUser.Id);
                            }
                        }
                    }
                    catch (Exception e)
                    {
                        logger.LogExceptionEvent("Driver Details POST Digital Dispatch Error",
                            e,
                            WebGlobal.CurrentUser,
                            new LogEventInfo { Properties = { ["driverId"] = model.Id } });

                        throw;
                    }
                }

                // TODO: Can be made async
                SaveDriverKeyValue(driver.CompanyId, driver.Id, Provider.Towbook.ProviderId, "ScheduleAlwaysOn", model.ScheduleAlwaysOn);
                // TODO: Can be made async
                SaveDriverKeyValue(driver.CompanyId, driver.Id, Provider.Towbook.ProviderId, "ScheduleForceOff", model.ScheduleForceOff);
            }

            //Features: AutoDispatch
            // TODO: Can be made async
            if (await WebGlobal.CurrentUser.Company.HasFeatureAsync(Extric.Towbook.Generated.Features.AutoDispatch))
            {
                if (model.BlockedZips == null) model.BlockedZips = "";

                // TODO: Can be made async
                SaveDriverKeyValue(driver.CompanyId, driver.Id, Provider.Towbook.ProviderId, "BlockedZipCodesJson", model.BlockedZips);
                // TODO: Can be made async
                SaveDriverKeyValue(driver.CompanyId, driver.Id, Provider.Towbook.ProviderId, "Rating", model.Rating.ToString());

                // TODO: Can be made async
                if (model.ScheduleForceOff && await WebGlobal.CurrentUser.Company.HasFeatureAsync(Extric.Towbook.Generated.Features.DriverCheckIn))
                {
                    await Extric.Towbook.API.Controllers.UserController.CheckUserInOrOut(false, 0, 0, WebGlobal.CurrentUser, await Extric.Towbook.User.GetByIdAsync(driver.UserId));
                }

                foreach (var comp in (await CompanyUser.GetByUserIdAsync(driver.UserId)).Select(o => o.CompanyId))
                {
                    // TODO: Not performant (n database/network calls on a loop)
                    var qc = await ServiceBusHelper.CreateProducerQueueAsync("companyDispatchStatus");
                    var bm = new BrokeredMessage(new
                    {
                        companyId = comp,
                        type = "update"
                    }.ToJson());

                    // don't let these pile up.
                    bm.TimeToLive = TimeSpan.FromHours(4);

                    await qc.SendAsync(bm);
                }
            }
        }

        /// <summary>
        /// Finish mapping for Driver
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="model"></param>
        private async Task<DriverModel> FinishMapAsync(int companyId, DriverModel model)
        {
            
            if (await WebGlobal.CurrentUser.Company.HasFeatureAsync(Extric.Towbook.Generated.Features.AutoDispatch) ||
            
            await WebGlobal.CurrentUser.Company.HasFeatureAsync(Extric.Towbook.Generated.Features.DriverSchedules))
            {
                // TODO: Can be made async
                model.ScheduleJson = DriverKeyValue.GetByDriver(companyId, model.Id, Provider.Towbook.ProviderId, "ScheduleJson").FirstOrDefault()?.Value;

                if (model.ScheduleJson != null)
                    model.ScheduleJson = DriverModel.ScheduleItem.OffsetDateTimes(JsonConvert.DeserializeObject<DriverModel.ScheduleItem[]>(model.ScheduleJson), false).ToJson(true);

                // TODO: Can be made async
                model.ScheduleAlwaysOn = DriverKeyValue.GetByDriver(companyId, model.Id, Provider.Towbook.ProviderId, "ScheduleAlwaysOn")
                    .FirstOrDefault()?.Value == "1";

                // TODO: Can be made async
                model.ScheduleForceOff = DriverKeyValue.GetByDriver(companyId, model.Id,
                    Provider.Towbook.ProviderId, "ScheduleForceOff").FirstOrDefault()?.Value == "1";
            }

            if (await WebGlobal.CurrentUser.Company.HasFeatureAsync(Extric.Towbook.Generated.Features.AutoDispatch))
            {
                // TODO: Can be made async
                model.BlockedZips = DriverKeyValue.GetByDriver(companyId, model.Id,
                    Provider.Towbook.ProviderId, "BlockedZipCodesJson").FirstOrDefault()?.Value ?? "";
                // TODO: Can be made async
                model.Rating = Convert.ToInt32(DriverKeyValue.GetByDriver(companyId, model.Id, Provider.Towbook.ProviderId, "Rating").FirstOrDefault()?.Value);

                if (model.Rating == 0)
                    model.Rating = 1;
            }

            if (model.GpsDriverId != null)
            {
                // TODO: Can be made async
                var keyDriverId = DriverKey.GetByProviderId(Provider.TomTom.ProviderId, "DriverId");
                // TODO: Can be made async
                var driverKeys = DriverKeyValue.GetByDriver(companyId, model.Id);
                var gpsId = driverKeys.Where(o => o.KeyId == keyDriverId.Id).FirstOrDefault();

                if (gpsId != null)
                    model.GpsDriverId = gpsId.Value;
            }

            return model;
        }

        private static string InternalGetProfilePhotoUrl(int userId)
        {
            // TODO: Can be made async
            var user = Towbook.User.GetById(userId);
            if (user != null && user.HasProfilePhoto == true)
            {
                return $"/users/{userId}/profileImage";
            };

            return null;
        }

        #region Driver Key Values
        private void SaveDriverKeyValue(int companyId, int driverId, int providerId, string keyName, bool value)
        {
            // TODO: Can be made async
            var dkvFO = DriverKeyValue.GetByDriver(companyId, driverId,
                providerId, keyName).FirstOrDefault();

            if (dkvFO == null)
                dkvFO = new DriverKeyValue()
                {
                    DriverId = driverId,
                    // TODO: Can be made async
                    KeyId = DriverKey.GetByProviderId(Provider.Towbook.ProviderId, keyName).Id
                };

            dkvFO.Value = value ? "1" : "0";
            if (!(dkvFO.Value == "0" && dkvFO.Id == 0))
                // TODO: Can be made async
                dkvFO.Save();
        }

        private void SaveDriverKeyValue(int companyId, int driverId, int providerId, string keyName, string value)
        {
            // TODO: Can be made async
            var dkvFO = DriverKeyValue.GetByDriver(companyId, driverId,
                providerId, keyName).FirstOrDefault();

            if (dkvFO == null)
                dkvFO = new DriverKeyValue()
                {
                    DriverId = driverId,
                    // TODO: Can be made async
                    KeyId = DriverKey.GetByProviderId(Provider.Towbook.ProviderId, keyName).Id
                };

            dkvFO.Value = value;
            if (!(dkvFO.Value == "" && dkvFO.Id == 0))
                // TODO: Can be made async
                dkvFO.Save();
        }
        #endregion

        /// <summary>
        /// Update user contact information from driver
        /// </summary>
        /// <param name="driver"></param>
        private async Task UpdateUserContactInformation(Driver driver)
        {
            if (driver.UserId == 0)
                return;

            var userDetails = await UserDetail.GetByUserIdAsync(driver.UserId);

            if (userDetails == null) {
                userDetails = new UserDetail
                {
                    UserId = driver.UserId
                };
            }

            userDetails.Address = driver.Address;
            userDetails.City = driver.City;
            userDetails.State = driver.State;
            userDetails.Zip = driver.Zip;
            // TODO: Can be made async
            userDetails.Save();

            var user = await Towbook.User.GetByIdAsync(driver.UserId);

            if (driver.EmergencyContactName != null)
                user.EmergencyContactName = driver.EmergencyContactName;

            if (driver.EmergencyContactPhone != null)
                user.EmergencyContactPhone = driver.EmergencyContactPhone;

            if (driver.BirthDate != null)
                user.BirthDate = driver.BirthDate;

            await user.Save();

            await CacheWorkerUtility.UpdateUser(user);
        }
    }
}
