using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using Extric.Towbook.WebShared;
using System.IO;
using System.Threading.Tasks;
using System.Net.Http.Headers;
using Extric.Towbook.Storage;
using System.Drawing;
using Extric.Towbook.Impounds;
using System.Configuration;
using System.Collections.ObjectModel;
using Extric.Towbook.API.Models.Impounds;
using Extric.Towbook.Utility;
using static Extric.Towbook.API.ApiUtility;
using NLog;
using Extric.Towbook.Integration;
using Extric.Towbook.Integrations.Email;
using System.Net.Mail;
using HttpContextHelper = Extric.Towbook.Web.HttpContext;
using Extric.Towbook.API.Models.Calls;
using Extric.Towbook.API.Models.Email;
using Microsoft.AspNetCore.Mvc;


namespace Extric.Towbook.API.Impounds.Controllers
{
    [Route("impounds/{impoundId}/propertyReleases")]
    public class PropertyReleasesController : ControllerBase
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        /// Don't change this to GetDomain. The API runs from https://api.towbook.com AND https://app.towbook.com/api/ ... 
        /// GetDomain() returns api.towbook.com when accessing from api.towbook.com ... api.towbook.com/dispatch2/invoice.aspx doesn't exist.
        private static readonly string _domain = ConfigurationManager.AppSettings["WebAppUrl"] ?? "https://app.towbook.com";

        /// <summary>
        /// Get a list of property release forms for an impound.
        /// </summary>
        /// <param name="impoundId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("")]
        public async Task<IEnumerable<PropertyReleaseFormModel>> GetAsync(int impoundId)
        {
            var impound = await Impound.GetByIdAsync(impoundId);
            await ThrowIfNoCompanyAccessAsync(impound?.Company?.Id, "impound");
            var forms = await PropertyReleaseForm.GetByImpoundIdAsync(impoundId);
            return forms.Select(PropertyReleaseFormModel.Map);
        }

        /// <summary>
        /// Get data for the property release form.
        /// </summary>
        /// <param name="impoundId"></param>
        /// <param name="formId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("{formId}")]
        [Route("{formId}/get")]
        public async Task<PropertyReleaseFormModel> GetAsync(int impoundId, int formId)
        {
            var form = await InternalGetPropertyReleaseFormAsync(impoundId, formId, "Get");
            return PropertyReleaseFormModel.Map(form);
        }

        /// <summary>
        /// Get the pdf file of a completed property release form
        /// </summary>
        /// <param name="impoundId"></param>
        /// <param name="formId"></param>
        /// <returns></returns>
        /// <exception cref="TowbookException"></exception>
        [HttpGet]
        [Route("{formId}/pdf")]
        public async Task<HttpResponseMessage> Pdf(int impoundId, int formId)
        {
            var impound = await Impound.GetByIdAsync(impoundId);
            await ThrowIfNoCompanyAccessAsync(impound?.Company?.Id, "impound");
            // TODO: Can be made async
            var form = PropertyReleaseForm.GetById(formId);
            if (form.ImpoundId != impoundId)
            {
                throw new TowbookException("Forbidden");
            }

            if (form.Status != FormStatus.Complete)
            {
                throw new TowbookException("Form is not complete");
            }

            MemoryStream stream = new MemoryStream();
            var bytes = await GetFormBytes(impound.DispatchEntryId, formId);
            await stream.WriteAsync(bytes, 0, bytes.Length);

            stream.Position = 0;
            var response = new HttpResponseMessage(HttpStatusCode.OK);
            response.Content = new StreamContent(stream);
            response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/pdf");
            return response;

        }

        /// <summary>
        /// Create a new property release form with the attached signature and description from the url.
        /// The new property release form is saved to the call's files and returned as a pdf.
        /// </summary>
        /// <param name="impoundId"></param>
        /// <param name="notes"></param>
        /// <returns></returns>
        /// <remarks>
        /// This method expects a form file for a signature to be included in the request.
        /// </remarks>
        [HttpPost]
        [Route("")]
        public async Task<PropertyReleaseFormModel> Post(int impoundId, [FromQuery] string notes = "")
        {
            var impound = await Impound.GetByIdAsync(impoundId);

            await ThrowIfNoCompanyAccessAsync(impound?.Company?.Id, "impound");

            if (!Web.HttpContext.Current.Request.Form.Files.Any())
                throw new TowbookException("No signature attached.");

            var releaseForm = new PropertyReleaseForm()
            {
                CreateDate = DateTime.Now,
                ImpoundId = impoundId,
                Description = notes,
                OwnerUserId = WebGlobal.CurrentUser.Id,
                Status = FormStatus.Created
            };
            await releaseForm.Save();
            int formId = releaseForm.Id;

            var signatureFile = Web.HttpContext.Current.Request.Form.Files[0];
            
            var cn = new CompanyFile
            {
                CompanyId = WebGlobal.CurrentUser.CompanyId,
                OwnerUserId = WebGlobal.CurrentUser.Id,
                Filename = signatureFile.FileName,
                Size = (int)signatureFile.Length
            };
            
            cn.RawUrl = cn.Filename;
            cn.Description = "propertyReleaseFormId=" + formId;
            cn.PropertyReleaseForms = new Collection<int>() { formId };
            // TODO: Can be made async
            cn.Save();
            releaseForm.Signature = cn;
            
            string localPath = HttpContextHelper.MapPath(cn.LocalLocation);
            string dir = Path.GetDirectoryName(localPath);
            
            if (!Directory.Exists(dir))
            {
                Directory.CreateDirectory(dir);
            }
            
            using (var stream = new FileStream(localPath, FileMode.Create))
            {
                await signatureFile.CopyToAsync(stream);
            }
            
            if (!DefenderApi.IsFileUnsafe(localPath))
            {
                var sendFileRes = await FileUtility.SendFileAsync(localPath);
                if (sendFileRes.IsHttpSuccess())
                    System.IO.File.Delete(localPath);
            }
            else
            {
                await cn.DeleteAsync(WebGlobal.CurrentUser);
                System.IO.File.Delete(localPath);
                await LogEventAsync("Complete Forbidden Unsafe File", WebGlobal.CurrentUser, releaseForm, LogLevel.Error);
                throw new TowbookException("File is unsafe");
            }
            
            await LogEventAsync("Create Successful", WebGlobal.CurrentUser, releaseForm, LogLevel.Info);
            return PropertyReleaseFormModel.Map(releaseForm);
        }

        /// <summary>
        /// Upload a photo to associate with a not yet completed property release form.
        /// </summary>
        /// <param name="impoundId"></param>
        /// <param name="formId"></param>
        /// <returns></returns>
        /// <exception cref="TowbookException"></exception>
        [HttpPost]
        [Route("{formId}/photos")]
        public async Task<HttpResponseMessage> Photos(int impoundId, int formId)
        {
            var impound = await Impound.GetByIdAsync(impoundId);
            await ThrowIfNoCompanyAccessAsync(impound?.Company?.Id, "impound");
            // TODO: Can be made async
            var form = PropertyReleaseForm.GetById(formId);
            if (form.ImpoundId != impoundId)
            {
                await LogEventAsync("Photo Forbidden", WebGlobal.CurrentUser, form, LogLevel.Error);
                throw new TowbookException("Forbidden");
            }
            if (!Web.HttpContext.Current.Request.Form.Files.Any())
                throw new TowbookException("No photo attached.");
            
            var postedFile = HttpContextHelper.Current.Request.Form.Files[0];;
            
            var cn = new CompanyFile
            {
                CompanyId = WebGlobal.CurrentUser.CompanyId,
                OwnerUserId = WebGlobal.CurrentUser.Id,
                Filename = postedFile.FileName,
                RawUrl = postedFile.FileName,
                Size = (int)postedFile.Length,
                Description = "propertyReleaseFormId=" + formId,
                PropertyReleaseForms = new Collection<int>() { formId },
            };
            // TODO: Can be made async
            cn.Save();
            
            string localPath = HttpContextHelper.MapPath(cn.LocalLocation);
            string dir = Path.GetDirectoryName(localPath);
            
            if (!Directory.Exists(dir))
            {
                Directory.CreateDirectory(dir);
            }
            
            using (var stream = new FileStream(localPath, FileMode.Create))
            {
                await postedFile.CopyToAsync(stream);
            }
            
            if (!DefenderApi.IsFileUnsafe(localPath))
            {
                var sendFileRes = await FileUtility.SendFileAsync(localPath);
                if (sendFileRes.IsHttpSuccess())
                    System.IO.File.Delete(localPath);
            }
            else
            {
                await cn.DeleteAsync(WebGlobal.CurrentUser);
                System.IO.File.Delete(localPath);
                await LogEventAsync("Photos Forbidden Unsafe File", WebGlobal.CurrentUser, form, LogLevel.Error);
                throw new TowbookException("File is unsafe");
            }
            
            return new HttpResponseMessage(HttpStatusCode.OK);
        }

        /// <summary>
        /// Completes the property release form and saves generated pdf to the impound's files.
        /// </summary>
        /// <param name="impoundId"></param>
        /// <param name="formId"></param>
        /// <returns></returns>
        /// <exception cref="TowbookException"></exception>
        [HttpPost]
        [Route("{formId}/complete")]
        public async Task<PropertyReleaseFormModel> Complete(int impoundId, int formId)
        {
            var impound = await Impound.GetByIdAsync(impoundId);
            await ThrowIfNoCompanyAccessAsync(impound?.Company?.Id, "impound");
            // TODO: Can be made async
            var form = PropertyReleaseForm.GetById(formId);

            ThrowIfNotFound(form, "form");
            if (form.ImpoundId != impoundId)
            {
                await LogEventAsync("Complete Forbidden", WebGlobal.CurrentUser, form, LogLevel.Error);
                ThrowAccessDenied("form");
            }

            var reportStream = await GeneratePropertyReleasePdf(impoundId, impound.DispatchEntryId, form.Id);

            var fileName = $"propertyRelease_{WebGlobal.OffsetDateTime(DateTime.Now, true).ToString("yyyy_MM_dd")}.pdf";
            MemoryStream stream = new MemoryStream();
            await reportStream.CopyToAsync(stream);
            var releaseCompanyFile = new CompanyFile
            {
                CompanyId = WebGlobal.CurrentUser.CompanyId,
                OwnerUserId = WebGlobal.CurrentUser.Id,
                Filename = fileName,
                RawUrl = fileName,
                DispatchEntries = new Collection<int>() { impound.DispatchEntryId },
                Description = $"PropertyReleaseFormId {form.Id}",
                Size = (int)stream.Length,
            };
            // TODO: Can be made async
            releaseCompanyFile.Save();

            try
            {
                string pdfLocalPath = HttpContextHelper.MapPath(releaseCompanyFile.LocalLocation);
            
                var dir = Path.GetDirectoryName(pdfLocalPath);
                if (!Directory.Exists(dir))
                {
                     Directory.CreateDirectory(dir);
                }

                using (var fileStream = System.IO.File.Create(pdfLocalPath))
                {
                    stream.Seek(0, SeekOrigin.Begin);
                    await stream.CopyToAsync(fileStream);
                }
            
                if (!DefenderApi.IsFileUnsafe(pdfLocalPath))
                {
                    var result = await FileUtility.SendFileAsync(pdfLocalPath);
                    if (result.IsHttpSuccess())
                        System.IO.File.Delete(pdfLocalPath);
                }
                else
                {
                    System.IO.File.Delete(pdfLocalPath);
                    await releaseCompanyFile.DeleteAsync(WebGlobal.CurrentUser);
                    form.Status = FormStatus.Error;
                    await form.Save();
                    await LogEventAsync("Complete Forbidden Unsafe File", WebGlobal.CurrentUser, form, LogLevel.Error);
                    throw new TowbookException("The generated pdf was found to be unsafe. Please contact towbook support if you believe this is in error.");
                }
            }
            catch (Exception ex)
            {
                await releaseCompanyFile.DeleteAsync(WebGlobal.CurrentUser);
                form.Status = FormStatus.Error;
                await form.Save();
                await LogEventAsync("Complete Forbidden Unsafe File", WebGlobal.CurrentUser, form, LogLevel.Error);
                throw new TowbookException("The generated pdf was found to be unsafe. Please contact towbook support if you believe this is in error.");
            }
            
            
            form.Status = FormStatus.Complete;
            await form.Save();
            
            await LogEventAsync("Complete Successful", WebGlobal.CurrentUser, form, LogLevel.Info);
            return PropertyReleaseFormModel.Map(form);
        }


        public sealed class EmailModel
        {
            public List<string> Emails { get; set; }
        }

        /// <summary>
        /// Email the property release form to the specified email addresses.
        /// </summary>
        /// <param name="impoundId"></param>
        /// <param name="formId"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        /// <exception cref="TowbookException"></exception>
        [HttpPost]
        [Route("{formId}/email")]
        public async Task<HttpResponseMessage> Email(int impoundId, int formId, [FromBody] EmailModel model)
        {
            var impound = await Impound.GetByIdAsync(impoundId);
            await ThrowIfNoCompanyAccessAsync(impound?.Company?.Id, "impound");
            // TODO: Can be made async
            var form = PropertyReleaseForm.GetById(formId);
            ThrowIfNotFound(form, "form");
            if (form.ImpoundId != impoundId)
            {
                await LogEventAsync("Email Forbidden", WebGlobal.CurrentUser, form, LogLevel.Error);
                ThrowAccessDenied("form");
            }

            if (form.Status != FormStatus.Complete)
            {
                await LogEventAsync("Email Forbidden", WebGlobal.CurrentUser, form, LogLevel.Error);
                throw new TowbookException("Form is not complete");
            }

            var company = impound.DispatchEntry.Company;
            int companyId = impound.DispatchEntry.CompanyId;
            string companyName = company.Name;
            using (var sc = new SmtpClient().Get())
            {
                using (MailMessage message = new MailMessage())
                {
                    // From
                    // TODO: Can be made async
                    message.From = new MailAddress(EmailAddress.GetTowbookDotNetEmailAddressForCompany(companyId), companyName);

                    // TODO: Can be made async
                    var replytoEmail = AccountKeyValue.GetFirstValueOrNull(companyId, impound.Account.Id, Provider.Towbook.ProviderId, "ReplyToEmailAddress");
                    if (replytoEmail != null && Core.IsEmailValid(replytoEmail))
                    {
                        // Reply to
                        message.ReplyToList.Add(new MailAddress(replytoEmail, company.Name));
                    }
                    else
                    {
                        // TODO: Can be made async
                        if (CompanyKeyValue.GetFirstValueOrNull(companyId,
                            Provider.Towbook.ProviderId,
                            "Towbook_Calls_EmailReplyToFromUserEmail") == "1")
                        {
                            // Reply to
                            message.ReplyToList.Add(new MailAddress(WebGlobal.CurrentUser.Email, WebGlobal.CurrentUser.FullName));
                        }
                        else
                        {
                            // Reply to
                            message.ReplyToList.Add(new MailAddress(WebGlobal.CurrentUser.Company.Email, WebGlobal.CurrentUser.Company.Name));
                        }
                    }

                    foreach (string email in model.Emails)
                    {
                        message.To.Add(email);
                    }

                    if (message.To.Count == 0)
                    {
                        throw new TowbookException("There must be at least one valid email address to send an email. Check that the account has an email address.");
                    }

                    message.Subject = "Property Release Form for Impound " + impound.Id;

                    message.Body += "Attached is a PDF copy of your property release form provided by " + companyName + ".\n\n"
                        + "To view it, please open the attachment.\n\n"
                        + "If you cannot open the attachment, you can download Adobe Acrobat Reader from http://get.adobe.com/reader/\n\n"
                        + "\nIf you have any questions, please feel free to contact us.\n\n"
                        + companyName + "\n" + company.Phone + "\n" +
                        (!String.IsNullOrWhiteSpace(company.Website) ? company.Website + "\n\n" : "\n") +
                        "Sent using Towbook";


                    // Add attachment
                    System.IO.MemoryStream ms = new System.IO.MemoryStream();
                
                    var bytes = await GetFormBytes(impound.DispatchEntryId, formId);

                    await ms.WriteAsync(bytes, 0, bytes.Length);
                    // Send the PdfDocument to the client
                    ms.Position = 0;

                    var ct = new System.Net.Mime.ContentType(System.Net.Mime.MediaTypeNames.Application.Pdf);
                    var attach = new Attachment(ms, ct);
                    attach.ContentDisposition.FileName = $"PropertyRelease_Impound_{impoundId}.pdf";

                    message.Attachments.Add(attach);

                    // Send
                    await sc.Send(message, WebGlobal.CurrentUser, "PropertyReleaseForm", EmailType.PropertyReleaseForm, form.Id, new int[] { impound.DispatchEntryId });
                }
            }

            await LogEventAsync("Email Successful", WebGlobal.CurrentUser, form, LogLevel.Info);

            return new HttpResponseMessage(HttpStatusCode.OK);
        }

        /// <summary>
        /// Get the email history for a property release form.
        /// </summary>
        /// <param name="impoundId"></param>
        /// <param name="formId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("{formId}/emailHistory")]
        public async Task<List<EmailTransactionModel>> EmailHistoryAsync(int impoundId, int formId)
        {
            await InternalGetPropertyReleaseFormAsync(impoundId, formId, "Email History");

            return (await EmailTransaction.GetByPropertyReleaseFormIdAsync(formId)).Select(EmailTransactionModel.Map).ToList();
        }

        #region Helpers
        private static async Task<byte[]> GetFormBytes(int dispatchEntryId, int formId)
        {
            var files = await CompanyFile.GetByDispatchEntryIdAsync(dispatchEntryId);
            var pdf = files.FirstOrDefault(f => f.Description == $"PropertyReleaseFormId {formId}" || f.Description == $"PropertyReleaseFormId={formId}");
            if (pdf == null)
            {
                throw new TowbookException("Pdf not found");
            }

            string location = await FileUtility.GetFileAsync(pdf.LocalLocation);
            return await System.IO.File.ReadAllBytesAsync(location);
        }

        private static async Task<PropertyReleaseForm> InternalGetPropertyReleaseFormAsync(int impoundId, int formId, string actionName)
        {
            var impound = await Impound.GetByIdAsync(impoundId);
            await ThrowIfNoCompanyAccessAsync(impound?.Company?.Id, "impound");
            var form = PropertyReleaseForm.GetById(formId);
            if (form.ImpoundId != impoundId)
            {
                await LogEventAsync(actionName + " Forbidden", WebGlobal.CurrentUser, form, LogLevel.Error);
                throw new TowbookException("Forbidden");
            }

            return form;
        }


        private static async Task<Stream> GeneratePropertyReleasePdf(int impoundId, int callId, int releaseFormId)
        {
            // TODO: Can be made async
            string key = Core.MD5(callId + ":27ed2fb84d816");
            string url = $"{_domain}/impounds/PropertyRelease.aspx?id={impoundId}&type=invoice&key={key}&local=true&static=true&propertyReleaseId={releaseFormId}&pdf=1";
            return await WebGlobal.GetResponseFromUrlAsStreamAsync(url, false);
        }
        private static void LogEvent(string message, User currentUser, PropertyReleaseForm form, LogLevel logLevel)
        {
            Dictionary<string, object> logData = new Dictionary<string, object>
            {
                ["FormStatus"] = form.Status.ToString(),
                ["FormId"] = form.Id.ToString()
            };

            var logEvent = new LogEventInfo
            {
                LoggerName = logger.Name,
                Message = "Property Release: " + message,
                Level = logLevel,
                TimeStamp = DateTime.Now,
                Properties =
                {
                    ["companyId"] = currentUser.CompanyId,
                    ["companyName"] = Company.Company.GetById(currentUser.CompanyId).Name,
                    ["username"] = currentUser.Username,
                    ["json"] = logData.ToJson()
                }
            };

            logger.Log(logEvent);
        }

        private static async Task LogEventAsync(string message, User currentUser, PropertyReleaseForm form, LogLevel logLevel)
        {
            Dictionary<string, object> logData = new Dictionary<string, object>
            {
                ["FormStatus"] = form.Status.ToString(),
                ["FormId"] = form.Id.ToString()
            };

            var logEvent = new LogEventInfo
            {
                LoggerName = logger.Name,
                Message = "Property Release: " + message,
                Level = logLevel,
                TimeStamp = DateTime.Now,
                Properties =
                {
                    ["companyName"] = (await Company.Company.GetByIdAsync(currentUser.CompanyId)).Name,
                    ["username"] = currentUser.Username,
                    ["json"] = logData.ToJson()
                }
            };

            logger.Log(logEvent);
        }
        #endregion
    }
}
