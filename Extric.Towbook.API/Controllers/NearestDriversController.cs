using Microsoft.AspNetCore.Mvc;
using Extric.Towbook.API.Models;
using Extric.Towbook.Integration;
using Extric.Towbook.Integrations.TomTom;
using Extric.Towbook.WebShared;
using Extric.Towbook.Dispatch;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Extric.Towbook.API.Controllers
{

    [Route("nearestDrivers")]
    public class NearestDriversController : ControllerBase
    {
        /// <summary>
        /// Returns a list of drivers closest to the destination passed in.
        /// </summary>
        /// <returns></returns>
        /// <remarks>
        /// 1. Calls GetCurrentCalls API which is our MOST expensive query in the system from a DB perspective. 
        /// 2. Calls GetDrivers instead of GetDriversExact.
        /// 
        /// If we solve those two things we can make this api run MUCH faster. 
        /// 
        /// Driver.GetByUserId could use redis for some perf improvement 
        /// </remarks>
        //routes.MapHttpRoute(
        //    name: "DefaultApiGetSingle",
        //    routeTemplate: "{controller}/{action}",
        //    defaults: new { action = "Get" },
        //    constraints: new
        //    {
        //        httpMethod = new HttpMethodConstraint(new string[] { "GET" }),
        //    }
        //    ).DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
        [Route("")]
        [HttpGet]
        public async Task<IEnumerable<ClosestUnitModel>> GetAsync([FromQuery] decimal latitude, 
            [FromQuery] decimal longitude, 
            [FromQuery] int? callId = null,
            [FromQuery] bool? checkInForAllDrivers = false)
        {
            if (!await WebGlobal.CurrentUser.Company.HasFeatureAsync(Generated.Features.Mapping))
                return Array.Empty<ClosestUnitModel>();

            Company.Company[] companies = null;
            if (callId != null)
            {
                var entry = await Entry.GetByIdAsync(callId.Value);
                if (entry != null && await WebGlobal.CurrentUser.HasAccessToCompanyAsync(entry.CompanyId))
                {
                    companies = new[] { entry.Company };

                    var companyIds = (await Driver.GetByExactCompanyIdAsync(entry.CompanyId)).SelectMany(o => o.Companies).ToArray();

                    if (companyIds.Length < 10)
                    {
                        companies = (await Company.Company.GetByIdsAsync(companyIds)).ToArray();
                    }
                }
            }

            if (companies == null)
                companies = await WebGlobal.GetCompaniesAsync();

            // Get drivers for these companies
            // We only care about Id and Name. Maybe a better APi to use?
            var drivers = (await DriversController.InternalDriverModel.GetByCompaniesAsync(companies))
                .Where(o => o.IsActive()).ToList();
            System.Collections.ObjectModel.Collection<UserCheckInStatus> checkedIn = null;

            var list = new List<ClosestUnitModel>();

            var ttu = TomTomUtility.GetInstance(WebGlobal.CurrentUser);
            if (ttu != null)
            {
                if (checkInForAllDrivers == true)
                {
                    checkedIn = UserCheckInStatus.GetByUserIds(drivers.Select(d => d.LinkedUserId).Distinct().ToArray());
                }
                try
                {
                    // Calculate from TomTom data
                    list = ttu.GetClosestUnits(latitude, longitude)
                        .Select(ClosestUnitModel.Map).ToList();
                }
                catch
                { }
            }
            else
            {
                // Calculate from Towbook / Google Maps data
                var uhi = (await LocationController.GetCurrentLocations(companies)).ToArray();

                var obj = await Mapping.GMapUtil.GetMatrix(
                    string.Join("|", uhi.Select(o => o.Latitude + "," + o.Longitude)), latitude + "," + longitude);

                // Get user checkin statuses
                var checkInIds = checkInForAllDrivers == true 
                    ? drivers.Select(d => d.LinkedUserId).Distinct().ToArray() 
                    : uhi.Select(d => d.UserId).Distinct().ToArray();

                checkedIn = UserCheckInStatus.GetByUserIds(checkInIds);

                for (int i = 0; i < obj.Rows.Count; i++)
                {
                    var row = obj.Rows[i];
                    var cl = uhi[i];
                    var d = Driver.GetByUserId(cl.UserId);
                    if (d != null && d.Any())
                    {
                        if (row.Elements.First().Status != "ZERO_RESULTS" && row.Elements.First().Distance != null)
                        {
                            list.Add(new ClosestUnitModel()
                            {
                                DriverId = d.First().Id,
                                DriverName = d.First().Name,
                                TruckId = cl.TruckId,
                                Latitude = cl.Latitude,
                                Longitude = cl.Longitude,
                                EstimatedDistanceMiles = (decimal)row.Elements.First().Distance.Value / (decimal)1609.344,
                                EstimatedTimeSeconds = row.Elements.First().Duration.Value,
                                IsCheckedIn = checkedIn?.FirstOrDefault(f => cl?.UserId == f.UserId)?.IsCheckedIn,
                            });
                        }
                    }
                }
            }

            // Filter out any drivers not belonging to these companies
            list = list.Where(w => drivers.Any(d => d.Id == w.DriverId)).ToList();

            // Add drivers that weren't reporting GPS
            foreach (var driver in drivers)
            {
                if (list.All(d => d.DriverId != driver.Id))
                {
                    list.Add(new ClosestUnitModel()
                    {
                        DriverId = driver.Id,
                        DriverName = driver.Name,
                        IsCheckedIn = checkedIn?.FirstOrDefault(f => driver?.LinkedUserId == f.UserId)?.IsCheckedIn,
                    });
                }
            }
            // Get current calls
            var calls = Entry.GetCachedCurrentByCompany(companies.Select(y => y.Id).ToArray());



            // For each call
            foreach (var c in calls)
            {
                // If call is active
                if (c.StatusId < 5 || c.StatusId == 7)
                {
                    // For each driver assigned to this call
                    foreach (var d in list.Where(d => c.Drivers != null && c.Drivers.Contains(d.DriverId)))
                    {
                        if (c.EndingLocation != null)
                        {
                            // Add call to driver.calls
                            d.Calls.Add(new ClosestCallModel()
                            {
                                CallId = c.Id,
                                CallNumber = c.CallNumber,
                                Status = c.StatusId,
                                Latitude = c.EndingLocation.Latitude.GetValueOrDefault(),
                                Longitude = c.EndingLocation.Longitude.GetValueOrDefault(),
                                EstimatedDistanceMiles = 0,
                                EstimatedTimeSeconds = 0,
                            });
                        }
                    }
                }
            }

            // Get all the calls for all the drivers
            var driverCalls = list.SelectMany(d => d.Calls).Distinct().ToArray();

            // Get distances from those calls
            var obj2 = await Mapping.GMapUtil.GetMatrix(
                string.Join("|", driverCalls.Select(o => o.Latitude + "," + o.Longitude)), latitude + "," + longitude);

            for (int i = 0; i < obj2.Rows.Count; i++)
            {
                var row = obj2.Rows[i];
                var cl = driverCalls[i];

                // Find the driver calls, and set their est dist and est time
                foreach (var d in list)
                {
                    foreach (var dc in d.Calls)
                    {
                        if (dc.CallId == cl.CallId)
                        {
                            dc.EstimatedDistanceMiles = (decimal)(row.Elements?.FirstOrDefault()?.Distance?.Value ?? 0) / (decimal)1609.344;
                            dc.EstimatedTimeSeconds = row.Elements?.FirstOrDefault()?.Duration?.Value ?? 0;
                        }
                    }
                }
            }

            return list;
        }

        public class ClosestCallModel
        {
            public int CallId { get; set; }
            public int CallNumber { get; set; }
            public int Status { get; set; }
            public decimal Latitude { get; set; }
            public decimal Longitude { get; set; }
            public decimal EstimatedDistanceMiles { get; set; }
            public long EstimatedTimeSeconds { get; set; }
        }

        public class ClosestUnitModel
        {
            public int DriverId { get; set; }
            public string DriverName { get; set; }
            public int TruckId { get; set; }
            public decimal Latitude { get; set; }
            public decimal Longitude { get; set; }
            public decimal EstimatedDistanceMiles { get; set; }
            public long EstimatedTimeSeconds { get; set; }
            public bool? IsCheckedIn { get; set; }
            public List<ClosestCallModel> Calls { get; set; } = new List<ClosestCallModel>();

            public static ClosestUnitModel Map(ClosestUnit c)
            {
                int driverId = 0;
                var ttDriver = TomTomUtility.GetInstance(WebGlobal.CurrentUser).GetDrivers().FirstOrDefault(o => o.CurrentUnitId == c.objectuid);
                if (ttDriver != null)
                {
                    var kv = DriverKeyValue.GetByCompany(WebGlobal.CurrentUser.PrimaryCompanyId, Provider.TomTom.ProviderId)
                        .FirstOrDefault(o => o.KeyId == DriverKey.GetByProviderId(Provider.TomTom.ProviderId, "DriverId").Id &&
                                             o.Value == ttDriver.Id);
                    if (kv != null)
                        driverId = kv.DriverId;
                }

                return new ClosestUnitModel()
                {
                    DriverId = driverId,
                    Latitude = c.latitude / 1000000M,
                    Longitude = c.longitude / 1000000M,
                    EstimatedDistanceMiles = c.routedistance / 1609.344M,
                    EstimatedTimeSeconds = c.routetime
                };
            }
        }
    }

}
