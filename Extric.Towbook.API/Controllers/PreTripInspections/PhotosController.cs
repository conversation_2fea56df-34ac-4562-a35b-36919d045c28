using System.Collections.Generic;
using Extric.Towbook.API.PreTripInspections.Models;
using Extric.Towbook.PreTripInspections;
using Extric.Towbook.Storage;
using SkiaSharp;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Extric.Towbook.Utility;
using Extric.Towbook.WebShared;
using Extric.Towbook.WebShared.Multipart;
using Microsoft.AspNetCore.Mvc;
using static Extric.Towbook.API.ApiUtility;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.WebUtilities;
using System;
using Extric.Towbook.API.Accounts.Models;
using Microsoft.Net.Http.Headers;

namespace Extric.Towbook.API.PreTripInspections.Controllers
{
    [Route("pretripinspections")]
    public class PhotosController : ControllerBase
    {
        //routes.MapHttpRoute(
        //    name: "PreTripInspectionPhotos",
        //    routeTemplate: "pretripinspections/{preTripInspectionId}/photos",
        //    defaults: new { controller = "Photos", id = RouteParameter.Optional })
        //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.PreTripInspections.Controllers" } } };
        [HttpGet]
        [Route("{preTripInspectionId}/photos")]
        public IEnumerable<PreTripInspectionPhotoModel> Get(int preTripInspectionId)
        {
            return PreTripInspectionPhoto.Get(preTripInspectionId).Select(o => PreTripInspectionPhotoModel.Map(o));
        }

        //routes.MapHttpRoute(
        //    name: "PreTripInspectionPhoto",
        //   routeTemplate: "pretripinspections/{preTripInspectionId}/photos/{id}",
        //    defaults: new { controller = "Photos", id = RouteParameter.Optional })
        //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.PreTripInspections.Controllers" } } };
        [HttpGet]
        [Route("{preTripInspectionId}/photos/{id}")]
        public async Task<HttpResponseMessage> Get(int preTripInspectionId, int id)
        {
            var p = await PreTripInspection.GetByIdAsync(preTripInspectionId);
            var o = PreTripInspectionPhoto.GetById(id);

            if (p == null || o == null)
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound));

            var result = new HttpResponseMessage(HttpStatusCode.OK);
            result.Content = new StreamContent(new FileStream(await FileUtility.GetFileAsync(o.Location.Replace("%1", p.CompanyId.ToString())), FileMode.Open, FileAccess.Read, FileShare.ReadWrite));
            result.Content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue(o.ContentType);

            return result;
        }

        //routes.MapHttpRoute(
        //    name: "PreTripInspectionPhoto",
        //   routeTemplate: "pretripinspections/{preTripInspectionId}/photos",
        //    defaults: new { controller = "Photos", id = RouteParameter.Optional })
        //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.PreTripInspections.Controllers" } } };
        [HttpPost]
        [Route("{preTripInspectionId}/photos")]
        [DisableFormValueModelBinding]
        public async Task<ObjectResult> Post(int preTripInspectionId, [FromQuery] long photoType, [FromQuery] int? itemType = null)
        {
            //if (!Request.Content.IsMimeMultipartContent())
            if (!Web.HttpContext.Current.Request.HasFormContentType)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.UnsupportedMediaType));
            }

            int nPhotoType = 0;

            if (photoType == 1 || photoType == 2 || photoType == 3 || photoType == 4)
            {
                nPhotoType = (int)photoType;
            }
            else
            {
                nPhotoType = 1; // default to Item.
            }

            var p = PreTripInspection.GetById(preTripInspectionId);
            await ThrowIfNoCompanyAccessAsync(p?.CompanyId, "pre trip inspection");

            var o = new PreTripInspectionPhoto();

            // Save to temp file
            var provider = new MultipartFormDataStreamProvider(Path.GetTempPath());
            FileInfo fileInfo = null;

            FormOptions _defaultFormOptions = new FormOptions();

            var boundary = MultipartRequestHelper.GetBoundary(
                MediaTypeHeaderValue.Parse(Web.HttpContext.Current.Request.ContentType),
                _defaultFormOptions.MultipartBoundaryLengthLimit);

            var reader = new MultipartReader(boundary, Web.HttpContext.Current.Request.Body);

            var section = await reader.ReadNextSectionAsync();

            string targetFilePath = "";

            if (section != null)
            {
                var hasContentDispositionHeader =
                    ContentDispositionHeaderValue.TryParse(
                        section.ContentDisposition, out var contentDisposition);

                if (hasContentDispositionHeader)
                {
                    if (MultipartRequestHelper.HasFileContentDisposition(contentDisposition))
                    {
                        targetFilePath = Path.GetTempFileName();
                        using (var targetStream = System.IO.File.Create(targetFilePath))
                        {
                            await section.Body.CopyToAsync(targetStream);
                            fileInfo = new FileInfo(targetFilePath);
                            Console.WriteLine($"Copied the uploaded file '{targetFilePath}'");
                        }
                    }
                }
            }

            if (fileInfo == null || !fileInfo.Exists)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.InternalServerError)
                {
                    Content = new StringContent("Request is missing file data.")
                });
            }
            var tempFile = targetFilePath;
            var tempFileInfo = new FileInfo(tempFile);

            // Copy temp file and rename with new file name and correct extension
            try
            {
                var newFile = "";

                using (var i = SKBitmap.Decode(tempFileInfo.FullName))
                {
                    o.PreTripInspectionId = preTripInspectionId;
                    o.PhotoType = nPhotoType;
                    o.ItemType = itemType;
                    o.ContentType = "image/jpg";
                    o.OwnerUserId = WebGlobal.CurrentUser.Id;
                    o.Save();

                    newFile = o.Location.Replace("%1", p.CompanyId.ToString());

                    if (!Directory.Exists(Path.GetDirectoryName(newFile)))
                    {
                        Directory.CreateDirectory(Path.GetDirectoryName(newFile));
                    }

                    // Keep it proportionately correct, but don't allow any dimension to exceed 1920
                    using (var resized = i.ResizeProportionately(1920))
                        resized.Save(newFile, SKEncodedImageFormat.Jpeg);
                }

                var result = await FileUtility.SendFileAsync(newFile);
                if (result.IsHttpSuccess())
                    System.IO.File.Delete(newFile);

                return StatusCode((int)HttpStatusCode.Created, PreTripInspectionPhotoModel.Map(o));
            }
            finally
            {
                tempFileInfo.Delete();
            }
        }

    }
}
