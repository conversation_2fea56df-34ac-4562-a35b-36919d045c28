using System.Collections.Generic;
using System.Linq;
using System.Web.Http;
using Extric.Towbook.Company;
using Extric.Towbook.WebShared;
using Microsoft.AspNetCore.Mvc;

namespace Extric.Towbook.API.Controllers
{
    //TODO: Fix for merge, route endpoints
    public class ReasonGroupsCompanyController : ControllerBase
    {
        /// <summary>
        /// Get Reason Group Company Model
        /// </summary>
        /// <returns></returns>
        public IEnumerable<ReasonGroupCompanyModel> Get()
        {
            return CompanyReasonGroup.GetByCompanyId(WebGlobal.CurrentUser.CompanyId).Select(x => ReasonGroupCompanyModel.Map(x));
        }

        public class ReasonGroupCompanyModel
        {
            public string Key { get; set; }
            public int CompanyId { get; set; }
            public int DispatchReasonGroupId { get; set; }

            public static ReasonGroupCompanyModel Map(CompanyReasonGroup input)
            {
                return new ReasonGroupCompanyModel()
                {
                    Key = input.Key,
                    CompanyId = input.CompanyId,
                    DispatchReasonGroupId = input.DispatchReasonGroupId
                };
            }
        }
    }
}
