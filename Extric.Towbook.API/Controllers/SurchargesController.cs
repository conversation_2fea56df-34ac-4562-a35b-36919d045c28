using Extric.Towbook.Surcharges;
using Extric.Towbook.WebShared;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Collections.ObjectModel;
using Extric.Towbook.Company;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace Extric.Towbook.API.Controllers
{
    [Route("Surcharges")]
    public class SurchargesController : ControllerBase
    {
        /// <summary>
        /// Returns Surcharge rates, the account surcharge rates, and the exclusions.
        /// </summary>
        /// <returns></returns>
        ///
        [HttpGet]
        [Route("")]
        public async Task<List<CompanySurchargeModel>> Get()
        {
            var r = new List<CompanySurchargeModel>();

            foreach (var c in await this.GetCompaniesForRequestAsync())
            {
                foreach (var x in await GetSurchargesByCompanyAsync(c.Id))
                {
                    r.Add(x);
                }
            }

            return r;
        }

        public static async Task<List<CompanySurchargeModel>> GetSurchargesByCompanyAsync(int companyId)
        {
            SurchargeRate sr = null;

            var srExcluded = new Dictionary<int, RateItemExclusion>();
            var arieExcluded = new List<AccountRateItemExclusion>();

            sr = await SurchargeRate.GetBySurchargeAsync(Surcharge.SURCHARGE_FUEL, companyId);

            var surcharges = new List<SurchargeRate>();

            if (sr != null)
                surcharges.Add(sr);

            if (sr != null)
            {
                var cId = companyId;

                // AC 11/12/2020 - Share exclusions from parent when rateItems are shared with child
                if ((await WebGlobal.GetCompaniesAsync()).Length > 1)
                {
                    var sc = SharedCompany.GetByCompanyId(companyId).FirstOrDefault(w => w.SharedCompanyId == companyId);
                    if (sc != null && sc.ShareAllRateItems)
                        cId = sc.CompanyId;
                }

                // todo: this isnt' cached... this will impact peformance ... fix the internals of the RateItemExclusion method to use the cache.
                srExcluded = await RateItemExclusion.GetByCompanyAsync(cId, Surcharge.SURCHARGE_FUEL);
            }

            arieExcluded = (await AccountRateItemExclusion.GetByCompanyIdAsync(companyId)).Where(o => o.SurchargeId == Surcharge.SURCHARGE_FUEL).ToList();
            var accountRates = await SurchargeAccountRate.GetByCompanyAsync(companyId);

            return surcharges.Select(o => 
                    CompanySurchargeModel.Map(o, srExcluded, arieExcluded, accountRates)
                ).ToList();
        }
    }

    public sealed class CompanySurchargeModel
    {
        public int Id { get; set; }
        public int CompanyId { get; set; }
        public string Name { get; set; }
        public string Rate { get; set; }
        public bool Taxable { get; set; }
        public bool TreatExclusionsAsInclusions { get; set; }
        public bool ApplyToCustomInvoiceItems { get; set; }
        public bool ReadOnly { get; set; }
        public CompanySurchargeExclusions Exclusions { get; set; }
        public IEnumerable<CompanyAccountRateItemExclusion> Accounts { get; set; }

        public static CompanySurchargeModel Map(SurchargeRate o, 
            Dictionary<int, RateItemExclusion> srExcluded, 
            List<AccountRateItemExclusion> arieExcluded,
            Collection<SurchargeAccountRate> accountRates)
        {
            if (o == null)
                return null;

            if (srExcluded == null)
                srExcluded = new Dictionary<int, RateItemExclusion>();

            if (arieExcluded == null)
                arieExcluded = new List<AccountRateItemExclusion>();

            if (accountRates == null)
                accountRates = new Collection<SurchargeAccountRate>();

            return new CompanySurchargeModel()
            {
                Id = o.SurchargeId,
                CompanyId = o.CompanyId,
                Name = o.Surcharge.Name,
                Rate = o.Rate.ToString("0.0000"),
                Taxable = o.Taxable,
                TreatExclusionsAsInclusions = o.TreatExclusionsAsInclusions,
                ApplyToCustomInvoiceItems = o.ApplyToCustomInvoiceItems,
                ReadOnly = o.ReadOnly,
                Exclusions = new CompanySurchargeExclusions()
                {
                    RateItems = srExcluded.OrderBy(ob => ob.Value.RateItemId).Select(r => r.Key),
                    AccountRateItems = arieExcluded.Select(a => 
                        new AccountRateItemExclusion() { 
                            Id = a.Id, 
                            AccountId = a.AccountId, 
                            RateItemId = a.RateItemId, 
                            SurchargeId = a.SurchargeId, 
                            TreatAsInclusion = !o.TreatExclusionsAsInclusions && srExcluded.Select(s => s.Key).Contains(a.RateItemId) ? true : (bool?)null 
                        })
                },
                Accounts = accountRates
                    .Where(ars => ars.SurchargeId == o.SurchargeId)
                    .Select(ar => new CompanyAccountRateItemExclusion()
                    {
                        AccountId = ar.AccountId,
                        Rate = ar.Rate.ToString("0.0000"),
                        OverrideInclusions = ar.OverrideCompanyInclusions
                    })
            };
        }
    }

    public sealed class CompanySurchargeExclusions
    {
        public IEnumerable<int> RateItems { get; set; }
        public IEnumerable<AccountRateItemExclusion> AccountRateItems { get; set; }
    }

    public sealed class CompanyAccountRateItemExclusion
    {
        public int AccountId { get; set; }
        public string Rate { get; set; }
        public bool OverrideInclusions { get; set; }
    }
}
