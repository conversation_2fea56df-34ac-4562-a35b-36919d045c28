using System;
using System.Net;
using System.Net.Http;
using Extric.Towbook.Configuration;
using Extric.Towbook.Configuration.Logging;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

namespace Extric.Towbook.API
{
    public class Program
    {
        public static void Main(string[] args)
        {
            CreateHostBuilder(args).Build().Run();
        }

        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .ConfigureLogglyWithNLog()
                .ConfigureWebHostDefaults(webBuilder =>
                {
                    webBuilder.UseStartup<Startup>();
                    webBuilder.ConfigureKestrel(options =>
                    {
                        options.Listen(IPAddress.Any, 5000);
                        options.Listen(IPAddress.Any, 5001, listenOptions =>
                        {
                            //listenOptions.UseHttps("C:\\https\\aspnetapp.pfx",
                            //    "towbook");
                        });
                        options.Limits.MaxRequestBodySize = null;
                    });
                })
                .ConfigureAppConfiguration(configurationBuilder =>
                {
                    configurationBuilder.AddUserSecrets<Program>();
                })
                ;
    }
}
