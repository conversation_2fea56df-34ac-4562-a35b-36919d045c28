<?xml version="1.0" encoding="utf-8"?>
<definitions xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:xoajsb="http://ws.easylink.com/JobSubmit/2011/01" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:xoarr="http://ws.easylink.com/RequestResponse/2011/01" targetNamespace="http://ws.easylink.com/JobSubmit/2011/01" xmlns="http://schemas.xmlsoap.org/wsdl/">
  <types>
    <xs:schema attributeFormDefault="unqualified" elementFormDefault="qualified" targetNamespace="http://ws.easylink.com/JobSubmit/2011/01">
      <xs:simpleType name="BlackoutAddressType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="default" />
          <xs:enumeration value="fax" />
          <xs:enumeration value="internet" />
          <xs:enumeration value="mbox" />
          <xs:enumeration value="x400" />
          <xs:enumeration value="telex" />
          <xs:enumeration value="cablegram" />
          <xs:enumeration value="mailgram" />
          <xs:enumeration value="ddd" />
          <xs:enumeration value="dedLine" />
          <xs:enumeration value="softswitch" />
          <xs:enumeration value="voice" />
          <xs:enumeration value="sms" />
          <xs:enumeration value="fod" />
          <xs:enumeration value="list" />
          <xs:enumeration value="smQuery" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="DeDupOptionType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="default" />
          <xs:enumeration value="none" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="FaxBannerFxPlacementType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="outside" />
          <xs:enumeration value="inside_replace" />
          <xs:enumeration value="insideor" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="HourMinuteType">
        <xs:restriction base="xs:string">
          <xs:pattern value="(([0-1][0-9]|2[0-3]):([0-5][0-9]))|24:00" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="HTMLOpenTrackingType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="none" />
          <xs:enumeration value="top" />
          <xs:enumeration value="bottom" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="PageOrientationType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="portrait" />
          <xs:enumeration value="landscape" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="PilotlineStyleType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="default" />
          <xs:enumeration value="none" />
          <xs:enumeration value="type1" />
          <xs:enumeration value="type2" />
          <xs:enumeration value="type3" />
          <xs:enumeration value="type4" />
          <xs:enumeration value="type5" />
          <xs:enumeration value="type6" />
          <xs:enumeration value="type7" />
          <xs:enumeration value="type8" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="TreatmentType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="body" />
          <xs:enumeration value="attachment" />
          <xs:enumeration value="pullfile" />
          <xs:enumeration value="voice_all" />
          <xs:enumeration value="voice_live" />
          <xs:enumeration value="voice_voicemail" />
          <xs:enumeration value="voice_whisper" />
          <xs:enumeration value="voice_call_control" />
          <xs:enumeration value="ezBanner" />
          <xs:enumeration value="docfx" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="VoiceDeliveryMethodType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="silence" />
          <xs:enumeration value="PAMD" />
          <xs:enumeration value="live_only" />
          <xs:enumeration value="voicemail_only" />
          <xs:enumeration value="force_play" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="WeekDayType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="Sunday" />
          <xs:enumeration value="Monday" />
          <xs:enumeration value="Tuesday" />
          <xs:enumeration value="Wednesday" />
          <xs:enumeration value="Thursday" />
          <xs:enumeration value="Friday" />
          <xs:enumeration value="Saturday" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="BlackoutScheduleType">
        <xs:sequence>
          <xs:element maxOccurs="unbounded" name="WeekDayInterval" type="xoajsb:WeekDayIntervalType" />
        </xs:sequence>
        <xs:attribute name="addrType" type="xoajsb:BlackoutAddressType" />
      </xs:complexType>
      <xs:complexType name="CallControlType">
        <xs:sequence>
          <xs:element name="ControlKeyAction" type="xs:string" />
          <xs:element name="ControlKey" type="xoajsb:DTMFKey" />
          <xs:element minOccurs="0" name="ControlKeyInfo" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CallControlOptionsType">
        <xs:sequence>
          <xs:element minOccurs="0" name="UseCallControl" type="xs:boolean" />
          <xs:element minOccurs="0" name="CallControlMenuPosition" type="xs:string" />
          <xs:element minOccurs="0" name="OptOutConfirmation" type="xoajsb:YesNo" />
          <xs:element minOccurs="0" name="OptOutLevel" type="xs:string" />
          <xs:element minOccurs="0" name="PauseTimeout" type="xs:int" />
          <xs:element minOccurs="0" name="ReplayLimit" type="xs:int" />
          <xs:element minOccurs="0" name="RewindLimit" type="xs:int" />
          <xs:element minOccurs="0" name="TransferSilence" type="xs:int" />
          <xs:element minOccurs="0" maxOccurs="unbounded" name="CallControl" type="xoajsb:CallControlType" />
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="DatafeedOptionsType">
        <xs:choice>
          <xs:element name="DDSProcess" type="xoajsb:DDSProcessType" />
        </xs:choice>
      </xs:complexType>
      <xs:complexType name="DDSProcessType">
        <xs:attribute name="datafeedName" type="xs:string" use="required" />
      </xs:complexType>
      <xs:complexType name="DeDuplicateFieldType">
        <xs:simpleContent>
          <xs:extension base="xs:string">
            <xs:attribute name="segment" type="xs:string" />
          </xs:extension>
        </xs:simpleContent>
      </xs:complexType>
      <xs:complexType name="DeDuplicateType">
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="DeDuplicateField" type="xoajsb:DeDuplicateFieldType" />
        </xs:sequence>
        <xs:attribute name="deDupOption" type="xoajsb:DeDupOptionType" />
      </xs:complexType>
      <xs:complexType name="EncryptOptionsType">
        <xs:sequence>
          <xs:element minOccurs="0" name="EncryptDoc" type="xs:boolean" />
          <xs:element minOccurs="0" name="EncryptReportDoc" type="xs:boolean" />
          <xs:element minOccurs="0" name="EncryptRecipientList" type="xs:boolean" />
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="FaxBannerFxOptionType">
        <xs:sequence>
          <xs:element minOccurs="0" name="UseBannerFx" type="xoajsb:YesNo" />
          <xs:element minOccurs="0" name="BannerFxName" type="xs:string" />
          <xs:element minOccurs="0" name="BannerFxPlacement" type="xoajsb:FaxBannerFxPlacementType" />
          <xs:element minOccurs="0" name="BannerFxOnCover" type="xoajsb:YesNo" />
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="FaxCoversheetOptionType">
        <xs:all>
          <xs:element name="UseCoversheet" type="xoajsb:YesNo" />
          <xs:element minOccurs="0" name="CoversheetName">
            <xs:simpleType>
              <xs:restriction base="xs:string">
                <xs:whiteSpace value="collapse" />
              </xs:restriction>
            </xs:simpleType>
          </xs:element>
          <xs:element minOccurs="0" name="CoversheetTo" type="xoajsb:EncodableStringType" />
          <xs:element minOccurs="0" name="CoversheetFrom" type="xoajsb:EncodableStringType" />
          <xs:element minOccurs="0" name="CoversheetAttn" type="xoajsb:EncodableStringType" />
        </xs:all>
      </xs:complexType>
      <xs:complexType name="FaxPilotlineOptionType">
        <xs:sequence>
          <xs:element minOccurs="0" name="PilotlineStyle" type="xoajsb:PilotlineStyleType" />
          <xs:element name="PilotlineText" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="JobExtensionsType">
        <xs:sequence>
          <xs:element maxOccurs="unbounded" name="Segment" type="xoajsb:SegmentType" />
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="PreviewDataType">
        <xs:sequence>
          <xs:element name="Preview" type="xs:string" />
          <xs:element name="PreviewDocData" type="xoajsb:DocDataType" />
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="WeekDayIntervalType">
        <xs:sequence>
          <xs:element name="IntervalBegin" type="xoajsb:HourMinuteType" />
          <xs:element name="IntervalEnd" type="xoajsb:HourMinuteType" />
        </xs:sequence>
        <xs:attribute name="weekDay" type="xoajsb:WeekDayType" />
      </xs:complexType>
      <xs:complexType name="EnhancedEmailOptionsType">
        <xs:all>
          <xs:element minOccurs="0" name="ExpirationDays" type="xs:decimal" />
          <xs:element minOccurs="0" name="Subject" type="xoajsb:EncodableStringType" />
          <xs:element minOccurs="0" name="AutoSendFriend" type="xoajsb:YesNo" />
          <xs:element minOccurs="0" ref="xoajsb:CharacterSet" />
          <xs:element minOccurs="0" name="PullPassword" type="xs:string">
            <xs:annotation>
              <xs:documentation>job-global, can be overridden per-recipient</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="FromAddress" type="xs:string" />
          <xs:element minOccurs="0" name="FromDisplayName" type="xs:string" />
          <xs:element minOccurs="0" name="ReplyTo" type="xs:string" />
          <xs:element minOccurs="0" name="HTMLOpenTracking" type="xoajsb:HTMLOpenTrackingType" />
          <xs:element minOccurs="0" name="PasswordLifetime" type="xs:integer">
            <xs:annotation>
              <xs:documentation>lifetime of cookie to avoid re-entry of password</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="DeliveryRetryPattern" type="xs:string" />
          <xs:element minOccurs="0" name="EnhancedEmailJobExtensions" type="xoajsb:JobExtensionsType" />
        </xs:all>
      </xs:complexType>
      <xs:complexType name="EmailOptionsType">
        <xs:all>
          <xs:element minOccurs="0" name="Subject" type="xoajsb:EncodableStringType" />
          <xs:element minOccurs="0" name="DeliveryRetryPattern" type="xs:string" />
          <xs:element minOccurs="0" name="EmailJobExtensions" type="xoajsb:JobExtensionsType" />
        </xs:all>
      </xs:complexType>
      <xs:complexType name="FaxOptionsType">
        <xs:all>
          <xs:element minOccurs="0" name="FaxCoversheet" type="xoajsb:FaxCoversheetOptionType" />
          <xs:element minOccurs="0" name="Subject" type="xoajsb:EncodableStringType" />
          <xs:element minOccurs="0" name="Pilotline" type="xoajsb:FaxPilotlineOptionType" />
          <xs:element minOccurs="0" name="BannerFX" type="xoajsb:FaxBannerFxOptionType" />
          <xs:element minOccurs="0" name="FaxMode" type="xoajsb:FaxModeType" />
          <xs:element minOccurs="0" name="Letterhead" type="xs:string" />
          <xs:element minOccurs="0" name="PageOrientation" type="xoajsb:PageOrientationType" />
          <xs:element minOccurs="0" name="DeliveryRetryPattern" type="xs:string" />
          <xs:element minOccurs="0" name="FaxJobExtensions" type="xoajsb:JobExtensionsType" />
        </xs:all>
      </xs:complexType>
      <xs:complexType name="MailMergeFaxOptionsType">
        <xs:all>
          <xs:element minOccurs="0" name="Subject" type="xoajsb:EncodableStringType" />
          <xs:element minOccurs="0" name="Pilotline" type="xoajsb:FaxPilotlineOptionType" />
          <xs:element minOccurs="0" name="FaxMode" type="xoajsb:FaxModeType" />
          <xs:element minOccurs="0" name="PageOrientation" type="xoajsb:PageOrientationType" />
          <xs:element minOccurs="0" name="FieldMapping" type="xoajsb:FieldMappingType" />
          <xs:element minOccurs="0" name="DeliveryRetryPattern" type="xs:string" />
          <xs:element minOccurs="0" name="MailMergeFaxJobExtensions" type="xoajsb:JobExtensionsType" />
        </xs:all>
      </xs:complexType>
      <xs:complexType name="VoiceOptionsType">
        <xs:all>
          <xs:element minOccurs="0" name="VoiceDeliveryMethod" type="xoajsb:VoiceDeliveryMethodType" />
          <xs:element minOccurs="0" name="RecipientTimezoneOption" type="xoajsb:YesNo" />
          <xs:element minOccurs="0" name="ANI" type="xs:string" />
          <xs:element minOccurs="0" name="ForcePlayTimer" type="xs:int" />
          <xs:element minOccurs="0" name="CallControlOptions" type="xoajsb:CallControlOptionsType" />
          <xs:element minOccurs="0" name="DeliveryRetryPattern" type="xs:string" />
          <xs:element minOccurs="0" name="VoiceJobExtensions" type="xoajsb:JobExtensionsType" />
        </xs:all>
      </xs:complexType>
      <xs:complexType name="SmsOptionsType">
        <xs:all>
          <xs:element minOccurs="0" name="ExpirationDays" type="xs:decimal" />
          <xs:element minOccurs="0" ref="xoajsb:CharacterSet" />
          <xs:element minOccurs="0" name="DeliveryRetryPattern" type="xs:string" />
          <xs:element minOccurs="0" name="SmsJobExtensions" type="xoajsb:JobExtensionsType" />
        </xs:all>
      </xs:complexType>
      <xs:complexType name="JobOptionsType">
        <xs:sequence>
          <xs:annotation>
            <xs:documentation>FaxOptions, EnhancedEmailOptions, VoiceOptions, and SmsOptions may coexist; MailMergeFaxOptions and EmailOptions can only appear by themselves</xs:documentation>
          </xs:annotation>
          <xs:element minOccurs="0" name="BillingCode" type="xoajsb:EncodableStringType" />
          <xs:element minOccurs="0" name="CustomerReference" type="xoajsb:EncodableStringType" />
          <xs:element minOccurs="0" name="Delivery" type="xoajsb:DeliveryType" />
          <xs:element minOccurs="0" name="EncryptOptions" type="xoajsb:EncryptOptionsType" />
          <xs:element minOccurs="0" name="DatafeedOptions" type="xoajsb:DatafeedOptionsType" />
          <xs:element minOccurs="0" name="Domain" type="xs:string" />
          <xs:element minOccurs="0" name="PriorityBoost" type="xs:boolean" />
          <xs:element minOccurs="0" name="DeDuplicate" type="xoajsb:DeDuplicateType" />
          <xs:element minOccurs="0" name="DeliveryBlackout">
            <xs:complexType>
              <xs:sequence>
                <xs:element maxOccurs="unbounded" name="BlackoutSchedule" type="xoajsb:BlackoutScheduleType" />
              </xs:sequence>
            </xs:complexType>
          </xs:element>
          <xs:element minOccurs="0" name="EnhancedJobOptions">
            <xs:complexType>
              <xs:sequence>
                <xs:element minOccurs="0" name="CallingWindowRetry" type="xoajsb:YesNo" />
                <xs:element minOccurs="0" name="FaxDetection" type="xoajsb:YesNo" />
                <xs:element minOccurs="0" name="IVRMode" type="xs:string" />
                <xs:element minOccurs="0" name="PINExpiration" type="xs:string" />
                <xs:element minOccurs="0" name="AttemptLogging" type="xoajsb:YesNo" />
                <xs:element minOccurs="0" name="CDCJob" type="xoajsb:YesNo" />
                <xs:element minOccurs="0" name="AnsweringMachineLogic" type="xs:string">
                  <xs:annotation>
                    <xs:documentation>msgmsg, hangupall, hangupmsg, mgshangup</xs:documentation>
                  </xs:annotation>
                </xs:element>
                <xs:element minOccurs="0" name="LineItemCanc" type="xoajsb:YesNo" />
                <xs:element minOccurs="0" name="TransferTimeout" type="xs:int" />
                <xs:element minOccurs="0" name="AppType" type="xs:string" />
                <xs:element minOccurs="0" name="PhoneTypeLists" type="xs:string" />
              </xs:sequence>
            </xs:complexType>
          </xs:element>
          <xs:element minOccurs="0" name="MultiMode" type="xs:boolean" />
          <xs:element minOccurs="0" name="FaxOptions" type="xoajsb:FaxOptionsType" />
          <xs:element minOccurs="0" name="EnhancedEmailOptions" type="xoajsb:EnhancedEmailOptionsType" />
          <xs:element minOccurs="0" name="VoiceOptions" type="xoajsb:VoiceOptionsType" />
          <xs:element minOccurs="0" name="SmsOptions" type="xoajsb:SmsOptionsType" />
          <xs:element minOccurs="0" name="MailMergeFaxOptions" type="xoajsb:MailMergeFaxOptionsType" />
          <xs:element minOccurs="0" name="EmailOptions" type="xoajsb:EmailOptionsType" />
          <xs:element minOccurs="0" name="JobExtensions" type="xoajsb:JobExtensionsType" />
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ReportOptionsType">
        <xs:all>
          <xs:element minOccurs="0" name="DeliveryReport">
            <xs:annotation>
              <xs:documentation>
		  This item has different meanings for different
		  systems. For faxREACH and voiceREACH, it means
		  completion report, i.e. report sent after the job
		  completes. For messageREACH, this means posting
		  report, i.e. report sent after job is posted. To
		  order a completion report for messageREACH, one must
		  specify the "FinalReport" element.
	       </xs:documentation>
            </xs:annotation>
            <xs:complexType>
              <xs:sequence>
                <xs:element name="DeliveryReportType" type="xoajsb:MainReportTypeEnum" />
                <xs:element minOccurs="0" name="ReportAddress" type="xoajsb:DeliveryItemListType" />
                <xs:element minOccurs="0" name="ReportTemplate" type="xs:string" />
              </xs:sequence>
            </xs:complexType>
          </xs:element>
          <xs:element minOccurs="0" name="FriendReport">
            <xs:complexType>
              <xs:sequence>
                <xs:element name="FriendReportType" type="xoajsb:FriendReportTypeEnum" />
                <xs:element minOccurs="0" name="ReportAddress" type="xoajsb:DeliveryItemListType" />
                <xs:element minOccurs="0" name="FriendReportTemplate" type="xs:string" />
              </xs:sequence>
            </xs:complexType>
          </xs:element>
          <xs:element minOccurs="0" name="FinalReport">
            <xs:complexType>
              <xs:sequence>
                <xs:element name="ReportType" type="xoajsb:AllReportTypeEnum" />
                <xs:element minOccurs="0" name="ReportAddress" type="xoajsb:DeliveryItemListType" />
                <xs:element minOccurs="0" name="ReportTemplate" type="xs:string" />
              </xs:sequence>
            </xs:complexType>
          </xs:element>
          <xs:element minOccurs="0" name="ProgressReport">
            <xs:complexType>
              <xs:sequence>
                <xs:element name="ProgressReportType" type="xoajsb:AllReportTypeEnum" />
                <xs:element minOccurs="0" name="ReportAddress" type="xoajsb:DeliveryItemListType" />
                <xs:element minOccurs="0" name="ProgressReportTemplate" type="xs:string" />
                <xs:element minOccurs="0" name="ProgressReportIntervals" type="xs:string">
                  <xs:annotation>
                    <xs:documentation>sequence of minute intervals</xs:documentation>
                  </xs:annotation>
                </xs:element>
                <xs:element minOccurs="0" name="ProgressReportBase" type="xs:dateTime" />
              </xs:sequence>
            </xs:complexType>
          </xs:element>
        </xs:all>
      </xs:complexType>
      <xs:complexType name="PullfileOptionsType">
        <xs:all>
          <xs:element minOccurs="0" name="Security" type="xoajsb:SecurityType" />
          <xs:element minOccurs="0" name="AutoPull" type="xoajsb:YesNo" />
          <xs:element minOccurs="0" name="PasswordNeeded" type="xoajsb:YesNo" />
          <xs:element minOccurs="0" name="PasswordLimit" type="xs:int" />
        </xs:all>
      </xs:complexType>
      <xs:complexType name="ContentPartType">
        <xs:sequence>
          <xs:choice>
            <xs:element name="DocRef" type="xs:string">
              <xs:annotation>
                <xs:documentation>must refer to a document in document_set</xs:documentation>
              </xs:annotation>
            </xs:element>
            <xs:element name="Document" type="xoajsb:DocumentType" />
          </xs:choice>
          <xs:element minOccurs="0" name="Treatment" type="xoajsb:TreatmentType" />
          <xs:element minOccurs="0" name="PullfileOptions" type="xoajsb:PullfileOptionsType" />
          <xs:element minOccurs="0" maxOccurs="unbounded" name="AddrTypeUse" type="xoajsb:AddressType" />
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ContentsType">
        <xs:sequence>
          <xs:element maxOccurs="unbounded" name="Part" type="xoajsb:ContentPartType" />
          <xs:element minOccurs="0" maxOccurs="unbounded" name="DynamicContent">
            <xs:complexType>
              <xs:sequence>
                <xs:element name="SectionName" type="xoajsb:EncodableStringType" />
                <xs:element maxOccurs="unbounded" name="SectionDocument">
                  <xs:complexType>
                    <xs:sequence>
                      <xs:element minOccurs="0" name="DeliveryFormat" type="xs:string" />
                      <xs:choice>
                        <xs:element name="DocRef" />
                        <xs:element name="Document" type="xoajsb:DocumentType" />
                      </xs:choice>
                    </xs:sequence>
                  </xs:complexType>
                </xs:element>
              </xs:sequence>
            </xs:complexType>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="MessageType">
        <xs:sequence>
          <xs:element minOccurs="0" name="MessageId" type="xs:string" />
          <xs:element name="JobOptions" type="xoajsb:JobOptionsType" />
          <xs:element name="Destinations" type="xoajsb:DeliveryItemListType" />
          <xs:element minOccurs="0" name="Reports" type="xoajsb:ReportOptionsType" />
          <xs:element name="Contents" type="xoajsb:ContentsType" />
          <xs:element minOccurs="0" name="Preview" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="MessageResultType">
        <xs:sequence>
          <xs:element minOccurs="0" name="MessageId" type="xs:string" />
          <xs:element name="Status" type="xoajsb:StatusType" />
          <xs:element minOccurs="0" name="JobId" type="xoajsb:JobIdType" />
          <xs:element minOccurs="0" name="PreviewData" type="xoajsb:PreviewDataType" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="JobSubmitRequest">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="SubmitId" type="xs:string" />
            <xs:element minOccurs="0" name="DocumentSet" type="xoajsb:DocumentSetType" />
            <xs:element maxOccurs="unbounded" name="Message" type="xoajsb:MessageType" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="JobSubmitResult">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="Status" type="xoajsb:StatusType" />
            <xs:element minOccurs="0" name="SubmitId" type="xs:string" />
            <xs:element minOccurs="0" maxOccurs="unbounded" name="MessageResult" type="xoajsb:MessageResultType" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:simpleType name="AddressType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="fax" />
          <xs:enumeration value="internet" />
          <xs:enumeration value="mbox" />
          <xs:enumeration value="x400" />
          <xs:enumeration value="telex" />
          <xs:enumeration value="cablegram" />
          <xs:enumeration value="mailgram" />
          <xs:enumeration value="ddd" />
          <xs:enumeration value="dedLine" />
          <xs:enumeration value="softswitch" />
          <xs:enumeration value="voice" />
          <xs:enumeration value="sms" />
          <xs:enumeration value="fod" />
          <xs:enumeration value="list" />
          <xs:enumeration value="smQuery" />
          <xs:enumeration value="URL" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="EformatType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="text" />
          <xs:enumeration value="html" />
          <xs:enumeration value="htmllite" />
          <xs:enumeration value="default" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="EmailType">
        <xs:restriction base="xs:string">
          <xs:minLength value="2" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="InsertListType">
        <xs:sequence>
          <xs:element maxOccurs="unbounded" name="Insert">
            <xs:complexType>
              <xs:simpleContent>
                <xs:extension base="xs:string">
                  <xs:attribute name="number" type="xs:integer" />
                  <xs:attribute name="b64charset" type="xs:string" use="optional" />
                </xs:extension>
              </xs:simpleContent>
            </xs:complexType>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ExtensionType">
        <xs:choice maxOccurs="unbounded">
          <xs:element name="Segment" type="xoajsb:SegmentType" />
        </xs:choice>
      </xs:complexType>
      <xs:complexType name="FieldMappingType">
        <xs:sequence>
          <xs:element maxOccurs="unbounded" name="Map">
            <xs:complexType>
              <xs:sequence>
                <xs:element name="FieldName" type="xs:string" />
                <xs:element minOccurs="0" name="SegmentName" type="xs:string" />
                <xs:element name="PropertyName" type="xs:string" />
                <xs:element minOccurs="0" name="IsBase64Encoded" type="xs:boolean" />
              </xs:sequence>
            </xs:complexType>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="DeliveryItemType" abstract="true">
        <xs:attribute name="ref" type="xs:string" use="optional" />
        <xs:attribute name="refb64" type="xs:string" use="optional" />
      </xs:complexType>
      <xs:complexType name="DeliveryItemGenericType">
        <xs:sequence>
          <xs:element minOccurs="0" name="Alternate" type="xoajsb:AlternateType" />
          <xs:element minOccurs="0" name="InsertList" type="xoajsb:InsertListType" />
          <xs:element minOccurs="0" name="Extension" type="xoajsb:ExtensionType" />
          <xs:element name="Address" type="xs:string" />
        </xs:sequence>
        <xs:attribute name="ref" type="xs:string" use="optional" />
        <xs:attribute name="refb64" type="xs:string" use="optional" />
        <xs:attribute name="type" type="xoajsb:AddressType" use="required" />
      </xs:complexType>
      <xs:complexType name="MboxType">
        <xs:sequence>
          <xs:element minOccurs="0" name="Alternate" type="xoajsb:AlternateType" />
          <xs:element name="UserId" type="xs:string" />
        </xs:sequence>
        <xs:attribute name="ref" type="xs:string" use="optional" />
        <xs:attribute name="refb64" type="xs:string" use="optional" />
      </xs:complexType>
      <xs:complexType name="TelexType">
        <xs:sequence>
          <xs:element minOccurs="0" name="Alternate" type="xoajsb:AlternateType" />
          <xs:element name="Number" type="xs:string" />
        </xs:sequence>
        <xs:attribute name="ref" type="xs:string" use="optional" />
        <xs:attribute name="refb64" type="xs:string" use="optional" />
      </xs:complexType>
      <xs:complexType name="FodType">
        <xs:sequence>
          <xs:element minOccurs="0" name="Alternate" type="xoajsb:AlternateType" />
          <xs:element name="Address" type="xs:string" />
        </xs:sequence>
        <xs:attribute name="ref" type="xs:string" use="optional" />
        <xs:attribute name="refb64" type="xs:string" use="optional" />
      </xs:complexType>
      <xs:complexType name="SmsType">
        <xs:sequence>
          <xs:element minOccurs="0" name="Alternate" type="xoajsb:AlternateType" />
          <xs:element name="Phone" type="xs:string" />
        </xs:sequence>
        <xs:attribute name="ref" type="xs:string" use="optional" />
        <xs:attribute name="refb64" type="xs:string" use="optional" />
      </xs:complexType>
      <xs:complexType name="FaxType">
        <xs:sequence>
          <xs:element minOccurs="0" name="Alternate" type="xoajsb:AlternateType" />
          <xs:element minOccurs="0" name="InsertList" type="xoajsb:InsertListType" />
          <xs:element minOccurs="0" name="Extension" type="xoajsb:ExtensionType" />
          <xs:element name="Phone" type="xs:string" />
          <xs:element minOccurs="0" name="Att" type="xoajsb:EncodableStringType" />
          <xs:element minOccurs="0" name="From" type="xoajsb:EncodableStringType" />
          <xs:element minOccurs="0" name="To" type="xoajsb:EncodableStringType" />
          <xs:element minOccurs="0" name="Salutation" type="xs:string" />
        </xs:sequence>
        <xs:attribute name="ref" type="xs:string" use="optional" />
        <xs:attribute name="refb64" type="xs:string" use="optional" />
      </xs:complexType>
      <xs:complexType name="VoiceType">
        <xs:sequence>
          <xs:element minOccurs="0" name="Alternate" type="xoajsb:AlternateType" />
          <xs:element minOccurs="0" name="InsertList" type="xoajsb:InsertListType" />
          <xs:element minOccurs="0" name="Extension" type="xoajsb:ExtensionType" />
          <xs:element name="Phone" type="xs:string" />
        </xs:sequence>
        <xs:attribute name="ref" type="xs:string" use="optional" />
        <xs:attribute name="refb64" type="xs:string" use="optional" />
      </xs:complexType>
      <xs:complexType name="InternetType">
        <xs:sequence>
          <xs:element minOccurs="0" name="Alternate" type="xoajsb:AlternateType" />
          <xs:element minOccurs="0" name="InsertList" type="xoajsb:InsertListType" />
          <xs:element minOccurs="0" name="Extension" type="xoajsb:ExtensionType" />
          <xs:element name="Email" type="xoajsb:EmailType" />
          <xs:element minOccurs="0" name="Subject" type="xoajsb:EncodableStringType" />
          <xs:element minOccurs="0" name="From" type="xoajsb:EncodableStringType" />
          <xs:element minOccurs="0" name="Eformat" type="xoajsb:EformatType" />
          <xs:element minOccurs="0" name="Password" type="xs:string">
            <xs:annotation>
              <xs:documentation>
			Only used in messageREACH. The password used
			by this recipient to retrieve pull
			attachments.
		     </xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
        <xs:attribute name="ref" type="xs:string" use="optional" />
        <xs:attribute name="refb64" type="xs:string" use="optional" />
      </xs:complexType>
      <xs:complexType name="ListType">
        <xs:sequence>
          <xs:element name="Name" type="xs:string" />
        </xs:sequence>
        <xs:attribute name="ref" type="xs:string" use="optional" />
        <xs:attribute name="refb64" type="xs:string" use="optional" />
        <xs:attribute name="ownershipLevel" type="xoajsb:OwnershipLevelType" />
      </xs:complexType>
      <xs:complexType name="SmQueryType">
        <xs:sequence>
          <xs:element name="Name" type="xs:string" />
        </xs:sequence>
        <xs:attribute name="ref" type="xs:string" use="optional" />
        <xs:attribute name="refb64" type="xs:string" use="optional" />
        <xs:attribute name="ownershipLevel" type="xoajsb:OwnershipLevelType" />
      </xs:complexType>
      <xs:complexType name="TableType">
        <xs:sequence>
          <xs:element minOccurs="0" name="FieldMapping" type="xoajsb:FieldMappingType" />
          <xs:choice>
            <xs:element name="DocRef" type="xs:string" />
            <xs:element name="Document" type="xoajsb:DocumentType" />
          </xs:choice>
        </xs:sequence>
        <xs:attribute name="ref" type="xs:string" use="optional" />
        <xs:attribute name="refb64" type="xs:string" use="optional" />
      </xs:complexType>
      <xs:complexType name="DeliveryItemListType">
        <xs:choice maxOccurs="unbounded">
          <xs:element name="List" type="xoajsb:ListType" />
          <xs:element name="SmQuery" type="xoajsb:SmQueryType" />
          <xs:element name="Table" type="xoajsb:TableType" />
          <xs:element name="DeliveryItemGeneric" type="xoajsb:DeliveryItemGenericType" />
          <xs:element name="Fax" type="xoajsb:FaxType" />
          <xs:element name="Internet" type="xoajsb:InternetType" />
          <xs:element name="Voice" type="xoajsb:VoiceType" />
          <xs:element name="Fod" type="xoajsb:FodType" />
          <xs:element name="Mbox" type="xoajsb:MboxType" />
          <xs:element name="Sms" type="xoajsb:SmsType" />
          <xs:element name="Telex" type="xoajsb:TelexType" />
        </xs:choice>
      </xs:complexType>
      <xs:complexType name="AlternateType">
        <xs:choice>
          <xs:element name="Fax" type="xoajsb:FaxType" />
          <xs:element name="Internet" type="xoajsb:InternetType" />
          <xs:element name="Voice" type="xoajsb:VoiceType" />
          <xs:element name="Fod" type="xoajsb:FodType" />
          <xs:element name="Mbox" type="xoajsb:MboxType" />
          <xs:element name="Sms" type="xoajsb:SmsType" />
          <xs:element name="Telex" type="xoajsb:TelexType" />
          <xs:element name="DeliveryItemGeneric" type="xoajsb:DeliveryItemGenericType" />
        </xs:choice>
      </xs:complexType>
      <xs:complexType name="DeliveryType">
        <xs:all>
          <xs:element minOccurs="0" name="Schedule" type="xoajsb:ScheduleType" />
          <xs:element minOccurs="0" name="StartTime" type="xs:dateTime" />
          <xs:element minOccurs="0" name="StopTime" type="xs:dateTime" />
        </xs:all>
      </xs:complexType>
      <xs:simpleType name="OwnershipLevelType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="user" />
          <xs:enumeration value="customer" />
          <xs:enumeration value="group" />
          <xs:enumeration value="system" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="EncodableStringType">
        <xs:simpleContent>
          <xs:extension base="xs:string">
            <xs:attribute name="b64charset" type="xs:string" use="optional" />
          </xs:extension>
        </xs:simpleContent>
      </xs:complexType>
      <xs:element name="CharacterSet" type="xs:string">
        <xs:annotation>
          <xs:documentation>use ISO names</xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:simpleType name="DocEncodingFormat">
        <xs:restriction base="xs:string">
          <xs:enumeration value="text" />
          <xs:enumeration value="base64" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="DocDataType">
        <xs:simpleContent>
          <xs:extension base="xs:string">
            <xs:attribute name="format" type="xoajsb:DocEncodingFormat" use="required" />
          </xs:extension>
        </xs:simpleContent>
      </xs:complexType>
      <xs:complexType name="SosObjectIdType">
        <xs:simpleContent>
          <xs:extension base="xs:string">
            <xs:attribute name="sosType" type="xs:string" use="required" />
            <xs:attribute name="ownershipLevel" type="xoajsb:OwnershipLevelType" />
          </xs:extension>
        </xs:simpleContent>
      </xs:complexType>
      <xs:complexType name="DocumentType">
        <xs:sequence>
          <xs:element name="DocType" type="xs:string">
            <xs:annotation>
              <xs:documentation>Value restricted by application to recognized values</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Filename" type="xoajsb:EncodableStringType" />
          <xs:choice>
            <xs:element name="DocData" type="xoajsb:DocDataType" />
            <xs:element name="DocCfs" type="xs:string" />
            <xs:element name="DocUrl" type="xs:anyURI" />
            <xs:element name="SosObject" type="xoajsb:SosObjectIdType" />
          </xs:choice>
          <xs:element minOccurs="0" ref="xoajsb:CharacterSet" />
        </xs:sequence>
        <xs:attribute name="ref" type="xs:string" use="optional" />
      </xs:complexType>
      <xs:complexType name="DocumentSetType">
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="Document" type="xoajsb:DocumentType" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="DTMFKey">
        <xs:restriction base="xs:string">
          <xs:enumeration value="0" />
          <xs:enumeration value="1" />
          <xs:enumeration value="2" />
          <xs:enumeration value="3" />
          <xs:enumeration value="4" />
          <xs:enumeration value="5" />
          <xs:enumeration value="6" />
          <xs:enumeration value="7" />
          <xs:enumeration value="8" />
          <xs:enumeration value="9" />
          <xs:enumeration value="#" />
          <xs:enumeration value="*" />
          <xs:enumeration value="" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="FaxModeType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="standard" />
          <xs:enumeration value="fine" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="JobIdType">
        <xs:sequence>
          <xs:element name="XDN" type="xs:string" />
          <xs:element name="MRN" type="xs:integer" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="MainReportTypeEnum">
        <xs:restriction base="xs:string">
          <xs:enumeration value="none" />
          <xs:enumeration value="summary" />
          <xs:enumeration value="detail" />
          <xs:enumeration value="exception" />
          <xs:enumeration value="conditional" />
          <xs:enumeration value="pull" />
          <xs:enumeration value="enhanced" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="FriendReportTypeEnum">
        <xs:restriction base="xs:string">
          <xs:enumeration value="none" />
          <xs:enumeration value="summary" />
          <xs:enumeration value="detail" />
          <xs:enumeration value="exception" />
          <xs:enumeration value="conditional" />
          <xs:enumeration value="pull" />
          <xs:enumeration value="enhanced" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="AllReportTypeEnum">
        <xs:restriction base="xs:string">
          <xs:enumeration value="none" />
          <xs:enumeration value="summary" />
          <xs:enumeration value="detail" />
          <xs:enumeration value="exception" />
          <xs:enumeration value="conditional" />
          <xs:enumeration value="pull" />
          <xs:enumeration value="badaddress" />
          <xs:enumeration value="enhanced" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="ScheduleType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="express" />
          <xs:enumeration value="offpeak" />
          <xs:enumeration value="scheduled" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="SecurityType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="none" />
          <xs:enumeration value="40bit" />
          <xs:enumeration value="128bit" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="SegmentType">
        <xs:sequence>
          <xs:element maxOccurs="unbounded" name="Property">
            <xs:complexType>
              <xs:simpleContent>
                <xs:extension base="xs:string">
                  <xs:attribute name="name" type="xs:string" use="required" />
                  <xs:attribute name="b64charset" type="xs:string" use="optional" />
                </xs:extension>
              </xs:simpleContent>
            </xs:complexType>
          </xs:element>
        </xs:sequence>
        <xs:attribute name="name" type="xs:string" />
      </xs:complexType>
      <xs:simpleType name="YesNo">
        <xs:restriction base="xs:string">
          <xs:enumeration value="yes" />
          <xs:enumeration value="no" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="StatusType">
        <xs:sequence>
          <xs:element name="StatusCode" type="xs:integer">
            <xs:annotation>
              <xs:documentation>0 = success, non-zero = error state TBD</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="StatusMessage" type="xs:string">
            <xs:annotation>
              <xs:documentation>text description of StatusCode</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="ErrorList">
            <xs:annotation>
              <xs:documentation>Container for Errors</xs:documentation>
            </xs:annotation>
            <xs:complexType>
              <xs:sequence>
                <xs:element maxOccurs="unbounded" name="Error">
                  <xs:annotation>
                    <xs:documentation>An Error</xs:documentation>
                  </xs:annotation>
                  <xs:complexType>
                    <xs:sequence>
                      <xs:element name="ErrorCode" type="xs:integer">
                        <xs:annotation>
                          <xs:documentation>non-zero code</xs:documentation>
                        </xs:annotation>
                      </xs:element>
                      <xs:element name="ErrorMessage" type="xs:string" />
                    </xs:sequence>
                  </xs:complexType>
                </xs:element>
              </xs:sequence>
            </xs:complexType>
          </xs:element>
          <xs:element name="SubmissionTime" type="xs:dateTime">
            <xs:annotation>
              <xs:documentation>The time the Request was received by the system</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CompletionTime" type="xs:dateTime">
            <xs:annotation>
              <xs:documentation>the time the Request was completed processing</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
    </xs:schema>
    <xs:schema attributeFormDefault="unqualified" elementFormDefault="qualified" targetNamespace="http://ws.easylink.com/RequestResponse/2011/01">
      <xs:complexType name="UIDType">
        <xs:simpleContent>
          <xs:extension base="xs:string">
            <xs:attribute name="aliasType" type="xs:string" />
          </xs:extension>
        </xs:simpleContent>
      </xs:complexType>
      <xs:complexType name="XDDSAuthType">
        <xs:sequence>
          <xs:element name="RequesterID" type="xoarr:UIDType" />
          <xs:element minOccurs="0" name="TargetID" type="xoarr:UIDType" />
          <xs:element name="Password" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="WSSAuthType">
        <xs:sequence>
          <xs:element minOccurs="0" name="RequesterType" type="xs:string" />
          <xs:element minOccurs="0" name="TargetID" type="xoarr:UIDType" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="Request">
        <xs:annotation>
          <xs:documentation>SOAP header block of XDDS Switch Requests</xs:documentation>
        </xs:annotation>
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="SenderKey" type="xs:anyURI" />
            <xs:element name="ReceiverKey" type="xs:anyURI" />
            <xs:element minOccurs="0" name="RequestID" type="xs:string" />
            <xs:element minOccurs="0" name="Authentication">
              <xs:complexType>
                <xs:choice>
                  <xs:element name="XDDSAuth" type="xoarr:XDDSAuthType" />
                  <xs:element name="WSSAuth" type="xoarr:WSSAuthType" />
                </xs:choice>
              </xs:complexType>
            </xs:element>
            <xs:element minOccurs="0" name="InputChannel" type="xs:string" />
            <xs:element minOccurs="0" maxOccurs="unbounded" name="ResultOption">
              <xs:complexType>
                <xs:simpleContent>
                  <xs:extension base="xs:string">
                    <xs:attribute name="name" type="xs:string" />
                  </xs:extension>
                </xs:simpleContent>
              </xs:complexType>
            </xs:element>
          </xs:sequence>
          <xs:anyAttribute namespace="##any" processContents="lax" />
        </xs:complexType>
      </xs:element>
      <xs:element name="Response">
        <xs:annotation>
          <xs:documentation>SOAP header block of Response to XDDS Switch Requests</xs:documentation>
        </xs:annotation>
        <xs:complexType>
          <xs:sequence>
            <xs:element name="SenderKey" type="xs:anyURI" />
            <xs:element minOccurs="0" name="ReceiverKey" type="xs:anyURI" />
            <xs:element minOccurs="0" name="RequestID" type="xs:string" />
            <xs:element minOccurs="0" name="ProcessingID" type="xs:string">
              <xs:annotation>
                <xs:documentation>Unique ID generated by PremiereConnect and returned to customer</xs:documentation>
              </xs:annotation>
            </xs:element>
          </xs:sequence>
          <xs:anyAttribute namespace="##any" processContents="lax" />
        </xs:complexType>
      </xs:element>
    </xs:schema>
  </types>
  <message name="JobSubmitRequest">
    <part name="request_parameter" element="xoajsb:JobSubmitRequest" />
    <part name="request_header" element="xoarr:Request" />
  </message>
  <message name="JobSubmitResult">
    <part name="response_parameter" element="xoajsb:JobSubmitResult" />
    <part name="response_header" element="xoarr:Response" />
  </message>
  <portType name="JobSubmitPortType">
    <operation name="JobSubmit">
      <input message="xoajsb:JobSubmitRequest" />
      <output message="xoajsb:JobSubmitResult" />
    </operation>
  </portType>
  <binding name="JobSubmitBinding" type="xoajsb:JobSubmitPortType">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <operation name="JobSubmit">
      <soap:operation soapAction="XOAJobSubmit" />
      <input>
        <soap:body use="literal" parts="request_parameter" />
        <soap:header message="xoajsb:JobSubmitRequest" part="request_header" use="literal" />
      </input>
      <output>
        <soap:body use="literal" parts="response_parameter" />
        <soap:header message="xoajsb:JobSubmitResult" part="response_header" use="literal" />
      </output>
    </operation>
  </binding>
  <service name="JobSubmitService">
    <port name="JobSubmitPort" binding="xoajsb:JobSubmitBinding">
      <soap:address location="http://endpoint.easylink.com" />
    </port>
  </service>
</definitions>