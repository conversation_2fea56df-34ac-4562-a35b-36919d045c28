//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Extric.Towbook.API.OpenText {
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.ServiceContractAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01", ConfigurationName="OpenText.JobSubmitPortType")]
    public interface JobSubmitPortType {
        
        // CODEGEN: Generating message contract since the operation JobSubmit is neither RPC nor document wrapped.
        [System.ServiceModel.OperationContractAttribute(Action="XOAJobSubmit", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DeliveryItemType))]
        Extric.Towbook.API.OpenText.JobSubmitResponse JobSubmit(Extric.Towbook.API.OpenText.JobSubmitRequest1 request);
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://ws.easylink.com/RequestResponse/2011/01")]
    public partial class Request : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string senderKeyField;
        
        private string receiverKeyField;
        
        private string requestIDField;
        
        private RequestAuthentication authenticationField;
        
        private string inputChannelField;
        
        private RequestResultOption[] resultOptionField;
        
        private System.Xml.XmlAttribute[] anyAttrField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="anyURI", Order=0)]
        public string SenderKey {
            get {
                return this.senderKeyField;
            }
            set {
                this.senderKeyField = value;
                this.RaisePropertyChanged("SenderKey");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="anyURI", Order=1)]
        public string ReceiverKey {
            get {
                return this.receiverKeyField;
            }
            set {
                this.receiverKeyField = value;
                this.RaisePropertyChanged("ReceiverKey");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string RequestID {
            get {
                return this.requestIDField;
            }
            set {
                this.requestIDField = value;
                this.RaisePropertyChanged("RequestID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public RequestAuthentication Authentication {
            get {
                return this.authenticationField;
            }
            set {
                this.authenticationField = value;
                this.RaisePropertyChanged("Authentication");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string InputChannel {
            get {
                return this.inputChannelField;
            }
            set {
                this.inputChannelField = value;
                this.RaisePropertyChanged("InputChannel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("ResultOption", Order=5)]
        public RequestResultOption[] ResultOption {
            get {
                return this.resultOptionField;
            }
            set {
                this.resultOptionField = value;
                this.RaisePropertyChanged("ResultOption");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAnyAttributeAttribute()]
        public System.Xml.XmlAttribute[] AnyAttr {
            get {
                return this.anyAttrField;
            }
            set {
                this.anyAttrField = value;
                this.RaisePropertyChanged("AnyAttr");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://ws.easylink.com/RequestResponse/2011/01")]
    public partial class RequestAuthentication : object, System.ComponentModel.INotifyPropertyChanged {
        
        private object itemField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("WSSAuth", typeof(WSSAuthType), Order=0)]
        [System.Xml.Serialization.XmlElementAttribute("XDDSAuth", typeof(XDDSAuthType), Order=0)]
        public object Item {
            get {
                return this.itemField;
            }
            set {
                this.itemField = value;
                this.RaisePropertyChanged("Item");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/RequestResponse/2011/01")]
    public partial class WSSAuthType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string requesterTypeField;
        
        private UIDType targetIDField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string RequesterType {
            get {
                return this.requesterTypeField;
            }
            set {
                this.requesterTypeField = value;
                this.RaisePropertyChanged("RequesterType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public UIDType TargetID {
            get {
                return this.targetIDField;
            }
            set {
                this.targetIDField = value;
                this.RaisePropertyChanged("TargetID");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/RequestResponse/2011/01")]
    public partial class UIDType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string aliasTypeField;
        
        private string valueField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string aliasType {
            get {
                return this.aliasTypeField;
            }
            set {
                this.aliasTypeField = value;
                this.RaisePropertyChanged("aliasType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public string Value {
            get {
                return this.valueField;
            }
            set {
                this.valueField = value;
                this.RaisePropertyChanged("Value");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/RequestResponse/2011/01")]
    public partial class XDDSAuthType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private UIDType requesterIDField;
        
        private UIDType targetIDField;
        
        private string passwordField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public UIDType RequesterID {
            get {
                return this.requesterIDField;
            }
            set {
                this.requesterIDField = value;
                this.RaisePropertyChanged("RequesterID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public UIDType TargetID {
            get {
                return this.targetIDField;
            }
            set {
                this.targetIDField = value;
                this.RaisePropertyChanged("TargetID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string Password {
            get {
                return this.passwordField;
            }
            set {
                this.passwordField = value;
                this.RaisePropertyChanged("Password");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public abstract partial class DeliveryItemType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string refField;
        
        private string refb64Field;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string @ref {
            get {
                return this.refField;
            }
            set {
                this.refField = value;
                this.RaisePropertyChanged("ref");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string refb64 {
            get {
                return this.refb64Field;
            }
            set {
                this.refb64Field = value;
                this.RaisePropertyChanged("refb64");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class JobIdType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string xDNField;
        
        private string mRNField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string XDN {
            get {
                return this.xDNField;
            }
            set {
                this.xDNField = value;
                this.RaisePropertyChanged("XDN");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="integer", Order=1)]
        public string MRN {
            get {
                return this.mRNField;
            }
            set {
                this.mRNField = value;
                this.RaisePropertyChanged("MRN");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class StatusType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string statusCodeField;
        
        private string statusMessageField;
        
        private StatusTypeError[] errorListField;
        
        private System.DateTime submissionTimeField;
        
        private System.DateTime completionTimeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="integer", Order=0)]
        public string StatusCode {
            get {
                return this.statusCodeField;
            }
            set {
                this.statusCodeField = value;
                this.RaisePropertyChanged("StatusCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string StatusMessage {
            get {
                return this.statusMessageField;
            }
            set {
                this.statusMessageField = value;
                this.RaisePropertyChanged("StatusMessage");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=2)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Error", IsNullable=false)]
        public StatusTypeError[] ErrorList {
            get {
                return this.errorListField;
            }
            set {
                this.errorListField = value;
                this.RaisePropertyChanged("ErrorList");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public System.DateTime SubmissionTime {
            get {
                return this.submissionTimeField;
            }
            set {
                this.submissionTimeField = value;
                this.RaisePropertyChanged("SubmissionTime");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public System.DateTime CompletionTime {
            get {
                return this.completionTimeField;
            }
            set {
                this.completionTimeField = value;
                this.RaisePropertyChanged("CompletionTime");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class StatusTypeError : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string errorCodeField;
        
        private string errorMessageField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="integer", Order=0)]
        public string ErrorCode {
            get {
                return this.errorCodeField;
            }
            set {
                this.errorCodeField = value;
                this.RaisePropertyChanged("ErrorCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string ErrorMessage {
            get {
                return this.errorMessageField;
            }
            set {
                this.errorMessageField = value;
                this.RaisePropertyChanged("ErrorMessage");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class MessageResultType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string messageIdField;
        
        private StatusType statusField;
        
        private JobIdType jobIdField;
        
        private PreviewDataType previewDataField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string MessageId {
            get {
                return this.messageIdField;
            }
            set {
                this.messageIdField = value;
                this.RaisePropertyChanged("MessageId");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public StatusType Status {
            get {
                return this.statusField;
            }
            set {
                this.statusField = value;
                this.RaisePropertyChanged("Status");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public JobIdType JobId {
            get {
                return this.jobIdField;
            }
            set {
                this.jobIdField = value;
                this.RaisePropertyChanged("JobId");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public PreviewDataType PreviewData {
            get {
                return this.previewDataField;
            }
            set {
                this.previewDataField = value;
                this.RaisePropertyChanged("PreviewData");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class PreviewDataType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string previewField;
        
        private DocDataType previewDocDataField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string Preview {
            get {
                return this.previewField;
            }
            set {
                this.previewField = value;
                this.RaisePropertyChanged("Preview");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public DocDataType PreviewDocData {
            get {
                return this.previewDocDataField;
            }
            set {
                this.previewDocDataField = value;
                this.RaisePropertyChanged("PreviewDocData");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class DocDataType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private DocEncodingFormat formatField;
        
        private string valueField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public DocEncodingFormat format {
            get {
                return this.formatField;
            }
            set {
                this.formatField = value;
                this.RaisePropertyChanged("format");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public string Value {
            get {
                return this.valueField;
            }
            set {
                this.valueField = value;
                this.RaisePropertyChanged("Value");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public enum DocEncodingFormat {
        
        /// <remarks/>
        text,
        
        /// <remarks/>
        base64,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class PullfileOptionsType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private SecurityType securityField;
        
        private bool securityFieldSpecified;
        
        private YesNo autoPullField;
        
        private bool autoPullFieldSpecified;
        
        private YesNo passwordNeededField;
        
        private bool passwordNeededFieldSpecified;
        
        private int passwordLimitField;
        
        private bool passwordLimitFieldSpecified;
        
        /// <remarks/>
        public SecurityType Security {
            get {
                return this.securityField;
            }
            set {
                this.securityField = value;
                this.RaisePropertyChanged("Security");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool SecuritySpecified {
            get {
                return this.securityFieldSpecified;
            }
            set {
                this.securityFieldSpecified = value;
                this.RaisePropertyChanged("SecuritySpecified");
            }
        }
        
        /// <remarks/>
        public YesNo AutoPull {
            get {
                return this.autoPullField;
            }
            set {
                this.autoPullField = value;
                this.RaisePropertyChanged("AutoPull");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool AutoPullSpecified {
            get {
                return this.autoPullFieldSpecified;
            }
            set {
                this.autoPullFieldSpecified = value;
                this.RaisePropertyChanged("AutoPullSpecified");
            }
        }
        
        /// <remarks/>
        public YesNo PasswordNeeded {
            get {
                return this.passwordNeededField;
            }
            set {
                this.passwordNeededField = value;
                this.RaisePropertyChanged("PasswordNeeded");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool PasswordNeededSpecified {
            get {
                return this.passwordNeededFieldSpecified;
            }
            set {
                this.passwordNeededFieldSpecified = value;
                this.RaisePropertyChanged("PasswordNeededSpecified");
            }
        }
        
        /// <remarks/>
        public int PasswordLimit {
            get {
                return this.passwordLimitField;
            }
            set {
                this.passwordLimitField = value;
                this.RaisePropertyChanged("PasswordLimit");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool PasswordLimitSpecified {
            get {
                return this.passwordLimitFieldSpecified;
            }
            set {
                this.passwordLimitFieldSpecified = value;
                this.RaisePropertyChanged("PasswordLimitSpecified");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public enum SecurityType {
        
        /// <remarks/>
        none,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("40bit")]
        Item40bit,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("128bit")]
        Item128bit,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public enum YesNo {
        
        /// <remarks/>
        yes,
        
        /// <remarks/>
        no,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class ContentPartType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private object itemField;
        
        private TreatmentType treatmentField;
        
        private bool treatmentFieldSpecified;
        
        private PullfileOptionsType pullfileOptionsField;
        
        private AddressType[] addrTypeUseField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("DocRef", typeof(string), Order=0)]
        [System.Xml.Serialization.XmlElementAttribute("Document", typeof(DocumentType), Order=0)]
        public object Item {
            get {
                return this.itemField;
            }
            set {
                this.itemField = value;
                this.RaisePropertyChanged("Item");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public TreatmentType Treatment {
            get {
                return this.treatmentField;
            }
            set {
                this.treatmentField = value;
                this.RaisePropertyChanged("Treatment");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool TreatmentSpecified {
            get {
                return this.treatmentFieldSpecified;
            }
            set {
                this.treatmentFieldSpecified = value;
                this.RaisePropertyChanged("TreatmentSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public PullfileOptionsType PullfileOptions {
            get {
                return this.pullfileOptionsField;
            }
            set {
                this.pullfileOptionsField = value;
                this.RaisePropertyChanged("PullfileOptions");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("AddrTypeUse", Order=3)]
        public AddressType[] AddrTypeUse {
            get {
                return this.addrTypeUseField;
            }
            set {
                this.addrTypeUseField = value;
                this.RaisePropertyChanged("AddrTypeUse");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class DocumentType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string docTypeField;
        
        private EncodableStringType filenameField;
        
        private object itemField;
        
        private ItemChoiceType itemElementNameField;
        
        private string characterSetField;
        
        private string refField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string DocType {
            get {
                return this.docTypeField;
            }
            set {
                this.docTypeField = value;
                this.RaisePropertyChanged("DocType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public EncodableStringType Filename {
            get {
                return this.filenameField;
            }
            set {
                this.filenameField = value;
                this.RaisePropertyChanged("Filename");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("DocCfs", typeof(string), Order=2)]
        [System.Xml.Serialization.XmlElementAttribute("DocData", typeof(DocDataType), Order=2)]
        [System.Xml.Serialization.XmlElementAttribute("DocUrl", typeof(string), DataType="anyURI", Order=2)]
        [System.Xml.Serialization.XmlElementAttribute("SosObject", typeof(SosObjectIdType), Order=2)]
        [System.Xml.Serialization.XmlChoiceIdentifierAttribute("ItemElementName")]
        public object Item {
            get {
                return this.itemField;
            }
            set {
                this.itemField = value;
                this.RaisePropertyChanged("Item");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public ItemChoiceType ItemElementName {
            get {
                return this.itemElementNameField;
            }
            set {
                this.itemElementNameField = value;
                this.RaisePropertyChanged("ItemElementName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string CharacterSet {
            get {
                return this.characterSetField;
            }
            set {
                this.characterSetField = value;
                this.RaisePropertyChanged("CharacterSet");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string @ref {
            get {
                return this.refField;
            }
            set {
                this.refField = value;
                this.RaisePropertyChanged("ref");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class EncodableStringType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string b64charsetField;
        
        private string valueField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string b64charset {
            get {
                return this.b64charsetField;
            }
            set {
                this.b64charsetField = value;
                this.RaisePropertyChanged("b64charset");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public string Value {
            get {
                return this.valueField;
            }
            set {
                this.valueField = value;
                this.RaisePropertyChanged("Value");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class SosObjectIdType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string sosTypeField;
        
        private OwnershipLevelType ownershipLevelField;
        
        private bool ownershipLevelFieldSpecified;
        
        private string valueField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string sosType {
            get {
                return this.sosTypeField;
            }
            set {
                this.sosTypeField = value;
                this.RaisePropertyChanged("sosType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public OwnershipLevelType ownershipLevel {
            get {
                return this.ownershipLevelField;
            }
            set {
                this.ownershipLevelField = value;
                this.RaisePropertyChanged("ownershipLevel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ownershipLevelSpecified {
            get {
                return this.ownershipLevelFieldSpecified;
            }
            set {
                this.ownershipLevelFieldSpecified = value;
                this.RaisePropertyChanged("ownershipLevelSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public string Value {
            get {
                return this.valueField;
            }
            set {
                this.valueField = value;
                this.RaisePropertyChanged("Value");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public enum OwnershipLevelType {
        
        /// <remarks/>
        user,
        
        /// <remarks/>
        customer,
        
        /// <remarks/>
        group,
        
        /// <remarks/>
        system,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01", IncludeInSchema=false)]
    public enum ItemChoiceType {
        
        /// <remarks/>
        DocCfs,
        
        /// <remarks/>
        DocData,
        
        /// <remarks/>
        DocUrl,
        
        /// <remarks/>
        SosObject,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public enum TreatmentType {
        
        /// <remarks/>
        body,
        
        /// <remarks/>
        attachment,
        
        /// <remarks/>
        pullfile,
        
        /// <remarks/>
        voice_all,
        
        /// <remarks/>
        voice_live,
        
        /// <remarks/>
        voice_voicemail,
        
        /// <remarks/>
        voice_whisper,
        
        /// <remarks/>
        voice_call_control,
        
        /// <remarks/>
        ezBanner,
        
        /// <remarks/>
        docfx,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public enum AddressType {
        
        /// <remarks/>
        fax,
        
        /// <remarks/>
        internet,
        
        /// <remarks/>
        mbox,
        
        /// <remarks/>
        x400,
        
        /// <remarks/>
        telex,
        
        /// <remarks/>
        cablegram,
        
        /// <remarks/>
        mailgram,
        
        /// <remarks/>
        ddd,
        
        /// <remarks/>
        dedLine,
        
        /// <remarks/>
        softswitch,
        
        /// <remarks/>
        voice,
        
        /// <remarks/>
        sms,
        
        /// <remarks/>
        fod,
        
        /// <remarks/>
        list,
        
        /// <remarks/>
        smQuery,
        
        /// <remarks/>
        URL,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class ContentsType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private ContentPartType[] partField;
        
        private ContentsTypeDynamicContent[] dynamicContentField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("Part", Order=0)]
        public ContentPartType[] Part {
            get {
                return this.partField;
            }
            set {
                this.partField = value;
                this.RaisePropertyChanged("Part");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("DynamicContent", Order=1)]
        public ContentsTypeDynamicContent[] DynamicContent {
            get {
                return this.dynamicContentField;
            }
            set {
                this.dynamicContentField = value;
                this.RaisePropertyChanged("DynamicContent");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class ContentsTypeDynamicContent : object, System.ComponentModel.INotifyPropertyChanged {
        
        private EncodableStringType sectionNameField;
        
        private ContentsTypeDynamicContentSectionDocument[] sectionDocumentField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public EncodableStringType SectionName {
            get {
                return this.sectionNameField;
            }
            set {
                this.sectionNameField = value;
                this.RaisePropertyChanged("SectionName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("SectionDocument", Order=1)]
        public ContentsTypeDynamicContentSectionDocument[] SectionDocument {
            get {
                return this.sectionDocumentField;
            }
            set {
                this.sectionDocumentField = value;
                this.RaisePropertyChanged("SectionDocument");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class ContentsTypeDynamicContentSectionDocument : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string deliveryFormatField;
        
        private object itemField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string DeliveryFormat {
            get {
                return this.deliveryFormatField;
            }
            set {
                this.deliveryFormatField = value;
                this.RaisePropertyChanged("DeliveryFormat");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("DocRef", typeof(object), Order=1)]
        [System.Xml.Serialization.XmlElementAttribute("Document", typeof(DocumentType), Order=1)]
        public object Item {
            get {
                return this.itemField;
            }
            set {
                this.itemField = value;
                this.RaisePropertyChanged("Item");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class ReportOptionsType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private ReportOptionsTypeDeliveryReport deliveryReportField;
        
        private ReportOptionsTypeFriendReport friendReportField;
        
        private ReportOptionsTypeFinalReport finalReportField;
        
        private ReportOptionsTypeProgressReport progressReportField;
        
        /// <remarks/>
        public ReportOptionsTypeDeliveryReport DeliveryReport {
            get {
                return this.deliveryReportField;
            }
            set {
                this.deliveryReportField = value;
                this.RaisePropertyChanged("DeliveryReport");
            }
        }
        
        /// <remarks/>
        public ReportOptionsTypeFriendReport FriendReport {
            get {
                return this.friendReportField;
            }
            set {
                this.friendReportField = value;
                this.RaisePropertyChanged("FriendReport");
            }
        }
        
        /// <remarks/>
        public ReportOptionsTypeFinalReport FinalReport {
            get {
                return this.finalReportField;
            }
            set {
                this.finalReportField = value;
                this.RaisePropertyChanged("FinalReport");
            }
        }
        
        /// <remarks/>
        public ReportOptionsTypeProgressReport ProgressReport {
            get {
                return this.progressReportField;
            }
            set {
                this.progressReportField = value;
                this.RaisePropertyChanged("ProgressReport");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class ReportOptionsTypeDeliveryReport : object, System.ComponentModel.INotifyPropertyChanged {
        
        private MainReportTypeEnum deliveryReportTypeField;
        
        private object[] reportAddressField;
        
        private string reportTemplateField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public MainReportTypeEnum DeliveryReportType {
            get {
                return this.deliveryReportTypeField;
            }
            set {
                this.deliveryReportTypeField = value;
                this.RaisePropertyChanged("DeliveryReportType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=1)]
        [System.Xml.Serialization.XmlArrayItemAttribute("DeliveryItemGeneric", typeof(DeliveryItemGenericType), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Fax", typeof(FaxType), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Fod", typeof(FodType), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Internet", typeof(InternetType), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("List", typeof(ListType), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Mbox", typeof(MboxType), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("SmQuery", typeof(SmQueryType), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Sms", typeof(SmsType), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Table", typeof(TableType), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Telex", typeof(TelexType), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Voice", typeof(VoiceType), IsNullable=false)]
        public object[] ReportAddress {
            get {
                return this.reportAddressField;
            }
            set {
                this.reportAddressField = value;
                this.RaisePropertyChanged("ReportAddress");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string ReportTemplate {
            get {
                return this.reportTemplateField;
            }
            set {
                this.reportTemplateField = value;
                this.RaisePropertyChanged("ReportTemplate");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public enum MainReportTypeEnum {
        
        /// <remarks/>
        none,
        
        /// <remarks/>
        summary,
        
        /// <remarks/>
        detail,
        
        /// <remarks/>
        exception,
        
        /// <remarks/>
        conditional,
        
        /// <remarks/>
        pull,
        
        /// <remarks/>
        enhanced,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class DeliveryItemGenericType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private AlternateType alternateField;
        
        private InsertListTypeInsert[] insertListField;
        
        private SegmentType[] extensionField;
        
        private string addressField;
        
        private string refField;
        
        private string refb64Field;
        
        private AddressType typeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public AlternateType Alternate {
            get {
                return this.alternateField;
            }
            set {
                this.alternateField = value;
                this.RaisePropertyChanged("Alternate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=1)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Insert", IsNullable=false)]
        public InsertListTypeInsert[] InsertList {
            get {
                return this.insertListField;
            }
            set {
                this.insertListField = value;
                this.RaisePropertyChanged("InsertList");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=2)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Segment", IsNullable=false)]
        public SegmentType[] Extension {
            get {
                return this.extensionField;
            }
            set {
                this.extensionField = value;
                this.RaisePropertyChanged("Extension");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string Address {
            get {
                return this.addressField;
            }
            set {
                this.addressField = value;
                this.RaisePropertyChanged("Address");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string @ref {
            get {
                return this.refField;
            }
            set {
                this.refField = value;
                this.RaisePropertyChanged("ref");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string refb64 {
            get {
                return this.refb64Field;
            }
            set {
                this.refb64Field = value;
                this.RaisePropertyChanged("refb64");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public AddressType type {
            get {
                return this.typeField;
            }
            set {
                this.typeField = value;
                this.RaisePropertyChanged("type");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class AlternateType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private object itemField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("DeliveryItemGeneric", typeof(DeliveryItemGenericType), Order=0)]
        [System.Xml.Serialization.XmlElementAttribute("Fax", typeof(FaxType), Order=0)]
        [System.Xml.Serialization.XmlElementAttribute("Fod", typeof(FodType), Order=0)]
        [System.Xml.Serialization.XmlElementAttribute("Internet", typeof(InternetType), Order=0)]
        [System.Xml.Serialization.XmlElementAttribute("Mbox", typeof(MboxType), Order=0)]
        [System.Xml.Serialization.XmlElementAttribute("Sms", typeof(SmsType), Order=0)]
        [System.Xml.Serialization.XmlElementAttribute("Telex", typeof(TelexType), Order=0)]
        [System.Xml.Serialization.XmlElementAttribute("Voice", typeof(VoiceType), Order=0)]
        public object Item {
            get {
                return this.itemField;
            }
            set {
                this.itemField = value;
                this.RaisePropertyChanged("Item");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class FaxType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private AlternateType alternateField;
        
        private InsertListTypeInsert[] insertListField;
        
        private SegmentType[] extensionField;
        
        private string phoneField;
        
        private EncodableStringType attField;
        
        private EncodableStringType fromField;
        
        private EncodableStringType toField;
        
        private string salutationField;
        
        private string refField;
        
        private string refb64Field;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public AlternateType Alternate {
            get {
                return this.alternateField;
            }
            set {
                this.alternateField = value;
                this.RaisePropertyChanged("Alternate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=1)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Insert", IsNullable=false)]
        public InsertListTypeInsert[] InsertList {
            get {
                return this.insertListField;
            }
            set {
                this.insertListField = value;
                this.RaisePropertyChanged("InsertList");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=2)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Segment", IsNullable=false)]
        public SegmentType[] Extension {
            get {
                return this.extensionField;
            }
            set {
                this.extensionField = value;
                this.RaisePropertyChanged("Extension");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string Phone {
            get {
                return this.phoneField;
            }
            set {
                this.phoneField = value;
                this.RaisePropertyChanged("Phone");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public EncodableStringType Att {
            get {
                return this.attField;
            }
            set {
                this.attField = value;
                this.RaisePropertyChanged("Att");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public EncodableStringType From {
            get {
                return this.fromField;
            }
            set {
                this.fromField = value;
                this.RaisePropertyChanged("From");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public EncodableStringType To {
            get {
                return this.toField;
            }
            set {
                this.toField = value;
                this.RaisePropertyChanged("To");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=7)]
        public string Salutation {
            get {
                return this.salutationField;
            }
            set {
                this.salutationField = value;
                this.RaisePropertyChanged("Salutation");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string @ref {
            get {
                return this.refField;
            }
            set {
                this.refField = value;
                this.RaisePropertyChanged("ref");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string refb64 {
            get {
                return this.refb64Field;
            }
            set {
                this.refb64Field = value;
                this.RaisePropertyChanged("refb64");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class InsertListTypeInsert : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string numberField;
        
        private string b64charsetField;
        
        private string valueField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="integer")]
        public string number {
            get {
                return this.numberField;
            }
            set {
                this.numberField = value;
                this.RaisePropertyChanged("number");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string b64charset {
            get {
                return this.b64charsetField;
            }
            set {
                this.b64charsetField = value;
                this.RaisePropertyChanged("b64charset");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public string Value {
            get {
                return this.valueField;
            }
            set {
                this.valueField = value;
                this.RaisePropertyChanged("Value");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class SegmentType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private SegmentTypeProperty[] propertyField;
        
        private string nameField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("Property", Order=0)]
        public SegmentTypeProperty[] Property {
            get {
                return this.propertyField;
            }
            set {
                this.propertyField = value;
                this.RaisePropertyChanged("Property");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string name {
            get {
                return this.nameField;
            }
            set {
                this.nameField = value;
                this.RaisePropertyChanged("name");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class SegmentTypeProperty : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string nameField;
        
        private string b64charsetField;
        
        private string valueField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string name {
            get {
                return this.nameField;
            }
            set {
                this.nameField = value;
                this.RaisePropertyChanged("name");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string b64charset {
            get {
                return this.b64charsetField;
            }
            set {
                this.b64charsetField = value;
                this.RaisePropertyChanged("b64charset");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public string Value {
            get {
                return this.valueField;
            }
            set {
                this.valueField = value;
                this.RaisePropertyChanged("Value");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class FodType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private AlternateType alternateField;
        
        private string addressField;
        
        private string refField;
        
        private string refb64Field;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public AlternateType Alternate {
            get {
                return this.alternateField;
            }
            set {
                this.alternateField = value;
                this.RaisePropertyChanged("Alternate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string Address {
            get {
                return this.addressField;
            }
            set {
                this.addressField = value;
                this.RaisePropertyChanged("Address");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string @ref {
            get {
                return this.refField;
            }
            set {
                this.refField = value;
                this.RaisePropertyChanged("ref");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string refb64 {
            get {
                return this.refb64Field;
            }
            set {
                this.refb64Field = value;
                this.RaisePropertyChanged("refb64");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class InternetType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private AlternateType alternateField;
        
        private InsertListTypeInsert[] insertListField;
        
        private SegmentType[] extensionField;
        
        private string emailField;
        
        private EncodableStringType subjectField;
        
        private EncodableStringType fromField;
        
        private EformatType eformatField;
        
        private bool eformatFieldSpecified;
        
        private string passwordField;
        
        private string refField;
        
        private string refb64Field;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public AlternateType Alternate {
            get {
                return this.alternateField;
            }
            set {
                this.alternateField = value;
                this.RaisePropertyChanged("Alternate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=1)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Insert", IsNullable=false)]
        public InsertListTypeInsert[] InsertList {
            get {
                return this.insertListField;
            }
            set {
                this.insertListField = value;
                this.RaisePropertyChanged("InsertList");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=2)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Segment", IsNullable=false)]
        public SegmentType[] Extension {
            get {
                return this.extensionField;
            }
            set {
                this.extensionField = value;
                this.RaisePropertyChanged("Extension");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string Email {
            get {
                return this.emailField;
            }
            set {
                this.emailField = value;
                this.RaisePropertyChanged("Email");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public EncodableStringType Subject {
            get {
                return this.subjectField;
            }
            set {
                this.subjectField = value;
                this.RaisePropertyChanged("Subject");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public EncodableStringType From {
            get {
                return this.fromField;
            }
            set {
                this.fromField = value;
                this.RaisePropertyChanged("From");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public EformatType Eformat {
            get {
                return this.eformatField;
            }
            set {
                this.eformatField = value;
                this.RaisePropertyChanged("Eformat");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool EformatSpecified {
            get {
                return this.eformatFieldSpecified;
            }
            set {
                this.eformatFieldSpecified = value;
                this.RaisePropertyChanged("EformatSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=7)]
        public string Password {
            get {
                return this.passwordField;
            }
            set {
                this.passwordField = value;
                this.RaisePropertyChanged("Password");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string @ref {
            get {
                return this.refField;
            }
            set {
                this.refField = value;
                this.RaisePropertyChanged("ref");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string refb64 {
            get {
                return this.refb64Field;
            }
            set {
                this.refb64Field = value;
                this.RaisePropertyChanged("refb64");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public enum EformatType {
        
        /// <remarks/>
        text,
        
        /// <remarks/>
        html,
        
        /// <remarks/>
        htmllite,
        
        /// <remarks/>
        @default,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class MboxType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private AlternateType alternateField;
        
        private string userIdField;
        
        private string refField;
        
        private string refb64Field;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public AlternateType Alternate {
            get {
                return this.alternateField;
            }
            set {
                this.alternateField = value;
                this.RaisePropertyChanged("Alternate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string UserId {
            get {
                return this.userIdField;
            }
            set {
                this.userIdField = value;
                this.RaisePropertyChanged("UserId");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string @ref {
            get {
                return this.refField;
            }
            set {
                this.refField = value;
                this.RaisePropertyChanged("ref");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string refb64 {
            get {
                return this.refb64Field;
            }
            set {
                this.refb64Field = value;
                this.RaisePropertyChanged("refb64");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class SmsType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private AlternateType alternateField;
        
        private string phoneField;
        
        private string refField;
        
        private string refb64Field;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public AlternateType Alternate {
            get {
                return this.alternateField;
            }
            set {
                this.alternateField = value;
                this.RaisePropertyChanged("Alternate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string Phone {
            get {
                return this.phoneField;
            }
            set {
                this.phoneField = value;
                this.RaisePropertyChanged("Phone");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string @ref {
            get {
                return this.refField;
            }
            set {
                this.refField = value;
                this.RaisePropertyChanged("ref");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string refb64 {
            get {
                return this.refb64Field;
            }
            set {
                this.refb64Field = value;
                this.RaisePropertyChanged("refb64");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class TelexType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private AlternateType alternateField;
        
        private string numberField;
        
        private string refField;
        
        private string refb64Field;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public AlternateType Alternate {
            get {
                return this.alternateField;
            }
            set {
                this.alternateField = value;
                this.RaisePropertyChanged("Alternate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string Number {
            get {
                return this.numberField;
            }
            set {
                this.numberField = value;
                this.RaisePropertyChanged("Number");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string @ref {
            get {
                return this.refField;
            }
            set {
                this.refField = value;
                this.RaisePropertyChanged("ref");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string refb64 {
            get {
                return this.refb64Field;
            }
            set {
                this.refb64Field = value;
                this.RaisePropertyChanged("refb64");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class VoiceType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private AlternateType alternateField;
        
        private InsertListTypeInsert[] insertListField;
        
        private SegmentType[] extensionField;
        
        private string phoneField;
        
        private string refField;
        
        private string refb64Field;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public AlternateType Alternate {
            get {
                return this.alternateField;
            }
            set {
                this.alternateField = value;
                this.RaisePropertyChanged("Alternate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=1)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Insert", IsNullable=false)]
        public InsertListTypeInsert[] InsertList {
            get {
                return this.insertListField;
            }
            set {
                this.insertListField = value;
                this.RaisePropertyChanged("InsertList");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=2)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Segment", IsNullable=false)]
        public SegmentType[] Extension {
            get {
                return this.extensionField;
            }
            set {
                this.extensionField = value;
                this.RaisePropertyChanged("Extension");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string Phone {
            get {
                return this.phoneField;
            }
            set {
                this.phoneField = value;
                this.RaisePropertyChanged("Phone");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string @ref {
            get {
                return this.refField;
            }
            set {
                this.refField = value;
                this.RaisePropertyChanged("ref");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string refb64 {
            get {
                return this.refb64Field;
            }
            set {
                this.refb64Field = value;
                this.RaisePropertyChanged("refb64");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class ListType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string nameField;
        
        private string refField;
        
        private string refb64Field;
        
        private OwnershipLevelType ownershipLevelField;
        
        private bool ownershipLevelFieldSpecified;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string Name {
            get {
                return this.nameField;
            }
            set {
                this.nameField = value;
                this.RaisePropertyChanged("Name");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string @ref {
            get {
                return this.refField;
            }
            set {
                this.refField = value;
                this.RaisePropertyChanged("ref");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string refb64 {
            get {
                return this.refb64Field;
            }
            set {
                this.refb64Field = value;
                this.RaisePropertyChanged("refb64");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public OwnershipLevelType ownershipLevel {
            get {
                return this.ownershipLevelField;
            }
            set {
                this.ownershipLevelField = value;
                this.RaisePropertyChanged("ownershipLevel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ownershipLevelSpecified {
            get {
                return this.ownershipLevelFieldSpecified;
            }
            set {
                this.ownershipLevelFieldSpecified = value;
                this.RaisePropertyChanged("ownershipLevelSpecified");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class SmQueryType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string nameField;
        
        private string refField;
        
        private string refb64Field;
        
        private OwnershipLevelType ownershipLevelField;
        
        private bool ownershipLevelFieldSpecified;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string Name {
            get {
                return this.nameField;
            }
            set {
                this.nameField = value;
                this.RaisePropertyChanged("Name");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string @ref {
            get {
                return this.refField;
            }
            set {
                this.refField = value;
                this.RaisePropertyChanged("ref");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string refb64 {
            get {
                return this.refb64Field;
            }
            set {
                this.refb64Field = value;
                this.RaisePropertyChanged("refb64");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public OwnershipLevelType ownershipLevel {
            get {
                return this.ownershipLevelField;
            }
            set {
                this.ownershipLevelField = value;
                this.RaisePropertyChanged("ownershipLevel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ownershipLevelSpecified {
            get {
                return this.ownershipLevelFieldSpecified;
            }
            set {
                this.ownershipLevelFieldSpecified = value;
                this.RaisePropertyChanged("ownershipLevelSpecified");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class TableType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private FieldMappingTypeMap[] fieldMappingField;
        
        private object itemField;
        
        private string refField;
        
        private string refb64Field;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Map", IsNullable=false)]
        public FieldMappingTypeMap[] FieldMapping {
            get {
                return this.fieldMappingField;
            }
            set {
                this.fieldMappingField = value;
                this.RaisePropertyChanged("FieldMapping");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("DocRef", typeof(string), Order=1)]
        [System.Xml.Serialization.XmlElementAttribute("Document", typeof(DocumentType), Order=1)]
        public object Item {
            get {
                return this.itemField;
            }
            set {
                this.itemField = value;
                this.RaisePropertyChanged("Item");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string @ref {
            get {
                return this.refField;
            }
            set {
                this.refField = value;
                this.RaisePropertyChanged("ref");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string refb64 {
            get {
                return this.refb64Field;
            }
            set {
                this.refb64Field = value;
                this.RaisePropertyChanged("refb64");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class FieldMappingTypeMap : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string fieldNameField;
        
        private string segmentNameField;
        
        private string propertyNameField;
        
        private bool isBase64EncodedField;
        
        private bool isBase64EncodedFieldSpecified;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string FieldName {
            get {
                return this.fieldNameField;
            }
            set {
                this.fieldNameField = value;
                this.RaisePropertyChanged("FieldName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string SegmentName {
            get {
                return this.segmentNameField;
            }
            set {
                this.segmentNameField = value;
                this.RaisePropertyChanged("SegmentName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string PropertyName {
            get {
                return this.propertyNameField;
            }
            set {
                this.propertyNameField = value;
                this.RaisePropertyChanged("PropertyName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public bool IsBase64Encoded {
            get {
                return this.isBase64EncodedField;
            }
            set {
                this.isBase64EncodedField = value;
                this.RaisePropertyChanged("IsBase64Encoded");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool IsBase64EncodedSpecified {
            get {
                return this.isBase64EncodedFieldSpecified;
            }
            set {
                this.isBase64EncodedFieldSpecified = value;
                this.RaisePropertyChanged("IsBase64EncodedSpecified");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class ReportOptionsTypeFriendReport : object, System.ComponentModel.INotifyPropertyChanged {
        
        private FriendReportTypeEnum friendReportTypeField;
        
        private object[] reportAddressField;
        
        private string friendReportTemplateField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public FriendReportTypeEnum FriendReportType {
            get {
                return this.friendReportTypeField;
            }
            set {
                this.friendReportTypeField = value;
                this.RaisePropertyChanged("FriendReportType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=1)]
        [System.Xml.Serialization.XmlArrayItemAttribute("DeliveryItemGeneric", typeof(DeliveryItemGenericType), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Fax", typeof(FaxType), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Fod", typeof(FodType), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Internet", typeof(InternetType), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("List", typeof(ListType), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Mbox", typeof(MboxType), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("SmQuery", typeof(SmQueryType), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Sms", typeof(SmsType), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Table", typeof(TableType), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Telex", typeof(TelexType), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Voice", typeof(VoiceType), IsNullable=false)]
        public object[] ReportAddress {
            get {
                return this.reportAddressField;
            }
            set {
                this.reportAddressField = value;
                this.RaisePropertyChanged("ReportAddress");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string FriendReportTemplate {
            get {
                return this.friendReportTemplateField;
            }
            set {
                this.friendReportTemplateField = value;
                this.RaisePropertyChanged("FriendReportTemplate");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public enum FriendReportTypeEnum {
        
        /// <remarks/>
        none,
        
        /// <remarks/>
        summary,
        
        /// <remarks/>
        detail,
        
        /// <remarks/>
        exception,
        
        /// <remarks/>
        conditional,
        
        /// <remarks/>
        pull,
        
        /// <remarks/>
        enhanced,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class ReportOptionsTypeFinalReport : object, System.ComponentModel.INotifyPropertyChanged {
        
        private AllReportTypeEnum reportTypeField;
        
        private object[] reportAddressField;
        
        private string reportTemplateField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public AllReportTypeEnum ReportType {
            get {
                return this.reportTypeField;
            }
            set {
                this.reportTypeField = value;
                this.RaisePropertyChanged("ReportType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=1)]
        [System.Xml.Serialization.XmlArrayItemAttribute("DeliveryItemGeneric", typeof(DeliveryItemGenericType), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Fax", typeof(FaxType), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Fod", typeof(FodType), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Internet", typeof(InternetType), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("List", typeof(ListType), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Mbox", typeof(MboxType), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("SmQuery", typeof(SmQueryType), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Sms", typeof(SmsType), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Table", typeof(TableType), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Telex", typeof(TelexType), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Voice", typeof(VoiceType), IsNullable=false)]
        public object[] ReportAddress {
            get {
                return this.reportAddressField;
            }
            set {
                this.reportAddressField = value;
                this.RaisePropertyChanged("ReportAddress");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string ReportTemplate {
            get {
                return this.reportTemplateField;
            }
            set {
                this.reportTemplateField = value;
                this.RaisePropertyChanged("ReportTemplate");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public enum AllReportTypeEnum {
        
        /// <remarks/>
        none,
        
        /// <remarks/>
        summary,
        
        /// <remarks/>
        detail,
        
        /// <remarks/>
        exception,
        
        /// <remarks/>
        conditional,
        
        /// <remarks/>
        pull,
        
        /// <remarks/>
        badaddress,
        
        /// <remarks/>
        enhanced,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class ReportOptionsTypeProgressReport : object, System.ComponentModel.INotifyPropertyChanged {
        
        private AllReportTypeEnum progressReportTypeField;
        
        private object[] reportAddressField;
        
        private string progressReportTemplateField;
        
        private string progressReportIntervalsField;
        
        private System.DateTime progressReportBaseField;
        
        private bool progressReportBaseFieldSpecified;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public AllReportTypeEnum ProgressReportType {
            get {
                return this.progressReportTypeField;
            }
            set {
                this.progressReportTypeField = value;
                this.RaisePropertyChanged("ProgressReportType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=1)]
        [System.Xml.Serialization.XmlArrayItemAttribute("DeliveryItemGeneric", typeof(DeliveryItemGenericType), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Fax", typeof(FaxType), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Fod", typeof(FodType), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Internet", typeof(InternetType), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("List", typeof(ListType), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Mbox", typeof(MboxType), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("SmQuery", typeof(SmQueryType), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Sms", typeof(SmsType), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Table", typeof(TableType), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Telex", typeof(TelexType), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Voice", typeof(VoiceType), IsNullable=false)]
        public object[] ReportAddress {
            get {
                return this.reportAddressField;
            }
            set {
                this.reportAddressField = value;
                this.RaisePropertyChanged("ReportAddress");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string ProgressReportTemplate {
            get {
                return this.progressReportTemplateField;
            }
            set {
                this.progressReportTemplateField = value;
                this.RaisePropertyChanged("ProgressReportTemplate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string ProgressReportIntervals {
            get {
                return this.progressReportIntervalsField;
            }
            set {
                this.progressReportIntervalsField = value;
                this.RaisePropertyChanged("ProgressReportIntervals");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public System.DateTime ProgressReportBase {
            get {
                return this.progressReportBaseField;
            }
            set {
                this.progressReportBaseField = value;
                this.RaisePropertyChanged("ProgressReportBase");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ProgressReportBaseSpecified {
            get {
                return this.progressReportBaseFieldSpecified;
            }
            set {
                this.progressReportBaseFieldSpecified = value;
                this.RaisePropertyChanged("ProgressReportBaseSpecified");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class EmailOptionsType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private EncodableStringType subjectField;
        
        private string deliveryRetryPatternField;
        
        private SegmentType[] emailJobExtensionsField;
        
        /// <remarks/>
        public EncodableStringType Subject {
            get {
                return this.subjectField;
            }
            set {
                this.subjectField = value;
                this.RaisePropertyChanged("Subject");
            }
        }
        
        /// <remarks/>
        public string DeliveryRetryPattern {
            get {
                return this.deliveryRetryPatternField;
            }
            set {
                this.deliveryRetryPatternField = value;
                this.RaisePropertyChanged("DeliveryRetryPattern");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Segment", IsNullable=false)]
        public SegmentType[] EmailJobExtensions {
            get {
                return this.emailJobExtensionsField;
            }
            set {
                this.emailJobExtensionsField = value;
                this.RaisePropertyChanged("EmailJobExtensions");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class MailMergeFaxOptionsType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private EncodableStringType subjectField;
        
        private FaxPilotlineOptionType pilotlineField;
        
        private FaxModeType faxModeField;
        
        private bool faxModeFieldSpecified;
        
        private PageOrientationType pageOrientationField;
        
        private bool pageOrientationFieldSpecified;
        
        private FieldMappingTypeMap[] fieldMappingField;
        
        private string deliveryRetryPatternField;
        
        private SegmentType[] mailMergeFaxJobExtensionsField;
        
        /// <remarks/>
        public EncodableStringType Subject {
            get {
                return this.subjectField;
            }
            set {
                this.subjectField = value;
                this.RaisePropertyChanged("Subject");
            }
        }
        
        /// <remarks/>
        public FaxPilotlineOptionType Pilotline {
            get {
                return this.pilotlineField;
            }
            set {
                this.pilotlineField = value;
                this.RaisePropertyChanged("Pilotline");
            }
        }
        
        /// <remarks/>
        public FaxModeType FaxMode {
            get {
                return this.faxModeField;
            }
            set {
                this.faxModeField = value;
                this.RaisePropertyChanged("FaxMode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool FaxModeSpecified {
            get {
                return this.faxModeFieldSpecified;
            }
            set {
                this.faxModeFieldSpecified = value;
                this.RaisePropertyChanged("FaxModeSpecified");
            }
        }
        
        /// <remarks/>
        public PageOrientationType PageOrientation {
            get {
                return this.pageOrientationField;
            }
            set {
                this.pageOrientationField = value;
                this.RaisePropertyChanged("PageOrientation");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool PageOrientationSpecified {
            get {
                return this.pageOrientationFieldSpecified;
            }
            set {
                this.pageOrientationFieldSpecified = value;
                this.RaisePropertyChanged("PageOrientationSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Map", IsNullable=false)]
        public FieldMappingTypeMap[] FieldMapping {
            get {
                return this.fieldMappingField;
            }
            set {
                this.fieldMappingField = value;
                this.RaisePropertyChanged("FieldMapping");
            }
        }
        
        /// <remarks/>
        public string DeliveryRetryPattern {
            get {
                return this.deliveryRetryPatternField;
            }
            set {
                this.deliveryRetryPatternField = value;
                this.RaisePropertyChanged("DeliveryRetryPattern");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Segment", IsNullable=false)]
        public SegmentType[] MailMergeFaxJobExtensions {
            get {
                return this.mailMergeFaxJobExtensionsField;
            }
            set {
                this.mailMergeFaxJobExtensionsField = value;
                this.RaisePropertyChanged("MailMergeFaxJobExtensions");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class FaxPilotlineOptionType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private PilotlineStyleType pilotlineStyleField;
        
        private bool pilotlineStyleFieldSpecified;
        
        private string pilotlineTextField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public PilotlineStyleType PilotlineStyle {
            get {
                return this.pilotlineStyleField;
            }
            set {
                this.pilotlineStyleField = value;
                this.RaisePropertyChanged("PilotlineStyle");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool PilotlineStyleSpecified {
            get {
                return this.pilotlineStyleFieldSpecified;
            }
            set {
                this.pilotlineStyleFieldSpecified = value;
                this.RaisePropertyChanged("PilotlineStyleSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string PilotlineText {
            get {
                return this.pilotlineTextField;
            }
            set {
                this.pilotlineTextField = value;
                this.RaisePropertyChanged("PilotlineText");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public enum PilotlineStyleType {
        
        /// <remarks/>
        @default,
        
        /// <remarks/>
        none,
        
        /// <remarks/>
        type1,
        
        /// <remarks/>
        type2,
        
        /// <remarks/>
        type3,
        
        /// <remarks/>
        type4,
        
        /// <remarks/>
        type5,
        
        /// <remarks/>
        type6,
        
        /// <remarks/>
        type7,
        
        /// <remarks/>
        type8,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public enum FaxModeType {
        
        /// <remarks/>
        standard,
        
        /// <remarks/>
        fine,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public enum PageOrientationType {
        
        /// <remarks/>
        portrait,
        
        /// <remarks/>
        landscape,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class SmsOptionsType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private decimal expirationDaysField;
        
        private bool expirationDaysFieldSpecified;
        
        private string characterSetField;
        
        private string deliveryRetryPatternField;
        
        private SegmentType[] smsJobExtensionsField;
        
        /// <remarks/>
        public decimal ExpirationDays {
            get {
                return this.expirationDaysField;
            }
            set {
                this.expirationDaysField = value;
                this.RaisePropertyChanged("ExpirationDays");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ExpirationDaysSpecified {
            get {
                return this.expirationDaysFieldSpecified;
            }
            set {
                this.expirationDaysFieldSpecified = value;
                this.RaisePropertyChanged("ExpirationDaysSpecified");
            }
        }
        
        /// <remarks/>
        public string CharacterSet {
            get {
                return this.characterSetField;
            }
            set {
                this.characterSetField = value;
                this.RaisePropertyChanged("CharacterSet");
            }
        }
        
        /// <remarks/>
        public string DeliveryRetryPattern {
            get {
                return this.deliveryRetryPatternField;
            }
            set {
                this.deliveryRetryPatternField = value;
                this.RaisePropertyChanged("DeliveryRetryPattern");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Segment", IsNullable=false)]
        public SegmentType[] SmsJobExtensions {
            get {
                return this.smsJobExtensionsField;
            }
            set {
                this.smsJobExtensionsField = value;
                this.RaisePropertyChanged("SmsJobExtensions");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class CallControlType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string controlKeyActionField;
        
        private DTMFKey controlKeyField;
        
        private string controlKeyInfoField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string ControlKeyAction {
            get {
                return this.controlKeyActionField;
            }
            set {
                this.controlKeyActionField = value;
                this.RaisePropertyChanged("ControlKeyAction");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public DTMFKey ControlKey {
            get {
                return this.controlKeyField;
            }
            set {
                this.controlKeyField = value;
                this.RaisePropertyChanged("ControlKey");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string ControlKeyInfo {
            get {
                return this.controlKeyInfoField;
            }
            set {
                this.controlKeyInfoField = value;
                this.RaisePropertyChanged("ControlKeyInfo");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public enum DTMFKey {
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("0")]
        Item0,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("1")]
        Item1,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("2")]
        Item2,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("3")]
        Item3,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("4")]
        Item4,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("5")]
        Item5,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("6")]
        Item6,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("7")]
        Item7,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("8")]
        Item8,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("9")]
        Item9,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("#")]
        Item,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("*")]
        Item10,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("")]
        Item11,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class CallControlOptionsType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private bool useCallControlField;
        
        private bool useCallControlFieldSpecified;
        
        private string callControlMenuPositionField;
        
        private YesNo optOutConfirmationField;
        
        private bool optOutConfirmationFieldSpecified;
        
        private string optOutLevelField;
        
        private int pauseTimeoutField;
        
        private bool pauseTimeoutFieldSpecified;
        
        private int replayLimitField;
        
        private bool replayLimitFieldSpecified;
        
        private int rewindLimitField;
        
        private bool rewindLimitFieldSpecified;
        
        private int transferSilenceField;
        
        private bool transferSilenceFieldSpecified;
        
        private CallControlType[] callControlField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public bool UseCallControl {
            get {
                return this.useCallControlField;
            }
            set {
                this.useCallControlField = value;
                this.RaisePropertyChanged("UseCallControl");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool UseCallControlSpecified {
            get {
                return this.useCallControlFieldSpecified;
            }
            set {
                this.useCallControlFieldSpecified = value;
                this.RaisePropertyChanged("UseCallControlSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string CallControlMenuPosition {
            get {
                return this.callControlMenuPositionField;
            }
            set {
                this.callControlMenuPositionField = value;
                this.RaisePropertyChanged("CallControlMenuPosition");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public YesNo OptOutConfirmation {
            get {
                return this.optOutConfirmationField;
            }
            set {
                this.optOutConfirmationField = value;
                this.RaisePropertyChanged("OptOutConfirmation");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool OptOutConfirmationSpecified {
            get {
                return this.optOutConfirmationFieldSpecified;
            }
            set {
                this.optOutConfirmationFieldSpecified = value;
                this.RaisePropertyChanged("OptOutConfirmationSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string OptOutLevel {
            get {
                return this.optOutLevelField;
            }
            set {
                this.optOutLevelField = value;
                this.RaisePropertyChanged("OptOutLevel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public int PauseTimeout {
            get {
                return this.pauseTimeoutField;
            }
            set {
                this.pauseTimeoutField = value;
                this.RaisePropertyChanged("PauseTimeout");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool PauseTimeoutSpecified {
            get {
                return this.pauseTimeoutFieldSpecified;
            }
            set {
                this.pauseTimeoutFieldSpecified = value;
                this.RaisePropertyChanged("PauseTimeoutSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public int ReplayLimit {
            get {
                return this.replayLimitField;
            }
            set {
                this.replayLimitField = value;
                this.RaisePropertyChanged("ReplayLimit");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ReplayLimitSpecified {
            get {
                return this.replayLimitFieldSpecified;
            }
            set {
                this.replayLimitFieldSpecified = value;
                this.RaisePropertyChanged("ReplayLimitSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public int RewindLimit {
            get {
                return this.rewindLimitField;
            }
            set {
                this.rewindLimitField = value;
                this.RaisePropertyChanged("RewindLimit");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool RewindLimitSpecified {
            get {
                return this.rewindLimitFieldSpecified;
            }
            set {
                this.rewindLimitFieldSpecified = value;
                this.RaisePropertyChanged("RewindLimitSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=7)]
        public int TransferSilence {
            get {
                return this.transferSilenceField;
            }
            set {
                this.transferSilenceField = value;
                this.RaisePropertyChanged("TransferSilence");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool TransferSilenceSpecified {
            get {
                return this.transferSilenceFieldSpecified;
            }
            set {
                this.transferSilenceFieldSpecified = value;
                this.RaisePropertyChanged("TransferSilenceSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("CallControl", Order=8)]
        public CallControlType[] CallControl {
            get {
                return this.callControlField;
            }
            set {
                this.callControlField = value;
                this.RaisePropertyChanged("CallControl");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class VoiceOptionsType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private VoiceDeliveryMethodType voiceDeliveryMethodField;
        
        private bool voiceDeliveryMethodFieldSpecified;
        
        private YesNo recipientTimezoneOptionField;
        
        private bool recipientTimezoneOptionFieldSpecified;
        
        private string aNIField;
        
        private int forcePlayTimerField;
        
        private bool forcePlayTimerFieldSpecified;
        
        private CallControlOptionsType callControlOptionsField;
        
        private string deliveryRetryPatternField;
        
        private SegmentType[] voiceJobExtensionsField;
        
        /// <remarks/>
        public VoiceDeliveryMethodType VoiceDeliveryMethod {
            get {
                return this.voiceDeliveryMethodField;
            }
            set {
                this.voiceDeliveryMethodField = value;
                this.RaisePropertyChanged("VoiceDeliveryMethod");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool VoiceDeliveryMethodSpecified {
            get {
                return this.voiceDeliveryMethodFieldSpecified;
            }
            set {
                this.voiceDeliveryMethodFieldSpecified = value;
                this.RaisePropertyChanged("VoiceDeliveryMethodSpecified");
            }
        }
        
        /// <remarks/>
        public YesNo RecipientTimezoneOption {
            get {
                return this.recipientTimezoneOptionField;
            }
            set {
                this.recipientTimezoneOptionField = value;
                this.RaisePropertyChanged("RecipientTimezoneOption");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool RecipientTimezoneOptionSpecified {
            get {
                return this.recipientTimezoneOptionFieldSpecified;
            }
            set {
                this.recipientTimezoneOptionFieldSpecified = value;
                this.RaisePropertyChanged("RecipientTimezoneOptionSpecified");
            }
        }
        
        /// <remarks/>
        public string ANI {
            get {
                return this.aNIField;
            }
            set {
                this.aNIField = value;
                this.RaisePropertyChanged("ANI");
            }
        }
        
        /// <remarks/>
        public int ForcePlayTimer {
            get {
                return this.forcePlayTimerField;
            }
            set {
                this.forcePlayTimerField = value;
                this.RaisePropertyChanged("ForcePlayTimer");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ForcePlayTimerSpecified {
            get {
                return this.forcePlayTimerFieldSpecified;
            }
            set {
                this.forcePlayTimerFieldSpecified = value;
                this.RaisePropertyChanged("ForcePlayTimerSpecified");
            }
        }
        
        /// <remarks/>
        public CallControlOptionsType CallControlOptions {
            get {
                return this.callControlOptionsField;
            }
            set {
                this.callControlOptionsField = value;
                this.RaisePropertyChanged("CallControlOptions");
            }
        }
        
        /// <remarks/>
        public string DeliveryRetryPattern {
            get {
                return this.deliveryRetryPatternField;
            }
            set {
                this.deliveryRetryPatternField = value;
                this.RaisePropertyChanged("DeliveryRetryPattern");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Segment", IsNullable=false)]
        public SegmentType[] VoiceJobExtensions {
            get {
                return this.voiceJobExtensionsField;
            }
            set {
                this.voiceJobExtensionsField = value;
                this.RaisePropertyChanged("VoiceJobExtensions");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public enum VoiceDeliveryMethodType {
        
        /// <remarks/>
        silence,
        
        /// <remarks/>
        PAMD,
        
        /// <remarks/>
        live_only,
        
        /// <remarks/>
        voicemail_only,
        
        /// <remarks/>
        force_play,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class EnhancedEmailOptionsType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private decimal expirationDaysField;
        
        private bool expirationDaysFieldSpecified;
        
        private EncodableStringType subjectField;
        
        private YesNo autoSendFriendField;
        
        private bool autoSendFriendFieldSpecified;
        
        private string characterSetField;
        
        private string pullPasswordField;
        
        private string fromAddressField;
        
        private string fromDisplayNameField;
        
        private string replyToField;
        
        private HTMLOpenTrackingType hTMLOpenTrackingField;
        
        private bool hTMLOpenTrackingFieldSpecified;
        
        private string passwordLifetimeField;
        
        private string deliveryRetryPatternField;
        
        private SegmentType[] enhancedEmailJobExtensionsField;
        
        /// <remarks/>
        public decimal ExpirationDays {
            get {
                return this.expirationDaysField;
            }
            set {
                this.expirationDaysField = value;
                this.RaisePropertyChanged("ExpirationDays");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ExpirationDaysSpecified {
            get {
                return this.expirationDaysFieldSpecified;
            }
            set {
                this.expirationDaysFieldSpecified = value;
                this.RaisePropertyChanged("ExpirationDaysSpecified");
            }
        }
        
        /// <remarks/>
        public EncodableStringType Subject {
            get {
                return this.subjectField;
            }
            set {
                this.subjectField = value;
                this.RaisePropertyChanged("Subject");
            }
        }
        
        /// <remarks/>
        public YesNo AutoSendFriend {
            get {
                return this.autoSendFriendField;
            }
            set {
                this.autoSendFriendField = value;
                this.RaisePropertyChanged("AutoSendFriend");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool AutoSendFriendSpecified {
            get {
                return this.autoSendFriendFieldSpecified;
            }
            set {
                this.autoSendFriendFieldSpecified = value;
                this.RaisePropertyChanged("AutoSendFriendSpecified");
            }
        }
        
        /// <remarks/>
        public string CharacterSet {
            get {
                return this.characterSetField;
            }
            set {
                this.characterSetField = value;
                this.RaisePropertyChanged("CharacterSet");
            }
        }
        
        /// <remarks/>
        public string PullPassword {
            get {
                return this.pullPasswordField;
            }
            set {
                this.pullPasswordField = value;
                this.RaisePropertyChanged("PullPassword");
            }
        }
        
        /// <remarks/>
        public string FromAddress {
            get {
                return this.fromAddressField;
            }
            set {
                this.fromAddressField = value;
                this.RaisePropertyChanged("FromAddress");
            }
        }
        
        /// <remarks/>
        public string FromDisplayName {
            get {
                return this.fromDisplayNameField;
            }
            set {
                this.fromDisplayNameField = value;
                this.RaisePropertyChanged("FromDisplayName");
            }
        }
        
        /// <remarks/>
        public string ReplyTo {
            get {
                return this.replyToField;
            }
            set {
                this.replyToField = value;
                this.RaisePropertyChanged("ReplyTo");
            }
        }
        
        /// <remarks/>
        public HTMLOpenTrackingType HTMLOpenTracking {
            get {
                return this.hTMLOpenTrackingField;
            }
            set {
                this.hTMLOpenTrackingField = value;
                this.RaisePropertyChanged("HTMLOpenTracking");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool HTMLOpenTrackingSpecified {
            get {
                return this.hTMLOpenTrackingFieldSpecified;
            }
            set {
                this.hTMLOpenTrackingFieldSpecified = value;
                this.RaisePropertyChanged("HTMLOpenTrackingSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="integer")]
        public string PasswordLifetime {
            get {
                return this.passwordLifetimeField;
            }
            set {
                this.passwordLifetimeField = value;
                this.RaisePropertyChanged("PasswordLifetime");
            }
        }
        
        /// <remarks/>
        public string DeliveryRetryPattern {
            get {
                return this.deliveryRetryPatternField;
            }
            set {
                this.deliveryRetryPatternField = value;
                this.RaisePropertyChanged("DeliveryRetryPattern");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Segment", IsNullable=false)]
        public SegmentType[] EnhancedEmailJobExtensions {
            get {
                return this.enhancedEmailJobExtensionsField;
            }
            set {
                this.enhancedEmailJobExtensionsField = value;
                this.RaisePropertyChanged("EnhancedEmailJobExtensions");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public enum HTMLOpenTrackingType {
        
        /// <remarks/>
        none,
        
        /// <remarks/>
        top,
        
        /// <remarks/>
        bottom,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class FaxBannerFxOptionType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private YesNo useBannerFxField;
        
        private bool useBannerFxFieldSpecified;
        
        private string bannerFxNameField;
        
        private FaxBannerFxPlacementType bannerFxPlacementField;
        
        private bool bannerFxPlacementFieldSpecified;
        
        private YesNo bannerFxOnCoverField;
        
        private bool bannerFxOnCoverFieldSpecified;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public YesNo UseBannerFx {
            get {
                return this.useBannerFxField;
            }
            set {
                this.useBannerFxField = value;
                this.RaisePropertyChanged("UseBannerFx");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool UseBannerFxSpecified {
            get {
                return this.useBannerFxFieldSpecified;
            }
            set {
                this.useBannerFxFieldSpecified = value;
                this.RaisePropertyChanged("UseBannerFxSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string BannerFxName {
            get {
                return this.bannerFxNameField;
            }
            set {
                this.bannerFxNameField = value;
                this.RaisePropertyChanged("BannerFxName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public FaxBannerFxPlacementType BannerFxPlacement {
            get {
                return this.bannerFxPlacementField;
            }
            set {
                this.bannerFxPlacementField = value;
                this.RaisePropertyChanged("BannerFxPlacement");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool BannerFxPlacementSpecified {
            get {
                return this.bannerFxPlacementFieldSpecified;
            }
            set {
                this.bannerFxPlacementFieldSpecified = value;
                this.RaisePropertyChanged("BannerFxPlacementSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public YesNo BannerFxOnCover {
            get {
                return this.bannerFxOnCoverField;
            }
            set {
                this.bannerFxOnCoverField = value;
                this.RaisePropertyChanged("BannerFxOnCover");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool BannerFxOnCoverSpecified {
            get {
                return this.bannerFxOnCoverFieldSpecified;
            }
            set {
                this.bannerFxOnCoverFieldSpecified = value;
                this.RaisePropertyChanged("BannerFxOnCoverSpecified");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public enum FaxBannerFxPlacementType {
        
        /// <remarks/>
        outside,
        
        /// <remarks/>
        inside_replace,
        
        /// <remarks/>
        insideor,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class FaxCoversheetOptionType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private YesNo useCoversheetField;
        
        private string coversheetNameField;
        
        private EncodableStringType coversheetToField;
        
        private EncodableStringType coversheetFromField;
        
        private EncodableStringType coversheetAttnField;
        
        /// <remarks/>
        public YesNo UseCoversheet {
            get {
                return this.useCoversheetField;
            }
            set {
                this.useCoversheetField = value;
                this.RaisePropertyChanged("UseCoversheet");
            }
        }
        
        /// <remarks/>
        public string CoversheetName {
            get {
                return this.coversheetNameField;
            }
            set {
                this.coversheetNameField = value;
                this.RaisePropertyChanged("CoversheetName");
            }
        }
        
        /// <remarks/>
        public EncodableStringType CoversheetTo {
            get {
                return this.coversheetToField;
            }
            set {
                this.coversheetToField = value;
                this.RaisePropertyChanged("CoversheetTo");
            }
        }
        
        /// <remarks/>
        public EncodableStringType CoversheetFrom {
            get {
                return this.coversheetFromField;
            }
            set {
                this.coversheetFromField = value;
                this.RaisePropertyChanged("CoversheetFrom");
            }
        }
        
        /// <remarks/>
        public EncodableStringType CoversheetAttn {
            get {
                return this.coversheetAttnField;
            }
            set {
                this.coversheetAttnField = value;
                this.RaisePropertyChanged("CoversheetAttn");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class FaxOptionsType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private FaxCoversheetOptionType faxCoversheetField;
        
        private EncodableStringType subjectField;
        
        private FaxPilotlineOptionType pilotlineField;
        
        private FaxBannerFxOptionType bannerFXField;
        
        private FaxModeType faxModeField;
        
        private bool faxModeFieldSpecified;
        
        private string letterheadField;
        
        private PageOrientationType pageOrientationField;
        
        private bool pageOrientationFieldSpecified;
        
        private string deliveryRetryPatternField;
        
        private SegmentType[] faxJobExtensionsField;
        
        /// <remarks/>
        public FaxCoversheetOptionType FaxCoversheet {
            get {
                return this.faxCoversheetField;
            }
            set {
                this.faxCoversheetField = value;
                this.RaisePropertyChanged("FaxCoversheet");
            }
        }
        
        /// <remarks/>
        public EncodableStringType Subject {
            get {
                return this.subjectField;
            }
            set {
                this.subjectField = value;
                this.RaisePropertyChanged("Subject");
            }
        }
        
        /// <remarks/>
        public FaxPilotlineOptionType Pilotline {
            get {
                return this.pilotlineField;
            }
            set {
                this.pilotlineField = value;
                this.RaisePropertyChanged("Pilotline");
            }
        }
        
        /// <remarks/>
        public FaxBannerFxOptionType BannerFX {
            get {
                return this.bannerFXField;
            }
            set {
                this.bannerFXField = value;
                this.RaisePropertyChanged("BannerFX");
            }
        }
        
        /// <remarks/>
        public FaxModeType FaxMode {
            get {
                return this.faxModeField;
            }
            set {
                this.faxModeField = value;
                this.RaisePropertyChanged("FaxMode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool FaxModeSpecified {
            get {
                return this.faxModeFieldSpecified;
            }
            set {
                this.faxModeFieldSpecified = value;
                this.RaisePropertyChanged("FaxModeSpecified");
            }
        }
        
        /// <remarks/>
        public string Letterhead {
            get {
                return this.letterheadField;
            }
            set {
                this.letterheadField = value;
                this.RaisePropertyChanged("Letterhead");
            }
        }
        
        /// <remarks/>
        public PageOrientationType PageOrientation {
            get {
                return this.pageOrientationField;
            }
            set {
                this.pageOrientationField = value;
                this.RaisePropertyChanged("PageOrientation");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool PageOrientationSpecified {
            get {
                return this.pageOrientationFieldSpecified;
            }
            set {
                this.pageOrientationFieldSpecified = value;
                this.RaisePropertyChanged("PageOrientationSpecified");
            }
        }
        
        /// <remarks/>
        public string DeliveryRetryPattern {
            get {
                return this.deliveryRetryPatternField;
            }
            set {
                this.deliveryRetryPatternField = value;
                this.RaisePropertyChanged("DeliveryRetryPattern");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Segment", IsNullable=false)]
        public SegmentType[] FaxJobExtensions {
            get {
                return this.faxJobExtensionsField;
            }
            set {
                this.faxJobExtensionsField = value;
                this.RaisePropertyChanged("FaxJobExtensions");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class WeekDayIntervalType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string intervalBeginField;
        
        private string intervalEndField;
        
        private WeekDayType weekDayField;
        
        private bool weekDayFieldSpecified;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string IntervalBegin {
            get {
                return this.intervalBeginField;
            }
            set {
                this.intervalBeginField = value;
                this.RaisePropertyChanged("IntervalBegin");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string IntervalEnd {
            get {
                return this.intervalEndField;
            }
            set {
                this.intervalEndField = value;
                this.RaisePropertyChanged("IntervalEnd");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public WeekDayType weekDay {
            get {
                return this.weekDayField;
            }
            set {
                this.weekDayField = value;
                this.RaisePropertyChanged("weekDay");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool weekDaySpecified {
            get {
                return this.weekDayFieldSpecified;
            }
            set {
                this.weekDayFieldSpecified = value;
                this.RaisePropertyChanged("weekDaySpecified");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public enum WeekDayType {
        
        /// <remarks/>
        Sunday,
        
        /// <remarks/>
        Monday,
        
        /// <remarks/>
        Tuesday,
        
        /// <remarks/>
        Wednesday,
        
        /// <remarks/>
        Thursday,
        
        /// <remarks/>
        Friday,
        
        /// <remarks/>
        Saturday,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class BlackoutScheduleType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private WeekDayIntervalType[] weekDayIntervalField;
        
        private BlackoutAddressType addrTypeField;
        
        private bool addrTypeFieldSpecified;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("WeekDayInterval", Order=0)]
        public WeekDayIntervalType[] WeekDayInterval {
            get {
                return this.weekDayIntervalField;
            }
            set {
                this.weekDayIntervalField = value;
                this.RaisePropertyChanged("WeekDayInterval");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public BlackoutAddressType addrType {
            get {
                return this.addrTypeField;
            }
            set {
                this.addrTypeField = value;
                this.RaisePropertyChanged("addrType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool addrTypeSpecified {
            get {
                return this.addrTypeFieldSpecified;
            }
            set {
                this.addrTypeFieldSpecified = value;
                this.RaisePropertyChanged("addrTypeSpecified");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public enum BlackoutAddressType {
        
        /// <remarks/>
        @default,
        
        /// <remarks/>
        fax,
        
        /// <remarks/>
        internet,
        
        /// <remarks/>
        mbox,
        
        /// <remarks/>
        x400,
        
        /// <remarks/>
        telex,
        
        /// <remarks/>
        cablegram,
        
        /// <remarks/>
        mailgram,
        
        /// <remarks/>
        ddd,
        
        /// <remarks/>
        dedLine,
        
        /// <remarks/>
        softswitch,
        
        /// <remarks/>
        voice,
        
        /// <remarks/>
        sms,
        
        /// <remarks/>
        fod,
        
        /// <remarks/>
        list,
        
        /// <remarks/>
        smQuery,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class DeDuplicateFieldType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string segmentField;
        
        private string valueField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string segment {
            get {
                return this.segmentField;
            }
            set {
                this.segmentField = value;
                this.RaisePropertyChanged("segment");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public string Value {
            get {
                return this.valueField;
            }
            set {
                this.valueField = value;
                this.RaisePropertyChanged("Value");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class DeDuplicateType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private DeDuplicateFieldType[] deDuplicateFieldField;
        
        private DeDupOptionType deDupOptionField;
        
        private bool deDupOptionFieldSpecified;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("DeDuplicateField", Order=0)]
        public DeDuplicateFieldType[] DeDuplicateField {
            get {
                return this.deDuplicateFieldField;
            }
            set {
                this.deDuplicateFieldField = value;
                this.RaisePropertyChanged("DeDuplicateField");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public DeDupOptionType deDupOption {
            get {
                return this.deDupOptionField;
            }
            set {
                this.deDupOptionField = value;
                this.RaisePropertyChanged("deDupOption");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool deDupOptionSpecified {
            get {
                return this.deDupOptionFieldSpecified;
            }
            set {
                this.deDupOptionFieldSpecified = value;
                this.RaisePropertyChanged("deDupOptionSpecified");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public enum DeDupOptionType {
        
        /// <remarks/>
        @default,
        
        /// <remarks/>
        none,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class DDSProcessType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string datafeedNameField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string datafeedName {
            get {
                return this.datafeedNameField;
            }
            set {
                this.datafeedNameField = value;
                this.RaisePropertyChanged("datafeedName");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class DatafeedOptionsType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private DDSProcessType itemField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("DDSProcess", Order=0)]
        public DDSProcessType Item {
            get {
                return this.itemField;
            }
            set {
                this.itemField = value;
                this.RaisePropertyChanged("Item");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class EncryptOptionsType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private bool encryptDocField;
        
        private bool encryptDocFieldSpecified;
        
        private bool encryptReportDocField;
        
        private bool encryptReportDocFieldSpecified;
        
        private bool encryptRecipientListField;
        
        private bool encryptRecipientListFieldSpecified;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public bool EncryptDoc {
            get {
                return this.encryptDocField;
            }
            set {
                this.encryptDocField = value;
                this.RaisePropertyChanged("EncryptDoc");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool EncryptDocSpecified {
            get {
                return this.encryptDocFieldSpecified;
            }
            set {
                this.encryptDocFieldSpecified = value;
                this.RaisePropertyChanged("EncryptDocSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public bool EncryptReportDoc {
            get {
                return this.encryptReportDocField;
            }
            set {
                this.encryptReportDocField = value;
                this.RaisePropertyChanged("EncryptReportDoc");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool EncryptReportDocSpecified {
            get {
                return this.encryptReportDocFieldSpecified;
            }
            set {
                this.encryptReportDocFieldSpecified = value;
                this.RaisePropertyChanged("EncryptReportDocSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public bool EncryptRecipientList {
            get {
                return this.encryptRecipientListField;
            }
            set {
                this.encryptRecipientListField = value;
                this.RaisePropertyChanged("EncryptRecipientList");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool EncryptRecipientListSpecified {
            get {
                return this.encryptRecipientListFieldSpecified;
            }
            set {
                this.encryptRecipientListFieldSpecified = value;
                this.RaisePropertyChanged("EncryptRecipientListSpecified");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class DeliveryType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private ScheduleType scheduleField;
        
        private bool scheduleFieldSpecified;
        
        private System.DateTime startTimeField;
        
        private bool startTimeFieldSpecified;
        
        private System.DateTime stopTimeField;
        
        private bool stopTimeFieldSpecified;
        
        /// <remarks/>
        public ScheduleType Schedule {
            get {
                return this.scheduleField;
            }
            set {
                this.scheduleField = value;
                this.RaisePropertyChanged("Schedule");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ScheduleSpecified {
            get {
                return this.scheduleFieldSpecified;
            }
            set {
                this.scheduleFieldSpecified = value;
                this.RaisePropertyChanged("ScheduleSpecified");
            }
        }
        
        /// <remarks/>
        public System.DateTime StartTime {
            get {
                return this.startTimeField;
            }
            set {
                this.startTimeField = value;
                this.RaisePropertyChanged("StartTime");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool StartTimeSpecified {
            get {
                return this.startTimeFieldSpecified;
            }
            set {
                this.startTimeFieldSpecified = value;
                this.RaisePropertyChanged("StartTimeSpecified");
            }
        }
        
        /// <remarks/>
        public System.DateTime StopTime {
            get {
                return this.stopTimeField;
            }
            set {
                this.stopTimeField = value;
                this.RaisePropertyChanged("StopTime");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool StopTimeSpecified {
            get {
                return this.stopTimeFieldSpecified;
            }
            set {
                this.stopTimeFieldSpecified = value;
                this.RaisePropertyChanged("StopTimeSpecified");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public enum ScheduleType {
        
        /// <remarks/>
        express,
        
        /// <remarks/>
        offpeak,
        
        /// <remarks/>
        scheduled,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class JobOptionsType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private EncodableStringType billingCodeField;
        
        private EncodableStringType customerReferenceField;
        
        private DeliveryType deliveryField;
        
        private EncryptOptionsType encryptOptionsField;
        
        private DatafeedOptionsType datafeedOptionsField;
        
        private string domainField;
        
        private bool priorityBoostField;
        
        private bool priorityBoostFieldSpecified;
        
        private DeDuplicateType deDuplicateField;
        
        private BlackoutScheduleType[] deliveryBlackoutField;
        
        private JobOptionsTypeEnhancedJobOptions enhancedJobOptionsField;
        
        private bool multiModeField;
        
        private bool multiModeFieldSpecified;
        
        private FaxOptionsType faxOptionsField;
        
        private EnhancedEmailOptionsType enhancedEmailOptionsField;
        
        private VoiceOptionsType voiceOptionsField;
        
        private SmsOptionsType smsOptionsField;
        
        private MailMergeFaxOptionsType mailMergeFaxOptionsField;
        
        private EmailOptionsType emailOptionsField;
        
        private SegmentType[] jobExtensionsField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public EncodableStringType BillingCode {
            get {
                return this.billingCodeField;
            }
            set {
                this.billingCodeField = value;
                this.RaisePropertyChanged("BillingCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public EncodableStringType CustomerReference {
            get {
                return this.customerReferenceField;
            }
            set {
                this.customerReferenceField = value;
                this.RaisePropertyChanged("CustomerReference");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public DeliveryType Delivery {
            get {
                return this.deliveryField;
            }
            set {
                this.deliveryField = value;
                this.RaisePropertyChanged("Delivery");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public EncryptOptionsType EncryptOptions {
            get {
                return this.encryptOptionsField;
            }
            set {
                this.encryptOptionsField = value;
                this.RaisePropertyChanged("EncryptOptions");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public DatafeedOptionsType DatafeedOptions {
            get {
                return this.datafeedOptionsField;
            }
            set {
                this.datafeedOptionsField = value;
                this.RaisePropertyChanged("DatafeedOptions");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public string Domain {
            get {
                return this.domainField;
            }
            set {
                this.domainField = value;
                this.RaisePropertyChanged("Domain");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public bool PriorityBoost {
            get {
                return this.priorityBoostField;
            }
            set {
                this.priorityBoostField = value;
                this.RaisePropertyChanged("PriorityBoost");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool PriorityBoostSpecified {
            get {
                return this.priorityBoostFieldSpecified;
            }
            set {
                this.priorityBoostFieldSpecified = value;
                this.RaisePropertyChanged("PriorityBoostSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=7)]
        public DeDuplicateType DeDuplicate {
            get {
                return this.deDuplicateField;
            }
            set {
                this.deDuplicateField = value;
                this.RaisePropertyChanged("DeDuplicate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=8)]
        [System.Xml.Serialization.XmlArrayItemAttribute("BlackoutSchedule", IsNullable=false)]
        public BlackoutScheduleType[] DeliveryBlackout {
            get {
                return this.deliveryBlackoutField;
            }
            set {
                this.deliveryBlackoutField = value;
                this.RaisePropertyChanged("DeliveryBlackout");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=9)]
        public JobOptionsTypeEnhancedJobOptions EnhancedJobOptions {
            get {
                return this.enhancedJobOptionsField;
            }
            set {
                this.enhancedJobOptionsField = value;
                this.RaisePropertyChanged("EnhancedJobOptions");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=10)]
        public bool MultiMode {
            get {
                return this.multiModeField;
            }
            set {
                this.multiModeField = value;
                this.RaisePropertyChanged("MultiMode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool MultiModeSpecified {
            get {
                return this.multiModeFieldSpecified;
            }
            set {
                this.multiModeFieldSpecified = value;
                this.RaisePropertyChanged("MultiModeSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=11)]
        public FaxOptionsType FaxOptions {
            get {
                return this.faxOptionsField;
            }
            set {
                this.faxOptionsField = value;
                this.RaisePropertyChanged("FaxOptions");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=12)]
        public EnhancedEmailOptionsType EnhancedEmailOptions {
            get {
                return this.enhancedEmailOptionsField;
            }
            set {
                this.enhancedEmailOptionsField = value;
                this.RaisePropertyChanged("EnhancedEmailOptions");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=13)]
        public VoiceOptionsType VoiceOptions {
            get {
                return this.voiceOptionsField;
            }
            set {
                this.voiceOptionsField = value;
                this.RaisePropertyChanged("VoiceOptions");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=14)]
        public SmsOptionsType SmsOptions {
            get {
                return this.smsOptionsField;
            }
            set {
                this.smsOptionsField = value;
                this.RaisePropertyChanged("SmsOptions");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=15)]
        public MailMergeFaxOptionsType MailMergeFaxOptions {
            get {
                return this.mailMergeFaxOptionsField;
            }
            set {
                this.mailMergeFaxOptionsField = value;
                this.RaisePropertyChanged("MailMergeFaxOptions");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=16)]
        public EmailOptionsType EmailOptions {
            get {
                return this.emailOptionsField;
            }
            set {
                this.emailOptionsField = value;
                this.RaisePropertyChanged("EmailOptions");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=17)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Segment", IsNullable=false)]
        public SegmentType[] JobExtensions {
            get {
                return this.jobExtensionsField;
            }
            set {
                this.jobExtensionsField = value;
                this.RaisePropertyChanged("JobExtensions");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class JobOptionsTypeEnhancedJobOptions : object, System.ComponentModel.INotifyPropertyChanged {
        
        private YesNo callingWindowRetryField;
        
        private bool callingWindowRetryFieldSpecified;
        
        private YesNo faxDetectionField;
        
        private bool faxDetectionFieldSpecified;
        
        private string iVRModeField;
        
        private string pINExpirationField;
        
        private YesNo attemptLoggingField;
        
        private bool attemptLoggingFieldSpecified;
        
        private YesNo cDCJobField;
        
        private bool cDCJobFieldSpecified;
        
        private string answeringMachineLogicField;
        
        private YesNo lineItemCancField;
        
        private bool lineItemCancFieldSpecified;
        
        private int transferTimeoutField;
        
        private bool transferTimeoutFieldSpecified;
        
        private string appTypeField;
        
        private string phoneTypeListsField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public YesNo CallingWindowRetry {
            get {
                return this.callingWindowRetryField;
            }
            set {
                this.callingWindowRetryField = value;
                this.RaisePropertyChanged("CallingWindowRetry");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool CallingWindowRetrySpecified {
            get {
                return this.callingWindowRetryFieldSpecified;
            }
            set {
                this.callingWindowRetryFieldSpecified = value;
                this.RaisePropertyChanged("CallingWindowRetrySpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public YesNo FaxDetection {
            get {
                return this.faxDetectionField;
            }
            set {
                this.faxDetectionField = value;
                this.RaisePropertyChanged("FaxDetection");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool FaxDetectionSpecified {
            get {
                return this.faxDetectionFieldSpecified;
            }
            set {
                this.faxDetectionFieldSpecified = value;
                this.RaisePropertyChanged("FaxDetectionSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string IVRMode {
            get {
                return this.iVRModeField;
            }
            set {
                this.iVRModeField = value;
                this.RaisePropertyChanged("IVRMode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string PINExpiration {
            get {
                return this.pINExpirationField;
            }
            set {
                this.pINExpirationField = value;
                this.RaisePropertyChanged("PINExpiration");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public YesNo AttemptLogging {
            get {
                return this.attemptLoggingField;
            }
            set {
                this.attemptLoggingField = value;
                this.RaisePropertyChanged("AttemptLogging");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool AttemptLoggingSpecified {
            get {
                return this.attemptLoggingFieldSpecified;
            }
            set {
                this.attemptLoggingFieldSpecified = value;
                this.RaisePropertyChanged("AttemptLoggingSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public YesNo CDCJob {
            get {
                return this.cDCJobField;
            }
            set {
                this.cDCJobField = value;
                this.RaisePropertyChanged("CDCJob");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool CDCJobSpecified {
            get {
                return this.cDCJobFieldSpecified;
            }
            set {
                this.cDCJobFieldSpecified = value;
                this.RaisePropertyChanged("CDCJobSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public string AnsweringMachineLogic {
            get {
                return this.answeringMachineLogicField;
            }
            set {
                this.answeringMachineLogicField = value;
                this.RaisePropertyChanged("AnsweringMachineLogic");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=7)]
        public YesNo LineItemCanc {
            get {
                return this.lineItemCancField;
            }
            set {
                this.lineItemCancField = value;
                this.RaisePropertyChanged("LineItemCanc");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool LineItemCancSpecified {
            get {
                return this.lineItemCancFieldSpecified;
            }
            set {
                this.lineItemCancFieldSpecified = value;
                this.RaisePropertyChanged("LineItemCancSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=8)]
        public int TransferTimeout {
            get {
                return this.transferTimeoutField;
            }
            set {
                this.transferTimeoutField = value;
                this.RaisePropertyChanged("TransferTimeout");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool TransferTimeoutSpecified {
            get {
                return this.transferTimeoutFieldSpecified;
            }
            set {
                this.transferTimeoutFieldSpecified = value;
                this.RaisePropertyChanged("TransferTimeoutSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=9)]
        public string AppType {
            get {
                return this.appTypeField;
            }
            set {
                this.appTypeField = value;
                this.RaisePropertyChanged("AppType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=10)]
        public string PhoneTypeLists {
            get {
                return this.phoneTypeListsField;
            }
            set {
                this.phoneTypeListsField = value;
                this.RaisePropertyChanged("PhoneTypeLists");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class MessageType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string messageIdField;
        
        private JobOptionsType jobOptionsField;
        
        private object[] destinationsField;
        
        private ReportOptionsType reportsField;
        
        private ContentsType contentsField;
        
        private string previewField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string MessageId {
            get {
                return this.messageIdField;
            }
            set {
                this.messageIdField = value;
                this.RaisePropertyChanged("MessageId");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public JobOptionsType JobOptions {
            get {
                return this.jobOptionsField;
            }
            set {
                this.jobOptionsField = value;
                this.RaisePropertyChanged("JobOptions");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=2)]
        [System.Xml.Serialization.XmlArrayItemAttribute("DeliveryItemGeneric", typeof(DeliveryItemGenericType), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Fax", typeof(FaxType), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Fod", typeof(FodType), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Internet", typeof(InternetType), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("List", typeof(ListType), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Mbox", typeof(MboxType), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("SmQuery", typeof(SmQueryType), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Sms", typeof(SmsType), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Table", typeof(TableType), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Telex", typeof(TelexType), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Voice", typeof(VoiceType), IsNullable=false)]
        public object[] Destinations {
            get {
                return this.destinationsField;
            }
            set {
                this.destinationsField = value;
                this.RaisePropertyChanged("Destinations");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public ReportOptionsType Reports {
            get {
                return this.reportsField;
            }
            set {
                this.reportsField = value;
                this.RaisePropertyChanged("Reports");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public ContentsType Contents {
            get {
                return this.contentsField;
            }
            set {
                this.contentsField = value;
                this.RaisePropertyChanged("Contents");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public string Preview {
            get {
                return this.previewField;
            }
            set {
                this.previewField = value;
                this.RaisePropertyChanged("Preview");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://ws.easylink.com/RequestResponse/2011/01")]
    public partial class RequestResultOption : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string nameField;
        
        private string valueField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string name {
            get {
                return this.nameField;
            }
            set {
                this.nameField = value;
                this.RaisePropertyChanged("name");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public string Value {
            get {
                return this.valueField;
            }
            set {
                this.valueField = value;
                this.RaisePropertyChanged("Value");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class JobSubmitRequest : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string submitIdField;
        
        private DocumentType[] documentSetField;
        
        private MessageType[] messageField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string SubmitId {
            get {
                return this.submitIdField;
            }
            set {
                this.submitIdField = value;
                this.RaisePropertyChanged("SubmitId");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=1)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Document", IsNullable=false)]
        public DocumentType[] DocumentSet {
            get {
                return this.documentSetField;
            }
            set {
                this.documentSetField = value;
                this.RaisePropertyChanged("DocumentSet");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("Message", Order=2)]
        public MessageType[] Message {
            get {
                return this.messageField;
            }
            set {
                this.messageField = value;
                this.RaisePropertyChanged("Message");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://ws.easylink.com/RequestResponse/2011/01")]
    public partial class Response : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string senderKeyField;
        
        private string receiverKeyField;
        
        private string requestIDField;
        
        private string processingIDField;
        
        private System.Xml.XmlAttribute[] anyAttrField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="anyURI", Order=0)]
        public string SenderKey {
            get {
                return this.senderKeyField;
            }
            set {
                this.senderKeyField = value;
                this.RaisePropertyChanged("SenderKey");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="anyURI", Order=1)]
        public string ReceiverKey {
            get {
                return this.receiverKeyField;
            }
            set {
                this.receiverKeyField = value;
                this.RaisePropertyChanged("ReceiverKey");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string RequestID {
            get {
                return this.requestIDField;
            }
            set {
                this.requestIDField = value;
                this.RaisePropertyChanged("RequestID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string ProcessingID {
            get {
                return this.processingIDField;
            }
            set {
                this.processingIDField = value;
                this.RaisePropertyChanged("ProcessingID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAnyAttributeAttribute()]
        public System.Xml.XmlAttribute[] AnyAttr {
            get {
                return this.anyAttrField;
            }
            set {
                this.anyAttrField = value;
                this.RaisePropertyChanged("AnyAttr");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.2001.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://ws.easylink.com/JobSubmit/2011/01")]
    public partial class JobSubmitResult : object, System.ComponentModel.INotifyPropertyChanged {
        
        private StatusType statusField;
        
        private string submitIdField;
        
        private MessageResultType[] messageResultField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public StatusType Status {
            get {
                return this.statusField;
            }
            set {
                this.statusField = value;
                this.RaisePropertyChanged("Status");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string SubmitId {
            get {
                return this.submitIdField;
            }
            set {
                this.submitIdField = value;
                this.RaisePropertyChanged("SubmitId");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("MessageResult", Order=2)]
        public MessageResultType[] MessageResult {
            get {
                return this.messageResultField;
            }
            set {
                this.messageResultField = value;
                this.RaisePropertyChanged("MessageResult");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class JobSubmitRequest1 {
        
        [System.ServiceModel.MessageHeaderAttribute(Namespace="http://ws.easylink.com/RequestResponse/2011/01")]
        public Extric.Towbook.API.OpenText.Request Request;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01", Order=0)]
        public Extric.Towbook.API.OpenText.JobSubmitRequest JobSubmitRequest;
        
        public JobSubmitRequest1() {
        }
        
        public JobSubmitRequest1(Extric.Towbook.API.OpenText.Request Request, Extric.Towbook.API.OpenText.JobSubmitRequest JobSubmitRequest) {
            this.Request = Request;
            this.JobSubmitRequest = JobSubmitRequest;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class JobSubmitResponse {
        
        [System.ServiceModel.MessageHeaderAttribute(Namespace="http://ws.easylink.com/RequestResponse/2011/01")]
        public Extric.Towbook.API.OpenText.Response Response;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://ws.easylink.com/JobSubmit/2011/01", Order=0)]
        public Extric.Towbook.API.OpenText.JobSubmitResult JobSubmitResult;
        
        public JobSubmitResponse() {
        }
        
        public JobSubmitResponse(Extric.Towbook.API.OpenText.Response Response, Extric.Towbook.API.OpenText.JobSubmitResult JobSubmitResult) {
            this.Response = Response;
            this.JobSubmitResult = JobSubmitResult;
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    public interface JobSubmitPortTypeChannel : Extric.Towbook.API.OpenText.JobSubmitPortType, System.ServiceModel.IClientChannel {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    public partial class JobSubmitPortTypeClient : System.ServiceModel.ClientBase<Extric.Towbook.API.OpenText.JobSubmitPortType>, Extric.Towbook.API.OpenText.JobSubmitPortType {
        
        public JobSubmitPortTypeClient() {
        }
                
        public JobSubmitPortTypeClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress) {
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Extric.Towbook.API.OpenText.JobSubmitResponse Extric.Towbook.API.OpenText.JobSubmitPortType.JobSubmit(Extric.Towbook.API.OpenText.JobSubmitRequest1 request) {
            return base.Channel.JobSubmit(request);
        }
        
        public Extric.Towbook.API.OpenText.Response JobSubmit(Extric.Towbook.API.OpenText.Request Request, Extric.Towbook.API.OpenText.JobSubmitRequest JobSubmitRequest, out Extric.Towbook.API.OpenText.JobSubmitResult JobSubmitResult) {
            Extric.Towbook.API.OpenText.JobSubmitRequest1 inValue = new Extric.Towbook.API.OpenText.JobSubmitRequest1();
            inValue.Request = Request;
            inValue.JobSubmitRequest = JobSubmitRequest;
            Extric.Towbook.API.OpenText.JobSubmitResponse retVal = ((Extric.Towbook.API.OpenText.JobSubmitPortType)(this)).JobSubmit(inValue);
            JobSubmitResult = retVal.JobSubmitResult;
            return retVal.Response;
        }
    }
}
