using System;
using System.Collections.ObjectModel;
using System.Data;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using Extric.Towbook.Surcharges;
using Extric.Towbook.Impounds;
using Extric.Towbook.Accounts;
using System.Linq;
using Extric.Towbook.Company;
using Extric.Towbook.Utility;
using Extric.Towbook.Vehicle;
using Extric.Towbook.API.Models;
using Extric.Towbook.Integration;
using Extric.Towbook.WebShared;
using Extric.Towbook.Dispatch;
using System.Collections.Generic;
using Extric.Towbook;
using Extric.Towbook.Generated;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using System.ComponentModel.DataAnnotations;
using Microsoft.Extensions.Primitives; // Add this for validation attributes

namespace Extric.Towbook.Web.Pages.Accounts
{
    // Enum for Permit Expiration Type
    public enum PermitExpirationType
    {
        None = 0,
        Fixed = 1,
        Annually = 2,
        Monthly = 3
    }

    public class AccountEditorModel : PageModel
    {
        // Dictionary to map dropdown lists to their value setters
        private Dictionary<string, Action<string>> _driverDropdownValueSetters;
        private Dictionary<string, Action<string>> _dispatcherDropdownValueSetters;

        public AccountEditorModel()
        {
            // Initialize the dictionaries - but defer mapping until properties are initialized
            _driverDropdownValueSetters = new Dictionary<string, Action<string>>();
            _dispatcherDropdownValueSetters = new Dictionary<string, Action<string>>();
        }

        // Initialize the dictionary mappings - call this in PageLoadAsync after properties are initialized
        private void InitializeDropdownValueSetters()
        {
            // Driver dropdown mappings
            _driverDropdownValueSetters[nameof(ddlReqDriverChargesItems)] = value => ddlReqDriverChargesSelectedValue = value;
            _driverDropdownValueSetters[nameof(ddlReqDriverContactNameItems)] = value => ddlReqDriverContactNameSelectedValue = value;
            _driverDropdownValueSetters[nameof(ddlReqDriverContactPhoneItems)] = value => ddlReqDriverContactPhoneSelectedValue = value;
            _driverDropdownValueSetters[nameof(ddlReqDriverPurchaseOrderNumberItems)] = value => ddlReqDriverPurchaseOrderNumberSelectedValue = value;
            _driverDropdownValueSetters[nameof(ddlReqDriverInvoiceNumberItems)] = value => ddlReqDriverInvoiceNumberSelectedValue = value;
            _driverDropdownValueSetters[nameof(ddlReqDriverPaidInFullItems)] = value => ddlReqDriverPaidInFullSelectedValue = value;
            _driverDropdownValueSetters[nameof(ddlReqDriverOveragesItems)] = value => ddlReqDriverOveragesSelectedValue = value;
            _driverDropdownValueSetters[nameof(ddlReqDriverVinItems)] = value => ddlReqDriverVinSelectedValue = value;
            _driverDropdownValueSetters[nameof(ddlReqDriverLicensePlateItems)] = value => ddlReqDriverLicensePlateSelectedValue = value;
            _driverDropdownValueSetters[nameof(ddlReqDriverOdometerItems)] = value => ddlReqDriverOdometerSelectedValue = value;
            _driverDropdownValueSetters[nameof(ddlReqDriverPhotosItems)] = value => ddlReqDriverPhotosSelectedValue = value;
            _driverDropdownValueSetters[nameof(ddlReqDriverSignatureItems)] = value => ddlReqDriverSignatureSelectedValue = value;
            _driverDropdownValueSetters[nameof(ddlReqDriverDamageFormItems)] = value => ddlReqDriverDamageFormSelectedValue = value;
            _driverDropdownValueSetters[nameof(ddlReqDriverReasonItems)] = value => ddlReqDriverReasonSelectedValue = value;
            _driverDropdownValueSetters[nameof(ddlReqDriverVehicleDestinationItems)] = value => ddlReqDriverVehicleDestinationSelectedValue = value;
            _driverDropdownValueSetters[nameof(ddlReqDriverVehicleYearItems)] = value => ddlReqDriverVehicleYearSelectedValue = value;
            _driverDropdownValueSetters[nameof(ddlReqDriverVehicleMakeItems)] = value => ddlReqDriverVehicleMakeSelectedValue = value;
            _driverDropdownValueSetters[nameof(ddlReqDriverVehicleModelItems)] = value => ddlReqDriverVehicleModelSelectedValue = value;
            _driverDropdownValueSetters[nameof(ddlReqDriverVehicleColorItems)] = value => ddlReqDriverVehicleColorSelectedValue = value;
            _driverDropdownValueSetters[nameof(ddlReqDriverVehicleKeysLocationItems)] = value => ddlReqDriverVehicleKeysLocationSelectedValue = value;

            // Dispatcher dropdown mappings
            _dispatcherDropdownValueSetters[nameof(ddlReqDispatcherChargesItems)] = value => ddlReqDispatcherChargesSelectedValue = value;
            _dispatcherDropdownValueSetters[nameof(ddlReqDispatcherContactNameItems)] = value => ddlReqDispatcherContactNameSelectedValue = value;
            _dispatcherDropdownValueSetters[nameof(ddlReqDispatcherContactPhoneItems)] = value => ddlReqDispatcherContactPhoneSelectedValue = value;
            _dispatcherDropdownValueSetters[nameof(ddlReqDispatcherPurchaseOrderNumberItems)] = value => ddlReqDispatcherPurchaseOrderNumberSelectedValue = value;
            _dispatcherDropdownValueSetters[nameof(ddlReqDispatcherInvoiceNumberItems)] = value => ddlReqDispatcherInvoiceNumberSelectedValue = value;
            _dispatcherDropdownValueSetters[nameof(ddlReqDispatcherPaidInFullItems)] = value => ddlReqDispatcherPaidInFullSelectedValue = value;
            _dispatcherDropdownValueSetters[nameof(ddlReqDispatcherOveragesItems)] = value => ddlReqDispatcherOveragesSelectedValue = value;
            _dispatcherDropdownValueSetters[nameof(ddlReqDispatcherVinItems)] = value => ddlReqDispatcherVinSelectedValue = value;
            _dispatcherDropdownValueSetters[nameof(ddlReqDispatcherLicensePlateItems)] = value => ddlReqDispatcherLicensePlateSelectedValue = value;
            _dispatcherDropdownValueSetters[nameof(ddlReqDispatcherOdometerItems)] = value => ddlReqDispatcherOdometerSelectedValue = value;
            _dispatcherDropdownValueSetters[nameof(ddlReqDispatcherPhotosItems)] = value => ddlReqDispatcherPhotosSelectedValue = value;
            _dispatcherDropdownValueSetters[nameof(ddlReqDispatcherSignatureItems)] = value => ddlReqDispatcherSignatureSelectedValue = value;
            _dispatcherDropdownValueSetters[nameof(ddlReqDispatcherDamageFormItems)] = value => ddlReqDispatcherDamageFormSelectedValue = value;
            _dispatcherDropdownValueSetters[nameof(ddlReqDispatcherReasonItems)] = value => ddlReqDispatcherReasonSelectedValue = value;
            _dispatcherDropdownValueSetters[nameof(ddlReqDispatcherVehicleDestinationItems)] = value => ddlReqDispatcherVehicleDestinationSelectedValue = value;
            _dispatcherDropdownValueSetters[nameof(ddlReqDispatcherVehicleYearItems)] = value => ddlReqDispatcherVehicleYearSelectedValue = value;
            _dispatcherDropdownValueSetters[nameof(ddlReqDispatcherVehicleMakeItems)] = value => ddlReqDispatcherVehicleMakeSelectedValue = value;
            _dispatcherDropdownValueSetters[nameof(ddlReqDispatcherVehicleModelItems)] = value => ddlReqDispatcherVehicleModelSelectedValue = value;
            _dispatcherDropdownValueSetters[nameof(ddlReqDispatcherVehicleColorItems)] = value => ddlReqDispatcherVehicleColorSelectedValue = value;
            _dispatcherDropdownValueSetters[nameof(ddlReqDispatcherVehicleKeysLocationItems)] = value => ddlReqDispatcherVehicleKeysLocationSelectedValue = value;
        }

        [BindProperty(SupportsGet = true)]
        public int AccountId { get; set; }

        [BindProperty]
        [Required(ErrorMessage = "Account Name is required")]
        [Display(Name = "Account Name")]
        public string txtCompany { get; set; }

        [BindProperty]
        [Display(Name = "Contact Name")]
        public string txtContactName { get; set; }

        [BindProperty]
        [Display(Name = "Phone")]
        [Phone(ErrorMessage = "Please enter a valid phone number")]
        public string txtPhone { get; set; }

        [BindProperty]
        [Display(Name = "Email")]
        [EmailAddress(ErrorMessage = "Please enter a valid email address")]
        public string txtEmail { get; set; }

        [BindProperty]
        [Display(Name = "Reply-To Email")]
        [EmailAddress(ErrorMessage = "Please enter a valid reply-to email address")]
        public string txtReplyToEmail { get; set; }

        [BindProperty]
        [Display(Name = "Address")]
        public string txtAddress { get; set; }

        [BindProperty]
        [Display(Name = "City")]
        public string txtCity { get; set; }

        [BindProperty]
        [Display(Name = "State")]
        public string txtState { get; set; }

        [BindProperty]
        [Display(Name = "ZIP")]
        [RegularExpression(@"^\d{5}(-\d{4})?$", ErrorMessage = "Please enter a valid ZIP code")]
        public string txtZip { get; set; }

        [BindProperty]
        [Display(Name = "Billing Contact Email")]
        [EmailAddress(ErrorMessage = "Please enter a valid billing contact email address")]
        public string txtBillingContactEmail { get; set; }

        [BindProperty]
        [Display(Name = "Impound Destination Type")]
        public AccountImpoundDestination ImpoundDestinationType { get; set; }

        [BindProperty]
        [Display(Name = "Storage Lot")]
        public int? ImpoundDestinationStorageLotId { get; set; }

        [BindProperty]
        public string AutoAcceptRules { get; set; }

        [BindProperty]
        public string txtBillingAddress { get; set; }

        [BindProperty]
        public string txtBillingCity { get; set; }

        [BindProperty]
        public string txtBillingState { get; set; }

        [BindProperty]
        public string txtBillingZip { get; set; }

        [BindProperty]
        public string txtBillingContactName { get; set; }

        [BindProperty]
        public string providerNumber { get; set; }

        [BindProperty]
        public string taxId { get; set; }

        [BindProperty]
        public string locationId { get; set; }

        [BindProperty]
        public string billingUsername { get; set; }

        [BindProperty]
        public string billingPassword { get; set; }

        public Extric.Towbook.Accounts.Account AccountDetail { get; set; }
        public Extric.Towbook.Accounts.StorageRate StorageRateDetail { get; set; }
        public bool ShowAllRateItems { get; set; }
        public bool IsFuelSurchargeEnabled { get; set; }
        public string RateItemsJson { get; private set; }
        public string ReasonsJson { get; private set; }
        public string BodyTypesJson { get; private set; }
        public string RulesJson { get; private set; }

        public string PredefinedRateItemsToExclude
        {
            get
            {
                return new[]
                {
                    PredefinedRateItem.BUILTIN_MILEAGE_UNLOADED, PredefinedRateItem.BUILTIN_MILEAGE_LOADED,
                    PredefinedRateItem.BUILTIN_MILEAGE_DEADHEAD, PredefinedRateItem.BUILTIN_AFTER_HOUR_RELEASE_FEE
                }.ToJson();
            }
        }

        public EntryValidationRuleSet EntryValidationRuleSetDetail { get; set; }
        public MasterAccount MasterAccountDetail { get; set; }
        public Collection<SelectListItem> GracePeriodStartItems { get; set; }
        public Collection<SelectListItem> BooleanItems { get; set; }
        public Collection<StickeringReasonTimeItem> RpReasonItems { get; set; } = new Collection<StickeringReasonTimeItem>();
        public bool EnableStickering { get; set; } = false;
        public bool EnablePermits { get; set; } = false;
        public bool EnableSiteVisits { get; set; } = false;
        public bool SquarePaymentsEnabled { get; set; } = false;
        public ParkingPermitPublicLink ParkingPermitPublicLinkDetail { get; set; }
        public string CompanyReplytoEmail { get; set; }
        public string CompanyReplytoEmailSource { get; set; }

        public bool cbAllowAccountUsersToViewFilesChecked { get; set; }
        public bool cbEnablePaymentImportChecked { get; set; }
        public bool cbEnableStickeringChecked { get; set; }
        public bool cbEnableSiteVisitsChecked { get; set; }
        public bool cbEnableParkingPermitsVisible { get; set; }
        public bool cbAlwaysSendSurveyVisible { get; set; }
        public bool cbEnableParkingPermitsChecked { get; set; }

        public bool ShowCreditOptions
        {
            get
            {
                return WebGlobal.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.AccountCreditLimits) ||
                       WebGlobal.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.AdvancedBilling);
            }
        }

        public bool ShowDigitalLocations { get; set; }

        public string DigitalLocationsJson { get; set; }

        // Service Rates & Pricing properties
        [BindProperty]
        public decimal DiscountRate { get; set; }

        [BindProperty]
        public decimal? MaximumCharges { get; set; }

        [BindProperty]
        public string FuelSurcharge { get; set; }

        public string FuelSurchargeNoteText { get; set; }

        // Auto-accept properties
        [BindProperty]
        public string AutoAcceptJson { get; set; }
        public bool ShowAutoAccept { get; set; }

        // Statement & Invoice properties
        [BindProperty]
        public string StatementDisclaimer { get; set; }
        [BindProperty]
        public string AccountDisclaimer { get; set; }
        [BindProperty]
        public string StatementSubject { get; set; }
        [BindProperty]
        public string StatementMessage { get; set; }
        [BindProperty]
        public string InvoiceSubject { get; set; }
        [BindProperty]
        public string InvoiceMessage { get; set; }

        public bool CompanyHasAdvancedBillingFeature =>
            WebGlobal.CurrentUser.Company.HasFeature(Features.AdvancedBilling);

        public class AutoAcceptRule
        {
            public string Zip { get; set; }
            public string Type { get; set; }
            public int Eta { get; set; }
        }

        public string AddressBookNameMapOrBilling
        {
            get
            {
                if (AccountDetail == null || AccountDetail.Type == AccountType.MotorClub)
                    return "MapFrom";
                else
                    return "Billing Address";
            }
        }

        [BindProperty]
        public string autoAcceptJson { get; set; }

        public bool motorClubVisible = false;
        public bool mcTaxIdVisible = false;
        public bool mcLocationIdVisible = false;
        public bool mcUsernameVisible = false;
        public bool mcPasswordVisible = false;

        public bool ShowNewFields
        {
            get
            {
                return WebGlobal.CurrentUser.Company.HasAccessToBaseFeature("newCompletionRequirements");
            }
        }

        public bool AllowAccess()
        {
            return (WebGlobal.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Manager ||
                WebGlobal.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Accountant ||
               WebGlobal.CurrentUser.Id == 1845 ||
               WebGlobal.CurrentUser.CompanyId == 366 ||
               (WebGlobal.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Dispatcher &&
               new int[] { 2508, 7778 }.Contains(WebGlobal.CurrentUser.CompanyId)));
        }

        public class SubcontractorRotationItemModel
        {
            public bool HasLight { get; set; }
            public bool HasHeavy { get; set; }
            public string Company { get; set; }
            public int Id { get; set; }
        }

        public List<SubcontractorRotationItemModel> SubcontractorRotationItems {get;set;}

        public async Task<IActionResult> OnGetAsync()
        {
            cbEnablePaymentImportVisible = false;
            cbEnableStickeringVisible = false;


            if (!AllowAccess())
                return RedirectToPage("/Accounts/Default");

            if (Request.Query.ContainsKey("id"))
            {
                AccountId = Convert.ToInt32(Request.Query["id"]);
                AccountDetail = await Account.GetByIdAsync(AccountId);

                if (AccountDetail == null ||
                    !WebGlobal.CurrentUser.HasAccessToCompany(AccountDetail.CompanyId))
                {
                    return RedirectToPage("/Accounts/Default");
                }

                StorageRateDetail = AccountDetail.StorageRates;

                Title = "Towbook - Modify Account - " + AccountDetail.Company;

                if (AccountDetail.MasterAccountId > 0)
                {
                    MasterAccountDetail = await MasterAccount.GetByIdAsync(AccountDetail.MasterAccountId);
                    standardAccountVisible = true;
                }

                if (MasterAccountDetail != null)
                {
                    if (MasterAccountDetail.AccountTypeId == (int)AccountType.MotorClub)
                    {
                        motorClubVisible = true;
                        mcTaxIdVisible = new int[] { 1, 5, 19, 24, 22, 20 }.Contains(MasterAccountDetail.Id);
                        mcLocationIdVisible = new int[] { 1, 5 }.Contains(MasterAccountDetail.Id);

                        openingBalanceVisible = false;
                    }

                    if (MasterAccountDetail.Id == 1 ||
                        MasterAccountDetail.Id == 2 ||
                        MasterAccountDetail.Id == 3 ||
                        MasterAccountDetail.Id == 4 ||
                        MasterAccountDetail.Id == 5 ||
                        MasterAccountDetail.Id == 7)
                    {
                        cbEnablePaymentImportVisible = true;
                        mcUsernameVisible = true;
                        mcPasswordVisible = true;
                    }


                    if (AccountKeyValue.GetFirstValueOrNull(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "DigitalAutoAccept") != null)
                    {
                        cbDisableAutoAcceptVisible = true;
                        cbDisableAutoAcceptChecked = AccountKeyValue.GetFirstValueOrNull(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "DigitalAutoAcceptDisabled") == "1";
                    }


                }
            }
            else
            {
                Title = "Towbook - Create a new account";

                AccountDetail = new Account();
                AccountDetail.CompanyId = WebGlobal.CurrentUser.Company.Id;
                CompanyReplytoEmail = WebGlobal.CurrentUser.Company.Email;
                CompanyReplytoEmailSource = "company email";
                var billingAddress = AddressBookEntry.GetByName("Billing Address", AccountDetail.CompanyId, false).FirstOrDefault();
                if (billingAddress != null && Core.IsEmailValid(billingAddress.Email))
                {
                    CompanyReplytoEmail = billingAddress.Email;
                    CompanyReplytoEmailSource = "billing address email";
                }
            }

            await PageLoadAsync();
            return Page();
        }

       


        private async Task PageLoadAsync()
        {
            RulesJson = "[]";

            // Initialize all the dropdown items
            ddlTypeItems = new List<SelectListItem>
            {
                new SelectListItem("Other", "0"),
                new SelectListItem("Motor Club", "5"),
                new SelectListItem("Automotive Service Shop", "7"),
                new SelectListItem("Body Shop", "3"),
                new SelectListItem("Insurance Company", "4"),
                new SelectListItem("Police Department", "1"),
                new SelectListItem("Municipality ", "6"),
                new SelectListItem("Individual", "2"),
                new SelectListItem("Storage Facility", "8"),
                new SelectListItem("Private Property", "9"),
                new SelectListItem("Repossession Agency", "10"),
                new SelectListItem("Dealership", "11"),
                new SelectListItem("Heavy Equipment", "12"),
                new SelectListItem("Fleet", "13"),
                new SelectListItem("Broker", "14"),
                new SelectListItem("Transport", "15"),
                new SelectListItem("Subcontractor", "16")
            };

            // Initialize all the dropdown setters after the properties have been initialized
            InitializeDropdownValueSetters();

            if (await WebGlobal.CurrentUser.Company.HasFeatureAsync(Features.DispatchToSubcontractors_SubcontractorRotation) &&
                !Request.Query.ContainsKey("id"))
            {
                ddlTypeSelectedValue = "16";
            }




            if (Request.Query.ContainsKey("id"))
            {

                var pn = AccountKeyValue.GetByAccount(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "ImportPayments").FirstOrDefault();

                if (pn != null && pn.Value == "1")
                    cbEnablePaymentImportChecked = true;

                if (ShowAutoAccept)
                {
                    var aajson = AccountKeyValue.GetByAccount(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "DigitalAutoAccept").FirstOrDefault();

                    if (aajson != null)
                    {
                        autoAcceptJson = aajson.Value;
                    }
                }

                if (MasterAccountDetail != null &&
                    (MasterAccountDetail.Id == MasterAccountTypes.Agero ||
                    MasterAccountDetail.Id == MasterAccountTypes.Allstate ||
                    MasterAccountDetail.Id == MasterAccountTypes.Swoop ||
                    MasterAccountDetail.Id == MasterAccountTypes.Urgently))
                {
                    cbPreventPhotoSharingChecked = AccountKeyValue.GetFirstValueOrNull(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "PreventPhotoSharing") == "1";
                }

                if (MasterAccountDetail != null && MasterAccountDetail.Id == MasterAccountTypes.Swoop)
                {
                    ShowDigitalLocations = true;

                    DigitalLocationsJson = await (await WebGlobal.GetResponseFromUrlAsync(
                        string.Format("/api/accounts/{0}/locations", AccountDetail.Id))).Content.ReadAsStringAsync();
                }
                else
                {
                    DigitalLocationsJson = "[]";
                }

                cbAllowAccountUsersToViewFilesChecked = AccountKeyValue.GetFirstValueOrNull(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "AllowAccountUsersToViewFiles") == "1";

                SubcontractorRotationItems = new List<SubcontractorRotationItemModel>();


                var rotations = RotationSubcontractor.GetByAccountId(AccountDetail.Id);
                foreach (var sub in await Account.GetByCompanyAsync(WebGlobal.CurrentUser.Company, Extric.Towbook.Accounts.AccountType.Subcontractor))
                {
                    var hasLight = rotations.Any(o => o.SubcontractorAccountId == sub.Id && o.BodyTypeId == 1);
                    var hasHeavy = rotations.Any(o => o.SubcontractorAccountId == sub.Id && o.BodyTypeId == 3);

                    SubcontractorRotationItems.Add(new SubcontractorRotationItemModel()
                    {
                        HasLight = hasLight,
                        HasHeavy = hasHeavy,
                        Company = sub.Company,
                        Id = sub.Id
                    });
                }

                AccountTagIds = (await AccountTag.GetByAccountIdAsync(AccountDetail.Id)).Select(o => o.AccountTagId).ToList();

                if (AccountDetail.Type == AccountType.PrivateProperty)
                {
                    privatePropertyVisible = true;

                    cbEnableParkingPermitsVisible = await WebGlobal.CurrentUser.Company.HasFeatureAsync(Features.ParkingPermits);
                    cbEnableStickeringVisible = await WebGlobal.CurrentUser.Company.HasFeatureAsync(Features.Stickering);
                    cbEnableSiteVisitsVisible = await WebGlobal.CurrentUser.Company.HasFeatureAsync(Features.SiteVisits);

                    EnablePermits = await WebGlobal.CurrentUser.Company.HasFeatureAsync(Features.ParkingPermits);
                    EnableStickering = await WebGlobal.CurrentUser.Company.HasFeatureAsync(Features.Stickering);
                    EnableSiteVisits = await WebGlobal.CurrentUser.Company.HasFeatureAsync(Features.SiteVisits);

                    if (cbEmailText == null)
                    {
                        cbEmailText = "Email Notice Automatically";
                    }
                    else
                    {
                        cbEmailText = "Email Notice Automatically";
                    }


                    pn = AccountKeyValue.GetByAccount(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "ParkingPermitsEnabled").FirstOrDefault();

                    if (pn != null && pn.Value == "1" || pn == null)
                    {
                        cbEnableParkingPermitsChecked = true;
                        EnablePermits = true;
                    }

                    var s = AccountKeyValue.GetByAccount(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "StickeringEnabled").FirstOrDefault();

                    if (s != null && s.Value == "1" || s == null)
                    {
                        cbEnableStickeringChecked = true;
                        EnableStickering = true;
                    }

                    var sv = AccountKeyValue.GetByAccount(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "SiteVisitsEnabled").FirstOrDefault();

                    if (sv != null && sv.Value == "1" || sv == null)
                    {
                        cbEnableSiteVisitsChecked = true;
                        EnableSiteVisits = true;
                    }

                }
                else
                {
                    cbEnableParkingPermitsVisible = false;
                    cbEnableStickeringVisible = false;
                    cbEnableSiteVisitsVisible = false;
                    EnableStickering = false;
                    EnablePermits = false;
                    EnableSiteVisits = false;
                }

                // Load rate items and rules
                RateItemsJson = Extric.Towbook.RateItem.GetByCompanyId(AccountDetail.CompanyId)
                    .Select(o => new
                    {
                        Name = o.Name,
                        Id = o.RateItemId,
                        o.CategoryId,
                        PredefinedRateItemId = o.Predefined != null ? o.Predefined.Id : -1
                    })
                    .ToJson();

                RulesJson = AccountRateItemRule.GetByAccountId(AccountId)
                    .Select(o => AccountRateItemRuleModel.Map(o))
                    .ToJson();

                ShowAllRateItems = (await RateItemHelper.GetAllRateItems(Account.GetById(AccountId), false)).Count() == 0;

                // Load fuel surcharge settings
                var sr = await SurchargeRate.GetBySurchargeAsync(Surcharge.SURCHARGE_FUEL, WebGlobal.CurrentUser.Company.Id);
                var sar = AccountId > 1 ?
                    await SurchargeAccountRate.GetBySurchargeAsync(Surcharge.SURCHARGE_FUEL, AccountId) : null;

                if (sr != null && sar == null)
                {
                    FuelSurchargeNoteText = $"Your companies standard fuel surcharge rate of {(sr.Rate * 100):0.00}% will apply to this account if you leave the box above blank. If you don't want to charge any fuel charge for this account, enter 0 in the box.<br /><br />&nbsp;";
                    IsFuelSurchargeEnabled = true;
                }

                if (sar != null)
                {
                    IsFuelSurchargeEnabled = true;
                    FuelSurcharge = (sar.Rate * 100).ToString("0.00");
                }

                // Load auto-accept settings
                ShowAutoAccept = await WebGlobal.CurrentUser.Company.HasFeatureAsync(Features.AutoDispatch);
                if (ShowAutoAccept)
                {
                    var autoAcceptSetting = AccountKeyValue.GetByAccount(AccountDetail.CompanyId, AccountDetail.Id,
                        Provider.Towbook.ProviderId, "DigitalAutoAccept").FirstOrDefault();
                    if (autoAcceptSetting != null)
                    {
                        autoAcceptJson = autoAcceptSetting.Value;
                    }
                }

                // Load statement and invoice settings
                if (await WebGlobal.CurrentUser.Company.HasFeatureAsync(Features.AdvancedBilling))
                {
                    var ieo = await InvoiceEmailOption.GetByCompanyIdAsync(AccountDetail.CompanyId, AccountDetail.Id);
                    var seo = StatementEmailOption.GetByCompanyId(AccountDetail.CompanyId, AccountDetail.Id);

                    if (ieo != null)
                    {
                        InvoiceSubject = Core.HtmlEncode(ieo.Subject);
                        InvoiceMessage = Core.HtmlEncode(ieo.Message);
                    }

                    if (seo != null)
                    {
                        StatementSubject = Core.HtmlEncode(seo.Subject);
                        StatementMessage = Core.HtmlEncode(seo.Message);
                    }
                }

                rpImpoundLotsDataSource = (await Lot.GetByCompanyAsync(WebGlobal.CurrentUser.Company, AccountDetail)).ToList();


                rpImpoundLotsVisible = rpImpoundLotsDataSource.Count > 0;

                this.ThirdPartyImpoundLots = new();
                this.AllAccountTags = await Extric.Towbook.Accounts.AccountTag.GetByCompanyIdAsync(WebGlobal.CurrentUser.Company.Id);


                // TODO: optimize
                foreach (Extric.Towbook.Accounts.Account acc in await Extric.Towbook.Accounts.Account.GetByCompanyAsync(WebGlobal.CurrentUser.Company,
                      Extric.Towbook.Accounts.AccountType.StorageFacility))
                {

                    foreach (Lot iLot in await Lot.GetByCompanyAsync(WebGlobal.CurrentUser.Company, acc))
                    {
                        ThirdPartyImpoundLots.Add(iLot);
                    }
                }

                        txtCompany = AccountDetail.Company;
                txtContactName = AccountDetail.FullName;
                txtAddress = AccountDetail.Address;
                txtCity = AccountDetail.City;
                txtState = AccountDetail.State;
                txtZip = AccountDetail.Zip;
                txtEmail = AccountDetail.Email;
                txtPhone = Core.FormatPhone(AccountDetail.Phone, WebGlobal.CurrentUser.Company);
                txtFax = AccountDetail.Fax;
                cbTaxExemptChecked = AccountDetail.TaxExempt;

                if (ShowCreditOptions)
                {
                    if (AccountDetail.CreditLimit != null)
                        txtCreditLimit = AccountDetail.CreditLimit.Value.ToString("0.00");

                    cbCreditHoldChecked = AccountDetail.CreditHold;
                }

                var billingAddress = (await AddressBookEntry.GetByAccountIdAsync(this.AccountId)).Where(o => o.Name == this.AddressBookNameMapOrBilling).FirstOrDefault();

                if (billingAddress != null)
                {
                    txtBillingAddress = billingAddress.Address;
                    txtBillingCity = billingAddress.City;
                    txtBillingState = billingAddress.State;
                    txtBillingZip = billingAddress.Zip;
                    txtBillingContactEmail = billingAddress.Email;
                    txtBillingContactName = string.IsNullOrWhiteSpace(billingAddress.Notes) ? "" : billingAddress.Notes.Replace("RecipientDisplayName:", "");
                }

                cbInactiveChecked = AccountDetail.Status == AccountStatus.Inactive;

                txtNotes = AccountDetail.Notes;

                discountRate = (AccountDetail.DiscountRate * 100).ToString("0.00");

                ddlTypeSelectedValue = Convert.ToInt32(AccountDetail.Type).ToString();

                txtDefaultPO = AccountDetail.DefaultPO;

                cbDefaultPriorityChecked = AccountDetail.DefaultPriority == 1;

                var priorityCallTextAlertForManagers = AccountKeyValue.GetByAccount(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "EnablePriorityCallTextAlertToAllManagers").FirstOrDefault();
                if (priorityCallTextAlertForManagers != null)
                    cbPriorityCallTextAlertToManagersChecked = (priorityCallTextAlertForManagers.Value == "1");

                var setting = CompanyKeyValue.GetFirstValueOrNull(AccountDetail.CompanyId,
                    Provider.Towbook.ProviderId, "HideChargesFromMotorClubInvoicesByDefault");

                var alwaysHide = AccountKeyValue.GetByAccount(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "AlwaysHideCharges").FirstOrDefault();
                if (alwaysHide != null)
                    cbAlwaysHideChargesChecked = (alwaysHide.Value == "1");
                else
                {
                    if (AccountDetail.Type == AccountType.MotorClub)
                    {
                        var companyHideMCCharges = CompanyKeyValue.GetFirstValueOrNull(AccountDetail.CompanyId,
                           Provider.Towbook.ProviderId, "HideChargesFromMotorClubInvoicesByDefault") ?? "1";

                        cbAlwaysHideChargesChecked = (companyHideMCCharges == "1");
                    }
                }

                var alwaysHidePhotos = AccountKeyValue.GetByAccount(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "AlwaysHidePhotos").FirstOrDefault();

                if (alwaysHidePhotos != null && alwaysHidePhotos.Value == "1")
                    cbAlwaysHidePhotosChecked = true;

                var key = AccountKeyValue.GetByAccount(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "AlwaysHideDiscounts").FirstOrDefault();
                if (key != null && key.Value == "1")
                    cbAlwaysHideDiscountsChecked = true;

                CompanyReplytoEmail = WebGlobal.CurrentUser.Company.Email;
                CompanyReplytoEmailSource = "company email";
                billingAddress = AddressBookEntry.GetByName("Billing Address", AccountDetail.CompanyId, false).FirstOrDefault();
                if (billingAddress != null && Core.IsEmailValid(billingAddress.Email))
                {
                    CompanyReplytoEmail = billingAddress.Email;
                    CompanyReplytoEmailSource = "billing address email";
                }

                var replyToEmail = AccountKeyValue.GetByAccount(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "ReplyToEmailAddress").FirstOrDefault();
                if (replyToEmail != null && !string.IsNullOrWhiteSpace(replyToEmail.Value))
                    txtReplyToEmail = replyToEmail.Value;

                ddlEmailEventPreferenceItems = new List<SelectListItem>
            {
                new SelectListItem("Call is Completed", "0"),
                new SelectListItem("Call is Marked Audited", "1")
            };


                var emailPreference = AccountKeyValue.GetByAccount(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "EmailInvoiceEventPreference").FirstOrDefault();
                if (emailPreference != null)
                {
                    lblEmailEventPreferenceText = "Email Invoice preference (using account setting)";
                    ddlEmailEventPreferenceSelectedValue = emailPreference.Value;
                }
                else
                {
                    var companyEmailPreference = CompanyKeyValue.GetFirstValueOrNull(WebGlobal.CurrentUser.CompanyId,
                        Provider.Towbook.ProviderId, "EmailInvoiceEventPreference") ?? "0";

                    lblEmailEventPreferenceText = "Email Invoice preference (using company preference)";
                    ddlEmailEventPreferenceSelectedValue = companyEmailPreference;
                }


                key = AccountKeyValue.GetByAccount(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "AccountPhysicalAddressAsDefaultLocation").FirstOrDefault();
                if (key != null && key.Value != "0")
                {
                    cbUsePhysicalAddressAsChecked = true;

                    ddlDefaultPhysicalAddressSelectedValue = key.Value;
                }

                ddlDefaultPhysicalAddressEnabled = false;
                if (cbUsePhysicalAddressAsChecked == true)
                    ddlDefaultPhysicalAddressEnabled = true;


                key = AccountKeyValue.GetByAccount(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "AccountContactAddedAtCallCreation").FirstOrDefault();
                if (key != null && key.Value == "1")
                    cbAutoFillContactChecked = true;


                if (await WebGlobal.CurrentUser.Company.HasFeatureAsync(Features.PaymentIntegrations_Square))
                {
                    var companyIncludeInvoice = CompanyKeyValue.GetFirstValueOrNull(AccountDetail.CompanyId, Provider.Towbook.ProviderId, "Square_AlwaysIncludePaymentLinkOnInvoices");
                    var companyIncludeStatement = CompanyKeyValue.GetFirstValueOrNull(AccountDetail.CompanyId, Provider.Towbook.ProviderId, "Square_AlwaysIncludePaymentLinkOnStatements");
                    var companyOptOutOfSquareEmails = CompanyKeyValue.GetFirstValueOrNull(AccountDetail.CompanyId, Provider.Towbook.ProviderId, "Square_OptOutOfEmailsOnTransactions");


                    var includeInvoicePaymentLink = AccountKeyValue.GetByAccount(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "Square_AlwaysIncludePaymentLinkOnInvoices").FirstOrDefault();

                    if (includeInvoicePaymentLink != null)
                    {
                        CbIncludeInvoicePaymentLinkText = CbIncludeInvoicePaymentLinkText + " (using account settings)";

                        if (includeInvoicePaymentLink.Value == "1")
                            cbIncludeInvoicePaymentLinkChecked = true;
                        else
                            cbIncludeStatementPaymentLinkChecked = false;
                    }
                    else
                    {
                        CbIncludeInvoicePaymentLinkText = CbIncludeInvoicePaymentLinkText + " (using company settings)";
                        cbIncludeInvoicePaymentLinkChecked = companyIncludeInvoice == "1";
                    }

                    var includeStatementPaymentLink = AccountKeyValue.GetByAccount(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "Square_AlwaysIncludePaymentLinkOnStatements").FirstOrDefault();

                    if (includeStatementPaymentLink != null)
                    {
                        CbIncludeStatementPaymentLinkText = CbIncludeStatementPaymentLinkText + " (using account settings)";

                        if (includeStatementPaymentLink.Value == "1")
                            cbIncludeStatementPaymentLinkChecked = true;
                        else
                            cbIncludeStatementPaymentLinkChecked = false;
                    }
                    else
                    {
                        CbIncludeStatementPaymentLinkText = CbIncludeStatementPaymentLinkText + " (using company settings)";
                        cbIncludeStatementPaymentLinkChecked = companyIncludeStatement == "1";
                    }


                    var optOutOfSquareEmails = AccountKeyValue.GetByAccount(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "Square_OptOutOfEmailsOnTransactions").FirstOrDefault();

                    if (optOutOfSquareEmails != null)
                    {
                        CbOptOutOfConfirmationEmailOnTransactionsText = CbOptOutOfConfirmationEmailOnTransactionsText + " (using account settings)";
                        if (optOutOfSquareEmails.Value != "1")
                            cbOptOutOfConfirmationEmailOnTransactionsChecked = true;
                        else
                            cbOptOutOfConfirmationEmailOnTransactionsChecked = false;
                    }
                    else
                    {
                        CbOptOutOfConfirmationEmailOnTransactionsText = CbOptOutOfConfirmationEmailOnTransactionsText + " (using company settings)";
                        cbOptOutOfConfirmationEmailOnTransactionsChecked = companyOptOutOfSquareEmails != "1";
                    }


                    var companyExcludeDispatchInvoice = CompanyKeyValue.GetFirstValueOrNull(AccountDetail.CompanyId, Provider.Towbook.ProviderId, "Square_ExcludeLinkOnPrintedDispatchInvoices");
                    var companyExcludeImpoundInvoice = CompanyKeyValue.GetFirstValueOrNull(AccountDetail.CompanyId, Provider.Towbook.ProviderId, "Square_ExcludeLinkOnPrintedImpoundInvoices");
                    var companyExcludeStatement = CompanyKeyValue.GetFirstValueOrNull(AccountDetail.CompanyId, Provider.Towbook.ProviderId, "Square_ExcludeLinkOnPrintedStatements");

                    var excludeAccountSetting = AccountKeyValue.GetByAccount(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "Square_ExcludeLinkOnPrintedDispatchInvoices").FirstOrDefault();
                    if (excludeAccountSetting != null)
                    {
                        CbExcludeLinkOnPrintedDispatchInvoicesText = CbExcludeLinkOnPrintedDispatchInvoicesText + " (using account settings)";
                        if (excludeAccountSetting.Value == "1")
                            cbExcludeLinkOnPrintedDispatchInvoicesChecked = false;
                        else
                            cbExcludeLinkOnPrintedDispatchInvoicesChecked = true;
                    }
                    else
                    {
                        CbExcludeLinkOnPrintedDispatchInvoicesText = CbExcludeLinkOnPrintedDispatchInvoicesText + " (using company settings)";
                        cbExcludeLinkOnPrintedDispatchInvoicesChecked = companyExcludeDispatchInvoice == "1";
                    }

                    excludeAccountSetting = AccountKeyValue.GetByAccount(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "Square_ExcludeLinkOnPrintedImpoundInvoices").FirstOrDefault();
                    if (excludeAccountSetting != null)
                    {
                        CbExcludeLinkOnPrintedImpoundInvoicesText = CbExcludeLinkOnPrintedImpoundInvoicesText + " (using account settings)";
                        if (excludeAccountSetting.Value == "1")
                            cbExcludeLinkOnPrintedImpoundInvoicesChecked = false;
                        else
                            cbExcludeLinkOnPrintedImpoundInvoicesChecked = true;
                    }
                    else
                    {
                        CbExcludeLinkOnPrintedImpoundInvoicesText = CbExcludeLinkOnPrintedImpoundInvoicesText + " (using company settings)";
                        cbExcludeLinkOnPrintedImpoundInvoicesChecked = companyExcludeImpoundInvoice == "1";
                    }

                    excludeAccountSetting = AccountKeyValue.GetByAccount(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "Square_ExcludeLinkOnPrintedStatements").FirstOrDefault();
                    if (excludeAccountSetting != null)
                    {
                        CbExcludeLinkOnPrintedStatementsText = CbExcludeLinkOnPrintedStatementsText + " (using account settings)";

                        if (excludeAccountSetting.Value == "1")
                            cbExcludeLinkOnPrintedStatementsChecked = false;
                        else
                            cbExcludeLinkOnPrintedStatementsChecked = true;
                    }
                    else
                    {
                        CbExcludeLinkOnPrintedStatementsText = CbExcludeLinkOnPrintedStatementsText + " (using company settings)";
                        cbExcludeLinkOnPrintedStatementsChecked = companyExcludeStatement == "1";
                    }


                    var companyTippingExcludeOnPaymentLinks = CompanyKeyValue.GetFirstValueOrNull(AccountDetail.CompanyId, Provider.Towbook.ProviderId, "SquareTipping_ExcludeOnPaymentLinks");
                    var companyTippingExcludeOnSquareReader = CompanyKeyValue.GetFirstValueOrNull(AccountDetail.CompanyId, Provider.Towbook.ProviderId, "SquareTipping_ExcludeOnSquareReader");
                    var companyTippingExcludeOnSquareTerimal = CompanyKeyValue.GetFirstValueOrNull(AccountDetail.CompanyId, Provider.Towbook.ProviderId, "SquareTipping_ExcludeOnSquareTerminal");

                    excludeAccountSetting = AccountKeyValue.GetByAccount(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "SquareTipping_ExcludeOnPaymentLinks").FirstOrDefault();
                    if (excludeAccountSetting != null)
                    {
                        CbExcludeTipsOnPaymentLinksText = CbExcludeTipsOnPaymentLinksText + " (using account settings)";

                        if (excludeAccountSetting.Value == "1")
                            cbExcludeTipsOnPaymentLinksChecked = false;
                        else
                            cbExcludeTipsOnPaymentLinksChecked = true;
                    }
                    else
                    {
                        CbExcludeTipsOnPaymentLinksText = CbExcludeTipsOnPaymentLinksText + " (using company settings)";
                        cbExcludeTipsOnPaymentLinksChecked = companyTippingExcludeOnPaymentLinks != "1";
                    }

                    excludeAccountSetting = AccountKeyValue.GetByAccount(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "SquareTipping_ExcludeOnSquareReader").FirstOrDefault();
                    if (excludeAccountSetting != null)
                    {
                        CbExcludeTipsOnSquareReaderText = CbExcludeTipsOnSquareReaderText + " (using account settings)";

                        if (excludeAccountSetting.Value == "1")
                            cbExcludeTipsOnSquareReaderChecked = false;
                        else
                            cbExcludeTipsOnSquareReaderChecked = true;
                    }
                    else
                    {
                        CbExcludeTipsOnSquareReaderText = CbExcludeTipsOnSquareReaderText + " (using company settings)";
                        cbExcludeTipsOnSquareReaderChecked = companyTippingExcludeOnSquareReader != "1";
                    }

                    excludeAccountSetting = AccountKeyValue.GetByAccount(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "SquareTipping_ExcludeOnSquareTerminal").FirstOrDefault();
                    if (excludeAccountSetting != null)
                    {
                        CbExcludeTipsOnSquareTerminalText = CbExcludeTipsOnSquareTerminalText + " (using account settings)";

                        if (excludeAccountSetting.Value == "1")
                            cbExcludeTipsOnSquareTerminalChecked = false;
                        else
                            cbExcludeTipsOnSquareTerminalChecked = true;
                    }
                    else
                    {
                        CbExcludeTipsOnSquareTerminalText = CbExcludeTipsOnSquareTerminalText + " (using company settings)";
                        cbExcludeTipsOnSquareTerminalChecked = companyTippingExcludeOnSquareTerimal != "1";
                    }

                }

                cbAlwaysSendRoadsideInviteChecked = false;
                cbAlwaysSendSurveyChecked = false;

                if (await WebGlobal.CurrentUser.Company.HasFeatureAsync(Features.Roadside))
                {
                    var rs = Extric.Roadside.RoadsideSetting.GetByCompanyId(AccountDetail.CompanyId, AccountDetail.Id);
                    var items = Extric.Roadside.JobProgressTextAlertItem.GetByCompanyId(AccountDetail.CompanyId, true);


                    if (items.Count() == 0)
                        items = Extric.Roadside.JobProgressTextAlertItem.GetDefaults(AccountDetail.CompanyId, AccountDetail.Id);

                    bool atLeastOneProgressText = items.Where(w => new int[] {
                        Extric.Roadside.JobProgressStatusType.Created.Id,
                        Extric.Roadside.JobProgressStatusType.Enroute.Id,
                        Extric.Roadside.JobProgressStatusType.Arriving.Id }.Contains(w.StatusTypeId) &&
                        !w.IsDeleted).Any();



                    var item = items.Where(w => w.StatusTypeId == Extric.Roadside.JobProgressStatusType.Completed.Id).LastOrDefault();

                    if (AccountDetail.Type == AccountType.MotorClub)
                    {
                        if (rs.EnableMotorClubAutoInvite.GetValueOrDefault() && atLeastOneProgressText)
                            cbAlwaysSendRoadsideInviteChecked = true;

                        if (rs.EnableMotorClubAutoInvite.GetValueOrDefault() && (item == null || !item.IsDeleted))
                            cbAlwaysSendSurveyChecked = true;
                    }

                    if (AccountDetail.Type != AccountType.MotorClub)
                    {
                        if (rs.EnableNonMotorClubAutoInvite.GetValueOrDefault() && atLeastOneProgressText)
                            cbAlwaysSendRoadsideInviteChecked = true;

                        if (rs.EnableNonMotorClubAutoInvite.GetValueOrDefault() && (item == null || !item.IsDeleted))
                            cbAlwaysSendSurveyChecked = true;
                    }

                    if (AccountDetail.Type == AccountType.Municipality ||
                        AccountDetail.Type == AccountType.PoliceDepartment ||
                        AccountDetail.Type == AccountType.PrivateProperty ||
                        (AccountDetail.Type == AccountType.MotorClub &&
                            (AccountDetail.MasterAccountId == 2 ||
                             AccountDetail.MasterAccountId == 3 ||
                             AccountDetail.MasterAccountId == 28 ||
                             AccountDetail.MasterAccountId == 29 ||
                             AccountDetail.MasterAccountId == 30 ||
                             AccountDetail.MasterAccountId == 31 ||
                             AccountDetail.MasterAccountId == 36
                            )))
                    {
                        cbAlwaysSendRoadsideInviteVisible = false;
                        cbAlwaysSendSurveyVisible = false;
                        labelRoadsideVisible = false;
                    }

                    key = AccountKeyValue.GetByAccount(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "AutoSendRoadsideInvite").FirstOrDefault();
                    if (key != null)
                    {
                        if (key.Value == "1")
                            cbAlwaysSendRoadsideInviteChecked = true;
                        else if (key.Value == "0")
                            cbAlwaysSendSurveyChecked = false;

                        if (key.Value == "1" || key.Value == "0")
                            cbAlwaysSendRoadsideInviteText = cbAlwaysSendRoadsideInviteText + " (using account settings)";

                    }
                    else
                    {
                        cbAlwaysSendRoadsideInviteText = cbAlwaysSendRoadsideInviteText + " (using company settings)";
                    }


                    key = AccountKeyValue.GetByAccount(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "AlwaysSendSurvey").FirstOrDefault();
                    if (key != null)
                    {
                        if (key.Value == "1")
                            cbAlwaysSendSurveyChecked = true;
                        else if (key.Value == "0")
                            cbAlwaysSendSurveyChecked = false;

                        if (key.Value == "1" || key.Value == "0")
                            cbAlwaysSendSurveyText = cbAlwaysSendSurveyText + " (using account settings)";
                    }
                    else
                    {
                        cbAlwaysSendSurveyText = cbAlwaysSendSurveyText + " (using company settings)";
                    }
                }

                cbIncludeInvoiceCopiesOnStatementsChecked = AccountKeyValue.GetFirstValueOrNull(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "IncludeInvoicesWithCopyOfStatement") == "1";

                ddlSuggestedDefaultItems = new List<SelectListItem>
                {
                    new SelectListItem("Optimal (Google Suggested Route)", "1"),
                    new SelectListItem("Shortest Distance", "2"),
                    new SelectListItem("Fastest Time", "3")
                };
                ddlSuggestedDefaultSelectedValue = AccountKeyValue.GetFirstValueOrNull(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "DefaultSuggestedMileageRoute") ?? "1";

                if (await WebGlobal.CurrentUser.Company.HasFeatureAsync(Features.GPS_AutomaticMileage) || await WebGlobal.CurrentUser.Company.HasFeatureAsync(Features.Options_AutomaticallyAddMiles))
                {
                    var companyAutoAddSetting = CompanyKeyValue.GetFirstValueOrNull(
                        AccountDetail.CompanyId,
                        Provider.Towbook.ProviderId,
                        "Towbook_Calls_AutomaticallyAddMiles") ?? "0";

                    if (await WebGlobal.CurrentUser.Company.HasFeatureAsync(Features.Options_AutomaticallyAddMiles))
                        companyAutoAddSetting = "1";

                    string autoMilesText = companyAutoAddSetting == "1" ? "Currently set to add automatically" : "Currently set to not add automatically";

                    var accountAutoAddSetting = AccountKeyValue.GetFirstValueOrNull(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "AutomaticallyAddMiles") ?? "0";

                    ddlAutomaticallyFillInMilesItems = new List<SelectListItem>
                    {
                        new SelectListItem("Use Company Default - " + autoMilesText, "0"),
                        new SelectListItem("Always add to invoice automatically", "1"),
                        new SelectListItem("Never add to invoice automatically", "2")
                    };

                    ddlAutomaticallyFillInMilesSelectedValue = accountAutoAddSetting ?? "0";
                }

                cbUnloadedMileageSetOneChecked = AccountKeyValue.GetFirstValueOrNull(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "MCBilling_ForceUnloadedMilesToOneIfMissing") == "1";
                cbUnloadedMileageRoundOneChecked = AccountKeyValue.GetFirstValueOrNull(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "RoundUpCalculatedMiles") == "1";

                cbUnloadedMileageRoundOneText = "When calculating mileage, round up to the next full " + WebGlobal.CurrentUser.Company.LocaleMile;
                cbUnloadedMileageRoundOneToolTip = "Towbook will round the mileage up to the next full " + WebGlobal.CurrentUser.Company.LocaleMile;

                if (await WebGlobal.CurrentUser.Company.HasFeatureAsync(Features.DeadheadMileage))
                    cbAlwaysAddDeadheadChecked = AccountKeyValue.GetFirstValueOrNull(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "AutomaticallyAddDeadheadMileage") == "1";

                switch (AccountDetail.InvoiceTerms)
                {
                    case Account.InvoiceTerm.Immediate:
                        rbPaymentImmediateChecked = true;
                        break;

                    case Account.InvoiceTerm.Net15:
                        rbPaymentNet15Checked = true;
                        break;

                    case Account.InvoiceTerm.Net30:
                        rbPaymentNet30Checked = true;
                        break;
                }

                string idt = "default";

                switch (AccountDetail.ImpoundDestinationType)
                {
                    case AccountImpoundDestination.None:
                        rbStorageLotsNoneChecked = true;
                        idt = "default";
                        break;
                    case AccountImpoundDestination.Default:
                        rbStorageLotsDefaultChecked = true;
                        idt = "default";
                        break;
                    case AccountImpoundDestination.ThirdParty:
                        rbStorageLotThirdPartyChecked = true;
                        idt = "other";
                        break;
                }

                if (AccountDetail.Type == AccountType.StorageFacility)
                {
                    ImpoundsChangePgScript = $"impounds_changePg('{idt}')";
                }


                cbEmailChecked = AccountDetail.InvoiceDeliverByEmail;
                cbFaxChecked = AccountDetail.InvoiceDeliverByFax;
                cbMailChecked = AccountDetail.InvoiceDeliverByMail;

                if (StorageRateDetail.MaximumCharge != null && StorageRateDetail.MaximumCharge > 0)
                    txtMaximumCharges = StorageRateDetail.MaximumCharge.Value.ToString("#0.00");

                var storageItems = Extric.Towbook.RateItem.GetByCompanyId(AccountDetail.CompanyId, AccountDetail)
                    .Where(w => (w.IsStorageItem() || w.IsTieredStorageItem()) && !w.IsTimeBasedItem() && w.ParentRateItemId == 0)
                    .OrderByDescending(b => b.Name.Contains("Daily Impound Rate"))
                    .ThenBy(b => b.Name)
                    .Select(s => new SelectListItem(s.Name, s.RateItemId.ToString()))
                    .ToList();

                if (storageItems != null)
                    storageItems.Insert(0, new SelectListItem("No default specified", "0"));

                ddlDefaultStorageItems = storageItems;

                if (AccountDetail.DefaultStorageRateItemId.GetValueOrDefault() != 0)
                    ddlDefaultStorageSelectedValue = AccountDetail.DefaultStorageRateItemId.ToString();

                ddlDefaultAssetBodyTypeItems = (await BodyType.GetByCompanyIdAsync(AccountDetail.CompanyId))
                    .Select(s => new SelectListItem(s.Name, s.Id.ToString())).ToList();

                ddlDefaultBillToAccountItems = new List<SelectListItem> { new SelectListItem("None", "-1") };
                ddlDefaultBillToAccountItems.AddRange(
                    (await Account.GetByCompanyAsync(await Company.Company.GetByIdAsync(AccountDetail.CompanyId)))
                    .Select(a => new SelectListItem(a.Company, a.Id.ToString()))
                    .ToList());

                var defaultBillTo = AccountKeyValue.GetFirstValueOrNull(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "DefaultBillToAccountId");
                ddlDefaultBillToAccountSelectedValue = defaultBillTo ?? "-1";

                key = AccountKeyValue.GetByAccount(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "DefaultAssetBodyTypeId").FirstOrDefault();
                if (key != null && key.Value != "0")
                    ddlDefaultAssetBodyTypeSelectedValue = key.Value;
                else
                    ddlDefaultAssetBodyTypeSelectedValue = "1";

                if (await WebGlobal.CurrentUser.Company.HasFeatureAsync(Features.DispatchToSubcontractors))
                {
                    ddlSubcontractorsItems = new List<SelectListItem> { new SelectListItem("(None)", "0") };

                    if (await WebGlobal.CurrentUser.Company.HasFeatureAsync(Features.DispatchToSubcontractors_SubcontractorRotation))
                        ddlSubcontractorsItems.Add(new SelectListItem("Use Automatic Subcontractor Rotation", "-9"));

                    if (WebGlobal.CurrentUser.Company.Id == 96242 ||
                        WebGlobal.CurrentUser.Company.Id == 101960 ||
                        await WebGlobal.CurrentUser.Company.HasFeatureAsync(Features.DispatchToSubcontractors_SendtoAllProviders))
                        ddlSubcontractorsItems.Add(new SelectListItem("Send to all subcontractors", "-10"));

                    if (await WebGlobal.CurrentUser.Company.HasFeatureAsync(Features.DispatchToSubcontractors_SendtoNearestProviders))
                        ddlSubcontractorsItems.Add(new SelectListItem("Send to nearest providers", "-11"));

                    ddlSubcontractorsItems.AddRange((await Account.GetByCompanyAsync(Company.Company.GetById(AccountDetail.CompanyId),
                        AccountType.Subcontractor)).Select(o => new SelectListItem(o.Company, o.Id.ToString())).ToList());

                    ddlSubcontractorsSelectedValue = AccountKeyValue.GetFirstValueOrNull(
                        AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId,
                        "DefaultSubcontractorAccountId") ?? "0";

                    towbookEmailAddress = AccountKeyValue.GetFirstValueOrNull(
                        AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId,
                        "DSC_SubcontractorAddress") ?? "";

                    txtMobileNumbers = AccountKeyValue.GetFirstValueOrNull(
                        AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId,
                        "SubcontractorMobileNumbers") ?? "";
                }

                mcTowbookEmailAddress = AccountKeyValue.GetFirstValueOrNull(
                    AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId,
                    "DSC_SenderAddress") ?? "";

                if (WebGlobal.CurrentUser.HasAccessToCompany(119800))
                {
                    towbookReferenceNumber = AccountKeyValue.GetFirstValueOrNull(
                        AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId,
                        "CustomReferenceNumber") ?? "";
                }

                trCompanyOverrideVisible = false;
                if (WebGlobal.Companies.Count() > 1)
                {
                    trCompanyOverrideVisible = true;

                    ddlCompanyOverrideItems = WebGlobal.Companies.OrderBy(o => o.Name)
                        .Select(li => new SelectListItem(li.Name, li.Id.ToString())).ToList();

                    key = AccountKeyValue.GetByAccount(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "OverrideToSharedCompanyId").FirstOrDefault();
                    int overrideCompanyId = AccountDetail.CompanyId;
                    if (key != null && int.TryParse(key.Value, out overrideCompanyId))
                        ddlCompanyOverrideSelectedValue = key.Value;
                    else
                        ddlCompanyOverrideSelectedValue = overrideCompanyId.ToString();
                }


                var allRuleSets = EntryValidationRuleSet.GetAllByCompanyIds(new[] { AccountDetail.CompanyId });

                EntryValidationRuleSetDetail = allRuleSets.FirstOrDefault(f => f.AccountId == AccountDetail.Id);

                var accountTypeDefaultRuleSet = allRuleSets.FirstOrDefault(x => x.AccountTypeId == (int)AccountDetail.Type && x.AccountId == null);
                var companyDefaultRuleSet = allRuleSets.FirstOrDefault(x => x.AccountId == null && x.AccountTypeId == null);

                if (EntryValidationRuleSetDetail == null)
                {
                    EntryValidationRuleSetDetail = new EntryValidationRuleSet();
                    EntryValidationRuleSetDetail.AccountId = AccountDetail.Id;
                    EntryValidationRuleSetDetail.CompanyId = AccountDetail.CompanyId;
                    EntryValidationRuleSetDetail.AccountTypeId = null;
                    EntryValidationRuleSetDetail.OwnerUserId = WebGlobal.CurrentUser.Id;
                }

                SetEntryRuleDropDownListValues(ddlReqDriverContactNameItems, ddlReqDispatcherContactNameItems, companyDefaultRuleSet.RequireContactName, accountTypeDefaultRuleSet.RequireContactName, EntryValidationRuleSetDetail.RequireContactName);
                SetEntryRuleDropDownListValues(ddlReqDriverContactPhoneItems, ddlReqDispatcherContactPhoneItems, companyDefaultRuleSet.RequireContactPhone, accountTypeDefaultRuleSet.RequireContactPhone, EntryValidationRuleSetDetail.RequireContactPhone);

                SetEntryRuleDropDownListValues(ddlReqDriverPurchaseOrderNumberItems, ddlReqDispatcherPurchaseOrderNumberItems, companyDefaultRuleSet.RequirePurchaseOrderNumber, accountTypeDefaultRuleSet.RequirePurchaseOrderNumber, EntryValidationRuleSetDetail.RequirePurchaseOrderNumber);
                SetEntryRuleDropDownListValues(ddlReqDriverInvoiceNumberItems, ddlReqDispatcherInvoiceNumberItems, companyDefaultRuleSet.RequireInvoiceNumber, accountTypeDefaultRuleSet.RequireInvoiceNumber, EntryValidationRuleSetDetail.RequireInvoiceNumber);

                SetEntryRuleDropDownListValues(ddlReqDriverChargesItems, ddlReqDispatcherChargesItems, companyDefaultRuleSet.RequireCharges, accountTypeDefaultRuleSet.RequireCharges, EntryValidationRuleSetDetail.RequireCharges);
                SetEntryRuleDropDownListValues(ddlReqDriverPaidInFullItems, ddlReqDispatcherPaidInFullItems, companyDefaultRuleSet.RequireInvoicePaid, accountTypeDefaultRuleSet.RequireInvoicePaid, EntryValidationRuleSetDetail.RequireInvoicePaid);

                var paidInFullRequired = GetEntryRuleDropDownRequirementType(ddlReqDriverPaidInFullSelectedValue, ddlReqDispatcherPaidInFullSelectedValue);
                cbCodAccountChecked = paidInFullRequired == EntryValidationType.Required || paidInFullRequired == EntryValidationType.RequiredIncludingDispatchers;

                SetEntryRuleDropDownListValues(ddlReqDriverOveragesItems, ddlReqDispatcherOveragesItems, companyDefaultRuleSet.RequireOveragesPaid, accountTypeDefaultRuleSet.RequireOveragesPaid, EntryValidationRuleSetDetail.RequireOveragesPaid);
                SetEntryRuleDropDownListValues(ddlReqDriverVinItems, ddlReqDispatcherVinItems, companyDefaultRuleSet.RequireVin, accountTypeDefaultRuleSet.RequireVin, EntryValidationRuleSetDetail.RequireVin);

                SetEntryRuleDropDownListValues(ddlReqDriverLicensePlateItems, ddlReqDispatcherLicensePlateItems, companyDefaultRuleSet.RequirePlateNumber, accountTypeDefaultRuleSet.RequirePlateNumber, EntryValidationRuleSetDetail.RequirePlateNumber);
                SetEntryRuleDropDownListValues(ddlReqDriverOdometerItems, ddlReqDispatcherOdometerItems, companyDefaultRuleSet.RequireOdometer, accountTypeDefaultRuleSet.RequireOdometer, EntryValidationRuleSetDetail.RequireOdometer);

                SetEntryRuleDropDownListValues(ddlReqDriverPhotosItems, ddlReqDispatcherPhotosItems, companyDefaultRuleSet.RequirePhotos, accountTypeDefaultRuleSet.RequirePhotos, EntryValidationRuleSetDetail.RequirePhotos);
                SetEntryRuleDropDownListValues(ddlReqDriverSignatureItems, ddlReqDispatcherSignatureItems, companyDefaultRuleSet.RequireSignature, accountTypeDefaultRuleSet.RequireSignature, EntryValidationRuleSetDetail.RequireSignature);

                SetEntryRuleDropDownListValues(ddlReqDriverDamageFormItems, ddlReqDispatcherDamageFormItems, companyDefaultRuleSet.RequireDamageForm, accountTypeDefaultRuleSet.RequireDamageForm, EntryValidationRuleSetDetail.RequireDamageForm);
                SetEntryRuleDropDownListValues(ddlReqDriverReasonItems, ddlReqDispatcherReasonItems, companyDefaultRuleSet.RequireReason, accountTypeDefaultRuleSet.RequireReason, EntryValidationRuleSetDetail.RequireReason);

                SetEntryRuleDropDownListValues(ddlReqDriverVehicleDestinationItems, ddlReqDispatcherVehicleDestinationItems, companyDefaultRuleSet.RequireVehicleDestination, accountTypeDefaultRuleSet.RequireVehicleDestination, EntryValidationRuleSetDetail.RequireVehicleDestination);
                SetEntryRuleDropDownListValues(ddlReqDriverVehicleYearItems, ddlReqDispatcherVehicleYearItems, companyDefaultRuleSet.RequireVehicleYear, accountTypeDefaultRuleSet.RequireVehicleYear, EntryValidationRuleSetDetail.RequireVehicleYear);

                SetEntryRuleDropDownListValues(ddlReqDriverVehicleMakeItems, ddlReqDispatcherVehicleMakeItems, companyDefaultRuleSet.RequireVehicleMake, accountTypeDefaultRuleSet.RequireVehicleMake, EntryValidationRuleSetDetail.RequireVehicleMake);
                SetEntryRuleDropDownListValues(ddlReqDriverVehicleModelItems, ddlReqDispatcherVehicleModelItems, companyDefaultRuleSet.RequireVehicleModel, accountTypeDefaultRuleSet.RequireVehicleModel, EntryValidationRuleSetDetail.RequireVehicleModel);

                SetEntryRuleDropDownListValues(ddlReqDriverVehicleColorItems, ddlReqDispatcherVehicleColorItems, companyDefaultRuleSet.RequireVehicleColor, accountTypeDefaultRuleSet.RequireVehicleColor, EntryValidationRuleSetDetail.RequireVehicleColor);
                SetEntryRuleDropDownListValues(ddlReqDriverVehicleKeysLocationItems, ddlReqDispatcherVehicleKeysLocationItems, companyDefaultRuleSet.RequireVehicleKeysLocation, accountTypeDefaultRuleSet.RequireVehicleKeysLocation, EntryValidationRuleSetDetail.RequireVehicleKeysLocation);


                GracePeriodStartItems = new Collection<SelectListItem>();
                foreach (Extric.Towbook.Stickering.GracePeriodStartFromType r in Enum.GetValues(typeof(Extric.Towbook.Stickering.GracePeriodStartFromType)))
                {
                    string name = null;
                    int id = (int)r;
                    switch (r)
                    {
                        case Extric.Towbook.Stickering.GracePeriodStartFromType.ActualTime:
                            name = "Actual Time";
                            break;

                        case Extric.Towbook.Stickering.GracePeriodStartFromType.CalendarDayMidnight:
                            name = "Calendar Day Midnight";
                            break;

                        case Extric.Towbook.Stickering.GracePeriodStartFromType.FollowingMidnight:
                            name = "Following Midnight";
                            break;
                    }

                    if (name == null)
                        continue;


                    SelectListItem item = new SelectListItem(name, id.ToString());
                    GracePeriodStartItems.Add(item);
                }

                BooleanItems = new Collection<SelectListItem>();
                BooleanItems.Add(new SelectListItem("No", "0"));
                BooleanItems.Add(new SelectListItem("Yes", "1"));

                ddlManagerApprovalItems = BooleanItems.ToList();

                ddlManagerSignatureRequiredItems = BooleanItems.ToList();

                ddlAllowExtensionsItems = BooleanItems.ToList();

                ddlSessionActivityEmailItems = BooleanItems.ToList();

                ddlDailySummaryEmailItems = BooleanItems.ToList();

                if (await WebGlobal.CurrentUser.Company.HasFeatureAsync(Features.Stickering))
                {
                    var companyGracePeriodSetting = new Extric.Towbook.Stickering.GracePeriodSetting();
                    var accountGracePeriodSetting = new Extric.Towbook.Stickering.GracePeriodSetting();

                    decimal defaultHours = 24;
                    var reasonTimes = Extric.Towbook.Stickering.ReasonTime.GetByAccountId(AccountId).ToList();
                    var stickeringReasons = Extric.Towbook.Stickering.Reason.GetByCompanyId(AccountDetail.CompanyId).OrderBy(a => a.Name);
                    var stickerSetting = Extric.Towbook.Stickering.StickerSetting.GetByAccountId(AccountDetail.CompanyId, AccountDetail.Id);

                    companyGracePeriodSetting = Extric.Towbook.Stickering.GracePeriodSetting.GetByCompanyId(AccountDetail.CompanyId, null);
                    accountGracePeriodSetting = Extric.Towbook.Stickering.GracePeriodSetting.GetByCompanyId(AccountDetail.CompanyId, AccountDetail.Id);

                    if (accountGracePeriodSetting != null)
                        defaultHours = accountGracePeriodSetting.Hours.Value;
                    else if (companyGracePeriodSetting != null)
                        defaultHours = companyGracePeriodSetting.Hours.Value;

                    foreach (Extric.Towbook.Stickering.Reason reason in stickeringReasons)
                    {
                        bool selected = reasonTimes.Count() == 0;
                        Extric.Towbook.Stickering.ReasonTime rt = null;
                        foreach (var item in AccountDetail.Reasons)
                        {
                            if (item.Id == reason.Id)
                            {
                                rt = reasonTimes.Where(w => w.StickerReasonId == item.Id).FirstOrDefault();
                                selected = true;
                                break;
                            }
                        }

                        var rItem = new StickeringReasonTimeItem();
                        rItem.Id = reason.Id;
                        rItem.Name = reason.Name;
                        rItem.Selected = selected;
                        rItem.DefaultHours = defaultHours;
                        if (rt != null)
                        {
                            if (rt.Time != null)
                                rItem.Hours = rt.Time.Value.ToString("G29");
                            else
                                rItem.Hours = "";
                        }


                        int rst = AccountDetail.GetReasonStartType(reason.Id);
                        rItem.StartFromType = Math.Max(rst, 0);

                        RpReasonItems.Add(rItem);
                    }

                    if (accountGracePeriodSetting != null && companyGracePeriodSetting != null &&
                        accountGracePeriodSetting.Hours != companyGracePeriodSetting.Hours)
                    {
                        tbWaitTime = String.Format("{0:0.##}", accountGracePeriodSetting.Hours.Value);
                        tbWaitTimePlaceholder = String.Format("{0:0.##}", companyGracePeriodSetting.Hours.Value);
                    }
                    else if (accountGracePeriodSetting != null)
                    {
                        tbWaitTime = String.Format("{0:0.##}", accountGracePeriodSetting.Hours);
                        tbWaitTimePlaceholder = String.Format("{0:0.##}", defaultHours);
                    }
                    else
                        tbWaitTimePlaceholder = String.Format("{0:0.##}", defaultHours);

                    var cgs = Extric.Towbook.Stickering.StickerSetting.GetByCompanyId(AccountDetail.CompanyId, null);
                    var ags = Extric.Towbook.Stickering.StickerSetting.GetByAccountId(AccountDetail.CompanyId, AccountDetail.Id);

                    int managerApprovalValue = 1;
                    if (ags != null)
                        managerApprovalValue = ags.TowManagerApprovalRequired == true ? 1 : 0;
                    else if (cgs != null)
                        managerApprovalValue = cgs.TowManagerApprovalRequired == true ? 1 : 0;

                    ddlManagerApprovalSelectedValue = managerApprovalValue.ToString();


                    if (await WebGlobal.CurrentUser.Company.HasFeatureAsync(Features.UserSignatures))
                    {
                        int managerSignatureValue = 0;
                        if (ags != null)
                            managerSignatureValue = ags.RequireApprovalSignature == true ? 1 : 0;
                        else if (cgs != null)
                            managerSignatureValue = cgs.RequireApprovalSignature == true ? 1 : 0;

                        ddlManagerSignatureRequiredSelectedValue = managerSignatureValue.ToString();
                    }

                    int allowExtension = 0;
                    if (ags != null)
                        allowExtension = ags.AllowExtensions == true ? 1 : 0;
                    else if (cgs != null)
                        allowExtension = cgs.AllowExtensions == true ? 1 : 0;

                    ddlAllowExtensionsSelectedValue = allowExtension.ToString();

                    int sendActivityEmail = 1;
                    int sendDailyEmail = 1;
                    if (ags != null)
                    {
                        sendActivityEmail = ags.SendActivitySessionEmail == true ? 1 : 0;
                        sendDailyEmail = ags.SendDailySummaryEmail == true ? 1 : 0;
                    }
                    else if (cgs != null)
                    {
                        sendActivityEmail = cgs.SendActivitySessionEmail == true ? 1 : 0;
                        sendDailyEmail = cgs.SendDailySummaryEmail == true ? 1 : 0;
                    }

                    ddlSessionActivityEmailSelectedValue = sendActivityEmail.ToString();
                    ddlDailySummaryEmailSelectedValue = sendDailyEmail.ToString();


                    if (ags != null && ags.ExpirationHours != null)
                        tbExpirationTime = String.Format("{0:0.##}", ags.ExpirationHours.Value);
                    else if (cgs != null && cgs.ExpirationHours != null)
                        tbExpirationTimePlaceholder = String.Format("{0:0.##}", cgs.ExpirationHours.Value);
                    else
                        tbExpirationTimePlaceholder = "never expires";

                    rpStickerReasonsDataSource = RpReasonItems;
                }

                if (await WebGlobal.CurrentUser.Company.HasFeatureAsync(Features.ParkingPermits))
                {
                    var cppSettings = Extric.Towbook.Accounts.ParkingPermitSetting.GetByCompanyId(AccountDetail.CompanyId, null);
                    var appSettings = Extric.Towbook.Accounts.ParkingPermitSetting.GetByCompanyId(AccountDetail.CompanyId, AccountDetail.Id);

                    if (appSettings != null && appSettings.ParkingPermitPublicLinkId != null)
                    {
                        ParkingPermitPublicLinkDetail = ParkingPermitPublicLink.GetById(appSettings.ParkingPermitPublicLinkId.Value);
                        if (ParkingPermitPublicLinkDetail != null)
                        {
                            tbPropertyCode = ParkingPermitPublicLinkDetail.PropertyCode;
                            hiddenPropertyCode = ParkingPermitPublicLinkDetail.PropertyCode;
                        }
                    }

                    if (appSettings != null && cppSettings != null &&
                        appSettings.PermitsPerResident != cppSettings.PermitsPerResident)
                    {
                        tbNumberOfParkingSpaces = appSettings.PermitsPerResident.ToString();
                        tbNumberOfParkingSpacesPlaceholder = cppSettings.PermitsPerResident.ToString();

                    }
                    else if (cppSettings != null)
                    {
                        tbNumberOfParkingSpacesPlaceholder = cppSettings.PermitsPerResident.ToString();
                        if (appSettings != null)
                            tbNumberOfParkingSpaces = appSettings.PermitsPerResident.ToString();
                    }

                    if (appSettings != null && appSettings.GuestPermitsPerResident != 0)
                    {
                        tbNumberOfGuests = appSettings.GuestPermitsPerResident.ToString();
                    }
                    else
                        tbNumberOfGuestsPlaceholder = "0";

                    if (appSettings != null)
                    {
                        ddlExpirationTypeSelectedValue = ((int)appSettings.ExpirationType).ToString();
                    }

                    if (appSettings != null && appSettings.GuestExpirationDays != null)
                    {
                        ddlGuestExpirationDaysSelectedValue = ((int)appSettings.GuestExpirationDays.Value).ToString();
                    }

                    if (appSettings != null)
                    {
                        if (appSettings.RequireGuestVehicleInfo == null)
                            ddlRequireGuestVehicleSelectedValue = "1";
                        else
                            ddlRequireGuestVehicleSelectedValue = ((int)appSettings.RequireGuestVehicleInfo.Value).ToString();
                    }

                    if (appSettings != null)
                    {
                        if (appSettings.AllowGuestPassUpdate == null)
                            ddlAllowGuestPassUpdateSelectedValue = "true";
                        else
                            ddlAllowGuestPassUpdateSelectedValue = appSettings.AllowGuestPassUpdate.Value == true ? "true" : "false";
                    }

                    if (appSettings != null)
                        permitContactEmailChecked = appSettings.RequireEmail;
                    else if (cppSettings != null)
                        permitContactEmailChecked = cppSettings.RequireEmail;

                    if (appSettings != null)
                        permitContactAddressChecked = appSettings.RequireAddress;
                    else if (cppSettings != null)
                        permitContactAddressChecked = cppSettings.RequireAddress;

                    if (appSettings != null)
                        permitVehicleColorChecked = appSettings.RequireColor;
                    else if (cppSettings != null)
                        permitVehicleColorChecked = cppSettings.RequireColor;

                    if (appSettings != null)
                        permitVehicleVinChecked = appSettings.RequireVin;
                    else if (cppSettings != null)
                        permitVehicleVinChecked = cppSettings.RequireVin;

                    if (appSettings != null)
                        permitStateRegistrationChecked = appSettings.RequireVehicleRegistration;
                    else if (cppSettings != null)
                        permitStateRegistrationChecked = cppSettings.RequireVehicleRegistration;

                    if (appSettings != null)
                        permitStateRegistrationExpirationChecked = appSettings.RequireVehicleRegistrationExpiration;
                    else if (cppSettings != null)
                        permitStateRegistrationExpirationChecked = cppSettings.RequireVehicleRegistrationExpiration;

                    var cDisclaimer = Extric.Towbook.Accounts.ParkingPermitDisclaimer.GetByCompanyId(AccountDetail.CompanyId, null);
                    var aDisclaimer = Extric.Towbook.Accounts.ParkingPermitDisclaimer.GetByCompanyId(AccountDetail.CompanyId, AccountDetail.Id);

                    if (aDisclaimer != null && aDisclaimer.AccountId != null)
                        tbDisclaimer = Core.HtmlEncode(aDisclaimer.Content);

                    if (cDisclaimer != null)
                        tbDisclaimerPlaceholder = Core.HtmlEncode(cDisclaimer.Content);
                }

                var accountDisclaimer = InvoiceDisclaimer.GetDefaultByCompanyId(AccountDetail.CompanyId, AccountDetail.Id, false);

                if (accountDisclaimer != null && accountDisclaimer.AccountId != null && accountDisclaimer.AccountId == AccountDetail.Id)
                    tbAccountDisclaimer = accountDisclaimer.Disclaimer;

                var defaultCompanyDisclaimer = InvoiceDisclaimer.GetDefaultByCompanyId(AccountDetail.CompanyId, null, false);
                if (defaultCompanyDisclaimer != null)
                    tbAccountDisclaimerPlaceholder = defaultCompanyDisclaimer.Disclaimer;

                var defaultStatementDisclaimer = Extric.Towbook.Accounts.StatementDisclaimer.GetByCompanyId(AccountDetail.CompanyId, null);
                var statementDisclaimer = Extric.Towbook.Accounts.StatementDisclaimer.GetByCompanyId(AccountDetail.CompanyId, AccountDetail.Id);

                if (defaultStatementDisclaimer != null && !string.IsNullOrEmpty(defaultStatementDisclaimer.Disclaimer))
                    tbStatementDisclaimerPlaceholder = Core.HtmlEncode(defaultStatementDisclaimer.Disclaimer);
                else
                    tbStatementDisclaimerPlaceholder = "Enter a statement disclaimer for this account";

                if (statementDisclaimer != null &&
                    !string.IsNullOrEmpty(statementDisclaimer.Disclaimer) &&
                    statementDisclaimer.AccountId == AccountDetail.Id)
                    tbStatementDisclaimer = Core.HtmlEncode(statementDisclaimer.Disclaimer);


                var dueDateItems = new List<SelectListItem>();
                foreach (var item in EnumExtensionMethods.Enum<DueDateDefaultType>.GetAllValuesAsIEnumerable()
                    .Select(s => new EnumExtensionMethods.EnumTypeDefinitionModel(s)))
                {
                    var li = new SelectListItem(item.Name, item.Id.ToString());
                    dueDateItems.Add(li);
                }

                ddlStatementDueDateItems = dueDateItems;

                var statementOption = StatementOption.GetByCompanyId(AccountDetail.CompanyId, AccountDetail.Id);
                if (statementOption != null && statementOption.DefaultDueDateType != DueDateDefaultType.Net30)
                    ddlStatementDueDateSelectedValue = ((int)statementOption.DefaultDueDateType).ToString();
                else
                    ddlStatementDueDateSelectedValue = ((int)DueDateDefaultType.Net30).ToString();

                var billingMethods = new List<SelectListItem>();
                foreach (var item in EnumExtensionMethods.Enum<Extric.Towbook.Accounts.Account.PreferredBillableMethod>.GetAllValuesAsIEnumerable()
                    .Where(w => w != Extric.Towbook.Accounts.Account.PreferredBillableMethod.Default)
                    .Select(s => new EnumExtensionMethods.EnumTypeDefinitionModel(s)))
                {
                    var li = new SelectListItem(item.Name, item.Id.ToString());
                    billingMethods.Add(li);
                }

                ddlBillingMethodItems = billingMethods;

                var pbm = AccountKeyValue.GetFirstValueOrNull(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "PreferredBillingMethod");
                Account.PreferredBillableMethod epbm = Account.PreferredBillableMethod.Default;
                if (pbm != null && Enum.TryParse(pbm, out epbm))
                {
                    ddlBillingMethodSelectedValue = ((int)epbm).ToString();
                }
                else
                {
                    if (new[] {
                        AccountType.MotorClub,
                        AccountType.PrivateProperty,
                        AccountType.PoliceDepartment,
                        AccountType.Individual
                    }.Contains(AccountDetail.Type) || AccountDetail.Id == 1)
                        ddlBillingMethodSelectedValue = ((int)Account.PreferredBillableMethod.Invoice).ToString();
                    else
                        ddlBillingMethodSelectedValue = ((int)Account.PreferredBillableMethod.Statement).ToString();
                }

                var psdm = AccountKeyValue.GetFirstValueOrNull(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "PreferredStatementDeliveryMethod");

                if (psdm == null || psdm == "0")
                {
                    cbPrintedDeliveryChecked = true;
                    cbEmailedDeliveryChecked = false;
                }
                else if (psdm == "1")
                {
                    cbPrintedDeliveryChecked = false;
                    if (!string.IsNullOrEmpty(AccountDetail.Email))
                        cbEmailedDeliveryChecked = true;
                }
                else if (psdm == "2")
                {
                    cbPrintedDeliveryChecked = true;
                    if (!string.IsNullOrEmpty(AccountDetail.Email))
                        cbEmailedDeliveryChecked = true;
                }
                else if (psdm == "3")
                {
                    cbPrintedDeliveryChecked = false;
                    cbEmailedDeliveryChecked = false;
                }

                if (await WebGlobal.CurrentUser.Company.HasFeatureAsync(Features.AdvancedBilling))
                {
                    var dieo = await InvoiceEmailOption.GetByCompanyIdAsync(AccountDetail.CompanyId);
                    var ieo = await InvoiceEmailOption.GetByCompanyIdAsync(AccountDetail.CompanyId, AccountDetail.Id);

                    tbInvoiceSubjectPlaceholder = dieo != null && !string.IsNullOrEmpty(dieo.Subject) ? Core.HtmlEncode(dieo.Subject) : "Enter a subject";
                    tbInvoiceMessagePlaceholder = dieo != null && !string.IsNullOrEmpty(dieo.Message) ? Core.HtmlEncode(dieo.Message) : "Enter a message";

                    tbInvoiceSubject = ieo != null && ieo.AccountId == AccountDetail.Id && !string.IsNullOrEmpty(ieo.Subject) ? Core.HtmlEncode(ieo.Subject) : string.Empty;
                    tbInvoiceMessage = ieo != null && ieo.AccountId == AccountDetail.Id && !string.IsNullOrEmpty(ieo.Message) ? Core.HtmlEncode(ieo.Message) : string.Empty;

                    var dseo = StatementEmailOption.GetByCompanyId(AccountDetail.CompanyId);
                    var seo = StatementEmailOption.GetByCompanyId(AccountDetail.CompanyId, AccountDetail.Id);

                    tbStatementSubjectPlaceholder = dseo != null && !string.IsNullOrEmpty(dseo.Subject) ? Core.HtmlEncode(dseo.Subject) : "Enter a subject";
                    tbStatementMessagePlaceholder = dseo != null && !string.IsNullOrEmpty(dseo.Message) ? Core.HtmlEncode(dseo.Message) : "Enter a message";

                    tbStatementSubject = seo != null && seo.AccountId == AccountDetail.Id && !string.IsNullOrEmpty(seo.Subject) ? Core.HtmlEncode(seo.Subject) : string.Empty;
                    tbStatementMessage = seo != null && seo.AccountId == AccountDetail.Id && !string.IsNullOrEmpty(seo.Message) ? Core.HtmlEncode(seo.Message) : string.Empty;
                }
                if (accountDisclaimer != null && accountDisclaimer.AccountId == null)
                    tbAccountDisclaimerPlaceholder = accountDisclaimer.Disclaimer;

                if (await WebGlobal.CurrentUser.Company.HasFeatureAsync(Features.PaymentIntegrations_Square))
                {
                    var authorization = await Extric.Towbook.API.Integration.Square.SquareUtils.GetAuthorizationAsync(WebGlobal.CurrentUser.Company.Id);
                    if (authorization != null)
                    {
                        SquarePaymentsEnabled = true;
                    }
                }

                rpUsersDataSource = Extric.Towbook.User.GetByAccountId(AccountDetail.Id).ToList();

            }



            if (!HttpContext.Request.Method.Equals("POST", StringComparison.OrdinalIgnoreCase) && await WebGlobal.CurrentUser.Company.HasFeatureAsync(Features.Accounts_AccountManagers))
            {
                var users = Extric.Towbook.User.GetByCompanyId(AccountDetail.CompanyId);

                ddlAccountManagerItems = new List<SelectListItem> { new SelectListItem("Choose a manager", "0") };

                foreach (var li in users.Where(w => !w.Disabled).OrderBy(o => o.FullName))
                    ddlAccountManagerItems.Add(new SelectListItem(li.FullName, li.Id.ToString()));

                var accountManagerUser = AccountManager.GetByAccountId(AccountDetail.Id);
                if (accountManagerUser != null && users.Any(a => a.Id == accountManagerUser.UserId))
                {
                    ddlAccountManagerSelectedValue = accountManagerUser.UserId.ToString();
                }

            }

            ReasonsJson = (await Extric.Towbook.Dispatch.Reason.GetByCompany(Company.Company.GetById(AccountDetail.CompanyId)))
                .Select(o => new { Name = o.Name, Id = o.Id, Active = o.IsActive })
                .ToJson();
            BodyTypesJson = (await BodyType.GetByCompanyIdAsync(AccountDetail.CompanyId)).Select(o => new { Name = o.Name, Id = o.Id }).ToJson();
        }


        public IActionResult OnPostCancel()
        {
            return RedirectToPage("Account", new { id = AccountId });
        }

        public async Task<IActionResult> OnPostDelete()
        {
            //   await _accountService.DeleteAccount(AccountId);

            await AccountDetail.Delete(WebGlobal.CurrentUser);
            await DoCacheUpdateAsync(true);
            Response.Redirect("Default.aspx");
            return RedirectToPage("/Accounts/Default");
        }

        public async Task<IActionResult> OnPostUndelete()
        {
            await btnUndeleteClickAsync();
            return RedirectToPage("Default");
        }

        private async Task btnDeleteClickAsync()
        {
            await AccountDetail.Delete(WebGlobal.CurrentUser);
            await DoCacheUpdateAsync(true);
        }

        private async Task btnUndeleteClickAsync()
        {
            await AccountDetail.Undelete(WebGlobal.CurrentUser);
            await DoCacheUpdateAsync();
        }

        public async Task TryConnect()
        {
            var myEmail = Extric.Towbook.Integrations.Email.EmailAddress.GetPrimaryTowbookEmailAddress(AccountDetail.CompanyId);

            if (myEmail == null)
                return;

            Extric.Towbook.Integrations.Email.EmailAddress ea = null;
            if (Core.IsEmailValid(mcTowbookEmailAddress))
            {
                ea = Extric.Towbook.Integrations.Email.EmailAddress.GetByEmailAddress(mcTowbookEmailAddress.Trim());
                if (ea == null)
                    return;

                var akv = AccountKeyValue.GetByCompany(ea.CompanyId, Provider.Towbook.ProviderId, new string[]
                        { "DSC_SubcontractorAddress" }).FirstOrDefault(o => o.Value == myEmail);

                if (akv == null)
                {
                    var sc = SharedCompany.GetByCompanyId(ea.CompanyId).FirstOrDefault();
                    if (sc != null && sc.ShareAllAccounts)
                    {
                        akv = AccountKeyValue.GetByCompany(sc.CompanyId, Provider.Towbook.ProviderId, new string[]
                            { "DSC_SenderAddress" }).FirstOrDefault(o => o.Value == myEmail);
                    }
                }

                if (akv != null)
                {
                    int nCompanyId = ea.CompanyId;
                    int nAccountId = akv.AccountId;
                    var remoteAccount = await Account.GetByIdAsync(nAccountId);
                    if (remoteAccount != null &&
                        remoteAccount.Type == AccountType.Subcontractor &&
                        AccountDetail.Type == AccountType.MotorClub)
                    {
                        AccountDetail.MasterAccountId = MasterAccountTypes.Towbook;
                        AccountDetail.ReferenceNumber = remoteAccount.CompanyId.ToString();
                        await AccountDetail.Save();

                        remoteAccount.ReferenceNumber = AccountDetail.CompanyId + "|" + AccountDetail.Id;
                        remoteAccount.MasterAccountId = MasterAccountTypes.Towbook;
                        await remoteAccount.Save();
                    }
                }

            }
            else if (Core.IsEmailValid(towbookEmailAddress))
            {
                ea = Extric.Towbook.Integrations.Email.EmailAddress.GetByEmailAddress(towbookEmailAddress.Trim());
                if (ea != null)
                {
                    var akv = AccountKeyValue.GetByCompany(ea.CompanyId, Provider.Towbook.ProviderId, new string[]
                        { "DSC_SenderAddress" }).FirstOrDefault(o => o.Value == myEmail);

                    if (akv != null)
                    {
                        int nCompanyId = ea.CompanyId;
                        int nAccountId = akv.AccountId;
                        var remoteAccount = await Account.GetByIdAsync(nAccountId);
                        if (remoteAccount != null &&
                            remoteAccount.Type == AccountType.MotorClub &&
                            AccountDetail.Type == AccountType.Subcontractor)
                        {
                            remoteAccount.MasterAccountId = MasterAccountTypes.Towbook;
                            remoteAccount.ReferenceNumber = AccountDetail.CompanyId.ToString();
                            await remoteAccount.Save();

                            AccountDetail.ReferenceNumber = AccountDetail.CompanyId + "|" + AccountDetail.Id;
                            AccountDetail.MasterAccountId = MasterAccountTypes.Towbook;
                            await AccountDetail.Save();
                        }
                    }
                }
            }

        }

        public async Task<IActionResult> OnPostSaveAsync()
        {
            await PageLoadAsync();

            bool newAccount = AccountDetail.Id == -1;
            bool addressChanged = AccountDetail.Address != txtAddress || AccountDetail.City != txtCity ||
                AccountDetail.State != txtState || AccountDetail.Zip != txtZip;

            AccountDetail.Company = txtCompany;
            AccountDetail.FullName = txtContactName;
            AccountDetail.Address = txtAddress;
            AccountDetail.City = txtCity;
            AccountDetail.State = txtState;
            AccountDetail.Zip = txtZip;
            AccountDetail.Email = txtEmail;
            AccountDetail.Phone = txtPhone;
            AccountDetail.Fax = txtFax;
            AccountDetail.TaxExempt = cbTaxExemptChecked;
            AccountDetail.Notes = txtNotes;

            if (AccountDetail.Type == AccountType.PrivateProperty && (addressChanged || AccountDetail.Latitude == null || AccountDetail.Longitude == null))
            {
                await AccountDetail.UpdateLocation();
            }

            if (ShowCreditOptions)
            {
                decimal cclimit = 0;

                if (decimal.TryParse(txtCreditLimit, out cclimit))
                    AccountDetail.CreditLimit = cclimit;

                AccountDetail.CreditHold = cbCreditHoldChecked;
            }

            AccountDetail.Status = (cbInactiveChecked == true ?
                AccountStatus.Inactive :
                AccountStatus.Active);

            try
            {
                if (!String.IsNullOrEmpty(discountRate.Replace("%", "")))
                    AccountDetail.DiscountRate = Convert.ToDecimal(discountRate.Replace("%", "")) / 100;
                else
                    AccountDetail.DiscountRate = 0;
            }
            catch (FormatException)
            {
                AccountDetail.DiscountRate = 0;
            }

            AccountDetail.Type = (AccountType)Convert.ToInt32(ddlTypeSelectedValue);

            var aceStates = new string[] { "KY", "NY", "OH", "PA",
            "WV", "AR", "IL", "MO", "IN", "LA", "MS", "MO", "TX",
            "ME", "NH", "VT", "VA", "NM", "HI", "AL", "CO", "KS",
            "CA", "MD" };

            if (AccountDetail.Company.ToLower().StartsWith("aaa") &&
                aceStates.Contains((WebGlobal.CurrentUser.Company.State ?? "").ToUpper()))
            {
                if (AccountDetail.MasterAccountId == 0)
                {
                    AccountDetail.MasterAccountId = MasterAccountTypes.AaaAce;

                }
                MasterAccountDetail = await MasterAccount.GetByIdAsync(AccountDetail.MasterAccountId);
            }
            if (AccountDetail.Company.ToLower().StartsWith("aaa northeast"))
            {
                AccountDetail.MasterAccountId = MasterAccountTypes.AaaNortheast;
                MasterAccountDetail = await MasterAccount.GetByIdAsync(AccountDetail.MasterAccountId);
            }
            if (AccountDetail.Company.ToLower().StartsWith("aaa aca"))
            {
                AccountDetail.MasterAccountId = 49;
                MasterAccountDetail = await MasterAccount.GetByIdAsync(AccountDetail.MasterAccountId);
            }
            if (AccountDetail.Company.ToLower().StartsWith("aaa washington"))
            {
                AccountDetail.MasterAccountId = 50;
                MasterAccountDetail = await MasterAccount.GetByIdAsync(AccountDetail.MasterAccountId);
            }
            if (AccountDetail.Company.ToLower().StartsWith("aaa wcny"))
            {
                AccountDetail.MasterAccountId = 51;
                MasterAccountDetail = await MasterAccount.GetByIdAsync(AccountDetail.MasterAccountId);
            }


            if (AccountDetail.Company.ToLower().StartsWith("honk"))
            {
                AccountDetail.MasterAccountId = MasterAccountTypes.Honk;
                MasterAccountDetail = await MasterAccount.GetByIdAsync(AccountDetail.MasterAccountId);
            }

            if (rbPaymentImmediateChecked == true)
            {
                AccountDetail.InvoiceTerms = Account.InvoiceTerm.Immediate;
            }
            else if (rbPaymentNet15Checked == true)
            {
                AccountDetail.InvoiceTerms = Account.InvoiceTerm.Net15;
            }
            else if (rbPaymentNet30Checked == true)
            {
                AccountDetail.InvoiceTerms = Account.InvoiceTerm.Net30;
            }

            if (rbStorageLotsNoneChecked)
            {
                AccountDetail.ImpoundDestinationType = AccountImpoundDestination.None;
                AccountDetail.ImpoundDestinationStorageLotId = Convert.ToInt32(Request.Form["dlImpoundLots_company"]);
            }
            else if (rbStorageLotsDefaultChecked)
            {
                AccountDetail.ImpoundDestinationType = AccountImpoundDestination.Default;
                AccountDetail.ImpoundDestinationStorageLotId = Convert.ToInt32(Request.Form["dlImpoundLots_company"]);
            }
            else if (rbStorageLotThirdPartyChecked)
            {
                AccountDetail.ImpoundDestinationType = AccountImpoundDestination.ThirdParty;
                AccountDetail.ImpoundDestinationStorageLotId = Convert.ToInt32(Request.Form["dlImpoundLots_other"]);
            }

            switch (AccountDetail.ImpoundDestinationType)
            {
                case AccountImpoundDestination.Default:
                    rbStorageLotsDefaultChecked = true;
                    break;
                case AccountImpoundDestination.ThirdParty:
                    rbStorageLotThirdPartyChecked = true;
                    break;
            }

            AccountDetail.InvoiceDeliverByEmail = cbEmailChecked;
            AccountDetail.InvoiceDeliverByFax = cbFaxChecked;
            AccountDetail.InvoiceDeliverByMail = cbMailChecked;
            AccountDetail.DefaultPriority = cbDefaultPriorityChecked ? 1 : 0;
            AccountDetail.DefaultPO = txtDefaultPO;

            if (ddlDefaultStorageSelectedValue == "0")
                AccountDetail.DefaultStorageRateItemId = null;
            else if ((ddlDefaultStorageSelectedValue ?? "") != "")
                AccountDetail.DefaultStorageRateItemId = Convert.ToInt32(ddlDefaultStorageSelectedValue);

            if (ddlDefaultBillToAccountSelectedValue != "-1")
                SaveKeyValue(ddlDefaultBillToAccountSelectedValue, "DefaultBillToAccountId");
            else
                DeleteKeyValue("DefaultBillToAccountId");

            await AccountDetail.Save(async () =>
            {

                var defaultSubcontractorAccountIdIsSet = false;
                if (await WebGlobal.CurrentUser.Company.HasFeatureAsync(Features.DispatchToSubcontractors_SubcontractorRotation))
                {
                    var subs = RotationSubcontractor.GetByAccountId(AccountDetail.Id);

                    List<KeyValuePair<int, int>> newIds = new List<KeyValuePair<int, int>>();

                    foreach (string x in Request.Form.Keys)
                    {
                        if (x.StartsWith("light_"))
                        {
                            newIds.Add(new KeyValuePair<int, int>(1, Convert.ToInt32(x.Replace("light_", ""))));
                        }
                        else if (x.StartsWith("heavy_"))
                        {
                            newIds.Add(new KeyValuePair<int, int>(3, Convert.ToInt32(x.Replace("heavy_", ""))));
                        }
                    }

                    foreach (var x in subs)
                    {
                        if (!newIds.Any(o => o.Key == x.BodyTypeId && o.Value == x.SubcontractorAccountId))
                            x.Delete(WebGlobal.CurrentUser, WebGlobal.GetRequestingIp());
                        else
                        {
                            var existing = newIds.Where(o => o.Key == x.BodyTypeId && o.Value == x.SubcontractorAccountId).FirstOrDefault();
                            if (existing.Key != 0 && existing.Value != 0)
                                newIds.Remove(existing);
                        }
                    }

                    foreach (var kv in newIds)
                    {
                        new RotationSubcontractor()
                        {
                            CompanyId = AccountDetail.CompanyId,
                            AccountId = AccountDetail.Id,
                            BodyTypeId = kv.Key,
                            SubcontractorAccountId = kv.Value,
                            OwnerUserId = WebGlobal.CurrentUser.Id
                        }.Save();
                    }

                    if (newIds.Any())
                    {
                        SaveKeyValue("-9", "DefaultSubcontractorAccountId");
                        defaultSubcontractorAccountIdIsSet = true;
                    }
                }

                var billingAddress = await AddressBookEntry.GetByAccountId(AccountDetail.Id,
                    AddressBookNameMapOrBilling);

                if (string.IsNullOrWhiteSpace(txtBillingAddress) &&
                    string.IsNullOrWhiteSpace(txtBillingCity) &&
                    string.IsNullOrWhiteSpace(txtBillingState) &&
                    string.IsNullOrWhiteSpace(txtBillingZip) &&
                    string.IsNullOrWhiteSpace(txtBillingContactName) &&
                    string.IsNullOrWhiteSpace(txtBillingContactEmail))
                {
                    if (billingAddress != null)
                        await billingAddress.Delete();
                }
                else
                {
                    if (billingAddress == null)
                    {
                        billingAddress = new AddressBookEntry();
                        billingAddress.Name = AddressBookNameMapOrBilling;
                        billingAddress.CompanyId = AccountDetail.CompanyId;
                        billingAddress.AccountId = AccountDetail.Id;
                    }

                    billingAddress.AccountId = AccountDetail.Id;
                    billingAddress.Name = AddressBookNameMapOrBilling;
                    billingAddress.Address = txtBillingAddress;
                    billingAddress.City = txtBillingCity;
                    billingAddress.State = txtBillingState;
                    billingAddress.Zip = txtBillingZip;
                    billingAddress.Email = txtBillingContactEmail;
                    if (!string.IsNullOrEmpty(txtBillingContactName))
                        billingAddress.Notes = "RecipientDisplayName:" + txtBillingContactName;
                    else
                        billingAddress.Notes = string.Empty;

                    await billingAddress.Save();
                }

                if (await Company.Company.GetById(AccountDetail.CompanyId).HasFeatureAsync(Extric.Towbook.Generated.Features.AccountTags))
                {
                    Collection<int> list = new Collection<int>();
                    foreach (string x in Request.Form.Keys)
                    {
                        if (x.StartsWith("tag_"))
                        {
                            int id = Convert.ToInt32(x.Substring(4));
                            list.Add(id);
                            AccountDetail.AddTag(id, WebGlobal.CurrentUser.Id);
                        }
                    }

                    foreach (AccountTag at in AccountDetail.Tags)
                    {
                        if (!list.Contains(at.AccountTagId))
                            AccountDetail.DeleteTag(at.AccountTagId);
                    }
                }

                if (!newAccount && AccountDetail.Type == AccountType.PrivateProperty)
                    await SaveStickeringAsync();

                if (!newAccount)
                    SavePermits();

                SurchargeAccountRate sar = await SurchargeAccountRate.GetBySurchargeAsync(Surcharge.SURCHARGE_FUEL, AccountDetail.Id);

                if (fuelSurcharge.Length == 0 && sar != null)
                {
                    await sar.Delete();
                }
                else if (fuelSurcharge == "0")
                {
                    if (sar == null)
                    {
                        sar = new SurchargeAccountRate();
                        sar.AccountId = AccountDetail.Id;
                        sar.SurchargeId = Surcharge.SURCHARGE_FUEL;
                    }
                    sar.Rate = 0;
                    await sar.Save();
                }
                else if (fuelSurcharge.Length > 0)
                {
                    if (sar == null)
                    {
                        sar = new SurchargeAccountRate();
                        sar.AccountId = AccountDetail.Id;
                        sar.SurchargeId = Surcharge.SURCHARGE_FUEL;
                    }
                    sar.Rate = (Convert.ToDecimal(fuelSurcharge.Replace("%", "")) / 100);
                    await sar.Save();
                }

                var content = tbAccountDisclaimer;
                var aDisclaimer = InvoiceDisclaimer.GetDefaultByCompanyId(AccountDetail.CompanyId, AccountDetail.Id, false);

                if (aDisclaimer == null || aDisclaimer.AccountId == null || aDisclaimer.AccountId != AccountDetail.Id)
                    aDisclaimer = new InvoiceDisclaimer();

                if (string.IsNullOrEmpty(content))
                {
                    if (!string.IsNullOrEmpty(aDisclaimer.Disclaimer))
                    {
                        await aDisclaimer.Delete();
                    }
                }
                else
                {
                    if (aDisclaimer.Disclaimer != content)
                    {
                        aDisclaimer.AccountId = AccountDetail.Id;
                        aDisclaimer.CompanyId = AccountDetail.CompanyId;
                        aDisclaimer.Disclaimer = Core.HtmlEncode(content);
                        await aDisclaimer.Save();
                    }
                }

                content = tbStatementDisclaimer;
                var sDisclaimer = Extric.Towbook.Accounts.StatementDisclaimer.GetByCompanyId(AccountDetail.CompanyId, AccountDetail.Id);

                if (sDisclaimer == null || sDisclaimer.AccountId == null)
                    sDisclaimer = new StatementDisclaimer();

                if (string.IsNullOrEmpty(content))
                {
                    if (!string.IsNullOrEmpty(sDisclaimer.Disclaimer) && sDisclaimer.AccountId == AccountDetail.Id)
                    {
                        await sDisclaimer.Delete();
                    }
                }
                else
                {
                    if (sDisclaimer.Disclaimer != content)
                    {
                        sDisclaimer.AccountId = AccountDetail.Id;
                        sDisclaimer.CompanyId = AccountDetail.CompanyId;
                        sDisclaimer.Disclaimer = Core.HtmlEncode(content);
                        await sDisclaimer.Save();
                    }
                }

                if (await WebGlobal.CurrentUser.Company.HasFeatureAsync(Features.AdvancedBilling))
                {
                    var statementDefaultOption = StatementOption.GetByCompanyId(AccountDetail.CompanyId);
                    var statementAccountDefaultOption = StatementOption.GetByCompanyId(AccountDetail.CompanyId, AccountDetail.Id);

                    var ddv = DueDateDefaultType.Net30;
                    if (ddlStatementDueDateItems.Count > 0)
                        ddv = (DueDateDefaultType)Enum.Parse(typeof(DueDateDefaultType), ddlStatementDueDateSelectedValue);

                    if (statementAccountDefaultOption == null || statementAccountDefaultOption.AccountId == null)
                        statementAccountDefaultOption = new StatementOption();

                    statementAccountDefaultOption.CompanyId = AccountDetail.CompanyId;
                    statementAccountDefaultOption.AccountId = AccountDetail.Id;
                    statementAccountDefaultOption.DefaultDueDateType = ddv;

                    if ((statementAccountDefaultOption.StatementOptionsId == 0 && statementDefaultOption.DefaultDueDateType != ddv) ||
                        (statementAccountDefaultOption.StatementOptionsId > 0))
                    {
                        statementAccountDefaultOption.Save();
                    }

                    var pbm = AccountKeyValue.GetFirstValueOrNull(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "PreferredBillingMethod");
                    var defaultPreferredMethod = ((int)GetPreferredBillableMethod(AccountDetail)).ToString();
                    if (ddlBillingMethodSelectedValue != defaultPreferredMethod)
                        SaveKeyValue(ddlBillingMethodSelectedValue, "PreferredBillingMethod");
                    else
                        DeleteKeyValue("PreferredBillingMethod");

                    var psdm = AccountKeyValue.GetFirstValueOrNull(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "PreferredStatementDeliveryMethod");
                    if (cbPrintedDeliveryChecked && !cbEmailedDeliveryChecked && psdm != null)
                        DeleteKeyValue("PreferredStatementDeliveryMethod");

                    if (txtEmail.Length > 0)
                    {
                        if (!cbPrintedDeliveryChecked && cbEmailedDeliveryChecked)
                            SaveKeyValue("1", "PreferredStatementDeliveryMethod");

                        if (cbPrintedDeliveryChecked && cbEmailedDeliveryChecked)
                            SaveKeyValue("2", "PreferredStatementDeliveryMethod");
                    }

                    if (!cbPrintedDeliveryChecked && !cbEmailedDeliveryChecked)
                        SaveKeyValue("3", "PreferredStatementDeliveryMethod");
                }


                if (await WebGlobal.CurrentUser.Company.HasFeatureAsync(Features.AdvancedBilling))
                {
                    var ieo = await InvoiceEmailOption.GetByCompanyIdAsync(AccountDetail.CompanyId, AccountDetail.Id);
                    var seo = StatementEmailOption.GetByCompanyId(AccountDetail.CompanyId, AccountDetail.Id);

                    if (ieo == null || ieo.AccountId == null)
                        ieo = new InvoiceEmailOption();

                    if (seo == null || seo.AccountId == null)
                        seo = new StatementEmailOption();

                    ieo.Subject = Core.HtmlEncode(tbInvoiceSubject);
                    ieo.Message = Core.HtmlEncode(tbInvoiceMessage);
                    if (string.IsNullOrEmpty(ieo.Subject) && string.IsNullOrEmpty(ieo.Message))
                    {
                        if (ieo.Id > 0)
                        {
                            ieo.Delete(WebGlobal.CurrentUser);
                        }
                    }
                    else
                    {
                        ieo.CompanyId = AccountDetail.CompanyId;
                        ieo.AccountId = AccountDetail.Id;

                        ieo.Save(WebGlobal.CurrentUser);
                    }

                    seo.Subject = Core.HtmlEncode(tbStatementSubject);
                    seo.Message = Core.HtmlEncode(tbStatementMessage);
                    if (string.IsNullOrEmpty(seo.Subject) && string.IsNullOrEmpty(seo.Message))
                    {
                        if (seo.Id > 0)
                        {
                            seo.Delete(WebGlobal.CurrentUser);
                        }
                    }
                    else
                    {
                        seo.CompanyId = AccountDetail.CompanyId;
                        seo.AccountId = AccountDetail.Id;

                        seo.Save(WebGlobal.CurrentUser);
                    }
                }

                SaveStorageRates();

                SaveRequiredFields();

                SaveKeyValue(cbAllowAccountUsersToViewFilesChecked ? "1" : "0", "AllowAccountUsersToViewFiles");

                if (MasterAccountDetail != null)
                {
                    if (MasterAccountDetail.Id == MasterAccountTypes.AaaAce ||
                        MasterAccountDetail.Id == MasterAccountTypes.AaaNortheast ||
                        MasterAccountDetail.Id == 49 ||
                        MasterAccountDetail.Id == 50 ||
                        MasterAccountDetail.Id == 51)
                    {
                        SaveTextBoxKeyValue(providerNumber, "ProviderId");
                        int validProviderId = 0;

                        if (int.TryParse(providerNumber, out validProviderId)
                            || MasterAccountDetail.Id == MasterAccountTypes.AaaNortheast
                            || MasterAccountDetail.Id == 49)
                        {
                            var ct = Extric.Towbook.Integrations.MotorClubs.Aaa.AaaContractor.GetByContractorId(providerNumber, MasterAccountDetail.Id, 3);
                            if (ct == null)
                            {
                                var acx = new Extric.Towbook.Integrations.MotorClubs.Aaa.AaaContractor()
                                {
                                    CompanyId = AccountDetail.CompanyId,
                                    AccountId = AccountDetail.Id,
                                    EnvironmentId = 3,
                                    MasterAccountId = MasterAccountDetail.Id,
                                    ContractorId = providerNumber,
                                    IsValidated = false
                                };
                                if (MasterAccountDetail.Id == 50 || MasterAccountDetail.Id == 51)
                                    acx.EnvironmentId = 4;

                                acx.Save();

                                var existingRateItems = Extric.Towbook.RateItem.GetByCompanyId(acx.CompanyId);

                                string[] namesToAdd = new string[] { "DL - DOLLIES",
                                "DS - DROP DRIVE SHAFT",
                                "FB - FLATBED",
                                "FD - Fuel Delivery Amount",
                                "FL - FUEL",
                                "FM - Field Manager Review",
                                "LS - LOCKSMITH AMOUNT",
                                "LT - LONG TOW",
                                "MA - Miscellaneous Amount",
                                "MD - MANAGEMENT DECISIONS",
                                "ML - MISCELLANEOUS",
                                "MT - Motorcycle Tow",
                                "MU - MEDIUM DUTY TOW",
                                "OM - ENROUTE MILES",
                                "PR - EXTRA PERSONNEL",
                                "PY - PRIORITY SERVICE",
                                "RV - RV CHARGES FOR RV EQUIPMENT",
                                "S1 - RS Rate",
                                "S2 - RSS Rate",
                                "SM - SCENE TIME MINUTES",
                                "ST - STORAGE",
                                "TL - TOLLS/PARKING FEE",
                                "TM - EXTRA TIME ON SCENE",
                                "TR - EXTRA TRUCK",
                                "TW - TOW MILEAGE" };

                                if (MasterAccountDetail.Id == 50)
                                {
                                    namesToAdd = new string[] {
                                    "E1 - Extrication - 1st Truck",
                                    "E2 - Extrication - 2nd truck",
                                    "EM - Extra Tow Mileage",
                                    "GS - Fuel - Basic Service",
                                    "MH - Medium/Heavy Duty Vehicle Svc",
                                    "MI - Miscellaneous - AAA Approval Reqd",
                                    "PG - Plus/Premier Fuel",
                                    "TJ - TireJect",
                                    "TL - Tolls/Parking",
                                    "TR - Tire Repair"
                                    };
                                }
                                else if (MasterAccountDetail.Id == 51)
                                {
                                    namesToAdd = new string[] {
                                    "AC - Accident",
                                    "BI - Battery Install",
                                    "BW - Battery Warranty",
                                    "CH - Chains",
                                    "EK - Extra Truck",
                                    "EP - Extra Personnel",
                                    "ET - Extra Time",
                                    "FE - Ferry Ticket/Tolls",
                                    "FF - Ferry Flat Rate",
                                    "FL - Fuel",
                                    "FT - Ferry Time",
                                    "GT - Military Gate Time",
                                    "LK - Locksmith",
                                    "MR - MTCY Resp Miles",
                                    "MT - MTCY Tow Miles",
                                    "NT - Night Rate",
                                    "OA - Out of Area",
                                    "OB - Special Boundary",
                                    "OE - Special OOA",
                                    "RE - RV Extra Time",
                                    "RM - RV Resp Miles",
                                    "RT - Return Miles",
                                    "RV - RV Coverage",
                                    "TN - 1 Ton"
                                    };
                                }

                                var newRates = new List<Extric.Towbook.RateItem>();
                                foreach (var name in namesToAdd)
                                {
                                    if (existingRateItems.Any(o => o.Name == name))
                                        continue;

                                    var newRateItem = new Extric.Towbook.RateItem();

                                    newRateItem.CompanyId = acx.CompanyId;
                                    newRateItem.Cost = 0;
                                    newRateItem.Name = name;

                                    await newRateItem.Save(null);
                                    newRates.Add(newRateItem);
                                }

                                await Extric.Towbook.Caching.CacheWorkerUtility.UpdateRateItems(newRates.Select(o => o.RateItemId).ToArray());
                            }
                        }
                    }
                    else
                    {
                        SaveTextBoxKeyValue(providerNumber, "ProviderId");
                        SaveTextBoxKeyValue(taxId, "TaxId");
                        SaveTextBoxKeyValue(locationId, "LocationId");
                    }

                    SaveKeyValue(cbEnablePaymentImportChecked ? "1" : "0", "ImportPayments");

                    if (MasterAccountDetail != null &&
                            (MasterAccountDetail.Id == MasterAccountTypes.Agero ||
                            MasterAccountDetail.Id == MasterAccountTypes.Allstate ||
                            MasterAccountDetail.Id == MasterAccountTypes.Swoop ||
                            MasterAccountDetail.Id == MasterAccountTypes.Urgently))
                    {
                        SaveKeyValue(cbPreventPhotoSharingChecked ? "1" : "0", "PreventPhotoSharing");
                    }

                    if (AccountKeyValue.GetFirstValueOrNull(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "DigitalAutoAccept") != null)
                    {
                        SaveKeyValue(cbDisableAutoAcceptChecked ? "1" : "0", "DigitalAutoAcceptDisabled");
                    }

                    if (ShowAutoAccept)
                    {
                        if (Request.Form["autoAcceptJson"] != StringValues.Empty)
                        {
                            var jsonValidate = Request.Form["autoAcceptJson"];

                            var deserialized = Newtonsoft.Json.JsonConvert.DeserializeObject<IEnumerable<AutoAcceptRule>>(jsonValidate);
                            SaveKeyValue(Request.Form["autoAcceptJson"], "DigitalAutoAccept");
                        }
                    }
                }

                SaveTextBoxKeyValue(gateCode, "PropertyGateCode");
                SaveTextBoxKeyValue(contractStartDate, "PropertyContractStartDate");
                SaveTextBoxKeyValue(contractEndDate, "PropertyContractEndDate");

                SaveKeyValue(cbIncludeInvoiceCopiesOnStatementsChecked ? "1" : "0", "IncludeInvoicesWithCopyOfStatement");

                if (AccountDetail.Type == AccountType.MotorClub)
                {
                    var setting = CompanyKeyValue.GetFirstValueOrNull(AccountDetail.CompanyId,
                            Provider.Towbook.ProviderId, "HideChargesFromMotorClubInvoicesByDefault");

                    if (setting != null)
                    {
                        if (!(setting == "1" && cbAlwaysHideChargesChecked) &&
                            !(setting == "0" && !cbAlwaysHideChargesChecked))
                        {
                            SaveKeyValue(cbAlwaysHideChargesChecked ? "1" : "0", "AlwaysHideCharges");
                        }
                        else
                            DeleteKeyValue("AlwaysHideCharges");
                    }
                    else
                    {
                        SaveKeyValue(cbAlwaysHideChargesChecked ? "1" : "0", "AlwaysHideCharges");
                    }
                }
                else
                {
                    SaveKeyValue(cbAlwaysHideChargesChecked ? "1" : "0", "AlwaysHideCharges");
                }

                SaveKeyValue(cbAlwaysHideDiscountsChecked ? "1" : "0", "AlwaysHideDiscounts");
                SaveKeyValue(cbAlwaysHidePhotosChecked ? "1" : "0", "AlwaysHidePhotos");
                SaveKeyValue(cbUnloadedMileageSetOneChecked ? "1" : "0", "MCBilling_ForceUnloadedMilesToOneIfMissing");
                SaveKeyValue(cbUnloadedMileageRoundOneChecked ? "1" : "0", "RoundUpCalculatedMiles");
                SaveKeyValue(ddlSuggestedDefaultSelectedValue, "DefaultSuggestedMileageRoute");

                if (ddlAutomaticallyFillInMilesSelectedValue == "0")
                    DeleteKeyValue("AutomaticallyAddMiles");
                else
                    SaveKeyValue(ddlAutomaticallyFillInMilesSelectedValue, "AutomaticallyAddMiles");

                if (await WebGlobal.CurrentUser.Company.HasFeatureAsync(Features.DeadheadMileage))
                    SaveKeyValue(cbAlwaysAddDeadheadChecked ? "1" : "0", "AutomaticallyAddDeadheadMileage");

                if (string.IsNullOrWhiteSpace(txtReplyToEmail))
                    DeleteKeyValue("ReplyToEmailAddress");
                else
                    SaveKeyValue(txtReplyToEmail, "ReplyToEmailAddress");

                var emailPreference = CompanyKeyValue.GetFirstValueOrNull(WebGlobal.CurrentUser.CompanyId,
                        Provider.Towbook.ProviderId, "EmailInvoiceEventPreference");

                if (emailPreference != null)
                {
                    if (!(emailPreference == "1" && ddlEmailEventPreferenceSelectedValue == "1") &&
                        !(emailPreference == "0" && ddlEmailEventPreferenceSelectedValue == "0"))
                    {
                        if (cbEmailChecked)
                            SaveKeyValue(ddlEmailEventPreferenceSelectedValue, "EmailInvoiceEventPreference");
                        else
                            DeleteKeyValue("EmailInvoiceEventPreference");
                    }
                    else
                        DeleteKeyValue("EmailInvoiceEventPreference");
                }
                else
                {
                    if (cbEmailChecked)
                        SaveKeyValue(ddlEmailEventPreferenceSelectedValue, "EmailInvoiceEventPreference");
                    else
                        DeleteKeyValue("EmailInvoiceEventPreference");
                }


                if (await WebGlobal.CurrentUser.Company.HasFeatureAsync(Features.PriorityCallTextAlert))
                {
                    if (cbPriorityCallTextAlertToManagersChecked)
                    {
                        SaveKeyValue("1", "EnablePriorityCallTextAlertToAllManagers");
                    }
                    else
                    {
                        if (AccountKeyValue.GetByAccount(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "EnablePriorityCallTextAlertToAllManagers").FirstOrDefault() != null)
                            DeleteKeyValue("EnablePriorityCallTextAlertToAllManagers");
                    }
                }

                if (AccountDetail.Type != AccountType.MotorClub)
                {
                    SaveKeyValue(cbAutoFillContactChecked ? "1" : "0", "AccountContactAddedAtCallCreation");

                    if (cbUsePhysicalAddressAsChecked)
                    {
                        if (ddlDefaultPhysicalAddressSelectedValue == "1")
                            SaveKeyValue("1", "AccountPhysicalAddressAsDefaultLocation");
                        else if (ddlDefaultPhysicalAddressSelectedValue == "2")
                            SaveKeyValue("2", "AccountPhysicalAddressAsDefaultLocation");
                    }
                    else
                        DeleteKeyValue("AccountPhysicalAddressAsDefaultLocation");
                }
                else
                {
                    DeleteKeyValue("AccountPhysicalAddressAsDefaultLocation");
                    DeleteKeyValue("AccountContactAddedAtCallCreation");
                }

                if (ddlDefaultAssetBodyTypeSelectedValue != "1")
                    SaveKeyValue(ddlDefaultAssetBodyTypeSelectedValue, "DefaultAssetBodyTypeId");
                else
                    DeleteKeyValue("DefaultAssetBodyTypeId");

                if (await WebGlobal.CurrentUser.Company.HasFeatureAsync(Features.DispatchToSubcontractors))
                {
                    if (!defaultSubcontractorAccountIdIsSet)
                    {
                        if (ddlSubcontractorsSelectedValue != "0")
                            SaveKeyValue(ddlSubcontractorsSelectedValue, "DefaultSubcontractorAccountId");
                        else
                            DeleteKeyValue("DefaultSubcontractorAccountId");
                    }

                    SaveKeyValue(towbookEmailAddress.Trim(), "DSC_SubcontractorAddress");
                    SaveKeyValue(txtMobileNumbers.Trim(), "SubcontractorMobileNumbers");
                }

                if (WebGlobal.CurrentUser.HasAccessToCompany(119800))
                {
                    SaveKeyValue(towbookReferenceNumber, "CustomReferenceNumber");
                }

                SaveKeyValue(mcTowbookEmailAddress.Trim(), "DSC_SenderAddress");

                if (!string.IsNullOrWhiteSpace(towbookEmailAddress) ||
                        !string.IsNullOrWhiteSpace(mcTowbookEmailAddress))
                {
                    await TryConnect();
                }

                if (trCompanyOverrideVisible)
                {
                    if (ddlCompanyOverrideSelectedValue != AccountDetail.CompanyId.ToString())
                    {
                        var selectedValue = 0;
                        try
                        {
                            selectedValue = int.Parse(ddlCompanyOverrideSelectedValue);
                        }
                        catch { }

                        if (selectedValue > 0)
                        {
                            SaveKeyValue(ddlCompanyOverrideSelectedValue, "OverrideToSharedCompanyId");
                            var aUsers = Extric.Towbook.User.GetByAccountId(AccountDetail.Id);
                            foreach (var u in aUsers)
                            {
                                u.PrimaryCompanyId = selectedValue;
                                await u.Save();
                            }
                        }

                    }
                    else
                    {
                        DeleteKeyValue("OverrideToSharedCompanyId");

                        var aUsers = Extric.Towbook.User.GetByAccountId(AccountDetail.Id);
                        foreach (var u in aUsers)
                        {
                            u.PrimaryCompanyId = AccountDetail.CompanyId;
                            await u.Save();
                        }
                    }
                }

                if (!newAccount && await WebGlobal.CurrentUser.Company.HasFeatureAsync(Features.Roadside))
                {
                    var rs = Extric.Roadside.RoadsideSetting.GetByCompanyId(AccountDetail.CompanyId, AccountDetail.Id);
                    bool isAutoInvoiteEnabledByDefault = AccountDetail.Type == AccountType.MotorClub ? rs.EnableMotorClubAutoInvite.GetValueOrDefault() : rs.EnableNonMotorClubAutoInvite.GetValueOrDefault();

                    var key = AccountKeyValue.GetByAccount(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "AutoSendRoadsideInvite").FirstOrDefault();

                    if (cbAlwaysSendRoadsideInviteChecked != isAutoInvoiteEnabledByDefault ||
                        (key != null && key.Value == "1" && !cbAlwaysSendRoadsideInviteChecked) ||
                        (key != null && key.Value == "0" && cbAlwaysSendRoadsideInviteChecked))
                    {
                        if (key != null)
                            DeleteKeyValue("AutoSendRoadsideInvite");

                        if (cbAlwaysSendRoadsideInviteVisible && cbAlwaysSendRoadsideInviteChecked != isAutoInvoiteEnabledByDefault)
                            SaveKeyValue(cbAlwaysSendRoadsideInviteChecked ? "1" : "0", "AutoSendRoadsideInvite");
                    }



                    var items = Extric.Roadside.JobProgressTextAlertItem.GetByCompanyId(AccountDetail.CompanyId, true);

                    if (items.Count() == 0)
                        items = Extric.Roadside.JobProgressTextAlertItem.GetDefaults(AccountDetail.CompanyId, AccountDetail.Id);

                    var completionProgressTextItem = items.Where(w => w.StatusTypeId == Extric.Roadside.JobProgressStatusType.Completed.Id && !w.IsDeleted).LastOrDefault();

                    key = AccountKeyValue.GetByAccount(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "AlwaysSendSurvey").FirstOrDefault();
                    if (key == null || (key.Value == "1" && !cbAlwaysSendSurveyChecked) || (key.Value == "0" && cbAlwaysSendSurveyChecked))
                    {
                        if (key != null)
                            DeleteKeyValue("AlwaysSendSurvey");

                        if (cbAlwaysSendSurveyVisible && (completionProgressTextItem == null && cbAlwaysSendSurveyChecked || completionProgressTextItem != null && !cbAlwaysSendSurveyChecked))
                            SaveKeyValue(cbAlwaysSendSurveyChecked ? "1" : "0", "AlwaysSendSurvey");
                    }
                }

                if (AccountDetail.Type == AccountType.PrivateProperty)
                {
                    SaveKeyValue(cbEnableParkingPermitsChecked ? "1" : "0", "ParkingPermitsEnabled");
                    SaveKeyValue(cbEnableStickeringChecked ? "1" : "0", "StickeringEnabled");
                    SaveKeyValue(cbEnableSiteVisitsChecked ? "1" : "0", "SiteVisitsEnabled");
                }


                var iplkey = AccountKeyValue.GetByAccount(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "Square_AlwaysIncludePaymentLinkOnInvoices").FirstOrDefault();
                var iplkeyDefault = (CompanyKeyValue.GetFirstValueOrNull(AccountDetail.CompanyId, Provider.Towbook.ProviderId, "Square_AlwaysIncludePaymentLinkOnInvoices") ?? "0") == "1";
                if (cbIncludeInvoicePaymentLinkChecked == iplkeyDefault)
                {
                    if (iplkey != null)
                        DeleteKeyValue("Square_AlwaysIncludePaymentLinkOnInvoices");
                }
                else if ((iplkey == null) || (iplkey != null && iplkey.Value == "1" && !cbIncludeInvoicePaymentLinkChecked) || (iplkey != null && iplkey.Value == "0" && cbIncludeInvoicePaymentLinkChecked))
                {
                    if (iplkey != null)
                        DeleteKeyValue("Square_AlwaysIncludePaymentLinkOnInvoices");

                    SaveKeyValue(cbIncludeInvoicePaymentLinkChecked ? "1" : "0", "Square_AlwaysIncludePaymentLinkOnInvoices");
                }

                iplkey = AccountKeyValue.GetByAccount(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "Square_AlwaysIncludePaymentLinkOnStatements").FirstOrDefault();
                iplkeyDefault = (CompanyKeyValue.GetFirstValueOrNull(AccountDetail.CompanyId, Provider.Towbook.ProviderId, "Square_AlwaysIncludePaymentLinkOnStatements") ?? "0") == "1";
                if (cbIncludeStatementPaymentLinkChecked == iplkeyDefault)
                {
                    if (iplkey != null)
                        DeleteKeyValue("Square_AlwaysIncludePaymentLinkOnStatements");
                }
                else if ((iplkey == null) || (iplkey != null && iplkey.Value == "1" && !cbIncludeStatementPaymentLinkChecked) || (iplkey != null && iplkey.Value == "0" && cbIncludeStatementPaymentLinkChecked))
                {
                    if (iplkey != null)
                        DeleteKeyValue("Square_AlwaysIncludePaymentLinkOnStatements");

                    SaveKeyValue(cbIncludeStatementPaymentLinkChecked ? "1" : "0", "Square_AlwaysIncludePaymentLinkOnStatements");
                }

                iplkey = AccountKeyValue.GetByAccount(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "Square_OptOutOfEmailsOnTransactions").FirstOrDefault();
                iplkeyDefault = (CompanyKeyValue.GetFirstValueOrNull(AccountDetail.CompanyId, Provider.Towbook.ProviderId, "Square_OptOutOfEmailsOnTransactions") ?? "0") != "1";
                if (cbOptOutOfConfirmationEmailOnTransactionsChecked == iplkeyDefault)
                {
                    if (iplkey != null)
                        DeleteKeyValue("Square_OptOutOfEmailsOnTransactions");
                }
                else if ((iplkey == null) || (iplkey != null && iplkey.Value == "1" && !cbOptOutOfConfirmationEmailOnTransactionsChecked) || (iplkey != null && iplkey.Value == "0" && cbOptOutOfConfirmationEmailOnTransactionsChecked))
                {
                    if (iplkey != null)
                        DeleteKeyValue("Square_OptOutOfEmailsOnTransactions");

                    SaveKeyValue(cbOptOutOfConfirmationEmailOnTransactionsChecked ? "0" : "1", "Square_OptOutOfEmailsOnTransactions");
                }


                iplkey = AccountKeyValue.GetByAccount(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "Square_ExcludeLinkOnPrintedDispatchInvoices").FirstOrDefault();
                iplkeyDefault = (CompanyKeyValue.GetFirstValueOrNull(AccountDetail.CompanyId, Provider.Towbook.ProviderId, "Square_ExcludeLinkOnPrintedDispatchInvoices") ?? "0") == "1";
                if (cbExcludeLinkOnPrintedDispatchInvoicesChecked == iplkeyDefault)
                {
                    if (iplkey != null)
                        DeleteKeyValue("Square_ExcludeLinkOnPrintedDispatchInvoices");
                }
                else if ((iplkey == null) || (iplkey != null && iplkey.Value == "1" && !cbExcludeLinkOnPrintedDispatchInvoicesChecked) || (iplkey != null && iplkey.Value == "0" && cbExcludeLinkOnPrintedDispatchInvoicesChecked))
                {
                    if (iplkey != null)
                        DeleteKeyValue("Square_ExcludeLinkOnPrintedDispatchInvoices");

                    SaveKeyValue(cbExcludeLinkOnPrintedDispatchInvoicesChecked ? "0" : "1", "Square_ExcludeLinkOnPrintedDispatchInvoices");
                }

                iplkey = AccountKeyValue.GetByAccount(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "Square_ExcludeLinkOnPrintedImpoundInvoices").FirstOrDefault();
                iplkeyDefault = (CompanyKeyValue.GetFirstValueOrNull(AccountDetail.CompanyId, Provider.Towbook.ProviderId, "Square_ExcludeLinkOnPrintedImpoundInvoices") ?? "0") == "1";
                if (cbExcludeLinkOnPrintedImpoundInvoicesChecked == iplkeyDefault)
                {
                    if (iplkey != null)
                        DeleteKeyValue("Square_ExcludeLinkOnPrintedImpoundInvoices");
                }
                else if ((iplkey == null) || (iplkey != null && iplkey.Value == "1" && !cbExcludeLinkOnPrintedImpoundInvoicesChecked) || (iplkey != null && iplkey.Value == "0" && cbExcludeLinkOnPrintedImpoundInvoicesChecked))
                {
                    if (iplkey != null)
                        DeleteKeyValue("Square_ExcludeLinkOnPrintedImpoundInvoices");

                    SaveKeyValue(cbExcludeLinkOnPrintedImpoundInvoicesChecked ? "0" : "1", "Square_ExcludeLinkOnPrintedImpoundInvoices");
                }

                iplkey = AccountKeyValue.GetByAccount(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "Square_ExcludeLinkOnPrintedStatements").FirstOrDefault();
                iplkeyDefault = (CompanyKeyValue.GetFirstValueOrNull(AccountDetail.CompanyId, Provider.Towbook.ProviderId, "Square_ExcludeLinkOnPrintedStatements") ?? "0") == "1";
                if (cbExcludeLinkOnPrintedStatementsChecked == iplkeyDefault)
                {
                    if (iplkey != null)
                        DeleteKeyValue("Square_ExcludeLinkOnPrintedStatements");
                }
                else if ((iplkey == null) || (iplkey != null && iplkey.Value == "1" && !cbExcludeLinkOnPrintedStatementsChecked) || (iplkey != null && iplkey.Value == "0" && cbExcludeLinkOnPrintedStatementsChecked))
                {
                    if (iplkey != null)
                        DeleteKeyValue("Square_ExcludeLinkOnPrintedStatements");

                    SaveKeyValue(cbExcludeLinkOnPrintedStatementsChecked ? "0" : "1", "Square_ExcludeLinkOnPrintedStatements");
                }


                iplkey = AccountKeyValue.GetByAccount(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "SquareTipping_ExcludeOnPaymentLinks").FirstOrDefault();
                iplkeyDefault = (CompanyKeyValue.GetFirstValueOrNull(AccountDetail.CompanyId, Provider.Towbook.ProviderId, "SquareTipping_ExcludeOnPaymentLinks") ?? "0") != "1";
                if (cbExcludeTipsOnPaymentLinksChecked == iplkeyDefault)
                {
                    if (iplkey != null)
                        DeleteKeyValue("SquareTipping_ExcludeOnPaymentLinks");
                }
                else if ((iplkey == null) || (iplkey != null && iplkey.Value == "1" && !cbExcludeTipsOnPaymentLinksChecked) || (iplkey != null && iplkey.Value == "0" && cbExcludeTipsOnPaymentLinksChecked))
                {
                    if (iplkey != null)
                        DeleteKeyValue("SquareTipping_ExcludeOnPaymentLinks");

                    SaveKeyValue(cbExcludeTipsOnPaymentLinksChecked ? "0" : "1", "SquareTipping_ExcludeOnPaymentLinks");
                }

                iplkey = AccountKeyValue.GetByAccount(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "SquareTipping_ExcludeOnSquareReader").FirstOrDefault();
                iplkeyDefault = (CompanyKeyValue.GetFirstValueOrNull(AccountDetail.CompanyId, Provider.Towbook.ProviderId, "SquareTipping_ExcludeOnSquareReader") ?? "0") != "1";
                if (cbExcludeTipsOnSquareReaderChecked == iplkeyDefault)
                {

                    if (iplkey != null)
                        DeleteKeyValue("SquareTipping_ExcludeOnSquareReader");
                }
                else if ((iplkey == null) || (iplkey != null && iplkey.Value == "1" && !cbExcludeTipsOnSquareReaderChecked) || (iplkey != null && iplkey.Value == "0" && cbExcludeTipsOnSquareReaderChecked))
                {
                    if (iplkey != null)
                        DeleteKeyValue("SquareTipping_ExcludeOnSquareReader");

                    SaveKeyValue(cbExcludeTipsOnSquareReaderChecked ? "0" : "1", "SquareTipping_ExcludeOnSquareReader");
                }

                iplkey = AccountKeyValue.GetByAccount(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, "SquareTipping_ExcludeOnSquareTerminal").FirstOrDefault();
                iplkeyDefault = (CompanyKeyValue.GetFirstValueOrNull(AccountDetail.CompanyId, Provider.Towbook.ProviderId, "SquareTipping_ExcludeOnSquareTerminal") ?? "0") != "1";
                if (cbExcludeTipsOnSquareTerminalChecked == iplkeyDefault)
                {
                    if (iplkey != null)
                        DeleteKeyValue("SquareTipping_ExcludeOnSquareTerminal");
                }
                else if ((iplkey == null) || (iplkey != null && iplkey.Value == "1" && !cbExcludeTipsOnSquareTerminalChecked) || (iplkey != null && iplkey.Value == "0" && cbExcludeTipsOnSquareTerminalChecked))
                {
                    if (iplkey != null)
                        DeleteKeyValue("SquareTipping_ExcludeOnSquareTerminal");

                    SaveKeyValue(cbExcludeTipsOnSquareTerminalChecked ? "0" : "1", "SquareTipping_ExcludeOnSquareTerminal");
                }

                if (await WebGlobal.CurrentUser.Company.HasFeatureAsync(Features.Accounts_AccountManagers))
                {
                    int accountManagerUserId = 0;
                    if (int.TryParse(ddlAccountManagerSelectedValue, out accountManagerUserId))
                    {
                        var accountManager = AccountManager.GetByAccountId(AccountDetail.Id);

                        if ((accountManagerUserId == 0 && accountManager != null) ||
                                (accountManager != null && accountManager.UserId != accountManagerUserId))
                        {
                            accountManager.Delete(WebGlobal.CurrentUser);
                        }

                        if (accountManagerUserId > 0)
                        {
                            accountManager = new AccountManager()
                            {
                                AccountId = AccountDetail.Id,
                                UserId = accountManagerUserId
                            };

                            accountManager.Save(WebGlobal.CurrentUser);
                        }
                    }

                }

            });

            await DoCacheUpdateAsync();

            return RedirectToPage("Account", new { id = AccountDetail.Id });
        }

        private async Task DoCacheUpdateAsync(bool delete = false)
        {

            var cmp = GetCompaniesForRequest();
            var key = "accounts:" + string.Join(",",
                GetCompaniesForRequest().Select(o => o.Id).OrderBy(ro => ro));

            var json = await Core.GetRedisValueAsync(key);

            if (json != null)
            {
                var accountList = Newtonsoft.Json.JsonConvert.DeserializeObject<IEnumerable<Extric.Towbook.API.Models.AccountMinimalModel>>(json);

                if (delete)
                    accountList = accountList.Where(o => o.Id != AccountDetail.Id);
                else
                    accountList = accountList.Where(o => o.Id != AccountDetail.Id).Union(new[]
                    {
                        (await AccountMinimalModel.MapAsync(await AccountModel.MapAsync(AccountDetail, true),
                        null,
                        null,
                        null,
                        null,
                        WebGlobal.CurrentUser))
                    }).OrderBy(o => o.Name);

                json = accountList.ToJson();
                await Core.SetRedisValueAsync(key, json);
            }
        }

        public static Extric.Towbook.Company.Company[] GetCompaniesForRequest()
        {
            try
            {
                var obj = System.Web.HttpContext.Current;

                string company = "";

                if (!string.IsNullOrWhiteSpace(company))
                {
                    if (company == "all")
                    {
                        return WebGlobal.GetCompanies();
                    }
                    else
                    {
                        return new Extric.Towbook.Company.Company[] {
                            WebGlobal.GetCompanies().Where(o => o.Id == Convert.ToInt32(company)).FirstOrDefault()
                        };
                    }
                }
                else
                {
                    return new Extric.Towbook.Company.Company[] { WebGlobal.CurrentUser.Company };
                }
            }
            finally
            {

            }
        }

        protected async Task SaveStickeringAsync()
        {
            #region Sticker settings
            var cSetting = Extric.Towbook.Stickering.StickerSetting.GetByCompanyId(AccountDetail.CompanyId, null)
                ?? new Extric.Towbook.Stickering.StickerSetting();

            var aSetting = Extric.Towbook.Stickering.StickerSetting.GetByAccountId(AccountDetail.CompanyId, AccountDetail.Id)
                ?? new Extric.Towbook.Stickering.StickerSetting();

            if (aSetting.AccountId == null)
                aSetting.Id = 0; // Don't override the company default

            bool isChanged = false;

            bool propertyApprovalValue = ddlManagerApprovalSelectedValue == "1";
            if (aSetting.Id > 0 && aSetting.PropertyApprovalRequired != propertyApprovalValue)
                isChanged = true;
            else if (aSetting.Id == 0 && cSetting.PropertyApprovalRequired != propertyApprovalValue)
                isChanged = true;

            bool managerApprovalValue = ddlManagerApprovalSelectedValue == "1";
            if (aSetting.Id > 0 && aSetting.TowManagerApprovalRequired != managerApprovalValue)
                isChanged = true;
            else if (aSetting.Id == 0 && cSetting.TowManagerApprovalRequired != managerApprovalValue)
                isChanged = true;

            if (await WebGlobal.CurrentUser.Company.HasFeatureAsync(Features.UserSignatures))
            {
                bool managerSignatureRequiredValue = ddlManagerSignatureRequiredSelectedValue == "1";
                if (aSetting.Id > 0 && aSetting.RequireApprovalSignature != managerSignatureRequiredValue)
                    isChanged = true;
                else if (aSetting.Id == 0 && cSetting.RequireApprovalSignature != managerSignatureRequiredValue)
                    isChanged = true;

                if (isChanged)
                    SaveKeyValue(managerSignatureRequiredValue ? "1" : "0", "RequireSignatureForStickerApproval");
            }

            bool allowExtensions = ddlAllowExtensionsSelectedValue == "1";
            if (aSetting.Id > 0 && aSetting.AllowExtensions != allowExtensions)
                isChanged = true;
            else if (aSetting.Id == 0 && cSetting.AllowExtensions != allowExtensions)
                isChanged = true;

            decimal? stickerExtension = cSetting.ExpirationHours;

            if (aSetting.Id > 0 && aSetting.ExpirationHours != null)
                stickerExtension = aSetting.ExpirationHours;

            decimal expTime;

            if (tbExpirationTime != "" && Decimal.TryParse(tbExpirationTime, out expTime) && expTime != stickerExtension)
            {
                isChanged = true;
                stickerExtension = expTime;
            }

            if (isChanged)
            {
                aSetting.CompanyId = AccountDetail.CompanyId;
                aSetting.AccountId = AccountDetail.Id;
                aSetting.ExpirationHours = stickerExtension;
                aSetting.PropertyApprovalRequired = propertyApprovalValue;
                aSetting.TowManagerApprovalRequired = managerApprovalValue;
                aSetting.AllowExtensions = allowExtensions;
                aSetting.Save(WebGlobal.CurrentUser);
            }

            if (AccountDetail.Type == AccountType.PrivateProperty)
            {
                isChanged = false;

                // send notification email settings
                bool sendActivityEmail = ddlSessionActivityEmailSelectedValue == "1";
                if (aSetting.Id > 0 && aSetting.SendActivitySessionEmail != sendActivityEmail)
                    isChanged = true;
                else if (cSetting.SendActivitySessionEmail != sendActivityEmail)
                    isChanged = true;

                bool sendDailyEmail = ddlDailySummaryEmailSelectedValue == "1";
                if (aSetting.Id > 0 && aSetting.SendDailySummaryEmail != sendDailyEmail)
                    isChanged = true;
                else if (cSetting.SendDailySummaryEmail != sendDailyEmail)
                    isChanged = true;

                if (isChanged)
                {
                    if (!aSetting.SendActivitySessionEmail && ddlSessionActivityEmailSelectedValue == "1")
                    {
                        if (cSetting.SendActivitySessionEmail)
                            DeleteKeyValue("DisableStickeringActivityReportAfter15Minutes"); // use company default
                        else
                            SaveKeyValue("0", "DisableStickeringActivityReportAfter15Minutes");  // opt-in
                    }
                    else if (ddlSessionActivityEmailSelectedValue == "0")
                    {
                        SaveKeyValue("1", "DisableStickeringActivityReportAfter15Minutes");
                    }

                    if (!aSetting.SendDailySummaryEmail && ddlDailySummaryEmailSelectedValue == "1")
                    {
                        if (cSetting.SendDailySummaryEmail)
                            DeleteKeyValue("DisableStickeringDailyWaitingForActionReport"); // use company default
                        else
                            SaveKeyValue("0", "DisableStickeringDailyWaitingForActionReport");  // opt-in
                    }
                    else if (ddlDailySummaryEmailSelectedValue == "0")
                    {
                        SaveKeyValue("1", "DisableStickeringDailyWaitingForActionReport");
                    }
                }
            }

            // wait time
            var cgSetting = Extric.Towbook.Stickering.GracePeriodSetting.GetByCompanyId(AccountDetail.CompanyId, null)
                ?? new Extric.Towbook.Stickering.GracePeriodSetting();
            var agSetting = Extric.Towbook.Stickering.GracePeriodSetting.GetByCompanyId(AccountDetail.CompanyId, AccountDetail.Id);

            decimal? waitTime = cgSetting.Hours ?? 24;

            if (agSetting != null && agSetting.Hours != waitTime)
                waitTime = agSetting.Hours;

            decimal currentWaitTime = 0.0M;

            if ((tbWaitTime == "" && agSetting != null) ||
                (tbWaitTime != "" && Decimal.TryParse(tbWaitTime, out currentWaitTime) &&
                currentWaitTime != waitTime))
            {
                agSetting = agSetting ?? new Extric.Towbook.Stickering.GracePeriodSetting();
                agSetting.CompanyId = AccountDetail.CompanyId;
                agSetting.AccountId = AccountDetail.Id;
                agSetting.Hours = tbWaitTime == "" ? cgSetting.Hours ?? 24 : currentWaitTime;
                agSetting.OwnerUserId = WebGlobal.CurrentUser.Id;

                agSetting.Save(WebGlobal.CurrentUser);
            }

            #endregion

            #region Reasons, Reason times, etc
            Dictionary<int, int?> reasonList = new Dictionary<int, int?>();

            // delete all account start times and save with selected reasons
            Extric.Towbook.Stickering.ReasonTime.Delete(AccountDetail.Id);

            // For each reason in rpStickerReasonsDataSource
            foreach (var item in rpStickerReasonsDataSource)
            {
                int? reasonId = item.Id;

                if (reasonId == null)
                    continue;

                if (item.Selected)
                {
                    int startType = item.StartFromType;

                    reasonList.Add(reasonId.Value, null);

                    AccountDetail.DeleteReason(reasonId.Value);
                    AccountDetail.AddReason(reasonId.Value, WebGlobal.CurrentUser.Id, startType);

                    // reason times
                    var rt = new Extric.Towbook.Stickering.ReasonTime();

                    decimal decHours = 0;
                    if (decimal.TryParse(item.Hours, out decHours))
                        rt.Time = decHours;
                    else
                        rt.Time = (decimal?)null;

                    rt.AccountId = AccountDetail.Id;
                    rt.StickerReasonId = reasonId;
                    rt.CreateDate = DateTime.Now;
                    rt.Save();
                }
            }

            // go through the newly created list of what's selected.
            foreach (Extric.Towbook.Stickering.Reason r in AccountDetail.Reasons)
            {
                if (!reasonList.ContainsKey(r.Id))
                    AccountDetail.DeleteReason(r.Id);
            }
            #endregion
        }

        protected void SavePermits()
        {
            #region Required fields & parking spaces
            var cppSettings = Extric.Towbook.Accounts.ParkingPermitSetting.GetByCompanyId(AccountDetail.CompanyId, null);
            var appSettings = Extric.Towbook.Accounts.ParkingPermitSetting.GetByCompanyId(AccountDetail.CompanyId, AccountDetail.Id);
            bool isChanged = false;

            // always save new account settings.
            if (appSettings == null)
                appSettings = new Extric.Towbook.Accounts.ParkingPermitSetting();

            // parking spaces
            var parking = tbNumberOfParkingSpaces;
            if (parking == "")
                parking = "1";

            if (appSettings.PermitsPerResident != Convert.ToInt32(parking))
                isChanged = true;

            // guest spaces
            var guests = tbNumberOfGuests;
            if (guests == "")
                guests = "0";

            if (appSettings.GuestPermitsPerResident != Convert.ToInt32(guests))
                isChanged = true;

            // guest vehicle info requirement
            var guestVehicle = Convert.ToInt32(ddlRequireGuestVehicleSelectedValue);
            if (appSettings.RequireGuestVehicleInfo == null && guestVehicle == 0)
                isChanged = true;
            else if (appSettings.RequireGuestVehicleInfo != null && appSettings.RequireGuestVehicleInfo.Value != guestVehicle)
                isChanged = true;

            // guest pass update allowance
            var allowGuestUpdate = ddlAllowGuestPassUpdateSelectedValue == "true" ? true : false;
            if (appSettings.AllowGuestPassUpdate == null && !allowGuestUpdate)
                isChanged = true;
            else if (appSettings.AllowGuestPassUpdate != null && appSettings.AllowGuestPassUpdate.Value != allowGuestUpdate)
                isChanged = true;

            // expiration type
            var type = (Extric.Towbook.Accounts.PermitExpirationType)Convert.ToInt32(ddlExpirationTypeSelectedValue);
            if (appSettings.ExpirationType != type)
                isChanged = true;

            // Guest expriation days
            var days = Convert.ToInt32(ddlGuestExpirationDaysSelectedValue);
            if (appSettings.GuestExpirationDays != null && appSettings.GuestExpirationDays.Value != days)
                isChanged = true;

            // require email
            bool email = permitContactEmailChecked;
            if (appSettings.RequireEmail != email)
                isChanged = true;
            else if (cppSettings != null && cppSettings.RequireEmail != email)
                isChanged = true;

            // require color
            bool color = permitVehicleColorChecked;
            if (appSettings != null && appSettings.RequireColor != color)
                isChanged = true;
            else if (cppSettings != null && cppSettings.RequireColor != color)
                isChanged = true;

            // require Vin
            bool vin = permitVehicleVinChecked;
            if (appSettings.RequireVin != vin)
                isChanged = true;
            else if (cppSettings != null && cppSettings.RequireVin != vin)
                isChanged = true;

            // require registration number
            bool registrationNumber = permitStateRegistrationChecked;
            if (appSettings.RequireVehicleRegistration != registrationNumber)
                isChanged = true;
            else if (cppSettings != null && cppSettings.RequireVehicleRegistration != registrationNumber)
                isChanged = true;

            // require registration expiration
            bool registrationExpiration = permitStateRegistrationExpirationChecked;
            if (appSettings.RequireVehicleRegistrationExpiration != registrationExpiration)
                isChanged = true;
            else if (cppSettings != null && cppSettings.RequireVehicleRegistrationExpiration != registrationExpiration)
                isChanged = true;

            // require address
            bool requireAddress = permitContactAddressChecked;
            if (appSettings.RequireAddress != requireAddress)
                isChanged = true;
            else if (cppSettings != null && cppSettings.RequireAddress != requireAddress)
                isChanged = true;

            // parking permit public link
            if (ddlPublicLinkEnableSelectedValue == "true")
            {
                if (appSettings.ParkingPermitPublicLinkId == null || appSettings.ParkingPermitPublicLinkId.Value == 0)
                {
                    ParkingPermitPublicLinkDetail = ParkingPermitPublicLink.GetByAccountId(AccountDetail.Id);
                    if (ParkingPermitPublicLinkDetail == null)
                        ParkingPermitPublicLinkDetail = new ParkingPermitPublicLink();

                    ParkingPermitPublicLinkDetail.AccountId = AccountDetail.Id;
                    ParkingPermitPublicLinkDetail.Disabled = false;
                    ParkingPermitPublicLinkDetail.PropertyCode = tbPropertyCode;
                    ParkingPermitPublicLinkDetail.Save(WebGlobal.CurrentUser);

                    appSettings.ParkingPermitPublicLinkId = ParkingPermitPublicLinkDetail.Id;
                    isChanged = true;
                }
                else
                {
                    ParkingPermitPublicLinkDetail = ParkingPermitPublicLink.GetById(appSettings.ParkingPermitPublicLinkId.Value);
                    if (ParkingPermitPublicLinkDetail != null && ParkingPermitPublicLinkDetail.PropertyCode != tbPropertyCode)
                    {
                        ParkingPermitPublicLinkDetail.PropertyCode = tbPropertyCode;
                        ParkingPermitPublicLinkDetail.Save(WebGlobal.CurrentUser);
                    }
                }
            }
            else
            {
                if (appSettings.ParkingPermitPublicLinkId != null)
                {
                    ParkingPermitPublicLinkDetail = ParkingPermitPublicLink.GetById(appSettings.ParkingPermitPublicLinkId.Value);
                    if (ParkingPermitPublicLinkDetail != null && !ParkingPermitPublicLinkDetail.Disabled)
                    {
                        ParkingPermitPublicLinkDetail.Delete();
                    }

                    appSettings.ParkingPermitPublicLinkId = (int?)null;
                    isChanged = true;
                }
            }

            if (isChanged)
            {
                var aSetting = appSettings ?? new Extric.Towbook.Accounts.ParkingPermitSetting();
                aSetting.CompanyId = AccountDetail.CompanyId;
                aSetting.AccountId = AccountDetail.Id;
                aSetting.RequireEmail = email;
                aSetting.RequireAddress = requireAddress;
                aSetting.RequireColor = color;
                aSetting.RequireVin = vin;
                aSetting.RequireVehicleRegistration = registrationNumber;
                aSetting.RequireVehicleRegistrationExpiration = registrationExpiration;
                aSetting.PermitsPerResident = parking == "" ? 1 : Convert.ToInt32(parking);
                aSetting.GuestPermitsPerResident = guests == "" ? 0 : Convert.ToInt32(guests);
                aSetting.ExpirationType = type;
                aSetting.GuestExpirationDays = days;
                aSetting.RequireGuestVehicleInfo = guestVehicle;
                aSetting.AllowGuestPassUpdate = allowGuestUpdate;
                aSetting.Save(WebGlobal.CurrentUser);
            }
            #endregion

            #region Disclaimer
            var content = tbDisclaimer;
            var aDisclaimer = Extric.Towbook.Accounts.ParkingPermitDisclaimer.GetByCompanyId(AccountDetail.CompanyId, AccountDetail.Id);

            // don't delete company disclaimer (accountId == null)
            if (aDisclaimer == null || aDisclaimer.AccountId == null)
                aDisclaimer = new ParkingPermitDisclaimer();

            if (string.IsNullOrEmpty(content))
            {
                if (!string.IsNullOrEmpty(aDisclaimer.Content))
                {
                    // Delete account version if user emptied text
                    aDisclaimer.Delete(WebGlobal.CurrentUser);
                }
            }
            else
            {
                // save new content
                if (aDisclaimer.Content != content)
                {
                    aDisclaimer.AccountId = AccountDetail.Id;
                    aDisclaimer.CompanyId = AccountDetail.CompanyId;
                    aDisclaimer.Content = Core.HtmlEncode(content);
                    aDisclaimer.Save(WebGlobal.CurrentUser);
                }
            }
            #endregion
        }

        protected void DeleteKeyValue(string key)
        {
            var kv = AccountKeyValue.GetByAccount(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, key).FirstOrDefault();
            if (kv != null)
                kv.Delete();
        }

        protected bool SaveKeyValue(string v, string key)
        {
            var saved = false;

            if (!string.IsNullOrWhiteSpace(v))
            {
                var kv = AccountKeyValue.GetByAccount(AccountDetail.CompanyId, AccountDetail.Id, Provider.Towbook.ProviderId, key).FirstOrDefault()
                    ?? new AccountKeyValue(AccountDetail.Id, Provider.Towbook.GetKey(Extric.Towbook.Integration.KeyType.Account, key).Id, "");

                if (kv.Value == "0" && key == "ImportPayments")
                    kv.Delete();
                else if (kv.Value != v)
                {
                    kv.Value = v;
                    kv.Save();
                    saved = true;
                }
            }

            return saved;
        }

        protected void SaveStorageRates()
        {
            if (StorageRateDetail == null)
            {
                StorageRateDetail = new Extric.Towbook.Accounts.StorageRate();
                StorageRateDetail.AccountId = AccountDetail.Id;
            }

            if (txtMaximumCharges.Length > 0)
                StorageRateDetail.MaximumCharge = Convert.ToSingle(txtMaximumCharges);
            else
                StorageRateDetail.MaximumCharge = 0;

            StorageRateDetail.Save();
        }

        protected void SaveRequiredFields(bool useCODValue = false)
        {
            if (AccountDetail == null || AccountDetail.Id == -1)
                return;

            var allRuleSets = EntryValidationRuleSet.GetAllByCompanyIds(new[] { AccountDetail.CompanyId });

            var EntryValidationRuleSetDetail = allRuleSets.FirstOrDefault(x => x.AccountId == AccountDetail.Id);

            if (EntryValidationRuleSetDetail == null)
            {
                EntryValidationRuleSetDetail = new EntryValidationRuleSet();
                EntryValidationRuleSetDetail.AccountId = AccountDetail.Id;
                EntryValidationRuleSetDetail.CompanyId = AccountDetail.CompanyId;
                EntryValidationRuleSetDetail.AccountTypeId = null;
                EntryValidationRuleSetDetail.OwnerUserId = WebGlobal.CurrentUser.Id;
            }

            EntryValidationRuleSetDetail.RequireContactName = GetEntryRuleDropDownRequirementType(ddlReqDriverContactNameSelectedValue, ddlReqDispatcherContactNameSelectedValue);
            EntryValidationRuleSetDetail.RequireContactPhone = GetEntryRuleDropDownRequirementType(ddlReqDriverContactPhoneSelectedValue, ddlReqDispatcherContactPhoneSelectedValue);
            EntryValidationRuleSetDetail.RequirePurchaseOrderNumber = GetEntryRuleDropDownRequirementType(ddlReqDriverPurchaseOrderNumberSelectedValue, ddlReqDispatcherPurchaseOrderNumberSelectedValue);
            EntryValidationRuleSetDetail.RequireInvoiceNumber = GetEntryRuleDropDownRequirementType(ddlReqDriverInvoiceNumberSelectedValue, ddlReqDispatcherInvoiceNumberSelectedValue);
            EntryValidationRuleSetDetail.RequireCharges = GetEntryRuleDropDownRequirementType(ddlReqDriverChargesSelectedValue, ddlReqDispatcherChargesSelectedValue);
            EntryValidationRuleSetDetail.RequireOveragesPaid = GetEntryRuleDropDownRequirementType(ddlReqDriverOveragesSelectedValue, ddlReqDispatcherOveragesSelectedValue);
            EntryValidationRuleSetDetail.RequireVin = GetEntryRuleDropDownRequirementType(ddlReqDriverVinSelectedValue, ddlReqDispatcherVinSelectedValue);
            EntryValidationRuleSetDetail.RequirePlateNumber = GetEntryRuleDropDownRequirementType(ddlReqDriverLicensePlateSelectedValue, ddlReqDispatcherLicensePlateSelectedValue);
            EntryValidationRuleSetDetail.RequireOdometer = GetEntryRuleDropDownRequirementType(ddlReqDriverOdometerSelectedValue, ddlReqDispatcherOdometerSelectedValue);
            EntryValidationRuleSetDetail.RequirePhotos = GetEntryRuleDropDownRequirementType(ddlReqDriverPhotosSelectedValue, ddlReqDispatcherPhotosSelectedValue);
            EntryValidationRuleSetDetail.RequireSignature = GetEntryRuleDropDownRequirementType(ddlReqDriverSignatureSelectedValue, ddlReqDispatcherSignatureSelectedValue);
            EntryValidationRuleSetDetail.RequireDamageForm = GetEntryRuleDropDownRequirementType(ddlReqDriverDamageFormSelectedValue, ddlReqDispatcherDamageFormSelectedValue);

            EntryValidationRuleSetDetail.RequireReason = GetEntryRuleDropDownRequirementType(ddlReqDriverReasonSelectedValue, ddlReqDispatcherReasonSelectedValue);
            EntryValidationRuleSetDetail.RequireVehicleDestination = GetEntryRuleDropDownRequirementType(ddlReqDriverVehicleDestinationSelectedValue, ddlReqDispatcherVehicleDestinationSelectedValue);
            EntryValidationRuleSetDetail.RequireVehicleYear = GetEntryRuleDropDownRequirementType(ddlReqDriverVehicleYearSelectedValue, ddlReqDispatcherVehicleYearSelectedValue);
            EntryValidationRuleSetDetail.RequireVehicleMake = GetEntryRuleDropDownRequirementType(ddlReqDriverVehicleMakeSelectedValue, ddlReqDispatcherVehicleMakeSelectedValue);
            EntryValidationRuleSetDetail.RequireVehicleModel = GetEntryRuleDropDownRequirementType(ddlReqDriverVehicleModelSelectedValue, ddlReqDispatcherVehicleModelSelectedValue);
            EntryValidationRuleSetDetail.RequireVehicleColor = GetEntryRuleDropDownRequirementType(ddlReqDriverVehicleColorSelectedValue, ddlReqDispatcherVehicleColorSelectedValue);
            EntryValidationRuleSetDetail.RequireVehicleKeysLocation = GetEntryRuleDropDownRequirementType(ddlReqDriverVehicleKeysLocationSelectedValue, ddlReqDispatcherVehicleKeysLocationSelectedValue);
            EntryValidationRuleSetDetail.RequireInvoicePaid = GetEntryRuleDropDownRequirementType(ddlReqDriverPaidInFullSelectedValue, ddlReqDispatcherPaidInFullSelectedValue);

            EntryValidationRuleSetDetail.Save();
        }

        protected EntryValidationType? GetEntryRuleDropDownRequirementType(string ddlDriverValue, string ddlDispatcherValue)
        {
            if (ddlDriverValue == null || ddlDispatcherValue == null)
                return null;


            var driverVal = ddlDriverValue;
            var dispatcherVal = ddlDispatcherValue;

            if (driverVal == "0" && dispatcherVal == "0")
                return null;

            if (driverVal == "1")
            {
                if (dispatcherVal == "1")
                    return EntryValidationType.RequiredIncludingDispatchers;

                return EntryValidationType.Required;
            }

            return null;
        }

        protected void SetEntryRuleDropDownListValues(List<SelectListItem> ddlDriverItems, List<SelectListItem> ddlDispatcherItems, EntryValidationType? companyDefault, EntryValidationType? accountTypeDefault, EntryValidationType? accountDefault)
        {
            string byInheritanceTypeText = "Using Company Default - Not Required";
            string selectedValue = "0";
            bool isDispatcherControl = ddlDispatcherItems != null;

            if (companyDefault != null)
            {
                byInheritanceTypeText = "Using Company Default - " +
                    (!isDispatcherControl && (companyDefault == EntryValidationType.RequiredIncludingDispatchers || companyDefault == EntryValidationType.Required) ||
                        (isDispatcherControl && companyDefault == EntryValidationType.RequiredIncludingDispatchers)
                        ? "Required"
                        : "Not Required");
            }

            if (accountTypeDefault != null)
            {
                byInheritanceTypeText = "Using Account Type Default - " +
                    (!isDispatcherControl && (accountTypeDefault == EntryValidationType.RequiredIncludingDispatchers || accountTypeDefault == EntryValidationType.Required) ||
                        (isDispatcherControl && accountTypeDefault == EntryValidationType.RequiredIncludingDispatchers)
                        ? "Required"
                        : "Not Required");

            }

            if (accountDefault == EntryValidationType.Required)
            {
                if (isDispatcherControl)
                    selectedValue = "2";
                else
                    selectedValue = "1";
            }
            else if (accountDefault == EntryValidationType.RequiredIncludingDispatchers)
                selectedValue = "1";
            else if (accountDefault == EntryValidationType.NotRequired)
                selectedValue = "2";

            List<SelectListItem> items = new List<SelectListItem>();

            if (Company.Company.GetById(AccountDetail.CompanyId).HasAccessToBaseFeature("newCompletionRequirements"))
            {
                if (!string.IsNullOrWhiteSpace(byInheritanceTypeText))
                    items.Add(new SelectListItem(byInheritanceTypeText, "0"));

                items.Add(new SelectListItem("Always Required for this account", "1"));
                items.Add(new SelectListItem("Never Required for this account", "2"));
            }
            else
            {
                items.Add(new SelectListItem("Required", "1"));
                items.Add(new SelectListItem("Not Required", "2"));

                if (selectedValue == "0")
                    selectedValue = "2";
            }

            if (!isDispatcherControl)
            {
                ddlDriverItems.Clear();
                ddlDriverItems.AddRange(items);

                // Use the dictionary to set the selected value
                if (_driverDropdownValueSetters.ContainsKey(nameof(ddlDriverItems)))
                {
                    _driverDropdownValueSetters[nameof(ddlDriverItems)](selectedValue);
                }
            }
            else
            {
                ddlDispatcherItems.Clear();
                ddlDispatcherItems.AddRange(items);

                // Use the dictionary to set the selected value
                if (_dispatcherDropdownValueSetters.ContainsKey(nameof(ddlDispatcherItems)))
                {
                    _dispatcherDropdownValueSetters[nameof(ddlDispatcherItems)](selectedValue);
                }
            }
        }

        protected bool SaveTextBoxKeyValue(string txtBoxValue, string key)
        {
            if (txtBoxValue != null)
                return SaveKeyValue(txtBoxValue, key);
            else
                return false;
        }

        protected Account.PreferredBillableMethod GetPreferredBillableMethod(Account acc)
        {
            if (new[] {
                AccountType.MotorClub,
                AccountType.PrivateProperty,
                AccountType.PoliceDepartment,
                AccountType.Individual
            }.Contains(acc.Type))
                return Account.PreferredBillableMethod.Invoice;

            if (acc.Id == 1)
                return Account.PreferredBillableMethod.Invoice;

            return Account.PreferredBillableMethod.Statement;
        }


        public string Title { get; set; } = "Account Editor";
        public string ImpoundsChangePgScript { get; set; } = "";

        public string txtOpeningBalance { get; set; }
        public bool cbEnablePaymentImportVisible { get; set; }
        public bool cbEnableStickeringVisible { get; set; }
        public bool cbEnableSiteVisitsVisible { get; set; }
        public bool cbPreventPhotoSharingChecked { get; set; }
        public bool privatePropertyVisible { get; set; }
        public bool standardAccountVisible { get; set; }
        public bool openingBalanceVisible { get; set; }
        public bool cbDisableAutoAcceptVisible { get; set; }
        public bool cbDisableAutoAcceptChecked { get; set; }
        public List<Lot> rpImpoundLotsDataSource { get; set; } = new List<Lot>();
        public bool rpImpoundLotsVisible { get; set; } = false;
        public List<Lot> ThirdPartyImpoundLots { get; private set; }
        public Collection<AccountTag> AllAccountTags { get; private set; } = new Collection<AccountTag>();
        public List<int> AccountTagIds { get; set; } = new List<int>();
        public string txtFax { get; set; }
        public bool cbTaxExemptChecked { get; set; }
        public string txtCreditLimit { get; set; }
        public bool cbCreditHoldChecked { get; set; }
        public bool cbInactiveChecked { get; set; }
        public string txtNotes { get; set; }
        public string discountRate { get; set; }
        public string txtDefaultPO { get; set; }
        public bool cbDefaultPriorityChecked { get; set; }
        public bool cbPriorityCallTextAlertToManagersChecked { get; set; }
        public bool cbAlwaysHideChargesChecked { get; set; }
        public bool cbAlwaysHidePhotosChecked { get; set; }
        public bool cbAlwaysHideDiscountsChecked { get; set; }
        public List<SelectListItem> ddlEmailEventPreferenceItems { get; set; } = new List<SelectListItem>();
        public string ddlEmailEventPreferenceSelectedValue { get; set; }
        public string lblEmailEventPreferenceText { get; set; }
        public bool cbUsePhysicalAddressAsChecked { get; set; }
        public string ddlDefaultPhysicalAddressSelectedValue { get; set; }
        public bool ddlDefaultPhysicalAddressEnabled { get; set; }
        public bool cbAutoFillContactChecked { get; set; }
        public bool cbIncludeInvoicePaymentLinkChecked { get; set; }
        public string CbIncludeInvoicePaymentLinkText { get; set; }
        public bool cbIncludeStatementPaymentLinkChecked { get; set; }
        public string CbIncludeStatementPaymentLinkText { get; set; }
        public bool cbOptOutOfConfirmationEmailOnTransactionsChecked { get; set; }
        public string CbOptOutOfConfirmationEmailOnTransactionsText { get; set; }
        public bool cbExcludeLinkOnPrintedDispatchInvoicesChecked { get; set; }
        public string CbExcludeLinkOnPrintedDispatchInvoicesText { get; set; }
        public bool cbExcludeLinkOnPrintedImpoundInvoicesChecked { get; set; }
        public string CbExcludeLinkOnPrintedImpoundInvoicesText { get; set; }
        public bool cbExcludeLinkOnPrintedStatementsChecked { get; set; }
        public string CbExcludeLinkOnPrintedStatementsText { get; set; }
        public bool cbExcludeTipsOnPaymentLinksChecked { get; set; }
        public string CbExcludeTipsOnPaymentLinksText { get; set; }
        public bool cbExcludeTipsOnSquareReaderChecked { get; set; }
        public string CbExcludeTipsOnSquareReaderText { get; set; }
        public bool cbExcludeTipsOnSquareTerminalChecked { get; set; }
        public string CbExcludeTipsOnSquareTerminalText { get; set; }
        public bool cbAlwaysSendRoadsideInviteChecked { get; set; }
        public bool cbAlwaysSendRoadsideInviteVisible { get; set; }
        public string cbAlwaysSendRoadsideInviteText { get; set; }
        public bool cbAlwaysSendSurveyChecked { get; set; }
        public string cbAlwaysSendSurveyText { get; set; }
        public bool cbIncludeInvoiceCopiesOnStatementsChecked { get; set; }
        public List<SelectListItem> ddlSuggestedDefaultItems { get; set; } = new List<SelectListItem>();
        public string ddlSuggestedDefaultSelectedValue { get; set; }
        public List<SelectListItem> ddlAutomaticallyFillInMilesItems { get; set; } = new List<SelectListItem>();
        public string ddlAutomaticallyFillInMilesSelectedValue { get; set; }
        public bool cbUnloadedMileageSetOneChecked { get; set; }
        public bool cbUnloadedMileageRoundOneChecked { get; set; }
        public string cbUnloadedMileageRoundOneText { get; set; }
        public string cbUnloadedMileageRoundOneToolTip { get; set; }
        public bool cbAlwaysAddDeadheadChecked { get; set; }
        public string fuelSurcharge { get; set; }
        public string fuelSurchargeNoteText { get; set; }
        public bool rbPaymentImmediateChecked { get; set; }
        public bool rbPaymentNet15Checked { get; set; }
        public bool rbPaymentNet30Checked { get; set; }
        public bool rbStorageLotsNoneChecked { get; set; }
        public bool rbStorageLotsDefaultChecked { get; set; }
        public bool rbStorageLotThirdPartyChecked { get; set; }
        public bool cbEmailChecked { get; set; }
        public bool cbFaxChecked { get; set; }
        public bool cbMailChecked { get; set; }
        public string txtMaximumCharges { get; set; }
        public List<SelectListItem> ddlDefaultStorageItems { get; set; } = new List<SelectListItem>();
        public string ddlDefaultStorageSelectedValue { get; set; }
        public List<SelectListItem> ddlDefaultAssetBodyTypeItems { get; set; } = new List<SelectListItem>();
        public string ddlDefaultAssetBodyTypeSelectedValue { get; set; }
        public List<SelectListItem> ddlDefaultBillToAccountItems { get; set; } = new List<SelectListItem>();
        public string ddlDefaultBillToAccountSelectedValue { get; set; }
        public List<SelectListItem> ddlSubcontractorsItems { get; set; } = new List<SelectListItem>();
        public string ddlSubcontractorsSelectedValue { get; set; }
        public string towbookEmailAddress { get; set; }
        public string txtMobileNumbers { get; set; }
        public string mcTowbookEmailAddress { get; set; }
        public string towbookReferenceNumber { get; set; }
        public bool trCompanyOverrideVisible { get; set; }
        public List<SelectListItem> ddlCompanyOverrideItems { get; set; } = new List<SelectListItem>();
        public string ddlCompanyOverrideSelectedValue { get; set; }

        public string gateCode { get; set; }
        public string contractStartDate { get; set; }
        public string contractEndDate { get; set; }
        public bool cbCodAccountChecked { get; set; }
        public List<SelectListItem> ddlReqDriverContactNameItems { get; set; } = new List<SelectListItem>();
        public List<SelectListItem> ddlReqDispatcherContactNameItems { get; set; } = new List<SelectListItem>();
        public string ddlReqDriverContactNameSelectedValue { get; set; }
        public string ddlReqDispatcherContactNameSelectedValue { get; set; }
        public List<SelectListItem> ddlReqDriverContactPhoneItems { get; set; } = new List<SelectListItem>();
        public List<SelectListItem> ddlReqDispatcherContactPhoneItems { get; set; } = new List<SelectListItem>();
        public string ddlReqDriverContactPhoneSelectedValue { get; set; }
        public string ddlReqDispatcherContactPhoneSelectedValue { get; set; }
        public List<SelectListItem> ddlReqDriverPurchaseOrderNumberItems { get; set; } = new List<SelectListItem>();
        public List<SelectListItem> ddlReqDispatcherPurchaseOrderNumberItems { get; set; } = new List<SelectListItem>();
        public string ddlReqDriverPurchaseOrderNumberSelectedValue { get; set; }
        public string ddlReqDispatcherPurchaseOrderNumberSelectedValue { get; set; }
        public List<SelectListItem> ddlReqDriverInvoiceNumberItems { get; set; } = new List<SelectListItem>();
        public List<SelectListItem> ddlReqDispatcherInvoiceNumberItems { get; set; } = new List<SelectListItem>();
        public string ddlReqDriverInvoiceNumberSelectedValue { get; set; }
        public string ddlReqDispatcherInvoiceNumberSelectedValue { get; set; }
        public List<SelectListItem> ddlReqDriverChargesItems { get; set; } = new List<SelectListItem>();
        public List<SelectListItem> ddlReqDispatcherChargesItems { get; set; } = new List<SelectListItem>();
        public string ddlReqDriverChargesSelectedValue { get; set; }
        public string ddlReqDispatcherChargesSelectedValue { get; set; }
        public List<SelectListItem> ddlReqDriverPaidInFullItems { get; set; } = new List<SelectListItem>();
        public List<SelectListItem> ddlReqDispatcherPaidInFullItems { get; set; } = new List<SelectListItem>();
        public string ddlReqDriverPaidInFullSelectedValue { get; set; }
        public string ddlReqDispatcherPaidInFullSelectedValue { get; set; }
        public List<SelectListItem> ddlReqDriverOveragesItems { get; set; } = new List<SelectListItem>();
        public List<SelectListItem> ddlReqDispatcherOveragesItems { get; set; } = new List<SelectListItem>();
        public string ddlReqDriverOveragesSelectedValue { get; set; }
        public string ddlReqDispatcherOveragesSelectedValue { get; set; }
        public List<SelectListItem> ddlReqDriverVinItems { get; set; } = new List<SelectListItem>();
        public List<SelectListItem> ddlReqDispatcherVinItems { get; set; } = new List<SelectListItem>();
        public string ddlReqDriverVinSelectedValue { get; set; }
        public string ddlReqDispatcherVinSelectedValue { get; set; }
        public List<SelectListItem> ddlReqDriverLicensePlateItems { get; set; } = new List<SelectListItem>();
        public List<SelectListItem> ddlReqDispatcherLicensePlateItems { get; set; } = new List<SelectListItem>();
        public string ddlReqDriverLicensePlateSelectedValue { get; set; }
        public string ddlReqDispatcherLicensePlateSelectedValue { get; set; }
        public List<SelectListItem> ddlReqDriverOdometerItems { get; set; } = new List<SelectListItem>();
        public List<SelectListItem> ddlReqDispatcherOdometerItems { get; set; } = new List<SelectListItem>();
        public string ddlReqDriverOdometerSelectedValue { get; set; }
        public string ddlReqDispatcherOdometerSelectedValue { get; set; }
        public List<SelectListItem> ddlReqDriverPhotosItems { get; set; } = new List<SelectListItem>();
        public List<SelectListItem> ddlReqDispatcherPhotosItems { get; set; } = new List<SelectListItem>();
        public string ddlReqDriverPhotosSelectedValue { get; set; }
        public string ddlReqDispatcherPhotosSelectedValue { get; set; }
        public List<SelectListItem> ddlReqDriverSignatureItems { get; set; } = new List<SelectListItem>();
        public List<SelectListItem> ddlReqDispatcherSignatureItems { get; set; } = new List<SelectListItem>();
        public string ddlReqDriverSignatureSelectedValue { get; set; }
        public string ddlReqDispatcherSignatureSelectedValue { get; set; }
        public List<SelectListItem> ddlReqDriverDamageFormItems { get; set; } = new List<SelectListItem>();
        public List<SelectListItem> ddlReqDispatcherDamageFormItems { get; set; } = new List<SelectListItem>();
        public string ddlReqDriverDamageFormSelectedValue { get; set; }
        public string ddlReqDispatcherDamageFormSelectedValue { get; set; }
        public List<SelectListItem> ddlReqDriverReasonItems { get; set; } = new List<SelectListItem>();
        public List<SelectListItem> ddlReqDispatcherReasonItems { get; set; } = new List<SelectListItem>();
        public string ddlReqDriverReasonSelectedValue { get; set; }
        public string ddlReqDispatcherReasonSelectedValue { get; set; }
        public List<SelectListItem> ddlReqDriverVehicleDestinationItems { get; set; } = new List<SelectListItem>();
        public List<SelectListItem> ddlReqDispatcherVehicleDestinationItems { get; set; } = new List<SelectListItem>();
        public string ddlReqDriverVehicleDestinationSelectedValue { get; set; }
        public string ddlReqDispatcherVehicleDestinationSelectedValue { get; set; }
        public List<SelectListItem> ddlReqDriverVehicleYearItems { get; set; } = new List<SelectListItem>();
        public List<SelectListItem> ddlReqDispatcherVehicleYearItems { get; set; } = new List<SelectListItem>();
        public string ddlReqDriverVehicleYearSelectedValue { get; set; }
        public string ddlReqDispatcherVehicleYearSelectedValue { get; set; }
        public List<SelectListItem> ddlReqDriverVehicleMakeItems { get; set; } = new List<SelectListItem>();
        public List<SelectListItem> ddlReqDispatcherVehicleMakeItems { get; set; } = new List<SelectListItem>();
        public string ddlReqDriverVehicleMakeSelectedValue { get; set; }
        public string ddlReqDispatcherVehicleMakeSelectedValue { get; set; }
        public List<SelectListItem> ddlReqDriverVehicleModelItems { get; set; } = new List<SelectListItem>();
        public List<SelectListItem> ddlReqDispatcherVehicleModelItems { get; set; } = new List<SelectListItem>();
        public string ddlReqDriverVehicleModelSelectedValue { get; set; }
        public string ddlReqDispatcherVehicleModelSelectedValue { get; set; }
        public List<SelectListItem> ddlReqDriverVehicleColorItems { get; set; } = new List<SelectListItem>();
        public List<SelectListItem> ddlReqDispatcherVehicleColorItems { get; set; } = new List<SelectListItem>();
        public string ddlReqDriverVehicleColorSelectedValue { get; set; }
        public string ddlReqDispatcherVehicleColorSelectedValue { get; set; }
        public List<SelectListItem> ddlReqDriverVehicleKeysLocationItems { get; set; } = new List<SelectListItem>();
        public List<SelectListItem> ddlReqDispatcherVehicleKeysLocationItems { get; set; } = new List<SelectListItem>();
        public string ddlReqDriverVehicleKeysLocationSelectedValue { get; set; }
        public string ddlReqDispatcherVehicleKeysLocationSelectedValue { get; set; }
        public List<SelectListItem> ddlTypeItems { get; set; } = new List<SelectListItem>();
        public string ddlTypeSelectedValue { get; set; }
        public string cbEmailText { get; set; }
        public Collection<StickeringReasonTimeItem> rpStickerReasonsDataSource { get; set; } = new Collection<StickeringReasonTimeItem>();
        public List<SelectListItem> ddlManagerApprovalItems { get; set; } = new List<SelectListItem>();
        public string ddlManagerApprovalSelectedValue { get; set; }
        public List<SelectListItem> ddlManagerSignatureRequiredItems { get; set; } = new List<SelectListItem>();
        public string ddlManagerSignatureRequiredSelectedValue { get; set; }
        public List<SelectListItem> ddlAllowExtensionsItems { get; set; } = new List<SelectListItem>();
        public string ddlAllowExtensionsSelectedValue { get; set; }
        public List<SelectListItem> ddlSessionActivityEmailItems { get; set; } = new List<SelectListItem>();
        public string ddlSessionActivityEmailSelectedValue { get; set; }
        public List<SelectListItem> ddlDailySummaryEmailItems { get; set; } = new List<SelectListItem>();
        public string ddlDailySummaryEmailSelectedValue { get; set; }
        public string tbWaitTime { get; set; }
        public string tbWaitTimePlaceholder { get; set; }
        public string tbExpirationTime { get; set; }
        public string tbExpirationTimePlaceholder { get; set; }
        public string tbPropertyCode { get; set; }
        public string hiddenPropertyCode { get; set; }
        public string tbNumberOfParkingSpaces { get; set; }
        public string tbNumberOfParkingSpacesPlaceholder { get; set; }
        public string tbNumberOfGuests { get; set; }
        public string tbNumberOfGuestsPlaceholder { get; set; }
        public List<SelectListItem> ddlExpirationTypeItems { get; set; } = new List<SelectListItem>();
        public string ddlExpirationTypeSelectedValue { get; set; }
        public List<SelectListItem> ddlGuestExpirationDaysItems { get; set; } = new List<SelectListItem>();
        public string ddlGuestExpirationDaysSelectedValue { get; set; }
        public List<SelectListItem> ddlRequireGuestVehicleItems { get; set; } = new List<SelectListItem>();
        public string ddlRequireGuestVehicleSelectedValue { get; set; }
        public List<SelectListItem> ddlAllowGuestPassUpdateItems { get; set; } = new List<SelectListItem>();
        public string ddlAllowGuestPassUpdateSelectedValue { get; set; }
        public bool permitContactEmailChecked { get; set; }
        public bool permitContactAddressChecked { get; set; }
        public bool permitVehicleColorChecked { get; set; }
        public bool permitVehicleVinChecked { get; set; }
        public bool permitStateRegistrationChecked { get; set; }
        public bool permitStateRegistrationExpirationChecked { get; set; }
        public string tbDisclaimer { get; set; }
        public string tbDisclaimerPlaceholder { get; set; }
        public string tbAccountDisclaimer { get; set; }
        public string tbAccountDisclaimerPlaceholder { get; set; }
        public string tbStatementDisclaimer { get; set; }
        public string tbStatementDisclaimerPlaceholder { get; set; }
        public List<SelectListItem> ddlStatementDueDateItems { get; set; } = new List<SelectListItem>();
        public string ddlStatementDueDateSelectedValue { get; set; }
        public List<SelectListItem> ddlBillingMethodItems { get; set; } = new List<SelectListItem>();
        public string ddlBillingMethodSelectedValue { get; set; }
        public bool cbPrintedDeliveryChecked { get; set; }
        public bool cbEmailedDeliveryChecked { get; set; }
        public string tbInvoiceSubject { get; set; }
        public string tbInvoiceSubjectPlaceholder { get; set; }
        public string tbInvoiceMessage { get; set; }
        public string tbInvoiceMessagePlaceholder { get; set; }
        public string tbStatementSubject { get; set; }
        public string tbStatementSubjectPlaceholder { get; set; }
        public string tbStatementMessage { get; set; }
        public string tbStatementMessagePlaceholder { get; set; }
        public List<Extric.Towbook.User> rpUsersDataSource { get; set; } = new List<Extric.Towbook.User>();
        public List<SelectListItem> ddlAccountManagerItems { get; set; } = new List<SelectListItem>();
        public string ddlAccountManagerSelectedValue { get; set; }
        public List<SelectListItem> ddlPublicLinkEnableItems { get; set; } = new List<SelectListItem>();
        public string ddlPublicLinkEnableSelectedValue { get; set; }
        public bool labelRoadsideVisible { get; set; } = true;
    }



    public class StickeringReasonTimeItem
    {
        public int? Id { get; set; }
        public string Name { get; set; }
        public string Hours { get; set; }
        public decimal DefaultHours { get; set; }
        public bool Selected { get; set; }
        public int StartFromType { get; set; }
    }

}
