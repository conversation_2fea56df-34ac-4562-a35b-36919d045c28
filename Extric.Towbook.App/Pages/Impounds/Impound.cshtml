@page
@model Towbook.Pages.Impounds.ImpoundModel
@using Microsoft.AspNetCore.Html
@using Microsoft.AspNetCore.Authorization
@using Extric.Towbook
@using Extric.Towbook.Storage
@using Extric.Towbook.WebShared
@using Extric.Towbook.Dispatch
@using Extric.Towbook.API.Models.Calls
@using System.Web

@{
    //ViewData["Title"] = "Towbook - Impound Details";
//    Layout = "~/UI/Shared/_TowbookV2Layout.cshtml";
}

<script type="text/javascript" src="https://maps.googleapis.com/maps/api/js?client=gme-extricllc&libraries=places"></script>
<script type="text/javascript" src="/UI/js/Towbook/callEditorWindow.js"></script>
<script src="/ui/js/jquery.tmpl.min.js"></script>
<script src="~/Impounds/Impound.js?v=10"></script>

<link rel="stylesheet" type="text/css" href="/DispatchEditor/editor.css?v=1647PM_5_23_2017" />
<link rel="stylesheet" type="text/css" href="/ui/scss/towbook-overrides.css?v=12" />
<style>
.CellLeft { border-left: dotted 1px #afafaf }
#paymentTable .Cell, #paymentTable .CellLeft { vertical-align: middle }
table.list tbody tr td a { color: #0072c6; display: inline; font-size: 16px }
.details .clx { border: none }
.details .crx { border: none }

.x-void-payment { color: #0072c6; font-size: 16px }
.standard-button { margin-right: 10px; }
.ui-timepicker-wrapper { z-index: 1001; }
</style>

<style type="text/css">
/**
 * Impound Details Page
 */

.impoundDetail__root {
  display: flex;
  flex-direction: column;
  gap: 24px;
  margin-right: 20px;
}

.impoundDetail__columns {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

@@media (min-width: 768px) {
  .impoundDetail__columns {
    flex-direction: row;
  }
}

.impoundDetail__column {
  flex-shrink: 0;
}

@@media (min-width: 768px) {
  .impoundDetail__column {
    flex: 1 1 50%;
  }
}

.impoundDetail__section {
  margin-bottom: 24px;
}

.impoundDetail__sectionHeading {
  color: #a2a2a2;
  font-size: 24px;
  margin-bottom: 12px;
  display: flex;
  justify-content: space-between;
}

.impoundDetail__sectionSubHeading {
  font-size: 18px;
  padding-bottom: 4px;
  border-bottom: 1px solid #dfdfdf;
  margin-bottom: 8px;
  font-weight: bolder;
}

.impoundDetail__sectionSubHeading:not(:first-child) {
  margin-top: 24px;
}

.impoundDetail__sectionContent {
  background: #fff;
  border: 1px solid #dfdfdf;
  padding: 24px;
}

.impoundDetail__sectionContent table {
  width: 100%;
}

.modal-box {
    padding: 0px 15px 30px 10px;
}

.fa-download {
    color: black;
}

#fileList > tbody > tr > td > a {
    color: black !important;
}
#closed-status {
  display: flex;
}
#closed-status div { 
  display: flex; 
  font-family: InterVar, sans-serif;
}
.closed-wrapper {
  flex-direction: row;
  column-gap: 10px;
  align-items: center;
  padding: 10px 20px;
  width: 100%;
  background-color: #fff8f7;
}
.closed-wrapper .title {
  color: #b57216;
}
.closed-wrapper .closed-icon {
  flex-direction: column;
  flex-grow: 0;
  row-gap: 3px;
  padding: 10px;
  border-style: solid;
  border-radius: 20px;
  color: white;
  align-items: center;
  font-size: 15px !important;
  background-color: #e54d2e;
}
.closed-wrapper .fa {
  font-family: "Font Awesome 5 Pro" !important;
  display: flex;
}
.closed-wrapper .closed-message {
  flex-direction: column;
  flex: 0 0 70%;
  row-gap: 5px;
}
</style>

<script>
    towbook.users =[]; // TODO: fix...  JSON.parse('Html.Raw(Json.Serialize(Model.GetUsers()))');
</script>

@if (Model.Impound.Hold == true)
{
    if (Model.Impound.DispatchEntry.GetAttribute(73) != null)
    {
        Model.PoliceHoldMsg = $"This vehicle is currently under police hold and cannot be released until {Model.Impound.Account.Company} removes the Police Hold.";
        <h3 style="margin-right: 20px;
            border-radius: 6px;
            background: var(--amber-5);
            border: 1px solid var(--amber-a7);
            padding: 12px 16px;
            font-size: 14px;
            color: var(--amber-12);
            margin-bottom: 6px;">There is a police hold on this vehicle.<br />@Html.Raw(HttpUtility.HtmlEncode(Model.Impound.Account.Company)) must remove the hold before you can release it.</h3>
    }
    else
    {
        <h3 style="margin-right: 20px;
            border-radius: 6px;
            background: var(--amber-5);
            border: 1px solid var(--amber-a7);
            padding: 12px 16px;
            font-size: 14px;
            color: var(--amber-12);
            margin-bottom: 6px;">There is a police hold on this vehicle</h3>
    }
}

@{
    var srAttrValue = Model.Impound.DispatchEntry.Attributes.FirstOrDefault(f => f.Key == Extric.Towbook.Dispatch.AttributeValue.BUILTIN_VEHICLE_STOLEN_RECORD_JSON).Value;
    if (srAttrValue != null)
    {
        var data = Newtonsoft.Json.JsonConvert.DeserializeObject<Extric.Towbook.API.Models.Impounds.StolenVehicleRecordModel>(srAttrValue.Value);

        if (data != null && data.Status == "ACTIVE" && !string.IsNullOrEmpty(data.Description))
        {
            <h3 style="margin-right: 20px;
                border-radius: 6px;
                background: var(--red-5);
                border: 1px solid var(--red-a7);
                padding: 12px 16px;
                font-size: 14px;
                color: var(--red-a12);
                margin-bottom: 6px; ">@Extric.Towbook.Core.HtmlEncode(data.Description)</h3>
        }
    }
}

@if (Model.Impound.Auction == true)
{
    <h3 style="margin-right: 20px;
        border-radius: 6px;
        background: var(--blue-5);
        border: 1px solid var(--blue-a7);
        padding: 12px 16px;
        font-size: 14px;
        color: var(--blue-12);
        margin-bottom: 6px;">
        This vehicle is on the Auction List
        <a href="#" onclick="setAuction(@Model.Id.ToString(), false)" style="padding-left: 20px;">
            <i class="fa fa-times"></i><span style="padding-left: 5px; text-decoration: underline;">Remove from Auction</span>
        </a>
    </h3>
}

@{
    var links = Extric.Towbook.Dispatch.AttributeValue.GetAllByValue(Model.Impound.Company.Id, 72, Model.Impound.DispatchEntry.Id.ToString());
    if (links.Any())
    {
        <h3>Linked Invoice</h3>
        <div>
            @foreach (var xar in links)
            {
                var call = Entry.GetById(xar.DispatchEntryId);
                if (call != null)
                {
                    <text>Call @call.CallNumber <a href="#" onclick="impoundEditor.show(@call.Id.ToString()); return false;">Modify</a> <a target="_blank" asp-page="/Dispatch2/Invoice" asp-route-id="@call.Id" asp-route-type="pdf">Print</a><br /></text>
                }
            }
        </div>
    }
}

<div class="impoundDetail__root">
  <div class="impoundDetail__columns">
    <div class="impoundDetail__column">

        @if (Model.Impound.DispatchEntry.Company.HasFeature(Extric.Towbook.Generated.Features.Impounds_ImpoundAuctions))
        {
            var ad = Extric.Towbook.Auctions.EntryAuctionDetail.GetByDispatchEntryId(Model.Impound.DispatchEntry.Id);
            if (ad != null && ad.IsOneValuePresent())
            {
                <div class="impoundDetail__section">
                    <div class="impoundDetail__sectionHeading">Auction Details</div>
                    <div class="impoundDetail__sectionContent">
                        <table style="width: 100%; border-collapse: collapse" class="details">

                            @if (ad.HasCatalyticConverter != null)
                            {
                                <tr>
                                    <td class="clx">Catalytic Converter</td>
                                    <td class="crx">@(ad.HasCatalyticConverter.GetValueOrDefault() ? "Yes" : "No")</td>
                                </tr>
                            }

                            @if (ad.StartPrice != null)
                            {
                                <tr>
                                    <td class="clx">Start Price</td>
                                    <td class="crx">@(ad.StartPrice.Value.ToString("C"))</td>
                                </tr>
                            }

                            @if (ad.ReservePrice != null)
                            {
                                <tr>
                                    <td class="clx">Reserve Price</td>
                                    <td class="crx">@(ad.ReservePrice.Value.ToString("C"))</td>
                                </tr>
                            }

                            @if (ad.SalePrice != null)
                            {
                                <tr>
                                    <td class="clx">Sale Price</td>
                                    <td class="crx">@(ad.SalePrice.Value.ToString("C"))</td>
                                </tr>
                            }

                            @if (!string.IsNullOrEmpty(ad.LotNumber))
                            {
                                <tr>
                                    <td class="clx">Lot Number</td>
                                    <td class="crx">@(ad.LotNumber)</td>
                                </tr>
                            }

                            @if (!string.IsNullOrEmpty(ad.RowNumber))
                            {
                                <tr>
                                    <td class="clx">Row Number</td>
                                    <td class="crx">@(ad.RowNumber)</td>
                                </tr>
                            }

                            @if (!string.IsNullOrEmpty(ad.Notes))
                            {
                                <tr>
                                    <td class="clx">Description</td>
                                    <td class="crx">@Extric.Towbook.Core.HtmlEncode(ad.Notes)</td>
                                </tr>
                            }
                        </table>
                    </div>
                </div>
            }
        }

      <div class="impoundDetail__section">
        <div class="impoundDetail__sectionHeading">Call Details</div>
        @if (Model.IsWithinClosedPeriod)
        {
            <div id="closed-status">
                <div class="closed-wrapper">
                    <div class="closed-icon">
                        <i class="fa fa-minus-circle"></i>
                    </div>
                    <div class="closed-message">
                        <div class="title">This call is inside of your company's closed accounting period.</div>
                    </div>
                </div>
            </div>
        }
        <div class="impoundDetail__sectionContent">
            <table style="width: 100%; border-collapse: collapse" class="details">
                @if (WebGlobal.CurrentUser.CompanyId == 465 || WebGlobal.CurrentUser.CompanyId == 2)
                {
                    <tr>
                        <td class="clx">Current Status</td>
                        <td class="crx">@Model.Impound.CurrentStatus.Name
                        </td>
                    </tr>
                }

                @if (WebGlobal.Companies.Count() > 1)
                {
                    <tr>
                        <td class="clx">Company</td>
                        <td class="clx">@Extric.Towbook.Core.HtmlEncode(Model.Impound.Company.ShortName)</td>
                    </tr>
                }
                
                @if (!Model.HidePricing)
                {
                    <tr>
                        <td class="clx">Impound Total </td>
                        <td class="crx">@Model.Impound.InvoiceTotal.ToString("C") as of @WebGlobal.OffsetDateTime(DateTime.Now).Date.ToShortDate()</td>
                    </tr>

                    @if (Model.Impound.Invoice.BalanceDue != Model.Impound.InvoiceTotal)
                    {
                        <tr>
                            <td class="clx">Balance Due</td>
                            <td class="crx">@Model.Impound.Invoice.BalanceDue.ToString("C") as of @WebGlobal.OffsetDateTime(DateTime.Now).Date.ToShortDate()
                            </td>
                        </tr>
                    }
                }

                @if (Model.Impound.Lot != null)
                {
                    <tr>
                        <td class="clx">Storage Lot</td>
                        <td class="crx">@HttpUtility.HtmlEncode(Model.Impound.Lot)</td>
                    </tr>
                }

                @if (Model.Impound.ReleaseDate != null)
                {
                    <tr>
                        <td class="clx">Date Released</td>
                        <td class="crx">@WebGlobal.OffsetDateTime(Model.Impound.ReleaseDate.Value).ToLongDateString() @WebGlobal.OffsetDateTime(Model.Impound.ReleaseDate.Value).ToShortTowbookTimeString()@(Model.Impound.ReleasePickupDate == null ? "(this vehicle is still in your lot/has not been picked up yet)" : "")</td>
                    </tr>

                    @if (Model.Impound.ReleasePickupDate != null && Model.Impound.ReleaseDate != Model.Impound.ReleasePickupDate)
                    {
                        <tr>
                            <td class="clx">Date Picked up after Release</td>
                            <td class="crx">@WebGlobal.OffsetDateTime(Model.Impound.ReleasePickupDate.Value).ToLongDateString() @WebGlobal.OffsetDateTime(Model.Impound.ReleasePickupDate.Value).ToShortTowbookTimeString(), held @Model.Impound.DaysHeld days.</td>
                        </tr>
                    }

                    @if (Model.Impound.ReleaseReason != null)
                    {
                        var rr = Extric.Towbook.Impounds.ReleaseReason.GetById(Model.Impound.ReleaseReason.Value);
                        if (rr != null)
                        {
                            <tr>
                                <td class="clx">Release Type</td>
                                <td class="crx">@rr.Description</td>
                            </tr>
                        }
                    }

                    @if (Model.ReleaseDetails != null)
                    {
                        string output = String.Empty;

                        output = String.Format("{0}\n{1}\n{2} {3} {4}\n\n", Model.ReleaseDetails.FullName,
                            Model.ReleaseDetails.Address, Model.ReleaseDetails.City, Model.ReleaseDetails.State,
                            Model.ReleaseDetails.Zip);

                        if (!String.IsNullOrEmpty(Model.ReleaseDetails.Phone))
                            output += "Phone: " + HttpUtility.HtmlEncode(Extric.Towbook.Core.FormatPhone(Model.ReleaseDetails.Phone, Model.Impound.Company)) + "\n";

                        if (!String.IsNullOrEmpty(Model.ReleaseDetails.LicenseNumber))
                            output += "License Number: " + HttpUtility.HtmlEncode(Model.ReleaseDetails.LicenseNumber) + "\n";

                        if (Model.ReleaseDetails.BirthDate.HasValue && Model.ReleaseDetails.BirthDate != DateTime.MinValue)
                            output += "Birth Date: " + Model.ReleaseDetails.BirthDate.Value.ToShortDate() + "\n";

                        if (Model.ReleaseDetails.LicenseExpirationDate.HasValue && Model.ReleaseDetails.LicenseExpirationDate != DateTime.MinValue)
                            output += "License Expiration Date: " + Model.ReleaseDetails.LicenseExpirationDate.Value.ToShortDate() + "\n";

                        if (output.Trim().Length > 1)
                        {
                            <tr>
                                <td class="clx">Released to</td>
                                <td class="crx">@Html.Raw(Extric.Towbook.Core.HtmlEncode(output.Trim()).Replace("\n", "<br />"))</td>
                            </tr>
                        }
                    }
                }

                @if (!String.IsNullOrWhiteSpace(Model.Impound.ReleaseNotes))
                {
                    <tr>
                        <td class="clx">Release Notes</td>
                        <td class="crx">@Extric.Towbook.Core.HtmlEncode(Model.Impound.ReleaseNotes)</td>
                    </tr>
                }

                @if (Model.Impound.ImpoundDate != null)
                {
                    <tr>
                        <td class="clx">
                        @{
                            if (WebGlobal.CurrentUser.Company.Id == 465)
                            {
                                <text>Call In Date</text>
                            }
                            else if (WebGlobal.CurrentUser.Company.State == "WA" &&
                                WebGlobal.CurrentUser.Company.Country == Extric.Towbook.Company.Company.CompanyCountry.USA)
                            {
                                <text>Date Stored</text>
                            }
                            else
                            {
                                <text>Date Impounded</text>
                            }
                        }
                        </td>
                        <td class="crx">@WebGlobal.OffsetDateTime(Model.Impound.ImpoundDate.Value).ToLongDateString() @WebGlobal.OffsetDateTime(Model.Impound.ImpoundDate.Value).ToShortTowbookTimeString() (@Model.Impound.DaysHeldBillable days)</td>
                    </tr>
                }

                @if (Model.Impound.MotorVehicleReportDate != null)
                {
                    <tr>
                        <td class="clx">Motor Vehicle Report Date</td>
                        <td class="crx">@WebGlobal.OffsetDateTime(Model.Impound.MotorVehicleReportDate.Value).ToLongDateString()</td>
                    </tr>
                }

                <tr>
                    <td class="clx">Towed From</td>
                    <td class="crx">@HttpUtility.HtmlEncode(Model.DE.TowSource)</td>
                </tr>

                <tr>
                    <td class="clx">Driver</td>
                    <td class="crx">@HttpUtility.HtmlEncode(Model.DE.Driver)</td>
                </tr>

                <tr>
                    <td class="clx">Reason for Impound</td>
                    <td class="crx">

                    @if (WebGlobal.CurrentUser.PrimaryCompanyId == 1376)
                    {
                        @HttpUtility.HtmlEncode(Model.Impound.DispatchEntry.Reason)
                    }
                    else
                    {
                        @(Model.Impound.ImpoundType.HasValue ? Model.Impound.ImpoundType.Value.ToString().Replace("NotSpecified", "") : "")
                    }
                    @if (!String.IsNullOrWhiteSpace(Model.Impound.Reason))
                    {
                        <text> | @HttpUtility.HtmlEncode(Model.Impound.Reason)</text>
                    }
                    else
                    {
                        @HttpUtility.HtmlEncode(Model.Impound.DispatchEntry.Reason)
                    }
                    </td>
                </tr>

                @if (Model.DE.Account != null)
                {
                    <tr>
                        <td class="clx">Account (@Model.DE.Account.Type.ToString())</td>
                        <td class="crx">@HttpUtility.HtmlEncode(Model.DE.Account.Company)<br />
                            @HttpUtility.HtmlEncode(Extric.Towbook.Core.FormatPhone(Model.DE.Account.Phone, Model.DE.Company))</td>
                    </tr>
                }

                @if (Model.DE.Invoice != null && Model.DE.Invoice.AccountId > 0)
                {
                    <tr>
                        <td class="clx">Bill To Account</td>
                        <td class="crx">@HttpUtility.HtmlEncode(Extric.Towbook.Accounts.Account.GetById(Model.DE.Invoice.AccountId.Value).Company)</td>
                    </tr>
                }

                @if (!String.IsNullOrEmpty(Model.Impound.DispatchEntry.Notes))
                {
                    <tr>
                        <td class="clx">Notes</td>
                        <td class="crx">@Html.Raw(HttpUtility.HtmlEncode(Model.Impound.DispatchEntry.Notes).Replace("\n", "<br />"))</td>
                    </tr>
                }
                <tr>
                    <td class="clx">Call Number</td>
                    <td class="crx">@Model.Impound.DispatchEntry.CallNumber</td>
                </tr>

                <tr>
                    <td class="clx">Stock Number</td>
                    <td class="crx">@HttpUtility.HtmlEncode(Model.Impound.Id.ToString().PadLeft(7, '0'))</td>
                </tr>

                @if (!String.IsNullOrWhiteSpace(Model.Impound.DispatchEntry.PurchaseOrderNumber))
                {
                    <tr>
                        <td class="clx">PO Number</td>
                        <td class="crx">@HttpUtility.HtmlEncode(Model.Impound.DispatchEntry.PurchaseOrderNumber)</td>
                    </tr>
                }

                @if (Model.Impound.DispatchEntry.Attributes.ContainsKey(1101))
                {
                    <tr>
                        <td class="clx">Claim Number</td>
                        <td class="crx">@HttpUtility.HtmlEncode(Model.Impound.DispatchEntry.Attributes[1101].Value)</td>
                    </tr>
                }

                @if (WebGlobal.CurrentUser.CompanyId == 885)
                {
                    <tr>
                        <td class="clx">Receive Time</td>
                        <td class="crx">@WebGlobal.OffsetDateTime(Model.Impound.DispatchEntry.CreateDate)</td>
                    </tr>

                    @if (Model.Impound.DispatchEntry.DispatchTime != null)
                    {
                        <tr>
                          <td class="clx">Dispatch Time</td>
                          <td class="crx">@WebGlobal.OffsetDateTime(Model.Impound.DispatchEntry.DispatchTime.Value)</td>
                        </tr>
                    }

                    @if (Model.Impound.DispatchEntry.EnrouteTime != null)
                    {
                        <tr>
                          <td class="clx">Enroute Time</td>
                          <td class="crx">@WebGlobal.OffsetDateTime(Model.Impound.DispatchEntry.EnrouteTime.Value)</td>
                        </tr>
                    }

                    @if (Model.Impound.DispatchEntry.ArrivalTime != null)
                    {
                        <tr>
                          <td class="clx">Arrival Time</td>
                          <td class="crx">@WebGlobal.OffsetDateTime(Model.Impound.DispatchEntry.ArrivalTime.Value)</td>
                        </tr>
                    }

                    @if (Model.Impound.DispatchEntry.TowTime != null)
                    {
                        <tr>
                          <td class="clx">Tow Time</td>
                          <td class="crx">@WebGlobal.OffsetDateTime(Model.Impound.DispatchEntry.TowTime.Value)</td>
                        </tr>
                    }

                    @if (Model.Impound.DispatchEntry.CompletionTime != null)
                    {
                        <tr>
                          <td class="clx">Tow Completion Time</td>
                          <td class="crx">@WebGlobal.OffsetDateTime(Model.Impound.DispatchEntry.CompletionTime.Value)</td>
                        </tr>
                    }
                }

                @foreach(var field in Model.Impound.DispatchEntry.Attributes.Values)
                {
                    var av = Extric.Towbook.Dispatch.Attribute.GetById(field.DispatchEntryAttributeId);

                    var allow = Model.Impound.DispatchEntry.Company.State.ToUpperInvariant() == "WA" &&
                        WebGlobal.CurrentUser.Company.Country == Extric.Towbook.Company.Company.CompanyCountry.USA &&
                        new int[] { 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47 }.Contains(av.Id);

                    if (av.Company.Id == 1 && !allow)
                        continue;
                
                    <tr>
                        <td class="clx">@HttpUtility.HtmlEncode(av.Name)</td>
                        <td class="crx">@HttpUtility.HtmlEncode(Model.FormatAttributeValue(av.Type, field.Value))</td>
                    </tr>
                }
            </table>
        </div>
      </div>

      <div class="impoundDetail__section">
        <script type="module" defer src="/dist/import-add123.js?_=@Model.CacheBreakerKey"></script>
        <div class="impoundDetail__sectionHeading">Vehicle Details
            @if(WebGlobal.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.AutoDataDirectIntegration))
            {
                @if (Extric.Towbook.AutoDataDirect.AutoDataDirectConnection.GetByUserId(WebGlobal.CurrentUser.CompanyId, WebGlobal.CurrentUser.Id) != null)
                {
                    <div id="import-add123" data-vin=@(!string.IsNullOrEmpty(Model.DE.VIN) ? HttpUtility.HtmlEncode(Model.DE.VIN) : "")></div>
                }
                else
                {
                    <div id="connect-add123"></div>
                }
            }
        </div>
        <div class="impoundDetail__sectionContent">
          <div class="impoundDetail__sectionSubHeading">Vehicle Info</div>
            <table style="width: 100%; border-collapse: collapse" class="details">
              <tr>
                <td class="clx">Vehicle Description</td>
                <td class="crx">@(Model.DE.Year > 0 ? Model.DE.Year.ToString() : "") @HttpUtility.HtmlEncode(Model.DE.MakeModelFormatted) @(Model.DE.Color != null ? "(" + HttpUtility.HtmlEncode(Model.DE.Color) + ")" : "")</td>
              </tr>

              <tr>
                <td class="clx">Plate #</td>
                <td class="crx">@HttpUtility.HtmlEncode(Model.DE.LicenseNumber) <span id="licenseState">@Model.DE.LicenseState</span></td>
              </tr>

            @if (!string.IsNullOrWhiteSpace(Model.DE.VIN))
            {
              <tr>
                <td class="clx">VIN</td>
                <td class="crx">@HttpUtility.HtmlEncode(Model.DE.VIN)</td>
              </tr>
            }

            @if (Model.DE.Assets.Any(a => !string.IsNullOrWhiteSpace(a.DriveType)))
            {
              <tr>
                <td class="clx">Drive Type</td>
                <td class="crx">@HttpUtility.HtmlEncode(Model.DE.Assets.First(f => !string.IsNullOrWhiteSpace(f.DriveType)).DriveType)</td>
              </tr>
            }

            @if (Model.DE.Odometer > 0)
            {
              <tr>
                <td class="clx">Odometer</td>
                <td class="crx">@(Model.DE.Odometer > 0 ? Model.DE.Odometer.ToString() : "(information wasn't entered at time of entry)")</td>
              </tr>
            }

            <tr>
                <td class="clx">Have Keys</td>
                <td class="crx">@(Model.Impound.HasKeys == true ? "Yes" : "No")
                @{
                    if (Model.Impound.DispatchEntry.Attributes.ContainsKey(8))
                    {
                        @HttpUtility.HtmlEncode(Model.Impound.DispatchEntry.Attributes[8].Value)
                    }
                    else if(Model.Impound.DispatchEntry.Assets.Any() &&
                        !string.IsNullOrEmpty(Model.Impound.DispatchEntry.Assets[0].KeysLocation))
                    {
                        @HttpUtility.HtmlEncode(Model.Impound.DispatchEntry.Assets[0].KeysLocation)
                    }
                }
                </td>
            </tr>

            @if (Model.DE.Assets.Count > 0)
            {
                var airbagStatus = Model.DE.Assets[0].AirbagStatus == AirbagStatus.Deployed
                                ? "Deployed"
                                : Model.DE.Assets[0].AirbagStatus == AirbagStatus.NotDeployed
                                    ? "Not Deployed"
                                    : "";
                if(!string.IsNullOrEmpty(airbagStatus))
                {
                    <tr>
                        <td class="clx">Airbag Status</td>
                        <td class="crx">@HttpUtility.HtmlEncode(airbagStatus)</td>
                    </tr>
                }
                <tr>
                    <td class="clx">Drivable</td>
                    <td class="crx">
                        @{
                            Extric.Towbook.Dispatch.Drivable drivable = (Extric.Towbook.Dispatch.Drivable)Model.DE.Assets[0].Drivable;
                            string output = "Unknown";

                            switch (drivable)
                            {
                                case Extric.Towbook.Dispatch.Drivable.Drivable:
                                    output = "Yes";
                                    break;
                                case Extric.Towbook.Dispatch.Drivable.NotDrivable:
                                    output = "No";
                                    break;
                                case Extric.Towbook.Dispatch.Drivable.Starts:
                                    output = "Starts Only";
                                    break;
                                case Extric.Towbook.Dispatch.Drivable.StartsWithAJump:
                                    output = "Starts with a Jump";
                                    break;
                            }
                            @output
                        }
                    </td>
                </tr>
            }
            @foreach(Extric.Towbook.Dispatch.EntryContact dc in Model.Impound.DispatchEntry.Contacts)
            {
              <tr>
                <td class="clx">@(dc.Type == ContactType.Unspecified ? "Contact" : dc.Type.ToString())</td>
                <td class="crx">@dc.Name @Extric.Towbook.Core.FormatPhone(dc.Phone, Model.Impound.DispatchEntry.Company)
                    @if (!string.IsNullOrEmpty(dc.Address))
                    {
                        <br />@dc.Address
                    }
                    @if (!string.IsNullOrEmpty(dc.City) || !string.IsNullOrEmpty(dc.State) || !string.IsNullOrEmpty(dc.Zip))
                    {
                        <br />@(dc.City +  " " + dc.State + " " + dc.Zip)
                    }
                </td>
              </tr>
            }
            </table>

          @{
              var vt = Extric.Towbook.Dispatch.VehicleTitle.GetByImpoundId(Model.Impound.Id);
              var titleReceived = Extric.Towbook.EnumExtensionMethods.Enum<Extric.Towbook.Dispatch.TitleReceivedType>
                  .GetAllValuesAsIEnumerable()
                  .Where(w => vt != null && vt.TitleReceived.GetValueOrDefault() > 0 &&  w == vt.TitleReceived)
                  .Select(d => new Extric.Towbook.EnumExtensionMethods.EnumTypeDefinitionModel(d))
                  .FirstOrDefault();

              if (vt != null)
              {
                  <div class="impoundDetail__sectionSubHeading">Title Info</div>
                  <table style="width: 100%; border-collapse: collapse" class="details">
                      @if(titleReceived != null)
                      {
                        <tr>
                            <td class="clx">Title Received</td>
                            <td class="crx">Yes, @(titleReceived.Name)</td>
                        </tr>
                      }

                      @if (!string.IsNullOrEmpty(vt.TitleNumber))
                      {
                        <tr>
                            <td class="clx">Title Number</td>
                            <td class="crx">@(vt.TitleNumber)</td>
                        </tr>
                      }

                      @if(!string.IsNullOrEmpty(vt.TitleBrand))
                      {
                        <tr>
                            <td class="clx">Title Brand</td>
                            <td class="crx">@(vt.TitleBrand)</td>
                        </tr>
                      }

                      @if(!string.IsNullOrEmpty(vt.TitleJurisdiction))
                      {
                        <tr>
                            <td class="clx">Title Jurisdiction</td>
                            <td class="crx">@(vt.TitleJurisdiction)</td>
                        </tr>
                      }

                      @if(vt.TitleIssueDate != null)
                      {
                        <tr>
                            <td class="clx">Title Issue Date</td>
                            <td class="crx">@Extric.Towbook.Core.OffsetDateTime(Model.Impound.Company, vt.TitleIssueDate.Value).ToShortDate()
                            </td>
                        </tr>
                      }
                  </table>
              }
          }
        </div>
      </div>
    </div>

    <div class="impoundDetail__column">
      @if (!Model.HidePricing)
      {
      <div class="impoundDetail__section">
        <div class="impoundDetail__sectionHeading">Charges</div>
        <div class="impoundDetail__sectionContent">
            <table>
              <tr style="font-weight: bold">
                <td style="width: 60%; padding-left: 10px">Item</td>
                <td style="">Quantity</td>
                <td style="text-align: right">Price</td>
                <td style="text-align: right; padding-right: 10px">Line Total</td>
              </tr>

              @foreach (var item in Model.InvoiceItems)
              {
                  <tr>
                      <td style="padding-left: 10px">@item.Name</td>
                      <td>@item.Quantity</td>
                      <td style="text-align: right">@item.Price</td>
                      <td style="text-align: right; padding-right: 10px">@item.Total</td>
                  </tr>
              }
              
              @if (Model.Impound.InvoiceTax > 0)
              {
                  <tr>
                    <td style="padding-top: 10px; padding-left:10px" colspan="3">Sales Tax</td>
                    <td style="padding-top: 10px; text-align: right; padding-right: 10px">@Model.Impound.InvoiceTax.ToString("C")</td>
                  </tr>
              }

              <tr>
                <td style="padding-top: 10px; padding-left:10px" colspan="3">Grand Total</td>
                <td style="padding-top: 10px; text-align: right; padding-right: 10px; font-size: 16px">@Model.Impound.InvoiceTotal.ToString("C")</td>
              </tr>
            </table>
        </div>
      </div>
      }

      @if (!(WebGlobal.CurrentUser.Type == Extric.Towbook.User.TypeEnum.AccountUser && WebGlobal.CurrentUser.CompanyId == 1042) && !Model.HidePricing)
      {
         var xyx = Extric.Towbook.Dispatch.InvoicePayment.GetByImpoundId(Model.Impound.Id);
         if (xyx != null && xyx.Count > 0)
         {
             <div class="impoundDetail__section">
                <div class="impoundDetail__sectionHeading">Payments</div>
                <div class="impoundDetail__sectionContent">
                    <table id="paymentTable" class="list">
                      <thead>
                        <tr>
                          <th style="width: 100px">Type</th>
                          <th style="width: 100px">Amount</th>
                          <th>Reference Number</th>
                          <th>Date</th>
                            @if (WebGlobal.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Manager)
                            {
                                <th></th>
                            }
                        </tr>
                      </thead>
                      <tbody>
                      @foreach(var pd in xyx)
                      {
                          var action = "Void";
                          if (pd.PaymentType == PaymentType.Square &&
                              Model.Impound.Company.HasFeature(Extric.Towbook.Generated.Features.PaymentIntegrations_Square))
                          {
                              var squarePayment = Extric.Towbook.SquareIntegration.SquarePayment.GetByInvoicePaymentId(pd.Id, Model.Impound.Company.Id);
                              if(squarePayment != null && squarePayment.PaymentStatus == "PENDING")
                                action = "Pending";
                              else
                                action = "Refund";

                              var squareRefund = Extric.Towbook.SquareIntegration.SquareRefund.GetByPaymentId(pd.ReferenceId);
                              if(squareRefund != null && squareRefund.Status == "PENDING")
                                action = "Refund Pending";
                          }

                          if (pd.ReferenceNumber != null && pd.ReferenceNumber.Contains("[TB:R]"))
                              pd.ReferenceNumber = pd.ReferenceNumber.Replace("[TB:R]", "");

                          if (pd.IsVoid)
                              continue;
                          
                          <tr data-payment-id="@pd.Id" data-entry-id="@Model.Impound.DispatchEntry.Id">
                              <td>@Extric.Towbook.Core.HtmlEncode(pd.PaymentType.ToString())</td>
                              <td>@pd.Amount.ToString("C")</td>
                              <td>@HttpUtility.HtmlEncode(pd.ReferenceNumber)</td>
                              <td>@WebGlobal.OffsetDateTime(pd.PaymentDate.Value).ToShortDate()</td>
                              @if (WebGlobal.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Manager)
                              {
                                  <td class="Cell">
                                      @if (action.Contains("Pending"))
                                      {
                                          @action
                                      }
                                      else
                                      {
                                          <a href="#" class="x-void-payment">@action</a>
                                      }
                                  </td>
                              }
                          </tr>
                      }
                      </tbody>
                    </table>
                </div>
             </div>
         }
      }

      <div class="impoundDetail__section">
        <div class="impoundDetail__sectionHeading">Photographs</div>
        <div class="impoundDetail__sectionContent">
            @{
                var list = Extric.Towbook.Impounds.Photo.GetByImpoundId((Model.Impound != null ? Model.Impound.Id : 0));
                var callPhotoList = Extric.Towbook.Dispatch.Photo.GetByDispatchEntryId(Model.DE.Id);
                IEnumerable<Extric.Towbook.Stickering.Photo> stickerPhotoList = new Extric.Towbook.Stickering.Photo[0];
                var sticker = Extric.Towbook.Stickering.Sticker.GetByDispatchEntryId(Model.DE.Id);
                if (sticker != null)
                {
                    stickerPhotoList = Extric.Towbook.Stickering.Photo.GetByStickerId(sticker.Id);
                }

                if (list.Count > 0 || callPhotoList.Count > 0 || stickerPhotoList.Count() > 0)
                {
                    <text>Click on the desired photo to view a larger copy of it.<br /></text>
                    foreach (Extric.Towbook.Impounds.Photo p in list)
                    {
                        var url = FileUtility.GetPresignedUrlForDownloadFromClient(p.Location.Replace("%1", Model.Impound.Company.Id.ToString()), p.ContentType, 30);
                        <a asp-page="/Impounds/Photo" asp-route-id="@p.Id.ToString()" rel="towbook-dialog" data-dialog-height="500" ><img src="@url" height="128" style="max-height:128px;margin-right: 10px; margin-top: 10px; border: solid 1px #afafaf; padding: 1px; float: left"/></a>
                    }

                    foreach (var p in callPhotoList)
                    {
                        var url = FileUtility.GetPresignedUrlForDownloadFromClient(p.Location.Replace("%1", Model.Impound.Company.Id.ToString()), p.ContentType, 30);
                        <a asp-page="/Impounds/Photo" asp-route-id="@p.Id.ToString()" asp-route-callId="@p.DispatchEntryId" rel="towbook-dialog" data-dialog-height="500"><img src="@url"
                              height="128" style="max-height:128px;margin-right: 10px; margin-top: 10px; border: solid 1px #afafaf; padding: 1px; float: left"/></a>
                    }

                    foreach (var p in stickerPhotoList)
                    {
                        var url = FileUtility.GetPresignedUrlForDownloadFromClient(p.Location.Replace("%1", Model.Impound.Company.Id.ToString()), p.ContentType, 30);
                        <a asp-page="/Impounds/Photo" asp-route-id="@p.Id.ToString()" asp-route-stickerId="@p.StickerId" rel="towbook-dialog" data-dialog-height="500"><img src="@url"
                              height="128" style="max-height:128px;margin-right: 10px; margin-top: 10px; border: solid 1px #afafaf; padding: 1px; float: left"/></a>
                    }
                }
                else
                {
                    <text>There are no photos to display. To upload a photo, click Upload Photos at the top of this page.</text>
                }
            }
            <div style="clear: both"></div>
        </div>
      </div>
      <div class="impoundDetail__section">
        <div class="impoundDetail__sectionHeading">Files</div>
        <div class="impoundDetail__sectionContent">
            @{
                var filelist = Extric.Towbook.CompanyFile.GetByDispatchEntryId(Model.Impound.DispatchEntry.Id).ToList();
                if (filelist != null && filelist.Count() == 0)
                {
                    <text>There are no files to display. To add a file, click Upload Files at the top of this page.</text>
                }

                if (Request.Query["debug"].Count == 0)
                {
                    filelist = filelist.Where(w => !Extric.Towbook.CompanyFile.IsInternalFile(w)).ToList();
                }
            }
            <style>
                .files-table td { padding-right: 15px; }
                .files-table .delete-file { cursor: pointer; }
                .files-table .delete-file:hover { color: red; }
            </style>

            <table class="files-table">
                @foreach (var file in filelist)
                {
                    <tr>
                        <td><a href="/File.ashx?id=@file.Id">@file.Filename</a></td>
                        <td>@WebGlobal.OffsetDateTime(file.CreateDate).ToShortDate()</td>
                        <td>
                            <i class="delete-file fa fa-trash" onclick="deleteFile(@file.Id)"></i>
                        </td>
                    </tr>
                }
            </table>
        </div>
      </div>
    </div>
  </div>


    @if(WebGlobal.CurrentUser.CompanyId != 32564 && Model.Notes.Count > 0)
    {
       <div class="impoundDetail__section">
        <div class="impoundDetail__sectionHeading">Notes</div>
        <div class="impoundDetail__sectionContent">
            @foreach (var note in Model.Notes)
            {
                <div style="padding-left: 10px; padding-top: 10px" class="impound-note">
                <strong>@note.User.FullName</strong> wrote at @WebGlobal.OffsetDateTime(note.CreateDate).ToShortTowbookTimeString() on @WebGlobal.OffsetDateTime(note.CreateDate).ToShortDate():
                    @if (WebGlobal.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Manager)
                    {
                        <span><a href="#" class="delete-note" data-impound-id="@Model.Id" data-id="@note.Id">Delete</a></span>
                    }
                <br />
                <div style="margin-left: 20px">
                @HttpUtility.HtmlEncode(note.Content)
                </div>
                </div>
            }
        </div>
      </div>
    }

  <script type="module" defer src="/dist/tasks-letters.js?_=@Model.CacheBreakerKey"></script>
  <div id="tasks-letters"></div>


  <div class="impoundDetail__columns">
    <div class="impoundDetail__column">

    @if (WebGlobal.CurrentUser.CompanyId != 32564)
    {
      <div class="impoundDetail__section">
        <div class="impoundDetail__sectionHeading">Impound History</div>
        <div class="impoundDetail__sectionContent">
            @if (Model.History.Count > 0)
            {
                <table class="list">
                    <thead>
                        <tr>
                            <th style="padding-left: 10px">Impound History</th>
                            <th style="width: 200px">Date</th>
                            <th style="width: 200px">Performed By</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var historyItem in Model.History)
                        {
                            <tr>
                                <td>@HttpUtility.HtmlEncode(historyItem.Description)</td>
                                <td>@WebGlobal.OffsetDateTime(historyItem.CreateDate)</td>
                                <td>@HttpUtility.HtmlEncode(historyItem.User.FullName)</td>
                            </tr>
                        }
                    </tbody>
                </table>
            }
        </div>
      </div>
    }
    </div>
  </div>
</div>


<style>
.impound-note span { display:none}
.impound-note:hover span { display: inline-block }
</style>

@section Scripts {
<script>
    var skipReload = false;

    // Pusher setup
    Pusher.channel_auth_endpoint = '/Security/Pusher.aspx';
    var pusher = window.pusher || new Pusher('00d0fb70749a0a4fd6f9', { authEndpoint: Pusher.channel_auth_endpoint });

    if (pusher.channel('private-TWBK_Client_' + @WebGlobal.CurrentUser.PrimaryCompanyId) == null)
        pusher.subscribe('private-TWBK_Client_' + @WebGlobal.CurrentUser.PrimaryCompanyId);

    pusher.channel('<EMAIL>')
        .bind('call_update', function (data) {
            console.log('Pusher::call_update -> with data:', data);
            if (data.extra != "update")
                return;

            if (data.callId == @Model.Impound.DispatchEntry.Id) {
                impoundEditor.popup.close();

                if (!skipReload)
                    reloadNow();
            }
        });

    pusher.bind('invoice_payment_update', function (data) {
        console.log("Pusher::invoice_payment_update -> ", data);

        if (data.statusId === 2 || data.statusId === 6) {
            if (!skipReload)
                reloadNow();
        }
    });

    reloadNow = function () {
        window.location.reload(true);
    }


    function showPrintInvoice(forceReloadOnClose) {

        if (forceReloadOnClose) {
            var href = $('#x-impound-print').attr('href');
            if (href) {
                href += '&reloadOnClose=true';
                $('#x-impound-print').attr('href', href);
            }
        }

        $('#x-impound-print').trigger('click');
    }

    $(function () {
        $('.delete-note').on('click', function () {
            var local = this;
            if (confirm('Are you sure you want to delete this note?')) {
                $.ajax({
                    url: "/api/impounds/" + $(this).data("impoundId") + "/notes/" + $(this).data("id"),
                    type: 'DELETE',
                    success: function (data) {
                        $(local).closest('div').remove();
                    },
                    error: function (data) { alert("Note couldnt be deleted"); }
                });
            }
        });

        $('.x-popup').on('click', function (event) {
            impounds.loadPopup($(this));

            event.preventDefault();
            return false;
        });

        @if(Request.Query["action"] == "release" && !Model.Impound.Hold && Model.Impound.ReleaseDate == null)
        {
            <text>$('.x-release-vehicle').trigger('click');</text>
        }

    });

    var letters = JSON.parse('@Html.Raw(Json.Serialize(Model.LetterTemplateJson))');
    var impoundTasksJson = JSON.parse('@Html.Raw(Json.Serialize(Model.TasksJson))');
    var contactsJson = JSON.parse('@Html.Raw(Json.Serialize(Model.ContactsJson))');
    var isConnectedToADD = false;

    var call = JSON.parse('@Html.Raw(Json.Serialize(Model.CallModelJson))');
    var photos = JSON.parse('@Html.Raw(Json.Serialize(Model.CallPhotosJson))');
    var impounds = window.impounds || {};
    var details = call.auctionDetails || {};
    var asset = call.assets.length ? call.assets[0] : {};
    var title = (call.impoundDetails || []).title || {};
    var auctions = JSON.parse('@Html.Raw(Json.Serialize(Model.AuctionsJson))');


    var impoundEditor = new CallEditorWindow();

    var impounds = {
        _popup: null,
        _config: {},
        _call: null,
        closePopup: function () {
            if (this._popup)
                this._popup.close();
        },
        loadPopup: function (obj) {
            var self = this;

            var config = {
                width: $(window).width() > 1200 ? 1000 : w = '80%',
                height: $(obj).data('height') != null ? $(obj).data('height') : ($(window).height() / 100) * 70,
                modal: true,
                url: $(obj).attr('href'),
                title: $(obj).data('title'),
            };

            $.extend(self._config, config);

            self._popup = new Popup({
                title: config.title,
                width: config.width,
                height: config.height,
                modal: config.modal,
                footer: '<div id="call-editor-panel-footer">' +
                    '<input type="button" class="standard-button" id="discard" value="Close" onclick="skipReload = false; impounds.closePopup();" />' +
                    '</div>',
                onClose: function () {

                }
            });

            self._popup.show();
            self._popup.showSpinner();

            $.ajax(config.url + (config.url.indexOf('?') != -1 ? '&_=' : '?_=') + Math.random())
                .done(function (resp) {
                    self._popup.setContent(resp);

                    if (config.title == "Release Vehicle") {
                        $('#button-container').remove();
                        $('#call-editor-panel-footer').prepend($('<input type="button" class="standard-button" value="Release" onclick="impounds.releaseVehicle(@Model.Id, false); return false;" />'));

                        $('#call-editor-panel-footer').prepend($('<input type="button" class="standard-button" value="Release and Print" onclick="impounds.releaseVehicle(@Model.Id, true); return false;" />'));
                    }

                    if (config.title == "Auction Vehicle") {
                        var buttonRemove = $('#btnRemove').detach();
                        var buttonSave = $('#btnSave').detach();
                        $('#button-container').remove();
                        $('#call-editor-panel-footer').prepend(buttonSave);
                        $('#call-editor-panel-footer').prepend(buttonRemove);

                        window.auctionPopupRemoveBtn = buttonRemove
                    }
                })
                .fail(function (xhr, status, error) {
                    impounds.handleError(xhr, status, error);
                })
                .always(function () {
                    self._popup.hideSpinner();
                });
        },
        releaseVehicle: function (id, releaseAndPrint) {

            function getPaymentType(obj) {
                switch (obj.val()) {
                    case "rbPaidCash":
                    case "rbPaidCash2":
                        return @((int)Extric.Towbook.Dispatch.PaymentType.Cash);
                    case "rbPaidCheck":
                    case "rbPaidCheck2":
                        return @((int)Extric.Towbook.Dispatch.PaymentType.Check);
                    case "rbPaidCcVisa":
                    case "rbPaidCcVisa2":
                        return @((int)Extric.Towbook.Dispatch.PaymentType.Visa);
                    case "rbPaidCcMasterCard":
                    case "rbPaidCcMasterCard2":
                        return @((int)Extric.Towbook.Dispatch.PaymentType.MasterCard);
                    case "rbPaidCcDiscover":
                    case "rbPaidCcDiscover2":
                        return @((int)Extric.Towbook.Dispatch.PaymentType.Discover);
                    case "rbPaidCcAmericanExpress":
                    case "rbPaidCcAmericanExpress2":
                        return @((int)Extric.Towbook.Dispatch.PaymentType.AmericanExpress);
                    case "rbPaidCcDebit":
                    case "rbPaidCcDebit2":
                        return @((int)Extric.Towbook.Dispatch.PaymentType.Debit);
                    case "rbPaidSquare":
                        return @((int)Extric.Towbook.Dispatch.PaymentType.Square);
                    default:
                        var cId = parseInt(obj.data('id'));
                        if (isNaN(cId))
                            return "";
                        else
                            return cId;
                }
            }

            function getPaymentTotal(pay1, pay2) {

                if (isNaN(pay1))
                    pay1 = 0;
                if (isNaN(pay2))
                    pay2 = 0;

                return pay1 + pay2;
            }

            var payments = [];

            var pay1 = parseFloat($('#payAmount').val());
            var pay2 = parseFloat($('#payAmount2').val());
            var totalPayment = getPaymentTotal(pay1, pay2);

            if (!isNaN(pay1) && !isNaN(pay2)) {

                payments.push({
                    type: getPaymentType($("input[name='payment']:checked")),
                    reference: $('#payReferenceNumber').val(),
                    amount: pay1
                });

                payments.push({
                    type: getPaymentType($("input[name='payment2']:checked")),
                    reference: $('#payReferenceNumber2').val(),
                    amount: pay2
                });
            }

            var data = {
                releaseDate: $('#x-ReleaseDate').getVal(),
                releaseReason: parseInt($("input[name='rbReleaseReason']:checked").val() || "0"),
                fullName: $('#txtFullName').val(),
                phone: $('#txtPhone').val(),
                address: $('#txtAddress').val(),
                city: $('#txtCity').val(),
                state: $('#txtState').val(),
                zip: $('#txtZip').val(),
                licenseNumber: $('#txtDriversLicense').val(),
                licenseNumberExpirationDate: $('#txtLicenseExpirationDate').getVal(),
                licenseType: $('#txtLicenseType').val(),
                birthDate: $('#txtBirthDate').getVal(),
                paymentType: getPaymentType($("input[name='payment']:checked")),
                paymentAmount: totalPayment,
                paymentReferenceNumber: $('#payReferenceNumber').val(),
                notes: $('#txtDescription').val(),
                payments: payments,
                shouldUpdateStorageQuantityOnRelease: $("#shouldUpdateStorageQuantityOnRelease").val() != "false"
            };

            if (data.licenseNumberExpirationDate &&
                !isValidDate(data.licenseNumberExpirationDate)) {
                swal({
                    title: "Invalid Date", text: "Invalid License Expiration Date. Please enter in correct format, like: 01/01/2000",
                    type: "error"
                });

                return;
            }

            if (data.birthDate &&
                !isValidDate(data.birthDate)) {
                swal({
                    title: "Invalid Date", text: "Invalid Birth Date. Please enter in correct format, like: 01/01/2000",
                    type: "error"
                });

                return;
            }

            $.ajax({
                url: '/api/impounds/@Model.Impound.Id/release',
                data: JSON.stringify(data),
                type: 'POST',
                contentType: "application/json; charset=utf-8",
                beforeSend: function () {
                    impounds._popup.showSpinner('Please wait');
                }
            }).done(function () {
                impounds._popup.close();

                if (releaseAndPrint) {
                    showPrintInvoice(true);
                }
                else
                {
                    swal({ type: 'success', text: 'Vehicle was released from storage successfully.' })
                        .then(function () {
                            window.location.reload(true);
                        });
                }
            }).fail(function (xhr, status, error) {
                    impounds.handleError(xhr, status, error);
            }).always(function () {
                    impounds._popup.hideSpinner();
            });
        },
        handleError: function (xhr, status, error) {
            console.log(xhr, status, error);
            var msg = xhr != null && xhr.responseText.length > 0 ?
                xhr.responseText : "Server returned status of " + xhr.status;

            swal({ title: "Error", text: msg, type: "error" });
        }
    }

    function towOutFromStorage(id, event) {
        event.preventDefault();
        event.stopPropagation();

        swal({
            title: 'Tow Out',
            text: 'Are you sure you want to tow this vehicle from storage?',
            type: 'warning',
            showCancelButton: true,
            cancelButtonText: "No",
            confirmButtonText: "Yes, tow from storage",
            allowEscapeKey: false,
            allowOutsideClick: false
        }).then(function () {

            swal({
                title: "Please wait",
                text: "Pulling vehicle out of storage...",
                showCloseButton: false,
                allowOutsideClick: false,
                closeOnEsc: false,
                onOpen: function () {
                    swal.showLoading()
                }
            });
            skipReload = true;

            $.post('/api/calls/' + id + '/towOut').done(function (data) {
                reloadNow();

                swal({
                    title: 'Success',
                    text: 'Vehicle is waiting to be dispatched.',
                    type: 'success',
                    closeOnEsc: false
                });
            }).fail(function (xhr, status, error) {
                handleError(xhr, status, error);
                skipReload = false;
            });
        });
    }

    function undoTowOutFromStorage(id, event) {
        event.preventDefault();
        event.stopPropagation();

        swal({
            title: 'Undo Tow Out',
            text: 'Are you sure you want to put the vehicle back in storage?',
            type: 'warning',
            showCancelButton: true,
            cancelButtonText: "No",
            confirmButtonText: "Yes, undo the tow out",
            allowEscapeKey: false,
            allowOutsideClick: false
        }).then(function () {

            swal({
                title: "Please wait",
                text: "Undoing the tow out and putting the vehicle back into storage...",
                showCloseButton: false,
                allowOutsideClick: false,
                onOpen: function () {
                    swal.showLoading()
                }
            });
            skipReload = true;

            $.post('/api/calls/' + id + '/undoTowOut').done(function (data) {
                reloadNow();

                swal({
                    title: 'Success',
                    text: 'Vehicle is back in storage.',
                    type: 'success',
                    closeOnEsc: false
                });
            }).fail(function (xhr, status, error) {
                handleError(xhr, status, error);
                skipReload = false;
            });
        });
    }

    function handleError(xhr, status, error) {
        var ex = null;
        msg = "Server returned: " + error;
        try {
            ex = JSON.parse(xhr.responseText);
            if (ex && ex.exceptionMessage)
                msg = ex.exceptionMessage;
        } catch (e) {
            ex = xhr;
        }
        console.log(ex, status, error);

        swal({ title: "Error", text: msg, type: "error" });
    }

    function sendToSquareTerminal(callId, deviceId, amount, releasePayment) {
        var deferred = new $.Deferred();

        var data = {
            callId,
            deviceId,
            amount,
        };

        console.log('Sending the checkout request to the terminal...', data);

        swal({
            title: 'Please Wait',
            text: 'Sending the checkout request to the terminal...',
            allowOutsideClick: false,
            closeOnEsc: false,
            onOpen: function () {
                swal.showLoading()
            }
        });

        var headers = { 'Content-Type': 'application/json' };

        if (releasePayment === true)
            headers["X-Impound-Release-Payment"] = 'true';

        fetch('/api/integration/square/terminalCheckout',
            {
                headers: headers,
                method: "POST",
                body: JSON.stringify(data)
            })
            .then(r => {
                if (r.ok)
                    return r.json();
                else
                    throw new Error(`Error ${r.status}`);
            })
            .then(r => {
                swal.closeModal();
                swal({ title: 'Checkout request sent!', text: 'The checkout request has been sent to terminal successfully.', type: 'success' });

                deferred.resolve(r);
            })
            .catch(error => {
                swal.closeModal();
                console.log(error)
                swal("Couldn't send the payment to the terminal, please try again.")

                deferred.reject(error);
            });

        return deferred.promise();
    }

    function cancelSquareTerminalCheckout(checkoutId) {
        var deferred = new $.Deferred();

        var data = {
            checkoutId,
        };

        console.log('Sending the checkout cancel request to the terminal...', data);

        fetch('/api/integration/square/terminalCheckout?cancel=true',
            {
                headers: { 'Content-Type': 'application/json' },
                method: "POST",
                body: JSON.stringify(data)
            })
            .then(r => {
            if (r.ok)
                return r.json();
            else
                throw new Error(`Error ${r.status}`);
            })
            .then(r => {
                swal({ title: 'Terminal canceled', text: 'The checkout request has been canceled successfully.', type: 'success' });
                deferred.resolve(r);
            })
            .catch(error => {
                console.log(error)
                swal("Couldn't cancle the terminal checkout process.  Please check the terminal to cancel the checkout manually.");
                deferred.reject(error);
            });

        return deferred.promise();
    }

    function undoRelease(id, event) {
        event.preventDefault();
        event.stopPropagation();

        swal({
            title: 'Confirm',
            text: 'Are you sure you want to unrelease this vehicle?<br/><br/>' +
                'You should not do this if the vehicle has been released from your storage facility.<br/><br/>' +
                'Payments applied when the vehicle was released will be voided.',
            type: 'info',
            allowOutsideClick: false,
            closeOnEsc: false,
            showConfirmButton: true,
            showCancelButton: true,
            cancelButtonText: "Cancel",
            cancelButtonColor: "#DD6B55"
        }).then(r => {

            swal({
                title: 'Please Wait',
                text: 'Unreleasing the vehicle...',
                allowOutsideClick: false,
                closeOnEsc: false,
                onOpen: function () {
                    swal.showLoading()
                }
            });

            $.when(unrelease(id))
                .done(function () {
                    swal({
                        title: 'Success',
                        text: 'The vehicle was unreleased. Reloading data...please wait.',
                        type: 'info',
                        showConfirmButton: false,
                        closeOnEsc: false,
                    });
                })
                .fail(function (xhr, status, error) {
                    impounds.handleError(xhr, status, error);
                });
        });
    }

    function unrelease(id) {
        var deferred = new $.Deferred();

        $.post("/api/impounds/" + id + "/unrelease")
            .done(r => {
                deferred.resolve(r);
            })
            .fail(function(xhr, status, error) {
                console.log(xhr, status, error);
                deferred.reject(xhr, status, error);
            });

        return deferred.promise();
    }

    var PopupWindow = function (config) {
        this.config = {
            width: $(window).width() > 1200 ? 1000 : w = '80%',
            height: ($(window).height() / 100) * 80,
            modal: true,
            title: '',
            standalone: false,
            url: null,
        };
        this.popup = null;
        this.isEditing = false;

        this.constructor = function () {
            this.config = $.extend(this.config, config);

            this.popup = new Popup({
                title: this.config.title,
                width: this.config.width,
                height: this.config.height,
                modal: this.config.modal,
                impoundId: this.config.impoundId,
                padding: '0px 10px 30px 10px',
                onClose: function () {
                    try {
                        delete window.checkForAjaxChanges;
                        delete window.onbeforeunload;
                    } catch (e) { }
                },
                footer: '<div id="panel-footer"></div>'
            })
        };

        this.show = function () {
            var self = this;
            this.isEditing = true;
            this.popup.show();
            this.showSpinner();

            clearDetailPanel = function (message, data, srcObj) {
                self.isEditing = false;

                self.popup.close();
                $("#tiptip_holder").fadeOut(500);
            }
        }

        this.showSpinner = function () {
            this.popup.setContent('<img src="/UI/images/ajax-loading2.gif" width="36" height="36" style="position:absolute;left:49%;top:49%"/>');
        }

        this.constructor();
    };
    let loadAjaxCurrentImpoundId = 0;
    let loadAjaxCurrentCallId = 0;

    function loadAjaxPopup(impoundId, callId, event) {
        const url = event.currentTarget.getAttribute('href');
        const title = event.currentTarget.getAttribute('title') || "";

        loadAjaxCurrentImpoundId = impoundId;
        loadAjaxCurrentCallId = callId;

        loadAjaxByUrl(url, title);
    }

    function loadAjaxByUrl(url, title) {
        console.log("loading ajax url", url, title);

        popup = new PopupWindow({
            title: title,
            impoundId: loadAjaxCurrentImpoundId,
            callId: loadAjaxCurrentCallId,
        });

        $.ajax({
            url: url,
            type: "GET"
        }).done(function (data) {
            if (data && data.length > 0) {
                popup.popup.setContent(data);
                if ($('#cancelBtn').length > 0) {
                    $('#cancelBtn').off('click').on('click', function () { popup.popup.close(); });
                }
            }
        }).fail(function (xhr, status, error) {
            handleError(xhr, status, error);
        });

        popup.show();

        event.preventDefault();
        event.stopPropagation();
    };

    window.sendToSquareTerminal = sendToSquareTerminal;

    function deleteFile(fileId) {
        if (confirm("Are you sure you want to permanently delete this file?")) {
            $.ajax({
                url: '/api/files/' + fileId + '/delete',
                type: 'DELETE',
            }).done(function (data) {
                location.reload(false);
            }).error(function (xhr, status, error) {
                alert("error: " + status + ", " + error);
            });
        }
    }
</script>
}

@section Toolbar {

@if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.AccountUser && WebGlobal.CurrentUser.CompanyId != 32564 &&
        WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.AccountUser && WebGlobal.CurrentUser.CompanyId != 49039)
{
    @if (WebGlobal.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.Impounds_OriginalImpoundEditor))
    {
        <li><a asp-page="/Impounds/Editor" asp-route-id="@Model.Id.ToString()"><span style="background-image: url(/ui/images/toolbaricons/newimpound.png)"></span>Modify Impound</a></li>
    }
    else
    {
        <li><a href="#" onclick="impoundEditor.show(@Model.DE.Id.ToString())" id="<EMAIL>()"><span style="background-image: url(/ui/images/toolbaricons/newimpound.png)"></span>Modify Impound</a></li>
    }
    if (Model.ShowStatus)
    {
        <li><a asp-page="/Impounds/ImpoundStatusUpdate" asp-route-id="@Model.Id.ToString()" rel="towbook-dialog">Update Status</a></li>
    }
    <li><a href="/ajax/dispatch/@Model.Impound.DispatchEntry.Id/payments" rel="towbook-dialog" data-dialog-height="650">Record Payment</a></li>

    @if (Model.Impound.Lot == null || Model.Impound.Lot.AccountId == 1 || WebGlobal.CurrentUser.Company.Id == 2045 || WebGlobal.CurrentUser.Company.Id == 3020)
    {
        @if (Model.Impound.ReleaseDate == null)
        {
            @if (Model.Impound.Hold == true)
            {
                @if (WebGlobal.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Manager &&
                        Model.Impound.DispatchEntry.GetAttribute(73) == null)
                {
                    <!-- Managers only:  Ask the user if they wish to remove the police hold and release the vehicle -->
                    <li><a asp-page="/Impounds/Release" asp-route-id="@Model.Id" asp-route-action="removeHold" class="x-popup x-release-vehicle" data-title="Release Vehicle" data-height="620"
                        onclick="return confirm('This vehicle has a Police Hold.\n\nAre you sure you want to remove the Police Hold and release the vehicle?')">Release Vehicle</a></li>
                }
                else
                {
                    <li><a href="javascript:;" onclick="alert('@Model.PoliceHoldMsg'); return false;">Release Vehicle</a></li>
                }
            }
            else
            {
                <li><a asp-page="/Impounds/Release" asp-route-id="@Model.Id" class="x-popup x-release-vehicle" data-title="Release Vehicle" data-height="620">Release Vehicle</a></li>
            }
        }
        else
        {
            if (Model.Impound.ReleasePickupDate == null)
            {
                <li><a asp-page="/Impounds/ReleasePickedUp" asp-route-id="@Model.Id.ToString()" rel="towbook-dialog">Mark Impound as being picked up</a></li>
            }

            bool disableUnrelease = false;
            var kv = Extric.Towbook.Integration.CompanyKeyValue.GetFirstValueOrNull(WebGlobal.CurrentUser.Company.Id, Extric.Towbook.Integration.Provider.Towbook.ProviderId, "PreventDispatchersFromUnreleasing");
            if (kv == '1'.ToString() && (WebGlobal.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Dispatcher || WebGlobal.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Driver))
                disableUnrelease = true;

            if(Model.Impound.DispatchEntry.Attributes.ContainsKey(Extric.Towbook.Dispatch.AttributeValue.BUILTIN_TOWOUT_CALL))
                disableUnrelease = true;

            if (!disableUnrelease)
            {
                <li><a href="#" onclick="undoRelease(@Model.Id, event); return false;">Undo Release</a></li>
            }
        }
    }
}

<script type="module" defer src="/dist/auction-dialog.js?_=@Model.CacheBreakerKey"></script>
@if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.AccountUser && WebGlobal.CurrentUser.CompanyId != 32564)
{
    @if (Model.Impound.ReleaseDate == null)
    {
        @if (Model.Impound.Auction)
        {
            @if(Model.Impound.Company.HasFeature(Extric.Towbook.Generated.Features.Impounds_ImpoundAuctions))
            {
                <li><div id="auction-dialog"></div></li>
            }
            else
            {
                <li><a href="#" onclick="setAuction(@Model.Id.ToString(), false)">Remove from Auction</a></li>
            }
        }
        else
        {
            @if(Model.Impound.Company.HasFeature(Extric.Towbook.Generated.Features.Impounds_ImpoundAuctions))
            {
                <li><div id="auction-dialog"></div></li>
            }
            else
            {
                <li><a href="#" onclick="setAuction(@Model.Id.ToString(), true)">Auction</a></li>
            }
        }
    }

    @if (WebGlobal.CurrentUser.CompanyId == 10000 || WebGlobal.CurrentUser.CompanyId == 7124 || WebGlobal.CurrentUser.CompanyId == 66171)
    {
        <li><a href="#" onclick="impoundEditor.show(@Model.DE.Id.ToString(), 'auction')"><span style="background-image: url(/ui/images/toolbaricons/newimpound.png)"></span>Create Vehicle Sale Invoice</a></li>
    }
}

@if (Model.AllowTowOutOptions())
{
    if(!Model.Impound.DispatchEntry.Attributes.ContainsKey(Extric.Towbook.Dispatch.AttributeValue.BUILTIN_TOWOUT_CALL) && !Model.Impound.DispatchEntry.Released)
    {
        <li><a href="#" onclick="towOutFromStorage(@Model.DE.Id, event); return false;">Tow from Storage</a></li>
    }
    else if (Model.Impound.DispatchEntry.Attributes.ContainsKey(Extric.Towbook.Dispatch.AttributeValue.BUILTIN_TOWOUT_CALL) && Model.Impound.DispatchEntry.Released)
    {
        <li><a href="#" onclick="undoTowOutFromStorage(@Model.DE.Id, event); return false;">Undo Tow Out</a></li>
    }
}

@if (WebGlobal.CurrentUser.CompanyId != 32564)
{
    <li><a asp-page="/Impounds/PhotoUpload" asp-route-id="@Model.Id.ToString()" rel="towbook-dialog">Upload Photos</a></li>
    <li><a asp-page="/Impounds/PhotoUpload" asp-route-id="@Model.Id.ToString()" asp-route-files="1" rel="towbook-dialog">Upload Files</a></li>
    <li><a asp-page="/Impounds/Note" asp-route-id="@Model.Id.ToString()" rel="towbook-dialog">Add Note</a></li>

    @if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.AccountUser || WebGlobal.CurrentUser.Company.Id == 112)
    {
        <li><a href="#" onclick="loadAjaxPopup(@Model.Id, @Model.Impound.DispatchEntry.Id, event); return false;" title="Email Invoice">Email Invoice/Photos</a></li>
        <li><a id="x-impound-print" asp-page="/Impounds/ImpoundPrint" asp-route-id="@Model.Id.ToString()" asp-route-type="invoice" rel="towbook-dialog">Print Invoice</a></li>
        @if (Model.Impound.Lot == null || Model.Impound.Lot.AccountId == 1)
        {
            <li><a asp-page="/Impounds/ImpoundSummaryOfCharges" asp-route-id="@Model.Id.ToString()" asp-route-type="invoice" rel="towbook-dialog">Print Summary</a></li>
        }
    }

    @if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.AccountUser)
    {
        @if (WebGlobal.CurrentUser.Company.State == "CA" || true)
        {
            <li><a asp-page="/Impounds/PropertyRelease" asp-route-id="@Model.Id.ToString()" asp-route-type="invoice" target="_blank">Print Property Release</a></li>
        }
    }

    @if(WebGlobal.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.PeddleIntegration) &&
        (WebGlobal.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Manager || WebGlobal.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Accountant))
    {
        <script type="module" defer src="/dist/peddle.js?_=@Model.CacheBreakerKey"></script>
        <div id="peddle-button"></div>
    }

    @if (WebGlobal.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Manager && !Model.IsWithinClosedPeriod)
    {
        <li style="float: right"><a asp-page="/Impounds/ImpoundDelete" asp-route-id="@Model.Id.ToString()" rel="towbook-dialog">Delete</a></li>
    }
}
}

