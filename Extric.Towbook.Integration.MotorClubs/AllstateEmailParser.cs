using Extric.Towbook.Dispatch;
using HtmlAgilityPack;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.Integration.MotorClubs
{
    public class AllstateEmailParser: IMotorClubRequestWithGpsCoordinates
    {
        public string RequestId { get; set; }

        public DateTime? RequestDate { get; set; }
        public string PurchaseOrderNumber { get; set; }

        public string BenefitAmount { get; set; }
        public string CustomerId { get; set; }
        public string CustomerName { get; set; }
        public string CustomerPhone { get; set; }
        public string PickupAddress { get; set; }
        public decimal? PickupLatitude { get; set; }
        public decimal? PickupLongitude { get; set; }
        public string PickupCrossStreet { get; set; }
        public string PickupType { get; set; }
        

        public string DestinationAddress { get; set; }
        public decimal? DestinationLatitude { get; set; }
        public decimal? DestinationLongitude { get; set;  }
        public string DestinationName { get; set; }
        public string DestinationCrossStreet { get; set; }
        public string ETA { get; set; }
        public decimal PayoutFees { get; set; }
        public string ServiceNeeded { get; set; }
        public string TowingLocationId { get; set; }
        public string TowingProviderId { get; set; }
        public string VehicleClass { get; set; }
        public string VehicleColor { get; set; }
        public string VehicleLicense { get; set; }
        public string VehicleLicenseState { get; set; }
        public string VehicleMake { get; set; }
        public string VehicleModel { get; set; }
        public string VehicleNotes { get; set; }
        public string VehicleType { get; set; }
        public string VehicleVIN { get; set; }
        public string VehicleYear { get; set; }
        public string CustomerAddress { get; set; }
        public string CustomerCity { get; set; }
        public string CustomerState { get; set; }
        public string CustomerZip { get; set; }

        public System.Threading.Tasks.Task PostSave(Entry entry)
        {
            return System.Threading.Tasks.Task.CompletedTask;
        }

        public static AllstateEmailParser FromFile(string fileName)
        {
            return AllstateEmailParser.FromText(File.ReadAllText(fileName));
        }

        public static AllstateEmailParser FromText(string content)
        {

            AllstateEmailParser allstate = new AllstateEmailParser();
            allstate.ContentBody = content.Replace("\r\n\r\n", "\r\n").Replace("\n\n", "\n");

            //System.IO.File.WriteAllText(System.IO.Path.GetTempPath() + "\\Allstate_" + DateTime.Now.ToFileTime() + "_" +Guid.NewGuid().ToString("N"), content);

            int lastIdx = 0;
            string towingProviderId = null;
            string serviceDate = null;
            string eta = null;
            string service = null;
            string year = null;
            string make = null;
            string model = null;
            string color = null;
            string vin = null;
            string odometer = null;
            string pickupType = null;
            string pickupCrossStreet = null;
            string pickupAddress = null;
            string pickupLat = null;
            string pickupLong = null;
            string destinationName = null;
            string destinationCrossStreet = null;
            string destinationAddress = null;
            string destinationLat = null;
            string destinationLong = null;
            string customerName = null;
            string customerPhone = null;
            string requestId = null;
            string coverageAmount = null;
            string notes = null;
            string businessName = null;
            string businessAddress = null;

            lastIdx = allstate.ExtractText("Service Date", lastIdx, ref serviceDate, ':', 1, true);
            lastIdx = allstate.ExtractText("Provider ID", lastIdx, ref towingProviderId);
            lastIdx = allstate.ExtractText("Business Name", lastIdx, ref businessName);
            lastIdx = allstate.ExtractText("Address", lastIdx, ref businessAddress, ':', 2, false, '\n', true);

            lastIdx = allstate.ExtractText("Provider ETA", lastIdx, ref eta, ' ');
            lastIdx = allstate.ExtractText("Service(s)", lastIdx, ref service);

            if (allstate.ContentBody.Contains("YEAR/MAKE/MODEL:"))
            {
                string vehicleInfo = string.Empty;
                lastIdx = allstate.ExtractText("YEAR/MAKE/MODEL", lastIdx, ref vehicleInfo);

                string[] parts = vehicleInfo.Split(' ');
                year = parts[0];
                make = parts[1];
                model = parts[2];

                lastIdx = allstate.ExtractText("COLOR", lastIdx, ref color);
            }
            else
            {
                lastIdx = allstate.ExtractText("Year", lastIdx, ref year);
                lastIdx = allstate.ExtractText("Make", lastIdx, ref make);
                lastIdx = allstate.ExtractText("Model", lastIdx, ref model);
                lastIdx = allstate.ExtractText("Color", lastIdx, ref color);
            }

            if (string.IsNullOrWhiteSpace(make) && allstate.ContentBody.Contains("VEHICLE:"))
            {
                var lines = allstate.ContentBody.Substring(allstate.ContentBody.IndexOf("VEHICLE:")).Split('\n');
                
                for(int i = 0; i < lines.Length; i++)
                {
                    if (lines[i].Trim() == "VEHICLE:")
                    {
                        var possibleVehicle = lines[i + 1].Replace("  ", " ");
                        if (possibleVehicle.Contains(" "))
                        {
                            string[] parts = possibleVehicle.Split(' ');
                            year = parts[0].Trim();
                            make = parts[1].Trim();
                            model = parts[2].Trim();
                        }
                    }
                }
            }

            lastIdx = allstate.ExtractText("Odometer", lastIdx, ref odometer);
            lastIdx = allstate.ExtractText("VIN", lastIdx, ref vin);
            lastIdx = allstate.ExtractText("Disabled Location", lastIdx, ref pickupType);
            int addressIndex = 0;
            lastIdx = allstate.ExtractText("Cross Street", lastIdx, ref pickupCrossStreet);
            addressIndex = lastIdx = allstate.ExtractText("Address", lastIdx, ref pickupAddress, ':', 2, dontStartBack:true);
            if (string.IsNullOrWhiteSpace(pickupAddress))
            {
                addressIndex = lastIdx = allstate.ExtractText("Business Name", lastIdx, ref pickupAddress, ':', 2, dontStartBack: true);
            }
            lastIdx = allstate.ExtractText("La", lastIdx, ref pickupLat, ':', 1, false, ',', dontStartBack:true);
            lastIdx = allstate.ExtractText("Lo", lastIdx, ref pickupLong, ':', 1, dontStartBack:true);
            lastIdx = allstate.ExtractText("Cross Street", lastIdx, ref destinationCrossStreet, dontStartBack: true);
            lastIdx = allstate.ExtractText("Business Name", lastIdx, ref destinationName, dontStartBack: true);
            lastIdx = allstate.ExtractText("Address", addressIndex, ref destinationAddress, ':', 2, dontStartBack: true);
            lastIdx = allstate.ExtractText("La", lastIdx, ref destinationLat, ':', 1, false, ',', dontStartBack: true);
            lastIdx = allstate.ExtractText("Lo", lastIdx, ref destinationLong, ':', 1, false, '\n', dontStartBack: true);
            lastIdx = allstate.ExtractText("Customer", lastIdx, ref customerName);
            lastIdx = allstate.ExtractText("Callback #", lastIdx, ref customerPhone, ' ');

            if (destinationCrossStreet?.Trim() == destinationAddress?.Trim())
                destinationCrossStreet = null;

            if (string.IsNullOrWhiteSpace(customerPhone))
                lastIdx = allstate.ExtractText("Customer Phone", lastIdx, ref customerPhone, ' ');

            lastIdx = allstate.ExtractText("Auth #", lastIdx, ref requestId, ' ');

            const string key = "Notes for Provider:";

            var notesKeyIndex = allstate.ContentBody.IndexOf(key);
            if (notesKeyIndex != -1)
            {
                int startIndex = notesKeyIndex + key.Length;


                int notesEndIndex = allstate.ContentBody.IndexOf("**** IN ORDER");
                int notesEndAltIndex = allstate.ContentBody.IndexOf("Only for Non");

                if (notesEndIndex == -1) notesEndIndex = allstate.ContentBody.IndexOf("****In");

                if (!(notesEndAltIndex == -1 && notesEndIndex == -1))
                {
                    notes = allstate.ContentBody.Substring(startIndex,
                        (notesEndIndex == -1 ? notesEndAltIndex : notesEndIndex) - startIndex).Trim();

                    // find duplicates - allstate tends to write the notes out twice.. dont know why.
                    string[] noteLines = notes.Split('\n');
                    if (noteLines.Length == 2)
                    {
                        if (noteLines[0].Trim() == noteLines[1].Trim())
                            notes = noteLines[0];
                    }

                    allstate.VehicleNotes = notes.Replace("\r", "");

                }
            }

            lastIdx = allstate.ExtractText("Benefit Limit", 0, ref coverageAmount);

            if (coverageAmount != " -  Miles")
                allstate.BenefitAmount = coverageAmount;

            towingProviderId = towingProviderId.Trim();
            if (towingProviderId.Contains(" "))
                towingProviderId = towingProviderId.Split(' ')[0];


            if (string.IsNullOrWhiteSpace(towingProviderId))
                towingProviderId = businessAddress;
            

            allstate.TowingProviderId = towingProviderId;


            allstate.RequestDate = MotorClubMapper.ParseDate(serviceDate);
            if (allstate.RequestDate == DateTime.MinValue)
                allstate.RequestDate = DateTime.Now;
            else
            {
                if ((serviceDate.Contains("CST") || 
                    serviceDate.Contains("EST") ||
                    serviceDate.Contains("PST")) && allstate.RequestDate.Value.IsDaylightSavingTime())
                {
                    allstate.RequestDate = allstate.RequestDate.Value.AddHours(1);
                }
            }
            
            allstate.ExtractText("Provider Arrive By", 0, ref eta, ':', 1, true);

            allstate.ETA = eta;
            allstate.ServiceNeeded = service;
            allstate.VehicleYear = year;
            allstate.VehicleMake = make;
            allstate.VehicleModel = model;


            if (!string.IsNullOrWhiteSpace(allstate.VehicleYear) && 
                !string.IsNullOrWhiteSpace(allstate.VehicleModel) &&
                allstate.VehicleModel.StartsWith(allstate.VehicleYear))
            {
                allstate.VehicleModel = allstate.VehicleModel.Substring(allstate.VehicleYear.Length).Trim();
            }

            allstate.VehicleColor = color;
            allstate.VehicleVIN = vin;
            allstate.PickupType = pickupType;
            allstate.PickupCrossStreet = pickupCrossStreet != "None Found" ? pickupCrossStreet : "";
            allstate.PickupAddress = pickupAddress;
            if (!string.IsNullOrEmpty(pickupLat))
            {

                if (pickupLat.Contains(","))
                    pickupLat = pickupLat.Substring(0, pickupLat.IndexOf(','));

                if (pickupLong.Contains(","))
                    pickupLong = pickupLong.Substring(0, pickupLong.IndexOf(','));


                decimal temp = 0;
                if (decimal.TryParse(pickupLat, out temp))
                    allstate.PickupLatitude = temp;

                if (decimal.TryParse(pickupLong, out temp))
                    allstate.PickupLongitude = temp;
            }

            allstate.DestinationName = destinationName;
            allstate.DestinationCrossStreet = destinationCrossStreet != "None Found" ? destinationCrossStreet : "";
            allstate.DestinationAddress = Core.StripLastFourFromNineDigitZipInAddress(destinationAddress);

            if (!string.IsNullOrEmpty(destinationLat))
            {
                if (destinationLat.Contains(","))
                    destinationLat = destinationLat.Substring(0, destinationLat.IndexOf(','));


                decimal temp = 0;
                if (decimal.TryParse(destinationLat, out temp))
                    allstate.DestinationLatitude = temp;

                if (destinationLong.Contains(","))
                    destinationLong = destinationLong.Substring(0, destinationLong.IndexOf(','));

                if (decimal.TryParse(destinationLong, out temp))
                    allstate.DestinationLongitude = temp;
            }
            allstate.CustomerName = Core.FormatName(customerName);
            allstate.CustomerPhone = Core.FormatPhoneWithDashesOnly(customerPhone);
            allstate.PurchaseOrderNumber = requestId;
            
            return allstate;
        }

        public string ContentBody { get; set; }

        private int ExtractText(string key, int startIndex, ref string output, char separator = ':', int rows = 1, bool isDate = false, char endWith = '\n', bool dontStartBack = false)
        {
            int keyIdx = this.ContentBody.IndexOf(key, startIndex);
            
            if (keyIdx == -1 && startIndex != 0)
            {
                if (dontStartBack)
                {
                    output = "";
                    return startIndex;
                }
                return ExtractText(key, 0, ref output, separator, rows, isDate, endWith);
            }

            if (keyIdx == -1)
            {
                // couldn't find the key... don't return an exception - this will cause fields that are missing to cause the whole parser to crash.
                output = "";
                return startIndex;
            }

            
            int lineEndIdx = this.ContentBody.IndexOf(endWith, keyIdx);
            int count = 1;
            while (count < rows)
            {
                lineEndIdx = this.ContentBody.IndexOf(endWith, lineEndIdx + 1);
                count += 1;
            }

            string[] parts = this.ContentBody.Substring(keyIdx + key.Length, lineEndIdx - (keyIdx + key.Length)).Split(separator);
            if (parts.Length == 1)
            {
                output = parts[0].Replace("\r", "");
                return lineEndIdx;
            }


            if (!isDate)
            {
                if (parts[1].Contains("\r\n"))
                {
                    output = parts[1].Substring(0, parts[1].LastIndexOf("\r")).Replace("\r", "").Trim();
                    
                    // trim back the line ending to only include up until where we are returning.
                    lineEndIdx = lineEndIdx - String.Join("", parts).Length + output.Length;
                }
                else
                    output = parts[1].Replace("\r", "").Trim();
            }
            else
            {
                if (parts.Length > 2)
                {
                    if (parts[1].Contains(","))
                        parts[1] = parts[1].Substring(parts[1].IndexOf(',') + 1);
                    output = string.Format("{0}:{1}:{2}", parts[1], parts[2], parts[3]).Replace("\r", "").Trim();
                }
                else
                    output = "";
            }

            if (output.Contains("\n"))
            {
                output = String.Join(", ", output.Split('\n').Select<string, string>(o => o.Trim()).ToArray());
            }

            output = output.Replace("<br>", "").Replace("<br />", "");
            return lineEndIdx;
        }
    }
}
