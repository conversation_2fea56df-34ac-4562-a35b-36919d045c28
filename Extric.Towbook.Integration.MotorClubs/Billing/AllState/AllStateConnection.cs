using Extric.Towbook.Utility;
using HtmlAgilityPack;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using NLog;
using System.Threading;
using System.Diagnostics;
using System.Text;

namespace Extric.Towbook.Integration.MotorClubs.Billing.Allstate
{
    public class AllStateConnection : MotorClubConnection
    {
        public string ProviderId { get; set; }
        private string webclaimsUrl = "";
        private ConnectionKeyModel _connKey;
        private const int maxSessionsPerLogin = 4;
        private static readonly Guid machineId = Guid.NewGuid();

        protected override string LoginUrl
        {
            get
            {
                return "https://reinventing-roadside.allstate.com/rr-prd/rspweb/common/login.do";
            }
            set
            {
                base.LoginUrl = value;
            }
        }

        private AllStateConnection(string username, string password, ConnectionKeyModel connKey)
            : base(username, password)
        {
            _connKey = connKey;
        }

        private static ConcurrentDictionary<string, AllStateConnection> connections
                 = new ConcurrentDictionary<string, AllStateConnection>();

        private static ConcurrentDictionary<string, ExpiringPurchaseOrderList> availablePOs
                 = new ConcurrentDictionary<string, ExpiringPurchaseOrderList>();
        
        public class AllstateProvider
        {
            public string Id { get; set; }
            public string Name { get; set; }
            public int UserId { get; set; }
            public Spa Spa { get; set; }
            public object DefaultLocation { get; set; }
        }

        public class Spa
        {
            public string ParentProviderID { get; set; }
            public string ProviderID { get; set; }
            public Address Address { get; set; }
            public string ContactName { get; set; }
            public string CompanyName { get; set; }
            public string Phone { get; set; }
            public object SecondPhone { get; set; }
            public object Fax { get; set; }
            public string Email { get; set; }
            public string SpaStatus { get; set; }
            public string SpaProvdrNpmName { get; set; }
            public string SpaProvdrNpmEmail { get; set; }
            public string SpaReportInd { get; set; }
            public int SpaMasterStatusId { get; set; }
            public string SpaLastUpdatedBy { get; set; }
            public string SpaCreatedBy { get; set; }
            public long SpaInterfaceDate { get; set; }
        }

        public class Address
        {
            public float Lati { get; set; }
            public float Longi { get; set; }
            public string Addr1 { get; set; }
            public string Addr2 { get; set; }
            public string City { get; set; }
            public string State { get; set; }
            public string ZipCode { get; set; }
            public string Country { get; set; }
            public string ConfidenceCode { get; set; }
            public string ZipCodeLong { get; set; }
            public string CrossStreet { get; set; }
        }



        /// <summary>
        /// Login to Allstate
        /// </summary>
        /// <param name="username"></param>
        /// <param name="password"></param>
        /// <param name="testMode"></param>
        /// <returns></returns>
        public static AllStateConnection Login(LogInfo logInfo, string username, string password, string providerId)
        {
            if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(password))
                throw new InvalidLoginException(username, "username or password is empty");

            var connKey = GetConnectionKey(logInfo, username, password, providerId);
            var conn = new AllStateConnection(username, password, connKey);

            try
            {
                var cacheKey = connKey.ToString();

                if (connections.TryGetValue(cacheKey, out conn))
                {
                    if (conn.Expiry > DateTime.Now)
                    {
                        conn._connKey = connKey;
                        return conn;
                    }
                    else
                        connections.TryRemove(cacheKey, out conn);
                }

                var tries = 0;
                do
                {
                    try
                    {
                        tries++;

                        conn = new AllStateConnection(username, password, connKey);

                        logInfo.AddEvent($"Logging into Allstate, connKey: {connKey}", LogLevel.Info);

                        // [call login.do]
                        var html = WebRequestHelper.GetHtml(conn.LoginUrl, null, ref conn.cookieContainer);

                        var loginForm = GetFormFields(html);

                        loginForm["destination"] = "landing";
                        loginForm["partnerName"] = "%2Frr-prd!viewName=common/login";
                        loginForm["username"] = username;
                        loginForm["password"] = password;

                        var pkms = new Dictionary<string, string>()
                        {
                            ["username"] = username,
                            ["password"] = password,
                            ["login"] = "LOGIN",
                            ["login-form-type"] = "pwd"
                        };

                        html = WebRequestHelper.GetHtml("https://reinventing-roadside.allstate.com/pkmslogin.form", pkms, ref conn.cookieContainer);
                        if (!html.DocumentNode.InnerHtml.Contains("Your login was successful."))
                            throw new InvalidLoginException(username, "Invalid Username or Password");

                        // [call login-new.do]
                        var log = new AutomatedHttpLog();
                        html = WebRequestHelper.GetHtml("https://reinventing-roadside.allstate.com/rr-prd/rspweb/common/login-new.do", loginForm, ref conn.cookieContainer, ref log, false, false, true);

                        var resultContents = html.DocumentNode.InnerHtml;

                        if (string.IsNullOrWhiteSpace(resultContents))
                            throw new InvalidLoginException();

                        var loginResultData = JsonConvert.DeserializeObject<LoginResult>(resultContents);

                        if (!string.IsNullOrWhiteSpace(loginResultData.Error))
                            throw new InvalidLoginException(username, loginResultData.Error);

                        // Set cookies
                        conn.cookieContainer.Add(conn.CreateCookie("IV_JCT", "%2Ftowingnetwork_PROD", "/", "www.arsnetwork.allstate.com", true));
                        if (loginResultData.Cookie?.Name != null)
                            conn.cookieContainer.Add(conn.CreateCookie(
                                loginResultData.Cookie.Name,
                                loginResultData.Cookie.Value,
                                "/",
                                "allstate.com" //cookieParam.domain
                            ));
                        if (loginResultData.TamCookies != null)
                        foreach (var cookieParam in loginResultData.TamCookies)
                        {
                            conn.cookieContainer.Add(conn.CreateCookie(
                                cookieParam.Name,
                                cookieParam.Value,
                                cookieParam.Path,
                                "reinventing-roadside.allstate.com" //cookieParam.domain
                            ));
                        }

                        // [call gateway/]
                        html = WebRequestHelper.GetHtml("https://reinventing-roadside.allstate.com/rr-prd" + loginResultData.Redirect, null, ref conn.cookieContainer);
                        if (!html.DocumentNode.InnerText.Contains("Log Out"))
                            throw new Exception("Login failed");

                        // [call spa.do]
                        html = WebRequestHelper.GetHtml("https://reinventing-roadside.allstate.com/rr-prd/rspweb/gateway/spa.do", null, ref conn.cookieContainer);

                        // [call userAccessMatrix.do]
                        html = WebRequestHelper.GetHtml("https://reinventing-roadside.allstate.com/rr-prd/rspweb/gateway/userAccessMatrix.do", null, ref conn.cookieContainer);

                        // [call webClaimsUrl.do]
                        html = WebRequestHelper.GetHtml("https://reinventing-roadside.allstate.com/rr-prd/rspweb/gateway/webClaimsUrl.do", null, ref conn.cookieContainer);
                        dynamic res = JsonConvert.DeserializeObject<dynamic>(html.DocumentNode.InnerText);
                        conn.webclaimsUrl = res["webclaimsUrl"];

                        // [call providerId.do]
                        html = WebRequestHelper.GetHtml("https://reinventing-roadside.allstate.com/rr-prd/rspweb/gateway/providerId.do", null, ref conn.cookieContainer);
                        res = JsonConvert.DeserializeObject<dynamic>(html.DocumentNode.InnerText);
                        conn.ProviderId = res["providerId"];

                        // [call providers.do]
                        html = WebRequestHelper.GetHtml("https://reinventing-roadside.allstate.com/rr-prd/rspweb/gateway/providers.do", null, ref conn.cookieContainer);
                        var providersJson = JsonConvert.DeserializeObject<AllstateProvider[]>(html.DocumentNode.InnerText);

                        // [call isUserLoggedIn.do]
                        html = WebRequestHelper.GetHtml("https://reinventing-roadside.allstate.com/rr-prd/rspweb/common/isUserLoggedIn.do", new Dictionary<string, string>(), ref conn.cookieContainer, ref log);
                        if (JsonConvert.DeserializeObject<dynamic>(html.DocumentNode.InnerText).result != "true")
                            throw new InvalidLoginException(username, html.DocumentNode.InnerText);
                        

                        // [call ssoLogin.do]
                        html = WebRequestHelper.GetHtml(conn.webclaimsUrl, null, ref conn.cookieContainer);

                        // If the initial provider id (when we login) is not the same as the providerId we want
                        if (providerId != conn.ProviderId)
                        {
                            // Get the list of available providers
                            var providers = conn.GetProviders(logInfo);

                            // If the providerId we want is in that list
                            if (providers.Contains(providerId))
                            {
                                // Select it
                                var url = "https://www.arsnetwork.allstate.com/towingnetwork_PROD/ARSWebClaim/provider/SetProvider.action";
                                var formData = new Dictionary<string, string> { { "selectedProviderId", providerId } };
                                conn.Post(logInfo, url, ref formData);
                                conn.ProviderId = providerId;
                            }
                        }

                        connections.TryAdd(cacheKey, conn);

                        return conn;
                    }
                    catch (InvalidLoginException)
                    {
                        throw;
                    }
                    catch (Exception e)
                    {
                        logInfo.AddEvent($"Retrying login, attempt #{tries} failed because of: {e.Message}", LogLevel.Warn);
                    }
                }
                while (tries < 4);

                throw new Exception("Error while attempting to connect to Allstate.  Please try resubmitting the invoice.");
            }
            catch
            {
                conn.ReleaseLock(logInfo);
                throw;
            }
        }

        private List<Dictionary<string, string>> ParseTable(HtmlNode table)
        {
            var rows = table.SelectNodes(".//tr");
            var data = new List<Dictionary<string, string>>();

            if (rows != null)
            {
                var headers = rows[0].SelectNodes(".//th|.//td").Select(td => td.InnerText.Trim()).ToArray();

                for (var i = 1; i < rows.Count; i++)
                {
                    var row = rows[i];
                    var rowData = new Dictionary<string, string>();

                    string GetDispatchIdFromLink(string html)
                    {
                        // optionally, find the dispatchId in the href link of the first cell
                        var qs = row.SelectSingleNode(".//td/a")?.Attributes["href"]?.Value ?? string.Empty;

                        // Define the regex pattern to capture the dispatchId
                        string pattern = @"dispatchId=(\d+)";

                        // Apply the regex to find the dispatchId
                        Match match = Regex.Match(html, pattern);

                        // Return the captured group (dispatchId) if found
                        return match.Success ? match.Groups[1].Value : string.Empty;
                    }
                    
                    var dispatchId = GetDispatchIdFromLink(row?.InnerHtml ?? string.Empty);
                    if(!string.IsNullOrEmpty(dispatchId))
                        rowData["DispatchId"] = dispatchId;

                    for (var j = 0; j < headers.Length; j++)
                    {
                        var cellHtml = row.SelectNodes(".//td")[j];
                        if (cellHtml != null)
                        {
                            // always capture inside text of the cell
                            rowData[headers[j]] = cellHtml
                                .InnerText
                                .Replace("<br>", " ")
                                .Replace("<br />", " ")
                                .Replace("<br/>", " ")
                                .Trim();
                        }
                    }

                    data.Add(rowData);
                }
            }

            return data;
        }

        public List<PurchaseOrderItem> GetAvailablePOs(LogInfo logInfo)
        {
            ExpiringPurchaseOrderList pos = null;

            var cacheKey = _connKey.ToString();

            if (availablePOs.TryGetValue(cacheKey, out pos))
            {
                if (pos.Expiry > DateTime.Now)
                    return pos;
                else
                    availablePOs.TryRemove(cacheKey, out pos);
            }

            pos = new ExpiringPurchaseOrderList();

            // Load the "Open POs (Last 90 Days)" page
            var url = "https://www.arsnetwork.allstate.com/towingnetwork_PROD/ARSWebClaim/claims/OpenClaims.action";
            var html = Get(logInfo, url);
            var formData = GetFormFields(html);

            // Select 30 claims per page, and resubmit
            url = "https://www.arsnetwork.allstate.com/towingnetwork_PROD/ARSWebClaim/claims/DisplayClaimDateSearchPageAction.action";
            formData["searchPaginationModel.rowsPerPage"] = "30";
            html = Post(logInfo, url, ref formData);

            while (html != null)
            {
                var tableData = ParseTable(html.DocumentNode.SelectSingleNode("//table[@id='claimsList']"));

                if (tableData.Any())
                {
                    foreach (var row in tableData)
                    {
                        var po = new PurchaseOrderItem();
                        var dateFormat = new System.Globalization.CultureInfo("en-US", true);

                        if (row.ContainsKey("Authorization#") && row["Authorization#"].Trim().Length > 0)
                            po.PurchaseOrderNumber = row["Authorization#"].Trim();

                        if (row.ContainsKey("DispatchId"))
                            po.DispatchId = Convert.ToInt32(row["DispatchId"].Trim());

                        if (row.ContainsKey("Service Date") && row["Service Date"].Trim().Length > 0)
                            po.ServiceDate = Convert.ToDateTime(row["Service Date"].Trim(), dateFormat);

                        if (row.ContainsKey("Provider Invoice") && row["Provider Invoice"].Trim().Length > 0)
                            po.ProviderInvoice = row["Provider Invoice"].Trim();

                        if ( row.ContainsKey("Amount") && row["Amount"].Trim().Length > 0)
                            po.Amount = Convert.ToDecimal(row["Amount"].Replace("$", "").Trim());

                        if (row.ContainsKey("Status") && row["Status"].Trim().Length > 0)
                            po.Status = row["Status"].Trim();

                        if (row.ContainsKey("Submit Date") && row["Submit Date"].Trim().Length > 0)
                            po.SubmitDate = Convert.ToDateTime(row["Submit Date"].Trim(), dateFormat);

                        if (row.ContainsKey("Payment Date") && row["Payment Date"].Trim().Length > 0)
                            po.PaymentDate = Convert.ToDateTime(row["Payment Date"].Trim(), dateFormat);

                        if (row.ContainsKey("Check/EFT#") && row["Check/EFT#"].Trim().Length > 0)
                            po.CheckEFTNumber = row["Check/EFT#"].Trim();

                        pos.Add(po);
                    }
                }

                // If there is a nav row with cells
                var tds = html.DocumentNode.SelectNodes("//fieldset/table/tbody/tr/td");
                if (tds != null && tds.Count() > 3)
                {
                    // If the 4th cell has a 'Next' link
                    if (HtmlToText.GetString(tds[3]) == "Next")
                    {
                        // Increase the pageNumber and resubmit
                        formData["searchPaginationModel.pageNumber"] = (Convert.ToInt32(formData["searchPaginationModel.pageNumber"]) + 1).ToString();
                        html = Post(logInfo, url, ref formData);
                        continue;
                    }
                }

                html = null;
            }

            availablePOs.TryAdd(cacheKey, pos);

            return pos;
        }

        public PurchaseOrder SearchPurchaseOrder(LogInfo logInfo, string poNumber, out HtmlDocument html)
        {
            PurchaseOrder po = null;

            // Load the Search page
            var url = "https://www.arsnetwork.allstate.com/towingnetwork_PROD/ARSWebClaim/claims/DisplaySearchClaimsPage.action";
            html = Get(logInfo, url);

            // Fill it out
            var formData = GetFormFields(html);
            formData["expiredDays"] = "90";
            formData["searchCriteria"] = "auth";
            formData["authNumber"] = $"{poNumber}";
            formData["membership"] = "";
            formData["servDateMbr"] = "";
            formData["invceNumber"] = "";
            formData["servDateFrom"] = "";
            formData["servDateTo"] = "";
            formData["searchBtn.x"] = "52";
            formData["searchBtn.y"] = "11";
            formData["redirectPage"] = "search";

            // Set the url for search
            url = "https://www.arsnetwork.allstate.com/towingnetwork_PROD/ARSWebClaim/claims/SearchClaims.action";

            // Submit it and get results
            html = Post(logInfo, url, ref formData);

            // If PO exists
            if (formData.ContainsKey("dispatch.claim.dispatchId"))
            {
                // Get it
                po = GetPoInfo(logInfo, html);
            }

            return po;
        }

        public PurchaseOrder GetPurchaseOrder(LogInfo logInfo, int dispatchId, bool saveToDatabase, out HtmlDocument html)
        {
            var url = $"https://www.arsnetwork.allstate.com/towingnetwork_PROD/ARSWebClaim/claims/DisplayClaim.action?dispatchId={dispatchId}";
            html = Get(logInfo, url);

            if (html.DocumentNode.InnerHtml == "")
            {
                throw new NotFoundException($"Unable to get the PO with dispatchId: {dispatchId}");
            }

            var po = GetPoInfo(logInfo, html);

            if (saveToDatabase)
            {
                po.CompanyId = logInfo.CompanyId;
                po.AccountId = logInfo.AccountId;
                po.Save();
            }

            return po;
        }

        public decimal SubmitPurchaseOrder(LogInfo logInfo, PurchaseOrder po, HtmlDocument html)
        {
            var logs = new List<AutomatedHttpLog>();
            var form = GetFormFields(html);

            #region Sample Form Data
            // form["dispatch.claim.dispatchId"]                                    = ********
            // form["dispatch.claim.providerId"]                                    = WA9600127
            // form["userID"]                                                       = KITSAPTOWING
            // form["dispatch.vehicle.countryCode"]                                 = 
            // form["dispatch.vehicle.vin"]                                         = ***************
            // form["dispatch.claim.primarySvcCode"]                                = DOL
            // form["dispatch.claim.primaryGroupServiceCode"]                       = TOW
            // form["eqClassId"]                                                    = 1
            // form["systemAcronymCode"]                                            = AARPRT1
            // form["clmStatus"]                                                    = 402
            // form["auth_id"]                                                      = **********
            // form["dispatch.partnerCode"]                                         = AAR
            // form["dispatch.duplicateClmInd"]                                     = false
            // form["goaConfirmInd"]                                                = 
            // form["dispatch.provdrInvoice"]                                       = 8914
            // form["dispatch.vehicle.plateNumber"]                                 = 
            // form["dispatch.claim.requestedGOA"]                                  = false
            // form["dispatch.prvdrCancl"]                                          = 
            // form["dispatch.claim.unloadedMileage.claimSvcId"]                    = 39078586
            // form["dispatch.claim.unloadedMileage.estimatedMiles"]                = 10.6
            // form["dispatch.claim.loadedMileage.claimSvcId"]                      = 39078586
            // form["dispatch.claim.loadedMileage.estimatedMiles"]                  = 5.6
            // form["dispatch.claim.hoursUnit.claimSvcId"]                          = 
            // form["dispatch.claim.hoursUnit.estimatedHours"]                      = 
            // form["dispatch.mapQuestURL"]                                         = 
            // form["dispatch.claim.unloadedMileage.additionalMiles"]               = 11.0
            // form["dispatch.claim.unloadedMileage.threshold"]                     = 0.0
            // form["dispatch.claim.unloadedMileage.includedMiles"]                 = 5.0
            // form["dispatch.claim.unloadedMileage.rate"]                          = 2.72
            // form["dispatch.claim.unloadedMileage.amount"]                        = 16.32
            // form["dispatch.claim.loadedMileage.additionalMiles"]                 = 5.6
            // form["dispatch.claim.loadedMileage.threshold"]                       = 0.0
            // form["dispatch.claim.loadedMileage.maxUnitNbr"]                      = 5.0
            // form["dispatch.claim.loadedMileage.includedMiles"]                   = 5.0
            // form["dispatch.claim.loadedMileage.rate"]                            = 2.99
            // form["dispatch.claim.loadedMileage.amount"]                          = 1.79
            // form["dispatch.claim.hoursUnit.additionalHours"]                     = 
            // form["dispatch.claim.hoursUnit.includedHours"]                       = 
            // form["dispatch.claim.hoursUnit.rate"]                                = 
            // form["dispatch.claim.hoursUnit.amount"]                              = 
            // form["dispatch.updatedRouteComments"]                                = Different route
            // form["dispatch.claim.additionalFees.fuelGallonsAuthorized"]          = 
            // form["dispatch.claim.additionalFees.ferryCharge"]                    = 
            // form["dispatch.claim.additionalFees.fuelCostPerGallon"]              = 
            // form["dispatch.claim.additionalFees.reqFuelCost"]                    = 0
            // form["dispatch.claim.additionalFees.userEnteredFuelCost"]            = false
            // form["dispatch.paymentCollectedCustomer"]                            = 
            // form["dispatch.claim.additionalFees.tolls"]                          = 
            // form["dispatch.claim.additionalFees.additionalPayAmount"]            = 
            // form["dispatch.claim.additionalFees.userEnteredAdditionalPayAmount"] = false
            // form["dispatch.parkingGarageDesc"]                                   = No
            // form["dispatch.addtnlAuthFeeComments"]                               = 
            // form["dispatch.claim.additionalFees.reqAdditionalTime"]              = 
            // form["dispatch.damageNotes"]                                         = 
            // form["dispatch.disputeComments"]                                     = 
            // form["dispatch.escalateComments"]                                    = 
            // form["x"]                                                            = 612
            // form["y"]                                                            = 3958
            #endregion

            form["dispatch.claim.dispatchId"]                                    = $"{po.DispatchId}";               // "********";
            form["dispatch.claim.providerId"]                                    = $"{po.ProviderId}";               // "WA9600127";
            form["userID"]                                                       = $"{po.UserId}";                   // "KITSAPTOWING";
            form["dispatch.vehicle.countryCode"]                                 = $"{po.CountryCode}";              // "";
            form["dispatch.vehicle.vin"]                                         = $"{po.VehicleId}";                // "***************";
            form["dispatch.claim.primarySvcCode"]                                = $"{po.PrimarySvcCode}";           // "DOL";
            form["dispatch.claim.primaryGroupServiceCode"]                       = $"{po.PrimaryGroupServiceCode}";  // "TOW";
            form["eqClassId"]                                                    = $"{po.EqClassId}";                // "1";
            form["systemAcronymCode"]                                            = $"{po.SystemAcronymCode}";        // "AARPRT1";
            form["clmStatus"]                                                    = $"{po.ClmStatus}";                // "402";
            form["auth_id"]                                                      = $"{po.AuthId}";                   // "**********";
            form["dispatch.partnerCode"]                                         = $"{po.PartnerCode}";              // "AAR";
            form["dispatch.duplicateClmInd"]                                     = $"{po.DuplicateClmInd}".ToLower(); // "false";
            form["goaConfirmInd"]                                                = $"{po.GoaConfirmInd}";            // "";
            form["dispatch.provdrInvoice"]                                       = $"{po.ProviderInvoice}";          // "8914";
            form["dispatch.vehicle.plateNumber"]                                 = $"{po.VehicleLicensePlate}";      // "";
            form["dispatch.claim.requestedGOA"]                                  = $"{po.RequestedGOA}".ToLower();   // "false";
            form["dispatch.prvdrCancl"]                                          = $"{po.ItemIds}";                  // "";
            form["dispatch.claim.unloadedMileage.claimSvcId"]                    = $"{po.UnloadedClaimServiceId}";   // "39078586";
            form["dispatch.claim.unloadedMileage.estimatedMiles"]                = $"{po.UnloadedEstimated:0.0}";    // "10.6";
            form["dispatch.claim.loadedMileage.claimSvcId"]                      = $"{po.LoadedClaimServiceId}";     // "39078586";
            form["dispatch.claim.loadedMileage.estimatedMiles"]                  = $"{po.LoadedEstimated:0.0}";      // "5.6";
            form["dispatch.claim.hoursUnit.claimSvcId"]                          = $"{po.HoursUnitClaimServiceId}";  // "";
            form["dispatch.claim.hoursUnit.estimatedHours"]                      = $"{po.HoursUnitEstimated:0.0}";   // "";
            form["dispatch.mapQuestURL"]                                         = $"{po.MapURL}";                   // "";
            form["dispatch.claim.unloadedMileage.additionalMiles"]               = $"{po.UnloadedRequested:0.0}";    // "11.0";
            form["dispatch.claim.unloadedMileage.threshold"]                     = $"{po.UnloadedThreshold:0.0}";    // "0.0";
            form["dispatch.claim.unloadedMileage.includedMiles"]                 = $"{po.UnloadedIncluded:0.0}";     // "5.0";
            form["dispatch.claim.unloadedMileage.rate"]                          = $"{po.UnloadedRate:0.00}";        // "2.72";
            form["dispatch.claim.unloadedMileage.amount"]                        = $"{po.UnloadedAmount:0.00}";      // "16.32";
            form["dispatch.claim.loadedMileage.additionalMiles"]                 = $"{po.LoadedRequested:0.0}";      // "5.6";
            form["dispatch.claim.loadedMileage.threshold"]                       = $"{po.LoadedThreshold:0.0}";      // "0.0";
            form["dispatch.claim.loadedMileage.maxUnitNbr"]                      = $"{po.LoadedMaxUnitNumber:0.##}"; // "5.0";
            form["dispatch.claim.loadedMileage.includedMiles"]                   = $"{po.LoadedIncluded:0.0}";       // "5.0";
            form["dispatch.claim.loadedMileage.rate"]                            = $"{po.LoadedRate:0.00}";          // "2.99";
            form["dispatch.claim.loadedMileage.amount"]                          = $"{po.LoadedAmount:0.00}";        // "1.79";
            form["dispatch.claim.hoursUnit.additionalHours"]                     = $"{po.HoursUnitRequested:0.0}";   // "";
            form["dispatch.claim.hoursUnit.includedHours"]                       = $"{po.HoursUnitIncluded:0.0}";    // "";
            form["dispatch.claim.hoursUnit.rate"]                                = $"{po.HoursUnitRate:0.00}";       // "";
            form["dispatch.claim.hoursUnit.amount"]                              = $"{po.HoursUnitAmount:0.00}";     // "";
            form["dispatch.updatedRouteComments"]                                = $"{po.UpdatedRouteComments}";     // "Different route";
            form["dispatch.claim.additionalFees.fuelGallonsAuthorized"]          = $"{po.FuelGalAuth:0.##}";         // "";
            form["dispatch.claim.additionalFees.ferryCharge"]                    = $"{po.FerryCharge:0.00}";         // "";
            form["dispatch.claim.additionalFees.fuelCostPerGallon"]              = $"{po.FuelCost:0.00}";            // "";
            form["dispatch.claim.additionalFees.reqFuelCost"]                    = $"{po.ReqFuelCost:0.##}";         // "0";
            form["dispatch.claim.additionalFees.userEnteredFuelCost"]            = $"{po.UserEnteredFuelCost}".ToLower(); // "false";
            form["dispatch.paymentCollectedCustomer"]                            = $"{po.PaymentFromCustomer:0.00}"; // "";
            form["dispatch.claim.additionalFees.tolls"]                          = $"{po.Tolls:0.00}";               // "";
            form["dispatch.parkingGarageDesc"]                                   = $"{po.ParkingGarageDesc}";        // "No";
            form["dispatch.claim.additionalFees.reqAdditionalTime"]              = $"{po.ReqAdditionalTime}";        // "";
            form["dispatch.damageNotes"]                                         = $"{po.DamageNotes}";              // "";
            form["dispatch.disputeComments"]                                     = $"{po.DisputeComments}";          // "";
            form["dispatch.escalateComments"]                                    = $"{po.EscalateComments}";         // "";
            form["x"]                                                            = new Random().Next(590, 626).ToString();   // "612";
            form["y"]                                                            = new Random().Next(3950, 4006).ToString(); // "3958";

            // If add'l amount is disabled
            if (!po.AdditionalAmountIsEditable)
            {
                // If we wanted to use it
                if (po.AdditionalAmount > 0)
                {
                    // If the PO came preloaded with an add'l amount
                    if (po.PreLoadedAdditionalAmount > 0)
                    {
                        // If the amount we wanted to enter is a bit more than the amount preloaded
                        if (po.AdditionalAmount.Value - po.PreLoadedAdditionalAmount > 2m)
                        {
                            // Throw exception
                            throw new BillingException("Allstate disabled the 'Additional Approved Amount' field, but there are Towbook invoice charges that can only be entered there.");
                        }
                    }
                    else
                    {
                        // Throw exception
                        throw new BillingException("Allstate disabled the 'Additional Approved Amount' field, but there are Towbook invoice charges that can only be entered there.");
                    }
                }

                // If the PO came preloaded with an add'l amount
                if (po.PreLoadedAdditionalAmount > 0)
                {
                    // Note the difference
                    po.AmountDiffs.Add(new AmountDiff()
                    {
                        Name = "Add'l Amount",
                        DiffType = AmountDiffType.PreLoadedAdditionalAmount,
                        AllstateAmount = po.PreLoadedAdditionalAmount,
                        TowbookAmount = po.AdditionalAmount ?? 0,
                    });
                }
            }
            else
            {
                form["dispatch.claim.additionalFees.additionalPayAmount"]            = $"{po.AdditionalAmount:0.00}";                   // "";
                form["dispatch.claim.additionalFees.userEnteredAdditionalPayAmount"] = $"{po.UserEnteredAddlAmount}".ToLower();         // "false";
                form["dispatch.addtnlAuthFeeComments"]                               = $"{po.AdditionalAmountComments}".Trim(',', ' '); // "";
            }

            var key = "";
            var i = 0;

            foreach (var svc in po.Services.Where(s => !s.PrePaid))
            {
                #region Sample Form Data
                // form["dispatch.claim.serviceList[0].clmSvcId"]        = 39078586
                // form["dispatch.claim.serviceList[0].providerRateId"]  = 294971
                // form["dispatch.claim.serviceList[0].grpSvcCd"]        = TOW
                // form["dispatch.claim.serviceList[0].grpSvcCdDesc"]    = Dollies
                // form["dispatch.claim.serviceList[0].svcCd"]           = DOL
                // form["dispatch.claim.serviceList[0].svcCount"]        = 1
                // form["dispatch.claim.serviceList[0].eqpmntClassId"]   = 1
                // form["dispatch.claim.serviceList[0].baseUnitAmt"]     = 70.59
                // form["dispatch.claim.serviceList[0].equipmentId"]     = 2
                // form["dispatch.claim.serviceList[0].svcTypeId"]       = 567
                // form["dispatch.claim.serviceList[0].processStatusId"] = 406
                // form["dispatch.claim.serviceList[0].goaAmt"]          = 30.41
                // form["dispatch.claim.serviceList[0].goaInd"]          = false
                // form["dispatch.claim.serviceList[0].isPrimary"]       = true
                // form["dispatch.claim.serviceList[0].saveInd"]         = N
                #endregion

                key = "dispatch.claim.serviceList[" + i + "]";

                if (svc.ServiceType == ServiceType.TowService)
                {
                    form[key + ".clmSvcId"]        = $"{svc.ClaimServiceId}";
                    form[key + ".providerRateId"]  = $"{svc.ProviderRateId}";
                    form[key + ".grpSvcCd"]        = $"{svc.GroupServiceCode}";
                    form[key + ".grpSvcCdDesc"]    = $"{svc.GroupServiceCodeDesc}";
                    form[key + ".svcCd"]           = $"{svc.ServiceCode}";
                    form[key + ".svcCount"]        = $"{svc.ServiceCount}";
                    form[key + ".eqpmntClassId"]   = $"{svc.EquipmentClassId}";
                    form[key + ".baseUnitAmt"]     = $"{svc.BaseUnitAmount:0.##}";
                    form[key + ".equipmentId"]     = $"{svc.EquipmentId}";
                    if (svc.PrePopulated)
                    {
                        form[key + ".svcTypeId"]       = $"{svc.ServiceTypeId}";
                        form[key + ".processStatusId"] = $"{svc.ProcessStatusId}";
                    }
                    form[key + ".goaAmt"]          = $"{svc.GoaAmount:0.00}";
                    form[key + ".goaInd"]          = $"{svc.GoaInd}".ToLower().Map("true:Y");
                    form[key + ".isPrimary"]       = $"{svc.IsPrimary}".ToLower();
                    form[key + ".saveInd"]         = $"{svc.SaveInd}";
                    if (!svc.PrePopulated)
                    {
                        form[key + ".TSTFlag"]     = $"{svc.TSTFlag}";
                        form[key + ".TowTypeDesc"] = $"{svc.TowTypeDesc}";
                        form[key + ".EqTypeDesc"]  = $"{svc.EqTypeDesc}";
                    }
                }
                else
                {
                    form[key + ".clmSvcId"]        = $"{svc.ClaimServiceId}";
                    form[key + ".providerRateId"]  = $"{svc.ProviderRateId}";
                    form[key + ".grpSvcCd"]        = $"{svc.GroupServiceCode}";
                    form[key + ".grpSvcCdDesc"]    = $"{svc.GroupServiceCodeDesc}";
                    form[key + ".svcCd"]           = $"{svc.ServiceCode}";
                    form[key + ".svcCount"]        = $"{svc.ServiceCount}";
                    form[key + ".eqpmntClassId"]   = $"{svc.EquipmentClassId}";
                    form[key + ".baseUnitAmt"]     = $"{svc.BaseUnitAmount:0.##}";
                    form[key + ".equipmentId"]     = $"{svc.EquipmentId}";
                    if (svc.PrePopulated)
                    {
                        form[key + ".svcTypeId"]       = $"{svc.ServiceTypeId}";
                        form[key + ".processStatusId"] = $"{svc.ProcessStatusId}";
                    }
                    else
                    {
                        form[key + ".secndUnitNbr"]     = $"{svc.SecondUnitNumber}";
                        form[key + ".secndIncrmntlAmt"] = $"{svc.SecondIncrementalAmount:0.00}";
                    }
                    form[key + ".goaAmt"]          = $"{svc.GoaAmount:0.00}";
                    form[key + ".goaInd"]          = $"{svc.GoaInd}".ToLower().Map("true:Y");
                    form[key + ".isPrimary"]       = $"{svc.IsPrimary}".ToLower();
                    form[key + ".saveInd"]         = $"{svc.SaveInd}";
                    form[key + ".reqMinutes"]      = $"{svc.RequiredMinutes}";
                }

                i++;
            }

            i = 0;

            // For any addresses we validated
            foreach (var addr in po.Addresses.Where(a => a.AddressValidation == true))
            {
                form[$"dispatch.addressesList[{i}].claimID"]           = $"{po.DispatchId}";
                form[$"dispatch.addressesList[{i}].addressType"]       = $"{addr.AddressType}";
                form[$"dispatch.addressesList[{i}].userID"]            = $"{po.UserId}";
                form[$"dispatch.addressesList[{i}].addressStatus"]     = $"{addr.AddressStatus}";
                form[$"dispatch.addressesList[{i}].orderNumber"]       = $"{addr.Order}";
                form[$"dispatch.addressesList[{i}].bizName"]           = $"{addr.BizName}";
                form[$"dispatch.addressesList[{i}].crossStreet"]       = $"{addr.CrossStreet}";
                form[$"dispatch.addressesList[{i}].addressLine1"]      = $"{addr.AddressLine1}";
                form[$"dispatch.addressesList[{i}].addressLine2"]      = $"{addr.AddressLine2}";
                form[$"dispatch.addressesList[{i}].city"]              = $"{addr.City}";
                form[$"dispatch.addressesList[{i}].state"]             = $"{addr.State}";
                form[$"dispatch.addressesList[{i}].stateCd"]           = $"{addr.State}";
                form[$"dispatch.addressesList[{i}].zip"]               = $"{addr.Zip}";
                form[$"dispatch.addressesList[{i}].country"]           = $"{addr.Country}";
                form[$"dispatch.addressesList[{i}].countryCd"]         = $"{addr.Country}";
                form[$"dispatch.addressesList[{i}].phoneNumber"]       = $"{addr.PhoneNumber}";
                form[$"dispatch.addressesList[{i}].LATITD_NBR"]        = addr.Latitude != 0 ? addr.Latitude.ToString() : "";
                form[$"dispatch.addressesList[{i}].LONGITD_NBR"]       = addr.Longitude != 0 ? addr.Longitude.ToString() : "";
                form[$"dispatch.addressesList[{i}].loc_confdnc_cd"]    = $"{addr.LocConfdncCd}";
                form[$"dispatch.addressesList[{i}].estDistncNbr"]      = $"{addr.EstDistncNbr}";
                form[$"dispatch.addressesList[{i}].estRadiusNbr"]      = $"{addr.EstRadiusNbr}";
                form[$"dispatch.addressesList[{i}].vehclInd"]          = $"{addr.VehclInd}";
                form[$"dispatch.addressesList[{i}].vehclId"]           = $"{po.VehicleId}";
                form[$"dispatch.addressesList[{i}].estInitlDistncNbr"] = $"{addr.EstInitlDistncNbr}";
                form[$"dispatch.addressesList[{i}].locVenderType"]     = $"{addr.LocVenderType}";
                form[$"dispatch.addressesList[{i}].distncVenderType"]  = $"{addr.DistncVenderType}";
                form[$"dispatch.addressesList[{i}].geoCodeRouteType"]  = $"{addr.DistncCalctnType}";  // not a typo; this is how Allstate address.js line 972 specifies it
                form[$"dispatch.addressesList[{i}].mstrTypeDesc"]      = $"{addr.AddressDescription}";
                form[$"dispatch.addressesList[{i}].driveMiles"]        = "";
                form[$"dispatch.addressesList[{i}].crowMiles"]         = "";
                form[$"dispatch.addressesList[{i}].retrnMiles"]        = "";
                form[$"dispatch.addressesList[{i}].orderNbrForMiles"]  = "";
                form[$"dispatch.addressesList[{i}].initlDistncNbr"]    = "0";
                form[$"dispatch.addressesList[{i}].validationFlag"]    = $"{addr.ValidationFlag}";
                form[$"dispatch.addressesList[{i}].county"]            = $"{addr.County}";
                form[$"dispatch.addressesList[{i}].stateTaxId"]        = $"{addr.StateTaxId}";
                i++;
            }

            i = 0;
            foreach (var c in po.Comments.Where(a => a.PrePopulated == false))
            {
                form[$"dispatch.commentsLog[{i}]"] = c.Message;
                i++;
            }

            var files = new Dictionary<string, WebRequestHelper.FormFieldFile>();

            #region File Preparation
            //var j = 1;
            //var fileName = "";
            //var keyName = "";

            //// Prepare files for submit
            //if (po.AdditionalFeeFiles != null)
            //{
            //    foreach (var af in po.AdditionalFeeFiles)
            //    {
            //        keyName = string.Format("additionalFee{0}", j++);
            //        fileName = af.Key.ToLowerInvariant();
            //        form.Remove(keyName);
            //        AddFile(keyName, fileName, af.Value, ref files);
            //    }
            //}
            //else
            //{
            //    form.Remove("additionalFee1");
            //    files.Add("additionalFee1", new WebRequestHelper.FormFieldFile()
            //    {
            //        Content = null,
            //        ContentType = "application/octet-stream",
            //        FileName = string.Empty
            //    });
            //}

            //if (po.DamageFiles != null)
            //{
            //    foreach (var af in po.DamageFiles)
            //    {
            //        keyName = string.Format("damage{0}", j++);
            //        fileName = af.Key.ToLowerInvariant();
            //        form.Remove(keyName);
            //        AddFile(keyName, fileName, af.Value, ref files);
            //    }
            //}
            //else
            //{
            //    form.Remove("damage1");
            //    files.Add("damage1", new WebRequestHelper.FormFieldFile()
            //    {
            //        Content = null,
            //        ContentType = "application/octet-stream",
            //        FileName = string.Empty
            //    });
            //}

            //form.Remove("overMileage1");
            //files.Add("overMileage1", new WebRequestHelper.FormFieldFile()
            //{
            //    Content = null,
            //    ContentType = "application/octet-stream",
            //    FileName = string.Empty
            //});

            //form.Remove("disputedFile1");
            //files.Add("disputedFile1", new WebRequestHelper.FormFieldFile()
            //{
            //    Content = null,
            //    ContentType = "application/octet-stream",
            //    FileName = string.Empty
            //});

            //form.Remove("escalatedFile1");
            //files.Add("escalatedFile1", new WebRequestHelper.FormFieldFile()
            //{
            //    Content = null,
            //    ContentType = "application/octet-stream",
            //    FileName = string.Empty
            //});
            #endregion

            // These should be removed; they're outside the calculate claim <form> tags
            // Also -- don't move these.  They need to be removed last.
            form.Remove("stateUpdate");
            form.Remove("inputTowType");

            // Calculate Claim
            var log = new AutomatedHttpLog();
            html = WebRequestHelper.GetHtmlMultipart(
                "https://www.arsnetwork.allstate.com/towingnetwork_PROD/ARSWebClaim/claims/CalculateClaim.action",
                form.ToList(),
                files.ToList(),
                ref cookieContainer,
                ref log
            );
            LogHttpSession(log, logInfo);

            // If errors, throw exception
            var headerText = html.DocumentNode.SelectSingleNode("//span[@class='headertext']");
            if (headerText != null)
            {
                var text = headerText.InnerText.Trim();
                if (text == "Error" || text == "Duplicate Claim")
                    text = $"Allstate says '{text}', with no description as to why.  Please bill manually.";

                throw new BillingException(text);
            }

            // Read the claim summary
            var claimSummary = GetClaimSummary(html, po);

            // Get the total we calculated from the claim summary
            var allstateTotal = claimSummary.Total ?? 0;

            // If the claim summary had a "Benefit Coverage Exceeded" amount to deduct, and we didn't have more than 1 amt diff
            var bceAmount = -claimSummary.Payments.FirstOrDefault(p => p.Item.Contains("Benefit Coverage Exceeded"))?.GetAmount() ?? 0;
            if (bceAmount > 0 && po.AmountDiffs.Count() <= 1)
            {
                // Log it
                logInfo.AddEvent($"Benefit Coverage Exceeded: \t{-bceAmount:0.00}", LogLevel.Info);
            }
            else
            {
                // If Allstate's total is less than 90% of Towbook's total
                if (allstateTotal < po.ExpectedTotal * .9m)
                {
                    // If its a GOA in Allstate, but not in Towbook
                    if (po.AmountDiffs.Any(a => a.DiffType == AmountDiffType.GOA))
                    {
                        throw new UserErrorException($"Allstate will only pay {allstateTotal:c} for this invoice; they say it's a GOA.  Either adjust the amount in Towbook and resubmit it, or bill it manually.");
                    }

                    // If part of the problem is Allstate has a lower mileage rate (or service rate - but check mileage rates first)
                    var amtDiff = po.AmountDiffs.FirstOrDefault(a => a.Diff() < 0 && a.DiffType == AmountDiffType.MileageRate)
                               ?? po.AmountDiffs.FirstOrDefault(a => a.Diff() < 0 && a.DiffType == AmountDiffType.Service);
                    if (amtDiff != null)
                    {
                        // Throw exception, but tell the user
                        throw new UserErrorException($"Allstate pays a lower rate of {amtDiff.AllstateAmount:c} for {amtDiff.TowbookName ?? amtDiff.Name}. Towbook has it listed as {amtDiff.TowbookAmount:c}. Change the rate in Towbook to match and then resubmit.");
                    }
                    else
                        throw new BillingException($"Allstate total of {allstateTotal:0.00} is significantly less than the expected total of {po.ExpectedTotal:0.00}");
                }

                // If Allstate's total is more than 500% of Towbook's total
                else if (allstateTotal > po.ExpectedTotal * 5.0m)
                {
                    throw new BillingException($"Allstate total of {allstateTotal:0.00} is significantly more than the expected total of {po.ExpectedTotal:0.00}");
                }

                // If Allstate's total is more than 150% of Towbook's total
                else if (allstateTotal > po.ExpectedTotal * 1.5m)
                {
                    // If this is for medium or heavy duty
                    if (po.EqClassId == 2 || po.EqClassId == 3)
                    {
                        // Let it through anyways (Allstate sometimes pays VERY well for these, more than was entered in Towbook)
                        logInfo.AddEvent($"{po.ServiceWeight} Duty -- Allowing submission of {allstateTotal:0.00} instead of expected total of {po.ExpectedTotal:0.00}", LogLevel.Info);
                    }
                    else
                    {
                        // If Allstate is offering / calculating more add'l amount, or more mileage than what we had in Towbook
                        var amtDiffs = po.AmountDiffs.Where(a => a.Diff() > 0 && (a.DiffType == AmountDiffType.Mileage || a.DiffType == AmountDiffType.PreLoadedAdditionalAmount));
                        if (amtDiffs.Count() > 0)
                        {
                            // Let it through anyways
                            var amtDiffsStr = string.Join(", ", amtDiffs.Select(d => d.Name).ToArray());
                            logInfo.AddEvent($"Allstate is offering more for: {amtDiffsStr} -- Allowing submission of {allstateTotal:0.00} instead of expected total of {po.ExpectedTotal:0.00}", LogLevel.Info);
                        }
                        else
                            throw new BillingException($"Allstate total of {allstateTotal:0.00} is significantly more than the expected total of {po.ExpectedTotal:0.00}");

                        // Total=$145.50 ,
                        // Allstate total of 256.50 is significantly more than the expected total of 145.50 ,$86.00 

                    }
                }
            }

            // Log totals
            logInfo.AddEvent($"Allstate -- Total: \t{allstateTotal:0.00}\t Expected: \t{po.ExpectedTotal:0.00}\t Towbook: \t{po.TowbookTotal:0.00}", LogLevel.Info);

            // If totals are too divergent, request logs be flushed at end of processing
            if (Math.Abs(allstateTotal - po.ExpectedTotal) > 5 ||
                Math.Abs(allstateTotal - po.TowbookTotal) > 10)
            {
                logInfo.FlushRequested = true;
            }

            // Submit the PO
            if (logInfo.TestMode)
            {
                // TestMode: cancel the submission
                log = new AutomatedHttpLog();
                html = WebRequestHelper.GetHtml(
                "https://www.arsnetwork.allstate.com/towingnetwork_PROD/ARSWebClaim/claims/Home!reDisplayHome.action",
                null,
                ref cookieContainer,
                ref log);
                LogHttpSession(log, logInfo);

                throw new BillingException("TestMode successful; final submission bypassed");
            }
            else
            {
                log = new AutomatedHttpLog();
                form = GetFormFields(html);
                html = WebRequestHelper.GetHtml(
                    "https://www.arsnetwork.allstate.com/towingnetwork_PROD/ARSWebClaim/claims/SubmitClaim.action",
                    form,
                    ref cookieContainer,
                    ref log
                );
                LogHttpSession(log, logInfo);

                // If the page doesn't show a success message
                headerText = html.DocumentNode.SelectSingleNode("//span[@class='headertext']");
                if (headerText != null && headerText.InnerText.Trim() != "Thank you!")
                {
                    // Get error message off the page and throw an exception
                    throw new BillingException(headerText.InnerText.Trim());
                }

                logs.Add(log);
            }

            return allstateTotal;
        }

        public List<PaymentStatement> GetPaymentStatements(LogInfo logInfo, DateTime startDate, DateTime endDate, bool retrieveDetails = false)
        {
            var payments = new List<PaymentStatement>();

            // With the new changes on the allstate site, we need to call the SSO url first
            var html = Get(logInfo, webclaimsUrl);

            var log = new AutomatedHttpLog();
            html = WebRequestHelper.GetHtml("https://www.arsnetwork.allstate.com/towingnetwork_PROD/ARSWebClaim/claims/SearchEFTPayments.action",
            new Dictionary<string, string>
            {
                {"eftDateFrom", startDate.ToString("MM/dd/yyyy")},
                {"eftDateTo", endDate.ToString("MM/dd/yyyy")},
                {"searchBtn.x", "34"},
                {"searchBtn.y", "10"},
                {"searchBtn", "Submit"}
            }, ref cookieContainer, ref log);
            LogHttpSession(log, logInfo);

            var tables = html.DocumentNode.SelectNodes("//table[@id='standard']");

            if (tables == null || tables.Count != 3)
                return payments;

            try
            {

                // getting the table with the payments records
                var table = tables[1];
                var nodes = table.SelectNodes("tbody/tr");

                foreach (var node in nodes)
                {
                    var columns = node.SelectNodes(".//td");

                    var payment = new PaymentStatement();
                    payment.Number = HtmlToText.GetString(columns[0]);
                    payment.Date = Convert.ToDateTime(HtmlToText.GetString(columns[1]).Substring(0, 10));
                    payment.Status = PaymentStatement.GetStatus(HtmlToText.GetString(columns[2]));
                    payment.AmountPaid = HtmlToText.GetDecimal(columns[3]);
                    payment.Type = PaymentStatement.GetType(HtmlToText.GetString(columns[4]));

                    if (retrieveDetails)
                    {
                        payment.PaymentDetails = GetPaymentStatementsDetails(logInfo, payment.Number, payment.Date, startDate, endDate);
                    }

                    payments.Add(payment);
                }

                return payments;
            }
            catch (ArgumentOutOfRangeException ex)
            {
                var tableHtml = tables?
                                .Select((table, index) => new { Index = index, Html = table.InnerHtml })
                                .ToDictionary(t => t.Index, t => t.Html) ?? new Dictionary<int, string>();

                LogEvent(logInfo, $"Error parsing ETF payment table data from Allstate statement search", LogLevel.Error, new Dictionary<object, object>
                {
                    { "data", string.Join(", ", tableHtml) },
                    { "json", new
                        {
                            StartDate = startDate.ToString("MM/dd/yyyy"),
                            EndDate = endDate.ToString("MM/dd/yyyy"),
                            RetrieveDetails = retrieveDetails,
                            Url = "https://www.arsnetwork.allstate.com/towingnetwork_PROD/ARSWebClaim/claims/SearchEFTPayments.action"
                        }.ToJson()
                    },
                    { "exception", ex }
                });

                throw ex;
            }
        }

        public List<PaymentStatementDetail> GetPaymentStatementsDetails(LogInfo logInfo, string paymentNumber, DateTime paymentDate, DateTime eftDateFrom, DateTime eftDateTo)
        {
            var details = new List<PaymentStatementDetail>();

            var url = String.Format(
                "https://www.arsnetwork.allstate.com/towingnetwork_PROD/ARSWebClaim/claims/DisplayEFTPaymentItem.action?paymentNumber={0}&paymentDate={1}&eftDateFrom={2}&eftDateTo={3}",
                WebUtility.UrlEncode(paymentNumber),
                WebUtility.UrlEncode(paymentDate.ToString("MM/dd/yyyy")),
                WebUtility.UrlEncode(eftDateFrom.ToString("MM/dd/yyyy")),
                WebUtility.UrlEncode(eftDateTo.ToString("MM/dd/yyyy"))
            );
            var html = Get(logInfo, url);

            var tables = html.DocumentNode.SelectNodes("//table[@id='standard']");

            if (tables == null || tables.Count < 3)
                return details;

            var tableNode = tables[1];

            var tableData = ParseTable(tableNode);

            if (tableData.Any())
            {
                foreach (var row in tableData)
                {
                    var payment = new PaymentStatementDetail();

                    if (row.ContainsKey("Auth ID"))
                        payment.Id = row["Auth ID"].Trim();

                    if (row.ContainsKey("Service Type"))
                        payment.ServiceType = HtmlToText.GetString(row["Service Type"]);
                    
                    if (row.ContainsKey("Service Date"))
                        payment.ServiceDate = Convert.ToDateTime(HtmlToText.GetString(row["Service Date"]).Substring(0, 10));

                    if (row.ContainsKey("Member ID"))
                        payment.MemberId = row["Member ID"];

                    if (row.ContainsKey("Member Name"))
                        payment.MemberName = row["Member Name"];

                    if (row.ContainsKey("Amount Paid"))
                        payment.AmountPaid = HtmlToText.GetDecimal(row["Amount Paid"]);

                    if (row.ContainsKey("Previous Amount Paid"))
                        payment.PrevAmountPaid = HtmlToText.GetDecimal(row["Previous Amount Paid"]);

                    details.Add(payment);
                }
            }

            return details;
        }

        public void ReleaseLock(LogInfo logInfo)
        {
            logInfo.AddEvent($"Disposing LockId: {_connKey.LockId.Value}, connKey: {_connKey}", LogLevel.Info);
            DistributedLock.ForceUnlock(_connKey.LockId.Value);
            logInfo.SecondaryPrefix = "";
        }

        public string GetServletData(
            LogInfo logInfo,
            PurchaseOrder po,
            string scriptName,
            string methodName,
            Dictionary<string, string> parameters,
            bool testMode = false,
            bool includeNextReverseAjaxIndex = true,
            bool includeWindowName = false,
            bool includeHttpSessionId = false,
            bool generateScriptSessionId = false)
        {
            // If we don't have a ScriptSessionId yet
            if (!generateScriptSessionId && po.ScriptSessionId == null)
            {
                // Recursively call this again, with generateScriptSessionId = 'true'
                var sessionId = ServletParser.GetValue(GetServletData(logInfo, po, "__System", "generateId", null, generateScriptSessionId: true));
                var pageId = GetPageId();
                po.ScriptSessionId = sessionId + "/" + pageId;
            }

            var paramsHeader = new Dictionary<string, string>()
            {
                { "callCount", "1" },
                { "windowName", string.Empty },
                { "nextReverseAjaxIndex", "0" },
                { "c0-scriptName", scriptName },
                { "c0-methodName", methodName },
                { "c0-id", "0" },
            };

            var paramsFooter = new Dictionary<string, string>()
            {
                { "batchId", po.ServletBatchId.ToString() },
                { "instanceId", "0" },
                { "page", "/towingnetwork_PROD/ARSWebClaim/claims/DisplayClaim.action?dispatchId=" + po.DispatchId },
                { "scriptSessionId", po.ScriptSessionId ?? "" },
                { "httpSessionId", string.Empty }
            };

            var formData = new Dictionary<string, string>();

            foreach (var x in paramsHeader)
                try { formData.Add(x.Key, x.Value); } catch { } // catch duplicates

            foreach (var x in parameters ?? new Dictionary<string, string>())
                try { formData.Add(x.Key, x.Value); } catch { } // catch duplicates

            foreach (var x in paramsFooter)
                try { formData.Add(x.Key, x.Value); } catch { } // catch duplicates

            if (!includeNextReverseAjaxIndex) formData.Remove("nextReverseAjaxIndex");
            if (!includeWindowName) formData.Remove("windowName");
            if (!includeHttpSessionId) formData.Remove("httpSessionId");

            var posted = formData.ToJson();

            var url = "https://www.arsnetwork.allstate.com/towingnetwork_PROD/ARSWebClaim/dwr/call/plaincall/" + scriptName + "." + methodName + ".dwr";
            var html = Post(logInfo, url, ref formData, testMode);
            var response = html.DocumentNode.OuterHtml;

            po.ServletBatchId += 1;

            // Check for errors in response
            var rgx = new Regex(@"Exception\((.+)\)", RegexOptions.IgnoreCase);
            var match = rgx.Match(response);
            if (match.Groups.Count > 1)
                throw new BillingException($"Error getting data for: [{scriptName}.{methodName}], Server Response: [{match.Groups[1].Value}], Form data: {posted}");

            return response;
        }

        #region Private Methods

        // Login() methods

        private class ConnectionKeyModel
        {
            public Guid Machine;
            public string UserName;
            public string Password;
            public string ProviderId;
            public int SessionNumber;
            public Guid? Code;
            public long? LockId;

            public override string ToString()
            {
                var x = new
                {
                    m = Machine,
                    u = UserName,
                    pv = ProviderId,
                    s = SessionNumber,
                };

                return x.ToJson();
            }
        }

        private static ConnectionKeyModel GetConnectionKey(LogInfo logInfo, string username, string password, string providerId)
        {
            var connKey = new ConnectionKeyModel() { UserName = username, Password = password, ProviderId = providerId, Machine = machineId };
            var s = Stopwatch.StartNew();

            // Try 900 times
            for (int x = 0; x < 900; x++)
            {
                // To get a lock on one of the available sessions
                for (int i = 0; i < maxSessionsPerLogin; i++)
                {
                    connKey.SessionNumber = i;

                    var lockRow = DistributedLock.GetLock("AllstateConnection", connKey.ToString());
                    if (lockRow == null) continue;

                    if (lockRow.Code != Guid.Empty)
                    {
                        s.Stop();
                        connKey.Code = lockRow.Code;
                        connKey.LockId = lockRow.DistributedLockId;
                        logInfo.SecondaryPrefix = $"S{connKey.SessionNumber}-T{Thread.CurrentThread.ManagedThreadId:00}";

                        logInfo.AddEvent($"Acquired LockId: {connKey.LockId.Value} after {s.Elapsed.TotalSeconds:0.0}s, connKey: {connKey}", LogLevel.Info);
                        return connKey;
                    }
                    else if (lockRow.CreateDate < DateTime.Now.ToUniversalTime().AddSeconds(-360))
                    {
                        // Release expired locks
                        logInfo.AddEvent($"Releasing Expired LockId: {lockRow.DistributedLockId}, connKey: {lockRow.Value}", LogLevel.Info);
                        DistributedLock.ForceUnlock(lockRow.DistributedLockId);
                    }
                }

                // Sleep before trying again
                Thread.Sleep(200);
            }

            s.Stop();
            throw new Exception($"Couldn't acquire lock after {s.Elapsed.TotalSeconds:0.0}s");
        }

        private List<string> GetProviders(LogInfo logInfo)
        {
            var result = new List<string>();

            var url = "https://www.arsnetwork.allstate.com/towingnetwork_PROD/ARSWebClaim/provider/DisplaySetProviderPage.action";
            var html = Get(logInfo, url);

            var rows = html.DocumentNode.SelectNodes("//table[@id='standard']//tr");

            for (int i = 2; i < rows.Count - 1; i++)
                result.Add(rows[i].ChildNodes[3].InnerText);

            return result;
        }

        private Cookie CreateCookie(string name, string value, string path, string domain = "", bool secure = false)
        {
            Cookie cookie;
            if (domain != "")
                cookie = new Cookie(name, value, path, domain);
            else
                cookie = new Cookie(name, value, path);

            if (secure)
                cookie.Secure = true;

            return cookie;
        }

        // Unused methods

        private List<CookieParam> GetCookiesParams(string contents)
        {
            var pattern = @"setCookiesFromTAM\('(.+)','(.+)','(.+)','(.+)',(.+)\)";

            var rgx = new Regex(pattern, RegexOptions.IgnoreCase);
            var matches = rgx.Matches(contents);
            var cookiesParams = new List<CookieParam>();
            var cookieParam = new CookieParam();

            if (matches.Count > 0)
            {
                foreach (Match match in matches)
                {
                    cookieParam = new CookieParam();

                    cookieParam.Name = match.Groups[1].Value.Trim();
                    cookieParam.Value = match.Groups[2].Value.Trim();
                    cookieParam.Path = match.Groups[3].Value.Trim();
                    cookieParam.Domain = match.Groups[4].Value.Trim();
                    cookieParam.Secure = match.Groups[5].Value.Trim() == "true" ? true : false;

                    cookiesParams.Add(cookieParam);
                }
            }

            //"IV_JCT=%2Ftowingnetwork_PROD; path=/";
            cookieParam = new CookieParam();
            cookieParam.Name = "IV_JCT";
            cookieParam.Value = "%2Ftowingnetwork_PROD";
            cookieParam.Path = "/";
            cookieParam.Domain = "www.arsnetwork.allstate.com";
            cookiesParams.Add(cookieParam);

            return cookiesParams;
        }

        public List<ServiceRateItem> GetServiceRates(LogInfo logInfo, string contractorId)
        {
            var programs = new List<System.Collections.Generic.KeyValuePair<string,string>> ();
            
            var url = "https://www.arsnetwork.allstate.com/towingnetwork_PROD/ARSWebClaim/provider/DisplayServiceRates.action";
            var html = Get(logInfo, url);

            var options = html.DocumentNode.SelectNodes("//select[@id='partnerSelectBox']/option");
            foreach(var opt in options)
            {
                programs.Add(new System.Collections.Generic.KeyValuePair<string,string>(opt.NextSibling.InnerText,
                    opt.Attributes["value"].Value));
            }

            var serviceRateCollection = new List<ServiceRateItem>();

            var serviceRateWeightTypes = new List<ServiceRateWeight>();
            serviceRateWeightTypes.Add(ServiceRateWeight.Light);
            serviceRateWeightTypes.Add(ServiceRateWeight.Medium);
            serviceRateWeightTypes.Add(ServiceRateWeight.Heavy);
            serviceRateWeightTypes.Add(ServiceRateWeight.MotorCycle);
            serviceRateWeightTypes.Add(ServiceRateWeight.Home);
            serviceRateWeightTypes.Add(ServiceRateWeight.SuperHeavy);

            foreach (var program in programs)
            {
                foreach (var weightType in serviceRateWeightTypes)
                {

                    serviceRateCollection.AddRange(GetServiceRateByWeightType(logInfo, weightType, contractorId, program.Value, program.Key));
                }
            }

            return serviceRateCollection;
        }

        private List<ServiceRateItem> GetServiceRateByWeightType(LogInfo logInfo, ServiceRateWeight WeightType, string contractorId, string programId, string programName)
        {
            if (programId == null)
                programId = "ANY";

            var serviceRates = new List<ServiceRateItem>();
            var url = "https://www.arsnetwork.allstate.com/ARSWebClaim/dwr/call/plaincall/providerRatesService.findProviderServiceRates.dwr";
            var batchId = 1;

            var log = new AutomatedHttpLog();
            var html = WebRequestHelper.GetHtml(url, new Dictionary<string, string>
            {
                {"callCount", "1"},
                {"windowName", ""},
                {"nextReverseAjaxIndex", "0"},
                {"c0-scriptName", "providerRatesService"},
                {"c0-methodName", "findProviderServiceRates"},
                {"c0-id", "0"},
                {"c0-param0", "string:" + contractorId},
                {"c0-param1", "string:" + programId},
                {"c0-param2", String.Format("string:{0}", (int) WeightType)},
                {"batchId", String.Format("{0}", batchId++)},
                {"instanceId", "0"},
                {"page", @"%2Ftowingnetwork_PROD%2FARSWebClaim%2Fprovider%2FDisplayServiceRates.action"},
                {"scriptSessionId", "3BwRbiidKFBduv81sJQwgQbCy7l/1x0Cy7l-9KlDeONu8"}
            }, ref cookieContainer, ref log);
            LogHttpSession(log, logInfo);

            var pattern = @"handleCallback\(""\w*"",""\w*"",(.*)\);";

            var rgx = new Regex(pattern, RegexOptions.IgnoreCase);

            var matches = rgx.Matches(html.DocumentNode.InnerHtml);
            ServiceRateItem item;

            if (matches.Count > 0)
            {
                var dataCollection = JsonConvert.DeserializeObject<dynamic>(matches[0].Groups[1].Value);

                foreach (var itemData in dataCollection)
                {
                    item = new ServiceRateItem();

                    item.ServiceDescription = itemData.serviceDescription;
                    item.ServiceWeight = WeightType;
                    if (itemData.serviceBaseRate != null)
                        item.Base = itemData.serviceBaseRate;
                    if (itemData.unloadedRate != null)
                        item.EnrouteAmount = Convert.ToDecimal(itemData.unloadedRate);
                    if (itemData.unloadedFreeUnits != null)
                        item.EnrouteFree = Convert.ToInt16(itemData.unloadedFreeUnits);
                    if (itemData.loadedRate != null)
                        item.LoadedAmount = itemData.loadedRate;
                    if (itemData.loadedFreeUnits != null)
                        item.LoadedFree = itemData.loadedFreeUnits;
                    if (itemData.secondUnitRate != null)
                        item.Additional = Convert.ToDecimal(itemData.secondUnitRate);
                    if (itemData.goneOnArrivalAmount != null)
                        item.GoneOnArrivalAmount = Convert.ToDecimal(itemData.goneOnArrivalAmount);

                    item.ProgramId = programId;
                    item.ProgramName = programName;
                    serviceRates.Add(item);
                }
            }

            return serviceRates;
        }

        // GetPurchaseOrder() methods

        private PurchaseOrder GetPoInfo(LogInfo logInfo, HtmlDocument html)
        {
            var form = GetFormFields(html);
            var poNumber = html.DocumentNode.SelectSingleNode("//div[@class='panel-heading']/div[1]/div[1]/label[2]").InnerText.Trim();

            var po = PurchaseOrder.GetByPurchaseOrderNumber(logInfo.AccountId, poNumber)
                 ?? new PurchaseOrder();

            // General
            po.DispatchId = Convert.ToInt32(form["dispatch.claim.dispatchId"]);
            po.ProviderId = form["dispatch.claim.providerId"];
            po.UserId = form["userID"];
            po.CountryCode = form["dispatch.vehicle.countryCode"];
            po.PrimarySvcCode = form["dispatch.claim.primarySvcCode"];
            po.PrimaryGroupServiceCode = form["dispatch.claim.primaryGroupServiceCode"];
            po.EqClassId = GetNullableInt(form["eqClassId"]);
            po.SystemAcronymCode = form["systemAcronymCode"];
            po.ClmStatus = form["clmStatus"];
            po.AuthId = form["auth_id"];
            po.PartnerCode = form["dispatch.partnerCode"];
            po.DuplicateClmInd = form["dispatch.duplicateClmInd"].Trim().ToLower(); // should return "true" or "false"
            po.GoaConfirmInd = form["goaConfirmInd"];
            po.PurchaseOrderNumber = poNumber;
            po.Status = (html.DocumentNode.SelectSingleNode("//div[@class='panel-heading']/div[1]/div[2]/label[2]")?.InnerText ?? string.Empty).Trim();
            po.ProviderInvoice = form["dispatch.provdrInvoice"];
            po.ServiceDate = GetDateTime(html.DocumentNode.SelectSingleNode("//input[@id='svcDate']")?.Attributes["value"]?.Value ?? string.Empty);
            po.ServiceWeight = html.DocumentNode.SelectSingleNode("//input[@id='svcWeight']")?.Attributes["value"]?.Value ?? string.Empty;
            po.Event = WebUtility.HtmlDecode(html.DocumentNode.SelectNodes("//label[@class='subheadertext']")[2].InnerText.Split(':')[1]).Trim();

            // Customer Information
            po.CustomerName = html.DocumentNode.SelectSingleNode("//input[@id='callerName']")?.Attributes["value"]?.Value ?? string.Empty;
            po.Partner = html.DocumentNode.SelectSingleNode("//input[@id='partnerDesc']")?.Attributes["value"]?.Value ?? string.Empty;
            po.CallbackNumber = html.DocumentNode.SelectSingleNode("//input[@id='callbackNbr']")?.Attributes["value"]?.Value ?? string.Empty;
            po.MembershipNumber = html.DocumentNode.SelectSingleNode("//input[@id='memberId']")?.Attributes["value"]?.Value ?? string.Empty;
            po.CallerName = html.DocumentNode.SelectSingleNode("//input[@id='customerName']")?.Attributes["value"]?.Value ?? string.Empty;

            // Vehicle Information
            var vehicleParts = html.DocumentNode.SelectNodes("//div[@class='col-xs-6']")[4].InnerText.Trim().Split(' ');
            var vehicleModel = string.Empty;
            foreach (var s in vehicleParts)
            {
                if (s != vehicleParts[0] && s != vehicleParts[vehicleParts.Length - 1])
                    vehicleModel += (s + " ");
            }
            po.VehicleMake = vehicleParts[0];
            po.VehicleModel = vehicleModel.Trim();
            po.VehicleColor = vehicleParts[vehicleParts.Length - 1];
            po.VehicleId = form["dispatch.vehicle.vin"]; // Although this comes from 'vehicle.vin', it is actually VehicleId. The real VIN is below:
            po.VehicleVIN = html.DocumentNode.SelectSingleNode("//input[@id='vin']")?.Attributes["value"]?.Value ?? string.Empty;
            po.VehicleLicensePlate = form["dispatch.vehicle.plateNumber"];

            // Mileage, Payments, Comments, etc.
            po.RequestedGOA = GetNullableBool(form["dispatch.claim.requestedGOA"]);
            po.ItemIds = form["dispatch.prvdrCancl"];
            po.UnloadedClaimServiceId = GetNullableInt(form["dispatch.claim.unloadedMileage.claimSvcId"]);
            po.UnloadedEstimated = GetNullableDecimal(form["dispatch.claim.unloadedMileage.estimatedMiles"]);
            po.UnloadedRequested = GetNullableDecimal(form["dispatch.claim.unloadedMileage.additionalMiles"]);
            po.UnloadedThreshold = GetNullableDecimal(form["dispatch.claim.unloadedMileage.threshold"]);
            po.UnloadedIncluded = GetNullableDecimal(form["dispatch.claim.unloadedMileage.includedMiles"]);
            po.UnloadedRate = GetNullableDecimal(form["dispatch.claim.unloadedMileage.rate"]);
            po.UnloadedAmount = GetNullableDecimal(form["dispatch.claim.unloadedMileage.amount"]);
            po.EstMileageUNL = po.UnloadedEstimated;
            po.LoadedClaimServiceId = GetNullableInt(form["dispatch.claim.loadedMileage.claimSvcId"]);
            po.LoadedEstimated = GetNullableDecimal(form["dispatch.claim.loadedMileage.estimatedMiles"]);
            po.LoadedRequested = GetNullableDecimal(form["dispatch.claim.loadedMileage.additionalMiles"]);
            po.LoadedThreshold = GetNullableDecimal(form["dispatch.claim.loadedMileage.threshold"]);
            po.LoadedIncluded = GetNullableDecimal(form["dispatch.claim.loadedMileage.includedMiles"]);
            po.LoadedRate = GetNullableDecimal(form["dispatch.claim.loadedMileage.rate"]);
            po.LoadedAmount = GetNullableDecimal(form["dispatch.claim.loadedMileage.amount"]);
            po.LoadedMaxUnitNumber = GetNullableDecimal(form["dispatch.claim.loadedMileage.maxUnitNbr"]);
            po.EstMileageLD = po.LoadedEstimated;
            po.HoursUnitClaimServiceId = GetNullableInt(form["dispatch.claim.hoursUnit.claimSvcId"]);
            po.HoursUnitEstimated = GetNullableDecimal(form["dispatch.claim.hoursUnit.estimatedHours"]);
            po.HoursUnitRequested = GetNullableDecimal(form["dispatch.claim.hoursUnit.additionalHours"]);
            po.HoursUnitIncluded = GetNullableDecimal(form["dispatch.claim.hoursUnit.includedHours"]);
            po.HoursUnitRate = GetNullableDecimal(form["dispatch.claim.hoursUnit.rate"]);
            po.HoursUnitAmount = GetNullableDecimal(form["dispatch.claim.hoursUnit.amount"]);
            po.MapURL = form["dispatch.mapQuestURL"];
            po.FuelGalAuth = GetNullableDecimal(form["dispatch.claim.additionalFees.fuelGallonsAuthorized"]);
            po.FuelCost = GetNullableDecimal(form["dispatch.claim.additionalFees.fuelCostPerGallon"]);
            po.ReqFuelCost = GetNullableDecimal(form["dispatch.claim.additionalFees.reqFuelCost"]);
            po.UserEnteredFuelCost = GetNullableBool(form["dispatch.claim.additionalFees.userEnteredFuelCost"]);
            po.Tolls = GetNullableDecimal(form["dispatch.claim.additionalFees.tolls"]);
            po.FerryCharge = GetNullableDecimal(form["dispatch.claim.additionalFees.ferryCharge"]);
            po.PaymentFromCustomer = GetNullableDecimal(form["dispatch.paymentCollectedCustomer"]);
            po.AdditionalAmount = GetNullableDecimal(form["dispatch.claim.additionalFees.additionalPayAmount"]);
            po.AdditionalAmountComments = form["dispatch.addtnlAuthFeeComments"];
            po.UserEnteredAddlAmount = GetNullableBool(form["dispatch.claim.additionalFees.userEnteredAdditionalPayAmount"]);
            po.ParkingGarageDesc = form["dispatch.parkingGarageDesc"];
            po.ReqAdditionalTime = GetNullableInt(form["dispatch.claim.additionalFees.reqAdditionalTime"]);
            po.DamageNotes = form["dispatch.damageNotes"];
            po.DisputeComments = form["dispatch.disputeComments"];
            po.EscalateComments = form["dispatch.escalateComments"];
            po.UpdatedRouteComments = form["dispatch.updatedRouteComments"];

            // Services
            foreach (var serviceParams in GetJsFnParameters(html.DocumentNode.InnerHtml, "addServicesOnload"))
            {
                #region Sample Javascript
                // => On JS Script
                //  function addServicesOnload()
                //    var newService = {
                //      clmSvcId: clmSvcId,                 // param 13
                //      providerRateId: providerRateId,     // param 0
                //      grpSvcCd: groupServiceCd,           // param 1
                //      grpSvcCdDesc: serviceName,          // param 3
                //      svcCd: serviceCd,                   // param 2
                //      svcCount: serviceCount,             // param 4
                //      eqpmntClassId: equipmentClassId,    // param 5
                //      baseUnitAmt: (goaConfirmInd && goaConfirmInd == 'N' && goaInd == "true") ? 0.0 : baseUnitAmount,    // param 11, param 6
                //      equipmentId: equipmentId,           // param 7
                //      svcTypeId: svcTypeId,               // param 8
                //      processStatusId: processStatusId,   // param 9
                //      goaAmt: goaAmt,                     // param 10
                //      goaInd: (goaInd == "true") ? 'Y' : goaInd,     // param 11
                //      isPrimary: isPrimary,               // param 12
                //      saveInd: saveInd                    // param 14
                //  ) {...}
                //
                // => On HTML Contents
                //    addServicesOnload('1767139', 'LO', 'LO', 'Lock-out', '1', '1', '35', '0', '567', '406', '15', 'false', 'true', '38359016', 'N');
                //                          0        1     2         3      4    5     6    7     8      9     10      11      12        13       14
                #endregion

                var svc = new Service();

                svc.ClaimServiceId = serviceParams[13];
                svc.ProviderRateId = serviceParams[0];
                svc.GroupServiceCode = serviceParams[1];
                svc.GroupServiceCodeDesc = serviceParams[3];
                svc.ServiceCode = serviceParams[2];
                svc.ServiceCount = GetNullableInt(serviceParams[4]);
                svc.EquipmentClassId = GetNullableInt(serviceParams[5]);
                svc.EquipmentId = GetNullableInt(serviceParams[7]);
                svc.ServiceTypeId = GetNullableInt(serviceParams[8]);
                svc.ProcessStatusId = GetNullableInt(serviceParams[9]);
                svc.GoaAmount = GetNullableDecimal(serviceParams[10]);
                svc.GoaInd = GetNullableBool(serviceParams[11]);
                svc.IsPrimary = GetNullableBool(serviceParams[12]);
                svc.SaveInd = serviceParams[14];

                svc.BaseUnitAmount = (po.GoaConfirmInd == "N" && svc.GoaInd == true) ? 0 : GetNullableDecimal(serviceParams[6]);

                if (svc.GroupServiceCode == "TOW")
                    svc.ServiceType = ServiceType.TowService;
                else
                    svc.ServiceType = ServiceType.NonTowService;

                svc.PrePopulated = true;
                po.Services.Add(svc);
            }

            // GOA Services
            foreach (var goaParams in GetJsFnParameters(html.DocumentNode.InnerHtml, "validateGOAService"))
            {
                #region Sample Javascript
                //
                // => On HTML Contents
                //    validateGOAService('0', '38.01', 'GAS', '40727443')
                //
                #endregion

                // validateGOAService (reqBase, baseRate, svcCd, clmSvcId) =>
                var reqBase = GetInt(goaParams[0]);
                var baseRate = GetDecimal(goaParams[1]);
                var clmSvcId = goaParams[3];

                // updateGOAinArrayServices() =>
                var svc = po.Services.FirstOrDefault(s => s.ClaimServiceId == clmSvcId);
                if (svc != null)
                {
                    svc.GoaInd = true;
                    svc.BaseUnitAmount = reqBase > 0 ? baseRate : 0;
                }
            }

            // Addresses
            var startIndex = html.DocumentNode.OuterHtml.IndexOf("pushAddress(") + 13;
            while (startIndex > 13)
            {
                var endIndex = html.DocumentNode.OuterHtml.IndexOf(");", startIndex);
                var addr = GetAddress(startIndex, endIndex, html.DocumentNode.OuterHtml);

                // If this is the current version of the address and we don't already have this address type
                if (addr.VersionStatus == "484" && !po.Addresses.Any(a => a.AddressType == addr.AddressType))
                {
                    // Add it
                    po.Addresses.Add(addr);
                }

                startIndex = html.DocumentNode.OuterHtml.IndexOf("pushAddress(", endIndex) + 13;
            }

            // Payments
            po.Payments = new List<Payment>();
            var paymentRows = html.DocumentNode.SelectNodes("//p[text() = 'Payments']").FirstOrDefault().ParentNode.ParentNode.SelectNodes("table//tr");
            if (paymentRows != null)
            {
                for (int i = 2; i < paymentRows.Count - 1; i++)
                {
                    var tds = paymentRows[i].SelectNodes(".//td");
                    if (tds != null && tds.Count > 0)
                    {
                        po.Payments.Add(new Payment()
                        {
                            ServiceDescription = tds[0].InnerText.Trim(),
                            Item = tds[1].InnerText.Trim(),
                            IncludedUnits = GetDecimal(tds[2].InnerText.Trim()),
                            RequestedAmount = GetDecimal(tds[3].InnerText.Trim()),
                            ApprovedAmount = GetDecimal(tds[4].InnerText.Trim()),
                            ExplanationCode = tds[5].InnerText.Trim(),
                            PaidAmount = GetDecimal(tds[6].InnerText.Trim()),
                            PaymentNumber = tds[7].InnerText.Trim(),
                            PaymentDate = GetDateTime(tds[8].InnerText.Trim())
                        });
                    }
                }
            }

            // Comments
            po.Comments = new List<Comment>();
            var commentsDiv = html.DocumentNode.SelectSingleNode("//div[@id='commentsSectionDiv']");
            if (commentsDiv != null && commentsDiv.ChildNodes.Count > 0)
            {
                var cns = commentsDiv.SelectNodes(".//div/ul/li");
                if (cns != null)
                {
                    foreach (var cn in cns)
                    {
                        var commentParts = cn.InnerText.Replace("&nbsp;&nbsp;&nbsp;", "|").Split('|');
                        po.Comments.Add(new Comment()
                        {
                            ProviderId = commentParts[0].Trim(),
                            CreateDate = GetDateTime(commentParts[1].Trim()),
                            Message = commentParts[2].Trim(),
                            PrePopulated = true
                        });
                    }
                }
            }

            // Document ready method calls:
            initElements(logInfo, po);
            validateGASBenefit(logInfo, po);
            validateServiceWeight(logInfo, po);
            disableAllClaimActions(logInfo, html, po);
            validateTowDropOff(logInfo, po);
            // updateAddresesFromArrayToSave();  unneeded - only updates UI if addressesToSaveArray is changed by validateTowDropOff()
            enableDisableFerryChargeField(logInfo, po);
            updateReqFuelCost(po);

            return po;
        }

        private string[][] GetJsFnParameters(string contents, string fnKey)
        {
            var rgx = new Regex(fnKey + @"\(([\w-',\.\s]+)\)", RegexOptions.IgnoreCase);
            var matches = rgx.Matches(contents);

            var result = new string[matches.Count][];
            var i = 0;

            foreach (Match match in matches)
            {
                var res = match.Groups[1].Value.Replace("'", "");
                result[i++] = Regex.Split(res, @",\s*");
            }

            return result;
        }

        private AddressBean GetAddress(int startIndex, int endIndex, string html)
        {
            #region Sample Javascript
            // => On HTML Contents
            //  <script>
            //      pushAddress(
            //          0,                           // index 0:   index
            //          "1",                         // index 1:   order
            //          "",                          // index 2:   bizName
            //          "228 W DICKENS AVE",         // index 3:   addressLine1
            //          "",                          // index 4:   addressLine2
            //          "TULARE",                    // index 5:   city
            //          "CA",                        // index 6:   state
            //          "USA",                       // index 7:   country
            //          "93274",                     // index 8:   zip
            //          "36.234386",                 // index 9:   latitude
            //          "-119.35114",                // index 10:  longitude
            //          "true",                      // index 11:  addressComplete
            //          "",                          // index 12:  addressValidated
            //          "Disablement Pick Up",       // index 13:  addressDescription
            //          12,                          // index 14:  addressType
            //          "Y",                         // index 15:  vehicleInd
            //          0,                           // index 16:  seqNumber
            //          "484",                       // index 17:  versionStatus
            //          "MED",                       // index 18:  loc_confdnc_cd
            //          "W Dickens Ave X N Oaks St", // index 19:  crossStreet
            //          "0",                         // index 20:  estDistncNbr
            //          "0.0",                       // index 21:  estRadiusNbr
            //          "",                          // index 22:  estInitlDistncNbr
            //          "1667",                      // index 23:  locVenderType
            //          "",                          // index 24:  distncVenderType
            //          "",                          // index 25:  distncCalctnType
            //          "",                          // index 26:  phone_nbr
            //          "233",                       // index 27:  addressStatus
            //          "",                          // index 28:  county
            //          "");                         // index 29:  stateTaxId
            //          isAddressComplete(12,0);
            //  </script>
            #endregion

            var jsData = html.Substring(startIndex, endIndex - startIndex);
            var addressParams = jsData.Split(new string[] { "\r\n", "\r", "\n" }, StringSplitOptions.RemoveEmptyEntries);
            for (int i = 0; i < addressParams.Length; i++)
                addressParams[i] = addressParams[i].Trim().Trim(',', '\"');

            var addressBean = new AddressBean()
            {
                Index = GetInt(addressParams[0]),
                Order = GetInt(addressParams[1]),
                BizName = addressParams[2],
                AddressLine1 = addressParams[3],
                AddressLine2 = addressParams[4],
                City = addressParams[5],
                State = addressParams[6],
                Country = addressParams[7],
                Zip = addressParams[8],
                Latitude = GetDecimal(addressParams[9]),
                Longitude = GetDecimal(addressParams[10]),
                AddressComplete = GetBool(addressParams[11]),
                AddressValidated = addressParams[12],
                AddressDescription = addressParams[13],
                AddressType = GetInt(addressParams[14]),
                VehclInd = addressParams[15],
                SeqNumber = addressParams[16],
                VersionStatus = addressParams[17],
                LocConfdncCd = addressParams[18],
                CrossStreet = addressParams[19],
                EstDistncNbr = addressParams[20],
                EstRadiusNbr = addressParams[21],
                EstInitlDistncNbr = addressParams[22],
                LocVenderType = addressParams[23],
                DistncVenderType = addressParams[24],
                DistncCalctnType = addressParams[25],
                PhoneNumber = addressParams[26],
                AddressStatus = addressParams[27],
                County = addressParams[28],
                StateTaxId = addressParams[29],
            };

            return addressBean;
        }

        // SubmitPurchaseOrder() methods

        private string ShortenUrl(LogInfo logInfo, string longUrl)
        {
            return longUrl;
            /*
            // Post to Google Url Shortener API
            var postUrl = $"https://www.googleapis.com/urlshortener/v1/url?key=AIzaSyAGoNOXjeP_pOaIqMJHywziDgk7pjfwJKc";
            var formData = new Dictionary<string, string> { ["longUrl"] = longUrl };

            var log = new AutomatedHttpLog();
            var html = WebRequestHelper.GetHtml(postUrl, formData, ref this.cookieContainer, ref log, false, false, true);
            LogHttpSession(log, logInfo);

            // Parse the JSON response
            var json = html.DocumentNode.OuterHtml;
            if (!string.IsNullOrEmpty(json) && json != "{}")
            {
                var o = JObject.Parse(json);

                // Return the "id" property (it's the shortened url)
                JToken val;
                if (o.TryGetValue("id", out val))
                    return val.ToString();
            }

            return null;*/

        }

        private bool AddFile(string keyName, string fileName, byte[] contents, ref Dictionary<string, WebRequestHelper.FormFieldFile> files)
        {
            //fileName = af.Key.ToLowerInvariant();
            var contentType = "application/octet-stream";

            if (fileName.Contains(".pdf"))
                contentType = "application/pdf";
            else if (fileName.Contains(".tif"))
                contentType = "image/tiff";

            //var keyName = string.Format("additionalFee{0}", j++);

            //if (form.ContainsKey(keyName))
            //    form.Remove(keyName);

            files.Add(keyName, new WebRequestHelper.FormFieldFile()
            {
                Content = contents,
                ContentType = contentType,
                FileName = fileName
            });

            return true;
        }

        private ClaimSummary GetClaimSummary(HtmlDocument html, PurchaseOrder po)
        {
            //-----------------------------
            // Get the claim summary
            //-----------------------------

            var claimSummary = new ClaimSummary();

            // Try and get the dispatch limit
            var div = html.DocumentNode.SelectSingleNode("//span[@class='claimTableHeader']");
            if (div != null)
            {
                claimSummary.DispatchLimit = CleanWhitespace(div.NextSibling.InnerText);
            }

            // Try and get the summary items and total
            var trs = html.DocumentNode.SelectNodes("//table[@class='table']/tbody[1]/tr");
            if (trs != null)
            {
                foreach (var tr in trs)
                {
                    var tds = tr.SelectNodes(".//td");
                    if (tds != null)
                    {
                        // If there are 8 td's, this row is probably a summary item
                        if (tds.Count == 8)
                        {
                            var payment = new ClaimPayment()
                            {
                                ServiceDescription = CleanWhitespace(tds[0].InnerText),
                                Item = CleanWhitespace(tds[1].InnerText),
                            };

                            var units = CleanWhitespace(tds[2].InnerText);
                            if (!string.IsNullOrWhiteSpace(units) && units != "Pending")
                                payment.IncludedUnits = GetNullableDecimal(units);

                            var requested = CleanWhitespace(tds[3].InnerText);
                            if (!string.IsNullOrWhiteSpace(requested) && requested != "Pending")
                                payment.Requested = GetNullableDecimal(requested);

                            var approved = CleanWhitespace(tds[4].InnerText);
                            if (!string.IsNullOrWhiteSpace(approved) && approved != "Pending")
                                payment.Approved = GetNullableDecimal(approved);

                            var paid = CleanWhitespace(tds[5].InnerText);
                            if (!string.IsNullOrWhiteSpace(paid) && paid != "Pending")
                                payment.Paid = GetNullableDecimal(paid);

                            claimSummary.Payments.Add(payment);
                        }

                        // If there are 6 td's, this row is probably the last row showing the Total
                        if (tds.Count == 6)
                        {
                            var total = CleanWhitespace(tds[5].InnerText).Replace("Total", "");
                            if (!string.IsNullOrWhiteSpace(total) && total != "Pending")
                                claimSummary.Total = GetNullableDecimal(total);
                        }
                    }
                }
            }

            //-------------------------------
            // Calculate the total on it
            //-------------------------------

            // If we can't read the calc summary, or it had nothing on it
            if (claimSummary.Payments.Count == 0)
            {
                if (po.Status == "Paid" || po.Status == "Closed")
                    throw new CancelledException($"Status on Allstate site is '{po.Status}'");
                else
                    throw new BillingException("Unable to review the claim prior to submission.  Please try submitting manually.");
            }
            else
            {
                // If there is no Allstate total 
                if (claimSummary.Total == null)
                {
                    claimSummary.Total = 0;

                    foreach (var p in claimSummary.Payments)
                    {
                        // Fuel Charges
                        if (p.Item == "Fuel Charges")
                            claimSummary.Total += p.Paid ?? (p.GetAmount() * po.FuelCost) ?? 0;

                        // Unloaded - Overage
                        else if (p.ServiceDescription == "Unloaded")
                        {
                            claimSummary.Total += p.Paid ?? (Math.Max((p.GetAmount() - po.UnloadedIncluded.GetValueOrDefault()), 0) * po.UnloadedRate) ?? 0;

                            var preUnloaded = claimSummary.Payments.Where(o => o.Item == "Unloaded" && o.GetAmount() > 0).FirstOrDefault();
                            if (preUnloaded != null)
                                claimSummary.Total -= preUnloaded.GetAmount();
                        }

                        // Loaded - Overage
                        else if (p.ServiceDescription == "Loaded")
                        {
                            claimSummary.Total += p.Paid ?? (Math.Max((p.GetAmount() - po.LoadedIncluded.GetValueOrDefault()), 0) * po.LoadedRate) ?? 0;

                            // but we have to take away what we already got paid... 

                            var preLoaded = claimSummary.Payments.Where(o => o.Item == "Loaded" && o.GetAmount() > 0).FirstOrDefault();
                            if (preLoaded != null)
                                claimSummary.Total -= preLoaded.GetAmount();

                        }

                        // Hours/Days - Overage
                        else if (p.ServiceDescription == "Hours")
                            claimSummary.Total += p.Paid ?? (p.GetAmount() * po.HoursUnitRate) ?? 0;

                        // Unloaded - Approved
                        else if (p.Item == "Unloaded")
                        {
                            // If payment has 'Pending' in both Paid and Approved, then fall back on the axiom that Requested - Overage - IncludedUnits = Approved
                            var Overage = claimSummary.Payments.FirstOrDefault(p2 => p2.ServiceDescription == "Unloaded")?.Requested ?? 0;

                            claimSummary.Total += p.Paid ?? (p.Approved ?? Math.Max((p.Requested ?? 0) - Overage - (p.IncludedUnits ?? 0), 0) * po.UnloadedRate) ?? 0;
                        }

                        // Loaded - Approved
                        else if (p.Item == "Loaded")
                        {
                            // If payment has 'Pending' in both Paid and Approved, then fall back on the axiom that Requested - Overage - IncludedUnits = Approved
                            var Overage = claimSummary.Payments.FirstOrDefault(p2 => p2.ServiceDescription == "Loaded")?.Requested ?? 0;

                            claimSummary.Total += p.Paid ?? (p.Approved ?? Math.Max((p.Requested ?? 0) - Overage - (p.IncludedUnits ?? 0), 0) * po.LoadedRate) ?? 0;
                        }

                        // Hours/Days - Approved
                        else if (p.Item == "Hours/Days")
                        {
                            // If payment has 'Pending' in both Paid and Approved, then fall back on the axiom that Requested - Overage - IncludedUnits = Approved
                            var Overage = claimSummary.Payments.FirstOrDefault(p2 => p2.ServiceDescription == "Hours")?.Requested ?? 0;

                            claimSummary.Total += p.Paid ?? (p.Approved ?? Math.Max((p.Requested ?? 0) - Overage - (p.IncludedUnits ?? 0), 0) * po.HoursUnitRate) ?? 0;
                        }
                        else
                            claimSummary.Total += p.GetAmount();
                    }
                }
            }

            return claimSummary;
        }

        // Utility methods / classes

        private string CleanWhitespace(string str)
        {
            if (str == null)
                return "";
            else
                return str
                    .Replace("\t", "")
                    .Replace("\r", "")
                    .Replace("\n", "")
                    .Trim();
        }

        private string CleanString(string str)
        {
            // Coerce all types of "", " ", "null", null, etc to an empty string
            // Otherwise return the value

            if (string.IsNullOrWhiteSpace(str) || str.ToLower().Trim() == "null")
                return "";
            else
                return str;
        }

        private string GetPageId()
        {
            // Translation from preInit function - Allstate engine.js, line 611:
            //    dwr.engine._pageId = dwr.engine.util.tokenify(new Date().getTime()) + "-" + dwr.engine.util.tokenify(Math.random() * 1E16);

            // Get milliseconds, equivalent to javascript:  new Date().getTime()
            var ms = Convert.ToInt64((DateTime.UtcNow - (new DateTime(1970, 1, 1, 0, 0, 0))).TotalMilliseconds);

            // Get random number, equivalent to javascript:  Math.random() * 1E16
            var rand = Convert.ToInt64(new Random().NextDouble() * 10000000000000000);

            // Get tokens
            var pageId = Tokenify(ms) + "-" + Tokenify(rand);

            return pageId;
        }

        private string Tokenify(long number)
        {
            // Translation of tokenify function - Allstate engine.js, line 2520:
            //
            //   tokenify: function(number) {
            //       var tokenbuf = [];
            //       var charmap = "1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ*$";
            //       var remainder = number;
            //       while (remainder > 0)
            //       {
            //           tokenbuf.push(charmap.charAt(remainder & 0x3F));
            //           remainder = Math.floor(remainder / 64); // Can't use shift operator due to 32-bit limit in JS
            //       }
            //     return tokenbuf.join("");
            //   },

            var tokenbuf = new List<char>();
            var charmap = "1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ*$";
            var remainder = number;
            while (remainder > 0)
            {
                tokenbuf.Add(charmap[Convert.ToInt32(remainder & 0x3F)]);
                remainder = Convert.ToInt64(Math.Floor(remainder / 64m));
            }

            return string.Join("", tokenbuf);
        }

        private class ServletRequestParam
        {
            public int Index { get; set; }
            public string Prefix { get; set; }
            public string DataType { get; set; }
            public string Contents { get; set; }

            public ServletRequestParam(int index, string prefix, string datatype, string contents)
            {
                Index = index;
                Prefix = prefix;
                DataType = datatype;
                Contents = contents;
            }
        }

        private class LoginResult
        {
            public string Redirect { get; set; }
            public LoginResultCookie Cookie { get; set; }
            public List<LoginResultTamCookie> TamCookies { get; set; }

            public string Error { get; set; }
        }

        private class LoginResultCookie
        {
            public string Value { get; set; }
            public string Domain { get; set; }
            public string Name { get; set; }
        }

        private class LoginResultTamCookie
        {
            public string Value { get; set; }
            public bool HttpOnly { get; set; }
            public string Domain { get; set; }
            public string Path { get; set; }
            public bool Secure { get; set; }
            public int Version { get; set; }
            public string Name { get; set; }
            public int MaxAge { get; set; }
            public string Comment { get; set; }
        }

        private class ExpiringPurchaseOrderList : List<PurchaseOrderItem>
        {
            public DateTime Expiry { get; } = DateTime.Now.AddMinutes(30);
        }

        #endregion

        #region Allstate Javascript Methods

        // Mileage
        public void UnloadedChanged(LogInfo logInfo, PurchaseOrder po)
        {
            checkForThresholdOverMiles(logInfo, po, "Unloaded");

            // updateMileage() =>
            po.UnloadedAmount = Math.Max(((po.UnloadedRequested ?? 0) - (po.UnloadedIncluded ?? 0)) * (po.UnloadedRate ?? 0), 0);
        }

        public void LoadedChanged(LogInfo logInfo, PurchaseOrder po)
        {
            checkForThresholdOverMiles(logInfo, po, "Loaded");

            // updateMileage() =>
            po.LoadedAmount = Math.Max(((po.LoadedRequested ?? 0) - (po.LoadedIncluded ?? 0)) * (po.LoadedRate ?? 0), 0);
        }

        private void checkForThresholdOverMiles(LogInfo logInfo, PurchaseOrder po, string mileageType)
        {
            var requested = po.UnloadedRequested ?? 0;
            var estimated = po.EstMileageUNL ?? 0;
            var threshold = po.UnloadedThreshold ?? 0;

            if (mileageType == "Loaded")
            {
                requested = po.LoadedRequested ?? 0;
                estimated = po.EstMileageLD ?? 0;
                threshold = po.LoadedThreshold ?? 0;
            }

            if (requested > (estimated + threshold))
            {
                var data = ServletParser.GetValue(GetServletData(logInfo, po, "serviceDetailsBO", "getOverMileThresholdAppCntrValue", new Dictionary<string, string>
                {
                    { "c0-param0", $"string:{getCountryFromPickUPAddress(po)}" }
                }));

                var overMileThreshold = 0m;
                if (data != null && decimal.TryParse(data, out overMileThreshold))
                {
                    if (requested > (estimated + threshold + overMileThreshold))
                    {
                        //-------------------------------------------------------------------
                        // Requested is way more than it should be.
                        // Allstate wants an updated route description AND a mapping link. 
                        //-------------------------------------------------------------------

                        // Get the provider's address
                        if (po.PLEAddress == null)
                        {
                            po.PLEAddress = ServletParser.GetPLEAddress(GetServletData(logInfo, po, "serviceDetailsBO", "getPLE_Address", new Dictionary<string, string>
                            {
                                { "c0-param0", $"string:{ProviderId}" }
                            }));
                        }

                        // If we have a provider addresses, and also pickup/dropoff addresses
                        if (po.PLEAddress != null && po.Addresses.Count > 0)
                        {
                            // Get the long URL of the Google Maps directions from provider to pickup to dropoff
                            var longUrl = $"https://www.google.com/maps/dir/{po.PLEAddress.GetLocationString()}/";
                            foreach (var addr in po.Addresses.OrderBy(a => a.Order))
                                longUrl += $"{addr.GetLocationString()}/";
                            longUrl += "am=t";

                            // If we don't have a map url already, or we're making a different one than we have
                            if (string.IsNullOrWhiteSpace(po.MapURL) || po.MapURL_Long != longUrl)
                            {
                                // Send Allstate the shortened form, and save the long form
                                po.MapURL = ShortenUrl(logInfo, longUrl);
                                po.MapURL_Long = longUrl;
                            }
                        }
                        else
                        {
                            throw new BillingException($"Unable to create a mapping link for {mileageType} miles; please bill manually");
                        }
                    }
                    else
                    {
                        //-------------------------------------------------------------------------------------
                        // Requested is more than estimated, but within limits.
                        // Allstate only wants an updated route description (which we set anyways, below).
                        //-------------------------------------------------------------------------------------
                    }
                }
            }

            // Add this comment anyways
            po.UpdatedRouteComments = "Different route";
        }

        // Services
        public void loadNonTowSvcs(LogInfo logInfo, PurchaseOrder po)
        {
            // If we didn't get the non-tow services already
            if (po.AvailableNonTowServices == null)
            {
                var data = ServletParser.GetNonTowServices(GetServletData(logInfo, po, "serviceDetailsBO", "getNonTowServices", new Dictionary<string, string>
                {
                    { "c0-param0", $"string:{po.PartnerCode}" },
                    { "c0-param1", $"string:{po.SystemAcronymCode}" },
                    { "c0-param2", $"string:{po.ServiceDate:MM/dd/yyyy}" },
                    { "c0-param3", $"string:{po.ProviderId}" },
                    { "c0-param4", $"string:{po.EqClassId}" },
                }));

                if (data != null && data.Count > 0)
                {
                    po.AvailableNonTowServices = data;
                }
            }
        }

        public void loadTowSvcCount(LogInfo logInfo, PurchaseOrder po)
        {
            // If we didn't get the tow types already
            if (po.AvailableTowTypes == null)
            {
                var data = ServletParser.GetList(GetServletData(logInfo, po, "serviceDetailsBO", "getSvcCountTypes", new Dictionary<string, string>
                {
                    { "c0-param0", $"string:{po.EqClassId}" },
                    { "c0-param1", $"string:TOW" },
                    { "c0-param2", $"string:{po.ProviderId}" },
                    { "c0-param3", $"string:{po.ServiceDate:MM/dd/yyyy}" },
                }));

                if (data != null && data.Count > 0)
                {
                    po.AvailableTowTypes = data;
                }
            }
        }

        public void loadEquipmentType(LogInfo logInfo, PurchaseOrder po, int towType)
        {
            // If we didn't get the equipment types already
            if (po.AvailableEqTypes == null)
            {
                var data = ServletParser.GetList(GetServletData(logInfo, po, "serviceDetailsBO", "getEqTypes", new Dictionary<string, string>
                {
                    { "c0-param0", $"string:{towType}" },
                    { "c0-param1", $"string:{po.EqClassId}" },
                    { "c0-param2", $"string:{po.ProviderId}" },
                    { "c0-param3", $"string:{po.ServiceDate:MM/dd/yyyy}" },
                }));

                if (data != null && data.Count > 0)
                {
                    po.AvailableEqTypes = data;
                }
            }
        }

        public Service addNewTowService(LogInfo logInfo, PurchaseOrder po, int towType, int eqType, string itemName)
        {
            var data = ServletParser.GetServiceRates(GetServletData(logInfo, po, "serviceDetailsBO", "getServiceRates", new Dictionary<string, string>
                {
                    { "c0-param0", $"string:{towType}" },
                    { "c0-param1", $"string:{po.ServiceDate:MM/dd/yyyy}" },
                    { "c0-param2", $"string:{po.ProviderId}" },
                    { "c0-param3", $"string:{po.EqClassId}" },
                    { "c0-param4", $"string:{eqType}" },
                    { "c0-param5", $"string:{po.PartnerCode}" },
                    { "c0-param6", $"string:TOW" },
                }));

            if (data != null)
            {
                var ratesObj = data;

                var newService = new Service()
                {
                    ServiceType = ServiceType.TowService,
                    ClaimServiceId = po.Services.Count.ToString(),
                    ProviderRateId = ratesObj.ProviderRateId,
                    GroupServiceCode = "TOW",
                    GroupServiceCodeDesc = ratesObj.ServiceDescription,
                    ServiceCode = ratesObj.ServiceCode,
                    ServiceCount = towType,
                    EquipmentClassId = po.EqClassId,
                    BaseUnitAmount = ratesObj.BaseUnitAmount,
                    EquipmentId = eqType,
                    GoaAmount = ratesObj.GoaUnitAmount,
                    GoaInd = null,
                    SaveInd = "Y",
                    TSTFlag = "false",
                    TowTypeDesc = po.AvailableTowTypes.First(t => t.Key == towType.ToString()).Value,
                    EqTypeDesc = po.AvailableEqTypes.First(t => t.Key == eqType.ToString()).Value,
                };

                po.Services.Add(newService);
                displayRouteOptions(logInfo, po, newService, ratesObj);

                // addComment =>
                po.Comments.Add(new Allstate.Comment()
                {
                    Message = $"Added \"{itemName}\" as new tow service: \"{ratesObj.ServiceDescription}\"",
                    CreateDate = DateTime.Now,
                    PrePopulated = false
                });

                addAddresses(logInfo, po, newService);

                return newService;
            }

            return null;
        }

        public Service loadNewNonTowService(LogInfo logInfo, PurchaseOrder po, Service svc)
        {
            var data = ServletParser.GetList(GetServletData(logInfo, po, "serviceDetailsBO", "getSvcCountTypes", new Dictionary<string, string>
                {
                    { "c0-param0", $"string:{po.EqClassId}" },
                    { "c0-param1", $"string:{svc.GroupServiceCode}" },
                    { "c0-param2", $"string:{po.ProviderId}" },
                    { "c0-param3", $"string:{po.ServiceDate:MM/dd/yyyy}" },
                }));

            if (data != null && data.Count > 0)
            {
                var svcCount = GetInt(data[0].Key);
                return addNewNonTowService(logInfo, po, svc, svcCount);
            }

            return null;
        }

        private Service addNewNonTowService(LogInfo logInfo, PurchaseOrder po, Service svc, int svcCount, int? winchMinutes = null)
        {
            var data = ServletParser.GetServiceRates(GetServletData(logInfo, po, "serviceDetailsBO", "getServiceRates", new Dictionary<string, string>
                {
                    { "c0-param0", $"string:{svcCount}" },
                    { "c0-param1", $"string:{po.ServiceDate:MM/dd/yyyy}" },
                    { "c0-param2", $"string:{po.ProviderId}" },
                    { "c0-param3", $"string:{po.EqClassId}" },
                    { "c0-param4", $"null:null" },
                    { "c0-param5", $"string:{po.PartnerCode}" },
                    { "c0-param6", $"string:{svc.GroupServiceCode}" },
                }));

            if (data != null)
            {
                var ratesObj = data;

                var newService = new Service()
                {
                    ServiceType = ServiceType.NonTowService,
                    ClaimServiceId = po.Services.Count.ToString(),
                    ProviderRateId = ratesObj.ProviderRateId,
                    GroupServiceCode = svc.GroupServiceCode,
                    GroupServiceCodeDesc = svc.GroupServiceCodeDesc,
                    ServiceCode = ratesObj.ServiceCode,
                    ServiceCount = svcCount,
                    EquipmentClassId = po.EqClassId,
                    BaseUnitAmount = ratesObj.BaseUnitAmount,
                    EquipmentId = null,
                    SecondUnitNumber = ratesObj.SecondUnitNumber,
                    SecondIncrementalAmount = ratesObj.SecondIncrementalAmount,
                    GoaAmount = ratesObj.GoaUnitAmount,
                    GoaInd = null,
                    SaveInd = "Y",
                    RequiredMinutes = winchMinutes,
                };

                po.Services.Add(newService);
                displayRouteOptions(logInfo, po, newService, ratesObj);

                // If it was a gas service
                if (newService.GroupServiceCode == "GAS")
                {
                    validateGASBenefit(logInfo, po);
                }

                return newService;
            }

            return null;
        }

        // Addresses
        private class ServField
        {
            public string Name { get; set; }
            public string DataType { get; set; }
            public string Value { get; set; }

            public ServField(string name, string dataType, string value)
            {
                Name = name;
                DataType = dataType;
                Value = value;
            }
        }
        private class ServObject
        {
            public List<ServField> Fields { get; set; } = new List<ServField>();
            public void AddToDict(ref int index, Dictionary<string, string> dict)
            {
                var refKey = $"c0-e{index}";
                var refVal = new StringBuilder();
                index++;
                
                foreach (var f in Fields)
                {
                    dict.Add($"c0-e{index}", $"{f.DataType}:{f.Value}");
                    refVal.Append($"{f.Name}:reference:c0-e{index}, ");
                    index++;
                }

                dict.Add(refKey, "Object_Object:{" + refVal.ToString().TrimEnd(' ', ',') + "}");
            }
        }

        public void validateAddress(LogInfo logInfo, PurchaseOrder po, AddressBean addr)
        {
            // Same as !isCompleteAddress()
            if (!validateAddressBean(addr))
                throw new UserErrorException($"Allstate requires address validation for '{addr.AddressDescription}', but no address was found in Towbook.  Please bill manually.");

            var arrayAddressToValidate = new List<AddressBean>();

            if (po.PLEAddress == null)
                po.PLEAddress = getProviderPLEAddress(logInfo, po, addr);

            if (po.PLEAddress != null)
                arrayAddressToValidate.Add(po.PLEAddress);

            foreach (var a in po.Addresses)
            {
                if (string.IsNullOrWhiteSpace(a.Country))
                    a.Country = a.CountryCd = po.Addresses.First().Country;

                if (validateAddressBean(a))
                    arrayAddressToValidate.Add(a);
            }

            var index = 1;
            var refIndices = "";
            var parameters = new Dictionary<string, string>();
            foreach (var a in arrayAddressToValidate)
            {
                refIndices += $"reference:c0-e{index},";
                var s = new ServObject();

                // If this is the PLE Address
                if (a.AddressType == 7)
                {
                    s.Fields.Add(new ServField("LATITD_NBR",            "string", $"{a.Latitude}"));
                    s.Fields.Add(new ServField("LONGITD_NBR",           "string", $"{a.Longitude}"));
                    s.Fields.Add(new ServField("estimatedLoadedMiles",  "string", ""));
                    s.Fields.Add(new ServField("estimatedULoadedMiles", "string", ""));
                    s.Fields.Add(new ServField("addressLine1",          "string", $"{a.AddressLine1}"));
                    s.Fields.Add(new ServField("addressLine2",          "string", ""));
                    s.Fields.Add(new ServField("country",               "string", $"{a.Country}"));
                    s.Fields.Add(new ServField("state",                 "string", $"{a.State}"));
                    s.Fields.Add(new ServField("city",                  "string", $"{a.City}"));
                    s.Fields.Add(new ServField("zip",                   "string", $"{a.Zip}"));
                    s.Fields.Add(new ServField("phoneNumber",           "string", ""));
                    s.Fields.Add(new ServField("claimID",               "string", $"{po.DispatchId}"));
                    s.Fields.Add(new ServField("addressType",           "string", "7"));
                    s.Fields.Add(new ServField("userID",                "string", $"{po.UserId}"));
                    s.Fields.Add(new ServField("vehclInd",              "string", $"{addr.VehclInd}"));
                    s.Fields.Add(new ServField("vehclId",               "string", $"{po.VehicleId}"));
                    s.Fields.Add(new ServField("orderNumber",           "string", "0"));
                    s.Fields.Add(new ServField("orderNbrForMiles",      "string", "0"));
                    s.Fields.Add(new ServField("stateCd",               "string", $"{a.State}"));
                    s.Fields.Add(new ServField("countryCd",             "string", $"{a.Country}"));
                    s.Fields.Add(new ServField("loc_confdnc_cd",        "null", "null"));
                    s.Fields.Add(new ServField("bizName",               "string", $"{a.BizName}"));
                    s.Fields.Add(new ServField("crossStreet",           "string", ""));
                    s.Fields.Add(new ServField("estDistncNbr",          "string", ""));
                    s.Fields.Add(new ServField("estRadiusNbr",          "string", ""));
                    s.Fields.Add(new ServField("estInitlDistncNbr",     "string", ""));
                    s.Fields.Add(new ServField("locVenderType",         "string", ""));
                    s.Fields.Add(new ServField("distncVenderType",      "string", ""));
                    s.Fields.Add(new ServField("distncCalctnType",      "string", ""));
                    s.Fields.Add(new ServField("geoCodeRouteType",      "string", ""));
                    s.Fields.Add(new ServField("geoCodeDesc",           "string", ""));
                    s.Fields.Add(new ServField("driveMiles",            "string", ""));
                    s.Fields.Add(new ServField("crowMiles",             "string", ""));
                    s.Fields.Add(new ServField("addressStatus",         "string", ""));
                    s.Fields.Add(new ServField("validationFlag",        "string", ""));
                }
                else
                {
                    s.Fields.Add(new ServField("LATITD_NBR",            "string", $"{a.Latitude}"));
                    s.Fields.Add(new ServField("LONGITD_NBR",           "string", $"{a.Longitude}"));
                    s.Fields.Add(new ServField("estimatedLoadedMiles",  "string", ""));
                    s.Fields.Add(new ServField("estimatedULoadedMiles", "string", ""));
                    s.Fields.Add(new ServField("addressLine1",          "string", $"{a.AddressLine1}"));
                    s.Fields.Add(new ServField("addressLine2",          "string", $"{a.AddressLine2}"));
                    s.Fields.Add(new ServField("country",               "string", $"{a.Country}"));
                    s.Fields.Add(new ServField("state",                 "string", $"{a.State}"));
                    s.Fields.Add(new ServField("city",                  "string", $"{a.City}"));
                    s.Fields.Add(new ServField("zip",                   "string", $"{a.Zip}"));
                    s.Fields.Add(new ServField("phoneNumber",           "string", ""));
                    s.Fields.Add(new ServField("claimID",               "null", "null"));
                    s.Fields.Add(new ServField("addressType",           "number", $"{a.AddressType}"));
                    s.Fields.Add(new ServField("userID",                "string", $"{po.UserId}"));
                    s.Fields.Add(new ServField("vehclInd",              "string", $"{addr.VehclInd}"));
                    s.Fields.Add(new ServField("vehclId",               "string", $"{po.VehicleId}"));
                    s.Fields.Add(new ServField("orderNumber",           "string", $"{a.Order}"));
                    s.Fields.Add(new ServField("stateCd",               "string", $"{a.State}"));
                    s.Fields.Add(new ServField("countryCd",             "string", $"{a.Country}"));
                    s.Fields.Add(new ServField("bizName",               "string", ""));
                    s.Fields.Add(new ServField("crossStreet",           "string", ""));
                    s.Fields.Add(new ServField("estDistncNbr",          "string", $"{a.EstDistncNbr}"));
                    s.Fields.Add(new ServField("estRadiusNbr",          "string", $"{a.EstRadiusNbr}"));
                    s.Fields.Add(new ServField("estInitlDistncNbr",     "string", $"{a.EstInitlDistncNbr}"));
                    s.Fields.Add(new ServField("locVenderType",         "string", $"{a.LocVenderType}"));
                    s.Fields.Add(new ServField("distncVenderType",      "string", $"{a.DistncVenderType}"));
                    s.Fields.Add(new ServField("distncCalctnType",      "string", $"{a.DistncCalctnType}"));
                    s.Fields.Add(new ServField("geoCodeRouteType",      "string", ""));
                    s.Fields.Add(new ServField("geoCodeDesc",           "string", ""));
                    s.Fields.Add(new ServField("driveMiles",            "string", ""));
                    s.Fields.Add(new ServField("crowMiles",             "string", ""));
                    s.Fields.Add(new ServField("addressStatus",         "string", ""));
                    s.Fields.Add(new ServField("validationFlag",        "string", ""));
                    s.Fields.Add(new ServField("stateTaxId",            "string", $"{a.StateTaxId}"));
                }

                s.AddToDict(ref index, parameters);
            }

            parameters.Add("c0-param0", $"array:[{refIndices.TrimEnd(',')}]");
            parameters.Add("c0-param1", $"string:{po.ServiceDate:MM/dd/yyyy}");

            var addrTypes = arrayAddressToValidate.Select(a => a.AddressType).ToArray();
            var validatedAddresses = new Dictionary<int, AddressBean>();
            var validatedAddr = new AddressBean();

            // Try to validate the address up to 4 times (Allstate occasionally rejects validation, though the data posted remains identical)
            var tries = 0;
            do
            {
                try
                {
                    tries++;
                    validatedAddresses = ServletParser.GetValidatedAddresses(addrTypes, GetServletData(logInfo, po, "addressBO", "validateAddresses", parameters));
                    validatedAddr = validatedAddresses[addr.AddressType];

                    // If it's a cross border claim
                    if (validatedAddr?.CrossBorderClaim == true)
                        throw new UserErrorException($"Allstate rejected the address for '{addr.AddressDescription}', saying \"Cross border claims are not allowed.\"  Please bill manually.");

                    // If address validation failed
                    if (validatedAddr == null || validatedAddr.AddressValidation == false)
                        throw new UserErrorException($"Allstate rejected the address for '{addr.AddressDescription}'.  Please bill manually.");

                    break;
                }
                catch
                {
                    if (tries < 4)
                        logInfo.AddEvent($"Retrying address validation for {addr.AddressDescription}, attempt #{tries} failed", LogLevel.Warn);
                    else
                        throw;
                }
            }
            while (tries < 4);

            // Update from validated
            addr.AddressLine1 = CleanString(validatedAddr.AddressLine1);
            addr.City = CleanString(validatedAddr.City);
            addr.Zip = CleanString(validatedAddr.Zip);
            addr.State = CleanString(validatedAddr.State);
            addr.Country = addr.CountryCd = CleanString(validatedAddr.Country);
            addr.Latitude = validatedAddr.Latitude;
            addr.Longitude = validatedAddr.Longitude;
            addr.LocConfdncCd = CleanString(validatedAddr.LocConfdncCd);
            addr.CrossStreet = CleanString(validatedAddr.CrossStreet);
            addr.LocVenderType = CleanString(validatedAddr.LocVenderType);
            addr.DistncVenderType = CleanString(validatedAddr.DistncVenderType);
            addr.DistncCalctnType = CleanString(validatedAddr.DistncCalctnType);
            addr.EstDistncNbr = CleanString(validatedAddr.EstDistncNbr);
            addr.EstRadiusNbr = CleanString(validatedAddr.EstRadiusNbr);
            addr.AddressComplete = true;
            addr.AddressValidated = "TRUE";
            addr.County = CleanString(validatedAddr.County);
            addr.StateTaxId = CleanString(validatedAddr.StateTaxId);
            addr.AddressValidation = validatedAddr.AddressValidation; // I added this so we'd know to post it

            setMileage(logInfo, po, validatedAddresses);

            // Get vehicle indicator
            addr.VehclInd = ServletParser.GetValue(GetServletData(logInfo, po, "serviceDetailsBI", "lookupVehicleIndicatorForAddressType", new Dictionary<string, string>
            {
                { "c0-param0", $"number:{addr.AddressType}" }
            }));
            if (string.IsNullOrWhiteSpace(addr.VehclInd))
                addr.VehclInd = "N";
        }

        private bool validateAddressBean(AddressBean a)
        {
            if (a.Latitude != 0 && a.Longitude != 0)
                return true;

            if ((!string.IsNullOrWhiteSpace(a.AddressLine1) && !string.IsNullOrWhiteSpace(a.State) && !string.IsNullOrWhiteSpace(a.City)) 
                || (!string.IsNullOrWhiteSpace(a.AddressLine1) && !string.IsNullOrWhiteSpace(a.Zip))) {
                return true;
            }

            return false;
        }

        private void setMileage(LogInfo logInfo, PurchaseOrder po, Dictionary<int, AddressBean> validatedAddresses)
        {
            var unloadedMiles = validatedAddresses.ContainsKey(7) ? GetNullableDecimal(validatedAddresses[7].EstDistncNbr) : null;
            if (unloadedMiles != null)
            {
                po.UnloadedRequested = unloadedMiles;
                po.EstMileageUNL = unloadedMiles;
                UnloadedChanged(logInfo, po);
            }

            // Loaded from Pickup
            var loadedMilesPickup = validatedAddresses.ContainsKey(12) ? GetNullableDecimal(validatedAddresses[12].EstDistncNbr) : null;
            // Loaded from Storage
            var loadedMilesStorage = validatedAddresses.ContainsKey(660) ? GetNullableDecimal(validatedAddresses[660].EstDistncNbr) : null;
            var loadedMilesNbr = 0m;
            if (loadedMilesPickup != null)
            {
                if (loadedMilesStorage != null)
                    loadedMilesNbr = (loadedMilesPickup ?? 0) + (loadedMilesStorage ?? 0);
                else
                    loadedMilesNbr = (loadedMilesPickup ?? 0);
            }
            var loadedMilesTotal = loadedMilesNbr > 0 ? Math.Round(loadedMilesNbr, 1) : 0;
            po.LoadedRequested = loadedMilesTotal;
            po.EstMileageLD = loadedMilesTotal;
            LoadedChanged(logInfo, po);
        }

        public AddressBean getProviderPLEAddress(LogInfo logInfo, PurchaseOrder po, AddressBean validating)
        {
            var pleAddress = ServletParser.GetPLEAddress(GetServletData(logInfo, po, "serviceDetailsBO", "getPLE_Address", new Dictionary<string, string>
            {
                { "c0-param0", $"string:{ProviderId}" }
            }));

            if (pleAddress != null)
            {
                pleAddress.ClaimID = po.DispatchId.ToString();
                pleAddress.UserID = po.UserId;
                pleAddress.VehclInd = validating.VehclInd;
                pleAddress.VehclId = po.VehicleId;
                pleAddress.Order = 0;
                pleAddress.OrderNbrForMiles = "0";
            }

            return pleAddress;
        }

        private void addAdress(LogInfo logInfo, PurchaseOrder po, int addrType)
        {
            // If its a dropoff type and we don't have one already, add it
            if (po.IsDropOff(addrType) && !po.Addresses.Any(a => a.AddressType == addrType))
            {
                addAddressIntoPanel(logInfo, po, addrType, po.addressTypeDescriptions[addrType], "", "", "", "", "", "", "", false);
            }

            // If its a disablement pickup, add it
            else if (addrType == 12)
            {
                addAddressIntoPanel(logInfo, po, addrType, po.addressTypeDescriptions[addrType], "", "", "", "", "", "", "", false);
            }

            // If its a disablement storage
            else if (addrType == 660)
            {
                //serviceDetailsBO.getPLE_Address($('#providerId').val(), function (data) {
                //    addAddressIntoPanel(addrType, addrDescriptions.disablementStorage, data['biz_name'], data['line1_addr'], data['city_name'], data['st_cd'], data['zip_cd'], data['latitd_nbr'], data['longitd_nbr'], true);
                //    pushAddressToSave(addrType, '233', '3', data['biz_name'], data['line1_addr'], data['line2_addr'], data['city_name'], data['st_cd'], data['zip_cd'], data['cntry_cd'], data['phone_nbr'], data['latitd_nbr'], data['longitd_nbr'], data['LOC_CONFDNC_CD'], '', '', '', '1665', '1665', '2386', addrDescriptions.disablementStorage, '', true, '', '');
                //});
            }
        }

        private void addAddressIntoPanel(LogInfo logInfo, PurchaseOrder po, int addrType, string addrDesc, string biz_name, string line1_addr, string city, string state, string zipCd, string latitude, string longitude, bool addressComplete)
        {
            // This value would come from $('#vehicleIndUpdate').val() which stores the vehicleInd from the last call to pushAddress(), or from loadUpdateAddress()
            // which is a UI method we don't implement here that is called just prior to validating an address.  Since we add tow services before we validate them, 
            // at this point in the code the vehicleInd would be the address with the highest index, from the last call to pushAddress().
            var vehicleInd = po.Addresses.OrderByDescending(a => a.Index).FirstOrDefault()?.VehclInd ?? "";

            var addressBean = new AddressBean()
            {
                Index = po.Addresses.Count,
                Order = po.Addresses.Max(a => a.Order) + 1, // getOrderNumberForNewAddress()
                BizName = biz_name,
                AddressLine1 = line1_addr,
                AddressLine2 = "",
                City = city,
                State = state,
                Country = "",
                Zip = zipCd,
                Latitude = GetDecimal(latitude),
                Longitude = GetDecimal(longitude),
                AddressComplete = addressComplete,
                AddressValidated = "false",
                AddressDescription = addrDesc,
                AddressType = addrType,
                VehclInd = vehicleInd,
                SeqNumber = "0",
                VersionStatus = "485",
                LocConfdncCd = "",
                CrossStreet = "",
                EstDistncNbr = "",
                EstRadiusNbr = "",
                EstInitlDistncNbr = "",
                LocVenderType = "",
                DistncVenderType = "",
                DistncCalctnType = "",
                PhoneNumber = "",
                AddressStatus = "233",
                County = "",
                StateTaxId = "",
            };

            po.Addresses.Add(addressBean);
        }

        private string getCountryFromPickUPAddress(PurchaseOrder po)
        {
            return po.Addresses.FirstOrDefault(a => a.AddressType == 12)?.Country ?? "";
        }

        private void addAddresses(LogInfo logInfo, PurchaseOrder po, Service newService)
        {
            // !existPickup()
            if (!po.Addresses.Any(a => po.IsPickup(a)))
            {
                addAdress(logInfo, po, 12);
            }

            validateTowDropOff(logInfo, po);

            //if (newService.svcCount == '3')
            //{
            //    if (!existStorage())
            //    {
            //        addAdress(addressTypes.disablementStorage);
            //    }
            //}
        }

        // Form load
        private void initElements(LogInfo logInfo, PurchaseOrder po)
        {
            // if hasActiveTwoWayTow() =>
            if (po.Services.Any(s => s.ServiceCode == "STW" && s.ProcessStatusId != 410))
                po.TowServiceIsEditable = false;
            else
                po.TowServiceIsEditable = true;

            // verifyActiveRouteOptions() =>
            foreach (var svc in po.Services.Where(s => s.ProcessStatusId != 410))
                displayRouteOptions(logInfo, po, svc, null);
        }

        private void validateGASBenefit(LogInfo logInfo, PurchaseOrder po)
        {
            var data = ServletParser.GetGasBenefits(GetServletData(logInfo, po, "serviceDetailsBO", "hasGASBenefit", new Dictionary<string, string>
            {
                { "c0-param0", $"string:{po.AuthId}" }
            }));

            if (data != null)
            {
                if (po.FuelGalAuth == null)
                {
                    var country = getCountryFromPickUPAddress(po);

                    if (country == "USA")
                        po.FuelGalAuth = data.USBenefit;

                    else if (country == "CAN")
                        po.FuelGalAuth = data.CABenefit;
                }

                //if ($('#isUserEnteredFuelCost').val() == "true" || ((hasGASServices(po) || ($("#FuelGalAuth").val() != "" && paidFuel ))&& $('#fuelCost').val() == "" && $('#clmStatus').val() == '402')) {
                //    $('#fuelCost').attr('readonly', false);
                //}
            }
            else
            {
                po.FuelCost = null;
                po.FuelGalAuth = null;
            }
        }

        private void validateServiceWeight(LogInfo logInfo, PurchaseOrder po)
        {
            if (!string.IsNullOrWhiteSpace(po.PrimarySvcCode))
            {

                var data = ServletParser.GetList(GetServletData(logInfo, po, "serviceDetailsBO", "getEqClassFromRatesTable", new Dictionary<string, string>
                {
                    { "c0-param0", $"string:{po.ProviderId}" },
                    { "c0-param1", $"string:{po.PrimarySvcCode}" }
                }));

                if (data != null && data.Count > 0)
                {
                    po.EqClassId = GetNullableInt(data[0].Key);
                    po.ServiceWeight = data[0].Value;

                    // We'll call loadNonTowSvcs() later, if we're adding a non-tow service
                }
            }
        }

        private void disableAllClaimActions(LogInfo logInfo, HtmlDocument html, PurchaseOrder po)
        {
            if (po.DuplicateClmInd == "true" || po.ClmStatus != "402")
            {
                po.DisableAllClaimActions = true;

                po.UnloadedIsEditable = false;
                po.LoadedIsEditable = false;
                po.TowServiceIsEditable = false;
                po.NonTowServiceIsEditable = false;
                po.AdditionalAmountIsEditable = false;

                // Buttons
                po.DisabledElements.Add("showNonTowSvcModal");
                po.DisabledElements.Add("showTowSvcModal");
                po.DisabledElements.Add("amendRouteBtn");
                po.DisabledElements.Add("updateRouteRemoveBtn");
                po.DisabledElements.Add("addFileAddtnl");
                po.DisabledElements.Add("additionalFee1");
                po.DisabledElements.Add("clearAddtnl");
                po.DisabledElements.Add("addFileDamage");
                po.DisabledElements.Add("damage1");
                po.DisabledElements.Add("clearDamagel");
                po.DisabledElements.Add("postComment");
                html.DocumentNode.SelectNodes("//*[starts-with(@id, 'updateAddress_')]")?.ToList().ForEach(n =>
                { po.DisabledElements.Add(n.Id); });
                // Inputs
                po.DisabledElements.Add("provdrInvoice");
                po.DisabledElements.Add("plateNbr");
                po.DisabledElements.Add("estimatedUnloadedMiles");
                po.DisabledElements.Add("damageNotes");
                po.DisabledElements.Add("ferryCharge");
                po.DisabledElements.Add("paymntFromCust");
                po.DisabledElements.Add("tollsAmt");
                po.DisabledElements.Add("addtnlApprovedAmt");
                po.DisabledElements.Add("addtnlAuthFeeComments");
                po.DisabledElements.Add("commentsLogTextArea");
                po.DisabledElements.Add("damageNotes");
                html.DocumentNode.SelectNodes("//input[@type='radio']")?.ToList().ForEach(n =>
                { po.DisabledElements.Add(n.Id ?? n.Attributes["class"].Value); });
                po.DisabledElements.Add("btn-goa");
                po.DisabledElements.Add("btn-remove");
            }

            // Disable Update Button for Storage Addresses
            html.DocumentNode.SelectNodes("//*[starts-with(@id, 'updateAddress_660')]")?.ToList().ForEach(n =>
            { po.DisabledElements.Add(n.Id); });

            // Disable Merlin Supplement Amount if it comes from MMA
            if (po.AdditionalAmount > 0)
            {
                po.AdditionalAmountIsEditable = false;
                po.PreLoadedAdditionalAmount = po.AdditionalAmount.Value;
                po.AdditionalAmount = 0;
            }
        }

        private void validateTowDropOff(LogInfo logInfo, PurchaseOrder po)
        {
            if (hasTowServices(po))
            {
                foreach (var pickupType in po.Addresses.Where(a => po.IsPickup(a)).Select(a => a.AddressType).ToArray())
                {
                    // If this pickup address does not have a corresponding dropoff address
                    var dropOffType = po.pickUpToDropOffAddressMap[pickupType];
                    if (!po.Addresses.Any(a => a.AddressType == dropOffType))
                    {
                        // Add it
                        addAdress(logInfo, po, dropOffType);
                    }
                }
            }
        }

        private void enableDisableFerryChargeField(LogInfo logInfo, PurchaseOrder po)
        {
            if (po.PrimarySvcCode != null && !po.DisabledElements.Contains("ferryCharge"))
            {
                var data = ServletParser.GetValue(GetServletData(logInfo, po, "serviceDetailsBO", "hasFerryCharges", new Dictionary<string, string>
                {
                    { "c0-param0", $"string:{po.PrimarySvcCode}" }
                }));

                if (string.IsNullOrWhiteSpace(data))
                {
                    po.DisabledElements.Add("ferryCharge");
                }
            }
        }

        public void displayRouteOptions(LogInfo logInfo, PurchaseOrder po, Service svc, ServiceRates ratesObj = null)
        {
            if (svc.GroupServiceCode == "TOW")
            {
                // Tow service - unhide loaded and unloaded
                if (!po.UnloadedIsEditable)
                {
                    if (svc.IsPrimary == false && ratesObj != null)
                    {
                        po.UnloadedEstimated = 0;
                        po.EstMileageUNL = po.UnloadedEstimated;
                        po.UnloadedIncluded = ratesObj.UnloadedFree;
                        po.UnloadedThreshold = ratesObj.UnloadedThreshold;
                        po.UnloadedRate = ratesObj.UnloadedRate;
                        po.UnloadedRequested = 0;
                        UnloadedChanged(logInfo, po);
                    }
                    po.UnloadedIsEditable = true;
                }
                if (!po.LoadedIsEditable)
                {
                    if (svc.IsPrimary == false && ratesObj != null)
                    {
                        po.LoadedEstimated = (po.LoadedEstimated ?? 0);
                        po.EstMileageLD = po.LoadedEstimated;
                        po.LoadedIncluded = ratesObj.LoadedFree;
                        po.LoadedThreshold = ratesObj.LoadedThreshold;
                        po.LoadedRate = ratesObj.LoadedRate;
                        po.LoadedRequested = po.LoadedEstimated;
                        LoadedChanged(logInfo, po);
                    }
                    po.LoadedIsEditable = true;
                }
            }
            else
            {
                // Non-tow service - unhide just unloaded
                if (!po.UnloadedIsEditable)
                {
                    if (svc.IsPrimary == false && ratesObj != null)
                    {
                        po.UnloadedEstimated = 0;
                        po.EstMileageUNL = po.UnloadedEstimated;
                        po.UnloadedIncluded = ratesObj.UnloadedFree;
                        po.UnloadedThreshold = ratesObj.UnloadedThreshold;
                        po.UnloadedRate = ratesObj.UnloadedRate;
                        po.UnloadedRequested = 0;
                        UnloadedChanged(logInfo, po);
                    }
                    po.UnloadedIsEditable = true;
                }
            }

            //if ($.inArray(svcCd, serviceCodesWithTimeUnits) >= 0) 
            //{
            //    $('[id^=hrsDiv]').each(function() {
            //        $(this).removeClass('hide');
            //    });
            //}
        }

        private void updateReqFuelCost(PurchaseOrder po)
        {
            if (po.FuelCost > 0)
            {
                if (po.FuelGalAuth > 0)
                    po.ReqFuelCost = po.FuelCost * po.FuelGalAuth;
                else
                    po.ReqFuelCost = po.FuelCost;
            }
            else if (po.FuelCost == null)
            {
                po.ReqFuelCost = 0;
            }
        }

        private bool hasGASServices(PurchaseOrder po)
        {
            return po.Services.Any(s => s.GroupServiceCode == "GAS");
        }

        private bool hasTowServices(PurchaseOrder po)
        {
            return po.Services.Any(s => s.GroupServiceCode == "TOW" && s.ProcessStatusId != 410);
        }

        #endregion
    }

    public static class ExtensionMethods
    {
        public static string Map(this string str, string overrides)
        {
            // Prototype: "False:N, True:Y"
            var array = overrides.Split(',');
            if (array.Length > 0)
            {
                foreach (var p in array)
                {
                    // Prototype: "False:N"
                    var pair = p.Split(':');
                    if (pair.Length == 2 && str == pair[0])
                    {
                        return pair[1];
                    }
                }
            }

            return str;
        }
    }
}
