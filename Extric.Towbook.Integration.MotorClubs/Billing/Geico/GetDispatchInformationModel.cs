using Extric.Towbook.Integration.MotorClubs.Billing.Geico;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.Integration.MotorClubs.Billing.Geico
{
    public class GetDispatchInformationModel
    {
        public int Code { get; set; }
        public string Message { get; set; }
        public GetDispatchInformationResultModel Result { get; set; }
        public bool IsSuccessful { get; set; }
    }

    public class GetDispatchInformationResultModel
    {
        public InvoiceList[] InvoiceList { get; set; }
        public InvoiceTypeList InvoiceTypeList { get; set; }
        public DispatchRates DispatchRates { get; set; }
        public ErsTaxPrompt ErsTaxPrompt { get; set; }
    }

    public class InvoiceTypeList
    {
        public InvoiceTypeListType[] Types { get; set; }
        public InvoiceTypeListCharge[] Charges { get; set; }
        public InvoiceTypeListService[] Services { get; set; }
        public InvoiceTypeListMileage[] Mileages { get; set; }
        public Fee[] Fees { get; set; }
    }

    public class InvoiceTypeListType
    {
        public string Value { get; set; }
        public string Text { get; set; }
        public string TaxCode { get; set; }
        public string FriendlyText { get; set; }
        public string Type { get; set; }
        public string ParentCode { get; set; }
    }

    public class InvoiceTypeListCharge
    {
        public string Value { get; set; }
        public string Text { get; set; }
        public string TaxCode { get; set; }
        public string FriendlyText { get; set; }
        public string Type { get; set; }
        public string ParentCode { get; set; }
    }

    public class InvoiceTypeListService
    {
        public string Value { get; set; }
        public string Text { get; set; }
        public string FriendlyText { get; set; }
        public string TaxCode { get; set; }
        public string Type { get; set; }
        public string ParentCode { get; set; }
    }

    public class InvoiceTypeListMileage
    {
        public string Value { get; set; }
        public string Text { get; set; }
        public string FriendlyText { get; set; }
        public string TaxCode { get; set; }
        public string Type { get; set; }
        public string ParentCode { get; set; }
    }

    public class Fee
    {
        public string Value { get; set; }
        public string Text { get; set; }
        public string FriendlyText { get; set; }
        public string TaxCode { get; set; }
        public string Type { get; set; }
        public string ParentCode { get; set; }
    }

    public class DispatchRates
    {
        public ServiceRate[] ServiceRates { get; set; }
        public AddonRate[] AddonRates { get; set; }
    }

    public class ServiceRate
    {
        public string ServiceType { get; set; }
        public string VehicleType { get; set; }
        public string RateType { get; set; }
        public GeicoMileageItem LoadedFreeMileage { get; set; }
        public GeicoMileageItem EnrouteFreeMileage { get; set; }
        public GeicoMileageItem DeadHeadMileageRate { get; set; }
        public decimal Rate { get; set; }
    }

    public class GeicoMileageItem
    {
        public int Miles { get; set; }
        public decimal? Rate { get; set; }
    }

    public class AddonRate
    {
        public string AddonType { get; set; }
        public decimal? Rate { get; set; }
    }

    public class ErsTaxPrompt
    {
        public string FeeTaxPromptMessage { get; set; }
        public string FeeTaxStatus { get; set; }
    }

    public class GeicoInvoice
    {
        public string InvoiceStatus { get; set; }
        public string Version { get; set; }

        public InvoiceDetails InvoiceDetails { get; set; }
        public List<Primaryservicedetail> PrimaryServiceDetails { get; set; }
        public List<Mileagedetail> MileageDetails { get; set; }
        public object[] PayoutFeesReceiptDocuments { get; set; }
        public PayoutFee[] PayoutFees { get; set; }
        public decimal PaidTotal { get; set; }
        public decimal PendingTotal { get; set; }
        public decimal PaidTax { get; set; }
        public decimal PendingTax { get; set; }
        
        public decimal TotalInvoiceAmount { get; set; }
        public string Status { get; set; }

        public bool IsSecondaryInvoice { get; set; }
        public bool IsRecommended { get; set; }
        public bool IsGOA { get; set; }

        [JsonProperty("atlasCategory")]
        public string AtlasCategory { get; set; }

        /// <summary>
        /// Used by Towbook internally - NOT to be submitted to the motor club.
        /// </summary>
        [JsonIgnore]
        public decimal ExpectedTotal { get; set; }

        /// <summary>
        /// Used by Towbook internally - NOT to be submitted to the motor club.
        /// </summary>
        [JsonIgnore]
        public decimal TowbookTotal { get; set; }
    }


    public class InvoiceList : GeicoInvoice
    {
        public int InvoiceCategory { get; set; }

        public object FriendlyInvoiceCategory { get; set; }
        public string SecondaryMessage { get; set; }
        public bool HasMileage { get; set; }
        public bool HasTow { get; set; }
        public bool CanEdit { get; set; }

        public bool IsAccepted { get; set; }
        public bool IsFinal { get; set; }

        public decimal PaidAmount { get; set; }
        public string FriendlyStatus { get; set; }
    }

    /// <summary>
    /// Used for RecalculateInvoice and GetDispatchInformation
    /// </summary>
    public class InvoiceDetails
    {
        [JsonProperty("dispatchNumber")]
        public string DispatchNumber { get; set; }
        [JsonProperty("customerName")]
        public string CustomerName { get; set; }
        [JsonProperty("claimNumber")]
        public string ClaimNumber { get; set; }
        [JsonProperty("vehicle")]
        public string Vehicle { get; set; }
        [JsonProperty("serviceDate")]
        public string ServiceDate { get; set; }

        [JsonProperty("lossLocation")]
        public string LossLocation { get; set; }
        
        [JsonProperty("serviceType")]
        public string ServiceType { get; set; }

        [JsonProperty("serviceCodes")]
        public string[] ServiceCodes { get; set; }

        [JsonProperty("providerReference")]
        public object ProviderReference { get; set; }

        [JsonProperty("invoiceType")]
        public string InvoiceType { get; set; }

        
        [JsonProperty("currentVehicleLocation")]
        [JsonIgnore]
        public GeicoLocation CurrentVehicleLocation { get; set; }

        [JsonProperty("destinationAddress")]
        [JsonIgnore]
        public GeicoLocation DestinationAddress { get; set; }
    }

    public class GeicoLocation
    {
        [JsonProperty("streetAddress1")]
        public string StreetAddress1 { get; set; }
        [JsonProperty("streetAddress2")]
        public string StreetAddress2 { get; set; }
        [JsonProperty("country")]
        public string Country { get; set; }
        [JsonProperty("state")]
        public string State { get; set; }
        [JsonProperty("county")]
        public string County { get; set; }
        [JsonProperty("city")]
        public string City { get; set; }
        [JsonProperty("zipCode")]
        public Zipcode ZipCode { get; set; }
        [JsonProperty("latitude")]
        public object Latitude { get; set; }
        [JsonProperty("longitude")]
        public object Longitude { get; set; }
        [JsonProperty("cassCode")]
        public object CassCode { get; set; }
    }

    public class Zipcode
    {
        [JsonProperty("zip")]
        public string Zip { get; set; }
        [JsonProperty("extension")]
        public object Extension { get; set; }
    }

    public class Primaryservicedetail
    {
        [JsonProperty("id")]
        public string id { get; set; }
        [JsonProperty("service")]
        public string Service { get; set; }
        [JsonProperty("amount")]
        public decimal Amount { get; set; }
        [JsonProperty("taxable")]
        public string Taxable { get; set; }
        [JsonProperty("haveAuditIssues")]
        public bool HaveAuditIssues { get; set; }
        [JsonProperty("note")]
        public string Note { get; set; }
        [JsonProperty("additionalCharges")]
        public List<AdditionalCharge> AdditionalCharges { get; set; } = new List<AdditionalCharge>();
        [JsonProperty("hours")]
        public int Hours { get; set; }
        [JsonProperty("minutes")]
        public float Minutes { get; set; }
        [JsonProperty("amountType")]
        public int AmountType { get; set; }
        [JsonProperty("goa")]
        public bool Goa { get; set; }
        [JsonProperty("rate")]
        public float? Rate { get; set; }

        [JsonProperty("calcType")]
        public int? CalcType { get; set; }
        [JsonProperty("uniqueId")]
        public string UniqueId { get; set; }
        [JsonProperty("atlasStatus")]
        public string AtlasStatus { get; set; }
        [JsonProperty("state")]
        public int? State { get; set; }
        [JsonProperty("amountChanged")]
        public bool AmountChanged { get; set; }
        [JsonProperty("comments")]
        public string Comments { get; set; }
        [JsonProperty("declineReason")]
        public string DeclineReason { get; set; }
        [JsonProperty("isDeclined")]
        public bool IsDeclined { get; set; }
        [JsonProperty("isCompromise")]
        public bool IsCompromise { get; set; }
        [JsonProperty("isTaxManual")]
        public bool IsTaxManual { get; set; }

        [JsonProperty("taxAmount")]
        public float? TaxAmount { get; set; }
        [JsonProperty("taxRate")]
        public float? TaxRate { get; set; }
        [JsonProperty("isTaxable")]
        public bool IsTaxable { get; set; }

        [JsonProperty("auditIssues")]
        public object[] AuditIssues { get; set; }
        [JsonProperty("friendlyStatus")]
        public string FriendlyStatus { get; set; }
        [JsonProperty("status")]
        public string Status { get; set; }
        [JsonProperty("hasFeedback")]
        public bool HasFeedback { get; set; }

        [JsonProperty("hasAuditIssues")]
        public bool HasAuditIssues { get; set; }

        // match reactjs code from client page.. this isnt part of the received model, so these will always be false
        //public bool isLabel { get; set; }
        //public bool isButton { get; set; }

        /// <summary>
        /// Towbook Internal
        /// </summary>
        [JsonIgnore]
        public bool MatchFound { get; set; }


       
        public bool hasChangesMade { get; set; }
        public string auditStatus { get; set; }

        public string auditMessage { get; set; }
    }

    public interface IAdditionalCharge
    {
        decimal? amount { get; set; }
        int amountType { get; set; }
        object atlasStatus { get; set; }
        object[] auditIssues { get; set; }
        string auditMessage { get; set; }
        string calcType { get; set; }
        string charge { get; set; }
        int? days { get; set; }
        string declineReason { get; set; }
        string feeType { get; set; }
        string friendlyStatus { get; set; }
        bool hasAuditIssues { get; set; }
        bool hasChangesMade { get; set; }
        bool hasFeedback { get; set; }
        bool haveAuditIssues { get; set; }
        int? hours { get; set; }
        string id { get; set; }
        bool isRateDisabled { get; set; }
        bool MatchFound { get; set; }
        float minutes { get; set; }
        string note { get; set; }
        decimal? rate { get; set; }
        string status { get; set; }
        string taxable { get; set; }
        string type { get; set; }
        string uniqueId { get; set; }
    }

    public class AdditionalCharge : IAdditionalCharge
    {
        public string id { get; set; }
        public string type { get; set; }
        public string charge { get; set; }
        public string feeType { get; set; } // waittime, etc
        public decimal? amount { get; set; }
        public string taxable { get; set; }
        public bool haveAuditIssues { get; set; }
        public string note { get; set; }
        public int amountType { get; set; }
        public string declineReason { get; set; }
        //public bool isLabel { get; set; }
        //public bool isButton { get; set; }
        //public string auditMessage { get; set; }
        public int? hours { get; set; }
        public string calcType { get; set; }
        public float minutes { get; set; }
        public decimal? rate { get; set; }
        public int? days { get; set; }

        public string status { get; set; }
        public string friendlyStatus { get; set; }

        // if you include gallons, it will throw an error. 
        //public float gallons { get; set; }

        public bool isRateDisabled { get; set; }
        public bool hasChangesMade { get; set; }

        [JsonIgnore]
        public bool MatchFound { get; set; }
        public object atlasStatus { get; set; }

        public object[] auditIssues { get; set; }
        public string auditMessage { get; set; }
        public bool hasFeedback { get; set; }

        public bool hasAuditIssues { get; set; }

        public string uniqueId { get; set; }
    }

    public class AdditionalChargeSubmit : IAdditionalCharge
    {

        public string id { get; set; }
        public string type { get; set; }
        public string charge { get; set; }
        public string feeType { get; set; } // waittime, etc
        public decimal? amount { get; set; }
        public string taxable { get; set; }
        public bool haveAuditIssues { get; set; }
        public string note { get; set; }
        public int amountType { get; set; }
        public string declineReason { get; set; }
        //public bool isLabel { get; set; }
        //public bool isButton { get; set; }
        //public string auditMessage { get; set; }
        public int? hours { get; set; }
        public string calcType { get; set; }
        public float minutes { get; set; }
        public decimal? rate { get; set; }
        public int? days { get; set; }

        public string status { get; set; }
        public string friendlyStatus { get; set; }

        // if you include gallons, it will throw an error. 
        //public float gallons { get; set; }

        public bool isRateDisabled { get; set; }
        public bool hasChangesMade { get; set; }

        [JsonIgnore]
        public bool MatchFound { get; set; }
        public object atlasStatus { get; set; }

        public object[] auditIssues { get; set; }
        public string auditMessage { get; set; }
        public bool hasFeedback { get; set; }

        public bool hasAuditIssues { get; set; }

        public string uniqueId { get; set; }
        public string state { get; set; }
        public bool amountChanged { get; set; }
        public string comments { get; set; }
        public bool isDeclined { get; set; }
        public bool isCompromise { get; set; }
        public bool isTaxManual { get; set; }
        public object taxAmount { get; set; }
        public object taxRate { get; set; }
        public bool isTaxable { get; set; }
    }


    public class Mileagedetail
    {
        public string id { get; set; }
        public string type { get; set; }
        public int miles { get; set; }
        public decimal rate { get; set; }
        public int rateType { get; set; }
        public decimal total { get; set; }
        public string taxable { get; set; }
//        public string auditMessage { get; set; }
        public bool haveAuditIssues { get; set; }
        public string note { get; set; }
        public int freeMiles { get; set; }
        public bool isVisible { get; set; }
        public bool canEdit { get; set; }
        public string uniqueId { get; set; }
        public string atlasStatus { get; set; }
        public int state { get; set; }
        public bool amountChanged { get; set; }
        public string comments { get; set; }
        public object declineReason { get; set; }
        public bool isDeclined { get; set; }
        public bool isCompromise { get; set; }
        public bool isTaxManual { get; set; }
        public object taxAmount { get; set; }
        public float? taxRate { get; set; }
        public bool isTaxable { get; set; }
        public object[] auditIssues { get; set; }
        public string friendlyStatus { get; set; }
        public string status { get; set; }
        public bool hasFeedback { get; set; }
        public bool hasAuditIssues { get; set; }
        public bool hasChangesMade { get; set; }
        public string auditStatus { get; set; }
        //public bool isLabel { get; set; } = true;
    }
    
    public class PaymentHistoryResult
    {
        public object PaymentHistory { get; set; }
        public object ClaimDetail { get; set; }
        [JsonProperty("ClaimDetails")]
        public List<ClaimDetail> Results { get; set; }
        [JsonProperty("NumberOfDispatches")]
        public int Count { get; set; }
        public string TotalPaidAmount { get; set; }
    }

    public class ClaimDetail
    {
        public string ClaimNumber { get; set; }

        public string DispatchNumber { get; set; }
        public string CustomerName { get; set; }
        public string VehicleYMM { get; set; }
        public string DepositDate { get; set; }
        public string ServiceDate { get; set; }
        public PaymentLine[] PaymentLine { get; set; }
        public string DispatchSubtotal { get; set; }
        public string Tax { get; set; }

        public string DispatchTotal { get; set; }


        public string CheckNumber { get; set; }
        public string ReferenceNumber { get; set; }
        public string PurchaseOrderNumber { get => DispatchNumber; }

        public decimal PaymentTotal
        {
            get
            {
                return Convert.ToDecimal(DispatchTotal.Replace("$", ""));
            }
        }

        public DateTime PaymentDate
        {
            get
            {
                return Convert.ToDateTime(DepositDate.Replace("-", "/"));
            }
        }

    }
    

    public class PaymentLine
    {
        public string ServiceType { get; set; }
        public string AddOn { get; set; }
        [JsonProperty("AmountPaid")]
        public string Amount { get; set; }
        
        [JsonProperty("Amount")]
        public decimal AmountPaid
        {
            get
            {
                return Convert.ToDecimal(Amount.Replace("$",""));
            }
        }
        
        public string Tax { get; set; }
    }



}
namespace Extric.Towbook.Integration.MotorClubs.Billing.Geico2
{
    public class GetDispatchInformationModel
    {
        public int Code { get; set; }
        public string Message { get; set; }
        public GetDispatchInformationResultModel Result { get; set; }
        public bool IsSuccessful { get; set; }
    }

    public class GetDispatchInformationResultModel
    {
        public InvoiceList[] InvoiceList { get; set; }
        public InvoiceTypeList InvoiceTypeList { get; set; }
        public DispatchRates DispatchRates { get; set; }
        public ErsTaxPrompt ErsTaxPrompt { get; set; }
    }

    public class InvoiceTypeList
    {
        public InvoiceTypeListType[] Types { get; set; }
        public InvoiceTypeListCharge[] Charges { get; set; }
        public InvoiceTypeListService[] Services { get; set; }
        public InvoiceTypeListMileage[] Mileages { get; set; }
        public Fee[] Fees { get; set; }
    }

    public class InvoiceTypeListType
    {
        public string Value { get; set; }
        public string Text { get; set; }
        public string TaxCode { get; set; }
        public string Type { get; set; }
        public string ParentCode { get; set; }
    }

    public class InvoiceTypeListCharge
    {
        public string Value { get; set; }
        public string Text { get; set; }
        public string TaxCode { get; set; }
        public string Type { get; set; }
        public object ParentCode { get; set; }
    }

    public class InvoiceTypeListService
    {
        public string Value { get; set; }
        public string Text { get; set; }
        public string TaxCode { get; set; }
        public object Type { get; set; }
        public string ParentCode { get; set; }
    }

    public class InvoiceTypeListMileage
    {
        public string Value { get; set; }
        public string Text { get; set; }
        public string TaxCode { get; set; }
        public object Type { get; set; }
        public object ParentCode { get; set; }
    }

    public class Fee
    {
        public string Value { get; set; }
        public string Text { get; set; }
        public string TaxCode { get; set; }
        public object Type { get; set; }
        public object ParentCode { get; set; }
    }

    public class DispatchRates
    {
        public ServiceRate[] ServiceRates { get; set; }
        public AddonRate[] AddonRates { get; set; }
    }

    public class ServiceRate
    {
        public string ServiceType { get; set; }
        public string VehicleType { get; set; }
        public string RateType { get; set; }
        public GeicoMileageItem LoadedFreeMileage { get; set; }
        public GeicoMileageItem EnrouteFreeMileage { get; set; }
        public GeicoMileageItem DeadHeadMileageRate { get; set; }
        public decimal Rate { get; set; }
    }

    public class GeicoMileageItem
    {
        public int Miles { get; set; }
        public decimal? Rate { get; set; }
    }

    public class AddonRate
    {
        public string AddonType { get; set; }
        public decimal Rate { get; set; }
    }

    public class ErsTaxPrompt
    {
        public string FeeTaxPromptMessage { get; set; }
        public string FeeTaxStatus { get; set; }
    }

    public class GeicoInvoice
    {
        public bool IsSecondaryInvoice { get; set; }
        public bool IsRecommended { get; set; }
        public bool IsGOA { get; set; }
        public string InvoiceStatus { get; set; }

        public InvoiceDetails InvoiceDetails { get; set; }
        public List<Primaryservicedetail> PrimaryServiceDetails { get; set; }
        public PayoutFee[] PayoutFees { get; set; }
        public List<Mileagedetail> MileageDetails { get; set; }
        public object[] PayoutFeesReceiptDocuments { get; set; }
        public object PaidTotal { get; set; }
        public string PendingTotal { get; set; }
        public object PaidTax { get; set; }
        public object PendingTax { get; set; }
        public string Status { get; set; }

        public float TotalInvoiceAmount { get; set; }

        public int Version { get; set; }

        [JsonProperty("atlasCategory")]
        public string AtlasCategory { get; set; }


        /// <summary>
        /// Used by Towbook internally - NOT to be submitted to the motor club.
        /// </summary>
        [JsonIgnore]
        public decimal ExpectedTotal { get; set; }

        /// <summary>
        /// Used by Towbook internally - NOT to be submitted to the motor club.
        /// </summary>
        [JsonIgnore]
        public decimal TowbookTotal { get; set; }
    }


    public class InvoiceList : GeicoInvoice
    {
        public int InvoiceCategory { get; set; }

        public object FriendlyInvoiceCategory { get; set; }
        public string SecondaryMessage { get; set; }
        public bool HasMileage { get; set; }
        public bool HasTow { get; set; }
        public bool CanEdit { get; set; }

        public bool IsAccepted { get; set; }
        public bool IsFinal { get; set; }

        public decimal PaidAmount { get; set; }
        public string FriendlyStatus { get; set; }
    }

    /// <summary>
    /// Used for RecalculateInvoice and GetDispatchInformation
    /// </summary>
    public class InvoiceDetails
    {
        [JsonProperty("dispatchNumber")]
        public string DispatchNumber { get; set; }
        [JsonProperty("customerName")]
        public string CustomerName { get; set; }
        [JsonProperty("claimNumber")]
        public string ClaimNumber { get; set; }
        [JsonProperty("vehicle")]
        public string Vehicle { get; set; }
        [JsonProperty("serviceDate")]
        public string ServiceDate { get; set; }
        [JsonProperty("serviceType")]
        public string ServiceType { get; set; }

        [JsonProperty("lossLocation")]
        public string LossLocation { get; set; }
        [JsonProperty("invoiceType")]
        public string InvoiceType { get; set; }

        [JsonProperty("providerReference")]
        public object ProviderReference { get; set; }

        /*
        [JsonProperty("currentVehicleLocation")]
        public GeicoLocation CurrentVehicleLocation { get; set; }

        [JsonProperty("destinationAddress")]
        public GeicoLocation DestinationAddress { get; set; }*/
    }

    public class GeicoLocation
    {
        [JsonProperty("streetAddress1")]
        public string StreetAddress1 { get; set; }
        [JsonProperty("streetAddress2")]
        public string StreetAddress2 { get; set; }
        [JsonProperty("country")]
        public string Country { get; set; }
        [JsonProperty("state")]
        public string State { get; set; }
        [JsonProperty("county")]
        public string County { get; set; }
        [JsonProperty("city")]
        public string City { get; set; }
        [JsonProperty("zipCode")]
        public Zipcode ZipCode { get; set; }
        [JsonProperty("latitude")]
        public object Latitude { get; set; }
        [JsonProperty("longitude")]
        public object Longitude { get; set; }
        [JsonProperty("cassCode")]
        public object CassCode { get; set; }
    }

    public class Zipcode
    {
        [JsonProperty("zip")]
        public string Zip { get; set; }
        [JsonProperty("extension")]
        public object Extension { get; set; }
    }

    public class Primaryservicedetail
    {
        [JsonProperty("id")]
        public string id { get; set; }
        [JsonProperty("service")]
        public string Service { get; set; }
        [JsonProperty("amount")]
        public decimal Amount { get; set; }
        [JsonProperty("taxable")]
        public string Taxable { get; set; }
        [JsonProperty("haveAuditIssues")]
        public bool HaveAuditIssues { get; set; }
        [JsonProperty("note")]
        public string Note { get; set; }
        [JsonProperty("additionalCharges")]
        public List<AdditionalCharge> AdditionalCharges { get; set; } = new List<AdditionalCharge>();
        [JsonProperty("hours")]
        public int Hours { get; set; }
        [JsonProperty("minutes")]
        public float Minutes { get; set; }
        [JsonProperty("amountType")]
        public int AmountType { get; set; }
        [JsonProperty("goa")]
        public bool Goa { get; set; }
        [JsonProperty("rate")]
        public float? Rate { get; set; }
        [JsonProperty("calcType")]
        public int? CalcType { get; set; }
        [JsonProperty("uniqueId")]
        public string UniqueId { get; set; }
        [JsonProperty("atlasStatus")]
        public string AtlasStatus { get; set; }
        [JsonProperty("state")]
        public int? State { get; set; }
        [JsonProperty("amountChanged")]
        public bool AmountChanged { get; set; }
        [JsonProperty("comments")]
        public string Comments { get; set; }
        [JsonProperty("declineReason")]
        public string DeclineReason { get; set; }
        [JsonProperty("isDeclined")]
        public bool IsDeclined { get; set; }
        [JsonProperty("isCompromise")]
        public bool IsCompromise { get; set; }
        [JsonProperty("isTaxManual")]
        public bool IsTaxManual { get; set; }

        [JsonProperty("taxAmount")]
        public object TaxAmount { get; set; }
        [JsonProperty("taxRate")]
        public float? TaxRate { get; set; }
        [JsonProperty("isTaxable")]
        public bool IsTaxable { get; set; }

        [JsonProperty("auditIssues")]
        public object[] AuditIssues { get; set; }
        [JsonProperty("friendlyStatus")]
        public string FriendlyStatus { get; set; }
        [JsonProperty("status")]
        public string Status { get; set; }
        [JsonProperty("hasFeedback")]
        public bool HasFeedback { get; set; }

        [JsonProperty("hasAuditIssues")]
        public bool HasAuditIssues { get; set; }

        // match reactjs code from client page.. this isnt part of the received model, so these will always be false
        //public bool isLabel { get; set; }
        //public bool isButton { get; set; }

        /// <summary>
        /// Towbook Internal
        /// </summary>
        [JsonIgnore]
        public bool MatchFound { get; set; }



        public bool hasChangesMade { get; set; }
        public string auditStatus { get; set; }

       // public string auditMessage { get; set; }
    }

    public interface IAdditionalCharge
    {
        decimal? amount { get; set; }
        int amountType { get; set; }
        object atlasStatus { get; set; }
        object[] auditIssues { get; set; }
        string auditMessage { get; set; }
        string calcType { get; set; }
        string charge { get; set; }
        int? days { get; set; }
        string declineReason { get; set; }
        string feeType { get; set; }
        string friendlyStatus { get; set; }
        bool hasAuditIssues { get; set; }
        bool hasChangesMade { get; set; }
        bool hasFeedback { get; set; }
        bool haveAuditIssues { get; set; }
        int? hours { get; set; }
        string id { get; set; }
        bool isRateDisabled { get; set; }
        bool MatchFound { get; set; }
        object minutes { get; set; }
        string note { get; set; }
        decimal? rate { get; set; }
        string status { get; set; }
        string taxable { get; set; }
        string type { get; set; }
        string uniqueId { get; set; }
    }

    public class AdditionalCharge 
    {

        public string id { get; set; }
        public string type { get; set; }
        public string charge { get; set; }
        public string feeType { get; set; } // waittime, etc
        public float? amount { get; set; }
        public string taxable { get; set; }
        public bool haveAuditIssues { get; set; }
        public string note { get; set; }
        public int amountType { get; set; }
        public object rate { get; set; }
        //public bool isLabel { get; set; }
        //public bool isButton { get; set; }
        public int? hours { get; set; }
        public object minutes { get; set; }
        public int? days { get; set; }
        public int? gallons { get; set; }
        public int? mileage { get; set; }

        public int? calcType { get; set; } = 0;
        // if you include gallons, it will throw an error. 
        //public float gallons { get; set; }


        [JsonIgnore]
        public bool MatchFound { get; set; }

        public bool canEdit { get; set; }
        public bool canEditAmount { get; set; }
        public string uniqueId { get; set; }
        public object atlasStatus { get; set; }
        public int? state { get; set; }


        public bool amountChanged { get; set; }
        public string comments { get; set; }

        public string declineReason { get; set; }
        public bool isDeclined { get; set; }
        public bool isCompromise { get; set; }
        public bool isTaxManual { get; set; }
        public object taxAmount { get; set; }
        public object taxRate { get; set; }
        public bool isTaxable { get; set; }



        public object[] auditIssues { get; set; }
        public string friendlyStatus { get; set; }
        public string status { get; set; }
        public bool hasFeedback { get; set; }
        public bool hasAuditIssues { get; set; }
        public bool hasChangesMade { get; set; }
        public string auditStatus { get; set; }
        public string auditMessage { get; set; }

    }


    public class Mileagedetail
    {
        public string id { get; set; }
        public string type { get; set; }
        public int miles { get; set; }
        public decimal rate { get; set; }
        public int rateType { get; set; }
        public decimal total { get; set; }
        public string taxable { get; set; }
        //        public string auditMessage { get; set; }
        public bool haveAuditIssues { get; set; }
        public string note { get; set; }
        public int freeMiles { get; set; }
        public bool isVisible { get; set; }
        public bool canEdit { get; set; }
        public string uniqueId { get; set; }
        public string atlasStatus { get; set; }
        public int state { get; set; }
        public bool amountChanged { get; set; }
        public string comments { get; set; }
        public object declineReason { get; set; }
        public bool isDeclined { get; set; }
        public bool isCompromise { get; set; }
        public bool isTaxManual { get; set; }
        public object taxAmount { get; set; }
        public float? taxRate { get; set; }
        public bool isTaxable { get; set; }
        public object[] auditIssues { get; set; }
        public string friendlyStatus { get; set; }
        public string status { get; set; }
        public bool hasFeedback { get; set; }
        public bool hasAuditIssues { get; set; }
        public bool hasChangesMade { get; set; }
        public string auditStatus { get; set; }
        //public bool isLabel { get; set; } = true;
    }

    public class PaymentHistoryResult
    {
        public object PaymentHistory { get; set; }
        public object ClaimDetail { get; set; }
        [JsonProperty("ClaimDetails")]
        public List<ClaimDetail> Results { get; set; }
        [JsonProperty("NumberOfDispatches")]
        public int Count { get; set; }
        public string TotalPaidAmount { get; set; }
    }

    public class ClaimDetail
    {
        public string ClaimNumber { get; set; }

        public string DispatchNumber { get; set; }
        public string CustomerName { get; set; }
        public string VehicleYMM { get; set; }
        public string DepositDate { get; set; }
        public string ServiceDate { get; set; }
        public PaymentLine[] PaymentLine { get; set; }
        public string DispatchSubtotal { get; set; }
        public string Tax { get; set; }

        public string DispatchTotal { get; set; }


        public string CheckNumber { get; set; }
        public string ReferenceNumber { get; set; }
        public string PurchaseOrderNumber { get => DispatchNumber; }

        public decimal PaymentTotal
        {
            get
            {
                return Convert.ToDecimal(DispatchTotal.Replace("$", ""));
            }
        }

        public DateTime PaymentDate
        {
            get
            {
                return Convert.ToDateTime(DepositDate.Replace("-", "/"));
            }
        }

    }


    public class PaymentLine
    {
        public string ServiceType { get; set; }
        public string AddOn { get; set; }
        [JsonProperty("AmountPaid")]
        public string Amount { get; set; }

        [JsonProperty("Amount")]
        public decimal AmountPaid
        {
            get
            {
                return Convert.ToDecimal(Amount.Replace("$", ""));
            }
        }

        public string Tax { get; set; }
    }



}
