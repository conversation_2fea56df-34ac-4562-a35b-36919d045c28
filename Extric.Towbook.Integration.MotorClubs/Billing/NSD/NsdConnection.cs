using Extric.Towbook.Utility;
using HtmlAgilityPack;
using NLog;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;

namespace Extric.Towbook.Integration.MotorClubs.Billing.Nsd
{
    public class NsdConnection: MotorClubConnection, IDisposable
    {
        protected override string LoginUrl
        {
            get
            {
                return @"https://www.nsdmc.com/providerdispatchportal/login.aspx";
            }
            set
            {
                base.LoginUrl = value;
            }
        }

        private NsdConnection(string username, string password)
            : base(username, password)
        {
            
        }

        private static ConcurrentDictionary<string, NsdConnection> connections
                 = new ConcurrentDictionary<string, NsdConnection>();

        /// <summary>
        /// Login to NSD 
        /// </summary>
        /// <param name="username"></param>
        /// <param name="password"></param>
        /// <returns></returns>
        public static NsdConnection Login(LogInfo logInfo, string username, string password, bool useCache = true)
        {
            NsdConnection conn = null;

            string key = username + ":" + password;
            if (useCache)
            {
                if (connections.TryGetValue(key, out conn))
                {
                    if (conn.Expiry > DateTime.Now)
                        return conn;
                    else
                        connections.TryRemove(key, out conn);
                }
            }

            conn = new NsdConnection(username, password);
            conn.LogHttpTraffic = true;

            var html = conn.Get(logInfo, conn.LoginUrl);

            conn.viewState = html.DocumentNode.SelectSingleNode("//input[@id='__VIEWSTATE']").Attributes["value"].Value;
            if (html.DocumentNode.SelectSingleNode("//input[@id='__EVENTVALIDATION']") != null)
                conn.eventValidation = html.DocumentNode.SelectSingleNode("//input[@id='__EVENTVALIDATION']").Attributes["value"].Value;

            var formData = new Dictionary<string, string>
            {
                {"__VIEWSTATE", conn.viewState},
                {"__EVENTVALIDATION", conn.eventValidation},
                {"__EVENTTARGET", "ctl00$ContentPlaceHolder1$btLogin"},
                {"__EVENTARGUMENT", string.Empty},
                {"ctl00$ContentPlaceHolder1$tbUserID", conn.Username},
                {"ctl00$ContentPlaceHolder1$tbPassword", conn.Password}
            };

            html = conn.Post(logInfo, conn.LoginUrl, ref formData);

            if (!html.DocumentNode.InnerHtml.Contains("Logout"))
                throw new InvalidLoginException();

            if (useCache)
                connections.TryAdd(key, conn);
            else
            {
                NsdConnection tempConn = null;

                connections.TryRemove(key, out tempConn);
                connections.TryAdd(key, conn);
            }

            return conn;
        }

        public PurchaseOrder GetPurchaseOrder(LogInfo logInfo, string poNumber, out HtmlDocument html)
        {
            var po = new PurchaseOrder();
            
            // fix to enable find input elements on nested divs inside the form element.
            HtmlNode.ElementsFlags.Remove("form");

            if (!int.TryParse(poNumber ?? "", out int temp))
                throw new UserErrorException($"PO # is not numeric: {poNumber}");

            var url = $"https://www.nsdmc.com/providerdispatchportal/account/podetails.aspx?id={poNumber}";
            html = Get(logInfo, url);

            po.PurchaseOrderNumber = poNumber;

            po.Message = html.DocumentNode.SelectSingleNode("//span[@id='ctl00_ContentPlaceHolder1_lblMSG']")?.InnerText;

            var nodes = html.DocumentNode.SelectNodes("//td");
            if (nodes != null && nodes.Count > 0)
            {
                var node = nodes.FirstOrDefault(n => n.InnerText.Contains("Total Amount Due from NSD:"));
                if (node != null)
                    po.TotalAmount = GetNullableDecimal(node.NextSibling?.InnerText, true);
                    
                //foreach (var node in nodes)
                //{
                //    if (node.InnerText.Contains("Total Amount Due from NSD:"))
                //    {
                //        po.TotalAmount = GetDecimal(node.NextSibling?.InnerText);
                //        break;
                //    }
                //}
            }

            return po;
        }

        public decimal SubmitPurchaseOrder(LogInfo logInfo, PurchaseOrder po, HtmlDocument html)
        {
            var urlSubmit = @"https://www.nsdmc.com/providerdispatchportal/account/submitpayment.aspx?id=" + po.PurchaseOrderNumber;
            var urlGoa = @"https://www.nsdmc.com/providerdispatchportal/account/GOA.ASPX?ID=" + po.PurchaseOrderNumber;
            var nsdTotal = 0m;

            html = Get(logInfo, urlSubmit);
            var form = GetFormFields(html);

            var node = html.DocumentNode.SelectSingleNode("//input[@id='ctl00_ContentPlaceHolder1_FormView1_btAccept']");
            var acceptButtonVisible = node != null;
            var acceptButtonEnabled = node != null && !node.Attributes.Any(a => a.Name.ToLower() == "disabled");
            
            node = html.DocumentNode.SelectSingleNode("//input[@id='ctl00_ContentPlaceHolder1_FormView1_btGOA']");
            var goaButtonVisible = node != null;
            var goaButtonEnabled = node != null && !node.Attributes.Any(a => a.Name.ToLower() == "disabled");
            
            // If ACCEPT button and GOA button are both missing
            if (!acceptButtonVisible && !goaButtonVisible)
                throw new NotFoundException("PO does not exist on the NSD site. ");

            // If ACCEPT button and GOA button are both disabled
            if (!acceptButtonEnabled && !goaButtonEnabled)
                throw new NotSubmittableException($"NSD will not allow this PO to be submitted");

            var submitGOA = (po.IsGOA && goaButtonEnabled) || !acceptButtonEnabled;
            if (submitGOA)
            {
                form["ctl00$ContentPlaceHolder1$FormView1$btGOA"] = "GOA";
                html = Post(logInfo, urlGoa, ref form);

                // If PO doesn't have a Submit button, or its disabled
                node = html.DocumentNode.SelectSingleNode("//input[@id='ctl00_ContentPlaceHolder1_btSubmit']");
                if (node == null || node.Attributes.Any(a => a.Name.ToLower() == "disabled"))
                    throw new NotSubmittableException($"NSD will not allow this PO to be submitted");

                // Get NSD Total
                node = html.DocumentNode.SelectSingleNode("//span[@id='ctl00_ContentPlaceHolder1_lblGOA_Amount']");
                if (node != null) nsdTotal = GetDecimal(node.InnerText);
            }
            else
            {
                // Get NSD Total
                node = html.DocumentNode.SelectSingleNode("//span[@id='ctl00_ContentPlaceHolder1_FormView1_LabelTotalAmt']");
                if (node != null) nsdTotal = GetDecimal(node.InnerText);
            }

            // If NSD's total is not between 90% and 150% of Towbook's total, throw exception
            if (nsdTotal < po.ExpectedTotal * .9m)
            {
                if (nsdTotal == 0m)
                    throw new BillingException($"NSD total for this invoice is $0.00");
                else
                    throw new BillingException($"NSD total of {nsdTotal:0.00} is significantly less than the expected total of {po.ExpectedTotal:0.00}");
            }
            else if (nsdTotal > po.ExpectedTotal * 1.5m)
            {
                throw new BillingException($"NSD total of {nsdTotal:0.00} is significantly more than the expected total of {po.ExpectedTotal:0.00}");
            }
            else
            {
                // Log totals
                logInfo.AddEvent($"NSD -- Total: \t{nsdTotal:0.00}\t Expected: \t{po.ExpectedTotal:0.00}\t Towbook: \t{po.TowbookTotal:0.00}", LogLevel.Info);

                // If totals are too divergent, request logs be flushed at end of processing
                if (Math.Abs(nsdTotal - po.ExpectedTotal) > 5 ||
                    Math.Abs(nsdTotal - po.TowbookTotal) > 10)
                {
                    logInfo.FlushRequested = true;
                }
            }

            // Submit the PO
            if (logInfo.TestMode)
                throw new BillingException("TestMode successful; final submission bypassed");
            else
            {
                if (submitGOA)
                {
                    form["ctl00$ContentPlaceHolder1$btSubmit"] = "Submit";
                    html = Post(logInfo, urlGoa, ref form);

                    // If the page doesn't show a success message
                    if (!html.DocumentNode.InnerHtml.Contains("GOA was submitted"))
                        throw new BillingException("NSD wouldn't accept the submission because of unknown errors.");
                }
                else
                {
                    form["ctl00$ContentPlaceHolder1$FormView1$btAccept"] = "ACCEPT";
                    html = Post(logInfo, urlSubmit, ref form);

                    // If the page doesn't show a success message
                    if (!html.DocumentNode.InnerHtml.Contains("The Purchase Order was submitted"))
                        throw new BillingException("NSD wouldn't accept the submission because of unknown errors.");
                }
            }

            return nsdTotal;
        }

        public List<PaymentStatement> GetPaymentStatements(LogInfo logInfo, DateTime startDate, DateTime endDate)
        {
            var result = new List<PaymentStatement>();

            string url = @"https://www.nsdmc.com/providerdispatchportal/account/payments.aspx";
            
            // get view state
            var html = Get(logInfo, url);

            this.viewState = html.DocumentNode.SelectSingleNode("//input[@id='__VIEWSTATE']").Attributes["value"].Value;
            if (html.DocumentNode.SelectSingleNode("//input[@id='__EVENTVALIDATION']") != null)
                this.eventValidation = html.DocumentNode.SelectSingleNode("//input[@id='__EVENTVALIDATION']").Attributes["value"].Value;

            // peform initial payment search by date range to get total number of pages and total number of payments
            var form = new Dictionary<string, string>()
            {
                {"__VIEWSTATE", this.viewState},
                {"__EVENTVALIDATION", this.eventValidation},
                {"__EVENTTARGET", "ctl00$ContentPlaceHolder1$btSearch"},
                {"__EVENTARGUMENT", ""},
                {"ctl00$ContentPlaceHolder1$startdt", startDate.ToShortDateString()},
                {"ctl00$ContentPlaceHolder1$enddt", endDate.ToShortDateString()}
            };
            
            html = Post(logInfo, url, ref form);

            var rows = html.DocumentNode.SelectNodes("//table[@id='ctl00_ContentPlaceHolder1_GridView2']//tr[td//a[contains(@href, 'Select$')]]");
            if (rows == null || rows.Count == 0)
                return result; // no payments found by date range

            // get paging info
            // Find all <a> tags that contain 'Page$' in the href attribute and take the final one (the last page number)
            var lastPageLink = html.DocumentNode.SelectNodes("//table[@id='ctl00_ContentPlaceHolder1_GridView2']//tr")
                .SelectMany(row => 
                    row.SelectNodes(".//a[contains(@href, 'Page$')]") ?? Enumerable.Empty<HtmlNode>()
                ).LastOrDefault()?.InnerText ?? "1"; 

            int numberOfPages = 0;
            if (!int.TryParse(lastPageLink.Trim(), out numberOfPages))
                numberOfPages = 1; // no pagination, only 1 page

            int pagesVisited = 1;
            int numberOfTotalRows = int.Parse(html.DocumentNode.SelectNodes("//*[@id='ctl00_ContentPlaceHolder1_lblnumberofpos']")[0].InnerText);
            int rowsProcessed = 0;

            Console.WriteLine($"NSD: Found {numberOfTotalRows} payment records across {numberOfPages} pages");

            do
            {
                // fetch by page number
                form = new Dictionary<string, string>()
                {
                    {"__VIEWSTATE", this.viewState},
                    {"__EVENTVALIDATION", this.eventValidation},
                    {"__EVENTTARGET", "ctl00$ContentPlaceHolder1$GridView2"},
                    {"__EVENTARGUMENT", $"Page${pagesVisited}"},
                    {"ctl00$ContentPlaceHolder1$startdt", startDate.ToShortDateString()},
                    {"ctl00$ContentPlaceHolder1$enddt", endDate.ToShortDateString()}
                };

                html = Post(logInfo, url, ref form);

                rows = html.DocumentNode.SelectNodes("//table[@id='ctl00_ContentPlaceHolder1_GridView2']//tr[td//a[contains(@href, 'Select$')]]");

                if (rows != null && rows.Count > 0)
                {
                    Console.WriteLine($"NSD: Found {rows.Count} payments from page {pagesVisited} of {numberOfPages}");

                    for (int i = 0; i < rows.Count; i++)
                    {
                        if (rowsProcessed < numberOfTotalRows)
                        {
                            var checkNumber = rows[i].ChildNodes[5].InnerText.Trim();
                            var currStatement = result.Where(o => o.CheckNumber == checkNumber).FirstOrDefault();
                            if (currStatement == null)
                            {
                                currStatement = new PaymentStatement()
                                {
                                    DatePaid = GetDateTime(rows[i].ChildNodes[7].InnerText.Trim()),
                                    CheckNumber = checkNumber
                                };

                                result.Add(currStatement);
                            }

                            currStatement.PaymentDetails.Add(new PaymentStatementDetail()
                            {
                                PurchaseOrderNumber = rows[i].ChildNodes[2].InnerText.Trim(),
                                NsdCallNumber = rows[i].ChildNodes[3].InnerText.Trim(),
                                Amount = GetDecimal(rows[i].ChildNodes[4].InnerText.Trim().Replace("$", "")),
                                CheckNumber = rows[i].ChildNodes[5].InnerText.Trim(),
                                PODate = GetDateTime(rows[i].ChildNodes[6].InnerText.Trim())
                            });
                            rowsProcessed++;
                        }
                    }
                }

                pagesVisited++;

            } while (pagesVisited <= numberOfPages);

            return result;
        }

        public void Dispose()
        {
            // close session
        }
    }
}