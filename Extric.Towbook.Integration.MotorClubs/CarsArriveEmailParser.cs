using Extric.Towbook.Utility;
using Extric.Towbook.Vehicle;
using HtmlAgilityPack;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Extric.Towbook.Integration.MotorClubs
{
    public class CarsArriveEmailParser: IMotorClubRequest
    {
        public string RequestId { get; set; }
        public DateTime? RequestDate { get; set; }
        public string BenefitAmount { get; set; }
        public string CustomerId { get; set; }
        public string CustomerName { get; set; }
        public string CustomerPhone { get; set; }
        public string DestinationAddress { get; set; }
        public string DestinationName { get; set; }
        public string DestinationCrossStreet { get; set; }
        public string ETA { get; set; }
        public decimal PayoutFees { get; set; }
        public string PickupAddress { get; set; }
        public string PickupCrossStreet { get; set; }
        public string PickupType { get; set; }
        public string PurchaseOrderNumber { get; set; }
        public string ServiceNeeded { get; set; }
        public string TowingLocationId { get; set; }
        public string TowingProviderId { get; set; }
        public string VehicleClass { get; set; }
        public string VehicleColor { get; set; }
        public string VehicleLicense { get; set; }
        public string VehicleLicenseState { get; set; }
        public string VehicleMake { get; set; }
        public string VehicleModel { get; set; }
        public string VehicleNotes { get; set; }
        public string VehicleType { get; set; }
        public string VehicleVIN { get; set; }
        public string VehicleYear { get; set; }
        public string CustomerAddress { get; set; }
        public string CustomerCity { get; set; }
        public string CustomerState { get; set; }
        public string CustomerZip { get; set; }

        private string data = string.Empty;

        public CarsArriveEmailParser()
        {
        }

        public CarsArriveEmailParser(string data)
        {
            this.data = data;
        }

        static public CarsArriveEmailParser FromHtml(string html)
        {
            CarsArriveEmailParser result = new CarsArriveEmailParser();
            HtmlDocument doc = new HtmlDocument();
            HtmlNode.ElementsFlags.Remove("form");
            doc.LoadHtml(html);

            if (doc.DocumentNode.SelectNodes("//h1").Select(s => s.InnerText.ToLowerInvariant()).Where(w => w.Contains("vehicle release authorization / bill of lading")).Any())
            {
                var results = new Collection<KeyValuePair<string, string>>();

                // Load Id
                var node = doc.DocumentNode.SelectNodes("//td[starts-with(., 'CarsArrive Load ID')]")[0];
                if (node != null)
                    results.Add(new KeyValuePair<string, string>("LoadId", node.InnerText.Replace("CarsArrive Load ID:", "").Trim()));

                // Customer
                node = doc.DocumentNode.SelectNodes("//td[starts-with(., 'Customer')]/..")[0];
                if (node != null)
                    results.Add(new KeyValuePair<string, string>("Customer", CleanHtml(node.SelectSingleNode(".//td[2]").InnerText)));

                // Trip Distance & ETA
                node = doc.DocumentNode.SelectNodes("//td[starts-with(., 'ETA')]/..")[0];
                if (node != null)
                {
                    // Trip Distance
                    results.Add(new KeyValuePair<string, string>("TripDistance", CleanHtml(node.SelectSingleNode(".//td[2]").InnerText)));

                    // ETA
                    results.Add(new KeyValuePair<string, string>("ETA", CleanHtml(node.SelectSingleNode(".//td[4]").InnerText)));
                }

                // Origin & Destination
                node = doc.DocumentNode.SelectNodes("//td[starts-with(., 'Origin')]/..")[0];
                if (node != null)
                {
                    var origin = node.SelectNodes(".//td[2]");
                    if (origin != null)
                        results.Add(new KeyValuePair<string, string>("TowSource", CleanHtml(origin[0].InnerHtml)));

                    var destination = node.SelectNodes(".//td[4]");
                    if (destination != null)
                        results.Add(new KeyValuePair<string, string>("Destination", CleanHtml(origin[0].InnerHtml)));
                }

                // Comments
                node = doc.DocumentNode.SelectNodes("//td[starts-with(., 'Comments')]/..")[0];
                if (node != null)
                    results.Add(new KeyValuePair<string, string>("Comments", CleanHtml(node.SelectSingleNode(".//td[2]").InnerText)));

                // Vehicle
                var vehicleKeyTable = doc.DocumentNode.SelectSingleNode("//*[@id='frmUnits']/table[1]/tr/td/table");
                var vehicleValueTable = doc.DocumentNode.SelectSingleNode("//*[@id='frmUnits']/table[2]");
                if (vehicleKeyTable != null && vehicleValueTable != null)
                {
                    var keys = vehicleKeyTable.SelectNodes(".//td").Select(s => s.InnerText.Trim()).ToArray();
                    var values = vehicleValueTable.SelectNodes(".//td").Select(s => s.InnerText.Trim()).ToArray();

                    if (keys.Count() == values.Count())
                    {
                        for (var i = 0; i < keys.Count(); i++)
                            if(!results.Select(s => s.Key).Contains(keys[i]))
                                results.Add(new KeyValuePair<string, string>(keys[i], values[i]));
                    }
                }

                foreach (var r in results) {
                    switch (r.Key)
                    {
                        case "Year/Make/Model/Color":
                            // vehicle comes over as a combined string
                            string year = string.Empty;
                            string color = string.Empty;
                            string make = string.Empty;
                            string model = string.Empty;

                            VehicleUtility.FindVehicleInfo(r.Value, out year, out make, out model, out color);
                            result.VehicleColor = color;
                            result.VehicleYear = year;
                            result.VehicleMake = make;
                            result.VehicleModel = model;
                            break;

                        case "LoadId": result.RequestId = r.Value; break;
                        case "VIN": result.VehicleVIN = r.Value; break;
                        case "Comments": result.VehicleNotes += "Comments: " + r.Value + "\r\n"; break;
                        case "TowSource": result.PickupAddress = r.Value; break;
                        case "Destination": result.DestinationAddress = r.Value; break;
                        case "TripDistance": result.VehicleNotes += "Trip Distance: " + r.Value + "\r\n"; break;
                        case "ETA": result.VehicleNotes += "ETA: " + r.Value + "\r\n"; break;
                        case "Customer": result.VehicleNotes += "Customer: " + r.Value + "\r\n"; break;

                    }
                }



            }
            else
            {

                var tablePickup = doc.DocumentNode.SelectNodes("//strong[starts-with(.,'Pick Up Location')]")[0].ParentNode.ParentNode.ParentNode;
                if (tablePickup != null)
                {
                    var company = tablePickup.SelectNodes(".//td[starts-with(.,'Company')]")[0].NextSibling.NextSibling.InnerText;
                    var contact = tablePickup.SelectNodes(".//td[starts-with(.,'Contact')]")[0].NextSibling.NextSibling.InnerText;

                    result.CustomerName = string.Format("{0} - {1}", company, contact).Trim();
                    result.CustomerPhone = tablePickup.SelectNodes(".//td[starts-with(.,'Phone')]")[0].NextSibling.NextSibling.InnerText;

                    var address = tablePickup.SelectNodes(".//td[starts-with(.,'Address')]")[0].NextSibling.NextSibling.InnerText;
                    var cityStateZip = tablePickup.SelectNodes(".//td[starts-with(.,'City/St/Zip')]")[0].NextSibling.NextSibling.InnerText;

                    result.PickupAddress = string.Format("{0}, {1}", address, cityStateZip);
                }

                var tableDestination = doc.DocumentNode.SelectNodes("//strong[starts-with(.,'Delivery Location')]")[0].ParentNode.ParentNode.ParentNode;
                if (tableDestination != null)
                {
                    var company = tableDestination.SelectNodes(".//td[starts-with(.,'Company')]")[0].NextSibling.NextSibling.InnerText;
                    var contact = tableDestination.SelectNodes(".//td[starts-with(.,'Contact')]")[0].NextSibling.NextSibling.InnerText;

                    result.DestinationName = string.Format("{0} - {1}", company, contact).Trim();

                    var address = tableDestination.SelectNodes(".//td[starts-with(.,'Address')]")[0].NextSibling.NextSibling.InnerText;
                    var cityStateZip = tableDestination.SelectNodes(".//td[starts-with(.,'City/St/Zip')]")[0].NextSibling.NextSibling.InnerText;

                    result.DestinationAddress = string.Format("{0}, {1}", address, cityStateZip);
                }

                var vehicleTable = doc.DocumentNode.SelectNodes("//strong[starts-with(., 'Year/Make/Model')]")[0].ParentNode.ParentNode.ParentNode;
                if (vehicleTable != null)
                {
                    var vehicleInfo = CleanHtml(vehicleTable.SelectNodes(".//tr")[1].SelectNodes(".//td")[1].InnerText);
                    result.VehicleYear = vehicleInfo.Split(' ')[0];
                    result.VehicleMake = vehicleInfo.Split(' ')[1];
                    result.VehicleModel = vehicleInfo.Split(' ')[2];

                    result.VehicleVIN = CleanHtml(vehicleTable.SelectNodes(".//tr")[1].SelectNodes(".//td")[2].InnerText);
                }

                result.PickupAddress = Regex.Replace(result.PickupAddress, @"\r\n?|\n", ", ");
                result.DestinationAddress = Regex.Replace(result.DestinationAddress, @"\r\n?|\n", ", ");
            }

            result.RequestDate = DateTime.Now;

            return result;
        }

        static public CarsArriveEmailParser FromText(string text)
        {            
            CarsArriveEmailParser result = new CarsArriveEmailParser(text);

            var results = GetKeyValues(Decode(result.data));

            var contactCount = 1;
            var phoneCount = 1;
            var zipCount = 1;
            var addressCount = 1;
            var companyCount = 1;

            var pickupZip = "";
            var pickupAddr = "";
            var pickupCompany = "";

            var destContact = "";
            var destCompany = "";
            var destAddr = "";
            var destZip = "";

            foreach (var res in results)
            {
                var value = res.Value.Trim();

                switch (res.Key)
                {
                    case "Contact":
                        if (contactCount == 1)
                        {
                            result.CustomerName = value;
                            contactCount++;
                        }
                        else if (contactCount == 2)
                            destContact = value;
                        break;
                    case "Phone":
                        if (phoneCount == 1)
                        {
                            result.CustomerPhone = value;
                            phoneCount++;
                        }
                        break;
                    case "Company":
                        if (companyCount == 1)
                        {
                            pickupCompany = value;
                            companyCount++;
                        }
                        else if (companyCount == 2)
                            destCompany = Regex.Split(value, @"\s{2,}")[0].Trim(); ;

                        break;
                    case "CT_ST_ZIP":
                        if (zipCount == 1)
                        {
                            pickupZip = value;
                            zipCount++;
                        }
                        if (zipCount == 2)
                        {
                            destZip = Regex.Split(value, @"\n")[0].Trim();
                        }
                        break;
                    case "Address":
                        if (addressCount == 1)
                        {
                            pickupAddr = value;
                            addressCount++;
                        }
                        else if (addressCount == 2)
                            destAddr = value;
                        break;
                    case "Year": result.VehicleYear = value; break;
                    case "Model": result.VehicleModel = value; break;
                    case "Make": result.VehicleMake = Regex.Split(value, @"\s{2,}")[0].Trim(); break;
                    case "VIN": result.VehicleVIN = value; break;
                }
            }

            result.DestinationName = string.Format("{0} - {1}", destCompany, destContact);
            result.PickupAddress = string.Format("{0}, {1}", pickupAddr, pickupZip);
            result.DestinationAddress = string.Format("{0}, {1}", destAddr, destZip);

            return result;
        }

        private static string Decode(string data)
        {
            string[] keys = new string[]{ "Load Id", "Year", "Make", "Model", "VIN", "Miles", "Company", "Address", "CT/ST/ZIP", "Contact", "Phone", "Pick Up Location", "Delivery Location", "Transporter" };
            string[] stripStrings = new string[] { "Bill Of Lading" };

            // split contents by data section & call history section
            var dataParts = data.Split(new string[] { "Loose Item Inspection" }, StringSplitOptions.None);
            var result = dataParts[0]; // parse only data section

            foreach (var key in keys)
            {
                var newKey = Regex.Replace(key, @"[^A-Za-z0-9]+", "_");
                result = Regex.Replace(result, key.Replace("(", "\\(").Replace(")", "\\)") + ":", "@{" + newKey + "}:");
            }

            // clean up texts (remove subtitles like "Membership Information" and others)
            foreach (var s in stripStrings)
            {
                result = result.Replace(s, "");
            }

            return result;
        }

        public static List<KeyValuePair<string, string>> GetKeyValues(string text)
        {
            var list = new List<KeyValuePair<string, string>>();

            var regex = new Regex("@\\{(?<key>\\w+)\\}:(?<value>[^@^\\{.]+)", RegexOptions.IgnoreCase);
            var results = regex.Matches(text);

            foreach (Match match in results)
            {
                var key = match.Groups["key"].Value.Trim();
                var value = match.Groups["value"].Value.Trim();

                list.Add(new KeyValuePair<string, string>(key, value));
            }

            return list;
        }

        static public string CleanHtml(string value)
        {
            if (value == null)
                return "";

            return Regex.Replace(HtmlEntity.DeEntitize(value)
                            .Replace("<br>", " ")
                            .Replace("<br/>", " ")
                            .Replace("\n", "")
                            .Replace("\t", "")
                            .Replace("\r", "")
                        , @"\s+", " ").Trim();
        }

        /// <summary>
        /// Get a given value from a string based on lines (each label and value are on one single line)
        /// </summary>
        /// <param name="key"></param>
        /// <param name="str"></param>
        /// <returns>Found value</returns>
        private string GetValueFrom(string key, string str)
        {
            var lines = str.Split(new char[] { '\n' }, StringSplitOptions.RemoveEmptyEntries)
                .Where(x => !string.IsNullOrWhiteSpace(x))
                .Select(s => s.Trim());

            for (var i=0; i<lines.Count(); i++)
            {
                var line = lines.ElementAt(i).Trim();
                if (line == key)
                {
                    if (i + 1 < lines.Count())
                        return lines.ElementAt(i + 1).Trim();
                    else
                        return "";
                }
            }

            return "";
        }

        private string ExtractSubText(string key, string nextKey = "\n")
        {
            int keyIdx = this.data.IndexOf(key);
            if (keyIdx == -1)
            {
                // couldn't find the key... don't return an exception - this will cause fields that are missing to cause the whole parser to crash.
                return "";
            }

            int endLineIdx = this.data.IndexOf(nextKey, keyIdx);
            if (endLineIdx == -1)
                return "";

            return this.data.Substring(keyIdx + key.Length, endLineIdx - (keyIdx + key.Length));
        }

        private string ExtractText(string key, string nextKey = "\n", int occurrence = 0)
        {
            int keyIdx;

            if (occurrence == 0)
                keyIdx = this.data.IndexOf(key);
            else 
                keyIdx = IndexOfNth(this.data, key, occurrence); 

            if (keyIdx == -1)
            {
                // couldn't find the key... don't return an exception - this will cause fields that are missing to cause the whole parser to crash.
                return "";
            }

            int endLineIdx = this.data.IndexOf(nextKey, keyIdx);
            if (endLineIdx == -1)
                return "";

            string[] parts = this.data.Substring(keyIdx + key.Length, endLineIdx - (keyIdx + key.Length)).Split(new char[] { ':' }, StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length == 1)
                return parts[0].Replace("\r", "").Trim();
            else if (parts.Length == 2)
                return parts[1].Trim().Replace("\r", "").Trim();
            else
                return string.Empty;
        }

        public static int IndexOfNth(string str, string value, int nth = 1)
        {
            if (nth <= 0)
                throw new ArgumentException("Can not find the zeroth index of substring in string. Must start with 1");
            int offset = str.IndexOf(value);
            for (int i = 1; i < nth; i++)
            {
                if (offset == -1) return -1;
                offset = str.IndexOf(value, offset + 1);
            }
            return offset;
        }

        public System.Threading.Tasks.Task PostSave(Extric.Towbook.Dispatch.Entry entry)
        {
            return System.Threading.Tasks.Task.CompletedTask;
        }
    }
}
