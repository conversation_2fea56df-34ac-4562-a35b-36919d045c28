using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Extric.Towbook.Dispatch;
using HtmlAgilityPack;
using System.Net;
using System.Globalization;

namespace Extric.Towbook.Integration.MotorClubs
{
    internal static class FaxProcessorExtensions
    {
        public static bool ChangeKey<TKey, TValue>(this IDictionary<TKey, TValue> dict,
                                           T<PERSON><PERSON> oldKey, T<PERSON>ey newKey)
        {
            TValue value;
            if (!dict.TryGetValue(oldKey, out value))
                return false;

            if (dict.ContainsKey(newKey))
                return false;

            dict.Remove(oldKey);  // do not change order
            dict[newKey] = value;  // or dict.Add(newKey, value) depending on ur comfort
            return true;
        }

        public static TValue Get<TKey, TValue>(this IDictionary<TKey, TValue> dict,
                                           TKey oldKey)
        {
            TValue value;
            if (!dict.TryGetValue(oldKey, out value))
                return default(TValue);
            else
                return value;
        }
    }


    /// <summary>
    /// Parses the text from a Nation Safe Drivers email into a usable dictionary of values.
    /// </summary>
    public class NsdEmailParser : IMotorClubRequest
    {
        public string BenefitAmount { get; private set; }
        public string CustomerId { get; private set; }
        public string CustomerName { get; private set; }
        public string CustomerPhone { get; private set; }
        public string DestinationAddress { get; private set; }
        public string DestinationCrossStreet { get; private set; }
        public string DestinationName { get; private set; }
        public string ETA { get; private set; }
        public decimal PayoutFees { get; private set; }
        public string PickupAddress { get; private set; }
        public string PickupCrossStreet { get; private set; }
        public string PickupType { get; private set; }
        public string PurchaseOrderNumber { get; private set; }
        public string ServiceNeeded { get; private set; }
        public string TowingLocationId { get; private set; }
        public string TowingProviderId { get; private set; }
        public string VehicleClass { get; private set; }
        public string VehicleColor { get; private set; }
        public string VehicleLicense { get; private set; }
        public string VehicleLicenseState { get; set; }
        public string VehicleMake { get; set; }
        public string VehicleModel { get; private set; }

        public string VehicleType { get; private set; }
        public string VehicleYear { get; private set; }

        public string VehicleNotes { get; set; }
        public string VehicleVIN { get; set; }

        public DateTime? RequestDate { get; set; }
        public string RequestId { get; set; }

        public string CustomerAddress { get; set; }
        public string CustomerCity { get; set; }
        public string CustomerState { get; set; }
        public string CustomerZip { get; set; }



        public System.Threading.Tasks.Task PostSave(Entry entry)
        {
            return System.Threading.Tasks.Task.CompletedTask;
        }

        HtmlDocument doc = new HtmlDocument();
        
        public Dictionary<string, string> Values { get; set; }

        public static NsdEmailParser FromFile(string filename)
        {
            return FromText(File.ReadAllText(filename));
        }

        public static NsdEmailParser FromText(string html)
        {
            NsdEmailParser nsd = new NsdEmailParser();
            nsd.doc.LoadHtml(html);
            nsd.Values = new Dictionary<string, string>();

            var values = new Dictionary<string, string>();


            string[] disablementKeys = new string[] { "Disablement Location:", "Disablement Location Information" };

            foreach (var key in disablementKeys)
                foreach (var a in nsd.ExtractColumnsInFollowingRow(key))
                    nsd.Values.Add(a.Key, a.Value);

            string[] destinationKeys = new string[] { "Destination:", "Destination" };

            foreach (var key in destinationKeys)
                foreach (var a in nsd.ExtractColumnsInFollowingRow(key))
                    nsd.Values.Add(a.Key, a.Value);

            string[] specialInstructionKeys = new string[] { "Special Instructions/ Comments:", "Special Instructions/ Comments" };

            foreach (var key in specialInstructionKeys)
            {
                var comments = nsd.ExtractColumnsInFollowingRow(key);
                if (comments.Count() > 1)
                {
                    foreach (var a in nsd.ExtractColumnsInFollowingRow(key))
                        nsd.Values.Add("Comments_" + a.Key, a.Value);
                }
            }


            foreach (var a in nsd.ExtractColumnsInFollowingRow("Member Information"))
                nsd.Values.Add(a.Key, a.Value);

            foreach (var a in nsd.ExtractColumnsInFollowingRow("Provider Information"))
                nsd.Values.Add(a.Key, a.Value);

            string[] keys = new string[] { "ISP Information", "Member Information", "Questions &amp; Answers:", "Questions &amp; Answers" };

            foreach (var key in keys)
                foreach (var kv in nsd.ExtractArea(key))
                    nsd.Values.Add(kv.Key, kv.Value);

            
            foreach (var kv in nsd.ExtractAreaCc("Card is only available for use 14 days from issuance"))
                nsd.Values.Add(kv.Key, kv.Value);


            foreach (var a in nsd.ExtractAreaTableInNext("Vehicle Information"))
                nsd.Values.Add(a.Key, a.Value);

            foreach (var a in nsd.ExtractColumnsInFollowingRow("Vehicle Information"))
                nsd.Values.Add(a.Key, a.Value);

            foreach (var a in nsd.ExtractEveryTwo("Contracted Base Rate:"))
                nsd.Values.Add(a.Key, a.Value);

            var baseRate = nsd.ExtractEveryTwo("Total Amount Due from NSD:");
            foreach (var a in baseRate)
                nsd.Values.Add(a.Key, a.Value);
            /*
            if (nsd.Values.Get("Total Amount Due from NSD") != null)
            {
                decimal amount = 0;

                if (decimal.TryParse(nsd.Values.Get("Total Amount Due from NSD").Replace("$", "").Trim(), out amount))
                {
                    nsd.BenefitAmount = amount.ToString("C");
                }
            }
            */

            nsd.BenefitAmount = nsd.Values.Get("Coverage Limit");

            nsd.VehicleYear = nsd.Values.Get("Year");
            nsd.VehicleMake = nsd.Values.Get("Make");

            if (nsd.VehicleMake != null && nsd.VehicleMake.ToLowerInvariant() == "tyta")
                nsd.VehicleMake = "Toyota";

            nsd.VehicleModel = nsd.Values.Get("Model");
            nsd.VehicleColor = nsd.Values.Get("Color");
            nsd.VehicleLicense = nsd.Values.Get("Tag #");
            nsd.VehicleVIN = nsd.Values.Get("Vin");

            nsd.CustomerName = nsd.Values.Get("Name");
            nsd.CustomerId = nsd.Values.Get("Contract #");
            nsd.CustomerPhone = Core.FormatPhone(nsd.Values.Get("Call Back #"));

            // Sometimes, purchase orders are coming in pairs.  Use Qore version.
            // Example: "13047656 (Qore PO# 52987)",
            // Example: "13053645 (Qore PO# 53575)"
            var purchaseOrderText = nsd.Values.Get("PO #") ?? string.Empty;
            if (purchaseOrderText.Contains("Qore PO#"))
            {
                // First, split on '(' to isolate the content inside the parentheses
                var parts = purchaseOrderText.Split('(');
                if (parts.Length > 1)
                {
                    // Split on the the '#' to get the PO number
                    var poNumber = parts[1].Split('#');
                    if (poNumber.Length > 1)
                    {
                        // Remove the closing parenthesis and trim whitespace
                        nsd.PurchaseOrderNumber = poNumber[1].Replace(")", "").Trim();
            
                        nsd.VehicleNotes += $"{purchaseOrderText}{Environment.NewLine}{Environment.NewLine}";
                    }
                }
            }
            else
                nsd.PurchaseOrderNumber = purchaseOrderText;

            
            nsd.ServiceNeeded = nsd.Values.Get("Service Type");

            nsd.RequestDate = ParseDate(nsd.Values.Get("Date of Service"));
            
            nsd.TowingProviderId = nsd.Values.Get("Tow ID");

            nsd.PickupAddress = (nsd.Values.Get("From_Address") ?? "") + " " +
                (nsd.Values.Get("From_City") ?? "") + " " +
                (nsd.Values.Get("From_State") ?? "") + " " +
                (nsd.Values.Get("From_Zip") ?? "").Trim();

            nsd.DestinationAddress = ((nsd.Values.Get("To_Address") ?? "") + " " +
                (nsd.Values.Get("To_City") ?? "") + " " +
                (nsd.Values.Get("To_State") ?? "") + " " +
                (nsd.Values.Get("To_Zip") ?? "")).Trim();

            nsd.VehicleNotes += nsd.Values.Get("Comments_Breakdown Reason") ?? "";
            nsd.VehicleNotes += (nsd.VehicleNotes.Length > 0 ? ", " : "") + nsd.Values.Get("Comments_VEH LOC");
            nsd.VehicleNotes += nsd.Values.Get("Comments_Notes") ?? "";
            nsd.VehicleNotes += nsd.Values.Get("Comments_KeyLocation") ?? "";

            return nsd; 
        }


        public Dictionary<string, string> ExtractColumnsInFollowingRow(string title)
        {
            var values = new Dictionary<string, string>();

            var r = doc.DocumentNode.SelectNodes("//span[text()=\"" + title + "\"]//ancestor::tr[1]/following-sibling::tr");
            if (r == null)
                r = doc.DocumentNode.SelectNodes("//td[text()=\"" + title + "\"]//ancestor::tr[1]/following-sibling::tr");

            if(r == null)
                return values;

            for (int row = 0; row < r.Count; row++)
            {
                var titles = r[row].ChildNodes.Where(o => o.NodeType == HtmlNodeType.Element).ToArray();
                if (row > 6) break;
                if (title == "Disablement Location:")
                {
                    if (titles.Length == 4 || titles.Length == 5)
                    {
                        values.Add("From_Address", Decode(titles[0].InnerText));
                        values.Add("From_City", Decode(titles[1].InnerText));
                        values.Add("From_State", Decode(titles[2].InnerText));
                        values.Add("From_Zip", Decode(titles[3].InnerText));
                    }
                    else if (r[row].Descendants("TABLE").Any() && row < 6)
                    {
                        var cols = r[row].Descendants("TABLE").First().Descendants("TD").Where(o => o.NodeType == HtmlNodeType.Element).DefaultIfEmpty().ToArray();

                        if (cols.Where(o => o.InnerText == "Lat:").Any())
                        {
                            for (int subRow = 0; subRow < cols.Length; subRow++)
                            {
                                if (cols[subRow].InnerText.Contains("Lat:"))
                                {
                                    if (!values.ContainsKey("From_Latitude"))
                                        values.Add("From_Latitude", Decode(cols[subRow + 1].InnerText));
                                }
                                if (cols[subRow].InnerText.Contains("Lon:"))
                                {
                                    if (!values.ContainsKey("From_Longitude"))
                                        values.Add("From_Longitude", Decode(cols[subRow + 1].InnerText));
                                }
                            }
                        }
                    }
                    else
                    {
                        foreach (var ry in r[row].ChildNodes
                            .Where(o => o.NodeType == HtmlNodeType.Element && o.Name.ToLowerInvariant() == "td"))
                        {
                            //Console.WriteLine(ry.OuterHtml.Trim());
                            var spans = ry.ChildNodes["SPAN"];
                            if (spans != null)
                            {
                                string name = "";
                                string value = "";

                                foreach (var y in spans.ChildNodes)
                                {
                                    if (y.NodeType == HtmlNodeType.Text)
                                    {
                                        name = Decode(y.InnerText.Trim().TrimEnd(':'));
                                    }
                                    else if (y.Name.ToLowerInvariant() == "strong")
                                    {
                                        value = Decode(y.InnerText);
                                    }
                                }
                                if (!string.IsNullOrWhiteSpace(name))
                                    values.Add(name, value);
                            }



                        }

                    }
                }
                else if (title == "Destination:")
                {

                    if (titles.Length >= 4)
                    {
                        values.Add("To_Address", Decode(titles[0].InnerText));
                        values.Add("To_City", Decode(titles[1].InnerText));
                        values.Add("To_State", Decode(titles[2].InnerText));
                        values.Add("To_Zip", Decode(titles[3].InnerText));
                    }
                    else if (row == 2)
                    {
                        var cols = r[row].Descendants("TD").ToArray();


                        // sometimes this info doesn't exist. so verify the indexes exist.
                        if (cols.Length >= 5)
                        {
                            values.Add("To_Latitude", Decode(cols[1].InnerText));
                            values.Add("To_Longitude", Decode(cols[3].InnerText));
                            values.Add("To_Miles", Decode(cols[5].InnerText));
                        }
                    }
                }
                else if (title == "Special Instructions/ Comments:")
                {
                    Console.WriteLine(r[row].InnerHtml);
                    var data = r[row].InnerText.Trim();

                    if (!this.Values.ContainsKey("Comments_Notes"))
                        this.Values.Add("Comments_Notes", data);

                    string[] kv = data.Split(new char[] { ':' }, 2);

                    if (kv.Length == 2)
                    {
                        if (!values.ContainsKey(kv[0].Trim()))
                            values.Add(kv[0].Trim(), kv[1].Trim());
                        else
                            values[kv[0].Trim()] += ", " + kv[1].Trim();
                    }
                }
                else if (title == "Disablement Location Information")
                {
                    var keys = new string[] { "Business Name:", "Full Address:", "Latitude:", "Longitude:" };

                    var foundValues = GetTableCellDataByKeys(r[row].SelectNodes(".//td"), keys);

                    if (foundValues.ContainsKey("Full Address:"))
                        values.Add("From_Address", foundValues["Full Address:"]);
                    if (foundValues.ContainsKey("Latitude:"))
                        values.Add("From_Latitude", foundValues["Latitude:"]);
                    if (foundValues.ContainsKey("Longitude:"))
                        values.Add("From_Longitude", foundValues["Longitude:"]);
                }
                else if (title == "Destination")
                {
                    var keys = new string[] { "Full Address:", "Latitude:", "Longitude:", "Tow Distance:" };
                    // generic extraction. Each cell, look for span tags in pairs of two.
                    var foundValues = GetTableCellDataByKeys(r[row].SelectNodes(".//td"), keys);

                    if (foundValues.ContainsKey("Full Address:"))
                        values.Add("To_Address", foundValues["Full Address:"]);
                    if (foundValues.ContainsKey("Latitude:"))
                        values.Add("To_Latitude", foundValues["Latitude:"]);
                    if (foundValues.ContainsKey("Longitude:"))
                        values.Add("To_Longitude", foundValues["Longitude:"]);
                    if (foundValues.ContainsKey("Tow Distance:"))
                        values.Add("To_Miles", foundValues["Tow Distance:"]);
                }
                else if (title == "Special Instructions/ Comments")
                {
                    var keys = new string[] { "Key Location:", "Special Instructions:" };
                    
                    var foundValues = GetTableCellDataByKeys(r[row].SelectNodes(".//td"), keys);

                    if(foundValues.Any())
                    {
                        if (foundValues.ContainsKey("Key Location:"))
                            values.Add("KeyLocation", $"Key Location: {foundValues["Key Location:"]}{Environment.NewLine}");

                        if (foundValues.ContainsKey("Special Instructions:"))
                            values.Add("SpecialInstructions", $"Special Instructions: {foundValues["Special Instructions:"]}{Environment.NewLine}");
                    }
                }
                else if (title == "Member Information")
                {
                    var keys = new string[] { "Name:", "Member #:", "Call Back #:", "Call Back #", "Producer Code:", "Contract #"};

                    var foundValues = GetTableCellDataByKeys(r[row].SelectNodes(".//td"), keys);

                    if (foundValues.ContainsKey("Name:"))
                        values.Add("Name", foundValues["Name:"]);
                    if (foundValues.ContainsKey("Contract #:"))
                        values["Contract #"] = foundValues["Contact #:"];
                    if (foundValues.ContainsKey("Contract #"))
                        values["Contract #"] = foundValues["Contact #"];
                    if (foundValues.ContainsKey("Call Back #:"))
                        values["Call Back #"] = foundValues["Call Back #:"];
                    if (foundValues.ContainsKey("Call Back #"))
                        values["Call Back #"] = foundValues["Call Back #"];
                }
                else if (title == "Vehicle Information")
                {
                    var keys = new string[] { "Year", "Year:", "Make", "Make:", "Model", "Model:", "Color", "Color:", "License Plate:", "Tag #", "Vin", "Vin:" };

                    var foundValues = GetTableCellDataByKeys(r[row].SelectNodes(".//td"), keys);

                    if (foundValues.ContainsKey("Year"))
                        values["Year"] = foundValues["Year"];
                    if (foundValues.ContainsKey("Year:"))
                        values["Year"] = foundValues["Year:"];

                    if (foundValues.ContainsKey("Model"))
                        values["Model"] = foundValues["Model"];
                    if (foundValues.ContainsKey("Model:"))
                        values["Model"] = foundValues["Model:"];

                    if (foundValues.ContainsKey("Make"))
                        values["Make"] = foundValues["Make"];
                    if (foundValues.ContainsKey("Make:"))
                        values["Make"] = foundValues["Make:"];

                    if (foundValues.ContainsKey("Color"))
                        values["Color"] = foundValues["Color"];
                    if (foundValues.ContainsKey("Color:"))
                        values["Color"] = foundValues["Color:"];

                    if (foundValues.ContainsKey("License Plate:"))
                        values["Tag #"] = foundValues["License Plate:"];
                    if (foundValues.ContainsKey("Tag #"))
                        values["Tag #"] = foundValues["Tag #"];

                    if (foundValues.ContainsKey("Vin"))
                        values["Vin"] = foundValues["Vin"];
                    if (foundValues.ContainsKey("Vin:"))
                        values["Vin"] = foundValues["Vin:"];
                }
                else if (title == "Provider Information")
                {
                    var keys = new string[] { "Purchase Order #:", "Date of Service:", "Service Type:", "Tow Id:", "Coverage Limit:" };

                    var foundValues = GetTableCellDataByKeys(r[row].SelectNodes(".//td"), keys);

                    if (foundValues.ContainsKey("Purchase Order #:"))
                        values["PO #"] = foundValues["Purchase Order #:"];
                    if (foundValues.ContainsKey("Date of Service:"))
                        values["Date of Service"] = foundValues["Date of Service:"];
                    if (foundValues.ContainsKey("Service Type:"))
                        values["Service Type"] = foundValues["Service Type:"];
                    if (foundValues.ContainsKey("Tow Id:"))
                        values["Tow ID"] = foundValues["Tow Id:"];
                    if (foundValues.ContainsKey("Coverage Limit:"))
                        values["Coverage Limit"] = foundValues["Coverage Limit:"];
                }

            }

            return values;
        }

        private static Dictionary<string, string> GetTableCellDataByKeys(HtmlNodeCollection nodes, string[] keys)
        {
            var data = new Dictionary<string, string>();

            if (nodes == null || keys == null)
                return data;

            foreach (var cell in nodes)
            {
                var spanRows = cell.SelectNodes(".//span");

                if (spanRows != null && spanRows.Count >= 2)
                {
                    var key = Decode(spanRows[0].InnerText);
                    var value = Decode(spanRows[1].InnerText);

                    if (keys.Contains(key) && !data.ContainsKey(key))
                        data.Add(key, value);
                }
            }

            return data;
        }

        public static string Decode(string x)
        {
            return WebUtility.HtmlDecode(x.Trim('\n', '\r', ' '));
        }

        static DateTime? ParseDate(string dateString)
        {
            // try original format (Example: 3/24/2025 1:40:27 PM)
            try
            {
                return Convert.ToDateTime(dateString);
            }
            catch
            { }

            // Some date examples:
            // Sun Mar 23 2025 02:17:24 GMT+0000 (Coordinated Universal Time)
            // Wed Mar 26 2025 10:04:30 GMT+0000 (Coordinated Universal Time)
            // Fri Mar 14 2025 16:13:01 GMT+0000 (Coordinated Universal Time)

            // Trim everything after "GMT+0000"
            int gmtIndex = dateString.IndexOf("GMT+0000");

            if (gmtIndex == -1)
                return null;

            dateString = dateString.Substring(0, gmtIndex).Trim();

            // Define expected format
            string format = "ddd MMM dd yyyy HH:mm:ss";

            // Attempt to parse as UTC
            if (DateTime.TryParseExact(dateString, format, CultureInfo.InvariantCulture, DateTimeStyles.AssumeUniversal, out DateTime utcDate))
            {
                // Explicitly set DateTimeKind to Utc
                utcDate = DateTime.SpecifyKind(utcDate, DateTimeKind.Utc);

                return utcDate;
            }

            // Return null if parsing fails
            return null;
        }


        public Dictionary<string, string> ExtractArea(string title)
        {
            Dictionary<string, string> values = new Dictionary<string, string>();

            var r = doc.DocumentNode.SelectNodes("//span[text()=\"" + title + "\"]/ancestor::table[1]//tr");
            if (r == null)
                r = doc.DocumentNode.SelectNodes("//td[text()=\"" + title + "\"]/ancestor::table[1]//tr");

            int max = 0;

            if (r == null)
                return values;

            foreach (var row in r)
            {
                var children = row.ChildNodes.Where(o => o.NodeType == HtmlNodeType.Element).ToArray();
                if (max > 0 && children.Length < max)
                    break;
                max = children.Length;

                if (children.Length == 2)
                    values.Add(children[0].InnerText.Trim().Trim(':'), children[1].InnerText.Replace("&nbsp;", " ").Trim('\n').Trim('\r').Trim());
            }

            return values;
        }

        public Dictionary<string, string> ExtractAreaCc(string title)
        {
            Dictionary<string, string> values = new Dictionary<string, string>();

            var r = doc.DocumentNode.SelectNodes("//td[text()=\"" + title + "\"]/ancestor::table[1]//tr");

            int max = 0;

            if (r == null)
                return values;

            foreach (var row in r)
            {
                var children = row.ChildNodes.Where(o => o.NodeType == HtmlNodeType.Element).ToArray();
                if (max > 0 && children.Length < max)
                    break;
                max = children.Length;

                if (children.Length == 4)
                {
                    values.Add(children[0].InnerText.Trim().Trim(':'),
                        children[1].InnerText.Replace("&nbsp;", " ").Trim('\n').Trim('\r').Trim());

                    values.Add(children[2].InnerText.Trim().Trim(':'),
                        children[3].InnerText.Replace("&nbsp;", " ").Trim('\n').Trim('\r').Trim());
                }
            }

            return values;
        }

        public Dictionary<string, string> ExtractAreaTableInNext(string title)
        {
            var values = new Dictionary<string, string>();

            var r = doc.DocumentNode.SelectNodes("//span[text()=\"" + title + "\"]/ancestor::table[1]//tr/following::table[1]/tbody/tr");
            if (r == null)
                r = doc.DocumentNode.SelectNodes("//td[text()=\"" + title + "\"]/ancestor::table[1]//tr/following::table[1]/tr");

            if (r == null)
                r = doc.DocumentNode.SelectNodes("//td[text()=\"" + title + "\"]/ancestor::table[1]//tr/following::table[1]//tr");

            if (r == null)
                return values;

            var titles = r[0].ChildNodes.Where(o => o.NodeType == HtmlNodeType.Element).ToArray();
            var title_values = r[1].ChildNodes.Where(o => o.NodeType == HtmlNodeType.Element).ToArray();

            for (int i = 0; i < titles.Length; i++)
            {
                values.Add(Decode(titles[i].InnerText), Decode(title_values[i].InnerText));
            }

            return values;
        }

        /// <summary>
        /// Every two columns is a key/value set.
        /// </summary>
        /// <param name="title"></param>
        /// <returns></returns>
        public Dictionary<string, string> ExtractEveryTwo(string title)
        {
            var values = new Dictionary<string, string>();

            var rx = doc.DocumentNode.SelectNodes("//span[text()=\"" + title + "\"]/ancestor::table[1]");
            if (rx == null)
                rx = doc.DocumentNode.SelectNodes("//td[text()=\"" + title + "\"]/ancestor::table[1]");

            if (rx == null)
                return values;

            var r = rx.First().SelectNodes(".//td")?
                .Where(w => w.OuterHtml.IndexOf("colspan=\"6\"") == -1)? // possible title message...ignore
                .Where(o => o.NodeType == HtmlNodeType.Element)?
                .ToArray();

            if (r == null)
                return values;

            for (int i = 0; i < r.Length; i = i + 2)
            {

                var key = r[i].InnerText.Trim().Trim(':').Trim('\n').Trim('\r');
                var value = r[i + 1].InnerText.Replace("&nbsp;", " ").Trim().Trim('\n').Trim('\r');

                if (string.IsNullOrWhiteSpace(key) || key == "&nbsp;")
                    continue;

                if (!values.ContainsKey(key))
                    values.Add(key, value);
            }

            return values;
        }
    }
}
