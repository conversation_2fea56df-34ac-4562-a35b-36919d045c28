<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions name="UserService" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:wsx="http://schemas.xmlsoap.org/ws/2004/09/mex" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:wsa10="http://www.w3.org/2005/08/addressing" xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:wsap="http://schemas.xmlsoap.org/ws/2004/08/addressing/policy" xmlns:msc="http://schemas.microsoft.com/ws/2005/12/wsdl/contract" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:wsa="http://schemas.xmlsoap.org/ws/2004/08/addressing" xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:tns="http://tempuri.org/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:wsaw="http://www.w3.org/2006/05/addressing/wsdl" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/">
    <wsp:Policy wsu:Id="BasicHttpBinding_IUserService_policy">
        <wsp:ExactlyOne>
            <wsp:All>
                <sp:TransportBinding xmlns:sp="http://schemas.xmlsoap.org/ws/2005/07/securitypolicy">
                    <wsp:Policy>
                        <sp:TransportToken>
                            <wsp:Policy>
                                <sp:HttpsToken RequireClientCertificate="false"/>
                            </wsp:Policy>
                        </sp:TransportToken>
                        <sp:AlgorithmSuite>
                            <wsp:Policy>
                                <sp:Basic256/>
                            </wsp:Policy>
                        </sp:AlgorithmSuite>
                        <sp:Layout>
                            <wsp:Policy>
                                <sp:Strict/>
                            </wsp:Policy>
                        </sp:Layout>
                    </wsp:Policy>
                </sp:TransportBinding>
            </wsp:All>
        </wsp:ExactlyOne>
    </wsp:Policy>
    <wsdl:types>
        <xsd:schema targetNamespace="http://tempuri.org/Imports">
            <xsd:import schemaLocation="https://services.dispatchanywhere.net/user/v4.242.0/Services/UserService.svc?xsd=xsd0" namespace="http://tempuri.org/"/>
            <xsd:import schemaLocation="https://services.dispatchanywhere.net/user/v4.242.0/Services/UserService.svc?xsd=xsd1" namespace="http://schemas.microsoft.com/2003/10/Serialization/"/>
            <xsd:import schemaLocation="https://services.dispatchanywhere.net/user/v4.242.0/Services/UserService.svc?xsd=xsd2" namespace="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            <xsd:import schemaLocation="https://services.dispatchanywhere.net/user/v4.242.0/Services/UserService.svc?xsd=xsd3" namespace="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
            <xsd:import schemaLocation="https://services.dispatchanywhere.net/user/v4.242.0/Services/UserService.svc?xsd=xsd4" namespace="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model.MotorClubBilling"/>
            <xsd:import schemaLocation="https://services.dispatchanywhere.net/user/v4.242.0/Services/UserService.svc?xsd=xsd5" namespace="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Services.MotorClubBilling"/>
            <xsd:import schemaLocation="https://services.dispatchanywhere.net/user/v4.242.0/Services/UserService.svc?xsd=xsd6" namespace="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model.TowLien"/>
            <xsd:import schemaLocation="https://services.dispatchanywhere.net/user/v4.242.0/Services/UserService.svc?xsd=xsd7" namespace="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Services.BeaconVIN"/>
            <xsd:import schemaLocation="https://services.dispatchanywhere.net/user/v4.242.0/Services/UserService.svc?xsd=xsd8" namespace="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model.TowMagic"/>
            <xsd:import schemaLocation="https://services.dispatchanywhere.net/user/v4.242.0/Services/UserService.svc?xsd=xsd9" namespace="http://schemas.datacontract.org/2004/07/System.Collections.Generic"/>
            <xsd:import schemaLocation="https://services.dispatchanywhere.net/user/v4.242.0/Services/UserService.svc?xsd=xsd10" namespace="http://schemas.datacontract.org/2004/07/System"/>
            <xsd:import schemaLocation="https://services.dispatchanywhere.net/user/v4.242.0/Services/UserService.svc?xsd=xsd11" namespace="http://schemas.datacontract.org/2004/07/System.ComponentModel.DataAnnotations"/>
            <xsd:import schemaLocation="https://services.dispatchanywhere.net/user/v4.242.0/Services/UserService.svc?xsd=xsd12" namespace="http://schemas.datacontract.org/2004/07/System.ComponentModel"/>
        </xsd:schema>
    </wsdl:types>
    <wsdl:message name="IUserService_SaveCreditMemo_InputMessage">
        <wsdl:part name="parameters" element="tns:SaveCreditMemo"/>
    </wsdl:message>
    <wsdl:message name="IUserService_SaveCreditMemo_OutputMessage">
        <wsdl:part name="parameters" element="tns:SaveCreditMemoResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_RefundCreditMemos_InputMessage">
        <wsdl:part name="parameters" element="tns:RefundCreditMemos"/>
    </wsdl:message>
    <wsdl:message name="IUserService_RefundCreditMemos_OutputMessage">
        <wsdl:part name="parameters" element="tns:RefundCreditMemosResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_FindReceipts_InputMessage">
        <wsdl:part name="parameters" element="tns:FindReceipts"/>
    </wsdl:message>
    <wsdl:message name="IUserService_FindReceipts_OutputMessage">
        <wsdl:part name="parameters" element="tns:FindReceiptsResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetInvoiceIdsForQBExport_InputMessage">
        <wsdl:part name="parameters" element="tns:GetInvoiceIdsForQBExport"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetInvoiceIdsForQBExport_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetInvoiceIdsForQBExportResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_ProcessBactchIdsForQBExport_InputMessage">
        <wsdl:part name="parameters" element="tns:ProcessBactchIdsForQBExport"/>
    </wsdl:message>
    <wsdl:message name="IUserService_ProcessBactchIdsForQBExport_OutputMessage">
        <wsdl:part name="parameters" element="tns:ProcessBactchIdsForQBExportResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetCreditCardTransaction_InputMessage">
        <wsdl:part name="parameters" element="tns:GetCreditCardTransaction"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetCreditCardTransaction_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetCreditCardTransactionResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_CheckMCInvoiceEnrollmentStatus_InputMessage">
        <wsdl:part name="parameters" element="tns:CheckMCInvoiceEnrollmentStatus"/>
    </wsdl:message>
    <wsdl:message name="IUserService_CheckMCInvoiceEnrollmentStatus_OutputMessage">
        <wsdl:part name="parameters" element="tns:CheckMCInvoiceEnrollmentStatusResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetMotorClubInvoice_InputMessage">
        <wsdl:part name="parameters" element="tns:GetMotorClubInvoice"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetMotorClubInvoice_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetMotorClubInvoiceResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_UpdateMotorClubInvoice_InputMessage">
        <wsdl:part name="parameters" element="tns:UpdateMotorClubInvoice"/>
    </wsdl:message>
    <wsdl:message name="IUserService_UpdateMotorClubInvoice_OutputMessage">
        <wsdl:part name="parameters" element="tns:UpdateMotorClubInvoiceResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetBillingMCKeys_InputMessage">
        <wsdl:part name="parameters" element="tns:GetBillingMCKeys"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetBillingMCKeys_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetBillingMCKeysResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetReconciliationMCKeys_InputMessage">
        <wsdl:part name="parameters" element="tns:GetReconciliationMCKeys"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetReconciliationMCKeys_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetReconciliationMCKeysResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetSurchargeServiceRate_InputMessage">
        <wsdl:part name="parameters" element="tns:GetSurchargeServiceRate"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetSurchargeServiceRate_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetSurchargeServiceRateResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_FindMotorClubPayments_InputMessage">
        <wsdl:part name="parameters" element="tns:FindMotorClubPayments"/>
    </wsdl:message>
    <wsdl:message name="IUserService_FindMotorClubPayments_OutputMessage">
        <wsdl:part name="parameters" element="tns:FindMotorClubPaymentsResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetLoggedMotorClubPayments_InputMessage">
        <wsdl:part name="parameters" element="tns:GetLoggedMotorClubPayments"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetLoggedMotorClubPayments_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetLoggedMotorClubPaymentsResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_ApplyMotorClubPayments_InputMessage">
        <wsdl:part name="parameters" element="tns:ApplyMotorClubPayments"/>
    </wsdl:message>
    <wsdl:message name="IUserService_ApplyMotorClubPayments_OutputMessage">
        <wsdl:part name="parameters" element="tns:ApplyMotorClubPaymentsResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetImpoundLots_InputMessage">
        <wsdl:part name="parameters" element="tns:GetImpoundLots"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetImpoundLots_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetImpoundLotsResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetImpoundLotVehicleCounts_InputMessage">
        <wsdl:part name="parameters" element="tns:GetImpoundLotVehicleCounts"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetImpoundLotVehicleCounts_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetImpoundLotVehicleCountsResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetSelectableZones_InputMessage">
        <wsdl:part name="parameters" element="tns:GetSelectableZones"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetSelectableZones_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetSelectableZonesResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetSelectablePriorities_InputMessage">
        <wsdl:part name="parameters" element="tns:GetSelectablePriorities"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetSelectablePriorities_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetSelectablePrioritiesResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetSelectableTruckTypes_InputMessage">
        <wsdl:part name="parameters" element="tns:GetSelectableTruckTypes"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetSelectableTruckTypes_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetSelectableTruckTypesResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetSelectablePaymentMethods_InputMessage">
        <wsdl:part name="parameters" element="tns:GetSelectablePaymentMethods"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetSelectablePaymentMethods_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetSelectablePaymentMethodsResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetVehicleStatuses_InputMessage">
        <wsdl:part name="parameters" element="tns:GetVehicleStatuses"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetVehicleStatuses_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetVehicleStatusesResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetDriverStatuses_InputMessage">
        <wsdl:part name="parameters" element="tns:GetDriverStatuses"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetDriverStatuses_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetDriverStatusesResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetJobStatusDescriptions_InputMessage">
        <wsdl:part name="parameters" element="tns:GetJobStatusDescriptions"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetJobStatusDescriptions_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetJobStatusDescriptionsResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetAssignedStatusDescriptions_InputMessage">
        <wsdl:part name="parameters" element="tns:GetAssignedStatusDescriptions"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetAssignedStatusDescriptions_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetAssignedStatusDescriptionsResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetOfferStatusDescriptions_InputMessage">
        <wsdl:part name="parameters" element="tns:GetOfferStatusDescriptions"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetOfferStatusDescriptions_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetOfferStatusDescriptionsResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetDepartments_InputMessage">
        <wsdl:part name="parameters" element="tns:GetDepartments"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetDepartments_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetDepartmentsResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetSelectableReasons_InputMessage">
        <wsdl:part name="parameters" element="tns:GetSelectableReasons"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetSelectableReasons_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetSelectableReasonsResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetCancelReasons_InputMessage">
        <wsdl:part name="parameters" element="tns:GetCancelReasons"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetCancelReasons_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetCancelReasonsResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetCities_InputMessage">
        <wsdl:part name="parameters" element="tns:GetCities"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetCities_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetCitiesResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetStates_InputMessage">
        <wsdl:part name="parameters" element="tns:GetStates"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetStates_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetStatesResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetVehicleColors_InputMessage">
        <wsdl:part name="parameters" element="tns:GetVehicleColors"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetVehicleColors_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetVehicleColorsResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetKeyLocations_InputMessage">
        <wsdl:part name="parameters" element="tns:GetKeyLocations"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetKeyLocations_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetKeyLocationsResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetVehicleStyles_InputMessage">
        <wsdl:part name="parameters" element="tns:GetVehicleStyles"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetVehicleStyles_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetVehicleStylesResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetForcedStatusDescriptions_InputMessage">
        <wsdl:part name="parameters" element="tns:GetForcedStatusDescriptions"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetForcedStatusDescriptions_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetForcedStatusDescriptionsResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetGroup1CustomDictionary1_InputMessage">
        <wsdl:part name="parameters" element="tns:GetGroup1CustomDictionary1"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetGroup1CustomDictionary1_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetGroup1CustomDictionary1Response"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetGroup1CustomDictionary2_InputMessage">
        <wsdl:part name="parameters" element="tns:GetGroup1CustomDictionary2"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetGroup1CustomDictionary2_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetGroup1CustomDictionary2Response"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetGroup2CustomDictionary1_InputMessage">
        <wsdl:part name="parameters" element="tns:GetGroup2CustomDictionary1"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetGroup2CustomDictionary1_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetGroup2CustomDictionary1Response"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetGroup2CustomDictionary2_InputMessage">
        <wsdl:part name="parameters" element="tns:GetGroup2CustomDictionary2"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetGroup2CustomDictionary2_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetGroup2CustomDictionary2Response"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetAccountTypes_InputMessage">
        <wsdl:part name="parameters" element="tns:GetAccountTypes"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetAccountTypes_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetAccountTypesResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetTaxRates_InputMessage">
        <wsdl:part name="parameters" element="tns:GetTaxRates"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetTaxRates_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetTaxRatesResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_ConnectToPush_InputMessage">
        <wsdl:part name="parameters" element="tns:ConnectToPush"/>
    </wsdl:message>
    <wsdl:message name="IUserService_ConnectToPush_OutputMessage">
        <wsdl:part name="parameters" element="tns:ConnectToPushResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_DisconnectFromPush_InputMessage">
        <wsdl:part name="parameters" element="tns:DisconnectFromPush"/>
    </wsdl:message>
    <wsdl:message name="IUserService_DisconnectFromPush_OutputMessage">
        <wsdl:part name="parameters" element="tns:DisconnectFromPushResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetVehicleDetailsURL_InputMessage">
        <wsdl:part name="parameters" element="tns:GetVehicleDetailsURL"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetVehicleDetailsURL_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetVehicleDetailsURLResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetVehicleSearchUrl_InputMessage">
        <wsdl:part name="parameters" element="tns:GetVehicleSearchUrl"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetVehicleSearchUrl_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetVehicleSearchUrlResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_TowSpecSearch_InputMessage">
        <wsdl:part name="parameters" element="tns:TowSpecSearch"/>
    </wsdl:message>
    <wsdl:message name="IUserService_TowSpecSearch_OutputMessage">
        <wsdl:part name="parameters" element="tns:TowSpecSearchResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetAllTowSpecVehiclesIfNewer_InputMessage">
        <wsdl:part name="parameters" element="tns:GetAllTowSpecVehiclesIfNewer"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetAllTowSpecVehiclesIfNewer_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetAllTowSpecVehiclesIfNewerResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetAllTowSpecVehicles_InputMessage">
        <wsdl:part name="parameters" element="tns:GetAllTowSpecVehicles"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetAllTowSpecVehicles_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetAllTowSpecVehiclesResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_SendToTowLien_InputMessage">
        <wsdl:part name="parameters" element="tns:SendToTowLien"/>
    </wsdl:message>
    <wsdl:message name="IUserService_SendToTowLien_OutputMessage">
        <wsdl:part name="parameters" element="tns:SendToTowLienResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetTowLienLocations_InputMessage">
        <wsdl:part name="parameters" element="tns:GetTowLienLocations"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetTowLienLocations_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetTowLienLocationsResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_DecodeVin_InputMessage">
        <wsdl:part name="parameters" element="tns:DecodeVin"/>
    </wsdl:message>
    <wsdl:message name="IUserService_DecodeVin_OutputMessage">
        <wsdl:part name="parameters" element="tns:DecodeVinResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_LookupVINByLicensePlate_InputMessage">
        <wsdl:part name="parameters" element="tns:LookupVINByLicensePlate"/>
    </wsdl:message>
    <wsdl:message name="IUserService_LookupVINByLicensePlate_OutputMessage">
        <wsdl:part name="parameters" element="tns:LookupVINByLicensePlateResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetAuction_InputMessage">
        <wsdl:part name="parameters" element="tns:GetAuction"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetAuction_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetAuctionResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetAuctions_InputMessage">
        <wsdl:part name="parameters" element="tns:GetAuctions"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetAuctions_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetAuctionsResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetAuctionAndUpcoming_InputMessage">
        <wsdl:part name="parameters" element="tns:GetAuctionAndUpcoming"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetAuctionAndUpcoming_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetAuctionAndUpcomingResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetUpcomingAuctions_InputMessage">
        <wsdl:part name="parameters" element="tns:GetUpcomingAuctions"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetUpcomingAuctions_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetUpcomingAuctionsResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_SaveAuction_InputMessage">
        <wsdl:part name="parameters" element="tns:SaveAuction"/>
    </wsdl:message>
    <wsdl:message name="IUserService_SaveAuction_OutputMessage">
        <wsdl:part name="parameters" element="tns:SaveAuctionResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetJobPhotos_InputMessage">
        <wsdl:part name="parameters" element="tns:GetJobPhotos"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetJobPhotos_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetJobPhotosResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetJobPhotoUploadUrl_InputMessage">
        <wsdl:part name="parameters" element="tns:GetJobPhotoUploadUrl"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetJobPhotoUploadUrl_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetJobPhotoUploadUrlResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetJobPhotoUploadToken_InputMessage">
        <wsdl:part name="parameters" element="tns:GetJobPhotoUploadToken"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetJobPhotoUploadToken_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetJobPhotoUploadTokenResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_SetJobPhotoPrivate_InputMessage">
        <wsdl:part name="parameters" element="tns:SetJobPhotoPrivate"/>
    </wsdl:message>
    <wsdl:message name="IUserService_SetJobPhotoPrivate_OutputMessage">
        <wsdl:part name="parameters" element="tns:SetJobPhotoPrivateResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_SetJobPhotoPublic_InputMessage">
        <wsdl:part name="parameters" element="tns:SetJobPhotoPublic"/>
    </wsdl:message>
    <wsdl:message name="IUserService_SetJobPhotoPublic_OutputMessage">
        <wsdl:part name="parameters" element="tns:SetJobPhotoPublicResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_DeleteJobPhoto_InputMessage">
        <wsdl:part name="parameters" element="tns:DeleteJobPhoto"/>
    </wsdl:message>
    <wsdl:message name="IUserService_DeleteJobPhoto_OutputMessage">
        <wsdl:part name="parameters" element="tns:DeleteJobPhotoResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_SendJobPhotosShareUrl_InputMessage">
        <wsdl:part name="parameters" element="tns:SendJobPhotosShareUrl"/>
    </wsdl:message>
    <wsdl:message name="IUserService_SendJobPhotosShareUrl_OutputMessage">
        <wsdl:part name="parameters" element="tns:SendJobPhotosShareUrlResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetPhotoCount_InputMessage">
        <wsdl:part name="parameters" element="tns:GetPhotoCount"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetPhotoCount_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetPhotoCountResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetSecurePhotoUploadUrl_InputMessage">
        <wsdl:part name="parameters" element="tns:GetSecurePhotoUploadUrl"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetSecurePhotoUploadUrl_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetSecurePhotoUploadUrlResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_SendReportEmail_InputMessage">
        <wsdl:part name="parameters" element="tns:SendReportEmail"/>
    </wsdl:message>
    <wsdl:message name="IUserService_SendReportEmail_OutputMessage">
        <wsdl:part name="parameters" element="tns:SendReportEmailResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetReportSender_InputMessage">
        <wsdl:part name="parameters" element="tns:GetReportSender"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetReportSender_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetReportSenderResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_SendReportFax_InputMessage">
        <wsdl:part name="parameters" element="tns:SendReportFax"/>
    </wsdl:message>
    <wsdl:message name="IUserService_SendReportFax_OutputMessage">
        <wsdl:part name="parameters" element="tns:SendReportFaxResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetDALienUrl_InputMessage">
        <wsdl:part name="parameters" element="tns:GetDALienUrl"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetDALienUrl_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetDALienUrlResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_LogUploadedJobPhotos_InputMessage">
        <wsdl:part name="parameters" element="tns:LogUploadedJobPhotos"/>
    </wsdl:message>
    <wsdl:message name="IUserService_LogUploadedJobPhotos_OutputMessage">
        <wsdl:part name="parameters" element="tns:LogUploadedJobPhotosResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_LogDeletedJobPhotos_InputMessage">
        <wsdl:part name="parameters" element="tns:LogDeletedJobPhotos"/>
    </wsdl:message>
    <wsdl:message name="IUserService_LogDeletedJobPhotos_OutputMessage">
        <wsdl:part name="parameters" element="tns:LogDeletedJobPhotosResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_UpdateOfferStatus_InputMessage">
        <wsdl:part name="parameters" element="tns:UpdateOfferStatus"/>
    </wsdl:message>
    <wsdl:message name="IUserService_UpdateOfferStatus_OutputMessage">
        <wsdl:part name="parameters" element="tns:UpdateOfferStatusResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_PostApprovalUpdate_InputMessage">
        <wsdl:part name="parameters" element="tns:PostApprovalUpdate"/>
    </wsdl:message>
    <wsdl:message name="IUserService_PostApprovalUpdate_OutputMessage">
        <wsdl:part name="parameters" element="tns:PostApprovalUpdateResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetServiceMappingsFromJson_InputMessage">
        <wsdl:part name="parameters" element="tns:GetServiceMappingsFromJson"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetServiceMappingsFromJson_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetServiceMappingsFromJsonResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetServiceMappingsFromTasks_InputMessage">
        <wsdl:part name="parameters" element="tns:GetServiceMappingsFromTasks"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetServiceMappingsFromTasks_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetServiceMappingsFromTasksResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetUTCNow_InputMessage">
        <wsdl:part name="parameters" element="tns:GetUTCNow"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetUTCNow_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetUTCNowResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_AgreeToEULA_InputMessage">
        <wsdl:part name="parameters" element="tns:AgreeToEULA"/>
    </wsdl:message>
    <wsdl:message name="IUserService_AgreeToEULA_OutputMessage">
        <wsdl:part name="parameters" element="tns:AgreeToEULAResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetChangelogArticleContent_InputMessage">
        <wsdl:part name="parameters" element="tns:GetChangelogArticleContent"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetChangelogArticleContent_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetChangelogArticleContentResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetBasicBSCompanies_InputMessage">
        <wsdl:part name="parameters" element="tns:GetBasicBSCompanies"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetBasicBSCompanies_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetBasicBSCompaniesResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetCurrentBasicBSCompany_InputMessage">
        <wsdl:part name="parameters" element="tns:GetCurrentBasicBSCompany"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetCurrentBasicBSCompany_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetCurrentBasicBSCompanyResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetCurrentUser_InputMessage">
        <wsdl:part name="parameters" element="tns:GetCurrentUser"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetCurrentUser_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetCurrentUserResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetBasicDAUsers_InputMessage">
        <wsdl:part name="parameters" element="tns:GetBasicDAUsers"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetBasicDAUsers_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetBasicDAUsersResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetCompany_InputMessage">
        <wsdl:part name="parameters" element="tns:GetCompany"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetCompany_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetCompanyResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetCompanyQuickBooksVersion_InputMessage">
        <wsdl:part name="parameters" element="tns:GetCompanyQuickBooksVersion"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetCompanyQuickBooksVersion_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetCompanyQuickBooksVersionResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetDivision_InputMessage">
        <wsdl:part name="parameters" element="tns:GetDivision"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetDivision_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetDivisionResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetBasicDivisions_InputMessage">
        <wsdl:part name="parameters" element="tns:GetBasicDivisions"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetBasicDivisions_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetBasicDivisionsResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetDivisions_InputMessage">
        <wsdl:part name="parameters" element="tns:GetDivisions"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetDivisions_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetDivisionsResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetDivisionAccess_InputMessage">
        <wsdl:part name="parameters" element="tns:GetDivisionAccess"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetDivisionAccess_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetDivisionAccessResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetDriverFields_InputMessage">
        <wsdl:part name="parameters" element="tns:GetDriverFields"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetDriverFields_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetDriverFieldsResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetAccount_InputMessage">
        <wsdl:part name="parameters" element="tns:GetAccount"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetAccount_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetAccountResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetAccountInfo_InputMessage">
        <wsdl:part name="parameters" element="tns:GetAccountInfo"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetAccountInfo_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetAccountInfoResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetBasicAccounts_InputMessage">
        <wsdl:part name="parameters" element="tns:GetBasicAccounts"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetBasicAccounts_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetBasicAccountsResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetAccounts_InputMessage">
        <wsdl:part name="parameters" element="tns:GetAccounts"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetAccounts_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetAccountsResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetAccountsListItems_InputMessage">
        <wsdl:part name="parameters" element="tns:GetAccountsListItems"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetAccountsListItems_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetAccountsListItemsResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetUsableAccountsForDivision_InputMessage">
        <wsdl:part name="parameters" element="tns:GetUsableAccountsForDivision"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetUsableAccountsForDivision_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetUsableAccountsForDivisionResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetUsableAccountsForDivisionListItems_InputMessage">
        <wsdl:part name="parameters" element="tns:GetUsableAccountsForDivisionListItems"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetUsableAccountsForDivisionListItems_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetUsableAccountsForDivisionListItemsResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetAccountAvailableCredit_InputMessage">
        <wsdl:part name="parameters" element="tns:GetAccountAvailableCredit"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetAccountAvailableCredit_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetAccountAvailableCreditResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetJobTemplateFieldsForAccount_InputMessage">
        <wsdl:part name="parameters" element="tns:GetJobTemplateFieldsForAccount"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetJobTemplateFieldsForAccount_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetJobTemplateFieldsForAccountResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetJobTemplateFieldsForDefault_InputMessage">
        <wsdl:part name="parameters" element="tns:GetJobTemplateFieldsForDefault"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetJobTemplateFieldsForDefault_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetJobTemplateFieldsForDefaultResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetServiceRatesForJob_InputMessage">
        <wsdl:part name="parameters" element="tns:GetServiceRatesForJob"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetServiceRatesForJob_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetServiceRatesForJobResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetAccountDefaultServiceRatesForJob_InputMessage">
        <wsdl:part name="parameters" element="tns:GetAccountDefaultServiceRatesForJob"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetAccountDefaultServiceRatesForJob_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetAccountDefaultServiceRatesForJobResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetSecuritySettings_InputMessage">
        <wsdl:part name="parameters" element="tns:GetSecuritySettings"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetSecuritySettings_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetSecuritySettingsResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetUserRoles_InputMessage">
        <wsdl:part name="parameters" element="tns:GetUserRoles"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetUserRoles_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetUserRolesResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetDispatchViewSettings_InputMessage">
        <wsdl:part name="parameters" element="tns:GetDispatchViewSettings"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetDispatchViewSettings_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetDispatchViewSettingsResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_SaveDispatchViewSettings_InputMessage">
        <wsdl:part name="parameters" element="tns:SaveDispatchViewSettings"/>
    </wsdl:message>
    <wsdl:message name="IUserService_SaveDispatchViewSettings_OutputMessage">
        <wsdl:part name="parameters" element="tns:SaveDispatchViewSettingsResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_SerializeServiceCalculatorSettings_InputMessage">
        <wsdl:part name="parameters" element="tns:SerializeServiceCalculatorSettings"/>
    </wsdl:message>
    <wsdl:message name="IUserService_SerializeServiceCalculatorSettings_OutputMessage">
        <wsdl:part name="parameters" element="tns:SerializeServiceCalculatorSettingsResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_DeserializeServiceCalculatorSettings_InputMessage">
        <wsdl:part name="parameters" element="tns:DeserializeServiceCalculatorSettings"/>
    </wsdl:message>
    <wsdl:message name="IUserService_DeserializeServiceCalculatorSettings_OutputMessage">
        <wsdl:part name="parameters" element="tns:DeserializeServiceCalculatorSettingsResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_SendEmail_InputMessage">
        <wsdl:part name="parameters" element="tns:SendEmail"/>
    </wsdl:message>
    <wsdl:message name="IUserService_SendEmail_OutputMessage">
        <wsdl:part name="parameters" element="tns:SendEmailResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetAccountBillingContact_InputMessage">
        <wsdl:part name="parameters" element="tns:GetAccountBillingContact"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetAccountBillingContact_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetAccountBillingContactResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetJobAutoNumberValue_InputMessage">
        <wsdl:part name="parameters" element="tns:GetJobAutoNumberValue"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetJobAutoNumberValue_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetJobAutoNumberValueResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetActiveJobs_InputMessage">
        <wsdl:part name="parameters" element="tns:GetActiveJobs"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetActiveJobs_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetActiveJobsResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetAssignedJobsForDriver_InputMessage">
        <wsdl:part name="parameters" element="tns:GetAssignedJobsForDriver"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetAssignedJobsForDriver_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetAssignedJobsForDriverResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetJob_InputMessage">
        <wsdl:part name="parameters" element="tns:GetJob"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetJob_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetJobResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetJobForEdit_InputMessage">
        <wsdl:part name="parameters" element="tns:GetJobForEdit"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetJobForEdit_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetJobForEditResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_SaveJob_InputMessage">
        <wsdl:part name="parameters" element="tns:SaveJob"/>
    </wsdl:message>
    <wsdl:message name="IUserService_SaveJob_OutputMessage">
        <wsdl:part name="parameters" element="tns:SaveJobResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_CreateJob_InputMessage">
        <wsdl:part name="parameters" element="tns:CreateJob"/>
    </wsdl:message>
    <wsdl:message name="IUserService_CreateJob_OutputMessage">
        <wsdl:part name="parameters" element="tns:CreateJobResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_UpdateJob_InputMessage">
        <wsdl:part name="parameters" element="tns:UpdateJob"/>
    </wsdl:message>
    <wsdl:message name="IUserService_UpdateJob_OutputMessage">
        <wsdl:part name="parameters" element="tns:UpdateJobResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_AssignJob_InputMessage">
        <wsdl:part name="parameters" element="tns:AssignJob"/>
    </wsdl:message>
    <wsdl:message name="IUserService_AssignJob_OutputMessage">
        <wsdl:part name="parameters" element="tns:AssignJobResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_UnassignJob_InputMessage">
        <wsdl:part name="parameters" element="tns:UnassignJob"/>
    </wsdl:message>
    <wsdl:message name="IUserService_UnassignJob_OutputMessage">
        <wsdl:part name="parameters" element="tns:UnassignJobResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_TimeStampJob_InputMessage">
        <wsdl:part name="parameters" element="tns:TimeStampJob"/>
    </wsdl:message>
    <wsdl:message name="IUserService_TimeStampJob_OutputMessage">
        <wsdl:part name="parameters" element="tns:TimeStampJobResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_MoveJobToWaiting_InputMessage">
        <wsdl:part name="parameters" element="tns:MoveJobToWaiting"/>
    </wsdl:message>
    <wsdl:message name="IUserService_MoveJobToWaiting_OutputMessage">
        <wsdl:part name="parameters" element="tns:MoveJobToWaitingResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_MoveJobToHolding_InputMessage">
        <wsdl:part name="parameters" element="tns:MoveJobToHolding"/>
    </wsdl:message>
    <wsdl:message name="IUserService_MoveJobToHolding_OutputMessage">
        <wsdl:part name="parameters" element="tns:MoveJobToHoldingResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetPromptOnFinishFields_InputMessage">
        <wsdl:part name="parameters" element="tns:GetPromptOnFinishFields"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetPromptOnFinishFields_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetPromptOnFinishFieldsResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetClearCodes_InputMessage">
        <wsdl:part name="parameters" element="tns:GetClearCodes"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetClearCodes_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetClearCodesResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetAllClearCodes_InputMessage">
        <wsdl:part name="parameters" element="tns:GetAllClearCodes"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetAllClearCodes_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetAllClearCodesResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_FinishJob_InputMessage">
        <wsdl:part name="parameters" element="tns:FinishJob"/>
    </wsdl:message>
    <wsdl:message name="IUserService_FinishJob_OutputMessage">
        <wsdl:part name="parameters" element="tns:FinishJobResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_UndoFinishJob_InputMessage">
        <wsdl:part name="parameters" element="tns:UndoFinishJob"/>
    </wsdl:message>
    <wsdl:message name="IUserService_UndoFinishJob_OutputMessage">
        <wsdl:part name="parameters" element="tns:UndoFinishJobResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_CancelJob_InputMessage">
        <wsdl:part name="parameters" element="tns:CancelJob"/>
    </wsdl:message>
    <wsdl:message name="IUserService_CancelJob_OutputMessage">
        <wsdl:part name="parameters" element="tns:CancelJobResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetJobResourceFromDriverId_InputMessage">
        <wsdl:part name="parameters" element="tns:GetJobResourceFromDriverId"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetJobResourceFromDriverId_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetJobResourceFromDriverIdResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_FindJobs_InputMessage">
        <wsdl:part name="parameters" element="tns:FindJobs"/>
    </wsdl:message>
    <wsdl:message name="IUserService_FindJobs_OutputMessage">
        <wsdl:part name="parameters" element="tns:FindJobsResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_BasicFindJobs_InputMessage">
        <wsdl:part name="parameters" element="tns:BasicFindJobs"/>
    </wsdl:message>
    <wsdl:message name="IUserService_BasicFindJobs_OutputMessage">
        <wsdl:part name="parameters" element="tns:BasicFindJobsResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetJobServices_InputMessage">
        <wsdl:part name="parameters" element="tns:GetJobServices"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetJobServices_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetJobServicesResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetJobServicesCount_InputMessage">
        <wsdl:part name="parameters" element="tns:GetJobServicesCount"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetJobServicesCount_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetJobServicesCountResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetJobService_InputMessage">
        <wsdl:part name="parameters" element="tns:GetJobService"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetJobService_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetJobServiceResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_SaveJobService_InputMessage">
        <wsdl:part name="parameters" element="tns:SaveJobService"/>
    </wsdl:message>
    <wsdl:message name="IUserService_SaveJobService_OutputMessage">
        <wsdl:part name="parameters" element="tns:SaveJobServiceResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetDriver_InputMessage">
        <wsdl:part name="parameters" element="tns:GetDriver"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetDriver_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetDriverResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetBasicDrivers_InputMessage">
        <wsdl:part name="parameters" element="tns:GetBasicDrivers"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetBasicDrivers_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetBasicDriversResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetDrivers_InputMessage">
        <wsdl:part name="parameters" element="tns:GetDrivers"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetDrivers_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetDriversResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetAssignableDrivers_InputMessage">
        <wsdl:part name="parameters" element="tns:GetAssignableDrivers"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetAssignableDrivers_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetAssignableDriversResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_SaveDriver_InputMessage">
        <wsdl:part name="parameters" element="tns:SaveDriver"/>
    </wsdl:message>
    <wsdl:message name="IUserService_SaveDriver_OutputMessage">
        <wsdl:part name="parameters" element="tns:SaveDriverResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetVehicle_InputMessage">
        <wsdl:part name="parameters" element="tns:GetVehicle"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetVehicle_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetVehicleResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetBasicVehicles_InputMessage">
        <wsdl:part name="parameters" element="tns:GetBasicVehicles"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetBasicVehicles_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetBasicVehiclesResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetAssignableVehicles_InputMessage">
        <wsdl:part name="parameters" element="tns:GetAssignableVehicles"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetAssignableVehicles_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetAssignableVehiclesResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetTrackableVehicles_InputMessage">
        <wsdl:part name="parameters" element="tns:GetTrackableVehicles"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetTrackableVehicles_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetTrackableVehiclesResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_SaveVehicle_InputMessage">
        <wsdl:part name="parameters" element="tns:SaveVehicle"/>
    </wsdl:message>
    <wsdl:message name="IUserService_SaveVehicle_OutputMessage">
        <wsdl:part name="parameters" element="tns:SaveVehicleResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_LinkResources_InputMessage">
        <wsdl:part name="parameters" element="tns:LinkResources"/>
    </wsdl:message>
    <wsdl:message name="IUserService_LinkResources_OutputMessage">
        <wsdl:part name="parameters" element="tns:LinkResourcesResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetDriverLastAssignTime_InputMessage">
        <wsdl:part name="parameters" element="tns:GetDriverLastAssignTime"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetDriverLastAssignTime_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetDriverLastAssignTimeResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_ChangeDriverStatus_InputMessage">
        <wsdl:part name="parameters" element="tns:ChangeDriverStatus"/>
    </wsdl:message>
    <wsdl:message name="IUserService_ChangeDriverStatus_OutputMessage">
        <wsdl:part name="parameters" element="tns:ChangeDriverStatusResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_ChangeVehicleStatus_InputMessage">
        <wsdl:part name="parameters" element="tns:ChangeVehicleStatus"/>
    </wsdl:message>
    <wsdl:message name="IUserService_ChangeVehicleStatus_OutputMessage">
        <wsdl:part name="parameters" element="tns:ChangeVehicleStatusResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_CreateTowOutJob_InputMessage">
        <wsdl:part name="parameters" element="tns:CreateTowOutJob"/>
    </wsdl:message>
    <wsdl:message name="IUserService_CreateTowOutJob_OutputMessage">
        <wsdl:part name="parameters" element="tns:CreateTowOutJobResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_CreateReTowJob_InputMessage">
        <wsdl:part name="parameters" element="tns:CreateReTowJob"/>
    </wsdl:message>
    <wsdl:message name="IUserService_CreateReTowJob_OutputMessage">
        <wsdl:part name="parameters" element="tns:CreateReTowJobResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_DuplicateJob_InputMessage">
        <wsdl:part name="parameters" element="tns:DuplicateJob"/>
    </wsdl:message>
    <wsdl:message name="IUserService_DuplicateJob_OutputMessage">
        <wsdl:part name="parameters" element="tns:DuplicateJobResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_UpdateJobGeoPoint_InputMessage">
        <wsdl:part name="parameters" element="tns:UpdateJobGeoPoint"/>
    </wsdl:message>
    <wsdl:message name="IUserService_UpdateJobGeoPoint_OutputMessage">
        <wsdl:part name="parameters" element="tns:UpdateJobGeoPointResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_UpdateVehicleGeoPoint_InputMessage">
        <wsdl:part name="parameters" element="tns:UpdateVehicleGeoPoint"/>
    </wsdl:message>
    <wsdl:message name="IUserService_UpdateVehicleGeoPoint_OutputMessage">
        <wsdl:part name="parameters" element="tns:UpdateVehicleGeoPointResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_UpdateDriverGeoPoint_InputMessage">
        <wsdl:part name="parameters" element="tns:UpdateDriverGeoPoint"/>
    </wsdl:message>
    <wsdl:message name="IUserService_UpdateDriverGeoPoint_OutputMessage">
        <wsdl:part name="parameters" element="tns:UpdateDriverGeoPointResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_HideDriverFromMap_InputMessage">
        <wsdl:part name="parameters" element="tns:HideDriverFromMap"/>
    </wsdl:message>
    <wsdl:message name="IUserService_HideDriverFromMap_OutputMessage">
        <wsdl:part name="parameters" element="tns:HideDriverFromMapResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_HideVehicleFromMap_InputMessage">
        <wsdl:part name="parameters" element="tns:HideVehicleFromMap"/>
    </wsdl:message>
    <wsdl:message name="IUserService_HideVehicleFromMap_OutputMessage">
        <wsdl:part name="parameters" element="tns:HideVehicleFromMapResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_AddJobMessage_InputMessage">
        <wsdl:part name="parameters" element="tns:AddJobMessage"/>
    </wsdl:message>
    <wsdl:message name="IUserService_AddJobMessage_OutputMessage">
        <wsdl:part name="parameters" element="tns:AddJobMessageResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetJobMessages_InputMessage">
        <wsdl:part name="parameters" element="tns:GetJobMessages"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetJobMessages_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetJobMessagesResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetJobMessage_InputMessage">
        <wsdl:part name="parameters" element="tns:GetJobMessage"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetJobMessage_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetJobMessageResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetJobHistory_InputMessage">
        <wsdl:part name="parameters" element="tns:GetJobHistory"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetJobHistory_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetJobHistoryResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetTowLienHistory_InputMessage">
        <wsdl:part name="parameters" element="tns:GetTowLienHistory"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetTowLienHistory_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetTowLienHistoryResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetDistancesFromResourcesToJobViaDistanceMatrix_InputMessage">
        <wsdl:part name="parameters" element="tns:GetDistancesFromResourcesToJobViaDistanceMatrix"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetDistancesFromResourcesToJobViaDistanceMatrix_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetDistancesFromResourcesToJobViaDistanceMatrixResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_SaveDivisionGeoLocation_InputMessage">
        <wsdl:part name="parameters" element="tns:SaveDivisionGeoLocation"/>
    </wsdl:message>
    <wsdl:message name="IUserService_SaveDivisionGeoLocation_OutputMessage">
        <wsdl:part name="parameters" element="tns:SaveDivisionGeoLocationResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetOfferPreview_InputMessage">
        <wsdl:part name="parameters" element="tns:GetOfferPreview"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetOfferPreview_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetOfferPreviewResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetPostApprovalPreview_InputMessage">
        <wsdl:part name="parameters" element="tns:GetPostApprovalPreview"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetPostApprovalPreview_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetPostApprovalPreviewResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetMCAdditionalServicesPreview_InputMessage">
        <wsdl:part name="parameters" element="tns:GetMCAdditionalServicesPreview"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetMCAdditionalServicesPreview_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetMCAdditionalServicesPreviewResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetJobResourceTruckTypes_InputMessage">
        <wsdl:part name="parameters" element="tns:GetJobResourceTruckTypes"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetJobResourceTruckTypes_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetJobResourceTruckTypesResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_SendCustomerLocationLink_InputMessage">
        <wsdl:part name="parameters" element="tns:SendCustomerLocationLink"/>
    </wsdl:message>
    <wsdl:message name="IUserService_SendCustomerLocationLink_OutputMessage">
        <wsdl:part name="parameters" element="tns:SendCustomerLocationLinkResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetServiceDescription_InputMessage">
        <wsdl:part name="parameters" element="tns:GetServiceDescription"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetServiceDescription_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetServiceDescriptionResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetLaborAverages_InputMessage">
        <wsdl:part name="parameters" element="tns:GetLaborAverages"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetLaborAverages_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetLaborAveragesResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetJobPaymentPreview_InputMessage">
        <wsdl:part name="parameters" element="tns:GetJobPaymentPreview"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetJobPaymentPreview_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetJobPaymentPreviewResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_TakePaymentForJob_InputMessage">
        <wsdl:part name="parameters" element="tns:TakePaymentForJob"/>
    </wsdl:message>
    <wsdl:message name="IUserService_TakePaymentForJob_OutputMessage">
        <wsdl:part name="parameters" element="tns:TakePaymentForJobResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetSelectableCreditCardTypes_InputMessage">
        <wsdl:part name="parameters" element="tns:GetSelectableCreditCardTypes"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetSelectableCreditCardTypes_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetSelectableCreditCardTypesResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_IsValidCreditCard_InputMessage">
        <wsdl:part name="parameters" element="tns:IsValidCreditCard"/>
    </wsdl:message>
    <wsdl:message name="IUserService_IsValidCreditCard_OutputMessage">
        <wsdl:part name="parameters" element="tns:IsValidCreditCardResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetUnbilledJobsSummary_InputMessage">
        <wsdl:part name="parameters" element="tns:GetUnbilledJobsSummary"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetUnbilledJobsSummary_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetUnbilledJobsSummaryResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_HasAdditionalUnbilledJobs_InputMessage">
        <wsdl:part name="parameters" element="tns:HasAdditionalUnbilledJobs"/>
    </wsdl:message>
    <wsdl:message name="IUserService_HasAdditionalUnbilledJobs_OutputMessage">
        <wsdl:part name="parameters" element="tns:HasAdditionalUnbilledJobsResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetOpenInvoicesSummary_InputMessage">
        <wsdl:part name="parameters" element="tns:GetOpenInvoicesSummary"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetOpenInvoicesSummary_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetOpenInvoicesSummaryResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_HasAdditionalOpenInvoices_InputMessage">
        <wsdl:part name="parameters" element="tns:HasAdditionalOpenInvoices"/>
    </wsdl:message>
    <wsdl:message name="IUserService_HasAdditionalOpenInvoices_OutputMessage">
        <wsdl:part name="parameters" element="tns:HasAdditionalOpenInvoicesResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetPaidInvoicesSummary_InputMessage">
        <wsdl:part name="parameters" element="tns:GetPaidInvoicesSummary"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetPaidInvoicesSummary_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetPaidInvoicesSummaryResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_BasicFindInvoices_InputMessage">
        <wsdl:part name="parameters" element="tns:BasicFindInvoices"/>
    </wsdl:message>
    <wsdl:message name="IUserService_BasicFindInvoices_OutputMessage">
        <wsdl:part name="parameters" element="tns:BasicFindInvoicesResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_FindInvoices_InputMessage">
        <wsdl:part name="parameters" element="tns:FindInvoices"/>
    </wsdl:message>
    <wsdl:message name="IUserService_FindInvoices_OutputMessage">
        <wsdl:part name="parameters" element="tns:FindInvoicesResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetInvoiceDetails_InputMessage">
        <wsdl:part name="parameters" element="tns:GetInvoiceDetails"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetInvoiceDetails_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetInvoiceDetailsResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetInvoicePaymentSummary_InputMessage">
        <wsdl:part name="parameters" element="tns:GetInvoicePaymentSummary"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetInvoicePaymentSummary_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetInvoicePaymentSummaryResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_FindUnbilledServices_InputMessage">
        <wsdl:part name="parameters" element="tns:FindUnbilledServices"/>
    </wsdl:message>
    <wsdl:message name="IUserService_FindUnbilledServices_OutputMessage">
        <wsdl:part name="parameters" element="tns:FindUnbilledServicesResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_PostJobs_InputMessage">
        <wsdl:part name="parameters" element="tns:PostJobs"/>
    </wsdl:message>
    <wsdl:message name="IUserService_PostJobs_OutputMessage">
        <wsdl:part name="parameters" element="tns:PostJobsResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_FindUnpaidServices_InputMessage">
        <wsdl:part name="parameters" element="tns:FindUnpaidServices"/>
    </wsdl:message>
    <wsdl:message name="IUserService_FindUnpaidServices_OutputMessage">
        <wsdl:part name="parameters" element="tns:FindUnpaidServicesResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetOpenDeposits_InputMessage">
        <wsdl:part name="parameters" element="tns:GetOpenDeposits"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetOpenDeposits_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetOpenDepositsResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetOpenDepositByAccountId_InputMessage">
        <wsdl:part name="parameters" element="tns:GetOpenDepositByAccountId"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetOpenDepositByAccountId_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetOpenDepositByAccountIdResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_CloseOpenDeposits_InputMessage">
        <wsdl:part name="parameters" element="tns:CloseOpenDeposits"/>
    </wsdl:message>
    <wsdl:message name="IUserService_CloseOpenDeposits_OutputMessage">
        <wsdl:part name="parameters" element="tns:CloseOpenDepositsResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_EnterPaymentForAccount_InputMessage">
        <wsdl:part name="parameters" element="tns:EnterPaymentForAccount"/>
    </wsdl:message>
    <wsdl:message name="IUserService_EnterPaymentForAccount_OutputMessage">
        <wsdl:part name="parameters" element="tns:EnterPaymentForAccountResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetCreditMemoBalancesForAccount_InputMessage">
        <wsdl:part name="parameters" element="tns:GetCreditMemoBalancesForAccount"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetCreditMemoBalancesForAccount_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetCreditMemoBalancesForAccountResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_FindMotorClubInvoices_InputMessage">
        <wsdl:part name="parameters" element="tns:FindMotorClubInvoices"/>
    </wsdl:message>
    <wsdl:message name="IUserService_FindMotorClubInvoices_OutputMessage">
        <wsdl:part name="parameters" element="tns:FindMotorClubInvoicesResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_SendMotorClubInvoices_InputMessage">
        <wsdl:part name="parameters" element="tns:SendMotorClubInvoices"/>
    </wsdl:message>
    <wsdl:message name="IUserService_SendMotorClubInvoices_OutputMessage">
        <wsdl:part name="parameters" element="tns:SendMotorClubInvoicesResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetMotorClubInvoiceEditUrl_InputMessage">
        <wsdl:part name="parameters" element="tns:GetMotorClubInvoiceEditUrl"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetMotorClubInvoiceEditUrl_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetMotorClubInvoiceEditUrlResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_SetTowMagicInvoiceSuccessful_InputMessage">
        <wsdl:part name="parameters" element="tns:SetTowMagicInvoiceSuccessful"/>
    </wsdl:message>
    <wsdl:message name="IUserService_SetTowMagicInvoiceSuccessful_OutputMessage">
        <wsdl:part name="parameters" element="tns:SetTowMagicInvoiceSuccessfulResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_SetInvoiceVisibility_InputMessage">
        <wsdl:part name="parameters" element="tns:SetInvoiceVisibility"/>
    </wsdl:message>
    <wsdl:message name="IUserService_SetInvoiceVisibility_OutputMessage">
        <wsdl:part name="parameters" element="tns:SetInvoiceVisibilityResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_SaveMCHideCriticalInvoices_InputMessage">
        <wsdl:part name="parameters" element="tns:SaveMCHideCriticalInvoices"/>
    </wsdl:message>
    <wsdl:message name="IUserService_SaveMCHideCriticalInvoices_OutputMessage">
        <wsdl:part name="parameters" element="tns:SaveMCHideCriticalInvoicesResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_ClearTowMagicInvoiceError_InputMessage">
        <wsdl:part name="parameters" element="tns:ClearTowMagicInvoiceError"/>
    </wsdl:message>
    <wsdl:message name="IUserService_ClearTowMagicInvoiceError_OutputMessage">
        <wsdl:part name="parameters" element="tns:ClearTowMagicInvoiceErrorResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_UndoPayments_InputMessage">
        <wsdl:part name="parameters" element="tns:UndoPayments"/>
    </wsdl:message>
    <wsdl:message name="IUserService_UndoPayments_OutputMessage">
        <wsdl:part name="parameters" element="tns:UndoPaymentsResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetReceiptPreviewsForJob_InputMessage">
        <wsdl:part name="parameters" element="tns:GetReceiptPreviewsForJob"/>
    </wsdl:message>
    <wsdl:message name="IUserService_GetReceiptPreviewsForJob_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetReceiptPreviewsForJobResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_UndoReleaseVehicle_InputMessage">
        <wsdl:part name="parameters" element="tns:UndoReleaseVehicle"/>
    </wsdl:message>
    <wsdl:message name="IUserService_UndoReleaseVehicle_OutputMessage">
        <wsdl:part name="parameters" element="tns:UndoReleaseVehicleResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_FindUnpaidServiceCommissions_InputMessage">
        <wsdl:part name="parameters" element="tns:FindUnpaidServiceCommissions"/>
    </wsdl:message>
    <wsdl:message name="IUserService_FindUnpaidServiceCommissions_OutputMessage">
        <wsdl:part name="parameters" element="tns:FindUnpaidServiceCommissionsResponse"/>
    </wsdl:message>
    <wsdl:message name="IUserService_SaveCommissions_InputMessage">
        <wsdl:part name="parameters" element="tns:SaveCommissions"/>
    </wsdl:message>
    <wsdl:message name="IUserService_SaveCommissions_OutputMessage">
        <wsdl:part name="parameters" element="tns:SaveCommissionsResponse"/>
    </wsdl:message>
    <wsdl:portType name="IUserService">
        <wsdl:operation name="SaveCreditMemo">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/SaveCreditMemo" message="tns:IUserService_SaveCreditMemo_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/SaveCreditMemoResponse" message="tns:IUserService_SaveCreditMemo_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="RefundCreditMemos">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/RefundCreditMemos" message="tns:IUserService_RefundCreditMemos_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/RefundCreditMemosResponse" message="tns:IUserService_RefundCreditMemos_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="FindReceipts">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/FindReceipts" message="tns:IUserService_FindReceipts_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/FindReceiptsResponse" message="tns:IUserService_FindReceipts_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetInvoiceIdsForQBExport">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetInvoiceIdsForQBExport" message="tns:IUserService_GetInvoiceIdsForQBExport_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetInvoiceIdsForQBExportResponse" message="tns:IUserService_GetInvoiceIdsForQBExport_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="ProcessBactchIdsForQBExport">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/ProcessBactchIdsForQBExport" message="tns:IUserService_ProcessBactchIdsForQBExport_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/ProcessBactchIdsForQBExportResponse" message="tns:IUserService_ProcessBactchIdsForQBExport_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetCreditCardTransaction">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetCreditCardTransaction" message="tns:IUserService_GetCreditCardTransaction_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetCreditCardTransactionResponse" message="tns:IUserService_GetCreditCardTransaction_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="CheckMCInvoiceEnrollmentStatus">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/CheckMCInvoiceEnrollmentStatus" message="tns:IUserService_CheckMCInvoiceEnrollmentStatus_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/CheckMCInvoiceEnrollmentStatusResponse" message="tns:IUserService_CheckMCInvoiceEnrollmentStatus_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetMotorClubInvoice">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetMotorClubInvoice" message="tns:IUserService_GetMotorClubInvoice_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetMotorClubInvoiceResponse" message="tns:IUserService_GetMotorClubInvoice_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateMotorClubInvoice">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/UpdateMotorClubInvoice" message="tns:IUserService_UpdateMotorClubInvoice_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/UpdateMotorClubInvoiceResponse" message="tns:IUserService_UpdateMotorClubInvoice_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetBillingMCKeys">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetBillingMCKeys" message="tns:IUserService_GetBillingMCKeys_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetBillingMCKeysResponse" message="tns:IUserService_GetBillingMCKeys_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetReconciliationMCKeys">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetReconciliationMCKeys" message="tns:IUserService_GetReconciliationMCKeys_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetReconciliationMCKeysResponse" message="tns:IUserService_GetReconciliationMCKeys_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetSurchargeServiceRate">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetSurchargeServiceRate" message="tns:IUserService_GetSurchargeServiceRate_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetSurchargeServiceRateResponse" message="tns:IUserService_GetSurchargeServiceRate_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="FindMotorClubPayments">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/FindMotorClubPayments" message="tns:IUserService_FindMotorClubPayments_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/FindMotorClubPaymentsResponse" message="tns:IUserService_FindMotorClubPayments_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetLoggedMotorClubPayments">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetLoggedMotorClubPayments" message="tns:IUserService_GetLoggedMotorClubPayments_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetLoggedMotorClubPaymentsResponse" message="tns:IUserService_GetLoggedMotorClubPayments_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="ApplyMotorClubPayments">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/ApplyMotorClubPayments" message="tns:IUserService_ApplyMotorClubPayments_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/ApplyMotorClubPaymentsResponse" message="tns:IUserService_ApplyMotorClubPayments_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetImpoundLots">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetImpoundLots" message="tns:IUserService_GetImpoundLots_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetImpoundLotsResponse" message="tns:IUserService_GetImpoundLots_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetImpoundLotVehicleCounts">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetImpoundLotVehicleCounts" message="tns:IUserService_GetImpoundLotVehicleCounts_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetImpoundLotVehicleCountsResponse" message="tns:IUserService_GetImpoundLotVehicleCounts_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetSelectableZones">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetSelectableZones" message="tns:IUserService_GetSelectableZones_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetSelectableZonesResponse" message="tns:IUserService_GetSelectableZones_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetSelectablePriorities">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetSelectablePriorities" message="tns:IUserService_GetSelectablePriorities_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetSelectablePrioritiesResponse" message="tns:IUserService_GetSelectablePriorities_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetSelectableTruckTypes">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetSelectableTruckTypes" message="tns:IUserService_GetSelectableTruckTypes_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetSelectableTruckTypesResponse" message="tns:IUserService_GetSelectableTruckTypes_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetSelectablePaymentMethods">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetSelectablePaymentMethods" message="tns:IUserService_GetSelectablePaymentMethods_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetSelectablePaymentMethodsResponse" message="tns:IUserService_GetSelectablePaymentMethods_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetVehicleStatuses">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetVehicleStatuses" message="tns:IUserService_GetVehicleStatuses_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetVehicleStatusesResponse" message="tns:IUserService_GetVehicleStatuses_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetDriverStatuses">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetDriverStatuses" message="tns:IUserService_GetDriverStatuses_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetDriverStatusesResponse" message="tns:IUserService_GetDriverStatuses_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetJobStatusDescriptions">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetJobStatusDescriptions" message="tns:IUserService_GetJobStatusDescriptions_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetJobStatusDescriptionsResponse" message="tns:IUserService_GetJobStatusDescriptions_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetAssignedStatusDescriptions">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetAssignedStatusDescriptions" message="tns:IUserService_GetAssignedStatusDescriptions_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetAssignedStatusDescriptionsResponse" message="tns:IUserService_GetAssignedStatusDescriptions_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetOfferStatusDescriptions">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetOfferStatusDescriptions" message="tns:IUserService_GetOfferStatusDescriptions_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetOfferStatusDescriptionsResponse" message="tns:IUserService_GetOfferStatusDescriptions_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetDepartments">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetDepartments" message="tns:IUserService_GetDepartments_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetDepartmentsResponse" message="tns:IUserService_GetDepartments_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetSelectableReasons">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetSelectableReasons" message="tns:IUserService_GetSelectableReasons_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetSelectableReasonsResponse" message="tns:IUserService_GetSelectableReasons_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetCancelReasons">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetCancelReasons" message="tns:IUserService_GetCancelReasons_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetCancelReasonsResponse" message="tns:IUserService_GetCancelReasons_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetCities">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetCities" message="tns:IUserService_GetCities_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetCitiesResponse" message="tns:IUserService_GetCities_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetStates">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetStates" message="tns:IUserService_GetStates_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetStatesResponse" message="tns:IUserService_GetStates_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetVehicleColors">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetVehicleColors" message="tns:IUserService_GetVehicleColors_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetVehicleColorsResponse" message="tns:IUserService_GetVehicleColors_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetKeyLocations">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetKeyLocations" message="tns:IUserService_GetKeyLocations_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetKeyLocationsResponse" message="tns:IUserService_GetKeyLocations_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetVehicleStyles">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetVehicleStyles" message="tns:IUserService_GetVehicleStyles_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetVehicleStylesResponse" message="tns:IUserService_GetVehicleStyles_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetForcedStatusDescriptions">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetForcedStatusDescriptions" message="tns:IUserService_GetForcedStatusDescriptions_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetForcedStatusDescriptionsResponse" message="tns:IUserService_GetForcedStatusDescriptions_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetGroup1CustomDictionary1">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetGroup1CustomDictionary1" message="tns:IUserService_GetGroup1CustomDictionary1_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetGroup1CustomDictionary1Response" message="tns:IUserService_GetGroup1CustomDictionary1_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetGroup1CustomDictionary2">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetGroup1CustomDictionary2" message="tns:IUserService_GetGroup1CustomDictionary2_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetGroup1CustomDictionary2Response" message="tns:IUserService_GetGroup1CustomDictionary2_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetGroup2CustomDictionary1">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetGroup2CustomDictionary1" message="tns:IUserService_GetGroup2CustomDictionary1_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetGroup2CustomDictionary1Response" message="tns:IUserService_GetGroup2CustomDictionary1_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetGroup2CustomDictionary2">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetGroup2CustomDictionary2" message="tns:IUserService_GetGroup2CustomDictionary2_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetGroup2CustomDictionary2Response" message="tns:IUserService_GetGroup2CustomDictionary2_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetAccountTypes">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetAccountTypes" message="tns:IUserService_GetAccountTypes_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetAccountTypesResponse" message="tns:IUserService_GetAccountTypes_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetTaxRates">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetTaxRates" message="tns:IUserService_GetTaxRates_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetTaxRatesResponse" message="tns:IUserService_GetTaxRates_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="ConnectToPush">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/ConnectToPush" message="tns:IUserService_ConnectToPush_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/ConnectToPushResponse" message="tns:IUserService_ConnectToPush_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="DisconnectFromPush">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/DisconnectFromPush" message="tns:IUserService_DisconnectFromPush_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/DisconnectFromPushResponse" message="tns:IUserService_DisconnectFromPush_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetVehicleDetailsURL">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetVehicleDetailsURL" message="tns:IUserService_GetVehicleDetailsURL_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetVehicleDetailsURLResponse" message="tns:IUserService_GetVehicleDetailsURL_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetVehicleSearchUrl">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetVehicleSearchUrl" message="tns:IUserService_GetVehicleSearchUrl_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetVehicleSearchUrlResponse" message="tns:IUserService_GetVehicleSearchUrl_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="TowSpecSearch">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/TowSpecSearch" message="tns:IUserService_TowSpecSearch_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/TowSpecSearchResponse" message="tns:IUserService_TowSpecSearch_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetAllTowSpecVehiclesIfNewer">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetAllTowSpecVehiclesIfNewer" message="tns:IUserService_GetAllTowSpecVehiclesIfNewer_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetAllTowSpecVehiclesIfNewerResponse" message="tns:IUserService_GetAllTowSpecVehiclesIfNewer_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetAllTowSpecVehicles">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetAllTowSpecVehicles" message="tns:IUserService_GetAllTowSpecVehicles_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetAllTowSpecVehiclesResponse" message="tns:IUserService_GetAllTowSpecVehicles_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="SendToTowLien">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/SendToTowLien" message="tns:IUserService_SendToTowLien_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/SendToTowLienResponse" message="tns:IUserService_SendToTowLien_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetTowLienLocations">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetTowLienLocations" message="tns:IUserService_GetTowLienLocations_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetTowLienLocationsResponse" message="tns:IUserService_GetTowLienLocations_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="DecodeVin">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/DecodeVin" message="tns:IUserService_DecodeVin_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/DecodeVinResponse" message="tns:IUserService_DecodeVin_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="LookupVINByLicensePlate">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/LookupVINByLicensePlate" message="tns:IUserService_LookupVINByLicensePlate_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/LookupVINByLicensePlateResponse" message="tns:IUserService_LookupVINByLicensePlate_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetAuction">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetAuction" message="tns:IUserService_GetAuction_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetAuctionResponse" message="tns:IUserService_GetAuction_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetAuctions">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetAuctions" message="tns:IUserService_GetAuctions_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetAuctionsResponse" message="tns:IUserService_GetAuctions_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetAuctionAndUpcoming">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetAuctionAndUpcoming" message="tns:IUserService_GetAuctionAndUpcoming_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetAuctionAndUpcomingResponse" message="tns:IUserService_GetAuctionAndUpcoming_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetUpcomingAuctions">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetUpcomingAuctions" message="tns:IUserService_GetUpcomingAuctions_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetUpcomingAuctionsResponse" message="tns:IUserService_GetUpcomingAuctions_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="SaveAuction">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/SaveAuction" message="tns:IUserService_SaveAuction_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/SaveAuctionResponse" message="tns:IUserService_SaveAuction_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetJobPhotos">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetJobPhotos" message="tns:IUserService_GetJobPhotos_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetJobPhotosResponse" message="tns:IUserService_GetJobPhotos_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetJobPhotoUploadUrl">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetJobPhotoUploadUrl" message="tns:IUserService_GetJobPhotoUploadUrl_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetJobPhotoUploadUrlResponse" message="tns:IUserService_GetJobPhotoUploadUrl_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetJobPhotoUploadToken">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetJobPhotoUploadToken" message="tns:IUserService_GetJobPhotoUploadToken_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetJobPhotoUploadTokenResponse" message="tns:IUserService_GetJobPhotoUploadToken_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="SetJobPhotoPrivate">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/SetJobPhotoPrivate" message="tns:IUserService_SetJobPhotoPrivate_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/SetJobPhotoPrivateResponse" message="tns:IUserService_SetJobPhotoPrivate_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="SetJobPhotoPublic">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/SetJobPhotoPublic" message="tns:IUserService_SetJobPhotoPublic_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/SetJobPhotoPublicResponse" message="tns:IUserService_SetJobPhotoPublic_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="DeleteJobPhoto">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/DeleteJobPhoto" message="tns:IUserService_DeleteJobPhoto_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/DeleteJobPhotoResponse" message="tns:IUserService_DeleteJobPhoto_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="SendJobPhotosShareUrl">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/SendJobPhotosShareUrl" message="tns:IUserService_SendJobPhotosShareUrl_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/SendJobPhotosShareUrlResponse" message="tns:IUserService_SendJobPhotosShareUrl_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetPhotoCount">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetPhotoCount" message="tns:IUserService_GetPhotoCount_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetPhotoCountResponse" message="tns:IUserService_GetPhotoCount_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetSecurePhotoUploadUrl">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetSecurePhotoUploadUrl" message="tns:IUserService_GetSecurePhotoUploadUrl_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetSecurePhotoUploadUrlResponse" message="tns:IUserService_GetSecurePhotoUploadUrl_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="SendReportEmail">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/SendReportEmail" message="tns:IUserService_SendReportEmail_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/SendReportEmailResponse" message="tns:IUserService_SendReportEmail_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetReportSender">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetReportSender" message="tns:IUserService_GetReportSender_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetReportSenderResponse" message="tns:IUserService_GetReportSender_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="SendReportFax">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/SendReportFax" message="tns:IUserService_SendReportFax_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/SendReportFaxResponse" message="tns:IUserService_SendReportFax_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetDALienUrl">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetDALienUrl" message="tns:IUserService_GetDALienUrl_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetDALienUrlResponse" message="tns:IUserService_GetDALienUrl_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="LogUploadedJobPhotos">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/LogUploadedJobPhotos" message="tns:IUserService_LogUploadedJobPhotos_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/LogUploadedJobPhotosResponse" message="tns:IUserService_LogUploadedJobPhotos_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="LogDeletedJobPhotos">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/LogDeletedJobPhotos" message="tns:IUserService_LogDeletedJobPhotos_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/LogDeletedJobPhotosResponse" message="tns:IUserService_LogDeletedJobPhotos_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateOfferStatus">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/UpdateOfferStatus" message="tns:IUserService_UpdateOfferStatus_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/UpdateOfferStatusResponse" message="tns:IUserService_UpdateOfferStatus_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="PostApprovalUpdate">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/PostApprovalUpdate" message="tns:IUserService_PostApprovalUpdate_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/PostApprovalUpdateResponse" message="tns:IUserService_PostApprovalUpdate_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetServiceMappingsFromJson">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetServiceMappingsFromJson" message="tns:IUserService_GetServiceMappingsFromJson_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetServiceMappingsFromJsonResponse" message="tns:IUserService_GetServiceMappingsFromJson_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetServiceMappingsFromTasks">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetServiceMappingsFromTasks" message="tns:IUserService_GetServiceMappingsFromTasks_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetServiceMappingsFromTasksResponse" message="tns:IUserService_GetServiceMappingsFromTasks_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetUTCNow">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetUTCNow" message="tns:IUserService_GetUTCNow_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetUTCNowResponse" message="tns:IUserService_GetUTCNow_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="AgreeToEULA">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/AgreeToEULA" message="tns:IUserService_AgreeToEULA_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/AgreeToEULAResponse" message="tns:IUserService_AgreeToEULA_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetChangelogArticleContent">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetChangelogArticleContent" message="tns:IUserService_GetChangelogArticleContent_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetChangelogArticleContentResponse" message="tns:IUserService_GetChangelogArticleContent_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetBasicBSCompanies">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetBasicBSCompanies" message="tns:IUserService_GetBasicBSCompanies_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetBasicBSCompaniesResponse" message="tns:IUserService_GetBasicBSCompanies_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetCurrentBasicBSCompany">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetCurrentBasicBSCompany" message="tns:IUserService_GetCurrentBasicBSCompany_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetCurrentBasicBSCompanyResponse" message="tns:IUserService_GetCurrentBasicBSCompany_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetCurrentUser">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetCurrentUser" message="tns:IUserService_GetCurrentUser_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetCurrentUserResponse" message="tns:IUserService_GetCurrentUser_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetBasicDAUsers">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetBasicDAUsers" message="tns:IUserService_GetBasicDAUsers_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetBasicDAUsersResponse" message="tns:IUserService_GetBasicDAUsers_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetCompany">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetCompany" message="tns:IUserService_GetCompany_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetCompanyResponse" message="tns:IUserService_GetCompany_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetCompanyQuickBooksVersion">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetCompanyQuickBooksVersion" message="tns:IUserService_GetCompanyQuickBooksVersion_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetCompanyQuickBooksVersionResponse" message="tns:IUserService_GetCompanyQuickBooksVersion_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetDivision">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetDivision" message="tns:IUserService_GetDivision_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetDivisionResponse" message="tns:IUserService_GetDivision_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetBasicDivisions">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetBasicDivisions" message="tns:IUserService_GetBasicDivisions_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetBasicDivisionsResponse" message="tns:IUserService_GetBasicDivisions_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetDivisions">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetDivisions" message="tns:IUserService_GetDivisions_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetDivisionsResponse" message="tns:IUserService_GetDivisions_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetDivisionAccess">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetDivisionAccess" message="tns:IUserService_GetDivisionAccess_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetDivisionAccessResponse" message="tns:IUserService_GetDivisionAccess_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetDriverFields">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetDriverFields" message="tns:IUserService_GetDriverFields_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetDriverFieldsResponse" message="tns:IUserService_GetDriverFields_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetAccount">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetAccount" message="tns:IUserService_GetAccount_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetAccountResponse" message="tns:IUserService_GetAccount_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetAccountInfo">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetAccountInfo" message="tns:IUserService_GetAccountInfo_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetAccountInfoResponse" message="tns:IUserService_GetAccountInfo_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetBasicAccounts">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetBasicAccounts" message="tns:IUserService_GetBasicAccounts_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetBasicAccountsResponse" message="tns:IUserService_GetBasicAccounts_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetAccounts">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetAccounts" message="tns:IUserService_GetAccounts_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetAccountsResponse" message="tns:IUserService_GetAccounts_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetAccountsListItems">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetAccountsListItems" message="tns:IUserService_GetAccountsListItems_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetAccountsListItemsResponse" message="tns:IUserService_GetAccountsListItems_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetUsableAccountsForDivision">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetUsableAccountsForDivision" message="tns:IUserService_GetUsableAccountsForDivision_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetUsableAccountsForDivisionResponse" message="tns:IUserService_GetUsableAccountsForDivision_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetUsableAccountsForDivisionListItems">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetUsableAccountsForDivisionListItems" message="tns:IUserService_GetUsableAccountsForDivisionListItems_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetUsableAccountsForDivisionListItemsResponse" message="tns:IUserService_GetUsableAccountsForDivisionListItems_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetAccountAvailableCredit">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetAccountAvailableCredit" message="tns:IUserService_GetAccountAvailableCredit_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetAccountAvailableCreditResponse" message="tns:IUserService_GetAccountAvailableCredit_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetJobTemplateFieldsForAccount">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetJobTemplateFieldsForAccount" message="tns:IUserService_GetJobTemplateFieldsForAccount_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetJobTemplateFieldsForAccountResponse" message="tns:IUserService_GetJobTemplateFieldsForAccount_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetJobTemplateFieldsForDefault">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetJobTemplateFieldsForDefault" message="tns:IUserService_GetJobTemplateFieldsForDefault_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetJobTemplateFieldsForDefaultResponse" message="tns:IUserService_GetJobTemplateFieldsForDefault_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetServiceRatesForJob">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetServiceRatesForJob" message="tns:IUserService_GetServiceRatesForJob_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetServiceRatesForJobResponse" message="tns:IUserService_GetServiceRatesForJob_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetAccountDefaultServiceRatesForJob">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetAccountDefaultServiceRatesForJob" message="tns:IUserService_GetAccountDefaultServiceRatesForJob_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetAccountDefaultServiceRatesForJobResponse" message="tns:IUserService_GetAccountDefaultServiceRatesForJob_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetSecuritySettings">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetSecuritySettings" message="tns:IUserService_GetSecuritySettings_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetSecuritySettingsResponse" message="tns:IUserService_GetSecuritySettings_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetUserRoles">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetUserRoles" message="tns:IUserService_GetUserRoles_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetUserRolesResponse" message="tns:IUserService_GetUserRoles_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetDispatchViewSettings">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetDispatchViewSettings" message="tns:IUserService_GetDispatchViewSettings_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetDispatchViewSettingsResponse" message="tns:IUserService_GetDispatchViewSettings_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="SaveDispatchViewSettings">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/SaveDispatchViewSettings" message="tns:IUserService_SaveDispatchViewSettings_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/SaveDispatchViewSettingsResponse" message="tns:IUserService_SaveDispatchViewSettings_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="SerializeServiceCalculatorSettings">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/SerializeServiceCalculatorSettings" message="tns:IUserService_SerializeServiceCalculatorSettings_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/SerializeServiceCalculatorSettingsResponse" message="tns:IUserService_SerializeServiceCalculatorSettings_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="DeserializeServiceCalculatorSettings">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/DeserializeServiceCalculatorSettings" message="tns:IUserService_DeserializeServiceCalculatorSettings_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/DeserializeServiceCalculatorSettingsResponse" message="tns:IUserService_DeserializeServiceCalculatorSettings_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="SendEmail">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/SendEmail" message="tns:IUserService_SendEmail_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/SendEmailResponse" message="tns:IUserService_SendEmail_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetAccountBillingContact">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetAccountBillingContact" message="tns:IUserService_GetAccountBillingContact_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetAccountBillingContactResponse" message="tns:IUserService_GetAccountBillingContact_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetJobAutoNumberValue">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetJobAutoNumberValue" message="tns:IUserService_GetJobAutoNumberValue_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetJobAutoNumberValueResponse" message="tns:IUserService_GetJobAutoNumberValue_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetActiveJobs">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetActiveJobs" message="tns:IUserService_GetActiveJobs_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetActiveJobsResponse" message="tns:IUserService_GetActiveJobs_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetAssignedJobsForDriver">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetAssignedJobsForDriver" message="tns:IUserService_GetAssignedJobsForDriver_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetAssignedJobsForDriverResponse" message="tns:IUserService_GetAssignedJobsForDriver_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetJob">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetJob" message="tns:IUserService_GetJob_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetJobResponse" message="tns:IUserService_GetJob_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetJobForEdit">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetJobForEdit" message="tns:IUserService_GetJobForEdit_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetJobForEditResponse" message="tns:IUserService_GetJobForEdit_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="SaveJob">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/SaveJob" message="tns:IUserService_SaveJob_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/SaveJobResponse" message="tns:IUserService_SaveJob_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="CreateJob">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/CreateJob" message="tns:IUserService_CreateJob_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/CreateJobResponse" message="tns:IUserService_CreateJob_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateJob">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/UpdateJob" message="tns:IUserService_UpdateJob_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/UpdateJobResponse" message="tns:IUserService_UpdateJob_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="AssignJob">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/AssignJob" message="tns:IUserService_AssignJob_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/AssignJobResponse" message="tns:IUserService_AssignJob_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="UnassignJob">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/UnassignJob" message="tns:IUserService_UnassignJob_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/UnassignJobResponse" message="tns:IUserService_UnassignJob_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="TimeStampJob">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/TimeStampJob" message="tns:IUserService_TimeStampJob_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/TimeStampJobResponse" message="tns:IUserService_TimeStampJob_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="MoveJobToWaiting">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/MoveJobToWaiting" message="tns:IUserService_MoveJobToWaiting_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/MoveJobToWaitingResponse" message="tns:IUserService_MoveJobToWaiting_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="MoveJobToHolding">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/MoveJobToHolding" message="tns:IUserService_MoveJobToHolding_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/MoveJobToHoldingResponse" message="tns:IUserService_MoveJobToHolding_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetPromptOnFinishFields">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetPromptOnFinishFields" message="tns:IUserService_GetPromptOnFinishFields_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetPromptOnFinishFieldsResponse" message="tns:IUserService_GetPromptOnFinishFields_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetClearCodes">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetClearCodes" message="tns:IUserService_GetClearCodes_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetClearCodesResponse" message="tns:IUserService_GetClearCodes_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetAllClearCodes">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetAllClearCodes" message="tns:IUserService_GetAllClearCodes_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetAllClearCodesResponse" message="tns:IUserService_GetAllClearCodes_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="FinishJob">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/FinishJob" message="tns:IUserService_FinishJob_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/FinishJobResponse" message="tns:IUserService_FinishJob_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="UndoFinishJob">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/UndoFinishJob" message="tns:IUserService_UndoFinishJob_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/UndoFinishJobResponse" message="tns:IUserService_UndoFinishJob_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="CancelJob">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/CancelJob" message="tns:IUserService_CancelJob_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/CancelJobResponse" message="tns:IUserService_CancelJob_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetJobResourceFromDriverId">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetJobResourceFromDriverId" message="tns:IUserService_GetJobResourceFromDriverId_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetJobResourceFromDriverIdResponse" message="tns:IUserService_GetJobResourceFromDriverId_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="FindJobs">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/FindJobs" message="tns:IUserService_FindJobs_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/FindJobsResponse" message="tns:IUserService_FindJobs_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="BasicFindJobs">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/BasicFindJobs" message="tns:IUserService_BasicFindJobs_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/BasicFindJobsResponse" message="tns:IUserService_BasicFindJobs_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetJobServices">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetJobServices" message="tns:IUserService_GetJobServices_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetJobServicesResponse" message="tns:IUserService_GetJobServices_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetJobServicesCount">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetJobServicesCount" message="tns:IUserService_GetJobServicesCount_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetJobServicesCountResponse" message="tns:IUserService_GetJobServicesCount_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetJobService">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetJobService" message="tns:IUserService_GetJobService_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetJobServiceResponse" message="tns:IUserService_GetJobService_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="SaveJobService">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/SaveJobService" message="tns:IUserService_SaveJobService_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/SaveJobServiceResponse" message="tns:IUserService_SaveJobService_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetDriver">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetDriver" message="tns:IUserService_GetDriver_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetDriverResponse" message="tns:IUserService_GetDriver_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetBasicDrivers">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetBasicDrivers" message="tns:IUserService_GetBasicDrivers_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetBasicDriversResponse" message="tns:IUserService_GetBasicDrivers_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetDrivers">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetDrivers" message="tns:IUserService_GetDrivers_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetDriversResponse" message="tns:IUserService_GetDrivers_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetAssignableDrivers">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetAssignableDrivers" message="tns:IUserService_GetAssignableDrivers_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetAssignableDriversResponse" message="tns:IUserService_GetAssignableDrivers_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="SaveDriver">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/SaveDriver" message="tns:IUserService_SaveDriver_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/SaveDriverResponse" message="tns:IUserService_SaveDriver_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetVehicle">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetVehicle" message="tns:IUserService_GetVehicle_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetVehicleResponse" message="tns:IUserService_GetVehicle_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetBasicVehicles">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetBasicVehicles" message="tns:IUserService_GetBasicVehicles_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetBasicVehiclesResponse" message="tns:IUserService_GetBasicVehicles_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetAssignableVehicles">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetAssignableVehicles" message="tns:IUserService_GetAssignableVehicles_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetAssignableVehiclesResponse" message="tns:IUserService_GetAssignableVehicles_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetTrackableVehicles">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetTrackableVehicles" message="tns:IUserService_GetTrackableVehicles_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetTrackableVehiclesResponse" message="tns:IUserService_GetTrackableVehicles_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="SaveVehicle">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/SaveVehicle" message="tns:IUserService_SaveVehicle_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/SaveVehicleResponse" message="tns:IUserService_SaveVehicle_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="LinkResources">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/LinkResources" message="tns:IUserService_LinkResources_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/LinkResourcesResponse" message="tns:IUserService_LinkResources_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetDriverLastAssignTime">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetDriverLastAssignTime" message="tns:IUserService_GetDriverLastAssignTime_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetDriverLastAssignTimeResponse" message="tns:IUserService_GetDriverLastAssignTime_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="ChangeDriverStatus">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/ChangeDriverStatus" message="tns:IUserService_ChangeDriverStatus_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/ChangeDriverStatusResponse" message="tns:IUserService_ChangeDriverStatus_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="ChangeVehicleStatus">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/ChangeVehicleStatus" message="tns:IUserService_ChangeVehicleStatus_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/ChangeVehicleStatusResponse" message="tns:IUserService_ChangeVehicleStatus_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="CreateTowOutJob">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/CreateTowOutJob" message="tns:IUserService_CreateTowOutJob_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/CreateTowOutJobResponse" message="tns:IUserService_CreateTowOutJob_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="CreateReTowJob">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/CreateReTowJob" message="tns:IUserService_CreateReTowJob_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/CreateReTowJobResponse" message="tns:IUserService_CreateReTowJob_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="DuplicateJob">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/DuplicateJob" message="tns:IUserService_DuplicateJob_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/DuplicateJobResponse" message="tns:IUserService_DuplicateJob_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateJobGeoPoint">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/UpdateJobGeoPoint" message="tns:IUserService_UpdateJobGeoPoint_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/UpdateJobGeoPointResponse" message="tns:IUserService_UpdateJobGeoPoint_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateVehicleGeoPoint">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/UpdateVehicleGeoPoint" message="tns:IUserService_UpdateVehicleGeoPoint_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/UpdateVehicleGeoPointResponse" message="tns:IUserService_UpdateVehicleGeoPoint_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateDriverGeoPoint">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/UpdateDriverGeoPoint" message="tns:IUserService_UpdateDriverGeoPoint_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/UpdateDriverGeoPointResponse" message="tns:IUserService_UpdateDriverGeoPoint_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="HideDriverFromMap">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/HideDriverFromMap" message="tns:IUserService_HideDriverFromMap_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/HideDriverFromMapResponse" message="tns:IUserService_HideDriverFromMap_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="HideVehicleFromMap">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/HideVehicleFromMap" message="tns:IUserService_HideVehicleFromMap_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/HideVehicleFromMapResponse" message="tns:IUserService_HideVehicleFromMap_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="AddJobMessage">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/AddJobMessage" message="tns:IUserService_AddJobMessage_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/AddJobMessageResponse" message="tns:IUserService_AddJobMessage_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetJobMessages">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetJobMessages" message="tns:IUserService_GetJobMessages_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetJobMessagesResponse" message="tns:IUserService_GetJobMessages_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetJobMessage">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetJobMessage" message="tns:IUserService_GetJobMessage_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetJobMessageResponse" message="tns:IUserService_GetJobMessage_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetJobHistory">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetJobHistory" message="tns:IUserService_GetJobHistory_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetJobHistoryResponse" message="tns:IUserService_GetJobHistory_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetTowLienHistory">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetTowLienHistory" message="tns:IUserService_GetTowLienHistory_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetTowLienHistoryResponse" message="tns:IUserService_GetTowLienHistory_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetDistancesFromResourcesToJobViaDistanceMatrix">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetDistancesFromResourcesToJobViaDistanceMatrix" message="tns:IUserService_GetDistancesFromResourcesToJobViaDistanceMatrix_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetDistancesFromResourcesToJobViaDistanceMatrixResponse" message="tns:IUserService_GetDistancesFromResourcesToJobViaDistanceMatrix_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="SaveDivisionGeoLocation">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/SaveDivisionGeoLocation" message="tns:IUserService_SaveDivisionGeoLocation_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/SaveDivisionGeoLocationResponse" message="tns:IUserService_SaveDivisionGeoLocation_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetOfferPreview">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetOfferPreview" message="tns:IUserService_GetOfferPreview_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetOfferPreviewResponse" message="tns:IUserService_GetOfferPreview_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetPostApprovalPreview">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetPostApprovalPreview" message="tns:IUserService_GetPostApprovalPreview_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetPostApprovalPreviewResponse" message="tns:IUserService_GetPostApprovalPreview_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetMCAdditionalServicesPreview">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetMCAdditionalServicesPreview" message="tns:IUserService_GetMCAdditionalServicesPreview_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetMCAdditionalServicesPreviewResponse" message="tns:IUserService_GetMCAdditionalServicesPreview_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetJobResourceTruckTypes">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetJobResourceTruckTypes" message="tns:IUserService_GetJobResourceTruckTypes_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetJobResourceTruckTypesResponse" message="tns:IUserService_GetJobResourceTruckTypes_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="SendCustomerLocationLink">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/SendCustomerLocationLink" message="tns:IUserService_SendCustomerLocationLink_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/SendCustomerLocationLinkResponse" message="tns:IUserService_SendCustomerLocationLink_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetServiceDescription">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetServiceDescription" message="tns:IUserService_GetServiceDescription_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetServiceDescriptionResponse" message="tns:IUserService_GetServiceDescription_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetLaborAverages">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetLaborAverages" message="tns:IUserService_GetLaborAverages_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetLaborAveragesResponse" message="tns:IUserService_GetLaborAverages_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetJobPaymentPreview">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetJobPaymentPreview" message="tns:IUserService_GetJobPaymentPreview_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetJobPaymentPreviewResponse" message="tns:IUserService_GetJobPaymentPreview_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="TakePaymentForJob">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/TakePaymentForJob" message="tns:IUserService_TakePaymentForJob_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/TakePaymentForJobResponse" message="tns:IUserService_TakePaymentForJob_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetSelectableCreditCardTypes">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetSelectableCreditCardTypes" message="tns:IUserService_GetSelectableCreditCardTypes_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetSelectableCreditCardTypesResponse" message="tns:IUserService_GetSelectableCreditCardTypes_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="IsValidCreditCard">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/IsValidCreditCard" message="tns:IUserService_IsValidCreditCard_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/IsValidCreditCardResponse" message="tns:IUserService_IsValidCreditCard_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetUnbilledJobsSummary">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetUnbilledJobsSummary" message="tns:IUserService_GetUnbilledJobsSummary_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetUnbilledJobsSummaryResponse" message="tns:IUserService_GetUnbilledJobsSummary_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="HasAdditionalUnbilledJobs">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/HasAdditionalUnbilledJobs" message="tns:IUserService_HasAdditionalUnbilledJobs_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/HasAdditionalUnbilledJobsResponse" message="tns:IUserService_HasAdditionalUnbilledJobs_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetOpenInvoicesSummary">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetOpenInvoicesSummary" message="tns:IUserService_GetOpenInvoicesSummary_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetOpenInvoicesSummaryResponse" message="tns:IUserService_GetOpenInvoicesSummary_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="HasAdditionalOpenInvoices">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/HasAdditionalOpenInvoices" message="tns:IUserService_HasAdditionalOpenInvoices_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/HasAdditionalOpenInvoicesResponse" message="tns:IUserService_HasAdditionalOpenInvoices_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetPaidInvoicesSummary">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetPaidInvoicesSummary" message="tns:IUserService_GetPaidInvoicesSummary_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetPaidInvoicesSummaryResponse" message="tns:IUserService_GetPaidInvoicesSummary_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="BasicFindInvoices">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/BasicFindInvoices" message="tns:IUserService_BasicFindInvoices_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/BasicFindInvoicesResponse" message="tns:IUserService_BasicFindInvoices_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="FindInvoices">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/FindInvoices" message="tns:IUserService_FindInvoices_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/FindInvoicesResponse" message="tns:IUserService_FindInvoices_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetInvoiceDetails">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetInvoiceDetails" message="tns:IUserService_GetInvoiceDetails_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetInvoiceDetailsResponse" message="tns:IUserService_GetInvoiceDetails_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetInvoicePaymentSummary">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetInvoicePaymentSummary" message="tns:IUserService_GetInvoicePaymentSummary_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetInvoicePaymentSummaryResponse" message="tns:IUserService_GetInvoicePaymentSummary_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="FindUnbilledServices">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/FindUnbilledServices" message="tns:IUserService_FindUnbilledServices_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/FindUnbilledServicesResponse" message="tns:IUserService_FindUnbilledServices_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="PostJobs">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/PostJobs" message="tns:IUserService_PostJobs_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/PostJobsResponse" message="tns:IUserService_PostJobs_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="FindUnpaidServices">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/FindUnpaidServices" message="tns:IUserService_FindUnpaidServices_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/FindUnpaidServicesResponse" message="tns:IUserService_FindUnpaidServices_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetOpenDeposits">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetOpenDeposits" message="tns:IUserService_GetOpenDeposits_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetOpenDepositsResponse" message="tns:IUserService_GetOpenDeposits_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetOpenDepositByAccountId">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetOpenDepositByAccountId" message="tns:IUserService_GetOpenDepositByAccountId_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetOpenDepositByAccountIdResponse" message="tns:IUserService_GetOpenDepositByAccountId_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="CloseOpenDeposits">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/CloseOpenDeposits" message="tns:IUserService_CloseOpenDeposits_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/CloseOpenDepositsResponse" message="tns:IUserService_CloseOpenDeposits_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="EnterPaymentForAccount">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/EnterPaymentForAccount" message="tns:IUserService_EnterPaymentForAccount_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/EnterPaymentForAccountResponse" message="tns:IUserService_EnterPaymentForAccount_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetCreditMemoBalancesForAccount">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetCreditMemoBalancesForAccount" message="tns:IUserService_GetCreditMemoBalancesForAccount_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetCreditMemoBalancesForAccountResponse" message="tns:IUserService_GetCreditMemoBalancesForAccount_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="FindMotorClubInvoices">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/FindMotorClubInvoices" message="tns:IUserService_FindMotorClubInvoices_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/FindMotorClubInvoicesResponse" message="tns:IUserService_FindMotorClubInvoices_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="SendMotorClubInvoices">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/SendMotorClubInvoices" message="tns:IUserService_SendMotorClubInvoices_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/SendMotorClubInvoicesResponse" message="tns:IUserService_SendMotorClubInvoices_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetMotorClubInvoiceEditUrl">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetMotorClubInvoiceEditUrl" message="tns:IUserService_GetMotorClubInvoiceEditUrl_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetMotorClubInvoiceEditUrlResponse" message="tns:IUserService_GetMotorClubInvoiceEditUrl_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="SetTowMagicInvoiceSuccessful">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/SetTowMagicInvoiceSuccessful" message="tns:IUserService_SetTowMagicInvoiceSuccessful_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/SetTowMagicInvoiceSuccessfulResponse" message="tns:IUserService_SetTowMagicInvoiceSuccessful_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="SetInvoiceVisibility">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/SetInvoiceVisibility" message="tns:IUserService_SetInvoiceVisibility_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/SetInvoiceVisibilityResponse" message="tns:IUserService_SetInvoiceVisibility_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="SaveMCHideCriticalInvoices">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/SaveMCHideCriticalInvoices" message="tns:IUserService_SaveMCHideCriticalInvoices_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/SaveMCHideCriticalInvoicesResponse" message="tns:IUserService_SaveMCHideCriticalInvoices_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="ClearTowMagicInvoiceError">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/ClearTowMagicInvoiceError" message="tns:IUserService_ClearTowMagicInvoiceError_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/ClearTowMagicInvoiceErrorResponse" message="tns:IUserService_ClearTowMagicInvoiceError_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="UndoPayments">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/UndoPayments" message="tns:IUserService_UndoPayments_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/UndoPaymentsResponse" message="tns:IUserService_UndoPayments_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetReceiptPreviewsForJob">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/GetReceiptPreviewsForJob" message="tns:IUserService_GetReceiptPreviewsForJob_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/GetReceiptPreviewsForJobResponse" message="tns:IUserService_GetReceiptPreviewsForJob_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="UndoReleaseVehicle">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/UndoReleaseVehicle" message="tns:IUserService_UndoReleaseVehicle_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/UndoReleaseVehicleResponse" message="tns:IUserService_UndoReleaseVehicle_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="FindUnpaidServiceCommissions">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/FindUnpaidServiceCommissions" message="tns:IUserService_FindUnpaidServiceCommissions_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/FindUnpaidServiceCommissionsResponse" message="tns:IUserService_FindUnpaidServiceCommissions_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="SaveCommissions">
            <wsdl:input wsaw:Action="http://tempuri.org/IUserService/SaveCommissions" message="tns:IUserService_SaveCommissions_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IUserService/SaveCommissionsResponse" message="tns:IUserService_SaveCommissions_OutputMessage"/>
        </wsdl:operation>
    </wsdl:portType>
    <wsdl:binding name="BasicHttpBinding_IUserService" type="tns:IUserService">
        <wsp:PolicyReference URI="#BasicHttpBinding_IUserService_policy"/>
        <soap:binding transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="SaveCreditMemo">
            <soap:operation soapAction="http://tempuri.org/IUserService/SaveCreditMemo" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="RefundCreditMemos">
            <soap:operation soapAction="http://tempuri.org/IUserService/RefundCreditMemos" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="FindReceipts">
            <soap:operation soapAction="http://tempuri.org/IUserService/FindReceipts" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetInvoiceIdsForQBExport">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetInvoiceIdsForQBExport" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ProcessBactchIdsForQBExport">
            <soap:operation soapAction="http://tempuri.org/IUserService/ProcessBactchIdsForQBExport" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetCreditCardTransaction">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetCreditCardTransaction" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="CheckMCInvoiceEnrollmentStatus">
            <soap:operation soapAction="http://tempuri.org/IUserService/CheckMCInvoiceEnrollmentStatus" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetMotorClubInvoice">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetMotorClubInvoice" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="UpdateMotorClubInvoice">
            <soap:operation soapAction="http://tempuri.org/IUserService/UpdateMotorClubInvoice" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetBillingMCKeys">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetBillingMCKeys" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetReconciliationMCKeys">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetReconciliationMCKeys" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetSurchargeServiceRate">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetSurchargeServiceRate" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="FindMotorClubPayments">
            <soap:operation soapAction="http://tempuri.org/IUserService/FindMotorClubPayments" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetLoggedMotorClubPayments">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetLoggedMotorClubPayments" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ApplyMotorClubPayments">
            <soap:operation soapAction="http://tempuri.org/IUserService/ApplyMotorClubPayments" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetImpoundLots">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetImpoundLots" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetImpoundLotVehicleCounts">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetImpoundLotVehicleCounts" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetSelectableZones">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetSelectableZones" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetSelectablePriorities">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetSelectablePriorities" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetSelectableTruckTypes">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetSelectableTruckTypes" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetSelectablePaymentMethods">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetSelectablePaymentMethods" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetVehicleStatuses">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetVehicleStatuses" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetDriverStatuses">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetDriverStatuses" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetJobStatusDescriptions">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetJobStatusDescriptions" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetAssignedStatusDescriptions">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetAssignedStatusDescriptions" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetOfferStatusDescriptions">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetOfferStatusDescriptions" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetDepartments">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetDepartments" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetSelectableReasons">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetSelectableReasons" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetCancelReasons">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetCancelReasons" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetCities">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetCities" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetStates">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetStates" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetVehicleColors">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetVehicleColors" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetKeyLocations">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetKeyLocations" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetVehicleStyles">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetVehicleStyles" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetForcedStatusDescriptions">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetForcedStatusDescriptions" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetGroup1CustomDictionary1">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetGroup1CustomDictionary1" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetGroup1CustomDictionary2">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetGroup1CustomDictionary2" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetGroup2CustomDictionary1">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetGroup2CustomDictionary1" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetGroup2CustomDictionary2">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetGroup2CustomDictionary2" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetAccountTypes">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetAccountTypes" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetTaxRates">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetTaxRates" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ConnectToPush">
            <soap:operation soapAction="http://tempuri.org/IUserService/ConnectToPush" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="DisconnectFromPush">
            <soap:operation soapAction="http://tempuri.org/IUserService/DisconnectFromPush" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetVehicleDetailsURL">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetVehicleDetailsURL" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetVehicleSearchUrl">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetVehicleSearchUrl" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="TowSpecSearch">
            <soap:operation soapAction="http://tempuri.org/IUserService/TowSpecSearch" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetAllTowSpecVehiclesIfNewer">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetAllTowSpecVehiclesIfNewer" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetAllTowSpecVehicles">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetAllTowSpecVehicles" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="SendToTowLien">
            <soap:operation soapAction="http://tempuri.org/IUserService/SendToTowLien" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetTowLienLocations">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetTowLienLocations" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="DecodeVin">
            <soap:operation soapAction="http://tempuri.org/IUserService/DecodeVin" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="LookupVINByLicensePlate">
            <soap:operation soapAction="http://tempuri.org/IUserService/LookupVINByLicensePlate" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetAuction">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetAuction" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetAuctions">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetAuctions" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetAuctionAndUpcoming">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetAuctionAndUpcoming" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetUpcomingAuctions">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetUpcomingAuctions" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="SaveAuction">
            <soap:operation soapAction="http://tempuri.org/IUserService/SaveAuction" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetJobPhotos">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetJobPhotos" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetJobPhotoUploadUrl">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetJobPhotoUploadUrl" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetJobPhotoUploadToken">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetJobPhotoUploadToken" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="SetJobPhotoPrivate">
            <soap:operation soapAction="http://tempuri.org/IUserService/SetJobPhotoPrivate" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="SetJobPhotoPublic">
            <soap:operation soapAction="http://tempuri.org/IUserService/SetJobPhotoPublic" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="DeleteJobPhoto">
            <soap:operation soapAction="http://tempuri.org/IUserService/DeleteJobPhoto" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="SendJobPhotosShareUrl">
            <soap:operation soapAction="http://tempuri.org/IUserService/SendJobPhotosShareUrl" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetPhotoCount">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetPhotoCount" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetSecurePhotoUploadUrl">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetSecurePhotoUploadUrl" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="SendReportEmail">
            <soap:operation soapAction="http://tempuri.org/IUserService/SendReportEmail" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetReportSender">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetReportSender" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="SendReportFax">
            <soap:operation soapAction="http://tempuri.org/IUserService/SendReportFax" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetDALienUrl">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetDALienUrl" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="LogUploadedJobPhotos">
            <soap:operation soapAction="http://tempuri.org/IUserService/LogUploadedJobPhotos" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="LogDeletedJobPhotos">
            <soap:operation soapAction="http://tempuri.org/IUserService/LogDeletedJobPhotos" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="UpdateOfferStatus">
            <soap:operation soapAction="http://tempuri.org/IUserService/UpdateOfferStatus" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="PostApprovalUpdate">
            <soap:operation soapAction="http://tempuri.org/IUserService/PostApprovalUpdate" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetServiceMappingsFromJson">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetServiceMappingsFromJson" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetServiceMappingsFromTasks">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetServiceMappingsFromTasks" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetUTCNow">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetUTCNow" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="AgreeToEULA">
            <soap:operation soapAction="http://tempuri.org/IUserService/AgreeToEULA" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetChangelogArticleContent">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetChangelogArticleContent" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetBasicBSCompanies">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetBasicBSCompanies" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetCurrentBasicBSCompany">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetCurrentBasicBSCompany" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetCurrentUser">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetCurrentUser" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetBasicDAUsers">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetBasicDAUsers" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetCompany">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetCompany" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetCompanyQuickBooksVersion">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetCompanyQuickBooksVersion" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetDivision">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetDivision" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetBasicDivisions">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetBasicDivisions" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetDivisions">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetDivisions" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetDivisionAccess">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetDivisionAccess" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetDriverFields">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetDriverFields" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetAccount">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetAccount" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetAccountInfo">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetAccountInfo" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetBasicAccounts">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetBasicAccounts" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetAccounts">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetAccounts" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetAccountsListItems">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetAccountsListItems" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetUsableAccountsForDivision">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetUsableAccountsForDivision" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetUsableAccountsForDivisionListItems">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetUsableAccountsForDivisionListItems" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetAccountAvailableCredit">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetAccountAvailableCredit" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetJobTemplateFieldsForAccount">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetJobTemplateFieldsForAccount" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetJobTemplateFieldsForDefault">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetJobTemplateFieldsForDefault" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetServiceRatesForJob">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetServiceRatesForJob" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetAccountDefaultServiceRatesForJob">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetAccountDefaultServiceRatesForJob" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetSecuritySettings">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetSecuritySettings" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetUserRoles">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetUserRoles" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetDispatchViewSettings">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetDispatchViewSettings" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="SaveDispatchViewSettings">
            <soap:operation soapAction="http://tempuri.org/IUserService/SaveDispatchViewSettings" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="SerializeServiceCalculatorSettings">
            <soap:operation soapAction="http://tempuri.org/IUserService/SerializeServiceCalculatorSettings" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="DeserializeServiceCalculatorSettings">
            <soap:operation soapAction="http://tempuri.org/IUserService/DeserializeServiceCalculatorSettings" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="SendEmail">
            <soap:operation soapAction="http://tempuri.org/IUserService/SendEmail" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetAccountBillingContact">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetAccountBillingContact" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetJobAutoNumberValue">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetJobAutoNumberValue" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetActiveJobs">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetActiveJobs" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetAssignedJobsForDriver">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetAssignedJobsForDriver" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetJob">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetJob" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetJobForEdit">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetJobForEdit" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="SaveJob">
            <soap:operation soapAction="http://tempuri.org/IUserService/SaveJob" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="CreateJob">
            <soap:operation soapAction="http://tempuri.org/IUserService/CreateJob" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="UpdateJob">
            <soap:operation soapAction="http://tempuri.org/IUserService/UpdateJob" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="AssignJob">
            <soap:operation soapAction="http://tempuri.org/IUserService/AssignJob" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="UnassignJob">
            <soap:operation soapAction="http://tempuri.org/IUserService/UnassignJob" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="TimeStampJob">
            <soap:operation soapAction="http://tempuri.org/IUserService/TimeStampJob" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="MoveJobToWaiting">
            <soap:operation soapAction="http://tempuri.org/IUserService/MoveJobToWaiting" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="MoveJobToHolding">
            <soap:operation soapAction="http://tempuri.org/IUserService/MoveJobToHolding" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetPromptOnFinishFields">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetPromptOnFinishFields" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetClearCodes">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetClearCodes" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetAllClearCodes">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetAllClearCodes" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="FinishJob">
            <soap:operation soapAction="http://tempuri.org/IUserService/FinishJob" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="UndoFinishJob">
            <soap:operation soapAction="http://tempuri.org/IUserService/UndoFinishJob" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="CancelJob">
            <soap:operation soapAction="http://tempuri.org/IUserService/CancelJob" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetJobResourceFromDriverId">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetJobResourceFromDriverId" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="FindJobs">
            <soap:operation soapAction="http://tempuri.org/IUserService/FindJobs" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="BasicFindJobs">
            <soap:operation soapAction="http://tempuri.org/IUserService/BasicFindJobs" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetJobServices">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetJobServices" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetJobServicesCount">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetJobServicesCount" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetJobService">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetJobService" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="SaveJobService">
            <soap:operation soapAction="http://tempuri.org/IUserService/SaveJobService" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetDriver">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetDriver" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetBasicDrivers">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetBasicDrivers" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetDrivers">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetDrivers" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetAssignableDrivers">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetAssignableDrivers" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="SaveDriver">
            <soap:operation soapAction="http://tempuri.org/IUserService/SaveDriver" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetVehicle">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetVehicle" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetBasicVehicles">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetBasicVehicles" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetAssignableVehicles">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetAssignableVehicles" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetTrackableVehicles">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetTrackableVehicles" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="SaveVehicle">
            <soap:operation soapAction="http://tempuri.org/IUserService/SaveVehicle" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="LinkResources">
            <soap:operation soapAction="http://tempuri.org/IUserService/LinkResources" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetDriverLastAssignTime">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetDriverLastAssignTime" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ChangeDriverStatus">
            <soap:operation soapAction="http://tempuri.org/IUserService/ChangeDriverStatus" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ChangeVehicleStatus">
            <soap:operation soapAction="http://tempuri.org/IUserService/ChangeVehicleStatus" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="CreateTowOutJob">
            <soap:operation soapAction="http://tempuri.org/IUserService/CreateTowOutJob" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="CreateReTowJob">
            <soap:operation soapAction="http://tempuri.org/IUserService/CreateReTowJob" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="DuplicateJob">
            <soap:operation soapAction="http://tempuri.org/IUserService/DuplicateJob" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="UpdateJobGeoPoint">
            <soap:operation soapAction="http://tempuri.org/IUserService/UpdateJobGeoPoint" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="UpdateVehicleGeoPoint">
            <soap:operation soapAction="http://tempuri.org/IUserService/UpdateVehicleGeoPoint" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="UpdateDriverGeoPoint">
            <soap:operation soapAction="http://tempuri.org/IUserService/UpdateDriverGeoPoint" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="HideDriverFromMap">
            <soap:operation soapAction="http://tempuri.org/IUserService/HideDriverFromMap" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="HideVehicleFromMap">
            <soap:operation soapAction="http://tempuri.org/IUserService/HideVehicleFromMap" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="AddJobMessage">
            <soap:operation soapAction="http://tempuri.org/IUserService/AddJobMessage" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetJobMessages">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetJobMessages" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetJobMessage">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetJobMessage" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetJobHistory">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetJobHistory" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetTowLienHistory">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetTowLienHistory" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetDistancesFromResourcesToJobViaDistanceMatrix">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetDistancesFromResourcesToJobViaDistanceMatrix" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="SaveDivisionGeoLocation">
            <soap:operation soapAction="http://tempuri.org/IUserService/SaveDivisionGeoLocation" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetOfferPreview">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetOfferPreview" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetPostApprovalPreview">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetPostApprovalPreview" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetMCAdditionalServicesPreview">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetMCAdditionalServicesPreview" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetJobResourceTruckTypes">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetJobResourceTruckTypes" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="SendCustomerLocationLink">
            <soap:operation soapAction="http://tempuri.org/IUserService/SendCustomerLocationLink" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetServiceDescription">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetServiceDescription" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetLaborAverages">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetLaborAverages" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetJobPaymentPreview">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetJobPaymentPreview" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="TakePaymentForJob">
            <soap:operation soapAction="http://tempuri.org/IUserService/TakePaymentForJob" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetSelectableCreditCardTypes">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetSelectableCreditCardTypes" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="IsValidCreditCard">
            <soap:operation soapAction="http://tempuri.org/IUserService/IsValidCreditCard" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetUnbilledJobsSummary">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetUnbilledJobsSummary" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="HasAdditionalUnbilledJobs">
            <soap:operation soapAction="http://tempuri.org/IUserService/HasAdditionalUnbilledJobs" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetOpenInvoicesSummary">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetOpenInvoicesSummary" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="HasAdditionalOpenInvoices">
            <soap:operation soapAction="http://tempuri.org/IUserService/HasAdditionalOpenInvoices" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetPaidInvoicesSummary">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetPaidInvoicesSummary" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="BasicFindInvoices">
            <soap:operation soapAction="http://tempuri.org/IUserService/BasicFindInvoices" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="FindInvoices">
            <soap:operation soapAction="http://tempuri.org/IUserService/FindInvoices" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetInvoiceDetails">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetInvoiceDetails" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetInvoicePaymentSummary">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetInvoicePaymentSummary" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="FindUnbilledServices">
            <soap:operation soapAction="http://tempuri.org/IUserService/FindUnbilledServices" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="PostJobs">
            <soap:operation soapAction="http://tempuri.org/IUserService/PostJobs" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="FindUnpaidServices">
            <soap:operation soapAction="http://tempuri.org/IUserService/FindUnpaidServices" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetOpenDeposits">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetOpenDeposits" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetOpenDepositByAccountId">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetOpenDepositByAccountId" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="CloseOpenDeposits">
            <soap:operation soapAction="http://tempuri.org/IUserService/CloseOpenDeposits" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="EnterPaymentForAccount">
            <soap:operation soapAction="http://tempuri.org/IUserService/EnterPaymentForAccount" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetCreditMemoBalancesForAccount">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetCreditMemoBalancesForAccount" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="FindMotorClubInvoices">
            <soap:operation soapAction="http://tempuri.org/IUserService/FindMotorClubInvoices" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="SendMotorClubInvoices">
            <soap:operation soapAction="http://tempuri.org/IUserService/SendMotorClubInvoices" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetMotorClubInvoiceEditUrl">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetMotorClubInvoiceEditUrl" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="SetTowMagicInvoiceSuccessful">
            <soap:operation soapAction="http://tempuri.org/IUserService/SetTowMagicInvoiceSuccessful" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="SetInvoiceVisibility">
            <soap:operation soapAction="http://tempuri.org/IUserService/SetInvoiceVisibility" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="SaveMCHideCriticalInvoices">
            <soap:operation soapAction="http://tempuri.org/IUserService/SaveMCHideCriticalInvoices" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ClearTowMagicInvoiceError">
            <soap:operation soapAction="http://tempuri.org/IUserService/ClearTowMagicInvoiceError" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="UndoPayments">
            <soap:operation soapAction="http://tempuri.org/IUserService/UndoPayments" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetReceiptPreviewsForJob">
            <soap:operation soapAction="http://tempuri.org/IUserService/GetReceiptPreviewsForJob" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="UndoReleaseVehicle">
            <soap:operation soapAction="http://tempuri.org/IUserService/UndoReleaseVehicle" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="FindUnpaidServiceCommissions">
            <soap:operation soapAction="http://tempuri.org/IUserService/FindUnpaidServiceCommissions" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="SaveCommissions">
            <soap:operation soapAction="http://tempuri.org/IUserService/SaveCommissions" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>
    <wsdl:service name="UserService">
        <wsdl:port name="BasicHttpBinding_IUserService" binding="tns:BasicHttpBinding_IUserService">
            <soap:address location="https://services.dispatchanywhere.net/user/v4.242.0/Services/UserService.svc"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>