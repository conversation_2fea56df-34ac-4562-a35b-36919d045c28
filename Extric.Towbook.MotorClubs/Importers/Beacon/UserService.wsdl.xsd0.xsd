<?xml version="1.0" encoding="utf-8"?>
<xs:schema elementFormDefault="qualified" targetNamespace="http://tempuri.org/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:tns="http://tempuri.org/">
    <xs:import schemaLocation="https://services.dispatchanywhere.net/user/v4.242.0/Services/UserService.svc?xsd=xsd2" namespace="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
    <xs:import schemaLocation="https://services.dispatchanywhere.net/user/v4.242.0/Services/UserService.svc?xsd=xsd3" namespace="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
    <xs:import schemaLocation="https://services.dispatchanywhere.net/user/v4.242.0/Services/UserService.svc?xsd=xsd4" namespace="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model.MotorClubBilling"/>
    <xs:import schemaLocation="https://services.dispatchanywhere.net/user/v4.242.0/Services/UserService.svc?xsd=xsd5" namespace="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Services.MotorClubBilling"/>
    <xs:import schemaLocation="https://services.dispatchanywhere.net/user/v4.242.0/Services/UserService.svc?xsd=xsd6" namespace="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model.TowLien"/>
    <xs:import schemaLocation="https://services.dispatchanywhere.net/user/v4.242.0/Services/UserService.svc?xsd=xsd7" namespace="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Services.BeaconVIN"/>
    <xs:import schemaLocation="https://services.dispatchanywhere.net/user/v4.242.0/Services/UserService.svc?xsd=xsd8" namespace="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model.TowMagic"/>
    <xs:import schemaLocation="https://services.dispatchanywhere.net/user/v4.242.0/Services/UserService.svc?xsd=xsd9" namespace="http://schemas.datacontract.org/2004/07/System.Collections.Generic"/>
    <xs:element name="SaveCreditMemo">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="creditMemo" nillable="true" type="q1:CreditMemo" xmlns:q1="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="SaveCreditMemoResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="creditMemo" nillable="true" type="q2:CreditMemo" xmlns:q2="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="RefundCreditMemos">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="refundCreditMemosRequest" nillable="true" type="q3:RefundCreditMemosRequest" xmlns:q3="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="RefundCreditMemosResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="FindReceipts">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="findReceiptsRequest" nillable="true" type="q4:FindReceiptsRequest" xmlns:q4="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="FindReceiptsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="FindReceiptsResult" nillable="true" type="q5:ArrayOfReceiptPreview" xmlns:q5="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetInvoiceIdsForQBExport">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="startDate" type="xs:dateTime"/>
                <xs:element minOccurs="0" name="endDate" type="xs:dateTime"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetInvoiceIdsForQBExportResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetInvoiceIdsForQBExportResult" nillable="true" type="q6:ArrayOfint" xmlns:q6="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="ProcessBactchIdsForQBExport">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="ids" nillable="true" type="q7:ArrayOfint" xmlns:q7="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="ProcessBactchIdsForQBExportResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="ProcessBactchIdsForQBExportResult" nillable="true" type="q8:ArrayOfstring" xmlns:q8="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetCreditCardTransaction">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="transId" nillable="true" type="xs:string"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetCreditCardTransactionResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetCreditCardTransactionResult" nillable="true" type="q9:CreditCardTransaction" xmlns:q9="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="CheckMCInvoiceEnrollmentStatus">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="CheckMCInvoiceEnrollmentStatusResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="CheckMCInvoiceEnrollmentStatusResult" type="xs:boolean"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetMotorClubInvoice">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="invoiceId" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetMotorClubInvoiceResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetMotorClubInvoiceResult" nillable="true" type="q10:InvoiceRequest" xmlns:q10="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model.MotorClubBilling"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="UpdateMotorClubInvoice">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="invoiceResult" nillable="true" type="q11:InvoiceResult" xmlns:q11="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="UpdateMotorClubInvoiceResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetBillingMCKeys">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetBillingMCKeysResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetBillingMCKeysResult" nillable="true" type="q12:ArrayOfstring" xmlns:q12="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetReconciliationMCKeys">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetReconciliationMCKeysResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetReconciliationMCKeysResult" nillable="true" type="q13:ArrayOfstring" xmlns:q13="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetSurchargeServiceRate">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="request" nillable="true" type="q14:SurchargeRateRequest" xmlns:q14="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetSurchargeServiceRateResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetSurchargeServiceRateResult" nillable="true" type="q15:ServiceRate" xmlns:q15="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="FindMotorClubPayments">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="findMCPaymentsRequest" nillable="true" type="q16:FindMCPaymentsRequest" xmlns:q16="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="FindMotorClubPaymentsResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetLoggedMotorClubPayments">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="auditLogId" type="xs:long"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetLoggedMotorClubPaymentsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetLoggedMotorClubPaymentsResult" nillable="true" type="q17:PaymentEvent" xmlns:q17="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Services.MotorClubBilling"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="ApplyMotorClubPayments">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="motorClubPayments" nillable="true" type="q18:ArrayOfMotorClubPayment" xmlns:q18="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Services.MotorClubBilling"/>
                <xs:element minOccurs="0" name="backgroundJobId" nillable="true" type="xs:string"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="ApplyMotorClubPaymentsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="ApplyMotorClubPaymentsResult" nillable="true" type="xs:string"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetImpoundLots">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetImpoundLotsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetImpoundLotsResult" nillable="true" type="q19:ArrayOfImpoundLot" xmlns:q19="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetImpoundLotVehicleCounts">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetImpoundLotVehicleCountsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetImpoundLotVehicleCountsResult" nillable="true" type="q20:ArrayOfImpoundLotVehicleCount" xmlns:q20="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetSelectableZones">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetSelectableZonesResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetSelectableZonesResult" nillable="true" type="q21:ArrayOfDictionaryEntry" xmlns:q21="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetSelectablePriorities">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetSelectablePrioritiesResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetSelectablePrioritiesResult" nillable="true" type="q22:ArrayOfDictionaryEntry" xmlns:q22="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetSelectableTruckTypes">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetSelectableTruckTypesResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetSelectableTruckTypesResult" nillable="true" type="q23:ArrayOfDictionaryEntry" xmlns:q23="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetSelectablePaymentMethods">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetSelectablePaymentMethodsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetSelectablePaymentMethodsResult" nillable="true" type="q24:ArrayOfDictionaryEntry" xmlns:q24="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetVehicleStatuses">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetVehicleStatusesResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetVehicleStatusesResult" nillable="true" type="q25:ArrayOfDictionaryEntry" xmlns:q25="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetDriverStatuses">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetDriverStatusesResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetDriverStatusesResult" nillable="true" type="q26:ArrayOfDictionaryEntry" xmlns:q26="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetJobStatusDescriptions">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetJobStatusDescriptionsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetJobStatusDescriptionsResult" nillable="true" type="q27:ArrayOfKeyValueOfJobStatusstringBCBMWSd4" xmlns:q27="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetAssignedStatusDescriptions">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetAssignedStatusDescriptionsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetAssignedStatusDescriptionsResult" nillable="true" type="q28:ArrayOfKeyValueOfAssignedStatusstringBCBMWSd4" xmlns:q28="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetOfferStatusDescriptions">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetOfferStatusDescriptionsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetOfferStatusDescriptionsResult" nillable="true" type="q29:ArrayOfKeyValueOfOfferStatusstringBCBMWSd4" xmlns:q29="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetDepartments">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetDepartmentsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetDepartmentsResult" nillable="true" type="q30:ArrayOfDictionaryEntry" xmlns:q30="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetSelectableReasons">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetSelectableReasonsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetSelectableReasonsResult" nillable="true" type="q31:ArrayOfstring" xmlns:q31="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetCancelReasons">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetCancelReasonsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetCancelReasonsResult" nillable="true" type="q32:ArrayOfstring" xmlns:q32="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetCities">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetCitiesResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetCitiesResult" nillable="true" type="q33:ArrayOfstring" xmlns:q33="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetStates">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetStatesResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetStatesResult" nillable="true" type="q34:ArrayOfstring" xmlns:q34="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetVehicleColors">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetVehicleColorsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetVehicleColorsResult" nillable="true" type="q35:ArrayOfstring" xmlns:q35="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetKeyLocations">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetKeyLocationsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetKeyLocationsResult" nillable="true" type="q36:ArrayOfstring" xmlns:q36="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetVehicleStyles">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetVehicleStylesResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetVehicleStylesResult" nillable="true" type="q37:ArrayOfstring" xmlns:q37="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetForcedStatusDescriptions">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetForcedStatusDescriptionsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetForcedStatusDescriptionsResult" nillable="true" type="q38:ArrayOfKeyValueOfForcedStatusstringBCBMWSd4" xmlns:q38="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetGroup1CustomDictionary1">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetGroup1CustomDictionary1Response">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetGroup1CustomDictionary1Result" nillable="true" type="q39:ArrayOfDictionaryEntry" xmlns:q39="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetGroup1CustomDictionary2">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetGroup1CustomDictionary2Response">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetGroup1CustomDictionary2Result" nillable="true" type="q40:ArrayOfDictionaryEntry" xmlns:q40="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetGroup2CustomDictionary1">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetGroup2CustomDictionary1Response">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetGroup2CustomDictionary1Result" nillable="true" type="q41:ArrayOfDictionaryEntry" xmlns:q41="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetGroup2CustomDictionary2">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetGroup2CustomDictionary2Response">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetGroup2CustomDictionary2Result" nillable="true" type="q42:ArrayOfDictionaryEntry" xmlns:q42="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetAccountTypes">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetAccountTypesResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetAccountTypesResult" nillable="true" type="q43:ArrayOfDictionaryEntry" xmlns:q43="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetTaxRates">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetTaxRatesResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetTaxRatesResult" nillable="true" type="q44:ArrayOfDictionaryEntry" xmlns:q44="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="ConnectToPush">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="clientInfo" nillable="true" type="q45:ClientInfo" xmlns:q45="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="ConnectToPushResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="DisconnectFromPush">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="DisconnectFromPushResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetVehicleDetailsURL">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="vehicleId" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetVehicleDetailsURLResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetVehicleDetailsURLResult" nillable="true" type="xs:string"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetVehicleSearchUrl">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="searchString" nillable="true" type="xs:string"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetVehicleSearchUrlResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetVehicleSearchUrlResult" nillable="true" type="xs:string"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="TowSpecSearch">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="searchString" nillable="true" type="xs:string"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="TowSpecSearchResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="TowSpecSearchResult" nillable="true" type="q46:ArrayOfTowSpecVehiclePreview" xmlns:q46="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetAllTowSpecVehiclesIfNewer">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="lastRetrieved" type="xs:dateTime"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetAllTowSpecVehiclesIfNewerResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetAllTowSpecVehiclesIfNewerResult" nillable="true" type="q47:ArrayOfTowSpecVehiclePreview" xmlns:q47="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetAllTowSpecVehicles">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetAllTowSpecVehiclesResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetAllTowSpecVehiclesResult" nillable="true" type="q48:ArrayOfTowSpecVehiclePreview" xmlns:q48="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="SendToTowLien">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="workOrdDtId" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="SendToTowLienResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="SendToTowLienResult" nillable="true" type="q49:AddVehicleResult" xmlns:q49="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model.TowLien"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetTowLienLocations">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="authToken" nillable="true" type="xs:string"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetTowLienLocationsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetTowLienLocationsResult" nillable="true" type="q50:ArrayOfLotLocation" xmlns:q50="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model.TowLien"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="DecodeVin">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="vin" nillable="true" type="xs:string"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="DecodeVinResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="DecodeVinResult" nillable="true" type="q51:DecodeResult" xmlns:q51="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Services.BeaconVIN"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="LookupVINByLicensePlate">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="licensePlate" nillable="true" type="q52:LicensePlate" xmlns:q52="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Services.BeaconVIN"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="LookupVINByLicensePlateResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="LookupVINByLicensePlateResult" nillable="true" type="q53:PlateLookUpResult" xmlns:q53="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Services.BeaconVIN"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetAuction">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="id" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetAuctionResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetAuctionResult" nillable="true" type="q54:Auction" xmlns:q54="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetAuctions">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="ids" nillable="true" type="q55:ArrayOfint" xmlns:q55="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetAuctionsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetAuctionsResult" nillable="true" type="q56:ArrayOfAuction" xmlns:q56="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetAuctionAndUpcoming">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="id" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetAuctionAndUpcomingResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetAuctionAndUpcomingResult" nillable="true" type="q57:ArrayOfAuction" xmlns:q57="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetUpcomingAuctions">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetUpcomingAuctionsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetUpcomingAuctionsResult" nillable="true" type="q58:ArrayOfAuction" xmlns:q58="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="SaveAuction">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="auction" nillable="true" type="q59:Auction" xmlns:q59="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="SaveAuctionResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="auction" nillable="true" type="q60:Auction" xmlns:q60="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetJobPhotos">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="jobPhotoRequest" nillable="true" type="q61:JobPhotoRequest" xmlns:q61="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetJobPhotosResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetJobPhotosResult" nillable="true" type="q62:ArrayOfDAPhotoInfo" xmlns:q62="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetJobPhotoUploadUrl">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="workOrdDtId" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetJobPhotoUploadUrlResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetJobPhotoUploadUrlResult" nillable="true" type="xs:string"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetJobPhotoUploadToken">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="workOrdDtId" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetJobPhotoUploadTokenResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetJobPhotoUploadTokenResult" nillable="true" type="xs:string"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="SetJobPhotoPrivate">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="workOrdDtId" type="xs:int"/>
                <xs:element minOccurs="0" name="photoName" nillable="true" type="xs:string"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="SetJobPhotoPrivateResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="SetJobPhotoPublic">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="workOrdDtId" type="xs:int"/>
                <xs:element minOccurs="0" name="photoName" nillable="true" type="xs:string"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="SetJobPhotoPublicResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="DeleteJobPhoto">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="workOrdDtId" type="xs:int"/>
                <xs:element minOccurs="0" name="photoName" nillable="true" type="xs:string"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="DeleteJobPhotoResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="SendJobPhotosShareUrl">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="sendJobPhotosEmailRequest" nillable="true" type="q63:SendJobPhotosEmailRequest" xmlns:q63="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="SendJobPhotosShareUrlResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetPhotoCount">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="workOrdId" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetPhotoCountResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetPhotoCountResult" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetSecurePhotoUploadUrl">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="request" nillable="true" type="q64:GetSecurePhotoUploadUrlRequest" xmlns:q64="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetSecurePhotoUploadUrlResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetSecurePhotoUploadUrlResult" nillable="true" type="xs:string"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="SendReportEmail">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="sendReportEmailRequest" nillable="true" type="q65:SendReportEmailRequest" xmlns:q65="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="SendReportEmailResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetReportSender">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="reportParameters" nillable="true" type="q66:ArrayOfKeyValueOfstringanyType" xmlns:q66="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetReportSenderResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetReportSenderResult" nillable="true" type="q67:ReportSender" xmlns:q67="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="SendReportFax">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="sendReportFaxRequest" nillable="true" type="q68:SendReportFaxRequest" xmlns:q68="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="SendReportFaxResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetDALienUrl">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="workOrdDtId" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetDALienUrlResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetDALienUrlResult" nillable="true" type="xs:string"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="LogUploadedJobPhotos">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="logJobPhotoRequest" nillable="true" type="q69:ArrayOfLogJobPhotoRequest" xmlns:q69="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="LogUploadedJobPhotosResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="LogDeletedJobPhotos">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="logJobPhotoRequest" nillable="true" type="q70:ArrayOfLogJobPhotoRequest" xmlns:q70="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="LogDeletedJobPhotosResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="UpdateOfferStatus">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="towMagicUpdateOfferStatusRequest" nillable="true" type="q71:UpdateOfferStatusRequest" xmlns:q71="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model.TowMagic"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="UpdateOfferStatusResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="UpdateOfferStatusResult" type="xs:boolean"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="PostApprovalUpdate">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="postApprovalUpdateRequest" nillable="true" type="q72:PostApprovalUpdateRequest" xmlns:q72="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model.TowMagic"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="PostApprovalUpdateResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="PostApprovalUpdateResult" type="xs:boolean"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetServiceMappingsFromJson">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="mcTasksJson" nillable="true" type="xs:string"/>
                <xs:element minOccurs="0" name="truckType" nillable="true" type="xs:string"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetServiceMappingsFromJsonResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetServiceMappingsFromJsonResult" nillable="true" type="q73:ServiceMappings" xmlns:q73="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetServiceMappingsFromTasks">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="tasks" nillable="true" type="q74:ArrayOfstring" xmlns:q74="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
                <xs:element minOccurs="0" name="truckType" nillable="true" type="xs:string"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetServiceMappingsFromTasksResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetServiceMappingsFromTasksResult" nillable="true" type="q75:ServiceMappings" xmlns:q75="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetUTCNow">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetUTCNowResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetUTCNowResult" type="xs:dateTime"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="AgreeToEULA">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="AgreeToEULAResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetChangelogArticleContent">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="version" nillable="true" type="xs:string"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetChangelogArticleContentResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetChangelogArticleContentResult" nillable="true" type="q76:ChangelogArticleContent" xmlns:q76="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetBasicBSCompanies">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetBasicBSCompaniesResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetBasicBSCompaniesResult" nillable="true" type="q77:ArrayOfBasicBSCompany" xmlns:q77="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetCurrentBasicBSCompany">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetCurrentBasicBSCompanyResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetCurrentBasicBSCompanyResult" nillable="true" type="q78:BasicBSCompany" xmlns:q78="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetCurrentUser">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetCurrentUserResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetCurrentUserResult" nillable="true" type="q79:BasicDAUser" xmlns:q79="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetBasicDAUsers">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetBasicDAUsersResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetBasicDAUsersResult" nillable="true" type="q80:ArrayOfBasicDAUser" xmlns:q80="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetCompany">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetCompanyResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetCompanyResult" nillable="true" type="q81:Company" xmlns:q81="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetCompanyQuickBooksVersion">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetCompanyQuickBooksVersionResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetCompanyQuickBooksVersionResult" type="q82:QuickbookVersion" xmlns:q82="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetDivision">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="divisionId" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetDivisionResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetDivisionResult" nillable="true" type="q83:Division" xmlns:q83="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetBasicDivisions">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetBasicDivisionsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetBasicDivisionsResult" nillable="true" type="q84:ArrayOfBasicDivision" xmlns:q84="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetDivisions">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetDivisionsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetDivisionsResult" nillable="true" type="q85:ArrayOfDivision" xmlns:q85="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetDivisionAccess">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetDivisionAccessResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetDivisionAccessResult" nillable="true" type="q86:ArrayOfDivisionAccess" xmlns:q86="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetDriverFields">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetDriverFieldsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetDriverFieldsResult" nillable="true" type="q87:ArrayOfDriverField" xmlns:q87="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetAccount">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="accountId" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetAccountResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetAccountResult" nillable="true" type="q88:Account" xmlns:q88="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetAccountInfo">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="accountId" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetAccountInfoResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetAccountInfoResult" nillable="true" type="q89:AccountInfo" xmlns:q89="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetBasicAccounts">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetBasicAccountsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetBasicAccountsResult" nillable="true" type="q90:ArrayOfBasicAccount" xmlns:q90="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetAccounts">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetAccountsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetAccountsResult" nillable="true" type="q91:ArrayOfAccount" xmlns:q91="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetAccountsListItems">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetAccountsListItemsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetAccountsListItemsResult" nillable="true" type="q92:ArrayOfKeyValuePairOfintstring" xmlns:q92="http://schemas.datacontract.org/2004/07/System.Collections.Generic"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetUsableAccountsForDivision">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="divisionId" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetUsableAccountsForDivisionResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetUsableAccountsForDivisionResult" nillable="true" type="q93:ArrayOfAccount" xmlns:q93="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetUsableAccountsForDivisionListItems">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="divisionId" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetUsableAccountsForDivisionListItemsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetUsableAccountsForDivisionListItemsResult" nillable="true" type="q94:ArrayOfKeyValuePairOfintstring" xmlns:q94="http://schemas.datacontract.org/2004/07/System.Collections.Generic"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetAccountAvailableCredit">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="accountId" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetAccountAvailableCreditResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetAccountAvailableCreditResult" nillable="true" type="q95:AvailableCredit" xmlns:q95="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetJobTemplateFieldsForAccount">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="accountId" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetJobTemplateFieldsForAccountResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetJobTemplateFieldsForAccountResult" nillable="true" type="q96:ArrayOfJobTemplateField" xmlns:q96="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetJobTemplateFieldsForDefault">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetJobTemplateFieldsForDefaultResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetJobTemplateFieldsForDefaultResult" nillable="true" type="q97:ArrayOfJobTemplateField" xmlns:q97="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetServiceRatesForJob">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="job" nillable="true" type="q98:Job" xmlns:q98="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetServiceRatesForJobResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetServiceRatesForJobResult" nillable="true" type="q99:ArrayOfServiceRate" xmlns:q99="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetAccountDefaultServiceRatesForJob">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="job" nillable="true" type="q100:Job" xmlns:q100="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetAccountDefaultServiceRatesForJobResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetAccountDefaultServiceRatesForJobResult" nillable="true" type="q101:ArrayOfServiceRate" xmlns:q101="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetSecuritySettings">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetSecuritySettingsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetSecuritySettingsResult" nillable="true" type="q102:ArrayOfSecuritySetting" xmlns:q102="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetUserRoles">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetUserRolesResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetUserRolesResult" nillable="true" type="q103:ArrayOfstring" xmlns:q103="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetDispatchViewSettings">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetDispatchViewSettingsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetDispatchViewSettingsResult" nillable="true" type="q104:DispatchViewSettings" xmlns:q104="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="SaveDispatchViewSettings">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="dispViewSettings" nillable="true" type="q105:DispatchViewSettings" xmlns:q105="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="SaveDispatchViewSettingsResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="SerializeServiceCalculatorSettings">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="serviceCalcSettings" nillable="true" type="q106:ServiceCalculatorSettings" xmlns:q106="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="SerializeServiceCalculatorSettingsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="SerializeServiceCalculatorSettingsResult" nillable="true" type="xs:string"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="DeserializeServiceCalculatorSettings">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="serviceCalcSettingsXML" nillable="true" type="xs:string"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="DeserializeServiceCalculatorSettingsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="DeserializeServiceCalculatorSettingsResult" nillable="true" type="q107:ServiceCalculatorSettings" xmlns:q107="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="SendEmail">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="sendEmailRequest" nillable="true" type="q108:SendEmailRequest" xmlns:q108="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="SendEmailResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetAccountBillingContact">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="accountId" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetAccountBillingContactResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetAccountBillingContactResult" nillable="true" type="q109:Contact" xmlns:q109="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetJobAutoNumberValue">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetJobAutoNumberValueResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetJobAutoNumberValueResult" nillable="true" type="xs:string"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetActiveJobs">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetActiveJobsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetActiveJobsResult" nillable="true" type="q110:ArrayOfJob" xmlns:q110="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetAssignedJobsForDriver">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="driverId" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetAssignedJobsForDriverResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetAssignedJobsForDriverResult" nillable="true" type="q111:ArrayOfJob" xmlns:q111="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetJob">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="workOrdDtId" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetJobResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetJobResult" nillable="true" type="q112:Job" xmlns:q112="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetJobForEdit">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="workOrdDtId" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetJobForEditResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetJobForEditResult" nillable="true" type="q113:Job" xmlns:q113="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="SaveJob">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="job" nillable="true" type="q114:Job" xmlns:q114="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="SaveJobResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="job" nillable="true" type="q115:Job" xmlns:q115="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="CreateJob">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="createJobReq" nillable="true" type="q116:CreateJobRequest2" xmlns:q116="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="CreateJobResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="CreateJobResult" nillable="true" type="q117:UpdateJobResponse" xmlns:q117="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="UpdateJob">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="updateJobReq" nillable="true" type="q118:UpdateJobRequest" xmlns:q118="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="UpdateJobResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="UpdateJobResult" nillable="true" type="q119:UpdateJobResponse" xmlns:q119="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="AssignJob">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="assignJobReq" nillable="true" type="q120:AssignJobRequest" xmlns:q120="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="AssignJobResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="UnassignJob">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="unassignJobReq" nillable="true" type="q121:UnassignJobRequest" xmlns:q121="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="UnassignJobResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="TimeStampJob">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="timestampJobReq" nillable="true" type="q122:TimeStampJobRequest" xmlns:q122="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="TimeStampJobResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="MoveJobToWaiting">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="moveToWaitingReq" nillable="true" type="q123:MoveJobToWaitingRequest" xmlns:q123="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="MoveJobToWaitingResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="MoveJobToHolding">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="moveToHoldingReq" nillable="true" type="q124:MoveJobToHoldingRequest" xmlns:q124="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="MoveJobToHoldingResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetPromptOnFinishFields">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="getPromptOnFinishFieldsRequest" nillable="true" type="q125:GetPromptOnFinishFieldsRequest" xmlns:q125="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetPromptOnFinishFieldsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetPromptOnFinishFieldsResult" nillable="true" type="q126:ArrayOfEditPropertyAttributes" xmlns:q126="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetClearCodes">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="workOrdDtId" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetClearCodesResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetClearCodesResult" nillable="true" type="q127:ClearCodesResponse" xmlns:q127="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetAllClearCodes">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="workOrdDtId" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetAllClearCodesResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetAllClearCodesResult" nillable="true" type="q128:ClearCodesResponse" xmlns:q128="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="FinishJob">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="finishJobRequest" nillable="true" type="q129:FinishJobRequest" xmlns:q129="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="FinishJobResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="UndoFinishJob">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="undoFinishJobReq" nillable="true" type="q130:UndoFinishJobRequest" xmlns:q130="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="UndoFinishJobResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="CancelJob">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="cancelJobRequest" nillable="true" type="q131:CancelJobRequest" xmlns:q131="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="CancelJobResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="CancelJobResult" nillable="true" type="q132:CancelJobResponse" xmlns:q132="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetJobResourceFromDriverId">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="jobResourceRequest" nillable="true" type="q133:JobResourceRequest" xmlns:q133="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetJobResourceFromDriverIdResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetJobResourceFromDriverIdResult" nillable="true" type="q134:JobResource" xmlns:q134="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="FindJobs">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="findJobsRequest" nillable="true" type="q135:FindJobsRequest" xmlns:q135="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="FindJobsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="FindJobsResult" nillable="true" type="q136:ArrayOfJob" xmlns:q136="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="BasicFindJobs">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="basicFindJobsRequest" nillable="true" type="q137:BasicFindJobsRequest" xmlns:q137="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="BasicFindJobsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="BasicFindJobsResult" nillable="true" type="q138:ArrayOfJob" xmlns:q138="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetJobServices">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="workOrdDtId" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetJobServicesResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetJobServicesResult" nillable="true" type="q139:ArrayOfJobService" xmlns:q139="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetJobServicesCount">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="workOrdDtId" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetJobServicesCountResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetJobServicesCountResult" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetJobService">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="WorkOrdChgDtId" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetJobServiceResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetJobServiceResult" nillable="true" type="q140:JobService" xmlns:q140="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="SaveJobService">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="jobService" nillable="true" type="q141:JobService" xmlns:q141="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="SaveJobServiceResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="jobService" nillable="true" type="q142:JobService" xmlns:q142="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetDriver">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="driverId" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetDriverResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetDriverResult" nillable="true" type="q143:Driver" xmlns:q143="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetBasicDrivers">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetBasicDriversResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetBasicDriversResult" nillable="true" type="q144:ArrayOfBasicDriver" xmlns:q144="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetDrivers">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetDriversResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetDriversResult" nillable="true" type="q145:ArrayOfDriver" xmlns:q145="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetAssignableDrivers">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="divisionId" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetAssignableDriversResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetAssignableDriversResult" nillable="true" type="q146:ArrayOfDriver" xmlns:q146="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="SaveDriver">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="driver" nillable="true" type="q147:Driver" xmlns:q147="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="SaveDriverResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="driver" nillable="true" type="q148:Driver" xmlns:q148="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetVehicle">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="vehicleId" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetVehicleResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetVehicleResult" nillable="true" type="q149:Vehicle" xmlns:q149="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetBasicVehicles">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetBasicVehiclesResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetBasicVehiclesResult" nillable="true" type="q150:ArrayOfBasicVehicle" xmlns:q150="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetAssignableVehicles">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="divisionId" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetAssignableVehiclesResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetAssignableVehiclesResult" nillable="true" type="q151:ArrayOfVehicle" xmlns:q151="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetTrackableVehicles">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetTrackableVehiclesResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetTrackableVehiclesResult" nillable="true" type="q152:ArrayOfVehicle" xmlns:q152="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="SaveVehicle">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="vehicle" nillable="true" type="q153:Vehicle" xmlns:q153="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="SaveVehicleResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="vehicle" nillable="true" type="q154:Vehicle" xmlns:q154="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="LinkResources">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="linkResourcesRequest" nillable="true" type="q155:LinkResourcesRequest" xmlns:q155="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="LinkResourcesResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetDriverLastAssignTime">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="driverId" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetDriverLastAssignTimeResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetDriverLastAssignTimeResult" type="xs:dateTime"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="ChangeDriverStatus">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="changeStatusRequest" nillable="true" type="q156:ChangeStatusRequest" xmlns:q156="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="ChangeDriverStatusResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="ChangeVehicleStatus">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="changeStatusRequest" nillable="true" type="q157:ChangeStatusRequest" xmlns:q157="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="ChangeVehicleStatusResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="CreateTowOutJob">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="workOrdDtId" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="CreateTowOutJobResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="CreateTowOutJobResult" nillable="true" type="q158:TowJobResult" xmlns:q158="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="CreateReTowJob">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="workOrdDtId" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="CreateReTowJobResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="CreateReTowJobResult" nillable="true" type="q159:TowJobResult" xmlns:q159="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="DuplicateJob">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="workOrdDtId" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="DuplicateJobResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="DuplicateJobResult" nillable="true" type="q160:DuplicateJobResult" xmlns:q160="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="UpdateJobGeoPoint">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="jobGeoPointReq" nillable="true" type="q161:UpdateJobGeoPointRequest" xmlns:q161="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="UpdateJobGeoPointResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="UpdateVehicleGeoPoint">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="vehGeoPointReq" nillable="true" type="q162:UpdateVehicleGeoPointRequest" xmlns:q162="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="UpdateVehicleGeoPointResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="UpdateDriverGeoPoint">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="driverGeoPointReq" nillable="true" type="q163:UpdateDriverGeoPointRequest" xmlns:q163="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="UpdateDriverGeoPointResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="HideDriverFromMap">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="driverId" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="HideDriverFromMapResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="HideVehicleFromMap">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="vehicleId" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="HideVehicleFromMapResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="AddJobMessage">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="jobMessage" nillable="true" type="q164:AddJobMessageRequest" xmlns:q164="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="AddJobMessageResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetJobMessages">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="workOrdDtId" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetJobMessagesResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetJobMessagesResult" nillable="true" type="q165:ArrayOfJobMessage" xmlns:q165="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetJobMessage">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="id" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetJobMessageResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetJobMessageResult" nillable="true" type="q166:JobMessage" xmlns:q166="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetJobHistory">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="workOrdDtId" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetJobHistoryResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetJobHistoryResult" nillable="true" type="q167:ArrayOfJobHistory" xmlns:q167="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetTowLienHistory">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="workOrdDtId" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetTowLienHistoryResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetTowLienHistoryResult" nillable="true" type="q168:ArrayOfJobHistory" xmlns:q168="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetDistancesFromResourcesToJobViaDistanceMatrix">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="distancesFromResourcesToJob" nillable="true" type="q169:DistancesFromResourcesToJobRequest" xmlns:q169="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetDistancesFromResourcesToJobViaDistanceMatrixResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetDistancesFromResourcesToJobViaDistanceMatrixResult" nillable="true" type="q170:ArrayOfKeyValueOfintdouble" xmlns:q170="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="SaveDivisionGeoLocation">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="id" type="xs:int"/>
                <xs:element minOccurs="0" name="geoPoint" nillable="true" type="q171:GeoPoint" xmlns:q171="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="SaveDivisionGeoLocationResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetOfferPreview">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="workOrdDtId" type="xs:int"/>
                <xs:element minOccurs="0" name="tmcvs" type="q172:TowMagicClientViewSchema" xmlns:q172="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetOfferPreviewResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetOfferPreviewResult" nillable="true" type="q173:OfferPreview" xmlns:q173="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetPostApprovalPreview">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="papr" nillable="true" type="q174:PostApprovalPreviewRequest" xmlns:q174="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model.TowMagic"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetPostApprovalPreviewResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetPostApprovalPreviewResult" nillable="true" type="q175:PostApprovalResponse" xmlns:q175="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model.TowMagic"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetMCAdditionalServicesPreview">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="motorClubKey" nillable="true" type="xs:string"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetMCAdditionalServicesPreviewResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetMCAdditionalServicesPreviewResult" nillable="true" type="q176:MCAdditionalServicesPreview" xmlns:q176="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model.TowMagic"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetJobResourceTruckTypes">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="workOrdDtId" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetJobResourceTruckTypesResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetJobResourceTruckTypesResult" nillable="true" type="q177:ArrayOfstring" xmlns:q177="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="SendCustomerLocationLink">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="cllr" nillable="true" type="q178:CustomerLocationLinkRequest" xmlns:q178="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="SendCustomerLocationLinkResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="SendCustomerLocationLinkResult" nillable="true" type="q179:CustomerLocationLinkResponse" xmlns:q179="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetServiceDescription">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="serviceDescId" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetServiceDescriptionResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetServiceDescriptionResult" nillable="true" type="q180:ServiceDescription" xmlns:q180="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetLaborAverages">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="laborAveragesRequest" nillable="true" type="q181:LaborAveragesRequest" xmlns:q181="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetLaborAveragesResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetLaborAveragesResult" nillable="true" type="q182:LaborAverages" xmlns:q182="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetJobPaymentPreview">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="workOrdDtId" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetJobPaymentPreviewResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetJobPaymentPreviewResult" nillable="true" type="q183:JobPaymentPreview" xmlns:q183="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="TakePaymentForJob">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="takeJobPaymentRequest" nillable="true" type="q184:TakeJobPaymentRequest" xmlns:q184="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="TakePaymentForJobResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="TakePaymentForJobResult" nillable="true" type="q185:TakeJobPaymentResponse" xmlns:q185="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
                <xs:element minOccurs="0" name="takeJobPaymentRequest" nillable="true" type="q186:TakeJobPaymentRequest" xmlns:q186="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetSelectableCreditCardTypes">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetSelectableCreditCardTypesResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetSelectableCreditCardTypesResult" nillable="true" type="q187:ArrayOfKeyValueOfintstring" xmlns:q187="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="IsValidCreditCard">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="creditCard" nillable="true" type="q188:CreditCard" xmlns:q188="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="IsValidCreditCardResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="IsValidCreditCardResult" nillable="true" type="q189:IsValidCreditCardResult" xmlns:q189="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
                <xs:element minOccurs="0" name="creditCard" nillable="true" type="q190:CreditCard" xmlns:q190="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetUnbilledJobsSummary">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetUnbilledJobsSummaryResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetUnbilledJobsSummaryResult" nillable="true" type="q191:QuickSummary" xmlns:q191="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="HasAdditionalUnbilledJobs">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="HasAdditionalUnbilledJobsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="HasAdditionalUnbilledJobsResult" type="xs:boolean"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetOpenInvoicesSummary">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="baseDate" type="xs:dateTime"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetOpenInvoicesSummaryResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetOpenInvoicesSummaryResult" nillable="true" type="q192:QuickSummary" xmlns:q192="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="HasAdditionalOpenInvoices">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="baseDate" type="xs:dateTime"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="HasAdditionalOpenInvoicesResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="HasAdditionalOpenInvoicesResult" type="xs:boolean"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetPaidInvoicesSummary">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="baseDate" type="xs:dateTime"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetPaidInvoicesSummaryResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetPaidInvoicesSummaryResult" nillable="true" type="q193:QuickSummary" xmlns:q193="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="BasicFindInvoices">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="basicFindInvoicesRequest" nillable="true" type="q194:BasicFindInvoicesRequest" xmlns:q194="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="BasicFindInvoicesResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="BasicFindInvoicesResult" nillable="true" type="q195:ArrayOfInvoicePreview" xmlns:q195="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="FindInvoices">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="findInvoicesRequest" nillable="true" type="q196:FindInvoicesRequest" xmlns:q196="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="FindInvoicesResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="FindInvoicesResult" nillable="true" type="q197:ArrayOfInvoicePreview" xmlns:q197="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetInvoiceDetails">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="invoiceId" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetInvoiceDetailsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetInvoiceDetailsResult" nillable="true" type="q198:InvoiceDetails" xmlns:q198="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetInvoicePaymentSummary">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="invoiceId" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetInvoicePaymentSummaryResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetInvoicePaymentSummaryResult" nillable="true" type="q199:InvoicePaymentsSummary" xmlns:q199="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="FindUnbilledServices">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="findUnbilledServicesRequest" nillable="true" type="q200:FindUnbilledServicesRequest" xmlns:q200="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="FindUnbilledServicesResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="FindUnbilledServicesResult" nillable="true" type="q201:ArrayOfUnbilledJobService" xmlns:q201="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="PostJobs">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="postJobsRequest" nillable="true" type="q202:PostJobsRequest" xmlns:q202="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="PostJobsResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="FindUnpaidServices">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="findUnpaidServicesRequest" nillable="true" type="q203:FindUnpaidServicesRequest" xmlns:q203="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="FindUnpaidServicesResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="FindUnpaidServicesResult" nillable="true" type="q204:ArrayOfUnpaidJobService" xmlns:q204="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetOpenDeposits">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetOpenDepositsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetOpenDepositsResult" nillable="true" type="q205:ArrayOfDeposit" xmlns:q205="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetOpenDepositByAccountId">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="accountId" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetOpenDepositByAccountIdResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetOpenDepositByAccountIdResult" nillable="true" type="q206:Deposit" xmlns:q206="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="CloseOpenDeposits">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="depositIds" nillable="true" type="q207:ArrayOfint" xmlns:q207="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="CloseOpenDepositsResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="EnterPaymentForAccount">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="enterPaymentRequest" nillable="true" type="q208:EnterPaymentRequest" xmlns:q208="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="EnterPaymentForAccountResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetCreditMemoBalancesForAccount">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="accountId" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetCreditMemoBalancesForAccountResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetCreditMemoBalancesForAccountResult" nillable="true" type="q209:ArrayOfCreditMemoBalance" xmlns:q209="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="FindMotorClubInvoices">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="findMotorClubInvoicesRequest" nillable="true" type="q210:FindMotorClubInvoicesRequest" xmlns:q210="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="FindMotorClubInvoicesResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="FindMotorClubInvoicesResult" nillable="true" type="q211:ArrayOfMotorClubInvoice" xmlns:q211="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="SendMotorClubInvoices">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="invoiceIds" nillable="true" type="q212:ArrayOfint" xmlns:q212="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="SendMotorClubInvoicesResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetMotorClubInvoiceEditUrl">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="invoiceId" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetMotorClubInvoiceEditUrlResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetMotorClubInvoiceEditUrlResult" nillable="true" type="xs:string"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="SetTowMagicInvoiceSuccessful">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="invoiceId" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="SetTowMagicInvoiceSuccessfulResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="SetInvoiceVisibility">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="invoiceVisibilityRequest" nillable="true" type="q213:InvoiceVisibilityRequest" xmlns:q213="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="SetInvoiceVisibilityResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="SaveMCHideCriticalInvoices">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="mcHideCriticalInvoicesRequest" nillable="true" type="q214:MCHideCriticalInvoicesRequest" xmlns:q214="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="SaveMCHideCriticalInvoicesResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="ClearTowMagicInvoiceError">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="invoiceId" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="ClearTowMagicInvoiceErrorResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="UndoPayments">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="undoPaymentRequest" nillable="true" type="q215:UndoPaymentRequest" xmlns:q215="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="UndoPaymentsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="UndoPaymentsResult" nillable="true" type="q216:UndoPaymentResult" xmlns:q216="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetReceiptPreviewsForJob">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="workOrdDtId" type="xs:int"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetReceiptPreviewsForJobResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="GetReceiptPreviewsForJobResult" nillable="true" type="q217:ArrayOfReceiptPreview" xmlns:q217="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="UndoReleaseVehicle">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="undoReleaseVehicleRequest" nillable="true" type="q218:UndoReleaseVehicleRequest" xmlns:q218="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="UndoReleaseVehicleResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="UndoReleaseVehicleResult" nillable="true" type="q219:UndoPaymentResult" xmlns:q219="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="FindUnpaidServiceCommissions">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="findUnpaidServiceCommissionsRequest" nillable="true" type="q220:FindUnpaidServiceCommissionsRequest" xmlns:q220="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="FindUnpaidServiceCommissionsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="FindUnpaidServiceCommissionsResult" nillable="true" type="q221:ArrayOfServiceCommission" xmlns:q221="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="SaveCommissions">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="saveCommissionsRequest" nillable="true" type="q222:SaveCommissionsRequest" xmlns:q222="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="SaveCommissionsResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
</xs:schema>