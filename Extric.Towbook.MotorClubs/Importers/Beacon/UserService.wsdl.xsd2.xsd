<?xml version="1.0" encoding="utf-8"?>
<xs:schema elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:tns="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model" xmlns:ser="http://schemas.microsoft.com/2003/10/Serialization/">
    <xs:import schemaLocation="https://services.dispatchanywhere.net/user/v4.242.0/Services/UserService.svc?xsd=xsd3" namespace="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
    <xs:import schemaLocation="https://services.dispatchanywhere.net/user/v4.242.0/Services/UserService.svc?xsd=xsd1" namespace="http://schemas.microsoft.com/2003/10/Serialization/"/>
    <xs:import schemaLocation="https://services.dispatchanywhere.net/user/v4.242.0/Services/UserService.svc?xsd=xsd10" namespace="http://schemas.datacontract.org/2004/07/System"/>
    <xs:import schemaLocation="https://services.dispatchanywhere.net/user/v4.242.0/Services/UserService.svc?xsd=xsd11" namespace="http://schemas.datacontract.org/2004/07/System.ComponentModel.DataAnnotations"/>
    <xs:import schemaLocation="https://services.dispatchanywhere.net/user/v4.242.0/Services/UserService.svc?xsd=xsd12" namespace="http://schemas.datacontract.org/2004/07/System.ComponentModel"/>
    <xs:import schemaLocation="https://services.dispatchanywhere.net/user/v4.242.0/Services/UserService.svc?xsd=xsd9" namespace="http://schemas.datacontract.org/2004/07/System.Collections.Generic"/>
    <xs:import schemaLocation="https://services.dispatchanywhere.net/user/v4.242.0/Services/UserService.svc?xsd=xsd8" namespace="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model.TowMagic"/>
    <xs:complexType name="CreditMemo">
        <xs:sequence>
            <xs:element minOccurs="0" name="AccountId" type="xs:int"/>
            <xs:element minOccurs="0" name="Amount" type="xs:decimal"/>
            <xs:element minOccurs="0" name="AppliedInFullOn" nillable="true" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="Comments" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="CreditMemoId" type="xs:int"/>
            <xs:element minOccurs="0" name="CreditType" type="tns:CreditType"/>
            <xs:element minOccurs="0" name="IssuedOn" nillable="true" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="ReceiptId" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="ReferenceNumber" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="CreditMemo" nillable="true" type="tns:CreditMemo"/>
    <xs:simpleType name="CreditType">
        <xs:annotation>
            <xs:appinfo>
                <ActualType Name="short" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/"/>
            </xs:appinfo>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="Unknown"/>
            <xs:enumeration value="Memo"/>
            <xs:enumeration value="Overpayment"/>
            <xs:enumeration value="Underpayment"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:element name="CreditType" nillable="true" type="tns:CreditType"/>
    <xs:complexType name="RefundCreditMemosRequest">
        <xs:sequence>
            <xs:element minOccurs="0" name="AppliedOn" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="CreditMemosToRefund" nillable="true" type="q1:ArrayOfKeyValueOfintdecimal" xmlns:q1="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
            <xs:element minOccurs="0" name="ReferenceNumber" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="RefundCreditMemosRequest" nillable="true" type="tns:RefundCreditMemosRequest"/>
    <xs:complexType name="FindReceiptsRequest">
        <xs:sequence>
            <xs:element minOccurs="0" name="BillToId" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="DateFrom" nillable="true" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="DateRangeKeyword" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="DateTo" nillable="true" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="InvoiceId" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="InvoiceNumber" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="LocalSearchTime" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="PayMethodId" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="ReceiptId" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="ReferenceNumber" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="WorkOrdDtId" nillable="true" type="xs:int"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="FindReceiptsRequest" nillable="true" type="tns:FindReceiptsRequest"/>
    <xs:complexType name="ArrayOfReceiptPreview">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="ReceiptPreview" nillable="true" type="tns:ReceiptPreview"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfReceiptPreview" nillable="true" type="tns:ArrayOfReceiptPreview"/>
    <xs:complexType name="ReceiptPreview">
        <xs:sequence>
            <xs:element minOccurs="0" name="AllowUndo" type="xs:boolean"/>
            <xs:element minOccurs="0" name="Amount" type="xs:decimal"/>
            <xs:element minOccurs="0" name="AuthorizationNumber" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="BillToAccount" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="CCTransactionNumber" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="PaymentMethod" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="ReceiptDate" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="ReceiptId" type="xs:int"/>
            <xs:element minOccurs="0" name="ReferenceNumber" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ReceiptPreview" nillable="true" type="tns:ReceiptPreview"/>
    <xs:complexType name="CreditCardTransaction">
        <xs:complexContent mixed="false">
            <xs:extension base="tns:BaseResponse">
                <xs:sequence>
                    <xs:element minOccurs="0" name="Amount" nillable="true" type="xs:decimal"/>
                    <xs:element minOccurs="0" name="AuthCreds" nillable="true" type="tns:AuthCreds"/>
                    <xs:element minOccurs="0" name="AvsSuccessful" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="BatchPostedOn" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="BatchSettledOn" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="CompletionId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="CompletionType" nillable="true" type="xs:unsignedByte"/>
                    <xs:element minOccurs="0" name="CreditId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="Cvv2Successful" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="Id" type="xs:int"/>
                    <xs:element minOccurs="0" name="MaskedCreditCard" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="MerchantAccountId" type="xs:int"/>
                    <xs:element minOccurs="0" name="PostedOn" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="RawData" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="ReceiptId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="ReferenceId" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="ResponseCode" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="ResponseText" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="SettledOn" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="TaxAmount" nillable="true" type="xs:decimal"/>
                    <xs:element minOccurs="0" name="TransactionDate" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="TransactionId" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="TransactionType" type="tns:TransactionType"/>
                    <xs:element minOccurs="0" name="WorkOrderDtId" nillable="true" type="xs:int"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="CreditCardTransaction" nillable="true" type="tns:CreditCardTransaction"/>
    <xs:complexType name="BaseResponse">
        <xs:sequence>
            <xs:element minOccurs="0" name="IsSuccessful" type="xs:boolean"/>
            <xs:element minOccurs="0" name="Message" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="BaseResponse" nillable="true" type="tns:BaseResponse"/>
    <xs:complexType name="AuthCreds">
        <xs:sequence>
            <xs:element minOccurs="0" name="AccessToken" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="AccessTokenExpiry" nillable="true" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="IsUpdated" type="xs:boolean"/>
            <xs:element minOccurs="0" name="RefreshToken" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="AuthCreds" nillable="true" type="tns:AuthCreds"/>
    <xs:simpleType name="TransactionType">
        <xs:annotation>
            <xs:appinfo>
                <ActualType Name="short" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/"/>
            </xs:appinfo>
        </xs:annotation>
        <xs:list>
            <xs:simpleType>
                <xs:restriction base="xs:string">
                    <xs:enumeration value="Unknown">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">0</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="Sale">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">1</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="PreAuthOnly">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">2</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="Capture">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">4</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="Credit">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">8</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="Void">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">16</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="CheckSale">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">32</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="CheckVoid">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">64</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="CheckCredit">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">128</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="OnlyPaymentRequest">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">256</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="RemotePaymentRequest">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">512</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="Verify">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">1024</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="AsyncPayment">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">2048</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                </xs:restriction>
            </xs:simpleType>
        </xs:list>
    </xs:simpleType>
    <xs:element name="TransactionType" nillable="true" type="tns:TransactionType"/>
    <xs:complexType name="InvoiceResult">
        <xs:sequence>
            <xs:element minOccurs="0" name="InvoiceId" type="xs:int"/>
            <xs:element minOccurs="0" name="InvoiceStatus" type="tns:TowMagicInvoiceStatus"/>
            <xs:element minOccurs="0" name="Message" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="RedirectUrl" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="RequestId" type="xs:int"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="InvoiceResult" nillable="true" type="tns:InvoiceResult"/>
    <xs:simpleType name="TowMagicInvoiceStatus">
        <xs:annotation>
            <xs:appinfo>
                <ActualType Name="unsignedByte" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/"/>
            </xs:appinfo>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="CriticalError"/>
            <xs:enumeration value="EditRequired"/>
            <xs:enumeration value="Submitted"/>
            <xs:enumeration value="SubmittedPreExisting"/>
            <xs:enumeration value="Processing"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:element name="TowMagicInvoiceStatus" nillable="true" type="tns:TowMagicInvoiceStatus"/>
    <xs:complexType name="SurchargeRateRequest">
        <xs:sequence>
            <xs:element minOccurs="0" name="PaymentMethodId" type="xs:int"/>
            <xs:element minOccurs="0" name="TotalAmountAppliedWithoutSurcharge" type="xs:decimal"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="SurchargeRateRequest" nillable="true" type="tns:SurchargeRateRequest"/>
    <xs:complexType name="ServiceRate">
        <xs:complexContent mixed="false">
            <xs:extension base="tns:ServiceBase">
                <xs:sequence>
                    <xs:element minOccurs="0" name="AccountDiscountMultiplier" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="AccountId" type="xs:int"/>
                    <xs:element minOccurs="0" name="PaymentMethodAdjustment" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="ServiceOverrideId" type="xs:int"/>
                    <xs:element minOccurs="0" name="TaxRate1" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="TaxRate2" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="TaxRate3" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="TaxRate4" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="TimeAdjustment" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="ZoneAdjustment" nillable="true" type="xs:string"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="ServiceRate" nillable="true" type="tns:ServiceRate"/>
    <xs:complexType name="ServiceBase">
        <xs:sequence>
            <xs:element minOccurs="0" name="CommissionAmount" nillable="true" type="xs:decimal"/>
            <xs:element minOccurs="0" name="Discount" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="IsDiscountable" type="xs:boolean"/>
            <xs:element minOccurs="0" name="IsTaxable" type="xs:boolean"/>
            <xs:element minOccurs="0" name="LinkedIsTaxable" type="xs:boolean"/>
            <xs:element minOccurs="0" name="LinkedRate" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="LinkedServiceDescriptionId" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="Rate" type="xs:decimal"/>
            <xs:element minOccurs="0" name="ServiceDescription" nillable="true" type="tns:ServiceDescription"/>
            <xs:element minOccurs="0" name="ServiceDescriptionId" type="xs:int"/>
            <xs:element minOccurs="0" name="ServiceId" type="xs:int"/>
            <xs:element minOccurs="0" name="TaxRateCode1" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="TaxRateCode2" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="TaxRateCode3" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="TaxRateCode4" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="XMLSettings" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ServiceBase" nillable="true" type="tns:ServiceBase"/>
    <xs:complexType name="ServiceDescription">
        <xs:sequence>
            <xs:element minOccurs="0" name="CategoryId" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="CompanyId" type="xs:int"/>
            <xs:element minOccurs="0" name="ContinueStorageAfterPayment" type="xs:boolean"/>
            <xs:element minOccurs="0" name="Description" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="Id" type="xs:int"/>
            <xs:element minOccurs="0" name="IsCommissionable" type="xs:boolean"/>
            <xs:element minOccurs="0" name="IsDeleted" type="xs:boolean"/>
            <xs:element minOccurs="0" name="IsNonRevenue" type="xs:boolean"/>
            <xs:element minOccurs="0" name="QBAccountName" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="TowMagicMotorClubBillingServiceType" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="Type" type="tns:ServiceType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ServiceDescription" nillable="true" type="tns:ServiceDescription"/>
    <xs:simpleType name="ServiceType">
        <xs:annotation>
            <xs:appinfo>
                <ActualType Name="short" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/"/>
            </xs:appinfo>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="None"/>
            <xs:enumeration value="Labor"/>
            <xs:enumeration value="Mileage"/>
            <xs:enumeration value="Storage"/>
            <xs:enumeration value="Surcharge"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:element name="ServiceType" nillable="true" type="tns:ServiceType"/>
    <xs:complexType name="FindMCPaymentsRequest">
        <xs:sequence>
            <xs:element minOccurs="0" name="BillToId" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="LocalSearchTime" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="PaymentDateKeyword" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="PaymentFromDate" nillable="true" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="PaymentToDate" nillable="true" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="RequestId" type="ser:guid"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="FindMCPaymentsRequest" nillable="true" type="tns:FindMCPaymentsRequest"/>
    <xs:complexType name="ArrayOfImpoundLot">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="ImpoundLot" nillable="true" type="tns:ImpoundLot"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfImpoundLot" nillable="true" type="tns:ArrayOfImpoundLot"/>
    <xs:complexType name="ImpoundLot">
        <xs:complexContent mixed="false">
            <xs:extension base="tns:Contact">
                <xs:sequence>
                    <xs:element minOccurs="0" name="CompanyId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="DaysBeforeSendToTowLien" nillable="true" type="xs:unsignedByte"/>
                    <xs:element minOccurs="0" name="Id" type="xs:int"/>
                    <xs:element minOccurs="0" name="IsDeleted" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="LotName" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="LotNumber" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="StateLicense" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="TowLienLotId" nillable="true" type="xs:long"/>
                    <xs:element minOccurs="0" name="TowLienMailFromId" nillable="true" type="xs:long"/>
                    <xs:element minOccurs="0" name="TowLienToken" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="TowLienUserName" nillable="true" type="xs:string"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="ImpoundLot" nillable="true" type="tns:ImpoundLot"/>
    <xs:complexType name="Contact">
        <xs:complexContent mixed="false">
            <xs:extension base="tns:NotifyErrorBase">
                <xs:sequence>
                    <xs:element minOccurs="0" name="Address" nillable="true" type="tns:Address"/>
                    <xs:element minOccurs="0" name="ContactName" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="Email" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="FaxNumber" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="Other" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="PhoneNumber" nillable="true" type="xs:string"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="Contact" nillable="true" type="tns:Contact"/>
    <xs:complexType name="NotifyErrorBase">
        <xs:sequence/>
    </xs:complexType>
    <xs:element name="NotifyErrorBase" nillable="true" type="tns:NotifyErrorBase"/>
    <xs:complexType name="Address">
        <xs:complexContent mixed="false">
            <xs:extension base="tns:NotifyErrorBase">
                <xs:sequence>
                    <xs:element minOccurs="0" name="AddressLine" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="City" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="Country" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="County" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="PostalCode" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="State" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="Suite" nillable="true" type="xs:string"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="Address" nillable="true" type="tns:Address"/>
    <xs:complexType name="ArrayOfImpoundLotVehicleCount">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="ImpoundLotVehicleCount" nillable="true" type="tns:ImpoundLotVehicleCount"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfImpoundLotVehicleCount" nillable="true" type="tns:ArrayOfImpoundLotVehicleCount"/>
    <xs:complexType name="ImpoundLotVehicleCount">
        <xs:sequence>
            <xs:element minOccurs="0" name="ImpoundLotId" type="xs:int"/>
            <xs:element minOccurs="0" name="Name" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="VehicleCount" type="xs:int"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ImpoundLotVehicleCount" nillable="true" type="tns:ImpoundLotVehicleCount"/>
    <xs:complexType name="ArrayOfDictionaryEntry">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="DictionaryEntry" nillable="true" type="tns:DictionaryEntry"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfDictionaryEntry" nillable="true" type="tns:ArrayOfDictionaryEntry"/>
    <xs:complexType name="DictionaryEntry">
        <xs:sequence>
            <xs:element minOccurs="0" name="Code" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="Description" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="Id" type="xs:int"/>
            <xs:element minOccurs="0" name="IntValue" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="IsDeleted" type="xs:boolean"/>
            <xs:element minOccurs="0" name="ParentId" type="xs:int"/>
            <xs:element minOccurs="0" name="Text" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="Value" nillable="true" type="xs:decimal"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="DictionaryEntry" nillable="true" type="tns:DictionaryEntry"/>
    <xs:simpleType name="JobStatus">
        <xs:annotation>
            <xs:appinfo>
                <ActualType Name="short" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/"/>
            </xs:appinfo>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="Unknown"/>
            <xs:enumeration value="Waiting"/>
            <xs:enumeration value="Assigned"/>
            <xs:enumeration value="Holding"/>
            <xs:enumeration value="Canceled"/>
            <xs:enumeration value="Finished"/>
            <xs:enumeration value="Impound"/>
            <xs:enumeration value="Offer"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:element name="JobStatus" nillable="true" type="tns:JobStatus"/>
    <xs:simpleType name="AssignedStatus">
        <xs:annotation>
            <xs:appinfo>
                <ActualType Name="short" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/"/>
            </xs:appinfo>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="Unknown"/>
            <xs:enumeration value="New"/>
            <xs:enumeration value="Dispatched"/>
            <xs:enumeration value="Confirmed"/>
            <xs:enumeration value="Arrived"/>
            <xs:enumeration value="Departed"/>
            <xs:enumeration value="DestinationArrived"/>
            <xs:enumeration value="UnconfirmedWarning"/>
            <xs:enumeration value="WaitWarningDispatched"/>
            <xs:enumeration value="WaitWarningNotDispatched"/>
            <xs:enumeration value="EnRoute"/>
            <xs:enumeration value="PendingFinish"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:element name="AssignedStatus" nillable="true" type="tns:AssignedStatus"/>
    <xs:simpleType name="OfferStatus">
        <xs:annotation>
            <xs:appinfo>
                <ActualType Name="unsignedByte" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/"/>
            </xs:appinfo>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="NewOffer"/>
            <xs:enumeration value="AcceptedByServiceProvider"/>
            <xs:enumeration value="RejectedByServiceProvider"/>
            <xs:enumeration value="PhoneCallRequested"/>
            <xs:enumeration value="PhoneCallAutoRequested"/>
            <xs:enumeration value="ApprovedByMotorClub"/>
            <xs:enumeration value="RejectedByMotorClub"/>
            <xs:enumeration value="CanceledByMotorClub"/>
            <xs:enumeration value="Finished"/>
            <xs:enumeration value="TimedOut"/>
            <xs:enumeration value="GoneOnArrival">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">20</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="CancelledByServiceProvider">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">21</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="EtaExtended">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">22</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="GoaRequested">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">23</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="SpCancelRequested">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">24</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="ExtendEtaRequested">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">25</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="GoneOnArrivalDenied">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">38</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="ServiceProviderCancelDenied">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">39</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="ExtendEtaDenied">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">40</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="AdditionalServiceRequested">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">43</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="AdditionalServiceApproved">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">44</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="AdditionalServiceDenied">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">45</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="InfoRequested">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">46</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="InfoAnswered">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">47</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="FinishFailed">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">48</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>
    <xs:element name="OfferStatus" nillable="true" type="tns:OfferStatus"/>
    <xs:simpleType name="ForcedStatus">
        <xs:annotation>
            <xs:appinfo>
                <ActualType Name="short" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/"/>
            </xs:appinfo>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="None"/>
            <xs:enumeration value="Waiting"/>
            <xs:enumeration value="Holding"/>
            <xs:enumeration value="Appointment"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:element name="ForcedStatus" nillable="true" type="tns:ForcedStatus"/>
    <xs:complexType name="ClientInfo">
        <xs:sequence>
            <xs:element minOccurs="0" name="ApplicationName" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="ApplicationVersion" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="ClientIP" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="ClientType" type="tns:ClientType"/>
            <xs:element minOccurs="0" name="Platform" type="tns:Platform"/>
            <xs:element minOccurs="0" name="PlatformVersion" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="Token" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ClientInfo" nillable="true" type="tns:ClientInfo"/>
    <xs:simpleType name="ClientType">
        <xs:annotation>
            <xs:appinfo>
                <ActualType Name="unsignedByte" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/"/>
            </xs:appinfo>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="Unknown"/>
            <xs:enumeration value="Browser"/>
            <xs:enumeration value="App"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:element name="ClientType" nillable="true" type="tns:ClientType"/>
    <xs:simpleType name="Platform">
        <xs:annotation>
            <xs:appinfo>
                <ActualType Name="unsignedByte" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/"/>
            </xs:appinfo>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="Unknown"/>
            <xs:enumeration value="Windows"/>
            <xs:enumeration value="MacOSX"/>
            <xs:enumeration value="Android"/>
            <xs:enumeration value="iOS"/>
            <xs:enumeration value="WindowsPhone"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:element name="Platform" nillable="true" type="tns:Platform"/>
    <xs:complexType name="ArrayOfTowSpecVehiclePreview">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="TowSpecVehiclePreview" nillable="true" type="tns:TowSpecVehiclePreview"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfTowSpecVehiclePreview" nillable="true" type="tns:ArrayOfTowSpecVehiclePreview"/>
    <xs:complexType name="TowSpecVehiclePreview">
        <xs:sequence>
            <xs:element minOccurs="0" name="DriveType" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="FromYear" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="Id" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="Make" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="Model" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="Style" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="ToYear" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="TowSpecVehiclePreview" nillable="true" type="tns:TowSpecVehiclePreview"/>
    <xs:complexType name="Auction">
        <xs:sequence>
            <xs:element minOccurs="0" name="Address" nillable="true" type="tns:Address"/>
            <xs:element minOccurs="0" name="AuctionDate" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="CompanyId" type="xs:int"/>
            <xs:element minOccurs="0" name="Id" type="xs:int"/>
            <xs:element minOccurs="0" name="IsDeleted" type="xs:boolean"/>
            <xs:element minOccurs="0" name="Name" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="Phone" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="TowLienLocationId" nillable="true" type="xs:long"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="Auction" nillable="true" type="tns:Auction"/>
    <xs:complexType name="ArrayOfAuction">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="Auction" nillable="true" type="tns:Auction"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfAuction" nillable="true" type="tns:ArrayOfAuction"/>
    <xs:complexType name="JobPhotoRequest">
        <xs:sequence>
            <xs:element minOccurs="0" name="ExtractExifData" type="xs:boolean"/>
            <xs:element minOccurs="0" name="ThumbnailSize" type="xs:int"/>
            <xs:element minOccurs="0" name="WorkOrdDtId" type="xs:int"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="JobPhotoRequest" nillable="true" type="tns:JobPhotoRequest"/>
    <xs:complexType name="ArrayOfDAPhotoInfo">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="DAPhotoInfo" nillable="true" type="tns:DAPhotoInfo"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfDAPhotoInfo" nillable="true" type="tns:ArrayOfDAPhotoInfo"/>
    <xs:complexType name="DAPhotoInfo">
        <xs:sequence>
            <xs:element minOccurs="0" name="LocationInfo" nillable="true" type="tns:DAPhotoLocationInfo"/>
            <xs:element minOccurs="0" name="Metadata" nillable="true" type="tns:DAPhotoMetadata"/>
            <xs:element minOccurs="0" name="Name" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="DAPhotoInfo" nillable="true" type="tns:DAPhotoInfo"/>
    <xs:complexType name="DAPhotoLocationInfo">
        <xs:sequence>
            <xs:element minOccurs="0" name="BrowserScaledUrl" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="DownloadUrl" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="ThumbUrl" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="Url" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="DAPhotoLocationInfo" nillable="true" type="tns:DAPhotoLocationInfo"/>
    <xs:complexType name="DAPhotoMetadata">
        <xs:sequence>
            <xs:element minOccurs="0" name="ContentType" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="ExifData" nillable="true" type="tns:ExifData"/>
            <xs:element minOccurs="0" name="ImageHeight" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="ImageWidth" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="IsPrivate" type="xs:boolean"/>
            <xs:element minOccurs="0" name="Size" type="xs:int"/>
            <xs:element minOccurs="0" name="UploadDateUTC" type="xs:dateTime"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="DAPhotoMetadata" nillable="true" type="tns:DAPhotoMetadata"/>
    <xs:complexType name="ExifData">
        <xs:sequence>
            <xs:element minOccurs="0" name="CameraMake" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="CameraModel" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="DateTaken" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="Latitude" nillable="true" type="xs:decimal"/>
            <xs:element minOccurs="0" name="Longitude" nillable="true" type="xs:decimal"/>
            <xs:element minOccurs="0" name="Orientation" type="tns:ExifOrientation"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ExifData" nillable="true" type="tns:ExifData"/>
    <xs:simpleType name="ExifOrientation">
        <xs:annotation>
            <xs:appinfo>
                <ActualType Name="unsignedShort" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/"/>
            </xs:appinfo>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="Unknown"/>
            <xs:enumeration value="TopLeft"/>
            <xs:enumeration value="TopRight"/>
            <xs:enumeration value="BottomRight"/>
            <xs:enumeration value="BottomLeft"/>
            <xs:enumeration value="LeftTop"/>
            <xs:enumeration value="RightTop"/>
            <xs:enumeration value="RightBottom"/>
            <xs:enumeration value="LeftBottom"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:element name="ExifOrientation" nillable="true" type="tns:ExifOrientation"/>
    <xs:complexType name="SendJobPhotosEmailRequest">
        <xs:sequence>
            <xs:element minOccurs="0" name="Message" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="ToEmail" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="WorkOrdDtId" type="xs:int"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="SendJobPhotosEmailRequest" nillable="true" type="tns:SendJobPhotosEmailRequest"/>
    <xs:complexType name="GetSecurePhotoUploadUrlRequest">
        <xs:sequence>
            <xs:element minOccurs="0" name="FileName" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="WorkOrdId" type="xs:int"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="GetSecurePhotoUploadUrlRequest" nillable="true" type="tns:GetSecurePhotoUploadUrlRequest"/>
    <xs:complexType name="SendReportEmailRequest">
        <xs:sequence>
            <xs:element minOccurs="0" name="EmailTo" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="Message" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="ReportInstance" nillable="true" type="tns:ReportInstance"/>
            <xs:element minOccurs="0" name="Subject" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="SendReportEmailRequest" nillable="true" type="tns:SendReportEmailRequest"/>
    <xs:complexType name="ReportInstance">
        <xs:sequence>
            <xs:element minOccurs="0" name="ClassName" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="Name" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="ReportParamaters" nillable="true" type="q2:ArrayOfKeyValueOfstringanyType" xmlns:q2="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ReportInstance" nillable="true" type="tns:ReportInstance"/>
    <xs:complexType name="ReportSender">
        <xs:sequence>
            <xs:element minOccurs="0" name="Name" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="ReplyTo" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ReportSender" nillable="true" type="tns:ReportSender"/>
    <xs:complexType name="SendReportFaxRequest">
        <xs:sequence>
            <xs:element minOccurs="0" name="AccountName" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="FaxNumber" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="RecipientName" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="ReportInstance" nillable="true" type="tns:ReportInstance"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="SendReportFaxRequest" nillable="true" type="tns:SendReportFaxRequest"/>
    <xs:complexType name="ArrayOfLogJobPhotoRequest">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="LogJobPhotoRequest" nillable="true" type="tns:LogJobPhotoRequest"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfLogJobPhotoRequest" nillable="true" type="tns:ArrayOfLogJobPhotoRequest"/>
    <xs:complexType name="LogJobPhotoRequest">
        <xs:sequence>
            <xs:element minOccurs="0" name="PhotoId" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="PhotoName" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="UTCUploadDate" nillable="true" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="UserId" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="WorkOrdDtId" type="xs:int"/>
            <xs:element minOccurs="0" name="WorkOrdId" nillable="true" type="xs:int"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="LogJobPhotoRequest" nillable="true" type="tns:LogJobPhotoRequest"/>
    <xs:simpleType name="TowMagicResponseType">
        <xs:annotation>
            <xs:appinfo>
                <ActualType Name="short" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/"/>
            </xs:appinfo>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="Accepted"/>
            <xs:enumeration value="Rejected"/>
            <xs:enumeration value="PhoneCallRequested"/>
            <xs:enumeration value="Finished"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:element name="TowMagicResponseType" nillable="true" type="tns:TowMagicResponseType"/>
    <xs:complexType name="GeoPoint">
        <xs:sequence>
            <xs:element minOccurs="0" name="Latitude" nillable="true" type="xs:float"/>
            <xs:element minOccurs="0" name="Longitude" nillable="true" type="xs:float"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="GeoPoint" nillable="true" type="tns:GeoPoint"/>
    <xs:simpleType name="TowMagicJobUpdateType">
        <xs:annotation>
            <xs:appinfo>
                <ActualType Name="short" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/"/>
            </xs:appinfo>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="ExtendEtaRequest">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">9</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="GoaRequest">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">10</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="ServiceProviderCancelRequest">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">11</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="AdditionalServicesRequest">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">17</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>
    <xs:element name="TowMagicJobUpdateType" nillable="true" type="tns:TowMagicJobUpdateType"/>
    <xs:complexType name="ServiceMappings">
        <xs:sequence>
            <xs:element minOccurs="0" name="ExcludedTruckCapacities" nillable="true" type="q3:ArrayOfstring" xmlns:q3="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
            <xs:element minOccurs="0" name="FallbackExact" type="xs:boolean"/>
            <xs:element minOccurs="0" name="ModifierKeywords" nillable="true" type="q4:ArrayOfstring" xmlns:q4="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
            <xs:element minOccurs="0" name="MotorClubServiceTypes" nillable="true" type="tns:ArrayOfMotorClubServiceType"/>
            <xs:element minOccurs="0" name="PreferExact" type="xs:boolean"/>
            <xs:element minOccurs="0" name="TruckCapacity" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ServiceMappings" nillable="true" type="tns:ServiceMappings"/>
    <xs:complexType name="ArrayOfMotorClubServiceType">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="MotorClubServiceType" nillable="true" type="tns:MotorClubServiceType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfMotorClubServiceType" nillable="true" type="tns:ArrayOfMotorClubServiceType"/>
    <xs:complexType name="MotorClubServiceType">
        <xs:sequence>
            <xs:element minOccurs="0" name="PreferredNames" nillable="true" type="q5:ArrayOfstring" xmlns:q5="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
            <xs:element minOccurs="0" name="ServiceType" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="MotorClubServiceType" nillable="true" type="tns:MotorClubServiceType"/>
    <xs:complexType name="ChangelogArticleContent">
        <xs:sequence>
            <xs:element minOccurs="0" name="Body" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="HasArticle" type="xs:boolean"/>
            <xs:element minOccurs="0" name="Title" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ChangelogArticleContent" nillable="true" type="tns:ChangelogArticleContent"/>
    <xs:complexType name="ArrayOfBasicBSCompany">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="BasicBSCompany" nillable="true" type="tns:BasicBSCompany"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfBasicBSCompany" nillable="true" type="tns:ArrayOfBasicBSCompany"/>
    <xs:complexType name="BasicBSCompany">
        <xs:sequence>
            <xs:element minOccurs="0" name="Address" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="BSCompanyId" type="xs:int"/>
            <xs:element minOccurs="0" name="CanAcceptTM4Jobs" type="xs:boolean"/>
            <xs:element minOccurs="0" name="City" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="Country" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="Email" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="Fax" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="IsRoadside" type="xs:boolean"/>
            <xs:element minOccurs="0" name="Name" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="OriginalStartDate" nillable="true" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="Phone" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="State" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="Zip" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="BasicBSCompany" nillable="true" type="tns:BasicBSCompany"/>
    <xs:complexType name="BasicDAUser">
        <xs:complexContent mixed="false">
            <xs:extension base="tns:UserBase">
                <xs:sequence>
                    <xs:element minOccurs="0" name="ChatOpenedOn" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="ClientType" type="tns:ClientType"/>
                    <xs:element minOccurs="0" name="DepartmentId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="FirstName" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="HasUnreadMessages" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="IsConnected" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="IsDeleted" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="LastName" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="LastVersionNotes" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="MiddleName" nillable="true" type="xs:string"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="BasicDAUser" nillable="true" type="tns:BasicDAUser"/>
    <xs:complexType name="UserBase">
        <xs:sequence>
            <xs:element minOccurs="0" name="BSCompanyId" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="CompanyId" type="xs:int"/>
            <xs:element minOccurs="0" name="DivisionId" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="HasReadOnlyAccess" type="xs:boolean"/>
            <xs:element minOccurs="0" name="Id" type="xs:int"/>
            <xs:element minOccurs="0" name="LinkedDriverId" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="RoleIds" nillable="true" type="q6:ArrayOfint" xmlns:q6="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
            <xs:element minOccurs="0" name="UserName" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="UserType" type="tns:UserType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="UserBase" nillable="true" type="tns:UserBase"/>
    <xs:simpleType name="UserType">
        <xs:annotation>
            <xs:appinfo>
                <ActualType Name="short" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/"/>
            </xs:appinfo>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="Standard"/>
            <xs:enumeration value="Super"/>
            <xs:enumeration value="Driver"/>
            <xs:enumeration value="Admin"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:element name="UserType" nillable="true" type="tns:UserType"/>
    <xs:complexType name="ArrayOfBasicDAUser">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="BasicDAUser" nillable="true" type="tns:BasicDAUser"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfBasicDAUser" nillable="true" type="tns:ArrayOfBasicDAUser"/>
    <xs:complexType name="Company">
        <xs:complexContent mixed="false">
            <xs:extension base="tns:CompanyOptions">
                <xs:sequence>
                    <xs:element minOccurs="0" name="Address" nillable="true" type="tns:CompanyAddress"/>
                    <xs:element minOccurs="0" name="BSCompanyId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="Contact" nillable="true" type="tns:CompanyContact"/>
                    <xs:element minOccurs="0" name="DriverOptions" type="tns:DriverOptions"/>
                    <xs:element minOccurs="0" name="HasLogoGraphic" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="QuickBooksAuthCredentials" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="QuickBooksVersion" type="tns:QuickbookVersion"/>
                    <xs:element minOccurs="0" name="ShopManagerEmail" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="StateLicense" nillable="true" type="xs:string"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="Company" nillable="true" type="tns:Company"/>
    <xs:complexType name="CompanyOptions">
        <xs:sequence>
            <xs:element minOccurs="0" name="Id" type="xs:int"/>
            <xs:element minOccurs="0" name="LicenseType" type="tns:LicenseType"/>
            <xs:element minOccurs="0" name="Name" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="PricingOptions" type="tns:PricingOptions"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="CompanyOptions" nillable="true" type="tns:CompanyOptions"/>
    <xs:simpleType name="LicenseType">
        <xs:annotation>
            <xs:appinfo>
                <ActualType Name="short" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/"/>
            </xs:appinfo>
        </xs:annotation>
        <xs:list>
            <xs:simpleType>
                <xs:restriction base="xs:string">
                    <xs:enumeration value="Unknown">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">0</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="Starter">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">1</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="Standard">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">2</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="Premium">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">4</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="MappingPlus">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">8</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="PerformancePack">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">16</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="MotorClubBilling">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">32</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                </xs:restriction>
            </xs:simpleType>
        </xs:list>
    </xs:simpleType>
    <xs:element name="LicenseType" nillable="true" type="tns:LicenseType"/>
    <xs:simpleType name="PricingOptions">
        <xs:list>
            <xs:simpleType>
                <xs:restriction base="xs:string">
                    <xs:enumeration value="Unknown">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">0</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="UseZoneAdjustment">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">1</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="UseTimeAdjustment">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">2</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="UsePaymentMethodAdjustment">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">4</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                </xs:restriction>
            </xs:simpleType>
        </xs:list>
    </xs:simpleType>
    <xs:element name="PricingOptions" nillable="true" type="tns:PricingOptions"/>
    <xs:complexType name="CompanyAddress">
        <xs:complexContent mixed="false">
            <xs:extension base="tns:Address">
                <xs:sequence>
                    <xs:element minOccurs="0" name="GeoPoint" nillable="true" type="tns:GeoPoint"/>
                    <xs:element minOccurs="0" name="StateDictionaryId" nillable="true" type="xs:int"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="CompanyAddress" nillable="true" type="tns:CompanyAddress"/>
    <xs:complexType name="CompanyContact">
        <xs:sequence>
            <xs:element minOccurs="0" name="Email" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="FaxNumber" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="Name" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="PhoneNumber" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="CompanyContact" nillable="true" type="tns:CompanyContact"/>
    <xs:simpleType name="DriverOptions">
        <xs:list>
            <xs:simpleType>
                <xs:restriction base="xs:string">
                    <xs:enumeration value="Unknown">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">0</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="EnableQRScanAssign">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">1</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="EnableDriverChecklist">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">2</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="DisableRequireGPS">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">4</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="DisableAutoTimestamp">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">8</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                </xs:restriction>
            </xs:simpleType>
        </xs:list>
    </xs:simpleType>
    <xs:element name="DriverOptions" nillable="true" type="tns:DriverOptions"/>
    <xs:simpleType name="QuickbookVersion">
        <xs:restriction base="xs:string">
            <xs:enumeration value="None"/>
            <xs:enumeration value="Desktop"/>
            <xs:enumeration value="Online"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:element name="QuickbookVersion" nillable="true" type="tns:QuickbookVersion"/>
    <xs:complexType name="Division">
        <xs:complexContent mixed="false">
            <xs:extension base="tns:BasicDivision">
                <xs:sequence>
                    <xs:element minOccurs="0" name="AutoPostOnPayment" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="Contact" nillable="true" type="tns:CompanyContact"/>
                    <xs:element minOccurs="0" name="CreditCardCapturePoint" nillable="true" type="tns:CreditCardCapturePoint"/>
                    <xs:element minOccurs="0" name="DefaultPreAuthAmount" nillable="true" type="xs:decimal"/>
                    <xs:element minOccurs="0" name="DriverHistoryEditLimit" type="xs:short"/>
                    <xs:element minOccurs="0" name="DriverHistoryLimit" type="xs:short"/>
                    <xs:element minOccurs="0" name="DriverResendInterval" nillable="true" type="xs:short"/>
                    <xs:element minOccurs="0" name="HAASAccountId" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="InvoiceLegalStatement" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="InvoiceMessage" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="InvoiceNumberType" type="tns:InvoiceNumberType"/>
                    <xs:element minOccurs="0" name="LogoName" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="MapScale" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="PageMethod" nillable="true" type="xs:short"/>
                    <xs:element minOccurs="0" name="ScheduledToWait" nillable="true" type="xs:short"/>
                    <xs:element minOccurs="0" name="StandardTimeZoneBias" nillable="true" type="xs:short"/>
                    <xs:element minOccurs="0" name="StateLicense" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="TowTicketMessage" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="UseDaylightSaving" type="xs:boolean"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="Division" nillable="true" type="tns:Division"/>
    <xs:complexType name="BasicDivision">
        <xs:sequence>
            <xs:element minOccurs="0" name="Address" nillable="true" type="tns:CompanyAddress"/>
            <xs:element minOccurs="0" name="CompanyId" type="xs:int"/>
            <xs:element minOccurs="0" name="CurrentETA" nillable="true" type="xs:short"/>
            <xs:element minOccurs="0" name="DispatchAssignMode" type="tns:DispatchAssignModeType"/>
            <xs:element minOccurs="0" name="DistanceCalculationType" type="tns:DistanceCalculationType"/>
            <xs:element minOccurs="0" name="DistanceUnit" type="tns:DistanceUnitType"/>
            <xs:element minOccurs="0" name="DivisionOptions" type="tns:DivisionOptions"/>
            <xs:element minOccurs="0" name="Id" type="xs:int"/>
            <xs:element minOccurs="0" name="JobWarnings" nillable="true" type="tns:JobWarnings"/>
            <xs:element minOccurs="0" name="Name" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="PasswordRequiredOnPayment" type="xs:boolean"/>
            <xs:element minOccurs="0" name="VerifyVINOnRelease" type="xs:boolean"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="BasicDivision" nillable="true" type="tns:BasicDivision"/>
    <xs:simpleType name="DispatchAssignModeType">
        <xs:annotation>
            <xs:appinfo>
                <ActualType Name="unsignedByte" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/"/>
            </xs:appinfo>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="Unknown"/>
            <xs:enumeration value="RoundRobin"/>
            <xs:enumeration value="LongestIdle"/>
            <xs:enumeration value="BestETAUnassignedPreferred"/>
            <xs:enumeration value="BestETAStrict"/>
            <xs:enumeration value="FuelEfficient"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:element name="DispatchAssignModeType" nillable="true" type="tns:DispatchAssignModeType"/>
    <xs:simpleType name="DistanceCalculationType">
        <xs:annotation>
            <xs:appinfo>
                <ActualType Name="unsignedByte" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/"/>
            </xs:appinfo>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="StraightLineDistance"/>
            <xs:enumeration value="DrivingDistance"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:element name="DistanceCalculationType" nillable="true" type="tns:DistanceCalculationType"/>
    <xs:simpleType name="DistanceUnitType">
        <xs:annotation>
            <xs:appinfo>
                <ActualType Name="unsignedByte" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/"/>
            </xs:appinfo>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="Miles"/>
            <xs:enumeration value="Kilometers"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:element name="DistanceUnitType" nillable="true" type="tns:DistanceUnitType"/>
    <xs:simpleType name="DivisionOptions">
        <xs:annotation>
            <xs:appinfo>
                <ActualType Name="long" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/"/>
            </xs:appinfo>
        </xs:annotation>
        <xs:list>
            <xs:simpleType>
                <xs:restriction base="xs:string">
                    <xs:enumeration value="Unknown">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">0</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="DriverTakePaymentEnabled">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">1</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="ShowInvoiceAfterTakePayment">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">2</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="ShowReceiptAfterTakePayment">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">4</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="DriverAllowServiceEdit">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">8</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="DriverAllowServiceViewPricing">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">16</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="DriverAllowServiceEditPricing">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">32</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="DriverPromptForKeysOnDestArrive">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">64</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="DisableGeocodeOnJobSave">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">128</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="AutoSelectHighConfidenceGeocodeResults">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">256</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="AutoCorrectJobAddressOnGeocode">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">512</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="AllowReleaseOnPartialPayment">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">1024</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="DriverAllowCreateJob">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">2048</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="DriverAllowServiceDelete">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">4096</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="DriverRequiredToUploadPhoto">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">8192</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="DriverPromptForTowTicketSignature">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">16384</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="AutoPostOnFinish">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">32768</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="DisplayLegacyLienTab">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">65536</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="DisableRoadAlertOnCreate">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">131072</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                </xs:restriction>
            </xs:simpleType>
        </xs:list>
    </xs:simpleType>
    <xs:element name="DivisionOptions" nillable="true" type="tns:DivisionOptions"/>
    <xs:complexType name="JobWarnings">
        <xs:sequence>
            <xs:element minOccurs="0" name="UnconfirmedWarning" nillable="true" type="xs:short"/>
            <xs:element minOccurs="0" name="WaitWarning" nillable="true" type="xs:short"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="JobWarnings" nillable="true" type="tns:JobWarnings"/>
    <xs:simpleType name="CreditCardCapturePoint">
        <xs:annotation>
            <xs:appinfo>
                <ActualType Name="unsignedByte" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/"/>
            </xs:appinfo>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="OnJobFinish"/>
            <xs:enumeration value="OnJobPost"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:element name="CreditCardCapturePoint" nillable="true" type="tns:CreditCardCapturePoint"/>
    <xs:simpleType name="InvoiceNumberType">
        <xs:annotation>
            <xs:appinfo>
                <ActualType Name="unsignedByte" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/"/>
            </xs:appinfo>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="Unknown"/>
            <xs:enumeration value="JobNumber"/>
            <xs:enumeration value="TicketNumber"/>
            <xs:enumeration value="AutoNumber"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:element name="InvoiceNumberType" nillable="true" type="tns:InvoiceNumberType"/>
    <xs:complexType name="ArrayOfBasicDivision">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="BasicDivision" nillable="true" type="tns:BasicDivision"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfBasicDivision" nillable="true" type="tns:ArrayOfBasicDivision"/>
    <xs:complexType name="ArrayOfDivision">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="Division" nillable="true" type="tns:Division"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfDivision" nillable="true" type="tns:ArrayOfDivision"/>
    <xs:complexType name="ArrayOfDivisionAccess">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="DivisionAccess" nillable="true" type="tns:DivisionAccess"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfDivisionAccess" nillable="true" type="tns:ArrayOfDivisionAccess"/>
    <xs:complexType name="DivisionAccess">
        <xs:sequence>
            <xs:element minOccurs="0" name="BSCompanyId" type="xs:int"/>
            <xs:element minOccurs="0" name="CompanyId" type="xs:int"/>
            <xs:element minOccurs="0" name="DivisionId" type="xs:int"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="DivisionAccess" nillable="true" type="tns:DivisionAccess"/>
    <xs:complexType name="ArrayOfDriverField">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="DriverField" nillable="true" type="tns:DriverField"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfDriverField" nillable="true" type="tns:ArrayOfDriverField"/>
    <xs:complexType name="DriverField">
        <xs:sequence>
            <xs:element minOccurs="0" name="CTFieldName" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="CompanyId" type="xs:int"/>
            <xs:element minOccurs="0" name="EmailLabel" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="Id" type="xs:int"/>
            <xs:element minOccurs="0" name="Order" type="xs:unsignedByte"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="DriverField" nillable="true" type="tns:DriverField"/>
    <xs:complexType name="Account">
        <xs:complexContent mixed="false">
            <xs:extension base="tns:BasicAccount">
                <xs:sequence>
                    <xs:element minOccurs="0" name="AccountDiscountMultiplier" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="AccountOptions" type="tns:AccountOptions"/>
                    <xs:element minOccurs="0" name="AccountRatingId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="AutoBillOnPost" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="AutoReconcile" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="BankAccountNumber" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="BillingCycleId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="BillingExportMethodId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="BillingFaxNumber" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="BillingLocation" nillable="true" type="tns:CompanyAddress"/>
                    <xs:element minOccurs="0" name="BillingMCCreds" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="BillingMethodId" nillable="true" type="xs:unsignedByte"/>
                    <xs:element minOccurs="0" name="BillingMileLimit" nillable="true" type="xs:float"/>
                    <xs:element minOccurs="0" name="BillingPhoneNumber" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="BillingTaxId" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="BillingTermsCreditPeriod" nillable="true" type="xs:unsignedByte"/>
                    <xs:element minOccurs="0" name="CarFaxSharing" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="CreatedDate" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="CreditLimit" nillable="true" type="xs:decimal"/>
                    <xs:element minOccurs="0" name="DefaultETA" nillable="true" type="xs:short"/>
                    <xs:element minOccurs="0" name="DefaultServiceDescriptionIds" nillable="true" type="q7:ArrayOfint" xmlns:q7="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
                    <xs:element minOccurs="0" name="DisableSendingInvoicesToFleetnet" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="GeneralRemarksRTF" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="InvoiceCron" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="InvoiceMessage" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="InvoiceSchedule" type="tns:BillingSendSchedule"/>
                    <xs:element minOccurs="0" name="InvoiceSendMethod" type="tns:BillingSendMethod"/>
                    <xs:element minOccurs="0" name="InvoiceStyleId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="IsHiddenFromAccountsReceivable" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="LastBillingDate" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="Number" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="PrimaryContact" nillable="true" type="tns:CompanyContact"/>
                    <xs:element minOccurs="0" name="QBAccountName" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="RemarksRTF" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="SecondaryContact" nillable="true" type="tns:CompanyContact"/>
                    <xs:element minOccurs="0" name="ServiceRateCode" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="ShowAdjustedPriceOnCallEntry" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="ShowAdjustedPriceOnInvoice" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="ShowAdjustedPriceOnStatement" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="ShowPartialPaymentsOnInvoice" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="StatementCron" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="StatementSchedule" type="tns:BillingSendSchedule"/>
                    <xs:element minOccurs="0" name="StatementSendMethod" type="tns:BillingSendMethod"/>
                    <xs:element minOccurs="0" name="StatementStyleId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="TowTicketMessage" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="UserDefinedCharacter1" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="UserDefinedCharacter2" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="UserDefinedDate" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="UserDefinedNumeric1" nillable="true" type="xs:decimal"/>
                    <xs:element minOccurs="0" name="UserDefinedNumeric2" nillable="true" type="xs:decimal"/>
                    <xs:element minOccurs="0" name="WebAccessPassword" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="WebSiteUrl" nillable="true" type="xs:string"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="Account" nillable="true" type="tns:Account"/>
    <xs:complexType name="BasicAccount">
        <xs:sequence>
            <xs:element minOccurs="0" name="AccountTypeId" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="Address" nillable="true" type="tns:CompanyAddress"/>
            <xs:element minOccurs="0" name="BillingEmailAddress" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="BillingMCKey" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="CompanyId" type="xs:int"/>
            <xs:element minOccurs="0" name="DivisionId" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="Id" type="xs:int"/>
            <xs:element minOccurs="0" name="IsDeleted" type="xs:boolean"/>
            <xs:element minOccurs="0" name="IsInactive" type="xs:boolean"/>
            <xs:element minOccurs="0" name="Name" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="BasicAccount" nillable="true" type="tns:BasicAccount"/>
    <xs:simpleType name="AccountOptions">
        <xs:list>
            <xs:simpleType>
                <xs:restriction base="xs:string">
                    <xs:enumeration value="None">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">0</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="HidePricingOnTowTicket">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">1</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="ShowPhotosURLOnInvoice">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">2</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="SendNotificationsToContactPhone">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">4</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="SendNotificationsToOwnerPhone">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">8</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="SendNotificationsToOwnerEmail">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">16</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="SendSurveyOnFinish">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">32</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="SendToTowLien">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">64</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="DisableNotificationsOnAssigned">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">128</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="DisableNotificationsOnEnroute">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">256</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="DisableNotificationsOnArrived">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">512</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="SendDriverFinishToPendingFinish">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">1024</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="DisableAutoAddServices">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">2048</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="DisableAutoAddUnloadedMileage">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">4096</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="DisableAutoAddLoadedMileage">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">8192</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="PromptForTowTicketSignature">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">16384</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="ShowPartialPaymentsOnInvoice">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">32768</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                </xs:restriction>
            </xs:simpleType>
        </xs:list>
    </xs:simpleType>
    <xs:element name="AccountOptions" nillable="true" type="tns:AccountOptions"/>
    <xs:simpleType name="BillingSendSchedule">
        <xs:annotation>
            <xs:appinfo>
                <ActualType Name="unsignedByte" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/"/>
            </xs:appinfo>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="None"/>
            <xs:enumeration value="Manual"/>
            <xs:enumeration value="Daily"/>
            <xs:enumeration value="Weekly"/>
            <xs:enumeration value="BiWeekly"/>
            <xs:enumeration value="Monthly"/>
            <xs:enumeration value="Custom"/>
            <xs:enumeration value="OnFinished"/>
            <xs:enumeration value="OnPosted"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:element name="BillingSendSchedule" nillable="true" type="tns:BillingSendSchedule"/>
    <xs:simpleType name="BillingSendMethod">
        <xs:annotation>
            <xs:appinfo>
                <ActualType Name="unsignedByte" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/"/>
            </xs:appinfo>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="None"/>
            <xs:enumeration value="Email"/>
            <xs:enumeration value="Fax"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:element name="BillingSendMethod" nillable="true" type="tns:BillingSendMethod"/>
    <xs:complexType name="AccountInfo">
        <xs:sequence>
            <xs:element minOccurs="0" name="ETA" nillable="true" type="xs:short"/>
            <xs:element minOccurs="0" name="Name" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="PrimaryContact" nillable="true" type="tns:CompanyContact"/>
            <xs:element minOccurs="0" name="Remarks" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="SecondaryContact" nillable="true" type="tns:CompanyContact"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="AccountInfo" nillable="true" type="tns:AccountInfo"/>
    <xs:complexType name="ArrayOfBasicAccount">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="BasicAccount" nillable="true" type="tns:BasicAccount"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfBasicAccount" nillable="true" type="tns:ArrayOfBasicAccount"/>
    <xs:complexType name="ArrayOfAccount">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="Account" nillable="true" type="tns:Account"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfAccount" nillable="true" type="tns:ArrayOfAccount"/>
    <xs:complexType name="AvailableCredit">
        <xs:sequence>
            <xs:element minOccurs="0" name="Amount" type="xs:decimal"/>
            <xs:element minOccurs="0" name="CreditType" type="tns:AvailableCreditType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="AvailableCredit" nillable="true" type="tns:AvailableCredit"/>
    <xs:simpleType name="AvailableCreditType">
        <xs:annotation>
            <xs:appinfo>
                <ActualType Name="unsignedByte" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/"/>
            </xs:appinfo>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="Unknown"/>
            <xs:enumeration value="Unlimited"/>
            <xs:enumeration value="Fixed"/>
            <xs:enumeration value="OverLimit"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:element name="AvailableCreditType" nillable="true" type="tns:AvailableCreditType"/>
    <xs:complexType name="ArrayOfJobTemplateField">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="JobTemplateField" nillable="true" type="tns:JobTemplateField"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfJobTemplateField" nillable="true" type="tns:ArrayOfJobTemplateField"/>
    <xs:complexType name="JobTemplateField">
        <xs:complexContent mixed="false">
            <xs:extension base="tns:JobTemplateFieldLabel">
                <xs:sequence>
                    <xs:element minOccurs="0" name="AccountId" type="xs:int"/>
                    <xs:element minOccurs="0" name="CompanyId" type="xs:int"/>
                    <xs:element minOccurs="0" name="DefaultLookupId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="DefaultValue" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="Description" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="Id" type="xs:int"/>
                    <xs:element minOccurs="0" name="IsRequired" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="IsRequiredToFinish" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="IsRequiredToPost" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="IsVisible" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="Legacy_ContLeft" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="Legacy_ContName" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="Legacy_ContTabInd" nillable="true" type="xs:short"/>
                    <xs:element minOccurs="0" name="Legacy_ContTag" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="Legacy_ContTop" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="Legacy_FldLab_ID" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="Legacy_LabLeft" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="Legacy_LabName" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="Legacy_LabTag" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="Legacy_LabTop" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="Legacy_LabVisible" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="MaximumLength" type="xs:int"/>
                    <xs:element minOccurs="0" name="PropertyName" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="ShortDisplayName" nillable="true" type="xs:string"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="JobTemplateField" nillable="true" type="tns:JobTemplateField"/>
    <xs:complexType name="JobTemplateFieldLabel">
        <xs:sequence>
            <xs:element minOccurs="0" name="CTFieldName" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="DisplayName" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="WasFound" type="xs:boolean"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="JobTemplateFieldLabel" nillable="true" type="tns:JobTemplateFieldLabel"/>
    <xs:complexType name="Job">
        <xs:complexContent mixed="false">
            <xs:extension base="tns:NotifyErrorBase">
                <xs:sequence>
                    <xs:element minOccurs="0" name="AdditionalDriverIds" nillable="true" type="q8:ArrayOfint" xmlns:q8="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
                    <xs:element minOccurs="0" name="AllowedOfferActions" type="tns:AllowedOfferActions"/>
                    <xs:element minOccurs="0" name="ArrivedTime" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="AssignedDriverId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="AssignedTime" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="AssignedVehicleId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="AttachmentURL" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="AuctionId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="BarcodeGeoPoint" nillable="true" type="tns:GeoPoint"/>
                    <xs:element minOccurs="0" name="BillTo" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="BillToId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="BoostAmount" nillable="true" type="xs:decimal"/>
                    <xs:element minOccurs="0" name="BoostMultiplier" nillable="true" type="xs:decimal"/>
                    <xs:element minOccurs="0" name="CalculatedArriveTime" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="CallTakerId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="CancelReason" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="CancelledById" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="ClearCode" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="ClearCodeComments" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="ClearedTime" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="ClubCode" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="CoPayAmount" nillable="true" type="xs:decimal"/>
                    <xs:element minOccurs="0" name="CompanyId" type="xs:int"/>
                    <xs:element minOccurs="0" name="ConfirmedTime" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="Contact" nillable="true" type="tns:JobContact"/>
                    <xs:element minOccurs="0" name="ContractorNumber" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="CustomData" nillable="true" type="tns:CustomData"/>
                    <xs:element minOccurs="0" name="CustomDetailData" nillable="true" type="tns:CustomDetailData"/>
                    <xs:element minOccurs="0" name="DestinationArrivedTime" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="DestinationLocation" nillable="true" type="tns:DestinationLocation"/>
                    <xs:element minOccurs="0" name="DetailRemarks" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="DispatchAction" type="tns:DispatchAction"/>
                    <xs:element minOccurs="0" name="DispatchedTime" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="DispatcherId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="DivisionId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="DivisionName" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="DriverHistoryLimit" type="xs:short"/>
                    <xs:element minOccurs="0" name="EnRouteTime" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="EstimatedClearedTime" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="EstimatedPrice" nillable="true" type="xs:decimal"/>
                    <xs:element minOccurs="0" name="ForcedStatus" type="tns:ForcedStatus"/>
                    <xs:element minOccurs="0" name="HasMCApprovedServices" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="HasUnreadMessages" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="ImpRelWaiverSignedOn" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="ImportId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="ImportKey" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="ImpoundLotId" type="xs:int"/>
                    <xs:element minOccurs="0" name="ImpoundReleaseTime" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="IncidentDepartedTime" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="IncidentLocation" nillable="true" type="tns:IncidentLocation"/>
                    <xs:element minOccurs="0" name="IsAuctionUpdate" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="IsDataSharing" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="IsPoliceHold" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="JobFlags" type="tns:JobFlags"/>
                    <xs:element minOccurs="0" name="JobNumber" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="KeyDropOffSignedOn" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="LastPageTime" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="LastScannedTime" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="LastUnconfirmedWarningTime" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="LastWaitWarningTime" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="Lienholder" nillable="true" type="tns:Lienholder"/>
                    <xs:element minOccurs="0" name="LinkedBarcode" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="LinkedWorkOrdDtId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="MCPromoText" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="MCTasksJson" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="MemberLimitAmount" nillable="true" type="xs:decimal"/>
                    <xs:element minOccurs="0" name="MemberMileLimit" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="MemberNumber" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="MotorClubKey" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="MotorClubName" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="Narrative" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="NextInvoiceNumber" nillable="true" type="xs:unsignedByte"/>
                    <xs:element minOccurs="0" name="Notes" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="OfferAmount" nillable="true" type="xs:decimal"/>
                    <xs:element minOccurs="0" name="OfferStatus" type="tns:OfferStatus"/>
                    <xs:element minOccurs="0" name="OriginalEntryTime" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="Owner" nillable="true" type="tns:JobOwner"/>
                    <xs:element minOccurs="0" name="PaymentMethodId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="PendingFinishTime" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="PriorityId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="PurchaseOrder" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="Reason" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="ReceivedTime" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="ReleasedById" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="ReleasedTo" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="RequestSource" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="RequestedBy" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="RequestedById" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="RequiredTruckTypeId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="ScheduledMoveToWaitingTime" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="ScheduledTime" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="SearchText" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="SentToTowLienOn" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="Status" type="tns:JobStatus"/>
                    <xs:element minOccurs="0" name="TicketNumber" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="TimeStamps" nillable="true" type="tns:ArrayOfTimeStamp"/>
                    <xs:element minOccurs="0" name="TimeoutTime" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="TowLienId" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="TowLienStatus" type="tns:TowLienStatus"/>
                    <xs:element minOccurs="0" name="TowTicketSignedOn" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="TroubleCode" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="UnassignedDriverId" type="xs:int"/>
                    <xs:element minOccurs="0" name="UnassignedVehicleId" type="xs:int"/>
                    <xs:element minOccurs="0" name="Vehicle" nillable="true" type="tns:JobVehicle"/>
                    <xs:element minOccurs="0" name="VehicleDamageSignedOn" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="WasCustomerContacted" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="WorkOrdDtId" type="xs:int"/>
                    <xs:element minOccurs="0" name="WorkOrdId" type="xs:int"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="Job" nillable="true" type="tns:Job"/>
    <xs:simpleType name="AllowedOfferActions">
        <xs:annotation>
            <xs:appinfo>
                <ActualType Name="unsignedByte" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/"/>
            </xs:appinfo>
        </xs:annotation>
        <xs:list>
            <xs:simpleType>
                <xs:restriction base="xs:string">
                    <xs:enumeration value="Unknown">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">0</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="AutoRequestPhoneCall">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">1</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="ServiceProviderCancel">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">2</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="GoneOnArrivalRequest">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">4</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="ExtendEta">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">8</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="PhoneCall">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">16</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="AdditionalServices">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">32</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                </xs:restriction>
            </xs:simpleType>
        </xs:list>
    </xs:simpleType>
    <xs:element name="AllowedOfferActions" nillable="true" type="tns:AllowedOfferActions"/>
    <xs:complexType name="JobContact">
        <xs:complexContent mixed="false">
            <xs:extension base="tns:Contact">
                <xs:sequence>
                    <xs:element minOccurs="0" name="IsMobilePhone" nillable="true" type="xs:boolean"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="JobContact" nillable="true" type="tns:JobContact"/>
    <xs:complexType name="CustomData">
        <xs:complexContent mixed="false">
            <xs:extension base="tns:NotifyErrorBase">
                <xs:sequence>
                    <xs:element minOccurs="0" name="CustomBoolean" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="CustomDate" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="CustomDictionaryId1" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="CustomDictionaryId2" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="CustomNumeric1" nillable="true" type="xs:float"/>
                    <xs:element minOccurs="0" name="CustomNumeric2" nillable="true" type="xs:float"/>
                    <xs:element minOccurs="0" name="CustomText1" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="CustomText2" nillable="true" type="xs:string"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="CustomData" nillable="true" type="tns:CustomData"/>
    <xs:complexType name="CustomDetailData">
        <xs:complexContent mixed="false">
            <xs:extension base="tns:CustomData">
                <xs:sequence/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="CustomDetailData" nillable="true" type="tns:CustomDetailData"/>
    <xs:complexType name="DestinationLocation">
        <xs:complexContent mixed="false">
            <xs:extension base="tns:Location">
                <xs:sequence/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="DestinationLocation" nillable="true" type="tns:DestinationLocation"/>
    <xs:complexType name="Location">
        <xs:complexContent mixed="false">
            <xs:extension base="tns:Address">
                <xs:sequence>
                    <xs:element minOccurs="0" name="GeoPoint" nillable="true" type="tns:GeoPoint"/>
                    <xs:element minOccurs="0" name="IsConfirmed" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="Name" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="ZoneId" nillable="true" type="xs:int"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="Location" nillable="true" type="tns:Location"/>
    <xs:simpleType name="DispatchAction">
        <xs:annotation>
            <xs:appinfo>
                <ActualType Name="unsignedInt" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/"/>
            </xs:appinfo>
        </xs:annotation>
        <xs:list>
            <xs:simpleType>
                <xs:restriction base="xs:string">
                    <xs:enumeration value="None">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">0</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="Update">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">1</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="Create">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">2</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="Assign">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">4</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="Timestamp">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">8</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="Finish">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">16</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="Hold">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">32</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="Unassign">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">64</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="Cancel">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">128</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="MoveToWaiting">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">256</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="UndoFinish">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">512</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="ResendDriverNotifications">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">1024</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="UpdateCalculatedArriveTime">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">2048</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="ReleaseFromImpound">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">4096</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="ShowWaitWarning">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">8192</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="ShowUnconfirmedWarning">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">16384</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="OfferUpdated">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">32768</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="NewOffer">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">65536</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="NewOfferReminder">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">131072</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                </xs:restriction>
            </xs:simpleType>
        </xs:list>
    </xs:simpleType>
    <xs:element name="DispatchAction" nillable="true" type="tns:DispatchAction"/>
    <xs:complexType name="IncidentLocation">
        <xs:complexContent mixed="false">
            <xs:extension base="tns:Location">
                <xs:sequence/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="IncidentLocation" nillable="true" type="tns:IncidentLocation"/>
    <xs:simpleType name="JobFlags">
        <xs:annotation>
            <xs:appinfo>
                <ActualType Name="short" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/"/>
            </xs:appinfo>
        </xs:annotation>
        <xs:list>
            <xs:simpleType>
                <xs:restriction base="xs:string">
                    <xs:enumeration value="Unknown">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">0</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="RequiredToPost">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">1</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="RequiredToFinish">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">2</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                </xs:restriction>
            </xs:simpleType>
        </xs:list>
    </xs:simpleType>
    <xs:element name="JobFlags" nillable="true" type="tns:JobFlags"/>
    <xs:complexType name="Lienholder">
        <xs:complexContent mixed="false">
            <xs:extension base="tns:Contact">
                <xs:sequence/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="Lienholder" nillable="true" type="tns:Lienholder"/>
    <xs:complexType name="JobOwner">
        <xs:complexContent mixed="false">
            <xs:extension base="tns:Contact">
                <xs:sequence/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="JobOwner" nillable="true" type="tns:JobOwner"/>
    <xs:complexType name="ArrayOfTimeStamp">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="TimeStamp" nillable="true" type="tns:TimeStamp"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfTimeStamp" nillable="true" type="tns:ArrayOfTimeStamp"/>
    <xs:complexType name="TimeStamp">
        <xs:sequence>
            <xs:element minOccurs="0" name="GeoPoint" nillable="true" type="tns:GeoPoint"/>
            <xs:element minOccurs="0" name="Id" type="xs:int"/>
            <xs:element minOccurs="0" name="SetOn" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="Status" type="tns:TimeStampStatus"/>
            <xs:element minOccurs="0" name="UserId" type="xs:int"/>
            <xs:element minOccurs="0" name="WorkOrdDtId" type="xs:int"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="TimeStamp" nillable="true" type="tns:TimeStamp"/>
    <xs:simpleType name="TimeStampStatus">
        <xs:annotation>
            <xs:appinfo>
                <ActualType Name="short" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/"/>
            </xs:appinfo>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="Unknown"/>
            <xs:enumeration value="Confirmed"/>
            <xs:enumeration value="EnRoute"/>
            <xs:enumeration value="Arrived"/>
            <xs:enumeration value="Departed"/>
            <xs:enumeration value="DestinationArrived"/>
            <xs:enumeration value="Finished"/>
            <xs:enumeration value="PendingFinish"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:element name="TimeStampStatus" nillable="true" type="tns:TimeStampStatus"/>
    <xs:simpleType name="TowLienStatus">
        <xs:annotation>
            <xs:appinfo>
                <ActualType Name="short" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/"/>
            </xs:appinfo>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="Unknown"/>
            <xs:enumeration value="Processing"/>
            <xs:enumeration value="Scheduled"/>
            <xs:enumeration value="InputRequired"/>
            <xs:enumeration value="Error"/>
            <xs:enumeration value="Completed"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:element name="TowLienStatus" nillable="true" type="tns:TowLienStatus"/>
    <xs:complexType name="JobVehicle">
        <xs:complexContent mixed="false">
            <xs:extension base="tns:BaseVehicle">
                <xs:sequence>
                    <xs:element minOccurs="0" name="FleetNumber" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="KeyLocation" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="TowSpecVehicleId" nillable="true" type="xs:int"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="JobVehicle" nillable="true" type="tns:JobVehicle"/>
    <xs:complexType name="BaseVehicle">
        <xs:complexContent mixed="false">
            <xs:extension base="tns:NotifyErrorBase">
                <xs:sequence>
                    <xs:element minOccurs="0" name="Color" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="Make" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="Model" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="Odometer" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="PlateNumber" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="PlateState" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="Style" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="Vin" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="Year" nillable="true" type="xs:int"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="BaseVehicle" nillable="true" type="tns:BaseVehicle"/>
    <xs:complexType name="ArrayOfServiceRate">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="ServiceRate" nillable="true" type="tns:ServiceRate"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfServiceRate" nillable="true" type="tns:ArrayOfServiceRate"/>
    <xs:complexType name="ArrayOfSecuritySetting">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="SecuritySetting" nillable="true" type="tns:SecuritySetting"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfSecuritySetting" nillable="true" type="tns:ArrayOfSecuritySetting"/>
    <xs:complexType name="SecuritySetting">
        <xs:sequence>
            <xs:element minOccurs="0" name="Category" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="CategoryId" type="xs:int"/>
            <xs:element minOccurs="0" name="Description" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="Role" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="RoleId" type="xs:int"/>
            <xs:element minOccurs="0" name="SecurityOption" type="tns:SecurityOption"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="SecuritySetting" nillable="true" type="tns:SecuritySetting"/>
    <xs:simpleType name="SecurityOption">
        <xs:restriction base="xs:string">
            <xs:enumeration value="AccessAccounting">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">1</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="AccessAccountSettingsSection">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">2</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="TakePayments">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">3</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="EnterCreditMemos">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">4</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="PostFinishedJobs">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">5</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="ModifyAccountPricing">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">6</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="ModifyAccountTemplates">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">7</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="ViewAccountHistory">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">8</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="CloseDeposit">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">9</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="PrintInvoices">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">10</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="PrintStatements">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">11</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="ViewStatement">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">12</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="AccessSettings">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">100</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="AccessDriverSettingsSection">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">101</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="AccessVehicleSettingsSection">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">102</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="AccessImpoundLotSettingsSection">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">103</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="AccessServiceSettingsSection">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">104</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="AccessPricingOptions">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">105</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="AccessPricingTemplates">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">106</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="AccessPricingZoneTemplates">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">107</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="AccessPricingPayMethodTemplates">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">108</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="AccessDictionarySettingsSection">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">109</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="AccessSecuritySettingsSection">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">110</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="AccessMobileSettingsSection">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">111</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="AccessPricingTimeTemplates">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">114</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="CreateEditJob">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">200</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="AssignUnassignJob">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">201</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="TimestampJob">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">202</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="QuickFinishJob">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">203</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="CancelJob">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">204</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="TransferJobToDivision">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">205</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="SearchJobHistory">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">206</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="PreviewInvoice">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">207</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="ReleaseVehicleFromImpound">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">208</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="ModifyJobPricing">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">209</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="ModifyJobCommissions">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">210</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="EditFinishedJob">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">212</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="ModifyJobPriority">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">213</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="EnterJobsFromAllDivisions">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">215</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="FinishJob">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">218</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="AllowClearImpoundLot">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">222</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="AccessDispatchDataTabOnNewJob">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">223</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="AccessDispatchDataTabOnFinishedJob">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">224</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="ModifyUserIdsOnNewFinishedJob">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">225</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="ModifyJobInvoiceDateNumberAfterPost">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">227</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="AccessDispatchReports">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">228</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="AccessAccountingReports">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">229</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="AccessImpoundReports">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">230</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="AccessAdministrationReports">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">231</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="DuplicateJob">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">234</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="AccessJobPhotos">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">236</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="AccessDriverPhotos">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">237</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="AccessVehiclePhotos">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">238</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="AccessAccountPhotos">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">239</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="ViewJobPricing">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">240</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="UndoImpoundedVehicleRelease">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">241</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="UndoPayment">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">242</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="EditCanceledJob">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">250</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="EditImpoundedJob">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">251</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="CanAcceptOffers">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">252</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="ModifyPostedServices">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">253</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="AllowPreviousMonthPosting">
                <xs:annotation>
                    <xs:appinfo>
                        <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">254</EnumerationValue>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>
    <xs:element name="SecurityOption" nillable="true" type="tns:SecurityOption"/>
    <xs:complexType name="DispatchViewSettings">
        <xs:complexContent mixed="false">
            <xs:extension base="tns:BaseDispatchViewSettings">
                <xs:sequence>
                    <xs:element minOccurs="0" name="AssetType" type="tns:AssetType"/>
                    <xs:element minOccurs="0" name="AutoZoomMap" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="DispatchMode" type="tns:DispatchModeType"/>
                    <xs:element minOccurs="0" name="DivisionFilters" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="DivisionsHeight" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="DriverGrouping" type="tns:ResourceGroupType"/>
                    <xs:element minOccurs="0" name="DriverIconCaption" type="tns:DriverCaptionType"/>
                    <xs:element minOccurs="0" name="DriverSort" type="tns:ResourceSortType"/>
                    <xs:element minOccurs="0" name="FavoriteReports" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="IsJobTimelineVisible" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="IsMapDetached" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="IsSidePanelCollapsed" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="JobIconCaption" type="tns:JobCaptionType"/>
                    <xs:element minOccurs="0" name="JobsListWidth" type="xs:double"/>
                    <xs:element minOccurs="0" name="LargeGridColumnSettings" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="MapMode" type="tns:MapModeType"/>
                    <xs:element minOccurs="0" name="Notifications" type="tns:DispatchAction"/>
                    <xs:element minOccurs="0" name="ReadNotificationHeaders" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="ResourcesHeight" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="ResourcesMode" type="tns:ResourcesModeFlags"/>
                    <xs:element minOccurs="0" name="SearchResultsMode" type="tns:DispatchModeType"/>
                    <xs:element minOccurs="0" name="ShowHoldingJobsInGroupView" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="ShowJobsOnMap" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="ShowVehiclesOnMap" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="SmallGridColumnSettings" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="VehicleGrouping" type="tns:ResourceGroupType"/>
                    <xs:element minOccurs="0" name="VehicleIconCaption" type="tns:VehicleCaptionType"/>
                    <xs:element minOccurs="0" name="VehicleSort" type="tns:ResourceSortType"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="DispatchViewSettings" nillable="true" type="tns:DispatchViewSettings"/>
    <xs:complexType name="BaseDispatchViewSettings">
        <xs:sequence>
            <xs:element minOccurs="0" name="CustomSoundsJSON" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="DisplayNumberName" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="NotificationSound" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="ShowRequestedBy" type="xs:boolean"/>
            <xs:element minOccurs="0" name="UserId" type="xs:int"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="BaseDispatchViewSettings" nillable="true" type="tns:BaseDispatchViewSettings"/>
    <xs:simpleType name="AssetType">
        <xs:annotation>
            <xs:appinfo>
                <ActualType Name="unsignedByte" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/"/>
            </xs:appinfo>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="Drivers"/>
            <xs:enumeration value="Vehicles"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:element name="AssetType" nillable="true" type="tns:AssetType"/>
    <xs:simpleType name="DispatchModeType">
        <xs:annotation>
            <xs:appinfo>
                <ActualType Name="unsignedByte" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/"/>
            </xs:appinfo>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="Unknown"/>
            <xs:enumeration value="GridLarge"/>
            <xs:enumeration value="GridSmall"/>
            <xs:enumeration value="VisualDispatch"/>
            <xs:enumeration value="Tiles"/>
            <xs:enumeration value="List"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:element name="DispatchModeType" nillable="true" type="tns:DispatchModeType"/>
    <xs:simpleType name="ResourceGroupType">
        <xs:annotation>
            <xs:appinfo>
                <ActualType Name="unsignedByte" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/"/>
            </xs:appinfo>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="None"/>
            <xs:enumeration value="Availability"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:element name="ResourceGroupType" nillable="true" type="tns:ResourceGroupType"/>
    <xs:simpleType name="DriverCaptionType">
        <xs:annotation>
            <xs:appinfo>
                <ActualType Name="unsignedByte" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/"/>
            </xs:appinfo>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="DriverInitials"/>
            <xs:enumeration value="DriverNumber"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:element name="DriverCaptionType" nillable="true" type="tns:DriverCaptionType"/>
    <xs:simpleType name="ResourceSortType">
        <xs:annotation>
            <xs:appinfo>
                <ActualType Name="unsignedByte" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/"/>
            </xs:appinfo>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="None"/>
            <xs:enumeration value="Name"/>
            <xs:enumeration value="LongestIdle"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:element name="ResourceSortType" nillable="true" type="tns:ResourceSortType"/>
    <xs:simpleType name="JobCaptionType">
        <xs:annotation>
            <xs:appinfo>
                <ActualType Name="unsignedByte" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/"/>
            </xs:appinfo>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="JobNumber"/>
            <xs:enumeration value="ReqTruckType"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:element name="JobCaptionType" nillable="true" type="tns:JobCaptionType"/>
    <xs:simpleType name="MapModeType">
        <xs:annotation>
            <xs:appinfo>
                <ActualType Name="unsignedByte" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/"/>
            </xs:appinfo>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="Road"/>
            <xs:enumeration value="Aerial"/>
            <xs:enumeration value="BirdsEye"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:element name="MapModeType" nillable="true" type="tns:MapModeType"/>
    <xs:simpleType name="ResourcesModeFlags">
        <xs:annotation>
            <xs:appinfo>
                <ActualType Name="unsignedByte" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/"/>
            </xs:appinfo>
        </xs:annotation>
        <xs:list>
            <xs:simpleType>
                <xs:restriction base="xs:string">
                    <xs:enumeration value="None">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">0</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="Drivers">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">1</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="Vehicles">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">2</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                </xs:restriction>
            </xs:simpleType>
        </xs:list>
    </xs:simpleType>
    <xs:element name="ResourcesModeFlags" nillable="true" type="tns:ResourcesModeFlags"/>
    <xs:simpleType name="VehicleCaptionType">
        <xs:annotation>
            <xs:appinfo>
                <ActualType Name="unsignedByte" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/"/>
            </xs:appinfo>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="VehicleNumber"/>
            <xs:enumeration value="TruckType"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:element name="VehicleCaptionType" nillable="true" type="tns:VehicleCaptionType"/>
    <xs:complexType name="ServiceCalculatorSettings">
        <xs:sequence>
            <xs:element minOccurs="0" name="CalculatorSettings" nillable="true" type="tns:CalculatorSettings"/>
            <xs:element minOccurs="0" name="Version" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ServiceCalculatorSettings" nillable="true" type="tns:ServiceCalculatorSettings"/>
    <xs:complexType name="CalculatorSettings">
        <xs:sequence>
            <xs:element minOccurs="0" name="DistanceSettings" nillable="true" type="tns:DistanceSettings"/>
            <xs:element minOccurs="0" name="LaborSettings" nillable="true" type="tns:LaborSettings"/>
            <xs:element minOccurs="0" name="StorageSettings" nillable="true" type="tns:StorageSettings"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="CalculatorSettings" nillable="true" type="tns:CalculatorSettings"/>
    <xs:complexType name="DistanceSettings">
        <xs:sequence>
            <xs:element minOccurs="0" name="DistanceRules" nillable="true" type="tns:ArrayOfDistanceRule"/>
            <xs:element minOccurs="0" name="InitialFee" type="xs:decimal"/>
            <xs:element minOccurs="0" name="RouteLeg" type="tns:RouteLegType"/>
            <xs:element minOccurs="0" name="UnitRounding" type="tns:UnitRoundingType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="DistanceSettings" nillable="true" type="tns:DistanceSettings"/>
    <xs:complexType name="ArrayOfDistanceRule">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="DistanceRule" nillable="true" type="tns:DistanceRule"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfDistanceRule" nillable="true" type="tns:ArrayOfDistanceRule"/>
    <xs:complexType name="DistanceRule">
        <xs:sequence>
            <xs:element minOccurs="0" name="Distance" type="xs:decimal"/>
            <xs:element minOccurs="0" name="Index" type="xs:int"/>
            <xs:element minOccurs="0" name="Rate" type="xs:decimal"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="DistanceRule" nillable="true" type="tns:DistanceRule"/>
    <xs:simpleType name="RouteLegType">
        <xs:annotation>
            <xs:appinfo>
                <ActualType Name="unsignedByte" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/"/>
            </xs:appinfo>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="Loaded"/>
            <xs:enumeration value="Unloaded"/>
            <xs:enumeration value="Return"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:element name="RouteLegType" nillable="true" type="tns:RouteLegType"/>
    <xs:simpleType name="UnitRoundingType">
        <xs:annotation>
            <xs:appinfo>
                <ActualType Name="unsignedByte" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/"/>
            </xs:appinfo>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="UpWhole"/>
            <xs:enumeration value="UpHalf"/>
            <xs:enumeration value="UpTenth"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:element name="UnitRoundingType" nillable="true" type="tns:UnitRoundingType"/>
    <xs:complexType name="LaborSettings">
        <xs:sequence>
            <xs:element minOccurs="0" name="InitialFee" type="xs:decimal"/>
            <xs:element minOccurs="0" name="LaborRules" nillable="true" type="tns:ArrayOfLaborRule"/>
            <xs:element minOccurs="0" name="UnitRounding" type="tns:UnitRoundingType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="LaborSettings" nillable="true" type="tns:LaborSettings"/>
    <xs:complexType name="ArrayOfLaborRule">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="LaborRule" nillable="true" type="tns:LaborRule"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfLaborRule" nillable="true" type="tns:ArrayOfLaborRule"/>
    <xs:complexType name="LaborRule">
        <xs:sequence>
            <xs:element minOccurs="0" name="Hours" type="xs:decimal"/>
            <xs:element minOccurs="0" name="Index" type="xs:int"/>
            <xs:element minOccurs="0" name="Rate" type="xs:decimal"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="LaborRule" nillable="true" type="tns:LaborRule"/>
    <xs:complexType name="StorageSettings">
        <xs:sequence>
            <xs:element minOccurs="0" name="BaseFee" type="xs:decimal"/>
            <xs:element minOccurs="0" name="DayUnit" type="tns:DayUnitType"/>
            <xs:element minOccurs="0" name="GracePeriodMinutes" type="xs:int"/>
            <xs:element minOccurs="0" name="OffsetHours" type="xs:int"/>
            <xs:element minOccurs="0" name="StorageRules" nillable="true" type="tns:ArrayOfStorageRule"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="StorageSettings" nillable="true" type="tns:StorageSettings"/>
    <xs:simpleType name="DayUnitType">
        <xs:annotation>
            <xs:appinfo>
                <ActualType Name="unsignedByte" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/"/>
            </xs:appinfo>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="CalendarDay"/>
            <xs:enumeration value="TwentyFourHours"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:element name="DayUnitType" nillable="true" type="tns:DayUnitType"/>
    <xs:complexType name="ArrayOfStorageRule">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="StorageRule" nillable="true" type="tns:StorageRule"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfStorageRule" nillable="true" type="tns:ArrayOfStorageRule"/>
    <xs:complexType name="StorageRule">
        <xs:sequence>
            <xs:element minOccurs="0" name="Index" type="xs:int"/>
            <xs:element minOccurs="0" name="Rate" type="xs:decimal"/>
            <xs:element minOccurs="0" name="UnitOfTime" nillable="true" type="tns:UnitOfTimeType"/>
            <xs:element minOccurs="0" name="Units" type="xs:int"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="StorageRule" nillable="true" type="tns:StorageRule"/>
    <xs:simpleType name="UnitOfTimeType">
        <xs:annotation>
            <xs:appinfo>
                <ActualType Name="unsignedByte" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/"/>
            </xs:appinfo>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="Days"/>
            <xs:enumeration value="Hours"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:element name="UnitOfTimeType" nillable="true" type="tns:UnitOfTimeType"/>
    <xs:complexType name="SendEmailRequest">
        <xs:sequence>
            <xs:element minOccurs="0" name="EmailTo" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="Message" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="Subject" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="SendEmailRequest" nillable="true" type="tns:SendEmailRequest"/>
    <xs:complexType name="ArrayOfJob">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="Job" nillable="true" type="tns:Job"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfJob" nillable="true" type="tns:ArrayOfJob"/>
    <xs:complexType name="CreateJobRequest2">
        <xs:sequence>
            <xs:element minOccurs="0" name="AutoAssign" type="xs:boolean"/>
            <xs:element minOccurs="0" name="NewJob" nillable="true" type="tns:Job"/>
            <xs:element minOccurs="0" name="NewJobServices" nillable="true" type="tns:ArrayOfJobService"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="CreateJobRequest2" nillable="true" type="tns:CreateJobRequest2"/>
    <xs:complexType name="ArrayOfJobService">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="JobService" nillable="true" type="tns:JobService"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfJobService" nillable="true" type="tns:ArrayOfJobService"/>
    <xs:complexType name="JobService">
        <xs:complexContent mixed="false">
            <xs:extension base="tns:ServiceBase">
                <xs:sequence>
                    <xs:element minOccurs="0" name="AccountDiscountMultiplier" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="AmountApplied" type="xs:decimal"/>
                    <xs:element minOccurs="0" name="CalculatedAdjustments" type="xs:decimal"/>
                    <xs:element minOccurs="0" name="CalculatedDiscounts" type="xs:decimal"/>
                    <xs:element minOccurs="0" name="CalculatedTax" type="xs:decimal"/>
                    <xs:element minOccurs="0" name="CalculatedTotal" type="xs:decimal"/>
                    <xs:element minOccurs="0" name="CalculatedTotalWithoutTax" type="xs:decimal"/>
                    <xs:element minOccurs="0" name="CommissionDriverId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="CommissionPaidOn" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="InvoiceDate" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="InvoiceId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="InvoiceNumber" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="IsChargeLocked" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="IsLinked" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="IsLocked" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="PaidInFullOn" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="PaymentMethodAdjustment" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="PostedOn" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="QBTransactionId" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="Quantity" type="xs:decimal"/>
                    <xs:element minOccurs="0" name="Remarks" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="TaxRate1" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="TaxRate2" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="TaxRate3" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="TaxRate4" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="TimeAdjustment" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="WasAddedAutomatically" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="WorkOrdChgDtId" type="xs:int"/>
                    <xs:element minOccurs="0" name="WorkOrdDtId" type="xs:int"/>
                    <xs:element minOccurs="0" name="ZoneAdjustment" nillable="true" type="xs:string"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="JobService" nillable="true" type="tns:JobService"/>
    <xs:complexType name="UpdateJobResponse">
        <xs:complexContent mixed="false">
            <xs:extension base="tns:BaseResponse">
                <xs:sequence>
                    <xs:element minOccurs="0" name="ConfirmPriceChange" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="ServicesToDeleteFromPriceChange" nillable="true" type="q9:ArrayOfstring" xmlns:q9="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
                    <xs:element minOccurs="0" name="UpdatedJob" nillable="true" type="tns:Job"/>
                    <xs:element minOccurs="0" name="UpdatedJobServices" nillable="true" type="tns:ArrayOfJobService"/>
                    <xs:element minOccurs="0" name="WasAutoAssignSuccessful" type="xs:boolean"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="UpdateJobResponse" nillable="true" type="tns:UpdateJobResponse"/>
    <xs:complexType name="UpdateJobRequest">
        <xs:sequence>
            <xs:element minOccurs="0" name="AutoAssign" type="xs:boolean"/>
            <xs:element minOccurs="0" name="Changes" nillable="true" type="q10:ArrayOfKeyValueOfstringstring" xmlns:q10="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
            <xs:element minOccurs="0" name="CheckForPriceChange" type="xs:boolean"/>
            <xs:element minOccurs="0" name="JobServiceIdsToDelete" nillable="true" type="q11:ArrayOfint" xmlns:q11="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
            <xs:element minOccurs="0" name="JobServicesToUpdate" nillable="true" type="tns:ArrayOfJobService"/>
            <xs:element minOccurs="0" name="WorkOrdDtId" type="xs:int"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="UpdateJobRequest" nillable="true" type="tns:UpdateJobRequest"/>
    <xs:complexType name="AssignJobRequest">
        <xs:complexContent mixed="false">
            <xs:extension base="tns:BaseJobRequest">
                <xs:sequence>
                    <xs:element minOccurs="0" name="DriverId" type="xs:int"/>
                    <xs:element minOccurs="0" name="MakePrimary" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="VehicleId" type="xs:int"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="AssignJobRequest" nillable="true" type="tns:AssignJobRequest"/>
    <xs:complexType name="BaseJobRequest">
        <xs:sequence>
            <xs:element minOccurs="0" name="WorkOrdDtId" type="xs:int"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="BaseJobRequest" nillable="true" type="tns:BaseJobRequest"/>
    <xs:complexType name="UnassignJobRequest">
        <xs:complexContent mixed="false">
            <xs:extension base="tns:BaseJobRequest">
                <xs:sequence>
                    <xs:element minOccurs="0" name="DriverIds" nillable="true" type="q12:ArrayOfint" xmlns:q12="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="UnassignJobRequest" nillable="true" type="tns:UnassignJobRequest"/>
    <xs:complexType name="TimeStampJobRequest">
        <xs:complexContent mixed="false">
            <xs:extension base="tns:BaseJobRequest">
                <xs:sequence>
                    <xs:element minOccurs="0" name="BackfillDates" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="CalculatedArriveTime" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="GeoPoint" nillable="true" type="tns:GeoPoint"/>
                    <xs:element minOccurs="0" name="NewStatus" type="tns:TimeStampStatus"/>
                    <xs:element minOccurs="0" name="TimestampedOn" nillable="true" type="xs:dateTime"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="TimeStampJobRequest" nillable="true" type="tns:TimeStampJobRequest"/>
    <xs:complexType name="MoveJobToWaitingRequest">
        <xs:complexContent mixed="false">
            <xs:extension base="tns:BaseJobRequest">
                <xs:sequence/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="MoveJobToWaitingRequest" nillable="true" type="tns:MoveJobToWaitingRequest"/>
    <xs:complexType name="MoveJobToHoldingRequest">
        <xs:complexContent mixed="false">
            <xs:extension base="tns:BaseJobRequest">
                <xs:sequence/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="MoveJobToHoldingRequest" nillable="true" type="tns:MoveJobToHoldingRequest"/>
    <xs:complexType name="GetPromptOnFinishFieldsRequest">
        <xs:sequence>
            <xs:element minOccurs="0" name="RequiredOnly" type="xs:boolean"/>
            <xs:element minOccurs="0" name="WorkOrdDtId" type="xs:int"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="GetPromptOnFinishFieldsRequest" nillable="true" type="tns:GetPromptOnFinishFieldsRequest"/>
    <xs:complexType name="ArrayOfEditPropertyAttributes">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="EditPropertyAttributes" nillable="true" type="tns:EditPropertyAttributes"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfEditPropertyAttributes" nillable="true" type="tns:ArrayOfEditPropertyAttributes"/>
    <xs:complexType name="EditPropertyAttributes">
        <xs:complexContent mixed="false">
            <xs:extension base="tns:BasePropertyAttributes">
                <xs:sequence>
                    <xs:element minOccurs="0" name="AutoCompleteType" nillable="true" type="tns:AutoCompleteType"/>
                    <xs:element minOccurs="0" name="DoVINCheck" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="FullName" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="GroupName" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="IsRequired" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="MaxStringLength" type="xs:int"/>
                    <xs:element minOccurs="0" name="MaxValue" type="xs:int"/>
                    <xs:element minOccurs="0" name="MinStringLength" type="xs:int"/>
                    <xs:element minOccurs="0" name="MinValue" type="xs:int"/>
                    <xs:element minOccurs="0" name="ReadOnly" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="SelectableValues" nillable="true" type="q13:ArrayOfKeyValueOfstringstring" xmlns:q13="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
                    <xs:element minOccurs="0" name="ValuesThatRequireComments" nillable="true" type="q14:ArrayOfstring" xmlns:q14="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="EditPropertyAttributes" nillable="true" type="tns:EditPropertyAttributes"/>
    <xs:complexType name="BasePropertyAttributes">
        <xs:sequence>
            <xs:element minOccurs="0" name="Data" nillable="true" type="q15:ArrayOfKeyValueOfstringstring" xmlns:q15="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
            <xs:element minOccurs="0" name="Label" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="Order" type="xs:int"/>
            <xs:element minOccurs="0" name="PropertyName" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="TypeCode" type="q16:TypeCode" xmlns:q16="http://schemas.datacontract.org/2004/07/System"/>
            <xs:element minOccurs="0" name="TypeDescription" type="q17:DataType" xmlns:q17="http://schemas.datacontract.org/2004/07/System.ComponentModel.DataAnnotations"/>
            <xs:element minOccurs="0" name="Value" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="BasePropertyAttributes" nillable="true" type="tns:BasePropertyAttributes"/>
    <xs:simpleType name="AutoCompleteType">
        <xs:annotation>
            <xs:appinfo>
                <ActualType Name="short" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/"/>
            </xs:appinfo>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="Unknown"/>
            <xs:enumeration value="Reasons"/>
            <xs:enumeration value="CancelReasons"/>
            <xs:enumeration value="Cities"/>
            <xs:enumeration value="States"/>
            <xs:enumeration value="VehicleColors"/>
            <xs:enumeration value="VehicleStyles"/>
            <xs:enumeration value="VehicleKeyLocations"/>
            <xs:enumeration value="VehicleDamageDescriptions"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:element name="AutoCompleteType" nillable="true" type="tns:AutoCompleteType"/>
    <xs:complexType name="ClearCodesResponse">
        <xs:sequence>
            <xs:element minOccurs="0" name="PromptCustomerContacted" type="xs:boolean"/>
            <xs:element minOccurs="0" name="SelectableValues" nillable="true" type="q18:ArrayOfKeyValueOfstringstring" xmlns:q18="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
            <xs:element minOccurs="0" name="ValuesThatRequireComments" nillable="true" type="q19:ArrayOfstring" xmlns:q19="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ClearCodesResponse" nillable="true" type="tns:ClearCodesResponse"/>
    <xs:complexType name="FinishJobRequest">
        <xs:complexContent mixed="false">
            <xs:extension base="tns:BaseJobRequest">
                <xs:sequence>
                    <xs:element minOccurs="0" name="Changes" nillable="true" type="q20:ArrayOfKeyValueOfstringstring" xmlns:q20="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
                    <xs:element minOccurs="0" name="FinishedOn" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="GeoPoint" nillable="true" type="tns:GeoPoint"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="FinishJobRequest" nillable="true" type="tns:FinishJobRequest"/>
    <xs:complexType name="UndoFinishJobRequest">
        <xs:complexContent mixed="false">
            <xs:extension base="tns:BaseJobRequest">
                <xs:sequence/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="UndoFinishJobRequest" nillable="true" type="tns:UndoFinishJobRequest"/>
    <xs:complexType name="CancelJobRequest">
        <xs:complexContent mixed="false">
            <xs:extension base="tns:BaseJobRequest">
                <xs:sequence>
                    <xs:element minOccurs="0" name="Reason" nillable="true" type="xs:string"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="CancelJobRequest" nillable="true" type="tns:CancelJobRequest"/>
    <xs:complexType name="CancelJobResponse">
        <xs:complexContent mixed="false">
            <xs:extension base="tns:BaseResponse">
                <xs:sequence/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="CancelJobResponse" nillable="true" type="tns:CancelJobResponse"/>
    <xs:complexType name="JobResourceRequest">
        <xs:sequence>
            <xs:element minOccurs="0" name="DriverId" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="VehicleId" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="WorkOrdDtId" type="xs:int"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="JobResourceRequest" nillable="true" type="tns:JobResourceRequest"/>
    <xs:complexType name="JobResource">
        <xs:sequence>
            <xs:element minOccurs="0" name="ArrivedTime" nillable="true" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="AssignedDriverId" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="AssignedTime" nillable="true" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="AssignedVehicleId" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="ClearedTime" nillable="true" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="ConfirmedTime" nillable="true" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="DestinationArrivedTime" nillable="true" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="EnRouteTime" nillable="true" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="Id" type="xs:int"/>
            <xs:element minOccurs="0" name="IncidentDepartedTime" nillable="true" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="LastPageTime" nillable="true" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="ResourceDispatchAction" type="tns:ResourceDispatchAction"/>
            <xs:element minOccurs="0" name="WorkOrdDtId" type="xs:int"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="JobResource" nillable="true" type="tns:JobResource"/>
    <xs:simpleType name="ResourceDispatchAction">
        <xs:annotation>
            <xs:appinfo>
                <ActualType Name="unsignedByte" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/"/>
            </xs:appinfo>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="Update"/>
            <xs:enumeration value="Create"/>
            <xs:enumeration value="Delete"/>
            <xs:enumeration value="GPSUpdate"/>
            <xs:enumeration value="DispatchUpdate"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:element name="ResourceDispatchAction" nillable="true" type="tns:ResourceDispatchAction"/>
    <xs:complexType name="FindJobsRequest">
        <xs:complexContent mixed="false">
            <xs:extension base="tns:BaseFindRequest">
                <xs:sequence>
                    <xs:element minOccurs="0" name="AssignedDriverId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="AssignedVehicleId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="BillToId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="Destination" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="DivisionId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="DivisionIds" nillable="true" type="q21:ArrayOfint" xmlns:q21="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
                    <xs:element minOccurs="0" name="FleetNumber" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="ImpoundLotId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="Incident" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="InvoiceNumber" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="IsNotPosted" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="JobFinishedDate" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="JobFinishedFromDate" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="JobFinishedToDate" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="JobNumber" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="JobReceivedDate" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="JobReceivedFromDate" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="JobReceivedToDate" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="JobStatuses" nillable="true" type="tns:ArrayOfJobStatus"/>
                    <xs:element minOccurs="0" name="MemberNumber" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="OfferStatuses" nillable="true" type="tns:ArrayOfOfferStatus"/>
                    <xs:element minOccurs="0" name="OwnerName" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="OwnerPhone" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="PONumber" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="Reason" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="RequestedById" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="SortDescriptions" nillable="true" type="tns:ArrayOfSortDescription"/>
                    <xs:element minOccurs="0" name="TicketNumber" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="VehicleDescription" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="VehiclePlate" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="VehicleVIN" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="WasReleasedFromImpound" type="xs:boolean"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="FindJobsRequest" nillable="true" type="tns:FindJobsRequest"/>
    <xs:complexType name="BaseFindRequest">
        <xs:sequence>
            <xs:element minOccurs="0" name="SearchSettings" nillable="true" type="tns:SearchSettings"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="BaseFindRequest" nillable="true" type="tns:BaseFindRequest"/>
    <xs:complexType name="SearchSettings">
        <xs:sequence>
            <xs:element minOccurs="0" name="LocalSearchTime" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="SkipCount" type="xs:int"/>
            <xs:element minOccurs="0" name="TakeCount" type="xs:int"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="SearchSettings" nillable="true" type="tns:SearchSettings"/>
    <xs:complexType name="ArrayOfJobStatus">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="JobStatus" type="tns:JobStatus"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfJobStatus" nillable="true" type="tns:ArrayOfJobStatus"/>
    <xs:complexType name="ArrayOfOfferStatus">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="OfferStatus" type="tns:OfferStatus"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfOfferStatus" nillable="true" type="tns:ArrayOfOfferStatus"/>
    <xs:complexType name="ArrayOfSortDescription">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="SortDescription" nillable="true" type="tns:SortDescription"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfSortDescription" nillable="true" type="tns:ArrayOfSortDescription"/>
    <xs:complexType name="SortDescription">
        <xs:sequence>
            <xs:element minOccurs="0" name="Direction" type="q22:ListSortDirection" xmlns:q22="http://schemas.datacontract.org/2004/07/System.ComponentModel"/>
            <xs:element minOccurs="0" name="PropertyName" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="SortDescription" nillable="true" type="tns:SortDescription"/>
    <xs:complexType name="BasicFindJobsRequest">
        <xs:complexContent mixed="false">
            <xs:extension base="tns:BaseFindRequest">
                <xs:sequence>
                    <xs:element minOccurs="0" name="JobStatuses" nillable="true" type="tns:ArrayOfJobStatus"/>
                    <xs:element minOccurs="0" name="Search" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="SortDescriptions" nillable="true" type="tns:ArrayOfSortDescription"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="BasicFindJobsRequest" nillable="true" type="tns:BasicFindJobsRequest"/>
    <xs:complexType name="Driver">
        <xs:complexContent mixed="false">
            <xs:extension base="tns:BasicDriver">
                <xs:sequence>
                    <xs:element minOccurs="0" name="Address" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="AlertEmail" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="AlertGroupId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="BackgroundCheckDate" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="BirthDate" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="City" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="CommissionBonusLevel" nillable="true" type="xs:unsignedByte"/>
                    <xs:element minOccurs="0" name="CommissionLimitTemplateId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="CommissionRateAdjustment" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="CommissionTemplateId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="CommissionTimeTemplateId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="CountryOfCitizenshipId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="EmergencyContact1" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="EmergencyContact1Phone" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="EmergencyContact2" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="EmergencyContact2Phone" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="HireDate" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="LicenseClassId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="LicenseExpirationDate" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="LicenseNumber" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="LicenseStateId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="MiddleName" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="NotificationMethod" type="tns:NotificationMethod"/>
                    <xs:element minOccurs="0" name="OtherPhoneNumber" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="OvertimePayRate" nillable="true" type="xs:decimal"/>
                    <xs:element minOccurs="0" name="PayTypeId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="PhotoName" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="PhysicalDate" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="PostalCode" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="RegularPayRate" nillable="true" type="xs:decimal"/>
                    <xs:element minOccurs="0" name="Remarks" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="SocialSecurityNumber" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="StateDictionaryId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="StateLicense" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="TerminationDate" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="TerminationReasonId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="YearsOfExperience" nillable="true" type="xs:unsignedByte"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="Driver" nillable="true" type="tns:Driver"/>
    <xs:complexType name="BasicDriver">
        <xs:sequence>
            <xs:element minOccurs="0" name="AssignedWorkOrdDtIds" nillable="true" type="q23:ArrayOfint" xmlns:q23="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
            <xs:element minOccurs="0" name="CompanyId" type="xs:int"/>
            <xs:element minOccurs="0" name="DisplayName" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="DivisionId" type="xs:int"/>
            <xs:element minOccurs="0" name="EmailAddress" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="FirstName" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="GeoPoint" nillable="true" type="tns:GeoPoint"/>
            <xs:element minOccurs="0" name="GpsDirection" nillable="true" type="xs:short"/>
            <xs:element minOccurs="0" name="GpsLastUpdate" nillable="true" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="GpsSpeed" nillable="true" type="xs:short"/>
            <xs:element minOccurs="0" name="HomePhoneNumber" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="Id" type="xs:int"/>
            <xs:element minOccurs="0" name="IsAvailableStatus" nillable="true" type="xs:boolean"/>
            <xs:element minOccurs="0" name="IsDeleted" type="xs:boolean"/>
            <xs:element minOccurs="0" name="IsInactive" type="xs:boolean"/>
            <xs:element minOccurs="0" name="LastClearTime" nillable="true" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="LastName" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="LinkedVehicleId" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="MobilePhoneNumber" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="Number" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="ResourceDispatchAction" type="tns:ResourceDispatchAction"/>
            <xs:element minOccurs="0" name="StatusId" nillable="true" type="xs:int"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="BasicDriver" nillable="true" type="tns:BasicDriver"/>
    <xs:simpleType name="NotificationMethod">
        <xs:annotation>
            <xs:appinfo>
                <ActualType Name="short" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/"/>
            </xs:appinfo>
        </xs:annotation>
        <xs:list>
            <xs:simpleType>
                <xs:restriction base="xs:string">
                    <xs:enumeration value="None">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">0</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="Global">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">1</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                    <xs:enumeration value="Email1Way"/>
                    <xs:enumeration value="Email2Way"/>
                    <xs:enumeration value="EmailBrowser"/>
                    <xs:enumeration value="Smartphone">
                        <xs:annotation>
                            <xs:appinfo>
                                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">256</EnumerationValue>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:enumeration>
                </xs:restriction>
            </xs:simpleType>
        </xs:list>
    </xs:simpleType>
    <xs:element name="NotificationMethod" nillable="true" type="tns:NotificationMethod"/>
    <xs:complexType name="ArrayOfBasicDriver">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="BasicDriver" nillable="true" type="tns:BasicDriver"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfBasicDriver" nillable="true" type="tns:ArrayOfBasicDriver"/>
    <xs:complexType name="ArrayOfDriver">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="Driver" nillable="true" type="tns:Driver"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfDriver" nillable="true" type="tns:ArrayOfDriver"/>
    <xs:complexType name="Vehicle">
        <xs:complexContent mixed="false">
            <xs:extension base="tns:BasicVehicle">
                <xs:sequence>
                    <xs:element minOccurs="0" name="DisableRoadAlerts" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="EndingMileage" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="ICCRegistrationNumber" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="MiscellaneousData" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="PlateExpirationDate" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="PlateNumber" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="PlateStateDictionaryId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="PurchasedDate" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="PurchasedForAmount" nillable="true" type="xs:decimal"/>
                    <xs:element minOccurs="0" name="Remarks" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="ServiceVehicleBedMakeDictionaryId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="ServiceVehicleMakeDictionaryId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="ServiceVehicleModelDictionaryId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="SoldDate" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="SoldForAmount" nillable="true" type="xs:decimal"/>
                    <xs:element minOccurs="0" name="StartingMileage" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="StateLicense" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="VehicleBedModel" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="VehicleBedSerialNumber" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="VehicleColorDictionaryId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="Vin" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="Year" nillable="true" type="xs:int"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="Vehicle" nillable="true" type="tns:Vehicle"/>
    <xs:complexType name="BasicVehicle">
        <xs:sequence>
            <xs:element minOccurs="0" name="AssignedWorkOrdDtIds" nillable="true" type="q24:ArrayOfint" xmlns:q24="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
            <xs:element minOccurs="0" name="CompanyId" type="xs:int"/>
            <xs:element minOccurs="0" name="DivisionId" type="xs:int"/>
            <xs:element minOccurs="0" name="GeoPoint" nillable="true" type="tns:GeoPoint"/>
            <xs:element minOccurs="0" name="GpsAccountId" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="GpsDirection" nillable="true" type="xs:short"/>
            <xs:element minOccurs="0" name="GpsLastUpdate" nillable="true" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="GpsSpeed" nillable="true" type="xs:short"/>
            <xs:element minOccurs="0" name="GpsStatus" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="GpsVehicleId" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="Id" type="xs:int"/>
            <xs:element minOccurs="0" name="IsAvailableStatus" nillable="true" type="xs:boolean"/>
            <xs:element minOccurs="0" name="IsDeleted" type="xs:boolean"/>
            <xs:element minOccurs="0" name="IsInactive" type="xs:boolean"/>
            <xs:element minOccurs="0" name="LastClearTime" nillable="true" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="LinkedDriverId" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="Number" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="ResourceDispatchAction" type="tns:ResourceDispatchAction"/>
            <xs:element minOccurs="0" name="StatusId" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="VehicleTypeId" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="VehicleTypeName" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="VehicleTypeNameAbbr" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="BasicVehicle" nillable="true" type="tns:BasicVehicle"/>
    <xs:complexType name="ArrayOfBasicVehicle">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="BasicVehicle" nillable="true" type="tns:BasicVehicle"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfBasicVehicle" nillable="true" type="tns:ArrayOfBasicVehicle"/>
    <xs:complexType name="ArrayOfVehicle">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="Vehicle" nillable="true" type="tns:Vehicle"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfVehicle" nillable="true" type="tns:ArrayOfVehicle"/>
    <xs:complexType name="LinkResourcesRequest">
        <xs:sequence>
            <xs:element minOccurs="0" name="DriverId" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="VehicleId" nillable="true" type="xs:int"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="LinkResourcesRequest" nillable="true" type="tns:LinkResourcesRequest"/>
    <xs:complexType name="ChangeStatusRequest">
        <xs:sequence>
            <xs:element minOccurs="0" name="Id" type="xs:int"/>
            <xs:element minOccurs="0" name="StatusId" type="xs:int"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ChangeStatusRequest" nillable="true" type="tns:ChangeStatusRequest"/>
    <xs:complexType name="TowJobResult">
        <xs:complexContent mixed="false">
            <xs:extension base="tns:BaseResponse">
                <xs:sequence>
                    <xs:element minOccurs="0" name="NewWorkOrdDtId" type="xs:int"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="TowJobResult" nillable="true" type="tns:TowJobResult"/>
    <xs:complexType name="DuplicateJobResult">
        <xs:complexContent mixed="false">
            <xs:extension base="tns:BaseResponse">
                <xs:sequence>
                    <xs:element minOccurs="0" name="NewWorkOrdDtId" type="xs:int"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="DuplicateJobResult" nillable="true" type="tns:DuplicateJobResult"/>
    <xs:complexType name="UpdateJobGeoPointRequest">
        <xs:sequence>
            <xs:element minOccurs="0" name="GeoPoint" nillable="true" type="tns:GeoPoint"/>
            <xs:element minOccurs="0" name="JobLocationType" type="tns:JobLocationType"/>
            <xs:element minOccurs="0" name="WorkOrdDtId" type="xs:int"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="UpdateJobGeoPointRequest" nillable="true" type="tns:UpdateJobGeoPointRequest"/>
    <xs:simpleType name="JobLocationType">
        <xs:annotation>
            <xs:appinfo>
                <ActualType Name="unsignedByte" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/"/>
            </xs:appinfo>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="Incident"/>
            <xs:enumeration value="Destination"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:element name="JobLocationType" nillable="true" type="tns:JobLocationType"/>
    <xs:complexType name="UpdateVehicleGeoPointRequest">
        <xs:sequence>
            <xs:element minOccurs="0" name="GeoPoint" nillable="true" type="tns:GeoPoint"/>
            <xs:element minOccurs="0" name="Id" type="xs:int"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="UpdateVehicleGeoPointRequest" nillable="true" type="tns:UpdateVehicleGeoPointRequest"/>
    <xs:complexType name="UpdateDriverGeoPointRequest">
        <xs:sequence>
            <xs:element minOccurs="0" name="GeoPoint" nillable="true" type="tns:GeoPoint"/>
            <xs:element minOccurs="0" name="Id" type="xs:int"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="UpdateDriverGeoPointRequest" nillable="true" type="tns:UpdateDriverGeoPointRequest"/>
    <xs:complexType name="AddJobMessageRequest">
        <xs:sequence>
            <xs:element minOccurs="0" name="IsSystemMessage" type="xs:boolean"/>
            <xs:element minOccurs="0" name="Message" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="WorkOrdDtId" type="xs:int"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="AddJobMessageRequest" nillable="true" type="tns:AddJobMessageRequest"/>
    <xs:complexType name="ArrayOfJobMessage">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="JobMessage" nillable="true" type="tns:JobMessage"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfJobMessage" nillable="true" type="tns:ArrayOfJobMessage"/>
    <xs:complexType name="JobMessage">
        <xs:sequence>
            <xs:element minOccurs="0" name="CreatedOn" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="Id" type="xs:int"/>
            <xs:element minOccurs="0" name="IsCurrentUser" type="xs:boolean"/>
            <xs:element minOccurs="0" name="Message" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="Name" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="ReadOn" nillable="true" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="UserId" type="xs:int"/>
            <xs:element minOccurs="0" name="WorkOrdDtId" type="xs:int"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="JobMessage" nillable="true" type="tns:JobMessage"/>
    <xs:complexType name="ArrayOfJobHistory">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="JobHistory" nillable="true" type="tns:JobHistory"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfJobHistory" nillable="true" type="tns:ArrayOfJobHistory"/>
    <xs:complexType name="JobHistory">
        <xs:complexContent mixed="false">
            <xs:extension base="tns:NotifyErrorBase">
                <xs:sequence>
                    <xs:element minOccurs="0" name="Action" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="Details" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="DetailsId" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="EventDate" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="UserId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="UserName" nillable="true" type="xs:string"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="JobHistory" nillable="true" type="tns:JobHistory"/>
    <xs:complexType name="DistancesFromResourcesToJobRequest">
        <xs:sequence>
            <xs:element minOccurs="0" name="distanceUnitType" type="tns:DistanceUnitType"/>
            <xs:element minOccurs="0" name="jobGeoPoint" nillable="true" type="tns:GeoPoint"/>
            <xs:element minOccurs="0" name="resourceGeoPoints" nillable="true" type="q25:ArrayOfKeyValuePairOfintGeoPointEo3syJv6" xmlns:q25="http://schemas.datacontract.org/2004/07/System.Collections.Generic"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="DistancesFromResourcesToJobRequest" nillable="true" type="tns:DistancesFromResourcesToJobRequest"/>
    <xs:simpleType name="TowMagicClientViewSchema">
        <xs:annotation>
            <xs:appinfo>
                <ActualType Name="short" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/"/>
            </xs:appinfo>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="Accept"/>
            <xs:enumeration value="Reject"/>
            <xs:enumeration value="SpCancel"/>
            <xs:enumeration value="GoaRequest"/>
            <xs:enumeration value="ExtendEta"/>
            <xs:enumeration value="Finish"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:element name="TowMagicClientViewSchema" nillable="true" type="tns:TowMagicClientViewSchema"/>
    <xs:complexType name="OfferPreview">
        <xs:sequence>
            <xs:element minOccurs="0" name="BoostMultiplier" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="BoostRate" nillable="true" type="xs:decimal"/>
            <xs:element minOccurs="0" name="ClientViewSchema" nillable="true" type="q26:ClientViewSchemasResponse" xmlns:q26="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model.TowMagic"/>
            <xs:element minOccurs="0" name="ContractorNumber" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="DestinationLocation" nillable="true" type="tns:Location"/>
            <xs:element minOccurs="0" name="IncidentLocation" nillable="true" type="tns:Location"/>
            <xs:element minOccurs="0" name="MCPromoText" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="MotorClubKey" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="MotorClubName" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="OfferAmount" nillable="true" type="xs:decimal"/>
            <xs:element minOccurs="0" name="OriginalEntryTime" nillable="true" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="Reason" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="StartingLocation" nillable="true" type="tns:Location"/>
            <xs:element minOccurs="0" name="TimeoutTime" nillable="true" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="VehicleDescription" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="OfferPreview" nillable="true" type="tns:OfferPreview"/>
    <xs:complexType name="CustomerLocationLinkRequest">
        <xs:sequence>
            <xs:element minOccurs="0" name="DivisionId" type="xs:int"/>
            <xs:element minOccurs="0" name="Guid" type="ser:guid"/>
            <xs:element minOccurs="0" name="ToPhone" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="WorkOrdDtId" type="xs:int"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="CustomerLocationLinkRequest" nillable="true" type="tns:CustomerLocationLinkRequest"/>
    <xs:complexType name="CustomerLocationLinkResponse">
        <xs:complexContent mixed="false">
            <xs:extension base="tns:BaseResponse">
                <xs:sequence/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="CustomerLocationLinkResponse" nillable="true" type="tns:CustomerLocationLinkResponse"/>
    <xs:complexType name="LaborAveragesRequest">
        <xs:sequence>
            <xs:element minOccurs="0" name="DivisionId" type="xs:int"/>
            <xs:element minOccurs="0" name="IsRoadsideJob" type="xs:boolean"/>
            <xs:element minOccurs="0" name="Make" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="Model" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="RequiredTruckTypeId" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="TowSpecVehicleId" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="Year" nillable="true" type="xs:int"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="LaborAveragesRequest" nillable="true" type="tns:LaborAveragesRequest"/>
    <xs:complexType name="LaborAverages">
        <xs:sequence>
            <xs:element minOccurs="0" name="AtDestinationDescription" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="AverageAtDestination" nillable="true" type="ser:duration"/>
            <xs:element minOccurs="0" name="AverageOnScene" nillable="true" type="ser:duration"/>
            <xs:element minOccurs="0" name="OnSceneDescription" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="LaborAverages" nillable="true" type="tns:LaborAverages"/>
    <xs:complexType name="JobPaymentPreview">
        <xs:sequence>
            <xs:element minOccurs="0" name="AllowChangeBillTo" type="xs:boolean"/>
            <xs:element minOccurs="0" name="AllowFinish" type="xs:boolean"/>
            <xs:element minOccurs="0" name="AllowRelease" type="xs:boolean"/>
            <xs:element minOccurs="0" name="AllowReleaseOnPartialPayment" type="xs:boolean"/>
            <xs:element minOccurs="0" name="AllowSMS" type="xs:boolean"/>
            <xs:element minOccurs="0" name="BillToId" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="Contact" nillable="true" type="tns:JobContact"/>
            <xs:element minOccurs="0" name="DivisionId" type="xs:int"/>
            <xs:element minOccurs="0" name="ImpoundedOn" nillable="true" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="IsInImpound" type="xs:boolean"/>
            <xs:element minOccurs="0" name="IsLinkedJob" type="xs:boolean"/>
            <xs:element minOccurs="0" name="IsPasswordRequiredOnPayment" type="xs:boolean"/>
            <xs:element minOccurs="0" name="IsPoliceHold" type="xs:boolean"/>
            <xs:element minOccurs="0" name="JobNumber" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="JobServicePayments" nillable="true" type="tns:ArrayOfJobServicePayment"/>
            <xs:element minOccurs="0" name="JobVehicle" nillable="true" type="tns:JobVehicle"/>
            <xs:element minOccurs="0" name="NextInvoiceNumber" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="Owner" nillable="true" type="tns:JobOwner"/>
            <xs:element minOccurs="0" name="PaymentMethodGateways" nillable="true" type="tns:ArrayOfPaymentMethodGateway"/>
            <xs:element minOccurs="0" name="PaymentMethodId" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="PreviousStorageEndedOn" nillable="true" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="StorageServiceRates" nillable="true" type="tns:ArrayOfServiceRate"/>
            <xs:element minOccurs="0" name="VerifyVinOnRelease" type="xs:boolean"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="JobPaymentPreview" nillable="true" type="tns:JobPaymentPreview"/>
    <xs:complexType name="ArrayOfJobServicePayment">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="JobServicePayment" nillable="true" type="tns:JobServicePayment"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfJobServicePayment" nillable="true" type="tns:ArrayOfJobServicePayment"/>
    <xs:complexType name="JobServicePayment">
        <xs:sequence>
            <xs:element minOccurs="0" name="AmountApplied" type="xs:decimal"/>
            <xs:element minOccurs="0" name="AmountDue" type="xs:decimal"/>
            <xs:element minOccurs="0" name="AmountPreviouslyPaid" type="xs:decimal"/>
            <xs:element minOccurs="0" name="InvoiceDate" nillable="true" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="InvoiceNumber" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="IsAutoCalcStorage" type="xs:boolean"/>
            <xs:element minOccurs="0" name="JobService" nillable="true" type="tns:JobService"/>
            <xs:element minOccurs="0" name="WasVehicleReleased" type="xs:boolean"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="JobServicePayment" nillable="true" type="tns:JobServicePayment"/>
    <xs:complexType name="ArrayOfPaymentMethodGateway">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="PaymentMethodGateway" nillable="true" type="tns:PaymentMethodGateway"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfPaymentMethodGateway" nillable="true" type="tns:ArrayOfPaymentMethodGateway"/>
    <xs:complexType name="PaymentMethodGateway">
        <xs:sequence>
            <xs:element minOccurs="0" name="AccountId" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="AllowCapturePayment" type="xs:boolean"/>
            <xs:element minOccurs="0" name="AllowRemotePayment" type="xs:boolean"/>
            <xs:element minOccurs="0" name="GatewayKey" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="LocationId" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="PaymentMethodId" type="xs:int"/>
            <xs:element minOccurs="0" name="UseDesktopReader" type="xs:boolean"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="PaymentMethodGateway" nillable="true" type="tns:PaymentMethodGateway"/>
    <xs:complexType name="TakeJobPaymentRequest">
        <xs:sequence>
            <xs:element minOccurs="0" name="AutoPayZeroBalance" type="xs:boolean"/>
            <xs:element minOccurs="0" name="BillToId" type="xs:int"/>
            <xs:element minOccurs="0" name="CapturePayment" type="xs:boolean"/>
            <xs:element minOccurs="0" name="CreditCard" nillable="true" type="tns:CreditCard"/>
            <xs:element minOccurs="0" name="FinishJob" type="xs:boolean"/>
            <xs:element minOccurs="0" name="GeoPoint" nillable="true" type="tns:GeoPoint"/>
            <xs:element minOccurs="0" name="IsCreditCardAsyncPayment" type="xs:boolean"/>
            <xs:element minOccurs="0" name="IsPaymentPopup" type="xs:boolean"/>
            <xs:element minOccurs="0" name="IsReTow" type="xs:boolean"/>
            <xs:element minOccurs="0" name="IsRemotePayment" type="xs:boolean"/>
            <xs:element minOccurs="0" name="JobServicePayments" nillable="true" type="tns:ArrayOfJobServicePayment"/>
            <xs:element minOccurs="0" name="Owner" nillable="true" type="tns:JobOwner"/>
            <xs:element minOccurs="0" name="Password" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="PaymentMethodId" type="xs:int"/>
            <xs:element minOccurs="0" name="PaymentSource" type="tns:PaymentSource"/>
            <xs:element minOccurs="0" name="ReceiptDate" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="ReferenceNumber" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="ReleaseDate" nillable="true" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="ReleaseVehicle" type="xs:boolean"/>
            <xs:element minOccurs="0" name="ReleasedTo" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="TransactionId" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="VIN" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="WorkOrdDtId" type="xs:int"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="TakeJobPaymentRequest" nillable="true" type="tns:TakeJobPaymentRequest"/>
    <xs:complexType name="CreditCard">
        <xs:sequence>
            <xs:element minOccurs="0" name="BillingContact" nillable="true" type="tns:BillingContact"/>
            <xs:element minOccurs="0" name="CardNumber" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="CardType" type="tns:CardType"/>
            <xs:element minOccurs="0" name="ExpirationMonth" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="ExpirationYear" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="NameOnCard" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="ReaderId" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="SecurityCode" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="SwipeData" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="CreditCard" nillable="true" type="tns:CreditCard"/>
    <xs:complexType name="BillingContact">
        <xs:complexContent mixed="false">
            <xs:extension base="tns:Contact">
                <xs:sequence/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="BillingContact" nillable="true" type="tns:BillingContact"/>
    <xs:simpleType name="CardType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="Unknown"/>
            <xs:enumeration value="Visa"/>
            <xs:enumeration value="MasterCard"/>
            <xs:enumeration value="AMEX"/>
            <xs:enumeration value="Discover"/>
            <xs:enumeration value="Diners"/>
            <xs:enumeration value="JCB"/>
            <xs:enumeration value="BankCard"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:element name="CardType" nillable="true" type="tns:CardType"/>
    <xs:simpleType name="PaymentSource">
        <xs:restriction base="xs:string">
            <xs:enumeration value="Unknown"/>
            <xs:enumeration value="Desktop"/>
            <xs:enumeration value="Mobile_Android"/>
            <xs:enumeration value="Mobile_iOS"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:element name="PaymentSource" nillable="true" type="tns:PaymentSource"/>
    <xs:complexType name="TakeJobPaymentResponse">
        <xs:complexContent mixed="false">
            <xs:extension base="tns:BaseResponse">
                <xs:sequence>
                    <xs:element minOccurs="0" name="BalanceAfterPayment" type="xs:decimal"/>
                    <xs:element minOccurs="0" name="CCReferenceNumber" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="CCTransactionId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="PaymentLink" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="PendingPaymentId" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="ReceiptSummary" nillable="true" type="tns:ReceiptSummary"/>
                    <xs:element minOccurs="0" name="ShowInvoice" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="ShowReceipt" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="WasFinished" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="WasPaymentMade" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="WasPosted" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="WasReleased" type="xs:boolean"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="TakeJobPaymentResponse" nillable="true" type="tns:TakeJobPaymentResponse"/>
    <xs:complexType name="ReceiptSummary">
        <xs:sequence>
            <xs:element minOccurs="0" name="AmountPaid" type="xs:decimal"/>
            <xs:element minOccurs="0" name="AuthorizationNumber" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="Cashier" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="CheckDate" nillable="true" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="DivisionAddress" nillable="true" type="tns:CompanyAddress"/>
            <xs:element minOccurs="0" name="DivisionName" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="DivisionPhoneNumber" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="InvoiceNumber" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="LegalStatement" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="LineItems" nillable="true" type="tns:ArrayOfReceiptSummaryLineItem"/>
            <xs:element minOccurs="0" name="LogoUrl" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="MaskedCreditCardNumber" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="Message" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="PaymentMethod" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="ReceiptDate" nillable="true" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="ReceiptId" type="xs:int"/>
            <xs:element minOccurs="0" name="SubTotal" type="xs:decimal"/>
            <xs:element minOccurs="0" name="Tax" type="xs:decimal"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ReceiptSummary" nillable="true" type="tns:ReceiptSummary"/>
    <xs:complexType name="ArrayOfReceiptSummaryLineItem">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="ReceiptSummaryLineItem" nillable="true" type="tns:ReceiptSummaryLineItem"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfReceiptSummaryLineItem" nillable="true" type="tns:ArrayOfReceiptSummaryLineItem"/>
    <xs:complexType name="ReceiptSummaryLineItem">
        <xs:sequence>
            <xs:element minOccurs="0" name="AmountPaidWithoutTax" type="xs:decimal"/>
            <xs:element minOccurs="0" name="Description" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="InvoiceNumber" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="Notes" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="Quantity" nillable="true" type="xs:decimal"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ReceiptSummaryLineItem" nillable="true" type="tns:ReceiptSummaryLineItem"/>
    <xs:complexType name="IsValidCreditCardResult">
        <xs:sequence>
            <xs:element minOccurs="0" name="CardType" type="tns:CardType"/>
            <xs:element minOccurs="0" name="IsValidCreditCard" type="xs:boolean"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="IsValidCreditCardResult" nillable="true" type="tns:IsValidCreditCardResult"/>
    <xs:complexType name="QuickSummary">
        <xs:sequence>
            <xs:element minOccurs="0" name="Count" type="xs:int"/>
            <xs:element minOccurs="0" name="Total" type="xs:decimal"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="QuickSummary" nillable="true" type="tns:QuickSummary"/>
    <xs:complexType name="BasicFindInvoicesRequest">
        <xs:complexContent mixed="false">
            <xs:extension base="tns:BaseFindRequest">
                <xs:sequence>
                    <xs:element minOccurs="0" name="Search" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="SortDescriptions" nillable="true" type="tns:ArrayOfSortDescription"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="BasicFindInvoicesRequest" nillable="true" type="tns:BasicFindInvoicesRequest"/>
    <xs:complexType name="ArrayOfInvoicePreview">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="InvoicePreview" nillable="true" type="tns:InvoicePreview"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfInvoicePreview" nillable="true" type="tns:ArrayOfInvoicePreview"/>
    <xs:complexType name="InvoicePreview">
        <xs:sequence>
            <xs:element minOccurs="0" name="Amount" type="xs:decimal"/>
            <xs:element minOccurs="0" name="BillToName" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="InvoiceDate" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="InvoiceId" type="xs:int"/>
            <xs:element minOccurs="0" name="IsPaidInFull" type="xs:boolean"/>
            <xs:element minOccurs="0" name="Number" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="InvoicePreview" nillable="true" type="tns:InvoicePreview"/>
    <xs:complexType name="FindInvoicesRequest">
        <xs:complexContent mixed="false">
            <xs:extension base="tns:BaseFindRequest">
                <xs:sequence>
                    <xs:element minOccurs="0" name="BillToId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="DivisionId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="InvoiceDate" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="InvoiceDateFromDate" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="InvoiceDateToDate" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="InvoiceId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="InvoiceNumber" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="InvoiceStatus" type="tns:InvoiceStatus"/>
                    <xs:element minOccurs="0" name="JobNumber" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="PONumber" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="ReceiptId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="TicketNumber" nillable="true" type="xs:string"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="FindInvoicesRequest" nillable="true" type="tns:FindInvoicesRequest"/>
    <xs:simpleType name="InvoiceStatus">
        <xs:annotation>
            <xs:appinfo>
                <ActualType Name="short" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/"/>
            </xs:appinfo>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="All"/>
            <xs:enumeration value="Open"/>
            <xs:enumeration value="Paid"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:element name="InvoiceStatus" nillable="true" type="tns:InvoiceStatus"/>
    <xs:complexType name="InvoiceDetails">
        <xs:sequence>
            <xs:element minOccurs="0" name="AmountApplied" type="xs:decimal"/>
            <xs:element minOccurs="0" name="AmountDue" type="xs:decimal"/>
            <xs:element minOccurs="0" name="Invoice" nillable="true" type="tns:Invoice"/>
            <xs:element minOccurs="0" name="InvoiceTotal" type="xs:decimal"/>
            <xs:element minOccurs="0" name="IsPaidInFull" type="xs:boolean"/>
            <xs:element minOccurs="0" name="Jobs" nillable="true" type="tns:ArrayOfJobAndServices"/>
            <xs:element minOccurs="0" name="ReceiptCount" type="xs:int"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="InvoiceDetails" nillable="true" type="tns:InvoiceDetails"/>
    <xs:complexType name="Invoice">
        <xs:sequence>
            <xs:element minOccurs="0" name="BillToAccountId" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="BillingKey" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="CompanyId" type="xs:int"/>
            <xs:element minOccurs="0" name="InvoiceDate" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="InvoiceId" type="xs:int"/>
            <xs:element minOccurs="0" name="IsHidden" type="xs:boolean"/>
            <xs:element minOccurs="0" name="Number" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="PaidInFullDate" nillable="true" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="QBTransactoinId" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="ScheduledBillDate" nillable="true" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="TowMagicInvoiceStatus" nillable="true" type="tns:TowMagicInvoiceStatus"/>
            <xs:element minOccurs="0" name="TowMagicMessage" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="TowMagicRequestId" nillable="true" type="xs:int"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="Invoice" nillable="true" type="tns:Invoice"/>
    <xs:complexType name="ArrayOfJobAndServices">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="JobAndServices" nillable="true" type="tns:JobAndServices"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfJobAndServices" nillable="true" type="tns:ArrayOfJobAndServices"/>
    <xs:complexType name="JobAndServices">
        <xs:sequence>
            <xs:element minOccurs="0" name="Job" nillable="true" type="tns:Job"/>
            <xs:element minOccurs="0" name="JobServicesAndTotalApplied" nillable="true" type="tns:ArrayOfJobServiceAndTotalApplied"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="JobAndServices" nillable="true" type="tns:JobAndServices"/>
    <xs:complexType name="ArrayOfJobServiceAndTotalApplied">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="JobServiceAndTotalApplied" nillable="true" type="tns:JobServiceAndTotalApplied"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfJobServiceAndTotalApplied" nillable="true" type="tns:ArrayOfJobServiceAndTotalApplied"/>
    <xs:complexType name="JobServiceAndTotalApplied">
        <xs:sequence>
            <xs:element minOccurs="0" name="JobService" nillable="true" type="tns:JobService"/>
            <xs:element minOccurs="0" name="TotalApplied" nillable="true" type="xs:decimal"/>
            <xs:element minOccurs="0" name="WasVehicleReleased" type="xs:boolean"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="JobServiceAndTotalApplied" nillable="true" type="tns:JobServiceAndTotalApplied"/>
    <xs:complexType name="InvoicePaymentsSummary">
        <xs:sequence>
            <xs:element minOccurs="0" name="AmmountApplied" type="xs:decimal"/>
            <xs:element minOccurs="0" name="InvoiceId" type="xs:int"/>
            <xs:element minOccurs="0" name="ReceiptIds" nillable="true" type="q27:ArrayOfint" xmlns:q27="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="InvoicePaymentsSummary" nillable="true" type="tns:InvoicePaymentsSummary"/>
    <xs:complexType name="FindUnbilledServicesRequest">
        <xs:sequence>
            <xs:element minOccurs="0" name="BillToId" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="DateFrom" nillable="true" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="DateRangeKeyword" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="DateTo" nillable="true" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="DivisionId" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="DriverId" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="IncludeImpounded" type="xs:boolean"/>
            <xs:element minOccurs="0" name="JobNumber" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="LocalSearchTime" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="WorkOrdDtId" nillable="true" type="xs:int"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="FindUnbilledServicesRequest" nillable="true" type="tns:FindUnbilledServicesRequest"/>
    <xs:complexType name="ArrayOfUnbilledJobService">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="UnbilledJobService" nillable="true" type="tns:UnbilledJobService"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfUnbilledJobService" nillable="true" type="tns:ArrayOfUnbilledJobService"/>
    <xs:complexType name="UnbilledJobService">
        <xs:sequence>
            <xs:element minOccurs="0" name="AdditionalDriverIds" nillable="true" type="q28:ArrayOfint" xmlns:q28="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
            <xs:element minOccurs="0" name="AutoBill" nillable="true" type="xs:boolean"/>
            <xs:element minOccurs="0" name="BillToId" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="BillToName" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="ClearedTime" nillable="true" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="DivisionId" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="DivisionName" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="DriverId" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="DriverName" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="HasMCApprovedServices" type="xs:boolean"/>
            <xs:element minOccurs="0" name="HasPayments" type="xs:boolean"/>
            <xs:element minOccurs="0" name="InvoiceDate" nillable="true" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="InvoiceNumber" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="JobFlags" type="tns:JobFlags"/>
            <xs:element minOccurs="0" name="JobNumber" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="JobService" nillable="true" type="tns:JobService"/>
            <xs:element minOccurs="0" name="JobStatus" type="tns:JobStatus"/>
            <xs:element minOccurs="0" name="Odometer" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="PurchaseOrder" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="TicketNumber" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="VIN" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="UnbilledJobService" nillable="true" type="tns:UnbilledJobService"/>
    <xs:complexType name="PostJobsRequest">
        <xs:sequence>
            <xs:element minOccurs="0" name="JobsToPost" nillable="true" type="tns:ArrayOfPostJobRequest"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="PostJobsRequest" nillable="true" type="tns:PostJobsRequest"/>
    <xs:complexType name="ArrayOfPostJobRequest">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="PostJobRequest" nillable="true" type="tns:PostJobRequest"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfPostJobRequest" nillable="true" type="tns:ArrayOfPostJobRequest"/>
    <xs:complexType name="PostJobRequest">
        <xs:sequence>
            <xs:element minOccurs="0" name="InvoiceDate" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="InvoiceNumber" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="IsAutoPost" type="xs:boolean"/>
            <xs:element minOccurs="0" name="JobServicesToUpdate" nillable="true" type="tns:ArrayOfJobService"/>
            <xs:element minOccurs="0" name="Odometer" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="PurchaseOrder" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="TicketNumber" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="VIN" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="WorkOrdChgDtIdsToDelete" nillable="true" type="q29:ArrayOfint" xmlns:q29="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
            <xs:element minOccurs="0" name="WorkOrdChgDtIdsToPost" nillable="true" type="q30:ArrayOfint" xmlns:q30="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
            <xs:element minOccurs="0" name="WorkOrdDtId" type="xs:int"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="PostJobRequest" nillable="true" type="tns:PostJobRequest"/>
    <xs:complexType name="FindUnpaidServicesRequest">
        <xs:sequence>
            <xs:element minOccurs="0" name="BillToId" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="InvoiceDateFrom" nillable="true" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="InvoiceDateRangeKeyword" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="InvoiceDateTo" nillable="true" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="InvoiceNumber" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="JobNumber" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="LocalSearchTime" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="MotorClubPONumbers" nillable="true" type="q31:ArrayOfstring" xmlns:q31="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
            <xs:element minOccurs="0" name="PurchaseOrder" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="FindUnpaidServicesRequest" nillable="true" type="tns:FindUnpaidServicesRequest"/>
    <xs:complexType name="ArrayOfUnpaidJobService">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="UnpaidJobService" nillable="true" type="tns:UnpaidJobService"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfUnpaidJobService" nillable="true" type="tns:ArrayOfUnpaidJobService"/>
    <xs:complexType name="UnpaidJobService">
        <xs:sequence>
            <xs:element minOccurs="0" name="BillToId" type="xs:int"/>
            <xs:element minOccurs="0" name="InvoiceDate" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="InvoiceId" type="xs:int"/>
            <xs:element minOccurs="0" name="InvoiceNumber" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="JobNumber" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="JobService" nillable="true" type="tns:JobService"/>
            <xs:element minOccurs="0" name="PurchaseOrder" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="RemainingBalance" type="xs:decimal"/>
            <xs:element minOccurs="0" name="TicketNumber" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="TotalApplied" nillable="true" type="xs:decimal"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="UnpaidJobService" nillable="true" type="tns:UnpaidJobService"/>
    <xs:complexType name="ArrayOfDeposit">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="Deposit" nillable="true" type="tns:Deposit"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfDeposit" nillable="true" type="tns:ArrayOfDeposit"/>
    <xs:complexType name="Deposit">
        <xs:sequence>
            <xs:element minOccurs="0" name="ClosedOn" nillable="true" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="CompanyId" type="xs:int"/>
            <xs:element minOccurs="0" name="DepositDate" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="DivisionId" type="xs:int"/>
            <xs:element minOccurs="0" name="DivisionName" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="Id" type="xs:int"/>
            <xs:element minOccurs="0" name="ReferenceNumber" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="Deposit" nillable="true" type="tns:Deposit"/>
    <xs:complexType name="EnterPaymentRequest">
        <xs:complexContent mixed="false">
            <xs:extension base="tns:NotifyErrorBase">
                <xs:sequence>
                    <xs:element minOccurs="0" name="AccountId" type="xs:int"/>
                    <xs:element minOccurs="0" name="AppliedCredits" nillable="true" type="q32:ArrayOfKeyValueOfintdecimal" xmlns:q32="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
                    <xs:element minOccurs="0" name="CheckDate" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="DepositId" type="xs:int"/>
                    <xs:element minOccurs="0" name="NewDepositDate" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="NewDepositReferenceNumber" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="PaymentAmount" type="xs:decimal"/>
                    <xs:element minOccurs="0" name="PaymentMethodId" type="xs:int"/>
                    <xs:element minOccurs="0" name="ReceiptDate" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="ReferenceNumber" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="RequestId" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="ServicePayments" nillable="true" type="q33:ArrayOfKeyValueOfintdecimal" xmlns:q33="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
                    <xs:element minOccurs="0" name="UnpaidInvoices" nillable="true" type="tns:ArrayOfUnpaidInvoice"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="EnterPaymentRequest" nillable="true" type="tns:EnterPaymentRequest"/>
    <xs:complexType name="ArrayOfUnpaidInvoice">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="UnpaidInvoice" nillable="true" type="tns:UnpaidInvoice"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfUnpaidInvoice" nillable="true" type="tns:ArrayOfUnpaidInvoice"/>
    <xs:complexType name="UnpaidInvoice">
        <xs:sequence>
            <xs:element minOccurs="0" name="AmountApplied" type="xs:decimal"/>
            <xs:element minOccurs="0" name="AmountDue" type="xs:decimal"/>
            <xs:element minOccurs="0" name="InvoiceId" type="xs:int"/>
            <xs:element minOccurs="0" name="InvoiceNo" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="IsInvoicePaidInFull" type="xs:boolean"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="UnpaidInvoice" nillable="true" type="tns:UnpaidInvoice"/>
    <xs:complexType name="ArrayOfCreditMemoBalance">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="CreditMemoBalance" nillable="true" type="tns:CreditMemoBalance"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfCreditMemoBalance" nillable="true" type="tns:ArrayOfCreditMemoBalance"/>
    <xs:complexType name="CreditMemoBalance">
        <xs:sequence>
            <xs:element minOccurs="0" name="AccountId" type="xs:int"/>
            <xs:element minOccurs="0" name="Balance" type="xs:decimal"/>
            <xs:element minOccurs="0" name="Comments" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="CreditMemoId" type="xs:int"/>
            <xs:element minOccurs="0" name="CreditType" type="tns:CreditType"/>
            <xs:element minOccurs="0" name="IssuedOn" nillable="true" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="ReferenceNumber" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="CreditMemoBalance" nillable="true" type="tns:CreditMemoBalance"/>
    <xs:complexType name="FindMotorClubInvoicesRequest">
        <xs:complexContent mixed="false">
            <xs:extension base="tns:BaseFindRequest">
                <xs:sequence>
                    <xs:element minOccurs="0" name="BillToId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="DivisionId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="IncludeHidden" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="IncludePreviouslySubmitted" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="InvoiceDateKeyword" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="InvoiceFromDate" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="InvoiceId" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="InvoiceNumber" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="InvoiceToDate" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="JobNumber" nillable="true" type="xs:string"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="FindMotorClubInvoicesRequest" nillable="true" type="tns:FindMotorClubInvoicesRequest"/>
    <xs:complexType name="ArrayOfMotorClubInvoice">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="MotorClubInvoice" nillable="true" type="tns:MotorClubInvoice"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfMotorClubInvoice" nillable="true" type="tns:ArrayOfMotorClubInvoice"/>
    <xs:complexType name="MotorClubInvoice">
        <xs:sequence>
            <xs:element minOccurs="0" name="Amount" type="xs:decimal"/>
            <xs:element minOccurs="0" name="BillToName" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="BillingMCKey" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="InvoiceDate" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="InvoiceId" type="xs:int"/>
            <xs:element minOccurs="0" name="IsHidden" type="xs:boolean"/>
            <xs:element minOccurs="0" name="JobNumber" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="Number" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="Odometer" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="PurchaseOrder" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="ScheduledBillDate" nillable="true" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="TicketNumber" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="TowMagicInvoiceStatus" nillable="true" type="tns:TowMagicInvoiceStatus"/>
            <xs:element minOccurs="0" name="TowMagicMessage" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="TowMagicRequestId" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="Vin" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="WorkOrdDtId" type="xs:int"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="MotorClubInvoice" nillable="true" type="tns:MotorClubInvoice"/>
    <xs:complexType name="InvoiceVisibilityRequest">
        <xs:sequence>
            <xs:element minOccurs="0" name="InvoiceId" type="xs:int"/>
            <xs:element minOccurs="0" name="IsHidden" type="xs:boolean"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="InvoiceVisibilityRequest" nillable="true" type="tns:InvoiceVisibilityRequest"/>
    <xs:complexType name="MCHideCriticalInvoicesRequest">
        <xs:sequence>
            <xs:element minOccurs="0" name="Id" type="ser:guid"/>
            <xs:element minOccurs="0" name="InvoiceIds" nillable="true" type="q34:ArrayOfint" xmlns:q34="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="MCHideCriticalInvoicesRequest" nillable="true" type="tns:MCHideCriticalInvoicesRequest"/>
    <xs:complexType name="UndoPaymentRequest">
        <xs:sequence>
            <xs:element minOccurs="0" name="IssueCreditCardRefund" type="xs:boolean"/>
            <xs:element minOccurs="0" name="ReceiptIds" nillable="true" type="q35:ArrayOfint" xmlns:q35="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="UndoPaymentRequest" nillable="true" type="tns:UndoPaymentRequest"/>
    <xs:complexType name="UndoPaymentResult">
        <xs:sequence>
            <xs:element minOccurs="0" name="BackedOutAmount" type="xs:decimal"/>
            <xs:element minOccurs="0" name="BackedOutCount" type="xs:int"/>
            <xs:element minOccurs="0" name="PreviouslyPaidOutAmound" type="xs:decimal"/>
            <xs:element minOccurs="0" name="RefundedToCardAmount" type="xs:decimal"/>
            <xs:element minOccurs="0" name="TotalOwedToCustomer" type="xs:decimal"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="UndoPaymentResult" nillable="true" type="tns:UndoPaymentResult"/>
    <xs:complexType name="UndoReleaseVehicleRequest">
        <xs:complexContent mixed="false">
            <xs:extension base="tns:UndoPaymentRequest">
                <xs:sequence>
                    <xs:element minOccurs="0" name="WorkOrdDtId" type="xs:int"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="UndoReleaseVehicleRequest" nillable="true" type="tns:UndoReleaseVehicleRequest"/>
    <xs:complexType name="FindUnpaidServiceCommissionsRequest">
        <xs:sequence>
            <xs:element minOccurs="0" name="BillToId" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="DateRangeKeyword" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="DivisionId" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="DriverId" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="FinishedDateFrom" nillable="true" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="FinishedDateTo" nillable="true" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="IncludeUnpostedServices" type="xs:boolean"/>
            <xs:element minOccurs="0" name="JobNumber" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="LocalSearchTime" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="OnlyCommissionableServices" type="xs:boolean"/>
            <xs:element minOccurs="0" name="OnlyServicesPaidInFull" type="xs:boolean"/>
            <xs:element minOccurs="0" name="UTCOffsetMinutes" type="xs:int"/>
            <xs:element minOccurs="0" name="WorkOrdDtId" nillable="true" type="xs:int"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="FindUnpaidServiceCommissionsRequest" nillable="true" type="tns:FindUnpaidServiceCommissionsRequest"/>
    <xs:complexType name="ArrayOfServiceCommission">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="ServiceCommission" nillable="true" type="tns:ServiceCommission"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfServiceCommission" nillable="true" type="tns:ArrayOfServiceCommission"/>
    <xs:complexType name="ServiceCommission">
        <xs:sequence>
            <xs:element minOccurs="0" name="AssignedTime" nillable="true" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="BillToId" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="BillToName" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="CalculatedCommission" nillable="true" type="xs:decimal"/>
            <xs:element minOccurs="0" name="CalculatedServiceTotal" nillable="true" type="xs:decimal"/>
            <xs:element minOccurs="0" name="ClearedTime" nillable="true" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="CommissionPaidOn" nillable="true" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="DivisionId" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="DriverId" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="DriverName" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="ImpoundReleaseTime" nillable="true" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="InvoiceDate" nillable="true" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="InvoiceId" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="InvoiceNumber" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="JobNumber" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="PurchaseOrder" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="RequestedById" nillable="true" type="xs:int"/>
            <xs:element minOccurs="0" name="ServiceCommissionRate" nillable="true" type="xs:decimal"/>
            <xs:element minOccurs="0" name="ServiceDescription" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="ServiceRemarks" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="Status" type="tns:JobStatus"/>
            <xs:element minOccurs="0" name="TicketNumber" nillable="true" type="xs:string"/>
            <xs:element minOccurs="0" name="WorkOrdChgDtId" type="xs:int"/>
            <xs:element minOccurs="0" name="WorkOrdDtId" type="xs:int"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ServiceCommission" nillable="true" type="tns:ServiceCommission"/>
    <xs:complexType name="SaveCommissionsRequest">
        <xs:sequence>
            <xs:element minOccurs="0" name="PaidOn" nillable="true" type="xs:dateTime"/>
            <xs:element minOccurs="0" name="ServiceCommissionGroups" nillable="true" type="tns:ArrayOfArrayOfServiceCommissionAmount"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="SaveCommissionsRequest" nillable="true" type="tns:SaveCommissionsRequest"/>
    <xs:complexType name="ArrayOfArrayOfServiceCommissionAmount">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="ArrayOfServiceCommissionAmount" nillable="true" type="tns:ArrayOfServiceCommissionAmount"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfArrayOfServiceCommissionAmount" nillable="true" type="tns:ArrayOfArrayOfServiceCommissionAmount"/>
    <xs:complexType name="ArrayOfServiceCommissionAmount">
        <xs:sequence>
            <xs:element minOccurs="0" maxOccurs="unbounded" name="ServiceCommissionAmount" nillable="true" type="tns:ServiceCommissionAmount"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ArrayOfServiceCommissionAmount" nillable="true" type="tns:ArrayOfServiceCommissionAmount"/>
    <xs:complexType name="ServiceCommissionAmount">
        <xs:sequence>
            <xs:element minOccurs="0" name="AppliedAmount" type="xs:decimal"/>
            <xs:element minOccurs="0" name="DriverId" type="xs:int"/>
            <xs:element minOccurs="0" name="WorkOrdChgDtId" type="xs:int"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ServiceCommissionAmount" nillable="true" type="tns:ServiceCommissionAmount"/>
</xs:schema>