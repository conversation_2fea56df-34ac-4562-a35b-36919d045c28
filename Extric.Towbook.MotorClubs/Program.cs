using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Collections.Specialized;
using System.Data.SqlClient;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Mail;
using System.ServiceModel.Channels;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
//using System.Web.Services.Protocols;
using Agero;
using Agero.Types;
using Ajax.Areas.Dispatch.Controllers;
using Braintree;
using Extric.Towbook.Accounts;
using Extric.Towbook.API.Controllers;
using Extric.Towbook.API.Integration.MotorClubs.Towbook.Model;
using Extric.Towbook.Chat;
using Extric.Towbook.Dispatch;
using Extric.Towbook.Gps;
using Extric.Towbook.Impounds;
using Extric.Towbook.Integration;
using Extric.Towbook.Integration.MotorClubs;
using Extric.Towbook.Integration.MotorClubs.Billing.Agero;
using Extric.Towbook.Integration.MotorClubs.Billing.Allstate;
using Extric.Towbook.Integration.MotorClubs.Billing.Geico;
using Extric.Towbook.Integration.MotorClubs.Queue;
using Extric.Towbook.Integration.MotorClubs.Services;
using Extric.Towbook.Integrations.Email;
using Extric.Towbook.Integrations.MotorClubs.Agero;
using Extric.Towbook.Integrations.MotorClubs.Allstate;
using Extric.Towbook.Integrations.MotorClubs.Allstate.Proxy;
using Extric.Towbook.Integrations.MotorClubs.Issc;
using Extric.Towbook.Integrations.MotorClubs.Quest.QuestService;
using Extric.Towbook.Integrations.Quickbooks;
using Extric.Towbook.Services.MotorClubDispatchingService;
using Extric.Towbook.Utility;
using Extric.Towbook.Vehicle;
using Extric.Towbook.WebShared;
using HtmlAgilityPack;
using Newtonsoft.Json;
using NLog;
using OonAgeroClient;
using static Extric.Towbook.MotorClubs.Helpers.DigitalConnectionHelpers;
using QBX = Extric.Towbook.Agent.QuickBooks;
using GH = Octokit;
using fleetnet = Extric.Towbook.Integrations.MotorClubs.Fleetnet;
using static Extric.Towbook.MotorClubs.Helpers.KeyValueHelpers;
using static Extric.Towbook.MotorClubs.Helpers.DirectBillingHelpers;
using static Extric.Towbook.MotorClubs.Helpers.EmailParserHelpers;
using Microsoft.Azure.Cosmos;
using Extric.Towbook.API.Models.Calls;
using Extric.Towbook.Dispatch.CallModels;
using Async = System.Threading.Tasks;
using Extric.Towbook.Integrations.MotorClubs.Honk;
using GeoCoordinatePortable;
using SkiaSharp;
using Extric.Towbook.Integrations.MotorClubs.Swoop;
using Extric.Towbook.AutoDataDirect;
using System.ServiceModel;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Extric.Towbook.MotorClubs.Helpers;
using System.Net.Http;
using Extric.Towbook.Configuration;

namespace Extric.Towbook.MotorClubs
{
    namespace SalesforceTest
    {
        using System.Dynamic;
        using Salesforce.Common;
        using Salesforce.Force;


        public class SalesforceImporter
        {
            private static readonly string SecurityToken = "e6nDiz054m2ZHJylRg5Ok9ip";
            private static readonly string ConsumerKey = "3MVG9i1HRpGLXp.rT4udGU18voiU_W9BnQo._gwiM6kuxBXdT72F4.adTBnRuBndUeiwIwGYipp5YyV._trUn";
            private static readonly string ConsumerSecret = "5097756323431763177";
            private static readonly string Username = "<EMAIL>";
            private static readonly string Password = "Zarat8989!" + SecurityToken;
            private static readonly string IsSandboxUser = "true";

            public static async System.Threading.Tasks.Task ImportAccountsAsync()
            {
                System.Net.ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;
                var auth = new AuthenticationClient();

                // Authenticate with Salesforce
                Console.WriteLine("Authenticating with Salesforce");
                var url = "https://login.salesforce.com/services/oauth2/token";
                await auth.UsernamePasswordAsync(ConsumerKey, ConsumerSecret, Username, Password, url);
                Console.WriteLine("Connected to Salesforce");

                var client = new ForceClient(auth.InstanceUrl, auth.AccessToken, auth.ApiVersion);

                // retrieve all accounts
                Console.WriteLine("Get Accounts");

                const string qry = "SELECT ID, Name FROM Account";
                var accts = new List<Account>();
                var results = await client.QueryAsync<Account>(qry);
                var totalSize = results.TotalSize;

                Console.WriteLine("Queried " + totalSize + " records.");

                accts.AddRange(results.Records);
                var nextRecordsUrl = results.NextRecordsUrl;

                if (!string.IsNullOrEmpty(nextRecordsUrl))
                {
                    Console.WriteLine("Found nextRecordsUrl.");

                    while (true)
                    {
                        var continuationResults = await client.QueryContinuationAsync<Account>(nextRecordsUrl);
                        totalSize = continuationResults.TotalSize;
                        Console.WriteLine("Queried an additional " + totalSize + " records.");

                        accts.AddRange(continuationResults.Records);
                        if (string.IsNullOrEmpty(continuationResults.NextRecordsUrl)) break;

                        //pass nextRecordsUrl back to client.QueryAsync to request next set of records
                        nextRecordsUrl = continuationResults.NextRecordsUrl;
                    }
                }
                Console.WriteLine("Retrieved accounts = " + accts.Count() + ", expected size = " + totalSize);

                // Create a sample record
                Console.WriteLine("Creating test record.");
                var account = new Account { Name = "Test Account" };
                var createAccountResponse = await client.CreateAsync(Account.SObjectTypeName, account);
                account.Id = createAccountResponse.Id;
                if (account.Id == null)
                {
                    Console.WriteLine("Failed to create test record.");
                    return;
                }

                Console.WriteLine("Successfully created test record.");

                // Update the sample record
                // Shows that annonymous types can be used as well
                Console.WriteLine("Updating test record.");
                var success = await client.UpdateAsync(Account.SObjectTypeName, account.Id, new { Name = "Test Update" });
                if (!string.IsNullOrEmpty(success.Errors.ToString()))
                {
                    Console.WriteLine("Failed to update test record!");
                    return;
                }

                Console.WriteLine("Successfully updated the record.");

                // Retrieve the sample record
                // How to retrieve a single record if the id is known
                Console.WriteLine("Retrieving the record by ID.");
                account = await client.QueryByIdAsync<Account>(Account.SObjectTypeName, account.Id);
                if (account == null)
                {
                    Console.WriteLine("Failed to retrieve the record by ID!");
                    return;
                }

                Console.WriteLine("Retrieved the record by ID.");

                // Query for record by name
                Console.WriteLine("Querying the record by name.");
                var accounts = await client.QueryAsync<Account>("SELECT ID, Name FROM Account WHERE Name = '" + account.Name + "'");
                account = accounts.Records.FirstOrDefault();
                if (account == null)
                {
                    Console.WriteLine("Failed to retrieve account by query!");
                    return;
                }

                Console.WriteLine("Retrieved the record by name.");

                // Delete account
                Console.WriteLine("Deleting the record by ID.");
                var deleted = await client.DeleteAsync(Account.SObjectTypeName, account.Id);
                if (!deleted)
                {
                    Console.WriteLine("Failed to delete the record by ID!");
                    return;
                }
                Console.WriteLine("Deleted the record by ID.");

                // Selecting multiple accounts into a dynamic
                Console.WriteLine("Querying multiple records.");
                var dynamicAccounts = await client.QueryAsync<dynamic>("SELECT ID, Name,Phone FROM Account LIMIT 10");
                foreach (dynamic acct in dynamicAccounts.Records)
                {
                    Console.WriteLine("Account - " + JsonExtensions.ToJson(acct));
                }

                // Creating parent - child records using a Dynamic
                Console.WriteLine("Creating a parent record (Account)");
                foreach (var cc in (await Company.Company.GetAllAsync()))
                {
                    dynamic a = new ExpandoObject();
                    a.Name = cc.Name;
                    a.Phone = cc.Phone;
                    a.DriversCount__c = cc.DriverCount;
                    a.CallCount30__c = cc.DriverCount;
                    a.TruckCount__c = cc.TruckCount;
                    a.RateItemCount__c = cc.RateItemCount;
                    a.LastLoginWeb__c = cc.LastLogin;

                    var createParentAccountResponse = await client.CreateAsync("Account", a);
                    a.Id = createParentAccountResponse.Id;
                    if (a.Id == null)
                    {
                        Console.WriteLine("Failed to create parent record.");
                        return;
                    }
                    else
                    {
                        Console.WriteLine("Created " + a.Id);
                    }

                    foreach (var user in cc.Users.Where(o => o.Type == User.TypeEnum.Manager))
                    {
                        Console.WriteLine("Creating a child record (Contact)");
                        dynamic c = new ExpandoObject();
                        c.FirstName = user.FullName;
                        c.LastName = ".";
                        c.AccountId = a.Id;
                        c.Email = user.Email;
                        c.Username__c = user.Username;

                        var createContactResponse = await client.CreateAsync("Contact", c);
                        c.Id = createContactResponse.Id;

                        if (c.Id == null)
                        {
                            Console.WriteLine("Failed to create child record.");
                            return;
                        }
                    }
                }

                Console.WriteLine("Deleting parent and child");
                Console.ReadLine();
                return;
                // Delete account (also deletes contact)
                Console.WriteLine("Deleting the Account by Id.");
                //deleted = await client.DeleteAsync(Account.SObjectTypeName, a.Id);
                if (!deleted)
                {
                    Console.WriteLine("Failed to delete the record by ID!");
                    return;
                }
                Console.WriteLine("Deleted the Account and Contact.");

            }

            private class Account
            {
                public const String SObjectTypeName = "Account";

                public String Id { get; set; }
                public String Name { get; set; }
            }
        }
    }
    public static class BraintreeExtensions
    {
        public static Customer GetCustomerByIdOrNull(this BraintreeGateway gateway, string id)
        {
            try
            {
                return gateway.Customer.Find(id);
            }
            catch (NotFoundException)
            {
                return null;
            }
        }
    }
    public static class TestStringExtensions
    {
        public static bool ContainsMoreThan(this string text, int count, string value, StringComparison comparison)
        {
            if (text == null) throw new ArgumentNullException("text");
            if (string.IsNullOrEmpty(value))
                return text != "";

            int contains = 0;
            int index = 0;

            while ((index = text.IndexOf(value, index, text.Length - index, comparison)) != -1)
            {
                if (++contains > count)
                    return true;
                index++;
            }
            return false;
        }

    }

    partial class Program
    {
        private static int CreateDispatch()
        {
            var t = new DispatchCreatorClient();
            var dc = t.AutomatedDispatch(new DispatchCreatorClient.AutomatedDispatchRequest()
            {
                VendorID = "73024",
                DisablementLocationAddress = new DispatchCreatorClient.AutomatedDispatchRequestAddress()
                {
                    Address = "1161 S. Carney",
                    ZipCode = "48079",
                    City = "Saint Clair",
                    CrossStreet = "Clinton",
                    Latitude = 0.5,
                    Longitude = 1.5,
                    StateAbbreviation = "MI"
                },
                CustomerPhoneNumber = "**********",
                ServiceType = "Tow",
                PaymentMethod = "CreditCard"
            });

            return dc.dispatchInfo.dispatchRequestNumber;
        }

        private static async Task PushInvoices(int companyId, DateTime startDate, DateTime endDate, bool pushOnlyIfThereArePayments, int[] ignoreAccounts = null)
        {
            var ips = (await InvoicePayment.GetByCompanyAsync(companyId, startDate, endDate)).Select(o => Invoice.GetById(o.InvoiceId).DispatchEntry);

            IQuickbooksConnector objQBConnecter = await QuickbooksUtility.GetConnector(companyId);

            IEnumerable<Entry> entities = ips;

            if (!pushOnlyIfThereArePayments)
            {
                entities = Entry.GetByCompany(Company.Company.GetById(companyId), null,
                    startDate,
                    endDate).Union(ips).Where(o => o != null && (o.Status.Id == 5) && (o.Impound == false || (o.Impound && Impound.GetByDispatchEntry(o)?.ReleaseDate != null)));

            }

            if (ignoreAccounts != null)
                entities = entities.Where(o => !ignoreAccounts.Contains(o.AccountId));

            entities = entities.Where(o => o.InvoiceTotal > 0);


            Console.WriteLine(String.Join(",", entities.Where(o => o != null).Select(o => o.Id)));

            var agentSession = await Agent.Session.GetByCompanyIdAsync(companyId);

            Agent.Sync.QuickbooksSyncService qss = null;
            if (agentSession != null)
                qss = Agent.Sync.QuickbooksSyncService.Get(agentSession.Id);
            foreach (var e in entities)
            {
                if (e == null)
                    continue;

                Console.WriteLine("Call # " + e.CallNumber + " ... about to push");
                try
                {
                    if (agentSession != null)
                    {
                        var r = qss.Send(e).Result;
                        Console.WriteLine("added " + e.Id + " / call # " + e.CallNumber + " to sync queue for agentSessionId " + agentSession.Id);
                    }
                    else
                    {
                        AsyncHelper.RunSync(() => InvoicePaymentHelper.SendEntryToQuickBooksAndPayInvoice(e, Provider.QuickBooks));
                        Console.WriteLine("pushed " + e.Id + " / call # " + e.CallNumber);
                    }
                }
                catch (Exception xe)
                {
                    Console.WriteLine("Error pushing #" + e.CallNumber + ":" + xe.Message);
                }
            }
            return;
        }

        #region QB Cleanup for PAtricks

        public class QbLink
        {
            public int CallNumber { get; set; }
            public string DocNumber { get; set; }
            public string KeyValue { get; set; }

            public int DispatchEntryid { get; set; }
        }

        public static IEnumerable<QbLink> GetExistingLinks(int companyId)
        {
            return SqlMapper.Query<QbLink>(
                "select distinct CallNumber, DocNumber, KeyValue, DispatchEntryId From integration.vwQuickBooksCalls where companyid = @Id",
                new { @Id = companyId });
        }

        public class QbInvoice
        {
            public string Id { get; set; }

            public string DocNumber { get; set; }
        }

        public static async Async.Task SafelyUpdateLinksForQb(int companyId)
        {
            var links = GetExistingLinks(companyId);

            var qbcn = await QuickbooksUtility.GetConnector(companyId);

            var invList = qbcn.GetInvoices();

            var ret = new Collection<QbInvoice>();

            foreach (var x in invList)
            {
                ret.Add(new QbInvoice
                {
                    DocNumber = x.Header.InvoiceNumber,
                    Id = x.Id
                });
            }

            foreach (var e in links)
            {
                var cl = ret.Where(o => o.DocNumber == e.DocNumber).ToCollection();

                if (!cl.Any())
                {
                    //Console.WriteLine(e.DocNumber + ": doesn't exist in QB result set!");
                    continue;
                }
                else if (cl.Count == 1)
                {
                    if (cl.First().Id != e.KeyValue)
                    {
                        //Console.WriteLine(e.DocNumber + ": ID's don't match. Needs update! QB=" + cl.First().Id + " vs Link=" + e.KeyValue);
                        Console.WriteLine("UPDATE Integration.ProviderDispatchEntryKeyValues SET Value='" + cl.First().Id + "' WHERE Value='" + e.KeyValue + "'; -- " + e.DocNumber + ": UPDATE to " + cl.First().Id + "");
                    }
                }
                else if (cl.Count > 1)
                {
                    Console.WriteLine(e.DocNumber + ": Exists multiple times..." + cl.Select(o => o.Id).ToJson());
                }
            }

        }


        #endregion

        static string GetIPAddress()
        {
            String strHostName = string.Empty;
            // Getting Ip address of local machine...
            // First get the host name of local machine.
            strHostName = Dns.GetHostName();
            Console.WriteLine("Local Machine's Host Name: " + strHostName);
            // Then using host name, get the IP address list..
            IPHostEntry ipEntry = Dns.GetHostEntry(strHostName);
            IPAddress[] addr = ipEntry.AddressList;

            if (addr.Length > 1)
                return addr[2].ToString();

            return string.Empty;
        }
        static void tst()
        {

            var companyId = 2;
            var company = Company.Company.GetById(companyId);
            var accounts = Extric.Towbook.Accounts.Account.GetByCompany(company, Accounts.AccountType.MotorClub)
                .Union(Extric.Towbook.Accounts.Account.GetByCompany(company, Accounts.AccountType.InsuranceCompany))
                .Union(Extric.Towbook.Accounts.Account.GetByCompany(company, Accounts.AccountType.Fleet))
                .ToCollection();


            var account = MotorClubMapper.DetermineAccount(accounts.Where(o => o.Company.ToLowerInvariant().Contains("agero") ||
                    o.Company.ToLowerInvariant().Contains("ccas") ||
                    o.Company.ToLowerInvariant().Contains("cross country")), "100453", "ASA ROAD SERVICE (WORC)");


        }


        static void GetAllstatePO()
        {
            try
            {
                Console.WriteLine("Logging into Allstate");

                var logInfo = new LogInfo("", 0, true);
                var userName = "wickedtow";
                var password = "Wicked123";
                var providerId = "TX1616954";
                var allstate = AllStateConnection.Login(logInfo, userName, password, providerId);

                try
                {
                    var dispatchId = ********;
                    var html = new HtmlDocument();

                    try
                    {
                        var po = allstate.GetPurchaseOrder(logInfo, dispatchId, false, out html);
                    }
                    catch
                    {
                        Console.WriteLine($"Error finding PO with dispatchId: {dispatchId}");
                    }
                }
                finally
                {
                    allstate.ReleaseLock(logInfo);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
            }
            finally
            {
                Console.WriteLine("Finished, please press a key");
                Console.ReadKey();
            }
        }

        static void FindAllstatePOs()
        {
            try
            {
                Console.WriteLine("Logging into Allstate");

                var logInfo = new LogInfo("", 0, true);
                var userName = "kitsaptowing";
                var password = "maddog";
                var providerId = "WA9600127";
                var allstate = AllStateConnection.Login(logInfo, userName, password, providerId);

                try
                {
                    var poNumbers = new List<int>()
                {
                    **********,
                    //402624955,
                    //402680856,
                    //402768802,
                    //402784512,
                    //**********,
                    //**********,
                    //**********,
                    //**********,
                    //**********,
                    //**********,
                    //**********,
                    //**********,
                    //**********,
                    //**********,
                    //**********,
                    //**********,
                    //**********,
                    //**********,
                    //**********,
                    //**********,
                    //**********,
                    //**********
                };

                    var html = new HtmlDocument();
                    foreach (var poNumber in poNumbers)
                    {
                        try
                        {
                            var po = allstate.SearchPurchaseOrder(logInfo, poNumber.ToString(), out html);

                            if (po != null)
                            {
                                Console.WriteLine($"{poNumber}    {po.ServiceDate:MM/dd/yyyy}    {po.MembershipNumber}    {po.ProviderInvoice}    {po.Payments.Sum(p => p.PaidAmount):N}    {po.Status}");
                            }
                            else
                            {
                                Console.WriteLine($"{poNumber}                    PO does not exist");
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine(ex.ToString());
                        }
                    }
                }
                finally
                {
                    allstate.ReleaseLock(logInfo);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
            }
            finally
            {
                Console.WriteLine("Finished, please press a key");
                Console.ReadKey();
            }
        }

        static void FindAgeroPOs()
        {
            try
            {
                Console.WriteLine("Logging into Agero");

                var logInfo = new LogInfo("", 0, true);
                var userName = "blairs towing";
                var password = "1500241";
                var agero = AgeroConnection.Login(userName, password);

                var poNumbers = new List<string>()
                {
                    "388598989",
                    "315369789",
                    "723137062",
                    "596832014",
                    "404126617",
                    "529717807",
                    "489925068",
                    "503333985",
                    "946452920",
                    "903434713",
                    "370856133",
                };

                var sb = new StringBuilder();
                var html = new HtmlDocument();
                foreach (var poNumber in poNumbers)
                {
                    try
                    {
                        var fi = agero.FindInvoice(poNumber);

                        var line = "";
                        if (fi != null)
                        {
                            line = $"{poNumber}    {fi.PurchaseOrderNumber}    {fi.VendorId}    {fi.YourInvoiceNumber}    {fi.ServiceDate:MM/dd/yyyy}    {fi.SubmitDate:MM/dd/yyyy}    {fi.Status}    {fi.Amount}    {fi.CheckNumber}    {fi.CheckDate:MM/dd/yyyy}";
                        }
                        else
                        {
                            line = $"{poNumber}                        PO does not exist";
                        }

                        Console.WriteLine(line);
                        sb.AppendLine(line);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine(ex.ToString());
                        sb.AppendLine(ex.ToString().Replace("\r\n", "    ").Replace("\n", "    "));
                    }
                }

                File.WriteAllText(@"D:\Temp\agero-find-invoices.txt", sb.ToString());
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
            }
            finally
            {
                Console.WriteLine("Finished, please press a key");
                Console.ReadKey();
            }
        }

        static void SyncRoadAmericaPayments(int accountId, string username, string password, DateTime startDate, DateTime endDate)
        {
            try
            {
                MotorClubPaymentImportService.RoadAmericaPaymentImporter.SyncRoadAmericaPayments(null, accountId, username, password, startDate, endDate);
            }
            catch (Exception e)
            {
                throw new Exception($"{accountId}: Road America : Error importing payments for username: {username}", e);
            }
            finally
            {
                Console.WriteLine("Finished, please press a key");
                Console.ReadKey();
            }
        }

        static void SyncAllstatePayments(int accountId, string username, string password, string providerId, DateTime startDate, DateTime endDate)
        {
            try
            {
                MotorClubPaymentImportService.AllstatePaymentImporter.SyncAllstatePayments(new LogInfo("x", 10000, false, "allstate"), accountId, username, password, providerId, startDate, endDate);
            }
            catch (Exception e)
            {
                Console.WriteLine($"{accountId}: Allstate : Error importing payments for username: {username}", e);
            }
            finally
            {
                Console.WriteLine("Finished, please press a key");
                Console.ReadKey();
            }
        }

        static async Async.Task SyncGeicoPayments(int accountId, string username, string password)
        {
            try
            {
                await MotorClubPaymentImportService.GeicoPaymentImporter.SyncGeicoPayments(null, accountId, username, password);
            }
            catch (Exception e)
            {
                throw new Exception($"{accountId}: Geico : Error importing payments for username: {username}", e);
            }
            finally
            {
                Console.WriteLine("Finished, please press a key");
                Console.ReadKey();
            }
        }

        static void SyncAgeroPayments(int accountId, string username, string password, string[] manualCheckNumbers = null)
        {
            try
            {
                MotorClubPaymentImportService.AgeroPaymentImporter.SyncAgeroPayments(null, accountId, username, password, manualCheckNumbers);
            }
            catch (Exception e)
            {
                throw new Exception($"{accountId}: Agero : Error importing payments for username: {username}", e);
            }
            finally
            {
                Console.WriteLine("Finished, please press a key");
                Console.ReadKey();
            }
        }

        private static void TestUrlShortener()
        {

            try
            {
                Console.WriteLine("Logging into Allstate");

                var logInfo = new LogInfo("UrlShortener-Test", 123456, false);
                var userName = "wickedtow";
                var password = "Wicked123";
                var providerId = "TX1616954";
                var allstate = AllStateConnection.Login(logInfo, userName, password, providerId);

                try
                {
                    try
                    {
                        var url = @"https://www.google.com/maps/dir/'39.852165,-104.889778'/'39.867324,-105.039383'/'39.914615,-105.002296'/am=t";
                        var shortened = ""; // allstate.ShortenUrl(logInfo, url);  // Change this method to public to test
                        Console.WriteLine($"Long URL: {url}");
                        Console.WriteLine($"Short URL: {shortened}");

                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error shortening:  {ex}");
                    }
                }
                finally
                {
                    allstate.ReleaseLock(logInfo);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
            }
            finally
            {
                Console.WriteLine("Finished, please press a key");
                Console.ReadKey();
            }
        }

        private static void GeicoPaymentReconcile(string username, string password, DateTime startDate, int weeks)
        {
            try
            {
                GeicoConnection geico = null;
                var logInfo = new LogInfo("", 0, false);

                try
                {
                    Console.WriteLine($"Geico : Logging in...");

                    geico = GeicoConnection.Login(logInfo, username, password);
                }
                catch (Exception)
                {
                    return;
                }

                var sb = new StringBuilder();
                for (int i = 0; i < weeks; i++)
                {
                    var endDate = startDate.AddDays(7);
                    Console.WriteLine($"Geico : Fetching payment list for {startDate:MM/dd/yyyy} - {endDate:MM/dd/yyyy}");

                    var paymentList = geico.GetPayments(logInfo, PaymentSearchBy.DispatchDate, startDate, endDate);

                    foreach (var payment in paymentList.Results)
                    {
                        sb.AppendLine($"{payment.PurchaseOrderNumber}\t{payment.ReferenceNumber}\t{payment.DispatchTotal:N}\t{payment.PaymentLine.ToJson()}");
                    }

                    startDate = startDate.AddDays(7);
                }
                File.WriteAllText(@"D:\Temp\geico-payments.txt", sb.ToString());
            }
            catch (Exception e)
            {
                throw new Exception($"Geico : Error importing payments for username: {username}", e);
            }
        }

        static void TestAllstatePush(string authNum)
        {
            var args = new Integrations.MotorClubs.Allstate.PushGpsTrackingService.pushGPSRequest();

            string username = "Advanced";
            string password = "Auto123";

            args.channelID = "CH1052";
            args.partnerIdentifier = "PR1078";
            args.eta = 45;
            args.eventTime = Convert.ToDateTime("2015-10-06T15:10:23.0556481-04:00");
            args.eventType = "ON_SCENE";
            args.lastUpdatedInterval = 0;
            args.longitude = 0;
            args.latitude = 0;
            args.reasonCode = "";
            args.responseID = "";
            args.authorizationNumber = "1029544255";
            args.sentTime = Convert.ToDateTime("2015-10-06T15:10:23.0556481-04:00");
            args.additionalInfo = "";

            ProxyTSPClient soapClient = new ProxyTSPClient();
            var cred = new Integrations.MotorClubs.Allstate.PushGpsTrackingService.receiveCredentials();

            cred.userName = username;
            cred.password = password;

            var msg = new Extric.Towbook.Integrations.MotorClubs.Allstate.PushGpsTrackingService.push();
            msg.arg0 = args;

            var ws = new Extric.Towbook.Integrations.MotorClubs.Allstate.PushGpsTrackingService.PushGPSTrackingWSClient();
            var z = ws.push(cred, msg);

            //            Console.WriteLine(z.status);
            return;


        }

        static void IsscRegister()
        {


            var providers = new int[] { 211, 205 };



            var zgc = new IsscRestClient(IsscConfig.GetByEnvironmentType(EnvironmentType.Live));
            //zgc.RegisterProvider("RP", 113693, 2055343, "SC1002J");

            var issc = new IsscRestClient(IsscConfig.GetByEnvironmentType(EnvironmentType.Live));




            List<int> providersToLogin = new List<int>();
            if (false)
            {
                foreach (var x in providers)
                {
                    try
                    {
                        issc.Logout(x);
                        providersToLogin.Add(x);
                    }
                    catch (Exception xz)
                    {
                        Console.WriteLine(x + ":" + xz.Message);
                    }
                }
                Console.ReadLine();

            }

            foreach (var x in providers)
            {
                try {
                    issc.Login(x);
                }
                catch (Exception e)
                {
                    Console.WriteLine(x + ":" + e.Message);
                }
                Thread.Sleep(75);
            }

            return;


            issc.RegisterProvider("GCO", 5540, 90117, "**********", "46-2524025");

            issc.RegisterProvider("GCO", 5684, 92562, "**********", "41-2197759");
            issc.RegisterProvider("GCO", 5684, 92562, "**********", "41-2197759");
            issc.RegisterProvider("GCO", 5684, 92562, "**********", "41-2197759");
            issc.RegisterProvider("GCO", 5684, 92562, "**********", "41-2197759");

            issc.RegisterProvider("GCO", 2444, 30934, "*********", "26-2797615");
            issc.RegisterProvider("GCOAPI", 2444, 30934, "*********", "26-2797615", "ab: 1740411");
            issc.RegisterProvider("GCO", 2444, 30934, "MD13219838", "26-2797615");
            issc.RegisterProvider("GCO", 2444, 30934, "*********", "26-2797615");

            issc.RegisterProvider("GCO", 4654, 86442, "CO15289103", "20-5772105");



            return;

            var c = new IsscRestClient(IsscConfig.GetByEnvironmentType(EnvironmentType.Live));

            c.Login(240);
            c.Login(241);
            c.Login(242);
            c.Login(243);
            c.Login(244);



            return;

            c.Login(206);
            c.Login(207);
            c.Login(211);
            c.Login(212);
            c.Login(213);
            c.Login(214);
            c.Login(215);
            c.Login(216);
            c.Login(217);
            c.Login(218);
            c.Login(219);
            c.Login(220);
            c.Login(221);
            c.Login(222);
            c.Login(223);
            c.Login(224);
            c.Login(225);
            c.Login(226);
            c.Login(227);
            c.Login(228);
            c.Login(229);
            c.Login(230);
            c.Login(231);
            c.Login(232);
            c.Login(233);
            c.Login(234);
            c.Login(235);
            c.Login(236);
            c.Login(237);
            c.Login(238);
            c.Login(239);

            return;

            zgc.Login(225);

            // zgc.Login(218);
            return;


            zgc.RegisterProvider("GCO", 4883, 73884, "MA11186959", "45-2894409", null);
            zgc.RegisterProvider("GCOAPI", 4883, 73884, "MA11186959", "45-2894409", "ab:1709299");

            return;


            zgc.Login(212);
            return;


            zgc.RegisterProvider("GCO", 5711, 93250, "MD13222924", "46-1645091");
            zgc.RegisterProvider("GCO", 5711, 93250, "MD14244832", "46-1645091");
            zgc.RegisterProvider("GCOAPI", 5711, 93250, "MD13222924", "46-1645091", "ab:1714303");

            zgc.RegisterProvider("GCO", 5802, 94385, "CA0612748", "72-1587145");
            zgc.RegisterProvider("GCO", 5802, 94385, "CA0916076", "72-1587145");
            zgc.RegisterProvider("GCO", 5802, 94385, "CA12205995", "72-1587145");
            zgc.RegisterProvider("GCO", 5802, 94385, "CA12205999", "72-1587145");
            zgc.RegisterProvider("GCOAPI", 5802, 94385, "CA0612748", "72-1587145", "ab:1738983");

            //zgc.RegisterProvider("GCO", 5711, 93250, "MD13222924", "30-609418");

            zgc.RegisterProvider("GCO", 2264, 24912, "GA9839018", "58-2612522");
            zgc.RegisterProvider("GCO", 2264, 24912, "GA09162883", "58-2612522");
            zgc.RegisterProvider("GCO", 2264, 24912, "GA12216203", "58-2612522");
            zgc.RegisterProvider("GCO", 2264, 24912, "GA12216204", "58-2612522");

            zgc.RegisterProvider("GCOAPI", 2264, 24912, "GA9839018", "58-2612522", "ab:1726035");











            return;



            //        zgc.RegisterProvider("GCO", 5157, 86486, "SC14267174", "46-2439008");
            ///          zgc.RegisterProvider("GCO", 4912, 74100, "PA15286426", "23-2487166");



            zgc.RegisterProvider("GCO", 5039, 79605, "TX15286010", "46-2633703");
            zgc.RegisterProvider("GCOAPI", 5039, 79605, "TX15286010", "46-2633703", "ab:1868612");
            zgc.RegisterProvider("GCO", 5304, 86353, "FL9704519", "65-0090862");
            zgc.RegisterProvider("GCO", 5304, 86353, "FL0061554", "65-0090862");
            zgc.RegisterProvider("GCOAPI", 5304, 86353, "FL9704519", "65-0090862", "ab:1736735");

            zgc.Login(218);
            zgc.Login(217);
            zgc.Login(216);
            zgc.Login(215);
            zgc.Login(214);

            return;


            /*zgc.RegisterProvider("GCO", 5157, 86486, "SC14267174", "46-2439008");m
            zgc.RegisterProvider("GCO", 5157, 86486, "SC14267193", "46-2439008");
            zgc.RegisterProvider("GCOAPI", 5157, 86486, "SC14267174", "46-2439008", "ab:1832570");
            zgc.RegisterProvider("GCOAPI", 4912, 74100, "PA15286426", "23-2487166", "ab:1871764"); */

            zgc.Login(213);
            zgc.Login(212);
            zgc.Login(211);
            zgc.Login(207);
            zgc.Login(206);
            zgc.Login(205);

            /*
            zgc.Login(194);
            zgc.Login(195);
            zgc.Login(196);
            zgc.Login(197);
            zgc.Login(198);
            zgc.Login(199);
            zgc.Login(200);
            */
            //zgc.RegisterProvider("GCO", 5477, 88713, "MD15280311", "45-4323745");

            return;


            return;

            /*
            var gc = new IsscRestClient(IsscConfig.GetByEnvironmentType(EnvironmentType.Live));


            gc.Connect("https://api.towbook.com/receivers/issc/", "towbook", "k638WrYceu3a");

            return;

            SyncAgeroPayments(12614, "mbtowing79", "dadfall06");
            SyncAgeroPayments(56235, "buzzy2374", "dadfall06");
            SyncAgeroPayments(44149, "101640", "ccc02033");
            SyncAgeroPayments(85370, "acebodyshop", "6452859");
             return;

             PushInvoices(2064, Convert.ToDateTime("4/6/2015"), Convert.ToDateTime("6/1/2015"), false);
             return;

             return;


             return;
            */
        }

        public class PaymentAccount
        {
            public int AccountId;
            public int CompanyId;
            public string Username;
            public string Password;

        }

        public static void RunWeeklyAgeroPaymentImport()
        {
            List<PaymentAccount> runList = new List<PaymentAccount>();

            foreach (var r in SqlMapper.Query<dynamic>(
                @"SELECT AKV.AccountId, A.CompanyId FROM Integration.ProviderAccountKeyValues AKV INNER JOIN Accounts A on A.AccountId=AKV.AccountId WHERE 
                    AKV.ProviderAccountKeyId=(select ProviderAccountKeyId From towbook.integration.provideraccountkeys where name='ImportPayments' and IntegrationProviderId=4)"))
            {
                var keys = AccountKeyValue.GetByAccount(r.CompanyId, r.AccountId);
                int companyId = r.CompanyId;
                int accountId = r.AccountId;

                string username = AccountKeyValue.GetByAccount(companyId, accountId, Provider.Towbook.ProviderId, "McUsername").FirstOrDefault()?.Value;
                string password = AccountKeyValue.GetByAccount(companyId, accountId, Provider.Towbook.ProviderId, "McPassword").FirstOrDefault()?.Value;

                runList.Add(new PaymentAccount() { CompanyId = r.CompanyId, AccountId = r.AccountId, Username = username, Password = password });
            }

            Parallel.ForEach(runList,
                new ParallelOptions() { MaxDegreeOfParallelism = 5 },
                (o) =>
                {
                    if (o.Username != null && o.Password != null)
                    {
                        Console.WriteLine("Import Payments for " + o.CompanyId);
                        try
                        {
                            SyncAgeroPayments(o.AccountId, o.Username, o.Password);
                        }
                        catch (Exception e)
                        {
                            Console.WriteLine("FAILED to import payments for " + o.CompanyId + ".... " + e.ToString());
                        }
                    }
                });

            return;
        }


        public static async Async.Task TestBillToWorks()
        {
            var e = new Entry();

            e.CompanyId = 2;
            e.AccountId = 8;
            e.TowSource = "Test";
            e.TowDestination = "y";
            e.OwnerUserId = 1;
            await e.Save();


            var e2 = Entry.GetById(e.Id);

            Console.WriteLine("BillTo: " + e.Invoice.AccountId);
            Console.WriteLine("Interested:" + e.AccountId);
        }



        public static async Task TimeAcccountBalance()
        {
            var a = await Account.GetByIdAsync(61142);

            var s = Stopwatch.StartNew();


            Console.WriteLine(await a.GetBalanceAsync());

            Console.WriteLine(s.ElapsedMilliseconds);
            return;


        }

        static DateTime TestStorage(int gracePeriodHours, double roundingValue, bool waitForGrace, DateTime start,
            DateTime? end = null,
            Company.StorageRate.StorageChargeStartEnum type = Company.StorageRate.StorageChargeStartEnum.Midnight)
        {
            var sr = new Extric.Towbook.Company.StorageRate();
            sr.StorageChargeStart = type;
            sr.StorageGracePeriodHours = gracePeriodHours;
            sr.StorageRoundingValue = roundingValue;
            sr.MidnightWaitForGracePeriod = waitForGrace;

            var dt = Impounds.Impound.NextStorageDayIs(2, null, start,
                end,
                sr);


            Console.WriteLine(
                new
                {
                    Result = dt.ToShortDateString() + " " + dt.ToLongTimeString(),
                    Start = start.ToShortDateString() + " " + start.ToLongTimeString(),
                    GracePeriodHours = gracePeriodHours,
                    WaitForGracePeriod = waitForGrace,
                    RoundingValue = roundingValue,
                    WaitForGrace = waitForGrace,
                    End = end,
                    ActualDaysResult = Impounds.Impound.CalculateDaysHeldBillable(sr, 2, null, start, end),
                    Type = type.ToString()
                }.ToJson(null));

            return dt;
        }

        static void TestStorage()
        {
            TestStorage(12, 1, true, Convert.ToDateTime("8/19/2015 10:02am"),
                Convert.ToDateTime("8/19/2015 10:03am"));

            TestStorage(24, 1, false, Convert.ToDateTime("8/19/2015 10:02am"),
                Convert.ToDateTime("8/19/2015 10:03am"));

            TestStorage(12, 1, true, Convert.ToDateTime("8/19/2015 10:02am"),
                Convert.ToDateTime("8/19/2015 10:03am"), Company.StorageRate.StorageChargeStartEnum.Immediate);

            TestStorage(24, 1, false, Convert.ToDateTime("8/19/2015 10:02am"),
                Convert.ToDateTime("8/19/2015 10:03am"), Company.StorageRate.StorageChargeStartEnum.Immediate);

            // Simulate a workflow

            // 1- Call entered into towbook

            var returnDate = TestStorage(24, 1, true, Convert.ToDateTime("8/19/2015 10:02am"),
                Convert.ToDateTime("8/20/2015 10:05am"));

            DateTime next = returnDate;
            for (int i = 0; i < 10; i++)
            {
                next = TestStorage(24, 1, true, Convert.ToDateTime("8/19/2015 10:02am"),
                    next);
            }


            Console.WriteLine("**** IMMEDIATE **** 0.5");

            var returnDate2 = TestStorage(24, 0.5, false, Convert.ToDateTime("8/19/2015 10:02am"),
                Convert.ToDateTime("8/19/2015 10:05am"), Company.StorageRate.StorageChargeStartEnum.Immediate);

            DateTime next2 = returnDate2;
            for (int i = 0; i < 10; i++)
            {
                next2 = TestStorage(24, 0.5, false, Convert.ToDateTime("8/19/2015 10:02am"),
                    next2, Company.StorageRate.StorageChargeStartEnum.Immediate);
            }

            Console.WriteLine("**** IMMEDIATE **** 1");
            returnDate2 = TestStorage(24, 1, false, Convert.ToDateTime("8/19/2015 10:02am"),
                Convert.ToDateTime("8/19/2015 10:05am"), Company.StorageRate.StorageChargeStartEnum.Immediate);

            next2 = returnDate2;
            for (int i = 0; i < 10; i++)
            {
                next2 = TestStorage(24, 1, false, Convert.ToDateTime("8/19/2015 10:02am"),
                    next2, Company.StorageRate.StorageChargeStartEnum.Immediate);
            }

        }
        delegate void TestDelegate(string s);

        static void TestTieredStorage()
        {

            Func<DateTime, Impounds.Impound> CreateImpoundTest = (impoundDate) =>
            {
                Console.WriteLine();
                Console.WriteLine();
                Console.WriteLine("************************");
                Console.WriteLine("Create Impound for Test");
                Console.WriteLine("************************");

                var dr = new Entry();
                dr.CompanyId = 2;
                dr.Impound = true;
                dr.TowSource = "Test Impound";
                dr.OwnerUserId = 1;
                AsyncHelper.RunSync(() => dr.Save());

                var r = new Impounds.Impound();
                r.DispatchEntry = dr;
                r.ImpoundDate = impoundDate;
                r.Company = dr.Company;

                r.OwnerUserId = 1;
                r.Save(User.GetById(1), true);

                return r;
            };

            Func<Impounds.Impound, bool> ReleaseVehicleAndVerifyStorageIsLockedAgain = (imp) =>
            {
                Console.WriteLine();
                Console.WriteLine();
                Console.WriteLine("************************");
                Console.WriteLine("Release Vehicle and Verify Storage is Locked in DB");
                Console.WriteLine("************************");

                imp.ReleaseDate = Convert.ToDateTime("8/23/2015 2:00pm");
                imp.Save(User.GetById(1), true);

                Console.WriteLine(
                   new
                   {
                       Locked = imp.Invoice.InvoiceItems.Where(o => o.RateItem != null && o.RateItem.CategoryId == 5).First().Locked,
                       Subtotal = imp.Invoice.Subtotal,
                       Tax = imp.Invoice.Tax
                   }.ToJson(true));

                return imp.Invoice.InvoiceItems.Where(o => o.RateItem != null && o.RateItem.CategoryId == 5).First().Locked
                 == InvoiceItem.InvoiceItemLock.LockedByUser;
            };

            Func<Impounds.Impound, bool> UnreleaseVehicleAndVerifyStorageIsAutomaticAgain = (imp) =>
             {
                 Console.WriteLine();
                 Console.WriteLine();
                 Console.WriteLine("************************");
                 Console.WriteLine("Unrelease Vehicle and Verify Storage is UNLOCKED in DB");
                 Console.WriteLine("************************");

                 imp.ReleaseDate = null;
                 imp.Save(User.GetById(1), true);


                 Console.WriteLine(
                    new
                    {
                        Locked = imp.Invoice.InvoiceItems.Where(o => o.RateItem != null && o.RateItem.CategoryId == 5).First().Locked,
                        Subtotal = imp.Invoice.Subtotal,
                        Tax = imp.Invoice.Tax
                    }.ToJson(true));

                 return imp.Invoice.InvoiceItems.Where(o => o.RateItem != null && o.RateItem.CategoryId == 5).First().Locked
                  == InvoiceItem.InvoiceItemLock.Unlocked;
             };

            Func<Impounds.Impound, bool> MoveImpoundDate = (imp) =>
            {
                Console.WriteLine();
                Console.WriteLine();
                Console.WriteLine("************************");
                Console.WriteLine("Move impound date back by 10 days");
                Console.WriteLine("************************");

                imp.ImpoundDate = imp.ImpoundDate.Value.AddDays(-10);
                imp.Save(User.GetById(1), true);


                Console.WriteLine(
                   new
                   {
                       Locked = imp.Invoice.InvoiceItems.Where(o => o.RateItem != null && o.RateItem.CategoryId == 5).First().Locked,
                       Subtotal = imp.Invoice.Subtotal,
                       Tax = imp.Invoice.Tax
                   }.ToJson(true));

                return imp.Invoice.InvoiceItems.Where(o => o.IsStorageItem()).First().Locked
                 == InvoiceItem.InvoiceItemLock.Unlocked;
            };


            Func<Impounds.Impound, bool> MoveImpoundDateAndVerifyChargesDontChange = (imp) =>
            {

                // first, grab the storage item.

                var ii = imp.InvoiceItems.Where(o => o.IsStorageItem()).First();

                Debug.Assert(ii.Locked == InvoiceItem.InvoiceItemLock.Unlocked, "Item should not be locked yet");

                Console.WriteLine("Lock:" + ii.Locked);


                ii.Locked = InvoiceItem.InvoiceItemLock.LockedByUser;
                var q = ii.Quantity;
                Console.WriteLine("Quantity:" + q);
                imp.ImpoundDate = imp.ImpoundDate.Value.AddDays(-90);

                Console.WriteLine("Quantity after setting impound date:" + ii.Quantity);

                Debug.Assert(ii.Quantity == q, "Quantity should not have changed");


                // first.. note that the item SHOULD be locked.
                Console.WriteLine("~~~~~~~~~~~~");
                imp.Save(User.GetById(1), true);

                //imp = Impounds.Impound.GetById(imp.Id);

                return true;


            };


            var im = CreateImpoundTest(Convert.ToDateTime("8/1/2015"));

            im.InvoiceItems.Add(new InvoiceItem() { RateItem = RateItem.GetById(45321), Quantity = im.DaysHeldBillable });
            im.Invoice.Save(null, null);

            Debug.Assert(ReleaseVehicleAndVerifyStorageIsLockedAgain(im), "Failed to release and unlock");
            Debug.Assert(UnreleaseVehicleAndVerifyStorageIsAutomaticAgain(im), "Lock != None");
            Debug.Assert(MoveImpoundDate(im), "Failed to update quantity upon changing impound date");

            Debug.Assert(MoveImpoundDateAndVerifyChargesDontChange(im), "Failed to update quantity upon changing impound date");


            im.Delete(User.GetById(1));

            return;


            var y = new List<int>(0);
        }




        static void TestBillTo()
        {

            var inv = Invoice.GetByDispatchEntry(3275503);

            inv.AccountId = 31243;
            inv.Save(null, "0.0.0.0");


            inv = Invoice.GetByDispatchEntry(3275503);
            Console.WriteLine("accountId after save and reinit is: " + inv.AccountId);


        }

        static Extric.Towbook.Integration.MotorClubs.Billing.Axis.AxisCall FindAAACallFromSelect(string select, Extric.Towbook.Integration.MotorClubs.Billing.Axis.AxisConnection aaa)
        {
            var axisCall = new Extric.Towbook.Integration.MotorClubs.Billing.Axis.AxisCall();
            var axisCalls = GetAllAxisCalls(aaa);

            if (axisCalls != null)
            {
                Console.WriteLine("Total of " + axisCalls.Count + " Axis Calls.  Searching for " + select);

                var found = axisCalls.Where(w => w.Key == select).FirstOrDefault();

                if (found != null)
                {
                    Console.WriteLine("Found It!\n\n");
                    return found;
                }
            }

            Console.WriteLine("Not found. Here is what is available:");
            Console.WriteLine(axisCalls.Select(s => s.Key).ToJson(true));
            return axisCall;
        }
        static List<Extric.Towbook.Integration.MotorClubs.Billing.Axis.AxisCall> GetAllAxisCalls(Extric.Towbook.Integration.MotorClubs.Billing.Axis.AxisConnection aaa)
        {
            return aaa.GetCalls();
        }
        static void aaa()
        {
            var aaa = Extric.Towbook.Integration.MotorClubs.Billing.Axis.AAAConnection.Login("239", "g239"); // USA Towing (2218)
            using (aaa)
            {
                Console.WriteLine("aaa : login sucessfull\n\n");
                string select = "SEL*7026*D*17418";

                var axisCall = FindAAACallFromSelect(select, aaa);
                if (axisCall.Key == select)
                {
                    //var c = MotorClubMapper.Translate(axisCall, company.Id, acc.Id, true);
                    var c = AsyncHelper.RunSync(() => MotorClubMapper.Translate(axisCall, 2218, 24672, true));
                    if (c != null)
                    {
                        Console.WriteLine("Created Call #" + c.CallNumber);
                    }
                }
                else
                {
                    Console.WriteLine("Could not find " + select + ".  Here are the active calls at this time:");
                    Console.WriteLine(GetAllAxisCalls(aaa).Select(s => s.Key).ToJson(true));
                }
            }
        }

        public class WorkItem
        {
            public List<Extric.Towbook.Integrations.Quickbooks.Model.IPayment> Payments { get; set; }
            public List<Extric.Towbook.Integrations.Quickbooks.Model.IInvoice> Invoices { get; set; }
            public string Key { get; set; }
            public WorkItem(QuickBooksOnlineConnector connector, string key)
            {
                Payments = new List<Integrations.Quickbooks.Model.IPayment>();
                Invoices = new List<Integrations.Quickbooks.Model.IInvoice>();
                Key = key;
                Connector = connector;
            }

            public QuickBooksOnlineConnector Connector { get; set; }

        }
        //private static SuperQueue<WorkItem> deletionQueue; //= new SuperQueue<WorkItem>(1, new Action<WorkItem>(PerformDelete));

        static void PerformDelete(WorkItem x)
        {
            foreach (var y in x.Payments)
            {
                Console.WriteLine(x.Key + ": Delete Payment " + y.PaymentId + ", " + y.SyncToken);
                x.Connector.DeletePaymentById(y.PaymentId, y.SyncToken);
            }

            foreach (var y in x.Invoices)
            {
                ;
                Console.WriteLine(x.Key + ": Delete Invoice " + y.Id + ", " + y.SyncToken);
                try
                {
                    x.Connector.DeleteInvoiceById(y.Id, y.SyncToken);
                }
                catch (Exception e)
                {
                    Console.WriteLine(x.Key + ": Couldnt Delete Invoice..." + e.Message);
                }
            }
        }
        private static void TestAllstateBeta()
        {
            AllstateLogin("*********");

            //AllstateResponse("*********", "2504374", "", "accept", 39);
            return;
            //TestAllstatePush("1020265491");
            //return;

            //return;

            Thread.Sleep(1000);
            AllstateLogin("*********");
            return;

            Thread.Sleep(500);
            AllstateLogout("*********");
            return;

            //AllstateLogout("*********");

            return;
            AllstateLogin("*********");

            return;

            AllstateLogin("*********");
            AllstateLogin("*********");
            AllstateLogin("*********");
            return;

            Console.ReadLine();
            AllstateLogout("*********");
            return;

            return;
            //return;
            AllstateLogout("*********");

            AllstateLogin("*********");
            return;

            System.Threading.Thread.Sleep(2000);
            AllstateLogin("*********");
            System.Threading.Thread.Sleep(2000);
            AllstateLogin("*********");
            System.Threading.Thread.Sleep(2000);
            return;

            //            TestAllstatePush("**********");
            //            return;


            AllstateResponse("*********", "2499452", "", "phone");
            return;



            //            RunWeeklyAgeroPaymentImport();
            //          return;

        }

        static void AutoLoginAllstate()
        {
            while (true)
            {
                try
                {
                    const string sql = "select * From MCDispatch.AllstateContractors where isDeleted=0 and " +
                        " masterAccountId=2 and companyid in (select companyid from vwcompanies " +
                        "where lastlogin > dateadd(day,-3,getdate()))";
                    
                    var logins = SqlMapper.Query<dynamic>(sql);
                    Console.WriteLine("..." + DateTime.Now.ToString());

                    Parallel.ForEach(logins,
                        new ParallelOptions() { MaxDegreeOfParallelism = 5 },
                        async (l) =>
                        {
                            var jobId = await Towbook.Integration.MotorClubs.Services.DigitalDispatchService.Login(l.CompanyId,
                              l.AccountId, 1);

                            Console.WriteLine(DateTime.Now.ToString() + ": " + l.CompanyId + "..." + jobId);
                        });

                    return;
                    
                }
                catch (Exception e)
                {
                    Console.WriteLine(e.ToString());
                }
            }
        }

        static void validate()
        {
            var msg = "SAFE...WNS....DEAD BATTERY... WILL STAY..CVRD FOR JUMPSTART...\n\n@ AUTO ZONE".ToLower().Replace("cvrd", "cvrd ").Replace(".", " ").Replace("  ", " ").Replace("  ", " ").Trim();

            string customerPaidMiles = "";
            string coveredMiles = "";

            if (msg.Contains("ccrf"))
            {
                var start3 = msg.IndexOf("ccrf");
                if (start3 > -1)
                {
                    var start4 = msg.IndexOf("mi", start3);
                    if (start4 != -1)
                    {
                        var m3 = msg.Substring(start3, start4 - start3).Trim();

                        m3 = m3.Substring(m3.LastIndexOf(' ') + 1);
                        customerPaidMiles = m3;
                    }
                }
            }

            if (msg.Contains("cvrd"))
            {
                var start3 = msg.IndexOf("cvrd");

                if (start3 > -1)
                {
                    var start4 = msg.IndexOf("mi", start3);
                    if (start4 != -1)
                    {
                        var m3 = msg.Substring(start3, start4 - start3).Trim();

                        m3 = m3.Substring(m3.LastIndexOf(' ') + 1);
                        coveredMiles = m3;
                    }
                }
            }

        }
        static void test2()
        {
            int companyId = 2220;

            var scs = Extric.Towbook.Company.SharedCompany.GetByCompanyId(companyId);
            int[] companyIds = scs
                .Select(o => Extric.Towbook.Company.Company.GetById(o.SharedCompanyId))
                .Select(y => y.Id).Union(new int[] { companyId }).Union(scs.Select(o => o.CompanyId)).ToArray();

            Console.WriteLine(companyIds.ToJson());

        }

        static async Task PushBlueHill()
        {

            #region push to qb
            if (true)
            {
                var ips = (await InvoicePayment.GetByCompanyAsync(2064,
                    Convert.ToDateTime("11/1/2015"), Convert.ToDateTime("12/1/2015"))).Select(o => Entry.GetById(o.DispatchEntryId));

                IQuickbooksConnector objQBConnecter = await QuickbooksUtility.GetConnector(2064);


                var entities = Entry.GetByCompany(Company.Company.GetById(2064), null,
                    Convert.ToDateTime("11/1/2015"),
                    Convert.ToDateTime("12/1/2015")).Union(ips).Where(o => o != null && (o.Status.Id == 5) && (o.Impound == false || (o.Impound && Impounds.Impound.GetByDispatchEntry(o).ReleaseDate != null)));

                Console.WriteLine(String.Join(",", entities.Where(o => o != null).Select(o => o.Id)));
                Console.ReadLine();

                foreach (var e in entities)
                {
                    if (e == null)
                        continue;
                    /*
                    var impound = Impounds.Impound.GetByDispatchEntry(e);

                    if (impound != null)
                    {
                        if (impound.ReleaseDate == null)
                        {
                            var dekv = DispatchEntryKeyValue.GetByDispatchEntryId(e.Id).FirstOrDefault();

                            if (dekv != null)
                            {
                                try
                                {
                                    var s = objQBConnecter.GetInvoiceById(dekv.Value);
                                    if (s != null)
                                    {
                                        try
                                        {
                                            objQBConnecter.DeleteInvoiceById(s.Id, s.SyncToken);
                                        }
                                        catch (Exception ex)
                                        {
                                            Console.WriteLine("failed deleting " + s.Id + " / dispatchEntryId " + e.Id + " / call num " + e.CallNumber + " / invoice num " + e.InvoiceNumber);
                                            Console.WriteLine(ex.InnerException.Message);

                                        }
                                        Console.WriteLine("Deleted " + s.Id + " / dispatchEntryId " + e.Id + " / call num " + e.CallNumber + " / invoice num " + e.InvoiceNumber);
                                        continue;
                                    }
                                    else
                                    {
                                        dekv.Delete();

                                    }
                                }
                                catch (Exception ez)
                                {
                                    throw new Exception(ez.ToJson(true));
                                }
                            }
                        }
                        continue;


                    }
                    else
                    {
                        continue;

                    }
                    */


                    try
                    {
                        AsyncHelper.RunSync(() => InvoicePaymentHelper.SendEntryToQuickBooksAndPayInvoice(e, Provider.QuickBooks));
                        Console.WriteLine("pushed " + e.Id + " / call # " + e.CallNumber);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine(e.Id + ": could not push " + e.Id + " / call # " + e.CallNumber);
                        Console.WriteLine(e.Id + ": " + ex.ToString());
                        Console.WriteLine(e.Id + ": *****************");
                    }
                }
                return;
            }

            #endregion
        }

        static void TestTomTom()
        {
            var gpsSource = "";
            decimal recentLat = 0;
            decimal recentLong = 0;

            try
            {
                var ttuz = Integrations.TomTom.TomTomUtility.GetInstance(2);

                #region try to get fromtom
                if (ttuz != null)
                {
                    var units = ttuz.GetUnits();

                    if (units != null && units.Any())
                    {
                        var data = TruckKeyValue.GetByTruck(2, 2192)
                            .Where(w => w.KeyId == Provider.TomTom.GetKey(KeyType.Truck, "TruckId").Id);

                        foreach (var row in data)
                        {
                            var t = units.Where(o => o.Id == row.Value).FirstOrDefault();

                            if (t != null)
                            {
                                recentLat = t.Latitude.Value;
                                recentLong = t.Longitude.Value;
                                gpsSource = "TomTom";
                                break;
                            }
                        }
                    }

                    var drivers = ttuz.GetDrivers();
                    foreach (var d in drivers)
                    {
                        Console.WriteLine(d.ToJson());
                    }
                }
                #endregion

            }
            catch (Exception e)
            {
                if (recentLat == 0)
                {
                    gpsSource = "TomTom; quota exceeded";
                }
                throw new Exception("Error completing call.", e);
            }

            Console.WriteLine(recentLat);
            Console.WriteLine(recentLong);
            Console.WriteLine(gpsSource);

            return;


            var uli = UserLocationHistoryItem.GetByUserId(23128).First();

            Console.WriteLine(uli.Timestamp.ToLocalTime() > DateTime.Now.AddMinutes(-3));
            Console.WriteLine(uli.Timestamp > DateTime.Now.AddMinutes(-3));

            return;

            Console.WriteLine(uli.Timestamp);
            Console.WriteLine(uli.Timestamp.ToLocalTime());
            return;

            //    .Where(o => o.Timestamp > DateTime.Now.AddMinutes(-15)).FirstOrDefault();





            Console.WriteLine(uli.ToJson(null));
            return;


            var xaa = Stopwatch.StartNew();
            var ttu = Integrations.TomTom.TomTomUtility.GetInstance(4817);
            var truck = ttu.GetUnits().Where(o => o.Id == "1-48217-323B40E75").FirstOrDefault();
            truck = ttu.GetUnits().Where(o => o.Id == "1-48217-323B40E75").FirstOrDefault();
            truck = ttu.GetUnits().Where(o => o.Id == "1-48217-323B40E75").FirstOrDefault();
            truck = ttu.GetUnits().Where(o => o.Id == "1-48217-323B40E75").FirstOrDefault();
            truck = ttu.GetUnits().Where(o => o.Id == "1-48217-323B40E75").FirstOrDefault();
            truck = ttu.GetUnits().Where(o => o.Id == "1-48217-323B40E75").FirstOrDefault();

            Console.WriteLine(xaa.ElapsedMilliseconds);

            Console.WriteLine(truck.ToJson(true));
            return;

        }

        static void GetTomTomIOEvents()
        {
            try
            {
                var username = "towbook";
                var password = "tomtom";
                var account = "patriot";
                var ttu = new Integrations.TomTom.TomTomUtility(username, password, account);

                if (ttu != null)
                {
                    var events = ttu.GetIOEvents(Integrations.TomTom.RangePattern.LastMonth);
                    foreach (var ev in events)
                        Console.WriteLine(ev.ToString());
                }
            }
            catch (Exception e)
            {
                throw new Exception("Error getting IO events.", e);
            }
            finally
            {
                Console.WriteLine("Press any key to exit");
                Console.ReadLine();
            }
        }

        static void Login(int cid, int aid)
        {
            var zdjobId = Towbook.Integration.MotorClubs.Services.DigitalDispatchService.Login(cid, aid, 1);
        }


        static void lane()
        {

            #region Test new sql query pattern
            if (true)
            {
                using (var cnn = Core.GetConnection())
                {
                    for (int i = 0; i < 10000; i++)
                    {
                        Stopwatch w = Stopwatch.StartNew();
                        var builder = new SqlBuilder();
                        var selector = builder.AddTemplate(
                        @"SELECT D.DispatchEntryId, D.CallNumber, D.CompanyId, 
                                    D.AccountId, D.Type, DispatchReasonId, ReceiveTime, DispatchTime, EnRouteTime, 
                                    D.ArrivalTime, TowTime, CompletionTime, TowSource, TowDestination, MilesSource,
                                    D.MilesDestination, DriverId, TruckId, Notes, VehicleVIN, VehicleBodyTypeId, VehicleColorId, 
                                    D.VehicleManufacturerId, VehicleModelId, VehicleYear, VehicleLicenseNumber, 
                                    D.VehicleLicenseState, VehicleLicenseYear, VehicleDrivable, VehicleOdometer,
                                    D.Priority, D.Status, D.OwnerUserId, D.CreateDate, D.Created, D.Impound, D.ArrivalETA, 
                                    D.Deleted, D.Version, @X as Test1, @Y as Test2, C.Name from DispatchEntries D 
                                    /**rightjoin**/ /**where**/ /**orderby**/", new { X = 1, Y = 2 });

                        builder.Where("D.CompanyId = @companyId");
                        //builder.Where("Status = @status", new { status = 0 });
                        builder.RightJoin("Companies C ON C.CompanyId=D.CompanyId");
                        builder.OrderBy("CompanyId");
                        builder.OrderBy("Status", null, 500, 50);
                        builder.AddParameters(new { X = 4 });
                        builder.AddParameters(new { companyId = 4 });




                        Console.WriteLine(selector.RawSql);

                        var dp = selector.Parameters as DynamicParameters;

                        foreach (var paramName in dp.ParameterNames)
                        {
                            var value = dp.Get<dynamic>(paramName);
                            Console.WriteLine(value);
                        }

                        var z = cnn.QueryMultiple(selector.RawSql, selector.Parameters);
                        Console.WriteLine(dp.ToJson());

                        return;




                        var results = z.Read();

                        //Console.WriteLine(JsonExtensions.ToJson(results));
                        Console.WriteLine(selector.Parameters.ToJson());



                        Console.WriteLine("TOOK: " + w.ElapsedMilliseconds + "MS for " + results.Count());
                        Console.ReadLine();

                    }
                    //var zrows = cnn.Query(selector.RawSql, selector.Parameters);

                    //Console.WriteLine(zrows.ToList().ToJson());

                    return;

                    // Same as:

                    //    var count = cnn.Query("select count(*) from table where a = @a and b = @b", new {a=1,b=1});
                    //  var rows = cnn.Query("select * from table where a = @a and b = @b order by a, b", new {a=1,b=1});
                }
            }
            #endregion
        }

        public class IsscBreadcrumbModel
        {
            public int DispatchEntryId { get; set; }
            public string DispatchId { get; set; }
            public string ContractorId { get; set; }
            public string ClientId { get; set; }
            public string LocationId { get; set; }
            public int DriverId { get; set; }
            public double Latitude { get; set; }
            public double Longitude { get; set; }
            public DateTime Timestamp { get; set; }
        }

        static void PushTest2()
        {
            var cr = CallRequest.GetById(900756);
            var r = cr.Deliver().Result;
            Console.ReadLine();
            return;

            Dictionary<string, string> values = new Dictionary<string, string>();


            var u = User.GetById(27558);

            var rs = NotificationHubHelper.GeneralChannelInstance.SendNotificationMessage(u,
                                   "You, sir, are a moron.",
                                   values).Result;
        }


        static void TestDSR()
        {
            var dispatch = new AllstateDispatch();
            dispatch.ContractorId = "1";
            dispatch.ResponseId = "123";


            var suReturn = new DSRMessage();
            var suHeader = new DDMessageHeader(dispatch.ContractorId, "DSR", key: "TOWBOOK");
            suHeader.ResponseID = dispatch.ResponseId;

            var suMsg = new DDMessage();
            suMsg.DDMessageHeader = suHeader;
            suMsg.DDContent = suReturn;

            suReturn.JobID = dispatch.DispatchId;
            suReturn.Lat = "0.00";
            suReturn.Lon = "0.00";
            suReturn.ResourceId = "1";
            suReturn.ResourceName = "John Doe";
            suReturn.Timestamp = DateTime.Now.ToJson().Trim('"');
            suReturn.Status = "10";

            var xmlStatusUpdate = suMsg.GetXml();
            Console.WriteLine(xmlStatusUpdate);
        }
        public static Uri EnvironmentSpecificRequestUrl(Uri uri, NameValueCollection headers, bool ignorePortNumber = true)
        {
            UriBuilder hostUrl = new UriBuilder();
            string hostHeader = headers["Host"];

            if (hostHeader.Contains(":"))
            {
                hostUrl.Host = hostHeader.Split(':')[0];

                if (!ignorePortNumber)
                    hostUrl.Port = Convert.ToInt32(hostHeader.Split(':')[1]);
                else
                    hostUrl.Port = -1;
            }
            else
            {
                hostUrl.Host = hostHeader;
                hostUrl.Port = -1;
            }

            Uri url = uri;
            UriBuilder uriBuilder = new UriBuilder(url);

            if (String.Equals(hostUrl.Host, "localhost", StringComparison.OrdinalIgnoreCase) || hostUrl.Host == "127.0.0.1")
            {
                // Do nothing
                // When we're running the application from localhost (e.g. debugging from Visual Studio), we'll keep everything as it is.
                // We're not using request.IsLocal because it returns true as long as the request sender and receiver are in same machine.
                // What we want is to only ignore the requests to 'localhost' or the loopback IP '127.0.0.1'.
                return uriBuilder.Uri;
            }

            // When the application is run behind a load-balancer (or forward proxy), request.IsSecureConnection returns 'true' or 'false'
            // based on the request from the load-balancer to the web server (e.g. IIS) and not the actual request to the load-balancer.
            // The same is also applied to request.Url.Scheme (or uriBuilder.Scheme, as in our case).
            bool isSecureConnection = String.Equals(headers["X-Forwarded-Proto"], "https", StringComparison.OrdinalIgnoreCase);

            if (isSecureConnection)
            {
                uriBuilder.Port = hostUrl.Port == -1 ? 443 : hostUrl.Port;
                uriBuilder.Scheme = "https";
            }
            else
            {
                uriBuilder.Port = hostUrl.Port == -1 ? 80 : hostUrl.Port;
                uriBuilder.Scheme = "http";
            }

            uriBuilder.Host = hostUrl.Host;

            return uriBuilder.Uri;
        }

        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        static void TestAllstateDistributedLoginLocks()
        {
            int[] fakeIds = { 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42 };

            logger.LogEvent("Fake processing " + fakeIds.Length + " jobs...", 0, LogLevel.Warn);

            try
            {
                Parallel.ForEach(fakeIds,
                    new ParallelOptions() { MaxDegreeOfParallelism = 10 },
                    (x) =>
                    {
                        var logInfo = new LogInfo($"DispatchEntryService/{x:00}", 0, true, "Allstate");
                        try
                        {
                            LogEvent(logInfo, $"Preparing to submit job # {x}", LogLevel.Info);

                            var allstate = Integration.MotorClubs.Billing.Allstate.AllStateConnection.Login(
                                logInfo, "MoxieTow", "Bellamax82", "*********");
                            try
                            {
                                logInfo.AddEvent($"Getting available PO's", LogLevel.Info);
                                var s = Stopwatch.StartNew();
                                var pos = allstate.GetAvailablePOs(logInfo);
                                s.Stop();
                                logInfo.AddEvent($"PO's - took {s.Elapsed.TotalSeconds:0.0}s", LogLevel.Info);

                                logInfo.AddEvent($"Processing ...", LogLevel.Info);
                                s.Restart();
                                var html = new HtmlDocument();
                                var po = allstate.GetPurchaseOrder(logInfo, pos[0].DispatchId, false, out html);
                                po = allstate.GetPurchaseOrder(logInfo, pos[1].DispatchId, false, out html);
                                po = allstate.GetPurchaseOrder(logInfo, pos[2].DispatchId, false, out html);

                                var r = new Random();
                                int delay = r.Next(1000, 5000);
                                logInfo.AddEvent($"Pretending to do work for {delay / 1000f:0.0}s", LogLevel.Info);
                                Thread.Sleep(delay);

                                s.Stop();
                                logInfo.AddEvent($"Processing took {s.Elapsed.TotalSeconds:0.0}s", LogLevel.Info);
                            }
                            finally
                            {
                                allstate.ReleaseLock(logInfo);
                            }

                            logInfo.FlushEvents(logger);
                        }
                        catch (Exception e)
                        {
                            logInfo.FlushEvents(logger);
                            LogEvent(logInfo, $"CAUGHT exception \"{e.Message}\" -- {e}", LogLevel.Error);
                        }

                        LogEvent(logInfo, $"Finished processing", LogLevel.Info);
                    });
            }
            catch (Exception e)
            {
                logger.LogEvent($"CAUGHT exception \"{e.Message}\" -- {e}", 0, LogLevel.Error);
            }
            finally
            {
                logger.LogEvent("Done", 0, LogLevel.Info);
            }
        }
        private static void LogEvent(LogInfo logInfo, string message, LogLevel logLevel, Dictionary<object, object> properties = null)
        {
            logger.LogEvent(logInfo.Prefix(message), logInfo.CompanyId, logLevel, properties);
        }

        private static void IsscGpsShare()
        {
            var isc = new IsscRestClient(IsscConfig.GetByEnvironmentType(EnvironmentType.Live));
            

            DateTime lastRan = DateTime.Now;
            while (true)
            {
                try
                {
                    foreach (var x in SqlMapper.Query<IsscBreadcrumbModel>("select * from mcdispatch.vwIsscGpsBreadcrumbExport"))
                    {
                        isc.DispatchStatus(x.ClientId, x.ContractorId, x.LocationId, x.DispatchId, DateTime.Now, IsscRestClient.DispatchSource.Automatic,
                             IsscRestClient.DispatchStatusType.GPS, x.Latitude, x.Longitude, x.Timestamp, x.DriverId.ToString());
                    }
                }
                catch (SqlException se)
                {
                    Console.WriteLine(se.ToString());
                }

                Console.WriteLine("");
                while (DateTime.Now < lastRan.AddSeconds(30))
                {
                    Console.Write(".");
                    Thread.Sleep(3000);
                }
                Console.WriteLine("---");
            }

        }

        static async Task AllstateGpsShare()
        {


            DateTime lastRan = DateTime.Now;
            while (true)
            {
                try
                {

                    var rowsToSend = SqlMapper.Query<dynamic>("select * from MCDispatch.[vwAllstateGpsBreadcrumbExport] with (nolock)");

                    foreach (var row in rowsToSend.Batch(50))
                    {
                        var outbound = new Integrations.MotorClubs.Allstate.Rest.OutgoingPushRootObject();

                        foreach (var r in row)
                        {
                            outbound.Push.PushGPSEvents.PushGPSEvent.Add(new Integrations.MotorClubs.Allstate.Rest.PushGpsEvent()
                            {
                                EventType = "ETA_UPDATE",
                                EventTime = DateTime.Now,
                                LastUpdatedInterval = 1,
                                ResponseId = r.ResponseId.ToString(),
                                Latitude = r.Latitude,
                                Longitude = r.Longitude,
                                Eta = 0,
                                ReasonCode = "0",
                                AdditionalInfo = "",
                                ContactName = ""
                            });
                        }

                        await AllstateRestClient.GetStaging().PushV3(outbound);
                        Console.WriteLine("Pushed " + outbound.Push.PushGPSEvents.PushGPSEvent.Count + " gps events");
                    }
                }
                catch (SqlException se)
                {
                    Console.WriteLine(se.ToString());
                }

                Console.WriteLine("");
                while (DateTime.Now < lastRan.AddSeconds(30))
                {
                    Console.Write(".");
                    Thread.Sleep(3000);
                }
                Console.WriteLine("---");
            }

        }

        static void QuestGpsShare()
        {


            void ShareGpsEvent(int companyId, 
                string contractorId, 
                string responseId, 
                string jobId, 
                decimal latitude, 
                decimal longitude, 
                int driverId, 
                string driverName)
            {


                using (var qc = new IqixWebRequestDataClient())
                {
                    int tranNum = 0;
                    int clientId = 75;
                    string password = "dSYPnkh!";
                    int toClientId = 5;

                    if (companyId == 5780)
                    {
                        clientId = 74;
                        password = "sFBy2ah$";
                        toClientId = 8;
                    }

                    var suReturn = new DSRMessage();

                    var suHeader = new DDMessageHeader(contractorId, "DSR", key: "TOWBOOK");
                    suHeader.ResponseID = responseId;

                    var suMsg = new DDMessage();
                    suMsg.DDMessageHeader = suHeader;
                    suMsg.DDContent = suReturn;

                    suReturn.JobID = jobId;
                    suReturn.Lat = latitude.ToString("0.0000");
                    suReturn.Lon = longitude.ToString("0.0000");
                    suReturn.ResourceId = "1";
                    suReturn.ResourceName = "John Doe";
                    suReturn.Timestamp = DateTime.Now.ToJson().Trim('"');
                    suReturn.Status = "10";
                    suReturn.ResponseType = "Automatic";

                    var xmlStatusUpdate = suMsg.GetXml();
                    var codeStatusUpdate = qc.qixSendXml(clientId, toClientId, password, ref xmlStatusUpdate, ref tranNum);

                    var respStatusUpdate = (codeStatusUpdate != 0 ? qc.qixErrorDesc(codeStatusUpdate) : "");
                    logger.Log(LogLevel.Info, "Quest/" + suHeader.ResponseID + "/StatusResponse/Response=" +
                        respStatusUpdate + ", TranNum=" + tranNum);
                }
            }

            DateTime lastRan = DateTime.Now;
            while (true)
            {
                try
                {
                    foreach (var x in SqlMapper.Query<QuestBreadcrumbModel>("select * from mcdispatch.vwQuestGpsBreadcrumbExport"))
                    {
                        ShareGpsEvent(x.CompanyId, x.ContractorId, x.ResponseId, x.JobId, x.Latitude, x.Longitude, x.DriverId, x.DriverName);
                    }
                }
                catch (SqlException se)
                {
                    Console.WriteLine(se.ToString());
                }

                Console.WriteLine("");
                while (DateTime.Now < lastRan.AddSeconds(30))
                {
                    Console.Write(".");
                    Thread.Sleep(3000);
                }
                Console.WriteLine("---");
            }


        }
        public class QuestBreadcrumbModel
        {
            public int CompanyId { get; set; }
            public int DispatchEntryId { get; set; }
            public string ResponseId { get; set; }
            public string JobId { get; set; }
            public string ContractorId { get; set; }
            
            public int DriverId { get; set; }
            public string DriverName { get; set; }
            public decimal Latitude { get; set; }
            public decimal Longitude { get; set; }
            public DateTime Timestamp { get; set; }
        }


        static int AgeroSyncTowbookDriver(int vendorId, Driver d)
        {
            DriverKey driverKey = DriverKey.GetByProviderId(Provider.Agero.ProviderId, "AgeroDriverId");

            var dkv = DriverKeyValue.GetByDriver(d.CompanyId, d.Id, driverKey.ProviderId, "AgeroDriverId");

            if (dkv.Any())
            {
                return d.Id;
            }

            var arc = new AgeroRestClient();

            try
            {
                var c = Company.Company.GetById(d.CompanyId);

                int id = arc.ExternalExportDriverProfile(vendorId, new ExternalDriverProfile()
                {
                    GpsTracking = true,
                    ExternalDriverId = d.Id.ToString(),
                    Name = d.Name,
                    OfficePhone = Core.FormatPhone(c.Phone),
                    OfficeEmail = Company.Company.GetById(d.CompanyId).Email,
                });


                // create the relative driver on towbook integration table
                DriverKeyValue newIntegrationDriver = new DriverKeyValue()
                {
                    DriverId = d.Id,
                    KeyId = driverKey.Id,
                    Value = id.ToString()
                };
                newIntegrationDriver.Save();
                return d.Id;
            }
            catch (AgeroResourceExistsException)
            {

                // create the relative driver on towbook integration table
                DriverKeyValue newIntegrationDriver = new DriverKeyValue()
                {
                    DriverId = d.Id,
                    KeyId = driverKey.Id,
                    Value = "pre_existing"
                };
                newIntegrationDriver.Save();
                return d.Id;
            }
            catch
            {
                return 0;
            }
        }


        static void TestQueue()
        {
            while (true)
            {
                var rjq = Extric.Towbook.Queues.RedisJobQueue.Create("quest_dsp");
                rjq.OnMessage(async (r) =>
                {
                    Console.WriteLine(r.Properties["payload"]);
                    var ct = JsonConvert.DeserializeObject<dynamic>(r.Properties["payload"]);

                    await DigitalDispatchService.HandleCallEventAsync(
                        (int)ct.CompanyId,
                        (int)ct.AccountId,
                        (string)ct.Json,
                        (DigitalDispatchService.CallEventType)ct.EventType);

                    Console.WriteLine(r.ToJson(true));

                    r.Complete();
                });
                Console.WriteLine("Press Ctrl+C to exit");
                Console.ReadLine();
            }
        }



        static async Task QuickBooksPush(string[] args)
        {
            System.Net.ServicePointManager.SecurityProtocol =
               System.Net.SecurityProtocolType.Tls12 |
               System.Net.SecurityProtocolType.Tls11 |
               System.Net.SecurityProtocolType.Tls;

            int companyId = 0;
            DateTime startDate = DateTime.Now.AddDays(-1);
            DateTime endDate = DateTime.Now;
            bool pushPaymentsOnly = false;


            for (int i = 0; i < args.Length; i++)
            {
                switch (args[i])
                {
                    case "/c":
                        companyId = Convert.ToInt32(args[i + 1]);
                        break;

                    case "/s":
                        startDate = Convert.ToDateTime(args[i + 1]);
                        break;

                    case "/e":
                        endDate = Convert.ToDateTime(args[i + 1]);
                        break;

                    case "/po":
                        pushPaymentsOnly = true;
                        break;
                }


            }


            Console.WriteLine("Start Date: " + startDate.ToString());

            Console.WriteLine("End Date: " + endDate.ToString());
            Console.WriteLine("CompanyId: " + companyId);
            Console.WriteLine("Push calls with payments only: " + pushPaymentsOnly);

            WebGlobal.CurrentUser = new User() { CompanyId = companyId };

            await PushInvoices(companyId, startDate, endDate, pushPaymentsOnly);

            Console.WriteLine(args.ToJson());
            return;

        }


        static void GitHubTest()
        {
            var client = new GH.GitHubClient(new Octokit.ProductHeaderValue("Towbook.Test"));
            client.Credentials = new GH.Credentials("5957bbb89b595b0cfc4aaa3a528493c5db702d59");
            var user = client.User.Get("xdansmith").Result;
            Console.WriteLine(user.Followers + " folks love the half ogre!");

            var versions = SqlMapper.Query<dynamic>("SELECT * FROM Platform.ClientVersions WHERE Type=2");

            foreach (var v in versions)
            {
                string version = "UNKNOWN";
                try
                {
                    var commitMessage =
                        client.Git.Commit.Get("towbook", "towbook-android",
                        v.GitHash.Replace("|UTC", "")).Result
                        .Message;

                    var split = commitMessage.Split(' ');

                    if (commitMessage.Contains("Release") && commitMessage.Contains("-"))
                    {
                        var startIndex = commitMessage.IndexOf("Release") + "Release".Length;
                        version = commitMessage.Substring(startIndex,
                            commitMessage.IndexOf("-") - startIndex).Trim();

                        Version v2 = null;
                        if (!Version.TryParse(version, out v2))
                        {
                            Console.WriteLine(commitMessage);
                            version = "UNKNOWN";
                        }
                        if (commitMessage.Contains("Debug"))
                            version = "Debug-" + version;
                    }
                    else
                    {
                        Console.WriteLine(commitMessage);

                    }

                    Console.WriteLine(v.GitHash + ":" + version);

                    if (version != "UNKNOWN")
                    {
                        using (var c = Core.GetConnection())
                        {
                            c.Execute("UPDATE Platform.ClientVersions SET Version=@A WHERE ClientVersionId=@Id",
                                new { A = version, Id = v.ClientVersionId });
                        }
                    }
                }
                catch
                {
                    Console.WriteLine("Invalid Commit:" + v.GitHash);
                }

            }
        }


    static void SendStickerNotifications()
        {
            while (true)
            {
                try
                {
                    foreach (var xzr in Stickering.StickerSession.GetUnfinishedReadyToFinish().OrderBy(o => o.Id))
                    {
                        //if (xzr.CompanyId != 536)
                        //    continue;

                        Console.WriteLine("sending notice for " + xzr.ToJson(true));

                        var email = EmailAddress.GetTowbookDotNetEmailAddressForCompany(xzr.CompanyId);
                        var company = Company.Company.GetById(xzr.CompanyId);
                        if (company == null)
                        {
                            xzr.CompletionDate = DateTime.Now;
                            xzr.Save();
                            continue;
                        }

                        xzr.SendNotifications(new MailAddress(email,
                            company.Name));
                        //xzr.ScheduleApprovalNotifications();





                        //break;

                        /*
                         * 3 cars waiting to be approved
                         * 
                         */

                    }
                    Thread.Sleep(30000);
                }
                catch (Exception e)
                {
                    Console.WriteLine(e.ToJson(true));
                }
            }

            //var ss2 = Stickering.StickerSession.GetOrCreateSessionForAccountId(10000, 2, 1) ;


        }

        static void SendParkingInvite()
        {
            /*
            var twilio = new Twilio.TwilioRestClient("**********************************", "6fcbc12f151dc2ae13ca891c47f31055");

            var msgx = "Confirm your Northside Apartments parking permit now.\nVisit https://towbook.com/3aidj399d2 to complete your registration.";
            var mx = twilio.SendMessage(TwilioNumberRepository.GetRandomNumber(), "1**********", msgx, new String[0]);
            Console.WriteLine(mx.ToJson(true));*/
        }

        static void TestStickerApprovalEmails()
        {
            var sp = SqlMapper.Query<dynamic>(
                "select S.StickerId, GracePeriodExpirationDate, S.AccountId From stickers S where GracePeriodExpirationDate<getdate() and StickerId not in (302, 303) and accountid > 0 and createdate > dateadd(day,-30,getdate()) and s.stickerid not in (select stickerid from StickerApprovalNotifications)");

            foreach (var x in sp.GroupBy(o => o.AccountId))
            {
                Console.WriteLine(x.Key + "=====" + JsonExtensions.ToJson(x, true));

                int destinationAccountId    = x.Key;
                int[] stickers                = x.Select(o => (int)o.StickerId).ToArray();
                var stickerCount = stickers.Count();

                
                var ac = Account.GetById(destinationAccountId);
                var cc = Company.Company.GetById(ac.CompanyId);
                
                Console.WriteLine("sending notice for " + cc.Name + " / " + ac.Company);

                var email = EmailAddress.GetTowbookDotNetEmailAddressForCompany(cc.Id);
                var sender = new MailAddress(email, cc.Name);

                using (var m = new SmtpClient().Get())
                {
                    var users = User.GetByAccountId(destinationAccountId);

                    foreach (var dest in users)
                    {
                        var message = new MailMessage();

                        message.From = sender;
                        message.To.Add("<EMAIL>");
                        message.Bcc.Add("<EMAIL>");

                        message.Subject = "Stickered Vehicle Notification - Ready to Approve";

                        message.Body = dest.FullName + ",\n\n";

                        message.Body += $"There are {stickerCount} stickered vehicles at the property located at " +
                            ac.Address + ".\n\nPlease review the stickered vehicles at the link below to approve the ones you want towed.\n\n";

                        message.Body += "https://app.towbook.com/stickering/" + "\n\n";

                        message.Body += cc.Name + "\n";
                        message.Body += Core.FormatPhone(cc.Phone) + "\n\n";

                        message.Body += "Sent using Towbook";

                        m.Send(message);

                        Stickering.Sticker.RecordApprovalsSent(destinationAccountId, stickers, dest.Id);
                    }
                }

                Console.WriteLine("send " + x.Count() +" approval notifications");

            }
        }


        static void TestPusherEvents()
        {
            //// Testing - LocationController.CurrentLocations()
            //var list = new List<UserLocationHistoryItem>();

            //foreach (var group in UserLocationHistoryItem.GetRecentByCompanyId(2).GroupBy(o => o.UserId))
            //{
            //    list.Add(group.OrderByDescending(o => o.Timestamp).FirstOrDefault());
            //}

            //var defaults = DriverTruckDefault.GetByCompanyId(2);

            //var groups = TruckLocationHistoryItem.GetRecentByCompanyId(2).GroupBy(o => o.TruckId);
            //foreach (var group in groups)
            //{
            //    var f = group.OrderByDescending(o => o.Timestamp).FirstOrDefault();

            //    if (f != null)
            //    {
            //        var defaultTruck = defaults.Where(o => o.TruckId == f.TruckId).FirstOrDefault();

            //        if (defaultTruck != null)
            //        {
            //            var userId = Driver.GetById(defaultTruck.DriverId)?.UserId;
            //            if (userId != null)
            //            {

            //                list = list.Where(o => o.UserId != userId.Value).ToList();

            //                list.Add(new UserLocationHistoryItem()
            //                {
            //                    Id = (int)-f.Id,
            //                    UserId = userId.Value,
            //                    Latitude = f.Latitude,
            //                    Longitude = f.Longitude,
            //                    Timestamp = f.Timestamp,
            //                    GpsSource = f.GetGpsSourceName()
            //                });
            //            }
            //        }
            //    }
            //}

            //Console.WriteLine(list.ToJson());
            //Console.ReadLine();

            //// Update Driver Location
            //var e = new UserLocationHistoryItem()
            //{
            //    //// John Doe
            //    //UserId = 3611,
            //    //Latitude = 43.038205m,
            //    //Longitude = -82.513521m,
            //    //Timestamp = DateTime.Now,
            //    // Berry Burst
            //    //UserId = 3634,
            //    //Latitude = 42.869213m,
            //    //Longitude = -82.484221m,
            //    //Timestamp = DateTime.Now,
            //    //// AC Driver
            //    //UserId = 3634,
            //    //Latitude = 42.936413m,
            //    //Longitude = -82.474228m,
            //    //Timestamp = DateTime.Now,
            //};
            //e.Save(2);

            //// Update Truck Location
            //var t = new TruckLocationHistoryItem()
            //{
            //    TruckId = 2192,
            //    Latitude = 42.82212m,
            //    Longitude = -82.481121m,
            //    Timestamp = DateTime.Now,
            //    SourceId = 2,
            //};
            //t.Save(2);

            //// Update Call
            //PushNotificationProvider.Push(
            //    $"private-TWBK_Client_2",
            //    "call_update",
            //    new
            //    {
            //        callId = 344728,
            //        extra = "update"
            //    }, true, true);

            //// Create Call
            //PushNotificationProvider.Push(
            //    $"private-TWBK_Client_2",
            //    "call_update",
            //    new
            //    {
            //        callId = 218409,
            //        extra = "create"
            //    }, true, true);

            for (var x = 0; x < 100000; x++)
            {
                // Oscillate driver position every 3 seconds
                Console.WriteLine("Superman's flying!!!");
                var e = new UserLocationHistoryItem()
                {
                    // Clark Kent (Superman)
                    UserId = 3646,
                    Latitude = x % 2 == 0 ? 42.346846m : 42.386090m,
                    Longitude = x % 2 == 0 ? -83.077528m : -83.160956m,
                    Timestamp = DateTime.Now,
                };
                //RealTimeService.DeliverPusher(3, e);

                Thread.Sleep(3000);
            }
        }

        static async Task GenerateMissingDamageFormLocations()
        {
            // Get all damage forms that have no Location generated
            var damageForms = SqlMapper.Query<EntryDamage>(
                "SELECT Id=DispatchEntryDamageId, * FROM DispatchEntryDamages WHERE Location IS NULL").ToCollection();

            Console.WriteLine($"Found {damageForms.Count()} damage forms needing locations");

            foreach (var d in damageForms)
            {
                try
                {
                    // Reverse-geocode location
                    var f = (await new GeocoderController().Get($"{d.Latitude}, {d.Longitude}")).FirstOrDefault();
                    if (f != null)
                    {
                        d.Location = string.Join(", ", new[] { f.Address, f.City, f.State, f.Zip }.Where(x => !string.IsNullOrWhiteSpace(x)));
                        d.Save();
                        Console.WriteLine($"Saved location \"{d.Location}\" for DispatchEntryDamageId: {d.Id}");
                    }
                }
                catch (Exception e)
                {
                    Console.WriteLine($"Error reverse-geocoding location for DispatchEntryDamageId: {d.Id}", e.ToString());
                }
            }
            Console.WriteLine("Finished!");
        }

        static async Async.Task TestCallRequestEventNotification()
        {
            try
            {
                var cr = new CallRequest()
                {
                    CompanyId = 3,
                    RequestDate = DateTime.Now,
                    ExpirationDate = DateTime.Now.AddMinutes(15),
                    AccountId = 12376,
                    StartingLocation = "4435 Krafft Rd, Fort Gratiot, MI 48059",
                    Vehicle = "2016 Ford F-150 Blue",
                };

                await cr.Save();

                await cr.UpdateStatus(CallRequestStatus.Expired);

                cr = new CallRequest()
                {
                    CompanyId = 3,
                    RequestDate = DateTime.Now,
                    ExpirationDate = DateTime.Now.AddMinutes(15),
                    AccountId = 12376,
                    StartingLocation = "2398 Hendersonton Boulevard, Westchesterton, VA 28094",
                    Vehicle = "2023 Land Rover Grand Canyon Aquamarine",
                };

                await cr.Save();

                await cr.UpdateStatus(CallRequestStatus.Rejected);

                cr = new CallRequest()
                {
                    CompanyId = 3,
                    RequestDate = DateTime.Now,
                    ExpirationDate = DateTime.Now.AddMinutes(15),
                    AccountId = 12376,
                    StartingLocation = "1234 Henderson Ave, Fort Gratiot, MI 48059",
                    Vehicle = "2015 Hyundai Sonata White",
                };

                await cr.Save();

                await cr.UpdateStatus(CallRequestStatus.Accepted, po: "test");

                cr = new CallRequest()
                {
                    CompanyId = 3,
                    RequestDate = DateTime.Now,
                    ExpirationDate = DateTime.Now.AddMinutes(15),
                    AccountId = 12376,
                    StartingLocation = "1234 Henderson Ave, Fort Gratiot, MI 48059",
                    Vehicle = "2015 Hyundai Sonata White",
                };

                await cr.Save();

                await cr.UpdateStatus(CallRequestStatus.RejectedByMotorClub);

                cr = new CallRequest()
                {
                    CompanyId = 3,
                    RequestDate = DateTime.Now,
                    ExpirationDate = DateTime.Now.AddMinutes(15),
                    AccountId = 12376,
                    StartingLocation = "1234 Henderson Ave, Fort Gratiot, MI 48059",
                    Vehicle = "2015 Hyundai Sonata White",
                };

                await cr.Save();

                await cr.UpdateStatus(CallRequestStatus.Cancelled);

                cr = new CallRequest()
                {
                    CompanyId = 3,
                    RequestDate = DateTime.Now,
                    ExpirationDate = DateTime.Now.AddMinutes(15),
                    AccountId = 12376,
                    StartingLocation = "1234 Henderson Ave, Fort Gratiot, MI 48059",
                    Vehicle = "2015 Hyundai Sonata White",
                };

                await cr.Save();

                await cr.UpdateStatus(CallRequestStatus.GoaApprovedByMotorClub);

            }
            catch (Exception e)
            {
                Console.WriteLine($"Error tesitng call request update status", e);
            }
            finally
            {
                Console.WriteLine("Finished, please press a key");
                Console.ReadKey();
            }
        }

        
        static async Task TestGps()
        {
            var d = Entry.GetById(********);
            var p = Dispatch.Photo.GetById(********);
            if (d.Status.Id < Dispatch.Status.AtSite.Id)
            {
                double withinDistance = double.MinValue;
                var pu = d.Waypoints.Where(o => o.Title == "Pickup").FirstOrDefault();
                if (pu != null && pu.Latitude != 0 && pu.Longitude != 0 &&
                    p.Latitude != 0 && p.Longitude != 0)
                {
                    var photoGeo = new GeoCoordinate((double)p.Latitude, (double)p.Longitude);
                    var pickupGeo = new GeoCoordinate((double)pu.Latitude, (double)pu.Longitude);

                    withinDistance = photoGeo.GetDistanceTo(pickupGeo);
                }

                if (withinDistance != double.MinValue)
                {
                    if (withinDistance < 100)
                    {
                        d.Status = Dispatch.Status.AtSite;
                        if (d.ArrivalTime == null || d.ArrivalTime > DateTime.Now)
                        {
                            d.ArrivalTime = DateTime.Now;
                        }
                        

                        var sharedWithMc = false;
                        var cr = Dispatch.CallRequest.GetByDispatchId(d.Id);
                        if (cr != null)
                        {
                            string gpsSource = "towbook";


                            await Towbook.Integration.MotorClubs.Services.DigitalDispatchService.NotifyCallUpdateEventAsync(
                                d.CompanyId, d.AccountId,
                                new
                                {
                                    CallRequestId = cr.CallRequestId,
                                    NewStatusId = d.Status.Id,
                                    Latitude = p.Latitude,
                                    Longitude = p.Longitude,
                                    Source = gpsSource,
                                    DriverId = d.Driver?.Id ?? 0,
                                    DriverName = d.Driver?.Name ?? "Unknown",
                                    IsAutomated = true
                                }.ToJson(null), Towbook.Integration.MotorClubs.Services.DigitalDispatchService.CallUpdateType.StatusUpdate,
                                ownerUserId: WebGlobal.CurrentUser.Id);
                        }

                        LogEventInfo lei = new LogEventInfo();
                        lei.Level = LogLevel.Info;

                        lei.Message = "Updated Status to on scene automatically";

                        lei.Properties.Add("callId", d.Id);
                        lei.Properties.Add("callGeoLocation", pu.Latitude + "," + pu.Longitude);
                        lei.Properties.Add("photoLocation", p.Latitude + "," + p.Longitude);
                        lei.Properties.Add("distanceMeters", withinDistance);
                        lei.Properties.Add("sharedWithMotorClub", sharedWithMc);

                        logger.Log(lei);
                    }
                }
            }

        }

        static void GetPO(int vendorId, int drn)
        {

            var arc = AgeroSession.GetByVendorId(vendorId);

            var client = new AgeroRestClient();
            Console.WriteLine(client.GetDispatchDetail(arc.AccessToken, drn).ToJson(true));

        }

        

        static void TestRedis()
        {
            Core.SetRedisValue("IS_IT_UP", DateTime.Now.ToJson());

            Console.WriteLine(Core.GetRedisValue("IS_IT_UP"));

            return;
        }


        public class CollectiveResponse
        {
            public string DispatchId { get; set; }
            public string RejectReason { get; set; }
            public string StatusName { get; set; }
            public string ProviderId { get; set; }
            public string CompanyName { get; set; }
            public string CompanyPhone { get; set; }
        }

        static void ReportToAgero()
        {

            var sql = @"select ad.DispatchId, code as RejectReason, StatusName, ProviderId=(select TOP 1 EmailAddress FROM email.emailaddresses where companyid=q.companyid), cx.name as CompanyName, cx.phone as CompanyPhone From vwInternalCallRequests q  with (nolock) 
left outer join MasterAccountReasons mar on mar.MasterAccountReasonId = q.ResponseReasonId
inner join MCDispatch.oonagerodispatches ad on ad.callrequestid = q.CallRequestId
inner join companies cx on cx.companyid=q.companyid
where q.MasterAccountId = 30
and q.CallRequestId > ********
and q.CreateDate > '5/28/2020'
and q.companyid not in (10000, 52587, 2)
order by 1 desc
";

            var rows = SqlMapper.Query<CollectiveResponse>(sql);
            var oarc = new OonAgeroRestClient("PROD");

            foreach (var x in rows.GroupBy(o => o.DispatchId))
            {
                Console.WriteLine(x.Key + ":" + x.ToList().ToJson(true));
                oarc.CollectiveResponse(x.Key,
                    x.ToList().Select(r => new OonAgeroClient.OonAgeroRestClient.CollectiveResponseModel()
                    {
                        VendorInfo = new OonAgeroRestClient.CollectiveResponseModel.VendorInfoModel()
                        {
                            TowbookVendorId = r.ProviderId,
                            CompanyPhone = r.CompanyPhone,
                            CompanyName = r.CompanyName
                        },
                        Eta = 0,
                        Outcome = r.StatusName == "Accepted" ? "Assigned" : "Not assigned",
                        Reason = r.RejectReason,
                        Response = r.StatusName
                    }));
            }
        }

        public class MigrateModel
        {
            public int DispatchEntryId { get; set; }
        }
        public class AcModel
        {
            public int AccountId { get; set; }
        }

        static void FixAgero()
        {
            var sql = @"select accountid from integration.ProviderAccountKeyValues
where AccountId in(
select distinct AccountId from (
select ap.*, rc=(select count(1) from DispatchEntryPayments dp where dp.AccountPaymentId=ap.AccountPaymentId)  from AccountPayments ap 
inner join accounts a on a.AccountId=ap.AccountId and a.MasterAccountId=3
where 
OwnerUserId=7 and ap.CreateDate > '12/15/2020' 
) r where rc=0) and ProviderAccountKeyId=64";

            var rows = SqlMapper.Query<AcModel>(sql);
            Parallel.ForEach(rows,
                new ParallelOptions() { MaxDegreeOfParallelism = 7 },
                (row) =>
                {
                    AccountKeyValueInsertOrUpdate(0, row.AccountId, Provider.Towbook.ProviderId, "ImportPaymentsLastProcessed", "12/15/2020");
                });
        }


        static async Task TestAzureReverseMap()
        {
            int id = ********;
            WebGlobal.CurrentUser = User.GetByCompanyId(16403).FirstOrDefault(r => r.Type == User.TypeEnum.Manager);

            var cosmosCall = await (CosmosDB.Get().QueryItems<CallModel>("calls",
                   new QueryDefinition("select * from c where c.id=@id")
                       .WithParameter("@id", id.ToString()))
                   .FirstOrDefault()).FinishMapAsync();

            //var stop = Stopwatch.StartNew();

            //var reverse = await cosmosCall.MapBack();

            //Console.WriteLine(cosmosCall.ToJson(true));

            //var translated = CallModel.Map(reverse, null, cosmosCall.Payments.Select(o => Dispatch.CallModels.PaymentModel.Map(o)));

            //Console.WriteLine(translated.ToJson(true));
        }

        static async System.Threading.Tasks.Task ReportTest()
        {
            var results = await Entry.GetByCompanyAsync(
                companyIds: new int[] { 4189 },
                user: null,
                driverId: null, //new int[] { 170004, 191673, 213611, 11909 },
                          // new int[] { 170004, 191673, 213611, 222477, 225464, 11914, 215622, 79187, 11909, 156356 },
                startDate: Convert.ToDateTime("1/1/2021"),
                endDate: Convert.ToDateTime("1/2/2021"),
                dispatchReasonId: null, //2431,
                accountId: null, // new int[] {  61970 },
                truckId: null, // 70937
                accountType: (int[])null, //new int[] { 5 },
                bodyTypeId: null, // 1
                masterAccountId: null, //MasterAccountTypes.Geico,
                statusId: new int[] { Status.Completed.Id },
                isLocked: null,
                isBilled: null);

            results = results.Where(o => o.Id == ********).ToCollection();

            Console.WriteLine(results.Select(o => new { o.Id, o.CallNumber, o.CompletionTime, o.InvoiceItems, o.Invoice.ClassBalances }).ToJson(true));

            // ********
            return;

            Console.WriteLine(results.SelectMany(o => o.Drivers).Distinct().ToJson());
            Console.WriteLine(results.Select(o => o.Reason.Id).Distinct().ToJson());
            //Console.WriteLine(results.Select(o => o.Account.Id).Distinct().ToJson());
            //Console.WriteLine(results.Select(o => o.Truck.Id).Distinct().ToJson());
        }

        static async Task RefreshAzure()
        {
            var stopwatch = Stopwatch.StartNew();
            var callsToMigrate = SqlMapper.Query<MigrateModel>(
                "SELECT DispatchEntryId FROM DispatchEntries with (nolock) WHERE CompanyId IN @Ids and Deleted=0 and Status=5 and createdate > '1/1/2022' ",
                new
                {
                    Ids = new int[] { 107499 }
                });

            Console.WriteLine(callsToMigrate.Count() + " to migrate.");

            List<Entry> entries = new List<Entry>();

            foreach (var toGet in callsToMigrate.OrderBy(o => o.DispatchEntryId).Batch(500))
            {
                entries.AddRange(await Entry.GetByIdsAsync(toGet.Select (o => o.DispatchEntryId).ToArray()));
                Console.WriteLine(entries.Count + ": " + DateTime.Now.ToString());

            }

            Parallel.ForEach(entries,
                new ParallelOptions() { MaxDegreeOfParallelism = 2 },
                async (c) =>
                {
                    await c.Save(true,null, null, true);
                    //await Entry.UpdateInAzure(c);
                    Console.WriteLine("cosmos:" + c.Id);
                });

            Console.WriteLine(stopwatch.ElapsedMilliseconds + "ms to write " + entries.Count);
            Console.WriteLine(entries.Count);
            Console.WriteLine(DateTime.Now.ToString());
            Console.ReadLine();
            return;
        }

        static void HonkTest()
        {
            var hrc = HonkRestClient.Dev();

            //hrc.CreateTestJob("1024958", "jump", 1);
            var jobId = "2792953";
            if (false)
            hrc.Respond(jobId, new HonkRestClient.JobOfferResponseModel()
            {
                DriverId = "7",
                DriverName = "Pineapple Orange",
                DriverPhone = "**********",
                Eta = DateTime.Now.AddMinutes(47),
                LeaveTime = DateTime.Now.AddMinutes(7),
                Value = "accept" // "or reject"
            });
            // {"id":2792953,"provider_id":"1024958","status":"DISPATCHED","sent_at":"2021-05-07T12:24:34-07:00","customer_first_name":"Lund","customer_phone":"**********","vehicle":"WHITE 2007 FORD TAURUS","vehicle_vin":null,"vehicle_year":2007,"vehicle_color":"WHITE","vehicle_make":"FORD","vehicle_model":"TAURUS","requires_winch_out":false,"vehicle_location_lat":null,"vehicle_location_lng":null,"vehicle_location_address":"1528 Armacost Ave, Los Angeles, CA","destination_location_business_name":null,"destination_location_phone":null,"estimated_mileage":null,"estimated_payout_cents":null,"notes":[]}

            // [enroute, arrived_at_disablement, loaded, arrived_at_destination,complete]
            if (false)
            {
                hrc.Event(jobId, new HonkRestClient.EventModel()
                {
                    EventDateTime = DateTime.Now,
                    EventName = "enroute",
                    Note = "Test"
                });
                Console.ReadLine();

                hrc.Event(jobId, new HonkRestClient.EventModel()
                {
                    EventDateTime = DateTime.Now,
                    EventName = "arrived_at_disablement",
                    Note = "Test"
                });
                Console.ReadLine();
                hrc.Event(jobId, new HonkRestClient.EventModel()
                {
                    EventDateTime = DateTime.Now,
                    EventName = "loaded",
                    Note = "Test"
                });
                Console.ReadLine();
                hrc.Event(jobId, new HonkRestClient.EventModel()
                {
                    EventDateTime = DateTime.Now,
                    EventName = "arrived_at_destination",
                    Note = "Test"
                });
                Console.ReadLine();
                hrc.Event(jobId, new HonkRestClient.EventModel()
                {
                    EventDateTime = DateTime.Now,
                    EventName = "complete",
                    Note = "Test"
                });
            }
            
            var p = Dispatch.Photo.GetById(176969431);

            hrc.UploadDocument(jobId, new HonkRestClient.DocumentModel()
            {
                Url = p.HttpLocation,
                Category = "disablement photo",
                CaptureDate = Convert.ToDateTime("5/30/2019"),
                Latitude = p.CameraLatitude,
                Longitude = p.CameraLongitude,
                UploadLatitude = 42.825530M,
                UploadLongitude = -82.486175M,
                StatusName = "completed",
                Name = p.DispatchEntryId + "_" + p.Id + ".jpg"
            });
        }

        static async Task MoveCall(int callId, int destinationCompanyId)
        {
            var n = Entry.GetById(callId);
            var user = User.GetById(1);
            var originalCompanyId = n.CompanyId;
            if (originalCompanyId == destinationCompanyId)
                return;

            if (n == null)
            {
                Console.WriteLine("Call doesn't exist: " + callId);
            }

            n.CompanyId = destinationCompanyId;
            await n.Save(false, new AuthenticationToken() { UserId = 1 }, "127.0.0.1");
            
            await CosmosDB.Get().DeleteItem<CallModel>("calls", callId.ToString(), new PartitionKey(originalCompanyId));

            foreach (var f in CompanyFile.GetByDispatchEntryId(callId))
            {
                var src = f.LocalLocation;
                f.CompanyId = destinationCompanyId;
                var dest = f.LocalLocation;
                f.Save();

                await Storage.FileUtility.RemoteCopyFile(src, dest);
            }

            foreach (var p in Dispatch.Photo.GetByDispatchEntryId(callId))
            {
                var src = p.Location.Replace("%1", originalCompanyId.ToString());
                var dest = p.Location.Replace("%1", destinationCompanyId.ToString());
                try
                {
                    await Storage.FileUtility.RemoteCopyFile(src, dest);
                }catch(Exception y)
                {
                    Console.WriteLine(y.ToString());
                }
            }


        }

        static void DoAutoAcceptImport(int vendorId, int defaultEta, string zips)
        {

            var ags = AgeroSession.GetByVendorId(vendorId);
            if (ags == null)
            {
                Console.WriteLine("no such registration");
                return;
            }


            var comp = Company.Company.GetById(ags.CompanyId);
            Console.WriteLine(comp.Id + "/" + comp.Name);

            var swoop = SwoopSession.GetByCompanyId(comp.Id).FirstOrDefault();
            
            var splitZips = zips.Split('\n').Select(o => o.Trim()).Where(o => o.Length > 1).ToArray();

            Console.WriteLine(splitZips.ToJson());

            doAdd(ags.AccountId, ags.CompanyId);
            
            if (swoop != null)
                doAdd(swoop.AccountId, ags.CompanyId);

            void doAdd(int accountId, int companyId)
            {

                var existing = AutoAcceptRule.GetByAccountId(accountId);
                Console.WriteLine(existing.Count() + " auto accept rules already exist");

                var toAdd = splitZips.Where(r => !existing.Any(o => o.Zip == r));

                Parallel.ForEach(toAdd,
                    new ParallelOptions() { MaxDegreeOfParallelism = 5 },
                    (zip) =>
                    {

                        var aar = new AutoAcceptRule()
                        {
                            AccountId = accountId,
                            CompanyId = companyId,
                            Eta = defaultEta,
                            OwnerUserId = 1,
                            Type = AutoAcceptRule.AutoAcceptService.All,
                            Zip = zip
                        };

                        aar.Save();
                        Console.WriteLine(aar.ToJson());
                    });

                AccountKeyValueInsertOrUpdate(companyId, accountId, Provider.Towbook.ProviderId, "DigitalAutoAcceptInstantly", "1");
            }


            return;
        }

        public sealed class RandomDummyTestsService() : IHostedService, IAsyncDisposable
        {
            private readonly Task _completedTask = Task.CompletedTask;

            public async Task StartAsync(CancellationToken stoppingToken)
            {
                await DigitalDispatchEngineQueueHelpers.ReplayQueueItem(**********, false, DigitalDispatchService.QueueDirection.Outgoing,
                     async (a) =>
                     {
                         a.QueueItemId = **********;
                         // digital cancel
                         await MotorClubDispatchingService.HandleSwoopQueueOutgoingMessage(a, JsonConvert.DeserializeObject<dynamic>(a.JsonObject), null);
                     });
                return;
            }

            private void DoWork(object? state) { }
            public Task StopAsync(CancellationToken stoppingToken) => _completedTask;
            public ValueTask DisposeAsync() => ValueTask.CompletedTask;
        }

        public static Microsoft.Extensions.Hosting.IHostBuilder CreateHostBuilder(string[] args) =>
            Microsoft.Extensions.Hosting.Host.CreateDefaultBuilder(args)
                .ConfigureServices((_, services) =>
                {
                    Extric.Towbook.Configuration.ServiceExtensions.ConfigureCore(services);
                    services.AddSingleton(config =>
                    {
                        return new HttpClient(
                            new SocketsHttpHandler
                            {
                                PooledConnectionLifetime =
                                    TimeSpan.FromMinutes(5),
                                PooledConnectionIdleTimeout =
                                    TimeSpan.FromSeconds(30)
                            }
                        );
                    });
                    services.AddHostedService(serviceProvider => new RandomDummyTestsService());
                    AppServicesHelper.Services =
                        services.BuildServiceProvider();
                });

        static async System.Threading.Tasks.Task Main(string[] args)
        {
            CreateHostBuilder(args).Build().Run();
            return;


            foreach (int ven in new int[] { 123298 })
            DoAutoAcceptImport(ven, 45,
                @"45002
45005
45011
45012
45013
45014
45015
45018
45030
45034
45036
45039
45040
45041
45042
45044
45050
45051
45055
45056
45061
45062
45065
45066
45067
45069
45071
45111
45140
45147
45150
45174
45201
45202
45203
45204
45205
45206
45207
45208
45209
45211
45212
45213
45214
45215
45216
45217
45218
45219
45220
45221
45222
45223
45224
45225
45226
45227
45229
45231
45232
45233
45234
45235
45236
45237
45238
45239
45240
45241
45242
45243
45246
45247
45248
45249
45250
45251
45252
45253
45254
45258
45262
45263
45264
45267
45268
45269
45270
45271
45273
45274
45277
45280
45296
45298
45299
45999");

            foreach (int ven in new int[] { 56865 })
                DoAutoAcceptImport(ven, 60,
                    @"44022
44023
44026
44040
44060
44061
44072
44077
44081
44092
44094
44095
44097
44105
44110
44112
44114
44115
44117
44118
44119
44121
44122
44123
44124
44125
44128
44131
44132
44137
44139
44143
44146
44202
");

            foreach (int ven in new int[] { 131911, 131914, 132463
 })
                DoAutoAcceptImport(ven, 75,
                    @"46035
46041
46058
46065
46301
46303
46304
46307
46310
46311
46312
46319
46320
46321
46322
46323
46324
46327
46340
46341
46342
46345
46346
46347
46348
46349
46350
46356
46360
46365
46366
46368
46371
46373
");

            foreach (int ven in new int[] { 319154
 })
                DoAutoAcceptImport(ven, 60
,
                    @"77035
77514
77519
77533
77535
77560
77564
77575
77582
77585
77597
77611
77612
77613
77616
77619
77622
77623
77625
77626
77627
77629
77630
77632
77640
77642
77651
77655
77656
77657
77659
77660
77662
77663
77664
77665
77701
77702
77703
77705
77706
77707
77708
77710
77713
78022
78330
78332
78335
78336
78339
78340
78343
78347
78351
78358
78359
78362
78363
78368
78370
78372
78373
78374
78377
78379
78380
78382
78383
78385
78387
78389
78390
78391
78393
78401
78402
78404
78405
78406
78407
78408
78409
78410
78411
78412
78413
78414
78415
78416
78417
78418
78419
78427
78465
78469");

            foreach (int ven in new int[] { 319074
 })
                DoAutoAcceptImport(ven, 45
,
                    @"80106
80132
80133
80808
80809
80817
80819
80829
80831
80863
80903
80904
80905
80906
80907
80908
80909
80910
80911
80915
80916
80917
80918
80919
80920
80921
80922
80923
80924
80925
80926
80928
80929
80938
80939
80941
80942
80946
80947
80950
80951
80977
80995
80997
81001
81003
81008");

            foreach (int ven in new int[] { 105771
 })
                DoAutoAcceptImport(ven, 60,
                    @"52060
52207
52358
52720
52722
52726
52728
52738
52739
52742
52745
52746
52747
52748
52753
52754
52756
52758
52760
52761
52767
52769
52773
52776
52778
52801
52802
52803
52804
52805
52806
52807
52808
52809
61071
61201
61204
61231
61232
61234
61235
61236
61237
61238
61239
61240
61241
61242
61244
61250
61251
61254
61256
61257
61258
61259
61260
61261
61262
61263
61264
61265
61266
61273
61274
61275
61277
61278
61279
61281
61282
61284
61299
61344
61361
61401
61412
61413
61415
61420
61425
61435
61437
61442
61443
61448
61450
61453
61458
61462
61465
61466
61468
61472
61473
61474
61475
61478
61480
61486
61488
61490
61531
61544
61572
62330");



            foreach (int ven in new int[] { 320267
 })
                DoAutoAcceptImport(ven, 60,
                    @"77017
77034
77058
77059
77061
77062
77075
77089
77502
77503
77504
77505
77506
77507
77536
77565
77571
77573
77586
77587
77598
");


            foreach (int ven in new int[] {

            312277
 })
                DoAutoAcceptImport(ven, 45,
                    @"90021
90022
90023
90032
90033
90040
90063
90606
90640
90660
91733
91754
91755
91770
91776
91801
91803
");
            foreach (int ven in new int[] { 307825
 })
                DoAutoAcceptImport(ven, 45,
                    @"98110
98310
98311
98312
98314
98315
98322
98329
98332
98333
98335
98337
98339
98340
98342
98345
98346
98353
98359
98365
98366
98367
98370
98380
98383
98392
98528
98548
98588");

            foreach (int ven in new int[] { 135391, 109105, 313591
 })
                DoAutoAcceptImport(ven, 75,
                    @"50007
50009
50010
50012
50014
50021
50023
50033
50035
50036
50046
50047
50054
50055
50056
50069
50073
50075
50107
50109
50111
50128
50130
50131
50154
50156
50158
50161
50168
50201
50208
50211
50220
50230
50236
50237
50244
50248
50250
50257
50263
50265
50266
50269
50271
50309
50310
50311
50312
50313
50314
50315
50316
50317
50318
50319
50320
50321
50322
50323
50324
50325
50327
50392
50595");

            foreach (int ven in new int[] { 128392
})
                DoAutoAcceptImport(ven, 60
,
                    @"53005
53007
53022
53045
53046
53051
53092
53097
53122
53205
53206
53208
53209
53210
53211
53212
53213
53214
53216
53217
53218
53222
53223
53224
53225
53226");

            foreach (int ven in new int[] { 135757
 })
                DoAutoAcceptImport(ven, 45,
                    @"97302
97303
97304
97305
97317
97322
97325
97327
97329
97330
97331
97333
97338
97341
97345
97347
97351
97355
97360
97361
97365
97366
97367
97368
97369
97374
97376
97380
97383
97384
97386
97391
97394
97396
97401
97403
97404
97446
97477
97488
97498
");


            foreach (int ven in new int[] { 125209
 })
                DoAutoAcceptImport(ven, 45,
                    @"53014
53049
53061
53088
53919
53946
54106
54107
54110
54111
54113
54115
54123
54126
54129
54130
54131
54136
54137
54140
54152
54155
54160
54162
54165
54166
54169
54170
54171
54173
54180
54207
54208
54215
54227
54229
54230
54241
54301
54302
54303
54304
54305
54306
54307
54308
54311
54313
54324
54344
54406
54486
54499
54901
54902
54903
54904
54906
54911
54912
54913
54914
54915
54919
54922
54927
54929
54931
54933
54940
54942
54944
54945
54947
54948
54949
54950
54952
54956
54957
54961
54963
54964
54965
54969
54970
54971
54980
54981
54982
54983
54984
54985
54986
");


            foreach (int ven in new int[] { 136231, 135943, 135949, 135952, 135958, 136297
 })
                DoAutoAcceptImport(ven, 60
,
                    @"77001
77002
77003
77004
77005
77006
77007
77008
77009
77010
77018
77019
77021
77023
77024
77025
77027
77028
77030
77031
77033
77035
77036
77040
77041
77042
77043
77045
77046
77047
77048
77051
77053
77054
77055
77056
77057
77063
77064
77065
77070
77071
77072
77074
77075
77077
77079
77080
77081
77082
77083
77084
77085
77086
77087
77089
77091
77092
77094
77095
77096
77097
77098
77099
77375
77377
77401
77402
77406
77407
77410
77411
77423
77429
77433
77449
77450
77459
77469
77471
77477
77478
77479
77487
77489
77491
77492
77493
77494
77496
77498
77545
");


            foreach (int ven in new int[] { 104451, 133783, 62553
 })
                DoAutoAcceptImport(ven, 60,
                    @"72113
72114
72115
72116
72117
72118
72119
72120
72142
72190
72202
72227
");

            foreach (int ven in new int[] {320191
 })
                DoAutoAcceptImport(ven, 60
,
                    @"77040
77041
77043
77064
77065
77070
77077
77079
77082
77084
77094
77095
77218
77315
77410
77413
77429
77433
77449
77450
77491
77492
77493
77494
");

            foreach (int ven in new int[] { 300469
 })
                DoAutoAcceptImport(ven, 89
,
                    @"30002
30030
30032
30033
30079
30084
30272
30297
30303
30304
30305
30306
30307
30308
30309
30310
30311
30312
30313
30314
30315
30316
30317
30318
30319
30322
30324
30325
30326
30327
30328
30329
30330
30331
30332
30334
30337
30338
30339
30340
30341
30342
30343
30344
30345
30346
30349
30353
30354
30355
30361
30363
30366
30368
30370
30371
30374
30375
30377
30380
30384
30385
30388
30394
30398
31106
31107
31126
31139
31141
31195
31196
");





            return;

            Console.WriteLine((new SwoopRestClient()).GetJobById("7TNQrmIZBJXxwvdSzP0pER", "nFKX4suwo-38YMpDVfi7z5eR160t4R5ZiOtRLXVADMY").ToJson(true));
            return;


            var driver = Driver.GetByIdWithoutCache(307689);
            driver.MobilePhone = "";
            driver.DispatchingNotificationValue = "";
            await driver.Save();

            Console.WriteLine(Driver.GetById(307689).MobilePhone);
            return;

            await RefreshAzure();
            return;

            var xrx = (new DpoPacket());
            xrx.PacketData.Add(new PacketData(PacketDataKey.Vin, "V"));
            xrx.PacketData.Add(new PacketData(PacketDataKey.AmountDue, "Y"));
            //x.PacketData.Add(new PacketData(PacketDataKey.City, "Sarasota"));
            xrx.PacketData.Add(new PacketData(PacketDataKey.TowDate, "X"));


            Console.WriteLine(xrx.ToXml());
            return;



            await RefreshAzure();
            return;


            foreach (var shared in Company.SharedCompany.GetByCompanyId(26130))
            {
                Cache.Instance.PartitionReset<Entry.CachedCall>(shared.SharedCompanyId);
                Console.WriteLine(Entry.GetCachedCurrentByCompany(new int[] { shared.SharedCompanyId }).ToJson(true));
            }
            return;




            var rzgc = new IsscRestClient(IsscConfig.GetByEnvironmentType(EnvironmentType.Live));
            rzgc.RegisterProvider("RP", 113693, 2055343, "SC1002J", "26-2644162");
            return;
            HonkTest();
            return;
            string tempPath = System.IO.Path.GetTempPath();
            Console.WriteLine(tempPath);
            int tran = 0;
            MotorClubDispatchingService.SendXmlToQuest(DigitalDispatchActionQueueItem.GetById(*********), 
                "<test></test>", ref tran);
            return;


            await ReportTest();
            return;

            ReplayEmailParser(79258346);
            //Console.WriteLine(DateTime.Now.ToString("o"));

            
            return;


            var entest = Entry.GetById(72936814);
            entest.Invoice.ForceRecalculate(false);


            Console.WriteLine(entest.InvoiceItems.Select(o => new { o.Id, o.Name, o.Quantity, o.Price, o.Taxable, o.Total }).ToJson(true));
            Console.WriteLine(entest.InvoiceTax);
            Console.WriteLine(entest.InvoiceTotal);
            return;



            WebGlobal.CurrentUser = new User() { Type = User.TypeEnum.Manager, CompanyId = 16368 };
            while (true)
            {
                TestAzureReverseMap();
                Console.ReadLine();
            }
            return;

            
            return;

            FixAgero();

            //SyncGeicoPayments(595864, "**********", "central2031");
            return;

            WebGlobal.CurrentUser = User.GetByUsername("kendall12");
            var calls1 = await CallsController.GetCallsByStatusRange(new[] { 10000 }, 5, 1, 20, new int[] { 192887 }, 0);

            Console.WriteLine(calls1.ToJson(true));
            return;



            var akv = AccountKeyValue.GetFirstValueOrNull(10000, 1254781, Provider.Towbook.ProviderId, "DefaultEta");
            Console.WriteLine(akv);

            var akv2 = AccountKeyValue.GetByAccount(10000, 1254781, Provider.Towbook.ProviderId, "DefaultEta").FirstOrDefault();
            Console.WriteLine(akv2.ToJson());
            //akv2.Save();
            
            return;





            Parallel.ForEach(Company.SharedCompany.GetByCompanyId(26130),
                new ParallelOptions() { MaxDegreeOfParallelism = 16 },
                async (c) =>
                {
                    await Services.MotorClubDispatchingService.AutoDispatch.CompanyDispatchStatusServiceBusHandler.UpdateCompanyDispatchStatus(c.SharedCompanyId, null, null, null, null);
                });

            return;
            PredictedArrivalEtas.PredictedArrivalService.RunPredictiveArrivalTimes();
            return;

            foreach (var rxx in Company.SharedCompany.GetByCompanyId(26130)) {
                CompanyKeyValueInsertOrUpdate(rxx.SharedCompanyId, Provider.Towbook.ProviderId,
                    "DispatcherRoundRobin_SetDispatcherJson",
                    null);
            }
            return;

            Core.DeleteRedisKey("mars:" + 20);
            return;
            UrgentlyTestClient.RepairAccount(13891);
            return;

            var isc = new IsscRestClient(IsscConfig.GetByEnvironmentType(EnvironmentType.Live));
            isc.RegisterProvider("ADS", 8831, 872310, "**********", "46-4652212", "");
        

            return;


            CompanyKeyValueInsertOrUpdate(4185, Provider.Towbook.ProviderId, "PreventDriversFromModifyingCalls", "1");
            CompanyKeyValueInsertOrUpdate(4189, Provider.Towbook.ProviderId, "PreventDriversFromModifyingCalls", "1");
            CompanyKeyValueInsertOrUpdate(4194, Provider.Towbook.ProviderId, "PreventDriversFromModifyingCalls", "1");
            return;

            //   MotorClubDispatchingService.ShouldAllowCancel(Entry.GetById(********));
            return;

            DirectBillingTestSubmit(4189, 97973, false);
            DirectBillingTestSubmit(4189, 98020, false);
            DirectBillingTestSubmit(4189, 98008, false);
            DirectBillingTestSubmit(4189, 98004, false);
            DirectBillingTestSubmit(4189, 97973, false);
            DirectBillingTestSubmit(4189, 98020, false);


            return;
            //DirectBillingTestSubmit(4185, 102149, false);
            //DirectBillingTestSubmit(4185, 102143, false); // fails on purpose
            //DirectBillingTestSubmit(4185, 102127, false); // fails on purpose
            //DirectBillingTestSubmit(4185, 102147,false);
            //DirectBillingTestSubmit(4185, 102124,false);

            //DirectBillingTestSubmit(4189, 98005);

            //DirectBillingTestSubmit(4189, 98008, false);
            //DirectBillingTestSubmit(4189, 98005, false);
            //DirectBillingTestSubmit(4189, 98004, false);
            DirectBillingTestSubmit(4189, 97973, false);
            DirectBillingTestSubmit(4189, 98020, false);
            DirectBillingTestSubmit(4189, 98008, false);
            DirectBillingTestSubmit(4189, 98004, false);
            DirectBillingTestSubmit(4189, 97973, false);
            DirectBillingTestSubmit(4189, 98020, false);
            //DirectBillingTestSubmit(4189, 98015);
            //DirectBillingTestSubmit(4189, 98044);
            //DirectBillingTestSubmit(4189, 98041);
            //DirectBillingTestSubmit(4189, 98020);


            foreach (var i in new int[] { 27461 })
            {

                Core.SetRedisValue("blockCancelFromDispatchers:" + i, "1");
            }
            return;

            //DirectBillingTestSubmit(4185, 102132, false); // 

            //DirectBillingTestSubmit(4185, 102157, false); -- FIRST INVOICE SUBMITTED SUCCESSFULLY
            //DirectBillingTestSubmit(4185, 102123, false); -- SECOND INVOICE SUBMITTD SUCCESSFULLY
            //DirectBillingTestSubmit(4185, 102129, false); // THIRD
            //DirectBillingTestSubmit(4185, 102147, false); // fifth
            //DirectBillingTestSubmit(4185, 102149, false); // fourth

            //DirectBillingTestSubmit(4185, 102185, false);
            //DirectBillingTestSubmit(4185, 102194, false);
            //DirectBillingTestSubmit(4185, 102173, false);
            //DirectBillingTestSubmit(4185, 102109, false);
            //DirectBillingTestSubmit(4185, 102102, false);

            //DirectBillingTestSubmit(4185, 102115, false);
            //DirectBillingTestSubmit(4185, 102178, false);
            //DirectBillingTestSubmit(4185, 102182, false);
            //DirectBillingTestSubmit(4185, 102193, false);
            //DirectBillingTestSubmit(4185, 102206, false);

            //DirectBillingTestSubmit(4185, 102178, false);
            //DirectBillingTestSubmit(4185, 102193, false);
            //DirectBillingTestSubmit(4185, 102182, false);
            //DirectBillingTestSubmit(4185, 102165, false);
            return;

            MoveConnections(sourceCompanyId: 10000, destinationCompanyId: 7124, contractorId: "MI1235", locationId: "", forceAccountId: 0);


            return;
            Console.WriteLine((await DispatchController.getAvailableUsersToDispatchAsync(Entry.GetById(********))).ToJson(true));
            return;

            
            
            return;
                            
            GetPO(102626, *********);

            return;

            WebGlobal.CurrentUser = User.GetByUsername("carlreid854");
            var xrrr = new DispatchController().History(********);



            foreach (var x in User.GetByCompanyId(26130).Where(o => o.Type == User.TypeEnum.Dispatcher && (o.Notes == null || !o.Notes.Contains("GrantSupervisor"))))
            {
                Console.WriteLine(x.Username + "..." + x.FullName);
                new Company.CompanyUser() { UserId = x.Id, CompanyId = 31020 }.Save();
                new Company.CompanyUser() { UserId = x.Id, CompanyId = 31021 }.Save();
            }
            return;





            Company.CompanyUser.GetByUserId(User.GetByUsername("steeli66").Id).First().Delete();

            Console.WriteLine(User.GetByUsername("steeli66").CompanyId);return;

            MoveConnections(4254, 4254, "NC11188152", "ab:1710819", 703836);
            MoveConnections(8340, 30162, "134224", null, 693440);
            MoveConnections(8340, 30162, "33257");

            return;



            var calls = await Entry.GetByPurchaseOrderNumbersByMasterAccountIdAsync(
                20005,
                MasterAccountTypes.Geico, 
                new string[] { "G337218335" });

            Console.WriteLine(calls.Select(o => 
                o.PurchaseOrderNumber).ToJson());

            return;

            var photoGeo = new GeoCoordinate(42.826013, -82.485936);
            var pickupGeo = new GeoCoordinate(42.824626, -82.485961);

            Console.WriteLine(photoGeo.GetDistanceTo(pickupGeo));
            return;



            await TestGps();
            return;
            await MoveConnections(9026, 9260, "OH1961754", "ab:3317736");
            return;




            return;

            Cache.Instance.PartitionReset<Dispatch.Reason>(4185);
            Cache.Instance.PartitionReset<Dispatch.Reason>(4189);
            Cache.Instance.PartitionReset<Dispatch.Reason>(4194);
            return;

            fleetnet.FleetnetRestClient.Get().OnlineNotification("10000", "web");

            return;

            MoveConnections(10193, 22545, "FL1960977", "ab:3238431");
            return;




            

            // fleetnet.FleetnetRestClient.Get().AcceptServiceRequest("47875", 45, "");

            return;
            MoveConnections(2015, 2015, "MO1959881", forceAccountId: 614319);
            return;

            Dispatch.Reason.GetByCompany(Company.Company.GetById(4185));
            Dispatch.Reason.GetByCompany(Company.Company.GetById(4189));
            Dispatch.Reason.GetByCompany(Company.Company.GetById(4194));
            return;





            fleetnet.FleetnetRestClient.Get().SignIn("10000");
            return;

            await TestCallRequestEventNotification();
            return;

            SyncAllstatePayments(351618, "AR0800410", "Twin2017!", "AR0800410", new DateTime(2018, 1, 30), new DateTime(2018, 2, 6));
            return;

            await SyncGeicoPayments(12374, "HI15277065", "Dansens-7");
            return;

            GetTomTomIOEvents();
            return;

            TestTomTom();

                return;

            TestQueue();

            return;

                FindAllstatePOs();
                return;

                SyncAllstatePayments(161260, "*********", "David143", "*********", new DateTime(2016, 10, 1), new DateTime(2016, 11, 5));
                return;

                await SyncGeicoPayments(70050, "*********", "towrjb446522");
                return;




                TestAllstateDistributedLoginLocks();
                return;


                var qss = Agent.Sync.QuickbooksSyncService.Get(1);

                //            Console.WriteLine(qss.Push(new Agent.QuickBooks.PaymentMethod() { Name = "XDAN", AgentSessionId = 1 }));
                for (int i = 0; i < 100; i++)
                    Console.WriteLine(qss.Push(new Agent.QuickBooks.Class() { Name = "Hosting", AgentSessionId = 1 }));

                return;



                var qbx14 = await QuickbooksUtility.GetConnector(3509);

                Console.WriteLine(qbx14.GetItemById("90:QB").ToJson(true));
                return;

                var arc = new AgeroRestClient();

                Console.WriteLine(arc.GetFacilities("gpoLi9DtvsES9TrzAfASv3gp7LU9", 26163).ToJson(true));
                return;


                var qbx = new QBX.DataService();


                if (false)
                {
                    var n = new Agent.QuickBooks.Class();
                    n.AgentSessionId = 1;
                    n.IsActive = true;
                    n.Name = "dan";
                    n.ObjectId = "1234";
                    qbx.SaveX(n);
                }

                var n1 = new Agent.QuickBooks.PaymentMethod();
                n1.AgentSessionId = 1;
                n1.IsActive = true;
                n1.Name = "dan";
                n1.ObjectId = "1234";
                //qbx.SaveX(n1);
                n1.Name += "-modified";
                //qbx.SaveX(n1);

                n1.Name += "-modified-twice";
                //qbx.SaveX(n1);

                var n2 = new Agent.QuickBooks.Item();
                n2.Name = "test";
                n2.ObjectId = "test1234";
                n2.EditSequence = "required";
                n2.AccountName = "Sales";
                n2.AgentSessionId = 1;
                n2.Description = "towbook";
                n2.Price = 1;

                var e1 = new Agent.Sync.Event();
                var e2 = new Agent.Sync.EventRequestData();
                var e3 = new Agent.Sync.EventResponseData();


                Console.WriteLine(new
                {
                    Events = new object[] { e1 },
                    EventRequestData = new object[] { e2 },
                    EventResponseData = new object[] { e3 }
                }.ToJson(null));

                return;


                qbx.SaveX(n2);
                n2.Description = "modified";
                qbx.SaveX(n2);



                if (false)
                    Console.WriteLine(SqlMapper.Query<Agent.QuickBooks.Class>("select *, ClassId as Id From agent.qbclasses where AgentSessionId=@Id",
                        new { id = 1 }).ToJson());

                Console.WriteLine(SqlMapper.Query<Agent.QuickBooks.Item>("select *, ItemId as Id From agent.qbitems where AgentSessionId=@Id",
                    new { id = 1 }).ToJson(true));

                Console.WriteLine(SqlMapper.Query<Agent.QuickBooks.PaymentMethod>("select *, PaymentMethodId as Id From agent.qbpaymentMethods where AgentSessionId=@Id",
                new { id = 1 }).ToJson(true));








                return;

                RunWeeklyAgeroPaymentImport();
                return;


                Message m = Message.CreateMessage(MessageVersion.CreateVersion(System.ServiceModel.EnvelopeVersion.Soap11), "SomethingResult");

                Console.WriteLine(m.ToString());
                return;












            return;


                // 30 char limit on reject reason
                //NsdResponse("82429", "74359df8-6e65-4e08-8783-85bb324ba984", "3699829", "phone", 45);
                NacResponse("82429", "74359df8-6e65-4e08-8783-85bb324ba984", "3699829", "phone", 45);
                NacResponse("82429", "74359df8-6e65-4e08-8783-85bb324ba984", "3699829", "accept", 45);
                NacResponse("82429", "74359df8-6e65-4e08-8783-85bb324ba984", "3699829", "reject", 45);
                return;


            

            
                return;


                logger.LogEvent("test", 1, LogLevel.Info, new Dictionary<object, object> { ["Hello"] = "world" });

                Console.ReadLine();
                return;


                var mk = MimeKit.MimeMessage.Load(@"e:\temp\2273526.eml");

                var aspfep = AgeroEmailParser.FromText(mk.HtmlBody);

                Console.WriteLine(aspfep.ToJson(true));

                return;



                RunWeeklyAgeroPaymentImport();
                return;


                TestDSR();
                return;
                Console.WriteLine(DateTime.Now.ToJson());
                return;


                var eds = EntryDamage.GetById(6);
                eds.Save();


                return;


                RunWeeklyAgeroPaymentImport();

                while (true)
                {
                    AutoLoginAllstate();


                    //    Thread.Sleep(1000 * 60 * 60 * 3);
                    break;
                }
                return;



                var zcm = new ChatMessage();

                zcm.ChatId = 1;
                zcm.SenderUserId = 1;
                zcm.Message = "Hello, world! ARe you out there?";
                zcm.Save();

                ChatMessage.MarkRead(1, 6, 10);
                ChatMessage.MarkDelivered(1, 6, 10);


                return;

                return;


                return;


                var xmr = new UserHideMessage();
                xmr.UserId = 1;
                xmr.MessageId = 5;
                xmr.Record();
                return;






                var nm = NotificationMessage.GetById(13);
                nm.StatusResponse = "tst";
                nm.Status = NotificationMessageStatus.Accepted;
                nm.StatusTime = DateTime.Now;
                await nm.Save();

                return;

                Console.WriteLine(VehicleUtility.GetModelByName("town &amp; country"));

                Console.WriteLine(VehicleUtility.GetColorIdByName("grey"));
                Console.WriteLine(VehicleUtility.GetColorIdByName("gray"));
                Console.WriteLine(VehicleUtility.GetColorIdByName(""));
                Console.WriteLine(VehicleUtility.GetColorIdByName(null));
                Console.WriteLine(VehicleUtility.GetManufacturerByName("hond"));
                Console.WriteLine(VehicleUtility.GetManufacturerByName("TOYT"));
                Console.WriteLine(VehicleUtility.GetManufacturerByName(null));
                Console.WriteLine(VehicleUtility.GetManufacturerByName(""));


                return;


                Console.WriteLine(nameof(Main));
                return;



                var bsx = JsonConvert.DeserializeObject<dynamic>(
                    "{'nonce':'e735c5d3-c92e-4b2a-bd22-f789459506aa','details':{'lastTwo':'11','cardType':'Visa'},'type':'CreditCard'}");




                #region Initialize the gateway

                var gateway = new BraintreeGateway
                {
                    Environment = Braintree.Environment.SANDBOX,
                    MerchantId = "ph5f67pgppwkdthk",
                    PublicKey = "bgcf56mzq4jyhfr7",
                    PrivateKey = "f8c1585645c39b5e8e33eba9ad7dc997"
                };

                #endregion

                var cc = gateway.Customer.Find("T1");
                var subscriptions = cc.PaymentMethods.Where(o => o is CreditCard)
                    .Select(r => r as CreditCard);

                var activeSubscriptions = new List<Subscription>();

                foreach (var cpm in subscriptions)
                {
                    activeSubscriptions.AddRange(cpm.Subscriptions.Where(o => o.Status == SubscriptionStatus.ACTIVE));
                }

                Console.WriteLine("Active Subscriptions: " + activeSubscriptions.Count());

                if (activeSubscriptions.Count() != 1)
                {
                    Console.WriteLine("Active Subscriptions cannot be updated.. It is : " + activeSubscriptions + "... only 1 is supported");
                    return;
                }


                #region Find a subscription
                var ss = gateway.Subscription.Find("S1");
                ss = activeSubscriptions.Single();
                #endregion

                Braintree.CreditCard pm = gateway.PaymentMethod.Find(ss.PaymentMethodToken) as CreditCard;

                PaymentMethodRequest pmm = new PaymentMethodRequest();
                pmm.Token = ss.PaymentMethodToken;
                // update the existing card. 
                pmm.PaymentMethodNonce = "fake-valid-country-of-issuance-usa-nonce";
                //pmm.PaymentMethodNonce = "fake-processor-declined-amex-nonce";
                pmm.PaymentMethodNonce = "fake-valid-mastercard-nonce";
                pmm.Options = new PaymentMethodOptionsRequest();
                pmm.Options.VerifyCard = true;


                var updatePayment = gateway.PaymentMethod.Update(ss.PaymentMethodToken, pmm);
                //Console.WriteLine(updatePayment.ToJson(true));
                Console.WriteLine(updatePayment.Message);
                Console.WriteLine(updatePayment.GetType());
                var verf = (updatePayment.Target as CreditCard)?.Verification;

                if (verf?.ProcessorResponseCode == "1000")
                {
                    Console.WriteLine("!!!SUCCESS!!!" + verf.ProcessorResponseText);
                }
                else if (updatePayment.CreditCardVerification != null)
                {
                    Console.WriteLine("!!! FAIL !!! payment update failed!");
                    Console.WriteLine(updatePayment.CreditCardVerification.ProcessorResponseCode);
                    Console.WriteLine(updatePayment.CreditCardVerification.ProcessorResponseText);
                }

                return;



                Customer existingCustomer = null; // gateway.GetCustomerByIdOrNull("T2");

                // create the customer if it doesn't exist already.
                if (existingCustomer == null)
                {
                    var request = new CustomerRequest();
                    request.FirstName = "Dan";
                    request.Id = "T2";
                    request.Company = "Dans Towing";
                    request.CustomFields = new Dictionary<string, string>();

                    Result<Customer> result = gateway.Customer.Create(request);

                    if (result.Message == "Customer ID has already been taken.")
                    {
                        Console.WriteLine($"Couldn't create customer: ID is already in use {request.Id}");
                        return;
                    }
                    Console.WriteLine(result.ToJson());
                }






                return;
                


                isc.RegisterProvider("GCOAPI", 6971, 112264, "FL13237598", "59-3416150", "ab:1716807");
                isc.RegisterProvider("GCO", 6971, 112264, "FL13237598", "59-3416150");
                isc.RegisterProvider("GCO", 6971, 115394, "FL14250471", "59-3416150");
                isc.RegisterProvider("GCO", 6971, 112268, "FL14250472", "59-3416150");
                isc.RegisterProvider("GCO", 6971, 115394, "FL14250473", "59-3416150");

                return;





                //Console.WriteLine(result2.ToJson());

                Console.WriteLine(bsx.nonce);

                Console.WriteLine(JsonExtensions.ToJson(bsx));
                return;



                RunWeeklyAgeroPaymentImport();
                return;

                while (true)
                {
                    AutoLoginAllstate();


                    //    Thread.Sleep(1000 * 60 * 60 * 3);
                    break;
                }


                return;



                return;



                return;
                var icm = new InboundCallModel();

                Console.WriteLine(icm.ToJson(true));
                return;


                Console.WriteLine(Stickering.Sticker.GetById(316136).ToJson(null));
                Console.WriteLine(Stickering.Reason.GetByStickerId(3356).ToJson(null));
                return;


                var en = Entry.GetById(4866716);

                await en.Delete(AuthenticationToken.GetByToken("fb850a80d78b4bfa91a35d5258a29d83dc45819e1fcf48ef98633dd9e65bf66f"), "1.2.3.4");

                return;



                isc.RegisterProvider("RDAM", 2015, 21476, "112038", "61-1651267", "001");
                isc.RegisterProvider("RDAM", 2015, 21476, "112038", "61-1651267", "002");
                isc.RegisterProvider("RDAM", 5154, 123727, "M103725", "46-1377928", "001");
                isc.RegisterProvider("RDAM", 7395, 121887, "M96191", "27-2609483", "005");
                isc.RegisterProvider("RDAM", 7395, 121887, "M96191", "27-2609483", "001");
                isc.RegisterProvider("RDAM", 7454, 122825, "112274", "81-0822532", "001");
                isc.RegisterProvider("RDAM", 7395, 122551, "M96199", "27-2609483", "001");
                return;

                return;

                isc.RegisterProvider("GCOAPI", 2887, 39665, "NY12208899", "47-2881790", "ab:1713071");

                return;


                RunWeeklyAgeroPaymentImport();
                return;


                AutoLoginAllstate();
                return;

                string vx1 = Core.GetRedisValue("keyZ");

                string vx = Core.GetRedisValue("key");

                //MotorClubMapper.Translate(), 2, 1, false, 0);

                return;


                ChatMessage cm = new ChatMessage();
                cm.SenderUserId = 1;
                cm.ChatId = 1;
                cm.Message = "Hello, world!";
                var swz = Stopwatch.StartNew();
                cm.Save();
                Console.WriteLine(swz.ElapsedMilliseconds);
                return;

                RunWeeklyAgeroPaymentImport();

                return;
                /* 
                            RunWeeklyAgeroPaymentImport();
                            return;



                            DigitalDispatchService.Login(7225, 117042, 1);
                            return;


                            var xl2l = new Logging("Test");

                            xl2l.EventLogger.LogEvent("Hello, world.", 5, LogLevel.Info); ;

                            xl2l.EventLogger.LogEvent("User johndoe logged in.", 2, LogLevel.Info);
                            xl2l.EventLogger.LogEvent("Call dispatched", 4082, LogLevel.Info);
                            xl2l.EventLogger.LogEvent("Error while dispatching a call", 800, LogLevel.Error); ;
                            Console.ReadLine();
    */


                return;

                isc.RegisterProvider("GCOAPI", 6588, 119937, "**********", "25-1920447", "ab:1713467");
                isc.RegisterProvider("GCO", 6588, 119937, "**********", "25-1920447");

                isc.RegisterProvider("GCOAPI", 7382, 106282, "**********", "25-1920447", "ab:1784352");
                isc.RegisterProvider("GCO", 7382, 106282, "**********", "25-1920447");

                isc.RegisterProvider("GCOAPI", 7537, 124408, "*********", "22-2860574", "ab:1740299");
                isc.RegisterProvider("GCO", 7537, 124408, "*********", "22-2860574");

                return;

                isc.RegisterProvider("GCOAPI", 2401, 30631, "**********", "46-1792915");
                isc.RegisterProvider("GCO", 2401, 30631, "**********", "46-1792915");
                return;
                return;
                AutoLoginAllstate();
                return;

                var xx = Stopwatch.StartNew();
                //Core.GetRedisDatabase().StringSet("key", "test");

                string v1 = Core.GetRedisDatabase().StringGet("keyZ");

                string v = Core.GetRedisDatabase().StringGet("key");
                Console.WriteLine(v);
                Console.WriteLine(xx.ElapsedMilliseconds);

                xx.Restart();
                v = Core.GetRedisDatabase().StringGet("key");
                Console.WriteLine(v);
                Console.WriteLine(xx.ElapsedMilliseconds);

                Console.WriteLine(Core.GetRedisConnection().GetCounters().ToJson(true));
                return;


                RunWeeklyAgeroPaymentImport();
                return;

                AutoLoginAllstate();
                return;

                isc.RegisterProvider("RDAM", 4273, 65744, "M105459", "46-288675", "001");
                isc.RegisterProvider("RDAM", 4273, 65744, "M105459", "46-288675", "001");
                isc.RegisterProvider("RDAM", 5346, 115371, "M108724", "47-3075201", "001");
                isc.RegisterProvider("RDAM", 7128, 115768, "M93329", "27-0386195", "003");
                isc.RegisterProvider("RDAM", 6729, 108104, "M107650", "46-4435178", "001");
                isc.RegisterProvider("RDAM", 6201, 102678, "M108368", "46-5036547", "001");
                isc.RegisterProvider("RDAM", 7128, 115580, "M93329", "27-0386195", "001");
                return;

                isc.RegisterProvider("RDAM", 2205, 25700, "M100834", "94-3011480", "001");
                isc.RegisterProvider("RDAM", 2205, 25700, "M100834", "94-3011480", "004");
                isc.RegisterProvider("RDAM", 2205, 25700, "M105078", "94-3011480", "002");
                isc.RegisterProvider("RDAM", 1917, 21772, "46927", "73-1541683", "001");
                isc.RegisterProvider("RDAM", 6384, 103259, "12062", "95-4560802", "001");
                isc.RegisterProvider("RDAM", 2822, 41158, "M99584", "48-1264828", "001");
                isc.RegisterProvider("RDAM", 4631, 70319, "119358", "46-5028302", "001");
                isc.RegisterProvider("RDAM", 2064, 23937, "M106036", "45-5029385", "001");
                isc.RegisterProvider("RDAM", 2064, 23937, "M106036", "45-5029385", "002");
                isc.RegisterProvider("RDAM", 2400, 28680, "M101311", "45-4348045", "001");
                isc.RegisterProvider("RDAM", 2822, 41158, "M99584", "48-1264828", "001");
                isc.RegisterProvider("RDAM", 3172, 44403, "M108362", "34-1957439", "003");
                isc.RegisterProvider("RDAM", 4082, 67149, "121831", "20-0403470", "001");
                isc.RegisterProvider("RDAM", 6522, 104734, "120934", "68-0307261", "001");
                isc.RegisterProvider("RDAM", 1873, 19721, "M105381", "04-3307454", "001");
                isc.RegisterProvider("RDAM", 5832, 94843, "C102990", "27-1821417", "001");
                isc.RegisterProvider("RDAM", 5678, 92521, "M99073", "27-3035639", "001");
                isc.RegisterProvider("RDAM", 1367, 53603, "C115853", "27-3060889", "001");
                isc.RegisterProvider("RDAM", 3490, 54058, "M101127", "30-0298145", "001");
                isc.RegisterProvider("RDAM", 3490, 54058, "C117718", "30-0298145", "001");
                isc.RegisterProvider("RDAM", 2364, 30591, "M107869", "34-1862164", "001");
                isc.RegisterProvider("RDAM", 4883, 73880, "M99661", "45-2894409", "002");
                isc.RegisterProvider("RDAM", 1306, 13975, "M98752", "46-0929770", "003");
                isc.RegisterProvider("RDAM", 1306, 13975, "M98752", "46-0929770", "002");
                isc.RegisterProvider("RDAM", 1306, 13975, "M98752", "46-0929770", "001");
                isc.RegisterProvider("RDAM", 6333, 101548, "M105113", "46-2596680", "001");
                isc.RegisterProvider("RDAM", 4521, 68979, "M108227", "47-1139713", "001");
                isc.RegisterProvider("RDAM", 2887, 43367, "M111062", "47-2881790", "001");
                isc.RegisterProvider("RDAM", 4770, 82510, "M108753", "47-3257429", "001");
                isc.RegisterProvider("RDAM", 2264, 24913, "43655", "58-2612522", "002");
                isc.RegisterProvider("RDAM", 2015, 69744, "M99834", "61-1651267", "001");
                isc.RegisterProvider("RDAM", 2015, 69745, "M99834", "61-1651267", "002");
                isc.RegisterProvider("RDAM", 5079, 79768, "M102058", "80-0870689", "001");


                // {"preregistrations":[{"ContractorID":"VA13223838","LocationID":"ab:1850048","LocationName":"...","Registered":false,"AddressType":"Dispatch","AddressLine1":"317 BYPASS RD","City":"WILLIAMSBURG","State":"Virginia","ZipCode":"23185-2922"}],"Result":"Success","Event":"preregistrations","Description":null}


                return;
                isc.RegisterProvider("GCOAPI", 2364, 28227, "PA14244374", "34-1862164", "ab:1717267");
                isc.RegisterProvider("GCO", 2364, 28227, "PA14244374", "34-1862164");
                return;
                isc.RegisterProvider("GCOAPI", 5893, 95575, "VA9704670", "54-1226687", "ab:1735375");
                isc.RegisterProvider("GCO", 5893, 95575, "VA9704670", "54-1226687");

                isc.RegisterProvider("GCOAPI", 6919, 111632, "PA11190346", "46-3417485", "ab:1710279");

                isc.RegisterProvider("GCOAPI", 7031, 113215, "SC0512112", "20-2164119", "ab:1742095");
                isc.RegisterProvider("GCO", 7031, 113215, "SC0512112", "20-2164119");
                isc.RegisterProvider("GCO", 7031, 115730, "SC11190940", "20-2164119");
                // {"preregistrations":[{"ContractorID":"VA9704670","LocationID":"ab:1735375","LocationName":"...",
                // "Registered":true,"AddressType":"Dispatch","AddressLine1":"1049 CAMPBELL AVE SE","City":"ROANOKE","State":"Virginia","ZipCode":"24013-1136"}],"Result":"Success","Event":"preregistrations","Description":null}

                return;



            

                isc.Login(637);
                isc.Login(638);
                isc.Login(640);
                isc.Login(641);
                isc.Login(642);
                isc.Login(646);
                isc.Login(650);
                isc.Login(655);
                isc.Login(656);
                isc.Login(657);
                isc.Login(658);
                return;


                isc.RegisterProvider("RDAM", 3887, 59338, "M110142", "47-2243018", "001");
                isc.RegisterProvider("RDAM", 6919, 111633, "M107125", "46-3417485", "001");
                isc.RegisterProvider("RDAM", 6954, 112008, "M107800", "46-4741097", "001");
                isc.RegisterProvider("RDAM", 6925, 111722, "M108681", "56-2094881", "001");
                isc.RegisterProvider("RDAM", 6990, 112669, "M108727", "46-3859578", "001");
                isc.RegisterProvider("RDAM", 3820, 58438, "M111665", "05-5645730", "001");
                isc.RegisterProvider("RDAM", 5163, 86109, "M107620", "46-1543806", "001");

                isc.RegisterProvider("RDAM", 6880, 110861, "6852", "51-0626277", "001");
                isc.RegisterProvider("RDAM", 7069, 114664, "49868", "58-2268320", "001");
                isc.RegisterProvider("RDAM", 7043, 114236, "M108289", "46-4295857", "001");
                isc.RegisterProvider("RDAM", 4281, 115447, "19575", "27-1484911", "001");
                return;

                isc.RegisterProvider("GCOAPI", 7055, 114368, "NY14245193", "16-1495457", "ab:1717463");
                isc.RegisterProvider("GCO", 7055, 114368, "NY14245193", "16-1495457");

                //{"preregistrations":[{"ContractorID":"NY14245193","LocationID":"ab:1717463","LocationName":"...","Registered":false,"AddressType":"Dispatch","AddressLine1":"2501 MILITARY RD","City":"NIAGARA FALLS","State":"New York","ZipCode":"14304-1505"}],"Result":"Success","Event":"preregistrations","Description":null}
                return;

                var xll = new DigitalDispatchService();
                var gcc = DigitalDispatchActionQueueItem.GetById(1054254);

                var dic = new Dictionary<string, object>();
                dic.Add("IsscConfigId", "1");
                await DigitalDispatchService.HandleCallEventAsync(5677, 94226,
                    "{'AuthorizationNumber':'G163515338','MemberCallBackNumber':'************','CustomerContactMethod':'Cell','CashPaymentOnly':null,'ClientID':'GCOAPI','ContractorID':'*********','LocationID':'ab:1736151','DispatchID':'7293747','ResponseID':'519768','JobID':'519768','Result':null,'Event':'dispatch.accepted','Description':null}"
                    , DigitalDispatchService.CallEventType.Accepted, dic
                    );

                return;


                Console.ReadLine();

                return;

                Console.WriteLine(GeicoSecondaryEmailParser.FromHtml(MimeKit.MimeMessage.Load(@"E:\temp\1738897.eml").HtmlBody).ToJson(true));

                return;

                isc.Login(455);
                isc.Login(456);
                isc.Login(457);
                isc.Login(458);
                isc.Login(459);
                isc.Login(460);
                isc.Login(461);
                isc.Login(462);
                isc.Login(463);
                isc.Login(464);
                isc.Login(465);
                isc.Login(466);
                isc.Login(467);
                isc.Login(468);
                isc.Login(469);
                isc.Login(470);
                isc.Login(471);
                isc.Login(472);
                isc.Login(473);
                isc.Login(474);
                isc.Login(475);
                isc.Login(476);
                isc.Login(477);
                isc.Login(478);
                isc.Login(479);
                isc.Login(480);
                isc.Login(481);
                return;

                /*
                var arc = new AgeroRestClient();


                AutoLoginAllstate();
                return;

                AutoLoginAllstate();
                return;
                TestAllstateBeta();
                return;
                */

                //isc.PreRegistrations("GCOAPI", "AR1520279", "26-3425444");

                isc.RegisterProvider("RDAM", 1713, 17136, "M100792", "45-3000799", "001");
                isc.RegisterProvider("RDAM", 1713, 17136, "M100792", "45-3000799", "002");
                isc.RegisterProvider("RDAM", 1713, 17136, "M100792", "45-3000799", "001");
                isc.RegisterProvider("RDAM", 1713, 17136, "M100792", "45-3000799", "002");
                isc.RegisterProvider("RDAM", 726, 19479, "M103680", "45-4363718", "005");
                isc.RegisterProvider("RDAM", 726, 19479, "M103680", "45-4363718", "006");
                isc.RegisterProvider("RDAM", 726, 19479, "M103680", "45-4363718", "001");
                isc.RegisterProvider("RDAM", 2599, 33372, "M104552", "52-1886525", "001");
                isc.RegisterProvider("RDAM", 2689, 34863, "M99783", "47-4008338", "001");
                isc.RegisterProvider("RDAM", 3465, 53320, "M9837", "31-1727258", "001");
                isc.RegisterProvider("RDAM", 3643, 56290, "6219", "20-4104616", "001");
                isc.RegisterProvider("RDAM", 3853, 58932, "M98413", "431626068", "001");
                isc.RegisterProvider("RDAM", 4817, 73269, "M90528", "20-4994019", "002");
                isc.RegisterProvider("RDAM", 4817, 73269, "M90528", "20-4994019", "003");
                isc.RegisterProvider("RDAM", 4817, 73269, "M90528", "20-4994019", "001");
                isc.RegisterProvider("RDAM", 4067, 73377, "M110952", "47-2571687", "001");
                isc.RegisterProvider("RDAM", 4375, 77284, "M109579", "46-3392779", "001");
                isc.RegisterProvider("RDAM", 5026, 78968, "M108634", "47-3629638", "001");
                isc.RegisterProvider("RDAM", 5124, 82365, "C117072", "46-2321560", "001");
                isc.RegisterProvider("RDAM", 5124, 82365, "C117072", "46-2321560", "002");
                isc.RegisterProvider("RDAM", 5241, 85600, "M95618", "68-0471443", "001");
                isc.RegisterProvider("RDAM", 5443, 88178, "M108810", "27-2621230", "001");
                isc.RegisterProvider("RDAM", 5582, 91207, "M108759", "47-4198152", "001");
                isc.RegisterProvider("RDAM", 6037, 97928, "C115735", "45-5284607", "001");
                isc.RegisterProvider("RDAM", 6037, 97928, "C115735", "45-5284607", "001");
                isc.RegisterProvider("RDAM", 6122, 98980, "M108069", "46-4386015", "001");
                isc.RegisterProvider("RDAM", 6646, 107063, "M110791", "43-5611898", "001");
                isc.RegisterProvider("RDAM", 6651, 107095, "111867", "47-4813663", "001");
                isc.RegisterProvider("RDAM", 6716, 107960, "M103883", "27-1621012", "001");
                isc.RegisterProvider("RDAM", 6724, 108051, "M110983", "11-3843558", "001");


                return;

                //isc.DeregisterProvider("GCO", "NC14240726", "ab:1717111", "189D3A26-0E7B4536-B931EF10-819F9F61");
                isc.Login(296);
                return;

                isc.Login(296);
                isc.Login(287);
                isc.Login(288);
                isc.Login(290);
                isc.Login(292);
                isc.Login(294);
                isc.Login(298);
                isc.Login(299);
                isc.Login(301);
                isc.Login(304);
                isc.Login(306);

                return;



                return;



                RunWeeklyAgeroPaymentImport();
                return;


                int dCompanyId = 2394;
                bool performDelete = false;
                Console.WriteLine(performDelete);

                QuickBooksOnlineConnector qb = (QuickBooksOnlineConnector)await QuickbooksUtility.GetConnector(dCompanyId);
                Stopwatch sw = Stopwatch.StartNew();
                //CreateMissingLinksForQb(dCompanyId, qb);

                var invoices = qb.GetInvoices().ToList();
                var payments = qb.GetPayments(1, 1).ToList();

                if (true) {
                    var filledPayments = new List<Integrations.Quickbooks.Model.IPayment>();
                    object lck = "";

                    Parallel.ForEach(payments,
                        new ParallelOptions() { MaxDegreeOfParallelism = 10 },
                        (o) =>
                    {
                        var x = qb.GetPaymentById(o.PaymentId);
                        lock (lck)
                        {
                            filledPayments.Add(x);
                        }

                        Console.Title = sw.ElapsedMilliseconds + "ms";
                    });

                    payments = filledPayments.Where(o => o.Header.DepositToAccountName == "Undeposited Funds").ToList();

                    Console.Title = sw.ElapsedMilliseconds + "ms";

                    var gp = payments.Select(o => new { Id = o.PaymentId, Amount = o.Header.Amount, RefNum = o.ReferenceNumber, InvoiceId = o.Lines.FirstOrDefault().ToString() });

                    // How many are linked?
                    Console.WriteLine(payments.Where(o => o.Lines.Any()).Count() + " are linked and are valid.");
                    Console.WriteLine(payments.Where(o => !o.Lines.Any()).Count() + " have no linked invoices, and are orphaned. these need to be deleted. they add up to...");
                    Console.WriteLine(payments.Where(o => !o.Lines.Any()).Sum(o => o.Header.Amount).ToString("C") + " orphaned total");
                }
                var DeleteThese = payments.Where(o => !o.Lines.Any());


                if (Console.ReadLine().StartsWith("Y"))
                {

                    sw.Stop();

                    var stop2 = Stopwatch.StartNew();

                    Parallel.ForEach(DeleteThese, new ParallelOptions() { MaxDegreeOfParallelism = 8 }, (o) =>
                    {
                        qb.DeletePaymentById(o.PaymentId, o.SyncToken);
                        Console.WriteLine("Deleted" + o.PaymentId + " / Ref# " + o.ReferenceNumber);
                        Console.Title = sw.ElapsedMilliseconds + "ms ... " + stop2.ElapsedMilliseconds + "ms deleting";
                    });

                    //            payments.ToList().Where(o => !o.Lines.Any()).Select(o => o.ReferenceNumber)

                    //            Console.WriteLine(gp.GroupBy(o => o.InvoiceId + "_" + o.Amount).Where(o => o.Count() > 1).ToJson(true));

                    // Group them by DocNumber to find duplicates.

                    var duplicatePayments = payments.GroupBy(o => o.ReferenceNumber + "_" + o.Header.Amount).Where(o => o.Count() > 1).ToList();

                    Console.Title = duplicatePayments.Count.ToString();
                    Console.WriteLine(duplicatePayments.ToJson(true));
                    return;
                }


                //var invoices = qb.GetInvoices().ToList();
                var duplicates = invoices.Select(o => new
                {
                    Id = o.Id,
                    DocNumber = o.Header.InvoiceNumber,
                    Date = o.Header.TransactionDate,
                    Balance = o.Header.Balance,
                    Base = o,
                })
                .GroupBy(o => o.DocNumber)
                .Where(o => o.Count() > 1).ToList();

                Console.WriteLine("Duplicates: " + duplicates.Count + ".. when done we shold have: " + (duplicates.Count / 2));

                Console.WriteLine(duplicates.ToJson());


                var existingOnes = SqlMapper.Query<string>(
                    "SELECT KeyValue FROM Integration.vwQuickbooksCalls WHERE Companyid=@CID",
                    new { CID = dCompanyId })
                    .Distinct()
                    .ToList();

                // list of all invoices that have duplicates.
                foreach (var rx in duplicates)
                {
                    if (rx.Count() > 2)
                    {
                        // there are more than two duplicates; dont do anything.
                        Console.WriteLine("## " + rx.Key + ": " + rx.Count() + " total... !!!");
                        continue;
                    }
                    Console.WriteLine("");
                    Console.WriteLine("## " + rx.Key);

                    var invoice1 = rx.First();
                    var invoice2 = rx.Last();

                    var ep = existingOnes.Where(o => o == invoice1.Id || o == invoice2.Id).ToArray();

                    if (ep.Length == 2)
                        throw new Exception("both InvoiceID's exist, this cannot be valid or handled - manually fix.");
                    else if (ep.Length == 1)
                        Console.WriteLine("Found " + ep.First() + " in vwQuickbooksCalls");

                    var bal1 = invoice1.Base.Header.Balance;
                    var bal2 = invoice2.Base.Header.Balance;

                    // Find the payments
                    var payments1 = payments.Where(o => o.ReferenceNumber == rx.Key).ToArray();

                    if (payments1.Length > 0)
                        Parallel.ForEach(payments1, t => qb.GetPaymentById(t.PaymentId));

                    #region Handle when both duplicates are PAID
                    if (bal1 == 0 && bal2 == 0)
                    {
                        // both invoices are paid. we need to find the one that isnt linked and delete it.
                        Console.WriteLine("## " + rx.Key + " both are paid.. this is BAD *!#^!#^*!#^!^!^#");
                        Console.WriteLine(invoice1);
                        Console.WriteLine(invoice2);
                        var wi2 = new WorkItem(qb, rx.Key);
                        if (ep.Length == 1)
                        {
                            Console.WriteLine("found invoice. its a valid link. so lets not delete this one " + invoice1);

                            var invoiceToDelete =
                                new string[] { invoice1.Id, invoice2.Id }.Where(o => o != ep.First()).First();

                            Console.WriteLine("DeleteInvoice (" + invoiceToDelete + ")");
                            Console.WriteLine("DeletePayments: " +
                                payments1.Where(o => o.Lines != null && o.Lines.Any() && o.Lines[0].InvoiceId == invoiceToDelete).Select(o => o.PaymentId).ToJson());

                            var invTT = rx.Where(o => o.Id == invoiceToDelete).First();
                            bool deleted = false;
                            foreach (var ptd in payments1.Where(o => o.Lines != null && o.Lines.Any() && o.Lines[0].InvoiceId == invoiceToDelete))
                            {
                                deleted = true;
                                wi2.Payments.Add(ptd);
                            }

                            if (deleted)
                            {
                                wi2.Invoices.Add(invTT.Base);
                            }
                            else
                            {
                                Console.WriteLine("couldnt find payments for " + invTT.Base.Id + " - skipping");
                                continue;

                            }
                        }
                        else
                        {
                            Console.WriteLine("couldnt find any references in the adtabase for " + invoice1.Id + " or " + invoice2.Id + "..." + ep.ToJson());
                            continue;
                        }
                        PerformDelete(wi2);
                        //deletionQueue.EnqueueTask(wi2);
                        Console.WriteLine("identical payments!");
                        continue;
                    }
                    #endregion

                    #region Decide which of the two to delete

                    var invoiceToDelete2 = new string[] { invoice1.Id, invoice2.Id }.Where(o => ep == null || ep.Length == 0 || o != ep.First()).First();
                    var invTT2 = rx.Where(o => o.Id == invoiceToDelete2).First();
                    var ptdl = payments1.Where(o => o.Lines != null && o.Lines.Any() && o.Lines[0].InvoiceId == invoiceToDelete2);

                    var wi = new WorkItem(qb, rx.Key);
                    Console.WriteLine("DeleteInvoice (" + invoiceToDelete2 + ")");

                    if (ptdl.Any())
                    {
                        Console.WriteLine("DeletePayments: " + ptdl.Select(o => o.PaymentId).ToJson());
                        foreach (var ptd in ptdl)
                        {
                            wi.Payments.Add(ptd);
                        }
                    }
                    else
                    {
                        Console.WriteLine("No payment to delete. Invoice has a total of " + invTT2.Base.Header.TotalAmount + " / balance of " + invTT2.Base.Header.Balance);

                    }
                    wi.Invoices.Add(invTT2.Base);
                    PerformDelete(wi);
                    #endregion
                }

                Console.WriteLine("*** waiting for finish ***");
                Console.ReadLine();

                Console.ReadLine();
                return;

                AllstateResponse("*********", "2499452", "", "accept", 45);
                return;

                AllstateLogin("*********");
                System.Threading.Thread.Sleep(2000);
                AllstateLogin("*********");
                System.Threading.Thread.Sleep(2000);
                AllstateLogin("*********");
                System.Threading.Thread.Sleep(2000);



                return;



                //AllstateResponse("*********", "123456", "12345", "phone", 45);
                return;

                AllstateResponse("*********", "123456", "12345", "accept", 45);





                return;





                IsscRegister();
                return;

                RunWeeklyAgeroPaymentImport();
                return;

                var mm = MimeKit.MimeMessage.Load("something.eml");
                await MotorClubMapper.Translate(GeicoEmailParser.FromHtml(mm.HtmlBody), 2, 1, false, 0);


                IsscRegister();

                TextWriterTraceListener writer = new TextWriterTraceListener(System.Console.Out);

                AsyncHelper.RunSync(async () => await InvoicePaymentHelper.SendEntryToQuickBooksAndPayInvoice(
                        await Entry.GetByCallNumberAsync(4973, Company.Company.GetById(2394)), Provider.QuickBooks));

                return;

                var ac = await Accounts.Statement.GetByIdAsync(95020);
                var zx = Stopwatch.StartNew();
                Console.WriteLine(new[] { ac }.Select(o => new { balance = o.Balance, Total = o.Total }).ToJson());
                Console.WriteLine(zx.ElapsedMilliseconds);




                return;



                var sww = Stopwatch.StartNew();
                for (int y = 0; y < 100; y++)
                    for (int i = 0; i < 1000; i++)
                    {
                        if (sww.ElapsedMilliseconds > 1000)
                        {
                            Console.WriteLine("Retrieved this many times in 1 sec: " + i);
                            break;
                        }
                    }
                Console.WriteLine(sww.ElapsedMilliseconds);
                return;


                Console.WriteLine((await Statement.GetByIdAsync(87263)).Balance.ToString("C"));
                return;


                //RunWeeklyAgeroPaymentImport();
                return;


                var gcx = new IsscRestClient(IsscConfig.GetByEnvironmentType(EnvironmentType.Live));

                gcx.Login(267);
                gcx.Login(268);
                gcx.Login(270);
                gcx.Login(271);
                gcx.Login(272);
                gcx.Login(274);
                gcx.Login(275);
                gcx.Login(276);
                gcx.Login(277);
                gcx.Login(278);
                gcx.Login(279);
                gcx.Login(280);

                return;

                //            gcx.RegisterProvider("GCOAPI", 5540, 90117, "**********", "46-2524025", "ab:1717363");


                return;


                gcx.Connect("https://api.towbook.com/receivers/issc/", "towbook", "k638WrYceu3a");
                return;




                return;

                Console.WriteLine(GeicoEmailParser.FromHtml(File.ReadAllText(@"E:\temp\geico.html")).ToJson(true));
                return;

                var imp = Impounds.Impound.GetById(668400);
                var original = imp.ImpoundDate;

                Console.WriteLine("*** ORIGINAL IMPOUND DATE ****" + imp.ImpoundDate);

                Console.WriteLine(imp.Invoice.Total);
                Console.ReadLine();

                imp.ImpoundDate = imp.ImpoundDate.Value.AddDays(-7);
                Console.WriteLine("\n\n\n*** SET IMPOUND DATE TO ****" + imp.ImpoundDate);

                Console.WriteLine(imp.Invoice.Total);
                Console.ReadLine();


                Console.WriteLine(imp.ZoresGetAuctionTotal());
                Console.WriteLine(imp.Invoice.Total);
                Console.ReadLine();
                imp.ImpoundDate = imp.ImpoundDate.Value.AddDays(7);
                Console.WriteLine("\n\n\n*** SET IMPOUND DATE TO ****" + imp.ImpoundDate);
                Console.WriteLine(imp.Invoice.Total);

                Console.ReadLine();
                imp.ImpoundDate = original;
                Console.WriteLine("\n\n\n*** SET IMPOUND DATE TO ****" + imp.ImpoundDate);
                Console.WriteLine(imp.Invoice.Total);


                return;


                Console.WriteLine((await Invoice.GetByIdAsync(3247741)).Subtotal);
                return;


                Console.WriteLine((await InvoiceStatus.GetByCompanyIdAsync(4189)).ToJson(true));

                return;


                var registration = new IsscRestClient(IsscConfig.GetByEnvironmentType(EnvironmentType.Live));

                registration.Login(251);
                registration.Login(254);
                registration.Login(253);



                return;


                var a = Accounts.Account.GetById(96620);
                Console.WriteLine(a.Balance);
                return;



                IsscRegister();
                return;


                TestTieredStorage();
                return;


                return;

                var inv = await Invoice.GetByIdAsync(765683);

                Console.WriteLine("*** GET SUBTOTAL + INVOICE TOTAL! ROUND 1");
                Console.WriteLine(new
                {
                    SubTotal = inv.Subtotal,
                    Tax = inv.Tax,
                    HiddenTotal = inv.HiddenTotal,
                    GrandTotal = inv.GrandTotal,
                    Total = inv.Total,
                    TicketValue = inv.TicketValue
                }.ToJson());

                var iii = inv.InvoiceItems.Where(o => o.RateItem == null || o.RateItem.RateItemId > 1).FirstOrDefault();
                Console.WriteLine("quantity:" + iii.Quantity);
                iii.Quantity = iii.Quantity + 1;
                Console.WriteLine("quantity:" + iii.Quantity);

                inv.Save(null, null);
                return;

                Console.WriteLine(inv.ToJson(true));
                return;

                /*
                            inv.InvoiceItems.First().Quantity = 50;
                            inv.InvoiceItems.First().CustomPrice += 500;
                            Console.WriteLine("*** GET SUBTOTAL + INVOICE TOTAL! AFTER QUANTITY+PRICE CHANGE.");
                            Console.WriteLine(new { SubTotal = inv.Subtotal, Tax = inv.Tax }.ToJson());

                            //inv.InvoiceItems.Add(new InvoiceItem() { CustomName = "Performance Boost!", CustomPrice = 50.0M, Quantity = 1.5M });
                            //inv.InvoiceItems.Add(new InvoiceItem() { CustomName = null, CustomPrice = 50.0M, Quantity = 1.5M, Taxable = true });
                            //inv.InvoiceItems.Add(new InvoiceItem() { CustomName = "Performance Booster.. Measure Up!", CustomPrice = 50.0M, Quantity = 1.5M });

                            Console.WriteLine("*** GET SUBTOTAL + INVOICE TOTAL! AFTER ITEMS ADDED");
                            var sw1 = Stopwatch.StartNew();
                            Console.WriteLine(new { SubTotal = inv.Subtotal, Tax = inv.Tax }.ToJson());
                            Console.WriteLine(sw1.ElapsedMilliseconds + "ms elapsed");
                            Console.Beep();
                            Console.ReadLine();

                            */

                inv.Save(null, null);
                Console.WriteLine(new { SubTotal = inv.Subtotal, Tax = inv.Tax }.ToJson());
                return;


                IsscRegister();
                return;

                var zzx = CompanyKeyValue.GetFirstValueOrNull(4387, Provider.Towbook.ProviderId, "Auto Send Dispatch Text Msg");

                Console.WriteLine(zzx);
                return;

                string text = File.ReadAllText("e:\\temp\\allstate_email.txt");
                var ass = AllstateEmailParser.FromText(text);

                Console.WriteLine(ass.ToJson(true));


                return;


                IsscRegister();
                return;


                RunWeeklyAgeroPaymentImport();
                return;

            





                return;



                PushInvoices(5376, Convert.ToDateTime("8/1/2015"), Convert.ToDateTime("10/1/2015"), true);
                PushInvoices(2394, Convert.ToDateTime("5/1/2015"), Convert.ToDateTime("7/1/2015"), true);

                return;



                PushInvoices(4770, Convert.ToDateTime("7/23/2015"), Convert.ToDateTime("9/1/2015"), false);
                return;




                DDMessage msg = new DDMessage();
                msg.DDMessageHeader = new DDMessageHeader();
                msg.DDContent = new DSPMessageBody();
                DSPMessageBody cont = (DSPMessageBody)msg.DDContent;

                cont.AccountInfo.AvailableBenefits = "12";
                cont.AccountInfo.BenefitLimit = "10";
                cont.AccountInfo.CallBackPhone = "**********";
                cont.AccountInfo.CustFirstName = "Matheus";
                cont.AccountInfo.CustLastName = "Kerkhoff";
                cont.AccountInfo.MailAddr = new DSPMessageMailAddress();
                cont.AccountInfo.MailAddr.Addr1 = "123 North Third";
                cont.AccountInfo.MailAddr.City = "St. Clair";
                cont.AccountInfo.MailAddr.Phone = "**********";
                cont.AccountInfo.MailAddr.State = "MI";
                cont.AccountInfo.MailAddr.Zip = "48079";
                cont.AccountInfo.MemFirstName = "Jorge";
                cont.AccountInfo.MemLastName = "Gonzales";
                cont.AccountInfo.MemNum = "**********";

                msg.UserDefined = new System.Collections.ObjectModel.Collection<UserDefinedNameValue>();
                msg.UserDefined.Add(new UserDefinedNameValue("ExtendedETAReasonCode", "2"));
                msg.UserDefined.Add(new UserDefinedNameValue("ExtendedETAReasonDescription", "Traffic"));

                string wa = msg.GetXml();
                Console.WriteLine(wa);


                return;



                AllstateLogin("IL1414717");
                AllstateLogin("IL1414570");
                AllstateLogin("IL1414716");
                return;




                IsscRegister();
                return;

                PushInvoices(4770, Convert.ToDateTime("7/17/2015"), Convert.ToDateTime("9/1/2015"), false);
                return;



                return;


                Console.WriteLine(DSPMessageJobInfo.ConvertAllstateTimestampToDateTime("07/21/2015 05:18:59 PM").ToLocalTime().ToString());
                return;

                IsscRegister();
                return;




                var qDspMsg = DDMessage.FromXml(File.ReadAllText(@"C:\temp\quest\incoming_call.xml"), typeof(DSPMessageBody)).DDContent as DSPMessageBody;

                DSPMessageJobInfo.ConvertAllstateTimestampToDateTime(qDspMsg.JobInfo.TimeStamp);


                DateTime? qExpDate = null;
                if (qDspMsg.JobInfo.RequiredAcknowledgeTime != 0 && !string.IsNullOrEmpty(qDspMsg.JobInfo.TimeStamp))
                    qExpDate = DSPMessageJobInfo.ConvertAllstateTimestampToDateTime(qDspMsg.JobInfo.TimeStamp).AddMinutes(qDspMsg.JobInfo.RequiredAcknowledgeTime);

                Console.WriteLine(qExpDate);

                Console.WriteLine(qDspMsg.ToJson(true));
                return;









                await TimeAcccountBalance();
                return;

                await TestBillToWorks();
                return;

                IsscRegister();
                return;




                var call = Entry.GetById(2956270);

                AsyncHelper.RunSync(() => InvoicePaymentHelper.SendEntryToQuickBooks(call,
                    Towbook.Integration.Provider.QuickBooks));


                return;



                return;





                return;


                AllstateLogout("*********");
                AllstateLogin("*********");



                return;

                var xzz = Extric.Towbook.Dispatch.Entry.GetById(2928616);

                xzz = Extric.Towbook.Dispatch.Entry.GetById(2928616);

                Console.WriteLine(xzz.CallNumber);
                Console.WriteLine(xzz.BalanceDue);

                Console.ReadLine();
                return;



                IsscRegister();
                return;

                PushInvoices(3738, Convert.ToDateTime("6/1/2015"), Convert.ToDateTime("8/1/2015"), false, new int[] { 73464,
                73467,
                73468,
                85134});

                return;

                //   IsscRegister();
                //   return;


                return;




                #region All State Secondary Email Parser
                var agero = AgeroConnection.Login("", "");

                List<Check> checks = agero.GetChecks();
                foreach (Check item in checks)
                {
                    Console.WriteLine(string.Format("agero : found check#:{0} / vendorId: {1} / amount: {2}", item.CheckNumber, item.VendorId, item.Amount));
                }

                var gc = new IsscRestClient();

                //return;


                //  return;

                #endregion

                #region NavistarEmailParser test

                //string text = File.ReadAllText("c:\\temp\\navistar4.html");
                //var nv = NavistarEmailParser.FromHtml(text);

                //return;

                #endregion

                #region NetcostTextParser test

                //string text = MotorClubMapper.TextFromPdf("c:\\temp\\netcost.pdf", false);
                //var ncp = NetcostTextParser.FromText(text);

                //return;

                #endregion

                #region TeslaEmailParser test

                //string text = File.ReadAllText("c:\\temp\\tesla3.txt");
                //var tep = TeslaEmailParser.FromText(text);

                //return;

                #endregion

                #region CarsArriveEmailParser test

                //string html = File.ReadAllText("c:\\temp\\carsarrive-1.html");
                //var ca = CarsArriveEmailParser.FromHtml(html);

                //return;

                #endregion

                #region AHL - cash test

                //string html = File.ReadAllText("c:\\temp\\ahl-cash.html");
                //var ahlc = AutoHelpLineCashEmailParser.FromHtml(html);

                #endregion

                //Console.WriteLine(EncryptionHelper.ToHashId(********));
                return;

                #region Road America Digital Dispatch Test

                #region DSP Test

                //DDMessage msg = new DDMessage("DSP");
                //DSPMessage cont = (DSPMessage)msg.DDContent;

                //cont.AccountInfo.AvailableBenefits = 12;
                //cont.AccountInfo.BenefitLimit = 10;
                //cont.AccountInfo.CallBackPhone = "**********";
                //cont.AccountInfo.CustFirstName = "Matheus";
                //cont.AccountInfo.CustLastName = "Kerkhoff";
                //cont.AccountInfo.MailAddr = new DSPMessageMailAddress();
                //cont.AccountInfo.MailAddr.Addr1 = "123 North Third";
                //cont.AccountInfo.MailAddr.City = "St. Clair";
                //cont.AccountInfo.MailAddr.Phone = "**********";
                //cont.AccountInfo.MailAddr.State = "MI";
                //cont.AccountInfo.MailAddr.Zip = "48079";
                //cont.AccountInfo.MemFirstName = "Jorge";
                //cont.AccountInfo.MemLastName = "Gonzales";
                //cont.AccountInfo.MemNum = "**********";

                //string wa = msg.GetXml();

                //return;

                #endregion

                #region LOG Test
                //DDMessage msg = new DDMessage("LOG");
                //((LOGMessage)msg.DDContent).CallbackURI = "";
                //((LOGMessage)msg.DDContent).UserName = "TOWBOOK";
                //((LOGMessage)msg.DDContent).Password = "4eFbhCtBHc7mF7Z";

                #endregion

                #region LOF Test
                //DDMessage msg = new DDMessage("LOF");
                //((LOFMessage)msg.DDContent).Username = "TOWBOOK";
                //((LOFMessage)msg.DDContent).Password = "4eFbhCtBHc7mF7Z";

                #endregion



                //msg.DDMessageHeader.Key = "TOWBOOK";
                //msg.DDMessageHeader.ContractorID = "Q12346_001";
                //msg.DDMessageHeader.ConRequired = "Y";
                //msg.DDMessageHeader.HeaderVersion = "1";
                //msg.DDMessageHeader.MsgVersion = "1.1";
                //msg.DDMessageHeader.ResponseType = "ACK";

                //string fileXml = File.ReadAllText(@"C:\temp\DD Message Examples\LOG.xml");
                //fileXml = fileXml.Replace("username1", "TOWBOOK").Replace("password1", "4eFbhCtBHc7mF7Z");

                //string xml = msg.GetXml("LOGMessage");

                //var fileObj = DDMessage.FromXml(fileXml, typeof(LOGMessage));
                //var obj = DDMessage.FromXml(xml, typeof(LOGMessage));

                //Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.DDXMLSoapClient client = new Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.DDXMLSoapClient();
                //string wa = client.DDXMLReceiveMessage(fileXml);

                //return;

                //Console.WriteLine(wa);

                #endregion


                #region Test agero/geico purchase orders list

                //Console.WriteLine("Press any key to test Agero...");
                //Console.ReadKey();
                //Console.WriteLine("Retrieving Agero PO numbers...");

                //var ageroUsername = "101640";
                //var ageroPassword = "ccc02033";

                //var agero = AgeroConnection.Login(ageroUsername, ageroPassword, true);
                //var invoice = agero.FindInvoice("956067907");

                //return;

                //List<string> ageroPos = agero.GetPurchaseOrderNumbers();

                //foreach (var po in ageroPos)
                //{
                //    Console.WriteLine(po);
                //}

                //Console.WriteLine("Updating database with new PO numbers...");

                //AvailablePurchaseOrder.SaveMultiple(1, ageroPos.ToArray(), MotorClubType.Agero);

                //Console.WriteLine("Database updated");
                //Console.WriteLine("Press any key to test Geico...");
                //Console.ReadLine();
                //Console.WriteLine("Retrieving Geico PO numbers...");

                //var geicoUsername = "**********";
                //var geicoPassword = "Familyguy4";

                //var geico = GeicoConnection.Login(geicoUsername, geicoPassword, true);
                //var geicoPos = geico.GetPurchaseOrderNumbers();

                //foreach (var po in geicoPos)
                //{
                //    Console.WriteLine(po);
                //}

                //Console.WriteLine("Updating database with new PO numbers...");

                //AvailablePurchaseOrder.SaveMultiple(1, geicoPos.ToArray(), MotorClubType.Geico);

                //Console.WriteLine("Database updated");
                //Console.WriteLine("Press any key to exit...");
                //Console.ReadKey();

                //return;

                #endregion

                //CallModel call1 = CallModel.Map(Entry.GetById(220599));
                //CallModel call2 = CallModel.Map(Entry.GetById(220599));

                //call2.TowDestination = "Something different";
                //call2.Account.Address = "New acc Address";

                //List<CallContactModel> contacts = new List<CallContactModel>();
                //contacts.Add(new CallContactModel() { Address = "Test", CallId = call2.Id, Name = "New contact", State = "MI" });

                //call2.Contacts = contacts.ToArray();

                //var test = JsonExtensions.GetChanges(call1, call2, true);

                #region Test Geico

                // -- Test Geico

                //string username = "**********";
                //string pass = "Familyguy4";

                //var testMode = false;
                //var geico = GeicoConnection.Login(username, pass, testMode);

                // ------
                //using (Geico.GeicoConnection geico = Geico.GeicoConnection.Login(username, password, true))
                //{
                //    submitLogs = geico.SubmitPurchaseOrder(
                //        poNumber: poNumber,
                //        referenceNumber: entry.CallNumber,
                //        adjustmentReasons: null,
                //        unloadedMiles: entry.InvoiceItems.Where(o => o.RateItem != null && o.RateItem.Predefined != null && o.RateItem.Predefined.Id == PredefinedRateItem.BUILTIN_MILEAGE_UNLOADED).Sum(o => o.Quantity),
                //        loadedMiles: entry.InvoiceItems.Where(o => o.RateItem != null && o.RateItem.Predefined != null && o.RateItem.Predefined.Id == PredefinedRateItem.BUILTIN_MILEAGE_LOADED).Sum(o => o.Quantity),
                //        comments: string.Empty);
                //}
                //break;

                //string username = "ca0915558";
                //string pass = "25atlastow";

                //using (GeicoConnection geico = GeicoConnection.Login(username, pass))
                //{
                //    DispatchStatus test = geico.GetDispatchStatus("750314179");

                //    List<Extric.Towbook.Integration.MotorClubs.Billing.Geico.Dispatch> ds = geico.GetDispatches(new DateTime(2014, 7, 15), new DateTime(2014, 7, 22));

                //    bool wa = geico.ValidatePurchaseOrder(ds[0].DispatchNumber);

                //    if (wa)
                //    {
                //        geico.SubmitPurchaseOrder(ds[3].DispatchNumber.ToString(), 0, new Dictionary<string, decimal> { 
                //            {"RR", 32},
                //            {"CLEANFL", 13}
                //            }, 100, 50, string.Empty);

                //    }

                //}

                //Console.ReadLine();

                #endregion

                //return;

                //AAAPioneerValleyConnection conn = AAAPioneerValleyConnection.Login("990", "AGAWAM1");
                //conn.GetCalls();

                //string user = "6454";
                //string pwd = "Bronco123";

                //using (AAAWpConnection aaawp = AAAWpConnection.Login(user, pwd))
                //{
                //    var wa = aaawp.GetCalls();
                //    aaawp.htmlText = File.ReadAllText("c:\\temp\\aaahesley-9.html");
                //    var wi = aaawp.GetCallDetails(wa[0].Key);
                //}

                //return;

                //string user = "425";
                //string pwd = "DAB";

                //string text = File.ReadAllText("c:\\temp\\DABtest.html");
                //using (AAANorthwayConnection northway = AAANorthwayConnection.Login(user, pwd))
                //{
                //    northway.htmlText = text;
                //    northway.GetCallDetails(string.Empty);
                //}

                //string text = File.ReadAllText(@"c:\temp\beaconHtml.txt", System.Text.Encoding.ASCII);
                //var beaconHtml = BeaconHtmlInvoiceParser.FromText(text);

                //return;

                //string text = MotorClubMapper.TextFromPdf(@"c:\temp\ahl.pdf", false);
                //var ahl = AutoHelpLineTextParser.FromText(text);
                //return;

                //string text = MotorClubMapper.TextFromPdf(@"c:\temp\aads-1.pdf", false);
                //string text2 = MotorClubMapper.TextFromPdf(@"c:\temp\aads-2.pdf", false);
                //string text3 = MotorClubMapper.TextFromPdf(@"c:\temp\aads-3.pdf", false);
                //var aads = AmericanAccessCasualtyEmailParser.FromText(text);
                //var aads2 = AmericanAccessCasualtyEmailParser.FromText(text2);
                //var aads3 = AmericanAccessCasualtyEmailParser.FromText(text3);

                //return;

                //string text = File.ReadAllText(@"c:\temp\allieddispatch-1.txt");
                //var allied = AlliedDispatchEmailParser.FromText(text);

                //return;

                //string text = MotorClubMapper.TextFromPdf(@"c:\temp\aainsurance-2.pdf");
                //AaInsuranceEmailParser p = AaInsuranceEmailParser.FromText(text);

                //return;

                //string text = File.ReadAllText(@"c:\temp\carstowing-1.html");
                //string text2 = File.ReadAllText(@"c:\temp\carstowing-2.html");
                //string text3 = File.ReadAllText(@"c:\temp\carstowing-3.html");

                //CarsTowingEmailParser ct = CarsTowingEmailParser.FromHtml(text);
                //CarsTowingEmailParser ct2 = CarsTowingEmailParser.FromHtml(text2);
                //CarsTowingEmailParser ct3 = CarsTowingEmailParser.FromHtml(text3);

                //return;

                //string text = MotorClubMapper.TextFromPdf(@"C:\temp\copart-1.pdf", true);
                //string text2 = MotorClubMapper.TextFromPdf(@"C:\temp\copart-2.pdf", true);
                //string text3 = MotorClubMapper.TextFromPdf(@"C:\temp\copart-3.pdf", true);
                //string text4 = MotorClubMapper.TextFromPdf(@"C:\temp\copart-4.pdf", true);
                //string text5 = MotorClubMapper.TextFromPdf(@"C:\temp\copart-5.pdf", true);
                //string text6 = MotorClubMapper.TextFromPdf(@"C:\temp\copart-6.pdf", true);

                //CopartEmailParser copartParser = CopartEmailParser.FromText(text);
                //CopartEmailParser copartParser2 = CopartEmailParser.FromText(text2);
                //CopartEmailParser copartParser3 = CopartEmailParser.FromText(text3);
                //CopartEmailParser copartParser4 = CopartEmailParser.FromText(text4);
                //CopartEmailParser copartParser6 = CopartEmailParser.FromText(text6);

                //Console.WriteLine(copartParser.ToJson());
                //Console.ReadLine();
                //return;

                //string username = "*********";
                //string pass = "Zippy40";

                //string username = "ASKKWU";
                //string pass = "Auto#123";

                //Console.WriteLine("allstate : login");
                //using (AllStateConnection allstate = AllStateConnection.Login(username, pass))
                //{
                //    Console.WriteLine("allstate : login sucessfull for user " + username);
                //    //Console.WriteLine("allstate : retrieving payment statements");
                //    //List<PaymentStatement> payments = allstate.GetPaymentStatements(new DateTime(2013, 12, 11), DateTime.Now);

                //    //foreach (PaymentStatement payment in payments)
                //    //    Console.WriteLine("allstate : found statement#:" + payment.Number);

                //    //Console.WriteLine("allstate : retrieving statement details # " + payments[0].Number);
                //    //PaymentStatement ps = allstate.GetPaymentStatementDetails(payments[0].Number);

                //    //Console.WriteLine("allstate : retrieving purchase orders for provider: " + username);
                //    //var pos = allstate.GetPurchaseOrders();
                //    //foreach (var po in pos)
                //    //    Console.WriteLine("allstate : found PO: " + po.ToJson());

                //    // var poDetails = allstate.GetPurchaseOrderDetails("31399639");

                //    //*********
                //    //Console.WriteLine("allstate : Changing to provider: *********");
                //    //allstate.SelectProvider("*********");

                //    byte[] buffer = null;

                //    FileStream f = File.OpenRead(@"c:\temp\test-image.pdf");
                //    try
                //    {
                //        buffer = new byte[f.Length];
                //        f.Read(buffer, 0, buffer.Length);
                //    }
                //    finally
                //    {
                //        f.Close();
                //    }


                //    Console.WriteLine("allstate : retrieving purchase orders for provider: *********");
                //    var pos = allstate.GetPurchaseOrders();
                //    Console.WriteLine("allstate : found " + pos.Count + " POs for this provider...press enter to start processing...");
                //    Console.ReadLine();
                //    for (int i = 1; i < pos.Count; i++)
                //    {
                //        var po = pos[i];
                //        Console.WriteLine("allstate : found PO: " + po.ToJson());
                //        Console.WriteLine("******************************************************");
                //        var wa = allstate.GetPurchaseOrderDetails(po.DispatchId, true);

                //        //PurchaseOrderTowService svc = new PurchaseOrderTowService();
                //        //svc.Description = "Fuel";

                //        //List<PurchaseOrderTowService> svcs = new List<PurchaseOrderTowService>();
                //        //svcs.Add(svc);

                //        //List<PurchaseOrderComment> comments = new List<PurchaseOrderComment>();
                //        //comments.Add(new PurchaseOrderComment()
                //        //{
                //        //    CreateDate = DateTime.Now,
                //        //    Message = "Test 3"
                //        //});

                //        //Dictionary<string, byte[]> additionalFees = new Dictionary<string, byte[]>();
                //        //additionalFees.Add(@"c:\temp\test-image.pdf", buffer);

                //        if (wa.Status == Extric.Towbook.Integration.MotorClubs.Billing.Allstate.PurchaseOrder.StatusNotSubmitted)
                //        {
                //            allstate.SubmitPurchaseOrder(
                //                wa.DispatchId,
                //                "",
                //                "",
                //                null,
                //                null,
                //                null,
                //                null,
                //                null,
                //                null,
                //                null,
                //                false,
                //                "",
                //                "",
                //                null,
                //                null);
                //        }

                //        Console.WriteLine();

                //        Console.WriteLine("******************************************************");
                //        Console.WriteLine("Press enter to process next...");
                //        Console.ReadLine();
                //    }

                //    //Console.WriteLine("allstate : retrieving providers");
                //    //List<string> providers = allstate.GetProviders();

                //    //foreach (string provider in providers)
                //    //    Console.WriteLine("allstate : found provider#:" + provider);

                //    //*********
                //    //allstate.SelectProvider(providers[providers.IndexOf("*********")]);

                //    //List<ClaimSupplement> supps = allstate.GetSupplementTypes("**********");
                //    //supps[0].Value = 12;

                //    //Extric.Towbook.Integration.MotorClubs.Billing.Allstate.PurchaseOrder po = allstate.GetPurchaseOrderDetails(31062785);
                //    //Extric.Towbook.Integration.MotorClubs.Billing.Allstate.PurchaseOrder po2 = allstate.GetPurchaseOrderDetails(31069223);

                //    //List<AutomatedHttpLog> logs = allstate.SubmitPurchaseOrder("**********", null, "Testing", false);
                //    //foreach (var log in logs)
                //    //    Console.WriteLine(log.ToJson());
                //}

                //Console.ReadLine();

                //return;




                //return;

                #region agero

                if (false)
                {
                    string testAccessToken = "iwNSvevl6kqZIGCcWDqQIu3zQjL3";

                    //int testDispatchId = CreateDispatch();
                    //int testDispatchId = 898547333;

                    int testVendorId = 73024;

                    var r = new Agero.AgeroRestClient();

                    using (SKImage image = SKImage.FromEncodedData("c:\\temp\\signature.jpg"))
                    using (SKData data = image.Encode())

                    using (System.IO.MemoryStream ms = new System.IO.MemoryStream(data.ToArray()))
                    {
                        byte[] imgBytes = ms.ToArray();
                    }

                // WORK
                //var dispatchDetails = r.GetDispatchDetail(testAccessToken, testDispatchId);

                // WORK
                //r.AcceptDispatchRequest(testAccessToken, testDispatchId, 15);

                // WORK
                //r.RefuseDispatchRequest(testAccessToken, testDispatchId, "406");

                // WORK
                //r.CancelDispatchRequest(testAccessToken, testDispatchId, DispatchStatusCode.CustomerNotWithVehicle, DispatchStatusReason.CustomerNotWithVehicle);

                // WORK
                //r.AssignDriverToDispatch(testAccessToken, testDispatchId, 342575);

                // WORK
                //var assignedDriver = r.GetDispatchAssignedDriver(testAccessToken, testDispatchId);

                // ERROR
                //var newSignature = r.AddDispatchSignature(testAccessToken, testDispatchId, "John Doe", "Default", "Tow Location", "None", 40.1234, -70.1234, "Work in Progress", "c:\\temp\\signature.jpg", imgBytes);

                // WAITING AddDispatchSignature
                //var signatures = r.GetDispatchSignatures(testAccessToken, testDispatchId);

                // WAITING AddDispatchSignature
                //var signature = r.GetDispatchSignature(testAccessToken, testDispatchId, 123);

                // ERROR
                //var newDocument = r.AddDispatchDocument(testAccessToken, testDispatchId, "c:\\temp\\signature.jpg", imgBytes, "2", "image/jpeg");

                // WAITING AddDispatchDocument
                //var newDocument = r.AddDispatchDocument(testAccessToken, testDispatchId, "c:\\temp\\signature.jpg", imgBytes, "2", "image/jpeg");

                // WORK
                //r.ChangeDispatchStatus(testAccessToken, testDispatchId, "2", "None", 15, 12345, "Towbook", 40.1234, -70.1234, DateTime.Now);

                // WORK
                //var currentStatus = r.GetDispatchCurrentStatus(testAccessToken, testDispatchId);

                // WORK
                //var statusHistory = r.GetDispatchStatusHistory(testAccessToken, testDispatchId);

                // ERROR
                //r.UndoDispatchCurrentStatus(testAccessToken, testDispatchId);

                // WORK
                //r.TrackDispatchStatusGPSLocation(testAccessToken, testDispatchId, 15, "2", 40.1234, -70.1234, "Towbook", 12345, DateTime.Now);

                // ERROR
                //r.TrackPTOEvent(testAccessToken, testDispatchId, "PTO_NO", 40.1234, -70.1234);

                // WORK
                //r.RequestExtension(testAccessToken, testDispatchId);

                // WORK
                //r.UploadDriverEquipmentPicture(testAccessToken, testVendorId, 342575, "c:\\temp\\signature.jpg", imgBytes);

                // WORK
                //var drivers = r.GetDriversList(testAccessToken, testVendorId);

                // ERROR
                //r.NotifyDriverCurrentLocation(testAccessToken, 342575, 40.1234, -70.1234);

                // WORK
                //r.NotifyCurrentLocation(testAccessToken, 40.1234, -70.1234);

                // WORK
                //var signeeTypes = r.GetEnumerator(testAccessToken, EnumeratorType.SigneeTypes);
                //var signatureReasonTypes = r.GetEnumerator(testAccessToken, EnumeratorType.SignatureReasonTypes);
                //var signatureLocationTypes = r.GetEnumerator(testAccessToken, EnumeratorType.SignatureLocationTypes);

                // ERROR
                //var notificationTypes = r.GetEnumerator(testAccessToken, EnumeratorType.NotificationTypes);
                //var dispatchStatus = r.GetEnumerator(testAccessToken, EnumeratorType.DispatchStatus);

                // WORK
                //var heatmapId = r.ServerHeatmapEnroll();

                // WORK
                //r.ServerHeatmapDeEnroll(heatmapId);

                // WORK
                //var enrollments = r.GetHeatMapEnrollments();

                // WORK
                //var enrollment = r.GetHeatMapEnrollment(1660);

                // ERROR
                //var subscriptions = r.GetServerNotificationSubscriptions(testAccessToken, testVendorId);

                // ERROR (What is enrollmentRequestId???)
                //List<HeatMapData> heatMapData = new List<HeatMapData>()
                //{
                //    JsonConvert.DeserializeObject<HeatMapData>("{\"vendorId\" : \"3253543\",\"driverName\":\"John Smith\",\"dispatchStatus\":{\"code\":\"10\"},\"currentLocation\" : {\"address1\":\"15 Worcester Rd\",\"address2\":\"\",\"crossStreet\":\"\",\"landmark\":\"\",\"locationType\":\"\",\"city\":\"Randolph\",\"poi1\":\"\",\"poi2\":\"\",\"state\":\"MA\",\"postalCode\":\"02368\",\"lat\":42.16531259806,\"lng\":-71.0567153130},\"disablementLocation\" : {\"address1\":\"22 Mazzeo Dr\",\"address2\":\"\",\"crossStreet\":\"\",\"landmark\":\"\",\"locationType\":\"\",\"city\":\"Randolph\",\"poi1\":\"\",\"poi2\":\"\",\"state\":\"MA\",\"postalCode\":\"02368\",\"lat\":42.165312598068283,\"lng\":-71.0567153130004},\"equipment\" : {\"code\":\"LDWL\"},\"towDestination\" : {\"address1\":\"1 Main St\",\"address2\":\"\",\"crossStreet\":\"\",\"landmark\":\"\",\"locationType\":\"\",\"city\":\"Randolph\",\"poi1\":\"\",\"poi2\":\"\",\"state\":\"MA\",\"postalCode\":\"02368\",\"lat\":42.875675675,\"lng\":-71.098098908}}")
                //};
                //r.PostHeatmapData(Convert.ToInt32(heatmapId), heatMapData.ToArray());

                // WORK
                //var driverId = r.ExternalExportDriverProfile(testVendorId, JsonConvert.DeserializeObject<ExternalDriverProfile>("{\"name\" : \"Sue Johnson\",\"equipment\" :{\"equipmentId\" : \"LDWL\",\"addressId\" : 1,\"type\" : \"Light Duty Wheel Lift\",\"vehicleNickname\" : \"blue truck\"},\"officeEmail\" : \"<EMAIL>\",\"officePhone\" : \"************\",\"gpsTracking\":\"true\"}"));

                // WORK
                //var driverProfiles = r.ExternalGetDriverProfiles(testVendorId);

                // WORK
                //var driverProfile = r.ExternalGetDriverProfile(testVendorId, 342575);

                // ERROR
                //r.ExternalUpdateDriverProfile(testVendorId, 342575, JsonConvert.DeserializeObject<ExternalDriverProfile>("{\"name\" : \"Sue Johnson\",\"equipment\" :{\"equipmentId\" : \"LDWL\",\"addressId\" : 1,\"type\" : \"Light Duty Wheel Lift\",\"vehicleNickname\" : \"blue truck\"},\"officeEmail\" : \"<EMAIL>\",\"officePhone\" : \"************\",\"gpsTracking\":\"true\"}"));

                // ERROR
                //r.ExternalDeleteDriverProfile(testVendorId, 342575);

                // ERROR
                //r.ExternalUploadDriverProfilePicture(testAccessToken, testVendorId, 342575, "c:\\temp\\signature.jpg", imgBytes, false);

                // ERROR
                //r.ExternalDeleteDriverProfilePicture(testAccessToken, testVendorId, 342757);

                // ERROR
                //r.ExternalUploadDriverEquipmentPicture(testAccessToken, testVendorId, 342575, "c:\\temp\\signature.jpg", imgBytes);

                // WORK
                //r.ExternalDeleteDriverEquipmentPicture(testAccessToken, testVendorId, 342575);

                // WORK
                #region Drivers Synchronization

                if (false)
                    {
                        AgeroRestClient driverClient = new AgeroRestClient();

                        DriverKey driverKey = DriverKey.GetByProviderId(Provider.Agero.ProviderId, "AgeroDriverId");

                        ExternalDriverProfile[] ageroDrivers = new AgeroRestClient().ExternalGetDriverProfiles(testVendorId);
                        List<Driver> towbookDrivers = Driver.GetByCompany(Company.Company.GetById(9));
                        DriverKeyValue[] integratedDrivers = DriverKeyValue.GetByCompany(9, Provider.Agero.ProviderId).ToArray();

                        // loop through towbook drivers and check if they exist in agero
                        foreach (var td in towbookDrivers)
                        {
                            // find relative id for agero to match
                            var syncDriver = integratedDrivers.Where(o => o.DriverId == td.Id).FirstOrDefault();

                            // driver exists in towbook but not synced in agero -> export driver to agero
                            if (syncDriver == null)
                            {
                                int newDriverId = driverClient.ExternalExportDriverProfile(testVendorId,
                                    new ExternalDriverProfile()
                                    {
                                        Equipment = null,
                                        GpsTracking = false,
                                        Name = td.Name,
                                        OfficeEmail = td.Email,
                                        OfficePhone = td.WorkPhone,
                                        ProfileLastUpdatedAt = DateTime.Now,
                                        ProfilePictureUrl = string.Empty
                                    });

                                // create the relative driver on towbook integration table
                                DriverKeyValue newIntegrationDriver = new DriverKeyValue()
                                {
                                    DriverId = td.Id,
                                    KeyId = driverKey.Id,
                                    Value = newDriverId.ToString()
                                };
                                newIntegrationDriver.Save();
                            }
                            else
                            {
                                // driver exists in towbook and was alread synced to agero -> update fields if needed
                                var ad = ageroDrivers.Where(o => o.DriverId == syncDriver.Value).FirstOrDefault();
                                if (ad != null &&
                                    ((ad.Name != td.Name) ||
                                     (ad.OfficeEmail != td.Email) ||
                                     (ad.OfficePhone != td.WorkPhone)))
                                {
                                    driverClient.ExternalUpdateDriverProfile(testVendorId, Convert.ToInt32(ad.DriverId), new ExternalDriverProfile()
                                    {
                                        Equipment = null,
                                        GpsTracking = false,
                                        Name = td.Name,
                                        OfficeEmail = td.Email,
                                        OfficePhone = td.WorkPhone,
                                        ProfileLastUpdatedAt = DateTime.Now,
                                        ProfilePictureUrl = string.Empty
                                    });
                                }
                            }
                        }

                        foreach (var ad in ageroDrivers)
                        {
                            var syncDriver = integratedDrivers.Where(o => o.Value == ad.DriverId).FirstOrDefault();

                            // driver exists on agero but not in towbook -> delete it from agero
                            if (syncDriver == null)
                                driverClient.ExternalDeleteDriverProfile(testVendorId, Convert.ToInt32(ad.DriverId));
                        }
                    }

                    #endregion

                    return;

                    //AllstateEmailParser allstate = AllstateEmailParser.FromFile(@"C:\temp\allstate12.txt");
                    //Console.WriteLine(allstate.ToJson(true));
                    //return;


                    //Console.WriteLine(MotorClubMapper.ParseDate("Mon, 10/06/2014 09:34:02 AM PDT"));
                    //Console.WriteLine(MotorClubMapper.ParseDate("Mon, 10/06/2014 09:34:02 AM PST"));
                    //Console.WriteLine(MotorClubMapper.ParseDate("Mon, 10/06/2014 09:34:02 AM EDT"));
                    //Console.WriteLine(MotorClubMapper.ParseDate("Mon, 10/06/2014 09:34:02 AM EST"));

                    //return;

                }
                #endregion

                #region push to qb
                if (false)
                {
                    var ips = (await InvoicePayment.GetByCompanyAsync(2064,
                        Convert.ToDateTime("7/1/2015"), Convert.ToDateTime("8/1/2015"))).Select(o => Entry.GetById(o.DispatchEntryId));

                    IQuickbooksConnector objQBConnecter = await QuickbooksUtility.GetConnector(2064);

                    var entities = Entry.GetByCompany(Company.Company.GetById(2064), null,
                        Convert.ToDateTime("7/1/2015"),
                        Convert.ToDateTime("8/1/2015")).Union(ips).Where(o => o != null && (o.Status.Id == 5) && (o.Impound == false || (o.Impound && Impounds.Impound.GetByDispatchEntry(o).ReleaseDate != null)));

                    Console.WriteLine(String.Join(",", entities.Where(o => o != null).Select(o => o.Id)));
                    Console.ReadLine();

                    foreach (var e in entities)
                    {
                        if (e == null)
                            continue;
                    /*
                    var impound = Impounds.Impound.GetByDispatchEntry(e);

                    if (impound != null)
                    {
                        if (impound.ReleaseDate == null)
                        {
                            var dekv = DispatchEntryKeyValue.GetByDispatchEntryId(e.Id).FirstOrDefault();

                            if (dekv != null)
                            {
                                try
                                {
                                    var s = objQBConnecter.GetInvoiceById(dekv.Value);
                                    if (s != null)
                                    {
                                        try
                                        {
                                            objQBConnecter.DeleteInvoiceById(s.Id, s.SyncToken);
                                        }
                                        catch (Exception ex)
                                        {
                                            Console.WriteLine("failed deleting " + s.Id + " / dispatchEntryId " + e.Id + " / call num " + e.CallNumber + " / invoice num " + e.InvoiceNumber);
                                            Console.WriteLine(ex.InnerException.Message);

                                        }
                                        Console.WriteLine("Deleted " + s.Id + " / dispatchEntryId " + e.Id + " / call num " + e.CallNumber + " / invoice num " + e.InvoiceNumber);
                                        continue;
                                    }
                                    else
                                    {
                                        dekv.Delete();

                                    }
                                }
                                catch (Exception ez)
                                {
                                    throw new Exception(ez.ToJson(true));
                                }
                            }
                        }
                        continue;


                    }
                    else
                    {
                        continue;

                    }
                    */


                    try
                    {
                        AsyncHelper.RunSync(() => InvoicePaymentHelper.SendEntryToQuickBooksAndPayInvoice(e, Provider.QuickBooks));
                        Console.WriteLine("pushed " + e.Id + " / call # " + e.CallNumber);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine(e.Id + ": could not push " + e.Id + " / call # " + e.CallNumber);
                        Console.WriteLine(e.Id + ": " + ex.ToString());
                        Console.WriteLine(e.Id + ": *****************");
                    }
                    }
                    return;
                }

                #endregion

                //var c = new CallRequest();

                //c.CompanyId = 2;
                //c.AccountId = 10;
                //c.RequestDate = DateTime.Now;
                //c.ExpirationDate = DateTime.Now.AddMinutes(5);
                //c.Vehicle = "2006 Acura TL";
                //c.ServiceNeeded = "Tire Change";
                //c.Reason = "Flat Tire";
                //c.StartingLocation = "1161 S. Carney Drive, St. Clair MI 48079";
                //c.Save();
                //Console.WriteLine(c.ToJson());
                //return;


                //string token = "519EB7D5-7F6E4EA7-AE502E13-ECA4BE95";
                //string clientId = "MI**********";
                //string taxId = "54-12345";

                string dispatchId = "101010";
                string jobId = "101010";

                //string token = "519EB7D5-7F6E4EA7-AE502E13-ECA4BE95";
                string clientId = "TX0299999";
                string taxId = "54-123477";



                if (false)
                {
                    gc.DispatchResponse("GCO", clientId, null, dispatchId, jobId, CallRequestStatus.PhoneCallRequested,
                        "Shane", "************", 30, "n/a", IsscRestClient.DispatchSource.Manual, string.Empty);
                    Thread.Sleep(2000);
                    return;

                    gc.Disconnect();
                    return;

                    var isp = new IsscProvider();
                    /*
                    gc.DispatchStatus("GCO", clientId, "Carney", dispatchId, DateTime.Now, IsscRestClient.DispatchSource.Manual, IsscRestClient.DispatchStatusType.GPS,
                        42.8208253,
                        -82.486224,
                        DateTime.Now, "QA-TEST-1");
                    Thread.Sleep(2000);

                    return;
                    */

                    //if (false)
                    //{
                    //    gc.RegisterProvider("GCO", clientId, "Carney", "03FA4666-E97D4195-9D84CB24-E4C91BB3");
                    //    return;

                    //}

                    var account = new Accounts.Account() { CompanyId = 2, Company = "Geico Test " + clientId, Status = Accounts.AccountStatus.Active, MasterAccountId = 5, Type = Accounts.AccountType.MotorClub };
                    await account.Save();
                    isp.ClientId = "GCO";
                    isp.CompanyId = account.CompanyId;
                    isp.AccountId = account.Id;
                    isp.ContractorId = clientId;
                    isp.LocationId = "Carney";
                    isp.TaxId = taxId;
                    isp.IsLoggedIn = true;
                    isp.Save();

                    Console.WriteLine(isp.ToJson());
                    Console.ReadKey();
                    //gc.RegisterProvider("GCO", clientId, "Carney", taxId);
                    return;
                }

                var token = "";

                if (false)
                {

                    gc.DispatchResponse("GCO", clientId, "Carney", dispatchId, jobId, CallRequestStatus.PhoneCallRequested,
                        "Shane", "************", 30, "n/a", IsscRestClient.DispatchSource.Manual, string.Empty);
                    Thread.Sleep(2000);

                    return;


                    gc.DispatchStatus("GCO", clientId, "Carney", dispatchId, DateTime.Now, IsscRestClient.DispatchSource.Manual, IsscRestClient.DispatchStatusType.GPS,
                        42.8208253,
                        -82.486224,
                        DateTime.Now, "QA-TEST-1");
                    Thread.Sleep(2000);




                    //gc.Disconnect();
                    //gc.Login("GCO", clientId, null, "B8D411EB-03694E8D-B9E3B16A-166DFCF1" );
                    return;



                    gc.Connect("https://api.towbook.com/receivers/issc/", "towbook", "k638WrYceu3a");
                    return;

                    Thread.Sleep(5000);
                    gc.Disconnect();
                    Console.WriteLine("Disconnected at " + DateTime.Now);
                    Console.ReadKey();
                    return;

                    /*
                     * afer this is ent, this is recevied by the wbehook: 
                     * [ 
                          { 
                            "Event": "disconnect", 
                            "Result": "Success" 
                          } 
                        ]
                     *
                     */

                    Thread.Sleep(5000);
                    Console.WriteLine("Press any key to send a re-connect message");
                    Console.ReadLine();

                    gc.Connect("https://api.towbook.com/receivers/geico/", "towbook", "k638WrYceu3a");


                    Thread.Sleep(5000);
                    Console.WriteLine("Press any key to send a LOGIN message");
                    Console.ReadLine();

                }
                if (false)
                {
                    gc.Logout("GCO", clientId, "Carney", token);
                    Thread.Sleep(3000);

                    Console.WriteLine("Press any key to send a LOGIN message");
                    Console.ReadLine();
                }

                if (false)
                {
                    gc.Login("GCO", clientId, "Carney", token);
                    return;
                }

                if (false)
                {
                    gc.Logout("GCO", clientId, "Carney", token);
                    return;
                }

                if (false)
                {
                    gc.Disconnect();
                    return;
                }

                if (false)
                {

                    gc.DispatchResponseReject("GCO", clientId, "Carney", dispatchId, jobId,
                        rejectionDescription: "Testing Rejection",
                        providerContactName: "Shane",
                        providerPhoneNumber: "************",
                        source: IsscRestClient.DispatchSource.Manual);

                    // a dispatchResponse doesnt retrieve an event.

                    return;
                }

                if (false)
                {
                    string[] rejects = { "Rejected", "Canceled", "Expired" };

                    gc.DispatchResponse("GCO", clientId, "Carney", dispatchId, jobId, CallRequestStatus.Accepted,
                        "Shane", "************", 2, "Respecting Max ETA of 3", IsscRestClient.DispatchSource.Manual, string.Empty);

                    return;
                }

                if (false)
                {
                    gc.DispatchResponse("GCO", clientId, "Carney", dispatchId, jobId, CallRequestStatus.PhoneCallRequested,
                        "Shane", "************", 30, "n/a", IsscRestClient.DispatchSource.Manual, string.Empty);
                    Thread.Sleep(2000);

                    gc.DispatchStatus("GCO", clientId, "Carney", dispatchId, DateTime.Now, IsscRestClient.DispatchSource.Manual, IsscRestClient.DispatchStatusType.DriverAssigned);
                    Thread.Sleep(2000);

                    gc.DispatchStatus("GCO", clientId, "Carney", dispatchId, DateTime.Now, IsscRestClient.DispatchSource.Manual, IsscRestClient.DispatchStatusType.GPS,
                        42.8208253,
                        -82.486224,
                        DateTime.Now);
                    Thread.Sleep(2000);

                    gc.DispatchStatus("GCO", clientId, "Carney", dispatchId, DateTime.Now, IsscRestClient.DispatchSource.Manual, IsscRestClient.DispatchStatusType.Enroute,
                        42.8208353,
                        -82.486324,
                        DateTime.Now);
                    Thread.Sleep(2000);

                    gc.DispatchStatus("GCO", clientId, "Carney", dispatchId, DateTime.Now, IsscRestClient.DispatchSource.Manual, IsscRestClient.DispatchStatusType.OnScene,
                        42.8108653,
                        -82.426024,
                        DateTime.Now);
                    Thread.Sleep(2000);

                    gc.DispatchStatus("GCO", clientId, "Carney", dispatchId, DateTime.Now, IsscRestClient.DispatchSource.Manual, IsscRestClient.DispatchStatusType.ServiceComplete,
                        42.8208653,
                        -82.486024,
                        DateTime.Now);

                    return;
                }


                //gc.Heartbeat();


                //gc.DeregisterProvider("GCO", clientId, "Carney", token);

                //return;



                //var arc = new AgeroRestClient();
                //Console.WriteLine(JsonExtensions.ToJson(arc.GetDispatchDetailByDispatchNumber("u0vpsM3G8HsPNt45E3SDRZFRnN1B", 705121287), true));
                //return;


                #region Test MC Billing Services 
                //if (false)
                //{

                //    var svcDE = new DispatchEntryService();
                //    var svcP = new PaymentSyncService();
                //    var svcA = new AuthenticationVerificationService();

                //    var x = Stopwatch.StartNew();
                //    for (int i = 0; i < 100; i++)
                //    {
                //        svcDE.RequestInvoiceSubmission(new DispatchEntryQueueItem { CompanyId = 2, DispatchEntryId = 5, OwnerUserId = 1 });
                //        svcA.RequestVerificationOfUserCredentials(new AuthenticationQueueItem() { CompanyId = 2, AccountId = 10, OwnerUserId = 1 });

                //        svcP.RequestPaymentSync(new PaymentQueueItem() { CompanyId = 2, AccountId = 10, OwnerUserId = 1 });
                //        Console.WriteLine(x.ElapsedMilliseconds);

                //    }

                //    Console.WriteLine(x.ElapsedMilliseconds);
                //    return;



                //    Console.WriteLine(InvoicePaymentHelper.DeterminePaymentMethod(Dispatch.PaymentType.Cash, 2));

                //    return;
                //}
                #endregion

                #region Test Agero

                // -- Test Agero

                //string username = "Fastraxrside";
                //string pass = "Damien1029";

                //Console.WriteLine("agero : login");
                //using (AgeroConnection agero = AgeroConnection.Login(username, pass))
                //{
                //    Console.WriteLine("agero : login sucessfull for user " + username);
                //    Console.WriteLine("agero : retrieving PO numbers");
                //    List<int> resultPOs = agero.GetPurchaseOrderNumbers();

                //    foreach (int i in resultPOs)
                //        Console.WriteLine("agero : found PO#:" + i);

                //    Console.WriteLine("agero : checking PO defaults for PO# " + resultPOs[0]);
                //    PurchaseOrder defaults = agero.GetPurchaseOrderStatus(147266097);
                //    Console.WriteLine("agero : status for PO " + resultPOs[0] + " -> " + defaults.Status);

                //    if (defaults.Status == "NOT SUBMITTED")
                //    {
                //        // Wait to test
                //        //agero.SubmitPONumber(resultPOs[0]);
                //    }

                //    Console.WriteLine("agero : retrieving checks list");
                //    List<Check> checks = agero.GetChecks();
                //    foreach (Check item in checks)
                //        Console.WriteLine(string.Format("agero : found check#:{0} / vendorId: {1} / amount: {2}", item.CheckNumber, item.VendorId, item.Amount));

                //    Console.WriteLine("agero : retrieving check details for check # " + checks[0].CheckNumber);
                //    //Check check = agero.GetCheckDetails(checks[0].CheckNumber);
                //    Check check = agero.GetCheckFullDetails(checks[0].CheckNumber);
                //    Console.WriteLine("agero : details for check # " + checks[0].CheckNumber + " [status: " + check.Status + " | amount: " + check.Amount + " | pos: " + check.POs.Count + "]");

                //    Console.WriteLine("agero : retrieving details for PO # " + check.POs[0].PONumber);
                //    PurchaseOrder po = agero.GetPurchaseOrderDetails(check.POs[0].PONumber, check.POs[0].ClaimId);
                //    Console.WriteLine("agero : Details for PO # " + po.PONumber + " [odometer: " + po.Odometer + " | eqpType: " + po.EquipmentType + "]");
                //}

                //Console.ReadLine();

                #endregion

                #region Test AllState

                // -- Test AllState

                //string username = "*********";
                //string pass = "Zippy40";

                //Console.WriteLine("allstate : login");
                //using (AllStateConnection allstate = AllStateConnection.Login(username, pass))
                //{
                //    Console.WriteLine("allstate : login sucessfull for user " + username);
                //    //Console.WriteLine("allstate : retrieving payment statements");
                //    //List<PaymentStatement> payments = allstate.GetPaymentStatements(new DateTime(2013, 12, 11), DateTime.Now);

                //    //foreach (PaymentStatement payment in payments)
                //    //    Console.WriteLine("allstate : found statement#:" + payment.Number);

                //    //Console.WriteLine("allstate : retrieving statement details # " + payments[0].Number);
                //    //PaymentStatement ps = allstate.GetPaymentStatementDetails(payments[0].Number);

                //    //Console.WriteLine("allstate : retrieving providers");
                //    //List<string> providers = allstate.GetProviders();

                //    //foreach (string provider in providers)
                //    //    Console.WriteLine("allstate : found provider#:" + provider);

                //    //allstate.SelectProvider(providers[providers.IndexOf("*********")]);

                //    //List<ClaimSupplement> supps = allstate.GetSupplementTypes("**********");
                //    //supps[0].Value = 12;

                //    Extric.Towbook.Integration.MotorClubs.Billing.Allstate.PurchaseOrder po = allstate.GetPurchaseOrderDetails(31062785);
                //    Extric.Towbook.Integration.MotorClubs.Billing.Allstate.PurchaseOrder po2 = allstate.GetPurchaseOrderDetails(31069223);

                //    //List<AutomatedHttpLog> logs = allstate.SubmitPurchaseOrder("**********", null, "Testing", false);
                //    //foreach (var log in logs)
                //    //    Console.WriteLine(log.ToJson());
                //}

                Console.ReadLine();

                #endregion

                #region Test AAA

                //string username = "239";
                //string pass = "g239";

                //Console.WriteLine("aaa : login");
                //using (AAAConnection aaa = AAAConnection.Login(username, pass))
                //{
                //    Console.WriteLine("aaa : login sucessfull for user " + username);
                //    Console.WriteLine("aaa : retrieving payment statements");

                //    List<Call> calls = aaa.GetCalls();
                //    foreach (Call call in calls)
                //        Console.WriteLine("aaa : found Call#:" + call.Number);

                //    Call callDetails = aaa.GetCallDetails(calls[1].Key);

                //    AaaCallParser parser = AaaCallParser.FromCall(callDetails);
                //    //parser.PostSave();

                //    //aaa.UpdateCallStatus(calls[2].Key, "ONSCENE");
                //}

                //Console.ReadLine();

                #endregion

                #region Test NSD

                //string username = "<EMAIL>";
                //string pass = "116723";

                //using (NsdConnection nsd = NsdConnection.Login(username, pass))
                //{
                //    nsd.SubmitPurchaseOrder("1799965", true);

                //    nsd.GetPayments(new DateTime(2014, 01, 08), new DateTime(2014, 01, 09));
                //}

                //Console.ReadLine();

                #endregion

                #region Test Road America

                //string username = "zippyunlocks40";
                //string pass = "jgandrg40";

                //using (RoadAmericaConnection roadAmerica = RoadAmericaConnection.Login(username, pass))
                //{
                //    roadAmerica.GetPayments(new DateTime(2014, 08, 01), new DateTime(2014, 10, 01));
                //}

                //Console.ReadLine();

                #endregion

                #region Parsers Test

                //UsacEmailParser usacParser = UsacEmailParser.FromText(File.ReadAllText(@"c:\temp\email.html"));
                //CoachNetEmailParser coachParser = CoachNetEmailParser.FromText(File.ReadAllText(@"c:\temp\coach.htm"));
                //CoachNetEmailParser coachParser = CoachNetEmailParser.FromText(File.ReadAllText(@"c:\temp\coachnet-fwd.htm"));

                //string text = MotorClubMapper.TextFromPdf(@"C:\temp\ahl-2.pdf", true);

                //Console.WriteLine(text);

                //AutoHelpLineTextParser netParser = AutoHelpLineTextParser.FromText(text);

                //QuestEmailParser questParser = QuestEmailParser.FromText(File.ReadAllText(@"c:\temp\quest.htm"));
                //MotorClubMapper.Translate(questParser, 2, 1);

                //IMotorClubRequest fax = AhlFax.FromPath(@"c:\temp\fax-ahl.tif");


                //NacEmailParser nac = NacEmailParser.FromFile(@"c:\temp\nac.txt");

                //EhiEmailParser[] ehi = EhiEmailParser.FromFile(@"c:\temp\ehi.html");

                #endregion

                #region Sendgrid Test

                //bool wa = SendgridUtility.IsEmailBlocked(2, "<EMAIL>");

                //using (SmtpClient smtp = new SmtpClient())
                //{
                //    MailMessage msg = new MailMessage(new MailAddress("<EMAIL>", "Towbook"),
                //                            new MailAddress("<EMAIL>", "Matheus"));
                //    msg.Subject = "Testing Subject";
                //    msg.Body = "Testing Body";

                //    smtp.Send(msg, null, "Testing email", EmailType.DispatchEntry, 1);
                //}

                #endregion

                #region PDF - TIF Conversion Text

                //using (var img = new Bitmap(@"c:\temp\fax-ahl-2.tif"))
                //{
                //    FrameDimension fd = new FrameDimension(img.FrameDimensionsList[0]);
                //    int totalFrames = img.GetFrameCount(fd);

                //    EO.Pdf.PdfDocument doc = new EO.Pdf.PdfDocument();
                //    EO.Pdf.Acm.AcmRender render = new EO.Pdf.Acm.AcmRender(doc, new AcmPageLayout(new SizeF(8.5f, 11.0f), new AcmPadding(0, 0, 0, 0)));

                //    for (int i = 0; i < totalFrames; i++)
                //    {
                //        try
                //        {
                //            img.SelectActiveFrame(fd, i);
                //        }
                //        catch
                //        {
                //            Console.WriteLine("Couldnt select frame!!");
                //            Console.Beep();
                //            continue;
                //        }

                //        EO.Pdf.Acm.AcmImage acmImg = new EO.Pdf.Acm.AcmImage(img);
                //        render.Render(acmImg);
                //    }

                //    doD.Save(@"c:\temp\test-pdf.pdf");
                //}

                #endregion

                #region Grasshopper Test

                //string user = "<EMAIL>";
                //string pass = "towbook2014";

                //using (GrasshopperConnection grasshopper = GrasshopperConnection.Login(user, pass))
                //{
                //    grasshopper.GetCalls(DateTime.MinValue);
                //}

                #endregion

                #region Vonage Test

                //string user = "8103205063";
                //string pass = "Towbook2014";

                //using (VonageConnection vonage = VonageConnection.Login(user, pass))
                //{
                //    vonage.GetCalls();
                //}

                #endregion



            }

            private static void TestFacilityId()
            {

                int FacilityId = 3;
                int VendorId = 6668;
                int CompanyId = 6683;

                if (FacilityId > 0)
                {
                    string findKey = VendorId + "." + FacilityId;

                    var accounts = Account.GetByCompany(Company.Company.GetById(CompanyId), false, AccountType.MotorClub);

                    var foundAccount = accounts.Where(o => o.Company.Contains(findKey) || o.ReferenceNumber == findKey).FirstOrDefault();

                    if (foundAccount != null)
                        Console.WriteLine(foundAccount.Id + ":" + foundAccount.Company);
                }
            }

            private static void CreateMissingLinksForQb(int dCompanyId, QuickBooksOnlineConnector qb)
            {
                var invoices = qb.GetInvoices().ToList();

                invoices = invoices.Where(o => o.Header.InvoiceNumber.StartsWith("T")).ToList();
                var tbInvoices = SqlMapper.Query<dynamic>(
                    "SELECT KeyValue, DocNumber, DispatchEntryId FROM Integration.vwQuickbooksCalls WHERE Companyid=@CID",
                    new { CID = dCompanyId })
                    .Distinct()
                    .ToList();

                var noMatch = invoices.Where(o => !tbInvoices.Where(z => z.DocNumber == o.Header.InvoiceNumber).Any()); // DocNumber doesnt exist in the DB.
                var match = invoices.Where(o => tbInvoices.Where(z => z.DocNumber == o.Header.InvoiceNumber).Any()); // DocNumber exists.
                var matchDiff = invoices.Where(o => tbInvoices.Where(z => z.DocNumber == o.Header.InvoiceNumber && o.Id != z.KeyValue).Any());
                var matchRight = invoices.Where(o => tbInvoices.Where(z => z.DocNumber == o.Header.InvoiceNumber && o.Id == z.KeyValue).Any());

                Console.WriteLine(noMatch.Count() + " that need to have links created");
                Console.WriteLine(match.Count() + " that exist in TB and QB, regardless of link");
                Console.WriteLine(matchRight.Count() + " that exist in both linked using the correct ID.");
                Console.WriteLine(matchDiff.Count() + " that exist in both, but are not linked using the correct ID.");

                var calls = SqlMapper.Query<dynamic>("SELECT DispatchEntryId, CallNumber FROM DispatchEntries WHERE CompanyId=@CID", new { CID = dCompanyId }).ToList();
                noMatch = noMatch.OrderBy(o => o.Header.InvoiceNumber).ToList();

                foreach (var x in noMatch)
                {
                    var y = new DispatchEntryKeyValue();
                    y.KeyId = 1;
                    y.Value = x.Id;
                    y.DispatchEntryId = calls.Where(o => o.CallNumber == Convert.ToInt32(x.Header.InvoiceNumber.Substring(1))).First().DispatchEntryId;
                    y.Save();
                }

                return;

            }

            private static void AllstateLogin(string providerId)
            {
                #region Allstate DD Tests

                string username = "towbook";
                string password = "*1%tow$%89buk#$%0A56!";

                ProxyTSPClient soapClient = new ProxyTSPClient();
                receiveCredentials cred = new receiveCredentials();
                cred.UserName = username;
                cred.Password = password;

                LOGMessage login = new LOGMessage();
                login.UserName = username;
                login.Password = password;
                //login.IPAddress = "***************";
                login.CallbackURI = "https://api.towbook.com/receivers/allstate/production?wsdl";
                DDMessage loginMsg = new DDMessage("LOG");
                loginMsg.DDMessageHeader = new DDMessageHeader(providerId, "LOG", "ACK", "1.0", "TOWBOOKSOFTWARE", "1.0", "Y");
                loginMsg.DDMessageHeader.ResponseID = "0123890";
                loginMsg.DDContent = login;

                LOFMessage logoff = new LOFMessage();
                //logoff.Username = username;
                //logoff.Password = password;

                DDMessage logoffMsg = new DDMessage("LOG");
                logoffMsg.DDMessageHeader = new DDMessageHeader(providerId, "LOF");
                logoffMsg.DDMessageHeader.Key = "TOWBOOKSOFTWARE";
                logoffMsg.DDMessageHeader.ResponseID = "0123890";
                logoffMsg.DDContent = logoff;

                receiveXMLMessage msg = new receiveXMLMessage();
                msg.PsXMLMessage = loginMsg.GetXml("LOGMessage");
                //msg.PsXMLMessage = logoffMsg.GetXml("LOFMessage");

                Console.WriteLine("***********");
                Console.WriteLine(msg.PsXMLMessage);
                Console.WriteLine("***********");


                var x = soapClient.receiveXMLMessage(cred, msg);
                Console.WriteLine("RESPONSE:" + x);

                return;

                #endregion
            }

            private static void NsdLogin(string providerId, bool runLogout = false)
            {
                #region Allstate DD Tests

                string username = "<EMAIL>";
                string password = "82429";

                var nsd = new Integrations.MotorClubs.Nsd.NsdService.digitaldispatchSoapClient(new BasicHttpBinding(BasicHttpSecurityMode.Transport),
                 new EndpointAddress("https://web1.nsddispatch.com/digitaldispatch/digitaldispatch.asmx"));

                LOGMessage login = new LOGMessage();
                login.UserName = username;
                login.Password = password;
                login.CallbackURI = "https://api.towbook.com/receivers/nsd?wsdl";

                DDMessage loginMsg = new DDMessage("LOG");
                loginMsg.DDMessageHeader = new DDMessageHeader(providerId, "LOG", "ACK", "1.0", "TOWBOOK", "1.1", "Y");
                loginMsg.DDMessageHeader.ResponseID = "";
                loginMsg.DDContent = login;

                LOFMessage logoff = new LOFMessage();
                logoff.UserName = username;
                logoff.Password = password;

                DDMessage logoffMsg = new DDMessage("LOF");
                logoffMsg.DDMessageHeader = new DDMessageHeader(providerId, "LOF", key: "TOWBOOK", msgVersion: "1.1");
                logoffMsg.DDMessageHeader.Key = "TOWBOOK";
                logoffMsg.DDMessageHeader.ResponseID = "";
                logoffMsg.DDMessageHeader.LocationID = "";
                logoffMsg.DDMessageHeader.ResponseType = "ACK";
                logoffMsg.DDContent = logoff;

                Console.WriteLine(logoffMsg.GetXml("LOFMessage"));

                var x = nsd.NSDDispatch(
                    runLogout ? logoffMsg.GetXmlElement() :
                    loginMsg.GetXmlElement());

                Console.WriteLine("RESPONSE:" + x.OuterXml);

                return;

                #endregion
            }


        private static void NsdResponse(string contractorNumber,
            string responseId,
            string dispatchId,
            string type,
            int eta = 0)
        {
            var returnProxy = new RETMessage();

            string username = "<EMAIL>";
            string password = "82429";

            var nsd = new Integrations.MotorClubs.Nsd.NsdService.digitaldispatchSoapClient(new BasicHttpBinding(BasicHttpSecurityMode.Transport),
                 new EndpointAddress("https://web1.nsddispatch.com/digitaldispatch/digitaldispatch.asmx"));

            var header = new DDMessageHeader(contractorNumber, "RET", key: "TOWBOOK");
            header.ResponseID = responseId; // ResponseId must be sent using the one that the original call came in with.
            returnProxy.JobID = dispatchId;
            returnProxy.ContactName = "Dan Smith";
            header.ResponseType = "ACK";

            var msg = new DDMessage();
            msg.DDMessageHeader = header;
            msg.DDContent = returnProxy;

            switch (type)
            {
                case "accept":
                    returnProxy.ServiceProviderResponse = 0;
                    returnProxy.ETA = eta;
                    returnProxy.RejectDescription = string.Empty;

                    try
                    {
                        Console.WriteLine("Accepting nsd  callRequestId " + dispatchId + "_" + responseId);

                        Console.WriteLine(msg.GetXml());
                        var xl = nsd.NSDDispatch(msg.GetXmlElement());
                        Console.WriteLine(xl.ToJson(true));
                    }
                    catch (Exception exc)
                    {
                        Console.WriteLine("Error accepting nsd call..." + exc.Message + "\n" + exc.ToJson());

                    }
                    break;

                case "reject":
                    returnProxy.ServiceProviderResponse = 1;
                    returnProxy.RejectDescription = "4 - Equipment not available";

                    Console.WriteLine(msg.GetXml());
                    var xr = nsd.NSDDispatch(msg.GetXmlElement());
                    Console.WriteLine(xr.ToJson(true));
                    break;

                case "phone":
                    returnProxy.ServiceProviderResponse = 2;
                    returnProxy.RejectDescription = string.Empty;

                    Console.WriteLine(msg.GetXml());
                    var xz = nsd.NSDDispatch(msg.GetXmlElement());
                    Console.WriteLine(xz.ToJson(true));
                    break;
            }
        }

        private static void NacResponse(string contractorNumber,
            string responseId,
            string dispatchId,
            string type,
            int eta = 0)
        {
            var returnProxy = new RETMessage();

            var nsd = new Integrations.MotorClubs.Nac.Nac.SPDDServiceClient();

            var header = new DDMessageHeader(contractorNumber, "RET", key: "TOWBOOK");
            header.ResponseID = responseId; // ResponseId must be sent using the one that the original call came in with.
            returnProxy.JobID = dispatchId;
            returnProxy.ContactName = "Dan Smith";
            header.ResponseType = "ACK";

            var msg = new DDMessage();
            msg.DDMessageHeader = header;
            msg.DDContent = returnProxy;

            switch (type)
            {
                case "accept":
                    returnProxy.ServiceProviderResponse = 0;
                    returnProxy.ETA = eta;
                    returnProxy.RejectDescription = string.Empty;

                    try
                    {
                        Console.WriteLine("Accepting allstate callRequestId " + dispatchId + "_" + responseId);

                        Console.WriteLine(msg.GetXml());
                        var xl = nsd.SoapDDXML(msg.GetXml());
                        Console.WriteLine(xl.ToJson(true));
                    }
                    catch (Exception exc)
                    {
                        Console.WriteLine("Error accepting Allstate call..." + exc.Message + "\n" + exc.ToJson());

                    }
                    break;

                case "reject":
                    returnProxy.ServiceProviderResponse = 1;
                    returnProxy.RejectDescription = "4 - Equipment not available";

                    Console.WriteLine(msg.GetXml());
                    var xr = nsd.SoapDDXML(msg.GetXml());
                    Console.WriteLine(xr.ToJson(true));
                    break;

                case "phone":
                    returnProxy.ServiceProviderResponse = 2;
                    returnProxy.RejectDescription = string.Empty;

                    Console.WriteLine(msg.GetXml());
                    var xz = nsd.SoapDDXML(msg.GetXml());
                    Console.WriteLine(xz.ToJson(true));

                    break;
            }
        }

            private static void AllstateResponse(string contractorNumber, string responseId, string dispatchId, string type, int eta = 0)
            {
                var returnProxy = new RETMessage();
                var soapClient = new ProxyTSPClient();
                var retMsg = new receiveXMLMessage();

                var cred = new receiveCredentials();
                cred.UserName = "Advanced";
                cred.Password = "Auto123";

                var header = new DDMessageHeader(contractorNumber, "RET");
                header.ResponseID = responseId; // ResponseId must be sent using the one that the original call came in with.
                returnProxy.JobID = dispatchId;
                returnProxy.ContactName = "dan";

                var msg = new DDMessage();
                msg.DDMessageHeader = header;
                msg.DDContent = returnProxy;

                switch (type)
                {
                    case "accept":
                        returnProxy.ServiceProviderResponse = 0;
                        returnProxy.ETA = eta;
                        returnProxy.RejectDescription = string.Empty;

                        try
                        {
                            Console.WriteLine("Accepting allstate callRequestId " + dispatchId + "_" + responseId);
                            retMsg.PsXMLMessage = msg.GetXml();
                            Console.WriteLine("Allstate/" + dispatchId + "_" + responseId + "/Accept... XML=" + retMsg.PsXMLMessage);

                            string responseAccept = soapClient.receiveXMLMessage(cred, retMsg);
                            // ToDo: Parse response to see if everything went ok (when we get an actual response :))

                            Console.WriteLine("Allstate/" + dispatchId + "_" + responseId + "/Accept/Response=" + responseAccept);
                        }
                        catch (Exception exc)
                        {
                            Console.WriteLine("Error accepting Allstate call..." + exc.Message + "\n" + exc.ToJson());

                        }
                        break;

                    case "reject":
                        returnProxy.ServiceProviderResponse = 1;
                        returnProxy.RejectDescription = "36 - Equipment not available";

                        retMsg.PsXMLMessage = msg.GetXml();
                        Console.WriteLine("Allstate/" + dispatchId + "_" + responseId + "/Reject... XML=" + retMsg.PsXMLMessage);
                        string responseReject = soapClient.receiveXMLMessage(cred, retMsg);
                        Console.WriteLine("Allstate/" + dispatchId + "_" + responseId + "/Reject/Response=" + responseReject);

                        break;

                    case "phone":
                        returnProxy.ServiceProviderResponse = 2;
                        returnProxy.RejectDescription = string.Empty;

                        retMsg.PsXMLMessage = msg.GetXml();
                        Console.WriteLine("Allstate/" + dispatchId + "_" + responseId + "/RequestPhoneCall... XML=" + retMsg.PsXMLMessage);
                        string responsePhone = soapClient.receiveXMLMessage(cred, retMsg);
                        Console.WriteLine("Allstate/" + dispatchId + "_" + responseId + "/RequestPhoneCall/Response=" + responsePhone);

                        break;
                }
            }

            private static void AllstateLogout(string providerId)
            {
                #region Allstate DD Tests

                string username = "Advanced";
                string password = "Auto123";

                ProxyTSPClient soapClient = new ProxyTSPClient();
                receiveCredentials cred = new receiveCredentials();
                cred.UserName = "Advanced";
                cred.Password = "Auto123";

                LOFMessage logoff = new LOFMessage();
                //logoff.UserName = username;
                //logoff.Password = password;

                DDMessage logoffMsg = new DDMessage("LOG");
                logoffMsg.DDMessageHeader = new DDMessageHeader(providerId, "LOF");
                logoffMsg.DDMessageHeader.Key = "TOWBOOKSOFTWARE";
                logoffMsg.DDMessageHeader.ResponseID = "0123890";
                logoffMsg.DDContent = logoff;



                receiveXMLMessage msg = new receiveXMLMessage();
                msg.PsXMLMessage = logoffMsg.GetXml("LOFMessage");

                Console.WriteLine(msg.PsXMLMessage);
                Console.WriteLine("****");


                var x = soapClient.receiveXMLMessage(cred, msg);
                Console.WriteLine(x);

                return;

                #endregion
            }

            private async Task<CallRequest> CreateCallRequest(dynamic jsonObj)
            {
                DispatchDetails callDetails = jsonObj.CallDetails;

                string ageroStartingLocation = string.Format("{0}\r\n{1}, {2}-{3}",
                            callDetails.DisablementLocation.Address1,
                            callDetails.DisablementLocation.Address2,
                            callDetails.DisablementLocation.City,
                            callDetails.DisablementLocation.State);

                string ageroVehicle = string.Format("{0} {1} {2} {3}",
                    callDetails.Vehicle.Year,
                    callDetails.Vehicle.Color,
                    callDetails.Vehicle.Make,
                    callDetails.Vehicle.Model);

                AgeroSession ageroSession = AgeroSession.GetByVendorId(jsonObj.VendorId);
                CallRequest ageroRequest = new CallRequest()
                {
                    AccountId = ageroSession.AccountId,
                    CompanyId = ageroSession.CompanyId,
                    Reason = callDetails.Problem,
                    RequestDate = callDetails.ReceivedTime,
                    ServiceNeeded = callDetails.ServiceType,
                    /*Status = CallRequestStatus.None,*/
                    StartingLocation = ageroStartingLocation,
                    Vehicle = ageroVehicle,
                    PurchaseOrderNumber = callDetails.PurchaseOrderNumber
                };
                await ageroRequest.Save();

                AgeroDispatch ageroDispatch = new AgeroDispatch()
                {
                    AccessToken = ageroSession.AccessToken,
                    CallJson = callDetails.ToJson(),
                    CallRequestId = ageroRequest.CallRequestId,
                    VendorId = jsonObj.VendorId,
                    DispatchId = Convert.ToInt32(callDetails.DispatchRequestNumber)
                };

                ageroDispatch.Save();
                return ageroRequest;
            }

            private static AgeroDispatch AcceptCallRequest(dynamic jsonObj)
            {
                CallRequest acceptRequest = CallRequest.GetById(Convert.ToInt32(jsonObj.Id));
                AgeroDispatch acceptDispatch = AgeroDispatch.GetByCallRequestId(acceptRequest.CallRequestId);

                acceptDispatch.Eta = jsonObj.Eta;
                acceptDispatch.EtaReason = AcceptDispatchETAReason.None;
                acceptDispatch.Save();

                new AgeroRestClient().AcceptDispatchRequest(
                    accessToken: acceptDispatch.AccessToken,
                    dispatchNumber: acceptDispatch.DispatchId,
                    eta: acceptDispatch.Eta.GetValueOrDefault(0),
                    reason: acceptDispatch.EtaReason);

                //new AgeroRestClient().RefuseDispatchRequest(
                //    accessToken: acceptDispatch.AccessToken,
                //    dispatchNumber: acceptDispatch.DispatchId,
                //    reasonCodeId: 90);

                //new AgeroRestClient().CancelDispatchRequest(
                //    accessToken: acceptDispatch.AccessToken,
                //    dispatchNumber: acceptDispatch.DispatchId,
                //    Code: DispatchStatusCode.CustomerNotWithVehicle,
                //    Reason: DispatchStatusReason.CustomerNotWithVehicle);

                //acceptRequest.OwnerUserId = Convert.ToInt32(jsonObj.OwnerUserId);
                //acceptRequest.Status = CallRequestStatus.Accepted;
                //acceptRequest.Save();

                return acceptDispatch;
            }

            private static async Async.Task ReceiveAcceptCallRequest(dynamic jsonObj)
            {
                AgeroDispatch ad = AgeroDispatch.GetByDispatchId(Convert.ToInt32(jsonObj.DispatchRequestNumber), 0);
                CallRequest cr = CallRequest.GetById(ad.CallRequestId);

                var detailedDispatch = JsonConvert.DeserializeObject<DispatchDetails>(ad.CallJson);

                var e = new Entry();
                e.CompanyId = cr.CompanyId;
                e.AccountId = cr.AccountId;
                e.Year = detailedDispatch.Vehicle.Year;
                e.VehicleMake = detailedDispatch.Vehicle.Make;
                e.VehicleModel = detailedDispatch.Vehicle.Model;
                e.Color = (await Vehicle.Color.GetAllAsync()).Where(o => o.Name.ToLowerInvariant() == detailedDispatch.Vehicle.Color.ToLowerInvariant()).FirstOrDefault();
                e.VIN = detailedDispatch.Vehicle.VIN;
                e.LicenseNumber = detailedDispatch.Vehicle.Plate;
                e.LicenseState = detailedDispatch.Vehicle.State;
                e.Notes = "Vehicle:" + cr.Vehicle;
                e.TowSource = cr.StartingLocation;
                e.CreateDate = cr.RequestDate;
                e.ArrivalETA = DateTime.Now.AddMinutes(ad.Eta.GetValueOrDefault(0));

                e.PurchaseOrderNumber = ad.DispatchId.ToString();

                if (jsonObj.OwnerUserId == null && cr.OwnerUserId == null)
                    throw new MotorClubException("The call request does not have an associated OwnerUserId.");

                e.OwnerUserId = cr.OwnerUserId.GetValueOrDefault(0);

                AuthenticationToken token = AuthenticationToken.GetByUserId(e.OwnerUserId);
                Platform.ClientVersion cv = Platform.ClientVersion.GetByGitHash("DirectDispatch", Platform.ClientVersionType.DirectDispatching);
                if (cv != null)
                    token.ClientVersionId = cv.Id;

                await e.Save(false, token, null);

                var customerName = detailedDispatch.DisablementLocation.ContactInfo.Name;
                EntryContact c = e.Contacts.Where(o => o.Name == customerName).FirstOrDefault();

                if (c == null)
                    c = new EntryContact() { Name = customerName };

                c.Phone = detailedDispatch.DisablementLocation.ContactInfo.CallbackNumber;
                c.DispatchEntryId = e.Id;
                c.Save();

                cr.DispatchEntryId = e.Id;
                await cr.Save();

                //PushNotificationProvider.UpdateCallRequestStatus(cr.CompanyId, cr.CallRequestId, cr.Status);
            }
        }

        //public class SoapLoggerExtension : SoapExtension
        //{
        //    private Stream oldStream;
        //    private Stream newStream;

        //    public override object GetInitializer(LogicalMethodInfo methodInfo, SoapExtensionAttribute attribute)
        //    {
        //        return null;
        //    }

        //    public override object GetInitializer(Type serviceType)
        //    {
        //        return null;
        //    }

        //    public override void Initialize(object initializer)
        //    {

        //    }

        //    public override System.IO.Stream ChainStream(System.IO.Stream stream)
        //    {
        //        oldStream = stream;
        //        newStream = new MemoryStream();
        //        return newStream;
        //    }

        //    public override void ProcessMessage(SoapMessage message)
        //    {

        //        switch (message.Stage)
        //        {
        //            case SoapMessageStage.BeforeSerialize:
        //                break;
        //            case SoapMessageStage.AfterSerialize:
        //                Log(message, "AfterSerialize");
        //                CopyStream(newStream, oldStream);
        //                newStream.Position = 0;
        //                break;
        //            case SoapMessageStage.BeforeDeserialize:
        //                CopyStream(oldStream, newStream);
        //                Log(message, "BeforeDeserialize");
        //                break;
        //            case SoapMessageStage.AfterDeserialize:
        //                break;
        //        }
        //    }

        //    public void Log(SoapMessage message, string stage)
        //    {

        //        newStream.Position = 0;
        //        string contents = (message is SoapServerMessage) ? "SoapRequest " : "SoapResponse ";
        //        contents += stage + ";";

        //        StreamReader reader = new StreamReader(newStream);

        //        contents += reader.ReadToEnd();

        //        newStream.Position = 0;

        //        Console.WriteLine(contents);
        //    }

        //    void ReturnStream()
        //    {
        //        CopyAndReverse(newStream, oldStream);
        //    }

        //    void ReceiveStream()
        //    {
        //        CopyAndReverse(newStream, oldStream);
        //    }

        //    public void ReverseIncomingStream()
        //    {
        //        ReverseStream(newStream);
        //    }

        //    public void ReverseOutgoingStream()
        //    {
        //        ReverseStream(newStream);
        //    }

        //    public void ReverseStream(Stream stream)
        //    {
        //        TextReader tr = new StreamReader(stream);
        //        string str = tr.ReadToEnd();
        //        char[] data = str.ToCharArray();
        //        Array.Reverse(data);
        //        string strReversed = new string(data);

        //        TextWriter tw = new StreamWriter(stream);
        //        stream.Position = 0;
        //        tw.Write(strReversed);
        //        tw.Flush();
        //    }
        //    void CopyAndReverse(Stream from, Stream to)
        //    {
        //        TextReader tr = new StreamReader(from);
        //        TextWriter tw = new StreamWriter(to);

        //        string str = tr.ReadToEnd();
        //        char[] data = str.ToCharArray();
        //        Array.Reverse(data);
        //        string strReversed = new string(data);
        //        tw.Write(strReversed);
        //        tw.Flush();
        //    }

        //    private void CopyStream(Stream fromStream, Stream toStream)
        //    {
        //        try
        //        {
        //            StreamReader sr = new StreamReader(fromStream);
        //            StreamWriter sw = new StreamWriter(toStream);
        //            sw.WriteLine(sr.ReadToEnd());
        //            sw.Flush();
        //        }
        //        catch (Exception ex)
        //        {
        //            string message = String.Format("CopyStream failed because: {0}", ex.Message);
        //            Console.WriteLine(message + ex);
        //        }
        //    }
        //}

        //[AttributeUsage(AttributeTargets.Method)]
        //public class SoapLoggerExtensionAttribute : SoapExtensionAttribute
        //{
        //    private int priority = 1;

        //    public override int Priority
        //    {
        //        get { return priority; }
        //        set { priority = value; }
        //    }

        //    public override System.Type ExtensionType
        //    {
        //        get { return typeof(SoapLoggerExtension); }
        //    }
        //}
    }
