using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Extric.Towbook.Accounts;
using Extric.Towbook.Utility;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Twilio.Exceptions;


namespace Extric.Towbook.PermitRequests.API.Controllers
{
    [Route("permits/requests/publicLinks")]
    public class PublicLinksController : ControllerBase
    {

        [HttpGet]
        [Route("{id}/accounts/{accountId}/get")]
        public async Task<object> GetSolver(int id, int accountId, [FromQuery] string phone, [FromQuery] string email, [FromQuery] string invite = null) =>
            (string.IsNullOrEmpty(phone) || string.IsNullOrEmpty(email)) ? await GetAsync(id, accountId) : Get(id, accountId, phone, email, invite);


        // GET Account adderss by public link and accountId
        //rl.Add(routes.MapHttpRoute(
        //    name: "Permits_PublicLinks_Get",
        //    routeTemplate: root + "requests/publicLinks/{id}/accounts/{accountId}/{action}",
        //    defaults: new { id = RouteParameter.Optional, controller = "publicLinks", action = "Get" },
        //    constraints: new { httpMethod = new HttpMethodConstraint(new string[] {"GET" }) }));
        [Route("{id}/accounts/{accountId}/{action}")]
        [Route("{id}/accounts/{accountId}/get")]
        [HttpGet]
        private async Task<object> GetAsync(int id, int accountId)
        {
            var link = ParkingPermitPublicLink.GetById(id);
            if (link == null)
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent("Link not found or is not valid anymore.")
                });
            }

            if (link.Disabled) 
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                {
                    Content = new StringContent("The property turned off their ability to create permits and visitor passes with ParkingPermits.io.")
                });
            }

            var account = await Accounts.Account.GetByIdAsync(accountId);
            if (account == null)
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent("AccountId is invalid or there is no account associated by that Id.")
                });
            }

            if (link.AccountId != account.Id)
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent("Invalid parameters provided or you don't have permission.")
                });
            }

            // don't expose account email (per Dan)
            var email = string.Empty;

            return new
            {
                accountId = link.AccountId,
                address = (account.Address + " " +
                        (!string.IsNullOrWhiteSpace(account.City) && !string.IsNullOrWhiteSpace(account.State) ?
                            account.City + ", " + account.State + " " + account.Zip : "")).Trim(),
                email,
                hash = ParkingPermitPublicLink.GenerateUrlHash(link.Id, link.AccountId, true),
                linkId = link.Id,
                name = account.Company,
                phone = account.Phone
            };
        }


        /// <summary>
        /// Request One Time Code
        /// </summary>
        /// <param name="phone">Mobile phone number that will receive the one time code.</param>
        /// <param name="email">Email address that will receive the one time code.</param>
        /// <returns>An HttpResponse message equivalent object</returns>
        //rl.Add(routes.MapHttpRoute(
        //    name: "Permits_PublicLinks_Get",
        //    routeTemplate: root + "requests/publicLinks/{id}/accounts/{accountId}/{action}",
        //    defaults: new { id = RouteParameter.Optional, controller = "publicLinks", action = "Get" },
        //    constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "GET" }) }));
        [Route("{id}/accounts/{accountId}/get")]
        [HttpGet]
        private async Task<object> Get(int id, int accountId, [FromQuery] string phone, [FromQuery] string email, [FromQuery] string invite = null)
        {
            if (string.IsNullOrWhiteSpace(phone) && string.IsNullOrWhiteSpace(email))
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent("Invalid parameters provided. You must provide a phone number or email to retrieve your code.")
                });
            }

            // check for demo
            if (id == 0 && accountId == 0)
            {
                await HelperUtility.SendCodeToResident(phone, email, accountId, id, true);
                return new { Message = "Your code has been re-sent via text message or email. Enter the code to continue." }.ToJson();
            }

            var link = ParkingPermitPublicLink.GetById(id);
            if (link == null)
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent("Link not found or is not valid anymore.")
                });
            }

            var account = await Accounts.Account.GetByIdAsync(accountId);
            if (account == null)
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent("AccountId is invalid or there is no account associated by that Id.")
                });
            }

            if (link.AccountId != account.Id)
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent("Invalid parameters provided or you don't have permission.")
                });
            }

            ParkingPermitRequest request = null;


            if (!string.IsNullOrEmpty(invite))
            {
                var decode = HelperUtility.DecodeUrlHash(invite);
                if (decode != null && decode.Length == 2)
                    request = ParkingPermitRequest.GetById(decode[0]);
            }
            else
            {
                // Search by permits, previous approval requests, and then invites.
                request = ParkingPermitRequest.Search(account.Id, phone, email).FirstOrDefault();
            }

            if (request != null)
            {
                // send one time password
                await HelperUtility.SendOneTimeCode(request.CellPhone, request.Email, account.Id, request.Id);
                return new { Message = "Your code has been re-sent via text message or email. Enter the code to continue." }.ToJson();
            }

            return new Collection<object>();
        }


        /// <summary>
        /// Initial registration from either an invite or general user at parkingPermits.io.
        /// This method will create an AccountParkingPermitRequest object and send out a
        /// one time code to the recipient.  Note: a permits.permitUser object is not created.
        /// </summary>
        /// <param name="id">AccountParkingPermitPublicLinkId of the property known by the property code used.</param>
        /// <param name="accountId">The account associated with the property code.</param>
        /// <param name="model">Contains name, email, phone and the type of user (visitor, general user, worker/employee).  If CodeOnly is true, 
        /// a one time code will be txt'ed or emailed. If false, a txt or email is sent with direct URL link.</param>
        /// <returns></returns>
        //rl.Add(routes.MapHttpRoute(
        //    name: "Permits_PublicLinks_Post",
        //    routeTemplate: root + "requests/publicLinks/{id}/accounts/{accountId}",
        //    defaults: new { id = RouteParameter.Optional, controller = "publicLinks", action = "Post" },
        //    constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "Post" }) }));
        [Route("{id}/accounts/{accountId}")]
        [HttpPost]
        [AllowAnonymous]
        public async Task<object> Post(int id, int accountId, ParkingPermitNewRequestModel model)
        {
            if (model == null || (string.IsNullOrWhiteSpace(model.Phone) && string.IsNullOrWhiteSpace(model.Email)))
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent("Invalid parameters provided.  You must provide a phone number or email to receive a new code.")
                });
            }

            // check for demo
            if (id == 0 && accountId == 0)
            {
                await HelperUtility.SendCodeToResident(model.Phone, model.Email, accountId, id, true);
                return new { Message = "Your code has been sent via text message or email. Enter the code to continue." }.ToJson();
            }

            var link = ParkingPermitPublicLink.GetById(id);
            if (link == null)
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent("Link not found or is not valid anymore.")
                });
            }

            var account = await Accounts.Account.GetByIdAsync(accountId);
            if (account == null)
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent("AccountId is invalid or there is no account associated by that Id.")
                });
            }

            if (link.AccountId != account.Id)
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent("Invalid parameters provided or you don't have permission.")
                });
            }

            // search for permit holder, an applicaiton for approval, and then a request before assuming a new application
            ParkingPermitRequest request = ParkingPermitRequest.Search(account.Id, Core.FormatPhone(model.Phone, true), model.Email).FirstOrDefault();
            if (request != null)
            {
                request.CellPhone = request.CellPhone ?? Core.FormatPhone(model.Phone, true);
                request.Email = request.Email ?? model.Email;
                request.FullName = request.FullName ?? model.Name;
            }

            // No request found
            if (request == null)
            {
                var statusIds = new int[] { PermitStatus.Valid.Id, PermitStatus.Revoked.Id, PermitStatus.Rejected.Id, PermitStatus.Extended.Id, PermitStatus.Expired.Id };


                // check for a permit (that was never a request)
                var byPhone = new Collection<ParkingPermit>();
                var byEmail = new Collection<ParkingPermit>();
                ParkingPermitApprovalSession session = null;

                if (!string.IsNullOrEmpty(model.Phone)) {
                    byPhone = ParkingPermit.Find(new int[] { accountId }.ToArray(),
                     null, statusIds, null, null, null, null,
                     Core.FormatPhone(model.Phone, true)).ToCollection();
                }

                if (!string.IsNullOrEmpty(model.Email)) {
                    byEmail = ParkingPermit.Find(new int[] { accountId }.ToArray(),
                        null, statusIds.ToArray(), null, null, null, null, null,
                        model.Email).ToCollection();
                }

                // create one permit collection
                var permits = byPhone.ToCollection();
                foreach (var p in byEmail)
                {
                    if (!permits.Select(s => s.Id).Contains(p.Id))
                        permits.Add(p);
                }

                // Save an initial request
                request = new ParkingPermitRequest()
                {
                    AccountId = accountId,
                    CellPhone = model.Phone,
                    Email = model.Email,
                    FullName = permits.FirstOrDefault()?.FullName ?? model.Name,
                    CreateDate = new DateTime(),
                    UserTypeId = model.userTypeId
                };
                await request.Save(false);


                // these are permits that never had an invitation. They were most
                // likely created in the office or by a tow manager.
                if (permits != null && permits.Any())
                {
                    // Save session and session items.  This allows permit holders
                    // to find their permits via parkingPermits.io

                    session = new ParkingPermitApprovalSession()
                    {
                        RequestId = request.Id,
                        AccountId = account.Id,
                        CompanyId = account.CompanyId,
                        SessionStatusId = (int)ApprovalSessionStatuses.Approved,
                        CreateDate = DateTime.Now,
                        Deleted = true
                    };

                    await session.Save(false);

                    foreach (var p in permits)
                    {
                        var item = new ParkingPermitApprovalSessionItem()
                        {
                            ApprovalSessionId = session.Id,
                            ParkingPermitId = p.Id,
                            ParkingPermitListId = p.ParkingPermitListId.GetValueOrDefault(),
                            Approved = true,
                            LicensePlate = p.LicensePlate,
                            LicensePlateState = p.LicensePlateState,
                            VehicleColor = p.VehicleColor,
                            VehicleMake = p.VehicleMake,
                            VehicleModel = p.VehicleModel,
                            VehicleYear = p.VehicleYear,
                            VIN = p.VIN
                        };
                        item.Save();
                    }

                    if (session != null)
                    {
                        request.SessionId = session.Id;
                    }
                }

                // generate and save url and code now that we have an Id
                request.Url = HelperUtility.GenerateUrlHash(request.Id, account.Id, false);
                request.Code = HelperUtility.GenerateUrlHash(request.Id, account.Id, true);
                await request.Save(true);


                // deleting the request is important.  This provides the date the request
                // was completed and the permits were submitted.
                if (session != null)
                    await request.Delete(null);
            }

            try
            {
                // Send txt/email to resident
                if (model.CodeOnly)
                {
                    await HelperUtility.SendOneTimeCode(request.CellPhone, request.Email, account.Id, request.Id);
                }
                else
                    await HelperUtility.SendInviteToResident(request.CellPhone, request.Email, account.Id, request.Id);
            }
            catch (TwilioException)
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent($"There was a problem sending a text message to the phone number {model.Phone}. Please verify the phone number is correct.")
                });
            }
            catch (Exception)
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent($"There was a problem sending an email message to the email {model.Email}. Please verify the email is correct.")
                });
            } 
            finally
            {
                HelperUtility.LogInfo(new Dictionary<object, object>
                {
                    ["type"] = model.CodeOnly ? "Send One-Time Code" : "Send invite",
                    ["request"] = request.ToJson(),
                    ["account"] = account.Company,
                    ["accountId"] = account.Id
                }, account.CompanyId);
            }

            return new { Message = "Your code has been sent via text message or email. Enter the code to continue." }.ToJson();
        }

        /// <summary>
        /// Validate one time code. The goal is to link an AccountParkingPermitRequest object to a 
        /// Permits.PermitUser object.  This will register the user on parkingpermits.io for future
        /// returns from the same device.
        /// </summary>
        /// <param name="id">AccountParkingPermitPublicLinkId - the property code used by the performer.</param>
        /// <param name="accountId">The account id the request belongs to. Needed for access validation.</param>
        /// <param name="code">The one time code sent to the user</param>
        /// <param name="phone">Needed for user validation.</param>
        /// <param name="email">Needed for user validation.</param>
        /// <returns></returns>
        //rl.Add(routes.MapHttpRoute(
        //    name: "Permits_PublicLinks_Get",
        //    routeTemplate: root + "requests/publicLinks/{id}/accounts/{accountId}/{action}",
        //    defaults: new { id = RouteParameter.Optional, controller = "publicLinks", action = "Get" },
        //    constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "GET" }) }));
        [Route("{id}/accounts/{accountId}/validateCode")]
        [HttpGet]
        [AllowAnonymous]
        public async Task<object> ValidateCode(int id, 
            int accountId, 
            [FromQuery]int code,
            [FromQuery] int userTypeId,
            [FromQuery] string invite = null,
            [FromQuery] string phone = null, 
            [FromQuery] string email = null)
        {
            if (code == 0 || userTypeId == 0 ||
                (phone == null && email == null))
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent("URL paramaters missing.  Provide the code and the user type along with a phone number or email address to continue.")
                });
            }

            if (!new Role[] { Role.Guest, Role.Member, Role.Worker }.Contains(Role.GetById(userTypeId)))
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent("The user type provided is invalid.")
                });
            }

            var link = ParkingPermitPublicLink.GetById(id);
            if (link == null)
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent("Link not found or is not valid anymore.")
                });
            }

            var account = await Accounts.Account.GetByIdAsync(accountId);
            if (account == null)
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent("AccountId is invalid or there is no account associated by that Id.")
                });
            }

            if (link.AccountId != account.Id)
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent("Invalid parameters provided or you don't have permission.")
                });
            }

            if (email == "\"\"")
                email = string.Empty;

            if (phone == "\"\"")
                phone = string.Empty;

            var cleanPhone = Core.FormatPhone(phone, true);

            // get all matched codes
            var otp = OneTimePassword.GetByCode(code);

            // get all requests belonging to the codes
            var requests = ParkingPermitRequest.GetByIds(otp.Select(s => s.PermitRequestId).ToArray(), true).ToCollection();
            
            // Narrow down the codes by the requests that belong to this account
            // Narrow down accountId, phone number, and email
            requests = requests
                        .Where(w => w.AccountId == account.Id)
                        .Where(w => w.CellPhone == cleanPhone || w.CellPhone == phone || w.Email == email)
                        .OrderBy(a => a.CellPhone ?? "")
                        .ThenBy(b => b.Email ?? "")
                        .ToCollection();

            var match = otp.Where(w => requests.Select(s => s.Id).Contains(w.PermitRequestId)).FirstOrDefault();
            if (match != null)
            {
                // Check for 'already used' code
                if (match.CapturedDate != null)
                {
                    throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                    {
                        Content = new StringContent("The one-time code provided was already used and is now invalid. Please request another code to continue.")
                    });
                }
                
                
                // Check for expired condition. Otherwise, return hash code.
                if (match.CreateDate.AddSeconds(OneTimePassword.EXPRIATION_IN_SECONDS) < DateTime.Now)
                {
                    throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                    {
                        Content = new StringContent("The code provided has expired.  Please request a new code to continue.")
                    });
                }
                else
                {
                    PermitUser user = null;

                    // check for permit user account 
                    var request = requests.Where(w => w.Id == match.PermitRequestId).FirstOrDefault();
                    if (request?.UserId != null)
                    {
                        user = PermitUser.GetById(request.UserId.Value);
                        if (user == null)
                        {
                            throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                            {
                                Content = new StringContent("We could not find your account or there is a problem with your account. Please contact the property manager for help.")
                            });
                        }

                        // force user type to be what the actually found user is.
                        userTypeId = user.UserTypeId;
                    }
                    else
                    {
                        // Create new user account
                        user = new PermitUser() {
                            Name = request?.FullName ?? string.Empty,
                            Email = email,
                            Phone = cleanPhone,
                            UserTypeId = userTypeId,
                            CreateDate = DateTime.Now,
                            UserName = null,
                            Password = null
                        };

                        user.Save();
                        
                        await HelperUtility.SendWelcomeMessage(user, request);

                        // make sure matched request has userId saved for easy authentication by UserId.
                        request.UserId = user.Id;
                        request.UserTypeId = user.UserTypeId;
                        await request.Save(false);
                    }

                    // generate jwtToken for authentication by Role for a period of time
                    // (this registers the device used to bypass getting another one time code)
                    user.Token = TokenValidationHandler.GenerateJWTToken(user);

                    match.MarkCaptured();

                    HelperUtility.LogInfo(new Dictionary<object, object>
                    {
                        ["type"] = "Validate One-Time Code",
                        ["code"] = code,
                        ["user"] = new {
                                user.Id, user.Name, user.Phone, user.Email,
                                UserType = user.UserType.ToString(), user.CreateDate
                            }.ToJson(),
                        ["request"] = request.ToJson(),
                        ["account"] = account.Company,
                        ["accountId"] = account.Id
                    }, account.CompanyId);

                    return PermitUserModel.Map(user, link.Id, match.PermitRequestId, accountId);
                }
            }

            throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
            {
                Content = new StringContent("The code provided cannot be validated. Try again or request a new code.")
            });
        }
    }
}
