using System;
using System.Collections.Generic;
using System.Linq;

namespace Extric.Towbook.Web
{
    public class Section
    {
        private const int CacheTimeout = 1440;

        public string UrlPartName { get; private set; }

        public int WebsiteId { get; set; }
        public int ParentSectionId { get; set; }
        public int SectionId { get; set; } 
        public string Name { get; set; }
        public int? DefaultPageId { get; set; }

        public Section()
        {
            UrlPartName = "section1";
        }

        public static Section GetByPartName(int websiteId, int? parentSectionId, string partName)
        {
            return new Section();
            /*return SqlHelper.Get<Section>("TWS.SectionsGetByPartName", 
                new { WebsiteId = websiteId,
                      ParentSectionId = parentSectionId, 
                      UrlPartName = partName },
                "section:" + websiteId +"_" + parentSectionId + "_" + partName.ToLowerInvariant(), CacheTimeout); */

        }

        public IUrlPart GetByPartName(string partName, IUrlPart parent)
        {
            throw new NotImplementedException();
        }
    }
}