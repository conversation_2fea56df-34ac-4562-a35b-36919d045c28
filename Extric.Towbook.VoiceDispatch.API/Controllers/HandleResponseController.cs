using System;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Extric.Towbook.Dispatch;
using Extric.Towbook.EventNotifications;
using Extric.Towbook.Generated;
using Extric.Towbook.Integration.MotorClubs.Services;
using Extric.Towbook.Utility;
using Twilio.TwiML;
using Microsoft.AspNetCore.Mvc;
using Extric.Towbook.WebShared;

namespace Extric.Towbook.VoiceDispatchApi.Controllers
{
    public sealed class HandleResponseModel
    {
        public string msg { get; set; }
        public string Called { get; set; }
        public string Digits { get; set; }
        public string ToState { get; set; }
        public string CallerCountry { get; set; }
        public string Direction { get; set; }
        public string CallerState { get; set; }
        public string ToZip { get; set; }
        public string CallSid { get; set; }
        public string To { get; set; }
        public string CallerZip { get; set; }
        public string ToCountry { get; set; }
        public string ApiVersion { get; set; }
        public string CalledZip { get; set; }
        public string CalledCity { get; set; }
        public string CallStatus { get; set; }
        public string From { get; set; }
        public string AccountSid { get; set; }
        public string CalledCountry { get; set; }
        public string CallerCity { get; set; }
        public string Caller { get; set; }
        public string FromCountry { get; set; }
        public string ToCity { get; set; }
        public string FromCity { get; set; }
        public string CalledState { get; set; }
        public string FromZip { get; set; }
        public string FromState { get; set; }
    }

    [Route("VoiceDispatch/HandleResponse")]
    public class HandleResponseController : ControllerBase

    {
        [HttpPost("{companyId}/{voiceId}")]
        public async Task<HttpResponseMessage> PostBodyAsync(int companyId, string voiceId, [FromBody] HandleResponseModel d) => await Post(companyId, voiceId, d);

        [ApiExplorerSettings(IgnoreApi = true)]
        [HttpPost("{companyId}/{voiceId}")]
        [FormContentType]
        public async Task<HttpResponseMessage> PostFormAsync(int companyId, string voiceId, [FromForm] HandleResponseModel d) => await Post(companyId, voiceId, d);

        internal async Task<HttpResponseMessage> Post(int companyId, string voiceId, 
            HandleResponseModel model)
        {
            var digits = model.Digits;
            var response = new VoiceResponse();

            Guid voiceGuid = Guid.Parse(voiceId);

            var vd = VoiceDispatch.GetByPublicId(voiceGuid);

            if (vd == null)
                return new HttpResponseMessage(System.Net.HttpStatusCode.NotFound);

            if (vd.Type == 1)
            {
                if (digits == "1")
                {
                    // Accept
                    response.Say("OK, give me your E.T.A. by keying it in and pressing pound.");
                    response.Append(new Twilio.TwiML.Voice.Gather(
                        new System.Collections.Generic.List<Twilio.TwiML.Voice.Gather.InputEnum>() { Twilio.TwiML.Voice.Gather.InputEnum.Dtmf },
                        new Uri($"{WebGlobal.GetDomain()}/voicedispatch/confirmResponse/{companyId}/{voiceGuid.ToString("N")}"), finishOnKey: "#"));
                }
                else if (digits == "2")
                {
                    // Refuse based on Sid. 
                    response.Say("OK, I'll refuse it for you.");
                }
                else
                {
                    response.Say(digits + " is not a valid response.");
                }
            }
            else if (vd.Type == 2)
            {
                var en = Entry.GetById((int)vd.ReferenceId);
                if (en == null)
                    return new HttpResponseMessage(System.Net.HttpStatusCode.NotFound);

                if (en.Status.Id > Status.Dispatched.Id)
                {
                    response.Say("This call has has already been responded to. Please check the Towbook app.");
                }
                else
                {
                    if (Core.FormatPhoneWithNumbersOnly(en.Driver.MobilePhone) !=
                        Core.FormatPhoneWithNumbersOnly(vd.PhoneNumber))
                    {
                        response.Say("This call has already been reassigned or cancelled. Please contact your dispatcher if you have any questions.");
                    }
                    else
                    {
                        var accept = false;
                        var invalid = false;
                        if (digits == "1")
                        {
                            response.Say($"Thank you, I'll accept call {en.CallNumber} on your behalf. Please open Towbook to check call details and update your status.");
                            accept = true;
                        }
                        else if (digits == "2")
                        {
                            response.Say("OK, I'll refuse the call on your behalf.");
                            accept = false;
                        }
                        else
                        {
                            response.Say("You provided an invalid response. Please login to Towbook and respond to this call.");
                            invalid = true;
                        }

                        if (!invalid)
                        {
                            var driver = en.Assets.FirstOrDefault()?.Drivers?.FirstOrDefault();
                            if (driver != null)
                            {
                                if (driver.ResponseStatusId == DriverDispatchStatus.Accepted || driver.ResponseStatusId == DriverDispatchStatus.Rejected)
                                {

                                    response = new VoiceResponse();
                                    response.Say("This call has already been responded to.");
                                }
                                else
                                {
                                    driver.ResponseStatusId = accept ? DriverDispatchStatus.Accepted : DriverDispatchStatus.Rejected;
                                    driver.ResponseTime = DateTime.Now;
                                    driver.ResponseUserId = en.Driver.UserId;
                                    driver.Save(null);
                                    var finalStatus = NotificationMessageStatus.NoResponse;

                                    if (accept)
                                    {
                                        await Integration.PushNotificationProvider.Push(en.CompanyId, "call_dispatch_accepted", new
                                        {
                                            callId = en.Id,
                                            extra = "driver_accepted",
                                            userId = en.Driver.UserId
                                        });

                                        // trigger notification event queue item that driver accepted call
                                        if (await en.Company.HasFeatureAsync(Features.Notifications_StandardEventNotifications))
                                        {
                                            var item = new EventNotifications.DispatchingQueueItem();

                                            item.Type = EventNotifications.DispatchingTriggerType.CallAcceptedByDriver;
                                            item.DispatchEntryId = en.Id;
                                            item.DriverId = driver.Id;
                                            await item.TriggerEvent();
                                        }

                                        finalStatus = NotificationMessageStatus.Accepted;
                                    }
                                    else
                                    {
                                        await Integration.PushNotificationProvider.Push(en.CompanyId, "call_dispatch_rejected", new
                                        {
                                            callId = en.Id,
                                            extra = "driver_rejected",
                                            userId = en.Driver.UserId,
                                            response = "Phone Call, rejected via option 2",
                                        });
                                        finalStatus = NotificationMessageStatus.Rejected;
                                    }


                                    await new ActivityLogging.ActivityLogItem()
                                    {
                                        ParentObjectId = en.Id,
                                        ParentObjectTypeId = ActivityLogging.ActivityLogType.DispatchEntry,
                                        ObjectId = (int)vd.Id,
                                        Type = ActivityLogging.ActivityLogType.NotificationMessage,
                                        ActionId = (int)ActivityLogging.ActivityLogActionType.MessageConfirmation,
                                        IpAddress = WebGlobal.GetRequestingIp(),
                                        UserId = en.Driver?.UserId ?? 1,
                                        Details = new ActivityLogging.ActivityLogItemDetail()
                                        {
                                            Data = new
                                            {
                                                UserId = en.Driver.Id,
                                                Status = Enum.GetName(typeof(NotificationMessageStatus), finalStatus),
                                                StatusResponse = "",
                                                VoiceDispatchId = vd.Id
                                            }.ToJson(),
                                            ActivityLogItemDetailTypeId = ActivityLogging.ActivityLogItemDetailType.RawJson
                                        }
                                    }.SaveAsync();

                                    if (finalStatus == NotificationMessageStatus.Rejected)
                                    {
                                        try
                                        {
                                            var qc = await ServiceBusHelper.CreateProducerQueueAsync("autodispatch");
                                            var bm = new BrokeredMessage(new
                                            {
                                                callId = en.Id,
                                                companyId = en.CompanyId,
                                                driverId = en.DriverId,
                                                type = "driver_refused"
                                            }.ToJson()) {
                                                TimeToLive = TimeSpan.FromMinutes(5)
                                            };

                                            await qc.SendAsync(bm);
                                        }
                                        catch (Exception)
                                        {
                                            // ignore errors writing to sb. 
                                        }
                                    }

                                }
                            }
                        }
                    }
                }

            }
            return new HttpResponseMessage()
            {
                Content = new StringContent(response.ToString(), Encoding.UTF8, "application/xml")
            };
        }
    }
}
