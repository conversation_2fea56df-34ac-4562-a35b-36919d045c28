using Extric.Towbook.Accounts;
using Extric.Towbook.API.Models.Accounts;
using Extric.Towbook.Web.PublicRequestCallMvc.Models;
using System;
using System.Linq;

namespace Extric.Towbook.Web.PublicRequestCallMvc
{
    public static class WebFormUtility
    {
        public static bool IsSectionVisible(string sectionName, AccountPublicRequestFormTemplateModel model, bool defaultVisibility = true)
        {
            var section = model?.Sections?.FirstOrDefault(f => f.Name.Equals(sectionName));
            if (section == null)
                return defaultVisibility;

            return section.Fields.Any(a => IsFieldVisible(section.Name, a.Name, model, defaultVisibility));
        }

        public static bool IsFieldVisible(string sectionName, string fieldName, AccountPublicRequestFormTemplateModel model, bool defaultVisibility = true)
        {
            if (model == null || !model.Sections.Any())
                return defaultVisibility;

            var section = model.Sections.FirstOrDefault(f => f.Name.Equals(sectionName));
            if (section == null)
                return defaultVisibility;

            var field = section.Fields.FirstOrDefault(f => f.Name.Equals(fieldName));
            if (field == null)
                return defaultVisibility;

            if (field.Option?.Id == (int)SettingOption.Hidden)
                return false;

            return defaultVisibility;
        }

        public static bool IsFieldRequired(string sectionName, string fieldName, AccountPublicRequestFormTemplateModel template)
        {
            if (template == null || !template.Sections.Any())
                return false;

            var section = template.Sections.FirstOrDefault(f => f.Name.Equals(sectionName));
            if (section == null)
                return false;

            var field = section.Fields.FirstOrDefault(f => f.Name.Equals(fieldName));
            if (field == null)
                return false;

            if (field.Option?.Id == (int)SettingOption.Required)
                return true;

            return false;
        }

        public static string GetDefaultValue(string sectionName, string fieldName, AccountPublicRequestFormTemplateModel model, string defaultValue = null)
        {
            var field = model.Sections.FirstOrDefault(f => f.Name.Equals(sectionName))?.Fields?.FirstOrDefault(f => f.Name.Equals(fieldName));
            if (field == null)
                return defaultValue;

            if (field.DefaultOption?.Id == (int)DefaultValueOption.LeaveEmpty)
                return string.Empty;

            return Core.HtmlEncode(field.DefaultValue?? defaultValue ?? string.Empty);
        }

        public static string GetContactDefaultValue(string fieldName, AccountPublicRequestFormTemplateModel model, string defaultValue = null)
        {
            var field = model.Sections.FirstOrDefault(f => f.Name.Equals("Contact Details"))?.Fields?.FirstOrDefault(f => f.Name.Equals(fieldName));
            if (field == null)
                return defaultValue ?? string.Empty;

            if (field.DefaultOption?.Id == (int)DefaultContactValueOption.LeaveEmpty)
                return string.Empty;
            else if (field.DefaultOption?.Id == (int)DefaultContactValueOption.UseAccountDefault && model.Accounts.Any())
            {
                var account = Account.GetById(model.Accounts.FirstOrDefault()?.Id ?? 0);
                if (account != null && string.IsNullOrEmpty(account.FullName))
                    return account.FullName;
            }

            return Core.HtmlEncode(field.DefaultValue ?? defaultValue ?? string.Empty);
        }

        public static string GetLocationDefaultAddress(string fieldName, AccountPublicRequestFormTemplateModel model, string address = null)
        {
            var field = model.Sections.FirstOrDefault(f => f.Name.Equals("Location Information"))?.Fields?.FirstOrDefault(f => f.Name.Equals(fieldName));
            if (field == null)
                return address ?? string.Empty;

            if (field.DefaultOption.Id == (int)DefaultLocationValueOption.LeaveEmpty)
                return string.Empty;
            else if (field.DefaultOption.Id == (int)DefaultLocationValueOption.UseDefaultAddress)
                return Core.HtmlEncode(field.DefaultValue);
            else if (field.DefaultOption.Id == (int)DefaultLocationValueOption.UseImpoundLot && field.DefaultImpoundLotId > 0)
            {
                var lot = Impounds.Lot.GetById(model.CompanyId, field.DefaultImpoundLotId.GetValueOrDefault());
                if (lot != null)
                    return lot.Address + " " + lot.City + ", " + lot.State + " " + lot.Zip;
            }
            else if (field.DefaultOption.Id == (int)DefaultLocationValueOption.UseAccountDefault && model.Accounts.Any())
            {
                var account = Account.GetById(model.Accounts.FirstOrDefault()?.Id ?? 0);
                if(account != null && string.IsNullOrEmpty(account.Address))
                    return account.Address + " " + account.City + ", " + account.State + " " + account.Zip;
            }
            
            return address ?? string.Empty;
        }

        public static async System.Threading.Tasks.Task<string> ValidateRequiredFieldsAsync(AccountPublicRequestFormTemplateModel template, PublicRequestCallModel model)
        {
            if (model == null || template == null)
                return null;

            foreach(var section in template.Sections)
            {
                foreach (var field in section.Fields)
                {
                    if(IsFieldRequired(section.Name, field.Name, template))
                    {
                        var valid = true;
                        var responseMessage = string.Empty;
                        
                        switch(field.Name)
                        {
                            case "VIN":
                                responseMessage = "The vehicle VIN is required.";
                                valid = !string.IsNullOrEmpty(model.VIN);
                                break;
                            case "Make":
                                responseMessage = "The vehicle make is required.";
                                valid = model.MakeId > 0 || !string.IsNullOrEmpty(model.Make);
                                break;
                            case "Model":
                                responseMessage = "The vehicle model is required.";
                                valid = model.ModelId > 0 || !string.IsNullOrEmpty(model.Model);
                                break;
                            case "Year":
                                responseMessage = "The vehicle year is required.";
                                valid = model.Year > 0;
                                break;
                            case "Color":
                                responseMessage = "The vehicle color is required.";
                                valid = model.ColorId > 0;
                                break;

                            case "License Plate":
                                responseMessage = "The vehicle license plate is required.";
                                valid = !string.IsNullOrEmpty(model.LicenseNumber);
                                break;
                            case "License Plate State":
                                responseMessage = "The license plate state is required.";
                                valid = !string.IsNullOrEmpty(model.LicenseState);
                                break;

                            case "Destination Location":
                                responseMessage = "The destination location is required.";
                                valid = !string.IsNullOrEmpty(model.TowDestination);
                                break;

                            case "Name":
                                responseMessage = "A contact name is required.";
                                valid = !string.IsNullOrEmpty(model.ContactName);
                                break;
                            case "Phone Number":
                                responseMessage = "A contact phone number is required.";
                                valid = !string.IsNullOrEmpty(model.ContactPhone);
                                break;
                            case "Email":
                                responseMessage = "A contact email is required.";
                                valid = !string.IsNullOrEmpty(model.ContactEmail);
                                break;

                            case "Reason":
                                responseMessage = "A reason for the service request must be provided.";
                                valid = model.ReasonId > 0;
                                break;

                            case "Purchase Order #":
                                responseMessage = "A purchase order number must be provided.";
                                valid = model.AttributeValues != null && model.AttributeValues.Any(a => a.Id == 4 && !string.IsNullOrEmpty(a.Value));
                                break;

                            case "Notes":
                                responseMessage = "Notes are empty and must be provided";
                                break;

                            default:
                                if(field.AttributeId > 0)
                                {
                                    var attr = await Dispatch.Attribute.GetByIdAsync(field.AttributeId.GetValueOrDefault());
                                    var av = model.AttributeValues.FirstOrDefault(f => f.Id == field.AttributeId.GetValueOrDefault());

                                    if(attr?.Name == field.Name)
                                    {
                                        responseMessage = $"{attr.Name} is empty and must be provided.";
                                        valid = !string.IsNullOrEmpty(av?.Value);
                                    }
                                }
                                break;
                        }
                        
                        if (!valid)
                            return responseMessage;
                    }
                }
            }

            return null;
        }
    }
}
