using System;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using Extric.Towbook.SSO;
using ITfoxtec.Identity.Saml2;
using ITfoxtec.Identity.Saml2.Schemas;
using NLog;

namespace Extric.Towbook.WebShared.SSO
{
    public class SAMLCallback : SSOCallbackBase
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        private User _ssoUser;
        private bool _ssoEnable;
        private SSOTokens _ssoTokens;
        private DateTimeOffset _SecurityTokenValidTo;
        private SSORelayState _relayState;

        protected HttpRequest _request;

        public override SSOTokens SsoTokens => _ssoTokens;

        public override bool IsMobileRequest => RelayState.IsMobileDevice;

        private SSORelayState RelayState
        {
            get
            {
                if (_relayState is null)
                {
                    var relayStateValues = HttpUtility.UrlDecode(_request.Form["RelayState"]);
                    _relayState = SSORelayState.Create(relayStateValues != null ? HttpUtility.ParseQueryString(relayStateValues)["ReturnUrl"] : null);
                }
                return _relayState;
            }
        }

        public override string SSO_TYPE => "SAML";

        public SAMLCallback(HttpRequest request)
        {
            _request = request;
            _guid = Guid.NewGuid();
        }

        public override async Task<User> GetUserAsync()
        {
            if (_ssoUser != null)
            {
                return _ssoUser;
            }

            if (_request.Form != null && _request.Form["SAMLResponse"] != null)
            {
                var configurationsSaml = await CompanySecuritySetting.GetEnabledSsoByType(SSO_TYPE);
                foreach (var configurationSaml in configurationsSaml)
                {
                    try
                    {
                        var config = SSOConfiguration.GetSamlConfiguration(configurationSaml);
                        var binding = new Saml2PostBinding();
                        var saml2AuthnResponse = new Saml2AuthnResponse(config);

                        binding.ReadSamlResponse(_request.ToGenericHttpRequest(), saml2AuthnResponse);

                        binding.Unbind(_request.ToGenericHttpRequest(), saml2AuthnResponse);

                        if (saml2AuthnResponse.Status == Saml2StatusCodes.Success)
                        {
                            var userName = saml2AuthnResponse.ClaimsIdentity.Name;

                            var domainCode = CompanySecuritySetting.GetDomain(userName);
                            if (!configurationSaml.CompanyDomain.Equals(domainCode))
                            {
                                logger.Warn("The Username's domain does not match with the Configuration.");
                                continue;
                            }

                            var email = saml2AuthnResponse.ClaimsIdentity.Claims
                                .Where(c => c.Type.Equals("email") || c.Type.Equals("http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress"))
                                .Select(c => c.Value).FirstOrDefault();
                            if (email == null)
                            {
                                email = userName;
                            }

                            var firstName = saml2AuthnResponse.ClaimsIdentity.Claims
                                .Where(c => c.Type.Equals("firstname") || c.Type.Equals("http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname"))
                                .Select(c => c.Value).FirstOrDefault();

                            var lastName = saml2AuthnResponse.ClaimsIdentity.Claims
                                .Where(c => c.Type.Equals("lastname") || c.Type.Equals("http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname"))
                                .Select(c => c.Value).FirstOrDefault();

                            var fullName = string.IsNullOrEmpty(firstName) || string.IsNullOrEmpty(lastName) ? userName : firstName + " " + lastName;
                            _SecurityTokenValidTo = saml2AuthnResponse.SecurityTokenValidTo;

                            if (configurationSaml != null && configurationSaml.SsoEnable)
                            {
                                _ssoEnable = configurationSaml.SsoEnable;
                                _ssoUser = await CheckAndGetUserAsync(userName, email, fullName, configurationSaml.CompanyId, configurationSaml.DefaultUserType);
                                _ssoTokens = new SSOTokens
                                {
                                    Username = userName,
                                    TokenHint = _ssoUser.Id.ToString(),
                                    ValidTo = _SecurityTokenValidTo.DateTime
                                };
                                break;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        logger.Warn(ex, "Error on decoding SAMLResponse. " + ex.Message);
                        continue;
                    }
                }
            }
            return _ssoUser;
        }

        public override DateTimeOffset ValidTo()
        {
            return _SecurityTokenValidTo;
        }

        public override string GetRedirectUrl()
        {
            if (_ssoEnable && _request.Form != null)
            {
                return RelayState.IsMobileDevice
                    ? string.Format(RETURN_MOBILE_FORMAT, _guid)
                    : RelayState.ReturnUrl;
            }
            return null;
        }

        public override void RegisterTokenHint(string uniqueKey)
        {
        }

    }
}
