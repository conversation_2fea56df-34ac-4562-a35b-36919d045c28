using System;
using System.Collections.ObjectModel;
using System.Data.SqlClient;
using Glav.CacheAdapter.Core.DependencyInjection;
using Extric.Towbook.Company;
using ProtoBuf;
using System.Linq;
using System.Diagnostics;
using Extric.Towbook.Integration;
using Extric.Towbook.Utility;
using System.Collections.Generic;
using Extric.Towbook.Dispatch;
using Async = System.Threading.Tasks;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace Extric.Towbook.Accounts
{
    public enum AccountImpoundDestination { None = 0, Default = 1, Own = 2, ThirdParty = 3 }
    public enum AccountStatus { None = 0, Active = 1, Inactive = 2 }

    [Serializable]
    [CacheKey("_accounts")]
    [ProtoContract(ImplicitFields = ImplicitFields.AllFields, Name = "Account")]
    public class Account
    {
        #region Enumerations

        public enum InvoiceTerm
        {
            Net15 = 0,
            Net30 = 1,
            Immediate = 2
        }

        public enum BillableType
        {
            Default = 0,
            Billable = 1,
            NotBillable = 2
        }

        public enum PreferredBillableMethod
        {
            Default = 0,
            Statement = 1,
            Invoice = 2
        }

        #endregion

        #region Fields
        private int _id = -1;
        private int _companyId;
        private DateTime _createDate;
        private string _referenceNumber;
        private string _company;
        private string _fullName;
        private string _address;
        private string _city;
        private string _state;
        private string _zip;
        private string _country;
        private string _email;
        private string _website;
        private string _notes;
        private string _phone;
        private string _fax;
        private AccountType _type;
        private Nullable<DateTime> _latestActivity;
        private InvoiceTerm _invoiceTerms;
        private Nullable<decimal> _creditLimit;
        private decimal _creditBalance;
        private bool _invoiceDeliveryByFax;
        private bool _invoiceDeliveryByMail;
        private bool _invoiceDeliveryByEmail;
        private Nullable<DateTime> _statementEndDate;
        private decimal _discountRate;
        private bool _deleted;
        private bool _taxExempt;
        private int _statementPaymentCount = -1;
        #endregion

        #region Properties

        [Key]
        public int Id
        {
            get { return _id; }
            set
            {
                _id = value;
            }
        }

        public AccountStatus Status { get; set; }

        /// <summary>
        /// Set whether the account should be billable. Use the IsBillable method though, to determine if the account should actually be billed for a call.
        /// </summary>
        public BillableType Billable
        {
            get;
            set;
        }

        public int ParentAccountId
        {
            get;
            set;
        }

        public bool TaxExempt
        {
            get { return _taxExempt; }
            set { _taxExempt = value; }
        }

        public string IdAndType
        {
            get { return _id + "|" + Convert.ToInt32(_type).ToString(); }
        }

        [PartitionKey]
        public int CompanyId
        {
            get { return _companyId; }
            set { _companyId = value; }
        }

        public DateTime CreateDate
        {
            get => _createDate;
            private set => _createDate = value;
        }

        public DateTime? LatestActivity
        {
            get { return _latestActivity; }
        }

        public AccountImpoundDestination ImpoundDestinationType { get; set; }
        public int? ImpoundDestinationStorageLotId { get; set; }

        /// <summary>
        /// List of companies have access to this account.
        /// </summary>
        [JsonProperty]
        public int[] Companies { get; private set; }

        public int MasterAccountId { get; set; }

        public int DefaultPriority { get; set; }

        public string DefaultPO { get; set; }

        public int? DefaultStorageRateItemId { get; set; }

        /// <summary>
        ///  HACK
        /// </summary>
        public string ImpoundDestinationStorageLotLocation
        {
            get
            {
                if (ImpoundDestinationStorageLotId != null)
                {
                    var lot = Extric.Towbook.Impounds.Lot.GetById(this.CompanyId, ImpoundDestinationStorageLotId.Value);
                    if (lot != null)
                    {
                        return (lot.AccountId.HasValue ? Accounts.Account.GetById(lot.AccountId.Value).Company + ": " : "") + lot.Address + ", " + lot.City + ", " + lot.State + " " + lot.Zip + ", Phone: " + lot.Phone;
                    }

                }
                return "(not specified)";
            }
        }

        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }

        public static void CacheClearById(int id, bool balanceOnly = false)
        {
            var x = Account.GetById(id);

            if (x != null)
            {
                Accounts.Account.UpdateBalance(id);
            }

            if (balanceOnly)
                return;

            AppServices.Cache.InvalidateCacheItem("account_" + id);
            AppServices.Cache.InvalidateCacheItem("account_" + id + "_tags");
        }

        /// <summary>
        /// set a cache key to tell caching to reset itself for any method that relies on it.
        /// </summary>
        /// <param name="companyId"></param>
        public static void SetCacheResetById(int companyId, bool remove)
        {
            if (remove)
                AppServices.Cache.InvalidateCacheItem("accnt:ResetCompany" + companyId);
            else
                AppServices.Cache.Add("accnt:ResetCompany" + companyId, DateTime.Now.AddYears(1),
                    Guid.NewGuid().ToString());
        }
        [ProtoContract]
        internal class DecimalContainer
        {
            [ProtoMember(1)]
            public decimal Value { get; set; }

            public DecimalContainer() { }

            public DecimalContainer(decimal value)
            {
                Value = value;
            }
        }

        /// <summary>
        /// Determine if this account should be billable for a DispatchEntry assigned to this account.
        /// </summary>
        /// <returns></returns>
        public bool IsBillable()
        {
            if (this.Billable == BillableType.Billable ||
               (this.Billable == BillableType.Default && this.Type != AccountType.PoliceDepartment))
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// Requests the balance cache be cleared and refreshed the next time it is needed.
        /// </summary>
        /// <param name="accountId"></param>
        /// <param name="companyId"></param>
        public static void UpdateBalance(int accountId, int companyId = 0)
        {
            if (accountId < 1)
                return;

            if (companyId == 0)
            {
                var account = Account.GetById(accountId);

                foreach (int company in SharedCompany.GetByCompanyId(account.CompanyId)
                    .Select(o => o.CompanyId)
                    .Union(new int[] { account.CompanyId }).Distinct())
                {
                    UpdateBalance(accountId, company);
                }
                return;
            }
            AppServices.Cache.InvalidateCacheItem("account_" + companyId + "_" + accountId + ".Balance");

            SqlMapper.ExecuteSP("AccountBalancesInsertOrUpdate", new
            {
                @AccountId = accountId,
                @CompanyId = companyId,
                @Balance = -1
            });
        }

        public class BalanceContainer
        {
            public decimal Balance { get; set; }
            public int CompanyId { get; set; }
            public int AccountId { get; set; }
        }

        internal static BalanceContainer GetBalanceFromDatabase(int companyId, int id)
        {
            return SqlMapper.Query<BalanceContainer>("SELECT Balance from AccountBalances with (nolock) WHERE CompanyId=@CompanyId AND AccountId=@AccountId",
                 new
                 {
                     CompanyId = companyId,
                     AccountId = id
                 }).FirstOrDefault();
        }

        internal static IEnumerable<BalanceContainer> GetBalancesFromDatabase(int companyId)
        {
            return SqlMapper.Query<BalanceContainer>("SELECT Balance from AccountBalances with (nolock) WHERE CompanyId=@CompanyId",
                 new
                 {
                     CompanyId = companyId
                 }).ToCollection();
        }

        public async Task<decimal> GetBalanceAsync(int companyId = 0)
        {
            if (companyId == 0)
                companyId = this.CompanyId;

            var cacheKey = "account/balances/block:balance:" + Id;

            if (Id == 1)
                cacheKey += "_" + companyId;

            if (await Core.GetRedisValueAsync(cacheKey) == "1")
                return 0;

            //AppServices.Cache.InvalidateCacheItem("account_" + companyId + "_" + Id + ".Balance");

            return (await AppServices.Cache.GetAsync("account_" + companyId + "_" + Id + ".Balance",
                DateTime.Now.AddSeconds(3), async () =>
                {
                    var balanceFromDb = GetBalanceFromDatabase(companyId, Id);
                    if (balanceFromDb != null && balanceFromDb?.Balance != -1)
                        return new DecimalContainer(balanceFromDb?.Balance ?? 0);

                    decimal invoicesBalance = 0;
                    decimal totals = 0;
                    decimal totals2 = 0;

                    if (_statementPaymentCount == -1)
                    {
                        Collection<StatementPayment> sps = null;
                        sps = StatementPayment.GetByAccountId(this.Id);
                        _statementPaymentCount = sps.Count;
                    }

                    if (_statementPaymentCount == 0)
                    {
                        // new mode
                        Console.WriteLine("!BALANCE StatementPaymentsCount == " + _statementPaymentCount + " for AccountId " + this.Id);

                        var invoices = await Invoice.GetByAccountIdAsync(this.Id, new int[] { companyId }, true);

                        var items = await InvoiceItem.GetByInvoiceIdAsync(invoices.Where(o => o.WillNeedToRetrieveInvoiceItemsFromDatabase).Select(o => o.Id).ToArray());

                        foreach (var i in invoices)
                        {
                            if (i.WillNeedToRetrieveInvoiceItemsFromDatabase)
                            {
                                var t = items.Where(o => o.InvoiceId == i.Id).ToCollection();
                                if (t.Any())
                                    i.InvoiceItems = t;
                            }
                        }

                        foreach (var inv in invoices)
                        {
                            var x = Stopwatch.StartNew();
                            invoicesBalance += inv.BalanceDue;
                            totals += inv.GrandTotal;
                            if (inv.BalanceDue > 0)
                                Console.WriteLine("!BALANCE EXECUTE InvoiceId " + inv.Id + " == " + inv.BalanceDue + " ... elapsed " + x.ElapsedMilliseconds);
                        }

                        SqlMapper.ExecuteSP("AccountBalancesInsertOrUpdate", new
                        {
                            @AccountId = this.Id,
                            @CompanyId = companyId,
                            @Balance = invoicesBalance
                        });

                        return new DecimalContainer(invoicesBalance);
                    }
                    else
                    {
                        decimal balance = 0;
                        #region old code, for pre 2013 invoices - this is awful.. need to migrate the old data so this code doesnt need to run

                        Collection<Statement> statements = await Statement.GetByCompanyAsync(companyId, Id, null, null, false, null);
                        Collection<Dispatch.Entry> entries = await Statement.GetUnbilledDispatchEntriesAsync(companyId, Id);

                        foreach (Statement st in statements)
                        {
                            balance += st.NowBalance;
                            Console.WriteLine(st.Id + " ... paidInFull: " + st.PaidInFull + ":" + st.Balance + " / " + st.Total);
                        }

                        foreach (Dispatch.Entry en in entries)
                        {
                            bool billable = false;

                            if (Billable == BillableType.Billable || Billable == BillableType.Default)
                            {
                                if (Billable == BillableType.Billable ||
                                    (Billable == BillableType.Default &&
                                        (_type != AccountType.PoliceDepartment &&
                                            _type != AccountType.PrivateProperty)))
                                {
                                    billable = true;
                                }
                            }

                            // Is this account a billable entitity? 
                            if (en.Account != null && en.Account.Id == this.Id && billable)
                            {
                                if (en.Invoice.AccountId == this.Id || en.Invoice.AccountId.GetValueOrDefault() == 0)
                                {
                                    if (en.BalanceDue > 0)
                                        balance += (decimal)en.BalanceDue;
                                    totals2 += en.InvoiceTotal;
                                    if (en.BalanceDue > 0)
                                        Console.WriteLine(en.Invoice.Id + ":" + en.BalanceDue + "|" + en.Invoice.AccountId);
                                }
                            }
                            else
                            {
                                if (en.Invoice.AccountId == this.Id)
                                {
                                    balance += (decimal)en.BalanceDue;
                                    totals2 += en.InvoiceTotal;

                                    if (en.BalanceDue > 0)
                                        Console.WriteLine(en.Id + ":" + en.BalanceDue + "...." + balance + "|" + en.Invoice.AccountId);
                                }
                            }
                        }

                        SqlMapper.ExecuteSP("AccountBalancesInsertOrUpdate", new
                        {
                            @AccountId = this.Id,
                            @CompanyId = companyId,
                            @Balance = balance
                        });

                        return new DecimalContainer(balance);

                        #endregion

                    }

                })).Value;

        }

        [Obsolete]
        public enum AgedCategory
        {
            d30 = 0,
            d60 = 1,
            d90 = 2,
            d120 = 3,
            d120beyond = 4
        }

        public enum AgedQuickbookCategory
        {
            Current = 0,
            D1_30 = 1,
            D31_60 = 2,
            D61_90 = 3,
            D91beyond = 4
        }

        public class AgedInvoice
        {
            public enum ImpoundInclusion
            {
                All = 0,
                ReleasedOnly = 1,
                None = 2,
                WithoutStorageCharges = 3,
                AllWithOnlyTowing = 4,
                ExcludeStorageLots = 5,
                ImpoundOnly = 6
            }
            public int DispatchEntryId { get; set; }
            public string InvoiceNumber { get; set; }
            public string PurchaseOrderNumber { get; set; }
            public int CallNumber { get; set; }
            public decimal BalanceDue { get; set; }
            public decimal CallTotal { get; set; }
            public DateTime JobDate { get; set; }
            public AgedQuickbookCategory Age { get; set; }

            public int? AccountManagerUserId { get; set; }

            public AgedInvoice(
                int dispatchEntryId,
                string invoiceNumber,
                int callNumber,
                decimal balanceDue,
                decimal callTotal,
                DateTime jobDate,
                AgedQuickbookCategory age,
                string purchaseOrderNumber,
                int? accountManagerUserId = null)
            {
                DispatchEntryId = dispatchEntryId;
                InvoiceNumber = invoiceNumber;
                CallNumber = callNumber;
                BalanceDue = balanceDue;
                CallTotal = callTotal;
                JobDate = jobDate;
                Age = age;
                AccountManagerUserId = accountManagerUserId;
                PurchaseOrderNumber = purchaseOrderNumber;
            }

            public AgedInvoice(
                Invoice invoice,
                List<CategoryInvoice> categoryInvoices,
                AgedQuickbookCategory age)
            {
                DispatchEntryId = invoice.DispatchEntry?.Id ?? 0;
                InvoiceNumber = invoice.DispatchEntry?.InvoiceNumber ?? "";
                CallNumber = invoice.DispatchEntry?.CallNumber ?? 0;
                BalanceDue = categoryInvoices.FirstOrDefault(a => a.DispatchEntryId == invoice.DispatchEntryId) != null
                    ? categoryInvoices.First(b => b.DispatchEntryId == invoice.DispatchEntryId).BalanceDue
                    : invoice.BalanceDue;
                CallTotal = categoryInvoices.FirstOrDefault(a => a.DispatchEntryId == invoice.DispatchEntryId) != null
                    ? categoryInvoices.First(b => b.DispatchEntryId == invoice.DispatchEntryId).Total
                    : invoice.Subtotal + invoice.Tax;
                JobDate = invoice.DispatchEntry != null
                    ? invoice.DispatchEntry.CompletionTime != null
                        ? invoice.DispatchEntry.CompletionTime.Value
                        : invoice.DispatchEntry.CreateDate
                    : invoice.CreateDate;
                Age = age;
                PurchaseOrderNumber = invoice.DispatchEntry?.PurchaseOrderNumber ?? "";
                AccountManagerUserId = invoice.DispatchEntry?.GetAccountManagerUserId();
            }
        }

        public class AgedBalanceList
        {
            public Dictionary<int, decimal> AgedBalances { get; set; }
            public Dictionary<int, DateTime> AgedDates { get; set; }

            public List<AgedInvoice> AgedInvoices { get; set; }

            public AgedBalanceList(Dictionary<int, decimal> agedBalances, List<AgedInvoice> agedInvoices, Dictionary<int, DateTime> agedDates = null)
            {
                AgedBalances = agedBalances;
                AgedInvoices = agedInvoices;
                AgedDates = agedDates ?? new Dictionary<int, DateTime>();
            }
        }

        public async Task<AgedBalanceList> GetAgedBalancesAsync(int companyId = 0, DateTime? endDate = null, IEnumerable<Invoice> invoices = null)
        {
            if (companyId == 0)
                companyId = this.CompanyId;

            return await GetAgedBalancesAsync(new int[] { companyId }, Account.AgedInvoice.ImpoundInclusion.ReleasedOnly, endDate, invoices);
        }

        public async Task<AgedBalanceList> GetAgedBalancesAsync(int[] companyIds, DateTime? endDate = null, IEnumerable<Invoice> invoices = null)
        {
            return await GetAgedBalancesAsync(companyIds, Account.AgedInvoice.ImpoundInclusion.ReleasedOnly, endDate, invoices);
        }

        public async Task<AgedBalanceList> GetAgedBalancesAsync(int[] companyIds, AgedInvoice.ImpoundInclusion impoundInclusion, DateTime? endDate = null, IEnumerable<Invoice> invoices = null)
        {
            string key = string.Join(",", companyIds);

            // TODO: change this to use a container instead of a dictionary. dictionaries cannot be serialized and thus will not be cached.
            return await AppServices.Cache.GetAsync<AgedBalanceList>("aged_account_" + key + "_" + Id + ".Balance",
                    DateTime.Now.AddMinutes(30),
                    async () => await GetAgedBalanceListWithoutCacheAsync(companyIds, impoundInclusion, endDate, invoices));
        }

        private async Task<AgedBalanceList> GetAgedBalanceListWithoutCacheAsync(int[] companyIds,
            AgedInvoice.ImpoundInclusion impoundInclusion,
            DateTime? endDate = null,
            IEnumerable<Invoice> invoiceInput = null)
        {
            if (_statementPaymentCount == -1)
            {
                Collection<StatementPayment> statementPayments = StatementPayment.GetByAccountId(this.Id);
                _statementPaymentCount = statementPayments.Count;
            }

            var company = Extric.Towbook.Company.Company.GetById(companyIds[0]);

            if (endDate == null)
            {
                // Set endDate to equivalent company local time at 1 second before midnight
                // We want to include all invoice for the entire endDate
                endDate = Core.OffsetDateTime(company, DateTime.Now.Date.AddHours(23).AddMinutes(59).AddSeconds(59), true);
            }

            Dictionary<int, decimal> agedBalance = new Dictionary<int, decimal>();
            Dictionary<int, DateTime> agedDates = new Dictionary<int, DateTime>();

            var invoices = invoiceInput != null ? invoiceInput : await Invoice.GetByAccountIdAsync(this.Id, companyIds, true, endDate);

            if (this.Type == AccountType.StorageFacility && impoundInclusion == AgedInvoice.ImpoundInclusion.ExcludeStorageLots)
                invoices = invoices.Where(w => w.AccountId == null && w.AccountId != this.Id);

            DateTime now = endDate.Value;

            List<AgedInvoice> agedInvoiceList = new List<AgedInvoice>();

            if (impoundInclusion == AgedInvoice.ImpoundInclusion.None) // no impounds
                invoices = invoices.Where(w => w.Impound == null);
            else if (impoundInclusion == AgedInvoice.ImpoundInclusion.ReleasedOnly) // include non impound and released impounds only (keep it historical)
                invoices = invoices.Where(w => w.Impound == null || !(w.Impound != null && w.Impound.ReleaseDate == null));

            var allCategories = await RateItemCategory.GetByCompanyAsync(companyIds[0]);
            var includeCategories = allCategories;
            // wrap invoices here
            if (impoundInclusion == AgedInvoice.ImpoundInclusion.WithoutStorageCharges)
                includeCategories = allCategories.Where(w => w.Id != 2 && w.Id != 5).ToCollection();
            else if (impoundInclusion == AgedInvoice.ImpoundInclusion.AllWithOnlyTowing)
                includeCategories = allCategories.Where(w => w.Id == 1).ToCollection();

            // This is about to be enumerated repeatedly
            var invoiceCollection = invoices.ToCollection();
            invoices = invoiceCollection;
            List<CategoryInvoice> categoryInvoices =
                GetCategoryInvoiceList(invoiceCollection, impoundInclusion, includeCategories, allCategories, endDate);

            agedInvoiceList.AddRange(invoices
                .Where(invoice => InvoiceHasBalanceAndDateWithinTimeFrame(invoice, now, 0, -1))
                .Select(invoice => new AgedInvoice(invoice, categoryInvoices, AgedQuickbookCategory.Current))
                .ToList());

            agedInvoiceList.AddRange(invoices
                .Where(invoice => InvoiceHasBalanceAndDateWithinTimeFrame(invoice, now, -1, -31))
                .Select(invoice => new AgedInvoice(invoice, categoryInvoices, AgedQuickbookCategory.D1_30))
                .ToList());

            agedInvoiceList.AddRange(invoices
                .Where(invoice => InvoiceHasBalanceAndDateWithinTimeFrame(invoice, now, -31, -61))
                .Select(invoice => new AgedInvoice(invoice, categoryInvoices, AgedQuickbookCategory.D31_60))
                .ToList());

            agedInvoiceList.AddRange(invoices
                .Where(invoice => InvoiceHasBalanceAndDateWithinTimeFrame(invoice, now, -61, -91))
                .Select(invoice => new AgedInvoice(invoice, categoryInvoices, AgedQuickbookCategory.D61_90))
                .ToList());

            agedInvoiceList.AddRange(invoices
                .Where(invoice => InvoiceHasBalanceAndDateWithinTimeFrame(invoice, now, -91, null))
                .Select(invoice => new AgedInvoice(invoice, categoryInvoices, AgedQuickbookCategory.D91beyond))
                .ToList());


            agedBalance.Add(0, 0m);
            agedBalance.Add(1, 0m);
            agedBalance.Add(31, 0m);
            agedBalance.Add(61, 0m);
            agedBalance.Add(91, 0m);

            agedDates.Add(0, Core.OffsetDateTime(company, now));
            agedDates.Add(1, Core.OffsetDateTime(company, now.AddDays(-1)));
            agedDates.Add(31, Core.OffsetDateTime(company, now.AddDays(-31)));
            agedDates.Add(61, Core.OffsetDateTime(company, now.AddDays(-61)));
            agedDates.Add(91, Core.OffsetDateTime(company, now.AddDays(-91)));

            agedBalance[0] = invoices
                .Where(invoice => InvoiceDateWithinTimeFrame(invoice, now, 0, -1))
                .Sum(invoice => GetAgedBalanceSum(invoice, categoryInvoices));
            agedBalance[1] = invoices
                .Where(invoice => InvoiceDateWithinTimeFrame(invoice, now, -1, -31))
                .Sum(invoice => GetAgedBalanceSum(invoice, categoryInvoices));
            agedBalance[31] = invoices
                .Where(invoice => InvoiceDateWithinTimeFrame(invoice, now, -31, -61))
                .Sum(invoice => GetAgedBalanceSum(invoice, categoryInvoices));
            agedBalance[61] = invoices
                .Where(invoice => InvoiceDateWithinTimeFrame(invoice, now, -61, -91))
                .Sum(invoice => GetAgedBalanceSum(invoice, categoryInvoices));
            agedBalance[91] = invoices
                .Where(invoice => InvoiceDateWithinTimeFrame(invoice, now, -91, null))
                .Sum(invoice => GetAgedBalanceSum(invoice, categoryInvoices));

            return new AgedBalanceList(agedBalance, agedInvoiceList.ToList(), agedDates);
        }

        private List<CategoryInvoice> GetCategoryInvoiceList(Collection<Invoice> invoices,
            AgedInvoice.ImpoundInclusion impoundInclusion,
            Collection<RateItemCategory> includeCategories,
            Collection<RateItemCategory> allCategories,
            DateTime? endDate = null)
        {
            List<CategoryInvoice> categoryInvoices = new List<CategoryInvoice>();

            foreach (var inv in invoices)
            {
                if (impoundInclusion == AgedInvoice.ImpoundInclusion.WithoutStorageCharges ||
                            impoundInclusion == AgedInvoice.ImpoundInclusion.AllWithOnlyTowing)
                {
                    if (inv.DispatchEntry?.Impound != null && !(inv.Impound?.ReleaseDate != null && endDate != null && inv.Impound?.ReleaseDate < endDate))
                        categoryInvoices.Add(new CategoryInvoice(inv, includeCategories));
                    else
                        categoryInvoices.Add(new CategoryInvoice(inv, allCategories));
                }
                else
                {
                    categoryInvoices.Add(new CategoryInvoice(inv, allCategories));
                }
            }
            return categoryInvoices;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="invoice"></param>
        /// <param name="now">May not be current time -  just the time the report revolves around</param>
        /// <param name="minDays"></param>
        /// <param name="maxDays"></param>
        /// <returns></returns>
        private static bool InvoiceHasBalanceAndDateWithinTimeFrame(Invoice invoice, DateTime now, int minDays, int? maxDays)
        {
            return invoice.BalanceDue != 0 &&
                (invoice.DispatchEntry != null && invoice.DispatchEntry.CompletionTime != null
                    ? invoice.DispatchEntry.CompletionTime.Value
                    : invoice.CreateDate) <= now.AddDays(minDays) &&
                // if max days is null then the second conditions are irrelevant
                (maxDays == null || (invoice.DispatchEntry != null && invoice.DispatchEntry.CompletionTime != null
                    ? invoice.DispatchEntry.CompletionTime.Value
                    : invoice.CreateDate) > now.AddDays(maxDays ?? 0));
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="invoice"></param>
        /// <param name="now">May not be current time -  just the time the report revolves around</param>
        /// <param name="minDays"></param>
        /// <param name="maxDays"></param>
        /// <returns></returns>
        private static bool InvoiceDateWithinTimeFrame(Invoice invoice, DateTime now, int minDays, int? maxDays)
        {
            bool withinMinDate = (invoice.DispatchEntry != null && invoice.DispatchEntry.CompletionTime != null
                    ? invoice.DispatchEntry.CompletionTime.Value
                    : invoice.CreateDate) <= now.AddDays(minDays);
            // if max days is null then the second conditions are irrelevant
            bool withinMaxDate = maxDays == null || (invoice.DispatchEntry != null && invoice.DispatchEntry.CompletionTime != null
                    ? invoice.DispatchEntry.CompletionTime.Value
                    : invoice.CreateDate) > now.AddDays(maxDays ?? 0);
            return withinMinDate && withinMaxDate;
        }

        private static decimal GetAgedBalanceSum(Invoice invoice, List<CategoryInvoice> categoryInvoices)
        {
            return categoryInvoices.FirstOrDefault(categoryInvoice => categoryInvoice.DispatchEntryId == invoice.DispatchEntryId) != null
                ? categoryInvoices.First(categoryInvoice => categoryInvoice.DispatchEntryId == invoice.DispatchEntryId).BalanceDue
                : invoice.BalanceDue;
        }

        /// <summary>
        /// Current Balance balance that's yet to be paid. 
        /// </summary>
        [Obsolete("Use GetBalance")]
        public decimal Balance
        {
            get
            {
                return 0;
            }
        }

        public decimal? CreditLimit
        {
            get => _creditLimit;
            set => _creditLimit = value;
        }

        /// <summary>
        /// Indicates how much in dollars exists in unapplied payments, thus creating a credit balance.
        /// </summary>
        public decimal CreditBalance
        {
            get => _creditBalance;
            set => _creditBalance = value;
        }

        public string Address
        {
            get { return _address; }
            set { _address = value; }
        }

        public string City
        {
            get { return _city; }
            set { _city = value; }
        }

        public string FullName
        {
            get { return _fullName; }
            set { _fullName = value != null ? value.Trim() : value; }
        }

        public string Company
        {
            get { return _company; }
            set { _company = value != null ? value.Trim() : value; }
        }

        public string Country
        {
            get { return _country; }
            set { _country = value; }
        }

        public string Email
        {
            get { return _email; }
            set { _email = value; }
        }

        public string Notes
        {
            get { return _notes; }
            set { _notes = value; }
        }

        public string ReferenceNumber
        {
            get { return _referenceNumber; }
            set { _referenceNumber = value; }
        }

        public AccountType Type
        {
            get { return _type; }
            set { _type = value; }
        }

        public string Website
        {
            get { return _website; }
            set { _website = value; }
        }

        public string State
        {
            get { return _state; }
            set { _state = value; }
        }

        public string Zip
        {
            get { return _zip; }
            set { _zip = value; }
        }

        public string Phone
        {
            get { return _phone; }
            set { _phone = value; }
        }

        public string Fax
        {
            get { return _fax; }
            set { _fax = value; }
        }

        public bool InvoiceDeliverByFax
        {
            get { return _invoiceDeliveryByFax; }
            set { _invoiceDeliveryByFax = value; }
        }

        public bool InvoiceDeliverByMail
        {
            get { return _invoiceDeliveryByMail; }
            set { _invoiceDeliveryByMail = value; }
        }

        public bool InvoiceDeliverByEmail
        {
            get { return _invoiceDeliveryByEmail; }
            set { _invoiceDeliveryByEmail = value; }
        }

        public Nullable<DateTime> StatementEndDate
        {
            get { return _statementEndDate; }
            set { _statementEndDate = value; }
        }

        public InvoiceTerm InvoiceTerms
        {
            get { return _invoiceTerms; }
            set { _invoiceTerms = value; }
        }

        public bool Deleted
        {
            get { return _deleted; }
        }

        [ProtoIgnore]
        private StorageRate _storageRates;
        public StorageRate StorageRates
        {
            get
            {
                if (_storageRates == null)
                {
                    _storageRates = Extric.Towbook.Accounts.StorageRate.GetByAccount(this);
                    if (_storageRates == null)
                    {
                        _storageRates = new StorageRate();
                        _storageRates.AccountId = this.Id;
                    }
                }
                return _storageRates;
            }
            set
            {
                _storageRates = value;
            }
        }

        public decimal DiscountRate
        {
            get { return _discountRate; }
            set { _discountRate = value; }
        }

        #endregion

        private Collection<AccountTag> _tags;
        public Collection<AccountTag> Tags
        {
            get
            {
                if (_tags == null)
                {
                    InternalAccountTagList x = AppServices.Cache.Get<InternalAccountTagList>("account_" + _id + "_tags",
                        TimeSpan.FromHours(36),
                        () =>
                        {
                            var list = new InternalAccountTagList();

                            list.Tags = AccountTag.GetByAccountId(_id);

                            return list;
                        });

                    _tags = x.Tags ?? new Collection<AccountTag>();
                }

                return _tags;
            }
            set
            {
                _tags = value;
                AppServices.Cache.InvalidateCacheItem("account_" + _id + "_tags");
            }
        }

        private IEnumerable<Stickering.Reason> _reasons;

        public IEnumerable<Stickering.Reason> Reasons
        {
            get
            {
                if (_reasons == null)
                {
                    var list = Stickering.Reason.GetByAccountId(_id);
                    _reasons = list;
                }
                return _reasons;
            }
            set
            {
                _reasons = value;
                AppServices.Cache.InvalidateCacheItem("account_" + _id + "_reasons");
            }
        }

        public string TagsSummary { get; set; }

        public void AddTag(int tagId, int userId)
        {
            cacheInvalidate("_tags");

            SqlHelper.ExecuteNonQuery(Core.ConnectionString,
                "AccountTagLinksInsert",
                new SqlParameter("@AccountId", _id),
                new SqlParameter("@AccountTagId", tagId),
                new SqlParameter("@OwnerUserId", userId));

            Tags = null;
        }

        public void DeleteTag(int tagId)
        {
            cacheInvalidate("_tags");

            SqlHelper.ExecuteNonQuery(Core.ConnectionString,
                "AccountTagLinksDelete",
                new SqlParameter("@AccountId", _id),
                new SqlParameter("@AccountTagId", tagId));

            Tags = null;
        }

        public void AddReason(int reasonId, int userId, int startFromType)
        {
            SqlHelper.ExecuteNonQuery(Core.ConnectionString,
                "AccountStickerReasonLinksInsert",
                new SqlParameter("@AccountId", _id),
                new SqlParameter("@StickerReasonId", reasonId),
                new SqlParameter("@UserId", userId),
                new SqlParameter("@StartFromType", startFromType));

            Reasons = null;
        }

        public int GetReasonStartType(int reasonId)
        {
            var ret = SqlMapper.Query<int?>(
                "SELECT StartFromType from AccountStickerReasonLinks WHERE AccountId=@AccountId and StickerReasonId=@StickerReasonId",
                new { AccountId = _id, StickerReasonId = reasonId }).FirstOrDefault();

            if (ret == null)
                return 0;
            else
                return ret.Value;
        }

        public void DeleteReason(int reasonId)
        {
            SqlHelper.ExecuteNonQuery(Core.ConnectionString,
                "AccountStickerReasonLinksDelete",
                new SqlParameter("@AccountId", _id),
                new SqlParameter("@StickerReasonId", reasonId));

            Reasons = null;
        }

        public RateItem StorageRate
        {
            get
            {
                return RateItem.GetPredefinedItemByAccount(this.Id, PredefinedRateItem.BUILTIN_IMPOUND_DAILYRATE);
            }
        }

        public decimal? CreditLimitUtilized { get; set; }
        public bool CreditHold { get; set; }
        public Account()
        {
            _id = -1;
            Billable = BillableType.Default;
        }

        public string FormatAddress()
        {
            string addr = "";

            if (!string.IsNullOrWhiteSpace(Address))
                addr += Address;

            if (!string.IsNullOrWhiteSpace(City))
                addr += " " + City;

            if (!string.IsNullOrWhiteSpace(State))
            {
                if (!string.IsNullOrWhiteSpace(City))
                    addr += ", " + State;
                else
                    addr += " " + State;
            }

            if (!string.IsNullOrWhiteSpace(Zip))
                addr += " " + Zip;

            return addr.Trim();
        }

        [Obsolete("Use GetById instead")]
        public Account(int id)
        {
            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "AccountsGetById", new SqlParameter("@AccountId", id)))
            {
                Billable = BillableType.Default;

                if (dr.Read())
                {
                    InitializeFromDataReader(dr);
                }
                else
                {
                    _id = 0;
                    throw new TowbookException("Account doesn't exist!");
                }
            }
        }

        private static Account GetById(int id, SqlDataReader reader)
        {
            return AppServices.Cache.Get("account_" + id, TimeSpan.FromMinutes(30), () =>
            {
                return new Account(reader);
            });
        }

        public static Account GetByIdWithoutCache(int id)
        {
            using (var dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "AccountsGetById", new SqlParameter("@AccountId", id)))
            {
                return dr.Read() ? new Account(dr) : null;
            }
        }

        public static async Task<Account> GetByIdWithoutCacheAsync(int id)
        {
            using (var dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString, System.Data.CommandType.StoredProcedure,
                "AccountsGetById", new SqlParameter("@AccountId", id)))
            {
                return await dr.ReadAsync() ? new Account(dr) : null;
            }
        }

        public static Account GetById(int id)
        {
            if (id == 0)
                return null;

            return AppServices.Cache.Get("account_" + id, TimeSpan.FromMinutes(30), () =>
            {
                var ret = Cache.Instance.Get(id, (int accountId) => GetByIdWithoutCache(id));

                if (ret != null && ret.Companies == null)
                {
                    var td = GetByIdWithoutCache(id);
                    if (td.Companies != null)
                    {
                        Cache.Instance.PartitionSet(td);
                        Cache.Instance.Set(td);
                    }

                    return td;
                }
                return ret;
            });
        }
        public static async Task<Account> GetByIdAsync(int id)
        {
            if (id == 0)
                return null;

            return await AppServices.Cache.GetAsync("account_" + id, TimeSpan.FromMinutes(30), async () =>
            {
                var ret = await Cache.Instance.GetAsync<Account>(id, async(int accountId) => await GetByIdWithoutCacheAsync(id));

                if (ret != null && ret.Companies == null)
                {
                    var td = await GetByIdWithoutCacheAsync(id);
                    if (td.Companies != null)
                    {
                        await Cache.Instance.PartitionSetAsync(td);
                        await Cache.Instance.SetAsync(td);
                    }

                    return td;
                }
                return ret;
            });
        }

        protected Account(SqlDataReader reader)
        {
            Billable = BillableType.Default;

            InitializeFromDataReader(reader);
        }

        public override string ToString()
        {
            return Company;
        }

        /// <summary>
        /// Get a list of all accounts for this company from Redis. 
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="showInactive"></param>
        /// <param name="showDeleted"></param>
        /// <remarks>
        /// Because of how expensive this method is, we should use an L1 cache that is looked at before Redis.
        /// For this to work though we must have platform-wide cache invalidation either using redis pub/sub or 
        /// service bus topics.</remarks>
        /// <returns>Account Collection</returns>
        public static async Task<Collection<Account>> GetByCompany(int companyId, bool showInactive, bool showDeleted = false)
        {
            var accounts = new Collection<Account>();

            // every company should always have accountId 1 in their list. 
            var defaultAccount = GetById(1);
            if (defaultAccount != null)
                accounts.Add(defaultAccount);


            // Get all the Accounts owned by this CompanyId (If this isn't a multi-company setup, this is the only 
            // data. 
            // We cache inactive accounts as well. They are filtered at the end of this function
            Collection<Account> cachedAccounts = null;
            try
            {
                cachedAccounts = (await Cache.Instance.PartitionGetAllAsync<Account>(companyId, async (int fetchId) =>
                {
                    var fromDatabase = await GetByExactCompanyId(Towbook.Company.Company.GetById(fetchId), true);
                    return fromDatabase;
                })).ToCollection();
            }
            catch (ProtoException)
            {
                cachedAccounts = await GetByExactCompanyId(Towbook.Company.Company.GetById(companyId), true);

                if (cachedAccounts != null)
                    Cache.Instance.PartitionSetMultiple(cachedAccounts, false, companyId);
            }


            if (cachedAccounts != null)
                accounts = accounts.Union(cachedAccounts).ToCollection();


            // TODO: cachify this.
            foreach (var cs in SharedCompany.GetByCompanyId(companyId)
                .Where(f => f.SharedCompanyId == companyId))
            {
                if (cs != null && cs.ShareAllAccounts)
                {
                    var secondId = cs.CompanyId;

                    if (secondId > 0)
                    {
                        var spga = await Cache.Instance.PartitionGetAllAsync<Account>(secondId, async (int fetchId) =>
                        {
                            var fromDatabase = await GetByExactCompanyId(Towbook.Company.Company.GetById(fetchId), true);
                            return fromDatabase;
                        });

                        if (spga != null)
                            accounts = accounts.Union(spga).ToCollection();
                    }
                }
            }

            // TODO: cachify this. 
            var extra = SqlMapper.Query<int>("SELECT AccountId FROM CompanyAccounts WITH (nolock) WHERE CompanyId=@C",
                new { C = companyId }).ToArray();

            if (extra.Any())
            {
                var extras = new Collection<int>();

                foreach (var item in extra)
                {
                    if (!accounts.Any(o => o.Id == item))
                    {
                        extras.Add(item);
                    }
                }

                var results = Cache.Instance.Get<Account>(extras.ToArray());

                // add the shared accounts that existed in cache.
                accounts = accounts.Union(results).ToCollection();

                // add the ones that aren't cached yet.
                var missing = extras.Where(e => !results.Any(r => r != null && r.Id == e));
                foreach (var x in missing)
                {
                    var a = GetById(x);

                    if (a != null)
                        accounts.Add(a);
                }
            }


            if (showDeleted)
            {
                var deletedAccs = await GetDeletedByCompanyAsync(companyId.ToString());
                accounts = accounts.Union(deletedAccs).ToCollection();
            }
            else
            {
                accounts = accounts.Where(o => !o.Deleted).ToCollection();
            }

            // Show inactive filter must follow showDeleted filter b/c the deleted accounts
            // may include inactive accounts 
            if (!showInactive)
                accounts = accounts.Where(o => o.Status == AccountStatus.Active).ToCollection();

            accounts = accounts.OrderBy(o => o.Company).ToCollection();

            return accounts;
        }

        public static Collection<Account> GetByCompany(Company.Company company)
        {
            return GetByCompany(company, null);
        }

        public static async Task<Collection<Account>> GetByCompanyAsync(Company.Company company)
        {
            return await GetByCompanyAsync(company, null);
        }

        public static Collection<Account> GetByCompany(Company.Company company, Nullable<AccountType> at)
        {
            return GetByCompany(company, false, at, false);
        }

        public static async Task<Collection<Account>> GetByCompanyAsync(Company.Company company, Nullable<AccountType> at)
        {
            return await GetByCompanyAsync(company, false, at, false);
        }

        public static int GetByCompanyCount(Company.Company company, Nullable<AccountType> at)
        {
            return Convert.ToInt32(SqlHelper.ExecuteScalar(Core.ConnectionString,
                "AccountsGetByCompanyIdGetCount",
                    new SqlParameter("@CompanyId", company.Id),
                    new SqlParameter("@TypeId", at)));
        }

        public static async Task<Collection<Account>> GetByExactCompanyId(Company.Company company, bool showInactive)
        {
            if (company == null)
                throw new ArgumentException("Company passed in is null", "company");

            var list = new Collection<Account>();
            using (var dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                System.Data.CommandType.Text,
                "SELECT * FROM vwAccounts WITH (nolock) WHERE CompanyId=@CompanyId" + (showInactive ? "" : " AND Status=1"),
                    new SqlParameter("@CompanyId", company.Id)))
            {
                while (await dr.ReadAsync())
                {
                    list.Add(Account.GetById(dr.GetValue<int>("AccountId"), dr));
                }
            }

            return list;
        }

        public static async Task<Collection<Account>> GetByIdsAsync(int[] accountIds)
        {
            var list = new Collection<Account>();

            foreach (var batch in accountIds.OrderBy(o => o).Batch(250))
            {
                var parameters = new List<string>();
                var p = new List<SqlParameter>();
                int i = 0;
                foreach (var row in batch)
                {
                    i++;
                    parameters.Add("@P" + i);
                    p.Add(new SqlParameter("@P" + i, row));
                }

                using (var dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                    System.Data.CommandType.Text,
                    $"SELECT * FROM vwAccounts WITH (nolock) WHERE AccountId IN({string.Join(",", parameters)})",
                        p.ToArray()))
                {
                    while (await dr.ReadAsync())
                    {
                        list.Add(GetById(dr.GetValue<int>("AccountId"), dr));
                    }
                }
            }

            return list;
        }

        public static Collection<Account> GetByCompany(Company.Company company, bool showInactive, AccountType? at, bool? showDeleted = false)
        {
            int? accountTypeId = null;

            if (at != null)
                accountTypeId = Convert.ToInt32(at);

            if (company == null)
                throw new ArgumentException("Company passed in is null", "company");

            var accounts = AppServices.Cache.Get(
                "account_" + company.Id + "_" + showInactive.ToString() + (at?.ToString() ?? "all"),
                TimeSpan.FromMinutes(30), () =>
                {
                    var list = new Collection<Account>();
                    using (var dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                        "AccountsGetByCompanyId",
                            new SqlParameter("@CompanyId", company.Id),
                            new SqlParameter("@ReturnInactive", showInactive),
                            new SqlParameter("@TypeId", accountTypeId),
                            new SqlParameter("@Start", (int?)null),
                            new SqlParameter("@End", (int?)null)))
                    {
                        while (dr.Read())
                        {
                            list.Add(GetById(dr.GetValue<int>("AccountId"), dr));
                        }
                    }

                    return new CacheCollection<Account>(list.OrderBy(o => o.Company));
                }).Items;

            if (showDeleted == true)
            {
                var deletedAccs = GetDeletedByCompany(company)
                    .OrderBy(o => o.Company).ToCollection();

                if (!showInactive)
                    deletedAccs = deletedAccs.Where(o => o.Status == AccountStatus.Active).ToCollection();

                accounts = accounts.Union(deletedAccs).ToCollection();
            }
            return accounts;

        }
        public static async Task<Collection<Account>> GetByCompanyAsync(Company.Company company, bool showInactive, AccountType? at, bool? showDeleted = false)
        {
            int? accountTypeId = null;

            if (at != null)
                accountTypeId = Convert.ToInt32(at);

            if (company == null)
                throw new ArgumentException("Company passed in is null", "company");

            var accounts = (await AppServices.Cache.GetAsync(
                "account_" + company.Id + "_" + showInactive.ToString() + (at?.ToString() ?? "all"),
                TimeSpan.FromMinutes(30), async() =>
                {
                    var list = new Collection<Account>();
                    using (var dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                        System.Data.CommandType.StoredProcedure,
                        "AccountsGetByCompanyId",
                        new SqlParameter("@CompanyId", company.Id),
                        new SqlParameter("@ReturnInactive", showInactive),
                        new SqlParameter("@TypeId", accountTypeId),
                        new SqlParameter("@Start", (int?)null),
                        new SqlParameter("@End", (int?)null)))
                    {
                        while (await dr.ReadAsync())
                        {
                            list.Add(GetById(dr.GetValue<int>("AccountId"), dr));
                        }
                    }

                    return new CacheCollection<Account>(list.OrderBy(o => o.Company));
                })).Items;

            if (showDeleted == true)
            {
                var deletedAccs = (await GetDeletedByCompanyAsync(company.ToString()))
                    .OrderBy(o => o.Company).ToCollection();

                if (!showInactive)
                    deletedAccs = deletedAccs.Where(o => o.Status == AccountStatus.Active).ToCollection();

                accounts = accounts.Union(deletedAccs).ToCollection();
            }
            return accounts;

        }

        public static Collection<Account> GetByCompany(Company.Company[] company, bool showInactive, Nullable<AccountType> at, bool showDeleted = false)
        {
            Nullable<int> accountTypeId = null;

            if (at != null)
                accountTypeId = Convert.ToInt32(at);


            Stopwatch sw = Stopwatch.StartNew();

            var list = new Collection<Account>();
            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "AccountsGetByCompanyId",
                    new SqlParameter("@CompanyId", string.Join(",", company.Select(o => o.Id))),
                    new SqlParameter("@ReturnInactive", showInactive),
                    new SqlParameter("@TypeId", accountTypeId)))
            {
                while (dr.Read())
                {
                    list.Add(Account.GetById(dr.GetValue<int>("AccountId"), dr));
                }
            }

            Console.WriteLine("AccountsGetByCompanyId elapsed ---- " + sw.ElapsedMilliseconds);

            if (showDeleted)
            {
                var deletedAccs = GetDeletedByCompany(company);

                if (!showInactive)
                    deletedAccs = deletedAccs.Where(o => o.Status == AccountStatus.Active).ToCollection();

                list = list.Union(deletedAccs).ToCollection();
            }

            return list;
        }

        public static async Task<Collection<Account>> GetByCompanyAsync(Company.Company[] company, bool showInactive, Nullable<AccountType> at, bool showDeleted = false)
        {
            Nullable<int> accountTypeId = null;

            if (at != null)
                accountTypeId = Convert.ToInt32(at);


            Stopwatch sw = Stopwatch.StartNew();

            var list = new Collection<Account>();
            using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString, System.Data.CommandType.StoredProcedure,
                "AccountsGetByCompanyId",
                new SqlParameter("@CompanyId", string.Join(",", company.Select(o => o.Id))),
                new SqlParameter("@ReturnInactive", showInactive),
                new SqlParameter("@TypeId", accountTypeId)))
            {
                while (await dr.ReadAsync())
                {
                    list.Add(Account.GetById(dr.GetValue<int>("AccountId"), dr));
                }
            }

            Console.WriteLine("AccountsGetByCompanyId elapsed ---- " + sw.ElapsedMilliseconds);

            if (showDeleted)
            {
                var deletedAccs = await GetDeletedByCompanyAsync(company);

                if (!showInactive)
                    deletedAccs = deletedAccs.Where(o => o.Status == AccountStatus.Active).ToCollection();

                list = list.Union(deletedAccs).ToCollection();
            }

            return list;
        }

        private static Collection<Account> GetDeletedByCompany(Company.Company[] company)
        {
            return GetDeletedByCompany(string.Join(",", company.Select(o => o.Id)));
        }

        private static Collection<Account> GetDeletedByCompany(Company.Company company)
        {
            return GetDeletedByCompany(company.Id.ToString());
        }

        private static Collection<Account> GetDeletedByCompany(string companyIdString)
        {
            var list = new Collection<Account>();
            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "AccountsGetDeletedByCompanyId",
                new SqlParameter("@CompanyId", companyIdString)))
            {
                while (dr.Read())
                {
                    list.Add(Account.GetById(dr.GetValue<int>("AccountId"), dr));
                }
            }
            return list;
        }
        
        private static async Task<Collection<Account>> GetDeletedByCompanyAsync(Company.Company[] company)
        {
            return await GetDeletedByCompanyAsync(string.Join(",", company.Select(o => o.Id)));
        }

        private static async Task<Collection<Account>> GetDeletedByCompanyAsync(string companyIdString)
        {
            var list = new Collection<Account>();
            using (var dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString, System.Data.CommandType.StoredProcedure,
                "AccountsGetDeletedByCompanyId",
                new SqlParameter("@CompanyId", companyIdString)))
            {
                while (await dr.ReadAsync())
                {
                    list.Add(Account.GetById(dr.GetValue<int>("AccountId"), dr));
                }
            }
            return list;
        }

        public static async Task<Collection<Account>> Search(Company.Company company, string name)
        {
            Collection<Account> list = new Collection<Account>();

            using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                "AccountsGetByName",
                new SqlParameter("@CompanyId", company.Id),
                new SqlParameter("@Name", name)))
            {
                while (await dr.ReadAsync())
                {
                    list.Add(new Account(dr));
                }
            }

            return list;
        }

        public static async Task<Collection<Account>> GetByTagLink(Company.Company company, int tagId, bool includeDisabled = false)
        {
            Collection<Account> list = new Collection<Account>();

            using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                "AccountsGetTagLink",
                new SqlParameter("@CompanyId", company.Id),
                new SqlParameter("@TagId", tagId),
                new SqlParameter("@IncludeOnlyEnabled", !includeDisabled)))
            {
                while (await dr.ReadAsync())
                {
                    list.Add(new Account(dr));
                }
            }

            return list;
        }

        public static async Task<IEnumerable<Account>> GetByDefaultStorageRateItemIdAsync(Company.Company[] companies, int defaultStorageRateItemId)
        {
            return (await GetByCompanyAsync(companies, true, null, true))
                .Where(w => w.DefaultStorageRateItemId == defaultStorageRateItemId);
        }

        private void InitializeFromDataReader(SqlDataReader reader)
        {
            _id = Convert.ToInt32(reader["AccountId"]);
            _companyId = Convert.ToInt32(reader["CompanyId"]);
            _referenceNumber = reader.GetValue<string>("ReferenceNumber");
            _company = reader.GetValue<string>("Company");
            _fullName = reader.GetValue<string>("FullName");
            _address = reader.GetValue<string>("Address");
            _city = reader.GetValue<string>("City");
            _state = reader.GetValue<string>("State");
            _zip = reader.GetValue<string>("Zip");

            Latitude = reader.GetValueOrNull<decimal>("Latitude");
            Longitude = reader.GetValueOrNull<decimal>("Longitude");

            _country = reader.GetValue<string>("Country");
            _email = reader.GetValue<string>("Email");

            if (!string.IsNullOrWhiteSpace(_email))
                _email = _email.Replace(",", ";").Replace(" ", "");

            _website = reader.GetValue<string>("Website");
            _notes = reader.GetValue<string>("Notes");
            _phone = reader.GetValue<string>("Phone");
            _fax = reader.GetValue<string>("Fax");
            _type = (AccountType)reader.GetValue<int>("TypeId");
            _invoiceTerms = (InvoiceTerm)reader.GetValue<int>("InvoiceTermId");
            _creditLimit = reader.GetValueOrNull<decimal>("CreditLimit");
            CreditLimitUtilized = reader.GetValueOrNull<decimal>("CreditLimitUtilized");
            _creditBalance = reader.GetValue<decimal>("CreditBalance");
            CreditHold = reader.GetValue<bool>("CreditHold");

            _invoiceDeliveryByFax = reader.GetValue<bool>("InvoiceDeliveryFax");
            _invoiceDeliveryByMail = reader.GetValue<bool>("InvoiceDeliveryMail");
            _invoiceDeliveryByEmail = reader.GetValue<bool>("InvoiceDeliveryEmail");

            _statementEndDate = reader.GetValue<DateTime>("StatementEndDate");
            if (_statementEndDate == DateTime.MinValue)
                _statementEndDate = null;

            _latestActivity = reader.GetValue<DateTime>("LastActivity");
            if (_latestActivity == DateTime.MinValue)
                _latestActivity = null;

            _discountRate = reader.GetValue<decimal>("DiscountRate");
            _deleted = reader.GetValue<bool>("Deleted");
            _createDate = reader.GetValue<DateTime>("CreateDate");
            _taxExempt = reader.GetValue<bool>("TaxExempt");

            ParentAccountId = reader.GetValue<int>("ParentAccountId");

            Billable = (BillableType)reader.GetValue<int>("Billable");
            ImpoundDestinationType = (AccountImpoundDestination)reader.GetValue<int>("ImpoundDestinationTypeId");
            ImpoundDestinationStorageLotId = reader.GetValueOrNull<int>("ImpoundDestinationStorageLotId");

            TagsSummary = reader.GetValue<string>("TagsSummary");

            // if its null, or, if it contains a comma, then use it, otherwise, ignore it. (database backwards compatibility to a older version of TB database)
            if (TagsSummary != null && TagsSummary.Contains(",") || TagsSummary == null)
            {
                Collection<int> tags = new Collection<int>();

                if (TagsSummary != null)
                {
                    foreach (var x in TagsSummary.Split(','))
                    {
                        int number;

                        if (!int.TryParse(x, out number))
                        {
                            break;
                        }

                        if (number > 0)
                        {
                            tags.Add(number);
                        }
                    }
                }

                _tags = new Collection<AccountTag>();

                foreach (int t in tags)
                {
                    var tag = Extric.Towbook.Accounts.AccountTag.GetById(t);
                    if (tag != null)
                        _tags.Add(tag);
                }
            }
            else
                _tags = null;

            Status = (AccountStatus)reader.GetValue<int>("Status");


            if (reader["Balance"] != DBNull.Value)
            {
                var balance = reader.GetValue<decimal>("Balance");
                if (balance != -1)
                    AppServices.Cache.Add("account_" + this.CompanyId + "_" + Id + ".Balance", DateTime.Now.AddMinutes(60), new DecimalContainer(balance));
            }

            try
            {
                if (reader["StatementPaymentsCount"] != DBNull.Value)
                {
                    _statementPaymentCount = reader.GetValue<int>("StatementPaymentsCount");
                }
                else
                    _statementPaymentCount = -1;
            }
            catch
            {
                _statementPaymentCount = -1;
            }

            this.Companies = reader["Companies"].ToString().Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries)
                .Select(o => Convert.ToInt32(o))
                .Union(new[] { this.CompanyId }).ToArray();

            MasterAccountId = reader.GetValue<int>("MasterAccountId");
            DefaultPriority = reader.GetValue<int>("DefaultPriority");
            DefaultPO = reader.GetValue<string>("DefaultPO");
            DefaultStorageRateItemId = reader.GetValue<int>("DefaultStorageRateItemId");
        }

        public async Task Save() => await Save(null);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="callback">callback will be invoked before the final cache refresh calls are made.</param>
        public async Task Save(Func<Task> callback)
        {
            if (_id == 0)
            {
                throw new TowbookException("No such Account. Can't save " +
                    "object! (this object should have already been discarded!)");
            }

            try
            {
                if (_id == -1)
                {
                    await DbInsertAsync();
                    //PushNotificationProvider.Push(_companyId, "accounts_update", new { accountId = _id, type = "add" });
                }
                else
                {
                    await DbUpdateAsync();
                    //PushNotificationProvider.Push(_companyId, "accounts_update", new { accountId = _id, type = "update" });
                }
            }
            finally
            {

                var tempAccount = GetByIdWithoutCache(Id);

                if (tempAccount != null)
                {
                    this.CreateDate = tempAccount.CreateDate;
                    this.Companies = tempAccount.Companies;
                    this.MasterAccountId = tempAccount.MasterAccountId;
                    this.Status = tempAccount.Status;
                    this.Company = tempAccount.Company;
                    this.CompanyId = tempAccount.CompanyId;
                }
                InvalidateInProcCaches();
            }

            // Callback should run before redis is updated in case the callback further modifies this object
            if (callback != null)
                await callback();

            await InvalidateAndUpdateRedis();
        }

        private async Task DbInsertAsync()
        {
            if (_companyId == 0)
                throw new ArgumentException("CompanyId is 0; set to a valid company id and try again.");

            using var conn = Core.GetConnection();
            _id = Convert.ToInt32(await SqlHelper.ExecuteScalarAsync(conn, System.Data.CommandType.StoredProcedure,
                "AccountsInsert",
                    new SqlParameter("@CompanyId", _companyId),
                    new SqlParameter("@ReferenceNumber", _referenceNumber),
                    new SqlParameter("@ParentAccountId", (ParentAccountId > 0 ? (int?)ParentAccountId : null)),
                    new SqlParameter("@Company", _company),
                    new SqlParameter("@FullName", _fullName),
                    new SqlParameter("@Address", _address),
                    new SqlParameter("@City", _city),
                    new SqlParameter("@State", _state),
                    new SqlParameter("@Zip", _zip),
                    new SqlParameter("@Country", _country),
                    new SqlParameter("@Email", _email),
                    new SqlParameter("@Website", _website),
                    new SqlParameter("@Notes", _notes),
                    new SqlParameter("@TypeId", _type),
                    new SqlParameter("@InvoiceTermId", _invoiceTerms),
                    new SqlParameter("@CreditLimit", _creditLimit),
                    new SqlParameter("@InvoiceDeliveryFax", _invoiceDeliveryByFax),
                    new SqlParameter("@InvoiceDeliveryMail", _invoiceDeliveryByMail),
                    new SqlParameter("@InvoiceDeliveryEmail", _invoiceDeliveryByEmail),
                    new SqlParameter("@StatementEndDate", _statementEndDate),
                    new SqlParameter("@DiscountRate", _discountRate),
                    new SqlParameter("@Phone", _phone),
                    new SqlParameter("@Fax", _fax),
                    new SqlParameter("@TaxExempt", _taxExempt),
                    new SqlParameter("@Billable", Billable),
                    new SqlParameter("@ImpoundDestinationTypeId", ImpoundDestinationType),
                    new SqlParameter("@ImpoundDestinationStorageLotId", (ImpoundDestinationStorageLotId != 0 ? ImpoundDestinationStorageLotId : null)),
                    new SqlParameter("@Status", Status),
                    new SqlParameter("@MasterAccountId", (MasterAccountId > 0 ? (int?)MasterAccountId : null)),
                    new SqlParameter("@DefaultPriority", DefaultPriority),
                    new SqlParameter("@DefaultPO", DefaultPO),
                    new SqlParameter("@DefaultStorageRateItemId", DefaultStorageRateItemId),
                    new SqlParameter("@Latitude", Latitude),
                    new SqlParameter("@Longitude", Longitude)
                    ), System.Globalization.CultureInfo.InvariantCulture);
        }

        private async Task DbUpdateAsync()
        {
            using var conn = Core.GetConnection();
            await SqlHelper.ExecuteNonQueryAsync(conn, System.Data.CommandType.StoredProcedure,
                "AccountsUpdateById",
                    new SqlParameter("@AccountId", _id),
                    new SqlParameter("@CompanyId", _companyId),
                    new SqlParameter("@ReferenceNumber", _referenceNumber),
                    new SqlParameter("@ParentAccountId", (ParentAccountId > 0 ? (int?)ParentAccountId : null)),
                    new SqlParameter("@Company", _company),
                    new SqlParameter("@FullName", _fullName),
                    new SqlParameter("@Address", _address),
                    new SqlParameter("@City", _city),
                    new SqlParameter("@State", _state),
                    new SqlParameter("@Zip", _zip),
                    new SqlParameter("@Country", _country),
                    new SqlParameter("@Email", _email),
                    new SqlParameter("@Website", _website),
                    new SqlParameter("@Notes", _notes),
                    new SqlParameter("@TypeId", _type),
                    new SqlParameter("@InvoiceTermId", _invoiceTerms),
                    new SqlParameter("@CreditLimit", _creditLimit),
                    new SqlParameter("@InvoiceDeliveryFax", _invoiceDeliveryByFax),
                    new SqlParameter("@InvoiceDeliveryMail", _invoiceDeliveryByMail),
                    new SqlParameter("@InvoiceDeliveryEmail", _invoiceDeliveryByEmail),
                    new SqlParameter("@StatementEndDate", _statementEndDate),
                    new SqlParameter("@DiscountRate", _discountRate),
                    new SqlParameter("@Phone", _phone),
                    new SqlParameter("@Fax", _fax),
                    new SqlParameter("@TaxExempt", _taxExempt),
                    new SqlParameter("@Billable", Billable),
                    new SqlParameter("@ImpoundDestinationTypeId", ImpoundDestinationType),
                    new SqlParameter("@ImpoundDestinationStorageLotId", ImpoundDestinationStorageLotId),
                    new SqlParameter("@Status", Status),
                    new SqlParameter("@MasterAccountId", (MasterAccountId > 0 ? (int?)MasterAccountId : null)),
                    new SqlParameter("@DefaultPriority", DefaultPriority),
                    new SqlParameter("@DefaultPO", DefaultPO),
                    new SqlParameter("@DefaultStorageRateItemId", DefaultStorageRateItemId),
                    new SqlParameter("@Latitude", Latitude),
                    new SqlParameter("@Longitude", Longitude),
                    new SqlParameter("@CreditHold", CreditHold));

            cacheInvalidate();
        }

        public async Task Delete(User performedBy)
        {
            try
            {
                await SqlMapper.ExecuteSpAsync("AccountsDeleteById", new { @AccountId = _id });
                _deleted = true;
            }
            finally
            {
                CacheClearById(_id);

                Cache.Instance.PartitionDelete<Account>(CompanyId, _id);
                Cache.Instance.PartitionSet(this);
                Cache.Instance.Set(this);

                await Caching.CacheWorkerUtility.DeleteAccount(this);
                await PushNotificationProvider.Push(_id, "accounts_update", new { accountId = _id, type = "delete" });
            }
        }

        public async Task Undelete(User performedBy)
        {
            try
            {
                await SqlMapper.ExecuteSpAsync("AccountsUndeleteById", new { @AccountId = _id });
                _deleted = false;
            }
            finally
            {
                CacheClearById(_id);
                await Cache.Instance.PartitionSetAsync<Account>(CompanyId, _id, this);

                InvalidateInProcCaches();
                await InvalidateAndUpdateRedis();
                await Caching.CacheWorkerUtility.UpdateAccount(this);
                await PushNotificationProvider.Push(_id, "accounts_update", new { accountId = _id, type = "update" });
            }
        }

        public async Task InvalidateAndUpdateRedis()
        {
            await Cache.Instance.PartitionSetAsync(this);
            await Cache.Instance.SetAsync(this);

            await Caching.CacheWorkerUtility.UpdateAccount(this);

            if (Companies == null || Companies.Length == 0)
                Companies = new int[] { CompanyId };

            var key = "accounts:" + string.Join(",",
                Companies.OrderBy(ro => ro));

            await Core.DeleteRedisKeyAsync(key);
        }

        private void InvalidateInProcCaches()
        {
            cacheInvalidate();
            cacheInvalidate("_tags");
            SetCacheResetById(_companyId, false);
            CacheClearById(_id);
        }

        private void cacheInvalidate(string postfix = "")
        {
            AppServices.Cache.InvalidateCacheItem($"account_{Id}");
            foreach (var x in Enum.GetNames(typeof(AccountType)))
            {
                AppServices.Cache.InvalidateCacheItem($"account_{CompanyId}_True{x}");
                AppServices.Cache.InvalidateCacheItem($"account_{CompanyId}_False{x}");
            }
            AppServices.Cache.InvalidateCacheItem($"account_{CompanyId}_Trueall");
        }

        public async Task UpdateLocation()
        {
            string formattedAddress = GeocodeHelper.FormatAddressForGeocoding(this.FormatAddress());
            if (formattedAddress == "")
            {
                Latitude = null;
                Longitude = null;
                return;
            }

            try
            {
                var location = await GeocodeHelper.Geocode(formattedAddress);
                Latitude = location.Latitude;
                Longitude = location.Longitude;
            } catch
            {
                Latitude = null;
                Longitude = null;
            }
        }

        public static Account GetById(int id, int companyId)
        {
            var ret = GetById(id);

            if (ret.CompanyId != companyId)
                throw new TowbookException("Trying to access another company's account... access denied!");

            return ret;
        }

        public static IEnumerable<Account> GetByParentId(int parentAccountId)
        {
            throw new NotImplementedException();
        }

        public static IEnumerable<Account> GetByParentId(int parentAccountId, string referenceNumber)
        {
            throw new NotImplementedException();
        }

        public class CategoryInvoice
        {
            public int DispatchEntryId { get; private set; }
            public Extric.Towbook.Invoice Invoice { get; set; }
            public IEnumerable<RateItemCategory> IncludeCategories { get; set; }

            public decimal TowingSubtotal
            {
                get
                {
                    if (Invoice == null)
                        return 0.0M;

                    if (IncludeCategories.Select(s => s.Id).Contains(1))
                        return Invoice.InvoiceItems.Where(w => w.RateItem == null || w.RateItem.CategoryId == null ||
                                                    w.RateItem.CategoryId == 1).Sum(s => s.Total);
                    else
                        return 0.0M;
                }
                private set { }
            }

            public decimal StorageSubtotal
            {
                get
                {
                    if (Invoice == null)
                        return 0.0M;

                    if (IncludeCategories.Select(s => s.Id).Contains(2) || IncludeCategories.Select(s => s.Id).Contains(5))
                        return Invoice.InvoiceItems.Where(y => y.RateItem != null &&
                                                    ((y.RateItem.Predefined != null &&
                                                    y.RateItem.Predefined.Id == Extric.Towbook.PredefinedRateItem.BUILTIN_IMPOUND_DAILYRATE) ||
                                                    y.RateItem.CategoryId == 2 || y.RateItem.CategoryId == 5)).Sum(s => s.Total);
                    else
                        return 0.0M;
                }
                private set { }
            }

            public decimal NotificationFeesSubtotal
            {
                get
                {
                    if (Invoice == null)
                        return 0.0M;

                    if (IncludeCategories.Select(s => s.Id).Contains(3))
                        return Invoice.InvoiceItems.Where(w => w.RateItem != null && w.RateItem.CategoryId == 3).Sum(s => s.Total);
                    else
                        return 0.0M;
                }
                private set { }
            }

            public decimal AdminFeesSubtotal
            {
                get
                {
                    if (Invoice == null)
                        return 0.0M;

                    if (IncludeCategories.Select(s => s.Id).Contains(4))
                        return Invoice.InvoiceItems.Where(w => w.RateItem != null && w.RateItem.CategoryId == 4).Sum(s => s.Total);
                    else
                        return 0.0M;
                }
                private set { }
            }

            public decimal RecoverySubtotal
            {
                get
                {
                    if (Invoice == null)
                        return 0.0M;

                    if (IncludeCategories.Select(s => s.Id).Contains(16))
                        return Invoice.InvoiceItems.Where(w => w.RateItem != null && w.RateItem.CategoryId == 16).Sum(s => s.Total);
                    else
                        return 0.0M;
                }
                private set { }
            }

            public decimal PartsSubtotal
            {
                get
                {
                    if (Invoice == null)
                        return 0.0M;

                    if (IncludeCategories.Select(s => s.Id).Contains(15))
                        return Invoice.InvoiceItems.Where(w => w.RateItem != null && w.RateItem.CategoryId == 15).Sum(s => s.Total);
                    else
                        return 0.0M;
                }
                private set { }
            }

            public decimal ReleaseFeeSubtotal
            {
                get
                {
                    if (Invoice == null)
                        return 0.0M;

                    if (IncludeCategories.Select(s => s.Id).Contains(17))
                        return Invoice.InvoiceItems.Where(w => w.RateItem != null && w.RateItem.CategoryId == 17).Sum(s => s.Total);
                    else
                        return 0.0M;
                }
                private set { }
            }

            public decimal SubTotal
            {
                get { return TowingSubtotal + StorageSubtotal + NotificationFeesSubtotal + AdminFeesSubtotal + PartsSubtotal + RecoverySubtotal; }
            }

            public decimal Tax
            {
                get
                {
                    if (Invoice == null)
                        return 0.0M;

                    return Invoice.Tax;
                }
                private set { }
            }

            public decimal Total
            {
                get { return SubTotal + Tax; }
                private set { }
            }

            public decimal BalanceDue
            {
                get
                {
                    if (Invoice == null)
                        return 0.0M;

                    var balanceDue = Total - PaymentsTotal;

                    if (balanceDue < 0)
                        balanceDue = 0.0M;

                    return balanceDue;
                }
                private set { }
            }

            public decimal PaymentsTotal { get; private set; }

            public CategoryInvoice(Invoice invoice, IEnumerable<RateItemCategory> categories, decimal? paymentsTotal = null)
            {
                Invoice = invoice;
                IncludeCategories = categories;
                DispatchEntryId = invoice.DispatchEntryId;
                PaymentsTotal = paymentsTotal != null ? paymentsTotal.Value : invoice != null ? invoice.PaymentsTotal : 0.0M;
            }

        }
    }

}
