using Extric.Towbook.Utility;
using Glav.CacheAdapter.Core.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.Accounts
{
    [Table("AccountPublicRequestLinks")]
    public class AccountPublicRequestLink
    {
        private const int CacheTimeout = 120;

        [Key("AccountPublicRequestLinkId")]
        public int Id { get; private set; }
        public int AccountId { get; set; }
        public int CompanyId { get; set; }
        public DateTime CreateDate { get; set; }
        public int OwnerUserId { get; set; }
        public bool Deleted { get; set; }
        public bool RequireAuthentication { get; set; }
        public string GUID { get; set; }
        public bool UseGUID { get; set; }
        [Key("AccountPublicRequestFormTemplateId")]
        public int? TemplateId { get; set; }

        // A list of Accounts that are available to be 
        // selected using the public link
        // Empty means use default account specified by AccountId
        public List<PublicRequestLinkAccount> Accounts
        {
            get
            {
                var accounts = new List<PublicRequestLinkAccount>();

                using (var dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                    "AccountPublicRequestLinkAccountsGetByAccountPublicRequestLinkId", 
                    new SqlParameter("@AccountPublicRequestLinkId", Id)))
                {
                    while (dr.Read())
                    {
                        var d = new PublicRequestLinkAccount();

                        d.Id = dr.GetValue<int>("AccountId");
                        d.Company = dr.GetValue<string>("Company");
                        d.Type = dr.GetValue<int>("TypeId");
                        d.ImpoundDestinationTypeId = dr.GetValueOrNull<int>("ImpoundDestinationTypeId");
                        d.ImpoundStorageLotId = dr.GetValueOrNull<int>("ImpoundDestinationStorageLotId");
                        d.LotFullAddress = dr.GetValue<string>("LotFullAddress");
                        d.FullAddress = dr.GetValue<string>("AccountFullAddress");

                        accounts.Add(d);
                    }
                }

                return accounts.OrderBy(o => o.Company).ToList();
            }
            private set { }
        }

        public class PublicRequestLinkAccount
        {
            public int Id { get; set; }
            public string Company { get; set; }
            public int Type { get; set; }
            public int? ImpoundDestinationTypeId { get; set; }
            public int? ImpoundStorageLotId { get; set; }
            public string LotFullAddress { get; set; }
            public string FullAddress { get; set; }
        }


        // A list of Accounts that are available to be 
        // selected using the public link for the billTo Field
        // Empty means no option for billTo
        public List<dynamic> BillToAccounts
        {
            get
            {
                List<dynamic> accounts = new List<dynamic>();
                dynamic acc = Array.Empty<object>();

                using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                    "AccountPublicRequestLinkBillToAccountsGetByAccountPublicRequestLinkId", new SqlParameter("@AccountPublicRequestLinkId", Id)))
                {
                    while (dr.Read())
                    {
                        dynamic d = new System.Dynamic.ExpandoObject();

                        d.Id = dr.GetValue<int>("AccountId");
                        d.Company = dr.GetValue<string>("Company");

                        accounts.Add(d);
                    }

                }

                return accounts.OrderBy(o => o.Company).ToList();
            }
            private set { }
        }

        public string Link 
        {
            get
            {
                if (UseGUID)
                    return "http://public.towbook.com/" + GUID;
                else
                    return "http://public.towbook.com/" + Utility.EncryptionHelper.ToHashId(Id);
            }
        }

        public AccountPublicRequestLink()
        {
            Id = -1;
            Deleted = false;
            GUID = Guid.NewGuid().ToString("N") + Guid.NewGuid().ToString("N");
        }

        protected AccountPublicRequestLink(IDataReader reader)
        {
            Id = reader.GetValue<int>("AccountPublicRequestLinkId");
            AccountId = reader.GetValue<int>("AccountId");
            CompanyId = reader.GetValue<int>("CompanyId");
            CreateDate = reader.GetValue<DateTime>("CreateDate");
            OwnerUserId = reader.GetValue<int>("OwnerUserId");
            Deleted = reader.GetValue<bool>("Deleted");
            RequireAuthentication = reader.GetValue<bool>("RequireAuthentication");
            GUID = reader.GetValue<string>("GUID");
            UseGUID = reader.GetValue<bool>("UseGuid");
            TemplateId = reader.GetValue<int>("AccountPublicRequestFormTemplateId");
        }

        public static async Task<AccountPublicRequestLink> GetByIdAsync(int id)
        {
            using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                "AccountPublicRequestLinkGetById", new SqlParameter("@Id", id)))
            {
                if (await dr.ReadAsync())
                {
                    return new AccountPublicRequestLink(dr);
                }
                else
                {
                    return null;
                }
            }
        }

        public static async Task<AccountPublicRequestLink> GetByGUID(string guid)
        {
            using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                "AccountPublicRequestLinkGetByGUID", new SqlParameter("@GUID", guid)))
            {
                if (await dr.ReadAsync())
                {
                    return new AccountPublicRequestLink(dr);
                }
                else
                {
                    return null;
                }
            }
        }

        public static async Task<AccountPublicRequestLink> GetByAccountIdAsync(int accountId, int companyId)
        {
            using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                "AccountPublicRequestLinkGetByAccountId", 
                    new SqlParameter("@AccountId", accountId),
                    new SqlParameter("CompanyId", companyId)))
            {
                if (await dr.ReadAsync())
                {
                    return new AccountPublicRequestLink(dr);
                }
                else
                {
                    return null;
                }
            }
        }

        public static IEnumerable<AccountPublicRequestLink> GetByAccountIds(int[] accountIds)
        {
            return SqlMapper.Query<AccountPublicRequestLink>(
                @"SELECT * FROM AccountPublicRequestLinks WITH (NOLOCK) WHERE AccountId in @Ids and Deleted=0",
                new { Ids = accountIds });
        }

        public static IEnumerable<AccountPublicRequestLink> GetByTemplateIds(int[] templateIds)
        {
            return SqlMapper.Query<AccountPublicRequestLink>(
                @"SELECT * FROM AccountPublicRequestLinks WITH (NOLOCK) WHERE AccountPublicRequestFormTemplateId in @Ids and Deleted=0",
                new { Ids = templateIds });
        }


        public void Save()
        {
            if (Id <= 0)
            {
                DbInsert();
            }
            else
            {
                DbUpdate();
            }
        }

        private void DbInsert()
        {
            Id = Convert.ToInt32(SqlHelper.ExecuteScalar(Core.ConnectionString,
                "AccountPublicRequestLinkInsert",
                    new SqlParameter("@AccountId", AccountId),
                    new SqlParameter("@CompanyId", CompanyId),
                    new SqlParameter("@OwnerUserId", OwnerUserId),
                    new SqlParameter("@GUID", GUID),
                    new SqlParameter("@RequireAuthentication", RequireAuthentication),
                    new SqlParameter("@UseGuid", UseGUID),
                    new SqlParameter("@TemplateId", TemplateId)));
        }

        private void DbUpdate()
        {
            if (string.IsNullOrEmpty(GUID))
            {
                GUID = Guid.NewGuid().ToString("N") + Guid.NewGuid().ToString("N");
            }

            SqlHelper.ExecuteNonQuery(Core.ConnectionString,
                "AccountPublicRequestLinkUpdateById",
                    new SqlParameter("@Id", Id),
                    new SqlParameter("@AccountId", AccountId),
                    new SqlParameter("@CompanyId", CompanyId),
                    new SqlParameter("@CreateDate", CreateDate),
                    new SqlParameter("@OwnerUserId", OwnerUserId),
                    new SqlParameter("@Deleted", Deleted),
                    new SqlParameter("@GUID", GUID),
                    new SqlParameter("@RequireAuthentication", RequireAuthentication),
                    new SqlParameter("@UseGUID", UseGUID),
                    new SqlParameter("@TemplateId", TemplateId));
        }


        public void Delete()
        {
            if (Id == 0)
                throw new TowbookException("Can't delete a link that doesn't exist.");

            SqlHelper.ExecuteNonQuery(Core.ConnectionString,
                "AccountPublicRequestLinkDeleteById",
                    new SqlParameter("@Id", Id));
        }
    }
}
