using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using Extric.Towbook.Utility;
using Glav.CacheAdapter.Core.DependencyInjection;

namespace Extric.Towbook.Commissions
{
    [Table("CommissionRateItems")]
    public class CommissionRateItem : Commission
    {
        private const string CacheKey = "CommissionRateItem";
        
        private RateItem _rateItem;

        public CommissionRateItem()
        {
            
        }

        public CommissionRateItem(SqlDataReader reader)
        {
            InitializeFromReader(reader);
        }

        public static CommissionRateItem GetByRateItem(IRateItem ri, Driver driver, int? commissionScheduleGroupId = null)
        {
            return GetByRateItem(ri.RateItemId, (driver != null ? driver.Id : 0), commissionScheduleGroupId);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="companyId"></param>
        /// <returns>a dictionary with the key being the rateItem that the commission represents</returns>
        public static async Task<Dictionary<int, CommissionRateItem>> GetByCompany(int companyId)
        {
            Dictionary<int, CommissionRateItem> list = new Dictionary<int, CommissionRateItem>();

            using (var reader = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                   CommandType.StoredProcedure, "CommissionRateItemsGetByCompanyId",
                   new SqlParameter("@CompanyId", companyId)))
            {
                while(await reader.ReadAsync())
                {
                    list.Add(reader.GetValue<int>("RateItemId"), new CommissionRateItem(reader));
                }

                return list;
            }
        }

        public static async Task<Dictionary<int, CommissionRateItem>> GetByCompany(int companyId, int driverId)
        {
            Dictionary<int, CommissionRateItem> list = new Dictionary<int, CommissionRateItem>();

            using (var reader = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                   CommandType.StoredProcedure, "CommissionRateItemsGetByCompanyId",
                   new SqlParameter("@CompanyId", companyId.ToString()),
                   new SqlParameter("@DriverId", (driverId == 0 ? null : (int?)driverId))))
            {
                while (await reader.ReadAsync())
                {
                    list.Add(reader.GetValue<int>("RateItemId"), new CommissionRateItem(reader));
                }

                return list;
            }
        }

        public static async Task<List<CommissionRateItem>> GetByCompanies(int[] companyIds, int driverId)
        {
            var list = new List<CommissionRateItem>();

            using (var reader = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                   CommandType.StoredProcedure, "CommissionRateItemsGetByCompanyId",
                   new SqlParameter("@CompanyId", string.Join(",", companyIds)),
                   new SqlParameter("@DriverId", (driverId == 0 ? null : (int?)driverId))))
            {
                while (await reader.ReadAsync())
                {
                    list.Add(new CommissionRateItem(reader));
                }

                return list;
            }
        }

        public static async Task<List<CommissionRateItem>> GetGroupValuesByCompanyId(int companyId, int rateItemId)
        {
            List<CommissionRateItem> list = new List<CommissionRateItem>();

            using (var reader = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                   CommandType.StoredProcedure, "CommissionRateItemsGetGroupValuesByCompanyId",
                   new SqlParameter("@CompanyId", companyId),
                   new SqlParameter("@RateItemId", rateItemId)))
            {
                while (await reader.ReadAsync())
                {
                    list.Add(new CommissionRateItem(reader));
                }

                return list;
            }
        }

        public static async Task<IEnumerable<CommissionRateItem>> GetGroupValuesByDriverId(int companyId, int driverId, int rateItemId)
        {
            List<CommissionRateItem> list = new List<CommissionRateItem>();

            using (var reader = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                   CommandType.StoredProcedure, "CommissionRateItemsGetGroupValuesByDriverId",
                   new SqlParameter("@CompanyId", companyId),
                   new SqlParameter("@RateItemId", rateItemId),
                   new SqlParameter("@DriverId", driverId)))
            {
                while (await reader.ReadAsync())
                {
                    list.Add(new CommissionRateItem(reader));
                }

                return list;
            }
        }

        public static async Task<CommissionRateItem> GetByRateItem(int rateItemId)
        {
            return await GetByRateItemAsync(rateItemId, 0);
        }

        /// <summary>
        /// Get the commission rate for a specified rate item, for the specified driver. If it doesn't exist for the specified driver,
        /// the default company rate will be returned if it exists. If you're modifying commissions, make sure not to use the return value of this
        /// if the driverId != the driverId you passed in.
        /// </summary>
        /// <param name="rateItemId"></param>
        /// <param name="driverId"></param>
        /// <returns></returns>
		public static CommissionRateItem GetByRateItem(int rateItemId, int driverId, int? commissionScheduleGroupId = null, int? companyId = null)
        {
            return AppServices.Cache.Get($"cri:{rateItemId}_{driverId}_{companyId.GetValueOrDefault()}",
                TimeSpan.FromMinutes(5), () =>
                {
                    using (var reader = SqlHelper.ExecuteReader(Core.ConnectionString,
                        CommandType.StoredProcedure, "CommissionRateItemsGetByRateItemId",
                        new SqlParameter("@RateItemId", (rateItemId != -1 ? (int?)rateItemId : null)),
                        new SqlParameter("@DriverId", (driverId != 0 ? (int?)driverId : null)),
                        new SqlParameter("@CommissionScheduleGroupId", commissionScheduleGroupId),
                        new SqlParameter("@CompanyId", companyId)))
                    {
                        if (reader.Read())
                        {
                            return new CommissionRateItem(reader);
                        }
                        else
                        {
                            return null;
                        }
                    }
                });
        }

        public static async Task<CommissionRateItem> GetByRateItemAsync(int rateItemId, int driverId, int? commissionScheduleGroupId = null, int? companyId = null)
        {
            return await AppServices.Cache.GetAsync($"cri:{rateItemId}_{driverId}_{companyId.GetValueOrDefault()}",
                TimeSpan.FromMinutes(5), async () =>
                {
                    using (var reader = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                        CommandType.StoredProcedure, "CommissionRateItemsGetByRateItemId",
                        new SqlParameter("@RateItemId", (rateItemId != -1 ? (int?)rateItemId : null)),
                        new SqlParameter("@DriverId", (driverId != 0 ? (int?)driverId : null)),
                        new SqlParameter("@CommissionScheduleGroupId", commissionScheduleGroupId),
                        new SqlParameter("@CompanyId", companyId)))
                    {
                        if (await reader.ReadAsync())
                        {
                            return new CommissionRateItem(reader);
                        }
                        else
                        {
                            return null;
                        }
                    }
                });
        }

        public static async Task<CommissionRateItem> GetById(int id)
        {
            using (var reader = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                    CommandType.StoredProcedure, "CommissionRateItemsGetById",
                    new SqlParameter("@CommissionRateItemId", id)))
            {
                if (await reader.ReadAsync())
                {
                    return new CommissionRateItem(reader);
                }
                else
                {
                    return null;
                }
            }
        }

        protected override void InitializeFromReader(SqlDataReader reader)
        {
            base.InitializeFromReaderInternal(reader);

            _rateItem = RateItem.GetById(reader.GetValue<int>("RateItemId"));
        }

        protected override void DbUpdate()
        {
            SqlHelper.ExecuteNonQuery(Core.ConnectionString,
                CommandType.StoredProcedure,
                "CommissionRateItemsUpdateById",
                    new SqlParameter[] 
				    {
                        new SqlParameter("@CommissionRateItemId", _id),
                        new SqlParameter("@CompanyId", CompanyId),
                        new SqlParameter("@RateItemId", _rateItem != null &&  _rateItem.Id > 0 ? (int?)_rateItem.Id : null ),
                        new SqlParameter("@DriverId", (_driverId > 0 ? (int?)_driverId : null) ),
						new SqlParameter("@Type", _type),
                        new SqlParameter("@PercentageValue", _percentage),
                        new SqlParameter("@FlatRateValue", _flatRate),
                        new SqlParameter("@FreeQuantity", FreeQuantity),
                        new SqlParameter("@CommissionScheduleGroupId", _commissionScheduleGroupId)
					});
        }

        protected override int DbInsert()
        {
            _id = Convert.ToInt32(SqlHelper.ExecuteScalar(Core.ConnectionString,
                CommandType.StoredProcedure,
                "CommissionRateItemsInsert",
                    new SqlParameter[] 
				    {
                        new SqlParameter("@CompanyId", CompanyId ),
						new SqlParameter("@RateItemId", _rateItem.Id > 0 ? (int?)_rateItem.Id : null ),
						new SqlParameter("@DriverId", (_driverId > 0 ? (int?)_driverId : null) ),
						new SqlParameter("@Type", _type),
                        new SqlParameter("@PercentageValue", _percentage),
                        new SqlParameter("@FlatRateValue", _flatRate),
                        new SqlParameter("@FreeQuantity", FreeQuantity),
                        new SqlParameter("@CommissionScheduleGroupId", _commissionScheduleGroupId)
					}));
            return _id;
        }

        public override void Delete(User user)
        {
            SqlHelper.ExecuteNonQuery(Core.ConnectionString, "CommissionRateItemsDeleteById", _id);
            _id = 0;
        }

		public RateItem RateItem
		{
			get { return _rateItem; }
			set { _rateItem = value; } 
		}

    }
}
