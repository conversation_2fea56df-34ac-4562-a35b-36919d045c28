using System;
using System.Collections.ObjectModel;
using System.Data.SqlClient;
using ProtoBuf;
using Glav.CacheAdapter.Core.DependencyInjection;
using System.Linq;
using System.Threading.Tasks;
using Extric.Towbook.Caching;
using Extric.Towbook.Utility;

namespace Extric.Towbook
{
    [Serializable]
    [ProtoContract(Name = "TaxRate")]
    public class TaxRate
    {
        private const int CacheTimeout = 60;

        [ProtoMember(1)]
        public int Id { get; private set; }
        [ProtoMember(2)]
        public int CompanyId { get; set; }
        [ProtoMember(3)]
        public string Description { get; set; }
        [ProtoMember(4)]
        public decimal Rate { get; set; }
        [ProtoMember(5)]
        public bool TaxServices { get; set; }
        [ProtoMember(6)]
        public bool TaxWinching { get; set; }
        [ProtoMember(7)]
        public bool TaxStorage { get; set; }
        [ProtoMember(8)]
        public bool TaxMileage { get; set; }
        [ProtoMember(9)]
        public DateTime CreateDate { get; private set; }
        [ProtoMember(10)]
        public bool Deleted { get; private set; }


        public Company.Company Company
        {
            get { return Extric.Towbook.Company.Company.GetById(CompanyId); }
            set { CompanyId = value.Id; }
        }

        /// <summary>
        /// Create a new Impound Lot
        /// </summary>
        public TaxRate()
        {
            Id = -1;
        }

        protected TaxRate(SqlDataReader reader)
        {
            InitializeFromDataReader(reader);
            AppServices.Cache.Add("tr:" + Id, TimeSpan.FromMinutes(CacheTimeout), this);
        }

        [Obsolete("Please use GetById instead")]
        public TaxRate(int id, int companyId)
        {
            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "TaxRatesGetById", new SqlParameter("@TaxRateId", id)))
            {
                if (dr.Read())
                {
                    InitializeFromDataReader(dr);
                }
                else
                    throw new Exception("no such TaxRate");
            }

        }

        public static TaxRate GetById(int id)
        {
            return AppServices.Cache.Get<TaxRate>("tr:" + id,
                TimeSpan.FromMinutes(CacheTimeout), () => GetByIdFromDb(id));
        }

        public static async Task<TaxRate> GetByIdAsync(int id)
        {
            return await AppServices.Cache.GetAsync<TaxRate>("tr:" + id,
                TimeSpan.FromMinutes(CacheTimeout), async () => await GetByIdFromDbAsync(id));
        }

        public static TaxRate GetByIdFromDb(int id)
        {
            using (var dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                       "TaxRatesGetById", new SqlParameter("@TaxRateId", id)))
            {
                return dr.Read() ? new TaxRate(dr) : null;
            }
        }

        public static async Task<TaxRate> GetByIdFromDbAsync(int id)
        {
            using (var dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                       "TaxRatesGetById", new SqlParameter("@TaxRateId", id)))
            {
                return await dr.ReadAsync() ? new TaxRate(dr) : null;
            }
        }


        public static TaxRate GetById(int id, int companyId)
        {
            return GetById(id);
        }

        public override string ToString()
        {
            return String.Format("Extric.Towbook.TaxRate [Id = {1}, CreateDate = {2}]", Id, CreateDate);
        }

        private void InitializeFromDataReader(SqlDataReader reader)
        {
            Id = reader.GetValue<int>("TaxRateId");
            Company = Extric.Towbook.Company.Company.GetById(Convert.ToInt32(reader.GetValue<int>("CompanyId")));

            Description = reader.GetValue<string>("Description");
            Rate = reader.GetValue<decimal>("Rate");

            TaxServices = reader.GetValue<bool>("TaxServices");
            TaxWinching = reader.GetValue<bool>("TaxWinching");
            TaxStorage = reader.GetValue<bool>("TaxStorage");
            TaxMileage = reader.GetValue<bool>("TaxMileage");
            Deleted = reader.GetValue<bool>("Deleted");
            CreateDate = reader.GetValue<DateTime>("CreateDate");
        }


        [ProtoContract]
        private class InternalTaxRateCollection
        {
            [ProtoMember(1)]
            public Collection<TaxRate> TaxRates { get; set; }

            public InternalTaxRateCollection()
            {

            }
        }

        //If you change this method, don't forget to change the async version as well
        public static Collection<TaxRate> GetByCompany(Company.Company company)
        {
            if (company == null)
                return new Collection<TaxRate>();

            return AppServices.Cache.Get("tr_c:" + company.Id, DateTime.Now.AddHours(CacheTimeout), () =>
            {
                byte[] rawRates = Core.GetRedisValueAsByteArray("tr_c:" + company.Id);

                var t1 = Cache.FromByteArray<Collection<TaxRate>>(rawRates);

                if (t1 != null)
                    return t1.ToCollection();

                var list = GetByCompany(new int[] { company.Id });

                Core.SetRedisValue("tr_c:" + company.Id, Cache.ToByteArray(list), TimeSpan.FromHours(24));

                return list;
            });
        }

        public static async Task<Collection<TaxRate>> GetByCompanyAsync(Company.Company company)
        {
            if (company == null)
                return new Collection<TaxRate>();

            return await AppServices.Cache.GetAsync("tr_c:" + company.Id, DateTime.Now.AddHours(CacheTimeout), async () =>
            {
                byte[] rawRates = await Core.GetRedisValueAsByteArrayAsync("tr_c:" + company.Id);

                var t1 = Cache.FromByteArray<Collection<TaxRate>>(rawRates);

                if (t1 != null)
                    return t1.ToCollection();

                var list = await GetByCompanyAsync(new int[] { company.Id });

                await Core.SetRedisValueAsync("tr_c:" + company.Id, Cache.ToByteArray(list), TimeSpan.FromHours(24));

                return list;
            });
        }

        /// <summary>
        /// Returns a list of all active tax rates (doesn't return deleted ones)
        /// </summary>
        //If you change this method, don't forget to change the async version as well
        public static Collection<TaxRate> GetByCompany(int[] companyIds)
		{
            if (companyIds == null || !companyIds.Any())
                return new Collection<TaxRate>();

            companyIds = companyIds.OrderBy(o => o).ToArray();

            var cacheKey = string.Join(",", companyIds);

            var x = AppServices.Cache.Get("tr_c:" + cacheKey,
                TimeSpan.FromMinutes(CacheTimeout), () =>
                {
                    InternalTaxRateCollection c = new InternalTaxRateCollection { TaxRates = new Collection<TaxRate>() };

                    using (var dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                               "TaxRatesGetByCompanyId", cacheKey))
                    {
                        while (dr.Read())
                        {
                            c.TaxRates.Add(new TaxRate(dr));
                        }
                    }

                    c.TaxRates = c.TaxRates.OrderBy(o => o.Description).ToCollection();
                    
                    return c;
                });

            return x.TaxRates ?? new Collection<TaxRate>();
		}

        public static async Task<Collection<TaxRate>> GetByCompanyAsync(int[] companyIds)
		{
            if (companyIds == null || !companyIds.Any())
                return new Collection<TaxRate>();

            companyIds = companyIds.OrderBy(o => o).ToArray();

            var cacheKey = string.Join(",", companyIds);

            var x = await AppServices.Cache.GetAsync("tr_c:" + cacheKey,
                TimeSpan.FromMinutes(CacheTimeout), async () =>
                {
                    InternalTaxRateCollection c = new InternalTaxRateCollection { TaxRates = new Collection<TaxRate>() };

                    using (var dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                               "TaxRatesGetByCompanyId", cacheKey))
                    {
                        while (await dr.ReadAsync())
                        {
                            c.TaxRates.Add(new TaxRate(dr));
                        }
                    }

                    c.TaxRates = c.TaxRates.OrderBy(o => o.Description).ToCollection();
                    
                    return c;
                });

            return x.TaxRates ?? new Collection<TaxRate>();
		}

        public async Task Delete(User u)
        {
            await Delete();
        }

        public async Task Delete()
        {
            SqlHelper.ExecuteScalar(Core.ConnectionString,
                "TaxRatesDeleteById",
                new SqlParameter("@TaxRateId", Id));

            await ClearCache(true);
        }

        /// <summary>
        /// Saves the Driver object to the data store.
        /// </summary>
        public async Task Save()
        {
            if (Id == 0)
            {
				throw new TowbookException("No such Tax Rate Item. Can't save " +
                    "object! (this object should have already been discarded!)");
            }
			if (Company == null) throw new TowbookException("Company must be set!");

            if (Id == -1)
            {
                DbInsert();
            }
            else
            {
                DbUpdate();
            }

            await ClearCache(false);
        }

        private async Task ClearCache(bool isDelete)
        {
            AppServices.Cache.InvalidateCacheItem("tr_c:" + CompanyId);
            AppServices.Cache.InvalidateCacheItem("tr:" + Id);
            await Core.DeleteRedisKeyAsync("tr_c:" + CompanyId);

            if(isDelete)
                await CacheWorkerUtility.DeleteTaxRate(this);
            else
                await CacheWorkerUtility.UpdateTaxRate(this);
        }

        private void DbInsert()
        {
            Id = Convert.ToInt32(SqlHelper.ExecuteScalar(Core.ConnectionString,
                "TaxRatesInsert",
                new SqlParameter("@CompanyId", Company.Id),
				new SqlParameter("@Description", Description),
                new SqlParameter("@Rate", Rate),
				new SqlParameter("@TaxServices", TaxServices),
				new SqlParameter("@TaxWinching", TaxWinching),
				new SqlParameter("@TaxStorage", TaxStorage),
				new SqlParameter("@TaxMileage", TaxMileage)
                ));
        }

        private void DbUpdate()
        {      
            SqlHelper.ExecuteNonQuery(Core.ConnectionString,
                "TaxRatesUpdateById", 
                new SqlParameter("@TaxRateId", Id),
                new SqlParameter("@CompanyId", Company.Id),
                new SqlParameter("@Description", Description),
				new SqlParameter("@Rate", Rate),
                new SqlParameter("@TaxServices", TaxServices),
				new SqlParameter("@TaxWinching", TaxWinching),
				new SqlParameter("@TaxStorage", TaxStorage),
				new SqlParameter("@TaxMileage", TaxMileage));
		}
	}
}
