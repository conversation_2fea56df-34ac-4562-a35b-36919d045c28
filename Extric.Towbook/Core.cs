using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Transactions;
using System.Web;
using Extric.Towbook.Configuration;
using Extric.Towbook.Utility;
using Extric.Towbook.WebWrapper.Configuration;
using StackExchange.Redis;

namespace Extric.Towbook
{
    public class Core : BaseCore
    {
        private static string _connectionString = @"Data Source=localhost;Initial Catalog=Towbook;Integrated Security=True;";

        public static string ConnectionString
        {
            get
            {
                if (Configuration.GetConnectionString("Database") != null)
                {
                    _connectionString = Configuration.GetConnectionString("Database");
                }

                return _connectionString;
            }
        }

        public static string ConnectionStringReadOnly
        {
            get
            {
                if (Configuration.GetConnectionString("DatabaseReadOnly") != null)
                {
                    return Configuration.GetConnectionString("DatabaseReadOnly");
                }

                return ConnectionString;
            }
        }

        public static string SupportPhoneNumber { get; } = "(*************";

        private static string[] _redisServers = new string[]
        {
            "*************",
            "*************",
            "10.173.18.213"
        };

        public static string[] RedisServers
        {
            get
            {
                if (Configuration.GetValue<string>("Redis:Servers") != null)
                {
                    var temp = Configuration.GetValue<string>("Redis:Servers")
                        .Replace(";", ",")
                        .Replace("\n", ",")
                        .Replace(",,", ",");

                    if (temp.Contains(","))
                        _redisServers = temp.Split(',');
                    else
                        _redisServers = new string[] { temp };
                }

                return _redisServers;
            }
        }

        private static Lazy<ConnectionMultiplexer>[] _redisConnections;
        private static Lazy<ConnectionMultiplexer>[] RedisConnections
        {
            get
            {
                if (_redisConnections == null)
                {
                }
                return _redisConnections;
            }
        }

        public Core(IConfig configuration) : base(configuration)
        {
            _redisConnections = CreateConnections();
        }

        public static SqlConnection GetConnection()
        {
            return new SqlConnection(Core.ConnectionString);
        }

        public static SqlConnection GetHyperscaleConnection()
        {
            if (Configuration.GetConnectionString("Database.Azure") != null)
                return new SqlConnection(Configuration.GetConnectionString("Database.Azure"));
            else
                return null;
        }

        public static SqlConnection GetHyperscaleConnectionReadOnly()
        {
            if (Configuration.GetConnectionString("Database.Azure") != null)
            {
                return new SqlConnection(Configuration.GetConnectionString("Database.Azure") +
                    "ApplicationIntent=ReadOnly;");
            }
            else
                return null;
        }

        public static long RedisIncrementKey(string key)
        {
            return GetRedisConnection().GetDatabase().StringIncrement(key);
        }

        private static Lazy<ConnectionMultiplexer> redisConnection
        {
            get => RedisConnections.OrderBy(o => o.Value.GetCounters().TotalOutstanding).First();
        }

        private static Lazy<ConnectionMultiplexer>[] CreateConnections()
        {
            var poolSize = Configuration.GetValue<string>("Redis:ConnectionPoolSize");

            var count = 12; // want 12 for web servers.

            if (poolSize != null)
                int.TryParse(poolSize, out count);

            if (System.Environment.UserInteractive && poolSize == null) // only want 1 for console apps, console services.
                count = 1;

            var newList = new Lazy<ConnectionMultiplexer>[count];

            for (int i = 0; i < newList.Length; i++)
            {
                newList[i] = CreateConnection();
            }

            return newList;
        }

        private static Lazy<ConnectionMultiplexer> CreateConnection()
        {
            return new Lazy<ConnectionMultiplexer>(RedisConnectionMultiplexer());
        }

        private static Lazy<ConnectionMultiplexer> dedicatedGpsRedis = CreateDedicatedGpsConnection();

        private static Lazy<ConnectionMultiplexer> CreateDedicatedGpsConnection()
        {
            return new Lazy<ConnectionMultiplexer>(RedisConnectionMultiplexer(true));
        }

        public static ConnectionMultiplexer GetRedisConnection()
        {
            return redisConnection.Value;
        }

        public static ConnectionMultiplexer GetDedicatedGpsRedisConnection()
        {
            return dedicatedGpsRedis.Value;
        }

        private static Func<ConnectionMultiplexer> RedisConnectionMultiplexer(bool forceServiceName = false)
        {
            return () =>
            {
                if (!string.IsNullOrWhiteSpace(RedisCredentials))
                {
                    var options = ConfigurationOptions.Parse(RedisCredentials);

                    options.SyncTimeout = 3000;
                    //options.AsyncTimeout = 10000;
                    options.AbortOnConnectFail = false;
                    options.ConnectTimeout = 3000;
                    options.ConnectRetry = 5;
                    options.ReconnectRetryPolicy = new ExponentialRetry(5000);
                    //options.KeepAlive = 60;

                    return ConnectionMultiplexer.Connect(options);
                }
                else
                {
                    var serviceName = Configuration.GetValue<string>("Redis:SentinelServiceName");
                    var co = new ConfigurationOptions
                    {
                        AbortOnConnectFail = false,
                        ConnectTimeout = 3000,
                        ConnectRetry = 5,
                        ReconnectRetryPolicy = new ExponentialRetry(5000),
                        SyncTimeout = 3000
                    };

                    if (serviceName != "!disable")
                    {
                        if (forceServiceName)
                        {
                            co.ServiceName = "mymaster";
                        }
                        else
                        {
                            // Use sentinel. This will discover the sentinel master+replicas,
                            // and fill in the nodes.
                            co.ServiceName = serviceName ?? "mymaster";
                        }
                    }

                    foreach (var endpoint in RedisServers)
                    {
                        if (!string.IsNullOrWhiteSpace(endpoint))
                            co.EndPoints.Add(endpoint);
                    }

                    if (co.EndPoints.Count == 0)
                        co.EndPoints.Add("**************");

                    return ConnectionMultiplexer.Connect(co);
                }
            };
        }

        public static IDatabase GetRedisDatabase()
        {
            return GetRedisConnection().GetDatabase();
        }

        public static IDatabaseAsync GetRedisDatabaseAsync()
        {
            return GetRedisConnection().GetDatabase();
        }

        public static string GetRedisValue(string key)
        {
            try
            {
                if (GetRedisConnection().IsConnected)
                    return GetRedisDatabase().StringGet(key, CommandFlags.PreferReplica);
                else
                    return null;
            }
            catch
            {
                return null;
            }
        }

        public static async Task<string> GetRedisValueAsync(string key)
        {
            try
            {
                if (GetRedisConnection().IsConnected)
                    return Cache.SafeStringDecode(await GetRedisDatabase().StringGetAsync(key, CommandFlags.PreferReplica));
                else
                    return null;
            }
            catch
            {
                return null;
            }
        }

        public static byte[] GetRedisValueAsByteArray(string key)
        {
            try
            {
                if (GetRedisConnection().IsConnected)
                    return Cache.SafeStringDecodeBytes(GetRedisDatabase().StringGet(key, CommandFlags.PreferReplica));
                else
                    return null;
            }
            catch
            {
                return null;
            }
        }

        public static async Task<byte[]> GetRedisValueAsByteArrayAsync(string key)
        {
            try
            {
                if (GetRedisConnection().IsConnected)
                    return Cache.SafeStringDecodeBytes(await GetRedisDatabase().StringGetAsync(key, CommandFlags.PreferReplica));
                else
                    return null;
            }
            catch
            {
                return null;
            }
        }

        public static void SetRedisValue(string key, string value, TimeSpan expires)
        {
            try
            {
                if (GetRedisConnection().IsConnected)
                    GetRedisDatabase().StringSet(key, value, expires);
            }
            catch (RedisConnectionException)
            {
                // ignore connection exceptions.
            }
            catch (ArgumentOutOfRangeException)
            {

            }
            catch
            {

            }
        }

        public static async Task SetRedisValueAsync(string key, string value, TimeSpan expires)
        {
            try
            {
                if (GetRedisConnection().IsConnected)
                    await GetRedisDatabase().StringSetAsync(key, value, expires);
            }
            catch (RedisConnectionException)
            {
                // ignore connection exceptions.
            }
            catch (ArgumentOutOfRangeException)
            {

            }
            catch
            {

            }
        }

        public static async Task SetRedisValueAsync(string key, string value)
        {
            try
            {
                if (GetRedisConnection().IsConnected)
                    await GetRedisDatabase().StringSetAsync(key, value);
            }
            catch (RedisConnectionException)
            {
                // ignore connection exceptions.
            }
            catch (ArgumentOutOfRangeException)
            {

            }
            catch
            {

            }
        }

        public static async Task SetRedisValueAsync(string key, byte[] value)
        {
            try
            {
                if (GetRedisConnection().IsConnected)
                    await GetRedisDatabase().StringSetAsync(key, value);
            }
            catch (RedisConnectionException)
            {
                // ignore connection exceptions.
            }
            catch (ArgumentOutOfRangeException)
            {

            }
            catch
            {

            }
        }


        public static void SetRedisValue(string key, long value, TimeSpan expires)
        {
            try
            {
                if (GetRedisConnection().IsConnected)
                    GetRedisDatabase().StringSet(key, value, expires);
            }
            catch (RedisConnectionException)
            {
                // ignore connection exceptions.
            }
            catch (ArgumentOutOfRangeException)
            {

            }
            catch
            {

            }
        }

        public static void SetRedisValue(string key, string value)
        {
            try
            {
                if (GetRedisConnection().IsConnected)
                    GetRedisDatabase().StringSet(key, value);
            }
            catch (RedisConnectionException)
            {
                // ignore connection exceptions.
            }
            catch (ArgumentOutOfRangeException)
            {

            }
            catch
            {

            }
        }

        public static void SetRedisValue(string key, byte[] value)
        {
            try
            {
                if (GetRedisConnection().IsConnected)
                    GetRedisDatabase().StringSet(key, value);
            }
            catch (RedisConnectionException)
            {
                // ignore connection exceptions.
            }
        }

        public static void SetRedisValue(string key, byte[] value, TimeSpan expires)
        {
            try
            {
                if (GetRedisConnection().IsConnected)
                    GetRedisDatabase().StringSet(key, value, expires);
            }
            catch (RedisConnectionException)
            {
                // ignore connection exceptions.
            }
        }


        public static async Task SetRedisValueAsync(string key, byte[] value, TimeSpan expires)
        {
            try
            {
                if (GetRedisConnection().IsConnected)
                    await GetRedisDatabase().StringSetAsync(key, value, expires);
            }
            catch (RedisConnectionException)
            {
                // ignore connection exceptions.
            }
        }


        public static void DeleteRedisKey(string key)
        {
            try
            {
                if (GetRedisConnection().IsConnected)
                    GetRedisDatabase().KeyDelete(key);
            }
            catch (RedisConnectionException)
            {
                // ignore connection exceptions
            }
        }


        public static async Task DeleteRedisKeyAsync(string key)
        {
            try
            {
                if (GetRedisConnection().IsConnected)
                    await GetRedisDatabase().KeyDeleteAsync(key);
            }
            catch (RedisConnectionException)
            {
                // ignore connection exceptions
            }
        }


        public static void SetGlobalUriConnectionLimit(Uri uri, int connectionLimit)
        {
            var servicePoint = System.Net.ServicePointManager.FindServicePoint(uri);

            // Increase the number of TCP connections from the default (2)
            servicePoint.ConnectionLimit = connectionLimit;
        }

        public static TransactionScope CreateTransactionScope()
        {
            var transactionOptions = new TransactionOptions();
            transactionOptions.IsolationLevel = IsolationLevel.ReadCommitted;
            transactionOptions.Timeout = TransactionManager.MaximumTimeout;
            return new TransactionScope(TransactionScopeOption.Required, transactionOptions, TransactionScopeAsyncFlowOption.Enabled);
        }


        public static string NormalizeString(string str)
        {
            char[] arr = str.ToLowerInvariant().Replace(" ", "").Trim().ToCharArray();

            arr = Array.FindAll<char>(arr, (c => (char.IsLetterOrDigit(c)
                                              || char.IsWhiteSpace(c)
                                              || c == '-')));
            return new string(arr);
        }

        /// <summary>
        /// Returns a string representation of the current stacktrace, excluding the call to this method. 
        /// Do not use this in finished production code, as calling this can be expensive, or so I've read. 
        /// </summary>
        /// <returns></returns>
        public static string GetStackTrace()
        {
            return string.Join("\n", Environment.StackTrace.Split('\n').Skip(3).Take(10));
        }

        public static string CsvFormat(string input)
        {
            if (input == null)
                return null;

            if (input.Contains("\""))
                input = string.Format("\"{0}\"", input.Replace("\"", "\"\""));
            else if (input.Contains(","))
                input = string.Format("\"{0}\"", input);

            return input;
        }

        /// <summary>
        /// Method to properly suffix numbers with "st", "nd", "rd" or "th".
        /// </summary>
        /// <param name="iNumber">Number to add the suffix to.</param>
        /// <returns>Number as a properly suffixed string (i.e. 1st, 2nd, 3rd, 4th, 11th, 21st, etc.)</returns>
        /// <example><code>
        /// for (int i = 1; i &lt; 1305; i++)
        /// {
        ///     Console.WriteLine(Utility.GetOrdinal(i));
        /// }
        /// </code></example>
        public static string GetOrdinal(int number)
        {
            string suf = "th";
            if (((number % 100) / 10) != 1) //Handles 11, 12 & 13.  Only equals one if iNumber has a one in the ten digit.
            {
                switch (number % 10) //Returns digit in the 1 column to evaluate.
                {
                    case 1:
                        suf = "st";
                        break;
                    case 2:
                        suf = "nd";
                        break;
                    case 3:
                        suf = "rd";
                        break;
                }
            }
            return number.ToString(System.Globalization.CultureInfo.InvariantCulture) + suf;
        }

        public static string FormatPhoneWithDashesOnly(string phone)
        {
            if (string.IsNullOrWhiteSpace(phone))
            {
                return "";
            }
            try
            {
                string phone2, phone3;

                phone2 = phone
                    .Replace(".", "")
                    .Replace("-", "")
                    .Replace(" ", "")
                    .Replace("(", "")
                    .Replace("\\", "")
                    .Replace(")", "").Replace("\t", "").Replace("\n", "").Replace("\r", "").Replace(" ", "").Trim();
                
                if (phone2.Length == 12 && phone2[0] == '+')
                    phone2 = phone2.Substring(2);

                if (phone2.Length == 11 && phone2[0] == '1')
                    phone2 = phone2.Substring(1);

                if (phone2.Length == 10)
                {
                    phone3 = phone2.Substring(0, 3) + "-" +
                        phone2.Substring(3, 3) + "-" +
                        phone2.Substring(6, 4);

                    return phone3;
                }
                else
                {
                    return phone;
                }
            }
            catch
            {
                return phone;
            }
        }


        public static string FormatPhoneWithNumbersOnly(string phone)
        {
            if (string.IsNullOrWhiteSpace(phone))
            {
                return "";
            }
            try
            {
                string phone2, phone3;

                phone2 = phone
                    .Replace(".", "")
                    .Replace("-", "")
                    .Replace(" ", "")
                    .Replace("+", "")
                    .Replace("(", "")
                    .Replace(")", "");

                if (phone2.Length == 12 && phone2[0] == '+')
                    phone2 = phone2.Substring(2);

                if (phone2.Length == 11 && phone2[0] == '1')
                    phone2 = phone2.Substring(1);

                if (phone2.Length == 10)
                {
                    phone3 = phone2.Substring(0, 3) +
                        phone2.Substring(3, 3) +
                        phone2.Substring(6, 4);

                    return phone3;
                }
                else
                {
                    return phone;
                }
            }
            catch
            {
                return phone;
            }
        }

        /// <summary>
        /// Formats a US phone number to friendly format.
        /// </summary>
        /// <param name="phone">input phone number; may contain -, . or spaces separating the phone number.</param>
        /// <returns>(xxx) yyy-zzzz formatted phone number if the inputted phone number is valid. 
        /// If the format isn't able to be formatted, the original value is returned</returns>
        public static string FormatPhone(string phone)
        {
            if (string.IsNullOrWhiteSpace(phone))
            {
                return "";
            }
            try
            {
                string phone2, phone3;

                phone2 = phone
                    .Replace(".", "")
                    .Replace("-", "")
                    .Replace(" ", "")
                    .Replace("(", "")
                    .Replace(")", "");

                if (phone2.Length == 12 && phone2[0] == '+')
                    phone2 = phone2.Substring(2);

                if (phone2.Length == 11 && phone2[0] == '1')
                    phone2 = phone2.Substring(1);

                if (phone2.Length == 10)
                {
                    phone3 = "(" + phone2.Substring(0, 3) + ") " +
                        phone2.Substring(3, 3) + "-" +
                        phone2.Substring(6, 4);

                    return phone3;
                }
                else
                {
                    return phone;
                }
            }
            catch
            {
                return phone;
            }
        }

        public static string FormatPhone(string phone, Company.Company company)
        {
            if (company != null && (company.Country == Company.Company.CompanyCountry.NewZealand
                    || company.Country == Company.Company.CompanyCountry.Australia))
            {
                if (string.IsNullOrWhiteSpace(phone))
                    return "";

                string phone2;

                phone2 = phone
                    .Replace(".", "")
                    .Replace("-", "")
                    .Replace(" ", "")
                    .Replace("(", "")
                    .Replace(")", "");

                if (phone2.Length == 12 && phone2[0] == '+')
                    phone2 = phone2.Substring(2);

                if (phone2.Length == 11 && phone2[0] == '1')
                    phone2 = phone2.Substring(1);

                if (phone2.Length == 10)
                {
                    return phone2.Substring(0, 4) + " " +
                            phone2.Substring(4, 3) + " " +
                            phone2.Substring(7, 3);
                }
                else
                {
                    return phone;
                }
            }
            else
            {
                return FormatPhone(phone);
            }
        }

        /// <summary>
        /// Formats a phone number
        /// </summary>
        /// <param name="phone"></param>
        /// <param name="numbersOnly">When true: 8105551234. When false: (*************</param>
        /// <returns></returns>
        public static string FormatPhone(string phone, bool numbersOnly)
        {
            phone = FormatPhone(phone);

            if (numbersOnly)
                return phone.Replace("(", "").Replace(")", "").Replace("-", "").Replace(" ", "");
            else
                return phone;
        }

        public static string FormatPhoneWithHyphens(string phone)
        {
            phone = FormatPhone(phone, true);

            if (phone.Length != 10)
                return phone;

            return string.Format("{0}-{1}-{2}", phone.Substring(0, 3), phone.Substring(3, 3), phone.Substring(6));
        }

        /// <summary>
        /// Takes an address like "123 Lakeside Drive, St Clair MI 48079-1234" and returns "123 Lakeside Drive, St Clair MI 48079". 
        /// If such a format can't be found, it will end up returning the same string as inputted.
        /// </summary>
        /// <param name="address">The input address that you want to attempt to truncate the zip in rom a 12345-1234 format to 12345.</param>
        /// <returns></returns>
        public static string StripLastFourFromNineDigitZipInAddress(string address)
        {
            if (address == null)
                return null;

            var output = new StringBuilder();

            foreach (var word in address.Split(' '))
            {
                if ((word.Length == 10 || word.Length == 9) &&
                    word.Where(o => char.IsNumber(o)).Count() == 9)
                {
                    output.Append(word.Substring(0, 5));
                }
                else
                {
                    output.Append(word);
                }
                output.Append(' ');
            }
            return output.ToString().Trim();

        }

        public static string GetZipFromAddress(string address)
        {
            if (address == null)
                return null;

            var words = address.Split(' ');
            for (var i = words.Length - 1; i > 0; i--)
            {
                var w = words[i].Trim();
                if (w.Length >= 5 && int.TryParse(w.Substring(0, 5), out int temp))
                    return w.Substring(0, 5);
            }

            return null;
        }

        public static string FormatName(object any)
        {
            return FormatName(any?.ToString());
        }
        /// <summary>
        /// Takes any full name, company name, initials, etc.. and formats it correctly with capitalization. 
        /// </summary>
        /// <param name="inString"></param>
        /// <returns></returns>
        public static string FormatName(string inString)
        {
            if (string.IsNullOrWhiteSpace(inString))
                return string.Empty;

            // pulled from: http://stackoverflow.com/a/7339260
            string outString = string.Empty;
            try
            {
                // Formal Format is made for names and addresses to assure 
                // proper formatting and capitalization
                inString = inString.Trim();

                // Break out each word in the string. 
                char[] charSep = { ' ' };
                string[] aWords = inString.Split(charSep);
                int i = 0;
                int CapAfterHyphen = 0;
                for (i = 0; i < aWords.Length; i++)
                {

                    string Word = aWords[i].Trim();
                    CapAfterHyphen = Word.IndexOf("-");
                    char[] chars = Word.ToCharArray();
                    if (chars.Length > 2)
                    {
                        if (Char.IsLower(chars[1]) && Char.IsUpper(chars[2]))
                        {
                            Word = Word.Substring(0, 1).ToUpper() + Word.Substring(1, 1).ToLower() + Word.Substring(2, 1).ToUpper() + Word.Substring(3).ToLower();
                        }
                        else
                        {
                            Word = Word.Substring(0, 1).ToUpper() + Word.Substring(1).ToLower();
                        }
                    }

                    Word = Word.Substring(0, 1).ToUpper() + Word.Substring(1);

                    if (CapAfterHyphen > 0)
                    {
                        Word = Word.Substring(0, CapAfterHyphen + 1) + Word.Substring(CapAfterHyphen + 1, 1).ToUpper() + Word.Substring(CapAfterHyphen + 2);
                    }
                    if (i > 0)
                    {
                        outString += " " + Word;
                    }
                    else
                    {
                        outString = Word;
                    }
                }
            }
            catch
            {
                return inString;
            }

            return outString;
        }


        public static DateTime? OffsetDateTime(Company.Company c, DateTime? dt)
        {
            if (dt != null)
                return OffsetDateTime(c, dt.Value, false);
            else
                return dt;
        }

        /// <summary>
        /// Offsets a DateTime based on the Company's timezone preference. 
        /// Use for text concatentation only; do not use to assign to a JSON value that is returned directly to the client.
        /// Towbook's JSON serialization takes care of offsetting dates automatically.
        /// </summary>
        /// <param name="c"></param>
        /// <param name="dt"></param>
        /// <returns></returns>
        public static DateTime OffsetDateTime(Company.Company c, DateTime dt)
        {
            return OffsetDateTime(c, dt, false);
        }

        /// <summary>
        /// Offsets a DateTime to/from server time.
        /// Use for text concatentation only; do not use to assign to a JSON value that is returned directly to the client.
        /// Towbook's JSON serialization takes care of offsetting dates automatically.
        /// </summary>
        /// <param name="c">Specify which Company to get Timezone preferences from</param>
        /// <param name="dt">DateTime value that you want to offset</param>
        /// <param name="reverse">Specify True if you want to convert the Company Local time back to Server Time. Specify False if you want to offset Server/Database to Company Local</param>
        /// <returns></returns>
        public static DateTime OffsetDateTime(Company.Company c, DateTime dt, bool reverse)
        {
            if (dt == DateTime.MinValue)
                return dt;

            if (c == null)
                return dt.ToDatabaseTime();

            try
            {
                if (c.TimezoneUseDST)
                {
                    if (dt != DateTime.MinValue)
                        return dt.AddHours(c.TimezoneOffset * (reverse ? -1 : 1));
                    else
                        return dt;
                }
                else
                {
                    int extraOffset = 0;

                    // take into account daylight savings time not being enabled...
                    if (dt.IsDaylightSavingTime())
                        extraOffset = 1 * (reverse ? 1 : -1);

                    if (dt != DateTime.MinValue)
                        return dt.AddHours(c.TimezoneOffset * (reverse ? -1 : 1)).AddHours(extraOffset);
                    else
                        return dt.AddHours(extraOffset);
                }
            }
            catch
            {
                return dt;
            }
        }

        public static string ProtectId(int id, int ownerUserId)
        {
            var l = MD5(id.ToString() + "N3v3rAga|nW|llS0m31" + ownerUserId);
            return l.Substring(l.Length - 10);
        }

        public static string MD5(string input)
        {
            System.Security.Cryptography.MD5CryptoServiceProvider x = new System.Security.Cryptography.MD5CryptoServiceProvider();
            byte[] bs = System.Text.Encoding.UTF8.GetBytes(input);
            bs = x.ComputeHash(bs);
            System.Text.StringBuilder s = new System.Text.StringBuilder();
            foreach (byte b in bs)
            {
                s.Append(b.ToString("x2").ToLower());
            }
            string password = s.ToString();
            return password;
        }

        public static bool IsPhoneValidStandard(string phone)
        {

            if (!string.IsNullOrWhiteSpace(phone))
            {
                if (phone.Length > 0 && phone.Where(o => char.IsDigit(o)).All(o => o == '0'))
                    return false;

                return Regex.IsMatch(phone, @"^\+?(\d[\d-. ]+)?(\([\d-. ]+\))?[\d-. ]+\d$", RegexOptions.IgnoreCase);
            }
            else
                return false;
        }

        /// <summary>
        /// Validate whether an individual email address is valid.
        /// </summary>
        /// <param name="email">Email address to validate</param>
        /// <returns>True if the email address is valid, false otherwise.</returns>
        public static bool IsEmailValid(string email)
        {
            if (string.IsNullOrWhiteSpace(email))
                return false;

            // trim away accidental spaces
            email = email.Trim();

            try
            {
                
                foreach (var address in email.Replace(";", ",").Split(new[] { "," }, StringSplitOptions.RemoveEmptyEntries))
                {
                    email = new System.Net.Mail.MailAddress(email).Address;

                }

                return true;
            }
            catch (FormatException)
            {
                // address is invalid
                return false;
            }
        }

        private static readonly string commitId = GetCoreVersionInternal(true);
        public static string GetCommitId() => commitId;

        private static readonly string coreVersion = GetCoreVersionInternal(false);

        public static string GetCoreVersion() => coreVersion;

        private static string GetCoreVersionInternal(bool returnCommitId)
        {
            var asm = Assembly.GetEntryAssembly() ?? Assembly.GetExecutingAssembly();
            var version = asm.GetName().Version;
            var product = asm.GetCustomAttributes(typeof(AssemblyProductAttribute), true).FirstOrDefault() as AssemblyProductAttribute;

            var entryAssemblyAttr = Attribute.GetCustomAttributes(asm,
                typeof(AssemblyInformationalVersionAttribute)) as AssemblyInformationalVersionAttribute[];

            var informationalVersion = entryAssemblyAttr.Length > 0 ? entryAssemblyAttr[0].InformationalVersion : "";

            var commit = "000000";
            var ver = "";

            if (informationalVersion != null)
            {
                commit = informationalVersion.Replace("+", "-");

                if (commit.Contains("-"))
                {
                    var vs = commit.Split(new char[] { '-' }, 2);

                    ver = vs[0];
                    commit = vs[1];
                }
            }

            if (returnCommitId)
                return commit;

            return version + "|" + commit; ;
        }


        private static readonly char[] BaseChars = "12345XFY".ToCharArray();
        private static readonly Dictionary<char, int> CharValues = BaseChars
                   .Select((c, i) => new { Char = c, Index = i })
                   .ToDictionary(c => c.Char, c => c.Index);

        public static string ReferralLongToBase(long value)
        {
            long targetBase = BaseChars.Length;
            // Determine exact number of characters to use.
            char[] buffer = new char[Math.Max(
                       (int)Math.Ceiling(Math.Log(value + 1, targetBase)), 1)];

            var i = buffer.Length;
            do
            {
                buffer[--i] = BaseChars[value % targetBase];
                value = value / targetBase;
            }
            while (value > 0);

            return new string(buffer, i, buffer.Length - i);
        }

        public static long ReferralBaseToLong(string number)
        {
            char[] chrs = number.ToCharArray();
            int m = chrs.Length - 1;
            int n = BaseChars.Length, x;
            long result = 0;
            for (int i = 0; i < chrs.Length; i++)
            {
                x = CharValues[chrs[i]];
                result += x * (long)Math.Pow(n, m--);
            }
            return result;
        }

        public static string GetOSInfo(string userAgent)
        {
            if (userAgent.Contains("Android"))
                return string.Format("Android {0}", GetMobileVersion(userAgent, "Android"));

            if (userAgent.Contains("iPad"))
                return string.Format("iPad OS {0}", GetMobileVersion(userAgent, "OS"));

            if (userAgent.Contains("iPhone"))
                return string.Format("iPhone OS {0}", GetMobileVersion(userAgent, "OS"));

            if (userAgent.Contains("Linux") && userAgent.Contains("KFAPWI"))
                return "Kindle Fire";

            if (userAgent.Contains("RIM Tablet") || (userAgent.Contains("BB") && userAgent.Contains("Mobile")))
                return "Black Berry";

            if (userAgent.Contains("Windows Phone"))
                return string.Format("Windows Phone {0}", GetMobileVersion(userAgent, "Windows Phone"));

            if (userAgent.Contains("Mac OS"))
                return "Mac OS";

            if (userAgent.Contains("Windows NT 5.1") || userAgent.Contains("Windows NT 5.2"))
                return "Windows XP";

            if (userAgent.Contains("Windows NT 6.0"))
                return "Windows Vista";

            if (userAgent.Contains("Windows NT 6.1"))
                return "Windows 7";

            if (userAgent.Contains("Windows NT 6.2"))
                return "Windows 8";

            if (userAgent.Contains("Windows NT 6.3"))
                return "Windows 8.1";

            if (userAgent.Contains("Windows NT 10"))
                return "Windows 10";

            //fallback to basic platform:
            return (userAgent.Contains("Mobile") ? " Mobile " : "");
        }
        public static String GetMobileVersion(string userAgent, string device)
        {
            var temp = userAgent.Substring(userAgent.IndexOf(device) + device.Length).TrimStart();
            var version = string.Empty;

            foreach (var character in temp)
            {
                var validCharacter = false;
                int test = 0;

                if (Int32.TryParse(character.ToString(), out test))
                {
                    version += character;
                    validCharacter = true;
                }

                if (character == '.' || character == '_')
                {
                    version += '.';
                    validCharacter = true;
                }

                if (validCharacter == false)
                    break;
            }

            return version;
        }

        /// <summary>
        /// Uses the HttpUtility.HtmlEncode method to sanitize text against XSS attacks.
        /// But, allow single quotes and ampersands to escape the encoding process.
        /// (example: "Bob's & Joe's Garage" will not return as "Bob&#39;s &amp; Garage")
        /// </summary>
        /// <param name="value">The text to encode</param>
        /// <returns></returns>
        public static string HtmlEncode(string value)
        {
            if (string.IsNullOrEmpty(value))
                return value;

            return HttpUtility
                .HtmlEncode(value)
                .Replace("&#39;", "'")
                .Replace("&amp;", "&");
        }

        /// <summary>
        /// This method replaces line break characters with html line breaks (<br />)
        /// </summary>
        /// <param name="value">The text to encode</param>
        public static string HtmlPreserveLineBreaks(string value)
        {
            if (string.IsNullOrEmpty(value))
                return value;

            return value
                .Replace(Environment.NewLine, "<br />")
                .Replace("\n", "<br />");
        }
    }

    [global::System.Serializable]
    public class TowbookException : ApplicationException
    {
        public TowbookException() { }
        public TowbookException(string message) : base(message) { }
        public TowbookException(string message, Exception inner) : base(message, inner) { }
        protected TowbookException(
          System.Runtime.Serialization.SerializationInfo info,
          System.Runtime.Serialization.StreamingContext context)
            : base(info, context) { }
    }

    public class MotorClubException : TowbookException
    {
        public MotorClubException() { }
        public MotorClubException(string message) : base(message) { }
    }

    public class TowbookReportException : TowbookException
    {
        public TowbookReportException(string message) : base(message) { }
    }

    public class BillingException : TowbookException
    {
        public BillingException() { }
        public BillingException(string message) : base(message) { }
    }

    public class InvalidLoginException : BillingException
    {
        public string Username { get; set; }
        public bool Transient { get; set; }

        public InvalidLoginException() : base("The username and password provided are not valid!") { }
        public InvalidLoginException(string username) : base(string.Format("Login failed for user: {0}", username))
        {
            this.Username = username;
        }
        public InvalidLoginException(string username, string message) : base(message)
        {
            this.Username = username;
        }

        public InvalidLoginException(string username, string message, bool transient)
        {
            this.Username = username;
            this.Transient = transient;
        }
    }

    public class PurchaseOrderNotFoundException : BillingException
    {
        public PurchaseOrderNotFoundException(string purchaseOrderNumber) : base(string.Format("Purchase order: {0} was not found/submitted!", purchaseOrderNumber)) { }
    }

    public class InvalidAmountsException : BillingException
    {
        public InvalidAmountsException() : base("The sum of the purchase order amounts does not match the total amount of the check!") { }
    }

    public class RetryException : BillingException
    {
        public RetryException(string message) : base(message) { }
    }

    public class NotFoundException : BillingException
    {
        public NotFoundException(string message) : base(message) { }
    }

    public class CancelledException : BillingException
    {
        public CancelledException(string message) : base(message) { }
    }

    public class NotSubmittableException : BillingException
    {
        public NotSubmittableException(string message) : base(message) { }
    }

    public class UnknownChargeException : BillingException
    {
        public UnknownChargeException(string chargeName) : base(string.Format("The charge: '{0}' is not recognized", chargeName)) { }
    }

    public class MatchingException : BillingException
    {
        public MatchingException(string message) : base(message) { }
    }

    public class UserErrorException : BillingException
    {
        public UserErrorException(string message) : base(message) { }
    }
}
