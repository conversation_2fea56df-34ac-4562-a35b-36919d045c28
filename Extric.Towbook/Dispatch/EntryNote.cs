using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Extric.Towbook.Utility;
using Glav.CacheAdapter.Core.DependencyInjection;
using ProtoBuf;

namespace Extric.Towbook.Dispatch
{
    /// <summary>
    /// Represents an individual date/time stamped Note for a Tow Ticket/Call that includes who wrote the note.
    /// </summary>
    [Serializable]
    [ProtoContract(ImplicitFields = ImplicitFields.AllFields, Name = "Note")]
    public class EntryNote
    {
        private const string cacheFormatNote = "s-nt-DispatchEntry-{0}";
        private const int CacheTimeout = 1440;

        public int Id { get; protected set; }
        public int DispatchEntryId { get; set; }
        public int OwnerUserId { get; set; }
        public string Content { get; set; }
        public DateTime CreateDate { get; set; }
        
        public static async Task<EntryNote> GetByIdAsync(int id)
        {
            return Map(await SqlMapper.QuerySpAsync<dynamic>("dbo.DispatchEntryNotesGetById",
                new
                {
                    @DispatchEntryNoteId = id
                })).FirstOrDefault();
        }

        /// <summary>
        /// Get all notes for the specified Call.
        /// </summary>
        /// <param name="DispatchEntryId"></param>
        /// <returns></returns>
        public static async Task<IEnumerable<EntryNote>> GetByDispatchEntryIdAsync(int DispatchEntryId, bool includeSystemNotes = false)
        {
            var result = (await AppServices.Cache.GetAsync<CacheCollection<EntryNote>>(String.Format(cacheFormatNote, DispatchEntryId), TimeSpan.FromDays(30), async() =>
            {
                return new CacheCollection<EntryNote>(Map(await SqlMapper.QuerySpAsync<dynamic>("dbo.DispatchEntryNotesGetByDispatchEntryId", new
                {
                    @DispatchEntryId = DispatchEntryId
                })));
            })).Items;

            if (includeSystemNotes)
                return result;
            else
                return result.Where(w => w.OwnerUserId != 1 && !w.Content.StartsWith("/* Automatic"));
        }

        public void Save()
        {
            this.Id = Convert.ToInt32(SqlMapper.QuerySP<dynamic>("DispatchEntryNotesInsert",
                new
                {
                    @DispatchEntryId = this.DispatchEntryId,
                    @OwnerUserId = this.OwnerUserId,
                    @Content = this.Content
                }).FirstOrDefault().Id);

            InvalidateCache(this.Id);
        }

        public async Task SaveAsync()
        {
            this.Id = Convert.ToInt32((await SqlMapper.QuerySpAsync<dynamic>("DispatchEntryNotesInsert",
                new
                {
                    @DispatchEntryId = this.DispatchEntryId,
                    @OwnerUserId = this.OwnerUserId,
                    @Content = this.Content
                })).FirstOrDefault().Id);

            InvalidateCache(this.Id);
        }
        public async Task DeleteAsync()
        {
            await SqlMapper.ExecuteSpAsync("dbo.DispatchEntryNotesDeleteById",
                new { @DispatchEntryNoteId = this.Id });
        }

        public static IEnumerable<EntryNote> Map(IEnumerable<dynamic> list)
        {
            return list.Select(o => new EntryNote()
            {
                Id = o.DispatchEntryNoteId,
                DispatchEntryId = o.DispatchEntryId,
                OwnerUserId = o.OwnerUserId,
                CreateDate = o.CreateDate,
                Content = o.Content,
            }).ToCollection();
        }

        public static void InvalidateCache(int id)
        {
            AppServices.Cache.InvalidateCacheItem(String.Format(cacheFormatNote, id));
        }
    }
}
