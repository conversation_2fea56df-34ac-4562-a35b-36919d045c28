using System;
using System.Collections.Generic;
using System.Linq;
using System.Data;
using System.Collections.ObjectModel;
using System.Data.SqlClient;
using System.Data.SqlTypes;
using Extric.Towbook.Accounts;
using Glav.CacheAdapter.Core.DependencyInjection;
using ProtoBuf;
using System.Dynamic;
using Extric.Towbook.Utility;
using Extric.Towbook.ActivityLogging;
using Async = System.Threading.Tasks;
using System.Threading.Tasks;
using System.Collections.Concurrent;
using Extric.Towbook.Company.Accounting;

// TODO: Move this to the Extric.Towbook namespace
// TODO: Rename table from DispatchEntryPayments to InvoicePayments, since it's not technically a DispatchEntryPayment, its an InvoicePayment which can belong to an Impound, DispatchEntry, or possibly something else in the future??. 
// TODO: 6/22/2010 10:22pm
namespace Extric.Towbook.Dispatch
{
    /// <summary>
    /// Represents a payment record to pay an Invoice (for either an impound or DispatchEntry)
    /// </summary>
    [ProtoContract]
    [Table("DispatchEntryPayments")]
    public class InvoicePayment
    {
        #region Public Properties
        [ProtoMember(1)]
        [Key("DispatchEntryPaymentId")]
        public int Id { get; internal set; }
        [ProtoMember(2)]
        public int InvoiceId { get; set; }

        [Obsolete("You should be using InvoiceId instead.")]
        public int DispatchEntryId { get; set; }

        [Obsolete("You should be using InvoiceId instead.")]
        public int ImpoundId { get; set; }

        [ProtoMember(3)]
        public PaymentType PaymentType { get; set; }


        [ProtoMember(4)]
        public int OwnerUserId { get; set; }
        [ProtoMember(5)]
        public decimal Amount { get; set; }
        [ProtoMember(6)]
        public string ReferenceNumber { get; set; }
        [ProtoMember(7)]
        public int AccountPaymentId { get; set; }
        [ProtoMember(8)]
        public DateTime CreateDate { get; internal set; }
        [ProtoMember(9)]
        public DateTime? PaymentDate { get; set; }
        [ProtoMember(10)]
        public int? PaymentVerificationId { get; set; }
        [ProtoMember(11)]
        public int ClassId { get; set; }
        [ProtoMember(12)]
        public bool IsVoid { get; set; }
        [ProtoMember(13)]
        public int? VoidedByUserId { get; set; }
        [ProtoMember(14)]
        public DateTime? VoidedDate { get; set; }
        
        public string ReferenceId => ReferenceNumber?.Replace("[TB:R]", "");

        #endregion

        /// <summary>
        /// A flag to indicate that this payment object is being alterred during the 
        /// closed accounting period.
        /// </summary>
        [ProtoIgnore]
        [Write(false)]
        public bool ClosedPeriodActivitySave { get; set; } = false;

        public InvoicePayment()
        {
            Id = -1;
        }

        public InvoicePayment(IDataReader dr)
        {
            InitializeFromDataReader(dr);
        }

        private void InitializeFromDataReader(IDataReader dr)
        {
            Id = Convert.ToInt32(dr["DispatchEntryPaymentId"]);
            InvoiceId = dr.GetValue<int>("InvoiceId");
            //DispatchEntryId = dr.GetValue<int>("DispatchEntryId");
            //ImpoundId = dr.GetValue<int>("ImpoundId");
            PaymentType = (PaymentType)Convert.ToInt32(dr["Type"]);
            OwnerUserId = dr.GetValue<int>("OwnerUserId");
            Amount = dr.GetValue<decimal>("Amount");
            CreateDate = dr.GetValue<DateTime>("CreateDate");
            PaymentDate = dr.GetValueOrNull<DateTime>("PaymentDate");
            PaymentVerificationId = dr.GetValueOrNull<int>("PaymentVerificationId");
            ReferenceNumber = dr.GetValue<string>("ReferenceNumber");
            IsVoid = dr.GetValue<bool>("IsVoid");
            AccountPaymentId = dr.GetValue<int>("AccountPaymentId");
            ClassId = dr.GetValue<int>("ClassId");
            VoidedByUserId = dr.GetValueOrNull<int>("VoidedByUserId");
            VoidedDate = dr.GetValueOrNull<DateTime>("VoidedDate");
        }

        public static async Task<IEnumerable<InvoicePayment>> GetByIdsAsync(int[] ids)
        {
            var payments = new Collection<InvoicePayment>();

            foreach (var batch in ids.OrderBy(o => o).Batch(250))
            {
                var parameters = new List<string>();
                int i = 0;
                var p = new List<SqlParameter>();
                foreach (var row in batch)
                {
                    i++;
                    parameters.Add("@P" + i);
                    p.Add(new SqlParameter("@P" + i, row));
                }

                var sql = $"SELECT * FROM DispatchEntryPayments WHERE DispatchEntryPaymentId IN ({string.Join(",", parameters)})";

                using (var dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                    CommandType.Text,
                    sql, p.ToArray()))
                {
                    while (await dr.ReadAsync())
                        payments.Add(new InvoicePayment(dr));
                }
            }
        
            return payments;
        }

        public static async Task<InvoicePayment> GetByIdAsync(int id)
        {
            if (id < 1)
                throw new TowbookException("Dispatch.Payment Id passed is < 1. invalid!");

            using (var dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString, CommandType.StoredProcedure,
                "DispatchEntryPaymentsGetById",
                new SqlParameter("@DispatchEntryPaymentId", id)))
            {
                if (await dr.ReadAsync())
                {
                    return new InvoicePayment(dr);
                }
                else
                {
                    return null;
                }
            }
        }

        public static async Task<Collection<InvoicePayment>> GetUnverifiedAsync(int companyId)
        {
            Collection<InvoicePayment> entries = new Collection<InvoicePayment>();

            using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                "DispatchEntryPaymentsGetUnverified",
                new SqlParameter("@CompanyId", companyId)))
            {
                while (await dr.ReadAsync())
                    entries.Add(new InvoicePayment(dr));
            }
            
            return entries;
        }

        public static Collection<InvoicePayment> GetByDispatchEntryId(int dispatchEntryId) =>
            GetByDispatchEntryId(dispatchEntryId, false);

        public static async Task<Collection<InvoicePayment>> GetByDispatchEntryIdAsync(int dispatchEntryId) =>
            await GetByDispatchEntryIdAsync(dispatchEntryId, false);

        public static async Task<Collection<InvoicePayment>> GetByDispatchEntryIdAsync(int dispatchEntryId, bool includeVoid)
        {
            var entries = new Collection<InvoicePayment>();

            if (dispatchEntryId < 1)
                throw new TowbookException("dispatchEntryId is < 1. invalid!");

            using (var dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString, CommandType.StoredProcedure,
                "DispatchEntryPaymentsGetByDispatchEntryId",
                new SqlParameter("@DispatchEntryId", dispatchEntryId),
                new SqlParameter("@IncludeVoid", includeVoid)))
            {
                while (await dr.ReadAsync())
                {
                    entries.Add(new InvoicePayment(dr));
                }
                return entries;
            }
        }
        public static Collection<InvoicePayment> GetByDispatchEntryId(int dispatchEntryId, bool includeVoid)
        {
            var entries = new Collection<InvoicePayment>();

            if (dispatchEntryId < 1)
                throw new TowbookException("dispatchEntryId is < 1. invalid!");

            using (var dr = SqlHelper.ExecuteReader(Core.ConnectionString, CommandType.StoredProcedure,
                "DispatchEntryPaymentsGetByDispatchEntryId",
                new SqlParameter("@DispatchEntryId", dispatchEntryId),
                new SqlParameter("@IncludeVoid", includeVoid)))
            {
                while (dr.Read())
                {
                    entries.Add(new InvoicePayment(dr));
                }
                return entries;
            }
        }

        public static async Task<Collection<InvoicePayment>> GetByCompanyAsync(int[] companyIds, DateTime startDate, DateTime endDate, int? type = null)
        {
            Collection<InvoicePayment> entries = new Collection<InvoicePayment>();

            using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                "DispatchEntryPaymentsGetByCompanyId",
                new SqlParameter("@CompanyId", String.Join(",", companyIds)),
                new SqlParameter("@StartDate", startDate),
                new SqlParameter("@EndDate", endDate),
                new SqlParameter("@Type", type)))
            {
                while (await dr.ReadAsync())
                {
                    entries.Add(new InvoicePayment(dr));
                }

                return entries;
            }
        }

        public static async Task<Collection<InvoicePayment>> GetByCompanyAsync(int companyId, DateTime startDate, DateTime endDate, int? type = null)
        {
            return await GetByCompanyAsync(new int[] { companyId }, startDate, endDate, type);
        }


        /// <summary>
        /// Get all payments associated with the specified AccountPaymentId
        /// </summary>
        /// <param name="accountPaymentId"></param>
        /// <returns></returns>
        public static Collection<InvoicePayment> GetByAccountPaymentId(int accountPaymentId)
        {
            var entries = new Collection<InvoicePayment>();

            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "DispatchEntryPaymentsGetByAccountPaymentId",
                new SqlParameter("@AccountPaymentId", accountPaymentId)))
            {
                while (dr.Read())
                {
                    entries.Add(new InvoicePayment(dr));
                }

                return entries;
            }
        }

        /// <summary>
        /// Get all payments associated with the specified AccountPaymentId
        /// </summary>
        /// <param name="accountPaymentId"></param>
        /// <returns></returns>
        public static async Task<Collection<InvoicePayment>> GetByAccountPaymentIdAsync(int accountPaymentId)
        {
            var entries = new Collection<InvoicePayment>();

            using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                "DispatchEntryPaymentsGetByAccountPaymentId",
                new SqlParameter("@AccountPaymentId", accountPaymentId)))
            {
                while (await dr.ReadAsync())
                {
                    entries.Add(new InvoicePayment(dr));
                }

                return entries;
            }
        }

        public async Async.Task Void()
        {
            await Void(null, null);
        }

        /// <summary>
        /// Void a payment (we don't allow deleting of payments, only voiding)
        /// </summary>
        public async Async.Task Void(User user = null, string requestIp = null)
        {
            SqlHelper.ExecuteNonQuery(Core.ConnectionString,
                "DispatchEntryPaymentsVoidById",
                    new SqlParameter("@DispatchEntryPaymentId", Id),
                    new SqlParameter("@UserId", user?.Id));

            var inv = await Invoice.GetByIdAsync(InvoiceId);
            if (inv != null)
            {
                await Invoice.UpdatePaymentTotal(this.InvoiceId, inv);

                if (inv.DispatchEntry != null)
                {
                    // delete the (possible) link to payment made at the time of release of an impound
                    if (inv.DispatchEntry.Impound)
                    {
                        var rp = Impounds.ImpoundReleasePayment.GetByInvoicePaymentId(Id);
                        if (rp != null)
                            rp.Delete(user);
                    }

                    var activityLogActionType = ActivityLogActionType.Void;

                    if (this.ClosedPeriodActivitySave)
                        activityLogActionType = ActivityLogActionType.ClosedAccountingPeriodActivityVoid;

                    // track void activity in history
                    await new ActivityLogItem()
                    {
                        ParentObjectId = inv.DispatchEntry.Id,
                        ParentObjectTypeId = ActivityLogType.DispatchEntry,
                        ObjectId = Id,
                        Type = ActivityLogType.Payment,
                        ActionId = (int)activityLogActionType,
                        IpAddress = requestIp ?? string.Empty,
                        UserId = user?.Id ?? 1,
                        Details = new ActivityLogItemDetail()
                        {
                            Data = new
                            {
                                PaymentId = Id,
                                Type = PaymentType.Name,
                                Amount = Amount.ToString("C")
                            }.ToJson(),
                            ActivityLogItemDetailTypeId = ActivityLogItemDetailType.RawJson
                        }
                    }.SaveAsync();

                    Entry.CacheClearById(inv.DispatchEntry.Id);

                    if (AccountPaymentId == 0)
                        await Integration.PushNotificationProvider.UpdateCall(inv.DispatchEntry.CompanyId, inv.DispatchEntry.Id);
                }
            }

        }

        /// <summary>
        /// Saves the InvoicePayment object to the data store.
        /// </summary>
        public async Async.Task Save()
        {
            await Save(null, null, null);
        }
        public async Async.Task Save(User user = null, string requestIp = null, ConcurrentBag<Task> bulkTasks = null) =>
            await Save(user, requestIp, bulkTasks, false);

        /// <summary>
        /// Save the InvoicePayment object to the data store
        /// </summary>
        /// <param name="user">(optional) the owner who is performing the action</param>
        /// <param name="requestIp">(optional) the Ip address of the user for tracking purposes</param>
        public async Task Save(User user, string requestIp, ConcurrentBag<Task> bulkTasks, bool overrideClosedPeriod)
        {
            if (Id == 0)
            {
                throw new ApplicationException("No such payment entry. Can't save " +
                    "object! (this object should have already been discarded!)");
            }
            if (InvoiceId == 0)
                throw new ApplicationException("must set InvoiceId. Did you set DispatchEntry or ImpoundId instead (which are now obsolete)?");

            if (PaymentDate != null && PaymentDate.Value <= SqlDateTime.MinValue)
                PaymentDate = null;

            if (this.Amount == 0)
                return;

            var invoice = await Invoice.GetByIdAsync(InvoiceId);
            var company = await Company.Company.GetByIdAsync(invoice.CompanyId);
            if (!overrideClosedPeriod && company.HasFeature(Generated.Features.AdvancedBilling_ClosedAccountingPeriod))
            {
                var closedPeriodOptions = ClosedPeriodOption.GetByCompanyId(company.Id);
                if (PaymentDate != null && !ClosedPeriodExtensions.IsDateOutsideOfClosedAccountingPeriod(closedPeriodOptions, PaymentDate.Value))
                {
                    throw new TowbookException($"Payment {Id} for invoice {InvoiceId} is locked by closed period settings. An override is required to modify it.");
                }
            }


            bool isNewPayment = Id == -1;

            if (Id == -1)
            {
                DbInsert();
            }
            else
            {
                DbUpdate();
            }

            AppServices.Cache.InvalidateCacheItem("payments/invoiceId/" + InvoiceId);

            var inv = await Invoice.GetByIdAsync(InvoiceId);
            if (inv != null)
            {
                await Invoice.UpdatePaymentTotal(this.InvoiceId, inv, bulkTasks);
           
                if (inv.DispatchEntry != null)
                {
                    // track void activity in history
                    if (isNewPayment)
                    {
                        ActivityLogActionType activityLogActionType = ActivityLogActionType.Create;

                        if (this.ClosedPeriodActivitySave)
                            activityLogActionType = ActivityLogActionType.ClosedAccountingPeriodActivityCreate;

                        await new ActivityLogItem()
                        {
                            ParentObjectId = inv.DispatchEntry.Id,
                            ParentObjectTypeId = ActivityLogType.DispatchEntry,
                            ObjectId = Id,
                            Type = ActivityLogType.Payment,
                            ActionId = (int)activityLogActionType,
                            IpAddress = requestIp ?? string.Empty,
                            UserId = user?.Id ?? 1,
                            Details = new ActivityLogItemDetail()
                            {
                                Data = new
                                {
                                    PaymentId = Id,
                                    Type = PaymentType?.Name,
                                    Amount = Amount.ToString("C")
                                }.ToJson(),
                                ActivityLogItemDetailTypeId = ActivityLogItemDetailType.RawJson
                            }
                        }.SaveAsync();
                    }

                    Entry.CacheClearById(inv.DispatchEntry.Id);

                    await SearchUtility.UpdateCall(inv.DispatchEntry);

                    // don't do this for an account payment, it'll flood the account.
                    if (AccountPaymentId == 0)
                        await Integration.PushNotificationProvider.UpdateCall(inv.DispatchEntry.CompanyId, inv.DispatchEntry.Id);
                }

                if (inv.AccountId != null)
                    Account.UpdateBalance(inv.AccountId.Value, inv.CompanyId);
            }

            if (this.AccountPaymentId > 0)
            {
                var p = await Payment.GetByIdAsync(AccountPaymentId);
                if (p != null && p.AccountId != inv.AccountId.GetValueOrDefault())
                {
                    Account.UpdateBalance(p.AccountId, p.CompanyId);
                }
            }
        }

        public enum ResolveDifference
        {
            ThrowError = 1,
            AdjustInvoiceWithHiddenItemIfGreaterThanTotal = 2,
            AdjustInvoiceWithHiddenItemIfLessThanTotal = 3
        }

        public async Task Save(ResolveDifference rd = ResolveDifference.ThrowError) =>
            await Save(rd, null);

        public async Task Save(ResolveDifference rd, ConcurrentBag<Task> bulkTasks) => 
            await Save(rd, bulkTasks, false);

        /// <summary>
        /// Adjusts the invoice that the payment is being applied against to reflect the payment amount.
        /// </summary>
        /// <param name="rd"></param>
        /// <param name="bulkTasks"></param>
        /// <param name="overrideClosedPeriod"></param>
        public async Task Save(ResolveDifference rd, ConcurrentBag<Task> bulkTasks, bool overrideClosedPeriod)
        {
            var ap = this.Amount;

            var inv = await Invoice.GetByIdAsync(this.InvoiceId);
            if (inv == null)
            {
                throw new Exception("Invalid InvoiceId:" + InvoiceId + ", Invoice is null.");
            }


            var company = await Company.Company.GetByIdAsync(inv.CompanyId);
            if (!overrideClosedPeriod && company.HasFeature(Generated.Features.AdvancedBilling_ClosedAccountingPeriod))
            {
                var closedPeriodOptions = ClosedPeriodOption.GetByCompanyId(inv.CompanyId);
                if (PaymentDate != null && !ClosedPeriodExtensions.IsDateOutsideOfClosedAccountingPeriod(closedPeriodOptions, PaymentDate.Value))
                {
                    throw new TowbookException($"Invoice {Id} for call {DispatchEntryId} is locked by closed period settings. An override is required to modify it.");
                }
            }

            if (rd == ResolveDifference.AdjustInvoiceWithHiddenItemIfGreaterThanTotal ||
                rd == ResolveDifference.AdjustInvoiceWithHiddenItemIfLessThanTotal)
            {
                if (inv.Total < this.Amount)
                {
                    var ii = new InvoiceItem();
                    ii.InvoiceId = this.InvoiceId;
                    ii.CustomName = "Adjustment to reflect payment difference";

                    // ifGreater, it will create a charge for the amount.
                    // ifLess, it will create a credit for the amount.
                    ii.CustomPrice = (this.Amount - inv.Total);

                    ii.Quantity = 1;
                    ii.Taxable = false;
                    ii.RateItem = await RateItem.GetByIdAsync(3);

                    // don't show this on invoices if the company has to print one 
                    // to give to the customer. this payment is being used to account for
                    // bonuses, commissions or discounts paid by the account. 
                    ii.Hidden = true;
                    inv.InvoiceItems.Add(ii);
                    await inv.SaveAsync(null, null);

                    await Entry.UpdateInvoiceStatusId(inv.Id, bulkTasks);
                }
            }
            else
            {
                if (inv.Total < this.Amount)
                    throw new TowbookException("Payment Amount of " + this.Amount.ToString("C") + " greater than " + inv.Total + " for invoiceId " + inv.Id);
            }

            await this.Save(null, null, bulkTasks);
        }

        private void DbInsert()
        {

            Id = Convert.ToInt32(SqlHelper.ExecuteScalar(Core.ConnectionString,
                "DispatchEntryPaymentsInsert",
                new SqlParameter("@InvoiceId", InvoiceId),
                new SqlParameter("@Amount", Amount),
                new SqlParameter("@Type", PaymentType?.Id),
                new SqlParameter("@ReferenceNumber", ReferenceNumber),
                new SqlParameter("@AccountPaymentId", (AccountPaymentId > 0 ? (int?)AccountPaymentId : null)),
                new SqlParameter("@OwnerUserId", OwnerUserId),
                new SqlParameter("@PaymentDate", PaymentDate),
                new SqlParameter("@ClassId", ClassId)
                ));
        }

        private void DbUpdate()
        {
            SqlHelper.ExecuteNonQuery(Core.ConnectionString,
                "DispatchEntryPaymentsUpdateById",
                    new SqlParameter("@DispatchEntryPaymentId", Id),
                    new SqlParameter("@InvoiceId", InvoiceId),
                    new SqlParameter("@Amount", Amount),
                    new SqlParameter("@Type", PaymentType?.Id),
                    new SqlParameter("@ReferenceNumber", ReferenceNumber),
                    new SqlParameter("@AccountPaymentId", (AccountPaymentId > 0 ? (int?)AccountPaymentId : null)),
                    new SqlParameter("@OwnerUserId", OwnerUserId),
                    new SqlParameter("@PaymentDate", PaymentDate),
                    new SqlParameter("@ClassId", ClassId)
                );
        }

        public async Task MarkPaymentVerified(Invoice inv = null, User user = null, string requestingIp = null)
        {
            using (var cnn = Core.GetConnection()) 
            {
                SqlMapper.Execute(cnn,
                    "UPDATE DispatchEntryPayments SET PaymentVerificationId=@PaymentVerificationId WHERE DispatchEntryPaymentId=@Id",
                    new { PaymentVerificationId, Id });
            }

            inv = inv ?? await Invoice.GetByIdAsync(InvoiceId);
            user = user ?? await User.GetByIdAsync(1);

            await Entry.UpdatePaymentVerifiedAzure(inv.CompanyId, inv.DispatchEntryId, Id, PaymentVerificationId);

            if (inv != null && inv.DispatchEntry != null && user != null)
            {
                await new ActivityLogItem()
                {
                    ParentObjectId = inv.DispatchEntry.Id,
                    ParentObjectTypeId = ActivityLogType.DispatchEntry,
                    ObjectId = Id,
                    Type = ActivityLogType.Payment,
                    ActionId = (int)ActivityLogActionType.Verified,
                    IpAddress = requestingIp ?? string.Empty,
                    UserId = user.Id,
                    Details = new ActivityLogItemDetail()
                    {
                        Data = new
                        {
                            PaymentId = Id,
                            Type = PaymentType.Name,
                            Amount = Amount.ToString("C")
                        }.ToJson(),
                        ActivityLogItemDetailTypeId = ActivityLogItemDetailType.RawJson
                    }
                }.SaveAsync();
            }

        }

        public async Task MarkPaymentUnVerified(Invoice inv = null, User user = null, string requestIp = null)
        {
            using (var cnn = Core.GetConnection())
            {
                SqlMapper.Execute(cnn,
                    "UPDATE DispatchEntryPayments SET PaymentVerificationId=NULL WHERE DispatchEntryPaymentId=@Id",
                    new { Id });
            }

            inv = inv ?? await Invoice.GetByIdAsync(InvoiceId);
            user = user ?? await User.GetByIdAsync(1);

            await Entry.UpdatePaymentVerifiedAzure(inv.CompanyId, inv.DispatchEntryId, Id, null);

            if (inv != null && inv.DispatchEntry != null && user != null)
            {
                await new ActivityLogItem()
                {
                    ParentObjectId = inv.DispatchEntry.Id,
                    ParentObjectTypeId = ActivityLogType.DispatchEntry,
                    ObjectId = Id,
                    Type = ActivityLogType.Payment,
                    ActionId = (int)ActivityLogActionType.Unverified,
                    IpAddress = requestIp ?? string.Empty,
                    UserId = user.Id,
                    Details = new ActivityLogItemDetail()
                    {
                        Data = new
                        {
                            PaymentId = Id,
                            Type = PaymentType.Name,
                            Amount = Amount.ToString("C")
                        }.ToJson(),
                        ActivityLogItemDetailTypeId = ActivityLogItemDetailType.RawJson
                    }
                }.SaveAsync();
            }
        }

        public static Collection<InvoicePayment> GetByImpoundId(int impoundId)
        {
            var Entries = new Collection<InvoicePayment>();

            if (impoundId < 1)
                throw new TowbookException("impoundId is < 1. invalid!");

            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "DispatchEntryPaymentsGetByImpoundId",
                new SqlParameter("@ImpoundId", impoundId)))
            {
                while (dr.Read())
                {
                    Entries.Add(new InvoicePayment(dr));
                }
                return Entries;
            }
        }

        public static async Task<Collection<InvoicePayment>> GetByImpoundIdAsync(int impoundId)
        {
            var Entries = new Collection<InvoicePayment>();

            if (impoundId < 1)
                throw new TowbookException("impoundId is < 1. invalid!");

            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "DispatchEntryPaymentsGetByImpoundId",
                new SqlParameter("@ImpoundId", impoundId)))
            {
                while (await dr.ReadAsync())
                {
                    Entries.Add(new InvoicePayment(dr));
                }
                return Entries;
            }
        }

        public static Collection<InvoicePayment> GetByInvoiceId(int id)
        {
            if (id < 1)
                return new Collection<InvoicePayment>();
            
            return AppServices.Cache.Get("payments/invoiceId/" + id, DateTime.Now.AddMinutes(30),
                () =>
                {
                    CacheCollection<InvoicePayment> entries = new CacheCollection<InvoicePayment>();

                    using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                        "DispatchEntryPaymentsGetByInvoiceId",
                        new SqlParameter("@InvoiceId", id)))
                    {
                        while (dr.Read())
                        {
                            entries.Items.Add(new InvoicePayment(dr));
                        }
                        return entries;
                    }
                }).ItemsOrEmpty();
        }

        public static Collection<InvoicePayment> GetByInvoiceIds(IEnumerable<int> invoiceIds)
        {
            var entries = new Collection<InvoicePayment>();

            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString, CommandType.Text,
                $"SELECT * FROM DispatchEntryPayments WHERE InvoiceId IN ({String.Join(",", invoiceIds)})"))
            {
                while (dr.Read())
                {
                    var p = new InvoicePayment(dr);
                    entries.Add(p);

                    AppServices.Cache.AddToPerRequestCache("payments/invoiceId/" + p.InvoiceId, p);
                }
                return entries;
            }
        }


        public static IDictionary<int, object> GetByDispatchEntryIds(int[] dispatchEntryIds)
        {
            var resultData = new Dictionary<int, object>();
            List<KeyValuePair<int, object>> dispatchEntryPayments = new List<KeyValuePair<int, object>>();


            var parameters = new List<string>();
            var p = new List<SqlParameter>();
            int i = 0;
            foreach (var row in dispatchEntryIds)
            {
                i++;
                parameters.Add("@P" + i);
                p.Add(new SqlParameter("@P" + i, row));
            }

            // Script creation
            var sql = @"
                SELECT 
                    DEP.*,
                    I.DispatchEntryId as InvoiceDispatchEntryId, 
                    AP.Notes as AccountPaymentNotes, 
                    AP.ReferenceNumber as AccountPaymentReference, 
                    AP.Type as AccountPaymentType, 
                    AP.Amount as AccountPaymentAmount
                FROM 
	                DispatchEntryPayments DEP WITH (nolock)
                      INNER JOIN Invoices I  WITH (nolock)on I.InvoiceId=DEP.InvoiceId
                      LEFT OUTER JOIN AccountPayments AP  WITH (nolock) on AP.AccountPaymentId=DEP.AccountPaymentId
                WHERE 
	                I.DispatchEntryId IN (" + String.Join(",", parameters) + @") 
                    AND DEP.isVoid = 0";

            using (var dr = SqlHelper.ExecuteReader(Core.ConnectionString, System.Data.CommandType.Text, sql, p.ToArray()))
            {
                while (dr.Read())
                {
                    var dispatchEntryId = dr.GetValueOrNull<int>("InvoiceDispatchEntryId");

                    if (dispatchEntryId.HasValue) {
                        var payment = new InvoicePayment(dr);
                        var accountPayment = dr.GetValue<decimal>("AccountPaymentAmount");
                        var accountPaymentType = dr.GetValueOrNull<int>("AccountPaymentType");
                        var accountPaymentReferenceNumber = dr.GetValue<string>("AccountPaymentReference");
                        var accountPaymentNotes = dr.GetValue<string>("AccountPaymentNotes");
                        var paymentVerificationId = dr.GetValue<string>("PaymentVerificationId");

                        if (payment != null)
                        {
                            dynamic d = new ExpandoObject();

                            // DispatchEntryPayment stuff...
                            d.PaymentId = payment.Id;
                            d.AccountPaymentId = payment.AccountPaymentId;
                            d.InvoiceId = payment.InvoiceId;
                            d.Amount = payment.Amount;
                            d.PaymentDate = payment.PaymentDate;
                            d.PaymentType = payment.PaymentType;
                            d.ReferenceNumber = payment.ReferenceNumber;
                            d.OwnerUserId = payment.OwnerUserId;
                            d.PaymentVerificationId = payment.PaymentVerificationId;

                            // Account payment stuff...
                            d.AccountPaymentAmount = accountPayment;
                            d.AccountPaymentType = accountPaymentType;
                            d.AccountReferenceNumber = accountPaymentReferenceNumber;
                            d.AccounPaymentNotes = accountPaymentNotes;

                            dispatchEntryPayments.Add(new KeyValuePair<int, object> (dispatchEntryId.Value, d));
                        }
                    }
                }
            }

            foreach (var id in dispatchEntryIds)
                resultData.Add(id, dispatchEntryPayments.Where(w => w.Key == id).Select(s => s.Value));

            return resultData;
        }

        public static Collection<InvoicePayment> GetByDispatchEntryIds(IEnumerable<int> dispatchEntryIds, DateTime? paymentDateLessThan)
        {
            var entries = new Collection<InvoicePayment>();

            foreach (var batch in dispatchEntryIds.OrderBy(o => o).Batch(500))
            {
                using (var dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                    "DispatchEntryPaymentsGetByDispatchEntryIdArray",
                    new SqlParameter("@DispatchEntryIds", string.Join(",", batch)),
                    new SqlParameter("@PaymentEndDate", paymentDateLessThan)))
                {
                    while (dr.Read())
                    {
                        entries.Add(new InvoicePayment(dr));
                    }
                }
            }

            return entries;
        }

        public static async Task<Collection<InvoicePayment>> GetByDispatchEntryIdsAsync(IEnumerable<int> dispatchEntryIds, DateTime? paymentDateLessThan)
        {
            var entries = new Collection<InvoicePayment>();

            foreach (var batch in dispatchEntryIds.OrderBy(o => o).Batch(500))
            {
                using (var dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                    "DispatchEntryPaymentsGetByDispatchEntryIdArray",
                    new SqlParameter("@DispatchEntryIds", string.Join(",", batch)),
                    new SqlParameter("@PaymentEndDate", paymentDateLessThan)))
                {
                    while (await dr.ReadAsync())
                    {
                        entries.Add(new InvoicePayment(dr));
                    }
                }
            }

            return entries;
        }

        public static Collection<IncomeData.DetailData> GetPaymentVerificationReportData(int[] companyIds,
            DateTime startDate,
            DateTime endDate,
            int[] accountIds,
            int[] paymentTypeIds,
            int filterByInvoiceDateByPaymentDateByVerifiedDate = 0,
            int? filterVoid = 0,
            int? recordedBy = null,
            int? filterVerified = null,
            int? verifiedBy = null,
            bool? filterImpound = null,
            int? accountManagerUserId = null)
        {

            // Account ids filter
            var sql = @"SELECT        
                            Type=Coalesce(AP.Type, DEP.Type),
                            DEP.Amount,      
                            DEP.ReferenceNumber,      
                            DE.DispatchEntryId,  
                            DE.CompanyId,  
                            DE.CallNumber,      
                            DEP.AccountPaymentId,      
                            PaymentDate=DEP.PaymentDate,      
                            DEP.CreateDate,      
                            DEP.OwnerUserId,      
                            DEP.ClassId,  
                            DEP.IsVoid,
			                DE.CompletionTime,
			                DEP.DispatchEntryPaymentId,
			                DEP.PaymentVerificationId,
			                PV.UserId as PaymentVerificationUserId,
			                PV.CreateDate as PaymentVerificationCreateDate,
                            DE.Impound
                        FROM
                            DispatchEntryPayments DEP WITH (nolock)
                                INNER JOIN Invoices I WITH (nolock) ON I.InvoiceId=DEP.InvoiceId
                                INNER JOIN DispatchEntries DE WITH (nolock) ON DE.DispatchEntryId=I.DispatchEntryId
                                LEFT JOIN AccountPayments AP  WITH (nolock) ON AP.AccountPaymentId=DEP.AccountPaymentId AND AP.CompanyId=I.CompanyId
		                        LEFT JOIN PaymentVerifications PV  WITH (nolock) ON PV.PaymentVerificationId=DEP.PaymentVerificationId
                        WHERE
                            DE.Deleted=0 AND
                            (DEP.AccountPaymentId IS NULL OR DEP.Type=11 or AP.Type != 11 or COALESCE(Ap.Type,-1) = -1) ";


            var parameters = new List<string>();
            var p = new List<SqlParameter>();
            
            int i = 0;
            foreach (var row in companyIds)
            {
                i++;
                parameters.Add("@P" + i);
                p.Add(new SqlParameter("@P" + i, row));
            }

            sql += " AND DE.CompanyId IN(" + string.Join(",", parameters) + ")";

            switch(filterByInvoiceDateByPaymentDateByVerifiedDate)
            {
                case 1: // payment recorded date
                    sql += " AND DEP.CreateDate >= @StartDate AND DEP.CreateDate < @EndDate";
                    break;

                case 2: // verification date
                    sql += @" AND DEP.PaymentVerificationId=PV.PaymentVerificationId AND 
				                PV.CreateDate >= @StartDate AND 
				                PV.CreateDate < @EndDate";
                    break;

                default: // payment date
                    sql += " AND DEP.PaymentDate >= @StartDate AND DEP.PaymentDate < @EndDate";
                    break;
            }
            p.Add(new SqlParameter("@StartDate", startDate));
            p.Add(new SqlParameter("@EndDate", endDate));

            if (accountIds != null && accountIds.Count() > 0)
            {
                i = 0;
                parameters.Clear();

                foreach (var row in accountIds)
                {
                    i++;
                    parameters.Add("@ACC" + i);
                    p.Add(new SqlParameter("@ACC" + i, row));
                }

                sql += " AND (DE.AccountId in (" + string.Join(",", parameters) + ") OR (I.AccountId IS NOT NULL AND I.AccountId in (" + string.Join(",", parameters) + ")))";
            }

            if (recordedBy != null)
            {
                sql += " AND DEP.OwnerUserId=@OwnerUserId";
                p.Add(new SqlParameter("@OwnerUserId", recordedBy.Value));
            }

            if (verifiedBy != null)
            {
                sql += @" AND DEP.PaymentVerificationId=PV.PaymentVerificationId 
                            AND PV.UserId=@VerifiedByUserId";
                
                p.Add(new SqlParameter("@VerifiedByUserId", verifiedBy.Value));
            }

            if (filterVerified != null)
            {
                switch(filterVerified.Value)
                {
                    case 0:
                        sql += " AND DEP.PaymentVerificationId IS NULL";
                        break;

                    case 1:
                        sql += " AND DEP.PaymentVerificationId IS NOT NULL";
                            break;
                }
            }

            if(paymentTypeIds != null && paymentTypeIds.Count() > 0)
            {
                i = 0;
                parameters.Clear();

                foreach (var row in paymentTypeIds)
                {
                    i++;
                    parameters.Add("@PT" + i);
                    p.Add(new SqlParameter("@PT" + i, row));
                }

                sql += " AND Coalesce(AP.Type, DEP.Type) IN(" + string.Join(",", parameters) + ")";
            }

            if(filterVoid != null)
            {
                if (filterVoid.Value == 0) // not voided
                    sql += " AND DEP.IsVoid=0";
                else if (filterVoid.Value == 1) // voided
                    sql += " AND DEP.IsVoid=1";
            }

            if (filterImpound != null)
            {
                if (filterImpound.Value == true) // is impound
                    sql += " AND DE.Impound=0";
                else if (filterImpound.Value == false) // is not impound
                    sql += " AND DE.Impound=1";
            }

            if(accountManagerUserId.GetValueOrDefault() > 0)
            {
                sql += " AND(DE.DispatchEntryId IN (SELECT TOP 1 DispatchEntryId FROM DispatchEntryAttributeValues WITH (NOLOCK) WHERE DispatchEntryId=DE.DispatchEntryId AND DispatchEntryAttributeId=74 AND Value=CAST(@AccountManagerUserId AS VARCHAR(255))))";
                p.Add(new SqlParameter("@AccountManagerUserId", accountManagerUserId));
            }

            // tell it not to use cached query plan.. our queries are too different because our parameters vary so much
            // one might be one companyid, another might be 120 companyIds, for example.
            sql += " OPTION (OPTIMIZE FOR UNKNOWN) ";

            Collection<IncomeData.DetailData> result = new Collection<IncomeData.DetailData>();

            using (SqlDataReader sq = SqlHelper.ExecuteReader(Core.ConnectionString,
                System.Data.CommandType.Text,
                sql,
                p.ToArray()))
            {
                while (sq.Read())
                {
                    result.Add(new IncomeData.DetailData()
                    {
                        Type = sq.GetValue<int>("Type"),
                        PaymentString = Entry.GetPaymentString(sq.GetValue<int>("Type")),
                        ReferenceNumber = sq.GetValue<string>("ReferenceNumber"),
                        Amount = sq.GetValue<decimal>("Amount"),
                        DispatchEntryId = sq.GetValue<int>("DispatchEntryId"),
                        CompanyId = sq.GetValue<int>("CompanyId"),
                        CallNumber = sq.GetValue<int>("Callnumber"),
                        AccountPaymentId = sq.GetValue<int>("AccountPaymentId"),
                        PaymentDate = sq.GetValue<DateTime>("PaymentDate"),
                        CreateDate = sq.GetValue<DateTime>("CreateDate"),
                        OwnerUserId = sq.GetValue<int>("OwnerUserId"),
                        ClassId = sq.GetValue<int>("ClassId"),
                        IsVoid = sq.GetValue<bool>("IsVoid"),
                        CompletionTime = sq.GetValue<DateTime>("CompletionTime"),
                        PaymentVerificationId = sq.GetValueOrNull<int>("PaymentVerificationId"),
                        DispatchEntryPaymentId = sq.GetValueOrNull<int>("DispatchEntryPaymentId"),
                        PaymentVerificationUserId = sq.GetValueOrNull<int>("PaymentVerificationUserId"),
                        PaymentVerificationCreateDate = sq.GetValueOrNull<DateTime>("PaymentVerificationCreateDate"),
                        Impound = sq.GetValueOrNull<bool>("Impound")
                    });
                }
            }

            return result;
        }

        public static Collection<UserPayment> GetIncomeByUserReport(int[] companyIds, DateTime startDate, DateTime endDate, int dateColumn)
        {
            var userPayments = new Collection<UserPayment>();

            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                    "DispatchPaymentReportsGetTotalsByUser",
                    new SqlParameter("@CompanyId", String.Join(",", companyIds)),
                    new SqlParameter("@StartDate", startDate),
                    new SqlParameter("@EndDate", endDate),
                    new SqlParameter("@DateColumn", dateColumn)))
            {
                while (dr.Read())
                {
                    userPayments.Add(new UserPayment()
                    {
                        UserId = dr.GetValue<int>("UserId"),
                        UserName = dr.GetValue<string>("UserName"),
                        UserTypeId = dr.GetValue<int>("UserTypeId"),
                        PaymentType = (PaymentType)dr.GetValue<int>("PaymentType"),
                        CallId = dr.GetValue<int>("DispatchEntryId"),
                        CallNumber = dr.GetValue<int>("CallNumber"),
                        InvoiceNumber = dr.GetValue<string>("InvoiceNumber"),
                        PoNumber = dr.GetValue<string>("PoNumber"),
                        PaymentDate = dr.GetValue<DateTime>("PaymentDate"),
                        Amount = dr.GetValue<decimal>("Amount"),
                        PaymentVerificationId = dr.GetValueOrNull<int>("PaymentVerificationId"),
                        PaymentVerificationUserId = dr.GetValueOrNull<int>("PaymentVerificationUserId"),
                        PaymentVerificationDate = dr.GetValueOrNull<DateTime>("PaymentVerificationDate")
                    });
                }

                return userPayments;
            }
        }
    }

    public class UserPayment
    {
        public int UserId { get; set; }
        public string UserName { get; set; }
        public int UserTypeId { get; set; }
        public PaymentType PaymentType { get; set; }
        public int CallId { get; set; }
        public int CallNumber { get; set; }
        public string InvoiceNumber { get; set; }
        public string PoNumber { get; set; }
        public DateTime PaymentDate { get; set; }
        public decimal Amount { get; set; }
        public int? PaymentVerificationId { get; set; }
        public int? PaymentVerificationUserId { get; set; }
        public DateTime? PaymentVerificationDate { get; set; }
    }
}
