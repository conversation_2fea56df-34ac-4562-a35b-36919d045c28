using Extric.Towbook.Utility;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook
{
    public static class EnumExtensionMethods
    {
        public static class Enum<T> where T : Enum
        {
            public static System.Collections.Generic.IEnumerable<T> GetAllValuesAsIEnumerable()
            {
                return Enum.GetValues(typeof(T)).Cast<T>();
            }
        }

        public class EnumTypeDefinitionModel
        {
            public int Id
            {
                get
                {
                    return (_enum != null ? Convert.ToInt32(_enum) : 0);
                }
            }
            public string Name
            {
                get
                {
                    return (_enum != null ? _enum.ToDescription() : string.Empty);
                }
            }

            private Enum _enum;

            public EnumTypeDefinitionModel(Enum inputEnum)
            {
                _enum = inputEnum;
            }
        }
    }
}
