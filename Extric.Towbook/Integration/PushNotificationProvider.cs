using System;
using System.Collections.Generic;
using System.Linq;
using Extric.Towbook.Company;
using Extric.Towbook.Dispatch;
using Extric.Towbook.Utility;
using Newtonsoft.Json;
using PusherServer;
using Newtonsoft.Json.Serialization;
using System.Threading.Tasks;
using NLog;

namespace Extric.Towbook.Integration
{
    public class PushNotificationProvider
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        public const bool EnableVerboseLogging = false;

        public static string GetChannelName(int companyId)
        {
            return "private-TWBK_Client_" + companyId;
        }

        public static string GetChannelName(User user) => GetChannelName(user.CompanyId);

        public static string GetCallChannelName(int dispatchEntryId, int userId, int clientVersionId) => 
            "private-TWBK_Client_" + dispatchEntryId + "-" + userId + "-" + clientVersionId + "-call";

        public static string GetEventNotificationChannelName(int companyId, int userId) =>
            $"{GetChannelName(companyId)}-{userId}-eventNotification";

        public static void ReplaceConfiguration(string applicationId, string applicationKey, string applicationSecret, string cluster)
        {
            ApplicationId = applicationId;
            ApplicationKey = applicationKey;
            ApplicationSecret = applicationSecret;
            Cluster = cluster;

            PusherUtc = new Pusher(ApplicationId, ApplicationKey, ApplicationSecret, new PusherOptions()
            {
                Cluster = Cluster,
                JsonSerializer = new UtcSerializer(true),
                RestClientTimeout = TimeSpan.FromMilliseconds(3000)
            });

            Pusher = new Pusher(ApplicationId, ApplicationKey, ApplicationSecret, new PusherOptions()
            {
                RestClientTimeout = TimeSpan.FromMilliseconds(3000),
                Cluster = Cluster
            });
        }

        public static string ApplicationId { get; private set; } = "25018";
        public static string ApplicationKey { get; private set; } = "00d0fb70749a0a4fd6f9";
        public static string ApplicationSecret { get; private set; } = "c72e0a16fef169ea6f97";
        public static string Cluster { get; private set; } = "mt1";

        /// <summary>
        /// Sends a push notification to clients via Pusher. 
        /// </summary>
        /// <param name="companyId">CompanyId to send the request for</param>
        /// <param name="id">id of the DispatchEntry/call referenced.</param>
        /// <param name="extra">Optional parameter that provides extra detail as to what the update 
        /// action was; update, delete, complete, etc. Clients can ignore this entirely and jus treat
        /// all updates as a request to update the list of calls. Clients with a fuller implementation,
        /// can monitor the extra field and act accordingly.</param>
        public static async Task UpdateCall(int companyId, int id, string extra = "update")
        {
            try
            {
                var companiesToSendTo = new List<int> { companyId };

                var sc = SharedCompany.GetByCompanyId(companyId);

                if (sc.Any())
                {
                    foreach (var x in sc)
                    {
                        if (!companiesToSendTo.Contains(x.CompanyId))
                            companiesToSendTo.Add(x.CompanyId);

                        // 35210 has users in multiple companies with their primarycompanyid set to the child companyid
                        // so we have to do this.
                        if (x.CompanyId == 35210)
                        {
                            if (!companiesToSendTo.Contains(x.SharedCompanyId))
                                companiesToSendTo.Add(x.SharedCompanyId);
                        }
                    }
                }

                foreach (var batch in companiesToSendTo.Select(o => "private-TWBK_Client_" + o).Batch(99))
                {
                    await TriggerAsync(companyId,
                        batch.ToArray(), "call_update", new
                        {
                            callId = id,
                            extra = extra
                        });
                }
            }
            catch
            {
                // silently ignore push error... sometimes Pusher is down and returns 500 errors.
                // TODO: we should log this somewhere?
            }
        }

        public static async Task NotifyCallEdit(int callId, int activeUserId, bool isActive, int clientVersionId)
        {
            try
            {
                Platform.ClientVersion cv = Platform.ClientVersion.GetById(clientVersionId);

                if(cv != null)
                {
                    var user = await User.GetByIdAsync(activeUserId);
                    if (user != null)
                    {

                        await Push(GetChannelName(user.CompanyId), "call_modify", new
                        {
                            id = callId,
                            userId = activeUserId,
                            active = isActive,
                            client = cv.Type.ToString().ToLowerInvariant()
                        });
                    }
                }
            }
            catch (Exception e)
            {
                // silently ignore push error... sometimes Pusher is down and returns 500 errors.
                // TODO: we should log this somewhere?
                System.Diagnostics.Debug.WriteLine("Pusher Exception during call_modify (active users)", e.ToJson());
            }
        }

        public static async Task UpdateSticker(int companyId, int id, string extra = "update")
        {
            try
            {
                List<int> companiesToSendTo = new List<int>();

                companiesToSendTo.Add(companyId);
                companiesToSendTo.AddRange(SharedCompany.GetByCompanyId(companyId).Where(o => o.SharedCompanyId != companyId).Select(o => o.SharedCompanyId));

                foreach (int sendToId in companiesToSendTo)
                {
                    await TriggerAsync(sendToId, "private-TWBK_Client_" + sendToId, "sticker_update", new
                    {
                        stickerId = id,
                        extra = extra
                    });
                }
            }
            catch (Exception e)
            {
                // silently ignore push error... sometimes Pusher is down and returns 500 errors.
                // TODO: we should log this somewhere?
                System.Diagnostics.Debug.WriteLine("Pusher Exception during sticker update", e.ToJson());
            }
        }

        private sealed class InsertEntryNoteModel
        {
            public int callId { get; set; }
            public int noteId { get; set; }
            public string extra { get; set; }
        }

        public static async Task InsertEntryNote(int companyId, int noteId, int dispatchEntryId, string extra = "insert")
        {
            try
            {
                var companiesToSendTo = new List<int>();

                companiesToSendTo.Add(companyId);

                var sc = SharedCompany.GetByCompanyId(companyId);
                foreach (var x in sc)
                {
                    if (!companiesToSendTo.Contains(x.CompanyId))
                    {
                        companiesToSendTo.Add(x.CompanyId);
                        break;
                    }
                }

                List<Task> tasks = new List<Task>();
                foreach (int sendToId in companiesToSendTo)
                {
                    tasks.Add(TriggerAsync(sendToId, "private-TWBK_Client_" + sendToId, "entryNote_insert_" + dispatchEntryId, new InsertEntryNoteModel
                    {
                        callId = dispatchEntryId,
                        noteId = noteId,
                        extra = extra
                    }));
                }
                
                await Task.WhenAll(tasks);
            }
            catch (Exception e)
            {
                // silently ignore push error... sometimes Pusher is down and returns 500 errors.
                // TODO: we should log this somewhere?
                System.Diagnostics.Debug.WriteLine("Pusher Exception during entryNote_insert", e.ToJson());
            }
        }

        public static async Task InsertStickerNote(int companyId, int noteId, int stickerId, string extra = "insert")
        {
            try
            {
                var companiesToSendTo = new List<int>();

                companiesToSendTo.Add(companyId);

                var sc = SharedCompany.GetByCompanyId(companyId);
                foreach (var x in sc)
                {
                    if (!companiesToSendTo.Contains(x.CompanyId))
                    {
                        companiesToSendTo.Add(x.CompanyId);
                        break;
                    }
                }

                foreach (int sendToId in companiesToSendTo)
                {
                    await TriggerAsync(sendToId, "private-TWBK_Client_" + sendToId, "stickerNote_insert_" + stickerId, new
                    {
                        stickerId = stickerId,
                        noteId = noteId,
                        extra = extra
                    });
                }
            }
            catch (Exception e)
            {
                // silently ignore push error... sometimes Pusher is down and returns 500 errors.
                // TODO: we should log this somewhere?
                System.Diagnostics.Debug.WriteLine("Pusher Exception during sticker note insert", e.ToJson());
            }
        }
        
        public static async Task CallInvoicePaymentUpdate(int companyId, int dispatchEntryId, int invoiceId, int invoicePaymentId, int newStatusId)
        {
            try
            {
                var companiesToSendTo = new List<int> { companyId };

                var sc = SharedCompany.GetByCompanyId(companyId);
                foreach (var x in sc)
                {
                    if (!companiesToSendTo.Contains(x.CompanyId))
                    {
                        companiesToSendTo.Add(x.CompanyId);
                        break;
                    }
                }

                foreach (int sendToId in companiesToSendTo)
                {
                    await TriggerAsync(sendToId, "private-TWBK_Client_" + sendToId, "invoice_payment_update", new
                    {
                        dispatchEntryId = dispatchEntryId,
                        invoiceId = invoiceId,
                        invoicePaymentId = invoicePaymentId,
                        statusId = newStatusId
                    });
                }
            }
            catch (Exception e)
            {
                // silently ignore push error... sometimes Pusher is down and returns 500 errors.
                // TODO: we should log this somewhere?
                System.Diagnostics.Debug.WriteLine("Pusher Exception during sticker note insert", e.ToJson());
            }
        }
        
        public static async Task SquareTerminalCheckoutUpdate(int companyId, int dispatchEntryId, string checkoutId, string deviceId, string status)
        {
            try
            {
                var companiesToSendTo = new List<int>();

                companiesToSendTo.Add(companyId);

                var sc = SharedCompany.GetByCompanyId(companyId);
                foreach (var x in sc)
                {
                    if (!companiesToSendTo.Contains(x.CompanyId))
                    {
                        companiesToSendTo.Add(x.CompanyId);
                        break;
                    }
                }

                foreach (int sendToId in companiesToSendTo)
                {

                    await TriggerAsync(sendToId, "private-TWBK_Client_" + sendToId, "integration_square_terminal_checkout_update", new
                    {
                        dispatchEntryId,
                        checkoutId,
                        deviceId,
                        status
                    });
                }

            }
            catch (Exception e)
            {
                System.Diagnostics.Debug.WriteLine("Pusher Exception during square terminal checkout update", e.ToJson());
            }
        }

        public static async Task SquareDeviceCodePaired(int companyId, string deviceCodeId, string deviceId, string status)
        {
            try
            {

                var companiesToSendTo = new List<int>();

                companiesToSendTo.Add(companyId);

                var sc = SharedCompany.GetByCompanyId(companyId);
                foreach (var x in sc)
                {
                    if (!companiesToSendTo.Contains(x.CompanyId))
                    {
                        companiesToSendTo.Add(x.CompanyId);
                        break;
                    }
                }

                foreach (int sendToId in companiesToSendTo)
                {
                    await TriggerAsync(sendToId, "private-TWBK_Client_" + sendToId, "integration_square_device_code_paired", new
                    {
                        deviceCodeId,
                        deviceId,
                        status
                    });
                }
            }
            catch (Exception e)
            {
                System.Diagnostics.Debug.WriteLine("Pusher Exception during square device code paring", e.ToJson());
            }
        }

        public static async Task AcknowlodgeHeartbeat(int companyId, string extra = "ack_heartbeat")
        {
            try
            {
                var companiesToSendTo = new List<int>();

                companiesToSendTo.Add(companyId);
                companiesToSendTo.AddRange(SharedCompany.GetByCompanyId(companyId).Where(o => o.SharedCompanyId != companyId).Select(o => o.SharedCompanyId));

                foreach (int sendToId in companiesToSendTo)
                {
                    await TriggerAsync(sendToId, "private-TWBK_Client_" + sendToId, "ack_heartbeat", null);
                }
            }
            catch (Exception e)
            {
                // silently ignore push error... sometimes Pusher is down and returns 500 errors.
                // TODO: we should log this somewhere?
                System.Diagnostics.Debug.WriteLine("Pusher Exception during ack_hearbeat", e.ToJson());
            }
        }

        public static async Task UpdateCallRequestStatus(int companyId, int callRequestId, CallRequestStatus status, string extra = "call_statusChange")
        {
            try
            {
                var companiesToSendTo = new List<int> { companyId };

                if (companyId == 4817 || companyId == 5406)
                {
                    companiesToSendTo.AddRange(
                        SharedCompany.GetByCompanyId(companyId)
                            .Where(o => o.SharedCompanyId != companyId)
                            .Select(o => o.SharedCompanyId));
                }
                else
                {
                    foreach (var x in SharedCompany.GetByCompanyId(companyId))
                    {
                        if (!companiesToSendTo.Contains(x.CompanyId))
                        {
                            companiesToSendTo.Add(x.CompanyId);
                            break;
                        }
                    }
                }

                foreach (int sendToId in companiesToSendTo)
                {
                    await TriggerAsync(sendToId, "private-TWBK_Client_" + sendToId, "callRequest_statusChanged", new { callRequestId = callRequestId, status = status });
                }
            }
            catch (Exception e)
            {
                // silently ignore push error... sometimes Pusher is down and returns 500 errors.
                // TODO: we should log this somewhere?
                System.Diagnostics.Debug.WriteLine("Pusher Exception during callRequest_statusChanged", e.ToJson());
            }
        }

        public static async Task<ITriggerResult> TriggerAsync(int companyId, 
            string channelName, string eventName, object data, ITriggerOptions options = null)
        {
            if (!EnableVerboseLogging)
            {
                return await Pusher.TriggerAsync(new string[1] { channelName }, eventName, data, options).ConfigureAwait(continueOnCapturedContext: false);
            }
            else
            {
                var lei = new LogEventInfo();
                lei.Level = LogLevel.Info;

                lei.Message = "Logged Pusher Event";
                lei.Properties["type"] = "Pusher";
                lei.Properties["commitId"] = Core.GetCommitId();
                lei.Properties["companyId"] = companyId;

                var c = Company.Company.GetById(companyId);

                if (c != null)
                    lei.Properties["companyName"] = c.Name;

                lei.Properties["stackTrace"] = Environment.StackTrace;
                lei.Properties["data"] = new { channelName, eventName, data }.ToJson();

                try
                {
                    return await Pusher.TriggerAsync(new string[1] { channelName }, eventName, data, options).ConfigureAwait(continueOnCapturedContext: false);
                }
                catch (Exception y)
                {
                    lei.Properties["exception"] = y;
                    lei.Level = LogLevel.Error;
                    throw;
                }
                finally
                {
                    logger.Log(lei);
                }
            }
        }


        public static async Task<ITriggerResult> TriggerAsync(int firstCompanyId,
            string[] channelNames, string eventName, object data, ITriggerOptions options = null)
        {
            if (!EnableVerboseLogging)
            {

                return await Pusher.TriggerAsync(channelNames, eventName, data, options).ConfigureAwait(continueOnCapturedContext: false);
            }
            else
            {
                var lei = new LogEventInfo();
                lei.Level = LogLevel.Info;

                lei.Properties["type"] = "Pusher";
                lei.Properties["commitId"] = Core.GetCommitId();
                lei.Properties["companyId"] = firstCompanyId;

                var c = Company.Company.GetById(firstCompanyId);

                if (c != null)
                    lei.Properties["companyName"] = c.Name;

                lei.Properties["stackTrace"] = Environment.StackTrace;
                lei.Properties["data"] = new { channelNames, eventName, data }.ToJson();

                try
                {
                    return await Pusher.TriggerAsync(channelNames, eventName, data, options).ConfigureAwait(continueOnCapturedContext: false);
                }
                catch (Exception y)
                {
                    lei.Properties["exception"] = y;
                    lei.Level = LogLevel.Error;
                    throw;
                }
                finally
                {
                    logger.Log(lei);
                }
            }
        }



        public static async Task UpdateQuote(int companyId, Guid id, string extra = "update")
        {
            try
            {
                List<int> companiesToSendTo = new List<int>();

                companiesToSendTo.Add(companyId);
                companiesToSendTo.AddRange(SharedCompany.GetByCompanyId(companyId).Where(o => o.SharedCompanyId != companyId).Select(o => o.SharedCompanyId));

                foreach (int sendToId in companiesToSendTo)
                {
                    await TriggerAsync(sendToId, "private-TWBK_Client_" + sendToId, "quote_update", new
                    {
                        id = id,
                        extra = extra
                    });
                }
            }
            catch (Exception e)
            {
                // silently ignore push error... sometimes Pusher is down and returns 500 errors.
                // TODO: we should log this somewhere?
                System.Diagnostics.Debug.WriteLine("Pusher Exception during quote update", e.ToJson());
            }
        }

        public static async Task<bool> Push(int companyId, string eventName, object contents = null)
        {
            try
            {
                var companiesToSendTo = new List<int>();

                companiesToSendTo.Add(companyId);

                if (companyId == 4817 || companyId == 5406)
                {
                    // weird situation ... this needs to be able to be handled with preferences but for now,
                    // they dont wnat 4817's call requests showing in 5406 or vice versa.
                    companiesToSendTo.AddRange(SharedCompany.GetByCompanyId(companyId).Where(o => o.SharedCompanyId != companyId).Select(o => o.SharedCompanyId));
                }
                else
                {
                    var sc = SharedCompany.GetByCompanyId(companyId);
                    foreach (var x in sc)
                    {
                        if (!companiesToSendTo.Contains(x.CompanyId))
                        {
                            companiesToSendTo.Add(x.CompanyId);
                        }

                        if (sc.Length < 15)
                        {
                            if (!companiesToSendTo.Contains(x.SharedCompanyId))
                                companiesToSendTo.Add(x.SharedCompanyId);
                        }
                    }
                }

                foreach (int sendToId in companiesToSendTo)
                {
                    //Console.WriteLine(new { c = "private-TWBK_Client_" + sendToId, eventName, contents }.ToJson(true));
                    await TriggerAsync(sendToId, "private-TWBK_Client_" + sendToId, eventName, contents);
                }
                return true;
            }
            catch (Exception ex)
            {
                if (Extric.Towbook.Web.HttpContextFactory.Instance == null)
                    Console.WriteLine(ex.ToString());

                // silently ignore push error... sometimes Pusher is down and returns 500 errors.
                // TODO: we should log this somewhere?
                return false;
            }
        }
        public static async Task PushExact(int companyId, string eventName, object contents = null)
        {
            try
            {
                var companiesToSendTo = new List<int>();

                companiesToSendTo.Add(companyId);

                foreach (int sendToId in companiesToSendTo)
                {
                    await TriggerAsync(sendToId, "private-TWBK_Client_" + sendToId, eventName, contents);
                }
            }
            catch (Exception ex)
            {
                if (Extric.Towbook.Web.HttpContextFactory.Instance == null)
                    Console.WriteLine(ex.ToString());

                // silently ignore push error... sometimes Pusher is down and returns 500 errors.
                // TODO: we should log this somewhere?
            }
        }

        public class UtcSerializer : ISerializeObjectsToJson
        {
            private bool camelCase;
            public UtcSerializer(bool camelCase)
            {
                this.camelCase = camelCase;
            }
            public string Serialize(object objectToSerialize)
            {
                var jss = new JsonSerializerSettings();
                jss.Converters.Add(new ForceUtcJsonConverter());
                jss.NullValueHandling = NullValueHandling.Ignore;
                if (camelCase)
                    jss.ContractResolver = new CamelCasePropertyNamesContractResolver();
                return JsonConvert.SerializeObject(objectToSerialize, jss);
            }
        }

        public class PusherResult
        {
            public System.Net.HttpStatusCode StatusCode { get; set; }
        }

        private static Pusher PusherUtc { get; set; } = new Pusher(ApplicationId, ApplicationKey, ApplicationSecret, new PusherOptions()
        {
            Cluster = Cluster,
            JsonSerializer = new UtcSerializer(true),
            RestClientTimeout = TimeSpan.FromMilliseconds(3000)
        });

        private static Pusher Pusher { get; set; } = new Pusher(ApplicationId, ApplicationKey, ApplicationSecret, new PusherOptions()
        {
            RestClientTimeout = TimeSpan.FromMilliseconds(3000),
            Cluster = Cluster
        });

        public static async Task<PusherResult> Push(string channel, string eventName, object contents, bool convertToUtc = false, bool camelCase = false)
        {
            try
            {
                var r = await (convertToUtc ? PusherUtc : Pusher).TriggerAsync(channel, eventName, contents);
                return new PusherResult() { StatusCode = r.StatusCode };
            }
            catch (Exception r)
            {
                Console.WriteLine(r.ToJson(true));
                // silently ignore push error... sometimes Pusher is down and returns 500 errors.
                // TODO: we should log this somewhere?
                return null;
            }
        }

        public static async Task RequestLocationUpdate(int companyId, int? trackingId = null)
        {
            await Push(companyId, "location_update", new { RequestDate = DateTime.Now, TrackingId = trackingId });
        }

        public static async Task ReportSubcontractorCallGps(int companyId, int callId, int subcontractorCompanyId, UserLocationHistoryItem ulhi)
        {
            await Push(Gps.RealTimeService.GetChannelForGps(companyId), "subcontractor_location_report", new
            {
                userId = ulhi.UserId,
                latitude = ulhi.Latitude,
                longitude = ulhi.Longitude,
                time = ulhi.Timestamp,
                callId = callId,
                subcontractorCompanyId = subcontractorCompanyId
            }, true, true);
        }

        public static async Task UpdateLoginStatus(int companyId, int accountId, int loginStatus)
        {
            // 1=logged out
            // 3=logged in

            await Push(companyId, "account_ddLoginStatus", new
            {
                accountId = accountId,
                status = loginStatus
            });
        }

        public static async Task BackgroundJobStatusUpdate(int companyId, long queueItemId, string type, bool success, string message, object details = null)
        {
            await Push(companyId, "backgroundStatusUpdate", new
            {
                jobId = queueItemId,
                type = type,
                success = success,
                message = message, 
                details = details
            });
        }

        public static async Task UpdateEventNotification(int companyId, int userId, string eventName, string[] itemIds, string[] groupIds, User performer = null)
        {
            await Push(
                GetEventNotificationChannelName(companyId, userId),
                eventName,
                new
                {
                    performer = performer?.Id,
                    ids = itemIds.ToArray(),
                    groupIds = groupIds.ToArray()
                });
        }
    }
}
