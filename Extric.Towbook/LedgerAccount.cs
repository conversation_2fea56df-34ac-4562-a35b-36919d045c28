using Extric.Towbook.Utility;
using Glav.CacheAdapter.Core.DependencyInjection;
using System;
using System.Collections.ObjectModel;
using System.Data;
using System.Linq;
using System.Threading.Tasks;

namespace Extric.Towbook
{
    /// <summary>
    /// Represents a Ledger Account.
    /// </summary>
    [Table("LedgerAccounts")]
    public sealed class LedgerAccount : IUsesSqlKey
    {
        private const int CacheTimeout = 10;

        [Key("LedgerAccountId")]
        public int Id { get; private set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string AccountNumber { get; set; }
        public int? CompanyId { get; set; }
        public int? ParentId { get; set; }

        public DateTime CreateDate { get; set; }

        public LedgerAccount()
        {

        }

        public LedgerAccount(IDataReader reader)
        {
            Id = reader.GetValue<int>("LedgerAccountId");
            CompanyId = reader.GetValueOrDefault<int>("CompanyId");

            if (CompanyId == 0)
                CompanyId = null;

            ParentId = reader.GetValueOrNull<int>("ParentId");
            Name = reader.GetValue<string>("Name");
            Description = reader.GetValue<string>("Description");
            AccountNumber = reader.GetValue<string>("AccountNumber");
        }

        public async Task Save()
        {
            if (CompanyId.GetValueOrDefault() == 0)
                throw new TowbookException("CompanyId must be set before saving.");

            if (string.IsNullOrWhiteSpace(Name))
                throw new TowbookException("Name must be set before saving.");

            if (this.Id == 0)
                this.Id = (int)await SqlMapper.InsertAsync(this);
            else
                await SqlMapper.UpdateAsync(this);
        }

        public static async Task<LedgerAccount> GetById(int id)
        {
            return await AppServices.Cache.GetAsync("la:" + id, TimeSpan.FromMinutes(CacheTimeout),
                 async () =>
                 {
                     return (await SqlMapper.QueryAsync<LedgerAccount>(
                        "SELECT *, Id=LedgerAccountId FROM LedgerAccounts WITH (nolock) WHERE LedgerAccountId=@Id",
                        new { Id = id })).FirstOrDefault();
                 });
        }

        public static Collection<LedgerAccount> GetByCompany(int companyId)
        {
            return AppServices.Cache.Get("la_c:" + companyId, TimeSpan.FromMinutes(CacheTimeout),
                () =>
                {
                    return SqlMapper.Query<LedgerAccount>(
                        "SELECT *, Id=LedgerAccountId FROM LedgerAccounts WITH (nolock) WHERE CompanyId=@CompanyId",
                        new { CompanyId = companyId }).ToCollection();
                });
        }
    }
}

