using Extric.Towbook.Dispatch;
using Extric.Towbook.Utility;
using Microsoft.Azure.Cosmos;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook
{
    public enum NotificationMessageStatus
    {
        NoResponse = 0,
        Accepted = 1,
        Rejected = 2
    }
    public class NotificationMessage
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        public string Json { get; set; }
        public NotificationMessageStatus Status { get; set; }
        public DateTime? StatusTime { get; set; }
        public string StatusResponse { get; set; }
        public DateTime? ReadDate { get; set; }
        public int? SenderUserId { get; set; }

        [Write(false)]
        public DateTime CreateDate { get; set; }

        public NotificationMessage()
        {

        }

        public static async Task<NotificationMessage> GetById(int id, int userId)
        {
            var rx =  await CosmosDB.Get().GetItemAsync<NotificationMessage>
                ("notifications",
                id.ToString(),
                new PartitionKey(userId));

            if (rx == null)
                return null;

            var ry = rx.Resource;

            ry.CreateDate = ry.CreateDate.ToLocalTime();
            
            if (ry.ReadDate != null)
                ry.ReadDate = ry.ReadDate.Value.ToLocalTime();

            return ry;

        }

        public static NotificationMessage GetById(int id)
        {
            var r = SqlMapper.QuerySP("NotificationMessagesGetById", new { @NotificationMessageId = id }).FirstOrDefault();

            if (r != null)
            {
                return new NotificationMessage()
                {
                    Id = r.NotificationMessageId,
                    UserId = r.UserId,
                    Json = r.Json,
                    Status = (NotificationMessageStatus) ((int?) r.StatusId).GetValueOrDefault(),
                    StatusResponse = r.StatusResponse != null ? r.StatusResponse : "",
                    ReadDate = r.ReadDate,
                    CreateDate = r.CreateDate,
                    SenderUserId = r.SenderUserId
                };
            }
            else
                return null;
        }

        public static IEnumerable<NotificationMessage> GetByUserId(int userId)
        {
            // TODO: read from cosmos. 

            return SqlMapper.QuerySP<NotificationMessage>("NotificationMessagesGetByUserId", new { UserId = userId }).ToList();
        }

        public static async Task<IEnumerable<NotificationMessage>> GetByUserIdAsync(int userId)
        {
            // TODO: read from cosmos. 

            return (await SqlMapper.QuerySpAsync<NotificationMessage>("NotificationMessagesGetByUserId", new { UserId = userId })).ToList();
        }

        /// <summary>
        /// Saves the notification message if it hasn't already been saved to the database.
        /// If this is an update, the only thing that will be updated is the StatusId (accepted, rejected), 
        /// the time of the update, and the response message if any.
        /// </summary>
        public async Task<NotificationMessage> Save()
        {
            if (this.Id > 0)
            {
                if (StatusTime == null)
                    StatusTime = DateTime.Now;

                SqlMapper.ExecuteSP("NotificationMessagesUpdateById", new
                {
                    NotificationMessageId = this.Id,
                    StatusId = this.Status,
                    StatusTime = this.StatusTime,
                    StatusResponse = this.StatusResponse
                });

                try
                {
                    var msg = JsonConvert.DeserializeObject<dynamic>(this.Json);

                    if (msg.callId != null)
                    {
                        var e = Entry.GetById((int)msg.callId);

                        bool found = false;

                        if (e != null)
                        {
                            // don't do anything if the status is already completed, cancelled, or beyond dispatched.
                            if (e.Status == Entry.EntryStatus.Completed ||
                                e.Status == Entry.EntryStatus.Canceled ||
                                e.Status.Id > (int)Entry.EntryStatus.Dispatched)
                                return this;

                            // make sure the driver is still assigned to this call.
                            var drivers = Driver.GetByUserId(this.UserId);

                            if (drivers.Any())
                            {
                                foreach (var driver in drivers)
                                {
                                    if (e.Drivers.Where(o => o == driver.Id).Any())
                                    {
                                        found = true;
                                        break;
                                    }
                                }
                            }

                            if (!found)
                            {
                                return this;
                            }

                            await new ActivityLogging.ActivityLogItem()
                            {
                                ParentObjectId = e.Id,
                                ParentObjectTypeId = ActivityLogging.ActivityLogType.DispatchEntry,
                                ObjectId = this.Id,
                                Type = ActivityLogging.ActivityLogType.NotificationMessage,
                                ActionId = (int)ActivityLogging.ActivityLogActionType.MessageConfirmation,
                                IpAddress = "0.0.0.0",
                                UserId = this.UserId,
                                Details = new ActivityLogging.ActivityLogItemDetail()
                                {
                                    Data = new
                                    {
                                        UserId = this.UserId,
                                        Status = Enum.GetName(typeof(NotificationMessageStatus), Status),
                                        StatusResponse = Status == NotificationMessageStatus.Rejected ? StatusResponse : "",
                                    }.ToJson(),
                                    ActivityLogItemDetailTypeId = ActivityLogging.ActivityLogItemDetailType.RawJson
                                }
                            }.SaveAsync();

                            // accept the driver assignment to show that the driver responded. 
                            int? acceptedDriverId = null;
                            foreach (var x in e.Assets)
                            {
                                foreach (var y in x.Drivers)
                                {
                                    var d = drivers.FirstOrDefault(driver => driver.Id == y.DriverId);
                                    if (d != null)
                                    {
                                        if (this.Status == NotificationMessageStatus.Accepted)
                                        {
                                            acceptedDriverId = d.Id;

                                            y.ResponseStatusId = DriverDispatchStatus.Accepted;
                                            y.ResponseTime = DateTime.Now;
                                            y.ResponseUserId = this.UserId;
                                            y.Save(null);
                                        }
                                        else if (this.Status == NotificationMessageStatus.Rejected)
                                        {
                                            y.ResponseStatusId = DriverDispatchStatus.Rejected;
                                            y.ResponseTime = DateTime.Now;
                                            y.ResponseUserId = this.UserId;
                                            y.Save(null);
                                        }
                                    }
                                }
                            }

                            await Entry.UpdateInAzure(e);

                            if (Status == NotificationMessageStatus.Accepted)
                            {
                                await Integration.PushNotificationProvider.Push(e.CompanyId, "call_dispatch_accepted", new
                                {
                                    callId = e.Id,
                                    extra = "driver_accepted",
                                    userId = this.UserId
                                });

                                // trigger notification event queue item that driver accepted call
                                if (acceptedDriverId != null && e.Company.HasFeature(Generated.Features.Notifications_StandardEventNotifications))
                                {
                                    var item = new Extric.Towbook.EventNotifications.DispatchingQueueItem();

                                    item.Type = Extric.Towbook.EventNotifications.DispatchingTriggerType.CallAcceptedByDriver;
                                    item.DispatchEntryId = e.Id;
                                    item.DriverId = acceptedDriverId;
                                    await item.TriggerEvent();
                                }

                            }
                            else if (Status == NotificationMessageStatus.Rejected)
                            {
                                await Integration.PushNotificationProvider.Push(e.CompanyId, "call_dispatch_rejected", new
                                {
                                    callId = e.Id,
                                    extra = "driver_rejected",
                                    userId = this.UserId,
                                    response = this.StatusResponse,
                                });
                            }
                        }
                    }
                }
                catch
                {
                    throw;

                }
            }
            else
            {
                var r = SqlMapper.QuerySP("NotificationMessagesInsert",
                    new
                    {
                        @UserId = UserId,
                        @Json = Json,
                        @SenderUserId = SenderUserId
                    }).FirstOrDefault();

                this.Id = Convert.ToInt32(r.Id);
            }

            return this;
        }

        
        public static async Task<NotificationMessage> WriteToCosmos(NotificationMessage nm) 
        {
            var cdb = CosmosDB.Get();

            // TODO: implement sql sequence
            //if (nm.Id < 1)            
            //    nm.Id = // get next id from sql sequencer.
            nm.CreateDate = nm.CreateDate.ToUniversalTime();

            await cdb.UpsertItem("notifications", nm, new PartitionKey(nm.UserId));

            return nm;

        }
    }
}
