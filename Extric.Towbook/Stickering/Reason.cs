using Extric.Towbook.Utility;
using Glav.CacheAdapter.Core.DependencyInjection;
using ProtoBuf;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.Stickering
{
    [Serializable]
    [ProtoContract(ImplicitFields = ImplicitFields.AllFields, Name = "Reason")]
    public class Reason
    {
        private const string cacheFormat = "stkrs-all-{0}";
        private const string cacheFormatReason = "s-rr-company-{0}";
        private const string cacheFormatAcc = "s-rr-account-{0}";
        private const int CacheTimeout = 1440;

        public int Id { get; protected set; }
        public int? CompanyId { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public bool Impound { get; set; }
        public bool Deleted { get; set; }

        public Reason() { }

        public static void InvalidateCache(int id)
        {
            AppServices.Cache.InvalidateCacheItem(String.Format(cacheFormat, id));
        }

        public static IEnumerable<Reason> GetByCompanyId(int? companyId = null, bool includeAll = false)
        {
            var reasons = AppServices.Cache.Get<ReasonCollection>(String.Format(cacheFormatReason, companyId), TimeSpan.FromDays(30), () =>
            {
                return new ReasonCollection(Map(SqlMapper.QuerySP<dynamic>("dbo.StickerReasonsGetByCompanyId", new { @CompanyId = companyId })));
            }).Collection;

            if (includeAll)
                return reasons;
            else
                return reasons.Where(w => w.Deleted == false).ToCollection();
        }

        public static IEnumerable<Reason> GetByAccountId(int accountId)
        {
            return AppServices.Cache.Get<ReasonCollection>(String.Format(cacheFormatAcc, accountId), TimeSpan.FromDays(30), () =>
            {
                return new ReasonCollection(Map(SqlMapper.QuerySP<dynamic>("dbo.StickerReasonsGetByAccountId", new { @AccountId = accountId })));
            }).Collection; 
        }

        public static IEnumerable<Reason> GetByStickerId(int stickerId)
        {
            ReasonCollection col = new ReasonCollection(Map(SqlMapper.QuerySP<dynamic>("dbo.StickerReasonsGetByStickerId", new { @StickerId = stickerId })));

            return col.Collection;
        }

        public static Dictionary<int, Collection<Reason>> GetByStickerIds(int[] stickerIds)
        {

            var reasons = SqlMapper.QuerySP<dynamic>("dbo.StickerReasonsGetByStickerIds", new
            {
                @StickerIds = string.Join(",", stickerIds)
            });

            Dictionary<int, Collection<Reason>> ret = new Dictionary<int, Collection<Reason>>();

            foreach (var id in stickerIds)
            {
                Collection<Reason> stickerReasons = new Collection<Reason>();
                foreach (var r in reasons) {
                    if (r.Id != id)
                        continue;

                    var reason = new Reason() {
                        Id = r.StickerReasonId,
                        CompanyId = r.CompanyId,
                        Name = r.Name,
                        Description = r.Description,
                        Impound = r.Impound,
                        Deleted = r.Deleted
                    };
                    stickerReasons.Add(reason);
                }
                ret.Add(id, stickerReasons);
            }

            return ret;
        }

        public static Reason GetById(int id)
        {
            return Map(SqlMapper.QuerySP<dynamic>("dbo.StickerReasonsGetById",
                new { @StickerReasonId = id })).FirstOrDefault();
        }

        public void Save()
        {
            if (this.Id == 0)
            {
                this.Id = Convert.ToInt32(SqlMapper.QuerySP<dynamic>("StickerReasonsInsert",
                    new
                    {
                        @CompanyId = this.CompanyId,
                        @Name = this.Name,
                        @Description = this.Description,
                        @Impound = this.Impound   
                    }).FirstOrDefault().Id);
            }
            else
            {
                SqlMapper.ExecuteSP("StickerReasonsUpdateById",
                    new
                    {
                        @StickerReasonId = this.Id,
                        @CompanyId = this.CompanyId,
                        @Name = this.Name,
                        @Description = this.Description,
                        @Impound = this.Impound,
                        @Deleted = this.Deleted
                    });

                InvalidateCache(this.Id);
            }

        }

        public void Delete()
        {
            SqlMapper.ExecuteSP("dbo.StickerReasonsDeleteById",
                new { @StickerReasonId = this.Id });
        }

        public static IEnumerable<Reason> Map(IEnumerable<dynamic> list)
        {
            return list.Select(o => new Reason()
            {
                Id = o.StickerReasonId,
                CompanyId = o.CompanyId,
                Description = o.Description,
                Impound = o.Impound,
                Name = o.Name,
                Deleted = o.Deleted
            }).ToCollection();
        }

        public override string ToString()
        {
            return this.ToJson();
        }
    }

    [ProtoContract]
    public class ReasonCollection
    {
        [ProtoMember(1)]
        public Collection<Reason> Collection { get; set; }

        public ReasonCollection() { }

        public ReasonCollection(IEnumerable<Reason> list)
        {
            Collection = list.ToCollection();
        }
    }
}
