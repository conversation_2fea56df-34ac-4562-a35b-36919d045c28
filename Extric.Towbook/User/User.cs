using System.Text.RegularExpressions;

namespace Extric.Towbook
{
    using System;
    using System.Collections.Generic;
    using System.Collections.ObjectModel;
    using System.Data.SqlClient;
    using System.Linq;
    using System.Threading.Tasks;
    using Extric.Towbook.Web;
    using Extric.Towbook.Integration;
    using Glav.CacheAdapter.Core.DependencyInjection;
    using Microsoft.Azure.Cosmos;
    using Newtonsoft.Json;
    using Newtonsoft.Json.Linq;
    using ProtoBuf;
    using Utility;

    /// <summary>
    /// User account for Towbook company.
    /// </summary>
    [Serializable]
    [ProtoContract(ImplicitFields = ImplicitFields.AllFields, Name = "User")]
    [C<PERSON>Key("_users")]
    public partial class User
    {
        private const int CacheTimeout = 10;

        public enum TypeEnum
        {
            /// <summary>
            /// Full Access
            /// </summary>
            Manager = 1,

            /// <summary>
            /// Dispatching only
            /// </summary>
            Dispatcher = 2,

            /// <summary>
            /// Tasks related to that driver only
            /// </summary>
            Driver = 3,

            /// <summary>
            /// Accounting tasks only
            /// </summary>
            Accountant = 4,

            /// <summary>
            /// Police.. Not associated with a specific towing company.
            /// </summary>
            PoliceOfficer = 5,

            /// <summary>
            /// User only has access to Reports. 
            /// </summary>
            ReportingOnly = 6,

            /// <summary>
            /// Account User... This is for users that are associated with an account
            /// </summary>
            AccountUser = 100,

            /// <summary>
            /// Account User... This is for users that are associated with an account
            /// </summary>
            AccountManager = 101,

            /// <summary>
            /// Towbook Global Administrator, access to ALL FUNCTIONALITTY.
            /// </summary>
            SystemAdministrator = 255,

            /// <summary>
            /// Private Property Spotter. For finding vehicles to sticker/tow / that are in volation.
            /// </summary>
            Spotter = 7,

            /// <summary>
            /// Able to manage users (PoliceManager, PoliceDispatcher, PoliceOfficer)
            /// </summary>
            PoliceManager = 8,

            /// <summary>
            /// A role limited to police department / law enforcement dispatchers. 
            /// </summary>
            PoliceDispatcher = 9
        }

        #region Fields
        private int _id = -1;
        private int _companyId;
        private string _username;
        [ProtoIgnore]
        private string _previousUsername;
        private string _password;
        private string _email;
        private string _fullName;
        private string _navigationPreferences = "";
        private string _notes;
        private TypeEnum _type = TypeEnum.Manager;
        private DateTime _createDate;
        private DateTime _lastLogin;
        private DateTime? _lastLoginAndroid;
        private DateTime? _lastLoginIOS;

        private int _impersonateCompanyId = 0;
        private bool _recordLogins;
        private bool _recordInvalidPasswords;

        private bool _disabled;
        private bool _deleted;

        #endregion

        #region Properties

        public int Id
        {
            get => _id;
            set { _id = value; }
        }

        /// <summary>
        /// Returns the actual CompanyId for this user. The CompanyId/Company property will return the company that the user is currently
        /// switched to/impersonated to, but this property will return the actual company that the user is stored under. 
        /// </summary>
        [PartitionKey]
        public int PrimaryCompanyId
        {
            get => _companyId;
            set => _companyId = value;
        }

        /// <summary>
        /// Retrieves the Current Company of the user. Doesn't mean it's the owner.
        /// </summary>
        public int CompanyId
        {
            get
            {
                if (Extric.Towbook.Web.HttpContextFactory.Instance != null && Towbook.Web.HttpContextFactory.Instance.Items["ServiceGlobal.CompanyTemp"] != null)
                    return Convert.ToInt32(Extric.Towbook.Web.HttpContextFactory.Instance.Items["ServiceGlobal.CompanyTemp"]);

                return (_impersonateCompanyId > 0 ? _impersonateCompanyId : _companyId);
            }
            set
            {
                if (_companyId == 0)
                    _companyId = value;
                else
                {
                    var list = Towbook.Company.CompanyUser.GetByUserId(_id);

                    _impersonateCompanyId = 0;

                    foreach (var c in list)
                    {
                        if (c.CompanyId == value)
                            _impersonateCompanyId = value;
                    }

                    if (_impersonateCompanyId == 0)
                    {
                        _impersonateCompanyId = 0;
                    }

                    SaveImpersonatedCompany();
                }
            }
        }

        public Company.Company Company
        {
            get
            {
                if (_companyId < 1)
                    throw new TowbookException("CompanyId isn't set");

                if (Extric.Towbook.Web.HttpContextFactory.Instance != null &&
                    Extric.Towbook.Web.HttpContextFactory.Instance.Items["ServiceGlobal.CompanyTemp"] != null)
                    return Towbook.Company.Company.GetById(Convert.ToInt32(Extric.Towbook.Web.HttpContextFactory.Instance.Items["ServiceGlobal.CompanyTemp"]));

                return Towbook.Company.Company.GetById(
                    _impersonateCompanyId > 0 ? _impersonateCompanyId : _companyId);
            }
        }

        public async Task<Company.Company> GetCompanyAsync()
        {
            if (_companyId < 1)
                throw new TowbookException("CompanyId isn't set");
            if (Extric.Towbook.Web.HttpContextFactory.Instance != null &&
                Extric.Towbook.Web.HttpContextFactory.Instance.Items["ServiceGlobal.CompanyTemp"] != null)
                return await Towbook.Company.Company.GetByIdAsync(Convert.ToInt32(Extric.Towbook.Web.HttpContextFactory.Instance.Items["ServiceGlobal.CompanyTemp"]));
            return await Towbook.Company.Company.GetByIdAsync(
                _impersonateCompanyId > 0 ? _impersonateCompanyId : _companyId);
        }

        public int AccountId { get; set; }

        public DateTime CreateDate => _createDate;
        public string Username
        {
            get => _username;
            set
            {
                _previousUsername = _username;
                _username = value;
            }
        }

        [JsonIgnore]
        public string Password
        {
            get => _password;
            set { _password = value; }
        }

        public string Email
        {
            get => _email; set => _email = value;
        }

        public string FullName
        {
            get => _fullName; set => _fullName = value;
        }

        public string Notes
        {
            get => _notes; set => _notes = value;
        }

        public TypeEnum Type
        {
            get => _type; set => _type = value;
        }

        public DateTime LastLogin => _lastLogin;

        public DateTime? LastLoginAndroid => _lastLoginAndroid;

        public DateTime? LastLoginIOS => _lastLoginIOS;

        public string NavigationPreferences
        {
            get
            {
                var permitsEnabled = false;

                if (AccountId > 0)
                {
                    var kv = Integration.AccountKeyValue.GetByAccount(CompanyId, AccountId, Integration.Provider.Towbook.ProviderId, "ParkingPermitsEnabled").FirstOrDefault();
                    if (kv != null && kv.Value == "1")
                        permitsEnabled = true;
                }

                switch (_type)
                {
                    case TypeEnum.SystemAdministrator:
                        return "251|253|255|254|252";
                    case TypeEnum.Manager:
                        if (Company.Type == Towbook.Company.Company.CompanyType.TowingCompany)
                        {
                            return "1|2|11|3|4|5|6|7";
                        }
                        else if (Company.Type == Towbook.Company.Company.CompanyType.LawEnforcement)
                        {
                            return "1|2|11|3|4|5|6|7|10";
                        }

                        return "1|2|11|3|4|5|6|7";

                    case TypeEnum.Dispatcher:
                        if (Notes != null && Notes.Contains("AllowReportsAccess"))
                            return "1|2|11|3|4|5|7";
                        else
                            return "1|2|11|3|4|7";

                    case TypeEnum.Driver:
                        return HasPermissionToViewImpounds() ? "1|2|11|3|4|7" : "1|2|11|4|7";

                    case TypeEnum.Accountant:
                        return "1|2|3|4|5|6|7";

                    case TypeEnum.AccountUser:
                        if (permitsEnabled)
                            return "100|101|10|3|11";

                        return "100|10|3|11";

                    case TypeEnum.AccountManager:
                        if (permitsEnabled)
                            return "2|100|101|10|3|11|6";
                        return "2|100|10|3|11|6";

                    case TypeEnum.PoliceManager:
                        return "1|2|3|5|7";
                    case TypeEnum.PoliceDispatcher:
                        return "1|2|7";
                    case TypeEnum.PoliceOfficer:
                        return "1|2|7";
                    case TypeEnum.ReportingOnly:
                        return "1|5|7";

                    default:
                        throw new Exception("nothing set for " + _type);
                        //return "1|7|8"; 
                }

            }
            set => _navigationPreferences = value;
        }

        public bool RecordLogins
        {
            get => _recordLogins; set => _recordLogins = value;
        }

        public bool RecordInvalidPasswordAttempts
        {
            get => _recordInvalidPasswords; set => _recordInvalidPasswords = value;
        }

        public bool Deleted
        {
            get => _deleted; set => _deleted = value;
        }

        public bool Disabled
        {
            get => _disabled; set => _disabled = value;
        }

        public string OfficePhone { get; set; }
        public string MobilePhone { get; set; }
        public DateTime? MobilePhoneConfirmed { get; set; }

        private bool requireGpsCheckin;
        public bool RequireGPSCheckin
        {
            get => requireGpsCheckin;
            set => requireGpsCheckin = value;
        }

        public DateTime? BirthDate { get; set; }
        public string EmergencyContactName { get; set; }
        public string EmergencyContactPhone { get; set; }
        public bool HasProfilePhoto { get; set; }

        #endregion

        public static bool IsUsernameValid(string username) => Regex.Match(username, @"^[a-zA-Z0-9@\._]{4,}$").Success;

        public User() { }

        internal User(SqlDataReader reader)
        {
            InitializeFromDataReader(reader);
            AppServices.Cache.Add("user:" + Id, TimeSpan.FromMinutes(CacheTimeout), this);
        }

        public static implicit operator System.Net.Mail.MailAddress(User v)
        {
            return new System.Net.Mail.MailAddress(v.Email, v.FullName);
        }

        public static User GetById(int id) =>
            Cache.Instance.Get(id, (int userId) => GetByIdWithoutCache(userId));
        public static async Task<User> GetByIdAsync(int id) =>
            await Cache.Instance.GetAsync(id, async (int userId) => await GetByIdWithoutCacheAsync(userId));
        

        public static User GetByIdWithoutCache(int id)
        {
            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "UsersGetById", new SqlParameter("@UserId", id)))
            {
                if (dr.Read())
                {
                    return new User(dr);
                }
                return null;
            }
        }

        public static async Task<User> GetByIdWithoutCacheAsync(int id)
        {
            using (var dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString, System.Data.CommandType.StoredProcedure,
                "UsersGetById", new SqlParameter("@UserId", id)))
            {
                if (await dr.ReadAsync())
                {
                    return new User(dr);
                }
                return null;
            }
        }

        //Get the UserHistory Count by ActionId
        public int GetUserHistoryCountByActionId(int actionId)
        {
            //var ret = SqlMapper.QuerySP<dynamic>("UserHistoryCountByActionId", new { @UserId = userId, @ActioniD = actionId})
            //	Collection<User> users = new Collection<User>();
            int resCount = 0;
            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "UserHistoryCountByActionId",
                new SqlParameter("@UserId", this.Id),
                new SqlParameter("@ActionId", actionId)))
            {
                while (dr.Read())
                {
                    resCount = dr.GetInt32(0);
                }
            }
            return resCount;
        }

        [Obsolete("Prefer using GetById method instead.")]
        public User(int id)
        {
            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "UsersGetById", new SqlParameter("@UserId", id)))
            {
                if (dr.Read())
                {
                    InitializeFromDataReader(dr);
                }
                else
                {
                    _id = 0;
                    throw new Extric.Towbook.TowbookException("User doesn't exist!");
                }
            }
        }

        [Obsolete("Use GetByUsername instead")]
        public User(string username)
        {
            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "UsersGetByUsername", new SqlParameter("@Username", username)))
            {
                if (dr.Read())
                {
                    InitializeFromDataReader(dr);
                }
                else
                {
                    _id = 0;
                    throw new TowbookException("User doesn't exist!");
                }
            }
        }

        [Obsolete("Use GetByUsernamePassword instead")]
        public User(string username, string password)
        {
            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "UsersGetByUsernamePassword", new SqlParameter("@Username", username), new SqlParameter("@Password", password)))
            {
                if (dr.Read())
                {
                    InitializeFromDataReader(dr);
                }
                else
                {
                    _id = 0;
                }
            }
        }

        public static User GetByUsernamePassword(string username, string password)
        {
            using (var dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "UsersGetByUsernamePassword",
                    new SqlParameter("@Username", username),
                    new SqlParameter("@Password", password)))
            {
                if (dr.Read())
                {
                    return new User(dr);
                }

                return null;
            }
        }

        /// <summary>
        /// Gets the specified user by username
        /// </summary>
        /// <param name="username"></param>
        /// <returns></returns>
        public static User GetByUsername(string username)
        {
            using (var dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "UsersGetByUsername", new SqlParameter("@Username", username)))
            {
                if (dr.Read())
                {
                    return new User(dr);
                }
                else
                {
                    return null;
                }
            }
        }

        public static async Task<User> GetByUsernameAsync(string username)
        {
            using (var dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString, 
                System.Data.CommandType.StoredProcedure,
                "UsersGetByUsername", new SqlParameter("@Username", username)))
            {
                if (await dr.ReadAsync())
                {
                    return new User(dr);
                }
                else
                {
                    return null;
                }
            }
        }

        public static async Task<User> GetByTokenAsync(string token, int? clientVersionId = null)
        {
            var t = await AuthenticationToken.GetByTokenAsync(token, clientVersionId);

            if (t != null)
                return await GetByIdAsync(t.UserId);
            else
                return null;
        }

        public static User GetByKey(string key, string value)
        {
            var ret = SqlMapper.QuerySP<dynamic>("UserKeysGetByKeyValue", new { @Key = key, @Value = value }).FirstOrDefault();

            if (ret != null)
            {
                return User.GetById(ret.UserId);
            }

            return null;
        }

        public static async Task<User> GetByResetPasswordTokenAsync(Guid token)
        {
            var result = (await SqlMapper.QueryAsync<User>(
                @"SELECT U.*, U.UserId as Id FROM Users U 
                 INNER JOIN UsersPasswordResetTokens UPRT ON UPRT.UserId = U.UserId AND UPRT.Token = @Token",
                new { Token = token })).FirstOrDefault();

            return result;
        }

        /// <summary>
        /// Returns a list of all active user accounts (doesn't return deleted ones)
        /// </summary>
        public static Collection<User> GetAll()
        {
            var users = new Collection<User>();

            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "UsersGetAll"))
            {
                while (dr.Read())
                {
                    users.Add(new User(dr));
                }
            }
            return users;
        }

        public static Collection<User> GetAll(TypeEnum type)
        {
            var users = new Collection<User>();

            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "UsersGetByUserTypeId",
                new SqlParameter("@UserTypeId", type)))
            {
                while (dr.Read())
                {
                    users.Add(new User(dr));
                }
            }

            return users;
        }

        /// <summary>
        /// Returns a list of all active user accounts (doesn't return deleted ones)
        /// </summary>
        public static Collection<User> GetByCompanyId(int companyId, bool? includeDeleted = false)
        {
            return includeDeleted == true
                ? GetByCompanyIdWithoutCache(companyId, true)
                : AppServices.Cache.Get("userByCompany:" + companyId, TimeSpan.FromMinutes(CacheTimeout), () =>
                {
                    return GetByCompanyIdWithoutCache(companyId, false);
                });
        }

        public static Collection<User> GetByCompanyIdWithoutCache(int companyId, bool includeDeleted = false)
        {
            var users = new Collection<User>();
            using (var dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "UsersGetByCompanyId", companyId, includeDeleted))
            {
                while (dr.Read())
                {
                    users.Add(new User(dr));
                }
            }
            return users;
        }

        public static async Task<Collection<User>> GetByCompanyIdsAsync(int[] companyIds)
            => await GetByCompanyIdsAsync(companyIds, false);


        /// <summary>
        /// Returns a list of all active user accounts (doesn't return deleted ones)
        /// </summary>
        public static async Task<Collection<User>> GetByCompanyIdsAsync(int[] companyIds, bool returnDeleted)
        {
            if (companyIds.Length == 0)
                return new Collection<User>();

            if (companyIds.Length == 1 && !returnDeleted)
                //TODO ASYNC
                return GetByCompanyId(companyIds[0]);

            string cacheKey = "usersByCompany:" + string.Join(",", companyIds);
            if (returnDeleted)
                cacheKey += "_rd";

            return await AppServices.Cache.GetAsync(cacheKey, TimeSpan.FromMinutes(CacheTimeout), async () =>
            {
                var parameters = new List<string>();

                var p = new List<SqlParameter>();

                int i = 0;
                foreach (var row in companyIds.Distinct())
                {
                    i++;
                    parameters.Add("@P" + i);
                    p.Add(new SqlParameter("@P" + i, row));
                }
                var users = new Collection<User>();
                var sql = $" SELECT * FROM Users WITH (NOLOCK) WHERE " +
                    (!returnDeleted ? $"Deleted = 0 AND " : "") +
                    $"(CompanyId IN ({string.Join(",", parameters)}) " +
                    $"OR UserId IN(SELECT DISTINCT UserId FROM CompanyUsers WITH (nolock) WHERE " +
                    $"CompanyId IN ({string.Join(", ", parameters)})) " +
                    $" OR CompanyId IN(SELECT DISTINCT CompanyId FROM CompaniesShared with(nolock) WHERE " +
                    $"SharedCompanyId IN ({string.Join(", ", parameters)}) and ShareAllUsers = 1)) OPTION (RECOMPILE)";

                using (var dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                    System.Data.CommandType.Text,
                    sql,
                    p.ToArray()))
                {
                    while (await dr.ReadAsync())
                    {
                        users.Add(new User(dr));
                    }
                }
                return users.Where(o => o.AccountId == 0).ToCollection();
            });
        }


        public static Collection<User> GetByAccountId(int accountId, bool? includeDeleted = false)
        {
            using (var dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "UsersGetByAccountId", accountId, includeDeleted))
            {
                Collection<User> users = new Collection<User>();

                while (dr.Read())
                {
                    users.Add(new User(dr));
                }

                return users;
            }
        }

        public static Collection<User> GetRecentLogins(User currentUser)
        {
            if (currentUser.Type != TypeEnum.SystemAdministrator)
            {
                throw new TowbookException("You do not have permission to perform this action");
            }

            Collection<User> users = new Collection<User>();

            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "UsersGetRecentLogins"))
            {
                while (dr.Read())
                {
                    users.Add(new User(dr));
                }
            }

            return users;
        }

        /// <summary>
        /// Checks if the user's domain is available for use.
        /// </summary>
        /// <param name="userName"></param>
        /// <returns></returns>
        public static bool IsDomainTaken(string userName)
        {
            if (string.IsNullOrWhiteSpace(userName))
                return false;

            var companySecuritySetting = CompanySecuritySetting.GetByCompanyDomain(CompanySecuritySetting.GetDomain(userName));

            if (companySecuritySetting != null && companySecuritySetting.SsoEnable)
            {
                return true;
            }
            return false;
        }

        /// <summary>
        /// Returns true if the username is taken
        /// </summary>
        /// <param name="username"></param>
        /// <returns></returns>
        public static async Task<bool> IsUsernameTaken(string userName)
        {
            if (string.IsNullOrWhiteSpace(userName))
                return false;

            using (var dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString, System.Data.CommandType.StoredProcedure,
                "UsersIsUsernameUsed", new SqlParameter("@Username", userName)))
            {
                return dr.HasRows;
            }
        }

        public override string ToString()
        {
            return _fullName;
        }

        private void InitializeFromDataReader(SqlDataReader reader)
        {
            _id = Convert.ToInt32(reader["UserId"]);
            _companyId = Convert.ToInt32(reader["CompanyId"]);

            if (reader["Username"] != DBNull.Value)
                _username = Convert.ToString(reader["Username"]);

            if (reader["Password"] != DBNull.Value)
                _password = Convert.ToString(reader["Password"]);

            if (reader["Email"] != DBNull.Value)
                _email = Convert.ToString(reader["Email"]);

            if (reader["FullName"] != DBNull.Value)
                _fullName = Convert.ToString(reader["FullName"]);

            if (reader["Notes"] != DBNull.Value)
                _notes = Convert.ToString(reader["Notes"]);

            if (reader["CreateDate"] != DBNull.Value)
                _createDate = Convert.ToDateTime(reader["CreateDate"]);

            if (reader["LastLoginDate"] != DBNull.Value)
                _lastLogin = Convert.ToDateTime(reader["LastLoginDate"]);

            if (reader["LastLoginDateAndroid"] != DBNull.Value)
                _lastLoginAndroid = Convert.ToDateTime(reader["LastLoginDateAndroid"]);

            if (reader["LastLoginDateIOS"] != DBNull.Value)
                _lastLoginIOS = Convert.ToDateTime(reader["LastLoginDateIOS"]);

            if (reader["UserTypeId"] != DBNull.Value)
                _type = (TypeEnum)Convert.ToInt32(reader["UserTypeId"]);

            if (reader["RecordLogins"] != DBNull.Value)
                _recordLogins = Convert.ToBoolean(reader["RecordLogins"]);

            if (reader["RecordInvalidPasswords"] != DBNull.Value)
                _recordInvalidPasswords = Convert.ToBoolean(reader["RecordInvalidPasswords"]);


            if (reader["Disabled"] != DBNull.Value)
                _disabled = Convert.ToBoolean(reader["Disabled"]);

            if (reader["Deleted"] != DBNull.Value)
                _deleted = Convert.ToBoolean(reader["Deleted"]);

            AccountId = reader.GetValue<int>("AccountId");
            _impersonateCompanyId = reader.GetValue<int>("ImpersonateCompanyId");

            if (reader["NavigationPreferencesData"] != DBNull.Value)
                _navigationPreferences = Convert.ToString(reader["NavigationPreferencesData"]);
            else
                _navigationPreferences = "UNKNOWN1";

            OfficePhone = reader.GetValue<string>("OfficePhone");
            MobilePhone = reader.GetValue<string>("MobilePhone");
            MobilePhoneConfirmed = reader.GetValueOrNull<DateTime>("MobilePhoneConfirmed");
            RequireGPSCheckin = Convert.ToBoolean(reader["RequireGPSCheckin"]);

            // Be sure to run the update on the database, otherwise this will break
            if (reader["BirthDate"] != DBNull.Value)
                BirthDate = Convert.ToDateTime(reader["BirthDate"]);

            EmergencyContactName = reader.GetValue<string>("EmergencyContactName");
            EmergencyContactPhone = reader.GetValue<string>("EmergencyContactPhone");

            HasProfilePhoto = false;
            if (reader["HasProfilePhoto"] != DBNull.Value)
            {
                HasProfilePhoto = Convert.ToBoolean(reader["HasProfilePhoto"]);
            }
        }


        /// <summary>
        /// This should be called whenever a user logs in succesfully.
        /// </summary>
        public void RecordLogin()
        {
            if (_id > 0)
            {
                if (_deleted == true)
                {
                    throw new TowbookException("The user you're trying to record the login for has been deleted. Method cannot be completed.");
                }
                SqlHelper.ExecuteNonQuery(Core.ConnectionString,
                    "UsersRecordLoginByUserId",
                    new SqlParameter("@UserId", _id));
            }
            else
            {
                throw new TowbookException("User object isn't initialized to an existing user account.");
            }
        }
        public async Task RecordLoginAsync()
        {
            if (_id < 1)
                throw new TowbookException("User object isn't initialized to an existing user account.");

            if (_deleted)
                throw new TowbookException("The user you're trying to record the login for has been deleted. Method cannot be completed.");

            using (var conn = Core.GetConnection())
                await SqlHelper.ExecuteNonQueryAsync(conn,
                    System.Data.CommandType.StoredProcedure,
                        "UsersRecordLoginByUserId",
                        new SqlParameter("@UserId", _id));
        }

        public async Task Save() => await Save(false, null);

        /// <summary>
        /// Saves the User object to the data store.
        /// </summary>
        public async Task Save(bool isSso = false, User savingUser = null, string ip = null)
        {
            if (CompanyId != 1 &&
                Type == TypeEnum.SystemAdministrator)
                throw new Exception("SystemAdministrator cannot be assigned to target CompanyId user.");

            if (_id == 0)
            {
                throw new ApplicationException("No such User. Can't save " +
                    "object! (this object should have already been discarded!)");
            }

            if (!Core.IsEmailValid(Email))
            {
                throw new ArgumentException("Valid Email address is a required field to save a User.", nameof(Email));
            }

            // Only throw when the username has been changed
            if (!(string.IsNullOrWhiteSpace(_previousUsername) || _previousUsername == Username) && !IsUsernameValid(Username))
            {
                throw new ArgumentException("Username is not valid", nameof(Username));
            }

            if (this.Type == 0)
                throw new Exception("Invalid User Type:" + ((int)this.Type));

            if (_id == -1 && await IsUsernameTaken(_username))
                throw new TowbookException("Attempted to create a user with a username that is already taken: " + _username);

            if (_id == -1 && !isSso && IsDomainTaken(_username))
                throw new TowbookException("Attempted to create a user with a domain that is already used in SSO config: " + _username);

            try
            {
                if (_id == -1)
                {
                    DbInsert();
                }
                else
                {
                    var companyUsers = Extric.Towbook.Company.CompanyUser.GetByUserId(_id);
                    int signedInCompanyId = this.CompanyId; // Distinct from _companyId which maps to PrimaryCompanyId
                    // If companyUsers have been changed so that user no longer has access to the
                    // company they're signed into we set the signed in CompanyId to the primaryCompanyId
                    if (companyUsers.All(cu => cu.CompanyId != signedInCompanyId))
                    {
                        CompanyId = _companyId;
                    }

                    var tempUser = User.GetByIdWithoutCache(Id);

                    DbUpdate();

                    if (tempUser.Password != _password ||
                        tempUser.Username != _username)
                    {
                        if (savingUser != null)
                            HistoryItem.RecordAction(HistoryItem.TypeEnum.ChangedPassword, _id, savingUser?.Id ?? _id, ip);
                        Console.WriteLine("invalidating all tokens");
                        AuthenticationToken.InvalidateAllTokens(_id);
                    }



                    if (tempUser.Disabled != _disabled && tempUser.Disabled)
                    {
                        Console.WriteLine("invalidating all tokens");
                        AuthenticationToken.InvalidateAllTokens(_id);
                    }



                    await SynchronizeLinkedDriver();
                }
            }
            finally
            {
                UpdateCaches();
                Towbook.Company.CompanyUser.ResetCacheByUserId(this.Id);
                await Caching.CacheWorkerUtility.UpdateUser(this);
            }
        }

        /// <summary>
        /// Update drivers linked to the user being saved so that the user and driver(s) are either both enabled
        /// or both disabled.
        /// </summary>
        private async Task SynchronizeLinkedDriver()
        {
            var linkedDrivers = Driver.GetByUserId(_id);
            if (linkedDrivers == null)
                return;

            foreach (var linkedDriver in linkedDrivers)
            {
                bool performSave = false;
                // If this user is disabled and the linked driver is not, we set the end date of the linked
                // driver's employment to now to disable that driver
                if (_disabled && (linkedDriver.EndDate == null || linkedDriver.EndDate < DateTime.Today))
                {
                    performSave = true;
                    linkedDriver.EndDate = DateTime.Today;
                }
                // If the user is not disabled and there is an end date on the linked driver's employment
                // that is in the past we remove that end date to re enable that driver.
                else if ((!_disabled) && (linkedDriver.EndDate != null && linkedDriver.EndDate <= DateTime.Today))
                {
                    performSave = true;
                    linkedDriver.EndDate = null;
                }

                if (performSave)
                    await linkedDriver.Save();
            }
        }


        private void UpdateCaches()
        {
            AppServices.Cache.InvalidateCacheItem("user:" + _id);
            AppServices.Cache.InvalidateCacheItem("userByCompany:" + _companyId);

            Cache.Instance.PartitionSet(this);
            Cache.Instance.Set(this);

            UpdateGlobalUserCache(this);
        }

        public async Task Delete(User deleter, string ipAddress)
        {
            if (_id < 1)
                throw new TowbookException($"user Id is {_id} - cannot delete.");

            if (deleter == null)
                throw new TowbookException($"Parameter {nameof(deleter)} is null");

            if (string.IsNullOrWhiteSpace(ipAddress))
                throw new TowbookException($"Parameter {nameof(ipAddress)} is null or empty. To delete a user, this must be recorded");

            try
            {

                HistoryItem.RecordAction(HistoryItem.TypeEnum.DeletedUser, _id, deleter.Id, ipAddress);

                await SqlMapper.ExecuteSpAsync("UsersDeleteById", new { UserId = _id });

                _deleted = true;
            }
            finally
            {
                UpdateCaches();
                await Caching.CacheWorkerUtility.DeleteUser(this);
            }
        }

        public async Task Undelete(User restorer, string ipAddress)
        {
            if (_id < 1)
                throw new TowbookException($"user Id is {_id} - cannot undelete.");

            if (restorer == null)
                throw new TowbookException($"Parameter {nameof(restorer)} is null");

            if (string.IsNullOrWhiteSpace(ipAddress))
                throw new TowbookException($"Parameter {nameof(ipAddress)} is null or empty. To restore a user, this must be recorded");

            try
            {
                HistoryItem.RecordAction(HistoryItem.TypeEnum.RestoredDeletedUser,
                    _id, restorer.Id, ipAddress);

                await SqlMapper.ExecuteSpAsync("UsersUndeleteById", new { UserId = _id });

                _deleted = false;
            }
            finally
            {
                UpdateCaches();
                await Caching.CacheWorkerUtility.UpdateUser(this);
            }
        }

        public class UserCacheModel
        {
            public int Id { get; set; }
            public string Name { get; set; }
            public User.TypeEnum Type { get; set; }
            public bool Disabled { get; set; }

            public static UserCacheModel Map(User u)
            {
                return new UserCacheModel()
                {
                    Id = u.Id,
                    Name = u.FullName,
                    Disabled = u.Disabled,
                    Type = u.Type
                };
            }

            public static bool IsRoadSyncUserAccount(string name)
            {
                var u = new User()
                {
                    FullName = name
                };

                return u.IsRoadSyncUserAccount();
            }
        }
        public static void UpdateGlobalUserCache(User user)
        {
            int[] userCompanies = Extric.Towbook.Company.CompanyUser.GetByUser(user).Select(uc => uc.CompanyId).ToArray();
            var keys = new List<string>();
            foreach (int companyId in userCompanies)
            {
                foreach (var key in Core.GetRedisDatabase().SetMembers($"global_users_caches:{companyId}"))
                {
                    if (!keys.Contains(key))
                        keys.Add(key);
                }
            }

            // If the set of user caches hasn't been populated yet we add the default key to the list
            if (!keys.Any())
            {
                keys.Add($"global:users:{string.Join(",", userCompanies.OrderBy(o => o))}");
            }

            var userCacheModel = UserCacheModel.Map(user);

            foreach (string key in keys)
            {
                string json = Core.GetRedisValue(key);
                if (json == null) return;

                var cache = JsonConvert.DeserializeObject<List<UserCacheModel>>(json);
                var cacheWithoutUser = cache.Where(u => u.Id != userCacheModel.Id).ToList();

                cacheWithoutUser.Add(userCacheModel);
                string newJson = cacheWithoutUser.OrderBy(o => o.Name).ToJson();

                Core.SetRedisValue(key, newJson, TimeSpan.FromHours(72));
            }
        }

        public static string ClearGlobalUserCache(int[] companyIds)
        {
            string key = GetUserCacheKey(companyIds);

            var users = new List<User>();
            foreach (int c in companyIds)
            {
                var cUsers = GetByCompanyId(c);
                foreach (var u in cUsers)
                {
                    if (!users.Contains(u))
                        users.Add(u);
                }
            }

            string userJson = users.Select(UserCacheModel.Map).ToJson();
            AddGlobalUserCacheValue(companyIds, userJson);
            return key;
        }

        private const string GlobalUsersKeyPrefix = "global:users:";
        private static string GetUserCacheKey(int[] companyIds)
        {
            return GlobalUsersKeyPrefix + string.Join(",", companyIds.OrderBy(o => o));
        }

        public static string GetFromGlobalCache(int[] companyIds)
        {
            string userCacheKey = GetUserCacheKey(companyIds);

            string userJson = null;
            try
            {
                userJson = Core.GetRedisValue(userCacheKey);
            }
            catch { }

            if (userJson != null)
                return userJson;

            var users = new List<User>();
            foreach (int c in companyIds)
            {
                var cUsers = GetByCompanyId(c);
                foreach (var u in cUsers)
                {
                    if (!users.Contains(u))
                        users.Add(u);
                }
            }

            userJson = users.Select(UserCacheModel.Map).ToJson();
            try
            {
                AddGlobalUserCacheValue(companyIds, userJson);
            }
            catch { }

            return userJson;
        }

        public static void AddGlobalUserCacheValue(int[] companyIds, string json)
        {
            string userCacheKey = GetUserCacheKey(companyIds);

            foreach (int companyId in companyIds)
            {
                Core.GetRedisDatabase().SetAdd($"global_users_caches:{companyId}", userCacheKey);
            }

            Core.SetRedisValue(userCacheKey, json, TimeSpan.FromHours(72));
        }

        private void DbInsert()
        {
            _id = Convert.ToInt32(SqlHelper.ExecuteScalar(Core.ConnectionString,
                "UsersInsert",
                new SqlParameter("@CompanyId", _companyId),
                new SqlParameter("@Username", _username),
                new SqlParameter("@Password", _password),
                new SqlParameter("@Email", _email),
                new SqlParameter("@FullName", _fullName),
                new SqlParameter("@NavigationPreferences", _navigationPreferences),
                new SqlParameter("@Notes", _notes),
                new SqlParameter("@UserTypeId", _type),
                new SqlParameter("@RecordLogins", _recordLogins),
                new SqlParameter("@RecordInvalidPasswords", _recordInvalidPasswords),
                new SqlParameter("@Disabled", _disabled),
                new SqlParameter("@AccountId", (int?)(AccountId > 0 ? (int?)AccountId : null)),
                new SqlParameter("@OfficePhone", OfficePhone),
                new SqlParameter("@MobilePhone", MobilePhone),
                new SqlParameter("@RequireGPSCheckin", RequireGPSCheckin ? 1 : 0),
                new SqlParameter("@BirthDate", BirthDate),
                new SqlParameter("@EmergencyContactName", EmergencyContactName),
                new SqlParameter("@EmergencyContactPhone", EmergencyContactPhone),
                new SqlParameter("@HasProfilePhoto", HasProfilePhoto)
                ));
        }

        private void DbUpdate()
        {
            SqlHelper.ExecuteNonQuery(Core.ConnectionString,
                "UsersUpdateById",
                new SqlParameter("@UserId", _id),
                new SqlParameter("@CompanyId", _companyId),
                new SqlParameter("@Username", _username),
                new SqlParameter("@Password", _password),
                new SqlParameter("@Email", _email),
                new SqlParameter("@FullName", _fullName),
                new SqlParameter("@NavigationPreferences", _navigationPreferences),
                new SqlParameter("@Notes", _notes),
                new SqlParameter("@UserTypeId", _type),
                new SqlParameter("@RecordLogins", _recordLogins),
                new SqlParameter("@RecordInvalidPasswords", _recordInvalidPasswords),
                new SqlParameter("@Disabled", _disabled),
                new SqlParameter("@AccountId", (int?)(AccountId > 0 ? (int?)AccountId : null)),
                new SqlParameter("@OfficePhone", OfficePhone),
                new SqlParameter("@MobilePhone", MobilePhone),
                new SqlParameter("@MobilePhoneConfirmed", MobilePhoneConfirmed),
                new SqlParameter("@RequireGPSCheckin", RequireGPSCheckin),
                new SqlParameter("@BirthDate", BirthDate),
                new SqlParameter("@EmergencyContactName", EmergencyContactName),
                new SqlParameter("@EmergencyContactPhone", EmergencyContactPhone),
                new SqlParameter("@HasProfilePhoto", HasProfilePhoto)
                );
        }

        /// <summary>
        /// This should be called whenever a user logs in successfully.
        /// </summary>
        private void SaveImpersonatedCompany()
        {
            if (_deleted == true)
            {
                return;
            }

            SqlHelper.ExecuteNonQuery(Core.ConnectionString,
                "UsersUpdateImpersonationByUserId",
                new SqlParameter("@UserId", _id),
                new SqlParameter("@ImpersonateCompanyId", _impersonateCompanyId));

            UpdateCaches();
        }

        public bool IsAccountTypeUser() { return this.Type == TypeEnum.AccountUser || this.Type == TypeEnum.AccountManager; }

        public bool IsRoadSyncUserAccount()
        {
            if (string.IsNullOrWhiteSpace(this.FullName))
                return false;

            // Regular expression to match "roadsync" (case-insensitive) followed by at least 10 digits
            var regex = new Regex(@"^roadsync\d{10,}$", RegexOptions.IgnoreCase);
            return regex.IsMatch(this.FullName);
        }

        #region Permission Checks
        /// <summary>
        /// Checks if the current user has access to the specified companyId
        /// </summary>
        /// <param name="companyId"></param>
        /// <returns></returns>
        public bool HasAccessToCompany(int companyId)
        {
            if (this.Type == TypeEnum.SystemAdministrator)
            {
                return true;
            }

            var list = Towbook.Company.CompanyUser.GetByUserId(_id);
            foreach (var u in list)
            {
                if (u.CompanyId == companyId)
                    return true;
            }

            if (this.Type == TypeEnum.AccountUser ||
                this.Type == TypeEnum.AccountManager)
            {
                var a = Accounts.Account.GetById(this.AccountId);
                if (a != null && a.Companies.Contains(companyId))
                    return true;
            }

            return false;
        }

        public async Task<bool> HasAccessToCompanyAsync(int companyId)
        {
            if (this.Type == TypeEnum.SystemAdministrator)
            {
                return true;
            }

            var list = await Towbook.Company.CompanyUser.GetByUserIdAsync(_id);
            foreach (var u in list)
            {
                if (u.CompanyId == companyId)
                    return true;
            }

            if (this.Type == TypeEnum.AccountUser ||
                this.Type == TypeEnum.AccountManager)
            {
                var a = await Accounts.Account.GetByIdAsync(this.AccountId);
                if (a != null && a.Companies.Contains(companyId))
                    return true;
            }

            return false;
        }

        public bool HasAccessToDispatchEntry(Dispatch.Entry entry)
        {
            if (!HasAccessToCompany(entry.CompanyId))
                return false;

            if (this.Type == TypeEnum.Driver)
            {
                var drivers = Driver.GetByUserId(this.Id);
                if (drivers.Any())
                {
                    if (DriverHasAccess(entry, drivers) == false)
                    {
                        var allowUnassigned = false;

                        if (this.Type == TypeEnum.Driver &&
                            Integration.CompanyKeyValue.GetFirstValueOrNull(
                            entry.CompanyId,
                            Integration.Provider.Towbook.ProviderId,
                            "AllowDriversToViewUnassignedCalls") == "1")
                            allowUnassigned = true;

                        // if a driver is assigned to the call that isn't the currentuser, don't allow them to view it.
                        if (entry.Assets.Where(o => o.Drivers != null && o.Drivers.Where(ro => ro.DriverId > 0).Any()).Any())
                        {
                            allowUnassigned = false;
                        }

                        if (entry.Impound &&
                            entry.Status.Id == Dispatch.Status.Completed.Id &&
                           this.HasPermissionToViewAllImpounds())
                        {
                            allowUnassigned = true;
                        }

                        if (!allowUnassigned)
                            return false;
                    }

                    if (entry.Status.Id == Dispatch.Status.Cancelled.Id)
                        return false;
                }
            }

            return true;

            bool DriverHasAccess(Dispatch.Entry call, Collection<Driver> drivers)
            {
                if (drivers == null || drivers.Count == 0)
                    return true;

                var allowUnassigned = false;

                if (this.Type == Towbook.User.TypeEnum.Driver &&
                    Integration.CompanyKeyValue.GetFirstValueOrNull(
                        call.CompanyId,
                        Integration.Provider.Towbook.ProviderId,
                        "AllowDriversToViewUnassignedCalls") == "1")
                    allowUnassigned = true;

                if (call.Assets != null &&
                    call.Assets.Where(a => (
                            (a.Drivers != null && !a.Drivers.Any() && allowUnassigned) || // if driver can see unassigned calls, or the call is a towout, let them see it.
                            (a.Drivers != null && a.Drivers.Any(d => drivers.Any(dx => dx.Id == d?.DriverId)))
                         )).Any())
                    return true;

                return false;
            }
        }

        public async Task<bool> HasAccessToDispatchEntryAsync(Dispatch.Entry entry)
        {
            if (!await HasAccessToCompanyAsync(entry.CompanyId))
                return false;

            if (this.Type == TypeEnum.Driver)
            {
                var drivers = await Driver.GetByUserIdAsync(this.Id);
                if (drivers.Any())
                {
                    if (!await DriverHasAccessAsync(entry, drivers))
                    {
                        var allowUnassigned = false;

                        if (this.Type == TypeEnum.Driver &&
                            await Integration.CompanyKeyValue.GetFirstValueOrNullAsync(
                            entry.CompanyId,
                            Integration.Provider.Towbook.ProviderId,
                            "AllowDriversToViewUnassignedCalls") == "1")
                            allowUnassigned = true;

                        // if a driver is assigned to the call that isn't the current user, don't allow them to view it.
                        if (entry.Assets.Where(o => o.Drivers != null && o.Drivers.Where(ro => ro.DriverId > 0).Any()).Any())
                        {
                            allowUnassigned = false;
                        }

                        if (entry.Impound &&
                            entry.Status.Id == Dispatch.Status.Completed.Id &&
                            await HasPermissionToViewAllImpoundsAsync())
                        {
                            allowUnassigned = true;
                        }

                        if (!allowUnassigned)
                            return false;
                    }

                    if (entry.Status.Id == Dispatch.Status.Cancelled.Id)
                        return false;
                }
            }

            return true;
        }

        private async Task<bool> DriverHasAccessAsync(Dispatch.Entry call, Collection<Driver> drivers)
        {
            if (drivers == null || drivers.Count == 0)
                return true;

            var allowUnassigned = false;

            if (this.Type == Towbook.User.TypeEnum.Driver &&
                await Integration.CompanyKeyValue.GetFirstValueOrNullAsync(
                    call.CompanyId,
                    Integration.Provider.Towbook.ProviderId,
                    "AllowDriversToViewUnassignedCalls") == "1")
                allowUnassigned = true;

            if (call.Assets != null &&
                call.Assets.Where(a => (
                        (a.Drivers != null && !a.Drivers.Any() && allowUnassigned) || // if driver can see unassigned calls, or the call is a towout, let them see it.
                        (a.Drivers != null && a.Drivers.Any(d => drivers.Any(dx => dx.Id == d?.DriverId)))
                     )).Any())
                return true;

            return false;
        }
        public bool HasAccessToCompany(int[] companyIds)
        {
            if (companyIds == null)
                return false;

            foreach (var companyId in companyIds)
            {
                if (HasAccessToCompany(companyId))
                    return true;
            }

            return false;
        }
        public async Task<bool> HasAccessToCompanyAsync(int[] companyIds)
        {
            if (companyIds == null)
                return false;

            foreach (var companyId in companyIds)
            {
                if (await HasAccessToCompanyAsync(companyId))
                    return true;
            }

            return false;
        }

        private bool? hasAccessToAuditCalls;
        public bool HasAccessToAuditCalls()
        {
            if (hasAccessToAuditCalls == null)
            {
                bool get()
                {
                    if (!Company.HasFeature(Generated.Features.Dispatching_AuditCalls))
                        return false;

                    var value = Integration.UserKeyValue.GetByUser(CompanyId, Id, Integration.Provider.Towbook.ProviderId, "Permission_Calls_Audit").FirstOrDefault()?.Value;

                    if (value == "1")
                        return true;
                    else if (value == "2")
                        return false;

                    if (Integration.CompanyKeyValue.GetFirstValueOrNull(this.CompanyId, Integration.Provider.Towbook.ProviderId, "PreventCalls_Audit") == "1")
                        return false;

                    if (Type == TypeEnum.Dispatcher)
                    {
                        value = Integration.CompanyKeyValue.GetFirstValueOrNull(CompanyId, Integration.Provider.Towbook.ProviderId, "Permission_Calls_Audit_Dispatchers");

                        if (value == "1" || (Notes ?? "").Contains("AllowAuditCalls"))
                            return true;
                    }

                    return (Type == User.TypeEnum.Manager ||
                        Type == User.TypeEnum.Accountant ||
                        (Notes ?? "").Contains("AllowAuditCalls"));
                }
                hasAccessToAuditCalls = get();
            }
            return hasAccessToAuditCalls.Value;
        }

        private bool? hasAccessToUnauditCalls;
        public bool HasAccessToUnauditCalls()
        {
            if (hasAccessToUnauditCalls == null)
            {
                bool get()
                {
                    if (!Company.HasFeature(Generated.Features.Dispatching_AuditCalls))
                        return false;

                    var value = Integration.UserKeyValue.GetByUser(CompanyId, Id, Integration.Provider.Towbook.ProviderId, "Permission_Calls_Unaudit").FirstOrDefault()?.Value;

                    if (value == "1")
                        return true;
                    else if (value == "2")
                        return false;

                    if (Integration.CompanyKeyValue.GetFirstValueOrNull(this.CompanyId, Integration.Provider.Towbook.ProviderId, "PreventCalls_Unaudit") == "1")
                        return false;

                    if (Type == TypeEnum.Dispatcher)
                    {
                        if (Integration.CompanyKeyValue.GetFirstValueOrNull(CompanyId, Integration.Provider.Towbook.ProviderId, "Permission_Calls_Unaudit_Dispatchers") == "1" || (Notes ?? "").Contains("AllowAuditCalls"))
                            return true;
                        else
                            return false;
                    }

                    return HasAccessToAuditCalls();
                }

                hasAccessToUnauditCalls = get();
            }

            return hasAccessToUnauditCalls.Value;
        }

        private bool? hasAccessToLockCalls;
        public bool HasAccessToLockCalls()
        {
            if (hasAccessToLockCalls == null)
            {
                bool get()
                {
                    if (!Company.HasFeature(Generated.Features.Dispatching_LockCalls))
                        return false;

                    var value = Integration.UserKeyValue.GetByUser(CompanyId, Id, Integration.Provider.Towbook.ProviderId, "Permission_Calls_Lock").FirstOrDefault()?.Value;

                    if (value == "1")
                        return true;
                    else if (value == "2")
                        return false;

                    if (Integration.CompanyKeyValue.GetFirstValueOrNull(this.CompanyId, Integration.Provider.Towbook.ProviderId, "PreventCalls_Lock") == "1")
                        return false;

                    return (Type == TypeEnum.Manager ||
                        Type == TypeEnum.Accountant ||
                        (Notes ?? "").Contains("AllowLockCalls"));
                }
                hasAccessToLockCalls = get();
            }

            return hasAccessToLockCalls.Value;
        }

        private bool? hasAccessToUnlockCalls;
        public bool HasAccessToUnlockCalls()
        {
            if (hasAccessToUnlockCalls == null)
            {
                bool get()
                {
                    if (!Company.HasFeature(Generated.Features.Dispatching_LockCalls))
                        return false;

                    var value = Integration.UserKeyValue.GetByUser(CompanyId, Id, Integration.Provider.Towbook.ProviderId, "Permission_Calls_Unlock").FirstOrDefault()?.Value;

                    if (value == "1")
                        return true;
                    else if (value == "2")
                        return false;

                    if (Integration.CompanyKeyValue.GetFirstValueOrNull(this.CompanyId, Integration.Provider.Towbook.ProviderId, "PreventCalls_Unlock") == "1")
                        return false;

                    return HasAccessToLockCalls();
                }
                hasAccessToUnlockCalls = get();
            }

            return hasAccessToUnlockCalls.Value;
        }


        public bool HasPermissionToViewAllImpounds()
        {
            if (this.Type == User.TypeEnum.Driver)
            {
                var kv = Integration.CompanyKeyValue.GetByCompanyId(this.CompanyId,
                    Integration.Provider.Towbook.ProviderId, "AllowDriversToViewAllImpounds").FirstOrDefault();
                if (kv != null && kv.Value == "1")
                    return true;
                else
                    return false;
            }
            return true;
        }
        public async Task<bool> HasPermissionToViewAllImpoundsAsync()
        {
            if (this.Type == User.TypeEnum.Driver)
            {
                var kv = (await Integration.CompanyKeyValue.GetByCompanyIdAsync(this.CompanyId,
                    Integration.Provider.Towbook.ProviderId, "AllowDriversToViewAllImpounds")).FirstOrDefault();
                return kv?.Value == "1";
            }
            return true;
        }


        public bool HasPermissionToViewImpounds()
        {
            if (this.Type == User.TypeEnum.Driver)
            {
                var kv = Integration.CompanyKeyValue.GetByCompanyId(this.CompanyId, Provider.Towbook.ProviderId, "PreventDriversFromViewingImpounds").FirstOrDefault();
                if (kv != null && kv.Value == "1")
                    return false;
                else
                    return true;
            }
            return true;
        }

        public bool AccountUserHasPermissionToViewImpounds()
        {
            if (this.Type != User.TypeEnum.AccountUser && this.Type != User.TypeEnum.AccountManager) return true;
            var companyKeyValue = CompanyKeyValue.GetByCompanyId(this.CompanyId, Provider.Towbook.ProviderId, "PreventAccountUsersFromViewingImpounds").FirstOrDefault();
            if (companyKeyValue == null || companyKeyValue.Value == "0")
            {
                // The default is to block all private property account users from viewing impounds
                // so if there is no key value that is what we do. Towing companies will often prefer that the 
                // businesses that they contract with to tow cars from their los do not want them to see the 
                // entire impound history and pricing. However they we still let them see the impounds in 
                // dispatching while they are in process.
                if (AccountId > 1)
                {
                    var account = Accounts.Account.GetById(AccountId);
                    if (account != null && account.Type == Accounts.AccountType.PrivateProperty)
                    {
                        // don't show impounds for private properties.
                        return false;
                    }

                    return true;
                }

                return false;
            }
            else if (companyKeyValue != null && companyKeyValue.Value == "1")
            {
                return true;
            }
            else if (companyKeyValue != null && companyKeyValue.Value == "2")
            {
                return false;
            }

            return false;
        }

        public bool HasPermissionToRemovePoliceHolds()
        {
            if (this.Type == TypeEnum.Dispatcher)
            {
                if ((Notes ?? "").Contains("AllowPoliceHolds"))
                    return true;

                var kv = Integration.CompanyKeyValue.GetFirstValueOrNull(CompanyId, Provider.Towbook.ProviderId, "PreventDispatchersFromRemovingPoliceHolds");

                if (kv == "1")
                    return false;

                return true;
            }

            return Type == User.TypeEnum.Manager || Type == User.TypeEnum.AccountUser || Type == User.TypeEnum.AccountManager ||
                    (Notes ?? "").Contains("AllowPoliceHolds");
        }

        public bool HasPermissionToEmailCall(Dispatch.Entry entry)
        {
            if (this.Type == TypeEnum.Driver)
            {
                var kv = CompanyKeyValue.GetFirstValueOrNull(entry.CompanyId, Provider.Towbook.ProviderId, "PreventDriversFromEmailingCalls");
                if (kv == "1") return false;
            }
            return true;
        }

        public bool HasPermissionToOverrideClosedPeriodBlock()
        {
            if (this.Type == User.TypeEnum.Manager || this.Type == User.TypeEnum.Accountant)
            {
                return true;
            }
            if ((Notes ?? "").Contains("CanOverrideClosedPeriodBlock"))
            {
                return true;
            }
            return false;
        }

        public bool CanAccessClosedPeriodReport()
        {
            if (this.Type == User.TypeEnum.Manager || this.Type == User.TypeEnum.Accountant)
                return true;

            if ((Notes ?? "").Contains("GrantClosedPeriod"))
            {
                return true;
            }

            return false;
        }


        public bool CanAccessClosedPeriodSettings()
        {
            if (!this.Company.HasFeature(Generated.Features.AdvancedBilling_ClosedAccountingPeriod)) return false;
            if (this.Type == TypeEnum.Accountant && (Notes ?? "").Contains("GrantClosePeriod")) return true;
            if (this.Type != TypeEnum.Manager) return false;
            var usersWithKey = User.GetByCompanyId(this.CompanyId).Where(u => u.Type == TypeEnum.Manager && (u.Notes?.Contains("GrantClosePeriod") ?? false)).ToCollection();
            if (!usersWithKey.Any()) return true;
            return usersWithKey.Any(u => u.Id == this.Id);
        }

        public bool HasPermissionToViewInternalNotes()
        {
            if (IsAccountTypeUser())
            {
                string viewNotesKey = CompanyKeyValue.GetFirstValueOrNull(this.CompanyId, Provider.Towbook.ProviderId, "PreventAccountUsersFromViewingInternalNotes");
                return (viewNotesKey ?? "1") != "1";
            }
            return true;
        }

        public bool HasPermissionToViewCharges()
        {
            if (IsAccountTypeUser())
            {
                string viewChargesKey = CompanyKeyValue.GetFirstValueOrNull(this.CompanyId, Provider.Towbook.ProviderId, "HideChargesFromAccountUsers");
                return viewChargesKey != "1";
            }
            return true;
        }
        #endregion

        /// <summary>
        /// Retrieve the current (or new, if one doesn't exist) token for this user with the specified versionId.  
        /// If there isnt a token for the specified version, a new token will be generated.
        /// </summary>
        /// <param name="clientVersionId">ClientVersionId, as retrieved using ClientVersion object</param>
        /// <returns></returns>
        public AuthenticationToken GetToken(int clientVersionId, string registrationHandle = null, int minsExpiration = 0)
        {
            return AuthenticationToken.GetByUserId(Id, clientVersionId, registrationHandle, minsExpiration);
        }

        public async Task<AuthenticationToken> GetTokenAsync(int clientVersionId, string registrationHandle = null, int minsExpiration = 0)
        {
            return await AuthenticationToken.GetByUserIdAsync(Id, clientVersionId, registrationHandle, minsExpiration);
        }


        public static void RevokeToken(User u)
        {
            //var token = u.GetToken();

            // TODO: implement revoke

            //AppServices.Cache.InvalidateCacheItem("useToken_t:" + token.Token);
            //AppServices.Cache.InvalidateCacheItem("useToken_u:" + token.UserId);
        }


        /// <summary>
        /// Add a security key:value pair. 
        /// </summary>
        /// <param name="key">The key, for example, ios</param>
        /// <param name="value">the value of the key/value pair... a GUID, for instance.</param>
        public void AddKey(string key, string value, string reference)
        {
            if (this.Id < 1)
                throw new TowbookException("Must save the user object before you can register a key.");

            SqlMapper.ExecuteSP("UserKeysInsertUnique", new
            {
                @UserId = this.Id,
                @Key = key,
                @Value = value,
                @Reference = reference
            });
        }

        /// <summary>
        /// Retrieve a list of keys associated for this user. 
        /// </summary>
        /// <returns></returns>
        public KeyValuePair<string, string>[] GetKeys()
        {
            return SqlMapper.QuerySP<dynamic>("UserKeysGetByUserId", new
            {
                @UserId = this.Id
            }).Select(o => new KeyValuePair<string, string>(o.Key, o.Value)).ToArray();
        }

        internal static User GetFirstFromCompanyId(int companyId)
        {
            var r = SqlMapper.Query<dynamic>("SELECT TOP 1 UserId FROM Users WHERE CompanyId=@CompanyId ORDER BY UserId", new { CompanyId = companyId }).FirstOrDefault();
            if (r != null)
            {
                return User.GetById(r.UserId);
            }
            else
                return null;
        }

        public void SetPasswordResetToken(Guid newToken)
        {
            // gets the token if existing for that user
            using (var cnn = Core.GetConnection())
            {
                SqlMapper.Execute(cnn,
                    @"IF (NOT EXISTS(SELECT 1 FROM UsersPasswordResetTokens WHERE UserId=@UserId))
                        INSERT INTO UsersPasswordResetTokens (UserId, Token)
                        VALUES ( @UserId, @Token )
                    ELSE
                        UPDATE UsersPasswordResetTokens SET Token=@Token WHERE UserId=@UserId", new { UserId = this.Id, Token = newToken });
            }
        }
    }
}
