using Glav.CacheAdapter.Helpers;
using StackExchange.Redis;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using System.Diagnostics;
using NLog;
using System.IO;
using System.IO.Compression;
using System.Text;
using System.Collections.ObjectModel;
using System.Text.Json;

namespace Extric.Towbook.Utility
{
    #region Attributes
    [AttributeUsage(AttributeTargets.Class)]
    public class CacheKeyAttribute : Attribute
    {
        public string Name { get; private set; }
        public CacheKeyAttribute() { }
        public CacheKeyAttribute(string hashSetName)
        {
            Name = hashSetName;
        }
    }
    // do not want to depend on data annotations that is not in client profile
    [AttributeUsage(AttributeTargets.Property)]
    public class PartitionKeyAttribute : Attribute
    {
        public string Name { get; private set; }
        public PartitionKeyAttribute() { }
        public PartitionKeyAttribute(string partitionName)
        {
            Name = partitionName;
        }
    }
    #endregion

     
    public class Cache
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        private static Cache _instance;

        public static Cache Instance
        {
            get
            {
                if (_instance == null)
                    _instance = new Cache();

                return _instance;
            }
        }

        private Cache()
        {

        }

        public static byte[] Compress(string stringValue)
        {
            var bytes = System.Text.Encoding.UTF8.GetBytes(stringValue); 

            using (var inputStream = new MemoryStream(bytes))
            {
                using (var outputStream = new MemoryStream())
                {
                    using (var gzipStream = new GZipStream(outputStream, CompressionMode.Compress))
                    {
                        inputStream.CopyTo(gzipStream);
                    }

                    return outputStream.ToArray();
                }
            }
        }

        public static string Decompress(byte[] bytes)
        {
            using (var input = new MemoryStream(bytes))
            {
                using (var output = new MemoryStream())
                {
                    using (var gzipStream = new GZipStream(input, CompressionMode.Decompress))
                    {
                        gzipStream.CopyTo(output);
                    }

                    return Encoding.UTF8.GetString(output.ToArray());
                }
            }
        }

        public static byte[] DecompressBytes(byte[] bytes)
        {
            using (var input = new MemoryStream(bytes))
            {
                using (var output = new MemoryStream())
                {
                    using (var gzipStream = new GZipStream(input, CompressionMode.Decompress))
                    {
                        gzipStream.CopyTo(output);
                    }

                    return output.ToArray();
                }
            }
        }
        private static bool isGZip(byte[] arr)
        {
            return arr.Length >= 2 && arr[0] == 31 && arr[1] == 139;
        }

        public static string SafeStringDecode(byte[] input)
        {
            if(input == null)
                return null;
            if (isGZip(input))
                return Decompress(input);
            else
                return System.Text.Encoding.UTF8.GetString(input);
        }

        public static byte[] SafeStringDecodeBytes(byte[] input)
        {
            if(input == null)
                return null;
            if (isGZip(input))
                return DecompressBytes(input);
            else
                return input;
        }

        private static readonly ConcurrentDictionary<RuntimeTypeHandle, IEnumerable<PropertyInfo>> KeyProperties =
           new ConcurrentDictionary<RuntimeTypeHandle, IEnumerable<PropertyInfo>>();

        private static readonly ConcurrentDictionary<RuntimeTypeHandle, IEnumerable<PropertyInfo>> PartitionProperties =
            new ConcurrentDictionary<RuntimeTypeHandle, IEnumerable<PropertyInfo>>();

        private static readonly ConcurrentDictionary<RuntimeTypeHandle, IEnumerable<PropertyInfo>> TypeProperties = new ConcurrentDictionary<RuntimeTypeHandle, IEnumerable<PropertyInfo>>();

        private static readonly ConcurrentDictionary<RuntimeTypeHandle, string> TypeTableName = new ConcurrentDictionary<RuntimeTypeHandle, string>();

        
        public T Get<T>(string keyValue)
        {
            var name = GetObjectCacheKey(typeof(T));

            var db = Core.GetRedisDatabase();

            return FromByteArray<T>(db.HashGet(name, keyValue, CommandFlags.PreferReplica));
        }

        public T Get<T>(int keyValue)
        {
            var name = GetObjectCacheKey(typeof(T));
            var db = Core.GetRedisDatabase();

            return FromByteArray<T>(db.HashGet(name, keyValue, CommandFlags.PreferReplica));
        }
        public async Task<T> GetAsync<T>(int keyValue)
        {
            var name = GetObjectCacheKey(typeof(T));
            var db = Core.GetRedisDatabase();

            return FromByteArray<T>(await db.HashGetAsync(name, keyValue, CommandFlags.PreferReplica));
        }

        public IEnumerable<T> Get<T>(int[] keyValues)
        {
            var name = GetObjectCacheKey(typeof(T));
            var db = Core.GetRedisDatabase();
            
            return db.HashGet(name, 
                Array.ConvertAll(keyValues, item => (RedisValue)item), 
                CommandFlags.PreferReplica)
                .Select(r => FromByteArray<T>(r));
        }

        private readonly Glav.CacheAdapter.Web.PerRequestCacheHelper requestCache = 
            new Glav.CacheAdapter.Web.PerRequestCacheHelper();

        /// <summary>
        /// Get the corresponding the perRequestCache entry that corresponds to a redis key.
        /// </summary>
        /// <param name="name">The object type name</param>
        /// <param name="keyValue">Value of key for object in redis. Generally an int but we 
        /// accept it precasted to string.</param>
        /// <returns>Per Request Cache Key</returns>
        private static string PerRequestCacheKey(string name, string keyValue) => $"_chrc_{name}_{keyValue}";
        
        public T Get<T>(int keyValue,
            Func<int, T> getValue) where T : class
        {
            // we don't allow lookups for 0. 
            if (keyValue == 0)
                return default;

            var name = GetObjectCacheKey(typeof(T));
            string perRequestCacheKey = PerRequestCacheKey(name, keyValue.ToString());
            T cached = requestCache.TryGetItemFromPerRequestCache<T>(perRequestCacheKey);

            if (cached != null)
                return cached;

            T rv;
            try
            {
                rv = Get<T>(keyValue);
            }
            catch //(ProtoBuf.ProtoException pe)
            {
                rv = null;
            }

            if (rv != null)
            {
                requestCache.AddToPerRequestCache(perRequestCacheKey, rv);
                return rv;
            }

            rv = getValue(keyValue);

            if (rv != null)
            {
                // don't cache nulls. 
                // we should probably create a way to allow for caching of nulls, but 
                // when we do that we need to optionally allow it - that should never be the default
                // across the whole system.
                try
                {
                    Set(rv);
                    PartitionSet(rv);
                }
                catch
                {

                }
            }

            requestCache.AddToPerRequestCache(perRequestCacheKey, rv);

            return rv;
        }


        public async Task<T> GetAsync<T>(int keyValue,
            Func<int, Task<T>> getValue) where T : class
        {
            // we don't allow lookups for 0. 
            if (keyValue == 0)
                return default;

            var name = GetObjectCacheKey(typeof(T));
            string perRequestCacheKey = PerRequestCacheKey(name, keyValue.ToString());
            T cached = requestCache.TryGetItemFromPerRequestCache<T>(perRequestCacheKey);

            if (cached != null)
                return cached;

            T rv;
            try
            {
                rv = await GetAsync<T>(keyValue);
            }
            catch //(ProtoBuf.ProtoException pe)
            {
                rv = null;
            }

            if (rv != null)
            {
                requestCache.AddToPerRequestCache(perRequestCacheKey, rv);
                return rv;
            }

            rv = await getValue(keyValue);

            if (rv != null)
            {
                // don't cache nulls. 
                // we should probably create a way to allow for caching of nulls, but 
                // when we do that we need to optionally allow it - that should never be the default
                // across the whole system.
                try
                {
                    await SetAsync(rv);
                    await PartitionSetAsync(rv);
                }
                catch
                {

                }
            }

            requestCache.AddToPerRequestCache(perRequestCacheKey, rv);

            return rv;
        }



        public IEnumerable<T> GetAll<T>()
        {
            var name = $"{GetObjectCacheKey(typeof(T))}";
            var db = Core.GetRedisDatabase();
            return HashGetAll<T>(name);
        }

        public void Set<T>(int v, T obj)
        {
            if (!Core.GetRedisConnection().IsConnected)
                return;

            var name = GetObjectCacheKey(typeof(T));
            var bytes = ToByteArray<T>(obj);

            var db = Core.GetRedisDatabase();

            MeasureLongRunning("HashSet-Set-N", name + ":" + v, () =>
                db.HashSet(name, new HashEntry[] { new HashEntry(v, bytes) }));
        }

        public void Set<T>(T obj)
        {
            var name = GetObjectCacheKey(typeof(T));
            var key = GetObjectPrimaryKeyValue(typeof(T), obj);
            var partitionKey = GetObjectPartitionValue(typeof(T), obj);

            // find the unique key
            var bytes = ToByteArray<T>(obj);

            var db = Core.GetRedisDatabase();

            MeasureLongRunning("HashSet-Set", name + ":" + key, () =>
                db.HashSet(name, new HashEntry[] { new HashEntry(key, bytes) }));
        }

        public async Task SetAsync<T>(T obj)
        {
            var name = GetObjectCacheKey(typeof(T));
            var key = GetObjectPrimaryKeyValue(typeof(T), obj);
            var partitionKey = GetObjectPartitionValue(typeof(T), obj);

            // find the unique key
            var bytes = ToByteArray<T>(obj);

            var db = Core.GetRedisDatabase();

            await MeasureLongRunningAsync("HashSet-Set", name + ":" + key, async() =>
                await db.HashSetAsync(name, new HashEntry[] { new HashEntry(key, bytes) }));
        }


        public void Delete<T>(T obj)
        {
            if (!Core.GetRedisConnection().IsConnected)
                return;

            var name = $"{GetObjectCacheKey(typeof(T))}";
            var primaryKeyValue = GetObjectPrimaryKeyValue(typeof(T), obj);

            var db = Core.GetRedisDatabase();
            db.HashDelete(name, primaryKeyValue);

            string requestCacheKey = PerRequestCacheKey(name, primaryKeyValue);
            requestCache.RemoveFromPerRequestCache(requestCacheKey);
        }
        
        public async Task DeleteAsync<T>(T obj)
        {
            if (!Core.GetRedisConnection().IsConnected)
                return;

            var name = $"{GetObjectCacheKey(typeof(T))}";
            var primaryKeyValue = GetObjectPrimaryKeyValue(typeof(T), obj);

            var db = Core.GetRedisDatabase();
            await db.HashDeleteAsync(name, primaryKeyValue);

            string requestCacheKey = PerRequestCacheKey(name, primaryKeyValue);
            requestCache.RemoveFromPerRequestCache(requestCacheKey);
        }




        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="partitionId"></param>
        /// <param name="keyValue"></param>
        /// <param name="tryAtRootIfNotFound">If the value cannot be found in the hashset/partition, 
        /// try to find it at the root level without the partition Id</param>
        /// <returns></returns>
        public T PartitionGet<T>(
            int partitionId, 
            int keyValue, 
            bool tryAtRootIfNotFound = false)
        {
            if (!Core.GetRedisConnection().IsConnected)
                return default(T);

            var name = $"{GetObjectCacheKey(typeof(T))}_{partitionId}";
            var db = Core.GetRedisDatabase();

            var rv = FromByteArray<T>(db.HashGet(name, keyValue));

            if (rv == null && tryAtRootIfNotFound)
            {
                // maybe its cached as an independant value and not part of the partition yet.
                rv = Get<T>(keyValue);
            }

            return rv;
        }

        public IEnumerable<T> PartitionGetAll<T>(int partitionId)
        {
            if (!Core.GetRedisConnection().IsConnected)
                return null;

            var name = $"{GetObjectCacheKey(typeof(T))}_{partitionId}";
            var db = Core.GetRedisDatabase();

            if (!Core.GetRedisConnection().IsConnected)
                return null;

            var rx = db.HashScan(name, flags: CommandFlags.PreferReplica);

            if (!rx.Any())
                return null;

            if (rx.Count() == 1 &&
                rx.First().Name == "_empty_list")
                return Array.Empty<T>();

            return rx
                .Where(o => o.Name != "_empty_list")
                .Select(o => FromByteArray<T>(o.Value));
        }


        public async Task<IEnumerable<T>> PartitionGetAllAsync<T>(int partitionId)
        {
            if (!Core.GetRedisConnection().IsConnected)
                return null;

            var name = $"{GetObjectCacheKey(typeof(T))}_{partitionId}";
            var db = Core.GetRedisDatabase();

            if (!Core.GetRedisConnection().IsConnected)
                return null;

            Collection<HashEntry> rx = new Collection<HashEntry>();
            await foreach (var r in db.HashScanAsync(name, flags: CommandFlags.PreferReplica))
            {
                rx.Add(r);
            }

            if (!rx.Any())
                return null;

            if (rx.Count() == 1 &&
                rx.First().Name == "_empty_list")
                return Array.Empty<T>();
            try
            {
                return rx
                    .Where(o => o.Name != "_empty_list")
                    .Select(o => FromByteArray<T>(o.Value));
            }
            catch (ProtoBuf.ProtoException)
            {
                return null;
            } 
        }


        /// <summary>
        /// Gets all of the values for the specified partition key. 
        /// If the partition doesn't exist, get the values by executing the passed in method, 
        /// and take the result and save it by performing a call to PartitionSetMultiple internally.
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="primaryKeyValue"></param>
        /// <param name="getValue"></param>
        /// <returns></returns>
        public IEnumerable<T> PartitionGetAll<T>(int partitionId,
            Func<int, IEnumerable<T>> getValue)
        {
            IEnumerable<T> rv = PartitionGetAll<T>(partitionId);

            if (rv != null)
                return rv;

            rv = getValue(partitionId);

            if (!rv.Any())
                PartitionSetAsEmptyList<T>(partitionId);
            else
                PartitionSetMultiple(rv, false, partitionId);

            return rv;
        }


        /// <summary>
        /// Gets all of the values for the specified partition key. 
        /// If the partition doesn't exist, get the values by executing the passed in method, 
        /// and take the result and save it by performing a call to PartitionSetMultiple internally.
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="primaryKeyValue"></param>
        /// <param name="getValue"></param>
        /// <returns></returns>
        public async Task<IEnumerable<T>> PartitionGetAllAsync<T>(int partitionId,
            Func<int, Task<IEnumerable<T>>> getValue)
        {
            IEnumerable<T> rv = await PartitionGetAllAsync<T>(partitionId);

            if (rv != null)
                return rv;

            rv = await getValue(partitionId);

            if (!rv.Any())
                PartitionSetAsEmptyList<T>(partitionId);
            else
                PartitionSetMultiple(rv, false, partitionId);

            return rv;
        }


        public T PartitionGet<T>(int partitionId, int primaryKeyValue,
            bool tryAtRootIfNotFound,
            Func<int, int, T> getValue)
        {
            // we don't allow lookups for 0. 
            if (primaryKeyValue == 0)
                return default(T);

            T rv = PartitionGet<T>(partitionId, primaryKeyValue);

            if (rv != null)
                return rv;

            if (rv == null && tryAtRootIfNotFound)
            {
                // maybe its cached as an independant value and not part of the partition yet.
                rv = Get<T>(primaryKeyValue);
                if (rv != null)
                    return rv;
            }

            rv = getValue(partitionId, primaryKeyValue);

            if (rv != null)
            {
                // don't cache nulls. 
                // we should probably create a way to allow for caching of nulls, but 
                // when we do that we need to optionally allow it - that should never be the default
                // across the whole system.
                Set(rv);
                PartitionSet(rv);
            }

            return rv;
        }

        public async Task<T> PartitionGetAsync<T>(int partitionId, int primaryKeyValue,
            bool tryAtRootIfNotFound,
            Func<int, int, Task<T>> getValue)
        {
            // we don't allow lookups for 0. 
            if (primaryKeyValue == 0)
                return default(T);

            T rv = PartitionGet<T>(partitionId, primaryKeyValue);

            if (rv != null)
                return rv;

            if (rv == null && tryAtRootIfNotFound)
            {
                // maybe its cached as an independant value and not part of the partition yet.
                rv = await GetAsync<T>(primaryKeyValue);
                if (rv != null)
                    return rv;
            }

            rv = await getValue(partitionId, primaryKeyValue);

            if (rv != null)
            {
                // don't cache nulls. 
                // we should probably create a way to allow for caching of nulls, but 
                // when we do that we need to optionally allow it - that should never be the default
                // across the whole system.
                await SetAsync(rv);
                await PartitionSetAsync(rv);
            }

            return rv;
        }

        public void PartitionDelete<T>(T obj)
        {
            if (!Core.GetRedisConnection().IsConnected)
                return;

            var name = $"{GetPartitionedObjectCacheKey(typeof(T), obj)}";
            var primaryKeyValue = GetObjectPrimaryKeyValue(typeof(T), obj);


            Console.WriteLine("delete " + name + " -- " + primaryKeyValue);
            var db = Core.GetRedisDatabase();
            db.HashDelete(name, primaryKeyValue);
        }

        public async Task PartitionDeleteAsync<T>(T obj)
        {
            if (!Core.GetRedisConnection().IsConnected)
                return;

            var name = $"{GetPartitionedObjectCacheKey(typeof(T), obj)}";
            var primaryKeyValue = GetObjectPrimaryKeyValue(typeof(T), obj);

            Console.WriteLine("delete " + name + " -- " + primaryKeyValue);
            var db = Core.GetRedisDatabase();
            await db.HashDeleteAsync(name, primaryKeyValue);
        }

        public void PartitionDelete<T>(int partitionId, int primaryKeyValue)
        {
            if (!Core.GetRedisConnection().IsConnected)
                return;

            var name = $"{GetObjectCacheKey(typeof(T))}_{partitionId}";
            
            Console.WriteLine("delete " + name + " -- " + primaryKeyValue);
            var db = Core.GetRedisDatabase();
            db.HashDelete(name, primaryKeyValue);
        }

        /// <summary>
        /// Sets the passed in object for the specified partition, but only if that partition/hashset already exists.
        /// If it doesn't already exist, this method will not set anything. 
        /// </summary>
        /// <remarks> 
        /// if we set a partition and it hasnt been initialized yet, we run the HIGH risk of the cache
        /// thinking we only have ONE element when there are actually hundreds. 
        /// if we run PartitionSet, then we need to check if the partition has been initialized 
        /// if it hasnt, then we have to flag it so that the partitiongetall knows. 
        /// </remarks>
        /// <typeparam name="T"></typeparam>
        /// <param name="obj"></param>
        public void PartitionSet<T>(T obj)
        {
            var name = $"{GetPartitionedObjectCacheKey(typeof(T), obj)}";
            var bytes = ToByteArray<T>(obj);
            var primaryKeyValue = GetObjectPrimaryKeyValue(typeof(T), obj);


            if (primaryKeyValue == null)
                return;

            //Console.WriteLine("set {partition=" + name + ", key=" + primaryKeyValue + "}");
            var db = Core.GetRedisDatabase();

            MeasureLongRunning("HashSet-PartitionSet", primaryKeyValue + ":" + name, () =>
            {
                // only perform the set if the hash already exists. 
                if (db.KeyExists(name))
                    db.HashSet(name, new HashEntry[] { new HashEntry(primaryKeyValue, bytes) });
            });
        }

        /// <summary>
        /// Sets the passed in object for the specified partition, but only if that partition/hashset already exists.
        /// If it doesn't already exist, this method will not set anything. 
        /// </summary>
        /// <remarks> 
        /// if we set a partition and it hasnt been initialized yet, we run the HIGH risk of the cache
        /// thinking we only have ONE element when there are actually hundreds. 
        /// if we run PartitionSet, then we need to check if the partition has been initialized 
        /// if it hasnt, then we have to flag it so that the partitiongetall knows. 
        /// </remarks>
        /// <typeparam name="T"></typeparam>
        /// <param name="obj"></param>
        public async Task PartitionSetAsync<T>(T obj)
        {
            var name = $"{GetPartitionedObjectCacheKey(typeof(T), obj)}";
            var bytes = ToByteArray<T>(obj);
            var primaryKeyValue = GetObjectPrimaryKeyValue(typeof(T), obj);


            if (primaryKeyValue == null)
                return;

            //Console.WriteLine("set {partition=" + name + ", key=" + primaryKeyValue + "}");
            var db = Core.GetRedisDatabase();

            await MeasureLongRunningAsync("HashSet-PartitionSet", primaryKeyValue + ":" + name, async () =>
            {
                // only perform the set if the hash already exists. 
                if (await db.KeyExistsAsync(name))
                    await db.HashSetAsync(name, new HashEntry[] { new HashEntry(primaryKeyValue, bytes) });
            });
        }



        private static void MeasureLongRunning(string type, string key, Action T)
        {
            var sw = Stopwatch.StartNew();

            try
            {
                T();
            }
            finally
            {
                sw.Stop();

                if (sw.ElapsedMilliseconds > 5)
                {
                    var log = new LogEventInfo();
                    log.Level = LogLevel.Info;
                    // TODO: CODE FOR DEBUGGING PURPOSES
                    log.Message = $"Performance Monitoring - Measure Long running Redis activity ({sw.ElapsedMilliseconds}) {CountersToString(Core.GetRedisConnection().GetCounters())}";
                    log.Properties["commitId"] = Core.GetCommitId();
                    log.Properties["data"] = key;
                    log.Properties["type"] = type;
                    log.Properties["totalTime"] = sw.ElapsedMilliseconds;

                    logger.Log(log);
                }
            }
        }

        // TODO: CODE FOR DEBUGGING PURPOSES
        public static string CountersToString(ServerCounters serverCounters) => JsonSerializer.Serialize(serverCounters);

        private static async Task MeasureLongRunningAsync(string type, string key, Func<Task> T)
        {
            var sw = Stopwatch.StartNew();

            try
            {
                await T();
            }
            finally
            {
                sw.Stop();

                if (sw.ElapsedMilliseconds > 5)
                {
                    var log = new LogEventInfo();
                    log.Level = LogLevel.Info;
                    // TODO: CODE FOR DEBUGGING PURPOSES
                    log.Message = $"Performance Monitoring - Measure Long running Redis activity (*{sw.ElapsedMilliseconds}) {CountersToString(Core.GetRedisConnection().GetCounters())}";
                    log.Properties["commitId"] = Core.GetCommitId();
                    log.Properties["data"] = key;
                    log.Properties["type"] = type;
                    log.Properties["totalTime"] = sw.ElapsedMilliseconds;

                    logger.Log(log);
                }
            }
        }


        public void PartitionSet<T>(int partitionId, int primaryKeyValue, T obj)
        {
            if (!Core.GetRedisConnection().IsConnected)
                return;

            var name = $"{GetObjectCacheKey(typeof(T))}_{partitionId}";
            var bytes = ToByteArray<T>(obj);

            var db = Core.GetRedisDatabase();

            MeasureLongRunning("HashSet-PartitionSet", name + ":" + primaryKeyValue, () =>
            {
                if (db.KeyExists(name))
                {
                    db.HashSet(name, new HashEntry[] { new HashEntry(primaryKeyValue, bytes) });
                }
            });
        }

        public async Task PartitionSetAsync<T>(int partitionId, int primaryKeyValue, T obj)
        {
            if (!Core.GetRedisConnection().IsConnected)
                return;

            var name = $"{GetObjectCacheKey(typeof(T))}_{partitionId}";
            var bytes = ToByteArray<T>(obj);

            var db = Core.GetRedisDatabase();

            await MeasureLongRunningAsync("HashSet-PartitionSet", name + ":" + primaryKeyValue, async () =>
            {
                if (await db.KeyExistsAsync(name))
                {
                    await db.HashSetAsync(name, new HashEntry[] { new HashEntry(primaryKeyValue, bytes) });
                }
            });
        }

        public void PartitionSetMultiple<T>(IEnumerable<T> obj, bool warn = true, int? forcedPartitionId = null)
        {
            if (obj == null || !obj.Any())
                return;

            if (!Core.GetRedisConnection().IsConnected)
                return;

            var t = typeof(T);

            List<HashEntry> he = new List<HashEntry>();

            string name = null;

            if (forcedPartitionId != null)
                name = GetObjectCacheKey(t) + "_" + forcedPartitionId;
            else
                name = $"{GetPartitionedObjectCacheKey(t, obj.First())}";

            foreach (var i in obj)
            {
                var bytes = ToByteArray(i);
                var primaryKeyValue = GetObjectPrimaryKeyValue(t, i);

                if (warn)
                {
                    var partitionKey = GetPartitionedObjectCacheKey(t, i);

                    if (partitionKey != name)
                    {
                        throw new Exception(
                            "Object has invalid partitionKey: " + partitionKey + ". Expected: " + name);
                    }
                }
                Console.WriteLine("SET: " + name + "/" + primaryKeyValue);
                he.Add(new HashEntry(primaryKeyValue, bytes));
            }

            var db = Core.GetRedisDatabase();
            Console.WriteLine("MODIFY SET: " + name);

            MeasureLongRunning("HashSet-PartitionSetMultiple", name, () =>
                db.HashSet(name, he.ToArray()));
        }


        public void PartitionSetAsEmptyList<T>(int partitionId)
        {
            if (!Core.GetRedisConnection().IsConnected)
                return;

            var t = typeof(T);

            string name = GetObjectCacheKey(t) + "_" + partitionId;

            var db = Core.GetRedisDatabase();

            var length = db.HashLength(name);

            if (length > 0)
            {
                Console.WriteLine("hash already contains values, no need to set empty list");
                return;
            }

            db.HashSet(name,
                new HashEntry[] { new HashEntry("_empty_list", new byte[0]) });
        }



        public void PartitionReset<T>(int primaryKeyValue)
        {
            if (!Core.GetRedisConnection().IsConnected)
                return;

            var name = $"{GetObjectCacheKey(typeof(T))}_{primaryKeyValue}";
            var db = Core.GetRedisDatabase();

            db.KeyDelete(name);
        }

        public async Task PartitionResetAsync<T>(int primaryKeyValue)
        {
            if (!Core.GetRedisConnection().IsConnected)
                return;

            var name = $"{GetObjectCacheKey(typeof(T))}_{primaryKeyValue}";
            var db = Core.GetRedisDatabase();

            await db.KeyDeleteAsync(name);
        }

        public T HashGet<T>(string name, string key)
        {
            if (!Core.GetRedisConnection().IsConnected)
                return default(T);
            var db = Core.GetRedisDatabase();
            return FromByteArray<T>(db.HashGet(name, key, CommandFlags.PreferReplica));
        }

        public T HashGet<T>(string name, int key)
        {
            if (!Core.GetRedisConnection().IsConnected)
                return default(T);

            var db = Core.GetRedisDatabase();
            return FromByteArray<T>(db.HashGet(name, key, CommandFlags.PreferReplica));
        }

        public void HashSet<T>(string name, int key, T val)
        {
            if (!Core.GetRedisConnection().IsConnected)
                return;

            var db = Core.GetRedisDatabase();

            MeasureLongRunning("HashSet-Bytes", name + ":" + key, () =>
                db.HashSet(name, new HashEntry[] { new HashEntry(key, ToByteArray(val)) }));
        }

        public void HashDelete(string name, int key)
        {
            if (!Core.GetRedisConnection().IsConnected)
                return;

            var db = Core.GetRedisDatabase();
            db.HashDelete(name, key);
        }

        public void HashDelete(string name, string key)
        {
            if (!Core.GetRedisConnection().IsConnected)
                return;

            var db = Core.GetRedisDatabase();
            db.HashDelete(name, key);
        }

        public void HashSet<T>(string name, string key, T val)
        {

            if (!Core.GetRedisConnection().IsConnected)
                return;

            var db = Core.GetRedisDatabase();
            MeasureLongRunning("HashSet-ToByteArray", name + ":" + key, () =>
                 db.HashSet(name, new HashEntry[] { new HashEntry(key, ToByteArray(val)) }));
        }


        public IEnumerable<T> HashGetAll<T>(string name)
        {
            if (!Core.GetRedisConnection().IsConnected)
                return Array.Empty<T>();

            var db = Core.GetRedisDatabase();
            return db.HashScan(name, flags: CommandFlags.PreferReplica)
                .Select(o => FromByteArray<T>(o.Value));
        }

        private static string GetObjectPrimaryKeyValue(Type t, Object o)
        {
            var k = KeyPropertiesCache(t);
            var v = k.FirstOrDefault()?.GetValue(o)?.ToString();

            return v;
        }

        private static string GetObjectPartitionValue(Type t, Object o)
        {
            var k = PartitionPropertiesCache(t);
            var v = k.First().GetValue(o)?.ToString();

            return v;
        }
        
        private static string GetObjectCacheKey(Type type)
        {
            string name;
            if (!TypeTableName.TryGetValue(type.TypeHandle, out name))
            {
                var tableattr = type.GetCustomAttributes(false).Where(attr => attr is CacheKeyAttribute).SingleOrDefault() as dynamic;
                if (tableattr != null)
                    name = tableattr.Name;

                if (name == null)
                    throw new Exception("No CacheKey specified on " + type.AssemblyQualifiedName);

                TypeTableName[type.TypeHandle] = name;
            }

            return name;
        }

        private static string GetPartitionedObjectCacheKey(Type type, Object o)
        {
            var key = GetObjectCacheKey(type);
            return $"{key}_{GetObjectPartitionValue(type, o)}";
        }

        private static IEnumerable<PropertyInfo> TypePropertiesCache(Type type)
        {
            IEnumerable<PropertyInfo> pis;
            if (TypeProperties.TryGetValue(type.TypeHandle, out pis))
            {
                return pis;
            }

            var properties = type.GetProperties();
            TypeProperties[type.TypeHandle] = properties;

            return properties;
        }

        private static IEnumerable<PropertyInfo> KeyPropertiesCache(Type type)
        {
            IEnumerable<PropertyInfo> pi;
            if (KeyProperties.TryGetValue(type.TypeHandle, out pi))
            {
                return pi;
            }

            var allProperties = TypePropertiesCache(type);
            var keyProperties = allProperties.Where(p => 
                p.GetCustomAttributes(true).Any(a => a is KeyAttribute)).ToList();

            if (keyProperties.Count == 0)
            {
                var idProp = allProperties.Where(p => p.Name.ToLower() == "id").FirstOrDefault();
                if (idProp != null)
                {
                    keyProperties.Add(idProp);
                }
            }

            KeyProperties[type.TypeHandle] = keyProperties;

            return keyProperties;
        }

        private static IEnumerable<PropertyInfo> PartitionPropertiesCache(Type type)
        {
            IEnumerable<PropertyInfo> pi;
            if (PartitionProperties.TryGetValue(type.TypeHandle, out pi))
            {
                return pi;
            }

            var allProperties = TypePropertiesCache(type);
            var keyProperties = allProperties.Where(p =>
                p.GetCustomAttributes(true).Any(a => a is PartitionKeyAttribute)).ToList();

            if (keyProperties.Count == 0)
            {
                var idProp = allProperties.Where(p => p.Name == "CompanyId").FirstOrDefault();
                if (idProp != null)
                {
                    keyProperties.Add(idProp);
                }
            }

            PartitionProperties[type.TypeHandle] = keyProperties;

            return keyProperties;
        }

        internal static byte[] ToByteArray<T>(T obj)
        {
            if (obj == null)
                return null;
                
            return obj.Serialize();
        }

        internal static T FromByteArray<T>(byte[] data)
        {
            if (data == null)
                return default(T);
            //Console.WriteLine("SIZE:  " + data.Length);
            object obj = data.Deserialize<T>();
            return (T)obj;
        }

        public void RelationshipListAdd<T>(RedisValue primaryKey, RedisValue foreignKey)
        {
            if (!Core.GetRedisConnection().IsConnected)
                return;

            Core.GetRedisDatabase().SetAdd($"{GetObjectCacheKey(typeof(T))}:{primaryKey}", foreignKey);
        }


        public RedisValue[] RelationshipListGet<T>(RedisValue primaryKey)
        {
            if (!Core.GetRedisConnection().IsConnected)
                return null;

            return Core.GetRedisDatabase().SetMembers($"{GetObjectCacheKey(typeof(T))}:{primaryKey}");
        }

    }
}
