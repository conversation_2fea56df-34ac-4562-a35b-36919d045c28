namespace Glav.CacheAdapter.Web
{
    public class PerRequestCacheHelper
    {
        public void AddToPerRequestCache(string cacheKey, object dataToAdd)
        {
            // If not in a web context, do nothing
            if (InWebContext() && Extric.Towbook.Web.HttpContextFactory.Instance.Items != null)
            {
                Extric.Towbook.Web.HttpContextFactory.Instance.Items[cacheKey] = dataToAdd;
            }
        }

        public void RemoveFromPerRequestCache(string cacheKey)
        {
            // If not in a web context, do nothing
            if (InWebContext())
            {
                if (Extric.Towbook.Web.HttpContextFactory.Instance.Items != null &&
                    Extric.Towbook.Web.HttpContextFactory.Instance.Items.Contains(cacheKey))
                {
                    Extric.Towbook.Web.HttpContextFactory.Instance.Items.Remove(cacheKey);
                }
            }
        }

        public T TryGetItemFromPerRequestCache<T>(string cacheKey) where T : class
        {
            // try per request cache first, but only if in a web context
            if (InWebContext())
            {
                if (Extric.Towbook.Web.HttpContextFactory.Instance.Items != null && 
                    Extric.Towbook.Web.HttpContextFactory.Instance.Items.Contains(cacheKey))
                {
                    var data = Extric.Towbook.Web.HttpContextFactory.Instance.Items[cacheKey];
                    var realData = data as T;
                    if (realData != null)
                    {
                        return realData;
                    }
                }
            }

            return null;
        }

        private static bool InWebContext()
        {
            return Extric.Towbook.Web.HttpContextFactory.Instance != null;
        }
    }
}
