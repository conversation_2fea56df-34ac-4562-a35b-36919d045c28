using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ProtoBuf;

namespace Extric.Towbook.Integrations.TomTom.Model
{
    [ProtoContract(ImplicitFields = ImplicitFields.AllFields)]
    public class Driver
    {
        public string Id { get; set; }
        public string Number { get; set; }
        public string Name { get; set; }
        public string Country { get; set; }
        public string State { get; set; }
        public string Zip { get; set; }
        public string City { get; set; }
        public string Street { get; set; }
        public string MobilePhone { get; set; }
        public string PrivatePhone { get; set; }
        public string Email { get; set; }
        public string Description { get; set; }
        public string Company { get; set; }
        public string Code { get; set; }
        public string ObjectNum { get; set; }
        public string SignonTime { get; set; }
        public string SignonRole { get; set; }
        public string CurrentWorkstate { get; set; }
        public string CardId { get; set; }
        public string CurrentWorkingTimestart { get; set; }
        public string CurrentWorkingTimeend { get; set; }
        public string ManualAssignment { get; set; }
        public string CurrentUnitId { get; set; }
        public string Pin { get; set; }
        public int DriverId { get; set; } // This is the Towbook driverId 
    }
}
