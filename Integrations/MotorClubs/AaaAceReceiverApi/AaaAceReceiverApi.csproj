<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk.Web">
	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<RootNamespace>Extric.Towbook.API.Integration.MotorClubs.AaaAce</RootNamespace>
		<AssemblyName>AaaAceReceiverApi</AssemblyName>
		<OutputType>Library</OutputType>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="NLog" Version="5.3.3" />
		<PackageReference Include="NLog.Extensions.Logging" Version="5.3.12" />
		<PackageReference Include="NLog.Targets.Loggly" Version="4.8.63" />
		<PackageReference Include="RestSharp" Version="111.4.1" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="6.7.1" />
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\..\..\Extric.Towbook.Integration.MotorClubs\Extric.Towbook.Integration.MotorClubs.csproj" />
	  <ProjectReference Include="..\..\..\Extric.Towbook.WebShared.Net5\Extric.Towbook.WebShared.Net5.csproj" />
	  <ProjectReference Include="..\Aaa\Aaa.csproj" />
	  <ProjectReference Include="..\Swoop\Swoop.csproj" />
	</ItemGroup>
</Project> 