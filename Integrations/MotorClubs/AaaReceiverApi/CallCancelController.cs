using Extric.Towbook.Integration.MotorClubs.Services;
using Extric.Towbook.Integrations.MotorClubs.Aaa.National;
using Newtonsoft.Json;
using NLog;
using System;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Extric.Towbook.Web;
using Extric.Towbook.WebShared;
using Microsoft.AspNetCore.Mvc;
using System.IO;

namespace Extric.Towbook.API.Integration.MotorClubs.Aaa
{
    [Route("receivers/aaa/callCancel")]
    public class CallCancelController : ControllerBase
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        [HttpPost]
        [Route("")]
        public async Task<ObjectResult> Post()
        {
            var rawJson = await Request.Body.ReadAsStringAsync();

            var log = new LogEventInfo();

            log.Level = LogLevel.Info;
            log.Message = "CallCancel";
            log.Properties["masterAccountName"] = "AaaNational";
            log.Properties["json"] = rawJson;

            if (this.Request.Headers.HasAuthorization())
                log.Properties.Add("Authorization", this.Request.Headers.AuthorizationParameter());

            try
            {
                var ccp = JsonConvert.DeserializeObject<CallCancelPayload>(rawJson);
                log.Properties["contractorId"] = ccp.CallEvents.First().StatusDetail.Facility.Id;

                var contractor = this.GetContractorById(ccp.CallEvents.First().StatusDetail.Facility.Id);
                if (contractor == null)
                    throw new Exception("Unknown ContractorId");

                log.Properties["queueItemId"] = await this.SendToBackendService(
                    DigitalDispatchService.CallEventType.Cancelled,
                    contractor,
                    rawJson,
                    ccp.CallEvents.First().CallKey);

                var resp = StatusCode((int)HttpStatusCode.OK, new AaaResponseModel<ResponsePayload>()
                {
                    StatusCode = "200",
                    StatusDescription = "Received",
                    SourceSystem = new AaaSystem(),
                    Header = new AaaResponseModel<ResponsePayload>.HeaderModel()
                    {
                        Id = ccp.Id,
                        Status = "OK",
                        SubType = ccp.SubType,
                        Type = ccp.RequestEventType,
                        Version = ccp.Version,
                    },
                    ResponsePayload = new ResponsePayload()
                    {
                        CallKey = ccp.CallEvents.First().CallKey,
                        MemberID = "",
                        Pta = "",
                        PtaInMinutes = 0
                    }
                });

                Response.Headers["X-Twbk-Id"] = log.Properties["queueItemId"].ToString();
                return resp;

            }
            catch (Exception y)
            {
                log.Properties["exception"] = y;
                log.Level = LogLevel.Error;

                throw;
            }
            finally
            {
                logger.Log(log);
            }

        }
    }
}


