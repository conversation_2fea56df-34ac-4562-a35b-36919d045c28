using RestSharp;
using Extric.Towbook.Utility;
using Newtonsoft.Json;
using System.Net;
using Agero;
using RestSharp.Authenticators.OAuth2;
using System;
using System.Net.Http;
using Extric.Towbook.Configuration;


namespace Extric.Towbook.Integrations.MotorClubs.Agero
{
    public class DispatchCreatorClient
    {
        private const string CONSUMER_KEY = "EGfUhUrkEktRmfFtb81gjCpN9YLe7FHG";
        private const string URL_BASE = "http://agero-stg.apigee.net/";

        private static HttpClient httpClient
        {
            get => AppServicesHelper.HttpClient;
        }
            
        public class AutomatedDispatchRequest
        {
            public int TenantID { get; set; }
            public string VendorID { get; set; }
            public AutomatedDispatchRequestAddress DisablementLocationAddress { get; set; }
            public string ServiceType { get; set; }
            public string CustomerPhoneNumber { get; set; }
            public string PaymentMethod { get; set; }
        }

        public class AutomatedDispatchRequestAddress
        {
            public string ZipCode { get; set; }
            public string StateAbbreviation { get; set; }

            public string City { get; set; }
            public string Address { get; set; }
            public string CrossStreet { get; set; }

            public double Latitude { get; set; }
            public double Longitude { get; set; }
        }

        public dynamic AutomatedDispatch(AutomatedDispatchRequest req)
        {
            var options = new RestClientOptions(URL_BASE);
            options.Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator(CONSUMER_KEY, "");

            var client = new RestClient(httpClient, options);
            
            var request = new RestRequest("/v1/dispatchcreator/AutomatedDispatch", Method.Post);

            request.AddParameter("application/json", req.ToJson(), ParameterType.RequestBody);

            var response = client.Execute(request);

            string r = response.Content;
            dynamic json = JsonConvert.DeserializeObject<dynamic>(r);
            if (response.StatusCode != HttpStatusCode.OK && response.StatusCode != HttpStatusCode.Created)
                AgeroRestClient.CustomizeExceptions(response);

            return json;
        }

    }
}
