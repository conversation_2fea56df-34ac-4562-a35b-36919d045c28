using System;
using System.Net.Http;
using Extric.Towbook.Utility;
using NLog;
using RestSharp;

namespace Extric.Towbook.Integrations.MotorClubs.StackThree
{
    public class IncomingModelBase
    {
        public string DispatchId { get; set; }
        public string ContractorId { get; set; }
        public string LocationId { get; set; }
        public string ResponseType { get; set; }
        public string PoNumber { get; set; }
        public string CaseNumber { get; set; }
        public string Notes { get; set; }
    }

    public class IncomingAcceptedModel : IncomingModelBase
    {

    }

    public class IncomingExpiredModel : IncomingModelBase
    {

    }

    public class IncomingRefusedModel : IncomingModelBase
    {

    }

    public class IncomingCancelledModel : IncomingModelBase
    {
    }

    public class IncomingGoaModel : IncomingModelBase
    {
        public bool Approved { get; set; }
    }

    /*
    public class DispatchModel
    {
        public string DispatchId { get; set; }
        public string Provider { get; set; }
        public string ContractorId { get; set; }
        public string LocationId { get; set; }
        public string[] Actions { get; set; }
        public Job Job { get; set; }
    }

    public class Job
    {
        public int RequiredAcknowledgeTimeInSeconds { get; set; }
        public string JobId { get; set; }
        public DateTime Timestamp { get; set; }
        public int? MaxETA { get; set; }
        public string Type { get; set; }
        public string Price { get; set; }
        public Customer Customer { get; set; }
        public Vehicle Vehicle { get; set; }
        public Incidentaddress IncidentAddress { get; set; }
    }

    public class Customer
    {
        public string MemberName { get; set; }
        public string CustomerName { get; set; }
        public string CustomerPhone { get; set; }
        public string CustomerPhoneType { get; set; }
    }

    public class Vehicle
    {
        public string Year { get; set; }
        public string Make { get; set; }
        public string Model { get; set; }
        public string Color { get; set; }
        public string LicensePlate { get; set; }
        public string Vin { get; set; }
        public string VehicleType { get; set; }
        public string Odometer { get; set; }
        public string AdditionalInformation { get; set; }
    }

    public class Incidentaddress
    {
        public string Address { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string Zip { get; set; }
        public decimal Latitude { get; set; }
        public decimal Longitude { get; set; }
        public string Landmark { get; set; }
        public string SafeLocation { get; set; }
        public string PersonInCar { get; set; }
    }
    */

    public class StackThreeRestClient
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();
        public string UrlBase = "";

        private static readonly HttpClient _httpClient = new(new SocketsHttpHandler
        {
            PooledConnectionLifetime = TimeSpan.FromMinutes(5),
            PooledConnectionIdleTimeout = TimeSpan.FromSeconds(30)
        });

        private readonly RestClient _restClient;

        public string AuthenticationToken { get; private set; }

        public string EnvironmentName { get; set; }
        public string CertificateThumbprint { get; set; }
        public string Ip { get; set; }
        public long QueueItemId { get; private set; }

        public static string GetEnvironmentName(int env)
        {
            if (env == 1)
                return "DEV";
            if (env == 2)
                return "TEST";
            else if (env == 3)
                return "PROD";

            return "DEV";
        }

        public static int GetEnvironmentId(string env)
        {
            if (env != null)
            {
                if (env.EndsWith("DEV"))
                    return 1;
                if (env.EndsWith("TEST"))
                    return 2;
                else if (env.EndsWith("PROD"))
                    return 3;
            }

            return 1;
        }


        public StackThreeRestClient(string environment, string ip, long queueItemId = 0)
        {
            EnvironmentName = environment;

            if (environment == "UAT")
            {
                UrlBase = "";
            }
            else if (environment == "DEV" || environment == "TEST")
            {
                UrlBase = Core.GetAppSetting("MotorClubs:StackThree:Test:Url");
                AuthenticationToken = Core.GetAppSetting("MotorClubs:StackThree:Test:ApiKey");
            }
            else if (environment == "PROD")
            {
                UrlBase = Core.GetAppSetting("MotorClubs:StackThree:Production:Url");
                AuthenticationToken = Core.GetAppSetting("MotorClubs:StackThree:Production:ApiKey");
            }
            else if (environment == "DRIVEN_PROD")
            {
                UrlBase = Core.GetAppSetting("MotorClubs:DrivenSolutions:Production:Url");
                AuthenticationToken = Core.GetAppSetting("MotorClubs:DrivenSolutions:Production:ApiKey");
            }
            else if (environment == "DRIVEN_DEV" || environment == "DRIVEN_TEST")
            {
                UrlBase = Core.GetAppSetting("MotorClubs:DrivenSolutions:Test:Url");
                AuthenticationToken = Core.GetAppSetting("MotorClubs:DrivenSolutions:Test:ApiKey");
            }
            Ip = ip;
            QueueItemId = queueItemId;

            var options = new RestClientOptions(UrlBase);
            _restClient = new RestClient(_httpClient, options);
        }

        private RestClient GetRestClient() => _restClient;

        public LogEventInfo NewLogEvent(string message, string json = null)
        {
            var log = new LogEventInfo();

            log.Level = LogLevel.Info;
            log.Message = message;

            if (json != null)
                log.Properties["json"] = json;

            log.Properties["commitId"] = Core.GetCommitId();
            log.Properties["masterAccountName"] = "StackThree";
            log.Properties["environment"] = EnvironmentName;
            log.Properties["requestingIp"] = Ip;

            if (QueueItemId > 0)
                log.Properties["queueItemId"] = QueueItemId;

            return log;
        }

        public class DispatchAcceptModel
        {
            public string Type { get; set; } = "accept";
            public string DispatchId { get; set; }
            public string ContractorId { get; set; }
            public string LocationId { get; set; }
            public string ResourceName { get; set; } = "";

            public int Eta { get; set; }
            public int? ReasonId { get; set; }
            public AcceptProviderPayload Provider { get; set; }
        }


        public class AcceptProviderPayload
        {
            public string ContractorId { get; set; }
            public string Name { get; set; }
            public string Address { get; set; }
            public string City { get; set; }
            public string State { get; set; }
            public string Zip { get; set; }
            public decimal Latitude { get; set; }
            public decimal Longitude { get; set; }
            public string Phone { get; set; }
            public string Email { get; set; }
        }



        public class DispatchRefuseModel
        {
            public string Type { get; set; } = "refuse";
            public string DispatchId { get; set; }
            public string ContractorId { get; set; }
            public string LocationId { get; set; }
            public int ReasonId { get; set; }
            public string ReasonName { get; set; }
            public string ResourceName { get; set; } = "";
            public string Status { get; set; } = "Refuse";
        }

        public class DispatchCancelModel
        {
            public string Type { get; set; } = "cancel";
            public string DispatchId { get; set; }
            public string ContractorId { get; set; }
            public string LocationId { get; set; }

            public int ReasonId { get; set; }
            public string ReasonName { get; set; }
            public string Comments { get; set; }
            public string ResourceName { get; set; }
        }



        public class DispatchStatusUpdateModel
        {
            public string Type { get; set; } = "statusUpdate";
            public string DispatchId { get; set; }
            public string ContractorId { get; set; }
            public string LocationId { get; set; }
            public string Status { get; set; }
            public DriverModel Driver { get; set; }

            public string ResourceName { get; set; }
            public decimal Latitude { get; set; }
            public decimal Longitude { get; set; }

        }

        public class DispatchPhotoModel
        {
            public string Type { get; set; } = "photo";
            public string DispatchId { get; set; }
            public string ContractorId { get; set; }
            public string LocationId { get; set; }
            public decimal Latitude { get; set; }
            public decimal Longitude { get; set; }
            public DateTime Timestamp { get; set; }
            public string Url { get; set; }
            public string ResourceName { get; set; } = "";
            public string CallStatus { get; set; }
        }

        public class DispatchGoaModel
        {
            public string Type { get; set; } = "goa";
            public string DispatchId { get; set; }
            public string ContractorId { get; set; }
            public string LocationId { get; set; }
            public string ReasonId { get; set; }
            public string ReasonName { get; set; }
            public string Comments { get; set; }
            public DateTime Timestamp { get; set; }
            public string ResourceName { get; set; }
        }

        public class DispatchExtendEtaModel
        {
            public string Type { get; set; } = "extend_eta";
            public string DispatchId { get; set; }
            public string ContractorId { get; set; }
            public string LocationId { get; set; }
            public string Reason { get; set; }
            public string Comments { get; set; }
            public string ResourceName { get; set; }

            public DateTime NewEta { get; set; }
        }

        public class DispatchUnsuccesfulServiceModel
        {
            public string Type { get; set; } = "unsuccessful_service";
            public string DispatchId { get; set; }
            public string ContractorId { get; set; }
            public string LocationId { get; set; }
            public string Reason { get; set; }
            public string Comments { get; set; }
            public string ResourceName { get; set; }
        }

        public class DriverModel
        {
            public string Id { get; set; }
            public string FirstName { get; set; }
            public string LastName { get; set; }
        }

        public class DispatchCompleteModel
        {
            public string Type { get; set; } = "complete";
            public string DispatchId { get; set; }
            public string ContractorId { get; set; }
            public string LocationId { get; set; }
            public DriverModel Driver { get; set; }

            public string ResolutionCode { get; set; }

            public InvoiceItemModel[] InvoiceItems { get; set; }

            public class InvoiceItemModel
            {
                public string Code { get; set; }
                public decimal Quantity { get; set; }
                public decimal Price { get; set; }
            }
        }

        public void Accept(DispatchAcceptModel payload)
        {
            if (payload.Type != "accept")
                throw new ArgumentException("Type must be: accept");

            var log = NewLogEvent("Accept", payload.ToJson());

            try
            {
                var resp = Send(CallUpdateUrl + "/accept", payload.ToJson(), Method.Post);

                log.Properties["contractorId"] = payload.ContractorId;
                log.Properties["dispatchId"] = payload.DispatchId;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;

                    throw new Exception("Error invoking accepted:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            finally
            {
                logger.Log(log);
            }
        }

        public void Refuse(DispatchRefuseModel payload)
        {
            if (payload.Type != "refuse")
                throw new ArgumentException("Type must be: refuse");

            var log = NewLogEvent("Refuse", payload.ToJson());

            try
            {
                var resp = Send(CallUpdateUrl + "/reject", payload.ToJson(),
                    Method.Post);

                log.Properties["contractorId"] = payload.ContractorId;
                log.Properties["dispatchId"] = payload.DispatchId;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;

                    throw new Exception("Error invoking Refused endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            finally
            {
                logger.Log(log);
            }
        }

        private static readonly string[] ValidStatuses = new string[] { "DISPATCHED", "EN_ROUTE", "ON_SCENE", "TOWING", "DESTINATION", "COMPLETE" };

        public class StatusCodes
        {
            public const string Dispatched = "DISPATCHED";
            public const string Enroute = "EN_ROUTE";
            public const string OnScene = "ON_SCENE";
            public const string Towing = "TOWING";
            public const string Destination = "DESTINATION";
            public const string Complete = "COMPLETE";
        }

        public void StatusUpdate(DispatchStatusUpdateModel payload)
        {
            var log = NewLogEvent("StatusUpdate", payload.ToJson());

            try
            {
                if (payload.Type != "statusUpdate")
                    throw new ArgumentException("Type must be: statusUpdate");


                if (payload.Status != StatusCodes.Dispatched &&
                    payload.Status != StatusCodes.Enroute &&
                    payload.Status != StatusCodes.OnScene &&
                    payload.Status != StatusCodes.Towing &&
                    payload.Status != StatusCodes.Destination &&
                    payload.Status != StatusCodes.Complete)
                    throw new ArgumentException("Invalid Status value passed: " + payload.Status + ".");

                var resp = Send(CallUpdateUrl + "/status_update", payload.ToJson(), Method.Post);

                log.Properties["contractorId"] = payload.ContractorId;
                log.Properties["dispatchId"] = payload.DispatchId;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;

                    throw new Exception("Error invoking Status Update endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            finally
            {
                logger.Log(log);
            }
        }

        public class DispatchBreadcrumbPayload
        {
            public string Type { get; set; } = "breadcrumb";
            public string DispatchId { get; set; }
            public DateTime Timestamp { get; set; }
            public decimal Latitude { get; set; }
            public decimal Longitude { get; set; }
            public string ContractorId { get; set; }
            public string DriverId { get; set; }
        }

        public void Breadcrumb(DispatchBreadcrumbPayload payload)
        {
            if (payload.Type != "breadcrumb")
                throw new ArgumentException("Type must be: breadcrumb");

            var log = NewLogEvent("Breadcrumb", payload.ToJson());

            try
            {
                var resp = Send(CallUpdateUrl + "/breadcrumb", payload.ToJson(), Method.Post);

                log.Properties["dispatchId"] = payload.DispatchId;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;

                    throw new Exception("Error invoking Refused endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            finally
            {
                logger.Log(log);
            }
        }

        public void Photo(DispatchPhotoModel payload)
        {
            if (payload.Type != "photo")
                throw new ArgumentException("Type must be: photo");

            var log = NewLogEvent("Photo", payload.ToJson());

            try
            {
                var resp = Send(CallUpdateUrl + "/photo_upload", payload.ToJson(), Method.Post);

                log.Properties["contractorId"] = payload.ContractorId;
                log.Properties["dispatchId"] = payload.DispatchId;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;

                    throw new Exception("Error invoking Refused endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            finally
            {
                logger.Log(log);
            }
        }

        private const string CallUpdateUrl = "/towbook";

        public void Cancel(DispatchCancelModel payload)
        {
            if (payload.Type != "cancel")
                throw new ArgumentException("Type must be: cancel");

            var log = NewLogEvent("Cancel", payload.ToJson());

            try
            {
                var resp = Send(CallUpdateUrl + "/cancel_call", payload.ToJson(), Method.Post);

                log.Properties["contractorId"] = payload.ContractorId;
                log.Properties["dispatchId"] = payload.DispatchId;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;

                    throw new Exception("Error invoking Cancel endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            finally
            {
                logger.Log(log);
            }
        }


        public void Goa(DispatchGoaModel payload)
        {
            if (payload.Type != "goa")
                throw new ArgumentException("Type must be goa");

            var log = NewLogEvent("GOA", payload.ToJson());

            try
            {
                var resp = Send(CallUpdateUrl + "/request_goa", payload.ToJson(), Method.Post);

                log.Properties["contractorId"] = payload.ContractorId;
                log.Properties["dispatchId"] = payload.DispatchId;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;

                    throw new Exception("Error invoking GOA endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            finally
            {
                logger.Log(log);
            }
        }

        public void ExtendEta(DispatchExtendEtaModel payload)
        {
            if (payload.Type != "extend_eta")
                throw new ArgumentException("Type must be goa");

            var log = NewLogEvent("ExtendETA", payload.ToJson());

            try
            {
                var resp = Send(CallUpdateUrl + "/extend_eta", payload.ToJson(), Method.Post);

                log.Properties["contractorId"] = payload.ContractorId;
                log.Properties["dispatchId"] = payload.DispatchId;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;

                    throw new Exception("Error invoking ExtendETA endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            finally
            {
                logger.Log(log);
            }
        }

        public void UnsuccessfulService(DispatchUnsuccesfulServiceModel payload)
        {
            if (payload.Type != "unsuccessful_service")
                throw new ArgumentException("Type must be unsuccessful_service");

            var log = NewLogEvent("UnsuccessfulService", payload.ToJson());

            try
            {
                var resp = Send(CallUpdateUrl + "/unsuccessful_service", payload.ToJson(), Method.Post);

                log.Properties["contractorId"] = payload.ContractorId;
                log.Properties["dispatchId"] = payload.DispatchId;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;

                    throw new Exception("Error invoking UnsuccessfulService endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            finally
            {
                logger.Log(log);
            }
        }


        public void Login(string contractorId)
        {
            var log = NewLogEvent("Login", contractorId);

            try
            {
                var resp = Send(CallUpdateUrl + "/session/login",
                    new
                    {
                        contractorId
                    }.ToJson(), Method.Post);

                log.Properties["contractorId"] = contractorId;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;

                    throw new Exception("Error invoking Login endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            finally
            {
                logger.Log(log);
            }
        }


        public void Logout(string contractorId)
        {
            var log = NewLogEvent("Logout", contractorId);

            try
            {
                var resp = Send(CallUpdateUrl + "/session/logout",
                    new
                    {
                        contractorId
                    }.ToJson(), Method.Post);

                log.Properties["contractorId"] = contractorId;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;

                    throw new Exception("Error invoking Logout endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            finally
            {
                logger.Log(log);
            }
        }



        public RestResponse Send(string url, string payload, Method method)
        {
            var client = GetRestClient();

            var request = new RestRequest(url, method);

            request.AddParameter("Authorization", "Token " + AuthenticationToken, ParameterType.HttpHeader);

            if (method != Method.Get)
                request.AddParameter("application/json", payload, ParameterType.RequestBody);

            Console.WriteLine(payload);

            var response = client.Execute(request);

            Console.WriteLine(DateTime.Now.ToString() + " " + UrlBase + url + ": " + response.Content);

            return response;
        }
    }
}
