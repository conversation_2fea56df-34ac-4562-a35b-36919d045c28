using RestSharp;
using System;
using System.Linq;
using System.Net.Http;
using HttpClient = System.Net.Http.HttpClient;
using System.Xml.Linq;

namespace Extric.Towbook.Integrations.MotorClubs.Gerber;

public class DispatchModel
{
    public string DispatchId { get; set; }
    public Customer Customer { get; set; }
    public Provider[] Providers { get; set; }
    public Order Order { get; set; }
    public Vehicle Vehicle { get; set; }
    public Incident Incident { get; set; }
    public Coverage Coverage { get; set; }
    public Destination Destination { get; set; }
}

public class Customer
{
    public string Name { get; set; }
    public Phone[] Phone { get; set; }
}

public class Phone
{
    public string Type { get; set; }
    public string Value { get; set; }
}

public class Order
{
    public Nullable<DateTime> Timestamp { get; set; }
    public int Expires { get; set; }
    public int MaxEta { get; set; }
    public string Cause { get; set; }
    public string DamageDescription { get; set; }
    public string AssignedBy { get; set; }
    public DateTime AssignedTime { get; set; }
    public string Notes { get; set; }
}

public class Vehicle
{
    public string Year { get; set; }
    public string Color { get; set; }
    public string Make { get; set; }
    public string Model { get; set; }
    public string Vin { get; set; }
    public string Style { get; set; }
    public string Plate { get; set; }
    public string PlateState { get; set; }

    public override string ToString()
    {
        return Year + " " + Make + " " + Model + (Color != null ? " (" + Color + ")" : "");
    }
}

public class Incident
{
    public string Name { get; set; }
    public string Address1 { get; set; }
    public string City { get; set; }
    public string State { get; set; }
    public string Zip { get; set; }
    public decimal Latitude { get; set; }
    public decimal Longitude { get; set; }
    public string Comments { get; set; }

    public override string ToString()
    {
        return (Address1 + ", " + City + " " + State + " " + Zip).Trim().Trim(',');
    }

    public Dispatch.EntryWaypoint ToWaypoint(Dispatch.EntryWaypoint wp)
    {
        if (wp == null)
            throw new ArgumentNullException("wp");

        wp.Address = (Address1 + ", " + City + " " + State + " " + Zip).Trim().Trim(',');
        wp.Latitude = Latitude;
        wp.Longitude = Longitude;

        return wp;
    }
}

public class Coverage
{
    public string WorkAuthorized { get; set; }
    public float TowLimit { get; set; }
    public float Deductible { get; set; }
    public bool DeductibleWaived { get; set; }
}

public class Destination
{
    public string Name { get; set; }
    public string Address1 { get; set; }
    public string City { get; set; }
    public string State { get; set; }
    public string Zip { get; set; }
    public decimal Latitude { get; set; }
    public decimal Longitude { get; set; }
    public string Phone { get; set; }

    public override string ToString()
    {
        return (Address1 + ", " + City + " " + State + " " + Zip).Trim().Trim(',');
    }

    public Dispatch.EntryWaypoint ToWaypoint(Dispatch.EntryWaypoint wp)
    {
        if (wp == null)
            throw new ArgumentNullException("wp");

        wp.Address = (Address1 + ", " + City + " " + State + " " + Zip).Trim().Trim(',');
        wp.Latitude = Latitude;
        wp.Longitude = Longitude;
        wp.Title = "Destination";
        wp.Position = 2;

        return wp;
    }

}

public class Provider
{
    public string ProviderId { get; set; }
    public string LocationId { get; set; }
    public string Name { get; set; }

    public string ContactFullName { get; set; }
    public string ContactEmail { get; set; }

    public string Address { get; set; }
    public string City { get; set; }
    public string State { get; set; }
    public string Zip { get; set; }

    public string Phone { get; set; }
    public string Fax { get; set; }

    public string TaxId { get; set; }

    /// <summary>
    /// How much are you offering to pay this towing company/roadside provider to do this work?
    /// </summary>
    public decimal OfferPrice { get; set; }
}

public class Error
{
    public string errorCode { get; set; }
    public string message { get; set; }
}

public class GerberRestClient
{
    private static readonly HttpClient _httpClient = new(new SocketsHttpHandler
    {

        PooledConnectionLifetime = TimeSpan.FromMinutes(5),
        PooledConnectionIdleTimeout = TimeSpan.FromSeconds(30),
    });
    
    private readonly RestClient _restClient;
    private readonly string USERNAME;
    private readonly string PASSWORD;
    private readonly string URL_BASE; 

    private GerberRestClient(string urlBase, string username, string password)
    {
        URL_BASE = urlBase;
        USERNAME = username;
        PASSWORD = password;

        var options = new RestClientOptions(URL_BASE)
        {
            UserAgent = "Towbook/4.0.0"
        };

        _restClient = new RestClient(_httpClient, options);
    }

    public static GerberRestClient Get()
    {
        return new GerberRestClient(
            urlBase: "https://secure.gerberncs.com/webservice",
            username: "TOWBOOKWEBSERVICES",
            password: "tb18!GNCS45$");
    }

    public RestClient GetRestClient() => _restClient;

    public RestRequest GetBaseRequest(string action)
    {
        if (!new string[] { "Accept", "Refuse", "RequestPhoneCall", "Registration", "StatusUpdate", "LocationUpdate", "Invoice", "AddClaimComment" }.Contains(action))
            throw new Exception("Invalid Action: " + action);

        var request = new RestRequest($"/", Method.Get);
        if (action == "Invoice")
            request.AddParameter("ServiceName", "Invoice", ParameterType.QueryString);

        if (action == "AddClaimComment")
            request.AddParameter("ServiceName", "AddClaimComment", ParameterType.QueryString);
        else
            request.AddParameter("ServiceName", "ers", ParameterType.QueryString);
        request.AddParameter("user", USERNAME, ParameterType.QueryString);
        request.AddParameter("password", PASSWORD, ParameterType.QueryString);

        if (action != "Invoice" && action != "AddClaimComment")
            request.AddParameter("action", action, ParameterType.QueryString);

        return request;
    }

    public string Accept(string claimId, string dispatchId, string vendorLocationId, int eta, 
        decimal? latitude = 0, 
        decimal? longitude = 0, 
        string driverId = null)
    {
        var request = GetBaseRequest("Accept");

        request.AddParameter("claimid", claimId, ParameterType.QueryString);
        request.AddParameter("dispatchid", dispatchId, ParameterType.QueryString);
        request.AddParameter("vendorlocationid", vendorLocationId, ParameterType.QueryString);
        request.AddParameter("eta", eta, ParameterType.QueryString);

        if (latitude != null && longitude != null)
        {
            request.AddParameter("latitude", latitude, ParameterType.QueryString);
            request.AddParameter("longitude", longitude, ParameterType.QueryString);
        }

        if (!string.IsNullOrWhiteSpace(driverId))
            request.AddParameter("driverId", longitude, ParameterType.QueryString);

        var response = GetRestClient().Execute(request);

        var responseText = response.Content.ToString();
        XElement x = XElement.Parse(responseText);
        

        var error = x.Element("error").Value;
        var result = x.Element("result").Value;

        Console.WriteLine("Error:" + error);
        Console.WriteLine("Result:" + result);

        return responseText;
    }

    public string Refuse(string claimId, string dispatchId, string vendorLocationId, string reason)
    {
        var request = GetBaseRequest("Refuse");

        request.AddParameter("claimid", claimId, ParameterType.QueryString);
        request.AddParameter("dispatchid", dispatchId, ParameterType.QueryString);
        request.AddParameter("vendorlocationid", vendorLocationId, ParameterType.QueryString);
        request.AddParameter("reason", reason, ParameterType.QueryString);

        var response = GetRestClient().Execute(request);

        return response.Content.ToString();
    }

    public string RequestPhoneCall(string claimId, string dispatchId, string vendorLocationId, string phone)
    {
        var request = GetBaseRequest("RequestPhoneCall");

        phone = Core.FormatPhoneWithDashesOnly(phone);
        if (!Core.IsPhoneValidStandard(phone))
            throw new Exception("RequestPhoneCall: Phone number is invalid: " + phone);

        request.AddParameter("claimid", claimId, ParameterType.QueryString);
        request.AddParameter("dispatchid", dispatchId, ParameterType.QueryString);
        request.AddParameter("vendorlocationid", vendorLocationId, ParameterType.QueryString);
        request.AddParameter("phone", phone, ParameterType.QueryString);

        var response = GetRestClient().Execute(request);

        return response.Content.ToString();
    }


    public string ShareLink(string claimId,
        string customerId,
        string comment,
        string link
        )
    {
        var request = GetBaseRequest("AddClaimComment");
        request.AddParameter("claimid", claimId, ParameterType.QueryString);
        request.AddParameter("customerID", customerId, ParameterType.QueryString);
        request.AddParameter("comment", comment, ParameterType.QueryString);
        request.AddParameter("link", link, ParameterType.QueryString);

        var response = GetRestClient().Execute(request);

        return response.Content.ToString();
    }



    public string Survey(string claimId, 
        string dispatchId, 
        string vendorLocationId, 
        int? surveyInviteTimestamp,
        int? surveyCompleteTimestamp,
        string surveyChannel,
        int? surveyScore,
        int? surveyQ1,
        int? surveyQ2,
        int? surveyQ3,
        int? surveyQ4,
        int? surveyQ5
        )
    {
        var request = GetBaseRequest("AddClaimComment");
        request.AddParameter("claimid", claimId, ParameterType.QueryString);
        request.AddParameter("dispatchid", dispatchId, ParameterType.QueryString);
        request.AddParameter("vendorlocationid", vendorLocationId, ParameterType.QueryString);

        if (surveyInviteTimestamp != null)
            request.AddParameter("surveyInviteTimestamp", surveyInviteTimestamp, ParameterType.QueryString);

        if (surveyCompleteTimestamp != null)
            request.AddParameter("surveyCompleteTimestamp", surveyCompleteTimestamp, ParameterType.QueryString);

        if (surveyChannel != null)
            request.AddParameter("surveyChannel", surveyChannel, ParameterType.QueryString);

        if (surveyScore != null)
            request.AddParameter("surveyScore", surveyScore, ParameterType.QueryString);

        if (surveyQ1 != null)
            request.AddParameter("surveyQ1", surveyQ1, ParameterType.QueryString);

        if (surveyQ2 != null)
            request.AddParameter("surveyQ2", surveyQ2, ParameterType.QueryString);

        if (surveyQ3 != null)
            request.AddParameter("surveyQ3", surveyQ3, ParameterType.QueryString);

        if (surveyQ4 != null)
            request.AddParameter("surveyQ4", surveyQ4, ParameterType.QueryString);

        if (surveyQ5 != null)
            request.AddParameter("surveyQ5", surveyQ5, ParameterType.QueryString);

        var response = GetRestClient().Execute(request);

        return response.Content.ToString();
    }

    public void Register(string registrationData)
    {

    }

    public string LocationUpdate(
        string claimId, 
        string dispatchId, 
        string vendorLocationId, 
        decimal latitude, 
        decimal longitude, 
        string driverId = null)
    {
        if (latitude == 0 || longitude == 0)
            return "NOT_SENT";

        var request = GetBaseRequest("LocationUpdate");

        request.AddParameter("claimid", claimId, ParameterType.QueryString);
        request.AddParameter("dispatchid", dispatchId, ParameterType.QueryString);
        request.AddParameter("vendorlocationid", vendorLocationId, ParameterType.QueryString);
        request.AddParameter("driverId", driverId, ParameterType.QueryString);
        request.AddParameter("latitude", latitude, ParameterType.QueryString);
        request.AddParameter("longitude", longitude, ParameterType.QueryString);

        var response = GetRestClient().Execute(request);

        return response.Content.ToString();
    }

    public void Cancel()
    {
        // TODO: need info from Gerber
    }

    public string StatusUpdate(
        string claimId,
        string dispatchId,
        string vendorLocationId,
        string status,
        decimal? latitude = null,
        decimal? longitude = null,
        string driverId = null,
        int? etda = 0)
    {
        if (latitude == 0 || longitude == 0)
        {
            latitude = null;
            longitude = null;
        }

        var request = GetBaseRequest("StatusUpdate");

        request.AddParameter("claimid", claimId, ParameterType.QueryString);
        request.AddParameter("dispatchid", dispatchId, ParameterType.QueryString);
        request.AddParameter("vendorlocationid", vendorLocationId, ParameterType.QueryString);
        request.AddParameter("status", status, ParameterType.QueryString);
        request.AddParameter("driverId", driverId, ParameterType.QueryString);
        request.AddParameter("latitude", latitude, ParameterType.QueryString);
        request.AddParameter("longitude", longitude, ParameterType.QueryString);

        if (etda != null)
            request.AddParameter("edta", etda.GetValueOrDefault(), ParameterType.QueryString);

        var response = GetRestClient().Execute(request);

        return response.Content.ToString();
    }

    public string SendInvoice(
       string claimId,
       string invoiceNumber,
       string insuredName,
       string insuredAddress,
       string insuredCity,
       string insuredState,
       string insuredZipcode,
       string insuredPhone,
       DateTime dateOfLoss,
       string causeOfLoss,
       string taxId,
       string remitName,
       string remitAddress,
       string remitCity,
       string remitState,
       string remitZipcode,
       string vehicleYear,
       string vehicleMake,
       string vehicleModel,
       string vehicleStyle,
       string vehiclePlate,
       string vehicleVIN,
       string policyNumber,
       decimal totalGlassCost,
       decimal totalLaborCost,
       decimal totalUrethaneCost,
       decimal totalMouldingCost,
       decimal totalOtherCost,
       decimal subTotalCost,
       decimal taxTotal,
        decimal grandTotalCost,
        decimal deductible,
        DateTime workDoneDate
        )
    {

        var request = GetBaseRequest("Invoice");

        request.AddParameter("claimID", claimId, ParameterType.QueryString);

        request.AddParameter("invoiceNumber", invoiceNumber);
        request.AddParameter("insuredName", insuredName);
        request.AddParameter("insuredAddress", insuredAddress);
        request.AddParameter("insuredCity", insuredCity);
        request.AddParameter("insuredState", insuredState);
        request.AddParameter("insuredZipcode", insuredZipcode);
        request.AddParameter("insuredPhone", insuredPhone);
        request.AddParameter("dateOfLoss", dateOfLoss.ToShortDateString());
        request.AddParameter("causeOfLoss", causeOfLoss);
        request.AddParameter("taxID", taxId);
        request.AddParameter("remitName", remitName);
        request.AddParameter("remitAddress", remitAddress);
        request.AddParameter("remitCity", remitCity);
        request.AddParameter("remitState", remitState);
        request.AddParameter("remitZipcode", remitZipcode);
        request.AddParameter("vehicleYear", vehicleYear);
        request.AddParameter("vehicleMake", vehicleMake);
        request.AddParameter("vehicleModel", vehicleModel);
        request.AddParameter("vehicleStyle", vehicleStyle);
        request.AddParameter("vehiclePlate", vehiclePlate);
        request.AddParameter("vehicleVIN", vehicleVIN.ToUpper());
        request.AddParameter("policyNumber", policyNumber);
        request.AddParameter("totalGlassCost", totalGlassCost.ToString("0.00"));
        request.AddParameter("totalLaborCost", totalLaborCost.ToString("0.00"));
        request.AddParameter("totalUrethaneCost", totalUrethaneCost.ToString("0.00"));
        request.AddParameter("totalMouldingCost", totalMouldingCost.ToString("0.00"));
        request.AddParameter("totalOtherCost", totalOtherCost.ToString("0.00"));
        request.AddParameter("subTotalCost", subTotalCost.ToString("0.00"));
        request.AddParameter("taxTotal", taxTotal.ToString("0.00"));
        request.AddParameter("grandTotalCost", grandTotalCost.ToString("0.00"));
        request.AddParameter("deductible", deductible.ToString("0.00"));
        request.AddParameter("workDoneDate", workDoneDate.ToShortDateString());

        var response = GetRestClient().Execute(request);
        return response.Content.ToString();
    }
}
