using Extric.Towbook.Accounts;
using Extric.Towbook.Utility;
using NLog;
using RestSharp;
using System;
using System.Net.Http;

namespace Extric.Towbook.Integrations.MotorClubs.Nac;

public class IncomingModelBase
{
    public string DispatchId { get; set; }
    public string ContractorId { get; set; }
    public string LocationId { get; set; }
    public string ResponseType { get; set; }
    public string PoNumber { get; set; }
    public string CaseNumber { get; set; }
    public string Notes { get; set; }
}

public class IncomingAcceptedModel : IncomingModelBase
{

}

public class IncomingExpiredModel : IncomingModelBase
{

}

public class IncomingRefusedModel : IncomingModelBase
{

}

public class IncomingCancelledModel : IncomingModelBase
{
}

public class IncomingGoaModel : IncomingModelBase
{
    public bool Approved { get; set; }
}

public class NacRestClient
{
    private static readonly HttpClient _httpClient = new(new SocketsHttpHandler
    {
        PooledConnectionLifetime = TimeSpan.FromMinutes(5),
        PooledConnectionIdleTimeout = TimeSpan.FromSeconds(30)
    });
    private static readonly Logger logger = LogManager.GetCurrentClassLogger();
    private static readonly string[] ValidStatuses = ["DISPATCHED", "EN_ROUTE", "ON_SCENE", "TOWING", "DESTINATION", "COMPLETE"];
    private readonly RestClient _restClient;

    public NacRestClient(string environment, string ip)
    {
        EnvironmentName = environment;

        if (environment == "DEV")
        {
            URL_BASE = "https://digitaldispatch.thenac.com/NACAPI/api/TowBook";
            AuthenticationToken = "8VrTfhXznwy76j7b3kJP3wgb";
        }
        else if (environment == "TEST")
        {
            URL_BASE = "https://digitaldispatch.thenac.com/NACAPI/api/TowBook";
            AuthenticationToken = "8VrTfhXznwy76j7b3kJP3wgb";
        }
        else if (environment == "PROD")
        {
            URL_BASE = "https://digitaldispatch.thenac.com/NACAPI/api/TowBook";
            AuthenticationToken = "8VrTfhXznwy76j7b3kJP3wgb";
        }

        Ip = ip;

        if (string.IsNullOrWhiteSpace(URL_BASE))
            throw new Exception("NAC FATAL: No URL for environment name=[" + environment +"]");

        var options = new RestClientOptions(URL_BASE)
        {
            UserAgent = "Towbook/4.0.0"
        };

        _restClient = new RestClient(_httpClient, options);
    }

    public readonly string URL_BASE = "";
    public string AuthenticationToken { get; private set; }
    public string EnvironmentName { get; set; }
    public string CertificateThumbprint { get; set; }
    public string Ip { get; set; }

    public static string GetEnvironmentName(int env)
    {
        if (env == 1)
            return "DEV";
        if (env == 2)
            return "TEST";
        else if (env == 3)
            return "PROD";

        return "DEV";
    }

    public static int GetEnvironmentId(string env)
    {
        if (env != null)
        {
            if (env.EndsWith("DEV"))
                return 1;
            if (env.EndsWith("TEST"))
                return 2;
            else if (env.EndsWith("PROD"))
                return 3;
        }

        return 1;
    }

    public LogEventInfo NewLogEvent(string message, string json = null)
    {
        var log = new LogEventInfo();

        log.Level = LogLevel.Info;
        log.Message = message;

        if (json != null)
            log.Properties["json"] = json;

        log.Properties["commitId"] = Core.GetCommitId();
        log.Properties["masterAccountId"] = MasterAccountTypes.Nac;
        log.Properties["masterAccountName"] = MasterAccountTypes.GetName(MasterAccountTypes.Nac);
        log.Properties["environment"] = EnvironmentName;
        log.Properties["requestingIp"] = Ip;

        return log;
    }

    public void Accept(DispatchAcceptModel payload)
    {
        if (payload.Type != "accept")
            throw new ArgumentException("Type must be: accept");

        var log = NewLogEvent("Accept", payload.ToJson());

        try
        {
            var resp = Send("/dispatchresponse", payload.ToJson(), Method.Post);

            log.Properties["contractorId"] = payload.ContractorId;
            log.Properties["dispatchId"] = payload.DispatchId;
            log.Properties["response"] = resp.Content;
            log.Properties["status"] = resp.StatusCode;

            if (!resp.IsSuccessful)
            {
                log.Level = LogLevel.Error;

                throw new Exception("Error invoking accepted:" + resp.StatusCode + "/" + resp.Content);
            }
        }
        finally
        {
            logger.Log(log);
        }
    }

    public void Refuse(DispatchRefuseModel payload)
    {
        if (payload.Type != "refuse")
            throw new ArgumentException("Type must be: refuse");

        var log = NewLogEvent("Refuse", payload.ToJson());

        try
        {
            var resp = Send("/dispatchresponse", payload.ToJson(),
                Method.Post);

            log.Properties["contractorId"] = payload.ContractorId;
            log.Properties["dispatchId"] = payload.DispatchId;
            log.Properties["response"] = resp.Content;
            log.Properties["status"] = resp.StatusCode;

            if (!resp.IsSuccessful)
            {
                log.Level = LogLevel.Error;

                throw new Exception("Error invoking Refused endpoint:" + resp.StatusCode + "/" + resp.Content);
            }
        }
        finally
        {
            logger.Log(log);
        }
    }

    public static string StatusIdToName(int n)
    {
        string code = null;
        if (n == 1)
            code = StatusCodes.Dispatched;
        if (n == 2)
            code = StatusCodes.Enroute;
        if (n == 3)
            code = StatusCodes.OnScene;
        if (n == 4)
            code = StatusCodes.Towing;
        if (n == 5)
            code = StatusCodes.Complete;
        if (n == 7)
            code = StatusCodes.Destination;

        return code;
    }
    public void StatusUpdate(DispatchStatusUpdateModel payload)
    {
        var log = NewLogEvent("StatusUpdate", payload.ToJson());

        try
        {
            if (payload.Type != "statusUpdate")
                throw new ArgumentException("Type must be: statusUpdate");


            if (payload.Status != StatusCodes.Dispatched &&
                payload.Status != StatusCodes.Enroute &&
                payload.Status != StatusCodes.OnScene &&
                payload.Status != StatusCodes.Towing &&
                payload.Status != StatusCodes.Destination &&
                payload.Status != StatusCodes.Complete)
                throw new ArgumentException("Invalid Status value passed: " + payload.Status + ".");

            var resp = Send(CallUpdateUrl, payload.ToJson(), Method.Post);

            log.Properties["contractorId"] = payload.ContractorId;
            log.Properties["dispatchId"] = payload.DispatchId;
            log.Properties["response"] = resp.Content;
            log.Properties["status"] = resp.StatusCode;

            if (!resp.IsSuccessful)
            {
                log.Level = LogLevel.Error;

                throw new Exception("Error invoking Status Update endpoint:" + resp.StatusCode + "/" + resp.Content);
            }
        }
        finally
        {
            logger.Log(log);
        }
    }

    public void Breadcrumb(DispatchBreadcrumbPayload payload)
    {
        if (payload.Type != "breadcrumb")
            throw new ArgumentException("Type must be: breadcrumb");

        var log = NewLogEvent("Breadcrumb", payload.ToJson());

        try
        {
            var resp = Send(CallUpdateUrl + "/breadcrumbs", payload.ToJson(), Method.Post);

            log.Properties["dispatchId"] = payload.DispatchId;
            log.Properties["response"] = resp.Content;
            log.Properties["status"] = resp.StatusCode;

            if (!resp.IsSuccessful)
            {
                log.Level = LogLevel.Error;

                throw new Exception("Error invoking Refused endpoint:" + resp.StatusCode + "/" + resp.Content);
            }
        }
        finally
        {
            logger.Log(log);
        }
    }

    public void Photo(DispatchPhotoModel payload)
    {
        if (payload.Type != "photo")
            throw new ArgumentException("Type must be: photo");

        var log = NewLogEvent("Photo", payload.ToJson());

        try
        {
            var resp = Send(CallUpdateUrl, payload.ToJson(), Method.Post);

            log.Properties["contractorId"] = payload.ContractorId;
            log.Properties["dispatchId"] = payload.DispatchId;
            log.Properties["response"] = resp.Content;
            log.Properties["status"] = resp.StatusCode;

            if (!resp.IsSuccessful)
            {
                log.Level = LogLevel.Error;

                throw new Exception("Error invoking Refused endpoint:" + resp.StatusCode + "/" + resp.Content);
            }
        }
        finally
        {
            logger.Log(log);
        }
    }

    private const string CallUpdateUrl = "/nac";

    public void Cancel(DispatchCancelModel payload)
    {
        if (payload.Type != "cancel")
            throw new ArgumentException("Type must be: cancel");

        var log = NewLogEvent("Cancel", payload.ToJson());

        try
        {
            var resp = Send(CallUpdateUrl, payload.ToJson(), Method.Post);

            log.Properties["contractorId"] = payload.ContractorId;
            log.Properties["dispatchId"] = payload.DispatchId;
            log.Properties["response"] = resp.Content;
            log.Properties["status"] = resp.StatusCode;

            if (!resp.IsSuccessful)
            {
                log.Level = LogLevel.Error;

                throw new Exception("Error invoking Cancel endpoint:" + resp.StatusCode + "/" + resp.Content);
            }
        }
        finally
        {
            logger.Log(log);
        }
    }

    public void Goa(DispatchGoaModel payload)
    {
        if (payload.Type != "goa")
            throw new ArgumentException("Type must be goa");

        var log = NewLogEvent("GOA", payload.ToJson());

        try
        {
            var resp = Send(CallUpdateUrl + "/request_goa", payload.ToJson(), Method.Post);

            log.Properties["contractorId"] = payload.ContractorId;
            log.Properties["dispatchId"] = payload.DispatchId;
            log.Properties["response"] = resp.Content;
            log.Properties["status"] = resp.StatusCode;

            if (!resp.IsSuccessful)
            {
                log.Level = LogLevel.Error;

                throw new Exception("Error invoking GOA endpoint:" + resp.StatusCode + "/" + resp.Content);
            }
        }
        finally
        {
            logger.Log(log);
        }
    }

    public void ExtendEta(DispatchExtendEtaModel payload)
    {
        if (payload.Type != "extend_eta")
            throw new ArgumentException("Type must be goa");

        var log = NewLogEvent("ExtendETA", payload.ToJson());

        try
        {
            var resp = Send(CallUpdateUrl + "/extend_eta", payload.ToJson(), Method.Post);

            log.Properties["contractorId"] = payload.ContractorId;
            log.Properties["dispatchId"] = payload.DispatchId;
            log.Properties["response"] = resp.Content;
            log.Properties["status"] = resp.StatusCode;

            if (!resp.IsSuccessful)
            {
                log.Level = LogLevel.Error;

                throw new Exception("Error invoking ExtendETA endpoint:" + resp.StatusCode + "/" + resp.Content);
            }
        }
        finally
        {
            logger.Log(log);
        }
    }

    public void UnsuccessfulService(DispatchUnsuccesfulServiceModel payload)
    {
        if (payload.Type != "unsuccessful_service")
            throw new ArgumentException("Type must be unsuccessful_service");

        var log = NewLogEvent("UnsuccessfulService", payload.ToJson());

        try
        {
            var resp = Send(CallUpdateUrl + "/unsuccessful_service", payload.ToJson(), Method.Post);

            log.Properties["contractorId"] = payload.ContractorId;
            log.Properties["dispatchId"] = payload.DispatchId;
            log.Properties["response"] = resp.Content;
            log.Properties["status"] = resp.StatusCode;

            if (!resp.IsSuccessful)
            {
                log.Level = LogLevel.Error;

                throw new Exception("Error invoking UnsuccessfulService endpoint:" + resp.StatusCode + "/" + resp.Content);
            }
        }
        finally
        {
            logger.Log(log);
        }
    }

    public void Login(string contractorId)
    {
        var log = NewLogEvent("Login", contractorId);

        try
        {
            var resp = Send(CallUpdateUrl + "/session/login",
                new
                {
                    contractorId
                }.ToJson(), Method.Post);

            log.Properties["contractorId"] = contractorId;
            log.Properties["response"] = resp.Content;
            log.Properties["status"] = resp.StatusCode;

            if (!resp.IsSuccessful)
            {
                log.Level = LogLevel.Error;

                throw new Exception("Error invoking Login endpoint:" + resp.StatusCode + "/" + resp.Content);
            }
        }
        finally
        {
            logger.Log(log);
        }
    }

    public void Logout(string contractorId)
    {
        var log = NewLogEvent("Logout", contractorId);

        try
        {
            var resp = Send(CallUpdateUrl + "/session/logout",
                new
                {
                    contractorId
                }.ToJson(), Method.Post);

            log.Properties["contractorId"] = contractorId;
            log.Properties["response"] = resp.Content;
            log.Properties["status"] = resp.StatusCode;

            if (!resp.IsSuccessful)
            {
                log.Level = LogLevel.Error;

                throw new Exception("Error invoking Logout endpoint:" + resp.StatusCode + "/" + resp.Content);
            }
        }
        finally
        {
            logger.Log(log);
        }
    }

    public RestResponse Send(string url, string payload, Method method)
    {
        var request = new RestRequest(url, method);

        request.AddParameter("TowbookKey", AuthenticationToken, ParameterType.HttpHeader);

        if (method != Method.Get)
            request.AddParameter("application/json", payload, ParameterType.RequestBody);

        Console.WriteLine(payload);

        var response = _restClient.Execute(request);

        Console.WriteLine($"{DateTime.Now.ToString()} {URL_BASE} {url}: {response.StatusCode}:{response.Content}");

        return response;
    }

    public class DispatchBreadcrumbPayload
    {
        public string Type { get; set; } = "breadcrumb";
        public string DispatchId { get; set; }
        public DateTime Timestamp { get; set; }
        public decimal Latitude { get; set; }
        public decimal Longitude { get; set; }
        public string ContractorId { get; set; }
        public string DriverId { get; set; }
    }

    public class StatusCodes
    {
        public const string Dispatched = "DISPATCHED";
        public const string Enroute = "EN_ROUTE";
        public const string OnScene = "ON_SCENE";
        public const string Towing = "TOWING";
        public const string Destination = "DESTINATION";
        public const string Complete = "COMPLETE";
    }
    public class DispatchAcceptModel
    {
        public string Type { get; set; } = "accept";
        public string DispatchId { get; set; }
        public string ContractorId { get; set; }
        public string LocationId { get; set; }
        public string ResourceName { get; set; } = "";

        public int Eta { get; set; }
        public int? ReasonId { get; set; }
        public AcceptProviderPayload Provider { get; set; }
    }

    public class AcceptProviderPayload
    {
        public string ContractorId { get; set; }
        public string Name { get; set; }
        public string Address { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string Zip { get; set; }
        public decimal Latitude { get; set; }
        public decimal Longitude { get; set; }
        public string Phone { get; set; }
        public string Email { get; set; }
    }

    public class DispatchRefuseModel
    {
        public string Type { get; set; } = "refuse";
        public string DispatchId { get; set; }
        public string ContractorId { get; set; }
        public string LocationId { get; set; }
        public int ReasonId { get; set; }
        public string ReasonName { get; set; }
        public string ResourceName { get; set; } = "";
        public string Status { get; set; } = "Refuse";
    }

    public class DispatchCancelModel
    {
        public string Type { get; set; } = "cancel";
        public string DispatchId { get; set; }
        public string ContractorId { get; set; }
        public string LocationId { get; set; }

        public int ReasonId { get; set; }
        public string ReasonName { get; set; }
        public string Comments { get; set; }
        public string ResourceName { get; set; }
    }

    public class DispatchStatusUpdateModel
    {
        public string Type { get; set; } = "statusUpdate";
        public string DispatchId { get; set; }
        public string ContractorId { get; set; }
        public string LocationId { get; set; }
        public string Status { get; set; }
        public DriverModel Driver { get; set; }
    }

    public class DispatchPhotoModel
    {
        public string Type { get; set; } = "photo";
        public string DispatchId { get; set; }
        public string ContractorId { get; set; }
        public string LocationId { get; set; }
        public decimal Latitude { get; set; }
        public decimal Longitude { get; set; }
        public DateTime Timestamp { get; set; }
        public string Url { get; set; }
        public string CallStatus { get; set; }
        public DriverModel Driver { get; set; }
    }

    public class DispatchGoaModel
    {
        public string Type { get; set; } = "goa";
        public string DispatchId { get; set; }
        public string ContractorId { get; set; }
        public string LocationId { get; set; }
        public string ReasonId { get; set; }
        public string ReasonName { get; set; }
        public string Comments { get; set; }
        public DateTime Timestamp { get; set; }
        public string ResourceName { get; set; }
    }

    public class DispatchExtendEtaModel
    {
        public string Type { get; set; } = "extend_eta";
        public string DispatchId { get; set; }
        public string ContractorId { get; set; }
        public string LocationId { get; set; }
        public string Reason { get; set; }
        public string Comments { get; set; }
        public string ResourceName { get; set; }

        public DateTime NewEta { get; set; }
    }

    public class DispatchUnsuccesfulServiceModel
    {
        public string Type { get; set; } = "unsuccessful_service";
        public string DispatchId { get; set; }
        public string ContractorId { get; set; }
        public string LocationId { get; set; }
        public string Reason { get; set; }
        public string Comments { get; set; }
        public string ResourceName { get; set; }
    }

    public class DriverModel
    {
        public string Id { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public decimal Latitude { get; set; }
        public decimal Longitude { get; set; }
    }

    public class DispatchCompleteModel
    {
        public string Type { get; set; } = "complete";
        public string DispatchId { get; set; }
        public string ContractorId { get; set; }
        public string LocationId { get; set; }
        public DriverModel Driver { get; set; }

        public string ResolutionCode { get; set; }

        public InvoiceItemModel[] InvoiceItems { get; set; }

        public class InvoiceItemModel
        {
            public string Code { get; set; }
            public decimal Quantity { get; set; }
            public decimal Price { get; set; }
        }
    }
}
