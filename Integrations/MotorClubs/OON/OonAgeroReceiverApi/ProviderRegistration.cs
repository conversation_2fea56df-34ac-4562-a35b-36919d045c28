using Extric.Towbook.Integration;
using Microsoft.AspNetCore.Routing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.API.Integration.MotorClubs.OON.Agero
{
    public class ProviderRegistration
    {
        public ProviderRegistration()
        {

        }

        public static void RegisterRoutes(RouteCollection routes)
        {
        //    string root = "receivers/oon/agero/";
        //    List<Route> rl = new List<Route>();

        //    rl.Add(routes.MapHttpRoute(
        //        name: "MC_OON_Agero_AssignPODispatch",
        //        routeTemplate: root + "dispatch/{id}/assignPO",
        //        defaults: new { controller = "AssignPO" },
        //        constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "PUT", "POST" }) }));

        //    rl.Add(routes.MapHttpRoute(
        //        name: "MC_OON_Agero_CancelDispatch",
        //        routeTemplate: root + "dispatch/{id}/cancel",
        //        defaults: new { controller = "Cancelled" },
        //        constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "PUT", "POST" }) }));


        //    rl.Add(routes.MapHttpRoute(
        //        name: "MC_OON_Agero_AcceptDispatch",
        //        routeTemplate: root + "dispatch/{id}/{controller}",
        //        defaults: new { controller = "Accepted" },
        //        constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "PUT", "POST" }) }));

        //    rl.Add(routes.MapHttpRoute(
        //        name: "MC_OON_Agero_CancelledDispatch",
        //        routeTemplate: root + "dispatch/{id}/{controller}",
        //        defaults: new { controller = "Cancelled" },
        //        constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "PUT", "POST" }) }));

        //    rl.Add(routes.MapHttpRoute(
        //        name: "MC_OON_Agero_RefuseDispatch",
        //        routeTemplate: root + "dispatch/{id}/{controller}",
        //        defaults: new { controller = "Refused" },
        //        constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "PUT", "POST" }) }));

        //    rl.Add(routes.MapHttpRoute(
        //        name: "MC_OON_Agero_Get",
        //        routeTemplate: root + "{controller}/{id}/{action}",
        //        defaults: new { id = RouteParameter.Optional, action = "Get" },
        //        constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "GET" }) }));

        //    rl.Add(routes.MapHttpRoute(
        //        name: "MC_OON_Agero_Put",
        //        routeTemplate: root + "{controller}/{id}/{action}",
        //        defaults: new { id = RouteParameter.Optional, action = "Put" },
        //        constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "PUT" }) }));

        //    rl.Add(routes.MapHttpRoute(
        //        name: "MC_OON_Agero_Post",
        //        routeTemplate: root + "{controller}/{id}",
        //        defaults: new { id = RouteParameter.Optional, action = "Post" },
        //        constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "POST" }) }));

        //    rl.Add(routes.MapHttpRoute(
        //        name: "MC_OON_Agero_Delete",
        //        routeTemplate: root + "{controller}/{id}",
        //        defaults: new { id = RouteParameter.Optional, action = "Delete" },
        //        constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "DELETE" }) }));

        //    rl.Add(routes.MapHttpRoute(
        //        name: "MC_OON_Agero",
        //        routeTemplate: root + "{controller}/{id}",
        //        defaults: new { action = "Get", id = RouteParameter.Optional }));

        //    rl.Add(routes.MapHttpRoute(
        //        name: "MC_OON_Agero_DDAPI",
        //        routeTemplate: root,
        //        defaults: new { controller = "Agero" }));

        //    foreach (var r in rl)
        //    {
        //        if (r.DataTokens == null)
        //            r.DataTokens = new RouteValueDictionary();

        //        r.DataTokens["Namespaces"] = new string[] { "Extric.Towbook.API.Integration.MotorClubs.OON.Agero" };
        //    }

            var p = Extric.Towbook.Integration.Provider.GetByName("OON_Agero");

            p.RegisterKey(KeyType.Account, "ContractorId");

        }


        //private static void AddRoute(List<Route> rl, RouteCollection routes, string url, string controller)
        //{
        //    rl.Add(routes.MapHttpRoute(
        //        name: "MC_OON_Agero_" + url,
        //        routeTemplate: "receivers/oon/agero/" + url,
        //        defaults: new { controller = controller }));
        //}

    }
}
