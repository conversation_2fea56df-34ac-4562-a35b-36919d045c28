using Extric.Towbook.Integration.MotorClubs.Services;
using Extric.Towbook.Integrations.MotorClubs.Allstate;
using Extric.Towbook.Integrations.MotorClubs.Allstate.Proxy;
using Extric.Towbook.Utility;
using Extric.Towbook.WebShared;
using System;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Reflection;
using System.ServiceModel.Channels;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using System.Xml;
using NLog;
using Extric.Towbook.Accounts;

namespace Extric.Towbook.API.Integration.MotorClubs.Pinnacle
{
    public class PinnacleController : ApiController
    {
        private const string masterAccountName = "Pinnacle";

        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        private string GetWsdl()
        {
            var assembly = Assembly.GetExecutingAssembly();
            string script = "";

            const string resourceName = "Extric.Towbook.API.Integration.MotorClubs.Pinnacle.Wsdl.xml";
            using (Stream stream = assembly.GetManifestResourceStream(resourceName))
            using (StreamReader reader = new StreamReader(stream))
            {
                var url = HttpContext.Current.Request.EnvironmentSpecificRequestUrl().AbsoluteUri.Replace("http://", "https://");

                script = reader
                    .ReadToEnd()
                    .Replace("http://localhost/api/receivers/pinnacle",
                        url.Substring(0, url.IndexOf('?')).TrimEnd('/'));
            }

            return script;
        }

        public object Get()
        {
            if (Request.RequestUri.OriginalString.ToLowerInvariant().EndsWith("wsdl"))
            {
                return new HttpResponseMessage
                {
                    Content = new StringContent(GetWsdl(), Encoding.UTF8, "application/xml")
                };
            }
            else
            {
                return new HttpResponseMessage(HttpStatusCode.OK);
            }
        }

        private DDMessage CreateDDMessageAsync(XmlReader reader, bool soap11, out string methodName)
        {
            Message m = Message.CreateMessage(reader, int.MaxValue, (soap11 ? MessageVersion.Soap11 : MessageVersion.Soap12));

            string xmlMsg = "";
            methodName = reader.Name;

            if (reader.Name == "DDXMLReceiveMessageEx")
            {
                DDXMLReceiveMessageExRequestBody body = m.GetBody<DDXMLReceiveMessageExRequestBody>();
                xmlMsg = body.xmlMessage;
                if (body.apiKey != "kpa7PXtUd32gbR3dN5R6gsCy" &&
                    body.apiKey != "N7jwrV3krnx6Sxmjgnqten68")
                    throw new HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                    {
                        Content = new StringContent("Invalid API Key")
                    });
            }

            return DDMessage.FromXml(xmlMsg);
        }

        public async Task<object> Post()
        {
            bool soap11 = (this.Request.Headers.Where(o => o.Key == "SOAPAction").Any());
            DDMessage msg = null;

            var lei = new LogEventInfo();
            lei.Level = LogLevel.Info;
            lei.Properties.Add("masterAccountName", masterAccountName);

            try
            {
                string s = await Request.Content.ReadAsStringAsync();

                lei.Properties.Add("request", s);

                string methodName = "";
                using (var reader = XmlReader.Create(new StringReader(s)))
                {
                    msg = CreateDDMessageAsync(reader, soap11, out methodName);
                }

                lei.Properties.Remove("request");
                lei.Properties.Add("ddxml", msg);

                var dbMsg = new AllstateMessage(MasterAccountTypes.Pinnacle, DDXmlMessageDirection.Incoming);
                var xml = msg.GetXml();
                dbMsg.Message = xml;

                var ackMsg = new DDMessage();
                ackMsg.DDContent = new ACKMessage();
                ((ACKMessage)ackMsg.DDContent).TriggerType = msg.DDMessageHeader.TransType;

                ackMsg.DDMessageHeader = new DDMessageHeader(msg.DDMessageHeader.ContractorID,
                    transType: "ACK",
                    key: "TOWBOOK",
                    responseType: "",  // response type not required for ack
                    conRequired: "N");
                ackMsg.DDMessageHeader.ResponseID = msg.DDMessageHeader.ResponseID ?? "777" + DateTime.Now.Ticks.ToString();

                string cId = "NJ1209032";
                if (!string.IsNullOrEmpty(msg.DDMessageHeader.ContractorID))
                    cId = msg.DDMessageHeader.ContractorID.Split('_')[0];

                lei.Properties.Add("headerKey", msg.DDMessageHeader.Key);
                lei.Properties.Add("contractorId", cId);
                lei.Properties.Add("transactionType", msg.DDMessageHeader.TransType);
                lei.Properties.Add("responseId", msg.DDMessageHeader.ResponseID);

                AllstateContractor ct = AllstateContractor.GetByContractorId(cId, MasterAccountTypes.Pinnacle);
                if (ct != null)
                {
                    switch (msg.DDMessageHeader.TransType)
                    {
                        case "DSP":
                            var dspContent = (DSPMessageBody)msg.DDContent;

                            dbMsg.DispatchRequestNumber = msg.DDMessageHeader.ResponseID;
                            dbMsg.ContractorId = cId;
                            dbMsg.Title = "[DSP]";
                            dbMsg.Type = AllstateMessageType.DSP;
                            dbMsg.Save();

                            DigitalDispatchService.HandleCallEvent(ct.CompanyId, ct.AccountId, dbMsg.ToJson(null), DigitalDispatchService.CallEventType.Received);
                            break;

                        case "RSP":
                            var rspContent = (RSPMessage)msg.DDContent;

                            dbMsg.DispatchRequestNumber = msg.DDMessageHeader.ResponseID;
                            dbMsg.PurchaseOrderNumber = rspContent.AuthorizationNumber;
                            dbMsg.ContractorId = cId;
                            dbMsg.Title = "[RSP]";
                            dbMsg.Type = AllstateMessageType.RSP;
                            dbMsg.Save();

                            DigitalDispatchService.HandleCallEvent(ct.CompanyId, ct.AccountId, dbMsg.ToJson(null), DigitalDispatchService.CallEventType.Accepted);
                            break;

                        case "DSI":
                            var dsiContent = (DSIMessage)msg.DDContent;

                            dbMsg.DispatchRequestNumber = msg.DDMessageHeader.ResponseID;
                            dbMsg.ContractorId = cId;
                            dbMsg.Title = "[DSI]";
                            dbMsg.Type = AllstateMessageType.DSI;
                            dbMsg.Save();

                            DigitalDispatchService.HandleCallEvent(ct.CompanyId,
                                ct.AccountId,
                                dbMsg.ToJson(null),
                                DigitalDispatchService.CallEventType.StatusInquiry);
                            break;

                        case "CNL":
                            var rspCNL = (CNLMessage)msg.DDContent;

                            dbMsg.DispatchRequestNumber = msg.DDMessageHeader.ResponseID;
                            dbMsg.ContractorId = cId;
                            dbMsg.Title = "[CNL]";
                            dbMsg.Type = AllstateMessageType.CNL;
                            dbMsg.Save();

                            DigitalDispatchService.HandleCallEvent(ct.CompanyId, ct.AccountId, dbMsg.ToJson(null), DigitalDispatchService.CallEventType.Cancelled);
                            break;
                    }
                }
                else
                {
                    lei.Message = "ContractorID couldn't be found: " + cId;
                    lei.Level = LogLevel.Error;
                }

                var returnXML = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                "<soap:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\"><soap:Body><" + methodName + "Response xmlns=\"https://api.towbook.com/receivers/pinnacle/\"><" + methodName + "Result>" +
     System.Net.WebUtility.HtmlEncode(ackMsg.GetXml()) +
    "</" + methodName + "Result></" + methodName + "Response></soap:Body></soap:Envelope>";

                var returnXML12 = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                "<soap:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soap=\"http://www.w3.org/2003/05/soap-envelope\"><soap:Body><" + methodName + "Response xmlns=\"https://api.towbook.com/receivers/pinnacle/\"><" + methodName + "Result>" +
     System.Net.WebUtility.HtmlEncode(ackMsg.GetXml()) +
    "</" + methodName + "Result></" + methodName + "Response></soap:Body></soap:Envelope>";

                var rm = new HttpResponseMessage()
                {
                    Content = new StringContent((soap11 ? returnXML : returnXML12), Encoding.UTF8, (soap11 ? "text/xml" : "application/soap+xml"))
                };

                return rm;
            }
            catch (Exception e)
            {
                lei.Message = "Inbound Request - Exception";
                lei.Exception = e;
                lei.Level = LogLevel.Error;

                throw;
            }
            finally
            {
                lei.Properties.Add("headers", Request.Headers);
                logger.Log(lei);
            }
        }
    }
}
