<?xml version="1.0" encoding="utf-8"?>
<definitions xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:tns="http://tempuri.org/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" name="IqixWebRequestDataservice" targetNamespace="http://tempuri.org/" xmlns="http://schemas.xmlsoap.org/wsdl/">
  <types />
  <message name="qixReceiveXml0Request">
    <part name="fromClientNumber" type="xs:int" />
    <part name="toClientNumber" type="xs:int" />
    <part name="password" type="xs:string" />
    <part name="Xml" type="xs:string" />
    <part name="transactionNumber" type="xs:int" />
  </message>
  <message name="qixReceiveXml0Response">
    <part name="Xml" type="xs:string" />
    <part name="transactionNumber" type="xs:int" />
    <part name="return" type="xs:int" />
  </message>
  <message name="qixReceiveXmlByDataType1Request">
    <part name="fromClientNumber" type="xs:int" />
    <part name="toClientNumber" type="xs:int" />
    <part name="password" type="xs:string" />
    <part name="dataType" type="xs:string" />
    <part name="Xml" type="xs:string" />
    <part name="transactionNumber" type="xs:int" />
  </message>
  <message name="qixReceiveXmlByDataType1Response">
    <part name="Xml" type="xs:string" />
    <part name="transactionNumber" type="xs:int" />
    <part name="return" type="xs:int" />
  </message>
  <message name="qixSendXml2Request">
    <part name="fromClientNumber" type="xs:int" />
    <part name="toClientNumber" type="xs:int" />
    <part name="password" type="xs:string" />
    <part name="Xml" type="xs:string" />
    <part name="transactionNumber" type="xs:int" />
  </message>
  <message name="qixSendXml2Response">
    <part name="Xml" type="xs:string" />
    <part name="transactionNumber" type="xs:int" />
    <part name="return" type="xs:int" />
  </message>
  <message name="qixGetQtsServiceRequestByClaimNumber3Request">
    <part name="fromClientNumber" type="xs:int" />
    <part name="toClientNumber" type="xs:int" />
    <part name="password" type="xs:string" />
    <part name="claimNumber" type="xs:string" />
    <part name="Xml" type="xs:string" />
    <part name="transactionNumber" type="xs:int" />
  </message>
  <message name="qixGetQtsServiceRequestByClaimNumber3Response">
    <part name="Xml" type="xs:string" />
    <part name="transactionNumber" type="xs:int" />
    <part name="return" type="xs:int" />
  </message>
  <message name="qixGetQtsServiceRequestByIndex4Request">
    <part name="fromClientNumber" type="xs:int" />
    <part name="toClientNumber" type="xs:int" />
    <part name="password" type="xs:string" />
    <part name="field" type="xs:string" />
    <part name="value" type="xs:string" />
    <part name="updateYN" type="xs:string" />
    <part name="MultipleYN" type="xs:string" />
    <part name="Xml" type="xs:string" />
    <part name="transactionNumber" type="xs:int" />
  </message>
  <message name="qixGetQtsServiceRequestByIndex4Response">
    <part name="Xml" type="xs:string" />
    <part name="transactionNumber" type="xs:int" />
    <part name="return" type="xs:int" />
  </message>
  <message name="qixGetOrderBySessionID5Request">
    <part name="fromClientNumber" type="xs:int" />
    <part name="toClientNumber" type="xs:int" />
    <part name="password" type="xs:string" />
    <part name="SessionID" type="xs:string" />
    <part name="Xml" type="xs:string" />
    <part name="transactionNumber" type="xs:int" />
  </message>
  <message name="qixGetOrderBySessionID5Response">
    <part name="Xml" type="xs:string" />
    <part name="transactionNumber" type="xs:int" />
    <part name="return" type="xs:int" />
  </message>
  <message name="qixGetTransactionBySessionID6Request">
    <part name="fromClientNumber" type="xs:int" />
    <part name="toClientNumber" type="xs:int" />
    <part name="password" type="xs:string" />
    <part name="sessionID" type="xs:string" />
    <part name="transactionType" type="xs:string" />
    <part name="Xml" type="xs:string" />
    <part name="transactionNumber" type="xs:int" />
  </message>
  <message name="qixGetTransactionBySessionID6Response">
    <part name="Xml" type="xs:string" />
    <part name="transactionNumber" type="xs:int" />
    <part name="return" type="xs:int" />
  </message>
  <message name="qixGetAllDataByIndex7Request">
    <part name="fromClientNumber" type="xs:int" />
    <part name="toClientNumber" type="xs:int" />
    <part name="password" type="xs:string" />
    <part name="dataType" type="xs:string" />
    <part name="field" type="xs:string" />
    <part name="value" type="xs:string" />
    <part name="maxDaysOld" type="xs:int" />
    <part name="Xml" type="xs:string" />
    <part name="transactionNumber" type="xs:int" />
  </message>
  <message name="qixGetAllDataByIndex7Response">
    <part name="Xml" type="xs:string" />
    <part name="transactionNumber" type="xs:int" />
    <part name="return" type="xs:int" />
  </message>
  <message name="qixReady8Request" />
  <message name="qixReady8Response">
    <part name="return" type="xs:boolean" />
  </message>
  <message name="qixErrorDesc9Request">
    <part name="errorCode" type="xs:int" />
  </message>
  <message name="qixErrorDesc9Response">
    <part name="return" type="xs:string" />
  </message>
  <message name="qixClientByQcs10Request">
    <part name="qcsNumber" type="xs:string" />
  </message>
  <message name="qixClientByQcs10Response">
    <part name="return" type="xs:int" />
  </message>
  <message name="qixQcsByClient11Request">
    <part name="clientNumber" type="xs:int" />
  </message>
  <message name="qixQcsByClient11Response">
    <part name="return" type="xs:string" />
  </message>
  <message name="qixPidByClient12Request">
    <part name="clientNumber" type="xs:int" />
  </message>
  <message name="qixPidByClient12Response">
    <part name="return" type="xs:string" />
  </message>
  <message name="SoapDDXML13Request">
    <part name="psMsg" type="xs:string" />
  </message>
  <message name="SoapDDXML13Response">
    <part name="return" type="xs:string" />
  </message>
  <portType name="IqixWebRequestData">
    <operation name="qixReceiveXml">
      <input message="tns:qixReceiveXml0Request" />
      <output message="tns:qixReceiveXml0Response" />
    </operation>
    <operation name="qixReceiveXmlByDataType">
      <input message="tns:qixReceiveXmlByDataType1Request" />
      <output message="tns:qixReceiveXmlByDataType1Response" />
    </operation>
    <operation name="qixSendXml">
      <input message="tns:qixSendXml2Request" />
      <output message="tns:qixSendXml2Response" />
    </operation>
    <operation name="qixGetQtsServiceRequestByClaimNumber">
      <input message="tns:qixGetQtsServiceRequestByClaimNumber3Request" />
      <output message="tns:qixGetQtsServiceRequestByClaimNumber3Response" />
    </operation>
    <operation name="qixGetQtsServiceRequestByIndex">
      <input message="tns:qixGetQtsServiceRequestByIndex4Request" />
      <output message="tns:qixGetQtsServiceRequestByIndex4Response" />
    </operation>
    <operation name="qixGetOrderBySessionID">
      <input message="tns:qixGetOrderBySessionID5Request" />
      <output message="tns:qixGetOrderBySessionID5Response" />
    </operation>
    <operation name="qixGetTransactionBySessionID">
      <input message="tns:qixGetTransactionBySessionID6Request" />
      <output message="tns:qixGetTransactionBySessionID6Response" />
    </operation>
    <operation name="qixGetAllDataByIndex">
      <input message="tns:qixGetAllDataByIndex7Request" />
      <output message="tns:qixGetAllDataByIndex7Response" />
    </operation>
    <operation name="qixReady">
      <input message="tns:qixReady8Request" />
      <output message="tns:qixReady8Response" />
    </operation>
    <operation name="qixErrorDesc">
      <input message="tns:qixErrorDesc9Request" />
      <output message="tns:qixErrorDesc9Response" />
    </operation>
    <operation name="qixClientByQcs">
      <input message="tns:qixClientByQcs10Request" />
      <output message="tns:qixClientByQcs10Response" />
    </operation>
    <operation name="qixQcsByClient">
      <input message="tns:qixQcsByClient11Request" />
      <output message="tns:qixQcsByClient11Response" />
    </operation>
    <operation name="qixPidByClient">
      <input message="tns:qixPidByClient12Request" />
      <output message="tns:qixPidByClient12Response" />
    </operation>
    <operation name="SoapDDXML">
      <input message="tns:SoapDDXML13Request" />
      <output message="tns:SoapDDXML13Response" />
    </operation>
  </portType>
  <binding name="IqixWebRequestDatabinding" type="tns:IqixWebRequestData">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" style="rpc" />
    <operation name="qixReceiveXml">
      <soap:operation soapAction="urn:qixWebRequestDataIntf-IqixWebRequestData#qixReceiveXml" style="rpc" />
      <input>
        <soap:body use="encoded" namespace="urn:qixWebRequestDataIntf-IqixWebRequestData" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </input>
      <output>
        <soap:body use="encoded" namespace="urn:qixWebRequestDataIntf-IqixWebRequestData" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </output>
    </operation>
    <operation name="qixReceiveXmlByDataType">
      <soap:operation soapAction="urn:qixWebRequestDataIntf-IqixWebRequestData#qixReceiveXmlByDataType" style="rpc" />
      <input>
        <soap:body use="encoded" namespace="urn:qixWebRequestDataIntf-IqixWebRequestData" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </input>
      <output>
        <soap:body use="encoded" namespace="urn:qixWebRequestDataIntf-IqixWebRequestData" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </output>
    </operation>
    <operation name="qixSendXml">
      <soap:operation soapAction="urn:qixWebRequestDataIntf-IqixWebRequestData#qixSendXml" style="rpc" />
      <input>
        <soap:body use="encoded" namespace="urn:qixWebRequestDataIntf-IqixWebRequestData" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </input>
      <output>
        <soap:body use="encoded" namespace="urn:qixWebRequestDataIntf-IqixWebRequestData" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </output>
    </operation>
    <operation name="qixGetQtsServiceRequestByClaimNumber">
      <soap:operation soapAction="urn:qixWebRequestDataIntf-IqixWebRequestData#qixGetQtsServiceRequestByClaimNumber" style="rpc" />
      <input>
        <soap:body use="encoded" namespace="urn:qixWebRequestDataIntf-IqixWebRequestData" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </input>
      <output>
        <soap:body use="encoded" namespace="urn:qixWebRequestDataIntf-IqixWebRequestData" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </output>
    </operation>
    <operation name="qixGetQtsServiceRequestByIndex">
      <soap:operation soapAction="urn:qixWebRequestDataIntf-IqixWebRequestData#qixGetQtsServiceRequestByIndex" style="rpc" />
      <input>
        <soap:body use="encoded" namespace="urn:qixWebRequestDataIntf-IqixWebRequestData" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </input>
      <output>
        <soap:body use="encoded" namespace="urn:qixWebRequestDataIntf-IqixWebRequestData" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </output>
    </operation>
    <operation name="qixGetOrderBySessionID">
      <soap:operation soapAction="urn:qixWebRequestDataIntf-IqixWebRequestData#qixGetOrderBySessionID" style="rpc" />
      <input>
        <soap:body use="encoded" namespace="urn:qixWebRequestDataIntf-IqixWebRequestData" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </input>
      <output>
        <soap:body use="encoded" namespace="urn:qixWebRequestDataIntf-IqixWebRequestData" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </output>
    </operation>
    <operation name="qixGetTransactionBySessionID">
      <soap:operation soapAction="urn:qixWebRequestDataIntf-IqixWebRequestData#qixGetTransactionBySessionID" style="rpc" />
      <input>
        <soap:body use="encoded" namespace="urn:qixWebRequestDataIntf-IqixWebRequestData" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </input>
      <output>
        <soap:body use="encoded" namespace="urn:qixWebRequestDataIntf-IqixWebRequestData" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </output>
    </operation>
    <operation name="qixGetAllDataByIndex">
      <soap:operation soapAction="urn:qixWebRequestDataIntf-IqixWebRequestData#qixGetAllDataByIndex" style="rpc" />
      <input>
        <soap:body use="encoded" namespace="urn:qixWebRequestDataIntf-IqixWebRequestData" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </input>
      <output>
        <soap:body use="encoded" namespace="urn:qixWebRequestDataIntf-IqixWebRequestData" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </output>
    </operation>
    <operation name="qixReady">
      <soap:operation soapAction="urn:qixWebRequestDataIntf-IqixWebRequestData#qixReady" style="rpc" />
      <input>
        <soap:body use="encoded" namespace="urn:qixWebRequestDataIntf-IqixWebRequestData" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </input>
      <output>
        <soap:body use="encoded" namespace="urn:qixWebRequestDataIntf-IqixWebRequestData" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </output>
    </operation>
    <operation name="qixErrorDesc">
      <soap:operation soapAction="urn:qixWebRequestDataIntf-IqixWebRequestData#qixErrorDesc" style="rpc" />
      <input>
        <soap:body use="encoded" namespace="urn:qixWebRequestDataIntf-IqixWebRequestData" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </input>
      <output>
        <soap:body use="encoded" namespace="urn:qixWebRequestDataIntf-IqixWebRequestData" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </output>
    </operation>
    <operation name="qixClientByQcs">
      <soap:operation soapAction="urn:qixWebRequestDataIntf-IqixWebRequestData#qixClientByQcs" style="rpc" />
      <input>
        <soap:body use="encoded" namespace="urn:qixWebRequestDataIntf-IqixWebRequestData" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </input>
      <output>
        <soap:body use="encoded" namespace="urn:qixWebRequestDataIntf-IqixWebRequestData" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </output>
    </operation>
    <operation name="qixQcsByClient">
      <soap:operation soapAction="urn:qixWebRequestDataIntf-IqixWebRequestData#qixQcsByClient" style="rpc" />
      <input>
        <soap:body use="encoded" namespace="urn:qixWebRequestDataIntf-IqixWebRequestData" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </input>
      <output>
        <soap:body use="encoded" namespace="urn:qixWebRequestDataIntf-IqixWebRequestData" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </output>
    </operation>
    <operation name="qixPidByClient">
      <soap:operation soapAction="urn:qixWebRequestDataIntf-IqixWebRequestData#qixPidByClient" style="rpc" />
      <input>
        <soap:body use="encoded" namespace="urn:qixWebRequestDataIntf-IqixWebRequestData" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </input>
      <output>
        <soap:body use="encoded" namespace="urn:qixWebRequestDataIntf-IqixWebRequestData" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </output>
    </operation>
    <operation name="SoapDDXML">
      <soap:operation soapAction="urn:qixWebRequestDataIntf-IqixWebRequestData#SoapDDXML" style="rpc" />
      <input>
        <soap:body use="encoded" namespace="urn:qixWebRequestDataIntf-IqixWebRequestData" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </input>
      <output>
        <soap:body use="encoded" namespace="urn:qixWebRequestDataIntf-IqixWebRequestData" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </output>
    </operation>
  </binding>
  <service name="IqixWebRequestDataservice">
    <port name="IqixWebRequestDataPort" binding="tns:IqixWebRequestDatabinding">
      <soap:address location="https://qix.questsoftware.com:80/qix/qixWebServiceCgiQtsDDv7.exe/soap/IqixWebRequestData" />
    </port>
  </service>
</definitions>