using System;
using System.Net.Http;
using System.Security.Cryptography;
using System.Text;
using Extric.Towbook.Accounts;
using Extric.Towbook.Utility;
using Newtonsoft.Json;
using NLog;
using RestSharp;
using RestSharp.Authenticators.OAuth2;

namespace Extric.Towbook.Integrations.MotorClubs.RoadsideProtect
{

    public class IncomingModelBase
    {
        public string DispatchId { get; set; }
        public string ContractorId { get; set; }
        public string LocationId { get; set; }
    }

    public sealed class IncomingAcceptedModel : IncomingModelBase
    {
        public string PoNumber { get; set; }

        public AcceptedPayment Payment { get; set; }

        public decimal CustomerPay { get; set; }
        public sealed class AcceptedPayment
        {
            public decimal Amount { get; set; }
            public string Zip { get; set; }
            public string Cvc { get; set; }
            public string ExpirationDate { get; set; }
            public string Number { get; set; }
        }
    }


    public sealed class IncomingExpiredModel : IncomingModelBase
    {
        public string Notes { get; set; }
    }

    public sealed class IncomingRefusedModel : IncomingModelBase
    {
        public string Notes { get; set; }
    }

    public sealed class IncomingCancelledModel : IncomingModelBase
    {
        public string Notes { get; set; }
    }

    public sealed class IncomingGoaModel : IncomingModelBase
    {
        public bool Approved { get; set; }
        public string Notes { get; set; }
    }


    public class RoadsideProtectRestClient
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        private static readonly HttpClient _httpClient = new(new SocketsHttpHandler
        {
            PooledConnectionLifetime = TimeSpan.FromMinutes(5),
            PooledConnectionIdleTimeout = TimeSpan.FromSeconds(30)
        });

        public string ClientId { get; private set; }
        private string ClientSecret { get; set; }
        private string Username { get; set; }
        private string Password { get; set; }

        public string EnvironmentName { get; set; }
        public string Ip { get; set; }
        public int CompanyId { get; private set; }
        public long QueueItemId { get; private set; }

        public int CallId { get; private set; }
        public int CallRequestId { get; private set; }
        public string DispatchId { get; private set; }

        public RoadsideProtectRestClient(
            int masterAccountId,
            string environment,
            string ip,
            int companyId,
            long queueItemId,
            int callRequestId,
            int callId,
            string dispatchId)
        {
            EnvironmentName = environment;
            CompanyId = companyId;
            QueueItemId = queueItemId;
            CallRequestId = callRequestId;
            CallId = callId;
            DispatchId = dispatchId;

            if (environment == "TEST" || environment == "DEV")
            {
                if (masterAccountId == MasterAccountTypes.OonRoadsideProtect)
                {
                    token = new RoadsideProtectAccessTokenPayload()
                    {
                        AccessToken = "E7AF8AC73A2BC32B563B182A2CEBF",
                        InstanceUrl = "https://towbookd.roadsideprotect.com/api/webhook"
                    };
                }
                else
                {
                    token = new RoadsideProtectAccessTokenPayload()
                    {
                        AccessToken = "D5CCA54DB1DF41D9F9C6819F3E1BD",
                        InstanceUrl = "https://towbookd.roadsideprotect.com/api/webhook"
                    };
                }
            }
            else if (environment == "PROD")
            {
                token = new RoadsideProtectAccessTokenPayload()
                {
                    AccessToken = "C9A7ED2D37882AEA412E5A9444D5B",
                    InstanceUrl = "https://towbook.roadsideprotect.com/api/webhook"
                };
            }
            else if (environment == "OON_PROD")
            {
                token = new RoadsideProtectAccessTokenPayload()
                {
                    AccessToken = "7E26D3B23CB18977B9BCABB526333",
                    InstanceUrl = "https://towbook.roadsideprotect.com/api/webhook"
                };
            }

            Ip = ip;

            var options = new RestClientOptions(token.InstanceUrl)
            {
                Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator(token.AccessToken, "Bearer")
            };

            _restClient = new RestClient(_httpClient, options);
        }

        private readonly RoadsideProtectAccessTokenPayload token;
        private readonly RestClient _restClient;

        private RestClient GetRestClient() => _restClient;

        public LogEventInfo NewLogEvent(string message, string json = null)
        {
            var log = new LogEventInfo();

            log.Level = LogLevel.Info;
            log.Message = message;

            if (json != null)
                log.Properties["json"] = json;

            log.Properties["commitId"] = Core.GetCommitId();
            log.Properties["masterAccountName"] = MasterAccountTypes.GetName(MasterAccountTypes.RoadsideProtect);
            log.Properties["masterAccountId"] = MasterAccountTypes.RoadsideProtect;
            log.Properties["environment"] = EnvironmentName;
            log.Properties["requestingIp"] = Ip;


            if (CompanyId > 0)
                log.Properties["companyId"] = CompanyId;

            if (QueueItemId > 0)
                log.Properties["queueItemId"] = QueueItemId;

            if (CallId > 0)
                log.Properties["callId"] = CallId;

            if (CallRequestId > 0)
                log.Properties["callRequestId"] = CallRequestId;

            if (!string.IsNullOrWhiteSpace(DispatchId))
                log.Properties["dispatchId"] = DispatchId;

            return log;
        }


        public static string GetEnvironmentName(int env)
        {
            if (env == 1)
                return "DEV";
            if (env == 2)
                return "TEST";
            else if (env == 3)
                return "PROD";
            else if (env == 4)
                return "OON_PROD";

            return "DEV";
        }

        public static int GetEnvironmentId(string env)
        {
            if (env == "DEV")
                return 1;
            if (env == "TEST")
                return 2;
            else if (env == "PROD")
                return 3;
            else if (env == "OON_PROD")
                return 4;

            return 1;
        }

        public sealed class DispatchAcceptModel
        {
            public string Type { get; set; } = "accept";
            public string DispatchId { get; set; }
            public string JobId { get; set; }
            public string ContractorId { get; set; }
            public int Eta { get; set; }

            /// <summary>
            /// Used for OON accepts only
            /// </summary>
            public AcceptProviderModel Provider { get; set; }
        }


        public sealed class AcceptProviderModel
        {
            public string ContractorId { get; set; }
            public string Name { get; set; }
            public string Address { get; set; }
            public string City { get; set; }
            public string State { get; set; }
            public string Zip { get; set; }
            public decimal Latitude { get; set; }
            public decimal Longitude { get; set; }
            public string Phone { get; set; }
            public string Email { get; set; }
        }


        public sealed class DispatchNotifyPaymentShared
        {
            public string Type { get; set; } = "confirmPaymentShared";
            public string DispatchId { get; set; }
            public string JobId { get; set; }
            public string ContractorId { get; set; }
            public decimal Amount { get; set; }
        }


        public sealed class RequestPhoneCallModel
        {

            public string Type { get; set; } = "requestCallback";
            public string DispatchId { get; set; }
            public string ContractorId { get; set; }

            public string Name { get; set; }
            public string Phone { get; set; }
        }

        public sealed class DispatchRefuseModel
        {
            public string Type { get; set; } = "refuse";
            public string DispatchId { get; set; }
            public string ContractorId { get; set; }
            public string JobId { get; set; }
            public int ReasonId { get; set; }
            public string ReasonName { get; set; }
        }

        public sealed class DispatchCancelModel
        {
            public string Type { get; set; } = "cancel";
            public string DispatchId { get; set; }
            public string ContractorId { get; set; }
            public string LocationId { get; set; }
            public string JobId { get; set; }
            public string ReasonId { get; set; }
            public string ReasonName { get; set; }
            public string Comments { get; set; }
        }


        public sealed class DispatchExpiredModel
        {
            public string Type { get; set; } = "expired";
            public string DispatchId { get; set; }
            public string ContractorId { get; set; }

        }
        public sealed class DispatchStatusUpdateModel
        {
            public string Type { get; set; } = "statusUpdate";
            public string DispatchId { get; set; }
            public string ContractorId { get; set; }
            public string Status { get; set; }
            public DriverModel Driver { get; set; }
        }

        public sealed class DispatchPhotoModel
        {
            public string Type { get; set; } = "photo";
            public string DispatchId { get; set; }
            public string ContractorId { get; set; }
            public decimal Latitude { get; set; }
            public decimal Longitude { get; set; }
            public DateTime Timestamp { get; set; }
            public string Url { get; set; }
        }

        public sealed class DispatchGoaModel
        {
            public string Type { get; set; } = "goa";
            public string DispatchId { get; set; }
            public string ContractorId { get; set; }
            public string ReasonId { get; set; }
            public string ReasonName { get; set; }
            public string Comments { get; set; }
        }

        public sealed class DriverModel
        {
            public string Id { get; set; }
            public string FirstName { get; set; }
            public string LastName { get; set; }
            public decimal Latitude { get; set; }
            public decimal Longitude { get; set; }
        }

        public sealed class DispatchCompleteModel
        {
            public string Type { get; set; } = "complete";
            public string DispatchId { get; set; }
            public string ContractorId { get; set; }
            public string LocationId { get; set; }
            public DriverModel Driver { get; set; }

            public DateTime? DispatchedTime { get; set; }
            public DateTime? EnrouteTime { get; set; }
            public DateTime? OnSceneTime { get; set; }
            public DateTime? TowingTime { get; set; }
            public DateTime? DestinationTime { get; set; }
            public DateTime? CompletionTime { get; set; }

            public int? Odometer { get; set; }
            public string Vin { get; set; }

            public sealed class InvoiceItemModel
            {
                public string Code { get; set; }
                public decimal Quantity { get; set; }
                public decimal Price { get; set; }
            }
        }


        public sealed class DispatchExtendEtaModel
        {
            public string Type { get; set; } = "updateEta";
            public string DispatchId { get; set; }
            public string ContractorId { get; set; }
            public string LocationId { get; set; }
            public string ReasonId { get; set; }
            public string ReasonName { get; set; }
            public string Comments { get; set; }
            public string ResourceName { get; set; }

            public DateTime NewEta { get; set; }
            public int ExtendEtaMinutes { get; set; }
        }


        public void Notify(DispatchNotifyPaymentShared payload)
        {
            var log = NewLogEvent("DispatchNotifyPaymentShared", payload.ToJson());

            try
            {
                if (payload.Type != "confirmPaymentShared")
                    throw new ArgumentException("Type must be: accept");

                if (string.IsNullOrWhiteSpace(payload.DispatchId))
                    throw new ArgumentException("DispatchId is null or empty - it must be specified.");

                if (string.IsNullOrWhiteSpace(payload.ContractorId))
                    throw new ArgumentException("ContractorId is null or empty - it must be specified.");

                var resp = Send(CallUpdateUrl, payload.ToJson(), Method.Post);

                Console.WriteLine(payload.ToJson());

                log.Properties["contractorId"] = payload.ContractorId;
                log.Properties["dispatchId"] = payload.DispatchId;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;

                    throw new Exception("Error invoking notify endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            finally
            {
                logger.Log(log);
            }
        }


        public void Accept(DispatchAcceptModel payload)
        {
            var log = NewLogEvent("Accept", payload.ToJson());

            try
            {
                if (payload.Type != "accept")
                    throw new ArgumentException("Type must be: accept");

                if (string.IsNullOrWhiteSpace(payload.DispatchId))
                    throw new ArgumentException("DispatchId is null or empty - it must be specified.");

                if (string.IsNullOrWhiteSpace(payload.ContractorId))
                    throw new ArgumentException("ContractorId is null or empty - it must be specified.");


                if (payload.ContractorId?.ToUpperInvariant() == "OUT_oF_NETWORK" &&
                    payload.Provider == null)
                    throw new ArgumentException("Provider is null or empty - it must be specified for an OUT_OF_NETWORK accept.");


                var resp = Send(CallUpdateUrl, payload.ToJson(), Method.Post);

                Console.WriteLine(payload.ToJson());
                Console.WriteLine(resp.Content);

                log.Properties["contractorId"] = payload.ContractorId;
                log.Properties["dispatchId"] = payload.DispatchId;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;

                    throw new Exception("Error invoking accept endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            finally
            {
                logger.Log(log);
            }
        }


        public void Refuse(DispatchRefuseModel payload)
        {
            var log = NewLogEvent("Refuse", payload.ToJson());

            try
            {
                if (payload.Type != "refuse")
                    throw new ArgumentException("Type must be: refuse");

                if (string.IsNullOrWhiteSpace(payload.DispatchId))
                    throw new ArgumentException("DispatchId is null or empty - it must be specified.");

                if (string.IsNullOrWhiteSpace(payload.ContractorId))
                    throw new ArgumentException("ContractorId is null or empty - it must be specified.");

                if (payload.ReasonId == 0)
                    throw new ArgumentException("ReasonId is 0 - it must have a value specified.");

                var resp = Send(CallUpdateUrl, payload.ToJson(), Method.Post);

                log.Properties["contractorId"] = payload.ContractorId;
                log.Properties["dispatchId"] = payload.DispatchId;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;

                    throw new Exception("Error invoking Refused endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            finally
            {
                logger.Log(log);
            }
        }


        public void RequestPhoneCall(RequestPhoneCallModel payload)
        {
            var log = NewLogEvent("RequestPhoneCall", payload.ToJson());

            try
            {
                if (payload.Type != "requestCallback")
                    throw new ArgumentException("Type must be: requestCallback");

                if (string.IsNullOrWhiteSpace(payload.DispatchId))
                    throw new ArgumentException("DispatchId is null or empty - it must be specified.");

                if (string.IsNullOrWhiteSpace(payload.ContractorId))
                    throw new ArgumentException("ContractorId is null or empty - it must be specified.");

                var resp = Send(CallUpdateUrl, payload.ToJson(), Method.Post);

                log.Properties["contractorId"] = payload.ContractorId;
                log.Properties["dispatchId"] = payload.DispatchId;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;

                    throw new Exception("Error invoking Request Callback endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            finally
            {
                logger.Log(log);
            }
        }



        public void ExtendEta(DispatchExtendEtaModel payload)
        {
            if (payload.Type != "updateEta")
                throw new ArgumentException("Type must be updateEta");

            var log = NewLogEvent("ExtendETA", payload.ToJson());

            try
            {
                var resp = Send(CallUpdateUrl, payload.ToJson(), Method.Post);

                log.Properties["contractorId"] = payload.ContractorId;
                log.Properties["dispatchId"] = payload.DispatchId;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;

                    throw new Exception("Error invoking ExtendETA endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            finally
            {
                logger.Log(log);
            }
        }



        private static readonly string[] ValidStatuses = new string[] { "DISPATCHED", "EN_ROUTE", "ON_SCENE", "TOWING", "DESTINATION", "COMPLETE" };

        public static class StatusCodes
        {
            public const string Dispatched = "DISPATCHED";
            public const string Enroute = "EN_ROUTE";
            public const string OnScene = "ON_SCENE";
            public const string Towing = "TOWING";
            public const string Destination = "DESTINATION";
            public const string Complete = "COMPLETE";
        }

        static string ComputeSignature(string stringToSign)
        {
            string secret = "31A5EAC1-FFAE-4E5A-AA0A-E1720A56A63A";
            using (var hmacsha256 = new HMACSHA256(Encoding.ASCII.GetBytes(secret)))
            {
                var bytes = Encoding.ASCII.GetBytes(stringToSign);
                var hashedBytes = hmacsha256.ComputeHash(bytes);
                //return Encoding.ASCII.GetString(hashedBytes);

                return BitConverter.ToString(hashedBytes).Replace("-", "").ToLower();
            }
        }
        public void StatusUpdate(DispatchStatusUpdateModel payload)
        {
            var log = NewLogEvent("StatusUpdate", payload.ToJson());

            try
            {
                if (payload.Type != "statusUpdate")
                    throw new ArgumentException("Type must be: statusUpdate");

                if (payload.Status != StatusCodes.Dispatched &&
                    payload.Status != StatusCodes.Enroute &&
                    payload.Status != StatusCodes.OnScene &&
                    payload.Status != StatusCodes.Towing &&
                    payload.Status != StatusCodes.Destination &&
                    payload.Status != StatusCodes.Complete)
                    throw new ArgumentException("Invalid Status value passed: " + payload.Status + ".");

                var resp = Send(CallUpdateUrl, payload.ToJson(), Method.Post);

                log.Properties["contractorId"] = payload.ContractorId;
                log.Properties["dispatchId"] = payload.DispatchId;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;

                    throw new Exception("Error invoking StatusUpdate endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            finally
            {
                logger.Log(log);
            }
        }

        public sealed class DispatchBreadcrumbPayload
        {
            public string Type { get; set; } = "breadcrumb";
            public string DispatchId { get; set; }
            public string DriverId { get; set; }
            public DateTime Timestamp { get; set; }
            public decimal Latitude { get; set; }
            public decimal Longitude { get; set; }
        }

        public void Breadcrumb(DispatchBreadcrumbPayload payload)
        {
            var log = NewLogEvent("Breadcrumb", payload.ToJson());

            try
            {
                if (payload.Type != "breadcrumb")
                    throw new ArgumentException("Type must be: breadcrumb");

                var resp = Send(CallUpdateUrl, payload.ToJson(), Method.Patch);

                log.Properties["dispatchId"] = payload.DispatchId;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;

                    throw new Exception("Error invoking Refused endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            catch (Exception y)
            {
                log.Level = LogLevel.Error;
                log.Exception = y;
                throw;
            }
            finally
            {
                logger.Log(log);
            }
        }

        public void Photo(DispatchPhotoModel payload)
        {
            var log = NewLogEvent("Photo", payload.ToJson());

            try
            {
                if (payload.Type != "photo")
                    throw new ArgumentException("Type must be: photo");

                if (string.IsNullOrWhiteSpace(payload.DispatchId))
                    throw new ArgumentException("DispatchId is null or empty - it must be specified.");

                if (string.IsNullOrWhiteSpace(payload.ContractorId))
                    throw new ArgumentException("ContractorId is null or empty - it must be specified.");

                var resp = Send(CallUpdateUrl, payload.ToJson(), Method.Post);

                log.Properties["contractorId"] = payload.ContractorId;
                log.Properties["dispatchId"] = payload.DispatchId;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;

                    throw new Exception("Error invoking Photo endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            finally
            {
                logger.Log(log);
            }
        }

        private const string CallUpdateUrl = "/";

        public void Cancel(DispatchCancelModel payload)
        {
            var log = NewLogEvent("Cancel", payload.ToJson());

            try
            {
                if (payload.Type != "cancel")
                    throw new ArgumentException("Type must be: cancel");

                if (string.IsNullOrWhiteSpace(payload.ContractorId))
                    throw new ArgumentException("ContractorId is null or empty - it must be specified.");

                if (string.IsNullOrWhiteSpace(payload.DispatchId))
                    throw new ArgumentException("DispatchId is null or empty - it must be specified.");

                var resp = Send(CallUpdateUrl, payload.ToJson(), Method.Post);

                log.Properties["contractorId"] = payload.ContractorId;
                log.Properties["dispatchId"] = payload.DispatchId;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;

                    throw new Exception("Error invoking Cancel endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            catch (Exception y)
            {
                log.Level = LogLevel.Error;
                log.Exception = y;
                throw;
            }
            finally
            {
                logger.Log(log);
            }
        }

        public void Goa(DispatchGoaModel payload)
        {
            var log = NewLogEvent("GOA", payload.ToJson());

            try
            {
                if (payload.Type != "goa")
                    throw new ArgumentException("Type must be goa");

                var resp = Send(CallUpdateUrl, payload.ToJson(), Method.Post);

                log.Properties["contractorId"] = payload.ContractorId;
                log.Properties["dispatchId"] = payload.DispatchId;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;

                    throw new Exception("Error invoking GOA endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            catch (Exception y)
            {
                log.Level = LogLevel.Error;
                log.Exception = y;
                throw;
            }
            finally
            {
                logger.Log(log);
            }
        }

        public void Complete(DispatchCompleteModel payload)
        {
            var log = NewLogEvent("Complete", payload.ToJson());

            try
            {
                if (payload.Type != "complete")
                    throw new ArgumentException("Type must be: complete");

                if (string.IsNullOrWhiteSpace(payload.DispatchId))
                    throw new ArgumentException("DispatchId is null or empty - it must be specified.");

                var resp = Send(CallUpdateUrl, payload.ToJson(), Method.Post);

                log.Properties["dispatchId"] = payload.DispatchId;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;

                    throw new Exception("Error invoking Complete endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            catch (Exception y)
            {
                log.Level = LogLevel.Error;
                log.Exception = y;
                throw;
            }
            finally
            {
                logger.Log(log);
            }
        }


        public void Expired(DispatchExpiredModel payload)
        {
            var log = NewLogEvent("Expired", payload.ToJson());

            try
            {
                if (payload.Type != "expired")
                    throw new ArgumentException("Type must be: expired");

                if (string.IsNullOrWhiteSpace(payload.ContractorId))
                    throw new ArgumentException("ContractorId is null or empty - it must be specified.");

                if (string.IsNullOrWhiteSpace(payload.DispatchId))
                    throw new ArgumentException("DispatchId is null or empty - it must be specified.");

                var resp = Send(CallUpdateUrl, payload.ToJson(), Method.Post);

                log.Properties["contractorId"] = payload.ContractorId;
                log.Properties["dispatchId"] = payload.DispatchId;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;

                    throw new Exception("Error invoking Cancel endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            catch (Exception y)
            {
                log.Level = LogLevel.Error;
                log.Exception = y;
                throw;
            }
            finally
            {
                logger.Log(log);
            }
        }

        public RestResponse Send(string url, string payload, Method method)
        {
            var client = GetRestClient();

            var request = new RestRequest(url, method);

            if (method != Method.Get)
                request.AddParameter("application/json", payload, ParameterType.RequestBody);

            Console.WriteLine(payload);

            var response = client.Execute(request);

            Console.WriteLine(DateTime.Now.ToString() + " " + token.InstanceUrl + url + ": " + response.Content);

            return response;
        }


        public void Login(string contractorId)
        {
            var log = NewLogEvent("Login", contractorId);

            try
            {
                var resp = Send(CallUpdateUrl,
                    new
                    {
                        type = "login",
                        contractorId
                    }.ToJson(), Method.Post);

                log.Properties["contractorId"] = contractorId;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;

                    throw new Exception("Error invoking Login endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            finally
            {
                logger.Log(log);
            }
        }


        public void Logout(string contractorId)
        {
            var log = NewLogEvent("Logout", contractorId);

            try
            {
                var resp = Send(CallUpdateUrl,
                    new
                    {
                        type = "logout",
                        contractorId
                    }.ToJson(), Method.Post);

                log.Properties["contractorId"] = contractorId;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;

                    throw new Exception("Error invoking Logout endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            finally
            {
                logger.Log(log);
            }
        }


        public sealed class RoadsideProtectAccount
        {
            public string Id { get; set; }
            public string Name { get; set; }
            public bool Logged_In_Towbook__c { get; set; }
        }


        public RoadsideProtectAccessTokenPayload GetToken()
        {

            return token;
        }

    }


    public sealed class RoadsideProtectAccessTokenPayload
    {
        [JsonProperty("access_token")]
        public string AccessToken { get; set; }

        [JsonProperty("instance_url")]
        public string InstanceUrl { get; set; }
        public string Id { get; set; }

        [JsonProperty("token_type")]
        public string TokenType { get; set; }

        [JsonProperty("issued_at")]
        public string IssuedAt { get; set; }
        public string Signature { get; set; }
    }

}
