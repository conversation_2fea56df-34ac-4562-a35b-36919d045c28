using Extric.Towbook.Dispatch;
using Extric.Towbook.SquareIntegration;
using Extric.Towbook.Utility;
using NLog;
using Square;
using Square.Models;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using Extric.Towbook.API.Integration.Square.Exceptions;
using Extric.Towbook.API.Integration.Square.Model;
using Square.Exceptions;
using Extric.Towbook.Accounts;
using Extric.Towbook.API.Integration.Square.Model.Square;
using Payment = Square.Models.Payment;
using System.Threading.Tasks;
using Environment = Square.Environment;
using Extric.Towbook.API.Integration.SquareApi.Model.Square.Webhook.DeviceCode;
using Extric.Towbook.Integration;
using System.Collections.ObjectModel;
using Square.Authentication;

namespace  Extric.Towbook.API.Integration.Square
{
    public class SquareUtils
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();
        public static readonly int SquareUserId = 272049;

        public static readonly Environment env = !String.IsNullOrEmpty(Core.GetAppSetting("Square:Environment")) ? Core.GetAppSetting<Environment>("Square:Environment") : Environment.Production;
        public static readonly int[] DefaultTipPercentages = new int[] { 10, 15, 20 };

        private static string _getApiExceptionMessage(ApiException exception)
        {
            return
                exception.Errors.Aggregate("", (current, err) =>
                                        current + ":" + err.ToJson() + "\n").Trim();
        }

        private static async Task<IEnumerable<Location>> _getLocations(int companyId)
        {
            var authorization = await GetAuthorizationAsync(companyId);
            
            if (authorization == null)
                throw new AuthorizationMissingException();
            
            var square = GetSquare(authorization.CompanyId, authorization.AccessToken);
            var res = await square.LocationsApi.ListLocationsAsync();

            if (res.Errors != null)
                throw new SquareApiException(companyId, new ApiException("Square api returned an error", res.Context));

            return res.Locations ?? new List<Location>();
        }

        public static Environment GetEnvironment(int companyId)
        {
            var url = Core.GetAppSetting("Towbook:WebAppUrl") ?? "https://app.towbook.com";

            var sandboxCompanyIds = new[] { 10577 };

            if (url.Contains("localhost"))
                return Environment.Sandbox;

            if (companyId > 0 && sandboxCompanyIds.Contains(companyId))
                return Environment.Sandbox;

            return Environment.Production;
        }

        public static string GetEnv(int companyId)
        {
            return GetEnvironment(companyId) == Environment.Sandbox ? "sandbox" : "production";
        }

        public static async Task<IEnumerable<Location>> GetLocations(int companyId)
        {
            return await SquareApiSafeCallWrapper(companyId, () => _getLocations(companyId));
        }

        public static async Task<IEnumerable<SquareLocation>> GetLocationsRaw(int companyId)
        {
            var res = await SquareApiSafeCallWrapper(companyId, () => _getLocations(companyId));

            return res.Select(o => new SquareLocation
            {
                LocationId = o.Id,
                Name = o.Name ?? o.BusinessName,
                Capabilities = o.Capabilities,
                Address = o.Address?.AddressLine1,
                Country = o.Country,
                Currency = o.Currency,
                MerchantId = o.MerchantId,
                Type = o.Type,
                Status = o.Status,
                PhoneNumber = o.PhoneNumber,
                CreatedAt = o.CreatedAt
            });
        }
        
        private static Location FetchLocationById(object locationId, SquareCompanyAuthorization authorization)
        {
            var square = GetSquare(authorization.CompanyId, authorization.AccessToken);
            var res = square.LocationsApi.RetrieveLocation(locationId.ToString());
            
            if (res.Errors != null)
                throw new ApiException("Square api returned an error", res.Context);

            return res.Location;
        }
        
        public static object GetLocationById(string locationId, SquareCompanyAuthorization authorization)
        {
            return FetchLocationById(locationId, authorization);
        }
        
        public static async Task<MobileAuthorizacion> GetMobileAuthorization(int companyId)
        {
            return await SquareApiSafeCallWrapper(companyId, () => _getMobileAuthorization(companyId));
        }

        private static async Task<MobileAuthorizacion> _getMobileAuthorization(int companyId)
        {
            if (!Company.Company.GetById(companyId).HasFeature(Generated.Features.PaymentIntegrations_Square))
                throw new Exception("Your company does not have Square enabled.");
                
            var authorization = await GetValidAuthorization(companyId);
            var square = GetSquare(authorization.CompanyId, authorization.AccessToken);
            var body = new CreateMobileAuthorizationCodeRequest.Builder()
                .LocationId(authorization.Location.LocationId)
                .Build();

            try
            {
                var res = await square.MobileAuthorizationApi.CreateMobileAuthorizationCodeAsync(body);

                if (res.Errors != null)
                    throw new ApiException("Square api returned an error", res.Context);

                return new MobileAuthorizacion
                {
                    Token = res.AuthorizationCode,
                    LocationId = authorization.Location.LocationId
                };
            }
            catch (Exception e)
            {
                if (e is ApiException apiException)
                    throw new SquareApiException(companyId, apiException);

                throw;
            }
        }

        #region Private Static methods

        public static async Task<SquareCompanyAuthorization> RenewAuthorization(int companyId, string reason)
        {
            var authorization = await GetAuthorizationAsync(companyId);
            return await RenewAuthorization(authorization, reason);
        }
        public static async Task<SquareCompanyAuthorization> RenewAuthorization(SquareCompanyAuthorization authorization, string reason)
        {
            var clientId = GetConfig(authorization.CompanyId, "ApplicationId");
            var clientSecret = GetConfig(authorization.CompanyId, "ApplicationSecret");
            var square = new SquareClient.Builder()
                .Environment(SquareUtils.GetEnvironment(authorization?.CompanyId ?? 0))
                .Build();
            var body = new ObtainTokenRequest.Builder(clientId,  "refresh_token")
                .ClientSecret(clientSecret)
                .RefreshToken(authorization.RefreshToken)
                .Build();

            try
            {
                // RenewToken
                var result = await square.OAuthApi.ObtainTokenAsync(body);

                // update prev authorization (as invalidated)
                authorization.Valid = false;
                authorization.InvalidationReason = reason;
                authorization.Save();

                // save new authorization
                var newAuthorization = new SquareCompanyAuthorization()
                {
                    CompanyId = authorization.CompanyId,
                    AccessToken = result.AccessToken,
                    TokenType = result.TokenType,
                    RefreshToken = result.RefreshToken,
                    ExpiresAt = result.ExpiresAt,
                    MerchantId = result.MerchantId,
                    SubscriptionId = result.SubscriptionId,
                    PlanId = result.PlanId,
                    Renewed = true
                };
                newAuthorization.Save();

                // update locations
                var companyLocations = SquareCompanyLocation.GetAllByAuthorizationId(authorization.Id);
                if (companyLocations.Any())
                {
                    foreach (var companyLocation in companyLocations)
                    {
                        var scl = new SquareCompanyLocation
                        {
                            CompanyId = companyLocation.CompanyId,
                            LocationId = companyLocation.LocationId,
                            AuthorizationId = newAuthorization.Id
                        };
                        scl.Save();   
                    }   
                }

                return newAuthorization;
            }
            catch (Exception e)
            {
                if (e is ApiException apiException)
                {
                    if (apiException.Errors.Any(x => x.Code == "too_many_requests"))
                    {
                        // When Square "rate limits" the request, don't save the authorization as invalid.
                        // Rather, throw the exception now to let the caller handle any retry/back-off strategies
                        throw;
                    }

                    authorization.Valid = false;
                    authorization.InvalidationReason = apiException.Errors?[0]?.Detail; 
                    authorization.Save();
                }

                throw;
            }
        }

        public static async Task<T> SquareApiSafeCallWrapper <T> (int companyId, Func<Task<T>> func)
        {
            try
            {
                return await func();
            }
            catch(Exception e)
            {
                // Check if the failure reason was: ACCESS_TOKEN_EXPIRED, If [YES] => Then try renew token 
                switch (e)
                {
                    case ApiException apiException:
                        const string tokenExpiredErrorCode = "ACCESS_TOKEN_EXPIRED";
                        var tokenExpired = apiException.Errors.Any(x => x.Code == tokenExpiredErrorCode);

                        if (!tokenExpired)
                            throw new SquareApiException(companyId, apiException);

                        try
                        {
                            await RenewAuthorization(companyId, tokenExpiredErrorCode);

                            return await func();
                        }
                        catch (Exception rae)
                        {
                            throw new SquareApiException(companyId, "BAD_REQUEST", rae);
                        }
                        
                    case AuthorizationMissingException ame:
                        throw new SquareApiException(companyId, "UNAUTHORIZED", ame);
                    
                    default:
                        throw new SquareApiException(companyId, "BAD_REQUEST", e);
                }
            }
        }

        public static async Task<Payment> CreatePayment(int companyId, string nonce, string verificationToken, decimal amount, string currency, string referenceId, string note, string customerId)
        {
            return await SquareApiSafeCallWrapper(companyId, () => _createPayment(companyId, nonce, verificationToken, amount, currency, referenceId, note, customerId));
        }

        private static async Task<Payment> _createPayment(int companyId, string nonce, string verificationToken, decimal amount, string currency, string referenceId, string note, string customerId)
        {
            var authorization = await GetAuthorizationAsync(companyId);
            
            if (authorization == null)
                throw new AuthorizationMissingException($"Square authorization is missing or invalid for companyId = {companyId}.");
            
            var square = GetSquare(authorization.CompanyId, authorization.AccessToken);
            var paymentsApi = square.PaymentsApi;

            var idempotencyKey = Core.GetRedisValue($"square:new_payment_idempotencyKey:{nonce}");

            if (idempotencyKey == null)
            {
                idempotencyKey = Guid.NewGuid().ToString();
                Core.SetRedisValue($"square:new_payment_idempotencyKey:{nonce}", idempotencyKey);
            }

            var bodyAmountMoney = new Money.Builder()
                .Amount((long)(amount * 100)) // converts dollars to cents
                .Currency(currency)
                .Build();

            var body = new CreatePaymentRequest.Builder(
                    nonce,
                    idempotencyKey)
                .AmountMoney(bodyAmountMoney)
                .Autocomplete(true)
                .LocationId(authorization.Location.LocationId)
                .ReferenceId(referenceId)
                .VerificationToken(verificationToken)
                .Note(note)
                .CustomerId(customerId)
                .Build();
            
            // Create the payment on square
            var result = await paymentsApi.CreatePaymentAsync(body);

            if (result.Errors != null)
            {
                throw new ApiException("Square api returned an error", result.Context);
            }
            
            return result.Payment;
        }
        
        public static async Task<Payment> CreatePaymentWithLineItems(int companyId, PaymentDefinition payload, LogEventInfo log = null, Dictionary<string, string> metaData = null)
        {
            return await SquareApiSafeCallWrapper(companyId, () => _createPaymentWithLineItems(companyId, payload, log, metaData));
        }
        
        private static async Task<Payment> _createPaymentWithLineItems(int companyId, PaymentDefinition payload, LogEventInfo log = null, Dictionary<string,string> metaData = null)
        {
            var authorization = await GetAuthorizationAsync(companyId);
            
            if (authorization == null)
                throw new AuthorizationMissingException($"Square authorization is missing or invalid for companyId = {companyId}.");
            
            if (authorization.Location == null)
                throw new AuthorizationMissingException($"Square location is not configured for company authorization, companyId = {companyId}.");
            
            var square = GetSquare(authorization.CompanyId, authorization.AccessToken);

            Customer customer = null;
            Order order = null;

            if (payload.BillingContact != null)
            {
                customer = await GetOrCreateCustomer(companyId, payload.BillingContact);
                
                if (log != null) 
                    log.Properties["customer"] = customer;
            }

            var locationId = authorization.Location.LocationId;
            var idempotencyKey = GetIdempotencyKey("nonce", payload.Nonce);
            var referenceId = payload.ReferenceId?.Truncate(40);

            if (metaData == null)
                metaData = new Dictionary<string, string>();
            
            if(payload.Entry?.Id > 0)
            {
                metaData["callNumber"] = payload.Entry.CallNumber.ToString();
                metaData["callId"] = payload.Entry.Id.ToString();
                metaData["companyName"] = payload.Entry.Company.Name.Truncate(255);
            }

            if (payload.LineItems != null && payload.LineItems.Count > 0)
            {
                try
                {
                    if (log != null) 
                        log.Properties["order_data"] = new
                        {
                            payload.Amount, 
                            payload.LineItems, 
                            LocationId = locationId, 
                            payload.Tax, 
                            payload.DiscountRate,
                            ReferenceId = referenceId, 
                            IdempotencyKey = "or" + idempotencyKey, 
                            payload.Currency, 
                            CustomerId = customer?.Id,
                            MetaData = metaData
                        };
                    
                    order = await CreateOrder(square, payload.Amount, payload.LineItems, locationId, payload.Tax, 
                        payload.DiscountRate,
                        referenceId, "or" + idempotencyKey, payload.Currency, customer?.Id, metaData);
                    
                    if (log != null)
                        log.Properties["order"] = order;
                }
                catch (Exception e)
                {
                    throw new SquareApiException(companyId, e);
                }
            }

            var paymentsApi = square.PaymentsApi;

            var bodyAmountMoney = new Money.Builder()
                .Amount((long)(payload.Amount * 100)) // converts dollars to cents
                .Currency(payload.Currency)
                .Build();

            if (log != null) 
                log.Properties["amountInCents"] = bodyAmountMoney.Amount.Value;

            var body = new CreatePaymentRequest.Builder(
                    payload.Nonce,
                    idempotencyKey)
                .AmountMoney(bodyAmountMoney)
                .Autocomplete(true)
                .LocationId(authorization.Location.LocationId);
            
            if (payload.Note != null)
                body.Note(payload.Note);
            if (payload.VerificationToken != null)
                body.VerificationToken(payload.VerificationToken);
            if (referenceId != null)
                body.ReferenceId(referenceId);
            if (customer != null)
                body.CustomerId(customer.Id);
            if (order != null)
                body.OrderId(order.Id);

            if (log != null)
                log.Properties["paymentBody"] = body.Build();
            
            // Create the payment on square
            var result = await paymentsApi.CreatePaymentAsync(body.Build());
            
            if (log != null)
                log.Properties["paymentResult"] = result;

            if (result.Errors != null)
            {
                if (log != null)
                    log.Properties["paymentErrors"] = result.Errors;
                
                throw new ApiException("Square api returned an error", result.Context);
            }
            
            return result.Payment;
        }

        public static async Task<PaymentLinkResult> ProcessPaymentLink(PaymentRequest paymentRequest)
        {
            var metaData = new Dictionary<string, string>();

            var log = new LogEventInfo
            {
                Level = LogLevel.Info,
                TimeStamp = DateTime.Now
            };
            log.Properties["type"] = "SquarePaymentLink";

            #region Request validations
            if (paymentRequest == null)
                throw new Exception($"Invalid request, parameter PaymentRequest cannot be null.");
                
            if (paymentRequest.PaymentLinkId < 0)
                throw new Exception($"Invalid request, invalid value for parameter PaymentRequest.PaymentLinkId.");
                
            if (string.IsNullOrEmpty(paymentRequest.Nonce))
                throw new Exception($"Invalid request, invalid value for parameter PaymentRequest.Nonce.");
                
            if (string.IsNullOrEmpty(paymentRequest.VerificationToken))
                throw new Exception($"Invalid request, parameter VerificationToken is required.");
                
            if (paymentRequest.BillingContact == null)
                throw new Exception($"Invalid request, parameter BillingContact cannot be null.");
                
            if (string.IsNullOrEmpty(paymentRequest.BillingContact.Phone))
                throw new Exception($"Invalid request, parameter BillingContact.Phone is required.");
                
            if (string.IsNullOrEmpty(paymentRequest.BillingContact.Email))
                throw new Exception($"Invalid request, parameter BillingContact.Email is required.");
                
            if (string.IsNullOrEmpty(paymentRequest.BillingContact.GivenName) || string.IsNullOrEmpty(paymentRequest.BillingContact.FamilyName))
                throw new Exception($"Invalid request, parameter BillingContact GivenName and FamilyName are required.");
            #endregion
            
            var paymentLink = PaymentLink.GetById(paymentRequest.PaymentLinkId);

            #region Validate paymentLink
            if (paymentLink == null)
                throw new PaymentLinkNotFoundException($"Payment Link with ID = {paymentRequest.PaymentLinkId} is invalid or does not exist.");
            if (paymentLink.DispatchEntryId == null && paymentLink.StatementId == null)
                throw new Exception($"Invalid Request: specify a DispatchEntryId or StatementId is required, both IDs can't be null or empty.");
            #endregion
            
            var authorization = await GetAuthorizationAsync(paymentLink.CompanyId);

            #region validate authorization 
            if (authorization == null || string.IsNullOrEmpty(authorization.AccessToken))
                throw new Exception("Square authorization is required for your company. Log in online to complete this setup under the Settings Page in Towbook.");
            
            if (authorization.Location == null || string.IsNullOrEmpty(authorization.Location.LocationId))
                throw new Exception("A Square Location is required to complete the integration. Log in online to complete this setup under the Settings Page in Towbook.");
            #endregion
            
            log.Properties["json"] = paymentRequest.ToJson();
            
            var paymentDef = paymentLink.DispatchEntryId != null
                ? CreatePaymentDefinitionFromEntry(paymentLink.DispatchEntryId.Value, paymentRequest.Amount)
                : await CreatePaymentDefinitionFromStatementAsync(paymentLink.StatementId.Value, paymentRequest.Amount);

            paymentDef.BillingContact = paymentRequest.BillingContact;
            paymentDef.Nonce = paymentRequest.Nonce;
            paymentDef.VerificationToken = paymentRequest.VerificationToken;
            
            log.Properties["paymentData"] = new
            {
                paymentDef.CompanyId,
                paymentDef.Amount,
                paymentDef.Tax,
                paymentDef.DiscountRate,
                paymentDef.Currency,
                paymentDef.Nonce,
                paymentDef.Note,
                paymentDef.LineItems,
                paymentDef.BillingContact
            };

            var companyId = paymentDef.CompanyId;
            
            try
            {
                paymentLink.Status = PaymentLinkStatus.Processing;
                paymentLink.Save();

                if(paymentLink?.Id > 0)
                {
                    if (paymentLink.DispatchEntryId > 0) {
                        var ewl = EntryWebLink.GetByPaymentLinkId(paymentLink.Id);
                        if(ewl != null)
                            metaData["referrer"] = ewl.TinyUrl;

                        metaData["callId"] = paymentLink.DispatchEntryId.ToString();

                        if (paymentDef.Entry == null)
                            paymentDef.Entry = await Entry.GetByIdAsync(paymentLink.DispatchEntryId.Value);
                    }

                    if (paymentLink.StatementId > 0)
                    {
                        var swl = StatementWebLink.GetByPaymentLinkId(paymentLink.Id);
                        if (swl != null)
                            metaData["referrer"] = swl.TinyUrl;

                        metaData["statementId"] = paymentLink.StatementId.ToString();

                        if (paymentDef.Statement == null)
                            paymentDef.Statement = await Statement.GetByIdAsync(paymentLink.StatementId.Value);
                    }
                    
                }

                metaData["commitId"] = Core.GetCommitId();

                // Create the payment on square
                var payment = await CreatePaymentWithLineItems(companyId, paymentDef, log, metaData);

                // update status of local payment references 
                if (paymentLink.DispatchEntryId != null && paymentDef.Entry != null)
                {
                    log.Properties["callId"] = paymentLink.DispatchEntryId;

                    #region Update Invoice Payment
                    var ip = new InvoicePayment
                    {
                        ReferenceNumber = payment.Id,
                        PaymentType = PaymentType.Square,
                        Amount = paymentDef.Amount,
                        InvoiceId = paymentDef.Entry.Invoice.Id,
                        OwnerUserId = 272049,
                        PaymentDate = DateTime.Now
                    };
                    await ip.Save();
                    #endregion

                    await InvoicePaymentState.RegisterStatusUpdate(ip.Id, InvoicePaymentStatus.Processing, "payment_link");

                    #region Record Square Payment transaction in local db
                    var squarePayment = BuildSquarePaymentFrom(ip, payment, authorization);
                    squarePayment.CompanyId = companyId;
                    squarePayment.CustomerId = payment.CustomerId;
                    squarePayment.PaymentLinkId = paymentLink.Id;
                    squarePayment.Save();
                    #endregion
                }
                else if (paymentLink.StatementId != null && paymentDef.Statement != null)
                {
                    log.Properties["statementId"] = paymentLink.StatementId;

                    #region Record Account Payment
                    var ap = new Accounts.Payment
                    {
                        CompanyId = companyId,
                        AccountId = paymentDef.Statement.AccountId,
                        OwnerUserId = 272049,
                        PaymentDate = DateTime.Now,
                        SplitType = SplitType.Multiple,
                        ReferenceNumber = payment.Id,
                        Type = PaymentType.Square,
                        Amount = paymentDef.Amount
                    };
                    ap.Save();
                    #endregion

                    // Record each invoice payment
                    var entries = paymentDef.Statement.DispatchEntries.OrderBy(e => e.CompletionTime);
                    var distributedAmount = paymentDef.Amount;

                    foreach(var e in entries)
                    {
                        if (e.BalanceDue == 0) continue;
                        if (distributedAmount <= 0) break;
                        
                        #region Update Invoice Payment
                        var ip = new InvoicePayment
                        {
                            AccountPaymentId = ap.Id,
                            ReferenceNumber = payment.Id,
                            PaymentType = PaymentType.Square,
                            InvoiceId = e.Invoice.Id,
                            OwnerUserId = paymentLink.OwnerUserId ?? 272049,
                            PaymentDate = ap.PaymentDate,
                            Amount = e.BalanceDue >= distributedAmount ? distributedAmount : e.BalanceDue
                        };
                        await ip.Save(InvoicePayment.ResolveDifference.AdjustInvoiceWithHiddenItemIfGreaterThanTotal);
                        #endregion
                        
                        distributedAmount -= ip.Amount;

                        await InvoicePaymentState.RegisterStatusUpdate(ip.Id, InvoicePaymentStatus.Processing, "payment_link");
                        
                        #region Record Square Payment transaction in local db
                        var squarePayment = BuildSquarePaymentFrom(ip, payment, authorization);
                        squarePayment.CompanyId = companyId;
                        squarePayment.CustomerId = payment.CustomerId;
                        squarePayment.PaymentLinkId = paymentLink.Id; // <-- save ref to paymentLink source 
                        squarePayment.Save();
                        #endregion
                    }
                }

                paymentLink.Status = PaymentLinkStatus.PreApproved;
                paymentLink.Save();

                var paymentLinkResult = new PaymentLinkResult
                {
                    CompanyId = paymentDef.CompanyId, 
                    Payment = payment
                };

                return paymentLinkResult;
            }
            catch (Exception e)
            {
                log.Level = LogLevel.Error;
                log.Properties["exception"] = e;
                log.Properties["type"] = "SquareError";
                log.Properties["source"] = "SquarePaymentLink";

                throw new SquareApiException(companyId, e);
            }
            finally
            {
                logger.Log(log);
            }
        }

        public async static Task<object> DeviceCodePaired(DeviceCodePayload deviceCode)
        {
            var stc = SquareTerminalCode.GetByDeviceCodeId(deviceCode.Id);
            if (stc == null)
                return null;

            // Save updated properties
            stc.Status = deviceCode.Status;
            stc.PairDate = SafeDateTimeParse(deviceCode.PairedAt);
            stc.Save();

            // Send pusher notification
            await PushNotificationProvider.SquareDeviceCodePaired(stc.CompanyId, stc.DeviceCodeId, stc.DeviceId, stc.Status);
            
            return stc;
        }

        public async static Task<SquareTerminalCode> CreateTerminalCode(int companyId, string locationId, string name)
        {
            var authorization = await GetAuthorizationAsync(companyId);

            if (authorization == null)
                throw new AuthorizationMissingException($"Square authorization is missing or invalid for companyId = {companyId}.");

            if (string.IsNullOrEmpty(authorization.Location?.LocationId))
                throw new AuthorizationMissingLocationIdException($"The location configuration of the Square authorization is missing or invalid for companyId = {companyId}. Please review your Square settings in Towbook and set up a location.");

            var square = GetSquare(authorization.CompanyId, authorization.AccessToken);
            var devicesApi = square.DevicesApi;

            var idempotencyKey = Guid.NewGuid().ToString("N");
            var bodyDeviceCode = new DeviceCode.Builder("TERMINAL_API")
                .LocationId(locationId)
                .Name(name)
                .Build();

            var body = new CreateDeviceCodeRequest.Builder(idempotencyKey, bodyDeviceCode)
                .Build();
            
            try
            {
                CreateDeviceCodeResponse result = await devicesApi.CreateDeviceCodeAsync(body);

                if (result.Errors != null)
                    throw new SquareApiException(companyId, new ApiException("Square api returned an error", result.Context));

                var locations = await GetLocationsRaw(companyId);

                // save the code
                var terminalCode = new SquareTerminalCode()
                {
                    CompanyId = authorization.CompanyId,
                    AuthorizationId = authorization.Id,
                    LocationId = locationId,
                    LocationName = locations.Where(l => l.LocationId == locationId).FirstOrDefault()?.Name,
                    Name = name,
                    Code = result.DeviceCode.Code,
                    DeviceCodeId = result.DeviceCode.Id,
                    Status = result.DeviceCode.Status
                }; 
                terminalCode.Save();

                // log successful response from square.
                var log = new LogEventInfo
                {
                    Level = LogLevel.Info,
                    Message = "SquareCreateTerminalCode"
                };

                log.Properties["companyId"] = companyId;
                log.Properties["companyName"] = Company.Company.GetById(companyId).Name;
                log.Properties["data"] = body.ToJson();
                log.Properties["json"] = new Dictionary<string, object> {
                    { "locationId", locationId },
                    {  "name", name },
                    { "result", result },
                    { "terminalCode", terminalCode }
                }.ToJson();

                logger.Log(log);

                return terminalCode;
            }
            catch (Exception e)
            {
                throw new SquareApiException(companyId, e);
            };
        }

        
        public static PaymentDefinition CreatePaymentDefinitionFromEntry(int dispatchEntryId, decimal? amount)
        {
            var entry = Entry.GetByIdNoCache(dispatchEntryId);
            
            #region DispatchEntry validations
            if (entry == null)
                throw new Exception($"Invoice entry cannot be found.");
            
            if (entry.BalanceDue <= 0)
                throw new Exception($"The invoice is paid in full. A payment cannot be applied.");
            
            if (amount != null && amount > entry.BalanceDue)
                throw new Exception($"The amount entered ${amount} is greater than the current balance due ${entry.BalanceDue}.");
            #endregion

            TaxRate taxRate = null;

            var paymentDef = new PaymentDefinition
            {
                CompanyId = entry.CompanyId,
                Amount = amount ?? entry.BalanceDue,
                Currency = entry.Company.CurrencyCode,
                ReferenceId = "CA" + entry.Id,
                Entry = entry
            };

            #region Resolve tax rate
            if (!entry.Invoice.IsTaxExempt)
            {
                taxRate = entry.Invoice.GetTaxRate();
            }
            #endregion

            if (paymentDef.Amount == entry.InvoiceTotal)
            {
                var unitDefinition = entry.Company.Country == Company.Company.CompanyCountry.USA ? "IMPERIAL_MILE" : "METRIC_KILOMETER";

                // Set items for payment in full
                paymentDef.LineItems.AddRange(GetPaymentLineItems(entry.InvoiceItems.ToList()).Select(x => new PaymentLineItem
                {
                    Name = x.Name,
                    Quantity = x.Quantity,
                    Price = x.Price,
                    Taxable = x.Taxable,
                    MeasurementUnit = x.Name.ToUpper().Contains("MILEAGE") ? unitDefinition : "",
                    DiscountQuantity = x.DiscountQuantity,
                    HasFreeQuantity = x.HasFreeQuantity,
                    Note = x.Note,
                    ReferenceId = x.ReferenceId
                }));
                
                if (taxRate != null && entry.InvoiceTax > 0)
                    paymentDef.Tax = taxRate.Rate;

                var discountRate = entry.Invoice.GetDiscountAsRate();
                if (discountRate > 0)
                {
                    paymentDef.DiscountRate = discountRate * 100;
                }
            }
            else
            {
                // Set items for payment "custom" amount
                var lineItem = new PaymentLineItem
                {
                    Name = $"Partial payment of call #{entry.CallNumber}",
                    Quantity = 1,
                    Price = paymentDef.Amount
                };

                paymentDef.LineItems.Add(lineItem);
            }
            
            paymentDef.Note = $"Towbook - Call #{entry.CallNumber}";

            return paymentDef;
        }

        public static List<PaymentLineItem> GetPaymentLineItems(List<InvoiceItem> invoiceItems)
        {
            var result = new List<PaymentLineItem>();

            // select free mile item IDs
            var freeItems = invoiceItems.Where(x => x.Total < 0).ToList();

            // filter invoice items
            invoiceItems = invoiceItems.Where(x => x.Total > 0 && (x.RateItem == null || x.RateItem.RateItemId != 2)).ToList();

            return invoiceItems.Select(x =>
                {
                    var qty = x.Quantity;
                    bool hasFreeQuantity = false;
                    decimal? totalDiscountQuantity = null;

                    var free = freeItems.FirstOrDefault(y => y.RelatedInvoiceItemId == x.Id);
                    if (free != null)
                    {
                        hasFreeQuantity = true;
                        totalDiscountQuantity = qty > 0 ? Math.Abs(free.Quantity) : 0;
                    }

                    return new PaymentLineItem
                    {
                        Name = x.Name,
                        Quantity = qty,
                        Price = x.Price,
                        Taxable = x.Taxable,
                        HasFreeQuantity = hasFreeQuantity,
                        DiscountQuantity = totalDiscountQuantity
                    };
                })
                .Where(x => x.Quantity > 0 && x.Price > 0)
                .ToList();
        }

        private static async Task<PaymentDefinition> CreatePaymentDefinitionFromStatementAsync(int statementId, decimal? amount)
        {
            var statement = await Statement.GetByIdAsync(statementId);
            
            if (amount != null && amount > statement.NowBalance)
                throw new Exception($"The amount entered ${amount} is greater than the current balance due ${statement.NowBalance}.");
            
            var paymentDef = new PaymentDefinition
            {
                CompanyId = statement.Company.Id,
                Amount = amount ?? statement.NowBalance,
                ReferenceId = "ST" + statement.Id,
                Statement = statement,
                Currency = (await Company.Company.GetByIdAsync(statement.Company.Id)).CurrencyCode
            };

            var account =await  Account.GetByIdAsync(statement.AccountId);
            var subject = account != null ? account.Company : "Statement";
            paymentDef.Note = $"Towbook - {subject} #{statement.StatementNumber}";
                
            if (paymentDef.Amount == statement.NowBalance)
            {
                foreach (var e in statement.DispatchEntries)
                {
                    paymentDef.LineItems.Add(new PaymentLineItem
                    {
                        Name = e.InvoiceNumber != null ? "Invoice #" + e.InvoiceNumber : "Call #" + e.CallNumber,
                        Quantity = 1,
                        Price = e.BalanceDue
                    });
                }
            }

            return paymentDef;
        }

        public static async Task<int> InvalidateAppAuthorization(int companyId, string reason = "ACCESS_TOKEN_REVOKED")
        {
            var env = SquareUtils.GetEnvironment(companyId);

            if (env == Environment.Sandbox)
                return 0;

            var authorization = await GetAuthorizationAsync(companyId);
            var accessToken = authorization?.AccessToken;
            
            if (authorization == null)
                return 0;

            // Invalidate authorization in db
            SquareCompanyAuthorization.Invalidate(companyId, reason);

            if (accessToken != null)
            {
                try
                {
                    // revoke token in square
                    var square = new SquareClient.Builder()
                        .Environment(env)
                        .Build();
                    var body = new RevokeTokenRequest.Builder()
                        .ClientId(SquareUtils.GetConfig(companyId, "ApplicationId"))
                        .AccessToken(accessToken)
                        .Build();

                    var res = await square.OAuthApi.RevokeTokenAsync(body, "Client " + SquareUtils.GetConfig(companyId, "ApplicationSecret"));

                    return res.Success != null && res.Success.Value ? 2 : -2;
                }
                catch (Exception e)
                {
                    if (e is ApiException ae)
                        throw new SquareApiException(companyId, ae);

                    throw;
                }
            }

            return 1;
        }

        public static async Task<int> InvalidateAppAuthorizationByMerchantId(string merchantId, string reason = "ACCESS_TOKEN_REVOKED")
        {
            var authorization = await SquareCompanyAuthorization.GetByMerchantIdAsync(merchantId);
            if (authorization != null)
                return await InvalidateAppAuthorization(authorization.CompanyId, reason);
            
            return 0;
        }
        
        public async static Task<bool> RefundPayment(int companyId, int dispatchEntryId, int invoicePaymentId, int? userId = null)
        {
            var ip = await InvoicePayment.GetByIdAsync(invoicePaymentId);
            if (ip == null)
                throw new InvoicePaymentNotFoundException(invoicePaymentId);
            
            var entry = Entry.GetByIdNoCache(dispatchEntryId);
            var authorization = await GetAuthorizationAsync(companyId);
            var paymentId = ip.ReferenceId;
            var square = GetSquare(authorization.CompanyId, authorization.AccessToken);

            var log = new LogEventInfo { Level = LogLevel.Info, Message = "SquareRefund" };
            log.Properties.Add("companyId", companyId);
            log.Properties.Add("callId", dispatchEntryId);
            log.Properties.Add("invoiceId", ip.InvoiceId);
            log.Properties.Add("invoicePaymentId", ip.Id);
            var jsonLog = new Dictionary<string, object> { { "paymentId", paymentId } };

            var tips = DispatchEntryPaymentTip.GetByPaymentId(ip.Id);
            var totalTips = tips?.Sum(t => t.Amount) ?? 0.0M;

            try
            {
                var idempotencyKeyName = $"square:refund:{paymentId}:{invoicePaymentId}";
                var idempotencyKey = await Core.GetRedisValueAsync(idempotencyKeyName);

                if (idempotencyKey == null)
                {
                    idempotencyKey = Guid.NewGuid().ToString();
                    await Core.SetRedisValueAsync(idempotencyKeyName, idempotencyKey);
                }

                var refundAmount = (long)((ip.Amount + totalTips) * 100); // Dollar -> Cents
                var currency = entry.Company.CurrencyCode;
                
                var paymentRes = await square.PaymentsApi.GetPaymentAsync(paymentId);
                
                if (paymentRes.Errors != null && paymentRes.Errors.Any())
                    throw new ApiException("Square API error", paymentRes.Context);
                
                var bodyAmountMoney = new Money.Builder()
                    .Amount(refundAmount)
                    .Currency(currency)
                    .Build();

                var referenceId = paymentRes.Payment.ReferenceId;
                var body = new RefundPaymentRequest.Builder(idempotencyKey, bodyAmountMoney)
                    .PaymentId(paymentId)
                    .Reason("Refund of " + paymentRes.Payment.Note)
                    .Build();
                
                var refundResult = await square.RefundsApi.RefundPaymentAsync(body);

                if (refundResult.Errors != null && refundResult.Errors.Any())
                    throw new ApiException("Square API error", refundResult.Context);

                var refund = refundResult.Refund;
                
                await InvoicePaymentState.RegisterStatusUpdate(invoicePaymentId, InvoicePaymentStatus.RefundPending, "local", userId);

                var squareRefund = new SquareRefund
                {
                    CompanyId = companyId,
                    PaymentId = paymentId,
                    RefundId = refund.Id,
                    MerchantId = authorization.MerchantId,
                    LocationId = refund.LocationId,
                    OrderId = refund.OrderId,
                    Status = refund.Status,
                    Amount = refundAmount,
                    Currency = refund.AmountMoney.Currency,
                    RefundCreatedAt = SafeDateTimeParse(refund.CreatedAt),
                    InvoiceId = ip.InvoiceId,
                    InvoicePaymentId = ip.Id
                };
                
                if (referenceId != null && referenceId.ToUpper().StartsWith("ST"))
                {
                    if (int.TryParse(referenceId.Substring(2), out var paymentLinkId))
                    {
                        squareRefund.PaymentLinkId = paymentLinkId;
                    }
                }
                
                squareRefund.Save();

                jsonLog.Add("AuthorizationId", authorization.Id);
                jsonLog.Add("MerchantId", authorization.MerchantId);
                jsonLog.Add("LocationId", refund.LocationId);
                jsonLog.Add("Amount", ip.Amount);
                jsonLog.Add("RefundId", refund.Id);
                jsonLog.Add("OrderId", refund.OrderId);
                jsonLog.Add("Status", refund.Status);
                jsonLog.Add("Currency", refund.AmountMoney.Currency);

                return true;
            }
            catch (Exception e)
            {
                log.Level = LogLevel.Error;

                if (e is ApiException ae)
                {
                    log.Exception = ae;
                    log.Message = "SquareRefund Exception: [ERROR CODE: " + ae.Errors[0].Code + "] " + ae.Errors[0].Detail;
                    log.Properties.Add("exception", ae);
                    log.Properties.Add("stackTrace", ae.StackTrace);
                    
                    throw new Exception(ae.Errors[0].Detail);
                }
                else
                {
                    log.Exception = e;
                    log.Message = "SquareRefund Exception: " + e.Message;
                    log.Properties.Add("exception", e);
                    log.Properties.Add("stackTrace", e.StackTrace);

                    throw new Exception(e.Message);
                }
            }
            finally
            {
                log.Properties["json"] = jsonLog;
                logger.Log(log);
            }
        }

        public static DateTime? SafeDateTimeParse(string dateTimeStr)
        {
            try
            {
                return DateTime.Parse(dateTimeStr);
            }
            catch  { }

            return null;
        }

        public static void LogCompanyError(int companyId, Exception e)
        {
            var log = new LogEventInfo
            {
                Level = LogLevel.Error,
                Message = e.Message,
                Exception = e
            };

            log.Properties["type"] = "SquareError";
            log.Properties["companyId"] = companyId;
            log.Properties["companyName"] = Company.Company.GetById(companyId)?.Name;

            if (e is ApiException sae)
            {
                log.Exception = sae;
                log.Properties.Add("errors", sae.Errors);
            }

            logger.Log(log);
        }

        public static void LogCompanyError(int companyId, string errorMessage)
        {
            var log = new LogEventInfo
            {
                Level = LogLevel.Error, 
                Message = errorMessage
            };

            log.Properties["type"] = "SquareError";
            log.Properties["companyId"] = companyId;
            log.Properties["companyName"] = Company.Company.GetById(companyId)?.Name;

            logger.Log(log);
        }

        #endregion

        public static SquareClient GetSquare(int companyId, string accessToken)
        {
            return GetSquare(accessToken, SquareUtils.GetEnvironment(companyId).ToString());
        }
        
        public static SquareClient GetSquare(string accessToken, string env)
        {
            var bearerAuthModel = new BearerAuthModel.Builder(accessToken);
            return new SquareClient.Builder()
                .Environment(env.ToLower() == "production" ? Environment.Production : Environment.Sandbox)
                .BearerAuthCredentials(bearerAuthModel.Build())
                .Build();
        }

        public static async Task<SquareCompanyAuthorization> GetAuthorizationAsync(int companyId)
        {          
            if (SquareUtils.GetEnvironment(companyId) == Environment.Sandbox)
            {
                var defaultAccessToken = Core.GetAppSetting("Square:Sandbox:AccessToken") ?? "****************************************************************";
                var defaultLocationId = Core.GetAppSetting("Square:Sandbox:LocationId") ?? "XPSRDD6DQ003P";
                    
                return new SquareCompanyAuthorization
                {
                    Id = 1,
                    Valid = true,
                    AccessToken = defaultAccessToken,
                    Location = new SquareCompanyLocation(-1, defaultLocationId),
                    MerchantId = "BG7A9MKQMP7BF",
                    CompanyId = companyId
                };
            }
            
            return await SquareCompanyAuthorization.GetByCompanyId(companyId);
        }

        public static async Task<SquareCompanyAuthorization> GetValidAuthorization(int companyId)
        {
            var authorization = await GetAuthorizationAsync(companyId);
            
            if (string.IsNullOrEmpty(authorization?.AccessToken))
                throw new Exception("Square authorization is required for your company. Login online to complete this setup under the Settings tab in Towbook.");
            
            if (string.IsNullOrEmpty(authorization?.Location?.LocationId))
                throw new Exception("A Square Location is required to complete the integration. Log in online to complete this setup under the Settings tab in Towbook.");

            return authorization;
        }

        public static string GetConfig(int companyId, string key)
        {
            var namespaceKey = "Square:";

            if (SquareUtils.GetEnvironment(companyId) == Environment.Sandbox)
                namespaceKey += "Sandbox:";

            var v = Core.GetAppSetting(namespaceKey + key);

            if (!string.IsNullOrWhiteSpace(v))
                return v;

            return null;
        }

        public static async Task<Location> CreateLocation(int companyId)
        {
            var company = await Company.Company.GetByIdAsync(companyId);
            var authorization = await GetAuthorizationAsync(companyId);
            var square = GetSquare(authorization.CompanyId, authorization.AccessToken);
            
            var bodyLocationAddress = new Address.Builder()
                .AddressLine1(company.Address)
                .Locality(company.City)
                .AdministrativeDistrictLevel1(company.State)
                .PostalCode(company.Zip)
                .Build();
            
            var bodyLocation = new Location.Builder()
                .BusinessName(company.Name)
                .Name(company.Name)
                .Address(bodyLocationAddress)
                .Build();
            
            var body = new CreateLocationRequest.Builder()
                .Location(bodyLocation)
                .Build();

            try
            {
                var locationResp = square.LocationsApi.CreateLocation(body);
                return locationResp.Location;
            }
            catch (ApiException ae)
            {
                if (ae.Errors.Count <= 0) throw;
                if (!ae.Errors[0].Detail.ToLower().Contains("must be unique")) throw;
                
                var locationsResp = square.LocationsApi.ListLocations();
                return locationsResp.Locations.FirstOrDefault(x => x.Name == company.Name);
            }
        }

        public static void SaveCompanyLocation(int companyId, int authorizationId, string locationId)
        {
            var companyLocation = SquareCompanyLocation.GetByAuthorizationId(authorizationId, companyId) ?? new SquareCompanyLocation()
            {
                CompanyId = companyId,
                AuthorizationId = authorizationId
            };

            if (companyLocation.LocationId != locationId)
            {
                companyLocation.LocationId = locationId;
                companyLocation.Save();
            }
        }

        public static async Task<Customer> CreateCustomer(int companyId, BillingContact billingContact)
        {
            var authorization = await GetAuthorizationAsync(companyId);
            var square = GetSquare(authorization.CompanyId, authorization.AccessToken);

            var bodyAddress = new Address.Builder()
                .AddressLine1(billingContact.AddressLine1)
                .AddressLine2(billingContact.AddressLine2)
                .Locality(billingContact.City)
                .AdministrativeDistrictLevel1(billingContact.Region)
                .AdministrativeDistrictLevel2(billingContact.CountryName)
                .PostalCode(billingContact.PostalCode)
                .Country(billingContact.Country)
                .Build();

            var body = new CreateCustomerRequest.Builder()
                .GivenName(billingContact.GivenName)
                .FamilyName(billingContact.FamilyName)
                .EmailAddress(billingContact.Email)
                .Address(bodyAddress)
                .PhoneNumber(billingContact.Phone)
                .ReferenceId("") //TODO
                .Note("") //TODO
                .Build();
            
            var result = await square.CustomersApi.CreateCustomerAsync(body);

            if (result.Errors != null)
                throw new ApiException("Square api returned an error", result.Context);
            
            return result.Customer;
        }

        public static async Task<Order> GetOrderById(int companyId, string orderId)
        {
            return await SquareApiSafeCallWrapper<Order>(companyId, () => _getOrderById(companyId, orderId));
        }

        private static async Task<Order> _getOrderById(int companyId, string orderId)
        {
            var authorization = await GetAuthorizationAsync(companyId);

            if (string.IsNullOrWhiteSpace(authorization.Location?.LocationId))
                throw new AuthorizationMissingException($"A Square location is required. A Square location is not configured for companyId = {companyId}.");

            var square = GetSquare(authorization.CompanyId, authorization.AccessToken);
            var bodyOrderIds = new List<string>() { orderId };
            
            var body = new BatchRetrieveOrdersRequest.Builder(bodyOrderIds)
                .LocationId(authorization.Location.LocationId)
                .Build();
            
            var result = await square.OrdersApi.BatchRetrieveOrdersAsync(body);

            if (result.Errors != null)
                throw new ApiException("Square Error", result.Context);
            
            return (result.Orders == null || result.Orders.Count == 0) ? null : result.Orders.First();
        }

        public static async Task<Order> CreateOrder(int companyId, decimal paymentAmount, List<PaymentLineItem> items, string locationId, decimal? tax = null, decimal? discountRate = null, string referenceId = null, string idempotencyKey = null,
            string currency = "USD", string customerId = null)
        {
            var authorization = await GetAuthorizationAsync(companyId);
            var square = GetSquare(authorization.CompanyId, authorization.AccessToken);

            return await CreateOrder(square, paymentAmount, items, locationId, tax, discountRate, referenceId, idempotencyKey, currency, customerId);
        }   

        public static async Task<Order> CreateOrder(SquareClient square, decimal paymentAmount, List<PaymentLineItem> items, string locationId, 
            decimal? tax = null, decimal? discountRate = null, string referenceId = null, 
            string idempotencyKey = null, string currency = "USD", string customerId = null, Dictionary<string, string> metaData = null)
        {
            var taxUid = Guid.NewGuid().ToString();
            var discountUid = Guid.NewGuid().ToString();
            var bodyOrderLineItems = new List<OrderLineItem>();
            var discounts = new List<OrderLineItemDiscount>();

            idempotencyKey = idempotencyKey ?? GetIdempotencyKey("order");

            if (items == null)
                items = new List<PaymentLineItem>();

            if (metaData == null)
                metaData = new Dictionary<string, string>();

            metaData["OrderType"] = "Standard";

            var paymentLineItems = items.ToList();

            // Check for a discount rate, one free qty line item, and FSC.
            // If so, simplify order to one line item to stop "order total does not match
            // payment amount" errors and stop attempts to perform "Rounding Adjustments".
            // The result will build an order with one line item that matches the payment amount 
            // exactly with no discount, no tax, qty=1 and name of "Service amount"
            if (discountRate > 0 &&
                items.Any(a => a.Name.StartsWith("Fuel Surcharge")) &&
                items.Any(a => a.DiscountQuantity > 0 && a.HasFreeQuantity))
            {
                var lineItemName = "Service Amount";

                if (metaData.ContainsKey("callNumber"))
                    lineItemName += " - Call #" + metaData["callNumber"];

                paymentLineItems.Clear();
                paymentLineItems.Add(new PaymentLineItem()
                {
                    HasFreeQuantity = false,
                    DiscountQuantity = 0.0M,
                    Name = lineItemName,
                    Price = paymentAmount,
                    Taxable = false,
                    Quantity = 1
                });

                discountRate = null;
                tax = null;

                metaData["OrderType"] = "Simple/OneLineItem";
            }

            foreach (var item in paymentLineItems)
            {
                var itemBasePrice = new Money.Builder()
                    .Amount((long)(item.Price * 100))
                    .Currency(currency)
                    .Build();

                MeasurementUnit.Builder measurementUnit;

                if (!string.IsNullOrEmpty(item.MeasurementUnit))
                {
                    measurementUnit = new MeasurementUnit.Builder()
                        .LengthUnit(item.MeasurementUnit);
                }
                else
                    measurementUnit = new MeasurementUnit.Builder()
                        .CustomUnit(new MeasurementUnitCustom.Builder("", "").Build());

                var qtyUnit = new OrderQuantityUnit.Builder()
                    .MeasurementUnit(measurementUnit.Build())
                    .Precision(2)
                    .Build();

                var appliedTaxes = new List<OrderLineItemAppliedTax>();

                if (item.Taxable && tax != null)
                    appliedTaxes.Add(new OrderLineItemAppliedTax.Builder(taxUid).Build());

                var appliedDiscounts = new List<OrderLineItemAppliedDiscount>();
                if (item.HasFreeQuantity)
                {
                    var amt = (long)(item.Price * 100 * item.DiscountQuantity.GetValueOrDefault());

                    var itemDiscountPrice = new Money.Builder()
                        .Amount(amt)
                        .Currency(currency)
                        .Build();

                    var itemDiscountId = Guid.NewGuid().ToString();

                    var d = new OrderLineItemDiscount.Builder()
                        .Uid(itemDiscountId)
                        .Name($"Free Qty{(string.IsNullOrEmpty(item.Name) ? "" : $" ({item.Name})")}")
                        .Type("FIXED_AMOUNT")
                        .AmountMoney(itemDiscountPrice)
                        .Scope("LINE_ITEM");

                    appliedDiscounts.Add(new OrderLineItemAppliedDiscount.Builder(itemDiscountId).Build());
                    discounts.Add(d.Build());
                }

                var orderLineItem = new OrderLineItem.Builder(item.Quantity.ToString(CultureInfo.InvariantCulture))
                    .Uid(Guid.NewGuid().ToString())
                    .Name(item.Name)
                    .Quantity(item.Quantity.ToString(CultureInfo.InvariantCulture))
                    .QuantityUnit(qtyUnit)
                    .Note(item.Note)
                    .BasePriceMoney(itemBasePrice);

                if (appliedDiscounts.Count > 0)
                    orderLineItem.AppliedDiscounts(appliedDiscounts);

                if (appliedTaxes.Count > 0)
                    orderLineItem.AppliedTaxes(appliedTaxes);

                bodyOrderLineItems.Add(orderLineItem.Build());
            }

            metaData["LineItemCount"] = bodyOrderLineItems.Count().ToString();

            var bodyOrder = new Order.Builder(locationId)
                .LineItems(bodyOrderLineItems);

            if (customerId != null)
                bodyOrder.CustomerId(customerId);
            if (referenceId != null)
                bodyOrder.ReferenceId(referenceId.Truncate(40));
            if(discounts.Any())
                bodyOrder.Discounts(discounts);

            if (tax != null && items.Any(a => a.Taxable))
            {
                var taxes = new List<OrderLineItemTax>();
                var t = new OrderLineItemTax.Builder()
                    .Uid(taxUid)
                    .Name("Taxes")
                    .Type("ADDITIVE")
                    .Percentage(tax.ToString())
                    .Scope("LINE_ITEM");
                
                taxes.Add(t.Build());
                bodyOrder.Taxes(taxes);
            }

           
            if (discountRate != null && discountRate.GetValueOrDefault() > 0)
            {
                var d = new OrderLineItemDiscount.Builder()
                    .Uid(discountUid)
                    .Name("Discount")
                    .Type("FIXED_PERCENTAGE")
                    .Percentage(Math.Abs(discountRate.Value).ToString().Truncate(10))
                    .Scope("ORDER");

                discounts.Add(d.Build());
                bodyOrder.Discounts(discounts);
            }

            bodyOrder.Metadata(metaData);

            bodyOrder.LocationId(locationId);
            var body = new CreateOrderRequest.Builder()
                .Order(bodyOrder.Build())
                .IdempotencyKey(idempotencyKey)
                .Build();

            var amount = (long)(paymentAmount * 100);
            var bc = new CalculateOrderRequest.Builder(bodyOrder.Build());
            var calculated = await square.OrdersApi.CalculateOrderAsync(bc.Build());
            
            // Amount Adjustment
            if (calculated.Order.TotalMoney.Amount != null && amount != calculated.Order.TotalMoney.Amount)
            {
                var diff = amount - (long)calculated.Order.TotalMoney.Amount;

                if (diff > 0)
                {
                    metaData["OrderType"] = "Rounded";

                    var qu = new OrderQuantityUnit.Builder()
                        .MeasurementUnit(new MeasurementUnit.Builder()
                            .CustomUnit(new MeasurementUnitCustom.Builder("", "").Build()).Build())
                        .Precision(2)
                        .Build();
                    
                    var orderLineItem = new OrderLineItem.Builder("1")
                        .Uid(Guid.NewGuid().ToString())
                        .Name("Rounding adjustment")
                        .QuantityUnit(qu)
                        .Note("Rounding adjustment")
                        .BasePriceMoney(new Money.Builder()
                            .Amount(diff)
                            .Currency(currency)
                            .Build());

                    bodyOrderLineItems.Add(orderLineItem.Build());

                    bodyOrder.LineItems(bodyOrderLineItems);
                    bodyOrder.LocationId(locationId);

                    body = new CreateOrderRequest.Builder()
                        .Order(bodyOrder.Build())
                        .IdempotencyKey(idempotencyKey)
                        .Build();
                }
                else
                {
                    var d = new OrderLineItemDiscount.Builder()
                        .Uid(Guid.NewGuid().ToString())
                        .Name("Rounding adjustment")
                        .Type("FIXED_AMOUNT")
                        .AmountMoney(new Money.Builder()
                            .Amount(Math.Abs(diff))
                            .Currency(currency)
                            .Build())
                        .Scope("ORDER");

                    discounts.Add(d.Build());
                    bodyOrder.Discounts(discounts);
                    bodyOrder.LocationId(locationId);

                    body = new CreateOrderRequest.Builder()
                        .Order(bodyOrder.Build())
                        .IdempotencyKey(idempotencyKey)
                        .Build();
                }
            }
            
            var response = await square.OrdersApi.CreateOrderAsync(body);
            
            if (response.Errors != null)
                throw new ApiException("Square api returned an error", response.Context);

            return response.Order;
        }

        public static async Task<Customer> GetOrCreateCustomer(int companyId, BillingContact billingContact)
        {
            var authorization = await GetAuthorizationAsync(companyId);
            var square = GetSquare(authorization.CompanyId, authorization.AccessToken);

            var bodyQueryFilterEmailAddress = new CustomerTextFilter.Builder()
                .Exact(billingContact.Email)
                .Build();
            
            var bodyQueryFilter = new CustomerFilter.Builder()
                .EmailAddress(bodyQueryFilterEmailAddress)
                .Build();
            
            var bodyQuery = new CustomerQuery.Builder()
                .Filter(bodyQueryFilter)
                .Build();
            
            var body = new SearchCustomersRequest.Builder()
                .Limit(1)
                .Query(bodyQuery)
                .Build();

            var searchResult = await square.CustomersApi.SearchCustomersAsync(body);

            if (searchResult.Errors != null)
                throw new CustomerApiException(companyId, "Square Customer api returned an error", searchResult.Errors.ToList());
            
            return searchResult?.Customers?.FirstOrDefault() ?? await CreateCustomer(companyId, billingContact);
        }
        
        public static string CreateHMAC(string json, string secret)
        {
            var encoding = new ASCIIEncoding();
            //var bufferSecret = Convert.FromBase64String(secret);
            var bufferJson = encoding.GetBytes(json);
            var hmac = new HMACSHA1(encoding.GetBytes(secret));
            var hashBytes = hmac.ComputeHash(bufferJson);
            var hash = Convert.ToBase64String(hashBytes);

            return hash;
        }

        public static int? GetCompanyIdFromLocation(string locationId)
        {
            if (!string.IsNullOrEmpty(locationId))
            {
                return SquareCompanyLocation.GetByLocationId(locationId)?.CompanyId;
            }

            return null;
        }

        public static InvoicePaymentStatus MapPaymentStatusToInvoicePaymentStatus(string squarePaymentStatus)
        {
            if (squarePaymentStatus == SquarePaymentStatus.Approved)
                return InvoicePaymentStatus.Processing;
            if (squarePaymentStatus == SquarePaymentStatus.Completed)
                return InvoicePaymentStatus.Paid;
            if (squarePaymentStatus == SquarePaymentStatus.Canceled)
                return InvoicePaymentStatus.Cancelled;
            if (squarePaymentStatus == SquarePaymentStatus.Failed)
                return InvoicePaymentStatus.Failed;

            return InvoicePaymentStatus.Created;
        }
        
        public static InvoicePaymentStatus MapRefundStatusToInvoicePaymentStatus(string squareRefundStatus)
        {
            if (squareRefundStatus == SquareRefundStatus.Approved)
                return InvoicePaymentStatus.RefundPending;
            if (squareRefundStatus == SquareRefundStatus.Completed)
                return InvoicePaymentStatus.Refunded;
            if (squareRefundStatus == SquareRefundStatus.Rejected)
                return InvoicePaymentStatus.RefundRejected;
            if (squareRefundStatus == SquareRefundStatus.Failed)
                return InvoicePaymentStatus.RefundFailed;

            return InvoicePaymentStatus.RefundPending;
        }
        
        public static SquarePayment BuildSquarePaymentFrom(InvoicePayment ip, Payment payment, SquareCompanyAuthorization authorization)
        {
            DateTime createDate = DateTime.Now;
            DateTime.TryParse(payment.CreatedAt, out createDate);

            DateTime updateDate = DateTime.Now;
            DateTime.TryParse(payment.UpdatedAt, out updateDate);

            return new SquarePayment
            {
                MerchantId = authorization.MerchantId,
                LocationId = authorization.Location.LocationId,
                PaymentId = payment.Id,
                Status = payment.Status,
                Amount = payment.AmountMoney.Amount,
                TotalMoney = payment.TotalMoney?.Amount,
                Tip = payment.TipMoney?.Amount,
                Currency = payment.AmountMoney.Currency,
                OrderId = payment.OrderId,
                SquareReferenceId = payment.ReferenceId,
                Note = payment.Note.Truncate(256),
                PaymentCreatedAt = createDate,
                InvoiceId = ip.InvoiceId,
                InvoicePaymentId = ip.Id,
                PaymentUpdatedAt = updateDate,
                PaymentCardType = payment.CardDetails?.Card?.CardType,
                PaymentCardBrand = payment.CardDetails?.Card?.CardBrand
            };
        }

        public static async Task<Payment> GetPaymentById(int companyId, string paymentId)
        {
            return await SquareApiSafeCallWrapper(companyId, () => _getPaymentById(companyId, paymentId));
        }

        private static async Task<Payment> _getPaymentById(int companyId, string paymentId)
        {
            var authorization = await GetAuthorizationAsync(companyId);
            if (authorization == null)
                throw new SquareApiException("Square not authorized for company " + companyId);
            
            var square = GetSquare(authorization.CompanyId, authorization.AccessToken);
            try
            {
                var paymentRes = await square.PaymentsApi.GetPaymentAsync(paymentId);

                if (paymentRes.Errors != null)
                {
                    if (paymentRes.Errors.Any(x => x.Code == "NOT_FOUND"))
                        return null;
                    
                    throw new ApiException("Square API error", paymentRes.Context);
                }

                return paymentRes.Payment;
            }
            catch (ApiException ae)
            {
                if (ae.Errors.Any(x => x.Code == "NOT_FOUND"))
                    return null;

                throw;
            }
        }
        
        public static async Task<PaymentRefund> GetRefundById(int companyId, string refundId)
        {
            return await SquareApiSafeCallWrapper(companyId, () => _getRefundById(companyId, refundId));
        }

        private static async Task<PaymentRefund> _getRefundById(int companyId, string refundId)
        {
            var authorization = await GetAuthorizationAsync(companyId);
            if (authorization == null)
                throw new SquareApiException("Square not authorized for company " + companyId);
            
            var square = GetSquare(authorization.CompanyId, authorization.AccessToken);
            try
            {
                var res = await square.RefundsApi.GetPaymentRefundAsync(refundId);

                if (res.Errors != null)
                {
                    if (res.Errors.Any(x => x.Code == "NOT_FOUND"))
                        return null;
                    
                    throw new ApiException("Square API error", res.Context);
                }

                return res.Refund;
            }
            catch (ApiException ae)
            {
                if (ae.Errors.Any(x => x.Code == "NOT_FOUND"))
                    return null;

                throw;
            }
        }

        public static async Task<string> ResolveSquarePaymentIdFromReferenceNumber(int companyId, string referenceNumber)
        {
            var log = new LogEventInfo
            {
                Level = LogLevel.Info,
                TimeStamp = DateTime.Now
            };
            log.Properties["companyId"] = companyId;
            log.Properties["referenceNumber"] = referenceNumber;
            log.Properties["type"] = "SquareInfo";
            log.Properties["event_type"] = "ResolveSquarePaymentId";
            
            try
            {
                var order = await GetOrderById(companyId, referenceNumber);
                if (order != null)
                {
                    log.Properties["json"] = order.ToJson();
                    
                    if (order.Tenders?.FirstOrDefault() != null)
                    {
                        log.Properties["message"] = "Found payment ref in Square.Orders api, resolving paymentId using Order.Tenders[0].id -> " + order.Tenders.First().Id;

                        return order.Tenders.First().Id;
                    }
                }
                else
                {
                    log.Properties["message"] = $"PaymentId ({referenceNumber}) Not Found in Square.Orders api.";
                }
            }
            catch (Exception e)
            {
                if (e is ApiException apiException)
                {
                    log.Exception = apiException;
                    log.Properties["errors"] = apiException.Errors;
                }
                else
                {
                    log.Level = LogLevel.Error;
                    log.Exception = e;
                }
            }
            finally
            {
                logger.Log(log);
            }
            
            return null;
        }
        
        public static async Task<SquarePayment> RegisterSquarePayment(InvoicePayment ip, int companyId, string currency = "USD", long? totalAmountInCents = null, long? tipAmountInCents = null)
        {
            await InvoicePaymentState.RegisterStatusUpdate(ip.Id, InvoicePaymentStatus.Processing, "local");

            var authorization = await GetAuthorizationAsync(companyId);

            // AC 9/7/2023 - null reference error from missing locationId was causing mass duplicate 
            // InvoicePayments to be created per webhook.  Throw an exception to avoid duplicate payments.
            // The exception will cause a 200 response so that the webhook is satisfied.
            if (string.IsNullOrEmpty(authorization?.Location?.LocationId))
            {
                var company = Company.Company.GetById(companyId);
                throw new AuthorizationMissingLocationIdException($"Could not find a Square location configured for use with {company?.ShortName ?? company?.Name ?? "company " + companyId.ToString()}.  Please review your Square settings in Towbook and set up a location.");
            }

            var squarePayment = new SquarePayment
            {
                CompanyId = companyId,
                Currency = currency,
                Status = SquarePaymentStatus.Approved,
                MerchantId = authorization.MerchantId,
                LocationId = authorization.Location.LocationId,
                CustomerId = "",
                PaymentId = ip.ReferenceId,
                Amount = (long)(ip.Amount * 100), // dollars -> cents (Square amount is in cents)
                CreateDate = ip.CreateDate,
                InvoiceId = ip.InvoiceId,
                InvoicePaymentId = ip.Id,
                PaymentCreatedAt = ip.CreateDate,
                TotalMoney = totalAmountInCents, // includes tip
                Tip = tipAmountInCents
            };
            squarePayment.CompanyId = companyId;
            squarePayment.Currency = currency;
            squarePayment.Save();

            return squarePayment;
        }


        public static async Task ApplySquarePaymentTips(InvoicePayment invoicePayment, Entry entry, long? tipInCents)
        {
            if (tipInCents <= 0)
                return;

            if (entry == null || invoicePayment == null)
                return;

            var totalTipAmountInDollars = tipInCents.Value / 100m; // cents -> dollars


            // Divide the tip amount among the drivers as equally as possible.
            Dictionary<int, decimal> DivideAmount(decimal totalAmount, int[] driverIds)
            {
                if (driverIds == null)
                    return null;

                if (driverIds.Length == 1)
                    return new Dictionary<int, decimal>() { { driverIds.First(), totalAmount } };

                int numOfDrivers = driverIds.Length == 0 ? 1 : driverIds.Length;

                // Best base split amount per driver
                decimal baseAmount = Math.Floor(totalAmount / numOfDrivers * 100) / 100;

                // Calculate the remainder to be assigned to the first driver
                decimal remainder = totalAmount - (baseAmount * numOfDrivers);

                var distributedAmounts = new Dictionary<int, decimal>();

                for (int i = 0; i < driverIds.Count(); i++)
                {
                    if (i == 0)
                    {
                        // Add the extra cent to the first driver, if any remainder
                        distributedAmounts.Add(driverIds.First(), baseAmount + remainder);
                    }
                    else
                    {
                        if (distributedAmounts.ContainsKey(driverIds[i]))
                        {
                            // Add the base amount to the driver
                            distributedAmounts[driverIds[i]] += baseAmount;
                        }
                        else
                        {
                            // Add the base amount for the remaining drivers
                            distributedAmounts.Add(driverIds[i], baseAmount);
                        }
                    }
                }

                return distributedAmounts;
            }

            var tipAmountsPerDriver = DivideAmount(totalTipAmountInDollars, entry.Drivers.ToArray());
            var token = new AuthenticationToken() { UserId = SquareUtils.SquareUserId };

            if (tipAmountsPerDriver?.Count() > 0)
            {
                var entryTips = DispatchEntryPaymentTip.GetByPaymentId(invoicePayment.Id)?.Where(w => !w.IsVoided);

                foreach (var driverTip in tipAmountsPerDriver)
                {
                    var tipEntry = entryTips?.FirstOrDefault(f => f.DispatchEntryPaymentId == invoicePayment.Id && f.DispatchEntryId == entry.Id && f.DriverId == driverTip.Key) ??
                        new DispatchEntryPaymentTip()
                        {
                            DispatchEntryId = entry.Id,
                            DispatchEntryPaymentId = invoicePayment.Id,
                            DriverId = driverTip.Key
                        };

                    tipEntry.Amount = driverTip.Value;

                    await tipEntry.SaveAsync(token);
                }
            }
            else
            {
                // Driverless tip.  We are not expecting this to happen since we are 
                // turning off tipping for driverless calls.
                var tipEntry = DispatchEntryPaymentTip.GetByPaymentId(invoicePayment.Id)?.FirstOrDefault(f => f.DispatchEntryPaymentId == invoicePayment.Id && f.DispatchEntryId == entry.Id && f.DriverId.GetValueOrDefault() == 0) ??
                        new DispatchEntryPaymentTip()
                        {
                            DispatchEntryId = entry.Id,
                            DispatchEntryPaymentId = invoicePayment.Id,
                            DriverId = (int?)null
                        };

                tipEntry.Amount = totalTipAmountInDollars;

                await tipEntry.SaveAsync(token);
            }
        }

        public static string GetIdempotencyKey(string key)
        {
            var idempotencyKey = Core.GetRedisValue($"square:new_payment_idempotencyKey:{key}");
            
            if (idempotencyKey == null)
            {
                idempotencyKey = Guid.NewGuid().ToString();
                Core.SetRedisValue($"square:new_payment_idempotencyKey:{key}", idempotencyKey);
            }

            return idempotencyKey;
        }
        
        public static string GetIdempotencyKey(string name, string key)
        {
            var idempotencyKey = Core.GetRedisValue($"square:towbook_ik:{name}-{key}");
            
            if (idempotencyKey == null)
            {
                idempotencyKey = Guid.NewGuid().ToString();
                Core.SetRedisValue($"square:towbook_ik:{name}-{key}", idempotencyKey, TimeSpan.FromMinutes(1));
            }

            return idempotencyKey;
        }

        public static int? GetRedisValueAsInt(string key)
        {
            var val = Core.GetRedisValue(key);

            if (val != null && Int32.TryParse(val, out var res))
                return res;

            return null;
        }

        public static IEnumerable<SquareCompanyAuthorization> GetCompanyAuthorizations(int companyId, int limit)
        {
            return SquareCompanyAuthorization.GetCompanyAuthorizations(companyId, limit)
                .Select(o =>
                {
                    o.Location = SquareCompanyLocation.GetByAuthorizationId(o.Id, companyId);

                    return o;
                });
        }

        public class AuthorizationResponse
        {
            public bool Successful { get; set; }
            public IEnumerable<SquareLocation> Locations { get; set; }

            public AuthorizationResponse(bool successful, IEnumerable<SquareLocation> locations)
            {
                Successful = successful;
                Locations = locations;
            }
        }

        public static async Task<AuthorizationResponse> ValidateAuthorization(int companyId)
        {
            try
            {
                var locations = await GetLocationsRaw(companyId);

                if (locations.Any())
                    return new AuthorizationResponse(true, locations);
                else
                    return new AuthorizationResponse(true, new List<SquareLocation>());
            }
            catch (Exception e)
            {
                if (!(e is SquareApiException sae)) 
                    return new AuthorizationResponse(false, null);

                if (!sae.Errors.Select(o => o.ErrorCode).ToList().Contains("ACCESS_TOKEN_REVOKED"))
                    return new AuthorizationResponse(false, null);
                
                try
                {
                    var r = await InvalidateAppAuthorization(companyId, "REVOKED_BY_UNKNOWN");
                }
                catch
                {
                    // ignored
                }

                return new AuthorizationResponse(false, null);
            }
        }

        public static async Task<object> UpdateInvoicePaymentIfRequired(int companyId, int invoicePaymentId, string paymentId)
        {
            try
            {
                var authorization = await GetAuthorizationAsync(companyId);
                if (authorization != null)
                {
                    var invoicePayment = await InvoicePayment.GetByIdAsync(invoicePaymentId);
                    var payment = await GetPaymentById(companyId, paymentId);
                    var hasUpdated = false;

                    if (payment != null)
                    {
                        var squarePayment = SquarePayment.GetByPaymentId(payment.Id, companyId);
                        var currentInvoicePaymentState = InvoicePaymentState.GetCurrentStatusByInvoicePaymentId(invoicePaymentId);
                        var paymentStatus = MapPaymentStatusToInvoicePaymentStatus(payment.Status);

                        if (squarePayment == null)
                        {
                            squarePayment = BuildSquarePaymentFrom(invoicePayment, payment, authorization);
                            squarePayment.CompanyId = companyId;
                            squarePayment.CustomerId = payment.CustomerId;
                            squarePayment.Save();
                        }

                        if (currentInvoicePaymentState == null || paymentStatus.IntValue() > currentInvoicePaymentState.StatusId)
                        {
                            UpdateSquarePayment(squarePayment, payment, authorization.MerchantId);
                            await InvoicePaymentState.RegisterStatusUpdate(squarePayment.InvoicePaymentId, paymentStatus, $"manual");
                            hasUpdated = true;
                        }
                    }

                    //
                    // If not updated previously then check if there is a refund update
                    //
                    if (!hasUpdated && payment != null)
                    {
                        var squareRefund = SquareRefund.GetByPaymentId(paymentId);
                        if (squareRefund != null)
                        {
                            var refund =  await GetRefundById(companyId, squareRefund.RefundId);
                            var currentInvoicePaymentState = InvoicePaymentState.GetCurrentStatusByInvoicePaymentId(invoicePaymentId);
                            var refundStatus = MapRefundStatusToInvoicePaymentStatus(refund.Status);

                            if (currentInvoicePaymentState != null && refundStatus.IntValue() > currentInvoicePaymentState.StatusId)
                            {
                                UpdateSquareRefund(squareRefund, refund);
                                await InvoicePaymentState.RegisterStatusUpdate(invoicePaymentId, refundStatus, "manual");
                            }
                        }
                    }
                }
            }
            catch (Exception exception)
            {
                var e = exception.Message + "";
            }

            return null;
        }
        
        public static void UpdateSquarePayment(SquarePayment squarePayment, Payment payment, string merchantId)
        {
            #region Update SquarePayment info
            squarePayment.MerchantId = merchantId;
            squarePayment.OrderId = payment.OrderId;
            squarePayment.Currency = payment.AmountMoney.Currency;
            squarePayment.LocationId = payment.LocationId;
            squarePayment.PaymentStatus = payment.CardDetails?.Status;
            squarePayment.SquareReferenceId = payment.ReferenceId;
            squarePayment.Status = payment.Status;
        
            squarePayment.PaymentUpdatedAt = SafeDateTimeParse(payment.UpdatedAt);
            squarePayment.PaymentCardBrand = payment.CardDetails?.Card?.CardBrand;
            squarePayment.PaymentCardType = payment.CardDetails?.Card?.CardType;
            squarePayment.PaymentCardLast4 = payment.CardDetails?.Card?.Last4;
            squarePayment.PaymentBin = payment.CardDetails?.Card?.Bin;
            squarePayment.PaymentAvsStatus = payment.CardDetails?.AvsStatus;
            squarePayment.PaymentCvvStatus = payment.CardDetails?.CvvStatus;
            squarePayment.PaymentEntryMethod = payment.CardDetails?.EntryMethod;
            squarePayment.PaymentStatementDescription = payment.CardDetails?.StatementDescription;
            squarePayment.ReceiptNumber = payment.ReceiptNumber;
            squarePayment.ReceiptUrl = payment.ReceiptUrl;
            squarePayment.UpdateDate = SafeDateTimeParse(payment.UpdatedAt);
            #endregion
        

            #region Tip & Total Money updates
            if (payment.TipMoney?.Amount.GetValueOrDefault() > 0 && squarePayment.Tip.GetValueOrDefault() != payment.TipMoney?.Amount.GetValueOrDefault())
                squarePayment.Tip = payment.TipMoney.Amount;

            if (payment.TotalMoney?.Amount.GetValueOrDefault() > 0 && squarePayment.TotalMoney.GetValueOrDefault() != payment.TotalMoney?.Amount.GetValueOrDefault())
                squarePayment.TotalMoney = payment.TotalMoney.Amount;
            #endregion

            squarePayment.Save();
        }
        
        public static void UpdateSquareRefund(SquareRefund squareRefund, PaymentRefund refund)
        {
            squareRefund.Status = refund.Status;
            squareRefund.RefundUpdatedAt = SafeDateTimeParse(refund.UpdatedAt);
            
            if (refund.ProcessingFee != null)
            {
                squareRefund.ProcessingFeeEffectiveAt = SafeDateTimeParse(refund.ProcessingFee[0].EffectiveAt);
                squareRefund.ProcessingFeeType = refund.ProcessingFee[0].Type;
                squareRefund.ProcessingFeeCurrency = refund.ProcessingFee[0].AmountMoney.Currency;
                    
                var amountMoney = refund.ProcessingFee[0].AmountMoney.Amount;
                if (amountMoney != null)
                    squareRefund.ProcessingFeeAmount = (double) amountMoney;
            }
            
            squareRefund.Save();
        }
        
        public static DateTime GetFirstNotificationTimestamp(string referenceId)
        {
            var timestamp = DateTime.Now;
            
            if (Core.GetRedisValue($"square:ref-store:{referenceId}") == null || !DateTime.TryParse(Core.GetRedisValue($"square:ref-store:{referenceId}"), out timestamp))
                Core.SetRedisValue($"square:ref-store:{referenceId}", timestamp.ToString("O"), TimeSpan.FromHours(1));

            return timestamp;
        }

        public static string GetInnerMessage(Exception exception)
        {
            var maxInnerExceptionChain = 10;
            var message = exception.Message;
            
            for (var c=0; c < maxInnerExceptionChain && exception.InnerException != null; c++)
            {
                message += System.Environment.NewLine + exception.InnerException.Message;
                exception = exception.InnerException;
            }

            return message;
        }

        public static SquareSettingModel FillKeyOptionValues(SquareSettingModel model, Company.Company company, Accounts.Account account = null)
        {
            model = model ?? new SquareSettingModel();
            
            if (company == null)
                return model;

            var accountKeyValues = new Collection<AccountKeyValue>();
            if (account != null)
            {
                var accountKeyNames = new Collection<string>(){ "Square_AlwaysIncludePaymentLinkOnInvoices",
                        "Square_AlwaysIncludePaymentLinkOnStatements",
                        "Square_OptOutOfEmailsOnTransactions",
                        "Square_ExcludeLinkOnPrintedDispatchInvoices",
                        "Square_ExcludeLinkOnPrintedImpoundInvoices",
                        "Square_ExcludeLinkOnPrintedStatements"
                    };

                if (company.HasAccessToBaseFeature("tipping"))
                {
                    accountKeyNames = accountKeyNames.Union(new Collection<string>(){
                        "SquareTipping_ExcludeOnPaymentLinks",
                        "SquareTipping_ExcludeOnSquareReader",
                        "SquareTipping_ExcludeOnSquareTerminal"
                    }).ToCollection();
                }

                accountKeyValues = accountKeyValues.Union(
                        AccountKeyValue.GetByAccount(company.Id, account.Id, Provider.Towbook.ProviderId, accountKeyNames.ToArray())
                    ).ToCollection();
            }

            var companyKeyValues = new Collection<CompanyKeyValue>();

            var companyKeyNames = new Collection<string>(){ "Square_AlwaysIncludePaymentLinkOnInvoices",
                        "Square_AlwaysIncludePaymentLinkOnStatements",
                        "Square_OptOutOfEmailsOnTransactions",
                        "Square_ExcludeLinkOnPrintedDispatchInvoices",
                        "Square_ExcludeLinkOnPrintedImpoundInvoices",
                        "Square_ExcludeLinkOnPrintedStatements"
                    };

                if (company.HasAccessToBaseFeature("tipping"))
                {
                    companyKeyNames = companyKeyNames.Union(new Collection<string>(){
                        "Square_EnableTipping",
                        "SquareTipping_DefaultPercentageAmounts",
                        "SquareTipping_ExcludeOnPaymentLinks",
                        "SquareTipping_ExcludeOnSquareReader",
                        "SquareTipping_ExcludeOnSquareTerminal"
                    }).ToCollection();
                }
                
            companyKeyValues = companyKeyValues.Union(
                    CompanyKeyValue.GetByCompany(company.Id, Provider.Towbook.ProviderId, companyKeyNames.ToArray())
                ).ToCollection();


            int[] percentages = null;

            try
            {
                var value = companyKeyValues.ValueOrEmpty("SquareTipping_DefaultPercentageAmounts");

                percentages = Newtonsoft.Json.JsonConvert.DeserializeObject<int[]>(value);
            }
            catch { }

            model.Options = new SquareOptions()
            {
                IncludePaymentLinkOnInvoices = (accountKeyValues.ValueOrNull("Square_AlwaysIncludePaymentLinkOnInvoices") ??
                                                    companyKeyValues.ValueOrNull("Square_AlwaysIncludePaymentLinkOnInvoices")) == "1",
                IncludePaymentLinkOnStatements = (accountKeyValues.ValueOrNull("Square_AlwaysIncludePaymentLinkOnStatements") ??
                                                        companyKeyValues.ValueOrEmpty("Square_AlwaysIncludePaymentLinkOnStatements")) == "1",
                OptOutOfConfirmationEmailOnTransactions = (accountKeyValues.ValueOrNull("Square_OptOutOfEmailsOnTransactions") ??
                                                                companyKeyValues.ValueOrEmpty("Square_OptOutOfEmailsOnTransactions")) == "1",
                ExcludePaymentLinkOnPrintedDispatchInvoices = (accountKeyValues.ValueOrNull("Square_ExcludeLinkOnPrintedDispatchInvoices") ??
                                                                    companyKeyValues.ValueOrEmpty("Square_ExcludeLinkOnPrintedDispatchInvoices")) == "1",
                ExcludePaymentLinkOnPrintedImpoundInvoices = (accountKeyValues.ValueOrNull("Square_ExcludeLinkOnPrintedImpoundInvoices") ??
                                                                    companyKeyValues.ValueOrEmpty("Square_ExcludeLinkOnPrintedImpoundInvoices")) == "1",
                ExcludePaymentLinkOnPrintedStatements = (accountKeyValues.ValueOrNull("Square_ExcludeLinkOnPrintedStatements") ??
                                                                companyKeyValues.ValueOrEmpty("Square_ExcludeLinkOnPrintedStatements")) == "1"
            };


            if (company.HasAccessToBaseFeature("tipping"))
            {
                model.IsTippingEnabled = companyKeyValues.ValueOrEmpty("Square_EnableTipping") == "1";

                model.TippingOptions = new SquareTippingOptionModel()
                {
                    ExcludeOnPaymentLinks = (accountKeyValues.ValueOrNull("SquareTipping_ExcludeOnPaymentLinks") ??
                                                    companyKeyValues.ValueOrEmpty("SquareTipping_ExcludeOnPaymentLinks")) == "1",
                    ExcludeOnSquareReader = (accountKeyValues.ValueOrNull("SquareTipping_ExcludeOnSquareReader") ??
                                                    companyKeyValues.ValueOrEmpty("SquareTipping_ExcludeOnSquareReader")) == "1",
                    ExcludeOnSquareTerminal = (accountKeyValues.ValueOrNull("SquareTipping_ExcludeOnSquareTerminal") ??
                                                    companyKeyValues.ValueOrEmpty("SquareTipping_ExcludeOnSquareTerminal")) == "1",
                    TipPercentages = percentages ?? DefaultTipPercentages.ToArray()
                };
            } 
            else
            {
                model.IsTippingEnabled = false;
                model.TippingOptions = null;
            }

            return model;
        }
    }
}
