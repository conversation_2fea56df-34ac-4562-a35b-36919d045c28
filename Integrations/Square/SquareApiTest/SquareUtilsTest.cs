using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Square.Models;
using Extric.Towbook.Dispatch;
using Extric.Towbook;
using System.Linq;
using Xunit;
using Extric.Towbook.API.Integration.Square.Model;
using Extric.Towbook.API.Integration.Square;
using static SquareApiTest.FactAttributeExtensions;

namespace SquareApiTest
{
    public class SquareUtilsTest
    {
        public SquareUtilsTest()
        {
            Configuration.Init();
        }

        [Fact]
        public async Task OrderBasicTest()
        {
            var paymentDef = new PaymentDefinition()
            {
                Amount = 10,
                Nonce = "cnon:card-nonce-ok"
            };

            paymentDef.LineItems.Add(new PaymentLineItem
            {
                Name = "Item 1",
                Quantity = 1,
                Price = 5
            });
            paymentDef.LineItems.Add(new PaymentLineItem
            {
                Name = "Item 2",
                Quantity = 1,
                Price = 5
            });

            var authorization = await SquareUtils.GetAuthorizationAsync(0);

            var order = await SquareUtils.CreateOrder(0, paymentDef.Amount, paymentDef.LineItems, authorization.Location.LocationId, null, null, "OrderBasicTest", Guid.NewGuid().ToString());
                        

            Assert.Equal(paymentDef.Amount * 100, (decimal)order.TotalMoney.Amount);
        }

        [Fact]
        public async void OrderWithTaxesTest()
        {
            var paymentDef = new PaymentDefinition()
            {
                Amount = 41.40m,
                Nonce = "cnon:card-nonce-ok",
                Tax = 7
            };

            paymentDef.LineItems.Add(new PaymentLineItem
            {
                Name = "Item 1",
                Quantity = 1,
                Price = 10
            });
            paymentDef.LineItems.Add(new PaymentLineItem
            {
                Name = "Item 2",
                Quantity = 1,
                Price = 10
            });
            paymentDef.LineItems.Add(new PaymentLineItem
            {
                Name = "Item 3",
                Quantity = 1,
                Price = 10,
                Taxable = true
            });
            paymentDef.LineItems.Add(new PaymentLineItem
            {
                Name = "Item 4",
                Quantity = 1,
                Price = 10,
                Taxable = true
            });

            var authorization = await SquareUtils.GetAuthorizationAsync(0);

            var order = await SquareUtils.CreateOrder(0, paymentDef.Amount, paymentDef.LineItems, authorization.Location.LocationId, paymentDef.Tax, null, "OrderWithTaxesTest", Guid.NewGuid().ToString());
            
            Assert.Equal(paymentDef.Amount * 100, (decimal)order.TotalMoney.Amount);
        }

        [Fact]
        public async Task OrderWithDiscountBasicTest()
        {
            var paymentDef = new PaymentDefinition()
            {
                Amount = 90,
                Nonce = "cnon:card-nonce-ok",
                DiscountRate = 10
            };

            paymentDef.LineItems.Add(new PaymentLineItem
            {
                Name = "Item 1",
                Quantity = 1,
                Price = 25
            });
            paymentDef.LineItems.Add(new PaymentLineItem
            {
                Name = "Item 2",
                Quantity = 1,
                Price = 25
            });
            paymentDef.LineItems.Add(new PaymentLineItem
            {
                Name = "Item 3",
                Quantity = 1,
                Price = 50
            });

            var authorization = await SquareUtils.GetAuthorizationAsync(0);

            var order = await SquareUtils.CreateOrder(0, paymentDef.Amount, paymentDef.LineItems, authorization.Location.LocationId, null, paymentDef.DiscountRate, "OrderWithDiscountBasicTest", Guid.NewGuid().ToString());
            
            Assert.Equal(paymentDef.Amount * 100, (decimal)order.TotalMoney.Amount);
        }

        [Fact]
        public async Task OrderWithDiscountComplexTest()
        {
            var paymentDef = new PaymentDefinition()
            {
                Amount = 2.00m,
                Nonce = "cnon:card-nonce-ok",
                DiscountRate = 20m
            };

            paymentDef.LineItems.Add(new PaymentLineItem
            {
                Name = "Item 1",
                Quantity = 1,
                Price = 0.50m
            });
            paymentDef.LineItems.Add(new PaymentLineItem
            {
                Name = "Item 2",
                Quantity = 1,
                Price = 0.50m
            });
            paymentDef.LineItems.Add(new PaymentLineItem
            {
                Name = "Item 3",
                Quantity = 1,
                Price = 0.50m
            });
            paymentDef.LineItems.Add(new PaymentLineItem
            {
                Name = "Item 4",
                Quantity = 1,
                Price = 1.00m
            });

            var authorization = await SquareUtils.GetAuthorizationAsync(0);

            var order = await SquareUtils.CreateOrder(0, paymentDef.Amount, paymentDef.LineItems, authorization.Location.LocationId, null, paymentDef.DiscountRate, "OrderWithDiscountComplexTest", Guid.NewGuid().ToString());
            
            Assert.Equal(paymentDef.Amount * 100, (decimal)order.TotalMoney.Amount);
        }

        [Fact]
        public async Task OrderWithDiscountAndTaxComplexTest()
        {
            var paymentDef = new PaymentDefinition()
            {
                Amount = 2.08m,
                Nonce = "cnon:card-nonce-ok",
                DiscountRate = 20m,
                Tax = 7
            };

            paymentDef.LineItems.Add(new PaymentLineItem
            {
                Name = "Item 1",
                Quantity = 1,
                Price = 0.50m,
                Taxable = true
            });
            paymentDef.LineItems.Add(new PaymentLineItem
            {
                Name = "Item 2",
                Quantity = 1,
                Price = 0.50m,
                Taxable = true
            });
            paymentDef.LineItems.Add(new PaymentLineItem
            {
                Name = "Item 3",
                Quantity = 1,
                Price = 0.50m,
                Taxable = true
            });
            paymentDef.LineItems.Add(new PaymentLineItem
            {
                Name = "Item 4",
                Quantity = 1,
                Price = 1.00m
            });

            var authorization = await SquareUtils.GetAuthorizationAsync(0);

            var order = await SquareUtils.CreateOrder(0, paymentDef.Amount, paymentDef.LineItems, authorization.Location.LocationId, paymentDef.Tax, paymentDef.DiscountRate, "OrderWithDiscountAndTaxComplexTest", Guid.NewGuid().ToString());
            
            Assert.Equal(paymentDef.Amount * 100, (decimal)order.TotalMoney.Amount);
        }

        [Fact]
        public async Task OrderWithDiscountAndTaxMostComplexTest()
        {
            var paymentDef = new PaymentDefinition()
            {
                Amount = 156.94m,
                Nonce = "cnon:card-nonce-ok",
                DiscountRate = 21.5m,
                Tax = 5
            };

            paymentDef.LineItems.Add(new PaymentLineItem
            {
                Name = "Item 1",
                Quantity = 1,
                Price = 10m
            });
            paymentDef.LineItems.Add(new PaymentLineItem
            {
                Name = "Item 2",
                Quantity = 2,
                Price = 21.50m
            });
            paymentDef.LineItems.Add(new PaymentLineItem
            {
                Name = "Item 3",
                Quantity = 3,
                Price = 19.99m,
                Taxable = true
            });
            paymentDef.LineItems.Add(new PaymentLineItem
            {
                Name = "Item 4",
                Quantity = 4,
                Price = 19.99m,
                Taxable = true
            });

            var authorization = await SquareUtils.GetAuthorizationAsync(0);

            var order = await SquareUtils.CreateOrder(0, paymentDef.Amount, paymentDef.LineItems, authorization.Location.LocationId, paymentDef.Tax, paymentDef.DiscountRate, "OrderWithDiscountAndTaxMostComplexTest", Guid.NewGuid().ToString());

            Assert.Equal(paymentDef.Amount * 100, (decimal)order.TotalMoney.Amount);
        }

        [Fact]
        public async Task OrderWithIssueTest()
        {
            var paymentDef = new PaymentDefinition()
            {
                Amount = 3.68m,
                Nonce = "cnon:card-nonce-ok",
                Tax = 7
            };

            paymentDef.LineItems.Add(new PaymentLineItem
            {
                Name = "Item 1",
                Quantity = 1,
                Price = 0.50m,
                Taxable = true
            });
            paymentDef.LineItems.Add(new PaymentLineItem
            {
                Name = "Item 2",
                Quantity = 1,
                Price = 0.50m,
                Taxable = true
            });
            paymentDef.LineItems.Add(new PaymentLineItem
            {
                Name = "Item 3",
                Quantity = 3,
                Price = 0.50m,
                Taxable = true
            });
            paymentDef.LineItems.Add(new PaymentLineItem
            {
                Name = "Item 4",
                Quantity = 1,
                Price = 1.00m
            });

            var authorization = await SquareUtils.GetAuthorizationAsync(0);

            var order = await SquareUtils.CreateOrder(0, paymentDef.Amount, paymentDef.LineItems, authorization.Location.LocationId, paymentDef.Tax, paymentDef.DiscountRate, "OrderWithIssueTest", Guid.NewGuid().ToString());
            
            Assert.Equal(paymentDef.Amount * 100, (decimal)order.TotalMoney.Amount);
        }

        [Fact]
        public async Task RoundingAdjustmentWithPossitiveDiffTest()
        {
            var paymentDef = new PaymentDefinition()
            {
                Amount = 2.61m,
                Nonce = "cnon:card-nonce-ok",
                Tax = 7
            };

            paymentDef.LineItems.Add(new PaymentLineItem
            {
                Name = "Item 1",
                Quantity = 1,
                Price = 0.50m,
                Taxable = true
            });
            paymentDef.LineItems.Add(new PaymentLineItem
            {
                Name = "Item 2",
                Quantity = 1,
                Price = 0.50m,
                Taxable = true
            });
            paymentDef.LineItems.Add(new PaymentLineItem
            {
                Name = "Item 3",
                Quantity = 1,
                Price = 0.50m,
                Taxable = true
            });
            paymentDef.LineItems.Add(new PaymentLineItem
            {
                Name = "Item 4",
                Quantity = 1,
                Price = 1.00m
            });

            var authorization = await SquareUtils.GetAuthorizationAsync(0);

            var order = await SquareUtils.CreateOrder(0, paymentDef.Amount, paymentDef.LineItems, authorization.Location.LocationId, paymentDef.Tax, paymentDef.DiscountRate, "unit-test", Guid.NewGuid().ToString());
            

            Assert.Equal(paymentDef.Amount * 100, (decimal)order.TotalMoney.Amount);
        }

        [Fact]
        public async Task RoundingAdjustmentWithNegativeDiffTest()
        {
            var paymentDef = new PaymentDefinition()
            {
                Amount = 2.58m,
                Nonce = "cnon:card-nonce-ok",
                Tax = 7
            };

            paymentDef.LineItems.Add(new PaymentLineItem
            {
                Name = "Item 1",
                Quantity = 1,
                Price = 0.50m,
                Taxable = true
            });
            paymentDef.LineItems.Add(new PaymentLineItem
            {
                Name = "Item 2",
                Quantity = 1,
                Price = 0.50m,
                Taxable = true
            });
            paymentDef.LineItems.Add(new PaymentLineItem
            {
                Name = "Item 3",
                Quantity = 1,
                Price = 0.50m,
                Taxable = true
            });
            paymentDef.LineItems.Add(new PaymentLineItem
            {
                Name = "Item 4",
                Quantity = 1,
                Price = 1.00m
            });

            var authorization = await SquareUtils.GetAuthorizationAsync(0);

            var order = await SquareUtils.CreateOrder(0, paymentDef.Amount, paymentDef.LineItems, authorization.Location.LocationId, paymentDef.Tax, paymentDef.DiscountRate, "RoundingAdjustmentWithNegativeDiffTest", Guid.NewGuid().ToString());
            
            Assert.Equal(paymentDef.Amount * 100, (decimal)order.TotalMoney.Amount);
        }

        [Fact]
        public async Task RoundingAdjustmentWithDiscountRateTest()
        {
            var paymentDef = new PaymentDefinition()
            {
                Amount = 2.57m,
                Nonce = "cnon:card-nonce-ok",
                Tax = 7,
                DiscountRate = 1
            };

            paymentDef.LineItems.Add(new PaymentLineItem
            {
                Name = "Item 1",
                Quantity = 1,
                Price = 0.50m,
                Taxable = true
            });
            paymentDef.LineItems.Add(new PaymentLineItem
            {
                Name = "Item 2",
                Quantity = 1,
                Price = 0.50m,
                Taxable = true
            });
            paymentDef.LineItems.Add(new PaymentLineItem
            {
                Name = "Item 3",
                Quantity = 1,
                Price = 0.50m,
                Taxable = true
            });
            paymentDef.LineItems.Add(new PaymentLineItem
            {
                Name = "Item 4",
                Quantity = 1,
                Price = 1.00m
            });

            var authorization = await SquareUtils.GetAuthorizationAsync(0);

            var order = await SquareUtils.CreateOrder(0, paymentDef.Amount, paymentDef.LineItems, authorization.Location.LocationId, paymentDef.Tax, paymentDef.DiscountRate, "RoundingAdjustmentWithDiscountRateTest", Guid.NewGuid().ToString());
            
            Assert.Equal(paymentDef.Amount * 100, (decimal)order.TotalMoney.Amount);
        }

        [Fact]
        public async Task GetOrCreateSquareCustomerTest()
        {
            Random rnd = new Random();

            var billingContact = new BillingContact()
            {
                AddressLine1 = "Addr. line 1",
                AddressLine2 = "Addr. line 2",
                City = "Quito",
                Country = "EC",
                CountryName = "Ecuador",
                Email = $"jdoe+towbook-{rnd.Next()}@gmail.com",
                FamilyName = "John",
                GivenName = "Doe",
                Phone = "+************",
                PostalCode = "170172",
                Region = "PI"
            };

            // should create the customer
            var customer = await SquareUtils.GetOrCreateCustomer(2, billingContact);
            
            Assert.Equal(billingContact.Email, customer.EmailAddress);

            System.Threading.Thread.Sleep(7000);

            // should retrieve the already created customer 
            var customerRead = await SquareUtils.GetOrCreateCustomer(2, billingContact);
            
            Assert.Equal(customer.Id, customerRead.Id);
            Assert.Equal(billingContact.Email, customerRead.EmailAddress);
        }

        [Fact]
        public void GetInitialNotificationTimestampTest()
        {
            var MAX_WAITING_TIME_IN_SECONDS = 5; // seconds
            var referenceNumber = "ACB" + new Random().Next();

            var initialTimestamp = SquareUtils.GetFirstNotificationTimestamp(referenceNumber);
            var now = DateTime.Now;
            var diff = (now - initialTimestamp).TotalSeconds;
            Console.WriteLine(now + " _ " + initialTimestamp + " - diff 1 = " + diff);
            Assert.True(diff <= MAX_WAITING_TIME_IN_SECONDS);

            Thread.Sleep(1000);
            initialTimestamp = SquareUtils.GetFirstNotificationTimestamp(referenceNumber);
            now = DateTime.Now;
            diff = (now - initialTimestamp).TotalSeconds;
            Console.WriteLine(now + " _ " + initialTimestamp + " - diff 2 = " + diff);
            Assert.True(diff <= MAX_WAITING_TIME_IN_SECONDS);

            Thread.Sleep(1000);
            initialTimestamp = SquareUtils.GetFirstNotificationTimestamp(referenceNumber);
            now = DateTime.Now;
            diff = (now - initialTimestamp).TotalSeconds;
            Console.WriteLine(now + " _ " + initialTimestamp + " - diff 3 = " + diff);
            Assert.True(diff <= MAX_WAITING_TIME_IN_SECONDS);

            Thread.Sleep(1000);
            initialTimestamp = SquareUtils.GetFirstNotificationTimestamp(referenceNumber);
            now = DateTime.Now;
            diff = (now - initialTimestamp).TotalSeconds;
            Console.WriteLine(now + " _ " + initialTimestamp + " - diff 4 = " + diff);
            Assert.True(diff <= MAX_WAITING_TIME_IN_SECONDS);

            Thread.Sleep(1000);
            initialTimestamp = SquareUtils.GetFirstNotificationTimestamp(referenceNumber);
            now = DateTime.Now;
            diff = (now - initialTimestamp).TotalSeconds;
            Console.WriteLine(now + " _ " + initialTimestamp + " - diff 5 = " + diff);
            Assert.True(diff <= MAX_WAITING_TIME_IN_SECONDS);

            Thread.Sleep(1000);
            initialTimestamp = SquareUtils.GetFirstNotificationTimestamp(referenceNumber);
            now = DateTime.Now;
            diff = (now - initialTimestamp).TotalSeconds;
            Console.WriteLine(now + " _ " + initialTimestamp + " - diff 5 = " + diff);
            Assert.True(diff <= MAX_WAITING_TIME_IN_SECONDS);
        }

        [Fact]
        public void UtilGetPaymentLineItemsTest()
        {
            var i1 = new InvoiceItem();
            //i1.Id = 12345;
            i1.CustomPrice = 0.4m;
            i1.Quantity = 2;

            var i2 = new InvoiceItem();
            i2.CustomPrice = 75m;
            i2.Quantity = 1;

            var i3 = new InvoiceItem();
            i3.CustomPrice = -0.4m;
            i3.Quantity = 2;
            i3.RelatedInvoiceItemId = i1.Id;

            var invoiceItems = new List<InvoiceItem>();
            invoiceItems.Add(i1);
            invoiceItems.Add(i2);
            invoiceItems.Add(i3);

            List<PaymentLineItem> actual = SquareUtils.GetPaymentLineItems(invoiceItems);

            var expected = new List<PaymentLineItem>();
            expected.Add(new PaymentLineItem
            {
                Quantity = i1.Quantity,
                Price = i1.CustomPrice.GetValueOrDefault(),
                HasFreeQuantity = true,
                DiscountQuantity = i3.Quantity
            });
            expected.Add(new PaymentLineItem
            {
                Quantity = i2.Quantity,
                Price = i2.CustomPrice.GetValueOrDefault()
            });

            Assert.Equal(expected.Count, actual.Count);
            Assert.Equal(expected[0].HasFreeQuantity, actual[0].HasFreeQuantity);
            Assert.Equal(expected[0].Quantity, actual[0].Quantity);
            Assert.Equal(expected[0].DiscountQuantity, actual[0].DiscountQuantity);
            Assert.Equal(expected[0].Price, actual[0].Price);
            Assert.Equal(expected[1].Quantity, actual[1].Quantity);
            Assert.Equal(expected[1].Price, actual[1].Price);

        }

        [Fact]
        public void UtilGetPaymentLineItemsNoExactFreeQtyTest()
        {
            var i1 = new InvoiceItem();

            //i1.Id = 12345;
            i1.CustomPrice = 0.4m;
            i1.Quantity = 2;

            var i2 = new InvoiceItem();
            i2.CustomPrice = 75m;
            i2.Quantity = 1;

            var i3 = new InvoiceItem();
            i3.CustomPrice = -0.4m;
            i3.Quantity = 0.5m;
            i3.RelatedInvoiceItemId = i1.Id;

            var invoiceItems = new List<InvoiceItem>();
            invoiceItems.Add(i1);
            invoiceItems.Add(i2);
            invoiceItems.Add(i3);

            List<PaymentLineItem> actual = SquareUtils.GetPaymentLineItems(invoiceItems);
            var expected = new List<PaymentLineItem>();
            expected.Add(new PaymentLineItem
            {
                Quantity = i1.Quantity,
                Price = i1.Price,
                HasFreeQuantity = true,
                DiscountQuantity = i3.Quantity
            });
            expected.Add(new PaymentLineItem
            {
                Quantity = i2.Quantity,
                Price = i2.Price
            });

            Assert.Equal(expected.Count, actual.Count);
            Assert.Equal(expected[0].Quantity, actual[0].Quantity);
            Assert.Equal(expected[0].HasFreeQuantity, actual[0].HasFreeQuantity);
            Assert.Equal(expected[0].DiscountQuantity, actual[0].DiscountQuantity);
            Assert.Equal(expected[0].Price, actual[0].Price);
            Assert.Equal(expected[1].Quantity, actual[1].Quantity);
            Assert.Equal(expected[1].Price, actual[1].Price);
        }


        internal Entry CreateVirtualEntry(bool taxable, bool isTaxExempt = false, bool includeLoaded = true)
        {
            var unique = -1;

            var entry = new Entry()
            {
                CompanyId = 2,
                AccountId = 1,
                Assets = new System.Collections.ObjectModel.Collection<EntryAsset>(),
            };

            var ri = new RateItem()
            {
                Cost = 1.0M,
                CompanyId = 2,
                RateType = RateItem.RateTypeEnum.FixedRate,
                Measurement = RateItem.MeasurementEnum.Hours,
                Taxable = taxable,
                Predefined = PredefinedRateItem.GetById(PredefinedRateItem.BUILTIN_MILEAGE_LOADED)
            };

            if (includeLoaded)
            {
                var i1 = InvoiceItem.GetById(unique--);
                i1.CustomName = "Loaded/Hooked Mileage";
                i1.CustomPrice = 1.0m;
                i1.Quantity = 15;
                i1.Taxable = taxable;
                i1.RateItem = ri;
                i1.AssetId = -1000;

                entry.Invoice.InvoiceItems.Add(i1);

                var i2 = InvoiceItem.GetById(unique--);
                i2.CustomName = "FreeQuantity Credit on Loaded";
                i2.CustomPrice = -1.0m;
                i2.Quantity = 10;
                i2.Taxable = taxable;
                i2.RateItem = ri;
                i2.AssetId = -1000;
                i2.RelatedInvoiceItemId = i1.Id;

                entry.Invoice.InvoiceItems.Add(i2);
            }


            entry.Invoice.IsTaxExempt = isTaxExempt;
            entry.Invoice.DispatchEntry = entry;

            // get subtotals, tax, etc
            entry.Invoice.ForceRecalculate(false);

            return entry;
        }

        [Fact]
        public async Task UtilGetFreeQtyWithNoTaxableItemsTest()
        {
            var entry = CreateVirtualEntry(false, false);
            var taxrate = (await TaxRate.GetByCompanyAsync(entry.Company)).FirstOrDefault();

            List<PaymentLineItem> actual = SquareUtils.GetPaymentLineItems(entry.InvoiceItems.ToList());

            var expected = new List<PaymentLineItem>();
            var i1 = entry.InvoiceItems.First();
            expected.Add(new PaymentLineItem
            {
                Quantity = i1.Quantity,
                Price = i1.Price,
                Taxable = i1.Taxable,
            });

            Assert.Equal(expected.Count, actual.Count);
            Assert.Equal(expected[0].Taxable, actual[0].Taxable);
            Assert.Equal(expected[0].Quantity, actual[0].Quantity);
            Assert.Equal(expected[0].Price, actual[0].Price);

            var paymentDef = new PaymentDefinition
            {
                CompanyId = entry.CompanyId,
                Amount = entry.Invoice.GrandTotal,
                Currency = entry.Company.CurrencyCode,
                ReferenceId = "UT-AC-1",
                Entry = entry,
                Tax = entry.Invoice.IsTaxExempt ? (decimal?)null : taxrate.Rate,
                DiscountRate = entry.Invoice.GetDiscountAsRate()
            };

            paymentDef.LineItems.AddRange(actual
                .Select(s =>
                {
                    return new PaymentLineItem()
                    {
                        Name = s.Name,
                        Quantity = s.Quantity,
                        Price = s.Price,
                        Taxable = s.Taxable,
                        MeasurementUnit = s.MeasurementUnit,
                        ReferenceId = s.ReferenceId,
                        Note = s.Note,
                        HasFreeQuantity = s.HasFreeQuantity,
                        DiscountQuantity = s.DiscountQuantity
                    };
                }));

            var authorization = await SquareUtils.GetAuthorizationAsync(0);

            var order = await SquareUtils.CreateOrder(0, paymentDef.Amount, paymentDef.LineItems, authorization.Location.LocationId, paymentDef.Tax, paymentDef.DiscountRate, "UtilGetFreeQtyWithNoTaxableItemsTest", Guid.NewGuid().ToString());

            Assert.Equal(paymentDef.Amount * 100, (decimal)order.TotalMoney.Amount);
        }

        [Fact]
        public async Task UtilGetFreeQtyWithTaxableItemsTest()
        {
            var entry = CreateVirtualEntry(true, false);
            var taxrate = (await TaxRate.GetByCompanyAsync(entry.Company)).FirstOrDefault();

            List<PaymentLineItem> actual = SquareUtils.GetPaymentLineItems(entry.InvoiceItems.ToList());

            var expected = new List<PaymentLineItem>();
            var i1 = entry.InvoiceItems.First();
            var i2 = entry.InvoiceItems.Last();
            expected.Add(new PaymentLineItem
            {
                Quantity = i1.Quantity,
                Price = i1.Price,
                Taxable = true,
                HasFreeQuantity = i2 != null,
                DiscountQuantity = i2?.Quantity
            });

            Assert.Equal(expected.Count, actual.Count);
            Assert.Equal(expected[0].Taxable, actual[0].Taxable);
            Assert.Equal(expected[0].Quantity, actual[0].Quantity);
            Assert.Equal(expected[0].HasFreeQuantity, actual[0].HasFreeQuantity);
            Assert.Equal(expected[0].DiscountQuantity, actual[0].DiscountQuantity);
            Assert.Equal(expected[0].Price, actual[0].Price);

            var paymentDef = new PaymentDefinition
            {
                CompanyId = entry.CompanyId,
                Amount = entry.Invoice.GrandTotal,
                Currency = entry.Company.CurrencyCode,
                ReferenceId = "UT-AC-1",
                Entry = entry,
                Tax = entry.Invoice.IsTaxExempt ? (decimal?)null : taxrate.Rate,
                DiscountRate = entry.Invoice.GetDiscountAsRate()
            };

            paymentDef.LineItems.AddRange(actual
                .Select(s =>
                {
                    return new PaymentLineItem()
                    {
                        Name = s.Name,
                        Quantity = s.Quantity,
                        Price = s.Price,
                        Taxable = s.Taxable,
                        MeasurementUnit = s.MeasurementUnit,
                        ReferenceId = s.ReferenceId,
                        Note = s.Note,
                        HasFreeQuantity = s.HasFreeQuantity,
                        DiscountQuantity = s.DiscountQuantity
                    };
                }));

            var authorization = await SquareUtils.GetAuthorizationAsync(0);

            var order = await SquareUtils.CreateOrder(0, paymentDef.Amount, paymentDef.LineItems, authorization.Location.LocationId, 
                paymentDef.Tax, paymentDef.DiscountRate, "UtilGetFreeQtyWithTaxableItemsTest", Guid.NewGuid().ToString());
            

            Assert.Equal(paymentDef.Amount * 100, (decimal)order.TotalMoney.Amount);
        }

        [Fact]
        public async void UtilGetFreeQtyWithTaxExemptSetTest()
        {
            var entry = CreateVirtualEntry(true, true);
            var taxrate = (await TaxRate.GetByCompanyAsync(entry.Company)).FirstOrDefault();

            List<PaymentLineItem> actual = SquareUtils.GetPaymentLineItems(entry.InvoiceItems.ToList());

            var expected = new List<PaymentLineItem>();
            var i1 = entry.InvoiceItems.First();
            var i2 = entry.InvoiceItems.Last();
            expected.Add(new PaymentLineItem
            {
                Quantity = i1.Quantity,
                Price = i1.Price,
                Taxable = true,
                HasFreeQuantity = i2 != null,
                DiscountQuantity = i2?.Quantity
            });

            Assert.Equal(expected.Count, actual.Count);
            Assert.Equal(expected[0].Taxable, actual[0].Taxable);
            Assert.Equal(expected[0].Quantity, actual[0].Quantity);
            Assert.Equal(expected[0].HasFreeQuantity, actual[0].HasFreeQuantity);
            Assert.Equal(expected[0].DiscountQuantity, actual[0].DiscountQuantity);
            Assert.Equal(expected[0].Price, actual[0].Price);

            var paymentDef = new PaymentDefinition
            {
                CompanyId = entry.CompanyId,
                Amount = entry.Invoice.GrandTotal,
                Currency = entry.Company.CurrencyCode,
                ReferenceId = "UT-AC-1",
                Entry = entry,
                Tax = entry.Invoice.IsTaxExempt ? (decimal?)null : taxrate.Rate,
                DiscountRate = entry.Invoice.GetDiscountAsRate()
            };

            paymentDef.LineItems.AddRange(actual
                .Select(s =>
                {
                    return new PaymentLineItem()
                    {
                        Name = s.Name,
                        Quantity = s.Quantity,
                        Price = s.Price,
                        Taxable = s.Taxable,
                        MeasurementUnit = s.MeasurementUnit,
                        ReferenceId = s.ReferenceId,
                        Note = s.Note,
                        HasFreeQuantity = s.HasFreeQuantity,
                        DiscountQuantity = s.DiscountQuantity
                    };
                }));

            var authorization = await SquareUtils.GetAuthorizationAsync(0);

            var order = await  SquareUtils.CreateOrder(0, paymentDef.Amount, paymentDef.LineItems, authorization.Location.LocationId, paymentDef.Tax, paymentDef.DiscountRate, "UtilGetFreeQtyWithTaxExemptSetTest", Guid.NewGuid().ToString());
            
            Assert.Equal(paymentDef.Amount * 100, (decimal)order.TotalMoney.Amount);
        }

        [Fact]
        public async Task UtilDiscountAndFSCAndTaxableTest()
        {
            int unique = -1000;
            var entry = CreateVirtualEntry(true, false);

            entry.InvoiceItems.Clear();

            var taxrate = (await TaxRate.GetByCompanyAsync(entry.Company)).FirstOrDefault();
            var originalTaxrate = taxrate.Rate;

            taxrate.Rate = 8.875m;
            await taxrate.Save(); // store taxrate to 8.875%

            var riUnloaded = new RateItem()
            {
                Cost = 4.0M,
                CompanyId = 2,
                RateType = RateItem.RateTypeEnum.FixedRate,
                Measurement = RateItem.MeasurementEnum.Hours,
                Taxable = true,
                Predefined = await PredefinedRateItem.GetByIdAsync(PredefinedRateItem.BUILTIN_MILEAGE_UNLOADED)
            };

            var riFuelSurcharge = new RateItem()
            {
                Cost = 5.50M,
                CompanyId = 2,
                RateType = RateItem.RateTypeEnum.FixedRate,
                Measurement = RateItem.MeasurementEnum.Hours,
                Taxable = true,
                Predefined = await PredefinedRateItem.GetByIdAsync(PredefinedRateItem.BUILTIN_FUEL_SURCHARGE)
            };

            var riDiscount = new RateItem()
            {
                Cost = 0M,
                CompanyId = 2,
                RateType = RateItem.RateTypeEnum.FixedRate,
                Measurement = RateItem.MeasurementEnum.Hours,
                Taxable = true,
                Predefined = await PredefinedRateItem.GetByIdAsync(PredefinedRateItem.BUILTIN_DISCOUNT)
            };

            var i1 = InvoiceItem.GetById(unique++);
            i1.CustomName = "Unloaded/Enroute Mileage";
            i1.Quantity = 6;
            i1.CustomPrice = 1.0m;
            i1.Taxable = true;
            i1.RateItem = riUnloaded;
            i1.AssetId = -1000;

            var i2 = InvoiceItem.GetById(unique--);
            i2.CustomName = "FreeQuantity Credit on Unloaded";
            i2.CustomPrice = -1.0m;
            i2.Quantity = 5.0m;
            i2.Taxable = true;
            i2.RateItem = riUnloaded;
            i2.AssetId = -1000;
            i2.RelatedInvoiceItemId = i1.Id;

            var i3 = InvoiceItem.GetById(unique++);
            i3.CustomName = "Tow/Hook Fee (Rollback/ Landoll)";
            i3.Quantity = 1.0M;
            i3.CustomPrice = 4.0m;
            i3.Taxable = true;
            i3.RateItem = riFuelSurcharge;
            i3.AssetId = -1000;

            var i4 = InvoiceItem.GetById(unique++);
            i4.CustomName = "Fuel Surcharge";
            i4.Quantity = 1.0M;
            i4.CustomPrice = 0.04m;
            i4.Taxable = false;
            i4.RateItem = null;
            i4.AssetId = -1000;

            var i5 = InvoiceItem.GetById(unique++);
            i5.CustomName = "Discount:2 - Disaocunt";
            i5.Quantity = 1.0M;
            i5.CustomPrice = -2.00m;
            i5.Taxable = false;
            i5.RateItem = riDiscount;
            i5.AssetId = -1000;

            entry.Invoice.InvoiceItems.Add(i1);
            entry.Invoice.InvoiceItems.Add(i2);
            entry.Invoice.InvoiceItems.Add(i3);
            entry.Invoice.InvoiceItems.Add(i4);
            entry.Invoice.InvoiceItems.Add(i5);

            // get subtotals, tax, etc
            entry.Invoice.ForceRecalculate(false);

            List<PaymentLineItem> actual = SquareUtils.GetPaymentLineItems(entry.InvoiceItems.ToList());



            var expected = new List<PaymentLineItem>();

            expected.Add(new PaymentLineItem
            {
                Quantity = 60m,
                Price = 1.00m,
                Taxable = true,
                HasFreeQuantity = true,
                DiscountQuantity = 5.0m
            });

            expected.Add(new PaymentLineItem
            {
                Quantity = 1.0m,
                Price = 4.00m,
                Taxable = true,
                HasFreeQuantity = false
            });

            expected.Add(new PaymentLineItem
            {
                Quantity = 1.0m,
                Price = 0.04m,
                Taxable = false,
                HasFreeQuantity = false
            });

            Assert.Equal(expected.Count, actual.Count);
            Assert.Equal(expected[0].Taxable, actual[0].Taxable);
            Assert.Equal(expected[1].Taxable, actual[1].Taxable);
            Assert.Equal(expected[0].HasFreeQuantity, actual[0].HasFreeQuantity);
            Assert.Equal(expected[0].DiscountQuantity, actual[0].DiscountQuantity);


            var paymentDef = new PaymentDefinition
            {
                CompanyId = entry.CompanyId,
                Amount = entry.Invoice.GrandTotal,
                Currency = entry.Company.CurrencyCode,
                ReferenceId = "UT-AC-1",
                Entry = entry,
                Tax = entry.Invoice.IsTaxExempt ? (decimal?)null : taxrate.Rate,
                DiscountRate = entry.Invoice.GetDiscountAsRate()
            };

            paymentDef.LineItems.AddRange(actual
                .Select(s =>
                {
                    return new PaymentLineItem()
                    {
                        Name = s.Name,
                        Quantity = s.Quantity,
                        Price = s.Price,
                        Taxable = s.Taxable,
                        MeasurementUnit = s.MeasurementUnit,
                        ReferenceId = s.ReferenceId,
                        Note = s.Note,
                        HasFreeQuantity = s.HasFreeQuantity,
                        DiscountQuantity = s.DiscountQuantity
                    };
                }));

            var authorization = await SquareUtils.GetAuthorizationAsync(0);

            var order = await SquareUtils.CreateOrder(0, paymentDef.Amount, paymentDef.LineItems, authorization.Location.LocationId, 
                paymentDef.Tax, paymentDef.DiscountRate, "UtilDiscountAndFSCAndTaxableTest", Guid.NewGuid().ToString());
            

            Assert.Equal(paymentDef.Amount * 100, (decimal)order.TotalMoney.Amount);

        }

        [Fact]
        public async Task UtilFreeQtyWithTaxableAndNonTaxableItemsTest()
        {
            int unique = -1000;
            var entry = CreateVirtualEntry(true, false);

            entry.InvoiceItems.Clear();

            var taxrate = (await TaxRate.GetByCompanyAsync(entry.Company)).FirstOrDefault();
            taxrate.Rate = 8.875m;
            await taxrate.Save(); // store taxrate to 8.875%

            var riUnloaded = new RateItem()
            {
                Cost = 4.0M,
                CompanyId = 2,
                RateType = RateItem.RateTypeEnum.FixedRate,
                Measurement = RateItem.MeasurementEnum.Hours,
                Taxable = true,
                Predefined = await PredefinedRateItem.GetByIdAsync(PredefinedRateItem.BUILTIN_MILEAGE_UNLOADED)
            };

            var riLoaded = new RateItem()
            {
                Cost = 5.50M,
                CompanyId = 2,
                RateType = RateItem.RateTypeEnum.FixedRate,
                Measurement = RateItem.MeasurementEnum.Hours,
                Taxable = true,
                Predefined = await PredefinedRateItem.GetByIdAsync(PredefinedRateItem.BUILTIN_MILEAGE_LOADED)
            };

            var riFuelSurcharge = new RateItem()
            {
                Cost = 5.50M,
                CompanyId = 2,
                RateType = RateItem.RateTypeEnum.FixedRate,
                Measurement = RateItem.MeasurementEnum.Hours,
                Taxable = true,
                Predefined = await PredefinedRateItem.GetByIdAsync(PredefinedRateItem.BUILTIN_MILEAGE_LOADED)
            };

            var i1 = InvoiceItem.GetById(unique++);
            i1.CustomName = "Unloaded/Enroute Mileage";
            i1.Quantity = 7;
            i1.CustomPrice = 4.0m;
            i1.Taxable = true;
            i1.RateItem = riUnloaded;
            i1.AssetId = -1000;


            var i2 = InvoiceItem.GetById(unique++);
            i2.CustomName = "Loaded/Hooked Mileage";
            i2.Quantity = 5;
            i2.CustomPrice = 5.50m;
            i2.Taxable = true;
            i2.RateItem = riLoaded;
            i2.AssetId = -1000;


            var i3 = InvoiceItem.GetById(unique++);
            i3.CustomName = "Tow/Hook Fee (Rollback/ Landoll)";
            i3.Quantity = 1.0M;
            i3.CustomPrice = 95.0m;
            i3.Taxable = true;
            i3.RateItem = null;
            i3.AssetId = -1000;


            var i4 = InvoiceItem.GetById(unique++);
            i4.CustomName = "Handling (CC) Fee";
            i4.Quantity = 112.0M;
            i4.CustomPrice = 0.10m;
            i4.Taxable = false;
            i4.RateItem = null;
            i4.AssetId = -1000;


            var i5 = InvoiceItem.GetById(unique--);
            i5.CustomName = "FreeQuantity Credit on Unloaded";
            i5.CustomPrice = -1.0m;
            i5.Quantity = 5.0m;
            i5.Taxable = true;
            i5.RateItem = riUnloaded;
            i5.AssetId = -1000;
            i5.RelatedInvoiceItemId = i1.Id;


            var i6 = InvoiceItem.GetById(unique--);
            i6.CustomName = "FreeQuantity Credit on Loaded";
            i6.CustomPrice = -1.0m;
            i6.Quantity = 5.0m;
            i6.Taxable = true;
            i6.RateItem = riLoaded;
            i6.AssetId = -1000;
            i6.RelatedInvoiceItemId = i2.Id;

            entry.Invoice.InvoiceItems.Add(i1);
            entry.Invoice.InvoiceItems.Add(i2);
            entry.Invoice.InvoiceItems.Add(i3);
            entry.Invoice.InvoiceItems.Add(i4);
            entry.Invoice.InvoiceItems.Add(i5);
            entry.Invoice.InvoiceItems.Add(i6);

            // get subtotals, tax, etc
            entry.Invoice.ForceRecalculate(false);


            List<PaymentLineItem> actual = SquareUtils.GetPaymentLineItems(entry.InvoiceItems.ToList());

            var expected = new List<PaymentLineItem>();

            expected.Add(new PaymentLineItem
            {
                Quantity = 7.0m,
                Price = 4.00m,
                Taxable = true,
                HasFreeQuantity = true,
                DiscountQuantity = 5.0m
            });

            expected.Add(new PaymentLineItem
            {
                Quantity = 5.0m,
                Price = 5.50m,
                Taxable = true,
                HasFreeQuantity = true,
                DiscountQuantity = 5.0m
            });

            expected.Add(new PaymentLineItem
            {
                Quantity = 1.0m,
                Price = 95.0m,
                Taxable = true,
                HasFreeQuantity = false
            });

            expected.Add(new PaymentLineItem
            {
                Quantity = 112.0m,
                Price = 0.10m,
                Taxable = false,
                HasFreeQuantity = false
            });

            Assert.Equal(expected.Count, actual.Count);
            Assert.Equal(expected[0].Taxable, actual[0].Taxable);
            Assert.Equal(expected[1].Taxable, actual[1].Taxable);
            Assert.Equal(expected[2].Taxable, actual[2].Taxable);
            Assert.Equal(expected[3].Taxable, actual[3].Taxable);

            Assert.Equal(expected[0].HasFreeQuantity, actual[0].HasFreeQuantity);
            Assert.Equal(expected[0].DiscountQuantity, actual[0].DiscountQuantity);

            Assert.Equal(expected[1].HasFreeQuantity, actual[1].HasFreeQuantity);
            Assert.Equal(expected[1].DiscountQuantity, actual[1].DiscountQuantity);


            var paymentDef = new PaymentDefinition
            {
                CompanyId = entry.CompanyId,
                Amount = entry.Invoice.GrandTotal,
                Currency = entry.Company.CurrencyCode,
                ReferenceId = "UT-AC-1",
                Entry = entry,
                Tax = entry.Invoice.IsTaxExempt ? (decimal?)null : taxrate.Rate,
                DiscountRate = entry.Invoice.GetDiscountAsRate()
            };

            paymentDef.LineItems.AddRange(actual
                .Select(s =>
                {
                    return new PaymentLineItem()
                    {
                        Name = s.Name,
                        Quantity = s.Quantity,
                        Price = s.Price,
                        Taxable = s.Taxable,
                        MeasurementUnit = s.MeasurementUnit,
                        ReferenceId = s.ReferenceId,
                        Note = s.Note,
                        HasFreeQuantity = s.HasFreeQuantity,
                        DiscountQuantity = s.DiscountQuantity
                    };
                }));

            var authorization = await SquareUtils.GetAuthorizationAsync(0);

            var order = await SquareUtils.CreateOrder(0, paymentDef.Amount, paymentDef.LineItems, authorization.Location.LocationId, 
                paymentDef.Tax, paymentDef.DiscountRate, "UtilFreeQtyWithTaxableAndNonTaxableItemsTest", Guid.NewGuid().ToString());
            
            Assert.Equal(paymentDef.Amount * 100, (decimal)order.TotalMoney.Amount);

        }


        /// <summary>
        /// A unit test that tests the conditions of an invoice with the following conditions:
        /// FSC, Discount, Free Quantity, taxable items, non-taxable items and having the 
        /// invoice marked as tax exempt.
        /// </summary>
        [Fact]
        public async Task ComplexDiscountAndTaxExemptTest()
        {
            int unique = -1000;
            var entry = CreateVirtualEntry(true, false);

            entry.InvoiceItems.Clear();

            var taxrate = (await TaxRate.GetByCompanyAsync(entry.Company)).FirstOrDefault();
            taxrate.Rate = 8.875m;
            //taxrate.Save(); // store taxrate to 8.875%

            var riUnloaded = new RateItem()
            {
                Cost = 0.25M,
                CompanyId = 2,
                RateType = RateItem.RateTypeEnum.FixedRate,
                Measurement = RateItem.MeasurementEnum.Hours,
                Taxable = true,
                Predefined = await PredefinedRateItem.GetByIdAsync(PredefinedRateItem.BUILTIN_MILEAGE_UNLOADED)
            };

            var riLoaded = new RateItem()
            {
                Cost = 0.1M,
                CompanyId = 2,
                RateType = RateItem.RateTypeEnum.FixedRate,
                Measurement = RateItem.MeasurementEnum.Hours,
                Taxable = true,
                Predefined = await PredefinedRateItem.GetByIdAsync(PredefinedRateItem.BUILTIN_MILEAGE_LOADED)
            };

            var riFuelSurcharge = new RateItem()
            {
                Cost = 1.0M,
                CompanyId = 2,
                RateType = RateItem.RateTypeEnum.FixedRate,
                Measurement = RateItem.MeasurementEnum.Hours,
                Taxable = true,
                Predefined = await PredefinedRateItem.GetByIdAsync(PredefinedRateItem.BUILTIN_MILEAGE_LOADED)
            };

            var riDiscount = new RateItem()
            {
                Cost = 0.0M,
                CompanyId = 2,
                RateType = RateItem.RateTypeEnum.FixedRate,
                Measurement = RateItem.MeasurementEnum.Hours,
                Taxable = true,
                Predefined = await PredefinedRateItem.GetByIdAsync(PredefinedRateItem.BUILTIN_DISCOUNT)
            };

            var i1 = InvoiceItem.GetById(unique++);
            i1.CustomName = "Unloaded/Enroute Mileage";
            i1.Quantity = 5.4M;
            i1.CustomPrice = 0.25m;
            i1.Taxable = true;
            i1.RateItem = riUnloaded;
            i1.AssetId = -1000;


            var i2 = InvoiceItem.GetById(unique++);
            i2.CustomName = "Loaded/Hooked Mileage";
            i2.Quantity = 25.1M;
            i2.CustomPrice = 0.1m;
            i2.Taxable = true;
            i2.RateItem = riLoaded;
            i2.AssetId = -1000;


            var i3 = InvoiceItem.GetById(unique++);
            i3.CustomName = "Tow/Hook Fee";
            i3.Quantity = 1.0M;
            i3.CustomPrice = 1.0m;
            i3.Taxable = true;
            i3.RateItem = null;
            i3.AssetId = -1000;


            var i4 = InvoiceItem.GetById(unique++);
            i4.CustomName = "Credit Card Fee";
            i4.Quantity = 1.0M;
            i4.CustomPrice = 0.04m;
            i4.Taxable = false;
            i4.RateItem = null;
            i4.AssetId = -1000;


            var i5 = InvoiceItem.GetById(unique--);
            i5.CustomName = "FreeQuantity Credit on Unloaded";
            i5.CustomPrice = -0.25m;
            i5.Quantity = 5.0m;
            i5.Taxable = true;
            i5.RateItem = riUnloaded;
            i5.AssetId = -1000;
            i5.RelatedInvoiceItemId = i1.Id;


            var i6 = InvoiceItem.GetById(unique--);
            i6.CustomName = "FreeQuantity Credit on Loaded";
            i6.CustomPrice = -0.10m;
            i6.Quantity = 5.0m;
            i6.Taxable = true;
            i6.RateItem = riLoaded;
            i6.AssetId = -1000;
            i6.RelatedInvoiceItemId = i2.Id;

            var i7 = InvoiceItem.GetById(unique--);
            i7.CustomName = "Fuel Surcharge";
            i7.CustomPrice = 1.01M;
            i7.Quantity = 1;
            i7.Taxable = false;
            i7.RateItem = riFuelSurcharge;
            i7.AssetId = -1000;

            var i8 = InvoiceItem.GetById(unique--);
            i8.CustomName = "Discount:3.01";
            i8.CustomPrice = -3.01M;
            i8.Quantity = 1;
            i8.Taxable = false;
            i8.RateItem = riDiscount;
            i8.AssetId = -1000;

            entry.Invoice.InvoiceItems.Add(i1);
            entry.Invoice.InvoiceItems.Add(i2);
            entry.Invoice.InvoiceItems.Add(i3);
            entry.Invoice.InvoiceItems.Add(i4);
            entry.Invoice.InvoiceItems.Add(i5);
            entry.Invoice.InvoiceItems.Add(i6);
            entry.Invoice.InvoiceItems.Add(i7);
            entry.Invoice.InvoiceItems.Add(i8);

            // mark Invoice tax exempt
            entry.Invoice.IsTaxExempt = true;

            // get subtotals, tax, etc
            entry.Invoice.ForceRecalculate(false);


            List<PaymentLineItem> actual = SquareUtils.GetPaymentLineItems(entry.InvoiceItems.ToList());

            var expected = new List<PaymentLineItem>();

            expected.Add(new PaymentLineItem
            {
                Name = "Unloaded/Enroute Mileage",
                Quantity = 5.4m,
                Price = 0.25m,
                Taxable = true,
                HasFreeQuantity = true,
                DiscountQuantity = 5.0m
            });

            expected.Add(new PaymentLineItem
            {
                Name = "Loaded/Hooked Mileage",
                Quantity = 25.1m,
                Price = 0.01m,
                Taxable = true,
                HasFreeQuantity = true,
                DiscountQuantity = 5.0m
            });

            expected.Add(new PaymentLineItem
            {
                Name = "Tow/Hook Fee",
                Quantity = 1.0m,
                Price = 1.0m,
                Taxable = true,
                HasFreeQuantity = false
            });

            expected.Add(new PaymentLineItem
            {
                Name = "Credit Card Fee",
                Quantity = 1.0m,
                Price = 0.04m,
                Taxable = false,
                HasFreeQuantity = false
            });

            expected.Add(new PaymentLineItem
            {
                Name = "Fuel Surcharge",
                Quantity = 1.0M,
                Price = 1.0M,
                Taxable = false,
                HasFreeQuantity = false
            });

            Assert.Equal(expected.Count, actual.Count);
            Assert.Equal(expected[0].Taxable, actual[0].Taxable);
            Assert.Equal(expected[1].Taxable, actual[1].Taxable);
            Assert.Equal(expected[2].Taxable, actual[2].Taxable);
            Assert.Equal(expected[3].Taxable, actual[3].Taxable);
            Assert.Equal(expected[4].Taxable, actual[4].Taxable);

            Assert.Equal(expected[0].HasFreeQuantity, actual[0].HasFreeQuantity);
            Assert.Equal(expected[0].DiscountQuantity, actual[0].DiscountQuantity);

            Assert.Equal(expected[1].HasFreeQuantity, actual[1].HasFreeQuantity);
            Assert.Equal(expected[1].DiscountQuantity, actual[1].DiscountQuantity);


            var paymentDef = new PaymentDefinition
            {
                CompanyId = entry.CompanyId,
                Amount = entry.Invoice.GrandTotal,
                Currency = entry.Company.CurrencyCode,
                ReferenceId = "UT-AC-COMPLEX-1",
                Entry = entry,
                Tax = entry.Invoice.IsTaxExempt ? (decimal?)null : taxrate.Rate,
                DiscountRate = entry.Invoice.GetDiscountAsRate()
            };

            paymentDef.LineItems.AddRange(actual
                .Select(s =>
                {
                    return new PaymentLineItem()
                    {
                        Name = s.Name,
                        Quantity = s.Quantity,
                        Price = s.Price,
                        Taxable = s.Taxable,
                        MeasurementUnit = s.MeasurementUnit,
                        ReferenceId = s.ReferenceId,
                        Note = s.Note,
                        HasFreeQuantity = s.HasFreeQuantity,
                        DiscountQuantity = s.DiscountQuantity
                    };
                }));

            var authorization = await SquareUtils.GetAuthorizationAsync(0);

            var order = await SquareUtils.CreateOrder(0, paymentDef.Amount, paymentDef.LineItems, authorization.Location.LocationId, paymentDef.Tax, paymentDef.DiscountRate, "UtilFreeQtyWithTaxableAndNonTaxableItemsTest", Guid.NewGuid().ToString());
            

            Assert.Equal(paymentDef.Tax.GetValueOrDefault() * 100, (decimal)order.TotalTaxMoney.Amount);
            Assert.Equal(paymentDef.Amount * 100, (decimal)order.TotalMoney.Amount);
        }

        [IgnoreOnMissingCompany(104675)]
        /// https://github.com/towbook/Towbook/issues/3245
        /// CompanyId 104675, call# 4773.  
        /// Call includes free quantities, discount, FSC, and is marked tax exempt.
        /// This test will create a virtual entry using the rateitems from the db for companyId 104675.
        /// Therefore, this test can only be run with access to a db containing real pricing items.
        public async Task UtilComplexFreeQtyAndDiscountAndFSCAndTaxableExemptTest()
        {
            int unique = -1000;
            var entry = CreateVirtualEntry(true, false, false);

            var rateItems = RateItem.GetByCompanyId(104675);
            

            // unloaded
            var riUnloaded = rateItems.FirstOrDefault(w => w.RateItemId == 1756403);
            var riHookFee = rateItems.FirstOrDefault(w => w.RateItemId == 1626800);
            var riFSC = rateItems.FirstOrDefault(w => w.IsFuelSurchargeItem() || w.Predefined?.Id == PredefinedRateItem.BUILTIN_FUEL_SURCHARGE) ??
                new RateItem()
                {
                    Cost = 1.0M,
                    CompanyId = 104675,
                    RateType = RateItem.RateTypeEnum.FixedRate,
                    Measurement = RateItem.MeasurementEnum.Hours,
                    Taxable = false,
                    Predefined = await PredefinedRateItem.GetByIdAsync(PredefinedRateItem.BUILTIN_MILEAGE_LOADED)
                };
            var riDiscount = new RateItem()
                {
                    Cost = 0.0M,
                    CompanyId = 104675,
                    RateType = RateItem.RateTypeEnum.FixedRate,
                    Measurement = RateItem.MeasurementEnum.Hours,
                    Taxable = false,
                    Predefined = await PredefinedRateItem.GetByIdAsync(PredefinedRateItem.BUILTIN_DISCOUNT)
                };

            Assert.True(riUnloaded != null, "RateItemId 1756403 could not be found");
            Assert.True(riHookFee != null, "RateItem 1626800 could not be found");
            Assert.True(riFSC != null, "FSC could not be found for companyId 104675");

            var i1 = InvoiceItem.GetById(unique++);
            i1.CustomName = riUnloaded.Name ?? "Unloaded/Enroute Mileage";
            i1.Quantity = 3.0M;
            i1.CustomPrice = 2.0m;
            i1.Taxable = false;
            i1.RateItem = riUnloaded;
            i1.AssetId = -1000;

            var i2 = InvoiceItem.GetById(unique++);
            i2.CustomName = riHookFee.Name ?? "Hook-Up Fee";
            i2.Quantity = 1.0M;
            i2.CustomPrice = 80.0m;
            i2.Taxable = false;
            i2.RateItem = riHookFee;
            i2.AssetId = -1000;

            var i3 = InvoiceItem.GetById(unique++);
            i3.CustomName = "Fuel Surcharge";
            i3.Quantity = 1.0M;
            i3.CustomPrice = 1.2m;
            i3.Taxable = false;
            i3.RateItem = riFSC;
            i3.AssetId = -1000;

            var i4 = InvoiceItem.GetById(unique++);
            i4.CustomName = "Discount:25%";
            i4.Quantity = 1.0M;
            i4.CustomPrice = -20.0m;
            i4.Taxable = false;
            i4.RateItem = riDiscount;

            var i5 = InvoiceItem.GetById(unique--);
            i5.CustomName = $"FreeQuantity Credit {riUnloaded.RateItemId}";
            i5.CustomPrice = -2.0m;
            i5.Quantity = 3.0m;
            i5.Taxable = false;
            i5.RateItem = riUnloaded;
            i5.AssetId = -1000;
            i5.RelatedInvoiceItemId = i1.Id;

            entry.Invoice.InvoiceItems.Add(i1);
            entry.Invoice.InvoiceItems.Add(i2);
            entry.Invoice.InvoiceItems.Add(i3);
            entry.Invoice.InvoiceItems.Add(i4);
            entry.Invoice.InvoiceItems.Add(i5);

            // mark Invoice tax exempt
            entry.Invoice.IsTaxExempt = true;

            // get subtotals, tax, etc
            entry.Invoice.ForceRecalculate(false);

            Assert.Equal(61.2M, entry.InvoiceSubtotal);
            Assert.Equal(0.0M, entry.InvoiceTax);
            Assert.Equal(61.2M, entry.InvoiceTotal);

            List<PaymentLineItem> actual = SquareUtils.GetPaymentLineItems(entry.InvoiceItems.ToList());

            var expected = new List<PaymentLineItem>();

            expected.Add(new PaymentLineItem
            {
                Name = riUnloaded.Name ?? "Unloaded/Enroute Mileage",
                Quantity = 3.0m,
                Price = 2.0m,
                Taxable = false,
                HasFreeQuantity = true,
                DiscountQuantity = 3.0m
            });

            expected.Add(new PaymentLineItem
            {
                Name = riHookFee.Name ?? "Hook-Up Fee",
                Quantity = 1.0m,
                Price = 80.0m,
                Taxable = false,
                HasFreeQuantity = false
            });

            expected.Add(new PaymentLineItem
            {
                Name = "Fuel Surcharge",
                Quantity = 1.0m,
                Price = 1.2m,
                Taxable = false,
                HasFreeQuantity = false
            });

            Assert.Equal(expected.Count, actual.Count);
            Assert.Equal(expected[0].Taxable, actual[0].Taxable);
            Assert.Equal(expected[1].Taxable, actual[1].Taxable);
            Assert.Equal(expected[2].Taxable, actual[2].Taxable);
            
            Assert.Equal(expected[0].HasFreeQuantity, actual[0].HasFreeQuantity);
            Assert.Equal(expected[0].DiscountQuantity, actual[0].DiscountQuantity);

            var paymentDef = new PaymentDefinition
            {
                CompanyId = entry.CompanyId,
                Amount = entry.Invoice.GrandTotal,
                Currency = entry.Company.CurrencyCode,
                ReferenceId = "UT-COMPLEX-104675",
                Entry = entry,
                Tax = (decimal?)null,
                DiscountRate = entry.Invoice.GetDiscountAsRate()
            };

            paymentDef.LineItems.AddRange(actual
                .Select(s =>
                {
                    return new PaymentLineItem()
                    {
                        Name = s.Name,
                        Quantity = s.Quantity,
                        Price = s.Price,
                        Taxable = s.Taxable,
                        MeasurementUnit = s.MeasurementUnit,
                        ReferenceId = s.ReferenceId,
                        Note = s.Note,
                        HasFreeQuantity = s.HasFreeQuantity,
                        DiscountQuantity = s.DiscountQuantity
                    };
                }));

            var authorization = await SquareUtils.GetAuthorizationAsync(0);

            var order = await SquareUtils.CreateOrder(0, paymentDef.Amount, paymentDef.LineItems, authorization.Location.LocationId, paymentDef.Tax, paymentDef.DiscountRate, "UtilComplexFreeQtyAndDiscountAndFSCAndTaxableExemptTest", Guid.NewGuid().ToString());

            Assert.Equal((long)(paymentDef.Amount * 100), order.TotalMoney.Amount);
        }
    }
}
