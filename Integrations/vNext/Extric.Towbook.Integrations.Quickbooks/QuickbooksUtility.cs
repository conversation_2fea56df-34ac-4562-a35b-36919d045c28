using System;
using Intuit.Ipp.Core;
using Intuit.Ipp.DataService;
using Extric.Towbook.Integration;
using System.Text;
using System.IO;
using Intuit.Ipp.Exception;
using Intuit.Ipp.Core.Configuration;
using Intuit.Ipp.OAuth2PlatformClient;
using RestSharp;
using Newtonsoft.Json;
using Extric.Towbook.Utility;
using NLog;
using System.Threading.Tasks;
using Extric.Towbook.Configuration;
using RestSharp.Authenticators.OAuth2;
using System.Net.Http;


namespace Extric.Towbook.Integrations.Quickbooks
{
    public static class QuickbooksUtility
    {
        private static readonly NLog.Logger logger = LogManager.GetCurrentClassLogger();
        private static readonly HttpClient httpClient = new(new SocketsHttpHandler
        {
            PooledConnectionLifetime = TimeSpan.FromMinutes(5),
            PooledConnectionIdleTimeout = TimeSpan.FromSeconds(30)
        });

        private static readonly string URL_BASE = "https://oauth.platform.intuit.com";

        /// <summary>
        /// Returns an instance of the Quickbooks Connector for the requested company. Returns null if one doesn't exist for the company.
        /// </summary>
        /// <param name="companyId"></param>
        /// <returns></returns>
        public static async Task<IQuickbooksConnector> GetConnector(int companyId)
        {
            var so = CompanyKeyValue.GetFirstValueOrNull(companyId, Provider.QuickBooks.ProviderId, "DataSource");
            var at = CompanyKeyValue.GetFirstValueOrNull(companyId, Provider.QuickBooks.ProviderId, "AccessToken");
            var rt = CompanyKeyValue.GetFirstValueOrNull(companyId, Provider.QuickBooks.ProviderId, "RefreshToken");

            if (!string.IsNullOrEmpty(so) && 
                !string.IsNullOrWhiteSpace(at) && 
                !string.IsNullOrWhiteSpace(rt))
            {
                if (Core.GetRedisValue("qbutil:" + companyId) != null)
                {
                    return new QuickBooksOnlineConnector(companyId);
                }
                else
                {
                    if (await RefreshTokenAsync(companyId))
                    {
                        var connector = new QuickBooksOnlineConnector(companyId);
                        return connector;
                    }
                    else
                    {
                        return null;
                    }
                }
            }
            else
            {
                return null;
            }
        }

        public static string GetBlueDotMenuHtml(int companyId)
        {
            return "";
        }

        internal static DataService GetDataService(int companyId)
        {
            var realmId = CompanyKeyValue.GetFirstValueOrNull(companyId, Provider.QuickBooks.ProviderId, "RealmId");
            var accessToken = CompanyKeyValue.GetFirstValueOrNull(companyId, Provider.QuickBooks.ProviderId, "AccessToken");

            return GetDataService(realmId, accessToken);
        }

        public static OAuth2Client GetClient()
        {
            return new OAuth2Client(QuickbooksConfiguration.clientId.ToString(),
                QuickbooksConfiguration.clientSecret.ToString(),
                QuickbooksConfiguration.redirectUri.ToString(),
                QuickbooksConfiguration.environment.ToString());
        }

        public static bool Disconnect(int companyId, string reason)
        {
            if (RevokeByCompanyId(companyId))
            {
                var keys = new string[] {
                    "AccessToken", "RefreshToken", "RefreshTokenExpires",
                    "Token", "SecretToken", "RealmId", "DataSource"
                };


                foreach (var key in keys)
                {
                    var k = CompanyKeyValue.GetFirstValueOrNew(companyId, Provider.QuickBooks.ProviderId, key);

                    if (k.Id > 0)
                    {
                        k.Delete();
                    }
                }

                var log = new LogEventInfo();
                log.Message = "QuickBooks Automatically Disconnected";
                log.Level = LogLevel.Info;
                log.Properties["companyId"] = companyId;
                log.Properties["reason"] = reason;

                logger.Log(log);

                return true;
            }

            return false;
        }

        public static bool RevokeByCompanyId(int companyId)
        {
            var token = CompanyKeyValue.GetFirstValueOrNull(companyId, Provider.QuickBooks.ProviderId, "AccessToken");

            if (token == null)
                return false;

            var request = new RestRequest("/oauth2/v1/tokens/revoke", Method.Post);

            request.AddParameter("Authorization", "Basic " +
                Base64Encode(QuickbooksConfiguration.clientId.ToString() + ":" + QuickbooksConfiguration.clientSecret.ToString())
                , ParameterType.HttpHeader);

            var response = GetRestClient().Execute(request);

            return response.IsSuccessful;
        }

        public static string Base64Encode(string plainText)
        {
            var plainTextBytes = Encoding.UTF8.GetBytes(plainText);
            return Convert.ToBase64String(plainTextBytes);
        }


        public static RestClient GetRestClient(string accessToken = null)
        {
            var options = new RestClientOptions(URL_BASE);

            if (accessToken != null)
                options.Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator(accessToken, "Bearer");

            options.UserAgent = "Towbook/4.0.0";

            return new RestClient(httpClient, options);
        }

        public sealed class RefreshTokenResponse
        {
            public string token_type { get; set; }
            public int expires_in { get; set; }
            public string refresh_token { get; set; }
            public int x_refresh_token_expires_in { get; set; }
            public string access_token { get; set; }
        }


        public static void BearerFromCode(int companyId, string code)
        {
            var request = new RestRequest("/oauth2/v1/tokens/bearer", Method.Post);

            request.AddParameter("Authorization", "Basic " +
                Base64Encode(QuickbooksConfiguration.clientId.ToString() + ":" + QuickbooksConfiguration.clientSecret.ToString())
                , ParameterType.HttpHeader);
            request.AddHeader("Content-Type", "application/x-www-form-urlencoded");
            request.AddHeader("Accept", "application/json");
            request.AddParameter("grant_type", "authorization_code", ParameterType.GetOrPost);
            request.AddParameter("code", code, ParameterType.GetOrPost);
            request.AddParameter("redirect_uri", QuickbooksConfiguration.redirectUri.ToString(), ParameterType.GetOrPost);
            

            var response = GetRestClient().Execute(request);

            var robj = JsonConvert.DeserializeObject<RefreshTokenResponse>(response.Content);

            if (robj.access_token == null)
                throw new Exception(response.Content);

            CompanyKeyValue.InsertOrUpdate(companyId, Provider.QuickBooks.ProviderId, "AccessToken", robj.access_token);
            CompanyKeyValue.InsertOrUpdate(companyId, Provider.QuickBooks.ProviderId, "RefreshToken", robj.refresh_token);
            CompanyKeyValue.InsertOrUpdate(companyId, Provider.QuickBooks.ProviderId, "RefreshTokenExpires", robj.x_refresh_token_expires_in.ToString());

            Core.SetRedisValue("qbutil:" + companyId,
                DateTime.Now.AddSeconds(robj.expires_in).ToJson(),
                TimeSpan.FromMinutes(59));

            return;
        }

        public static async Task<bool> RefreshTokenAsync(int companyId)
        {
            try
            {
                using (var configLock = await Core.GetRedisDatabaseAsync().AcquireLockAsync("qbr:" + companyId))
                {
                    var existingRefreshToken = CompanyKeyValue.GetFirstValueOrNull(companyId, Provider.QuickBooks.ProviderId, "RefreshToken");
                    var realmId = CompanyKeyValue.GetFirstValueOrNull(companyId, Provider.QuickBooks.ProviderId, "RealmId");

                    var context = new
                    {
                        refreshToken = existingRefreshToken
                    };

                    var request = new RestRequest("/oauth2/v1/tokens/bearer", Method.Post);

                    if (QuickbooksConfiguration.clientId.ToString() == null)
                        throw new TowbookException("required app configuration is missing clientId.");

                    if (QuickbooksConfiguration.clientSecret.ToString() == null)
                        throw new TowbookException("required client secret is missing.");

                    
                    request.AddParameter("Authorization", "Basic " +
                        Base64Encode(QuickbooksConfiguration.clientId.ToString() + ":" + QuickbooksConfiguration.clientSecret.ToString())
                        , ParameterType.HttpHeader);

                    request.AddParameter("grant_type", "refresh_token", ParameterType.GetOrPost);
                    request.AddParameter("refresh_token", context.refreshToken, ParameterType.GetOrPost);

                    var response = GetRestClient().Execute(request);

                    var robj = JsonConvert.DeserializeObject<RefreshTokenResponse>(response.Content);

                    if (!string.IsNullOrWhiteSpace(robj.refresh_token) &&
                        !string.IsNullOrWhiteSpace(robj.access_token) && 
                        robj.x_refresh_token_expires_in != 0)
                    {
                        CompanyKeyValue.InsertOrUpdate(companyId, Provider.QuickBooks.ProviderId, "AccessToken", robj.access_token);
                        CompanyKeyValue.InsertOrUpdate(companyId, Provider.QuickBooks.ProviderId, "RefreshToken", robj.refresh_token);
                        CompanyKeyValue.InsertOrUpdate(companyId, Provider.QuickBooks.ProviderId, "RefreshTokenExpires", robj.x_refresh_token_expires_in.ToString());
                        
                        Core.SetRedisValue("qbutil:" + companyId, 
                            DateTime.Now.AddSeconds(robj.expires_in).ToJson(), 
                            TimeSpan.FromMinutes(59));

                        var log = new LogEventInfo();
                        log.Level = LogLevel.Info;
                        log.Message = "QuickBooks Online RefreshToken Succeeded";
                        log.Properties["type"] = "RefreshToken";
                        log.Properties["companyId"] = companyId;
                        log.Properties["companyName"] = (await Company.Company.GetByIdAsync(companyId))?.Name;
                        log.Properties["contractorId"] = realmId;
                        log.Properties["object"] = robj;
                        log.Properties["json"] = response.ToJson();

                        logger.Log(log);

                        return true;
                    }
                    else
                    {
                        var log = new LogEventInfo();

                        log.Level = LogLevel.Warn;
                        log.Message = "QuickBooks Online RefreshToken Failed";
                        log.Properties["type"] = "RefreshToken";
                        log.Properties["companyId"] = companyId;
                        log.Properties["companyName"] = (await Company.Company.GetByIdAsync(companyId))?.Name;
                        log.Properties["contractorId"] = realmId;
                        log.Properties["json"] = response.Content;

                        logger.Log(log);
                    }
                }
            }
            catch (Exception r)
            {
                logger.LogExceptionEvent("Quickbooks Online RefreshToken failed", r, companyId);
            }

            return false;
        }

        internal static ServiceContext GetServiceContext(int companyId)
        {
            var realmId = CompanyKeyValue.GetFirstValueOrNull(companyId, Provider.QuickBooks.ProviderId, "RealmId");
            var accessToken = CompanyKeyValue.GetFirstValueOrNull(companyId, Provider.QuickBooks.ProviderId, "AccessToken");

            var context = GetServiceContext(realmId, accessToken);

            string logDir = @"C:\windows\temp\Towbook-Intuit-Debug";
            if (Directory.Exists(logDir))
            {
                context.IppConfiguration.Logger.RequestLog.EnableRequestResponseLogging = true;
                context.IppConfiguration.Logger.RequestLog.ServiceRequestLoggingLocation = logDir;
            }

            context.IppConfiguration.Message.Request.SerializationFormat = Intuit.Ipp.Core.Configuration.SerializationFormat.Json;
            context.IppConfiguration.Message.Response.SerializationFormat = Intuit.Ipp.Core.Configuration.SerializationFormat.Json;

            return context;
        }

        public static string GetDataSource(int companyId)
        {
            var dataSource = CompanyKeyValue.GetFirstValueOrNull(companyId, Provider.QuickBooks.ProviderId, "DataSource");

            if (!string.IsNullOrWhiteSpace(dataSource))
                return dataSource;
            else
                return null;
        }

        /// <summary>
        /// This method is used to Intialize and return object of Data Services class.
        /// </summary>
        /// <returns></returns>
        private static DataService GetDataService(string realmId, string accessToken)
        {
            if (string.IsNullOrWhiteSpace(realmId) ||
                string.IsNullOrWhiteSpace(accessToken))
                throw new Exception("Invalid QuickBooks Connection");

            var sc = GetServiceContext(realmId, accessToken);

            // Intialize object of Data Services class.
            DataService commonService = new DataService(sc);

            // return object of Data Services class.
            return commonService;
        }

        private static ServiceContext GetServiceContext(string realmId, string accessToken)
        {
            var oauthValidator = QuickbooksInitializer.InitializeOAuthValidator(accessToken);
            var context = QuickbooksInitializer.InitializeServiceContext(oauthValidator, realmId);
            /*
            string logDir = @"C:\windows\temp\Towbook-Intuit-Debug";
            if (Directory.Exists(logDir))
            {
                context.IppConfiguration.Logger.RequestLog.EnableRequestResponseLogging = true;
                context.IppConfiguration.Logger.RequestLog.ServiceRequestLoggingLocation = logDir;
            }*/

            context.IppConfiguration.Message.Request.SerializationFormat = SerializationFormat.Json;
            context.IppConfiguration.Message.Response.SerializationFormat = SerializationFormat.Json;

            return context;
        }

        private static string[] charsToClean = new string[] { "/", "\\", ",", ";", ".", ":" };
        public static string CleanSpecialCharacters(this string s)
        {
            var sb = new StringBuilder(s);
            foreach (var c in charsToClean)
            {
                sb.Replace(c, " ");
            }
            return sb.ToString();
        }

        public static async Task<bool> VerifyConnector(int companyId)
        {
            var x = await GetConnector(companyId);

            if (x == null)
                return false;

            bool deleteConnection = false;
            try
            {
                var y = x.GetAccounts(1, 10);
                return true;
            }
            catch (IdsException ex)
            {
                if (ex.ErrorCode == "3200" || ex.Message == "Unauthorized-401")
                {
                    if (await RefreshTokenAsync(companyId))
                    {
                        try
                        {
                            var y = x.GetAccounts(1, 10);
                            return true;
                        }
                        catch
                        {

                            deleteConnection = true;
                            return false;
                        }
                    }
                    else
                    {
                        return false;
                    }
                }
                throw new Exception("errorCode == " + ex.ErrorCode, ex);
            }
            finally
            {
                if (deleteConnection)
                {
                    var keys = new string[] { "AccessToken", "SecretToken", "RealmId", "DataSource" };

                    foreach (var key in keys)
                    {
                        var k = CompanyKeyValue.GetFirstValueOrNew(companyId, Provider.QuickBooks.ProviderId, key);

                        if (k.Id > 0)
                        {
                            k.Delete();
                        }
                    }
                }
            }
        }

    }
}
