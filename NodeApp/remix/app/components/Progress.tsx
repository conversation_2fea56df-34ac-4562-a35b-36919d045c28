import { useNavigation, useFetchers } from "@remix-run/react";
import { useNProgress } from "@tanem/react-nprogress";
import ms from "ms";
import * as React from "react";
import { isClient } from "~/utils";

/**
 * Based on these two implementations:
 * - https://sergiodxa.com/articles/use-nprogress-in-a-remix-app
 * - https://github.com/tanem/react-nprogress/tree/master/examples/original-design
 */
export function Progress() {
  const [state, setState] = React.useState({
    isAnimating: false,
    key: 0,
  });

  const navigation = useNavigation();
  const fetchers = useFetchers();

  /**
   * This gets the state of every fetcher active on the app and combine it with
   * the state of the global transition (Link and Form), then use them to
   * determine if the app is idle or if it's loading.
   * Here we consider both loading and submitting as loading.
   */
  const navigationState = React.useMemo(
    function getGlobalState() {
      const states = [
        navigation.state,
        ...fetchers.map((fetcher) => fetcher.state),
      ];
      if (states.every((state) => state === "idle")) return "idle";
      return "loading";
    },
    [fetchers, navigation.state],
  );

  if (isClient()) {
    // eslint-disable-next-line
    React.useLayoutEffect(() => {
      // and when it's something else it means it's either submitting a form or
      // waiting for the loaders of the next location so we start it
      if (navigationState === "loading") {
        setState((prevState) => {
          return {
            isAnimating: true,
            key: prevState.isAnimating ? prevState.key : prevState.key ^ 1,
          };
        });
      }
      // when the state is idle then we can to complete the progress bar
      if (navigationState === "idle") {
        setState((prevState) => {
          return {
            isAnimating: false,
            key: prevState.isAnimating ? prevState.key : prevState.key ^ 1,
          };
        });
      }
    }, [navigationState]);
  }

  return <ProgressBar isAnimating={state.isAnimating} key={state.key} />;
}

function ProgressBar({ isAnimating }: { isAnimating: boolean }) {
  const { animationDuration, isFinished, progress } = useNProgress({
    isAnimating,
    animationDuration: ms("200ms"),
    minimum: 0,
  });

  return (
    <div
      className="pointer-events-none relative z-[1000]"
      style={{
        opacity: isFinished ? 0 : 1,
        transition: `all ${animationDuration}ms ease`,
        transform: `translate3d(${(-1 + progress) * 100}%,0,0)`,
      }}
    >
      <div
        className="fixed left-0 top-0 h-0.5 w-full border-t border-amber-10 bg-amber-9"
        style={{
          borderRadius: isFinished ? "0" : "0 2px 2px 0",
        }}
      />
    </div>
  );
}
