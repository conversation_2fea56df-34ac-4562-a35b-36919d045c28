import * as React from "react";
import {
  Button,
  ComboBox,
  Field,
  Icon,
  Input,
  TextAreaAutoGrow,
} from "@towbook/flatbed";
import { useQueryClient } from "@tanstack/react-query";
import { Towbook, useTowbook } from "@towbook/sdk";
import {
  getFormProps,
  getInputProps,
  getTextareaProps,
  useForm,
} from "@conform-to/react";
import { parseWithZod } from "@conform-to/zod";
import z from "zod";
import { faSpinnerThird } from "@fortawesome/pro-duotone-svg-icons";
import { useImpoundLots } from "../../../hooks/useImpoundLots";
import { useCompany } from "../../../hooks/useCompany";
import { useAuctions } from "../../../hooks/useAuctions";

const AuctionFormSchema = z.object({
  name: z.string(),
  location: z.string(),
  startDate: z.string(),
  startTime: z.string(),
  endDate: z.string(),
  endTime: z.string(),
  description: z.string().optional(),
});

export function FormAuction({
  auctionId,
  closeDialog,
  onSuccess,
}: {
  auctionId?: number;
  closeDialog: () => void;
  onSuccess?: (
    auction: Awaited<ReturnType<Towbook["auctions"]["create"]>>,
  ) => Promise<void>;
}) {
  const auctions = useAuctions();
  const auction = React.useMemo(() => {
    return auctions.data?.find((a) => a.id === auctionId);
  }, [auctionId, auctions.data]);
  const impoundLots = useImpoundLots();
  const company = useCompany();

  const queryClient = useQueryClient();

  const mode = !auctionId ? "create" : "update";

  const [isSubmitting, setIsSubmitting] = React.useState(false);

  const valueMap = React.useMemo(() => {
    return {
      impoundLots: Object.fromEntries(
        impoundLots.data?.map((impoundLot) => [
          impoundLot.name,
          impoundLot.id,
        ]) ?? [],
      ),
    };
  }, [impoundLots.data]);

  const towbook = useTowbook();
  const [form, fields] = useForm({
    defaultValue: {
      name: auction?.name,
      location: auction?.location?.id,
      startDate: auction?.startDate
        ? UTCtoCompanyLocal(
            auction?.startDate,
            company.data?.timezoneOffset,
            company.data?.timezoneUseDST,
          )
            ?.toISOString()
            .split("T")[0] || ""
        : "",
      startTime: auction?.startDate
        ? UTCtoCompanyLocal(
            auction?.startDate,
            company.data?.timezoneOffset,
            company.data?.timezoneUseDST,
          )
            ?.toISOString()
            .split("T")[1]
            .slice(0, 5) || ""
        : "",
      endDate: auction?.endDate
        ? UTCtoCompanyLocal(
            auction?.endDate,
            company.data?.timezoneOffset,
            company.data?.timezoneUseDST,
          )
            ?.toISOString()
            .split("T")[0] || ""
        : "",
      endTime: auction?.endDate
        ? UTCtoCompanyLocal(
            auction?.endDate,
            company.data?.timezoneOffset,
            company.data?.timezoneUseDST,
          )
            ?.toISOString()
            .split("T")[1]
            .slice(0, 5) || ""
        : "",
      description: auction?.description,
    },
    onValidate({ formData }) {
      const parseResult = parseWithZod(formData, { schema: AuctionFormSchema });

      return parseResult;
    },
    async onSubmit(event, context) {
      event.preventDefault();

      setIsSubmitting(true);

      const payload = {
        ...context.submission.value,
        location: {
          id: context.submission.value.location,
        },
        startDate: CompanyLocaltoUTC(
          {
            date: context.submission.value.startDate,
            time: context.submission.value.startTime,
          },
          company.data?.timezoneOffset,
          company.data?.timezoneUseDST,
        ).toISOString(),
        endDate: CompanyLocaltoUTC(
          {
            date: context.submission.value.endDate,
            time: context.submission.value.endTime,
          },
          company.data?.timezoneOffset,
          company.data?.timezoneUseDST,
        ).toISOString(),
      };

      let auctionResponse;

      try {
        switch (mode) {
          case "update": {
            auctionResponse = await towbook.auctions.update(auctionId, payload);
            break;
          }
          case "create": {
            auctionResponse = await towbook.auctions.create(payload);
            break;
          }
        }

        if (typeof onSuccess === "function") {
          await onSuccess(auctionResponse);
        }

        await queryClient.invalidateQueries({
          queryKey: ["auctions"],
        });

        closeDialog();
      } catch (error) {
        console.log(error);
      }

      setIsSubmitting(false);
    },
  });

  if (!impoundLots.data || (mode === "update" && !auction))
    return (
      <div className="flex items-center justify-center flex-col bg-slate-2 rounded-lg p-8 gap-2">
        <div>
          <Icon
            icon={faSpinnerThird}
            className="text-slate-9 w-10 h-10 animate-spin"
          />
        </div>
        <div className="font-medium text-slate-11 text-base">Loading...</div>
      </div>
    );

  return (
    <form {...getFormProps(form)}>
      <div className="grid grid-cols-12 gap-4 auto-rows-min">
        <Field.Root className="col-span-12">
          <Field.Label>Name</Field.Label>
          <Field.Control>
            <Input {...getInputProps(fields.name, { type: "text" })} />
          </Field.Control>
          <Field.Error>{fields.name.errors}</Field.Error>
        </Field.Root>
        <Field.Root className="col-span-12">
          <Field.Label>Location</Field.Label>
          <Field.Control>
            <ComboBox.Root
              label="Location"
              name="location"
              defaultSelectedKey={getInputProps(fields.location, {
                type: "text",
              }).defaultValue?.toString()}
            >
              {Object.entries(valueMap.impoundLots).map(([value, key]) => (
                <ComboBox.Item key={key.toString()} textValue={value}>
                  {value}
                </ComboBox.Item>
              ))}
            </ComboBox.Root>
          </Field.Control>
          <Field.Error>{fields.location.errors}</Field.Error>
        </Field.Root>
        <Field.Root className="col-span-6">
          <Field.Label>Start Date</Field.Label>
          <Field.Control>
            <Input {...getInputProps(fields.startDate, { type: "date" })} />
          </Field.Control>
          <Field.Error>{fields.startDate.errors}</Field.Error>
        </Field.Root>
        <Field.Root className="col-span-6">
          <Field.Label>Start Time</Field.Label>
          <Field.Control>
            <Input {...getInputProps(fields.startTime, { type: "time" })} />
          </Field.Control>
          <Field.Error>{fields.startTime.errors}</Field.Error>
        </Field.Root>
        <Field.Root className="col-span-6">
          <Field.Label>End Date</Field.Label>
          <Field.Control>
            <Input {...getInputProps(fields.endDate, { type: "date" })} />
          </Field.Control>
          <Field.Error>{fields.endDate.errors}</Field.Error>
        </Field.Root>
        <Field.Root className="col-span-6">
          <Field.Label>End Time</Field.Label>
          <Field.Control>
            <Input {...getInputProps(fields.endTime, { type: "time" })} />
          </Field.Control>
          <Field.Error>{fields.endTime.errors}</Field.Error>
        </Field.Root>
        <Field.Root className="col-span-12">
          <Field.Label>Description</Field.Label>
          <Field.Control>
            <TextAreaAutoGrow {...getTextareaProps(fields.description)} />
          </Field.Control>
          <Field.Error>{fields.description.errors}</Field.Error>
        </Field.Root>
      </div>
      <div className="flex justify-end items-center mt-6 gap-4">
        <Button type="submit" disabled={isSubmitting}>
          {mode === "create" && "Create Auction"}
          {mode === "update" && "Save Changes"}
        </Button>
      </div>
    </form>
  );
}

export function UTCtoCompanyLocal(
  datetime?: string,
  companyOffset: number = 0,
  companyUseDST: boolean = true,
) {
  if (!datetime) return null;

  if (!datetime.endsWith("Z")) throw new Error("datetime must be UTC");

  const date = new Date(datetime);

  const baseOffset = companyUseDST ? (isDST(date) ? -4 : -5) : -5;
  const offset = baseOffset + companyOffset;

  date.setTime(date.getTime() + offset * 60 * 60 * 1000);

  // return new Date(date.toISOString().replace("Z", ""));

  // console.log(companyOffset, companyUseDST);

  // console.log(
  //   `${date.toISOString().split("T")[0]}T${date
  //     .toISOString()
  //     .split("T")[1]
  //     .slice(0, 5)}`
  // );
  return new Date(
    `${date.toISOString().split("T")[0]}T${date
      .toISOString()
      .split("T")[1]
      .slice(0, 5)}Z`,
  );
}

export function CompanyLocaltoUTC(
  { date, time }: { date: string; time: string },
  companyOffset: number = 0,
  companyUseDST: boolean = true,
) {
  const baseOffset = companyUseDST
    ? isDST(new Date(`${date}T${time}:00Z`))
      ? -4
      : -5
    : -5;
  const offset = baseOffset + companyOffset;

  const datetime = new Date(`${date}T${time}:00Z`);

  datetime.setTime(datetime.getTime() - offset * 60 * 60 * 1000);

  return datetime;
  // return datetime.toISOString();
}

function isDST(d) {
  const jan = new Date(d.getFullYear(), 0, 1).getTimezoneOffset();
  const jul = new Date(d.getFullYear(), 6, 1).getTimezoneOffset();
  return Math.max(jan, jul) !== d.getTimezoneOffset();
}
