import * as React from "react";
import { z } from "zod";
import { AjaxHeader } from "../../components/AjaxHeader";
import { getFormProps, getInputProps, useForm } from "@conform-to/react";
import { parseWithZod } from "@conform-to/zod";
import {
  Button,
  Checkbox,
  Field,
  Icon,
  Table,
  Tabs,
} from "@towbook/flatbed";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useTowbook } from "@towbook/sdk";
import { faSpinnerThird } from "@fortawesome/pro-duotone-svg-icons";
import { IpAddressTable } from "./ApprovedIPsettings";
import { ILoginRestrictionSettings, ISettingsHistoryEntry } from "./Types";
import { HistoryTableControls } from "./HistoryTable";

declare global {
  interface Window {
    refetchComponentData?: () => void;
  }
}

const SettingsFormSchema = z
  .object({
    companyDomain: z.string().optional(),

    // User Access
    preventDriversFromSigningInOnWeb: z.boolean().optional(),
    preventDispatchersSigningInOnMobile: z.boolean().optional(),

    // IP Address Restrictions
    restrictManagersBasedOnIpAddress: z.boolean().optional(),
    restrictAccountantsBasedOnIpAddress: z.boolean().optional(),
    restrictDispatchersBasedOnIpAddress: z.boolean().optional(),
    restrictDriversBasedOnIpAddress: z.boolean().optional(),

    approvedIpAddresses: z.string().optional(), // it's an array but formdata needs to be a string
  })
  .superRefine((data, ctx) => {
    if (
      (data.restrictAccountantsBasedOnIpAddress ||
        data.restrictDispatchersBasedOnIpAddress ||
        data.restrictDriversBasedOnIpAddress ||
        data.restrictManagersBasedOnIpAddress) &&
      JSON.parse(data.approvedIpAddresses ?? "[]").length === 0
    ) {
      if (data.restrictAccountantsBasedOnIpAddress) {
        ctx.addIssue({
          path: ["restrictAccountantsBasedOnIpAddress"],
          message:
            "Please add at least one approved IP address before you turn on restrictions.",
          code: "custom",
        });
      }
      if (data.restrictDispatchersBasedOnIpAddress) {
        ctx.addIssue({
          path: ["restrictDispatchersBasedOnIpAddress"],
          message:
            "Please add at least one approved IP address before you turn on restrictions.",
          code: "custom",
        });
      }
      if (data.restrictDriversBasedOnIpAddress) {
        ctx.addIssue({
          path: ["restrictDriversBasedOnIpAddress"],
          message:
            "Please add at least one approved IP address before you turn on restrictions.",
          code: "custom",
        });
      }
      if (data.restrictManagersBasedOnIpAddress) {
        ctx.addIssue({
          path: ["restrictManagersBasedOnIpAddress"],
          message:
            "Please add at least one approved IP address before you turn on restrictions.",
          code: "custom",
        });
      }
      return false;
    }
    return true;
  });

export function LoginRestrictionSettings() {
  const towbook = useTowbook();
  const [pageNumber, setPageNumber] = React.useState(1);
  const [pageCount, setPageCount] = React.useState(1);

  const loginRestrictionSettings = useQuery({
    queryKey: ["loginRestrictionSettings"],
    queryFn: async () => {
      const data = await towbook.settings.securityTools.getSettings();
      return data;
    },
    staleTime: Infinity,
  });

  const settingsHistory = useQuery({
    queryKey: ["loginRestrictionSettingsHistory", pageNumber],
    queryFn: async () => {
      const pageSize = 15;
      const settings = await towbook.settings.securityTools.getSettingsHistory({
        page: pageNumber,
        pageSize,
      });
      setPageCount(Math.ceil(settings.totalCount / pageSize));
      return settings.items;
    },
    staleTime: Infinity,
  });

  React.useEffect(() => {
    // This effect is necessary to reload the page when returning to the page with the ajax tload() method
    window.refetchComponentData = () => {
      console.log("Security Tool Settings: Refetch Data");
      loginRestrictionSettings.refetch();
      settingsHistory.refetch();
    };
    return () => {
      window.refetchComponentData = undefined;
    };
  }, [loginRestrictionSettings, settingsHistory]);

  const queryClient = useQueryClient();

  const settingsMutation = useMutation({
    mutationFn: async (data: any) => {
      try {
        const result =
          await towbook.settings.securityTools.updateSettings(data);
        console.log("result", result);
        // @ts-expect-error swal must be included - replace once a react component is finalized
        swal({ text: "Successfully saved settings", type: "success" });
        settingsHistory.refetch();
        queryClient.invalidateQueries({ queryKey: ["loginRestrictionSettings"] });
        return result;
      } catch (e) {
        console.error("Error saving settings", e);
        // @ts-expect-error swal must be included - replace once a react component is finalized
        swal({
          text: "There was a problem saving your settings\n" + e,
          type: "error",
        });
      }
    },
  });

  return (
    <div>
      <AjaxHeader
        title="Login Restrictions"
        description="Configure various login restrictions for your company."
      />
      <div className="p-5">
        <Tabs.Root defaultValue="settings">
          <Tabs.List className="mf-4 flex-shrink-0 mb-5">
            <Tabs.Trigger value="settings">Settings</Tabs.Trigger>
            <Tabs.Trigger value="history">History</Tabs.Trigger>
          </Tabs.List>
          <Tabs.Content
            value="settings"
            forceMount
            className="state-inactive:hidden"
          >
            <div>
              {loginRestrictionSettings.data ? (
                <SettingsForm
                  data={loginRestrictionSettings.data}
                  submit={settingsMutation.mutate}
                  isPending={settingsMutation.isPending}
                  isError={settingsMutation.isError}
                  updateHistory={() => settingsHistory.refetch()}
                />
              ) : (
                <Loading />
              )}
            </div>
          </Tabs.Content>
          <Tabs.Content
            value="history"
            forceMount
            className="state-inactive:hidden"
          >
            <div className="bg-white rounded-lg shadow-md shadow-slateA-3 outline outline-1 outline-slateA-4 p-5">
              {settingsHistory.data ? (
                <SettingsHistoryTable data={settingsHistory.data} />
              ) : (
                <Loading />
              )}
              <HistoryTableControls
                totalPages={pageCount}
                pageNumber={pageNumber}
                setPageNumber={setPageNumber}
              />
            </div>
          </Tabs.Content>
        </Tabs.Root>
      </div>
    </div>
  );
}

type SettingsFormProps = {
  data: ILoginRestrictionSettings;
  submit: (data: ILoginRestrictionSettings) => void;
  isPending: boolean;
  isError: boolean;
  updateHistory: () => void;
};

function SettingsForm(props: SettingsFormProps) {
  const { data, submit, isPending, isError } = props;

  const [approvedIpAddresses, setApprovedIpAddresses] = React.useState(
    props.data.approvedIpAddresses || [],
  );
  const [
    form,
    {
      preventDriversFromSigningInOnWeb,
      preventDispatchersSigningInOnMobile,
      restrictManagersBasedOnIpAddress,
      restrictAccountantsBasedOnIpAddress,
      restrictDispatchersBasedOnIpAddress,
      restrictDriversBasedOnIpAddress,
    },
  ] = useForm<z.infer<typeof SettingsFormSchema>>({
    defaultValue: {
      preventDriversFromSigningInOnWeb: data.preventDriversFromSigningInOnWeb,
      preventDispatchersSigningInOnMobile:
        data.preventDispatchersSigningInOnMobile,
      restrictManagersBasedOnIpAddress: data.restrictManagersBasedOnIpAddress,
      restrictAccountantsBasedOnIpAddress:
        data.restrictAccountantsBasedOnIpAddress,
      restrictDispatchersBasedOnIpAddress:
        data.restrictDispatchersBasedOnIpAddress,
      restrictDriversBasedOnIpAddress: data.restrictDriversBasedOnIpAddress,
      approvedIpAddresses: "",
    },
    onValidate({ formData }) {
      console.log("On validate form data", formData);
      formData.append(
        "approvedIpAddresses",
        JSON.stringify(approvedIpAddresses),
      );
      const res = parseWithZod(formData, { schema: SettingsFormSchema });

      console.log("On validate ", res);
      return res;
    },
    onSubmit(event, context) {
      console.log("On submit event", event, context);
      event.preventDefault();
      const payload = context.submission?.payload;
      if (!payload) {
        console.warn("No payload found in submission", context.submission);
        return;
      }
      const submitData: ILoginRestrictionSettings = {
        id: data.id,
        companyId: data.companyId,
        preventDriversFromSigningInOnWeb:
          payload.preventDriversFromSigningInOnWeb === "on",
        preventDispatchersSigningInOnMobile:
          payload.preventDispatchersSigningInOnMobile === "on",
        restrictManagersBasedOnIpAddress:
          payload.restrictManagersBasedOnIpAddress === "on",
        restrictAccountantsBasedOnIpAddress:
          payload.restrictAccountantsBasedOnIpAddress === "on",
        restrictDispatchersBasedOnIpAddress:
          payload.restrictDispatchersBasedOnIpAddress === "on",
        restrictDriversBasedOnIpAddress:
          payload.restrictDriversBasedOnIpAddress === "on",
      } as any;
      submit(submitData);
    },
    shouldValidate: "onBlur",
    shouldRevalidate: "onInput",
  });

  /** When approved ip addresses are added or removed we want to
   *  revalidate the form to hide show the ip address warning message.
   */
  React.useEffect(() => {
    form.validate();
  }, [form, approvedIpAddresses]);

  return (
    <div className="grid grid-cols-2 gap-5">
      <form
        {...getFormProps(form)}
        className="bg-white rounded-lg shadow-md shadow-slateA-3 outline outline-1 outline-slateA-4"
      >
        <fieldset
          className="flex flex-col h-full"
          style={{
            padding: 0,
          }}
        >
          <div className="flex-1 px-8 py-6">
            <div className="grid ">
              <div id="ip-settings" className="mb-6">
                <h3 className="text-slate-12 font-medium text-lg p-0 mb-0.5">
                  IP Address Restrictions
                </h3>
                <div className="text-slate-11 mb-4">
                  When this option is enabled, the selected user types will only
                  be able to access Towbook from approved IP addresses for web
                  logins. This does not affect mobile logins.
                </div>
                <div className="grid grid-flow-row gap-1">
                  <Field.Root className="hidden">
                    <Field.Control>
                      <Checkbox.Root
                        {...getInputProps(restrictManagersBasedOnIpAddress, {
                          type: "checkbox",
                        })}
                        type="button" 
                      >
                        Managers
                      </Checkbox.Root>
                    </Field.Control>
                    <Field.Error>
                      {restrictManagersBasedOnIpAddress.errors}
                    </Field.Error>
                  </Field.Root>
                  <Field.Root>
                    <Field.Control>
                      <Checkbox.Root
                        {...getInputProps(restrictAccountantsBasedOnIpAddress, {
                          type: "checkbox",
                        })}
                        type="button" 
                      >
                        Accountants
                      </Checkbox.Root>
                    </Field.Control>
                    <Field.Error>
                      {restrictAccountantsBasedOnIpAddress.errors}
                    </Field.Error>
                  </Field.Root>
                  <Field.Root>
                    <Field.Control>
                      <Checkbox.Root
                        {...getInputProps(restrictDispatchersBasedOnIpAddress, {
                          type: "checkbox",
                        })}
                        type="button" 
                      >
                        Dispatchers
                      </Checkbox.Root>
                    </Field.Control>
                    <Field.Error>
                      {restrictDispatchersBasedOnIpAddress.errors}
                    </Field.Error>
                  </Field.Root>
                  <Field.Root>
                    <Field.Control>
                      <Checkbox.Root
                        {...getInputProps(restrictDriversBasedOnIpAddress, {
                          type: "checkbox",
                        })}
                        type="button" 
                      >
                        Drivers
                      </Checkbox.Root>
                    </Field.Control>
                    <Field.Error>
                      {restrictDriversBasedOnIpAddress.errors}
                    </Field.Error>
                  </Field.Root>
                </div>
              </div>

              <div id="user-access-settings">
                <h3 className="text-slate-12 font-medium text-lg p-0 mb-0.5">
                  User Access
                </h3>
                <div className="text-slate-11 mb-4">
                  When you enable these options, you can prevent your users from
                  logging in through either web or mobile depending on their
                  user type.
                </div>
                <div className="grid grid-flow-row gap-1">
                  <Field.Root>
                    <Field.Control>
                      <Checkbox.Root
                        {...getInputProps(preventDriversFromSigningInOnWeb, {
                          type: "checkbox",
                        })}
                        type="button" 
                      >
                        Prevent Drivers from Signing in on Web
                      </Checkbox.Root>
                    </Field.Control>
                    <Field.Error>
                      {preventDriversFromSigningInOnWeb.errors}
                    </Field.Error>
                  </Field.Root>
                  <Field.Root>
                    <Field.Control>
                      <Checkbox.Root
                        {...getInputProps(preventDispatchersSigningInOnMobile, {
                          type: "checkbox",
                        })}
                        type="button" 
                      >
                        Prevent Dispatchers from Signing in on Mobile
                      </Checkbox.Root>
                    </Field.Control>
                    <Field.Error>
                      {preventDispatchersSigningInOnMobile.errors}
                    </Field.Error>
                  </Field.Root>
                </div>
              </div>
            </div>
          </div>
          <div className="flex justify-end px-8 py-6 bg-slate-1 rounded-b-lg border-t border-slateA-3 gap-6 items-center">
            {isError && <span className="text-red-10">Error</span>}
            <Button type="submit" disabled={isPending}>
              {isPending ? (
                <div className="flex items-center gap-2">
                  <Icon icon={faSpinnerThird} spin />
                  Saving...
                </div>
              ) : (
                "Save Changes"
              )}
            </Button>
          </div>
        </fieldset>
      </form>
      <IpAddressTable
        ipAddresses={approvedIpAddresses}
        setIpAddresses={setApprovedIpAddresses}
        updateHistory={() => props.updateHistory()}
        securitySettings={data}
        currentIp={data.currentIpAddress}
      />
    </div>
  );
}

function SettingsHistoryTable(props: { data: ISettingsHistoryEntry[] }) {
  const { data } = props;
  return (
    <Table.Root className="w-full">
      <Table.Head>
        <Table.Row>
          <Table.HeadCell>Action</Table.HeadCell>
          <Table.HeadCell>User</Table.HeadCell>
          <Table.HeadCell>IP Address</Table.HeadCell>
          <Table.HeadCell>Date</Table.HeadCell>
        </Table.Row>
      </Table.Head>
      <Table.Body>
        {data.map((historyEntry: ISettingsHistoryEntry, i: number) => (
          <Table.Row key={i} zebra={i % 2 !== 0}>
            <Table.Cell>{historyEntry.description}</Table.Cell>
            <Table.Cell>{historyEntry.userFullName}</Table.Cell>
            <Table.Cell>{historyEntry.ipAddress}</Table.Cell>
            <Table.Cell>
              {new Date(historyEntry.createDate).toLocaleString()}
            </Table.Cell>
          </Table.Row>
        ))}
      </Table.Body>
    </Table.Root>
  );
}

export function Loading() {
  return (
    <div className="bg-white rounded-lg shadow-md shadow-slateA-3 outline outline-1 outline-slateA-4 p-12 flex flex-col items-center text-center">
      <Icon
        icon={faSpinnerThird}
        className="text-blue-10 w-12 h-12 mb-4"
        spin
      />
      <div className="text-xl font-medium mb-1">Loading...</div>
      <div className="text-base text-slate-11 mb-4 leading-snug">
        Please wait, this may take a moment.
      </div>
    </div>
  );
}
