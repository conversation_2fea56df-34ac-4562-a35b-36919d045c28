import React from "react";
import { useTowbook } from "@towbook/sdk";
import { Report } from "../components/Report";
import {
  formatters,
  formValuesToSearchParams,
  getInitialFilters,
  useFilterSetStorage,
  zodFormStringsToStringArray,
  zodGroupOptionCall,
  zodImpounds,
  zodRefineDateRange,
} from "../utils";
import { createColumnHelper } from "@tanstack/react-table";
import {
  Button,
  DataTable,
  DropdownMenu,
  ExtractObjectType,
  Icon,
  Tooltip,
} from "@towbook/flatbed";
import {
  DefaultError,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import { Parameters } from "../components/Parameters";
import { z } from "zod";
import { parseWithZod } from "@conform-to/zod";
import {
  FormProvider,
  getFormProps,
  getInputProps,
  useForm,
} from "@conform-to/react";
import ms from "ms";
import { useSearchParams } from "react-router-dom";
import { faFileExport } from "@fortawesome/pro-regular-svg-icons";
import { useDownload } from "../../../hooks/useDownload";
import {
  faLock,
  faUnlock,
  faUserCheck,
  faUserXmark,
} from "@fortawesome/pro-solid-svg-icons";
import { ButtonGroup } from "../components/ButtonGroup";

const reportType = "CancelledCall";
const reportName = "Cancelled Calls";
const reportFnName = "cancelledCall";
const allReportsQueryKey = ["reports", reportFnName];
const reportKey = "dispatching/cancelled-call";
const today = new Date();
const yesterday = new Date(today.getTime() - 1000 * 60 * 60 * 24);

const DEFAULT_FORM_VALUES: z.input<typeof CancelledCallParamsSchema> = {
  dateStart: yesterday.toISOString().split("T")[0],
  dateEnd: today.toISOString().split("T")[0],
  impounds: "0",
  groupOption: "CallVolume",
};

const CancelledCallParamsSchema = z
  .object({
    dateStart: z.string(),
    dateEnd: z.string(),
    companyId: zodFormStringsToStringArray.optional(),
    dispatchReasonId: z
      .string()
      .optional()
      .transform((id) => id || undefined),
    masterAccountId: z
      .string()
      .optional()
      .transform((id) => id || undefined),
    accountIds: zodFormStringsToStringArray.optional(),
    accountTypeIds: zodFormStringsToStringArray.optional(),
    driverId: z
      .string()
      .optional()
      .transform((id) => id || undefined),
    truckId: z
      .string()
      .optional()
      .transform((id) => id || undefined),
    performerUserId: z
      .string()
      .optional()
      .transform((id) => id || undefined),
    impounds: zodImpounds,
    groupOption: zodGroupOptionCall,
  })
  .refine(...zodRefineDateRange);

export default function CancelledCall() {
  const towbook = useTowbook();

  const [searchParams, setSearchParams] = useSearchParams();
  const [filterSets, setFilterSets] = useFilterSetStorage<
    {
      name: string;
      filters: z.input<typeof CancelledCallParamsSchema>;
    }[]
  >(reportType, reportName, reportKey);

  const [parameters, setParameters] = React.useState<z.infer<
    typeof CancelledCallParamsSchema
  > | null>(
    CancelledCallParamsSchema.nullable().parse(
      getInitialFilters(searchParams, filterSets),
    ),
  );
  const reportQueryKey = [...allReportsQueryKey, JSON.stringify(parameters)];

  const reportQuery = useQuery({
    queryKey: reportQueryKey,
    queryFn: () => {
      if (!parameters) {
        return null;
      }
      return towbook.reports[reportFnName](parameters);
    },
    enabled: Boolean(parameters),
    staleTime: ms("5m"),
  });

  const callMutation = useMutation<
    typeof reportQuery,
    DefaultError,
    { callId: number; action: "lock" | "unlock" | "audit" | "unaudit" },
    { previousData: typeof reportQuery.data }
  >({
    async mutationFn({ callId, action }) {
      return await towbook.calls[action](callId).then(() => reportQuery);
    },
    onMutate({ callId, action }) {
      queryClient.cancelQueries({
        queryKey: allReportsQueryKey,
      });
      const previousData = queryClient.getQueryData([
        "reports",
        reportFnName,
        JSON.stringify(parameters),
      ]) as typeof reportQuery.data;
      queryClient.setQueryData(reportQueryKey, {
        ...previousData,
        reportData: previousData?.reportData.map((call) =>
          call.dispatchEntryId === callId
            ? {
                ...call,
                isLocked: ["lock", "unlock"].includes(action)
                  ? action === "lock"
                  : call.isLocked,
                isAudited: ["audit", "unaudit"].includes(action)
                  ? action === "audit"
                  : call.isAudited,
              }
            : call,
        ),
      });
      return { previousData };
    },
    async onError(_, __, context) {
      queryClient.setQueryData(reportQueryKey, context?.previousData);
    },
    async onSettled() {
      await queryClient.invalidateQueries({
        queryKey: allReportsQueryKey,
      });
    },
  });

  const queryClient = useQueryClient();

  const [form, fields] = useForm<z.input<typeof CancelledCallParamsSchema>>({
    defaultValue: getInitialFilters(
      searchParams,
      filterSets,
      DEFAULT_FORM_VALUES,
    ),
    onValidate({ formData }) {
      return parseWithZod(formData, {
        schema: CancelledCallParamsSchema,
      });
    },
    onSubmit(event, { submission }) {
      event.preventDefault();
      if (submission?.payload) {
        const parsedPayload = CancelledCallParamsSchema.parse(
          submission.payload,
        );

        /**
         * If the parsed payload is different from the current parameters,
         * update the search params and the parameters.
         *
         * If the parameters are the same, we don't need to update the search params,
         * but we still need to invalidate the query to force a re-fetch.
         *
         * This is necessary because the `useQuery` hook will not re-run
         * if the parameters are the same as the previous parameters.
         */
        if (JSON.stringify(parsedPayload) !== JSON.stringify(parameters)) {
          setSearchParams(formValuesToSearchParams(parsedPayload));
          setParameters(parsedPayload);
        } else {
          queryClient.resetQueries({
            queryKey: ["reports", reportFnName, JSON.stringify(parsedPayload)],
          });
        }
      }
    },
  });

  const handleResetForm = React.useCallback(() => {
    form.update({
      value: DEFAULT_FORM_VALUES,
    });
    setParameters(null);
    setSearchParams(formValuesToSearchParams(DEFAULT_FORM_VALUES));
  }, [form, setSearchParams]);

  const columnHelper = React.useMemo(
    () =>
      createColumnHelper<
        ExtractObjectType<
          NonNullable<(typeof reportQuery)["data"]>["reportData"]
        >
      >(),
    [],
  );

  const columns = React.useMemo(
    () => [
      columnHelper.display({
        id: "actions",
        cell: ({ row }) => {
          const disabled = row.original.impound && !row.original.released;
          return (
            <div
              className="flex items-center gap-1 px-4"
              onClick={(event) => event.stopPropagation()}
            >
              <Tooltip.Provider>
                <Tooltip.Root>
                  <Tooltip.Trigger asChild>
                    <Button
                      size="small"
                      variant="text"
                      square
                      className={
                        row.original.isLocked
                          ? "text-red-9"
                          : "text-slate-7 hover:text-slate-9"
                      }
                      disabled={disabled}
                      onClick={() => {
                        callMutation.mutate({
                          callId: row.original.dispatchEntryId,
                          action: row.original.isLocked ? "unlock" : "lock",
                        });
                      }}
                    >
                      <Icon icon={row.original.isLocked ? faLock : faUnlock} />
                    </Button>
                  </Tooltip.Trigger>
                  <Tooltip.Content>
                    {disabled
                      ? "This call cannot be locked because it is an unreleased impound."
                      : row.original.isLocked
                        ? "Unlock call"
                        : "Lock call"}
                  </Tooltip.Content>
                </Tooltip.Root>
              </Tooltip.Provider>
              <Tooltip.Provider>
                <Tooltip.Root>
                  <Tooltip.Trigger asChild>
                    <Button
                      size="small"
                      variant="text"
                      square
                      className={
                        row.original.isAudited
                          ? "text-green-9"
                          : "text-slate-7 hover:text-slate-9"
                      }
                      onClick={() =>
                        callMutation.mutate({
                          callId: row.original.dispatchEntryId,
                          action: row.original.isAudited ? "unaudit" : "audit",
                        })
                      }
                    >
                      <Icon
                        icon={
                          row.original.isAudited ? faUserCheck : faUserXmark
                        }
                      />
                    </Button>
                  </Tooltip.Trigger>
                  <Tooltip.Content>
                    {row.original.isAudited ? "Unaudit call" : "Audit call"}
                  </Tooltip.Content>
                </Tooltip.Root>
              </Tooltip.Provider>
            </div>
          );
        },
        size: 85,
        enableGrouping: false,
        enableResizing: false,
        enablePinning: false,
        enableHiding: false,
        meta: {
          paddingY: false,
          paddingX: false,
        },
      }),
      columnHelper.accessor("callNumber", {
        header: "Call Number",
        cell: (props) => (
          <a
            className="text-blue-11 hover:text-blue-10 hover:no-underline cursor-pointer"
            onClick={() => editCall(props.row.original.dispatchEntryId)}
          >
            {props.getValue()}
          </a>
        ),
        size: 200,
      }),
      columnHelper.accessor("invoiceNumber", {
        header: "Invoice Number",
        size: 200,
      }),
      columnHelper.accessor("purchaseOrder", {
        header: "PO Number",
        size: 200,
      }),
      columnHelper.accessor(
        (rowData) => formatters.datetime(rowData.received),
        {
          header: "Date",
          size: 200,
        },
      ),
      columnHelper.accessor(
        (rowData) =>
          Math.round(
            (new Date(rowData.cancelDate).getTime() -
              new Date(rowData.received).getTime()) /
              60000,
          ) + " minutes",
        {
          header: "Time Elapsed",
          size: 200,
        },
      ),
      columnHelper.accessor(
        (rowData) => formatters.datetime(rowData.cancelDate),
        {
          header: "Cancel Date",
          size: 200,
        },
      ),
      columnHelper.accessor("canceledBy", {
        header: "Performed By",
        size: 200,
      }),
      columnHelper.accessor("driver", {
        header: "Driver",
      }),
      columnHelper.accessor("truck", {
        header: "Truck",
        size: 200,
      }),
      columnHelper.accessor("accountName", {
        header: "Account",
      }),
      columnHelper.accessor("reason", {
        header: "Reason",
      }),
      columnHelper.accessor("cancelReason", {
        header: "Cancel Reason",
        size: 200,
      }),
      columnHelper.accessor("photosCount", {
        header: "Photos",
        cell: (props) => (
          <a
            className="text-blue-11 hover:text-blue-10 hover:no-underline cursor-pointer"
            href={`/ajax/dispatch/${props.row.original.dispatchEntryId}/photos`}
            target="_blank"
            rel="noreferrer noopener"
          >
            {props.getValue()}
          </a>
        ),
      }),
      columnHelper.accessor("invoiceTotal", {
        header: "Total",
      }),
    ],
    [callMutation, columnHelper],
  );

  const download = useDownload();

  const getExport = React.useCallback(
    (exportType?: string, shouldUseFormValues = false) => {
      download.downloadFile(
        `${reportType}.xlsx`,
        fetch(`${towbook.options.api}/reports?format=xlsx&export=1`, {
          method: "POST",
          body: JSON.stringify({
            ...(shouldUseFormValues
              ? CancelledCallParamsSchema.parse(form.value)
              : parameters),
            reportType,
            ...(exportType && { customExport: exportType }),
          }),
        }),
      );
    },
    [download, parameters, towbook.options.api],
  );

  return (
    <form
      {...getFormProps(form)}
      onBlur={() => form.validate()}
      className="flex overflow-auto"
    >
      <FormProvider context={form.context}>
        <Report.Root>
          <Report.Header reportKey={reportKey}>{reportName}</Report.Header>
          <Report.Parameters
            resetButton={
              <Button
                variant="light"
                onClick={() => {
                  handleResetForm();
                }}
              >
                Reset
              </Button>
            }
            updateOptions={
              <ExportOptions getExport={getExport} shouldUseFormValues />
            }
            filterSets={filterSets}
            loadFilterSet={(filters, submitForm) => {
              form.update({ value: filters });
              setParameters(CancelledCallParamsSchema.parse(filters));
              setSearchParams(formValuesToSearchParams(filters));
              submitForm?.();
            }}
            saveFilterSet={(setName) =>
              setFilterSets((oldPresets) => [
                ...oldPresets,
                {
                  name: setName,
                  filters: form.value as z.infer<
                    typeof CancelledCallParamsSchema
                  >,
                },
              ])
            }
            renameFilterSet={(setName, newName) =>
              setFilterSets((oldPresets) => {
                const presetIndex = oldPresets.findIndex(
                  (p) => p.name === setName,
                );
                return [
                  ...oldPresets.slice(0, presetIndex),
                  { ...oldPresets[presetIndex], name: newName },
                  ...oldPresets.slice(presetIndex + 1),
                ];
              })
            }
            deleteFilterSet={(setName) =>
              setFilterSets((oldPresets) =>
                oldPresets.filter((p) => p.name !== setName),
              )
            }
          >
            <input {...getInputProps(fields.impounds, { type: "hidden" })} />
            <Parameters.DateRange
              startName={fields.dateStart.name}
              endName={fields.dateEnd.name}
            />
            <Parameters.Company name={fields.companyId.name} />
            <Parameters.Reason name={fields.dispatchReasonId.name} />
            <Parameters.MasterAccount name={fields.masterAccountId.name} />
            <Parameters.Accounts name={fields.accountIds.name} />
            <Parameters.AccountTypes name={fields.accountTypeIds.name} />
            <Parameters.Driver name={fields.driverId.name} />
            <Parameters.Truck name={fields.truckId.name} />
            <Parameters.User
              name={fields.performerUserId.name}
              label="Performed By"
            />
          </Report.Parameters>
          <Report.Content>
            <DataTable.Provider
              data={reportQuery.data?.reportData}
              columns={columns}
              options={{
                initialState: {
                  columnPinning: {
                    left: ["checkbox", "actions"],
                  },
                },
              }}
            >
              <Report.QueryState query={reportQuery}>
                <Report.Summary>
                  {Object.entries(reportQuery.data?.summary || {}).map(
                    ([key, value]) => (
                      <Report.SummaryItem key={key} name={key} value={value} />
                    ),
                  )}
                </Report.Summary>
                <Report.ChartSection className="col-span-2">
                  <Report.ChartOptions>
                    <ButtonGroup
                      {...getInputProps(fields.groupOption, { type: "radio" })}
                      options={[
                        { value: "CallVolume", label: "Volume" },
                        { value: "Revenue", label: "Revenue" },
                      ]}
                      buttonType="submit"
                    />
                  </Report.ChartOptions>
                  <Report.Chart
                    dateGroupArray={reportQuery.data?.dateGroupArray}
                    valueGroupArray={reportQuery.data?.valueGroupArray}
                    yAxisLabel={reportQuery.data?.yAxisLabel}
                  />
                </Report.ChartSection>
                <Report.Table>
                  <DataTable.Root className="flex-1">
                    <DataTable.Toolbar>
                      <DataTable.Search />
                      <DataTable.Actions>
                        <DataTable.Action.Reset />
                        <DataTable.Action.Columns />
                        <DropdownMenu.Root>
                          <DropdownMenu.Trigger asChild>
                            <Button variant="light">
                              <Icon
                                icon={faFileExport}
                                className="text-slate-9 mr-2"
                              />
                              Export
                            </Button>
                          </DropdownMenu.Trigger>
                          <DropdownMenu.Content>
                            <ExportOptions getExport={getExport} />
                          </DropdownMenu.Content>
                        </DropdownMenu.Root>

                        <DataTable.Action.Fullscreen />
                      </DataTable.Actions>
                    </DataTable.Toolbar>
                    <DataTable.Filters />
                    <DataTable.Table>
                      <DataTable.TableHeader />
                      <DataTable.TableBody>
                        {({ rows }) =>
                          rows.map(({ row, virtualItem }) => (
                            <Report.CallRow
                              key={row.id}
                              row={row}
                              virtualItem={virtualItem}
                            />
                          ))
                        }
                      </DataTable.TableBody>
                      <DataTable.TableFooter />
                    </DataTable.Table>
                  </DataTable.Root>
                </Report.Table>
              </Report.QueryState>
            </DataTable.Provider>
          </Report.Content>
        </Report.Root>
      </FormProvider>
    </form>
  );
}

function ExportOptions({
  getExport,
  shouldUseFormValues = false,
}: {
  getExport: (exportType?: string, shouldUseFormValues?: boolean) => void;
  shouldUseFormValues?: boolean;
}) {
  return (
    <DropdownMenu.Item
      onSelect={() => getExport(undefined, shouldUseFormValues)}
    >
      Export to Excel
    </DropdownMenu.Item>
  );
}
