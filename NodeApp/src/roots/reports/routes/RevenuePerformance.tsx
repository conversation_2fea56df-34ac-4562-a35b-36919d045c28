import React from "react";
import { useTowbook } from "@towbook/sdk";
import { Report } from "../components/Report";
import {
  formatters,
  formValuesToSearchParams,
  getInitialFilters,
  useFilterSetStorage,
  zodFormStringsToStringArray,
  zodDateOptions,
  zodGroupOptionPayment,
  zodImpounds,
  zodRefineDateRange,
} from "../utils";
import { createColumnHelper } from "@tanstack/react-table";
import {
  Button,
  DataTable,
  DropdownMenu,
  ExtractObjectType,
  Icon,
} from "@towbook/flatbed";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Parameters } from "../components/Parameters";
import { z } from "zod";
import { parseWithZod } from "@conform-to/zod";
import {
  FormProvider,
  getFormProps,
  getInputProps,
  useForm,
} from "@conform-to/react";
import ms from "ms";
import { useSearchParams } from "react-router-dom";
import { faFileExport } from "@fortawesome/pro-regular-svg-icons";
import { useDownload } from "../../../hooks/useDownload";
import { ButtonGroup } from "../components/ButtonGroup";

const reportType = "RevenuePerformance";
const reportName = "Daily Activity";
const reportFnName = "revenuePerformance";
const allReportsQueryKey = ["reports", reportFnName];
const reportKey = "dispatching/daily-activity";
const today = new Date();

const DEFAULT_FORM_VALUES: z.input<typeof RevenuePerformanceParamsSchema> = {
  dateStart: today.toISOString().split("T")[0] + "T00:00:00",
  dateEnd: today.toISOString().split("T")[0] + "T23:59:59",
  impounds: "0",
  groupOption: "PaymentType",
  dateGroupType: "Hour",
};

const RevenuePerformanceParamsSchema = z
  .object({
    dateStart: z.string(),
    dateEnd: z.string(),
    companyId: zodFormStringsToStringArray.optional(),
    accountId: z
      .string()
      .optional()
      .transform((id) => id || undefined),
    impounds: zodImpounds,
    groupOption: zodGroupOptionPayment,
    dateGroupType: zodDateOptions,
  })
  .refine(...zodRefineDateRange);

export default function RevenuePerformance() {
  const towbook = useTowbook();

  const [searchParams, setSearchParams] = useSearchParams();
  const [filterSets, setFilterSets] = useFilterSetStorage<
    {
      name: string;
      filters: z.input<typeof RevenuePerformanceParamsSchema>;
    }[]
  >(reportType, reportName, reportKey);

  const [parameters, setParameters] = React.useState<z.infer<
    typeof RevenuePerformanceParamsSchema
  > | null>(
    RevenuePerformanceParamsSchema.nullable().parse(
      getInitialFilters(searchParams, filterSets),
    ),
  );
  const reportQueryKey = [...allReportsQueryKey, JSON.stringify(parameters)];

  const reportQuery = useQuery({
    queryKey: reportQueryKey,
    queryFn: () => {
      if (!parameters) {
        return null;
      }
      return towbook.reports[reportFnName](parameters);
    },
    enabled: Boolean(parameters),
    staleTime: ms("5m"),
  });

  const queryClient = useQueryClient();

  const [form, fields] = useForm<
    z.input<typeof RevenuePerformanceParamsSchema>
  >({
    defaultValue: getInitialFilters(
      searchParams,
      filterSets,
      DEFAULT_FORM_VALUES,
    ),
    onValidate({ formData }) {
      return parseWithZod(formData, {
        schema: RevenuePerformanceParamsSchema,
      });
    },
    onSubmit(event, { submission }) {
      event.preventDefault();
      if (submission?.payload) {
        const parsedPayload = RevenuePerformanceParamsSchema.parse(
          submission.payload,
        );

        /**
         * If the parsed payload is different from the current parameters,
         * update the search params and the parameters.
         *
         * If the parameters are the same, we don't need to update the search params,
         * but we still need to invalidate the query to force a re-fetch.
         *
         * This is necessary because the `useQuery` hook will not re-run
         * if the parameters are the same as the previous parameters.
         */
        if (JSON.stringify(parsedPayload) !== JSON.stringify(parameters)) {
          setSearchParams(formValuesToSearchParams(parsedPayload));
          setParameters(parsedPayload);
        } else {
          queryClient.resetQueries({
            queryKey: ["reports", reportFnName, JSON.stringify(parsedPayload)],
          });
        }
      }
    },
  });

  const handleResetForm = React.useCallback(() => {
    form.update({
      value: DEFAULT_FORM_VALUES,
    });
    setParameters(null);
    setSearchParams(formValuesToSearchParams(DEFAULT_FORM_VALUES));
  }, [form, setSearchParams]);

  const columnHelper = React.useMemo(
    () =>
      createColumnHelper<
        ExtractObjectType<
          NonNullable<(typeof reportQuery)["data"]>["reportData"]
        >
      >(),
    [],
  );

  const columns = React.useMemo(
    () => [
      columnHelper.accessor("callNumber", {
        header: "Call Number",
        size: 200,
      }),
      columnHelper.accessor("invoiceNumber", {
        header: "Invoice Number",
        size: 200,
      }),
      columnHelper.accessor("accountName", { header: "Account" }),
      columnHelper.accessor(
        (rowData) => formatters.currency(rowData.invoiceTotal),
        {
          header: "Total",
        },
      ),
      columnHelper.accessor(
        (rowData) => formatters.currency(rowData.balanceDue),
        {
          header: "Balance",
        },
      ),
      columnHelper.accessor(
        (rowData) => formatters.currency(rowData.revenueAmount || 0),
        {
          header: "Revenue Amount",
          size: 250,
        },
      ),
      columnHelper.accessor(
        (rowData) => formatters.currency(rowData.paymentAmount || 0),
        {
          header: "Payment Amount",
          size: 250,
        },
      ),
      columnHelper.accessor("paymentType", {
        header: "Payment Type",
        size: 200,
      }),
      columnHelper.accessor(
        (rowData) => formatters.date(rowData.paymentDate || ""),
        {
          header: "Payment Date",
          size: 200,
        },
      ),
      columnHelper.accessor((rowData) => formatters.date(rowData.createDate), {
        header: "Job Date",
        size: 200,
      }),
    ],
    [columnHelper],
  );

  const download = useDownload();

  const getExport = React.useCallback(
    (exportType?: string, shouldUseFormValues = false) => {
      download.downloadFile(
        `${reportType}.xlsx`,
        fetch(`${towbook.options.api}/reports?format=xlsx&export=1`, {
          method: "POST",
          body: JSON.stringify({
            ...(shouldUseFormValues
              ? RevenuePerformanceParamsSchema.parse(form.value)
              : parameters),
            reportType,
            ...(exportType && { customExport: exportType }),
          }),
        }),
      );
    },
    [download, parameters, towbook.options.api],
  );

  return (
    <form
      {...getFormProps(form)}
      onBlur={() => form.validate()}
      className="flex overflow-auto"
    >
      <FormProvider context={form.context}>
        <Report.Root>
          <Report.Header reportKey={reportKey}>{reportName}</Report.Header>
          <Report.Parameters
            resetButton={
              <Button
                variant="light"
                onClick={() => {
                  handleResetForm();
                }}
              >
                Reset
              </Button>
            }
            updateOptions={
              <ExportOptions getExport={getExport} shouldUseFormValues />
            }
            filterSets={filterSets}
            loadFilterSet={(filters, submitForm) => {
              form.update({ value: filters });
              setParameters(RevenuePerformanceParamsSchema.parse(filters));
              setSearchParams(formValuesToSearchParams(filters));
              submitForm?.();
            }}
            saveFilterSet={(setName) =>
              setFilterSets((oldPresets) => [
                ...oldPresets,
                {
                  name: setName,
                  filters: form.value as z.infer<
                    typeof RevenuePerformanceParamsSchema
                  >,
                },
              ])
            }
            renameFilterSet={(setName, newName) =>
              setFilterSets((oldPresets) => {
                const presetIndex = oldPresets.findIndex(
                  (p) => p.name === setName,
                );
                return [
                  ...oldPresets.slice(0, presetIndex),
                  { ...oldPresets[presetIndex], name: newName },
                  ...oldPresets.slice(presetIndex + 1),
                ];
              })
            }
            deleteFilterSet={(setName) =>
              setFilterSets((oldPresets) =>
                oldPresets.filter((p) => p.name !== setName),
              )
            }
          >
            <input {...getInputProps(fields.impounds, { type: "hidden" })} />
            <Parameters.DateRange
              startName={fields.dateStart.name}
              endName={fields.dateEnd.name}
              granularity="minute"
            />
            <Parameters.Company name={fields.companyId.name} />
            <Parameters.Account name={fields.accountId.name} />
          </Report.Parameters>
          <Report.Content>
            <DataTable.Provider
              data={reportQuery.data?.reportData}
              columns={columns}
            >
              <Report.QueryState query={reportQuery}>
                <Report.Summary>
                  {Object.entries(reportQuery.data?.summary || {}).map(
                    ([key, value]) => (
                      <Report.SummaryItem key={key} name={key} value={value} />
                    ),
                  )}
                </Report.Summary>
                <Report.ChartSection className="col-span-2">
                  <Report.ChartOptions>
                    <ButtonGroup
                      {...getInputProps(fields.groupOption, { type: "radio" })}
                      options={[
                        {
                          value: "PaymentType",
                          label: "Income by Payment Type",
                        },
                        { value: "Account", label: "Income by Account" },
                      ]}
                      buttonType="submit"
                    />
                    <ButtonGroup
                      {...getInputProps(fields.dateGroupType, {
                        type: "radio",
                      })}
                      options={[
                        { value: "Hour", label: "Hour" },
                        { value: "Day", label: "Day" },
                        { value: "Week", label: "Week" },
                        { value: "Month", label: "Month" },
                        { value: "Year", label: "Year" },
                      ]}
                      buttonType="submit"
                    />
                  </Report.ChartOptions>
                  <Report.Chart
                    dateGroupArray={reportQuery.data?.dateGroupArray}
                    valueGroupArray={reportQuery.data?.valueGroupArray}
                    yAxisLabel={reportQuery.data?.yAxisLabel}
                    type={
                      parameters?.groupOption === "PaymentType" ||
                      parameters?.dateGroupType === "Hour"
                        ? "bar"
                        : "line"
                    }
                    getYLabel={(value) => formatters.currency(value)}
                  />
                </Report.ChartSection>
                <Report.Table>
                  <DataTable.Root className="flex-1">
                    <DataTable.Toolbar>
                      <DataTable.Search />
                      <DataTable.Actions>
                        <DataTable.Action.Reset />
                        <DataTable.Action.Columns />
                        <DropdownMenu.Root>
                          <DropdownMenu.Trigger asChild>
                            <Button variant="light">
                              <Icon
                                icon={faFileExport}
                                className="text-slate-9 mr-2"
                              />
                              Export
                            </Button>
                          </DropdownMenu.Trigger>
                          <DropdownMenu.Content>
                            <ExportOptions getExport={getExport} />
                          </DropdownMenu.Content>
                        </DropdownMenu.Root>

                        <DataTable.Action.Fullscreen />
                      </DataTable.Actions>
                    </DataTable.Toolbar>
                    <DataTable.Filters />
                    <DataTable.Table>
                      <DataTable.TableHeader />
                      <DataTable.TableBody>
                        {({ rows }) =>
                          rows.map(({ row, virtualItem }) => (
                            <DataTable.TableBodyRow
                              key={row.id}
                              row={row}
                              virtualItem={virtualItem}
                            />
                          ))
                        }
                      </DataTable.TableBody>
                      <DataTable.TableFooter />
                    </DataTable.Table>
                  </DataTable.Root>
                </Report.Table>
              </Report.QueryState>
            </DataTable.Provider>
          </Report.Content>
        </Report.Root>
      </FormProvider>
    </form>
  );
}

function ExportOptions({
  getExport,
  shouldUseFormValues = false,
}: {
  getExport: (exportType?: string, shouldUseFormValues?: boolean) => void;
  shouldUseFormValues?: boolean;
}) {
  return (
    <DropdownMenu.Item
      onSelect={() => getExport(undefined, shouldUseFormValues)}
    >
      Export to Excel
    </DropdownMenu.Item>
  );
}
