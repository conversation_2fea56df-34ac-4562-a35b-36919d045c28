using Extric.Towbook;
using Extric.Towbook.Accounts;
using Extric.Towbook.Dispatch;
using Extric.Towbook.Integration;
using Extric.Towbook.Utility;
using NLog;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Roadside
{
    [Table("Roadside.DispatchMessages")]
    public class RoadsideDispatchMessage
    {
        [Key("RoadsideDispatchMessageId")]
        public int Id { get; set; }
        public int RoadsideDispatchId { get; set; }
        public int RoadsideDispatchUserId { get; set; }
        public string Message { get; set; }
        public int? CallStatus { get; set; }
        public int? JobProgressTextAlertItemId { get; set; }
        public DateTime CreateDate { get; set; }
        public DateTime? ReadDate { get; set; }
        public DateTime? LastActivityDate { get; set; }

        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        public RoadsideDispatchMessage()
        {

        }

        public RoadsideDispatchMessage(SqlDataReader reader)
        {
            InitializeFromDataReader(reader);
        }

        private void InitializeFromDataReader(SqlDataReader reader)
        {
            Id = reader.GetValue<int>("RoadsideDispatchMessageId");
            RoadsideDispatchId = reader.GetValue<int>("RoadsideDispatchId");
            RoadsideDispatchUserId = reader.GetValue<int>("RoadsideDispatchUserId");
            Message = reader.GetValue<string>("Message");
            CallStatus = reader.GetValueOrNull<int>("CallStatus");
            JobProgressTextAlertItemId = reader.GetValueOrNull<int>("JobProgressTextAlertItemId");
            CreateDate = reader.GetValue<DateTime>("CreateDate");

            ReadDate = reader.GetValueOrNull<DateTime>("ReadDate");
            LastActivityDate = reader.GetValueOrNull<DateTime>("LastActivityDate");
        }

        public static RoadsideDispatchMessage GetById(int id)
        {
            return SqlMapper.Query<RoadsideDispatchMessage>(
                "SELECT * FROM Roadside.DispatchMessages WHERE RoadsideDispatchMessageId = @Id",
                new
                {
                    Id = id
                }).FirstOrDefault();
        }

        public static async Task<RoadsideDispatchMessage> GetMessageByStatusIdAsync(int roadsideDispatchUserId, int callStatus)
        {
            using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                "Roadside.DispatchMessageGetByStatusId",
                new SqlParameter("@RoadsideDispatchUserId", roadsideDispatchUserId), 
                new SqlParameter("@CallStatus", callStatus)))
            {
                if (await dr.ReadAsync())
                    return new RoadsideDispatchMessage(dr);
                else
                    return null;
            }
        }

        public static RoadsideDispatchMessage GetSurveyMessage(int roadsideDispatchId, int roadsideUserId)
        {
            var msg = SqlMapper.Query<RoadsideDispatchMessage>(
                "SELECT * FROM Roadside.DispatchMessages WHERE RoadsideDispatchId = @DispatchId AND RoadsideDispatchUserId=@UserId AND CallStatus=5",
                new
                {
                    DispatchId = roadsideDispatchId,
                    UserId = roadsideUserId
                }).FirstOrDefault();

            if (msg != null && msg.Message.Contains("roadside.io"))
                return msg;

            return null;
        }

        public static RoadsideDispatchMessage GetMessageByJobProgressTextAlertItemId(int roadsideDispatchUserId, int itemId, int[] callStatusIds)
        {
            return SqlMapper.Query<RoadsideDispatchMessage>(
                "SELECT * FROM Roadside.DispatchMessages WHERE RoadsideDispatchUserId = @UserId AND Coalesce(JobProgressTextAlertItemId,0)=@ItemId AND CallStatus IN @StatusIds",
                new {
                    UserId = roadsideDispatchUserId,
                    ItemId = itemId,
                    StatusIds = callStatusIds
                }).FirstOrDefault();
        }


        public static IEnumerable<RoadsideDispatchMessage> GetMessageByJobProgressTextAlertItemIds(int[] roadsideDispatchUserIds, int[] itemIds)
        {
            if (roadsideDispatchUserIds == null || roadsideDispatchUserIds.Length == 0)
                return new RoadsideDispatchMessage[0];

            return SqlMapper.Query<RoadsideDispatchMessage>(
                "SELECT * FROM Roadside.DispatchMessages WHERE RoadsideDispatchUserId IN @UserIds AND Coalesce(JobProgressTextAlertItemId,0) IN @ItemIds",
                new
                {
                    UserIds = roadsideDispatchUserIds,
                    ItemIds = itemIds,
                });
        }



        public static async Task<Collection<RoadsideDispatchMessage>> GetMessagesByDispatchUserIdAsync(int roadsideDispatchUserId, int? callStatus = null)
        {
            Collection<RoadsideDispatchMessage> messages = new Collection<RoadsideDispatchMessage>();

            using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                "Roadside.DispatchMessagesGetByDispatchUserId",
                new SqlParameter("@RoadsideDispatchUserId", roadsideDispatchUserId)))
            {

                while (await dr.ReadAsync())
                {
                    RoadsideDispatchMessage msg = new RoadsideDispatchMessage(dr);
                    messages.Add(msg);
                }
            }

            if (callStatus != null)
                return messages.Where(w => w.CallStatus == callStatus).ToCollection();

            return messages;
        }


        /// <summary>
        /// Saves the Dispatch Message object to the data store.
        /// </summary>
        public void Save()
        {
            if (Id > 0)
                throw new ApplicationException("Dispatch Message is already created. Cannot save.");

            DbInsert();
        }

        public void MarkRead()
        {
            if (Id > 0 && ReadDate == null)
            {
                using (var c = Core.GetConnection())
                {
                    c.Execute("UPDATE Roadside.DispatchMessages SET ReadDate=@ReadDate WHERE RoadsideDispatchMessageId=@Id",
                        new
                        {
                            Id = Id,
                            ReadDate = DateTime.Now
                        });
                }
            }
        }

        private void DbInsert()
        {
            Id = Convert.ToInt32(SqlHelper.ExecuteScalar(Core.ConnectionString,
                "Roadside.DispatchMessageInsert",
                new SqlParameter("@RoadsideDispatchId", RoadsideDispatchId),
                new SqlParameter("@RoadsideDispatchUserId", RoadsideDispatchUserId),
                new SqlParameter("@Message", Message),
                new SqlParameter("@CallStatus", CallStatus),
                new SqlParameter("@JobProgressTextAlertItemId", JobProgressTextAlertItemId)));
        }

        public static async Task UpdateStatus(Collection<RoadsideDispatchUser> users, Towbook.Dispatch.Entry entry) {

            foreach (RoadsideDispatchUser endUser in users)
            {
                // don't send update to user who is actively viewing roadside.io (webhook channel occupied)
                // Note: always allow completed status updates
                if (endUser.IsActive && 
                    entry.Status != null && 
                    (entry.Status.Id < 5 || entry.Status.Id == 7))
                    continue;

                if (string.IsNullOrWhiteSpace(endUser.MobileNumber))
                    continue;

                await UpdateStatus(endUser, entry);
            }
        }

        public static async Task<string> UpdateStatus(RoadsideDispatchUser dispatchUser, Towbook.Dispatch.Entry entry)
        {
            var settings = Roadside.RoadsideSetting.GetByCompanyId(entry.CompanyId, entry.AccountId);

            return await UpdateStatus(dispatchUser, entry, settings);
        }

        public static async Task<string> UpdateStatus(RoadsideDispatchUser dispatchUser, Towbook.Dispatch.Entry entry, RoadsideSetting setting = null)
        {
            if (setting == null)
                setting = Roadside.RoadsideSetting.GetByCompanyId(entry.CompanyId, entry.AccountId);

            return await UpdateStatus(dispatchUser, entry, setting, null);
        }

        public static async Task<string> UpdateStatus(RoadsideDispatchUser dispatchUser, Towbook.Dispatch.Entry entry, RoadsideSetting setting = null, Collection<JobProgressTextAlertItem> items = null)
        {
            if (string.IsNullOrWhiteSpace(dispatchUser.MobileNumber))
                return null;

            var av = AccountKeyValue.GetByAccount(entry.Account.CompanyId, entry.Account.Id, Provider.Towbook.ProviderId, "AutoSendRoadsideInvite").FirstOrDefault();

            if (entry.Account == null ||
                entry.Account.MasterAccountId == MasterAccountTypes.Tesla || 
                new Towbook.Accounts.AccountType[] {
                    Towbook.Accounts.AccountType.PoliceDepartment,
                    Towbook.Accounts.AccountType.PrivateProperty
                }.Contains(entry.Account.Type))
            {
                if(av == null || av.Value != "1")
                    return null;
            }

            if(setting == null)
                setting = RoadsideSetting.GetByCompanyId(entry.CompanyId, entry.AccountId);

            if(items == null)
                items = JobProgressTextAlertItem.GetByCompanyId(entry.CompanyId, true).ToCollection();
            
            // No items means a company has never saved the roadside settings since introduction of custom text alerts.
            // Most likely new customer or we need to be backwark compatible.  Provide defaults.
            if (items.Count == 0)
            {
                items = JobProgressTextAlertItem.GetDefaults(entry.CompanyId, null);
            }
            else
            {
                av = AccountKeyValue.GetByAccount(entry.Account.CompanyId, entry.Account.Id, Provider.Towbook.ProviderId, "AlwaysSendSurvey").FirstOrDefault();
                bool forceSurvey = false;

                if (av?.Value == "1")
                    forceSurvey = true;
                else
                {
                    if (av == null && !items.Where(w => w.StatusTypeId == JobProgressStatusType.Completed.Id).Any())
                        forceSurvey = true;
                }

                items = items.Where(w => !w.IsDeleted).ToCollection();

                if (forceSurvey)
                {
                    // force survey always. Only exception is when account key value overrides this.
                    items.Add(new JobProgressTextAlertItem()
                    {
                        Id = 0,
                        CompanyId = entry.CompanyId,
                        StatusTypeId = JobProgressStatusType.Completed.Id,
                        Message = JobProgressStatusType.Completed.DefaultMessage,
                        CreateDate = DateTime.Now
                    });
                }
            }

            var item = JobProgressTextAlertItem.TranslateEntryStatusToMessageItem(setting, entry, items);
            if(item == null)
                return null;

            
            bool excludeRoadsideUrl = setting.NonMotorClubTextAlertPreferenceType == Settings.TextAlertPreferenceType.TextOnly;

            excludeRoadsideUrl = entry.Account.Type == AccountType.MotorClub && (entry.Account.Notes == null || !entry.Account.Notes.Contains("IncludeRoadside"));

            if (new [] {
                MasterAccountTypes.AaaAca,
                MasterAccountTypes.AaaNortheast,
                MasterAccountTypes.AaaNationalFsl,
                }.Contains(entry.Account.MasterAccountId))
            {
                excludeRoadsideUrl = false;
            }

            if (new int[] { Status.Completed.Id, Status.DestinationArrival.Id }.Contains(entry.Status.Id))
            {
                // force survey on completed calls
                excludeRoadsideUrl = false;
            }

            var callstatusIds = JobProgressStatusType.GetById(item.StatusTypeId).CallStatuses.Select(s => s.Id).ToArray();
            
            // if the progress text item was created after the call was completed, don't perform this.
            if (item.CreateDate > (entry.CompletionTime ?? entry.CreateDate))
                return null;

            // Check for an existing message...if so, don't send another text message
            var message = RoadsideDispatchMessage.GetMessageByJobProgressTextAlertItemId(dispatchUser.Id, item.Id, callstatusIds);

            if (message == null)
            {
                

                var companySig = setting.CompanySignatureName;
                if (string.IsNullOrEmpty(companySig) && entry.Company.Name.Length < 30)
                    companySig = entry.Company.Name;

                // store message in the data store
                message = new RoadsideDispatchMessage();
                message.RoadsideDispatchId = dispatchUser.RoadsideDispatchId;
                message.RoadsideDispatchUserId = dispatchUser.Id;
                message.Message = RoadsideHelper.GenerateTextMessage(dispatchUser.Id, dispatchUser.RoadsideDispatchId, entry, item.Message, excludeRoadsideUrl, companySig);
                message.CallStatus = entry.Status.Id;
                message.JobProgressTextAlertItemId = item.Id;
                message.Save();

                #region create log object
                var logProps = new Dictionary<object, object>
                {
                    ["callId"] = entry.Id,
                    ["callNumber"] = entry.CallNumber,
                    ["accountId"] = entry.AccountId,
                    ["roadsideUser"] = new
                    {
                        dispatchUser.Id,
                        dispatchUser.Name,
                        Phone = dispatchUser.MobileNumber,
                        dispatchUser.RoadsideDispatchId,
                        dispatchUser.DispatchEntryContactId,
                        dispatchUser.Url
                    }.ToJson(),
                    ["json"] = new
                    {
                        message.Id,
                        message.Message,
                        ItemId = message.JobProgressTextAlertItemId,
                        message.CreateDate
                    }.ToJson(),
                    ["callStatusId"] = entry.Status?.Id                    
                };

                if(!string.IsNullOrEmpty(av?.Value))
                {
                    logProps["accountAutoInvite"] = av.Value;
                }

                if (entry?.Account.MasterAccountId != 0)
                {
                    logProps["masterAccountId"] = entry.Account?.MasterAccountId;
                    logProps["masterAccountName"] = MasterAccountTypes.GetName(entry.Account.MasterAccountId);
                }

                #endregion

                // Send sms text message
                try
                {
                    await DispatchNotificationMessage.SendAsync(dispatchUser.MobileNumber, message.Message);

                    // add redis value for handling response to txt msg
                    Core.SetRedisValue("replyToTextsFrom:" +
                                    dispatchUser.MobileNumber, new
                                    {
                                        companyId = entry.CompanyId,
                                        callId = entry.Id,
                                        callNumber = entry.CallNumber,
                                        roadsideMessageId = message.Id
                                    }.ToJson());

                    // log event
                    logger.LogEvent($"Roadside.io -> Send Message",
                        entry.CompanyId,
                        LogLevel.Info, properties: logProps);

                }
                catch
                {
                    logger.LogEvent($"Roadside.io -> SendText Error",
                        entry.CompanyId,
                        LogLevel.Error, 
                        properties: logProps);
                }

                return message.Message;
            }

            return null;
        }

        public static async Task SendMessage(RoadsideDispatchUser dispatchUser, string msg)
        {
            await SendMessage(dispatchUser, msg, null);
        }

        public static async Task SendMessage(RoadsideDispatchUser dispatchUser, string msg, int? itemId = null) {
            if (string.IsNullOrWhiteSpace(dispatchUser.MobileNumber))
                throw new ApplicationException("Invalid user provided or the user doesn't have a phone number.");

            // store message in the data store
            var message = new RoadsideDispatchMessage();
            message.RoadsideDispatchId = dispatchUser.RoadsideDispatchId;
            message.RoadsideDispatchUserId = dispatchUser.Id;
            message.Message = msg;
            message.CallStatus = null;
            message.JobProgressTextAlertItemId = itemId;

            message.Save();

            // Send sms text message
            await DispatchNotificationMessage.SendAsync(dispatchUser.MobileNumber, message.Message);
        }
    }
}
