# EditorConfig is awesome: https://EditorConfig.org
# top-most EditorConfig file
root = true

# Unix-style newlines with a newline ending every file
[*]
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true

# Matches multiple files with brace expansion notation
# Set default charset
[*.js]
charset = utf-8

# Solution Files
[*.sln]
indent_style = tab

# XML Project Files
[*.{csproj,vbproj,vcxproj,vcxproj.filters,proj,projitems,shproj}]
indent_size = 2

# YAML Files
[*.{yml,yaml}]
indent_size = 2
indent_style = space

# Markdown Files
[*.md]
trim_trailing_whitespace = false

# .NET Code Style Settings
[*.{cs,csx,cake,vb}]

[*.cshtml.cs]
dotnet_diagnostic.SA1649.severity = none

# "this." and "Me." qualifiers
# https://docs.microsoft.com/en-us/visualstudio/ide/editorconfig-code-style-settings-reference#this_and_me
dotnet_style_qualification_for_field = false:warning
dotnet_style_qualification_for_property = false:warning
dotnet_style_qualification_for_method = false:warning
dotnet_style_qualification_for_event = false:warning

# Language keywords instead of framework type names for type references
# https://docs.microsoft.com/en-us/visualstudio/ide/editorconfig-code-style-settings-reference#language_keywords
dotnet_style_predefined_type_for_locals_parameters_members = true:warning
dotnet_style_predefined_type_for_member_access = true:warning

# Modifier preferences
# https://docs.microsoft.com/en-us/visualstudio/ide/editorconfig-code-style-settings-reference#normalize_modifiers
dotnet_style_require_accessibility_modifiers = always:warning
csharp_preferred_modifier_order = public,private,protected,internal,static,extern,new,virtual,abstract,sealed,override,readonly,unsafe,volatile,async:warning
dotnet_style_readonly_field = true:warning

# Expression-level preferences
# https://docs.microsoft.com/en-us/visualstudio/ide/editorconfig-code-style-settings-reference#expression_level
dotnet_style_object_initializer = true:warning
dotnet_style_collection_initializer = true:warning
dotnet_style_explicit_tuple_names = true:warning
dotnet_style_prefer_inferred_tuple_names = true:warning
dotnet_style_prefer_inferred_anonymous_type_member_names = true:warning
dotnet_style_prefer_auto_properties = true:warning
dotnet_style_prefer_is_null_check_over_reference_equality_method = true:warning

# Null-checking preferences
# https://docs.microsoft.com/en-us/visualstudio/ide/editorconfig-code-style-settings-reference#null_checking
dotnet_style_coalesce_expression = true:warning
dotnet_style_null_propagation = true:warning

# Other (Undocumented)
dotnet_style_prefer_conditional_expression_over_return = false
dotnet_style_prefer_conditional_expression_over_assignment = false

# C# Code Style Settings
[*.{cs,csx,cake}]
# Implicit and explicit types
# https://docs.microsoft.com/en-us/visualstudio/ide/editorconfig-code-style-settings-reference#implicit-and-explicit-types
csharp_style_var_for_built_in_types = true:warning
csharp_style_var_when_type_is_apparent = true:warning
csharp_style_var_elsewhere = true:warning

# Expression-bodied members
# https://docs.microsoft.com/en-us/visualstudio/ide/editorconfig-code-style-settings-reference#expression_bodied_members
csharp_style_expression_bodied_methods = false:warning
csharp_style_expression_bodied_constructors = false:warning
csharp_style_expression_bodied_operators = true:warning
csharp_style_expression_bodied_properties = true:warning
csharp_style_expression_bodied_indexers = true:warning
csharp_style_expression_bodied_accessors = true:warning

# Pattern matching
# https://docs.microsoft.com/en-us/visualstudio/ide/editorconfig-code-style-settings-reference#pattern_matching
csharp_style_pattern_matching_over_is_with_cast_check = true:warning
csharp_style_pattern_matching_over_as_with_null_check = true:warning

# Inlined variable declarations
# https://docs.microsoft.com/en-us/visualstudio/ide/editorconfig-code-style-settings-reference#inlined_variable_declarations
csharp_style_inlined_variable_declaration = true:warning

# Expression-level preferences
# https://docs.microsoft.com/en-us/visualstudio/ide/editorconfig-code-style-settings-reference#expression_level_csharp
csharp_prefer_simple_default_expression = true:warning
csharp_style_deconstructed_variable_declaration = true:warning
csharp_style_pattern_local_over_anonymous_function = true:warning

# "Null" checking preferences
# https://docs.microsoft.com/en-us/visualstudio/ide/editorconfig-code-style-settings-reference#null_checking_csharp
csharp_style_throw_expression = true:warning
csharp_style_conditional_delegate_call = true:warning

# Code block preferences
# https://docs.microsoft.com/en-us/visualstudio/ide/editorconfig-code-style-settings-reference#code_block
csharp_prefer_braces = true:warning

#############################
# .NET Formatting Conventions
# https://docs.microsoft.com/en-us/visualstudio/ide/editorconfig-code-style-settings-reference#formatting-conventions
#############################

# Organize usings
# https://docs.microsoft.com/en-us/visualstudio/ide/editorconfig-code-style-settings-reference#usings
dotnet_sort_system_directives_first = true
dotnet_separate_import_directive_groups = true

# C# formatting settings
# https://docs.microsoft.com/en-us/visualstudio/ide/editorconfig-code-style-settings-reference#c-formatting-settings
csharp_new_line_before_open_brace = all
csharp_new_line_before_else = true:warning
csharp_new_line_before_catch = true:warning
csharp_new_line_before_finally = true:warning
csharp_new_line_before_members_in_object_initializers = true:warning
csharp_new_line_before_members_in_anonymous_types = true:warning
csharp_new_line_between_query_expression_clauses = true:warning

# Indentation options
# https://docs.microsoft.com/en-us/visualstudio/ide/editorconfig-code-style-settings-reference#indent
csharp_indent_case_contents = true:warning
csharp_indent_switch_labels = true:warning
csharp_indent_labels = no_change:warning

# Spacing options
# https://docs.microsoft.com/en-us/visualstudio/ide/editorconfig-code-style-settings-reference#spacing
csharp_space_after_cast = false:warning
csharp_space_after_keywords_in_control_flow_statements = true:warning
csharp_space_between_method_declaration_parameter_list_parentheses = false:warning
csharp_space_between_method_call_parameter_list_parentheses = false:warning
csharp_space_between_parentheses = expressions:warning
csharp_space_before_colon_in_inheritance_clause = true:warning
csharp_space_after_colon_in_inheritance_clause = true:warning
csharp_space_around_binary_operators = before_and_after:warning
csharp_space_between_method_declaration_empty_parameter_list_parentheses = false:warning
csharp_space_between_method_call_name_and_opening_parenthesis = false:warning
csharp_space_between_method_call_empty_parameter_list_parentheses = false:warning

# Wrapping options
# https://docs.microsoft.com/en-us/visualstudio/ide/editorconfig-code-style-settings-reference#wrapping
csharp_preserve_single_line_statements = false:warning
csharp_preserve_single_line_blocks = true:warning

# More Indentation options (Undocumented)
csharp_indent_block_contents = true:warning
csharp_indent_braces = false:warning

# Spacing Options (Undocumented)
csharp_space_after_comma = true:warning
csharp_space_after_dot = false:warning
csharp_space_after_semicolon_in_for_statement = true:warning
csharp_space_around_declaration_statements = do_not_ignore:warning
csharp_space_before_comma = false:warning
csharp_space_before_dot = false:warning
csharp_space_before_semicolon_in_for_statement = false:warning
csharp_space_before_open_square_brackets = false:warning
csharp_space_between_empty_square_brackets = false:warning
csharp_space_between_method_declaration_name_and_open_parenthesis = false:warning
csharp_space_between_square_brackets = false:warning

#########################
# .NET Naming conventions
# https://docs.microsoft.com/en-us/visualstudio/ide/editorconfig-naming-conventions
#########################

[*.{cs,csx,cake,vb}]
# Naming Symbols
# constant_fields - Define constant fields
dotnet_naming_symbols.constant_fields.applicable_kinds = field
dotnet_naming_symbols.constant_fields.required_modifiers = const

# non_private_readonly_fields - Define public, internal and protected readonly fields
dotnet_naming_symbols.non_private_readonly_fields.applicable_accessibilities = public, internal, protected
dotnet_naming_symbols.non_private_readonly_fields.applicable_kinds = field
dotnet_naming_symbols.non_private_readonly_fields.required_modifiers = readonly

# static_readonly_fields - Define static and readonly fields
dotnet_naming_symbols.static_readonly_fields.applicable_kinds = field
dotnet_naming_symbols.static_readonly_fields.required_modifiers = static, readonly

# private_readonly_fields - Define private readonly fields
dotnet_naming_symbols.private_readonly_fields.applicable_accessibilities = private
dotnet_naming_symbols.private_readonly_fields.applicable_kinds = field
dotnet_naming_symbols.private_readonly_fields.required_modifiers = readonly

# public_internal_fields - Define public and internal fields
dotnet_naming_symbols.public_internal_fields.applicable_accessibilities = public, internal
dotnet_naming_symbols.public_internal_fields.applicable_kinds = field

# private_protected_fields - Define private and protected fields
dotnet_naming_symbols.private_protected_fields.applicable_accessibilities = private, protected
dotnet_naming_symbols.private_protected_fields.applicable_kinds = field

# private_fields - Define private and protected fields
dotnet_naming_symbols.private_fields.applicable_accessibilities = private
dotnet_naming_symbols.private_fields.applicable_kinds = field

# public_symbols - Define any public symbol
dotnet_naming_symbols.public_symbols.applicable_accessibilities = public, internal, protected, protected_internal
dotnet_naming_symbols.public_symbols.applicable_kinds = method, property, event, delegate

# parameters - Defines any parameter
dotnet_naming_symbols.parameters.applicable_kinds = parameter

# non_interface_types - Defines class, struct, enum and delegate types
dotnet_naming_symbols.non_interface_types.applicable_kinds = class, struct, enum, delegate

# interface_types - Defines interfaces
dotnet_naming_symbols.interface_types.applicable_kinds = interface

#########################
# Naming Styles
#########################

# camel_case - Define the camelCase style
dotnet_naming_style.camel_case.capitalization = camel_case

# underscore_camel_case   _camelCase style
dotnet_naming_style.underscore_camel_case.required_prefix = _
dotnet_naming_style.underscore_camel_case.capitalization = camel_case

# pascal_case - Define the Pascal_case style
dotnet_naming_style.pascal_case.capitalization = pascal_case

# first_upper - The first character must start with an upper-case character
dotnet_naming_style.first_upper.capitalization = first_word_upper

# prefix_interface_interface_with_i - Interfaces must be PascalCase and the first character of an interface must be an 'I'
dotnet_naming_style.prefix_interface_interface_with_i.capitalization = pascal_case
dotnet_naming_style.prefix_interface_interface_with_i.required_prefix = I

#########################
# Naming Rules
#########################

# Constant fields must be PascalCase
dotnet_naming_rule.constant_fields_must_be_pascal_case.severity = warning
dotnet_naming_rule.constant_fields_must_be_pascal_case.symbols = constant_fields
dotnet_naming_rule.constant_fields_must_be_pascal_case.style = pascal_case

# Public, internal and protected readonly fields must be PascalCase
dotnet_naming_rule.non_private_readonly_fields_must_be_pascal_case.severity = warning
dotnet_naming_rule.non_private_readonly_fields_must_be_pascal_case.symbols = non_private_readonly_fields
dotnet_naming_rule.non_private_readonly_fields_must_be_pascal_case.style = pascal_case

# Static readonly fields must be PascalCase
dotnet_naming_rule.static_readonly_fields_must_be_pascal_case.severity = warning
dotnet_naming_rule.static_readonly_fields_must_be_pascal_case.symbols = static_readonly_fields
dotnet_naming_rule.static_readonly_fields_must_be_pascal_case.style = pascal_case

# Private readonly fields must be _camelCase
dotnet_naming_rule.private_readonly_fields_must_be_camel_case.severity = warning
dotnet_naming_rule.private_readonly_fields_must_be_camel_case.symbols = private_readonly_fields
dotnet_naming_rule.private_readonly_fields_must_be_camel_case.style = underscore_camel_case

# Public and internal fields must be PascalCase
dotnet_naming_rule.public_internal_fields_must_be_pascal_case.severity = warning
dotnet_naming_rule.public_internal_fields_must_be_pascal_case.symbols = public_internal_fields
dotnet_naming_rule.public_internal_fields_must_be_pascal_case.style = pascal_case

# Private and protected fields must be _camelCase
dotnet_naming_rule.private_protected_fields_must_be_camel_case.severity = warning
dotnet_naming_rule.private_protected_fields_must_be_camel_case.symbols = private_protected_fields
dotnet_naming_rule.private_protected_fields_must_be_camel_case.style = underscore_camel_case

# Private fields must be _camelCase
dotnet_naming_rule.private_protected_must_be_camel_case.severity = warning
dotnet_naming_rule.private_protected_must_be_camel_case.symbols = private_fields
dotnet_naming_rule.private_protected_must_be_camel_case.style = underscore_camel_case

# Public members must be capitalized
dotnet_naming_rule.public_members_must_be_capitalized.severity = warning
dotnet_naming_rule.public_members_must_be_capitalized.symbols = public_symbols
dotnet_naming_rule.public_members_must_be_capitalized.style = first_upper

# Parameters must be camelCase
dotnet_naming_rule.parameters_must_be_camel_case.severity = warning
dotnet_naming_rule.parameters_must_be_camel_case.symbols = parameters
dotnet_naming_rule.parameters_must_be_camel_case.style = camel_case

# Class, struct, enum and delegates must be PascalCase
dotnet_naming_rule.non_interface_types_must_be_pascal_case.severity = warning
dotnet_naming_rule.non_interface_types_must_be_pascal_case.symbols = non_interface_types
dotnet_naming_rule.non_interface_types_must_be_pascal_case.style = pascal_case

# Interfaces must be PascalCase and start with an 'I'
dotnet_naming_rule.interface_types_must_be_prefixed_with_i.severity = warning
dotnet_naming_rule.interface_types_must_be_prefixed_with_i.symbols = interface_types
dotnet_naming_rule.interface_types_must_be_prefixed_with_i.style = prefix_interface_interface_with_i
