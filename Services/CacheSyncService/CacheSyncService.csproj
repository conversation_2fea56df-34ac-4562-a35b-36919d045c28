<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerfileContext>..\..</DockerfileContext>
    <ContainerDevelopmentMode>Regular</ContainerDevelopmentMode>
    <ErrorOnDuplicatePublishOutputFiles>false</ErrorOnDuplicatePublishOutputFiles>
    <UserSecretsId>9d92ffcc-16da-450a-b1cb-617b1549ccc9</UserSecretsId>
  </PropertyGroup>
  <ItemGroup>
    <None Remove="appsettings.json" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup>
	  <None Include="../../openssl.cnf">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </None>
	</ItemGroup>
  <ItemGroup>
	<PackageReference Include="Azure.Core" Version="1.42.0" />
    <PackageReference Include="Azure.Core.Amqp" Version="1.3.1" />
    <PackageReference Include="Azure.Messaging.ServiceBus" Version="7.18.1" />
    <PackageReference Include="FileHelpers" Version="3.5.2" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.DataAnnotations" Version="2.2.0" />
    <PackageReference Include="Microsoft.Azure.Amqp" Version="2.6.9" />
	<PackageReference Include="Microsoft.Bcl.AsyncInterfaces" Version="9.0.0" />
    <PackageReference Include="Microsoft.CSharp" Version="4.7.0" />
	<PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Microsoft.IdentityModel.JsonWebTokens" Version="8.0.2" />
    <PackageReference Include="Microsoft.IdentityModel.Logging" Version="8.0.2" />
    <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="8.0.2" />
    <PackageReference Include="Microsoft.Rest.ClientRuntime" Version="2.3.24" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
    <PackageReference Include="NewRelic.Agent.Api" Version="10.29.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Pipelines.Sockets.Unofficial" Version="2.2.8" />
    <PackageReference Include="protobuf-net" Version="3.2.30" />
    <PackageReference Include="protobuf-net.Core" Version="3.2.30" />
    <PackageReference Include="RestSharp" Version="111.4.1" />
    <PackageReference Include="StackExchange.Redis" Version="2.8.0" />
	<PackageReference Include="System.AppContext" Version="4.3.0" />
    <PackageReference Include="System.Buffers" Version="4.5.1" />
	<PackageReference Include="System.Collections.Immutable" Version="8.0.0" />
    <PackageReference Include="System.ComponentModel" Version="4.3.0" />
    <PackageReference Include="System.ComponentModel.Annotations" Version="5.0.0" />
    <PackageReference Include="System.ComponentModel.Composition" Version="8.0.0" />
    <PackageReference Include="System.ComponentModel.Primitives" Version="4.3.0" />
    <PackageReference Include="System.Console" Version="4.3.1" />
    <PackageReference Include="System.Data.Common" Version="4.3.0" />
    <PackageReference Include="System.Data.DataSetExtensions" Version="4.5.0" />
    <PackageReference Include="System.Data.OracleClient" Version="1.0.8" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.6" />
	<PackageReference Include="System.Diagnostics.DiagnosticSource" Version="9.0.1" />
	<PackageReference Include="System.Diagnostics.PerformanceCounter" Version="8.0.0" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.0.2" />
    <PackageReference Include="System.IO" Version="4.3.0" />
    <PackageReference Include="System.IO.Compression" Version="4.3.0" />
    <PackageReference Include="System.IO.Pipelines" Version="9.0.0" />
    <PackageReference Include="System.Memory" Version="4.5.5" />
    <PackageReference Include="System.Memory.Data" Version="8.0.0" />
    <PackageReference Include="System.Net.Http" Version="4.3.4" />
	<PackageReference Include="System.Net.Http.WinHttpHandler" Version="8.0.2" />
    <PackageReference Include="System.Net.Primitives" Version="4.3.1" />
    <PackageReference Include="System.Net.Requests" Version="4.3.0" />
    <PackageReference Include="System.Net.WebSockets" Version="4.3.0" />
    <PackageReference Include="System.Net.WebSockets.Client" Version="4.3.2" />
    <PackageReference Include="System.Numerics.Vectors" Version="4.5.0" />
    <PackageReference Include="System.Private.DataContractSerialization" Version="4.3.0" />
    <PackageReference Include="System.Runtime" Version="4.3.1" />
    <PackageReference Include="System.Runtime.CompilerServices.Unsafe" Version="6.0.0" />
    <PackageReference Include="System.Runtime.Serialization.Json" Version="4.3.0" />
    <PackageReference Include="System.Runtime.Serialization.Primitives" Version="4.3.0" />
    <PackageReference Include="System.Runtime.Serialization.Xml" Version="4.3.0" />
    <PackageReference Include="System.Security.AccessControl" Version="6.0.1" />
    <PackageReference Include="System.Security.Principal" Version="4.3.0" />
    <PackageReference Include="System.Security.Principal.Windows" Version="5.0.0" />
    <PackageReference Include="System.ServiceModel.Http" Version="8.0.0" />
    <PackageReference Include="System.ServiceModel.Primitives" Version="8.0.0" />
    <PackageReference Include="System.ServiceModel.Security" Version="6.0.0" />
    <PackageReference Include="System.ServiceProcess.ServiceController" Version="8.0.0" />
	<PackageReference Include="System.Text.Encodings.Web" Version="9.0.0" />
	<PackageReference Include="System.Text.Json" Version="9.0.0" />
	<PackageReference Include="System.Threading.Channels" Version="8.0.0" />
    <PackageReference Include="System.Threading.Tasks.Extensions" Version="4.5.4" />
    <PackageReference Include="System.Xml.ReaderWriter" Version="4.3.1" />
    <PackageReference Include="System.Xml.XDocument" Version="4.3.0" />
  </ItemGroup>
  <ItemGroup>
    <None Update="NLog.config" CopyToOutputDirectory="Always" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Extric.Towbook.API\Extric.Towbook.API.csproj" />
  </ItemGroup>
</Project>