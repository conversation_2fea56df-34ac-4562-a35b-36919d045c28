apiVersion: apps/v1
kind: Deployment
metadata:
  name: eventnotificationservice
spec:
  selector:
    matchLabels:
      app: eventnotificationservice-pod
  template:
    metadata:
      labels:
        app: eventnotificationservice-pod
    spec:
      containers:
      - name: eventnotificationservice-container
        image: towbookapiregistry.azurecr.io/eventnotificationservice:latest
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "250Mi"
            cpu: "100m"
        env:
        - name: ASPNETCORE_ENVIRONMENT
          value: "Development"
        - name: ConnectionStrings__Database
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: ConnectionStrings-Database
        - name: ConnectionStrings__Database.Azure
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: ConnectionStrings-DatabaseAzure
        - name: ConnectionStrings__Microsoft.ServiceBus
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: ConnectionStrings-MicrosoftServiceBus
        - name: Redis__Credentials
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: Redis-Credentials
        - name: CosmosDb__Url
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: CosmosDb-Url
        - name: CosmosDb__AuthKey
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: CosmosDb-AuthKey
        - name: CosmosDb__Database
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: CosmosDb-Database
        - name: EventNotificationDb__Url
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: CosmosDb-Url
        - name: EventNotificationDb__AuthKey
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: CosmosDb-AuthKey
        - name: EventNotificationDb__Database
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: CosmosDb-Database
        - name: EventNotificationDb__Collection
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: EventNotificationDb-Collection
        volumeMounts:
          - name:  secrets-store
            mountPath:  "mnt/secrets-store"
            readOnly: true
      nodeSelector:
        selector: nplin
      volumes:
        - name: secrets-store
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: "azure-kvtowbook-msi"