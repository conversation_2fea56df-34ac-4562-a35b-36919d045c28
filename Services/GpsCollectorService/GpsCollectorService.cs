using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Net.Http;
using System.Net.Sockets;
using System.ServiceProcess;
using System.Threading;
using System.Threading.Tasks;
using Extric.Towbook.Integration;
using Extric.Towbook.Integrations.DriverLocate;
using Extric.Towbook.Integrations.Fleetmatics;
using Extric.Towbook.Integrations.GeoTab;
using Extric.Towbook.Integrations.Gps.Azuga;
using Extric.Towbook.Integrations.Samsara;
using Extric.Towbook.Integrations.TomTom;
using Extric.Towbook.Integrations.USFleetTracking;
using Extric.Towbook.Utility;
using Humanizer;
using Microsoft.Extensions.Hosting;
using System.Net;
using NLog;
using System.Collections.ObjectModel;

namespace Extric.Towbook.Services.GpsCollectorService;
public class GpsCollectorService : IHostedService, IAsyncDisposable
{
    private readonly System.Timers.Timer executionTimer = new System.Timers.Timer();
    private static int numberOfRunningThreads;
    private const int Interval = 30000;
    private readonly string[] _args;
    private IHostApplicationLifetime _applicationLifeTime;

    private static readonly Logger logger = LogManager.GetCurrentClassLogger();

    public GpsCollectorService(string[] args, IHostApplicationLifetime applicationLifeTime)
    {
        _args = args;
        
        if (applicationLifeTime == null)    
            throw new ArgumentNullException(nameof(applicationLifeTime));

        _applicationLifeTime = applicationLifeTime;
        _applicationLifeTime.ApplicationStopped.Register(OnStopped);
    }


    protected void OnStart(string[] args)
    {
        System.Net.ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;

        Console.WriteLine("Service is running as console application");

        executionTimer.AutoReset = false;
        executionTimer.Interval = GpsCollectorService.Interval;
        executionTimer.Elapsed += executionTimer_Elapsed;
        executionTimer.Start();
        executionTimer_Elapsed(null, null);
    }

    public static async Task ProcessQueueItem(GpsCollectionTask stateInfo)
    {
        if (stateInfo == null)
            return;

        var sw = Stopwatch.StartNew();

        try
        {
            GpsCollectorRun.UpdateLastRun(stateInfo.SourceId, stateInfo.CompanyId);

            switch (stateInfo.SourceId)
            {
                case 1:
                    await ProcessGeoTab(stateInfo);
                    break;

                case 2:
                    await ProcessTomTomTom(stateInfo);
                    break;

                case 3:
                    await ProcessDriverLocate(stateInfo);
                    break;

                case 4:
                    await ProcessUSFT(stateInfo);
                    break;

                case 5:
                    await ProcessFleetComplete(stateInfo);
                    break;

                case 6:
                    await ProcessSamsara(stateInfo);
                    break;

                case 7:
                    await ProcessAzuga(stateInfo);
                    break;

                case 8:
                    await ProcessFleetmatics(stateInfo);
                    break;
            }
        }
        catch (SocketException se)
        {
            Console.WriteLine("Timeout occurred..." + se.ToString());
        }

        Console.WriteLine("Finished " + stateInfo.ToJson() + " in " + sw.ElapsedMilliseconds);
    }

    public static async Task ProcessDriverLocate(GpsCollectionTask stateInfo)
    {
        if (stateInfo == null)
            return;

        try
        {
            string username = CompanyKeyValue.GetFirstValueOrNull(stateInfo.CompanyId, Provider.DriverLocate.ProviderId, "Username");
            string password = CompanyKeyValue.GetFirstValueOrNull(stateInfo.CompanyId, Provider.DriverLocate.ProviderId, "Password");

            var gtc = DriverLocateService.Create(username, password);
            if (gtc == null)
                return;

            var currentLocations = gtc.GetVehicles()
                .Where(o => o.LastLocation != null).ToCollection();
            var truckKeys = TruckKeyValue.GetByCompanyId(stateInfo.CompanyId, Provider.DriverLocate.ProviderId, "TruckId");

            Console.WriteLine("DriverLocate: processing " + stateInfo.CompanyId + ":" + stateInfo.ToJson());

            foreach (var l in currentLocations)
            {
                TruckLocationHistoryItem ni = new TruckLocationHistoryItem();
                var t = truckKeys.FirstOrDefault(o => o.Value == l.Id);

                if (t == null)
                {
                    string vin = l.Id;
                    //Console.WriteLine("DriverLocate/" + stateInfo.CompanyId + ": " + vin + ", -- truck not found");
                    continue;
                }

                ni.TruckId = t.TruckId;
                ni.Latitude = l.LastLocation.Latitude;
                ni.Longitude = l.LastLocation.Longitude;
                ni.Timestamp = l.LocationLastReported;
                ni.SourceId = stateInfo.SourceId;
                await ni.Save(stateInfo.CompanyId);

                Console.WriteLine("DriverLocate2/" + stateInfo.CompanyId + ": Saved " + ni.ToJson());

            }
        }
        catch (Exception e)
        {
            Console.WriteLine("DriverLocate: Failed processing " + stateInfo.ToJson() + "..." + e.ToString());
        }
    }

    public static async Task ProcessTomTomTom(GpsCollectionTask stateInfo)
    {
        if (stateInfo == null)
            return;

        var ttu = TomTomUtility.GetInstance(stateInfo.CompanyId);

        if (ttu == null)
            return;

        try
        {
            var tomtoms = DriverKeyValue.GetByCompany(stateInfo.CompanyId);
            var units = ttu.GetUnits();

            var truckKeys = TruckKeyValue.GetByCompanyId(stateInfo.CompanyId, Provider.TomTom.ProviderId, "TruckId");

            foreach (var dv in units)
            {
                var assignedUnit = dv;

                var towbookTruckId = truckKeys.FirstOrDefault(o => o.Value == dv.Id);

                if (towbookTruckId == null)
                {
                    continue;
                }

                var ni = new TruckLocationHistoryItem
                {
                    TruckId = towbookTruckId.TruckId,
                    Latitude = assignedUnit.Latitude.Value,
                    Longitude = assignedUnit.Longitude.Value,
                    Timestamp = Convert.ToDateTime(assignedUnit.PosDateTime, culture).ToLocalTime(),
                    SourceId = stateInfo.SourceId
                };

                await ni.Save(stateInfo.CompanyId);
            }
        }
        catch (Exception e)
        {
            Console.WriteLine(e.ToString());
        }
    }

    private static readonly CultureInfo culture = CultureInfo.GetCultureInfo("en-US");

    private static async Task ProcessUSFT(GpsCollectionTask stateInfo)
    {
        var username = CompanyKeyValue.GetFirstValueOrNull(stateInfo.CompanyId, Provider.USFleetTracking.ProviderId, "Username");
        var password = CompanyKeyValue.GetFirstValueOrNull(stateInfo.CompanyId, Provider.USFleetTracking.ProviderId, "Password");

        USFT.RestClient.v1.UsftClient service = null;

        try
        {
            service = USFleetTrackingService.Create(username, password);
        }
        catch (InvalidLoginException er)
        {
            Console.WriteLine("USFT/" + stateInfo.CompanyId + ": " + username + "/" + password + " is INVALID:" +
                er.Message);
            return;
        }



        Console.WriteLine("USFT/" + stateInfo.CompanyId + ": " + username + "/" + password);

        if (service == null)
            return;
        try
        {
            var units = service.GetDeviceLocations();
            var truckKeys = TruckKeyValue.GetByCompanyId(stateInfo.CompanyId, Provider.USFleetTracking.ProviderId, "TruckId");

            Console.WriteLine("USFT/" + stateInfo.CompanyId + ":" + units.ToJson(true));



            foreach (var dv in truckKeys)
            {
                var assignedUnit = units.FirstOrDefault(o => o.DeviceId.ToString(culture) == dv.Value);

                if (assignedUnit == null)
                {
                    Console.WriteLine("USFT/" + stateInfo.CompanyId + ": warning - no matched unit for " +
                        dv.Value);
                    continue;
                }

                var ni = new TruckLocationHistoryItem
                {
                    TruckId = dv.TruckId,
                    Latitude = (decimal)assignedUnit.Latitude,
                    Longitude = (decimal)assignedUnit.Longitude,
                    Timestamp = assignedUnit.LastUpdated.ToLocalTime(),
                    SourceId = stateInfo.SourceId
                };

                await ni.Save(stateInfo.CompanyId);
            }
        }
        catch (Exception e)
        {
            Console.WriteLine(e.Message);
        }
    }

    private static async Task ProcessFleetComplete(GpsCollectionTask stateInfo)
    {
        var clientId = CompanyKeyValue.GetFirstValueOrNull(stateInfo.CompanyId, Provider.FleetComplete.ProviderId, "AccountName");
        var username = CompanyKeyValue.GetFirstValueOrNull(stateInfo.CompanyId, Provider.FleetComplete.ProviderId, "Username");
        var password = CompanyKeyValue.GetFirstValueOrNull(stateInfo.CompanyId, Provider.FleetComplete.ProviderId, "Password");


        Integrations.FleetComplete.FleetCompleteRestClient service = null;

        service = Integrations.FleetComplete.FleetCompleteRestClient.Login(clientId, username, password);
        if (service == null)
        {
            Console.WriteLine("FleetComplete/" + stateInfo.CompanyId + ": " + username + "/" + password + " is INVALID:");
        }


        try
        {
            var units = await service.GetAssets();
            var truckKeys = TruckKeyValue.GetByCompanyId(stateInfo.CompanyId, Provider.FleetComplete.ProviderId, "TruckId");

            Console.WriteLine("FleetComplete/" + stateInfo.CompanyId + ":" + units.Select(o => new
            {
                Id = o.ID,
                Name = o.Description,
                Location = o.Position.Latitude + "," + o.Position.Longitude
            }).ToJson(true));


            foreach (var dv in truckKeys)
            {
                var assignedUnit = units.FirstOrDefault(o => o.ID == dv.Value);

                if (assignedUnit == null)
                {
                    Console.WriteLine("FleetComplete/" + stateInfo.CompanyId + ": warning - no matched unit for " +
                        dv.Value);
                    continue;
                }

                var ni = new TruckLocationHistoryItem
                {
                    TruckId = dv.TruckId,
                    Latitude = (decimal)assignedUnit.Position.Latitude,
                    Longitude = (decimal)assignedUnit.Position.Longitude,
                    Timestamp = assignedUnit.LastUpdatedTimeStamp.ToLocalTime(),
                    SourceId = stateInfo.SourceId
                };

                await ni.Save(stateInfo.CompanyId);
                Console.WriteLine("Save " + ni.ToJson());
            }
        }
        catch (Exception e)
        {
            Console.WriteLine(e.Message);
        }
    }

    private static async Task ProcessGeoTab(GpsCollectionTask stateInfo)
    {
        var gtc = await GeoTabConnection.Login(stateInfo.CompanyId);
        if (gtc == null)
            return;

        try
        {
            var devices = await gtc.GetVehicles();
            var x = await gtc.GetVehiclesInfo();

            var truckKeys = TruckKeyValue.GetByCompanyId(stateInfo.CompanyId, Provider.GeoTab.ProviderId, "TruckId");

            foreach (var l in x)
            {
                var device = devices.FirstOrDefault(o => o.Id.ToString() == l.Device.Id.ToString());

                Console.WriteLine((device as dynamic)?.VehicleIdentificationNumber + "," + l.Latitude + "," + l.Longitude
                    + l.DateTime.Value.ToLocalTime());

                var t = truckKeys.FirstOrDefault(o => o.Value == device.Id.ToString());

                if (t == null)
                {
                    string vin = (device as dynamic)?.VehicleIdentificationNumber;
                    Console.WriteLine(vin + ", -- truck not found");
                    // try to auto link

                    var truckFound = (await Truck
                        .GetByCompanyAsync(await Company.Company.GetByIdAsync(stateInfo.CompanyId))).FirstOrDefault(o => o.VIN?.ToUpperInvariant() == vin);
                    if (truckFound != null)
                    {
                        var tkv = new TruckKeyValue();
                        tkv.TruckId = truckFound.Id;
                        tkv.Value = device.Id.ToString();
                        tkv.KeyId = TruckKey.GetByProviderId(Provider.GeoTab.ProviderId, "TruckId").Id;
                        await tkv.SaveAsync();
                        t = tkv;
                    }
                    else
                    {
                        continue;
                    }
                }

                TruckLocationHistoryItem ni = new TruckLocationHistoryItem
                {
                    TruckId = t.TruckId,
                    Latitude = (decimal)l.Latitude.Value,
                    Longitude = (decimal)l.Longitude.Value,
                    Timestamp = l.DateTime.Value.ToLocalTime(),
                    SourceId = stateInfo.SourceId
                };

                await ni.Save(stateInfo.CompanyId);
            }
        }
        catch (HttpRequestException)
        {
            return;
        }
        catch (Exception e)
        {
            Console.WriteLine(e.ToString());
        }
    }

    private static async Task ProcessSamsara(GpsCollectionTask stateInfo)
    {
        var accessToken = CompanyKeyValue.GetFirstValueOrNull(stateInfo.CompanyId, Provider.Samsara.ProviderId, "AccessToken");

        if (accessToken == null)
            return;

        var service = SamsaraRestClient.Get(accessToken);

        try
        {
            var units = await service.GetRecentLocations();
            var truckKeys = TruckKeyValue.GetByCompanyId(stateInfo.CompanyId, Provider.Samsara.ProviderId, "TruckId");

            var tasks = new Collection<Task>();
            foreach (var dv in truckKeys)
            {
                var assignedUnit = units.FirstOrDefault(o => o.Id == dv.Value);

                if (assignedUnit == null)
                {
                    Console.WriteLine("Samsara/" + stateInfo.CompanyId + ": warning - no matched unit for " + dv.Value);
                    continue;
                }

                var ni = new TruckLocationHistoryItem
                {
                    TruckId = dv.TruckId,
                    Latitude = assignedUnit.Location.Latitude,
                    Longitude = assignedUnit.Location.Longitude,
                    Timestamp = assignedUnit.Location.Time.ToLocalTime(),
                    SourceId = stateInfo.SourceId
                };

                tasks.Add(ni.Save(stateInfo.CompanyId));
            }

            await Task.WhenAll(tasks);
        }
        catch (Exception e)
        {
            Console.WriteLine(e.Message);
        }
    }

    private static async Task ProcessFleetmatics(GpsCollectionTask stateInfo)
    {
        Console.WriteLine("Process Fleetmatics...");

        try
        {
            var token = TokenGenerator.GetInstance(stateInfo.CompanyId);
            var service = FleetmaticsRestClient.Create(token);

            var truckKeys = TruckKeyValue.GetByCompanyId(stateInfo.CompanyId, Provider.Fleetmatics.ProviderId, "TruckId");
            var vehicleNumbers = truckKeys.Select(t => t.Value).ToList();

            var locations = await service.GetLocations(vehicleNumbers);

            var truckLocations = (from truck in truckKeys
                                  join location in locations on truck.Value equals location.VehicleNumber
                                  select new TruckLocationHistoryItem
                                  {
                                      TruckId = truck.TruckId,
                                      Latitude = location.Latitude,
                                      Longitude = location.Longitude,
                                      Timestamp = location.UpdateUTC.ToLocalTime(),
                                      SourceId = stateInfo.SourceId
                                  }).Select(async t =>
                                    {
                                        await t.Save(stateInfo.CompanyId);
                                        Console.WriteLine("Save " + t.ToJson());
                                    }).ToArray();

            await Task.WhenAll(truckLocations);
        }
        catch (Exception e)
        {
            Console.WriteLine(e.Message);
        }
    }

    static ConcurrentDictionary<int, DateTime> azugaLast = new ConcurrentDictionary<int, DateTime>();

    private static async Task ProcessAzuga(GpsCollectionTask stateInfo)
    {
        var apiKey = CompanyKeyValue.GetFirstValueOrNull(stateInfo.CompanyId, Provider.Azuga.ProviderId, "ApiKey");

        if (apiKey == null)
            return;
        
        if (azugaLast.TryGetValue(stateInfo.CompanyId, out var last))
        {
            // skip if its newer than 30 seconds.
            if (last > DateTime.Now.AddSeconds(-30))
                return;
        }

        var service = AzugaRestClient.Create(apiKey);

        var units = await service.GetVehicles();
        var truckKeys = TruckKeyValue.GetByCompanyId(stateInfo.CompanyId, Provider.Azuga.ProviderId, "TruckId");

        azugaLast.AddOrUpdate(stateInfo.CompanyId, DateTime.Now, (key, oldValue) => DateTime.Now);

        Parallel.ForEach(truckKeys, new ParallelOptions() { MaxDegreeOfParallelism = 128 },
            async (dv) =>
            {
                try
                {
                    var assignedUnit = units.FirstOrDefault(o => o.VehicleId == dv.Value);

                    if (assignedUnit == null)
                    {
                        Console.WriteLine("Azuga/" + stateInfo.CompanyId + ": warning - no matched unit for " +
                            dv.Value);
                        return;
                    }
                    string status = null;

                    int? vehicleSpeed = null;
                    if (int.TryParse(assignedUnit.Speed?.Replace(" MPH", ""), out int speed) && speed > 0)
                    {
                        status = "Moving";
                        vehicleSpeed = speed;
                    }
                    else if (int.TryParse(assignedUnit.StopTime?.Replace(" mins", ""), out int stopTimeMins) && stopTimeMins > 0)
                    {
                        status = $"Stopped for {TimeSpan.FromMinutes(stopTimeMins).Humanize(2, minUnit: Humanizer.Localisation.TimeUnit.Second)}.";
                    }
                    else if (int.TryParse(assignedUnit.IdleTime?.Replace(" mins", ""), out int idleTimeMins))
                    {
                        if (assignedUnit.Event == "UNPAIR_MESSAGE")
                            status = $"Unit is unpaired";
                        else if (idleTimeMins > 0)
                            status = $"Idle for {TimeSpan.FromMinutes(idleTimeMins).Humanize(2, minUnit: Humanizer.Localisation.TimeUnit.Second)}.";
                    }

                    var ni = new TruckLocationHistoryItem
                    {
                        TruckId = dv.TruckId,
                        Latitude = assignedUnit.Latitude,
                        Longitude = assignedUnit.Longitude,
                        Timestamp = DateTime.Now, //assignedUnit.Timestamp.ToLocalTime(),
                        SourceId = stateInfo.SourceId,
                        Status = status,
                        Speed = vehicleSpeed
                    };

                    await ni.Save(stateInfo.CompanyId);
                    Console.WriteLine("Save " + ni.ToJson());
                }
                catch (Exception e)
                {
                    Console.WriteLine(e.Message);
                }
            });
    }

    private void executionTimer_Elapsed(object sender, System.Timers.ElapsedEventArgs e)
    {
        numberOfRunningThreads++;
        const string driverLocate = "driverlocate";
        const string azuga = "azuga";
        const string webfleet = "webfleet";
        const string usft = "usfleettracking";
        const string samsara = "samsara";
        const string verizonConnect = "fleetmatics";
        const string fleetcomplete = "fleetcomplete";
        const string geotab = "geotab";

        var types = new List<string>()
        {
            driverLocate,
            azuga,
            webfleet,
            usft,
            samsara,
            verizonConnect,
            fleetcomplete,
            geotab
        };

        if (_args.Any(o => o == "--azuga"))
        {
            types = new List<string>
            {
                azuga
            };
        }
        else if (_args.Any(o => o == "--samsara"))
        {
            types = new List<string>
            {
                samsara
            };
        }

        using (var queue = new BlockingCollection<GpsCollectionTask>())
        {
            // Call asynchronous network methods in a try/catch block to handle exceptions 
            try
            {
                if (types.Contains(geotab))
                {
                    int[] companyIds = CompanyKeyValue.GetByCompanyId(0, Provider.GeoTab.ProviderId, "AccountName")
                        .Select(o => o.CompanyId)
                        .Distinct()
                        .ToArray();
                    GpsCollectorRun.AddMissingBySource(1, companyIds);

                    companyIds = GpsCollectorRun.GetByCompanyIdSourceIds(100, 1).Select(o => o.CompanyId).ToArray();

                    foreach (int companyId in companyIds)
                    {
                        queue.Add(new GpsCollectionTask() { CompanyId = companyId, SourceId = 1 });
                    }
                }

                if (types.Contains(driverLocate))
                {
                    int[] driverLocateCompanyIds = CompanyKeyValue.GetByCompanyId(0, Provider.DriverLocate.ProviderId, "Username")
                        .Select(o => o.CompanyId)
                        .ToArray();
                    GpsCollectorRun.AddMissingBySource(3, driverLocateCompanyIds);

                    driverLocateCompanyIds = GpsCollectorRun.GetByCompanyIdSourceIds(100, 3).Select(o => o.CompanyId).ToArray();

                    foreach (var x in driverLocateCompanyIds)
                        queue.Add(new GpsCollectionTask() { CompanyId = x, SourceId = 3 });
                }

                if (types.Contains(webfleet))
                {
                    int[] tomTomIds = CompanyKeyValue.GetByCompanyId(0, Provider.TomTom.ProviderId, "Username")
                        .Select(o => o.CompanyId)
                        .Distinct()
                        .ToArray();

                    GpsCollectorRun.AddMissingBySource(2, tomTomIds);

                    tomTomIds = GpsCollectorRun.GetByCompanyIdSourceIds(100, 2).Select(o => o.CompanyId).ToArray();

                    foreach (var x in tomTomIds)
                        queue.Add(new GpsCollectionTask() { CompanyId = x, SourceId = 2 });
                }

                if (types.Contains(usft))
                {
                    int[] usftCompanyIds = CompanyKeyValue.GetByCompanyId(0, Provider.USFleetTracking.ProviderId, "Username")
                        .Select(o => o.CompanyId)
                        .Distinct()
                        .ToArray();
                    GpsCollectorRun.AddMissingBySource(4, usftCompanyIds);

                    usftCompanyIds = GpsCollectorRun.GetByCompanyIdSourceIds(100, 4).Select(o => o.CompanyId).ToArray();

                    foreach (var x in usftCompanyIds)
                        queue.Add(new GpsCollectionTask() { CompanyId = x, SourceId = 4 });
                }

                if (types.Contains(fleetcomplete))
                {
                    int[] fcCompanyIds = CompanyKeyValue.GetByCompanyId(0, Provider.FleetComplete.ProviderId, "Username")
                        .Select(o => o.CompanyId)
                        .Distinct()
                        .ToArray();
                    GpsCollectorRun.AddMissingBySource(5, fcCompanyIds);

                    fcCompanyIds = GpsCollectorRun.GetByCompanyIdSourceIds(100, 5).Select(o => o.CompanyId).ToArray();


                    foreach (var x in fcCompanyIds)
                        queue.Add(new GpsCollectionTask() { CompanyId = x, SourceId = 5 });
                }

                if (types.Contains(samsara))
                {
                    int[] samsaraCompanyIds = CompanyKeyValue.GetByCompanyId(0, Provider.Samsara.ProviderId, "AccessToken")
                        .Select(o => o.CompanyId)
                        .Distinct()
                        .ToArray();

                    GpsCollectorRun.AddMissingBySource(6, samsaraCompanyIds);

                    samsaraCompanyIds = GpsCollectorRun.GetByCompanyIdSourceIds(100, 6).Select(o => o.CompanyId).ToArray();

                    foreach (var x in samsaraCompanyIds)
                        queue.Add(new GpsCollectionTask() { CompanyId = x, SourceId = 6 });
                }

                if (types.Contains(azuga))
                {
                    int[] azugaCompanyIds = CompanyKeyValue.GetByCompanyId(0, Provider.Azuga.ProviderId, "ApiKey")
                        .Select(o => o.CompanyId)
                        .Distinct()
                        .ToArray();

                    GpsCollectorRun.AddMissingBySource(7, azugaCompanyIds);

                    azugaCompanyIds = GpsCollectorRun.GetByCompanyIdSourceIds(100, 7).Select(o => o.CompanyId).ToArray();

                    foreach (var x in azugaCompanyIds)
                    {
                        queue.Add(new GpsCollectionTask() { CompanyId = x, SourceId = 7 });
                    }
                }

                if (types.Contains(verizonConnect))
                {
                    int[] verizonConnectCompanyIds = CompanyKeyValue.GetByCompanyId(0, Provider.Fleetmatics.ProviderId, TokenGenerator.KEY_PASSWORD)
                        .Select(o => o.CompanyId)
                        .Distinct()
                        .ToArray();

                    GpsCollectorRun.AddMissingBySource(8, verizonConnectCompanyIds);

                    verizonConnectCompanyIds = GpsCollectorRun.GetByCompanyIdSourceIds(100, 8).Select(o => o.CompanyId).ToArray();

                    foreach (var x in verizonConnectCompanyIds)
                        queue.Add(new GpsCollectionTask() { CompanyId = x, SourceId = 8 });
                }

                queue.CompleteAdding();

                Parallel.ForEach(queue.GetConsumingEnumerable(), new ParallelOptions() { MaxDegreeOfParallelism = 128 },
                    async (r) =>
                    {
                        try
                        {
                            await ProcessQueueItem(r);
                        }
                        catch (Exception y)
                        {
                            Console.WriteLine(y.ToString());
                        }
                    });

                Console.Title = "last ran at : " + DateTime.Now.ToString();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception Message: {ex.Message} ");
                Console.WriteLine(ex.ToString());
                Console.WriteLine(ex.ToJson());
            }
            finally
            {
                numberOfRunningThreads--;

                if (types.Count == 1)
                    executionTimer.Interval = 1;

                executionTimer.Start();
            }
        }
    }

    protected void OnStop()
    {
        executionTimer.Enabled = false;
    }

    public Task StartAsync(CancellationToken cancellationToken)
    {
        logger.Info("StartAsync");

        OnStart(_args);

        logger.Info("End StartAsync");

        return Task.CompletedTask;
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        logger.Info("StopAsync");

        OnStop();

        logger.Info("End StopAsync");

        return Task.CompletedTask;
    }

    private void OnStopped()
    {
        logger.Info("Stopped successfully");
    }



    async ValueTask IAsyncDisposable.DisposeAsync()
    {
        if (executionTimer is IAsyncDisposable timer)
        {
            await timer.DisposeAsync();
        }
        LogManager.Shutdown();

        GC.SuppressFinalize(this);
    }
}
