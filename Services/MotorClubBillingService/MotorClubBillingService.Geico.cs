using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data;
using System.Linq;
using Extric.Towbook.Dispatch;
using Extric.Towbook.Integration;
using Extric.Towbook.Integration.MotorClubs;
using Extric.Towbook.Utility;
using Microsoft.Extensions.Hosting;
using NLog;
using Geico = Extric.Towbook.Integration.MotorClubs.Billing.Geico;

namespace Extric.Towbook.Services.MotorClubBillingService
{
    public partial class MotorClubBillingService : IHostedService
    {
        public static void GeicoSubmitInvoice(LogInfo logInfo, Entry entry, string username, string password, string poNumber)
        {
            if (string.IsNullOrWhiteSpace(poNumber))
                throw new NotFoundException("No PO number passed");

            poNumber = poNumber.ToUpperInvariant();

            if (!poNumber.StartsWith("G"))
                throw new NotFoundException("PO number must start with a G.");

            // Get conn
            var geico = Geico.GeicoConnection.Login(logInfo, username, password);

            var p = geico.GetDispatchInformation(poNumber);

            if (p == null)
            {
                throw new NotFoundException("PO does not exist on the Geico site.");
            }
            //Console.WriteLine(p.ToJson(true));

            var po = p.InvoiceList.Where(o => o.Version == "Current").FirstOrDefault();
                       
            //if (!logInfo.TestMode && Core.GetRedisValue("directBilling:geico:enable:" + entry.CompanyId) != "1")
            //    throw new NotSubmittableException($"Direct Billing to Geico is currently unavailable. ");

            if (po.InvoiceStatus == "Submitted")
            {
                LogEvent(logInfo, $"PO is already Submitted according to GEICO website.", LogLevel.Info);
                return;
            }
            entry.Invoice.ForceRecalculate();

            int idGen = 1;
            string uniqueId(string name)
            {
                idGen++;
                return name + idGen;
            }

            if (po.InvoiceStatus != "Pending" &&
                po.InvoiceStatus != "Returned to Provider")
            {
                throw new CancelledException($"Status on Geico site is '{po.InvoiceStatus}'");
            }
            else
            {
                var sendInvoiceNumber = CompanyKeyValue.GetFirstValueOrNull(entry.CompanyId, Provider.Towbook.ProviderId, "MCBilling_UseInvoiceNumber") == "1";
                po.InvoiceDetails.ProviderReference = sendInvoiceNumber ? (entry.InvoiceNumber ?? entry.CallNumber.ToString()) : entry.CallNumber.ToString();

                // Set the total we expect Geico to calculate for this PO, and a copy of the original total
                po.ExpectedTotal = po.TowbookTotal = entry.InvoiceTotal;

                //--------------------------------------------------------------------------------------------------------
                // Invoice item filtering:  save and remove all ii's that aren't valid for reverse- or forward-matching
                //--------------------------------------------------------------------------------------------------------

                // Get list of entry invoice items & reverse the order to smooth out future matching
                var invoiceItems = entry.InvoiceItems.Reverse().ToCollection();

                //var foundGOA = false;  
                var unloadedMileageItems = new List<InvoiceItem>();
                var loadedMileageItems = new List<InvoiceItem>();
                var deadHeadMileageItems = new List<InvoiceItem>();

                if (po.IsGOA && !entry.Reason.IsGoa())
                {
                    throw new NotSubmittableException(
                        $"PO is marked as a GOA on GEICO website. It's reason in Towbook is set to {entry.Reason.Name}. " +
                        $"Change to GOA and resubmit.");
                }

                for (var x = invoiceItems.Count - 1; x >= 0; x--)
                {
                    var ii = invoiceItems[x];
                    var item = new { Name = ii.CustomName, Price = ii.Price, Quantity = ii.Quantity, Total = ii.Total, RateItemId = ii.RateItem?.RateItemId ?? 0 }.ToJson();
                    var itemName = ii.Name.ToLowerInvariant().Replace("-", " ").Replace("  ", " ").Replace(".", "").Trim();
                    var rateItemName = (ii.RateItem?.Name ?? "").ToLowerInvariant().Replace("-", " ").Replace("  ", " ").Replace(".", "").Trim();

                    // Check if its unloaded
                    if (itemName.Contains("unloaded mil") ||
                        itemName.Contains("enroute mil") ||
                        itemName.Contains("en route mil") ||
                        ii.RateItem?.Predefined?.Id == 1)
                    {
                        unloadedMileageItems.Add(ii);
                        invoiceItems.RemoveAt(x);
                        continue;
                    }

                    // Check if its loaded
                    if (itemName.Contains("loaded mil") ||
                        itemName.Contains("hooked mil") ||
                        ii.RateItem?.Predefined?.Id == 2)
                    {
                        loadedMileageItems.Add(ii);
                        invoiceItems.RemoveAt(x);
                        continue;
                    }

                    // If its a discount, then ignore it
                    if (itemName.StartsWith("discount:"))
                    {
                        logInfo.AddEvent($"Ignoring Discount: {item}", LogLevel.Warn);

                        // If its total is positive, deduct it from the total we expect Geico to calculate
                        if (ii.Total > 0)
                            po.ExpectedTotal -= ii.Total;
                        invoiceItems.RemoveAt(x);
                        continue;
                    }

                    // If its GOA, note that we have one
                    if (itemName.Contains("goa") ||
                        itemName.Contains("gone on arrival"))
                    {
                        //foundGOA = true;
                        // Don't remove it though.  It may be needed in reverse-matching, below.
                        continue;
                    }

                    // If its a deadhead mileage item
                    if (itemName.Contains("dhm") || rateItemName.Contains("dhm") ||
                        itemName.Contains("deadhead") || rateItemName.Contains("deadhead") ||
                        itemName.Contains("dead head") || rateItemName.Contains("dead head") ||
                        itemName == "negotiated additional charge" || rateItemName == "negotiated additional charge" ||
                        itemName == "extra" || rateItemName == "extra")
                    {
                        deadHeadMileageItems.Add(ii);
                        invoiceItems.RemoveAt(x);
                        continue;
                    }

                    // If its a customer overage charge, ignore it
                    if (itemName.Contains("customer overage") ||
                        itemName.Contains("customer paid") ||
                        itemName.Contains("customer to pay") ||
                        itemName.Contains("overage") ||
                        itemName.Contains("overmil") ||
                        itemName.Contains("over mil") ||
                        itemName.Contains("out of pocket") ||
                        ii.ClassId == ChargeClass.Cash || // overage
                        ii.ClassId == ChargeClass.Reimbursement) // reimbursements
                    {
                        logInfo.AddEvent($"Ignoring {item} because it is a CUSTOMER OVERAGE charge", LogLevel.Warn);

                        // Deduct it from the total we expect Geico to calculate
                        po.ExpectedTotal -= ii.Total;
                        invoiceItems.RemoveAt(x);
                        continue;
                    }
                    if (itemName.Contains("adjustment to reflect payment difference"))
                    {
                        logInfo.AddEvent($"Ignoring {item} because it is an adjustment from a payment import", LogLevel.Warn);

                        // Deduct it from the total we expect Geico to calculate
                        po.ExpectedTotal -= ii.Total;
                        invoiceItems.RemoveAt(x);
                        continue;
                    }
                }

                //-------------------------------------------------------------------------------------------------
                // Reverse-matching Services (by name):  Match each preloaded service to an ii, using their names
                //-------------------------------------------------------------------------------------------------

                // For each service on this PO
                foreach (var svc in po.PrimaryServiceDetails)
                {
                    // If there is an ii that matches this service
                    var ii = GeicoGetMatch(p.InvoiceTypeList, svc, invoiceItems);
                    if (ii != null)
                    {
                        // svc.Taxable = (ii.Taxable && entry.InvoiceTax > 0) ? "Yes" : "No";
                        svc.MatchFound = true;

                        if (svc.Amount > ii.CustomPrice)
                        {
                            po.ExpectedTotal -= ii.Total;

                            ii.CustomPrice = svc.Amount;
                            po.ExpectedTotal += ii.Total;
                        }

                        // Don't add it to the invoice later; it's already on it
                        invoiceItems.Remove(ii);

                        // Diff ii and service totals
                        GeicoServiceDiff(logInfo, po, svc, ii);
                    }
                }

                //-------------------------------------------------------------------------------------------------
                // Reverse-matching Charges (by name):  Match each preloaded charge to an ii, using their names
                //-------------------------------------------------------------------------------------------------

                // For each service on this PO
                foreach (var svc in po.PrimaryServiceDetails)
                {
                    // For each charge on this service 
                    foreach (var chg in svc.AdditionalCharges.Select(o => o as Geico.AdditionalCharge))
                    {
                        // If there is an ii that matches this charge
                        var ii = GeicoGetMatch(p.InvoiceTypeList, chg, invoiceItems);
                        if (ii != null)
                        {
                            //  chg.taxable = (ii.Taxable && entry.InvoiceTax > 0) ? "Yes" : "No";
                            chg.MatchFound = true;

                            // Don't add it to the invoice later; it's already on it
                            invoiceItems.Remove(ii);

                            // Diff ii and charge totals
                            GeicoChargeDiff(logInfo, po, svc, chg, ii);
                        }
                    }
                }

                //----------------------------------------------------------------------------------------------------
                // Reverse-matching Services (by total):  Match each preloaded service to an ii, using their totals
                //----------------------------------------------------------------------------------------------------

                // For each unmatched service
                foreach (var svc in po.PrimaryServiceDetails.Where(s => !s.MatchFound))
                {
                    // If there is an ii whose total is equal to the service
                    var ii = invoiceItems.Reverse().FirstOrDefault(i => i.Total == svc.Amount);
                    if (ii != null)
                    {
                        svc.MatchFound = true;
                        invoiceItems.Remove(ii);
                    }
                }

                //----------------------------------------------------------------------------------------------------
                // Reverse-matching Charges (by total):  Match each preloaded charge to an ii, using their totals
                //----------------------------------------------------------------------------------------------------

                // For each unmatched, preloaded charge
                foreach (var chg in po.PrimaryServiceDetails.SelectMany(s => s.AdditionalCharges).Where(c => !c.MatchFound))
                {
                    // If there is an ii whose total is equal to the charge
                    var ii = invoiceItems.Reverse().FirstOrDefault(i => i.Total == chg.amount);
                    if (ii != null)
                    {
                        chg.MatchFound = true;
                        invoiceItems.Remove(ii);
                    }
                }

                //---------------------------------------------------
                // Reverse-matching Unmatched Services and Charges
                //---------------------------------------------------

                // If multiple unmatched services
                var services = po.PrimaryServiceDetails.Where(s => !s.MatchFound);
                if (services.Any())
                {
                    var friendlyName = Core.FormatName(services.First().Service.Replace("svc_", ""));
                    var reason = entry.Reason.Name;

                    throw new MatchingException("Unable to match between Towbook Invoice and GEICO. Listed as " + friendlyName + " on GEICO, Reason is " +
                        reason + " in Towbook.");
                }

                // If multiple unmatched charges
                var charges = po.PrimaryServiceDetails.SelectMany(s => s.AdditionalCharges).Where(c => !c.MatchFound);
                if (charges.Any())
                {
                    throw new MatchingException("Unable to find Towbook invoice item matches for multiple charges on Geico purchase order:" +
                        string.Join(",", charges.Select(o => o.charge)));
                }

                //---------------------------------------------------------------------------------------
                // Forward-matching:  Match each ii to either a service or charge and add it to the PO
                //---------------------------------------------------------------------------------------

                int additionalChargeIdCounter = 0;

                // For each invoice item
                foreach (var ii in invoiceItems)
                {
                    var item = new { ii.Name, ii.Price, ii.Quantity, ii.Total, RateItemId = ii.RateItem?.RateItemId ?? 0 }
                        .ToJson();
                    var itemName = ii.Name.ToLowerInvariant().Replace("-", " ").Replace("  ", " ").Replace(".", "").Trim();

                    // If its GOA or its total is 0, skip over it
                    if ((itemName.Contains("goa") || 
                         itemName.Contains("gone on arrival")) && 
                         !itemName.Contains("cat service call"))
                    {
                        continue;
                    }
                    else if (ii.Total == 0)
                    {
                        continue;
                    }

                    if (!ReimbursementShouldSubmit(logInfo, ii, entry))
                        continue;

                    // If it might be an invoice charge
                    var charge = FromInvoiceTypeListCharge(itemName);
                    var chargeDescriptor = p.InvoiceTypeList.Charges.Where(o => o.Value == charge).FirstOrDefault();
                    var addonRate = p.DispatchRates.AddonRates.Where(o => o.AddonType == charge).FirstOrDefault();

                    if (addonRate == null && charge != "tolls")
                        throw new MatchingException("Unable to find Geico Addon Rate match for "
                            + itemName + " / " + charge);

                    if (chargeDescriptor == null)
                        throw new MatchingException("Unable to find Geico Charge match for " + itemName + " / " + charge);

                    if (charge != null)
                    {
                        var newCharge = new Geico.AdditionalCharge()
                        {
                            charge = charge,
                            type = chargeDescriptor.Type,
                            feeType = charge != "tolls" ? charge : null,
                            taxable = ii.Taxable ? "Yes" : "No",
                            hours = 0,
                            days = 0,
                            rate = addonRate?.Rate ?? 0,
                            hasChangesMade = true,
                            id = "AdditionalCharges" + (++additionalChargeIdCounter),
                            status = "New",
                            friendlyStatus = "New",
                            amountType = 2
                        };

                        // If we don't already have this charge (Geico only allows one of each charge)
                        if (!po.PrimaryServiceDetails.Any(s => s.AdditionalCharges.Any(c => c.charge == charge)))
                        {
                            // I think our implementation works, but it hasnt been tested, so don't allow it for now.
                            //throw new MatchingException("Towbook doesn't support adding additional services like " + charge + 
                            //    " yet. Please manually submit on Geico website. ");

                            // If it's a wait time or labor (Geico prompts for hours/minutes/rate for just these)
                            if (newCharge.charge == "waittime" ||
                                newCharge.charge == "labor")
                            {
                                // Users sometimes enter price and quantity backwards, so the best assumption we
                                // can make is that the smaller number is the rate per minute, and the larger number 
                                // is the number of minutes. We then multiply and divide by 60 to get hours and hourly rate.
                                if (ii.Price < ii.Quantity)
                                {
                                    // TODO: need to test this.
                                    newCharge.rate = ii.Price * 60m; // we shoudlnt be setting this... should come from geico  invoiceTypeList.addonRates
                                    newCharge.hours = (int)Math.Floor(ii.Quantity / 60m);
                                }
                                else
                                {
                                    // TODO: need to test this.
                                    newCharge.rate = ii.Quantity * 60m; // we shoudlnt be setting this. .. should come from geico 
                                    newCharge.hours = (int)Math.Floor(ii.Price / 60m);
                                }
                            }

                            newCharge.note = ii.Name;
                            newCharge.amount = ii.Total;

                            // Add it to the primary service on the PO
                            po.PrimaryServiceDetails[0].AdditionalCharges.Add(newCharge);
                            continue;
                        }
                    }

                    // If it might be a service
                    var serviceName = FromInvoiceTypeListService(itemName);
                    if (serviceName.Any())
                    {
                        // If we don't already have this service (Geico only allows one of each service)
                        if (!po.PrimaryServiceDetails.Any(s => serviceName.Contains(s.Service)))
                        {
                            throw new MatchingException("Adding of additional primary service: " + serviceName + " not yet supported. Please manually submit");
                            /*
                            var service = new Geico.Primaryservicedetail();
                            // Add it to the PO
                            service.Service = serviceName;
                            service.Amount = ii.Total;
                            service.Comments = ii.Name;
                            service.AmountChanged = true;
                            service.hasChangesMade = true;
                            po.PrimaryServiceDetails.Add(service);

                            continue; */
                        }
                    }

                    if (itemName.Replace(" ", "").Contains("payout"))
                    {
                        if (po.PayoutFees.Sum(o => o.amount) == ii.Total)
                        {
                            Console.WriteLine("Payout fees match");
                            continue;
                        }
                    }

                    throw new MatchingException("Couldn't match: " + $"{ii.Name.Trim()}:  {ii.Quantity:0.##} @ {ii.Price:0.00} = {ii.Total:C}");
                }

                var oldTax = entry.Invoice.Tax;

                foreach (var miles in unloadedMileageItems)
                {
                    po.ExpectedTotal -= miles.Total;
                    miles.Quantity = Math.Ceiling(miles.Quantity);
                    po.ExpectedTotal += miles.Total;
                }

                foreach (var miles in loadedMileageItems)
                {
                    po.ExpectedTotal -= miles.Total;
                    miles.Quantity = Math.Ceiling(miles.Quantity);
                    po.ExpectedTotal += miles.Total;
                }

                foreach (var miles in deadHeadMileageItems)
                {
                    po.ExpectedTotal -= miles.Total;
                    miles.Quantity = Math.Ceiling(miles.Quantity);
                    po.ExpectedTotal += miles.Total;
                } 

                entry.Invoice.ForceRecalculate();
                po.ExpectedTotal = po.ExpectedTotal - oldTax + entry.Invoice.Tax;

                // Get mileages
                var unloadedMiles = Math.Ceiling(unloadedMileageItems.Where(i => i.Price > 0 && i.Quantity > 0).Sum(i => i.Quantity));
                var unloadedRate = unloadedMileageItems.Where(i => i.Price > 0 && i.Quantity > 0).Select(i => i.Price).FirstOrDefault();
                var unloadedTotal = unloadedMileageItems.Sum(i => i.Total);
                var unloadedTotalActual = unloadedMileageItems.Sum(i => i.Total);

                if (unloadedTotal != unloadedTotalActual)
                    po.ExpectedTotal = po.ExpectedTotal - unloadedTotalActual + unloadedTotal;

                var loadedMiles = Math.Ceiling(loadedMileageItems.Where(i => i.Price > 0 && i.Quantity > 0).Sum(i => i.Quantity));
                var loadedRate = loadedMileageItems.Where(i => i.Price > 0 && i.Quantity > 0).Select(i => i.Price).FirstOrDefault();
                var loadedTotal = loadedMileageItems.Sum(i => i.Total);
                var loadedTotalActual = loadedMileageItems.Sum(i => i.Total);
                if (loadedTotal != loadedTotalActual)
                    po.ExpectedTotal = po.ExpectedTotal - loadedTotalActual + loadedTotal;

                var freeUnloadedMiles = Convert.ToInt32(unloadedMileageItems.Where(i => (i.CustomName ?? "").Contains("FreeQuantity")).Sum(f => f.Quantity));
                var freeUnloadedTotal = freeUnloadedMiles * unloadedRate;

                var freeLoadedMiles = Convert.ToInt32(loadedMileageItems.Where(i => (i.CustomName ?? "").Contains("FreeQuantity")).Sum(f => f.Quantity));
                var freeLoadedTotal = freeLoadedMiles * loadedRate;

                // Get deadhead mileage
                var deadheadMiles = Math.Ceiling(deadHeadMileageItems.Where(i => i.Price > 0 && i.Quantity > 0).Sum(i => i.Quantity));
                var deadheadRate = deadHeadMileageItems.Where(i => i.Price > 0 && i.Quantity > 0).Select(i => i.Price).FirstOrDefault();
                var deadheadTotal = deadHeadMileageItems.Sum(i => i.Total);
                var deadheadTotalActual = deadHeadMileageItems.Sum(i => i.Total);

                if (deadheadTotal != deadheadTotalActual)
                    po.ExpectedTotal = po.ExpectedTotal - deadheadTotalActual + deadheadTotal;

                var freeDeadheadMiles = Convert.ToInt32(deadHeadMileageItems.Where(i => (i.CustomName ?? "").Contains("FreeQuantity")).Sum(f => f.Quantity));
                var deadheadTaxable = deadHeadMileageItems.FirstOrDefault()?.Taxable ?? false;
                var unloadedTaxable = unloadedMileageItems.FirstOrDefault()?.Taxable ?? false;
                var loadedTaxable = loadedMileageItems.FirstOrDefault()?.Taxable ?? false;

                // If there are no unloaded miles
                if (CheckUnloadedMilesForceToOne(entry, unloadedMiles, unloadedTotal, logInfo))
                    unloadedMiles = 1;

                // If there is unloaded mileage on the PO
                var m = po.MileageDetails.FirstOrDefault(o => o.type == "En-Route");
                if (m != null)
                {
                    var newMiles = (int)Math.Ceiling(unloadedMiles); // Always round up
                    var newTotal = (newMiles - m.freeMiles) * m.rate;
                    // Set its amount
                    if (m.miles != newTotal ||
                        m.total != newTotal)
                    {
                        m.miles = newMiles;
                        m.total = newTotal;
                        if (m.total < 0)
                            m.total = 0;
                        m.hasChangesMade = true;
                        m.amountChanged = true;
                        m.canEdit = true;
                        m.declineReason = null;
                    }
                }
                else
                {
                    po.MileageDetails.Add(new Geico.Mileagedetail
                    {
                        id = uniqueId("AddMileage"),
                        type = "En-Route",
                        miles = (int)Math.Ceiling(unloadedMiles), // Always round up
                        rate = unloadedRate,
                        freeMiles = freeUnloadedMiles,
                        status = "Pending",
                        friendlyStatus = "Pending",
                        hasChangesMade = true,
                        amountChanged = true,
                        declineReason = null,
                        canEdit = true,
                        taxable = unloadedTaxable ? "Yes" : "No",
                        total = unloadedTotal,
                        rateType = 2
                    });
                }

                // If there is loaded mileage on the PO
                m = po.MileageDetails.FirstOrDefault(o => o.type == "Loaded");
                if (m != null)
                {
                    var newMiles = (int)Math.Ceiling(loadedMiles); // Always round up
                    var newTotal = (newMiles - m.freeMiles) * m.rate;
                    // Set its amount
                    if (m.miles != newMiles ||
                        m.total != newTotal)
                    {
                        m.miles = newMiles;
                        m.total = newTotal;
                        if (m.total < 0)
                            m.total = 0;
                        m.hasChangesMade = true;
                        m.canEdit = true;
                        m.amountChanged = true;
                        m.declineReason = null;
                    }
                }
                else if (loadedMiles > 0)
                {
                    po.MileageDetails.Add(new Geico.Mileagedetail
                    {
                        id = uniqueId("AddMileage"),
                        type = "Loaded",
                        miles = (int)Math.Ceiling(loadedMiles), // Always round up
                        rate = loadedRate,
                        freeMiles = freeLoadedMiles,
                        status = "Pending",
                        friendlyStatus = "Pending",
                        hasChangesMade = true,
                        declineReason = null,
                        taxable = loadedTaxable ? "Yes" : "No",
                        total = loadedTotal,
                        rateType = 2
                    });
                }

                // If deadhead is already on the PO
                m = po.MileageDetails.FirstOrDefault(o => o.type == "Deadhead");
                if (m != null)
                {
                    var newMiles = (int)Math.Ceiling(deadheadMiles); // Always round up
                    var newTotal = (newMiles - m.freeMiles) * m.rate;
                    // Set its amount
                    if (m.miles != newTotal ||
                        m.total != newTotal)
                    {
                        m.miles = newMiles;
                        m.total = newTotal;
                        if (m.total < 0)
                            m.total = 0;
                        m.hasChangesMade = true;
                    }
                }
                else if (deadheadMiles > freeDeadheadMiles)
                {
                    // Add it to the PO
                    po.MileageDetails.Add(new Geico.Mileagedetail
                    {
                        id = uniqueId("AddMileage"),
                        type = "Deadhead",
                        miles = (int)Math.Ceiling(deadheadMiles), // Always round up
                        rate = deadheadRate,
                        freeMiles = freeDeadheadMiles,
                        status = "Pending",
                        friendlyStatus = "Pending",
                        taxable = deadheadTaxable ? "Yes" : "No",
                        total = deadheadTotal,
                        rateType = 2,
                        hasChangesMade = true,
                        declineReason = "",
                        note = ""
                    });
                }

                if (entry.InvoiceTax > 0) // && po.PrimaryServiceDetails.All(o => o.Taxable == "Yes" && !o.IsTaxable))
                {
                    // items aren't taxable but should be. 
                    foreach (var psd in po.PrimaryServiceDetails)
                    {
                        psd.AdditionalCharges.Add(new Geico.AdditionalCharge()
                        {
                            amount = entry.InvoiceTax,
                            amountType = 2,
                            charge = "taxes",
                            note = "Sales Tax",
                            taxable = "No",
                            type = "fees",
                            status = "New",
                            friendlyStatus = "New",
                            hours = 0,
                            minutes = 0,
                            rate = 0, 
                            days = 0,
                            declineReason = "",
                            id = "AdditionalCharges" + (psd.AdditionalCharges.Count() + 1),
                            hasChangesMade = true
                        });
                        //psd.hasChangesMade = true;
                        //psd.Note = "Added Sales Tax";
                    }
                }

                po.TotalInvoiceAmount = po.PayoutFees.Sum(o => o.amount) +
                    po.MileageDetails.Sum(o => o.total) +
                    po.PrimaryServiceDetails.Sum(o => o.Amount) +
                    po.PrimaryServiceDetails.Sum(o => o.AdditionalCharges.Sum(r => r.amount.GetValueOrDefault()));

                po.PendingTotal = po.MileageDetails.Where(o => o.status == "Pending" || o.status == "New")
                    .Sum(o => o.total) + po.PayoutFees.Where(o => o.status == "Pending" || o.status == "New").Sum(o => o.amount) +
                    po.PrimaryServiceDetails.Where(o => o.Status == "Pending" || o.Status == "New").Sum(o => o.Amount) +
                    po.PrimaryServiceDetails.Where(o => o.Status == "Pending" || o.Status == "New")
                    .Sum(o => o.AdditionalCharges.Where(rx => rx.status == "Pending" || rx.status == "New")
                    .Sum(r => r.amount.GetValueOrDefault()));

                foreach (var s in po.PrimaryServiceDetails)
                {
                    s.DeclineReason = null;
                }

                po.Version = p.InvoiceList.Count().ToString();

                var result2 = geico.RecalculateInvoice(po);

                LogEvent(logInfo, $"RecalculateInvoiceResponse", LogLevel.Trace,
                    new Dictionary<object, object>
                    {
                        ["response"] = result2.ToJson()
                    });

                foreach (var px in result2.Result.invoice.PrimaryServiceDetails)
                {
                    foreach(var x in px.AdditionalCharges)
                    {
                        var originalPSD = po.PrimaryServiceDetails.Where(r => r.id == px.id).FirstOrDefault();
                        if (originalPSD != null)
                        {
                            var originalAD = originalPSD.AdditionalCharges.Where(o => o.id == x.id).FirstOrDefault();
                            originalAD.uniqueId = x.uniqueId;
                        }
                    }
                }


                foreach (var pa in result2.Result.primaryServicesAudit)
                {
                    var adMatch = po.PrimaryServiceDetails
                        .Where(o => o.AdditionalCharges.Any(c => c.id == pa.ChargeId)).FirstOrDefault()?
                        .AdditionalCharges.Where(o => o.id == pa.ChargeId).FirstOrDefault();

                    if (adMatch != null)
                    {
                        adMatch.haveAuditIssues = true;
                        adMatch.hasAuditIssues = false;
                        adMatch.hasChangesMade = true;
                        adMatch.auditMessage = pa.AuditMessage;
                        adMatch.auditIssues = Array.Empty<object>();
                        adMatch.declineReason = "";
                    }
                    else
                    {
                        throw new Exception("couldn't associate " + pa.ToJson());
                    }
                }
                try
                {
                    var po2 = Newtonsoft.Json.JsonConvert.DeserializeObject<Extric.Towbook.Integration.MotorClubs.Billing.Geico2.InvoiceList>
                        (po.ToJson());

                    if (result2.IsSuccessful)
                    {
                        if (result2.Result.invoice.TotalInvoiceAmount != po.ExpectedTotal)
                        {
                            throw new NotSubmittableException($"GEICO Invoice Total is different than Towbook: Geico=" +
                                    result2.Result.invoice.TotalInvoiceAmount.ToString("C") + ", Towbook=" +
                                po.ExpectedTotal.ToString("C"));
                        }
                    }
                    else
                    {
                        var guid = Guid.NewGuid().ToString("N").Truncate(10);

                        LogEvent(logInfo, $"RecalculateInvoice Failure", LogLevel.Error,
                            new Dictionary<object, object>
                            {
                                ["errorId"] = guid,
                                ["response"] = result2.ToJson()
                            });

                        throw new NotSubmittableException($"We ran into a problem while communicating with the GEICO website. Please contact Towbook with this error ID: " + guid);
                    }

                    Console.WriteLine(result2.ToJson(true));

                    if (logInfo.TestMode)
                    {
                        Console.WriteLine("Exiting, we're in test mode.");
                        LogEvent(logInfo, $"Skipped submission to Geico. We're in Test Mode.", LogLevel.Info);
                        return;
                    }
                    

                    // Submit the purchase order
                    // Send it the PO we retrieved, and the HTML used to retrieve it to avoid fetching it twice
                    var submitTotal = geico.SaveAdjustedInvoice(po2, logInfo.TestMode);

                    if (submitTotal == null)
                    {
                        var errorGuid = Guid.NewGuid().ToString("N").Truncate(10);

                        LogEvent(logInfo, $"SaveAdjustedInvoice returned null", LogLevel.Error,
                            new Dictionary<object, object>
                            {
                                ["errorId"] = errorGuid,
                                ["recalculateInvoice"] = result2,
                                ["dispatchInformation"] = po,
                            });

                        throw new NotSubmittableException($"GEICO website returned an empty response while submitting invoice. Please try re-submitting. (ID: " + errorGuid + ")");
                    }

                    LogEvent(logInfo, $"Diagnostic Data", LogLevel.Info,
                        new Dictionary<object, object>
                        {
                            ["dispatchInformation"] = po,
                            ["recalculateInvoice"] = result2,
                            ["saveAdjustedInvoice"] = submitTotal,
                        });

                    Console.WriteLine("Response:" + submitTotal.ToJson(true));

                    var en = new EntryNote() { DispatchEntryId = entry.Id, OwnerUserId = 1 };
                    en.Content = "Submitted motor club job to GEICO (Atlas)...\n\nTestMode=" + logInfo.TestMode + "\n\n";
                    en.Content += "InvoiceAmount: " + entry.InvoiceTotal.ToString("C") + "\n";
                    en.Content += "Services: " + po.PrimaryServiceDetails.ToJson(true) + "\n";
                    en.Content += "Mileages: " + po.MileageDetails.ToJson(true) + "\n\n";
                    en.Content += "Unloaded Miles: " + unloadedMiles + "\n";
                    en.Content += "Loaded Miles: " + loadedMiles + "\n";
                    en.Content += "Deadhead Miles: " + deadheadMiles + "\n";
                    en.Save();

                    // Flush logs if requested
                    if (logInfo.FlushRequested)
                        logInfo.FlushEvents(logger);

                    LogEvent(logInfo, $"Submitted to Geico: ${submitTotal.ToJson()}, NoteId: {en.Id}", LogLevel.Info);
                }
                catch (Exception y)
                {
                    LogEvent(logInfo, $"Fatal Error Submitting: " + y.Message, LogLevel.Fatal,
                        new Dictionary<object, object>
                        {
                            ["exception"] = y
                        });

                    Console.WriteLine(y.ToJson());

                    throw;
                }

                geico.Release();
            }
        }





        private static void GeicoServiceDiff(LogInfo logInfo, Geico.GeicoInvoice po, Geico.Primaryservicedetail svc, InvoiceItem ii)
        {
            var diff = (ii.Total - svc.Amount);
            if (diff > 0 && !svc.Goa) // if the Towbook amount is more than the Geico amount for this service (and the service is not GOA)
            {
                throw new BillingException($"Geico amount of {svc.Amount:0.00} is less than the amount in Towbook of {ii.Total:0.00} for line item {ii.Name}");
            }
            else if (diff < 0) // if the Towbook amount is less than the Geico amount for this service
            {
                if ((svc.Amount - ii.Total) < 50)
                    return;

                throw new BillingException($"Geico amount of {svc.Amount:0.00} is more than the amount in Towbook of {ii.Total:0.00} for line item {ii.Name}. Update invoice in Towbook to match and re-submit.");

                /*
                // Adjust the expectedTotal by the difference
                po.ExpectedTotal += (-diff);

                // Log an event for this
                logInfo.AddEvent($"Towbook's item total of {ii.Total} for '{svc.Type}' is less than the Geico service total of {svc.Amount}", LogLevel.Info); */
            }
        }

        private static void GeicoChargeDiff(LogInfo logInfo, Geico.GeicoInvoice po, Geico.Primaryservicedetail svc, Geico.AdditionalCharge chg, InvoiceItem ii)
        {
            // If the Towbook amount is more than the Geico amount for this charge
            var diff = (ii.Total - chg.amount);
            if (diff > 0)
            {
                throw new BillingException($"Geico amount of {chg.amount:0.00} is less than the amount in Towbook of {ii.Total:0.00} for line item {ii.Name}");
            }

            // If the Towbook amount is less than the Geico amount for this charge
            else if (diff < 0)
            {
                throw new BillingException($"Geico amount of {chg.amount:0.00} is more than the amount in Towbook of {ii.Total:0.00} for line item {ii.Name}. Update invoice in Towbook to match and re-submit.");
/*
                // Adjust the expectedTotal by the difference
                po.ExpectedTotal += (-diff);

                // Log an event for this
                logInfo.AddEvent($"Towbook's item total of {ii.Total} for '{chg.Charge}' is less than the Geico invoice charge total of {chg.Amount}", LogLevel.Info);
                */
            }
        }


        private static string[] FromInvoiceTypeListService(string itemName)
        {
            var matches = new List<string>();

            if (((itemName.Contains("goa") && !itemName.Contains("goal")) ||
                (itemName.Contains("gone") && itemName.Contains("arrival"))))
                matches.Add("svc_goa");

            if (itemName.Contains("tow") && !itemName.Contains("goa"))
                matches.Add("svc_tow");

            if ((itemName.Contains("flat") || itemName.Contains("tire")) &&
                (itemName.Contains("change") || itemName.Contains("service")))
                matches.Add("svc_flat");

            if ((itemName.Contains("fuel") || itemName.Contains("fluid") || itemName.Contains("fuild") || itemName.Contains("gas")) &&
                !itemName.Contains("clean"))
                matches.Add("svc_fuel");

            if (itemName.Contains("jump") || itemName.Contains("start"))
                matches.Add("svc_jump");

            if (itemName.Contains("locksmith"))
                matches.Add("svc_locksmith");

            if (itemName.Contains("lockout") ||
                itemName.Contains("lock out") ||
                itemName.Contains("unlock") ||
                itemName.Contains("un lock"))
                matches.Add("svc_lockout");

            if (itemName.Contains("maintenance"))
                matches.Add("svc_labor");

            if (itemName.Contains("winch"))
                matches.Add("svc_winch");

            return matches.ToArray();
        }



        private static string FromInvoiceTypeListCharge(string itemName)
        {
            // Equipment charges

            if ((itemName.Contains("additional") || itemName.Contains("addt") || itemName.Contains("add'l")) && itemName.Contains("truck"))
                return "additltruck";

            if (itemName.Contains("dolly") || itemName.Contains("dollies"))
                return "dollies";

            if ((itemName.Contains("other") || itemName.Contains("special")) && itemName.Contains("equip"))
                return "otherspleqp";

            if (itemName.Contains("ramp"))
                return "ramps";

            if (itemName.Contains("skate") || itemName.Contains("ski"))
                return "skatesskis";

            if (itemName.Contains("snatch") && itemName.Contains("block"))
                return "snatchblks";

            // Fees charges

            if (itemName.Contains("access"))
                return "access";

            if (itemName.Contains("admin"))
                return "adminfee";

            if ((itemName.Contains("fuel") || itemName.Contains("fluid") || itemName.Contains("fuild") || itemName.Contains("gas")) &&
                !itemName.Contains("clean"))
                return "fuelsurchrg";

            if (itemName.Contains("gate"))
                return "gatefee";

            if (itemName.Contains("invoice") && itemName.Contains("correction"))
                return "invoicecorectin";

            if (itemName.Contains("recovery"))
                return "recoveryfee";

            if (itemName.Contains("restrict") && itemName.Contains("roadway"))
                return "restrctdrdway";

            if (itemName.Contains("storage"))
                return "storgfee";

            // THIS INVOICE CHARGE RESERVED FOR SUBMITTING TOTAL TAX FOR THE WHOLE PO
            //if (itemName.Contains("tax"))
            //    matches.Add(Charge.Taxes);

            if (itemName.Contains("toll"))
                return "tolls";

            if (itemName.Contains("wait"))
                return "waittime";

            // Labor charges

            if ((itemName.Contains("additional") || itemName.Contains("addt") || itemName.Contains("add'l")) && itemName.Contains("hook"))
                return "addtlhookup";

            if (itemName.Contains("extra man") || itemName.Contains("extra person"))
                return "extraperson";

            if (itemName.Contains("glass") && itemName.Contains("clean"))
                return "glassclnup";

            if (itemName.Contains("labor") || itemName.Contains("battery")) // includes battery installs
                return "labor";

            if (itemName.Contains("push") || itemName.Contains("pull"))
                return "pullout";

            if (itemName.Contains("scene") && itemName.Contains("clean"))
                return "sceneclnup";

            if ((itemName.Contains("speed") || itemName.Contains("dry") || itemName.Contains("fluid")) &&
                itemName.Contains("clean"))
                return "spedydryflud";

            if (itemName.Contains("winch"))
                return "winching";

            return null;
        }

        private static InvoiceItem GeicoGetMatch(Geico.InvoiceTypeList list, Geico.Primaryservicedetail svc, Collection<InvoiceItem> items)
        {
            InvoiceItem match = null;

            var serviceCode = svc.Service;
            var serviceItem = list.Services.Where(o => o.Value == serviceCode).FirstOrDefault();

            if (serviceItem == null)
            {
                throw new MatchingException("Unknown Service Code:" + serviceCode);
            }

            Console.WriteLine(serviceItem.ToJson(true));

            string[] last = null;
            foreach (var ii in items)
            {
                var itemName = ii.Name.ToLowerInvariant().Replace("-", " ").Replace("  ", " ").Replace(".", "").Trim();

                var service = FromInvoiceTypeListService(itemName);
                if (service != null)
                    last = service;

                if (service.Any(o => o == serviceCode))
                {
                    return ii;
                }
            }

            if (last != null && last.Any())
            {
                var towbook = list.Services.FirstOrDefault(o => last.Contains(o.Value))?.Text ??
                    list.Fees.FirstOrDefault(o => last.Contains(o.Value))?.Text;

                if (towbook == null)
                    towbook = Core.FormatName(last.First().Replace("svc_", ""));

                if (towbook == "Goa")
                    towbook = "GOA";

                throw new MatchingException("Call is listed as a " + towbook + " in Towbook, but is listed as a " +
                    serviceItem.Text + " on GEICO's website.");
            }

            return match;
        }

        private static InvoiceItem GeicoGetMatch(Geico.InvoiceTypeList list, Geico.AdditionalCharge chg, Collection<InvoiceItem> items)
        {
            InvoiceItem match = null;

            var chargeCode = chg.charge;
            var chargeItem = list.Charges.Where(o => o.Value == chargeCode).FirstOrDefault();

            foreach (var ii in items)
            {
                var itemName = ii.Name.ToLowerInvariant().Replace("-", " ").Replace("  ", " ").Replace(".", "").Trim();

                var towbookCharge = FromInvoiceTypeListCharge(itemName);

                if (towbookCharge != null)
                {
                    return ii;
                }
            }

            return match;
        }
    }
}
