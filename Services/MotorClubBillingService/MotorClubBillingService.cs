using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using Azure.Messaging.ServiceBus;
using Extric.Towbook.Accounts;
using Extric.Towbook.Dispatch;
using Extric.Towbook.Integration;
using Extric.Towbook.Integration.MotorClubs.Queue;
using Extric.Towbook.Integration.MotorClubs.Services;
using Extric.Towbook.Storage;
using Extric.Towbook.Utility;
using HtmlAgilityPack;
using Microsoft.Extensions.Configuration;
using NLog;
using AgeroDB = Extric.Towbook.Integration.MotorClubs.Billing.Agero;
using Allstate = Extric.Towbook.Integration.MotorClubs.Billing.Allstate;
using Async = System.Threading.Tasks;
using Geico = Extric.Towbook.Integration.MotorClubs.Billing.Geico;
using Nsd = Extric.Towbook.Integration.MotorClubs.Billing.Nsd;
using Quest = Extric.Towbook.Integration.MotorClubs.Billing.Quest;
using RoadAmerica = Extric.Towbook.Integration.MotorClubs.Billing.RoadAmerica;
using Extric.Towbook.EventNotifications;
using Microsoft.Extensions.Hosting;
using System.Reflection;
using System.Threading.Tasks;
using LogInfo = Extric.Towbook.Integration.MotorClubs.LogInfo;

namespace Extric.Towbook.Services.MotorClubBillingService;

public partial class MotorClubBillingService : IHostedService
{
    private static readonly Logger logger = LogManager.GetCurrentClassLogger();

    private int maxThreads = 10;
    
    private string[] _args;
    int _queueItemId = 0;

    private IDictionary<string, Extric.Towbook.EventNotifications.Consumer> cqs = new ConcurrentDictionary<string, Extric.Towbook.EventNotifications.Consumer>();
    private readonly IConfiguration _configuration;
    public MotorClubBillingService(string[] args, IHostApplicationLifetime applicationLifetime, IConfiguration configuration)
    {
        try
        {
            _args = args;
            applicationLifetime.ApplicationStopped.Register(OnStopped);
            _configuration = configuration;

        }
        catch (Exception ex)
        {
            logger.Log(LogLevel.Error, "Application " + ex.ToString());
        }
    }

    public Task StartAsync(CancellationToken cancellationToken)
    {
        logger.Info("MotorClubBillingService Start Async ...");
        logger.LogEvent("Application Starting ...", 1, LogLevel.Info, new Dictionary<object, object>
        {
            ["coreVersion"] = Core.GetCoreVersion(),
            ["path"] = Assembly.GetExecutingAssembly().Location
        });
        OnStart(_args);
        logger.Info("MotorClubBillingService Started successfully");

        return Task.CompletedTask;
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        logger.Info("MotorClubBillingService Stop Async ...");
        OnStop();
        return Task.CompletedTask;
    }

    protected void OnStart(string[] args)
    {
        bool disablePush = false;
        for (int i = 0; i < args.Length; i++)
        {
            if (args[i] == "/threads" || args[i] == "/t")
                maxThreads = Convert.ToInt32(args[i + 1]);
            if (args[i].ToLowerInvariant() == "/disablepush" || args[i] == "/dp")
                disablePush = true;

            if (args[i] == "/qi")
            {
                _queueItemId = Convert.ToInt32(args[i + 1]);
                disablePush = true;
            }
        }

        if (_configuration != null && _configuration.GetValue<string>("ConnectionStrings:Microsoft.ServiceBus").Contains("dev-towbook"))
            logger.Info("Connected to dev-towbook");

        AsyncHelper.RunSync(() => Init(disablePush));

        // Start up the purchase order list queue
        //PurchaseOrderListEnqueue();
    }

    public async Async.Task Init(bool disablePush)
    {
        if (!disablePush)
        {
            await Async.Task.WhenAll(
                PurchaseOrderListSync(),
                DispatchEntryDetailsAsync(),
                DispatchEntryAsync()
            );
        }
        if (_queueItemId > 0)
        {
            var messageProperties = new Dictionary<string, object>();
            messageProperties.Add("id", _queueItemId);
            var qu = DispatchEntryQueueItem.GetById(_queueItemId);
            messageProperties.Add("companyId", qu.CompanyId);

            ServiceBusReceivedMessage message = ServiceBusModelFactory.ServiceBusReceivedMessage(
                    body: new BinaryData(" "),
                    messageId: "_queueItemId_" + _queueItemId,
                    properties: messageProperties
            );

            ProcessMessageEventArgs processArgs = new ProcessMessageEventArgs(
                message: message,
                receiver: new FakeServiceBusReceiver(),
                cancellationToken: CancellationToken.None);
            _ = Task.Run(async () => await oma(processArgs));
        }
        else
        {
            await Async.Task.WhenAll(
                AuthenticationAsync(),
                PaymentAsync()
            );
        }
    }

    public async Async.Task PurchaseOrderListSync()
    {
        var options = new EventNotifications.OnMessageOptions()
        {
            AutoCompleteMessages = false,
            MaxConcurrentCalls = 4 // 1 during testing, use 16 during production, maybe pull this from app.config?
            ,ReceiveMode = (Azure.Messaging.ServiceBus.ServiceBusReceiveMode)ReceiveMode.PeekLock
        };

        var cqPO = await ServiceBusHelper.CreateConsumerQueueAsync(PurchaseOrderListService.PurchaseOrdersQueueName, options);
        cqs.Add(PurchaseOrderListService.PurchaseOrdersQueueName, cqPO);
        await cqPO.OnMessage(async (r) =>
        {
            // Get logInfo from BrokeredMessage
            var logInfo = new LogInfo(r.Message.MessageId, 0, false, "Motor Club");
            if (r.Message.ApplicationProperties.ContainsKey("companyId"))
            {
                int companyId = 0;
                int.TryParse(r.Message.ApplicationProperties["companyId"].ToString(), out companyId);
                logInfo.CompanyId = companyId;
            }

            // Get logInfo ServiceType
            logInfo.ServiceType = (int)ServiceType.PurchaseOrderListService;

            // Start processing message
            var body = r.GetBody<string>();
            LogEvent(logInfo, $"Processing request to retrieve PO list from motor club, Body: {body}", LogLevel.Info);

            if (r.Message.ApplicationProperties.ContainsKey("id"))
            {
                var id = Convert.ToInt32(r.Message.ApplicationProperties["id"]);
                var x = PurchaseOrderListQueueItem.GetById(id);
                if (x == null)
                {
                    await DeadLetter(logInfo, $"{r.Message.MessageId}: DeadLettered because PurchaseOrderListQueueItem with id: {id} does not exist.", r);
                    return;
                }

                if (r.Message.ApplicationProperties.ContainsKey("force"))
                    x.Force = true;

                // Get logInfo from PurchaseOrderListQueueItem
                logInfo.CompanyId = x.CompanyId;
                logInfo.TestMode = false;

                try
                {
                    var account = await Account.GetByIdAsync(x.AccountId);
                    var company = await Company.Company.GetByIdAsync(x.CompanyId);
                    var masterAccount = await MasterAccount.GetByIdAsync(account.MasterAccountId);
                    if (masterAccount == null)
                        throw new Exception("Account doesn't have a MasterAccount set");

                    var masterAccountName = MasterAccountTypes.GetName(masterAccount.Id);

                    // Get logInfo from account
                    logInfo.AccountId = account.Id;
                    logInfo.MotorClubName = masterAccountName;

                    PurchaseOrderListQueueItem.UpdateStatus(x, Extric.Towbook.Integration.MotorClubs.Queue.QueueItemStatus.AboutToStart);

                    // Important - Log the PO list we're about to retrieve
                    LogEvent(logInfo,
                        $"Preparing to retrieve PO list from {masterAccountName}, " +
                        $"Company: {company.Id} ({company.Name}), " +
                        $"Account: {account.Id} ({account.Company}), " +
                        $"PurchaseOrderListQueueItem: {x.ToJson()}",
                        LogLevel.Warn);

                    string username = AccountKeyValue.GetByAccount(x.CompanyId, x.AccountId, Provider.Towbook.ProviderId, "McUsername").FirstOrDefault().Value;
                    string password = AccountKeyValue.GetByAccount(x.CompanyId, x.AccountId, Provider.Towbook.ProviderId, "McPassword").FirstOrDefault().Value;
                    string providerId = AccountKeyValue.GetByAccount(x.CompanyId, x.AccountId, Provider.Towbook.ProviderId, "ProviderId").FirstOrDefault()?.Value;

                    // If PO matching is not enabled for this account, exit
                    var enablePoMatching = AccountKeyValue.GetByAccount(account.CompanyId, account.Id, Provider.Towbook.ProviderId, "EnablePoMatching").FirstOrDefault();
                    if (enablePoMatching == null || enablePoMatching.Value != "1")
                    {
                        await DeadLetter(logInfo, $"{r.Message.MessageId}: DeadLettered because PO matching is not enabled for account: {account.Id}", r);
                        return;
                    }

                    switch (masterAccount.Id)
                    {
                        case MasterAccountTypes.Agero:

                            // Create conn
                            var agero = AgeroDB.AgeroConnection.Login(username, password);
                            agero.LogHttpTraffic = false;

                            // Mark all existing agero PO's in the database as deleted
                            AgeroDB.PurchaseOrder.DeleteAll(x.CompanyId, x.AccountId);

                            // Get list of available PO's
                            LogEvent(logInfo, $"Getting available PO's", LogLevel.Info);
                            foreach (var poNumber in agero.GetAvailablePOs(logInfo))
                            {
                                try
                                {
                                    // If we have this PO already
                                    var po = AgeroDB.PurchaseOrder.GetByPurchaseOrderNumber(logInfo.AccountId, poNumber);
                                    if (po != null)
                                    {
                                        // Mark it as undeleted
                                        LogEvent(logInfo, $"PO # {poNumber} - already have it", LogLevel.Info);
                                        po.Save();
                                    }

                                    // If we have a dispatch entry with this PO number
                                    else if (await Entry.GetByPurchaseOrderNumberAsync(logInfo.AccountId, poNumber) != null)
                                    {
                                        // Don't bother getting it (it's not a missing invoice), just save its PO number so we know its available
                                        LogEvent(logInfo, $"PO # {poNumber} - not missing", LogLevel.Info);
                                        po = new AgeroDB.PurchaseOrder();
                                        po.CompanyId = logInfo.CompanyId;
                                        po.AccountId = logInfo.AccountId;
                                        po.PurchaseOrderNumber = poNumber;
                                        po.Save();
                                    }

                                    else
                                    {
                                        // Get it and save it to the database
                                        LogEvent(logInfo, $"PO # {poNumber} - getting from {masterAccountName} website", LogLevel.Info);
                                        agero.GetPurchaseOrder(logInfo, poNumber, true);
                                    }
                                }
                                catch (Exception e)
                                {
                                    LogEvent(logInfo, $"{logInfo.MotorClubName} -- Failed to get PO # {poNumber} because of exception \"{e.Message}\" -- {e}", LogLevel.Error);
                                }
                            }

                            break;

                        case MasterAccountTypes.Geico:

                            // Create conn
                            var geico = Geico.GeicoConnection.Login(logInfo, username, password);
                            geico.LogHttpTraffic = false;

                            // Mark all existing geico PO's in the database as deleted
                            Geico.PurchaseOrder.DeleteAll(x.CompanyId, x.AccountId);

                            // Get list of available PO's
                            LogEvent(logInfo, $"Getting available PO's", LogLevel.Info);
                            foreach (var poItem in geico.GetAvailablePOs(logInfo))
                            {
                                try
                                {
                                    // If we have this PO already
                                    var po = Geico.PurchaseOrder.GetByPurchaseOrderNumber(logInfo.AccountId, poItem.DispatchNumber);
                                    if (po != null)
                                    {
                                        // Mark it as undeleted
                                        LogEvent(logInfo, $"PO # {poItem.DispatchNumber} - already have it", LogLevel.Info);
                                        po.Save();
                                    }

                                    // If we have a dispatch entry with this PO number
                                    else if (await Entry.GetByPurchaseOrderNumberAsync(logInfo.AccountId, poItem.DispatchNumber) != null)
                                    {
                                        // Don't bother getting it (it's not a missing invoice), just save its PO number so we know its available
                                        LogEvent(logInfo, $"PO # {poItem.DispatchNumber} - not missing", LogLevel.Info);
                                        po = new Geico.PurchaseOrder();
                                        po.CompanyId = logInfo.CompanyId;
                                        po.AccountId = logInfo.AccountId;
                                        po.PurchaseOrderNumber = poItem.DispatchNumber;
                                        po.Save();
                                    }
                                    else
                                    {
                                        // Get it and save it to the database
                                        LogEvent(logInfo, $"PO # {poItem.DispatchNumber} - getting from {masterAccountName} website", LogLevel.Info);

                                        geico.GetDispatchInformation(poItem.DispatchNumber);
                                    }
                                }
                                catch (Exception e)
                                {
                                    LogEvent(logInfo, $"{logInfo.MotorClubName} -- Failed to get PO # {poItem.DispatchNumber} because of exception \"{e.Message}\" -- {e}", LogLevel.Error);
                                }
                            }
                            geico.Release();
                            break;

                        case MasterAccountTypes.Allstate:
                            // Create conn
                            var allstate = Allstate.AllStateConnection.Login(logInfo, username, password, providerId);
                            try
                            {
                                allstate.LogHttpTraffic = false;

                                // Mark all existing allstate PO's in the database as deleted
                                Allstate.PurchaseOrder.DeleteAll(x.CompanyId, x.AccountId);

                                // Get list of available PO's
                                LogEvent(logInfo, $"Getting available PO's", LogLevel.Info);
                                foreach (var poItem in allstate.GetAvailablePOs(logInfo))
                                {
                                    try
                                    {
                                        // If we have this PO already
                                        var po = Allstate.PurchaseOrder.GetByPurchaseOrderNumber(logInfo.AccountId, poItem.PurchaseOrderNumber);
                                        if (po != null)
                                        {
                                            // Mark it as undeleted
                                            LogEvent(logInfo, $"PO # {poItem.PurchaseOrderNumber} - already have it", LogLevel.Info);
                                            po.Save();
                                        }

                                        // If we have a dispatch entry with this PO number
                                        else if (await Entry.GetByPurchaseOrderNumberAsync(logInfo.AccountId, poItem.PurchaseOrderNumber) != null)
                                        {
                                            // Don't bother getting it (it's not a missing invoice), just save its PO number so we know its available
                                            LogEvent(logInfo, $"PO # {poItem.PurchaseOrderNumber} - not missing", LogLevel.Info);
                                            po = new Allstate.PurchaseOrder();
                                            po.CompanyId = logInfo.CompanyId;
                                            po.AccountId = logInfo.AccountId;
                                            po.PurchaseOrderNumber = poItem.PurchaseOrderNumber;
                                            po.Save();
                                        }

                                        else
                                        {
                                            // Get it and save it to the database
                                            LogEvent(logInfo, $"PO # {poItem.PurchaseOrderNumber} - getting from {masterAccountName} website", LogLevel.Info);
                                            var html = new HtmlDocument();
                                            allstate.GetPurchaseOrder(logInfo, poItem.DispatchId, true, out html);
                                        }
                                    }
                                    catch (Exception e)
                                    {
                                        LogEvent(logInfo, $"{logInfo.MotorClubName} -- Failed to get PO # {poItem.PurchaseOrderNumber} because of exception \"{e.Message}\" -- {e}", LogLevel.Error);
                                    }
                                }
                            }
                            finally
                            {
                                allstate.ReleaseLock(logInfo);
                            }
                            break;

                        default:
                            throw new Exception($"No GetAvailablePOs() implementation for {masterAccountName}");
                    }
                }
                catch (Exception e)
                {
                    HandlePurchaseOrderListQueueException(logInfo, $"Failed because of exception \"{e.Message}\" -- {e}", e, x);
                }
                finally
                {
                    // Submission succeeded, mark as completed
                    PurchaseOrderListQueueItem.UpdateStatus(x, Extric.Towbook.Integration.MotorClubs.Queue.QueueItemStatus.Completed);
                    LogEvent(logInfo, $"Finished processing", LogLevel.Info);
                    await r.CompleteAsync();

                    // If we didn't force it to run, call this again tomorrow at 8:00 AM
                    if (!x.Force)
                    {
                        var pols = new PurchaseOrderListService();
                        await pols.RequestPurchaseOrderListUpdate(x.AccountId, DateTime.Today.AddDays(1).AddHours(8));
                    }
                }
            }
            else
            {
                await DeadLetter(logInfo, $"{r.Message.MessageId}: DeadLettered because it doesn't contain an id property.", r);
            }
        });
    }

    public async Async.Task DispatchEntryDetailsAsync()
    {
        var options = new Extric.Towbook.EventNotifications.OnMessageOptions()
        {
            AutoCompleteMessages = false,
            MaxConcurrentCalls = 1, // 1 during testing, use 16 during production, maybe pull this from app.config?
            ReceiveMode = (Azure.Messaging.ServiceBus.ServiceBusReceiveMode)ReceiveMode.PeekLock
        };

        var cqDED = await ServiceBusHelper.CreateConsumerQueueAsync(DispatchEntryService.DetailsQueueName, options);
        cqs.Add(DispatchEntryService.DetailsQueueName, cqDED);
        await cqDED.OnMessage(async (r) => 
        {
            var body = r.GetBody<string>();
            logger.LogEvent("Processing request to retrieve Entry from MC.. Message: Id = {0}, Body = {1}", null, LogLevel.Info, r.Message.MessageId, body);

            if (r.Message.ApplicationProperties.ContainsKey("id"))
            {
                logger.LogEvent("Processing request  Id = {0}, Body = {1}", null, LogLevel.Info, r.Message.ApplicationProperties["id"], body);

                var x = DispatchEntryDetailsQueueItem.GetById(Convert.ToInt32(r.Message.ApplicationProperties["id"]));
                try
                {
                    var acc = await Account.GetByIdAsync(x.AccountId);
                    var masterAccount = await MasterAccount.GetByIdAsync(acc.MasterAccountId);

                    x.Status = DispatchEntryDetailsQueueItemStatus.AboutToStart;
                    x.Save();
                
                    if (masterAccount == null)
                    {
                        await r.DeadLetterAsync();
                        Console.WriteLine("deadletter!");
                        logger.LogEvent("Queue item doesn't have a MasterAccount set. MessageId {0}, Body = {1}", x.CompanyId, LogLevel.Error, r.Message.MessageId, x.ToJson());
                        return;
                    }

                    string username = AccountKeyValue.GetByAccount(x.CompanyId, x.AccountId, Provider.Towbook.ProviderId, "McUsername").FirstOrDefault().Value;
                    string password = AccountKeyValue.GetByAccount(x.CompanyId, x.AccountId, Provider.Towbook.ProviderId, "McPassword").FirstOrDefault().Value;
                    string providerId = AccountKeyValue.GetByAccount(x.CompanyId, x.AccountId, Provider.Towbook.ProviderId, "ProviderId").FirstOrDefault()?.Value;

                    var logInfo = new LogInfo(r.Message.MessageId, x.CompanyId, false);

                    switch (masterAccount.Id)
                    {
                        case MasterAccountTypes.Agero:
                            logger.LogEvent("Processing Agero... MessageId {0}, Body = {1}", x.CompanyId, LogLevel.Error, r.Message.MessageId, x.ToJson());

                            var agero = AgeroDB.AgeroConnection.Login(username, password);

                            foreach (string po in x.PurchaseOrderNumbers.Split(','))
                                agero.GetPurchaseOrder(logInfo, po, true);

                            break;

                        case MasterAccountTypes.Geico:
                            break;

                        case MasterAccountTypes.Allstate:
                            logger.LogEvent("Processing Allstate... MessageId {0}, Body = {1}", x.CompanyId, LogLevel.Error, r.Message.MessageId, x.ToJson());

                            var allstate = Allstate.AllStateConnection.Login(logInfo, username, password, providerId);
                            try
                            {
                                HtmlDocument html;
                                foreach (string po in x.PurchaseOrderNumbers.Split(','))
                                    allstate.GetPurchaseOrder(logInfo, Convert.ToInt32(po), true, out html);
                            }
                            finally
                            {
                                allstate.ReleaseLock(logInfo);
                            }

                            break;

                        default:
                            await r.DeadLetterAsync("Queue item doesn't have a MasterAccount set that we have a sync details implementation written for", "");
                            x.Status = DispatchEntryDetailsQueueItemStatus.Error;
                            logger.LogEvent("Queue item doesn't have a MasterAccount set that we have a sync details implementation written for... MessageId {0}, Body = {1}", x.CompanyId, LogLevel.Error, r.Message.MessageId, x.ToJson());
                            return;
                    }

                    x.Status = DispatchEntryDetailsQueueItemStatus.Completed;
                    await r.CompleteAsync();

                    logger.LogEvent("Finished processing " + r.Message.MessageId);
                }
                catch (Exception ex)
                {
                    logger.LogEvent("Error processing: " + r.Message.MessageId + "..." + ex.ToString());
                    await r.DeadLetterAsync();
                    logger.LogEvent("DeadLettered " + r.Message.MessageId + " because of exception " + ex.Message);

                    x.Status = DispatchEntryDetailsQueueItemStatus.Error;
                    throw;
                }
                finally
                {
                    if (x != null)
                        x.Save();
                }
            }
        });
    }

    public async Async.Task DispatchEntryAsync()
    {
        var options = new Extric.Towbook.EventNotifications.OnMessageOptions()
        {
            AutoCompleteMessages = false,
            MaxConcurrentCalls = 4 // 1 during testing, use 16 during production, maybe pull this from app.config?
            , ReceiveMode = (Azure.Messaging.ServiceBus.ServiceBusReceiveMode)ReceiveMode.PeekLock
        };

        var cqDE = await ServiceBusHelper.CreateConsumerQueueAsync(DispatchEntryService.QueueName, options);
        cqs.Add(DispatchEntryService.QueueName, cqDE);
        //await cqDE.OnMessage( async (r) =>
        await cqDE.OnMessage(oma);
    }
    public async Task oma(ProcessMessageEventArgs r)
    {
        // Get logInfo from BrokeredMessage
        var logInfo = new LogInfo(r.Message.MessageId, 0, false, "Motor Club");
        if (r.Message.ApplicationProperties.ContainsKey("companyId"))
        {
            int companyId = 0;
            int.TryParse(r.Message.ApplicationProperties["companyId"].ToString(), out companyId);
            logInfo.CompanyId = companyId;
        }

        // Get logInfo ServiceType
        logInfo.ServiceType = (int)ServiceType.DispatchEntryService;

        // Start processing message
        var body = r.GetBody<string>();
        LogEvent(logInfo, $"Processing request to submit invoice to motor club",
            LogLevel.Info);

        if (r.Message.ApplicationProperties.ContainsKey("id"))
        {
            var x = DispatchEntryQueueItem.GetById(Convert.ToInt32(r.Message.ApplicationProperties["id"]));

            // Get logInfo from DispatchEntryQueueItem
            logInfo.CompanyId = x.CompanyId;
            logInfo.TestMode = x.TestMode;

            Entry entry = null;
            MasterAccount masterAccount = null;

            try
            {
                entry = await Entry.GetByIdAsync(x.DispatchEntryId);
                var poNumber = entry.PurchaseOrderNumber.ToUpperInvariant();
                masterAccount = await MasterAccount.GetByIdAsync(entry.Account.MasterAccountId);
                if (masterAccount == null)
                    throw new Exception("Call doesn't have an account with a MasterAccount set");

                var motorClubName = MasterAccountTypes.GetName(masterAccount.Id);
                if (masterAccount.Id == MasterAccountTypes.Geico)
                {
                    if (poNumber.StartsWith("G"))
                        motorClubName += " (Atlas)";
                    else
                        motorClubName += " (Legacy)";
                }

                // Get logInfo from entry
                logInfo.AccountId = entry.AccountId;
                logInfo.DispatchEntryId = entry.Id;
                logInfo.MotorClubName = motorClubName;
                logInfo.CallNumber = entry.CallNumber;
                logInfo.PoNumber = poNumber;

                DispatchEntryQueueItem.UpdateStatus(x, Extric.Towbook.Integration.MotorClubs.Queue.QueueItemStatus.AboutToStart);

                // Important - Log the PO we're about to submit
                LogEvent(logInfo,
                    $"Preparing to submit invoice to to {motorClubName}, " +
                    $"PO: {entry.PurchaseOrderNumber}, " +
                    $"TestMode: {logInfo.TestMode}, " +
                    $"InvoiceNumber: {entry.InvoiceNumber}, " +
                    $"InvoiceTotal: {entry.InvoiceTotal:C}, " +
                    $"InvoiceTax: {entry.InvoiceTax:C}, " +
                    $"InvoiceItem Count: {entry.InvoiceItems.Count}, " +
                    $"DispatchEntryQueueItem: {x.ToJson()}",
                    LogLevel.Info, 
                    new Dictionary<object, object>()
                    {
                        ["masterAccountId"] = entry.Account.MasterAccountId,
                        ["masterAccountName"] = MasterAccountTypes.GetName(entry.Account.MasterAccountId),
                        ["companyId"] = entry.CompanyId,
                        ["companyName"] = entry.Company.Name,
                        ["accountId"] = entry.AccountId,
                        ["callId"] = entry.Id,
                        ["callNumber"] = entry.CallNumber,
                        ["poNumber"] = entry.PurchaseOrderNumber,
                        ["queueItemId"] = x.Id
                    });

                // Log the invoice items so we have a record of what we attempted to submit
                logInfo.AddEvent($"InvoiceItems: {InvoiceItemsToJson(entry.InvoiceItems)}", LogLevel.Info);

                // If it was already completed, log a warning and exit
                if (x.Status == Extric.Towbook.Integration.MotorClubs.Queue.QueueItemStatus.Completed)
                {
                    logInfo.AddEvent($"This call is already marked as complete, thus no work will be done. ", LogLevel.Warn);
                    await r.CompleteAsync();
                    return;
                }

                string username = AccountKeyValue.GetFirstValueOrNull(entry.CompanyId, entry.AccountId, Provider.Towbook.ProviderId, "McUsername");
                string password = AccountKeyValue.GetFirstValueOrNull(entry.CompanyId, entry.AccountId, Provider.Towbook.ProviderId, "McPassword");
                string providerId = AccountKeyValue.GetFirstValueOrNull(entry.CompanyId, entry.AccountId, Provider.Towbook.ProviderId, "ProviderId");

                if (string.IsNullOrWhiteSpace(poNumber))
                    throw new UserErrorException("Call doesn't have a PO # set");

                if (masterAccount.Id != MasterAccountTypes.Fleetnet)
                    if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(password))
                        throw new UserErrorException($"Company doesn't have a username/password set for AccountId: {entry.AccountId}");

                switch (masterAccount.Id)
                {
                    case MasterAccountTypes.Agero:
                        DispatchEntrySyncAgero(logInfo, entry, username, password, poNumber);
                        break;

                    case MasterAccountTypes.Geico:
                        GeicoSubmitInvoice(logInfo, entry, username, password, poNumber);
                        break;

                    case MasterAccountTypes.Allstate:
                        DispatchEntrySyncAllstate(logInfo, entry, username, password, providerId, poNumber);
                        break;

                    case MasterAccountTypes.RoadAmerica:
                        await DispatchEntrySyncRoadAmerica(logInfo, entry, username, password, poNumber);
                        break;

                    case MasterAccountTypes.Nsd:
                        DispatchEntrySyncNsd(logInfo, entry, username, password, poNumber);
                        break;

                    case MasterAccountTypes.Quest:
                        DispatchEntrySyncQuest(logInfo, entry, username, password, providerId, poNumber);
                        break;

                    case MasterAccountTypes.Fleetnet:
                        var fbs = new FleetnetBillingService(logInfo, entry);
                        await fbs.SubmitInvoice();
                        break;

                    default:
                        throw new Exception($"No SubmitPurchaseOrder() implementation for {motorClubName}");
                }

                // Flush logs if requested
                if (logInfo.FlushRequested)
                    logInfo.FlushEvents(logger);

                // Submission succeeded, mark as submitted
                DispatchEntryQueueItem.UpdateStatus(x, Extric.Towbook.Integration.MotorClubs.Queue.QueueItemStatus.Completed, "", SubmitResult.Submitted);
                LogEvent(logInfo, $"Finished processing", LogLevel.Info);
                await r.CompleteAsync();
            }
            catch (CancelledException e)
            {
                // Submission succeeded, mark as cancelled
                DispatchEntryQueueItem.UpdateStatus(x, Integration.MotorClubs.Queue.QueueItemStatus.Completed, e.Message, SubmitResult.Cancelled);
                LogEvent(logInfo, $"Submission cancelled; {e.Message}", LogLevel.Info);
                LogEvent(logInfo, $"Finished processing", LogLevel.Info);
                await r.CompleteAsync();
                }
                catch (NotSubmittableException nse)
                {
                    Console.WriteLine(nse.ToJson(true));
                    
                    if (masterAccount?.Id == MasterAccountTypes.Fleetnet)
                    {
                        // Submission error, mark as not submittable and complete
                        DispatchEntryQueueItem.UpdateStatus(x, Integration.MotorClubs.Queue.QueueItemStatus.Error, nse.Message, SubmitResult.NotSubmittable);
                        LogEvent(logInfo, $"Submission failed; {nse.Message}", LogLevel.Info);
                        LogEvent(logInfo, $"Finished processing", LogLevel.Info);
                        await r.CompleteAsync();
                    }
                    else
                    {
                        // handle like general exception
                        await HandleDispatchEntryQueueException(logInfo, nse, x, r);
                    }
            }
            catch (Exception e)
            {
                await HandleDispatchEntryQueueException(logInfo, e, x, r);
            }
            finally
            {
                // Remove any changes that were made to the entry, such as edits to invoice items
                // to aid in processing submission
                if (entry != null)
                    Entry.CacheClearById(entry.Id);
            }
        }
        else
        {
            await r.CompleteAsync();
            LogEvent(logInfo, $"DeadLettered because it doesn't contain an id property.", LogLevel.Info);
        }
    }

    public async Async.Task AuthenticationAsync()
    {
        var options = new Extric.Towbook.EventNotifications.OnMessageOptions()
        {
            AutoCompleteMessages = false,
            MaxConcurrentCalls = 16
            ,ReceiveMode = (Azure.Messaging.ServiceBus.ServiceBusReceiveMode)ReceiveMode.PeekLock
        };

        var cqAV = await ServiceBusHelper.CreateConsumerQueueAsync(AuthenticationVerificationService.QueueName, options);
        cqs.Add(AuthenticationVerificationService.QueueName, cqAV);
        await cqAV.OnMessage(async (r) =>
        {
            logger.LogEvent($"{r.Message.MessageId}: Processing request to verify login details for account, Body: {r.GetBody<string>()}", null, LogLevel.Info);

            if (r.Message.ApplicationProperties.ContainsKey("id"))
            {
                var x = AuthenticationQueueItem.GetById(Convert.ToInt32(r.Message.ApplicationProperties["id"]));
                var masterAccount = await MasterAccount.GetByIdAsync(Account.GetById(x.AccountId).MasterAccountId);
                AuthenticationQueueItem.UpdateStatus(x, AuthenticationQueueItemStatus.AboutToStart);

                try
                {
                    if (masterAccount == null)
                    {
                        await r.DeadLetterAsync();
                        logger.LogEvent($"{r.Message.MessageId}: Queue item doesn't have a MasterAccount set", x.CompanyId, LogLevel.Error);

                        await PushNotificationProvider.BackgroundJobStatusUpdate(x.CompanyId,
                            x.Id,
                            "billing_login",
                            true,
                            "Account supplied doesn't have a MasterAccountId set", null);
                    }

                    var username = x.Username;
                    var password = x.Password;
                    var masterAccountName = MasterAccountTypes.GetName(masterAccount.Id);
                    var providerId = AccountKeyValue.GetByAccount(x.CompanyId, x.AccountId, Provider.Towbook.ProviderId, "ProviderId").FirstOrDefault()?.Value;

                    var logInfo = new LogInfo(r.Message.MessageId, x.CompanyId, false, "Motor Club")
                    {
                        MotorClubName = masterAccountName,
                        AccountId = x.AccountId,
                    };

                    logger.LogEvent($"{r.Message.MessageId}: Attempting to login to {masterAccountName} for account: {x.AccountId}, with username: \"{username}\", password: \"{password}\", providerId: {providerId ?? "NULL"}", 
                        x.CompanyId, LogLevel.Warn);

                    switch (masterAccount.Id)
                    {
                        case MasterAccountTypes.Agero:
                            AgeroDB.AgeroConnection.Login(username, password);
                            break;

                        case MasterAccountTypes.Geico:
                            Geico.GeicoConnection.Login(logInfo, username, password, false);
                            break;

                        case MasterAccountTypes.Allstate:
                            Allstate.AllStateConnection.Login(logInfo, username, password, providerId).ReleaseLock(logInfo);
                            break;

                        case MasterAccountTypes.Nsd:
                            Nsd.NsdConnection.Login(logInfo, username, password, false);
                            break;

                        case MasterAccountTypes.Quest:
                            Quest.QuestConnection.Login(logInfo, username, password, providerId).ReleaseLock(logInfo);
                            break;

                        case MasterAccountTypes.RoadAmerica:
                            RoadAmerica.RoadAmericaConnection.Login(logInfo, username, password, false);
                            break;

                        default:
                            throw new Exception($"No Login() implementation for {masterAccountName}");
                    }

                    // Authentication succeeded, save credentials to db
                    SaveAccountKeyValue(x.CompanyId, x.AccountId, "McUsername", username);
                    SaveAccountKeyValue(x.CompanyId, x.AccountId, "McPassword", password);
                    await r.CompleteAsync();

                    // Log success
                    logger.LogEvent($"{r.Message.MessageId}: Login succeeded", x.CompanyId, LogLevel.Info);

                    // Mark as completed
                    AuthenticationQueueItem.UpdateStatus(x, AuthenticationQueueItemStatus.Completed);

                    // Create request to retrieve the list of available PO's for this account
                    var pols = new PurchaseOrderListService();
                    await pols.RequestPurchaseOrderListUpdate(x.AccountId);

                    // Push notification - succeeded
                    await PushNotificationProvider.BackgroundJobStatusUpdate(x.CompanyId,
                        x.Id,
                        "billing_login",
                        true,
                        "Successfully logged in with supplied credentials.", null);
                }

                catch (InvalidLoginException e)
                {
                    await HandleAuthenticationQueueException($"{r.Message.MessageId}: Failed to login because of invalid username/password.", e, x, r);
                }
                catch (Exception e)
                {
                    await HandleAuthenticationQueueException($"{r.Message.MessageId}: Failed to login because of error: \"{e.Message}\" -- {e}", e, x, r);
                }
            }
            else
            {
                await r.DeferMessageAsync(r.Message);
                logger.LogEvent("DeadLettered " + r.Message.MessageId + " because it doesn't contain an id property.", null, LogLevel.Error);
            }
        });
    }

    public async Async.Task PaymentAsync()
    {
        var options = new Extric.Towbook.EventNotifications.OnMessageOptions()
        {
            AutoCompleteMessages = false,
            MaxConcurrentCalls = 16
            ,ReceiveMode = (Azure.Messaging.ServiceBus.ServiceBusReceiveMode)ReceiveMode.PeekLock
        };

        var cqPS = await ServiceBusHelper.CreateConsumerQueueAsync(PaymentSyncService.QueueName, options);
        cqs.Add(PaymentSyncService.QueueName, cqPS);
        await cqPS.OnMessage(async (r) =>
        {
            logger.LogEvent("Processing request to download payment records from motor club... Message: Id = {0}, Body = {1}", null, LogLevel.Info, r.Message.MessageId, r.GetBody<string>());

            if (r.Message.ApplicationProperties.ContainsKey("id"))
            {
                var x = PaymentQueueItem.GetById(Convert.ToInt32(r.Message.ApplicationProperties["id"]));
                var masterAccount = await MasterAccount.GetByIdAsync(x.AccountId);

                PaymentQueueItem.UpdateStatus(x, PaymentQueueItemStatus.AboutToStart);

                try
                {
                    Console.WriteLine("PROCESS ZZ: " + x.ToJson());
                    logger.LogEvent("TODO: Download and sync payment records from motor club to towbook : Id = {0}, Body = {1}", x.CompanyId, LogLevel.Warn, r.Message.MessageId, x.ToJson(), x.AccountId);

                    if (masterAccount == null)
                    {
                        await r.DeferMessageAsync(r.Message);
                        logger.LogEvent("Queue item doesn't have a MasterAccount set. MessageId {0}, Body = {1}", x.CompanyId, LogLevel.Error, r.Message.MessageId, x.ToJson());
                    }

                    string username = AccountKeyValue.GetByAccount(x.CompanyId, x.Id, Provider.Towbook.ProviderId, "McUsername").FirstOrDefault().Value;
                    string password = AccountKeyValue.GetByAccount(x.CompanyId, x.Id, Provider.Towbook.ProviderId, "McPassword").FirstOrDefault().Value;
                    string providerId = AccountKeyValue.GetByAccount(x.CompanyId, x.Id, Provider.Towbook.ProviderId, "ProviderId").FirstOrDefault()?.Value;

                    var logInfo = new LogInfo(r.Message.MessageId, x.CompanyId, false);

                    // Get logInfo ServiceType
                    logInfo.ServiceType = (int)ServiceType.PaymentService;

                    switch (masterAccount.Id)
                    {
                        case MasterAccountTypes.Agero:
                            var agero = AgeroDB.AgeroConnection.Login(username, password);

                            var checks = agero.GetChecks();

                            foreach (var check in checks)
                            {
                                if ((await Payment.GetByReferenceNumberAsync(check.CheckNumber, x.AccountId)).Count > 0)
                                    continue;

                                if (check.PurchaseOrders.Sum(o => o.Amount) > check.Amount)
                                    throw new InvalidAmountsException();

                                Payment newPayment = new Payment();
                                newPayment.AccountId = x.AccountId;
                                newPayment.Amount = check.Amount;
                                newPayment.CompanyId = x.CompanyId;
                                newPayment.Notes = check.Comments;
                                newPayment.OwnerUserId = x.OwnerUserId;
                                newPayment.PaymentDate = check.CheckDate;
                                newPayment.SplitType = SplitType.Single;
                                newPayment.Type = Dispatch.PaymentType.Check;

                                newPayment.Save();
                                foreach (var purchaseOrder in check.PurchaseOrders)
                                {
                                    var entry = await Entry.GetByPurchaseOrderNumberAsync(x.AccountId, purchaseOrder.PurchaseOrderNumber);

                                    if (entry == null)
                                        throw new PurchaseOrderNotFoundException(purchaseOrder.PurchaseOrderNumber);

                                    InvoicePayment newInvoicePayment = new InvoicePayment();
                                    newInvoicePayment.AccountPaymentId = newPayment.Id;
                                    newInvoicePayment.Amount = purchaseOrder.Amount;
                                    newInvoicePayment.InvoiceId = entry.Invoice.Id;
                                    newInvoicePayment.OwnerUserId = entry.OwnerUserId;
                                    newInvoicePayment.PaymentDate = newPayment.PaymentDate;
                                    newInvoicePayment.PaymentType = Dispatch.PaymentType.Account;
                                    newInvoicePayment.ReferenceNumber = check.CheckNumber;

                                    await newInvoicePayment.Save();
                                }
                            }

                            break;

                        case MasterAccountTypes.Allstate:
                            var allstate = Allstate.AllStateConnection.Login(logInfo, username, password, providerId);
                            try
                            {
                                List<Allstate.PaymentStatement> payments = allstate.GetPaymentStatements(logInfo, DateTime.Now.AddDays(-1), DateTime.Now);

                                foreach (var payment in payments)
                                {
                                    if ((await Payment.GetByReferenceNumberAsync(payment.Number, x.AccountId)).Count > 0)
                                        continue;

                                    if (payment.PaymentDetails.Sum(o => o.AmountPaid) > payment.AmountPaid)
                                        throw new InvalidAmountsException();

                                    Payment newPayment = new Payment();
                                    newPayment.AccountId = x.AccountId;
                                    newPayment.Amount = payment.AmountPaid;
                                    newPayment.CompanyId = x.CompanyId;
                                    newPayment.Notes = string.Empty;
                                    newPayment.OwnerUserId = 1; // System User.. consider having a UserId for Agero, Allstate, etc.. so we can show the person that entered them as the actual motor club.
                                    newPayment.PaymentDate = payment.Date;
                                    newPayment.SplitType = SplitType.Single;
                                    newPayment.Type = Dispatch.PaymentType.Check;

                                    newPayment.Save();
                                    foreach (var purchaseOrder in payment.PaymentDetails)
                                    {
                                        var entry = await Entry.GetByPurchaseOrderNumberAsync(x.AccountId, purchaseOrder.Id);

                                        if (entry == null)
                                            throw new PurchaseOrderNotFoundException(purchaseOrder.Id);

                                        InvoicePayment newInvoicePayment = new InvoicePayment();
                                        newInvoicePayment.AccountPaymentId = newPayment.Id;
                                        newInvoicePayment.Amount = purchaseOrder.AmountPaid;
                                        newInvoicePayment.InvoiceId = entry.Invoice.Id;
                                        newInvoicePayment.OwnerUserId = 1; // System User.. consider having a UserId for Agero, Allstate, etc.. so we can show the person that entered them as the actual motor club.
                                        newInvoicePayment.PaymentDate = newPayment.PaymentDate;
                                        newInvoicePayment.PaymentType = Dispatch.PaymentType.Account;
                                        newInvoicePayment.ReferenceNumber = payment.Number;

                                        await newInvoicePayment.Save();
                                    }
                                }
                            }
                            finally
                            {
                                allstate.ReleaseLock(logInfo);
                            }
                            break;

                        case MasterAccountTypes.Nsd:
                            using (Nsd.NsdConnection nsd = Nsd.NsdConnection.Login(logInfo, username, password))
                            {
                                List<Nsd.PaymentStatement> payments = nsd.GetPaymentStatements(logInfo, DateTime.Now.AddMonths(-1), DateTime.Now);

                                foreach (var payment in payments)
                                {
                                    if ((await Payment.GetByReferenceNumberAsync(payment.CheckNumber, x.AccountId)).Count > 0)
                                        continue;

                                    Payment newPayment = new Payment();
                                    newPayment.AccountId = x.AccountId;
                                    newPayment.Amount = payment.PaymentDetails.Sum(o => o.Amount);
                                    newPayment.CompanyId = x.CompanyId;
                                    newPayment.Notes = string.Empty;
                                    newPayment.OwnerUserId = x.OwnerUserId;
                                    newPayment.PaymentDate = payment.DatePaid;
                                    newPayment.SplitType = SplitType.Single;
                                    newPayment.Type = Dispatch.PaymentType.Check;

                                    newPayment.Save();
                                    foreach (var detail in payment.PaymentDetails)
                                    {
                                        var entry = await Entry.GetByPurchaseOrderNumberAsync(x.AccountId, detail.PurchaseOrderNumber);

                                        if (entry == null)
                                            throw new PurchaseOrderNotFoundException(detail.PurchaseOrderNumber);

                                        InvoicePayment newInvoicePayment = new InvoicePayment();
                                        newInvoicePayment.AccountPaymentId = newPayment.Id;
                                        newInvoicePayment.Amount = detail.Amount;
                                        newInvoicePayment.InvoiceId = entry.Invoice.Id;
                                        newInvoicePayment.OwnerUserId = entry.OwnerUserId;
                                        newInvoicePayment.PaymentDate = newPayment.PaymentDate;
                                        newInvoicePayment.PaymentType = Dispatch.PaymentType.Account;
                                        newInvoicePayment.ReferenceNumber = payment.CheckNumber;

                                        await newInvoicePayment.Save();
                                    }
                                }
                            }
                            break;

                        case MasterAccountTypes.RoadAmerica:
                            var roadAmerica = RoadAmerica.RoadAmericaConnection.Login(logInfo, username, password);

                            foreach (var payment in roadAmerica.GetPayments(logInfo, DateTime.Now.AddMonths(-1), DateTime.Now))
                            {
                                if ((await Payment.GetByReferenceNumberAsync(payment.PurchaseOrderNumber, x.AccountId)).Count > 0)
                                    continue;

                                Payment newPayment = new Payment();
                                newPayment.AccountId = x.AccountId;
                                newPayment.Amount = payment.Approved;
                                newPayment.CompanyId = x.CompanyId;
                                newPayment.Notes = string.Empty;
                                newPayment.OwnerUserId = x.OwnerUserId;
                                newPayment.PaymentDate = payment.PaymentDate.Value;
                                newPayment.SplitType = SplitType.Single;
                                newPayment.Type = Dispatch.PaymentType.Check;

                                var entry = await Entry.GetByPurchaseOrderNumberAsync(x.AccountId, payment.PurchaseOrderNumber);

                                if (entry == null)
                                    throw new PurchaseOrderNotFoundException(payment.PurchaseOrderNumber);

                                InvoicePayment newInvoicePayment = new InvoicePayment();
                                newInvoicePayment.AccountPaymentId = newPayment.Id;
                                newInvoicePayment.Amount = payment.Approved;
                                newInvoicePayment.InvoiceId = entry.Invoice.Id;
                                newInvoicePayment.OwnerUserId = entry.OwnerUserId;
                                newInvoicePayment.PaymentDate = newPayment.PaymentDate;
                                newInvoicePayment.PaymentType = Dispatch.PaymentType.Account;
                                newInvoicePayment.ReferenceNumber = payment.PurchaseOrderNumber;

                                await newInvoicePayment.Save();
                            }
                            break;
                        default:
                            await r.DeadLetterAsync();

                            logger.LogEvent("Queue item doesn't have a MasterAccount set that we have a payments implementation written for... MessageId {0}, Body = {1}", x.CompanyId, LogLevel.Error, r.Message.MessageId, x.ToJson());
                            break;
                    }
                }
                catch
                {
                    PaymentQueueItem.UpdateStatus(x, PaymentQueueItemStatus.Error);
                    throw;
                }

                PaymentQueueItem.UpdateStatus(x, PaymentQueueItemStatus.Completed);
                await r.CompleteAsync();

                logger.LogEvent("Finished processing " + r.Message.MessageId);
            }
            else
            {
                await r.DeadLetterAsync();
                logger.LogEvent("DeadLettered " + r.Message.MessageId + " because it doesn't contain an id property.");
            }
        });

    }

    public static bool ReimbursementShouldSubmit(LogInfo logInfo, InvoiceItem ii, Entry entry)
    {
        if (ii != null && ii.ClassId == ChargeClass.Reimbursement)
        {
            // check if it's billable.
            var allowSubmit = RateItemKeyValue.GetByRateItem(entry.CompanyId, ii.RateItem?.RateItemId ?? 0).FirstOrDefault(o =>
                o.KeyId == (RateItemKey.GetByProviderId(Provider.Towbook.ProviderId).Where(r => r.Name == "MCBilling_Submit")
                .FirstOrDefault()?.Id ?? 0))?.Value == "1";

            if (!allowSubmit)
            {
                // ignore reimbursements
                LogEvent(logInfo, $"R.OCT-17-19: Ignoring reimbursement item: {ii.Id}/{ii.Price}/{ii.CustomName}", LogLevel.Info);
                return false;
            }
            return true;
        }

        return true;
    }

    public static void DispatchEntrySyncAgero(LogInfo logInfo, Entry entry, string username, string password, string poNumber)
    {
        // Repair PO numbers
        poNumber = poNumber.Replace("3MA", "JMA").Replace("L", "1").Trim();

        // Get conn
        var agero = AgeroDB.AgeroConnection.Login(username, password);

        // Set logging vars
        agero.LogHttpTraffic = false;

        var po = agero.GetPurchaseOrder(logInfo, poNumber, false);

        if (po.PurchaseOrderNumber == null)
        {
            LogEvent(logInfo, $"PO does not exist on Agero submit invoice page; manually entering PO.", LogLevel.Info);

            var foundInvoice = agero.FindInvoice(poNumber);
            if (foundInvoice != null)
            {
                // PO already exists on agero site as a previously submitted PO.
                var foundInvoiceDetails = agero.GetPurchaseOrder(logInfo,
                    foundInvoice.PurchaseOrderNumber, foundInvoice.ClaimId, foundInvoice.Location);

                var error = "PO was previously submitted. Status on Agero site is: '" + foundInvoice.Status + "'";
                
                if (!string.IsNullOrWhiteSpace(foundInvoiceDetails.Adjustment))
                    error += ", Adjustment Reason: " + foundInvoiceDetails.Adjustment;

                LogEvent(logInfo, error, LogLevel.Warn);
                throw new CancelledException(error);
            }
            po.ServiceDate = entry.CreateDate;
        }

        if (po.Status != AgeroDB.PurchaseOrder.StatusNotSubmitted)
        {
            throw new CancelledException($"Status on Agero site is '{po.Status}'");
        }
        else
        {
            var invoiceCharges = new List<KeyValuePair<AgeroDB.InvoiceCharge, decimal>>();
            bool isGOA = false;

            var nonPreDefinedItems = entry.InvoiceItems.Count(o => !(o.RateItem != null && o.RateItem.Predefined != null &&
                (o.RateItem.Predefined.Id == PredefinedRateItem.BUILTIN_MILEAGE_LOADED ||
                 o.RateItem.Predefined.Id == PredefinedRateItem.BUILTIN_MILEAGE_UNLOADED)) && o.Total > 0);

            decimal payoutTotal = 0;


            foreach (var ii in entry.InvoiceItems)
            {
                // ignore free quantity lines for custom mileage items
                if (ii.CustomName != null && ii.CustomName.Contains("FreeQuantity Credit"))
                    continue;

                if (!ReimbursementShouldSubmit(logInfo, ii, entry))
                    continue;

                var item = new { Name = ii.CustomName, Price = ii.Price, Quantity = ii.Quantity, Total = ii.Total, RateItemId = ii.RateItem != null ? ii.RateItem.RateItemId : 0 }.ToJson();

                if (ii.RateItem != null && ii.RateItem.Predefined != null &&
                    (ii.RateItem.Predefined.Id == PredefinedRateItem.BUILTIN_MILEAGE_LOADED ||
                     ii.RateItem.Predefined.Id == PredefinedRateItem.BUILTIN_MILEAGE_UNLOADED))
                    continue;

                if (ii.Name.ToLowerInvariant().Contains("unloaded miles") ||
                    ii.Name.ToLowerInvariant().Contains("loaded miles") ||
                    ii.Name.ToLowerInvariant().Contains("enroute mileage") ||
                    ii.Name.ToLowerInvariant().Contains("unloaded mileage") ||
                    ii.Name.ToLowerInvariant().Contains("loaded mileage") ||
                    ii.Name.ToLowerInvariant().Contains("hooked mileage"))
                    continue;

                var invoiceItemName = ii.Name.ToLowerInvariant()
                    .Replace("surecharge", "surcharge")
                    .Replace("suecharge", "surcharge")
                    .Replace("-", " ").Replace("  ", " ").Replace(".", "").Trim();


                if (invoiceItemName.Replace(" ", "").Contains("payout"))
                {
                    payoutTotal += ii.Price;
                    continue;
                }

                if (!isGOA)
                    isGOA = invoiceItemName == "goa" || invoiceItemName.EndsWith(" goa") || invoiceItemName.Contains("gone on arrival");

                var thisItemIsGOA = invoiceItemName == "goa" || invoiceItemName.EndsWith(" goa") || invoiceItemName.Contains("gone on arrival");

                if (ii.Total == 0 && !thisItemIsGOA)
                    continue;

                if (invoiceItemName.StartsWith("discount:"))
                {
                    LogEvent(logInfo, $"Ignoring Discount: {item}", LogLevel.Warn);
                    continue;
                }

                bool roadService = invoiceItemName.Contains("lockout") ||
                    invoiceItemName.Contains("lock out") ||
                    invoiceItemName.Contains("road service") ||
                    invoiceItemName.Contains("tire change") ||
                    invoiceItemName.Contains("tire service") ||
                    invoiceItemName.Contains("fuel delivery") ||
                    invoiceItemName.Contains("feel delivery") ||
                    invoiceItemName.Contains("flat tire") ||
                    invoiceItemName == "start" ||
                    invoiceItemName.Contains("jump start");

                if (invoiceItemName.Contains("tow") ||
                    invoiceItemName.Contains("hook") ||
                    (invoiceItemName.Contains("winch") && entry.Reason.NameMatchable().Contains("winch")) ||
                    invoiceItemName.Contains("goa") ||
                    invoiceItemName.Contains("standard rate") ||
                    invoiceItemName.Contains("wheel lift") ||
                    invoiceItemName.Contains("base") ||
                    invoiceItemName == "agero" ||
                    invoiceItemName == "primary" ||
                    invoiceItemName == "secondary" ||
                    invoiceItemName.Contains("accident") ||
                    invoiceItemName.Contains("flatbed") ||
                    invoiceItemName.Contains("flat bed") ||
                    invoiceItemName.Contains("light duty") ||
                    invoiceItemName.Contains("motorcycle"))
                {
                    if (ii.Price > 10 || isGOA)
                    {
                        LogEvent(logInfo, $"Ignoring {item}", LogLevel.Warn);
                        continue;
                    }
                }

                if (invoiceItemName.Contains("motorcycle"))
                {
                    // re classify as motorcycle job here based on invoice item.  we attempt further down
                    // to classify it as a motorcycle if the body type is motorcycle also.

                    if (entry.TowDestination == null || entry.TowDestination.Length < 5)
                        po.EquipmentType = "MS"; // motorcycle service
                    else
                        po.EquipmentType = "MP"; // motorcycle pickup

                    LogEvent(logInfo, $"R.JUL-29-17: Manually set equipmentType to {po.EquipmentType} because towDestination length is = {entry.TowDestination?.Length ?? 0}.", LogLevel.Info);
                }
            

                if (roadService && (entry.TowDestination == null || entry.TowDestination.Length < 5))
                {
                    // only treat road service as a primary item if there is no destination.

                    if (po.ServiceType != "ROAD" && po.ServiceType != "LOCK")
                    {
                        LogEvent(logInfo, $"Changing ServiceType from {po.ServiceType} to ROAD", LogLevel.Warn);
                        po.ServiceType = "ROAD";
                    }

                    if (ii.Price > 10 || isGOA)
                    {
                        LogEvent(logInfo, $"Ignoring {item}", LogLevel.Warn);
                        continue;
                    }
                }

                if (nonPreDefinedItems == 1)
                {
                    LogEvent(logInfo, $"Ignoring {item} because it is the only non-mileage item on the invoice.", LogLevel.Warn);
                    continue;
                }

                var charge = AgeroDB.InvoiceCharge.FromString(invoiceItemName);
                if (charge == null)
                {
                    if (invoiceItemName.Contains("customer overage") ||
                        invoiceItemName.Contains("customer paid") ||
                        invoiceItemName.Contains("customer to pay") ||
                        invoiceItemName.Contains("customer cover") ||
                        invoiceItemName.Contains("overage") ||
                        invoiceItemName.Contains("overmiles") ||
                        invoiceItemName.Contains("over miles") ||
                        invoiceItemName.Contains("overmileage") ||
                        invoiceItemName.Contains("over mileage") ||
                        invoiceItemName.Contains("overmilage") ||
                        invoiceItemName.Contains("over milage") ||
                        invoiceItemName.Contains("credit card fee") ||
                        invoiceItemName.Contains("out of pocket") ||
                        ii?.ClassId == ChargeClass.Cash)
                    {
                        LogEvent(logInfo, $"Ignoring {item} because it is a CUSTOMER OVERAGE charge", LogLevel.Warn);
                        continue;
                    }

                    if (invoiceItemName.Contains("(DirectBillingIgnore)"))
                    {
                        LogEvent(logInfo, $"Ignoring {item} because it contains (DirectBillingIgnore)", LogLevel.Warn);
                        continue;
                    }

                    throw new UnknownChargeException(ii.Name);
                }

                invoiceCharges.Add(new KeyValuePair<AgeroDB.InvoiceCharge, decimal>(charge, ii.Total));
            }

            if (payoutTotal > 0)
            {
                // find out if we have a payment for this 

                // we have to account for 1 penny differences because either the MC or our customers
                // dont round properly.
                
                var ip = InvoicePayment.GetByInvoiceId(entry.Invoice.Id)
                    .Where(o => o.Amount == payoutTotal - 0.01M ||
                    o.Amount == payoutTotal ||
                    o.Amount == payoutTotal + 0.01M)
                    .FirstOrDefault();


                // only submit the payout as an advance charge IF it has NOT been paid already.
                if (ip == null)
                {
                    invoiceCharges.Add(new KeyValuePair<AgeroDB.InvoiceCharge, decimal>(
                        AgeroDB.InvoiceCharge.AdvanceCharge,
                        payoutTotal));
                }
            }

            if (entry.InvoiceTax > 0)
                invoiceCharges.Add(new KeyValuePair<AgeroDB.InvoiceCharge, decimal>(AgeroDB.InvoiceCharge.Taxes, entry.InvoiceTax));

            var unloadedInvoiceItems = entry.InvoiceItems.Where(o => ((o.Name != null &&
                o.Name.ToLowerInvariant().Contains("unloaded miles") ||
                o.Name.ToLowerInvariant().Contains("unloaded mileage") ||
                o.Name.ToLowerInvariant().Contains("enroute mileage")) || (o.RateItem != null && o.RateItem.Predefined != null && o.RateItem.Predefined.Id == PredefinedRateItem.BUILTIN_MILEAGE_UNLOADED)) && o.Quantity > 0 && !(o.Price < 0));
            var unloadedMiles = unloadedInvoiceItems.Sum(o => o.Quantity);
            var unloadedTotal = unloadedInvoiceItems.Sum(o => o.Total);

            var loadedMiles = entry.InvoiceItems.Where(o => ((o.Name != null &&
                o.Name.ToLowerInvariant().Contains("loaded miles") ||
                o.Name.ToLowerInvariant().Contains("loaded mileage") ||
                o.Name.ToLowerInvariant().Contains("hooked miles") ||
                o.Name.ToLowerInvariant().Contains("hooked milage") ||
                o.Name.ToLowerInvariant().Contains("hooked mileage")) || (o.RateItem != null && o.RateItem.Predefined != null && o.RateItem.Predefined.Id == PredefinedRateItem.BUILTIN_MILEAGE_LOADED)) && o.Quantity > 0 && !(o.Price < 0)).Sum(o => o.Quantity);

            if (CheckUnloadedMilesForceToOne(entry, unloadedMiles, unloadedTotal, logInfo))
                unloadedMiles = 1;

            if (entry.Contacts.Any())
            {
                var cc = entry.Contacts.First();

                string fname = "";
                string lname = "";
                SplitName.Split(cc.Name, out fname, out lname);

                if (!String.IsNullOrWhiteSpace(fname))
                    po.MemberFirstName = fname;

                if (!String.IsNullOrWhiteSpace(lname))
                    po.MemberLastName = lname;

                po.MemberMiddleName = "";
            }

            var paymentsApplied = entry.PaymentsApplied;
            var amountToSubmit = entry.BalanceDue;

            LogEvent(logInfo, invoiceCharges.ToJson(true), LogLevel.Info);
            LogEvent(logInfo, $"Requesting to be paid {amountToSubmit:C}; invoice Total is {entry.InvoiceTotal:C}", LogLevel.Info);

            if (isGOA && po.ServiceType != "GOA")
            {
                LogEvent(logInfo, $"Manually set service type to GOA because it was serviceType {po.ServiceType}", LogLevel.Info);
                po.ServiceType = "GOA";
            }

            if (String.IsNullOrWhiteSpace(po.EquipmentType))
            {
                // lets try to digitally get the original type.

                string et = AgeroGetEquipmentType(logInfo, entry.AccountId, entry.PurchaseOrderNumber);

                if (et != null)
                {
                    po.EquipmentType = et;
                    LogEvent(logInfo, $"Automatically set equipmentType to {po.EquipmentType} from DigitalDispatchDetails API. ", LogLevel.Info);
                }
                else
                {
                    if (po.ServiceType == "TOW")
                        po.EquipmentType = "LDF";
                    else if (po.ServiceType == "ROAD")
                        po.EquipmentType = "MRS";
                    else if (po.ServiceType == "GOA")
                        po.EquipmentType = "LDF";
                    else if (po.ServiceType == "LOCK")
                        po.EquipmentType = "LDF";

                    LogEvent(logInfo, $"Manually set equipmentType to {po.EquipmentType} because serviceType = {po.ServiceType} because it was empty", LogLevel.Info);
                }
            }

            if (entry.BodyType?.Name == "Medium")
            {
                if (po.EquipmentType[0] != 'M' ||
                    po.EquipmentType == "MP" ||
                    po.EquipmentType == "MS")
                {
                    LogEvent(logInfo, $"R.JUL-29-17: Manually set equipmentType to MDF because it was = {po.EquipmentType} and towed vehicle weight is Medium", LogLevel.Info);
                    po.EquipmentType = "MDF";
                }
            }
            else if (entry.BodyType?.Name == "Motorcycle")
            {
                if (po.EquipmentType != "MP" &&
                    po.EquipmentType != "MS")
                {
                    if (entry.TowDestination == null || entry.TowDestination.Length < 5)
                        po.EquipmentType = "MS"; // motorcycle service
                    else
                        po.EquipmentType = "MP"; // motorcycle pickup
                }
            }

            var submitLogs = agero.SubmitPurchaseOrder(
                logInfo: logInfo,
                poNumber: poNumber,
                serviceType: po.ServiceType,
                serviceDate: Core.OffsetDateTime(entry.Company, po.ServiceDate.Value, true),
                equipmentType: po.EquipmentType,
                odometer: entry.Odometer,
                vin: entry.VIN,
                memberFirstName: po.MemberFirstName,
                memberMiddleName: po.MemberMiddleName,
                memberLastName: po.MemberLastName,
                milesToSite: unloadedMiles,
                milesTowed: loadedMiles,
                hours: po.ActualHours,
                wasAtSameLocation: true,
                charges: invoiceCharges,
                invoiceNumber: entry.CallNumber.ToString(),
                invoiceAmount: amountToSubmit,
                comments: entry.BillingNotes(),
                watchdog: po.Watchdog,
                changeReason: "",
                otherReason: string.Empty,
                actualTowToLocation: entry.TowDestination,
                checkStatus: true,
                existingPO: po);

            var en = new EntryNote() { DispatchEntryId = entry.Id, OwnerUserId = 1 };
            en.Content = "Submitted invoice to Agero...\n\nTestMode=" + logInfo.TestMode + "\n\n";
            en.Content += "EquipmentType: " + po.EquipmentType + "\n";
            en.Content += "ServiceType: " + po.ServiceType + "\n";
            en.Content += "InvoiceAmount: " + entry.InvoiceTotal.ToString("C") + "\n";
            try
            {
                en.Content += "InvoiceCharges: (" + string.Join(", ", invoiceCharges.Select(o => o.Key.Name + " = " + o.Value)) + ")\n";
            }
            catch
            {

            }

            en.Content += "Unloaded Miles: " + unloadedMiles + "\n";
            en.Content += "Loaded Miles: " + loadedMiles + "\n";
            
            en.Save();

            LogEvent(logInfo, $"Submitted to Agero, Unloaded: {unloadedMiles}, Loaded: {loadedMiles}, NoteId: {en.Id}, # of log lines: {submitLogs.Count}, not dumping.", LogLevel.Info);
        }
    }

    
    public static void DispatchEntrySyncAllstate(LogInfo logInfo, Entry entry, string username, string password, string providerId, string poNumber)
    {
        // Get conn
        var allstate = Allstate.AllStateConnection.Login(logInfo, username, password, providerId);

        try
        {
            // Set logging vars
            allstate.LogHttpTraffic = true;

            var html = new HtmlDocument();
            Allstate.PurchaseOrder po = null;

            // Get the po from the Allstate site
            var p = allstate.GetAvailablePOs(logInfo).FirstOrDefault(a => a.PurchaseOrderNumber == poNumber);
            if (p != null)
                po = allstate.GetPurchaseOrder(logInfo, p.DispatchId, false, out html);
            else
                po = allstate.SearchPurchaseOrder(logInfo, poNumber, out html);

            // If we couldn't find it
            if (po == null)
            {
                // If it was within the last 14 days
                if (entry.CreateDate > DateTime.Today.AddDays(-14))
                    throw new RetryException("Not yet available to submit on the Allstate website.");
                else
                    throw new NotFoundException("PO does not exist on the Allstate site. ");
            }
            else
            {
                // If we shouldn't touch it; they need to work out payment with the motor club
                if (po.Status == "Denied" || po.Status == "Disputed")
                    throw new NotSubmittableException($"Status on Allstate site is '{po.Status}'");

                // If it cannot be submitted or everything on it's disabled
                if (html.DocumentNode.SelectSingleNode("//input[@id='submit_continue']") == null || po.DisableAllClaimActions)
                {
                    // If they won't get paid unless they call the motor club about it
                    if (po.Status == "Open" || po.Status == "Closed" || po.Status == "Auto Closed")
                        throw new NotSubmittableException($"Allstate says 'Dispatch cannot be paid online.'");

                    // If it's been paid, soon to be paid, or the motor club's thinking about paying it
                    else
                        throw new CancelledException($"Status on Allstate site is '{po.Status}'");
                }
                else 
                {
                    // If we can submit it for payment, but we shouldn't (status is some form of 'in process')
                    if (po.Status != "Open" && po.Status != "Paid" && po.Status != "Closed")
                        throw new CancelledException($"Status on Allstate site is '{po.Status}'");

                    // If it qualifies for a supplemental payment (it has payments already posted on it)
                    var supplemental = po.Payments.Count > 0;
                    if (supplemental)
                    {
                        // If its all paid up, don't submit any supplemental
                        if (po.Payments.Sum(s => s.PaidAmount) >= entry.InvoiceTotal)
                            throw new CancelledException($"Status on Allstate site is '{po.Status}'");
                        else
                        {
                            // If there are no services on the PO
                            if (po.Services.Count == 0)
                            {
                                // Foreach payment
                                foreach (var payment in po.Payments)
                                {
                                    // If this payment is for a service
                                    var svc = Allstate.Service.FromString(payment.ServiceDescription?.ToLower().Replace("-", " ") ?? "");
                                    if (svc != null)
                                    {
                                        // Add it as a PrePaid service
                                        svc.PrePaid = true;
                                        svc.PrePopulated = true;
                                        svc.BaseUnitAmount = payment.PaidAmount;
                                        po.Services.Add(svc);

                                        // Replicate hack on Allstate page to enable editing mileage based on this service
                                        allstate.displayRouteOptions(logInfo, po, svc);
                                    }
                                }
                            }
                        }
                    }

                    // Set the total we expect Allstate to calculate for this PO, and a copy of the original total
                    po.ExpectedTotal = po.TowbookTotal = entry.InvoiceTotal;

                    //--------------------------------------------------------------------------------------------------------
                    // Invoice item filtering:  save and remove all ii's that aren't valid for reverse- or forward-matching
                    //--------------------------------------------------------------------------------------------------------

                    // Get list of entry invoice items & reverse the order to smooth out future matching
                    var invoiceItems = entry.InvoiceItems.Reverse().ToCollection();

                    var foundGOA = false;
                    var unloadedMileageItems = new List<InvoiceItem>();
                    var loadedMileageItems = new List<InvoiceItem>();
                    var roadServiceItems = new List<InvoiceItem>();

                    for (var x = invoiceItems.Count - 1; x >= 0; x--)
                    {
                        var ii = invoiceItems[x];
                        var item = new { Name = ii.CustomName, Price = ii.Price, Quantity = ii.Quantity, Total = ii.Total, RateItemId = ii.RateItem?.RateItemId ?? 0 }.ToJson();
                        var itemName = ii.Name.ToLowerInvariant().Replace("-", " ").Replace("  ", " ").Replace(".", "").Trim();

                        // Check if its unloaded
                        if (itemName.Contains("unloaded mil") ||
                            itemName.Contains("enroute mil") ||
                            itemName.Contains("en route mil") ||
                            ii.RateItem?.Predefined?.Id == 1)
                        {
                            unloadedMileageItems.Add(ii);
                            invoiceItems.RemoveAt(x);
                            continue;
                        }

                        // Check if its loaded
                        if (itemName.Contains("loaded mil") ||
                            itemName.Contains("hooked mil") ||
                            ii.RateItem?.Predefined?.Id == 2)
                        {
                            loadedMileageItems.Add(ii);
                            invoiceItems.RemoveAt(x);
                            continue;
                        }

                        // If its a discount, then ignore it
                        if (itemName.StartsWith("discount:"))
                        {
                            logInfo.AddEvent($"Ignoring Discount: {item}", LogLevel.Warn);

                            // If its total is positive, deduct it from the total we expect Allstate to calculate
                            if (ii.Total > 0)
                                po.ExpectedTotal -= ii.Total;
                            invoiceItems.RemoveAt(x);
                            continue;
                        }

                        if (!ReimbursementShouldSubmit(logInfo, ii, entry))
                        {
                            if (ii.Total > 0)
                                po.ExpectedTotal -= ii.Total;
                            invoiceItems.RemoveAt(x);

                            continue;
                        }
                        
                        // If its GOA, note that we have one
                        if (itemName.Contains("goa") || itemName.Contains("gone on arrival"))
                        {
                            foundGOA = true;
                            continue;
                        }

                        // If it might be a service
                        var svc = Allstate.Service.FromString(itemName);
                        if (svc != null)
                        {
                            // Add it to road services (used to check if GOA completed a service)
                            roadServiceItems.Add(ii);
                            continue;
                        }
                    }

                    //------------------------------------------------------------------------------------------
                    // Dollies upgrades:  If we have dollies in Towbook, figure out how to add them to the PO
                    //------------------------------------------------------------------------------------------

                    // If we have a dollies ii in Towbook
                    var dollies_ii = invoiceItems.FirstOrDefault(i => 
                        (i.Name.ToLower().Contains("dolly") || i.Name.ToLower().Contains("dollies")) && 
                        !(i.Name.ToLower().Contains("w/o doll") || i.Name.ToLower().Contains("without doll")));
                    if (dollies_ii != null)
                    {
                        // Check if dollies is a transport
                        var transport = dollies_ii.Name.ToLower().Contains("transport");

                        // If there is a tow ii (that isn't the dollies ii)
                        var tow_ii = invoiceItems.FirstOrDefault(i => i != dollies_ii && (
                            i.Name.ToLower().Contains("tow") || 
                            i.Name.ToLower().Contains("flatbed") ||
                            i.Name.ToLower().Contains("flat bed")));
                        if (tow_ii != null)
                        {
                            // Check if tow is a transport
                            transport = transport || (tow_ii != null && tow_ii.Name.ToLower().Contains("transport"));

                            // Combine it with dollies ii and remove it
                            if(dollies_ii.Price != 0)
                                dollies_ii.Quantity += (tow_ii.Total / dollies_ii.Price);
                            invoiceItems.Remove(tow_ii);
                        }

                        // If we don't already have a dollies service on the PO
                        if (!po.TowServices.Any(s => s.EquipmentId == 2 || s.EquipmentId == 12))
                        {
                            // Look for an existing tow service
                            var tow_svc = po.TowServices.FirstOrDefault();

                            // If this is a non-supplemental PO (or it is, and it doesn't have any tow services already)
                            if (!supplemental || tow_svc == null)
                            {
                                // If we can add a tow service
                                if (po.TowServiceIsEditable)
                                {
                                    // Get the tow types
                                    allstate.loadTowSvcCount(logInfo, po);

                                    // If we can choose "One-way Tow"
                                    if (po.AvailableTowTypes.Any(t => t.Key == "1"))
                                    {
                                        // Get the equipment types for "One-way Tow"
                                        allstate.loadEquipmentType(logInfo, po, 1);

                                        // If it was a transport ii AND we can add a transport with dollies service
                                        if (transport && po.AvailableEqTypes.Any(t => t.Key == "12"))
                                        {
                                            // Add it
                                            var svc = allstate.addNewTowService(logInfo, po, 1, 12, dollies_ii.Name);
                                            if (svc != null)
                                            {
                                                // Mark existing tow service as deleted
                                                if (tow_svc != null)
                                                {
                                                    po.ItemIds += $",{tow_svc.ClaimServiceId}";
                                                    tow_svc.ProcessStatusId = 410;
                                                    tow_svc.MatchFound = true;
                                                }
                                            }
                                        }

                                        // Else if we can add a dollies service
                                        else if (po.AvailableEqTypes.Any(t => t.Key == "2"))
                                        {
                                            // Add it
                                            var svc = allstate.addNewTowService(logInfo, po, 1, 2, dollies_ii.Name);
                                            if (svc != null)
                                            {
                                                // Mark existing tow service as deleted
                                                if (tow_svc != null)
                                                {
                                                    po.ItemIds += $",{tow_svc.ClaimServiceId}";
                                                    tow_svc.ProcessStatusId = 410;
                                                    tow_svc.MatchFound = true;
                                                }
                                            }
                                        }

                                        // Else if there was no existing tow service
                                        else if (tow_svc == null)
                                        {
                                            // Add it, using first equipment type
                                            var eqType = Convert.ToInt32(po.AvailableEqTypes[0].Key);
                                            allstate.addNewTowService(logInfo, po, 1, eqType, (tow_ii ?? dollies_ii).Name);
                                        }
                                    }
                                }
                            }
                        }
                    }

                    //-------------------------------------------------------------------------------------------------
                    // Reverse-matching:  Match each service that comes preloaded on the PO to a Towbook invoice item
                    //-------------------------------------------------------------------------------------------------

                    // For each service on this PO (that hasn't been marked for deletion)
                    foreach (var svc in po.Services.Where(s => s.ProcessStatusId != 410))
                    {
                        // Look at all the Towbook invoice items (in reverse order in case we remove them from this list)
                        for (var x = invoiceItems.Count - 1; x >= 0; x--)
                        {
                            var ii = invoiceItems[x];
                            var itemName = ii.Name.ToLowerInvariant().Replace("-", " ").Replace("  ", " ").Replace(".", "").Trim();

                            // If one matches this service
                            var service = Allstate.Service.FromString(itemName);
                            if (service != null && service.GroupServiceCode == svc.GroupServiceCode)
                            {
                                svc.MatchFound = true;

                                // Don't add it to the invoice later; it's already on it
                                invoiceItems.RemoveAt(x);

                                // Diff ii and service totals
                                AllstateServiceDiff(logInfo, po, svc, ii);
                                break;
                            }
                        }
                    }

                    // If there were multiple services that we couldn't find matches for
                    var unmatchedServices = po.Services.Where(s => s.MatchFound == false);
                    if (unmatchedServices.Count() > 1)
                    {
                        // Throw exception
                        throw new MatchingException("Unable to find Towbook invoice item matches for multiple services on Allstate purchase order.");
                    }

                    // However, if there was only one
                    else if (unmatchedServices.Count() == 1)
                    {
                        var svc = unmatchedServices.First();

                        // Get any invoice item that might be a service
                        var possibleServices = new Collection<InvoiceItem>();
                        for (var x = 0; x < invoiceItems.Count; x++)
                        {
                            var ii = invoiceItems[x];
                            var itemName = ii.Name.ToLowerInvariant().Replace("-", " ").Replace("  ", " ").Replace(".", "").Trim();
                            var service = Allstate.Service.FromString(itemName);
                            if (service != null)
                                possibleServices.Add(ii);
                        }

                        // If there was just one
                        if (possibleServices.Count() == 1)
                        {
                            // Assume they're a match
                            svc.MatchFound = true;

                            // Log that we're making this assumption
                            logInfo.AddEvent($"Assuming Towbook invoice item '{possibleServices[0].Name.Trim()}' matches Allstate service '{svc.GroupServiceCodeDesc}'", LogLevel.Info);

                            // Don't add it to the invoice later; it's already on it
                            invoiceItems.Remove(possibleServices[0]);

                            // Diff ii and service totals
                            AllstateServiceDiff(logInfo, po, svc, possibleServices[0]);
                        }
                        else
                            throw new MatchingException($"Unable to find a Towbook invoice item match for Allstate service '{svc.GroupServiceCodeDesc}'");
                    }

                    //----------------------------------------------------------------------------------------------------------------------
                    // Forward-matching:  Match each Towbook invoice item to a service, toll, ferry, or add'l amount and add it to the PO
                    //----------------------------------------------------------------------------------------------------------------------

                    // For each invoice item
                    foreach (var ii in invoiceItems)
                    {
                        // Get it as a JSON string
                        var item = new { Name = ii.CustomName, Price = ii.Price, Quantity = ii.Quantity, Total = ii.Total, RateItemId = ii.RateItem?.RateItemId ?? 0 }.ToJson();
                        var itemName = ii.Name.ToLowerInvariant().Replace("-", " ").Replace("  ", " ").Replace(".", "").Trim();
                        var rateItemName = (ii.RateItem?.Name ?? "").ToLowerInvariant().Replace("-", " ").Replace("  ", " ").Replace(".", "").Trim();

                        // If its GOA or its total is 0, skip over it
                        if (itemName.Contains("goa") || itemName.Contains("gone on arrival"))
                        {
                            foundGOA = true;
                            continue;
                        }
                        else if (ii.Total == 0)
                        {
                            continue;
                        }

                        // If its a ferry charge 
                        // (must come before tolls, below, as some ii's are listed as 'ferry tolls')
                        if (itemName.Contains("ferry") || itemName.Contains("ferries"))
                        {
                            // If ferry charges box is not disabled
                            if (!po.DisabledElements.Contains("ferryCharge"))
                            {
                                // Add it to ferry charges
                                po.FerryCharge = (po.FerryCharge ?? 0) + ii.Total;
                            }
                            else
                            {
                                // Add it to add'l amount
                                po.AddAdditionalAmount(ii.Total, $"{ii.Name}: {ii.Total:C}");
                            }
                            continue;
                        }

                        // If its a toll fee
                        if (itemName.Contains("toll"))
                        {
                            po.Tolls = (po.Tolls ?? 0) + ii.Total;
                            continue;
                        }

                        // If its a deadhead mileage item
                        if (itemName.Contains("dhm") || rateItemName.Contains("dhm") ||
                            itemName.Contains("deadhead") || rateItemName.Contains("deadhead") ||
                            itemName.Contains("dead head") || rateItemName.Contains("dead head") ||
                            itemName == "negotiated additional charge" || rateItemName == "negotiated additional charge" ||
                            itemName == "extra" || rateItemName == "extra")
                        {
                            // Add to add'l amount
                            po.AddAdditionalAmount(ii.Total, $"{ii.Name}: {ii.Total:C}");
                            continue;
                        }

                        // If its a customer overage charge, then ignore it
                        if (itemName.Contains("customer overage") ||
                            itemName.Contains("customer paid") ||
                            itemName.Contains("customer to pay") ||
                            itemName.Contains("overage") ||
                            itemName.Contains("overmil") ||
                            itemName.Contains("over mil") ||
                            itemName.Contains("out of pocket"))
                        {
                            logInfo.AddEvent($"Ignoring {item} because it is a CUSTOMER OVERAGE charge", LogLevel.Warn);

                            // Deduct it from the total we expect Allstate to calculate
                            po.ExpectedTotal -= ii.Total;
                            ii.Quantity = 0;
                            continue;
                        }

                        // If it might be a service
                        var svc = Allstate.Service.FromString(itemName);
                        if (svc != null)
                        {
                            // If it is a non-tow service and we can edit those
                            if (svc.ServiceType == Allstate.ServiceType.NonTowService && po.NonTowServiceIsEditable)
                            {
                                // If it's not winching (not willing to code for this yet)
                                if (svc.GroupServiceCode != "WIN")
                                {
                                    // If we don't already have this service (Allstate only allows one of each non-tow service)
                                    if (!po.NonTowServices.Any(t => t.GroupServiceCode == svc.GroupServiceCode))
                                    {
                                        // Get the non-tow services
                                        allstate.loadNonTowSvcs(logInfo, po);

                                        // If they have the non-tow service we want
                                        svc = po.AvailableNonTowServices.FirstOrDefault(t => t.GroupServiceCode == svc.GroupServiceCode);
                                        if (svc != null)
                                        {
                                            // Enter the service
                                            svc = allstate.loadNewNonTowService(logInfo, po, svc);
                                            if (svc != null)
                                            {
                                                // Diff ii and service totals
                                                AllstateServiceDiff(logInfo, po, svc, ii);
                                                continue;
                                            }
                                        }
                                    }
                                }
                            }

                            // If it is a tow service and we can edit those
                            else if (svc.ServiceType == Allstate.ServiceType.TowService && po.TowServiceIsEditable)
                            {
                                // Get the tow types
                                allstate.loadTowSvcCount(logInfo, po);

                                // If we can choose "One-way Tow"
                                if (po.AvailableTowTypes.Any(t => t.Key == "1"))
                                {
                                    // Get the equipment types for "One-way Tow"
                                    allstate.loadEquipmentType(logInfo, po, 1);

                                    // If they have the equipment type we want
                                    if (po.AvailableEqTypes.Any(t => t.Key == svc.EquipmentId.ToString()))
                                    {
                                        // Enter the service
                                        svc = allstate.addNewTowService(logInfo, po, 1, svc.EquipmentId.Value, ii.Name);
                                        if (svc != null)
                                        {
                                            // Diff ii and service totals
                                            AllstateServiceDiff(logInfo, po, svc, ii);
                                            continue;
                                        }
                                    }
                                }
                            }
                        }

                        // If it didn't match anything else, add to add'l amount
                        po.AddAdditionalAmount(ii.Total, $"{ii.Name}: {ii.Total:C}");
                    }

                    // If this was a GOA
                    if (foundGOA)
                    {
                        // If the GOA button is not disabled
                        if (!po.DisabledElements.Contains("btn-goa"))
                        {
                            // Get the first service
                            var service = po.Services.FirstOrDefault();
                            if (service != null)
                            {
                                service.GoaInd = true;

                                // If the GOA completed a service
                                if (roadServiceItems.Count > 0)
                                {
                                    po.GoaConfirmInd = "Y";
                                }
                                else
                                {
                                    po.GoaConfirmInd = "N";
                                    service.BaseUnitAmount = 0;
                                }
                            }
                        }
                    }

                    // If it wasn't a GOA in Towbook, but it was in Allstate
                    else if (po.Services.Count(s => s.GoaInd == true) > 0)
                    {
                        // Note it
                        po.AmountDiffs.Add(new Allstate.AmountDiff()
                        {
                            Name = "GOA",
                            DiffType = Allstate.AmountDiffType.GOA
                        });
                    }

                    // Populate general fields
                    po.ProviderInvoice = entry.CallNumber.ToString();
                    po.VehicleLicensePlate = entry.LicenseNumber;

                    if (entry.Invoice.PaymentsTotal > 0)
                    {
                        var ip = InvoicePayment.GetByInvoiceId(entry.Invoice.Id).Where(o => !o.IsVoid && o.AccountPaymentId == 0 && o.PaymentType != PaymentType.EFT).Sum(o => o.Amount);
                        po.PaymentFromCustomer = ip;
                    }

                    // Billing notes
                    if (!string.IsNullOrWhiteSpace(entry.BillingNotes()))
                    {
                        var found = false;
                        foreach (var comment in po.Comments)
                        {
                            if (comment.Message == entry.BillingNotes())
                            {
                                found = true;
                                break;
                            }
                        }

                        if (!found)
                        {
                            po.Comments.Add(new Allstate.Comment()
                            {
                                Message = entry.BillingNotes(),
                                CreateDate = DateTime.Now,
                                PrePopulated = false
                            });
                        }
                    }

                    // Address validation
                    foreach (var addr in po.Addresses.Where(a => !a.AddressComplete))
                    {
                        // If this is a pickup, and its the only one
                        if (po.IsPickup(addr) && po.Addresses.Count(a => po.IsPickup(a)) == 1)
                        {
                            // If there is an entry waypoint called "Pickup", containing lat/lon
                            var waypoint = entry.Waypoints.FirstOrDefault(w => w.Title == "Pickup" && w.Latitude != 0 && w.Longitude != 0);
                            if (waypoint != null)
                            {
                                addr.Latitude = waypoint.Latitude;
                                addr.Longitude = waypoint.Longitude;

                                // resetAddressFields()
                                addr.AddressLine1 = "";
                                addr.AddressLine2 = "";
                                addr.City = "";
                                addr.State = addr.StateCd = "";
                                addr.Zip = "";
                            }
                        }

                        // If this is a dropoff, and its the only one
                        else if (po.IsDropOff(addr) && po.Addresses.Count(a => po.IsDropOff(a)) == 1)
                        {
                            // If there is an entry waypoint called "Destination", containing lat/lon
                            var waypoint = entry.Waypoints.FirstOrDefault(w => w.Title == "Destination" && w.Latitude != 0 && w.Longitude != 0);
                            if (waypoint != null)
                            {
                                addr.Latitude = waypoint.Latitude;
                                addr.Longitude = waypoint.Longitude;

                                // resetAddressFields()
                                addr.AddressLine1 = "";
                                addr.AddressLine2 = "";
                                addr.City = "";
                                addr.State = addr.StateCd = "";
                                addr.Zip = "";
                            }
                        }

                        // Validate it
                        allstate.validateAddress(logInfo, po, addr);
                    }

                    // Get mileages
                    var unloadedMiles = unloadedMileageItems.Where(i => i.Price > 0 && i.Quantity > 0).Sum(i => i.Quantity);
                    var unloadedRate = unloadedMileageItems.Where(i => i.Price > 0 && i.Quantity > 0).Select(i => i.Price).FirstOrDefault();
                    var unloadedTotal = unloadedMileageItems.Where(i => i.Price > 0 && i.Quantity > 0).Sum(i => i.Total);
                    var loadedMiles = loadedMileageItems.Where(i => i.Price > 0 && i.Quantity > 0).Sum(i => i.Quantity);
                    var loadedRate = loadedMileageItems.Where(i => i.Price > 0 && i.Quantity > 0).Select(i => i.Price).FirstOrDefault();
                    var loadedTotal = loadedMileageItems.Where(i => i.Price > 0 && i.Quantity > 0).Sum(i => i.Total);
                    var freeUnloadedMiles = unloadedMileageItems.Where(i => (i.CustomName ?? "").Contains("FreeQuantity")).Sum(m => m.Quantity);
                    var freeUnloadedTotal = freeUnloadedMiles * unloadedRate;
                    var freeLoadedMiles = loadedMileageItems.Where(i => (i.CustomName ?? "").Contains("FreeQuantity")).Sum(m => m.Quantity);
                    var freeLoadedTotal = freeLoadedMiles * loadedRate;

                    if (CheckUnloadedMilesForceToOne(entry, unloadedMiles, unloadedTotal, logInfo))
                        unloadedMiles = 1;

                    // Check unloaded is editable
                    if (unloadedMiles > 0 && !po.UnloadedIsEditable && po.Status != "Closed")
                        throw new UserErrorException("Allstate does not allow unloaded miles to be entered for this invoice.");

                    // Check loaded is editable
                    if (loadedMiles > 0 && !po.LoadedIsEditable && po.Status != "Closed")
                        throw new UserErrorException("Allstate does not allow loaded miles to be entered for this invoice.");

                    // Check for missing mileage
                    if (unloadedMiles <= 0 && loadedMiles <= 0 && (po.UnloadedIsEditable || po.LoadedIsEditable) && (po.UnloadedRate > 0 || po.LoadedRate > 0) && (po.UnloadedRequested ?? 0) <= 0 && (po.LoadedRequested ?? 0) <= 0)
                    {
                        throw new UserErrorException("Unloaded miles and loaded miles are both missing.  Enter one (or both) and resubmit.");
                    }

                    // If Allstate is deducting more unloaded free miles than we are
                    var miles = unloadedMiles - freeUnloadedMiles;
                    var freeMilesDiff = (po.UnloadedIncluded ?? 0) - freeUnloadedMiles;
                    if (miles > 0 && freeMilesDiff > 0)
                    {
                        // Adjust the expected total to compensate for the difference
                        po.ExpectedTotal -= Math.Min(freeMilesDiff, miles) * unloadedRate;
                    }

                    // If Allstate is deducting more loaded free miles than we are
                    miles = loadedMiles - freeLoadedMiles;
                    freeMilesDiff = (po.LoadedIncluded ?? 0) - freeLoadedMiles;
                    if (miles > 0 && freeMilesDiff > 0)
                    {
                        // Adjust the expected total to compensate for the difference
                        po.ExpectedTotal -= Math.Min(freeMilesDiff, miles) * loadedRate;
                    }

                    // Enter unloaded mileage, only if we can, and only if its more than what Allstate estimated (or thinks should be requested)
                    if (po.UnloadedIsEditable && unloadedMiles > (po.UnloadedRequested ?? 0))
                    {
                        po.UnloadedRequested = unloadedMiles;
                        allstate.UnloadedChanged(logInfo, po);
                    }

                    // Enter loaded mileage, only if we can, and only if its more than what Allstate estimated (or thinks should be requested)
                    if (po.LoadedIsEditable && loadedMiles > (po.LoadedRequested ?? 0))
                    {
                        po.LoadedRequested = loadedMiles;
                        allstate.LoadedChanged(logInfo, po);
                    }

                    // If Allstate had a different unloaded miles than Towbook
                    if ((po.UnloadedRequested ?? 0) != unloadedMiles)
                    {
                        // Note the difference
                        po.AmountDiffs.Add(new Allstate.AmountDiff()
                        {
                            Name = "Unloaded",
                            DiffType = Allstate.AmountDiffType.Mileage,
                            AllstateAmount = po.UnloadedRequested ?? 0,
                            TowbookAmount = unloadedMiles,
                        });
                    }

                    // If Allstate had a different loaded miles than Towbook
                    if ((po.LoadedRequested ?? 0) != loadedMiles)
                    {
                        // Note the difference
                        po.AmountDiffs.Add(new Allstate.AmountDiff()
                        {
                            Name = "Loaded",
                            DiffType = Allstate.AmountDiffType.Mileage,
                            AllstateAmount = po.LoadedRequested ?? 0,
                            TowbookAmount = loadedMiles,
                            TowbookName = loadedMileageItems.Where(i => i.Price > 0 && i.Quantity > 0).FirstOrDefault()?.Name
                        });
                    }

                    // If Allstate had a different unloaded rate than Towbook
                    if (po.UnloadedRate > 0 && po.UnloadedRate != unloadedRate)
                    {
                        // Note the difference
                        po.AmountDiffs.Add(new Allstate.AmountDiff()
                        {
                            Name = "Unloaded Rate",
                            DiffType = Allstate.AmountDiffType.MileageRate,
                            AllstateAmount = po.UnloadedRate ?? 0,
                            TowbookAmount = unloadedRate,
                            TowbookName = unloadedMileageItems.Where(i => i.Price > 0 && i.Quantity > 0).FirstOrDefault()?.Name
                        });
                    }

                    // If Allstate had a different loaded rate than Towbook
                    if (po.LoadedRate > 0 && po.LoadedRate != loadedRate)
                    {
                        // Note the difference
                        po.AmountDiffs.Add(new Allstate.AmountDiff()
                        {
                            Name = "Loaded Rate",
                            DiffType = Allstate.AmountDiffType.MileageRate,
                            AllstateAmount = po.LoadedRate ?? 0,
                            TowbookAmount = loadedRate,
                        });
                    }

                    // Log the PO we're about to submit
                    logInfo.AddEvent($"PO: {po.ToJson()}", LogLevel.Info);

                    // Submit the purchase order
                    // Send it the PO we retrieved, and the HTML used to retrieve it to avoid fetching it twice
                    var submitTotal = allstate.SubmitPurchaseOrder(logInfo, po, html);

                    var en = new EntryNote() { DispatchEntryId = entry.Id, OwnerUserId = 1 };
                    en.Content = "Submitted motor club job to Allstate...\n\nTestMode=" + logInfo.TestMode + "\n\n";
                    en.Content += "invoiceAmount: " + entry.InvoiceTotal.ToString("C") + "\n";
                    en.Content += "services: " + po.Services.ToJson(true) + "\n";
                    en.Content += "unloadedMiles: " + unloadedMiles + "\n";
                    en.Content += "loadedMiles: " + loadedMiles + "\n";
                    en.Save();

                    // Flush logs if requested
                    if (logInfo.FlushRequested)
                        logInfo.FlushEvents(logger);

                    LogEvent(logInfo, $"Submitted to Allstate: ${submitTotal:N}, NoteId: {en.Id}", LogLevel.Info);
                }
            }
        }
        finally
        {
            allstate.ReleaseLock(logInfo);
        }
    }

    private static async Task DispatchEntrySyncRoadAmerica(LogInfo logInfo, Entry entry, string username, string password, string poNumber)
    {
        // Get conn
        var roadAmerica = RoadAmerica.RoadAmericaConnection.Login(logInfo, username, password);

        // Set logging vars
        roadAmerica.LogHttpTraffic = true;

        // Get the po from the Road America site
        var html = new HtmlDocument();
        var po = roadAmerica.GetPurchaseOrder(logInfo, poNumber, out html);

        if (po == null)
            throw new NotFoundException("PO does not exist on the Road America site. ");

        if (!string.IsNullOrWhiteSpace(po.Message))
        {
            logInfo.AddEvent($"PO message: '{po.Message}'", LogLevel.Info);

            if (po.Message == "This PO is not ready for invoicing yet. Please try again later.")
                throw new RetryException("Not yet available to submit on the Road America website.");

            else if (po.Message == "You can not invoice this PO." ||
                     po.Message == "This PO cannot be invoiced because it has been cancelled." ||
                     po.Message == "This is a Cash Call PO.")
                throw new NotSubmittableException($"Status on Road America site is '{po.Status}'");

            else if (po.Message == "This PO has been invoiced.")
            {
                if (po.Status.ToLower().Contains("denied"))
                    throw new NotSubmittableException($"Status on Road America site is '{po.Status}'");
                else
                    throw new CancelledException($"Status on Road America site is '{po.Status}'");
            }

            else if (po.Message == "The Auto Approval System will invoice this claim on your behalf, no further action is required.")
                throw new CancelledException($"Road America says '{po.Message}'");

            else
                throw new BillingException($"Road America says '{po.Message}'");
        }

        // If PO doesn't have a submit button, or its disabled
        var node = html.DocumentNode.SelectSingleNode("//input[@id='btnSubmit']");
        if (node == null || node.Attributes.Any(a => a.Name.ToLower() == "disabled"))
            throw new NotSubmittableException($"Status on Road America site is '{po.Status}'");

        // Set the total we expect Road America to calculate for this PO, and a copy of the original total
        po.ExpectedTotal = po.TowbookTotal = entry.InvoiceTotal;

        // Calculate the tax rate (if used)
        var taxMultiplier = 0m;
        if (entry.InvoiceTax > 0 && entry.InvoiceSubtotal > 0)
        {
            var taxedSubTotal = entry.InvoiceItems.Where(i => i.Taxable).Sum(i => i.Total);
            var taxRate = entry.InvoiceTax / taxedSubTotal;
            taxMultiplier = 1 + taxRate;
        }

        //--------------------------------------------------------------------------------------------------------
        // Invoice item filtering:  save and remove all ii's that aren't valid for reverse- or forward-matching
        //--------------------------------------------------------------------------------------------------------

        // Get list of entry invoice items
        var invoiceItems = new Collection<InvoiceItem>();
        foreach (var ii in entry.InvoiceItems.Reverse()) { invoiceItems.Add(ii); } // Reverse the order to smooth out future matching

        var unloadedMileageItems = new List<InvoiceItem>();
        var loadedMileageItems = new List<InvoiceItem>();
        var deadHeadMileageItems = new List<InvoiceItem>();

        for (var x = invoiceItems.Count - 1; x >= 0; x--)
        {
            var ii = invoiceItems[x];
            var item = new { Name = ii.CustomName, Price = ii.Price, Quantity = ii.Quantity, Total = ii.Total, RateItemId = ii.RateItem?.RateItemId ?? 0 }.ToJson();
            var itemName = ii.Name.ToLowerInvariant().Replace("-", " ").Replace("  ", " ").Replace(".", "").Trim();
            var rateItemName = (ii.RateItem?.Name ?? "").ToLowerInvariant().Replace("-", " ").Replace("  ", " ").Replace(".", "").Trim();

            // Check if its unloaded
            if (itemName.Contains("unloaded mil") ||
                itemName.Contains("enroute mil") ||
                itemName.Contains("en route mil") ||
                ii.RateItem?.Predefined?.Id == 1)
            {
                unloadedMileageItems.Add(ii);
                invoiceItems.RemoveAt(x);
                continue;
            }

            // Check if its loaded
            if (itemName.Contains("loaded mil") ||
                itemName.Contains("hooked mil") ||
                ii.RateItem?.Predefined?.Id == 2)
            {
                loadedMileageItems.Add(ii);
                invoiceItems.RemoveAt(x);
                continue;
            }

            // If its a discount, then ignore it
            if (itemName.StartsWith("discount:"))
            {
                logInfo.AddEvent($"Ignoring Discount: {item}", LogLevel.Warn);

                // If its total is positive, deduct it from the total we expect Road America to calculate
                if (ii.Total > 0)
                    po.ExpectedTotal -= ii.Total;
                invoiceItems.RemoveAt(x);
                continue;
            }

            if (!ReimbursementShouldSubmit(logInfo, ii, entry))
            {
                po.ExpectedTotal -= ii.Total;
                invoiceItems.RemoveAt(x);

                continue;
            }



            // If its a deadhead mileage item
            if (itemName.Contains("dhm") || rateItemName.Contains("dhm") ||
                itemName.Contains("deadhead") || rateItemName.Contains("deadhead") ||
                itemName.Contains("dead head") || rateItemName.Contains("dead head") ||
                itemName == "negotiated additional charge" || rateItemName == "negotiated additional charge" ||
                itemName == "extra" || rateItemName == "extra")
            {
                deadHeadMileageItems.Add(ii);
                invoiceItems.RemoveAt(x);
                continue;
            }

            // If its a customer overage charge, ignore it
            if (itemName.Contains("customer overage") ||
                itemName.Contains("customer paid") ||
                itemName.Contains("customer to pay") ||
                itemName.Contains("overage") ||
                itemName.Contains("overmil") ||
                itemName.Contains("over mil") ||
                itemName.Contains("out of pocket"))
            {
                logInfo.AddEvent($"Ignoring {item} because it is a CUSTOMER OVERAGE charge", LogLevel.Warn);

                // Deduct it from the total we expect Road America to calculate
                po.ExpectedTotal -= ii.Total;
                invoiceItems.RemoveAt(x);
                continue;
            }
        }

        //-------------------------------------------------------------------------------------------------
        // Reverse-matching:  Match each charge that comes preloaded on the PO to a Towbook invoice item
        //-------------------------------------------------------------------------------------------------

        // For each charge on this PO
        foreach (var chg in po.InvoiceCharges.Where(c => !c.IsMileage()))
        {
            // If there is an ii that matches this charge
            var ii = RoadAmericaGetMatch(chg, invoiceItems);
            if (ii != null)
            {
                var ii_Total = ii.Taxable ? ii.Total * taxMultiplier : ii.Total;

                chg.MatchFound = true;

                // Don't add it to the invoice later; it's already on it
                invoiceItems.Remove(ii);

                // Enter charge amount
                chg.InvoiceAmount = (ii_Total - (chg.ServiceRate ?? 0) > 0.1m) ? ii_Total : chg.ServiceRate;
            }
        }

        // If multiple unmatched charges
        var charges = po.InvoiceCharges.Where(c => !c.IsMileage() && !c.MatchFound);
        if (charges.Count() > 1)
        {
            // Throw exception
            throw new MatchingException("Unable to find Towbook invoice item matches for multiple charges on Road America purchase order.");
        }

        // If single unmatched charge
        else if (charges.Count() == 1)
        {
            var chg = charges.First();

            // If no more ii's, and this charge has a service rate
            if (invoiceItems.Count == 0 && chg.ServiceRate > 0)
            {
                // Let it submit using its own service rate
                chg.InvoiceAmount = chg.ServiceRate ?? 0;
            }

            // If single unmatched ii
            else if (invoiceItems.Count == 1)
            {
                var ii = invoiceItems.First();
                var ii_Total = ii.Taxable ? ii.Total * taxMultiplier : ii.Total;

                // Assume they match
                chg.MatchFound = true;

                // Enter charge amount
                chg.InvoiceAmount = (ii_Total - (chg.ServiceRate ?? 0) > 0.1m) ? ii_Total : chg.ServiceRate;

                invoiceItems.Clear();
            }

            // If multiple unmatched ii's
            else if (invoiceItems.Count > 1)
            {
                // Avoid submitting too much in case one of the ii's should have matched
                throw new MatchingException($"Unable to find a Towbook invoice item match for Road America invoice charge '{chg.FeeDescription}'");
            }
        }

        //---------------------------------------------------------------------------------------
        // Forward-matching:  Match each ii to a charge and add it to the PO
        //---------------------------------------------------------------------------------------

        // For each invoice item (including deadhead we previously filtered out)
        foreach (var ii in invoiceItems.Concat(deadHeadMileageItems))
        {
            var item = new { Name = ii.CustomName, Price = ii.Price, Quantity = ii.Quantity, Total = ii.Total, RateItemId = ii.RateItem != null ? ii.RateItem.RateItemId : 0 }.ToJson();
            var itemName = ii.Name.ToLowerInvariant().Replace("-", " ").Replace("  ", " ").Replace(".", "").Trim();
            var ii_Total = ii.Taxable ? ii.Total * taxMultiplier : ii.Total;

            // If its GOA or its total is 0, skip over it
            if (itemName.Contains("goa") || itemName.Contains("gone on arrival") || ii.Total == 0)
                continue;

            // If it matches a charge (or Labor by default)
            var charge = RoadAmerica.InvoiceCharge.FromString(itemName, true);
            if (charge != null)
            {
                // If we already have this charge (Road America only allows one of each charge)
                var chg = po.InvoiceCharges.FirstOrDefault(c => c.FeeType == charge.FeeType);
                if (chg != null)
                {
                    // Add to it
                    chg.InvoiceAmount = (chg.InvoiceAmount ?? 0) + ii_Total;
                    continue;
                }
                else
                {
                    // If we're allowed to add this charge
                    chg = po.AvailableCharges.FirstOrDefault(c => c.FeeType == charge.FeeType);
                    if (chg != null)
                    {
                        // Create it
                        chg.InvoiceAmount = ii_Total;
                        po.InvoiceCharges.Add(chg);
                        continue;
                    }
                }
            }

            // If it didn't match anything else, throw exception
            throw new MatchingException($"Unable to add Towbook invoice item to the PO: '{ii.Name.Trim()}'");
        }

        // Billing Notes
        po.ProviderComment = new string(entry.BillingNotes().Take(90).ToArray()); // Road America only accepts max 90 chars

        // Get mileages
        var unloadedMiles = unloadedMileageItems.Where(i => i.Price > 0 && i.Quantity > 0).Sum(i => i.Quantity);
        var unloadedRate = unloadedMileageItems.Where(i => i.Price > 0 && i.Quantity > 0).Select(i => i.Price).FirstOrDefault();
        var unloadedTotal = unloadedMileageItems.Where(i => i.Price > 0 && i.Quantity > 0).Sum(i => i.Total);
        var loadedMiles = loadedMileageItems.Where(i => i.Price > 0 && i.Quantity > 0).Sum(i => i.Quantity);
        var loadedRate = loadedMileageItems.Where(i => i.Price > 0 && i.Quantity > 0).Select(i => i.Price).FirstOrDefault();
        var loadedTotal = loadedMileageItems.Where(i => i.Price > 0 && i.Quantity > 0).Sum(i => i.Total);
        var freeUnloadedMiles = unloadedMileageItems.Where(i => (i.CustomName ?? "").Contains("FreeQuantity")).Sum(f => f.Quantity);
        var freeUnloadedTotal = freeUnloadedMiles * unloadedRate;
        var freeLoadedMiles = loadedMileageItems.Where(i => (i.CustomName ?? "").Contains("FreeQuantity")).Sum(f => f.Quantity);
        var freeLoadedTotal = freeLoadedMiles * loadedRate;
        var enr = po.InvoiceCharges.FirstOrDefault(c => c.FeeType == "ENR");
        var loa = po.InvoiceCharges.FirstOrDefault(c => c.FeeType == "LOA");

        if (CheckUnloadedMilesForceToOne(entry, unloadedMiles, unloadedTotal, logInfo))
            unloadedMiles = 1;
        
        // Check for missing mileage
        if (unloadedMiles <= 0 && loadedMiles <= 0 && enr != null && loa != null)
        {
            throw new UserErrorException("Unloaded miles and loaded miles are both missing.  Enter one (or both) and resubmit.");
        }

        // If there is more than one ENR
        if (po.InvoiceCharges.Count(c => c.FeeType == "ENR") > 1)
        {
            // Throw exception - we can't submit those yet; they require more advanced mapping
            throw new BillingException("Road America PO contains multiple 'Enroute' mileage fees; please bill manually");
        }

        // If there is more than one LOA
        if (po.InvoiceCharges.Count(c => c.FeeType == "LOA") > 1)
        {
            // Throw exception - we can't submit those yet; they require more advanced mapping
            throw new BillingException("Road America PO contains multiple 'Loaded' mileage fees; please bill manually");
        }

        // Check unloaded free miles
        if (unloadedMiles > freeUnloadedMiles && enr?.FreeMiles != null && freeUnloadedMiles != enr.FreeMiles)
        {
            logInfo.AddEvent($"Free miles difference (Unloaded) - Road America: {enr.FreeMiles}, Towbook: {freeUnloadedMiles}", LogLevel.Info);
        }

        // Check loaded free miles
        if (loadedMiles > freeLoadedMiles && loa?.FreeMiles != null && freeLoadedMiles != loa.FreeMiles)
        {
            logInfo.AddEvent($"Free miles difference (Loaded) - Road America: {loa.FreeMiles}, Towbook: {freeLoadedMiles}", LogLevel.Info);
        }

        // Enter unloaded mileage
        if (enr != null)
        {
            enr.TotalMiles = unloadedMiles;
            enr.InvoiceAmount = Math.Max((enr.TotalMiles ?? 0) - (enr.FreeMiles ?? 0), 0) * (enr.ServiceRate ?? 0);
        }
        else if (unloadedTotal - freeUnloadedTotal > 0)
        {
            po.AddAdditionalLabor(unloadedTotal - freeUnloadedTotal); // Enter unloaded as Labor
        }

        // Enter loaded mileage
        if (loa != null)
        {
            loa.TotalMiles = loadedMiles;
            loa.InvoiceAmount = Math.Max((loa.TotalMiles ?? 0) - (loa.FreeMiles ?? 0), 0) * (loa.ServiceRate ?? 0);
        }
        else if (loadedTotal - freeLoadedTotal > 0)
        {
            po.AddAdditionalLabor(loadedTotal - freeLoadedTotal); // Enter loaded as Labor
        }

        // If there are files required
        if (po.RequiresInventorySheet || po.RequiresPayoutReceipt)
        {
            // Get all files for this entry (containing valid extensions)
            foreach (var cf in CompanyFile.GetByDispatchEntryId(entry.Id)
                .Where(c => (new [] {".pdf", ".tif", ".jpg", ".png"}).Contains(Path.GetExtension(c.Filename).ToLower())))
            {
                // If we need an Inventory Sheet and we found one
                if (po.RequiresInventorySheet && cf.Filename.ToLowerInvariant().Contains("inventory sheet"))
                {
                    // Get the file contents
                    var localLocation = cf.LocalLocation.Replace("%1", $"{entry.CompanyId}");
                    var path = await FileUtility.GetFileAsync(localLocation);
                    var bytes = File.ReadAllBytes(path);

                    // Add it to the PO
                    po.InventorySheet = new RoadAmerica.FileUpload()
                    {
                        FileName = cf.Filename,
                        LocalLocation = localLocation,
                        PathUsed = path,
                        Size = cf.Size,
                        Contents = bytes,
                    };

                    continue;
                }

                // If we need a Payout Receipt and we found one
                if (po.RequiresPayoutReceipt && (cf.Filename.ToLowerInvariant().Contains("payout receipt") ||
                                                 cf.Filename.ToLowerInvariant().Contains("payout reciept")))
                {
                    // Get the file contents
                    var localLocation = cf.LocalLocation.Replace("%1", $"{entry.CompanyId}");
                    var path = await FileUtility.GetFileAsync(localLocation);
                    var bytes = File.ReadAllBytes(path);

                    // Add it to the PO
                    po.PayoutReceipt = new RoadAmerica.FileUpload()
                    {
                        FileName = cf.Filename,
                        LocalLocation = cf.LocalLocation,
                        PathUsed = path,
                        Size = cf.Size,
                        Contents = bytes,
                    };

                    continue;
                }
            }

            // Missing both files
            if (po.RequiresInventorySheet && po.RequiresPayoutReceipt && po.InventorySheet == null && po.PayoutReceipt == null)
                throw new UserErrorException("Road America requires files to be uploaded for this PO. Add two files named 'Inventory Sheet' and 'Payout Receipt' to the Towbook call, then resubmit.");

            // Missing Inventory Sheet
            if (po.RequiresInventorySheet && po.InventorySheet == null)
                throw new UserErrorException("Road America requires a file to be uploaded for this PO. Add a file named 'Inventory Sheet' to the Towbook call, then resubmit.");

            // Missing Payout Receipt
            if (po.RequiresPayoutReceipt && po.PayoutReceipt == null)
                throw new UserErrorException("Road America requires a file to be uploaded for this PO. Add a file named 'Payout Receipt' to the Towbook call, then resubmit.");
        }

        // Log the PO we're about to submit
        logInfo.AddEvent($"PO: {po.ToJson()}", LogLevel.Info);

        // Submit the purchase order
        // Send it the PO we retrieved, and the HTML used to retrieve it to avoid fetching it twice
        var submitTotal = roadAmerica.SubmitPurchaseOrder(logInfo, po, html);

        var en = new EntryNote() { DispatchEntryId = entry.Id, OwnerUserId = 1 };
        en.Content = "Submitted motor club job to Road America...\n\nTestMode=" + logInfo.TestMode + "\n\n";
        en.Content += "invoiceAmount: " + entry.InvoiceTotal.ToString("C") + "\n";
        en.Content += "invoiceCharges: " + po.InvoiceCharges.ToJson(true) + "\n";
        en.Save();

        // Flush logs if requested
        if (logInfo.FlushRequested)
            logInfo.FlushEvents(logger);

        LogEvent(logInfo, $"Submitted to Road America: ${submitTotal:N}, NoteId: {en.Id}", LogLevel.Info);
    }

    private static void DispatchEntrySyncNsd(LogInfo logInfo, Entry entry, string username, string password, string poNumber)
    {
        // Get conn
        var nsd = Nsd.NsdConnection.Login(logInfo, username, password);

        // Set logging vars
        nsd.LogHttpTraffic = true;
        logInfo.FlushRequested = true;

        // Get the po from the NSD site
        var html = new HtmlDocument();
        var po = nsd.GetPurchaseOrder(logInfo, poNumber, out html);

        if (!string.IsNullOrWhiteSpace(po.Message))
        {
            logInfo.AddEvent($"PO message: '{po.Message}'", LogLevel.Info);

            if (po.Message.Contains("An error on this page is preventing it from being viewed"))
                throw new BillingException("NSD says 'An error on this page is preventing it from being viewed at this time'");

            else if (po.Message.Contains("ALREADY SUBMITTED") || 
                     po.Message.Contains("ALREADY APPROVED") || 
                     po.Message.Contains("ALREADY PAID"))
                throw new CancelledException("PO has already been submitted");

            else if (po.Message.Contains("This TEST PO is not visible"))
                throw new NotFoundException("PO does not exist on the NSD site. ");

            else if (po.Message.Contains("CANCELLED"))
                throw new NotSubmittableException($"PO has been cancelled");

            else if (po.Message.Contains("NO Coverage. Customer responsible for all costs"))
                throw new NotSubmittableException("Unable to submit PO.  NSD says 'NO Coverage. Customer responsible for all costs.'");

            else
                throw new BillingException($"NSD says '{po.Message}'");
        }

        // If we couldn't parse a total
        if (po.TotalAmount == null)
            throw new NotFoundException("PO does not exist on the NSD site. ");

        // Set the total we expect Road America to calculate for this PO, and a copy of the original total
        po.ExpectedTotal = po.TowbookTotal = entry.InvoiceTotal;

        foreach (var ii in entry.InvoiceItems)
        {
            var item = new { Name = ii.CustomName, Price = ii.Price, Quantity = ii.Quantity, Total = ii.Total, RateItemId = ii.RateItem?.RateItemId ?? 0 }.ToJson();
            var itemName = ii.Name.ToLowerInvariant().Replace("-", " ").Replace("  ", " ").Replace(".", "").Trim();

            if (!ReimbursementShouldSubmit(logInfo, ii, entry))
                continue;

            // Check if its unloaded
            if (itemName.Contains("unloaded mil") ||
                itemName.Contains("enroute mil") ||
                itemName.Contains("en route mil") ||
                ii.RateItem?.Predefined?.Id == 1)
            {
                continue;
            }

            // Check if its loaded
            if (itemName.Contains("loaded mil") ||
                itemName.Contains("hooked mil") ||
                ii.RateItem?.Predefined?.Id == 2)
            {
                continue;
            }

            // If its a discount, then ignore it
            if (itemName.StartsWith("discount:"))
            {
                logInfo.AddEvent($"Ignoring Discount: {item}", LogLevel.Warn);

                // If its total is positive, deduct it from the total we expect Geico to calculate
                if (ii.Total > 0)
                    po.ExpectedTotal -= ii.Total;
                continue;
            }

            // If its GOA, note that we have one
            if (itemName.Contains("goa") || itemName.Contains("gone on arrival"))
            {
                po.IsGOA = true;
                continue;
            }

            // If its a customer overage charge, ignore it
            if (itemName.Contains("customer overage") ||
                itemName.Contains("customer paid") ||
                itemName.Contains("customer to pay") ||
                itemName.Contains("overage") ||
                itemName.Contains("overmil") ||
                itemName.Contains("over mil") ||
                itemName.Contains("out of pocket"))
            {
                logInfo.AddEvent($"Ignoring {item} because it is a CUSTOMER OVERAGE charge", LogLevel.Warn);

                // Deduct it from the total we expect Geico to calculate
                po.ExpectedTotal -= ii.Total;
                continue;
            }
        }

        // Submit the purchase order
        var submitTotal = nsd.SubmitPurchaseOrder(logInfo, po, html);

        var en = new EntryNote() { DispatchEntryId = entry.Id, OwnerUserId = 1 };
        en.Content = "Submitted motor club job to Nsd...\n\nTestMode=" + logInfo.TestMode + "\n\n";
        en.Content += "invoiceAmount: " + entry.InvoiceTotal.ToString("C") + "\n";
        en.Save();

        // Flush logs if requested
        if (logInfo.FlushRequested)
            logInfo.FlushEvents(logger);

        LogEvent(logInfo, $"Submitted to Nsd: ${submitTotal:N}, NoteId: {en.Id}", LogLevel.Info);
    }

    private static void DispatchEntrySyncQuest(LogInfo logInfo, Entry entry, string username, string password, string providerId, string poNumber)
    {
        // Get conn
        var quest = Quest.QuestConnection.Login(logInfo, username, password, providerId);

        try
        {
            // Set logging vars
            quest.LogHttpTraffic = true;
            logInfo.FlushRequested = true;

            // If po is not available
            var p = quest.GetAvailablePOs(logInfo).FirstOrDefault(a => a.PurchaseOrderNumber == poNumber);
            if (p == null)
            {
                // If it was within the last 14 days
                if (entry.CreateDate > DateTime.Today.AddDays(-14))
                    throw new RetryException("Not yet available to submit on the Quest website.");
                else
                    throw new NotFoundException("PO does not exist on the Quest site. ");
            }

            // Get the po from the Quest site
            var html = new HtmlDocument();
            var po = quest.GetPurchaseOrder(logInfo, poNumber, out html);

            if (po == null)
                throw new NotFoundException("PO does not exist on the Quest site. ");

            po.VIN = entry.VIN;
            po.InvoiceNumber = entry.CallNumber.ToString();
            po.InvoiceTax = entry.InvoiceTax;

            // Set the total we expect Quest to calculate for this PO, and a copy of the original total
            po.ExpectedTotal = po.TowbookTotal = entry.InvoiceTotal;

            //--------------------------------------------------------------------------------------------------------
            // Invoice item filtering:  save and remove all ii's that aren't valid for reverse- or forward-matching
            //--------------------------------------------------------------------------------------------------------

            var unloadedMileageItems = new List<InvoiceItem>();
            var loadedMileageItems = new List<InvoiceItem>();
            var roadServiceItems = new List<InvoiceItem>();
            var nonPreDefinedItems = entry.InvoiceItems.Count(o => !(o.RateItem != null && o.RateItem.Predefined != null &&
                (o.RateItem.Predefined.Id == PredefinedRateItem.BUILTIN_MILEAGE_LOADED ||
                    o.RateItem.Predefined.Id == PredefinedRateItem.BUILTIN_MILEAGE_UNLOADED)));

            foreach (var ii in entry.InvoiceItems)
            {
                var item = new { Name = ii.CustomName, Price = ii.Price, Quantity = ii.Quantity, Total = ii.Total, RateItemId = ii.RateItem?.RateItemId ?? 0 }.ToJson();
                var itemName = ii.Name.ToLowerInvariant().Replace("-", " ").Replace("  ", " ").Replace(".", "").Trim();
                var rateItemName = (ii.RateItem?.Name ?? "").ToLowerInvariant().Replace("-", " ").Replace("  ", " ").Replace(".", "").Trim();
                var isGOA = itemName == "goa" || itemName.EndsWith(" goa") || itemName.Contains("gone on arrival");

                // ONLY FOR QUEST - High priority check for "Overage"
                //                  Low priority checks for other types of overages are further down
                if (itemName.Contains("overage"))
                {
                    logInfo.AddEvent($"Ignoring {item} because it is a CUSTOMER OVERAGE charge", LogLevel.Warn);

                    // Deduct it from the total we expect Quest to calculate
                    po.ExpectedTotal -= ii.Total;
                    ii.Quantity = 0;
                    continue;
                }

                if (!ReimbursementShouldSubmit(logInfo, ii, entry))
                    continue;

                // Check if its unloaded
                if (itemName.Contains("unloaded mil") ||
                    itemName.Contains("enroute mil") ||
                    itemName.Contains("en route mil") ||
                    ii.RateItem?.Predefined?.Id == 1)
                {
                    unloadedMileageItems.Add(ii);
                    continue;
                }

                // Check if its loaded
                if (itemName.Contains("loaded mil") ||
                    itemName.Contains("hooked mil") ||
                    ii.RateItem?.Predefined?.Id == 2)
                {
                    loadedMileageItems.Add(ii);
                    continue;
                }

                // If it isn't, and total is 0 then ignore it
                if (ii.Total == 0 && !isGOA)
                    continue;

                // If its a discount, then ignore it
                if (itemName.StartsWith("discount:"))
                {
                    logInfo.AddEvent($"Ignoring Discount: {item}", LogLevel.Warn);

                    // If its total is positive, deduct it from the total we expect Quest to calculate
                    if (ii.Total > 0)
                        po.ExpectedTotal -= ii.Total;
                    ii.Quantity = 0;
                    continue;
                }

                // If its a road service item
                if (itemName == "quest" ||
                    itemName == "start" ||
                    itemName.Contains("lockout") ||
                    itemName.Contains("lock out") ||
                    itemName.Contains("road service") ||
                    itemName.Contains("tire change") ||
                    itemName.Contains("tire service") ||
                    itemName.Contains("fuel delivery") ||
                    itemName.Contains("feel delivery") ||
                    itemName.Contains("flat tire") ||
                    itemName.Contains("jump start") ||
                    itemName.Contains("jumpstart") ||
                    itemName == "j/s" ||
                    itemName.Contains("tow") ||
                    itemName.Contains("hook") ||
                    itemName.Contains("goa") ||
                    itemName.Contains("standard rate") ||
                    itemName.Contains("wheel lift") ||
                    itemName.Contains("base") ||
                    itemName.Contains("accident") ||
                    itemName.Contains("flatbed") ||
                    itemName.Contains("flat bed") ||
                    itemName.Contains("light duty") ||
                    itemName.Contains("motorcycle"))
                {
                    // And it has a price or its GOA, ignore it
                    if (ii.Price > 10 || isGOA)
                    {
                        logInfo.AddEvent($"Ignoring {item}", LogLevel.Warn);
                        roadServiceItems.Add(ii);
                        continue;
                    }
                }

                // If its the only non-mileage item, ignore it
                if (nonPreDefinedItems == 1)
                {
                    logInfo.AddEvent($"Ignoring {item} because it is the only non-mileage item on the invoice.", LogLevel.Warn);
                    continue;
                }

                // If its a customer overage charge, ignore it
                if (itemName.Contains("customer overage") ||
                    itemName.Contains("customer paid") ||
                    itemName.Contains("customer to pay") ||
                    itemName.Contains("overage") ||
                    itemName.Contains("overmil") ||
                    itemName.Contains("over mil") ||
                    itemName.Contains("out of pocket"))
                {
                    logInfo.AddEvent($"Ignoring {item} because it is a CUSTOMER OVERAGE charge", LogLevel.Warn);

                    // Deduct it from the total we expect Quest to calculate
                    po.ExpectedTotal -= ii.Total;
                    ii.Quantity = 0;
                    continue;
                }

                //-----------------------------------------------------
                // If we got this far, it must be an invoice charge
                //-----------------------------------------------------

                var charge = po.GetChargeByName(itemName);
                if (charge != null)
                {
                    // If we don't already have this charge (Quest only allows one of each charge)
                    if (!po.InvoiceCharges.Any(c => c.ServiceNum == charge.ServiceNum))
                    {
                        // Add it to po
                        po.InvoiceCharges.Add(new Quest.InvoiceCharge()
                        {
                            ServiceNum = charge.ServiceNum,
                            Name = charge.Name,
                            Quantity = ii.Total,
                            Amount = 1m
                        });
                    }
                    continue;
                }
            }

            // Get mileages
            var unloadedMiles = unloadedMileageItems.Where(i => i.Price > 0 && i.Quantity > 0).Sum(i => i.Quantity);
            var unloadedRate = unloadedMileageItems.Where(i => i.Price > 0 && i.Quantity > 0).Select(i => i.Price).FirstOrDefault();
            var unloadedTotal = unloadedMileageItems.Where(i => i.Price > 0 && i.Quantity > 0).Sum(i => i.Total);
            var loadedMiles = loadedMileageItems.Where(i => i.Price > 0 && i.Quantity > 0).Sum(i => i.Quantity);
            var loadedRate = loadedMileageItems.Where(i => i.Price > 0 && i.Quantity > 0).Select(i => i.Price).FirstOrDefault();
            var loadedTotal = loadedMileageItems.Where(i => i.Price > 0 && i.Quantity > 0).Sum(i => i.Total);
            var freeUnloadedMiles = unloadedMileageItems.Where(i => (i.CustomName ?? "").Contains("FreeQuantity")).Sum(f => f.Quantity);
            var freeUnloadedTotal = freeUnloadedMiles * unloadedRate;
            var freeLoadedMiles = loadedMileageItems.Where(i => (i.CustomName ?? "").Contains("FreeQuantity")).Sum(f => f.Quantity);
            var freeLoadedTotal = freeLoadedMiles * loadedRate;

            if (CheckUnloadedMilesForceToOne(entry, unloadedMiles, unloadedTotal, logInfo))
                unloadedMiles = 1;

            // Enter unloaded mileage
            if (unloadedMiles > 0)
            {
                // If an MTV (miles to vehicle) charge is available,
                // either 'Tow or GOA MTV' or 'Service MTV'
                var charge = po.GetChargeByName("MTV");
                if (charge != null)
                {
                    // Add it to po
                    po.InvoiceCharges.Add(new Quest.InvoiceCharge()
                    {
                        ServiceNum = charge.ServiceNum,
                        Name = charge.Name,
                        Quantity = unloadedMiles,
                        TowbookFreeMilesTotal = freeUnloadedTotal,
                    });
                }
            }

            // Enter loaded mileage
            if (loadedMiles > 0)
            {
                // If Loaded Mileage charge is available
                var charge = po.GetChargeByName("Loaded Mileage");
                if (charge != null)
                {
                    // Add it to po
                    po.InvoiceCharges.Add(new Quest.InvoiceCharge()
                    {
                        ServiceNum = charge.ServiceNum,
                        Name = charge.Name,
                        Quantity = loadedMiles,
                        TowbookFreeMilesTotal = freeLoadedTotal,
                    });
                }
            }

            // Log the PO we're about to submit
            logInfo.AddEvent($"PO: {po.ToJson()}", LogLevel.Info);

            // Submit the purchase order
            // Send it the PO we retrieved, and the HTML used to retrieve it to avoid fetching it twice
            var submitTotal = quest.SubmitPurchaseOrder(logInfo, po, html);

            var en = new EntryNote() { DispatchEntryId = entry.Id, OwnerUserId = 1 };
            en.Content = "Submitted motor club job to Quest...\n\nTestMode=" + logInfo.TestMode + "\n\n";
            en.Content += "invoiceAmount: " + entry.InvoiceTotal.ToString("C") + "\n";
            en.Content += "invoiceCharges: " + po.InvoiceCharges.ToJson(true) + "\n";
            en.Save();

            // Flush logs if requested
            if (logInfo.FlushRequested)
                logInfo.FlushEvents(logger);

            LogEvent(logInfo, $"Submitted to Quest: ${submitTotal:N}, NoteId: {en.Id}", LogLevel.Info);
        }
        finally
        {
            quest.ReleaseLock(logInfo);
        }
    }

    private static async Task HandleDispatchEntryQueueException(LogInfo logInfo, Exception e, DispatchEntryQueueItem x, ProcessMessageEventArgs r)
    {
        var resultId = GetSubmitResult(e);

        var logMessage = "";
        switch (resultId)
        {
            // Level 1 errors - ignore these
            case SubmitResult.LoginError:
                logMessage = $"(L1) Failed to login because of exception \"{e.Message}\"";
                break;

            // Level 2 errors - take a look at these; some will need fixing
            case SubmitResult.NotFound:
            case SubmitResult.NotSubmittable:
            case SubmitResult.Retry:
            case SubmitResult.UserError:
            case SubmitResult.MatchingError:
                logMessage = $"(L2) Failed to submit invoice because of exception \"{e.Message}\""; ;
                break;

            // Level 3 errors - these are an issue
            default:
                logMessage = $"(L3) Failed because of exception \"{e.Message}\" -- {e}";
                break;
        }

        logInfo.FlushEvents(logger);
        await r.DeadLetterAsync($"{r.Message.MessageId}: {logInfo.MotorClubName} -- " + logMessage, e.ToJson());
        LogEvent(logInfo, $"{logInfo.MotorClubName} -- " + logMessage, LogLevel.Error);
        DispatchEntryQueueItem.UpdateStatus(x, Extric.Towbook.Integration.MotorClubs.Queue.QueueItemStatus.Error, e.Message, resultId);
    }

    private static void HandlePurchaseOrderListQueueException(LogInfo logInfo, string logMessage, Exception e, PurchaseOrderListQueueItem x)
    {
        LogEvent(logInfo, $"{logInfo.MotorClubName} -- " + logMessage, LogLevel.Error);
        PurchaseOrderListQueueItem.UpdateStatus(x, Extric.Towbook.Integration.MotorClubs.Queue.QueueItemStatus.Error);
    }

    private static async Task HandleAuthenticationQueueException(string logMessage, Exception e, AuthenticationQueueItem x, ProcessMessageEventArgs r)
    {
        await r.CompleteAsync();
        logger.LogEvent(logMessage, x.CompanyId, LogLevel.Error);

        // Delete the username/password if its already part of the account AND it's the same password.
        if (GetAccountKeyValue(x.CompanyId, x.AccountId, "McUsername")?.Value == x.Username)
            SaveAccountKeyValue(x.CompanyId, x.AccountId, "McUsername", "");

        if (GetAccountKeyValue(x.CompanyId, x.AccountId, "McPassword")?.Value == x.Password)
            SaveAccountKeyValue(x.CompanyId, x.AccountId, "McPassword", "");

        // Mark as error
        AuthenticationQueueItem.UpdateStatus(x, AuthenticationQueueItemStatus.Error);

        // Push notification - failed
        await PushNotificationProvider.BackgroundJobStatusUpdate(x.CompanyId,
            x.Id,
            "billing_login",
            false,
            "Invalid username/password", null);
    }

    private static void LogEvent(LogInfo logInfo, string message, LogLevel logLevel, Dictionary<object, object> properties = null)
    {
        logger.LogEvent(logInfo.Prefix(message), logInfo.CompanyId, logLevel, logInfo.GetProperties(properties));
    }

    private static async Async.Task DeadLetter(LogInfo logInfo, string message, ProcessMessageEventArgs r)
    {
        await r.DeadLetterAsync("");
        LogEvent(logInfo, message, LogLevel.Error);
    }

    private static AccountKeyValue GetAccountKeyValue(int companyId, int accountId, string keyName)
    {
        return AccountKeyValue.GetByAccount(companyId, accountId, Provider.Towbook.ProviderId, keyName).FirstOrDefault();
    }

    private static void SaveAccountKeyValue(int companyId, int accountId, string keyName, string value)
    {
        var bu = GetAccountKeyValue(companyId, accountId, keyName);

        if (bu == null)
            bu = new AccountKeyValue(accountId, Provider.Towbook.GetKey(KeyType.Account, keyName).Id, "");

        bu.Value = value;
        bu.Save();
    }

    private static string InvoiceItemsToJson(IEnumerable<InvoiceItem> items)
    {
        var list = new List<object>();

        foreach (var ii in items)
        {
            var rateItemId = 0;
            var rateItemName = "";
            var predefined = "";

            if (ii.RateItem != null)
            {
                rateItemId = ii.RateItem.RateItemId;
                rateItemName = ii.RateItem.Name;

                if (ii.RateItem.Predefined != null)
                    predefined = ii.RateItem.Predefined.Name;

                if (rateItemName == (ii.CustomName ?? ii.Name) || rateItemName == predefined)
                    rateItemName = "";
            }

            list.Add(new { Name = (ii.CustomName ?? ii.Name), Price = ii.Price, Quantity = ii.Quantity, Total = ii.Total, Taxable = ii.Taxable,
                RateItemId = rateItemId, RateItemName = rateItemName, Predefined = predefined });
        }

        return list.ToJson();
    }

    private static SubmitResult GetSubmitResult(Exception e)
    {
        if (e is InvalidLoginException)
            return SubmitResult.LoginError;

        else if (e is NotFoundException)
            return SubmitResult.NotFound;

        else if (e is NotSubmittableException)
            return SubmitResult.NotSubmittable;

        else if (e is RetryException)
            return SubmitResult.Retry;

        else if (e is UserErrorException)
            return SubmitResult.UserError;

        else if (e is UnknownChargeException || e is MatchingException)
            return SubmitResult.MatchingError;

        else
            return SubmitResult.TowbookError;
    }

    protected void OnStop()
    {
        List<Async.Task> tasks = new List<Async.Task>();
        foreach (KeyValuePair<string, Extric.Towbook.EventNotifications.Consumer> entry in cqs)
        {
            Console.WriteLine("Stopping consumer MotorClubBillingService for queue " + entry.Key);
            tasks.Add(entry.Value.CleanAsync());
        }
        Thread.Sleep(1000);
        Async.Task.WaitAll(tasks.ToArray());
        ENServiceBusHelper.Cleanup();
    }

    private void OnStopped()
    {
        logger.Info("MotorClubBillingService stopped successfully");
        LogManager.Shutdown();
        Console.WriteLine("MotorClubBillingService Stopped finished");
    }

    #region DRY Methods

    private static readonly string[] validAgeroEquipmentTypes = new string[] {
        "5WT","HDLB","HDRS","HDTS","HDUR","HDW" ,"LDF" ,"LDWL" ,"LRS",
        "LT","MDF","MDRS","MDTS","MDUR","MDW","MRS","MM","MP","MS","TS",
        "WLD","WMD","WHD","LO","LCT","RRTS","AAR","M3",
        "M2","PAR","MAP" };

    public static string AgeroGetEquipmentType(LogInfo logInfo, int accountId, string po)
    {
        var arc = new Agero.AgeroRestClient();
        var ags = Agero.AgeroSession.GetByAccountId(accountId);

        if (ags == null)
        {
            var cr = CallRequest.GetByDispatchId(logInfo.DispatchEntryId);

            if (cr != null && cr.AccountId != logInfo.AccountId)
            {
                ags = Agero.AgeroSession.GetByAccountId(cr.AccountId);
            }

            if (ags == null)
                return null;
        }

        var dd = arc.GetDispatchDetail(ags.AccessToken, po);
        if (dd == null)
            return null;

        var rt = dd.Equipment?.Id;

        if (rt == "DW")
            rt = "LDWL";

        if (rt == "SAR") // digital dispatch api returns SAR for secondary. 
            rt = "AAR";  // billing portal expects AAR for secondary.

        if (rt == "LPT") // light duty transPorT.
            rt = "LDF";

        if (rt == "VEX")
            rt = "LDF";

        if (!validAgeroEquipmentTypes.Contains(rt))
            return null;

        return rt;
    }

    private static bool CheckUnloadedMilesForceToOne(Entry entry, decimal unloadedMiles, decimal unloadedTotal, LogInfo logInfo)
    {
        if (CompanyKeyValue.GetFirstValueOrNull(entry.CompanyId, Provider.Towbook.ProviderId, "MCBilling_ForceUnloadedMilesToOneIfMissing") == "1" ||
                    AccountKeyValue.GetFirstValueOrNull(entry.CompanyId, entry.AccountId, Provider.Towbook.ProviderId, "MCBilling_ForceUnloadedMilesToOneIfMissing") == "1")
        {
            // only apply this rule IF there is no destination set to prevent accidentally submitting invoices without miles set.
            if (string.IsNullOrWhiteSpace((entry.TowDestination ?? "").Replace(",", "").Replace(" ", "")) ||
                ((entry.TowDestination ?? "").Contains("Drive Type:") &&
                (entry.TowDestination ?? "").Contains("Service Type")))
            {
                if (unloadedMiles == 0 || unloadedTotal == 0)
                {
                    LogEvent(logInfo, $"Forcing unloaded miles to 1 - {entry.Id.ToString()} / {entry.CallNumber}", LogLevel.Info);
                    return true;
                }
            }
        }

        return false;
    }

    private static void AllstateServiceDiff(LogInfo logInfo, Allstate.PurchaseOrder po, Allstate.Service svc, InvoiceItem ii)
    {
        // If the Towbook amount is different than the Allstate amount for this service (and the service is not GOA)
        var diff = (ii.Total - (svc.BaseUnitAmount ?? 0));
        if (diff != 0 && !(svc.GoaInd == true))
        {
            // Note it
            po.AmountDiffs.Add(new Allstate.AmountDiff()
            {
                Name = svc.GroupServiceCodeDesc,
                DiffType = Allstate.AmountDiffType.Service,
                AllstateAmount = svc.BaseUnitAmount ?? 0,
                TowbookAmount = ii.Total,
            });

            // If Towbook would pay more, issue an invoice correction for the difference
            if (diff > 0)
                po.AddAdditionalAmount(diff, $"Invoice correction for {svc.GroupServiceCodeDesc}: {diff:C}");
        }

        //// Else if the Towbook amount + 10% (offset for tax) is less than the Allstate amount for this service
        //else if (ii.Total * 1.1m < svc.BaseUnitAmount.Value)
        //{
        //    // Adjust the expectedTotal by the difference
        //    po.ExpectedTotal += (-diff);

        //    // Log an event for this
        //    logInfo.AddEvent($"Towbook's service total of {ii.Total} for '{svc.GroupServiceCodeDesc}' is less than the Allstate service total of {svc.BaseUnitAmount}", LogLevel.Info);
        //}
    }


    private static InvoiceItem RoadAmericaGetMatch(RoadAmerica.InvoiceCharge chg, Collection<InvoiceItem> items)
    {
        InvoiceItem match = null;

        foreach (var ii in items)
        {
            var itemName = ii.Name.ToLowerInvariant().Replace("-", " ").Replace("  ", " ").Replace(".", "").Trim();

            var charge = RoadAmerica.InvoiceCharge.FromString(itemName, chg.IsFee(), chg.FeeType);
            if (charge?.FeeType == chg.FeeType)
            {
                if (match == null || (
                    Math.Abs((chg.ServiceRate ?? 0) - ii.Total) <
                    Math.Abs((chg.ServiceRate ?? 0) - match.Total)))
                {
                    match = ii;
                }
            }
        }

        return match;
    }

    #endregion
}

public class SplitName
{
    private static List<string> Suffixes = null;

    static SplitName()
    {
        // Initialize suffixes
        Suffixes = new List<string>();
        Suffixes.Add("jr");
        Suffixes.Add("sr");
        Suffixes.Add("esq");
        Suffixes.Add("ii");
        Suffixes.Add("iii");
        Suffixes.Add("iv");
        Suffixes.Add("v");
        Suffixes.Add("2nd");
        Suffixes.Add("3rd");
        Suffixes.Add("4th");
        Suffixes.Add("5th");
    }

    /// <summary>
    /// Splits a name string into the first and last name
    /// </summary>
    /// <param name="name">Name to be split</param>
    /// <param name="firstName">Returns the first name</param>
    /// <param name="lastName">Returns the last name</param>
    public static void Split(string name, out string firstName, out string lastName)
    {
        if (string.IsNullOrWhiteSpace(name) || !name.Contains(" "))
        {
            firstName = null;
            lastName = null;
            return;
        }

        // Parse last name
        int pos = FindWordStart(name, name.Length - 1);

        // If last token is suffix, include next token
        // as part of last name also
        if (IsSuffix(name.Substring(pos)))
            pos = FindWordStart(name, pos);

        // Set results
        firstName = name.Substring(0, pos).Trim();
        lastName = name.Substring(pos).Trim();
    }

    /// <summary>
    /// Finds the start of the word that comes before startIndex.
    /// </summary>
    /// <param name="s">String to examine</param>
    /// <param name="startIndex">Position to begin search</param>
    private static int FindWordStart(string s, int startIndex)
    {
        // Find end of previous word
        while (startIndex > 0 && Char.IsWhiteSpace(s[startIndex]))
            startIndex--;
        // Find start of previous word
        while (startIndex > 0 && !Char.IsWhiteSpace(s[startIndex]))
            startIndex--;
        // Return final position
        return startIndex;
    }

    /// <summary>
    /// Returns true if the given string appears to be a name suffix
    /// </summary>
    /// <param name="s">String to test</param>
    private static bool IsSuffix(string s)
    {
        // Strip any punctuation from string
        StringBuilder sb = new StringBuilder();
        foreach (char c in s)
        {
            if (Char.IsLetterOrDigit(c))
                sb.Append(c);
        }
        return Suffixes.Contains(sb.ToString(), StringComparer.OrdinalIgnoreCase);
    }
}

public class FakeServiceBusReceiver : ServiceBusReceiver
{
    public virtual string FullyQualifiedNamespace => "Extric.Towbook.Services.MotorClubBillingService";

    //commented to remove await warning
    // public override async Task CompleteMessageAsync(
    //     ServiceBusReceivedMessage message,
    //     CancellationToken cancellationToken = default)
    // {
    //     Console.WriteLine("Complete async with fake message: " + message.MessageId);
    // }
}

public enum ServiceType
{
    DispatchEntryService,        // 0
    PurchaseOrderListService,    // 1
    PaymentService,              // 2
    AuthenticationService,       // 3
    DispatchEntryDetailsService, // 4
}
