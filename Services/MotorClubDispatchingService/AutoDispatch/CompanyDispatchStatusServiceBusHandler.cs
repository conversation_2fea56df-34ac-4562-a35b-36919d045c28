using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Mail;
using System.Threading.Tasks;
using Extric.Towbook.Accounts;
using Extric.Towbook.Dispatch;
using Extric.Towbook.EventNotifications;
using Extric.Towbook.Integration;
using Extric.Towbook.Integration.MotorClubs;
using Extric.Towbook.Integration.MotorClubs.Services;
using Extric.Towbook.Utility;
using Newtonsoft.Json;
using NLog;
using Async = System.Threading.Tasks;

namespace Extric.Towbook.Services.MotorClubDispatchingService.AutoDispatch
{
    public class CompanyDispatchStatusServiceBusHandler
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();
        public static string QueueName = "companyDispatchStatus";

        private static Consumer iq;
        public static async Task SendAsync(int companyId, TimeSpan ts) => await SendAsync(companyId, ts, null);

        public static async Task SendAsync(int companyId, TimeSpan ts, Entry en)
        {
            try
            {
                if (!await(await Company.Company.GetByIdAsync(companyId)).HasFeatureAsync(Generated.Features.AutoDispatch))
                    return;

                var qc = await ServiceBusHelper.CreateProducerQueueAsync(QueueName);
                var bm = new BrokeredMessage(new CompanyDispatchStatusServiceBusMessageBody(companyId, "update", ts, en?.Id, en?.CallNumber, en?.Account?.Company, en?.Driver?.Name).ToJson());

                // don't let these pile up.
                bm.TimeToLive = ts < TimeSpan.FromHours(4) ? TimeSpan.FromHours(4) : ts.Add(TimeSpan.FromHours(4));

                if (ts.Ticks > 0)
                {
                    var time = DateTime.Now.Date.Add(ts);
                    if (time < DateTime.Now)
                        time = time.AddDays(1);

                    bm.ScheduledEnqueueTime = time.ToUniversalTime();
                }

                await qc.SendAsync(bm);

                Console.WriteLine("Added Scheduled Event for " + companyId + " -- " + ts.ToString());
            }
            catch (Exception e)
            {
                Console.WriteLine(e.ToString());
            }
        }

        public static async Task SendText(string phone, string message)
        {
            try
            {
                await DispatchNotificationMessage.SendAsync(phone, message);
            }
            catch(Exception e)
            {
                logger.Log(LogLevel.Warn, e, "couldnt send text");
            }
        }

        public static async Async.Task<CompanyDispatchStatusServiceBusHandler> Init()
        {
            var incomingOptions = new OnMessageOptions()
            {
                AutoCompleteMessages = false,
                MaxConcurrentCalls = 32
            };
            
            // TODO: use incoming options
            iq = await ServiceBusHelper.CreateConsumerQueueAsync(QueueName, incomingOptions);
            // NewAutoDispatch
            // AutoDispatchExpired

            await iq.OnMessageAsync(async (args) => {

                string body = args.GetBody<string>();
                Console.WriteLine($"Received: {body}");

                //var body = r.GetBody<string>();

                Console.WriteLine(body);
                try
                {
                    //"{type:'update', companyId: 123}"

                    var obj = JsonConvert.DeserializeObject<CompanyDispatchStatusServiceBusMessageBody>(body);
                    int companyId = (int)obj.CompanyId;
                    var time = (TimeSpan?)obj.Scheduled;

                    if (time != null)
                    {
                        ScheduleTimeDelete(companyId, time.Value);
                    }

                    logger.Info(MasterAccountTypes.Towbook, "CompanyDispatchStatusHandler", obj.Type, companyId: companyId, data: obj);

                    try
                    {
                        if (obj.Type == "update") // 2 minutes
                        {
                            await UpdateCompanyDispatchStatus(companyId, obj.CallId, obj.CallNumber, obj.AccountName, obj.DriverName);
                        }
                    }
                    catch (Exception y)
                    {
                        Console.WriteLine(y.ToString());
                    }
                }
                catch (Exception e)
                {
                    Console.WriteLine(e.ToJson());
                }
                // complete the message. messages is deleted from the queue. 
                await args.CompleteAsync();

            });
            
            CompanyDispatchStatusServiceBusHandler sbh = new CompanyDispatchStatusServiceBusHandler();
            
            return sbh;
        }

        public static async Task Deinit()
        {
            if (iq == null)
                return;

            Console.WriteLine("Stopping consumer CompanyDispatchStatusServiceBusHandler");
            await iq.CleanAsync();
        }

        public static async Async.Task UpdateCompanyDispatchStatus(int companyId, int? callId, int? callNumber, string accountName, string driverName)
        {
            var totalTime = Stopwatch.StartNew();
            var company = await Company.Company.GetByIdAsync(companyId);

            var cds = CompanyDispatchStatusCurrent.GetByCompanyId(companyId) ?? new CompanyDispatchStatusCurrent() { CompanyId = companyId };

            // human-readable reasons for the location being the current status.
            List<string> reasons = new List<string>();

            bool match(int userId)
            {
                var u = User.GetById(userId);
                if (u != null)
                {
                    if (u.Type == User.TypeEnum.Driver) return true;
                    if (u.PrimaryCompanyId == companyId) return true;
                }
                return false;
            }
            var d1 = await Driver.GetByExactCompanyIdAsync(companyId);
            // limit supervisors to parent company only when evaluating company status.
            var drivers = d1
                .Where(o => o.UserId > 0 && o.EndDate == null && o.Deleted == false)
                .Where(o => o.CompanyId == companyId || match(o.UserId)).ToList();

            List<int> companyIds = new List<int>() { companyId };

            foreach (var x in drivers.SelectMany(o => o != null && o.Companies != null ? o.Companies : Array.Empty<int>()))
            {
                if (!companyIds.Contains(x))
                    companyIds.Add(x);
            }
            
            var keys = DriverKeyValue.GetByDriver(companyId,
                drivers.Select(o => o.Id),
                Provider.Towbook.ProviderId,
                new string[] {
                    "Rating",
                    "BlockedZipCodesJson",
                    "ScheduleForceOff",
                    "ScheduleAlwaysOn",
                    "ScheduleJson"
                });

            // We are not looking at GPS 

            var driverKeys = DriverKey.GetAll().Where(o => o.ProviderId == Provider.Towbook.ProviderId);

            var keyBlockedZipCodesJson = driverKeys.Where(o => o.Name == "BlockedZipCodesJson").First().Id;
            var keyScheduleForceOff = driverKeys.Where(o => o.Name == "ScheduleForceOff").First().Id;
            var keyScheduleAlwaysOn = driverKeys.Where(o => o.Name == "ScheduleAlwaysOn").First().Id;
            var keyScheduleJson = driverKeys.Where(o => o.Name == "ScheduleJson").First().Id;

            var today = DateTime.Now.DayOfWeek.ToString().ToUpperInvariant();
            var zipsByDriver = new Dictionary<int, string[]>();
            var drivers2 = new List<Driver>();

            var scheduledTimes = new List<TimeSpan>();

            var ignoreOff = CompanyKeyValue.GetFirstValueOrNull(companyId, Provider.Towbook.ProviderId,
                "AutoDispatch_DontMarkDownForNoDriverResponse") == "1";

            if (callNumber != null)
                reasons.Add(driverName + " failed to accept Call #" + callNumber + " from " + accountName);




            foreach (var driver in drivers)
            {
                var exitNeeded = false;

                // step 1: ignore drivers that are forced off
                var forceOff = keys.Where(o => o.DriverId == driver.Id && o.KeyId == keyScheduleForceOff).FirstOrDefault()?.Value;
                if (forceOff == "1")
                {
                    if (ignoreOff)
                    {
                        reasons.Add(driver.Name + " is currently marked unavailable, but will not be factored into location status");
                    }
                    else
                    {
                        reasons.Add(driver.Name + " is currently marked unavailable");
                        continue;
                    }
                }


                // step 3: check if they are always on
                var alwaysOn = keys.Where(o => o.DriverId == driver.Id && o.KeyId == keyScheduleAlwaysOn).FirstOrDefault()?.Value;

                if (alwaysOn != "1")
                {
                    var schedule = keys.Where(o => o.DriverId == driver.Id && o.KeyId == keyScheduleJson).FirstOrDefault()?.Value;
                    if (schedule != null)
                    {
                        var sched = JsonConvert.DeserializeObject<IEnumerable<AutoDispatchServiceBusHandler.ScheduleItem>>(schedule).Where(o => 
                            string.Equals(o.DayName, today, StringComparison.InvariantCultureIgnoreCase));
                        var foundMatch = false;

                        var timeofday = DateTime.Now.TimeOfDay;
                        foreach (var slot in sched)
                        {
                            if (slot.Start.Ticks == 0 && slot.Stop.Ticks == 0)
                            {
                                Console.WriteLine(driver.Name + "driver is scheduled to work all day");
                                reasons.Add(driver.Name + " is scheduled available all day");
                                foundMatch = true;
                                break;
                            }
                            // datetime.now.timeofday is eastern
                            // slot.start/slot.stop are user local time, they need to be converted from pacific to eastern, for example. 

                            // slot.start / slot.stop needs to be offset. User is entering it in 'their time' 
                            slot.Start = slot.Start.Add(TimeSpan.FromMinutes((long)company.TimezoneOffset * 60));
                            slot.Stop = slot.Stop.Add(TimeSpan.FromMinutes((long)company.TimezoneOffset * 60));

                            if (!scheduledTimes.Contains(slot.Start))
                                scheduledTimes.Add(slot.Start);

                            if (!scheduledTimes.Contains(slot.Stop))
                                scheduledTimes.Add(slot.Stop);

                            if ((timeofday >= slot.Start && ((slot.Stop.Ticks == 0 || timeofday < slot.Stop || slot.Stop < slot.Start))) ||  // handle stopping at 12:00am which is 0 ticks.
                                ((slot.Stop < slot.Start) ? timeofday < slot.Stop : false) // handle crossover (7pm-7am)
                              )
                            {
                                string time = "";

                                if (slot.Stop < DateTime.Now.TimeOfDay)
                                    time = DateTime.Today.AddDays(1).Add(slot.Stop).ToShortTowbookTimeString();
                                else
                                    time = DateTime.Today.Add(slot.Stop).ToShortTowbookTimeString();

                                var timezone = "";
                                switch (company.TimezoneOffset)
                                {
                                    case -1: timezone = "CT"; break;
                                    case -2: timezone = "MT"; break;
                                    case -3: timezone = "PT"; break;
                                }
                                
                                reasons.Add(driver.Name + " is scheduled to work until " + time + " " + timezone);

                                foundMatch = true;
                                break;
                            }
                        }

                        if (!foundMatch)
                        {
                            reasons.Add(driver.Name + " is not currently scheduled to work.");
                            exitNeeded = true;
                        }
                    }
                }
                else
                {
                    if (!exitNeeded)
                    {
                        reasons.Add(driver.Name + " is marked always available.");
                    }
                }

                if (exitNeeded)
                    continue;

                drivers2.Add(driver);
            }

            foreach (var driver in drivers2)
            {
                // step 2: make sure the call isn't in a zip that the driver won't work in
                var blockedZips = keys.Where(o => o.DriverId == driver.Id && o.KeyId == keyBlockedZipCodesJson).FirstOrDefault()?.Value;
                if (blockedZips != null)
                {
                    // only 5 digit zip codes are valid.
                    var blockedZipList = blockedZips.Replace(",", " ")
                        .Replace(";", "")
                        .Replace("\n", " ")
                        .Replace("\r", " ")
                        .Split(' ')
                        .Where(o => o.Length == 5)
                        .ToCollection();

                    zipsByDriver[driver.Id] = blockedZipList.ToArray();
                }
            }

            Console.WriteLine(company.Id + "/" + company.Name + ": " + (drivers2.Any() ? "ONLINE" : "OFFLINE"));

            if (!drivers2.Any())
                Console.WriteLine(reasons.ToJson(true));

            List<string> blockedZipsToSave = new List<string>();
            if (drivers2.Any())
            {
                //zipsByDriver.Keys

                // all the drivers
                // all the zips
                if (drivers2.Count == 1 && zipsByDriver.ContainsKey(drivers2.First().Id))
                {
                    cds.BlockedZips = zipsByDriver[drivers2.First().Id].ToList().ToJson();
                }
                else
                {
                    var allZips = new List<string>();

                    foreach (var key in zipsByDriver)
                    {
                        allZips.AddRange(key.Value);
                    }

                    cds.BlockedZips = allZips.Distinct().GroupBy(o => o)
                        .Select(o => new { Zip = o.FirstOrDefault(), Count = o.Count() })
                        .Where(o => o.Count == drivers2.Count).Select(o => o.Zip).ToArray()
                        .ToJson();
                }
            }



            cds.IsActive = drivers2.Any();
            cds.Reasons = reasons.ToJson();
            bool changed = false;

            if (cds.IsDirty)
            {
                if (cds.ForceDown)
                {
                    cds.IsActive = false;
                    reasons.Add("Company is manually set as down, so it won't be automatically brought up by a driver checking in or being scheduled.");
                }

                cds.Save();
                changed = true;
                Console.WriteLine(cds.ChangedFields.ToJson(true));
            }

            var pusherEvent = new
            {
                companyId = cds.CompanyId,
                isActive = cds.IsActive,
                reasons = reasons
            };

            if (changed)
            {
                var sc = (await Company.SharedCompany.GetByCompanyIdAsync(companyId)).Where(o => o.CompanyId != companyId).FirstOrDefault();

                if (sc != null)
                {
                    // send to parent companyId
                    await PushNotificationProvider.PushExact(sc.CompanyId, "companyDispatchStatus_changed", pusherEvent);
                }

                await PushNotificationProvider.PushExact(cds.CompanyId, "companyDispatchStatus_changed", pusherEvent);
            }
            
            if (!cds.ChangedFields.Where(o => o.Field == "IsActive").Any())
                changed = false;

            if (changed && !cds.IsActive)
            {
                // evaluate current jobs to see if they should be cancelled.

                var allCurrentCalls = await Entry.GetCurrentByCompanyAsync(company, true, false, false, true);

                foreach (var x in allCurrentCalls)
                {
                    // make it wait 10 minutes before performing evaluation, 
                    // this gives company long enough to come back in case its 
                    // quickly going up/down. May want to make this configurable.
                    
                    await AutoDispatchServiceBusHandler.Send(x, TimeSpan.FromMinutes(10));
                }
            }

            var currentlyScheduled = GetScheduledTimes(companyId);
            Console.WriteLine("SCHEDULED: " + currentlyScheduled.ToJson() + " for companyid" + companyId);

            List<TimeSpan> futures = new List<TimeSpan>();
            foreach(var time in scheduledTimes)
            {
                if (!currentlyScheduled.Contains(time))
                {
                    var time2 = time;
                    if (time2.Ticks == 0)
                        time2 = time2.Add(TimeSpan.FromTicks(1));

                    Console.WriteLine("UpdateCompanyDispatchStatus: scheduling to run at " + time2.ToString());

                    foreach (var id in companyIds)
                    {
                        if (!GetScheduledTimes(id).Contains(time2))
                        {
                            await SendAsync(id, time2);
                            ScheduleTime(id, time2);
                        }
                        else
                        {
                            Console.WriteLine("*** ALREADY SCHEDULED: " + id + " ... " + time2.ToString());
                        }
                    }
                    futures.Add(time2);
                }
            }
            try
            {
                if (changed)
                {
                    string email = null;

                    var rootCompany = Company.SharedCompany.GetByCompanyId(companyId).FirstOrDefault()?.CompanyId ?? company.Id;
                    if (rootCompany == 26130)
                        email = "<EMAIL>";
                    else
                        email = company.Email;

                    var users = User.GetByCompanyId(companyId).Where(o => !o.Disabled);

                    if (Core.IsEmailValid(email))
                    {
                        var msg = new MailMessage();

                        msg.From = new MailAddress("<EMAIL>", "Towbook");
                        msg.To.Add(new MailAddress(email));

                        foreach (var supervisor in users.Where(o => o.Notes != null && o.Notes.Contains("GrantSupervisorRole")))
                        {
                            if (Core.IsEmailValid(supervisor.Email))
                            {
                                msg.To.Add(new MailAddress(supervisor.Email, supervisor.FullName));
                            }
                        }

                        msg.Bcc.Add(new MailAddress("<EMAIL>"));
                        msg.Priority = MailPriority.High;
                        msg.Subject = company.Name + ": Location status has changed to " + (cds.IsActive ? "UP" : "DOWN");

                        msg.IsBodyHtml = false;
                        msg.Body = "The status for " + company.Name + " has been changed to " + (cds.IsActive ? "UP" : "DOWN") + ".\n\n";
                        msg.Body += "The conditions used to determine this status are:\n\n" +
                            string.Join("\n", reasons);

                        msg.Body += "\n\n---\n";
                        msg.Body += "Generated by Towbook\n";

                        using (var scc = new SmtpClient().Get())
                        {
                            await scc.Send(msg, await User.GetByIdAsync(1), "autodispatch");
                        }

                        logger.Info(MasterAccountTypes.Towbook, "CompanyDispatchStatusHandler", "Sent Email Notification",
                            companyId: companyId, data: new
                            {
                                email = email,
                                subject = msg.Subject,
                                message = msg.Body
                            });
                    }

                    foreach (var user in users.Where(o => 
                        Core.IsPhoneValidStandard(Core.FormatPhoneWithNumbersOnly(o.MobilePhone)) &&
                     ((o.Notes ?? "").Contains("GrantSupervisorRole") ||
                      (o.Notes ?? "").Contains("SendLocationStatusTexts"))))
                    {
                        var msg = company.Name + " location status has changed to " +
                                (cds.IsActive ? "UP" : "DOWN") + " as of " + DateTime.Now.ToShortTowbookTimeString() + " ET.";

                        try
                        {
                            await SendText(Core.FormatPhoneWithNumbersOnly(user.MobilePhone), msg);

                            logger.Info(MasterAccountTypes.Towbook, "CompanyDispatchStatusHandler", "Sent Text Notification",
                                companyId: companyId, data: new
                                {
                                    phone = user.MobilePhone,
                                    message = msg
                                });
                        }
                        catch (Exception er2)
                        {

                            logger.Info(MasterAccountTypes.Towbook, "CompanyDispatchStatusHandler", "Sent Text Notification",
                                companyId: companyId, data: new
                                {
                                    phone = user.MobilePhone,
                                    message = msg,
                                    exception = er2
                                });
                        }
                    }

                }
            }
            catch (Exception er)
            {
                logger.Info(MasterAccountTypes.Towbook,
                    "CompanyDispatchStatusHandler",
                    "Send Email Failed",
                    companyId: companyId,
                    data: new
                    {
                        exception = er
                    });
            }

            logger.Info(MasterAccountTypes.Towbook,
                "CompanyDispatchStatusHandler",
                "Performance",
                companyId: companyId,
                data: new
                {
                    changed = changed,
                    pusher = pusherEvent,
                    totalTimeMs = totalTime.ElapsedMilliseconds,
                    scheduledNext = futures
                });
        }

        public static void ScheduleTime(int companyId, TimeSpan time)
        {
            Cache.Instance.HashSet("times:" + companyId, time.ToString(), time);
        }

        public static void ScheduleClearAll(int companyId)
        {
            Core.DeleteRedisKey("times:" + companyId);
        }

        public static void ScheduleTimeDelete(int companyId, TimeSpan time)
        {
            Cache.Instance.HashDelete("times:" + companyId, time.ToString());
            Console.WriteLine("DELETED " + "times:" + companyId + "/" + time.ToString());
        }

        public static TimeSpan[] GetScheduledTimes(int companyId)
        {
            Console.WriteLine("GetScheduledTimes(" + companyId + ")");
            var ret = Cache.Instance.HashGetAll<TimeSpan>("times:" + companyId)
                .Select(o => o).ToArray();

            Console.WriteLine("-->" + ret.ToJson());
            return ret;
        }
    }

    internal class CompanyDispatchStatusServiceBusMessageBody
    {
        public int CompanyId { get; set; }
        public string Type { get; set; }
        public TimeSpan Scheduled { get; set; }
        public int? CallId { get; set; }
        public int? CallNumber { get; set; }
        public string AccountName { get; set; }
        public string DriverName { get; set; }

        public CompanyDispatchStatusServiceBusMessageBody(int companyId, string type, TimeSpan scheduled, int? callId, int? callNumber, string accountName, string driverName)
        {
            CompanyId = companyId;
            Type = type;
            Scheduled = scheduled;
            CallId = callId;
            CallNumber = callNumber;
            AccountName = accountName;
            DriverName = driverName;
        }
    }
}
