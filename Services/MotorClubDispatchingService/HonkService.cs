using System;
using System.Linq;
using System.Threading.Tasks;
using Extric.Towbook.Accounts;
using Extric.Towbook.Integration.MotorClubs.Queue;
using Extric.Towbook.Integrations.MotorClubs.Honk;
using Extric.Towbook.Utility;
using Newtonsoft.Json;
using NLog;
using Extric.Towbook.Integration.MotorClubs;
using System.Collections.Generic;
using Extric.Towbook.Dispatch;
using Extric.Towbook.Integrations.Email;
using Extric.Towbook.Integration;
using Extric.Towbook.Vehicle;
using Extric.Towbook.Integration.MotorClubs.Dispatch;
using Extric.Towbook.Integration.MotorClubs.Services;
using Extric.Towbook.Integration.MotorClubs.Honk;
using Azure.Messaging.ServiceBus;
using System.ServiceModel.Channels;

namespace Extric.Towbook.Services.MotorClubDispatchingService
{
    public class HonkService
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        private const int HonkUserId = 18;
        public static async Task<bool> CreateOonJob(DigitalDispatchActionQueueItem qi, ProcessMessageEventArgs r, HonkRestClient.JobModel accept)
        {
            var dispatch = HonkJob.GetByDispatchId(accept.Id, accept.provider_id);

            var callRequest = await CallRequest.GetByIdAsync(dispatch.CallRequestId);
            var fe = await DistributedLock.ForAsync("Honk", accept.Id.ToString(), 10000,

            lockAcquired: async delegate ()
            {
                Entry call = null;
                call = await Entry.GetByPurchaseOrderNumberAsync(qi.AccountId.Value, accept.Id.ToString());

                if (call == null)
                {
                    call = new Entry();
                    call.CompanyId = callRequest.CompanyId;
                    call.AccountId = callRequest.AccountId;
                }
                else
                {
                    logger.LogEvent("Honk/{0}: Found existing towbook call for Case {1}... Call #{2}... we're going to update this one.",
                        callRequest.CompanyId, LogLevel.Info, r.Message.MessageId, accept.Id.ToString(), call.CallNumber);

                    if (call.Status == Status.Cancelled)
                    {
                        logger.LogEvent("Honk/{0}: Call is cancelled. Proceeding to uncancel and set status to waiting...",
                            callRequest.CompanyId, LogLevel.Info, r.Message.MessageId, accept.Id.ToString(), call.CallNumber);
                        await call.Uncancel(new AuthenticationToken() { UserId = HonkUserId, ClientVersionId = MotorClubDispatchingService.MyClientVersionId });
                    }
                }

                if (call.Account?.DefaultPriority == 1)
                    call.Priority = Entry.EntryPriority.High;

                #region Vehicle

                EntryAsset asset = null;
                if (call.Assets != null)
                    asset = call.Assets.FirstOrDefault();

                if (asset == null)
                    asset = new EntryAsset() { BodyTypeId = 1 };

                if (asset.BodyTypeId == 0)
                    asset.BodyTypeId = 1;

                var vc = VehicleUtility.GetColorIdByName(accept.VehicleColor);

                if (int.TryParse(accept.VehicleYear, out var nYear))
                    asset.Year = nYear;

                asset.Make = VehicleUtility.GetManufacturerByName(accept.VehicleMake);
                asset.Model = VehicleUtility.GetModelByName(accept.VehicleModel);

                if (vc != 0)
                    asset.ColorId = vc;

                asset.Vin = accept.VehicleVin;

                #endregion Vehicle

                #region Locations

                var pickup = call.Waypoints.FirstOrDefault(o => o.Title == "Pickup");
                var dest = call.Waypoints.FirstOrDefault(o => o.Title == "Destination");

                if (call.Notes == null)
                    call.Notes = "";

                if (accept.vehicle_location_address != null)
                {
                    if (string.IsNullOrWhiteSpace(call.TowSource) || call.Version < 3)
                    {
                        call.TowSource = accept.vehicle_location_address;
                        if (pickup == null)
                        {
                            pickup = new EntryWaypoint() { Title = "Pickup", Position = 1 };
                            call.Waypoints.Add(pickup);
                        }
                        pickup.Address = accept.vehicle_location_address;
                        if (accept.vehicle_location_lat != null)
                            pickup.Latitude = accept.vehicle_location_lat.Value;

                        if (accept.vehicle_location_lng != null)
                            pickup.Longitude = accept.vehicle_location_lng.Value;
                    }
                }

                if (!string.IsNullOrWhiteSpace(accept.destination_location_address))
                {
                    if (string.IsNullOrWhiteSpace(call.TowDestination) || call.Version < 3)
                    {
                        call.TowDestination = accept.destination_location_address;
                        if (dest == null && !string.IsNullOrWhiteSpace(call.TowDestination))
                        {
                            dest = new EntryWaypoint() { Title = "Destination", Position = 2 };
                            call.Waypoints.Add(dest);
                        }

                        dest.Address = accept.destination_location_address;

                        if (accept.destination_location_lat != null)
                            dest.Latitude = accept.destination_location_lat.Value;

                        if (accept.destination_location_lng != null)
                            dest.Longitude = accept.destination_location_lng.Value;
                    }
                }

                #endregion Locations

                // don't use offerExpires time, and they dont have a transaction timestamp, so we'll just use current server time.
                if (call.CreateDate == DateTime.MinValue)
                    call.CreateDate = DateTime.Now.AddSeconds(-DateTime.Now.Second); // dont use accept.service.OfferExpiresLocal;

                if (dispatch.Eta != null)
                {
                    call.ArrivalETA = call.CreateDate.AddMinutes(dispatch.Eta.Value);
                }

                #region po number
                call.PurchaseOrderNumber = accept.Id.ToString();
                #endregion

                #region Reason

                call.ReasonId = await ReasonHelper.DetermineReasonId(call.Account.MasterAccountId, qi.CompanyId.Value, accept.service_type);
                if (call.ReasonId == 1635)
                    call.Notes += "Service Needed: " + accept.service_type + "\n";



                #endregion Reason

                if (dispatch.AssignedDriverId != null)
                    call.DriverId = dispatch.AssignedDriverId.Value;

                if (callRequest != null)
                {
                    if (callRequest.OwnerUserId == null)
                        callRequest.OwnerUserId = 1;

                    call.CallRequestId = callRequest.CallRequestId;

                    if (call.OwnerUserId < 100)
                        call.OwnerUserId = callRequest.OwnerUserId.GetValueOrDefault(0);
                }
                else if (call.OwnerUserId < 100)
                    call.OwnerUserId = 1;
                #region Notes

                void addNote(string line, bool top = false)
                {
                    if (string.IsNullOrWhiteSpace(line))
                        return;


                    if (call.Notes == null || !call.Notes.Contains(line))
                    {
                        if (call.Notes == null)
                            call.Notes = line + "\n";
                        else
                        {
                            if (top)
                                call.Notes = line + call.Notes.Trim('\n') + "\n";
                            else
                                call.Notes += "\n" + line.Trim('\n');
                        }
                    }
                }

                if (accept.Notes != null)
                {
                    foreach (var note in accept.Notes)
                        addNote(note.Text);
                }
                #endregion Notes

                await MotorClubDispatchingService.ApplyRoundRobinDispatcherLogicAsync(qi, call, dispatch.JobId.ToString(), dispatch.ProviderId);

                if (call.Assets == null || call.Assets.Count == 0)
                {
                    call.Assets = new System.Collections.ObjectModel.Collection<EntryAsset>();
                    call.Assets.Add(asset);
                }

                var customerName = Core.FormatName(accept.customer_first_name);
                var phone = Core.FormatPhone(accept.customer_phone);

                EntryContact c = call.Contacts.FirstOrDefault(o =>
                    o.Name?.ToLowerInvariant() == customerName?.ToLowerInvariant() ||
                    Core.FormatPhone(o.Phone) == phone);

                bool newContact = false;
                if (c == null)
                {
                    c = new EntryContact() { Name = customerName };
                    call.Contacts.Add(c);
                    newContact = true;
                }

                c.Phone = phone;

                var oa = call.InvoiceItems.FirstOrDefault(o => o.Name == MotorClubDispatchingService.OON_LINE_ITEM_NAME);
                if (oa == null)
                {
                    call.InvoiceItems.Add(new InvoiceItem()
                    {
                        CustomName = MotorClubDispatchingService.OON_LINE_ITEM_NAME,
                        Quantity = 1,
                        CustomPrice = accept.estimated_payout_cents / 100
                    });
                }

                call.Notes += $"\nOnce this service is fulfilled and call status is set to Completed, a one-time credit card number will appear in the Billing Notes field of this call automatically.\n" +
                    $"If a GOA results, a one-time credit card number will appear upon Honk confirming that the customer is not at the scene.\n\n";

                var bn = "Out of Network Offer Amount: " + (accept.estimated_payout_cents.Value / 100).ToString("C");
                bn += ",\nGOA Rate: " + (accept.goa_cents.Value / 100).ToString("C");

                call.SetAttribute(Dispatch.AttributeValue.BUILTIN_BILLING_NOTES, bn);

                await call.Save();

                if (newContact)
                    await MotorClubDispatchingService.CheckForRoadsideFeatureAndAutoInvite(call, c);

                await AutoDispatch.AutoDispatchServiceBusHandler.Send(call);

                return call;
                
            },
            alreadyLocked: async delegate ()
            {
                logger.LogEvent("{0}/CR{1}: Lock already exists for {2}:{3}... pausing 250ms",
                    qi.CompanyId, LogLevel.Warn,
                    r.Message.MessageId,
                    (callRequest != null ? callRequest.CallRequestId.ToString() : "NULL"),
                    qi.AccountId.Value,
                    accept.Id);


                await System.Threading.Tasks.Task.Delay(250);

                return true;
            });

            if (fe == null)
            {
                logger.LogEvent("Honk/{0}: Creation of call failed; {1}",
                qi.CompanyId, LogLevel.Error, r.Message.MessageId, qi.ToJson());

                if (callRequest != null)
                    await callRequest.UpdateStatus(CallRequestStatus.AcceptFailed);
            }
            else
            {
                if (callRequest != null)
                {
                    await callRequest.UpdateStatus(CallRequestStatus.Accepted, po: fe.PurchaseOrderNumber);

                    qi.CallRequestId = callRequest.CallRequestId;
                    DigitalDispatchService.LogAction(qi);

                    callRequest.DispatchEntryId = fe.Id;
                    await callRequest.Save();

                    if (r != null && r.Message.MessageId != "MOCK")
                        await r.CompleteAsync();    

                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);

                    logger.Info(MasterAccountTypes.OonHonk, "CallCreated",
                        "Created new towbook call for OON Honk",
                        companyId: qi.CompanyId,
                        data: new
                        {
                            waypoints = fe.Waypoints.Select(o => new { o.Address, o.Latitude, o.Longitude })
                        },
                        dispatchId: accept.Id.ToString(),
                        callId: fe.Id,
                        callRequestId: callRequest.CallRequestId,
                        queueItemId: qi.QueueItemId,
                        poNumber: fe.PurchaseOrderNumber,
                        callNumber: fe.CallNumber);

                }
            }

            return true;
        }
        public static async Task<bool> HandleOonIncomingOffer(DigitalDispatchActionQueueItem qi, ProcessMessageEventArgs r)
        {
            var hm = JsonConvert.DeserializeObject<HonkMessage>(qi.JsonObject);



            if (qi.Type == DigitalDispatchActionQueueItemType.IncomingCallPaymentOutOfNetwork)
            {

                var rpayment = JsonConvert.DeserializeObject<Extric.Towbook.Integration.MotorClubs.Honk.OonPaymentModel>(hm.JsonData);
                var payment = rpayment.Payment;

                
                var hj = HonkJob.GetByDispatchId(hm.JobId, hm.ProviderId);
                var crt = await CallRequest.GetByIdAsync(hj.CallRequestId);

                if (crt.DispatchEntryId.GetValueOrDefault() != 0)
                {
                    var entry = await Entry.GetByIdNoCacheAsync(crt.DispatchEntryId.Value);

                    entry.SetAttribute(Dispatch.AttributeValue.BUILTIN_BILLING_NOTES,
                        $"CCN: {payment.CardNumber}, Exp: {payment.CardExpiration}, CCV: {payment.CardCvc}, " +
                        $"Billing Zip: {payment.BillingZip}, " +
                        $"Amount: {payment.Amount.ToString("C")} USD");

                    await entry.Save(
                        token: new AuthenticationToken() { UserId = HonkUserId, ClientVersionId = MotorClubDispatchingService.MyClientVersionId },
                        ipAddress: "127.0.0.1");

                    logger.Info(MasterAccountTypes.OonHonk,
                        "HandlePayment",
                        "Assigned payment successfully",
                        dispatchId: hj.JobId.ToString(),
                        companyId: entry.CompanyId,
                        data: new
                        {
                            accountId = entry.AccountId,
                        },
                        callId: entry.Id,
                        callRequestId: crt.CallRequestId,
                        queueItemId: qi.QueueItemId);

                    await r.CompleteAsync();
                }


            }

            var jo = JsonConvert.DeserializeObject<HonkRestClient.JobModelContainer>(hm.JsonData)?.Job;

            var client = HonkRestClient.Prod();

            var devIds = new int[] { 10000, 7124, 209810, 209811, 209812 };

            if ((r.Message.ApplicationProperties.ContainsKey("environment") && (r.Message.ApplicationProperties["environment"].ToString() == "TEST" ||
                r.Message.ApplicationProperties["environment"].ToString() == "DEV")) ||
                devIds.Contains(qi.CompanyId.GetValueOrDefault()) ||
                (jo?.Notes != null && jo.Notes.Any(o => o?.Text != null && o.Text.Contains("Towbook Staging PO"))))
                client = HonkRestClient.Dev();

            var offerPrice = (jo.estimated_payout_cents / 100);

            if (qi.Type == DigitalDispatchActionQueueItemType.IncomingCallAccepted)
            {
                await CreateOonJob(qi, r, jo);
                return true;
            }

            if (hm.TypeId != 1)
            {
                DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);

                if (r?.Message?.MessageId != "MOCK")
                    await r.CompleteAsync();

                logger.Info(MasterAccountTypes.OonHonk,
                        "CallReceived",
                        "Payload ignored, Type != 1",
                        "OON", hm.ToJson(), jo.Id.ToString(),
                        qi.CompanyId,
                        queueItemId: qi.QueueItemId);

                return false;
            }

            if (jo.destination_location_lat != null &&
                jo.destination_location_lat != jo.vehicle_location_lat &&
                offerPrice > 0)
            {
                var loadedMileage = await OutOfNetworkIncoming.GetDistanceFromGoogleAsync(
                    jo.vehicle_location_lat.Value,
                    jo.vehicle_location_lng.Value,
                    jo.destination_location_lat.Value,
                    jo.destination_location_lng.Value, true);

                if (loadedMileage > 0 &&
                    offerPrice > 0 &&
                    offerPrice < loadedMileage)
                {
                    client.Respond(jo.Id, new HonkRestClient.JobOfferResponseModel()
                    {
                        Value = "reject"
                    });

                    logger.Info(MasterAccountTypes.OonHonk,
                        "CallReceived",
                        "Incoming Job Offer Ignored due to Loaded Distance being < $1/mile",
                        "OON", null, jo.Id.ToString(),
                        qi.CompanyId,
                        "{loadedMiles:" + loadedMileage + "}",
                        queueItemId: qi.QueueItemId);

                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);

                    if (r != null)
                        await r.CompleteAsync();

                    return true;
                }
            }
            // handle OON

            // find providers
            var oon = (client.Environment == "PROD" ? await OutOfNetworkIncoming.FindNonHonkProvidersByLatLong(
                jo.vehicle_location_lat.Value,
                jo.vehicle_location_lng.Value,
                jo.service_type) : await OutOfNetworkIncoming.FindNonHonkProvidersByLatLongDev(
                    jo.vehicle_location_lat.Value,
                    jo.vehicle_location_lng.Value,
                    jo.service_type));

            if (!oon.Any())
            {
                client.Respond(jo.Id, new
                     HonkRestClient.JobOfferResponseModel()
                { Value = "reject" });

                logger.Info(MasterAccountTypes.OonHonk,
                    "CallReceived",
                    "Rejected - No available providers nearby",
                    "OON", null, jo.Id.ToString(), null,
                    new
                    {
                        latitude = jo.vehicle_location_lat,
                        longitude = jo.vehicle_location_lng
                    },
                   queueItemId: qi.QueueItemId);
                // auto reject, no providers found.
            }
            else
            {
                var requests = new List<CallRequest>();

                foreach (var x in oon)
                {
                    try
                    {
                        var account = await OutOfNetworkIncoming.OonCreateAccount(x.CompanyId, x.AccountId, MasterAccountTypes.OonHonk,
                            qi.QueueItemId);

                        if (account != null && account.Status != AccountStatus.Inactive)
                        {
                            if (x.AccountId == 0)
                                x.AccountId = account.Id;

                            var ema = EmailAddress.GetByCompanyId(x.CompanyId).FirstOrDefault()?.Address;
                            if (ema == null)
                            {
                                var ea = (await Company.Company.GetByIdAsync(x.CompanyId)).DefaultEmailAddress();
                                ea.Save();

                                ema = ea.Address;
                            }

                            var upx = HonkProvider.GetByProviderId(ema);

                            if (upx == null)
                            {
                                upx = new HonkProvider()
                                {
                                    CompanyId = x.CompanyId,
                                    AccountId = x.AccountId,
                                    MasterAccountId = MasterAccountTypes.OonHonk,
                                    ProviderId = ema,
                                };
                                await upx.Save();
                            }

                            x.AccountId = account.Id;
                            var ooncr = await OonHonkCreateCallRequest(account, jo, hm);

                            if (ooncr != null)
                                requests.Add(ooncr);
                        }
                    }
                    catch (Exception ry)
                    {
                        logger.Info(MasterAccountTypes.OonHonk,
                            "CallReceived",
                            "Incoming Job Offers Creation Failure",
                            jo.provider_id, null, jo.Id.ToString(), x.CompanyId,
                           new
                           {
                               exception = ry
                           },
                           queueItemId: qi.QueueItemId);
                    }
                }

                logger.Info(MasterAccountTypes.OonHonk,
                    "CallReceived",
                    "Incoming Job Offers Created",
                    jo.provider_id, null, jo.Id.ToString(), null,
                    new
                    {
                        callRequests = requests.Select(o => new { o.CallRequestId, o.CompanyId }).ToArray(),
                        latitude = jo.vehicle_location_lat,
                        longitude = jo.vehicle_location_lng,
                        providers = oon,
                    },
                    poNumber: jo.Id.ToString(),
                    queueItemId: qi.QueueItemId);

                Console.WriteLine(oon.ToJson(true));

                if (r != null)
                    await r.CompleteAsync();
                return true;
            }

            if (r != null)
                await r.CompleteAsync();
            return true;
        }

        public static async Task<CallRequest> OonHonkCreateCallRequest(Account a, HonkRestClient.JobModel jo, HonkMessage honkMessage)
        {
            var ulPid = EmailAddress.GetByCompanyId(a.CompanyId).FirstOrDefault()?.Address;

            if (ulPid == null)
                return null;


            if (a.Status == AccountStatus.Inactive)
                return null;

            string startingLocation = jo.vehicle_location_address;
            string towDestination = jo.destination_location_address ?? "";
            string vehicle = jo.VehicleYear + " " + jo.VehicleMake + " " + jo.VehicleModel + " " + jo.VehicleColor;

            var ulRequest = new CallRequest()
            {
                AccountId = a.Id,
                CompanyId = a.CompanyId,
                ExpirationDate = DateTime.Now.AddSeconds(900),
                PurchaseOrderNumber = jo.Id.ToString(),
                Reason = jo.service_type,
                RequestDate = DateTime.Now,
                ServiceNeeded = jo.service_type,
                StartingLocation = startingLocation.Truncate(512),
                TowDestination = towDestination.Truncate(512),
                Vehicle = vehicle.Truncate(500),
                MaxEta = 90,
                ProviderId = ulPid
            };

            var deliver = false;
            var rejectInstantly = AccountKeyValue.GetFirstValueOrNull(ulRequest.CompanyId, ulRequest.AccountId,
                Provider.Towbook.ProviderId, "DigitalAutoAcceptInstantly") == "999";

            if (rejectInstantly)
            {
                await ulRequest.Save();
            }
            else
            {
                await ulRequest.Save();
                deliver = true;
            }

            var ulDispatch = new HonkJob()
            {
                CallRequestId = ulRequest.CallRequestId,
                OfferJson = JsonExtensions.ToJson(honkMessage),
                ProviderId = ulPid,
                JobId = jo.Id
            };

            ulDispatch.Save();

            if (ulRequest.ExpirationDate != null)
                await MotorClubDispatchingService.SendExpiredRequestEvent(ulRequest.CallRequestId, ulRequest.ExpirationDate.Value);

            if (deliver)
                await ulRequest.Deliver();

            return ulRequest;
        }
    }
}
