using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Configuration;
using System.Data;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Xml;
using Azure.Messaging.ServiceBus;
using Extric.Towbook.Accounts;
using Extric.Towbook.Dispatch;
using Extric.Towbook.Generated;
using Extric.Towbook.Integration;
using Extric.Towbook.Integration.MotorClubs;
using Extric.Towbook.Integration.MotorClubs.Dispatch;
using Extric.Towbook.Integration.MotorClubs.Queue;
using Extric.Towbook.Integration.MotorClubs.Services;
using Extric.Towbook.Integration.MotorClubs.Services.Models;
using Extric.Towbook.Integrations.MotorClubs.Allstate;
using Extric.Towbook.Storage;
using Extric.Towbook.Utility;
using Microsoft.Extensions.Hosting;
using Newtonsoft.Json;
using NLog;
using static Extric.Towbook.Vehicle.VehicleUtility;
using Allstate = Extric.Towbook.Integrations.MotorClubs.Allstate.Proxy;
using Async = System.Threading.Tasks;
using Extric.Towbook.Integrations.MotorClubs.Allstate.Proxy;
using Extric.Towbook.Integrations.MotorClubs.Allstate.Rest;
using NewRelic.Api.Agent;
using System.ServiceModel;

namespace Extric.Towbook.Services.MotorClubDispatchingService
{
    public partial class MotorClubDispatchingService : IHostedService
    {
        public static bool disableAllstate = false;

        [Transaction]
        public static async Task<bool> HandleAllstateIncomingMessage(
            DigitalDispatchActionQueueItem qi, 
            AllstateMessage jsonObj,
            ProcessMessageEventArgs sourceMessage)
        {
            if (disableAllstate)
                return false;

            if (qi?.CompanyId == 155249 &&
                qi.Type != DigitalDispatchActionQueueItemType.IncomingCallReceived)
            {
                return await GatewayForwardInboundQuest(qi, sourceMessage);
            }

            switch (qi.Type)
            {
                case DigitalDispatchActionQueueItemType.IncomingLogin:
                    logger.Log(LogLevel.Fatal, "Allstate/{0}: IncomingLogin... Unhandled.. Type = {1}, {2}",
                        qi.QueueItemId, qi.Type, jsonObj.ToJson());
                    break;

                case DigitalDispatchActionQueueItemType.IncomingLogout:
                    // Allstate no longer logs companies out automatically; no action needed here.
                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    break;

                case DigitalDispatchActionQueueItemType.IncomingCallReceived:
                    DDMessage dspMsg;

                    dspMsg = DDMessage.FromXml(jsonObj.Message, typeof(DSPMessageBody));

                    var rcr = await CreateCallRequest(dspMsg, MotorClubName.Allstate, true, jsonObj.MasterAccountId);

                    qi.CallRequestId = rcr.CallRequestId;
                    DigitalDispatchService.LogAction(qi);

                    logger.Log(LogLevel.Info, "Allstate/{0}: New call request created. Type = {1}, CallRequestId = {2}", qi.QueueItemId, qi.Type, rcr.CallRequestId);

                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    break;

                case DigitalDispatchActionQueueItemType.IncomingCallRejected:
                {
                    var asd = AllstateDispatch.GetByResponseId(jsonObj.ContractorId, jsonObj.DispatchRequestNumber);

                    if (asd != null)
                    {
                        var cr = CallRequest.GetById(asd.CallRequestId);

                        Allstate.DDMessage rspMsg = DDMessage.FromXml(jsonObj.Message, typeof(RSPMessage));

                        var rsp = (rspMsg.DDContent as RSPMessage);

                        if (rsp?.MotorClubResponseCode == "1")
                        {
                            if (rsp.Remarks != null && rsp.Remarks.Contains("longer in need of service"))
                                await cr.UpdateStatus(CallRequestStatus.ServiceNoLongerNeeded);
                            else
                                await cr.UpdateStatus(CallRequestStatus.RejectedByMotorClub);

                            qi.CallRequestId = cr.CallRequestId;

                            DigitalDispatchService.LogAction(qi);

                                logger.LogEvent("DDXML/{0}/{1}/RejectedByMotorClub/{2}: {0} rejected call. ",
                                    qi.CompanyId, LogLevel.Info, "Quest", sourceMessage.Message.MessageId, rspMsg.DDMessageHeader.ResponseID);

                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                        }
                    }
                }
                break;

                case DigitalDispatchActionQueueItemType.IncomingCallAccepted:
                {
                    logger.Log(LogLevel.Info, "Allstate/{0}: Call Accepted.. XML = {1} ",
                        qi.QueueItemId,
                        jsonObj.Message);

                    DDMessage rspMsg;
                    try
                    {
                        rspMsg = DDMessage.FromXml(jsonObj.Message, typeof(RSPMessage));
                    }
                    catch (Exception e)
                    {
                        logger.Log(LogLevel.Info, "Allstate/{0}: Error while executing FromXml.. Exception = {2}, XML = {1} ",
                            qi.QueueItemId,
                            jsonObj.Message,
                            e.ToJson());

                        await sourceMessage.CompleteAsync();
                        return false;
                    }

                    CallRequest cr = null;
                    AllstateDispatch asd = AllstateDispatch.GetByResponseId(jsonObj.ContractorId, jsonObj.DispatchRequestNumber);

                        if (asd == null)
                        {
                            logger.LogEvent("{0}: Couldn't find a AllstateDispatch with ID of {2} - it was probably accepted via the phone.  Body = {1}",
                                qi.CompanyId, LogLevel.Warn, sourceMessage.Message.MessageId, qi.ToJson(), jsonObj.DispatchRequestNumber);
                        }
                        else
                        {
                            cr = await CallRequest.GetByIdAsync(asd.CallRequestId);
                        }

                    if (rspMsg != null)
                    {
                        var asDispatchDetails =
                            (Allstate.DSPMessageBody)Allstate.DDMessage.FromXml(
                                asd.CallXml, typeof(Allstate.DSPMessageBody)).DDContent;

                        var raPONumber = ((Allstate.RSPMessage)rspMsg.DDContent).AuthorizationNumber;

                        asd.PurchaseOrderNumber = raPONumber;
                        asd.Save();

                        if (((Allstate.RSPMessage)rspMsg.DDContent).MotorClubResponseCode == "1")
                        {
                            await cr.UpdateStatus(CallRequestStatus.RejectedByMotorClub);

                                logger.LogEvent("{0}: Allstate rejected call. ",
                                    qi.CompanyId, LogLevel.Error, sourceMessage.Message.MessageId, qi.ToJson());

                            break;
                        }

                        int callAccountId = qi.AccountId.Value;

                        if (asDispatchDetails.AccountInfo.ProgramName != null)
                        {
                            var acc2 = (await Account.GetByCompanyAsync(await Company.Company.GetByIdAsync(qi.CompanyId.Value), AccountType.MotorClub))
                                .Where(o => o.Company.ToLowerInvariant().Contains("allstate") &&
                                    o.Company.ToLowerInvariant().Contains(asDispatchDetails.AccountInfo.ProgramName.ToLowerInvariant()) &&
                                    o.Company.ToLowerInvariant().Contains(rspMsg.DDMessageHeader.ContractorID.ToLowerInvariant()))
                                .FirstOrDefault();

                            if (acc2 != null)
                                callAccountId = acc2.Id;
                        }

                        var fe = await DistributedLock.ForAsync("Allstate", raPONumber, 15000,
                            lockAcquired: async delegate ()
                            {
                                Entry e = null;
                                e = await Entry.GetByPurchaseOrderNumberAsync(callAccountId, raPONumber);

                                if (e == null)
                                {
                                    foreach (var a in await Account.GetByCompanyAsync(await Company.Company.GetByIdAsync(qi.CompanyId.Value), AccountType.MotorClub))
                                    {
                                        e = await Entry.GetByPurchaseOrderNumberAsync(a.Id, raPONumber);
                                        if (e != null)
                                            break;
                                    }
                                }

                                    if (e == null)
                                    {
                                        e = new Entry();
                                        e.CompanyId = qi.CompanyId.Value;
                                    }
                                    else
                                    {
                                        logger.LogEvent("{0}: Found existing towbook call for PO {1}/ResponseID {2}... Call #{3}... we're going to update this one.",
                                            qi.CompanyId, LogLevel.Warn, sourceMessage.Message.MessageId, asd.PurchaseOrderNumber, rspMsg.DDMessageHeader.ResponseID, e.CallNumber);
                                    }

                                e.AccountId = callAccountId;
                                e.PurchaseOrderNumber = raPONumber;

                                if (e.Account?.DefaultPriority == 1)
                                    e.Priority = Entry.EntryPriority.High;

                                #region #CustomerWorkaround to send Service Calls for Quality Towing (4817) to a separate companyId 
                                if (e.CompanyId == 4817)
                                {
                                    if (String.IsNullOrWhiteSpace(cr.TowDestination))
                                        e.CompanyId = 5406;
                                }
                                #endregion

                                #region Handle MoveRoadsideCallsToAccountId to move roadside calls to their own AccountId 

                                if (string.IsNullOrWhiteSpace(cr.TowDestination))
                                {
                                    var akv = AccountKeyValue.GetByAccount(e.CompanyId,
                                        e.AccountId,
                                        Provider.Towbook.ProviderId,
                                        "MoveRoadsideCallsToAccountId"
                                        ).FirstOrDefault();

                                    if (akv != null)
                                    {
                                        int tempAccId = 0;
                                        if (int.TryParse(akv.Value, out tempAccId))
                                        {
                                            var tempAcc = await Account.GetByIdAsync(tempAccId);

                                            if (tempAcc != null)
                                            {
                                                e.AccountId = tempAcc.Id;
                                            }
                                        }
                                    }
                                }
                                #endregion


                                EntryAsset asset = null;
                                if (e.Assets != null)
                                    asset = e.Assets.FirstOrDefault();

                                var bt = (await Vehicle.BodyType.GetByCompanyIdAsync(e.CompanyId))
                                    .FirstOrDefault(o =>
                                        o?.Name?.ToLowerInvariant() == asDispatchDetails.VehicleInfo.VehicleType?.ToLowerInvariant());

                                if (asset == null)
                                    asset = new EntryAsset() { BodyTypeId = bt?.Id ?? 1 };

                                if (asset.BodyTypeId == 0)
                                    asset.BodyTypeId = bt?.Id ?? 1;

                                var vc = GetColorIdByName(asDispatchDetails.VehicleInfo.Color);

                                if (!string.IsNullOrEmpty(asDispatchDetails.VehicleInfo.Year))
                                    asset.Year = Convert.ToInt32(asDispatchDetails.VehicleInfo.Year);

                                asset.Make = GetManufacturerByName(asDispatchDetails.VehicleInfo.Make);
                                asset.Model = GetModelByName(asDispatchDetails.VehicleInfo.Model);

                                if (vc != 0)
                                    asset.ColorId = vc;
                                asset.Vin = asDispatchDetails.VehicleInfo.VIN;
                                asset.LicenseNumber = asDispatchDetails.VehicleInfo.Lic;
                                asset.LicenseState = asDispatchDetails.VehicleInfo.State;

                                if (string.IsNullOrWhiteSpace(asset.Vin) && asset.DriveType == null)
                                    asset.DriveType = asDispatchDetails.JobInfo.ServiceDetails;

                                var pickup = e.Waypoints.FirstOrDefault(o => o.Title == "Pickup");
                                var dest = e.Waypoints.FirstOrDefault(o => o.Title == "Destination");

                                e.Notes = "";

                                if (e.Account?.MasterAccountId == MasterAccountTypes.OonAllstate)
                                {
                                    if (e.Company.Country != Company.Company.CompanyCountry.Canada)
                                        e.Notes = "For Assistance, please call: (800) 582-6626 and press option 1.\n\n";
                                    else
                                        e.Notes = "For Assistance, please call: (877) 273-3037 and press option 1.\n\n";
                                }

                                if (!string.IsNullOrWhiteSpace(asDispatchDetails.ExchangeDestinationAddress?.Addr1) ||
                                   !string.IsNullOrWhiteSpace(asDispatchDetails.ExchangePickupAddress?.Addr1) ||
                                   !string.IsNullOrWhiteSpace(asDispatchDetails.TowStorageInfo?.Addr1))
                                {
                                    if (!string.IsNullOrWhiteSpace(asDispatchDetails.ExchangePickupAddress?.Addr1))
                                    {
                                        var epa = e.Waypoints.FirstOrDefault(o => o.Title == "Exchange Pickup");
                                        if (epa == null)
                                        {
                                            epa = new EntryWaypoint() { Position = 1, Title = "Exchange Pickup" };
                                            e.Waypoints.Add(epa);
                                        }
                                        epa = await asDispatchDetails.ExchangePickupAddress.ToWaypoint(epa);
                                    }

                                    if (!string.IsNullOrWhiteSpace(asDispatchDetails.ExchangeDestinationAddress?.Addr1))
                                    {
                                        var eda = e.Waypoints.Where(o => o.Title == "Exchange Destination").FirstOrDefault();
                                        if (eda == null)
                                        {
                                            eda = new EntryWaypoint() { Position = 2, Title = "Exchange Destination" };
                                            e.Waypoints.Add(eda);
                                        }
                                        eda = await asDispatchDetails.ExchangeDestinationAddress.ToWaypoint(eda);
                                    }

                                    if (!string.IsNullOrWhiteSpace(asDispatchDetails.TowStorageInfo?.Addr1))
                                    {
                                        var tst = e.Waypoints.Where(o => o.Title == "Storage Location").FirstOrDefault();
                                        if (tst == null)
                                        {
                                            tst = new EntryWaypoint() { Position = 2, Title = "Storage Location" };
                                            e.Waypoints.Add(tst);
                                        }
                                        tst = await asDispatchDetails.TowStorageInfo.ToWaypoint(tst);
                                    }

                                    if (!string.IsNullOrWhiteSpace(asDispatchDetails.IncAddr.Addr1) &&
                                        asDispatchDetails.IncAddr.Addr1 != asDispatchDetails.ExchangePickupAddress?.Addr1)
                                    {
                                        e.TowSource = asDispatchDetails.IncAddr.ToString();
                                        if (pickup == null)
                                        {
                                            pickup = new EntryWaypoint() { Title = "Pickup", Position = asDispatchDetails.IncAddr.OrderNoAsInt };
                                            e.Waypoints.Add(pickup);
                                        }

                                        pickup = await asDispatchDetails.IncAddr.ToWaypoint(pickup);
                                    }

                                    if (!string.IsNullOrWhiteSpace(asDispatchDetails.DestAddr.Addr1))
                                    {
                                        e.TowDestination = asDispatchDetails.DestAddr.ToString();
                                        if (dest == null && !string.IsNullOrWhiteSpace(e.TowDestination))
                                        {
                                            dest = new EntryWaypoint() { Title = "Destination", Position = e.Waypoints.Count() + 1 };
                                            e.Waypoints.Add(dest);
                                        }
                                        dest = await asDispatchDetails.DestAddr.ToWaypoint(dest);
                                    }

                                    if (!string.IsNullOrWhiteSpace(asDispatchDetails.PassengerInfo?.Addr1))
                                    {
                                        var tst = e.Waypoints.Where(o => o.Title == "Passenger Dropoff").FirstOrDefault();
                                        if (string.IsNullOrWhiteSpace(asDispatchDetails.PassengerInfo.OrderNo))
                                            asDispatchDetails.PassengerInfo.OrderNo = "2";

                                        if (tst == null)
                                        {
                                            tst = new EntryWaypoint() { Position = asDispatchDetails.PassengerInfo.OrderNoAsInt, Title = "Passenger Dropoff" };
                                            e.Waypoints.Add(tst);
                                        }
                                        tst = await asDispatchDetails.PassengerInfo.ToWaypoint(tst);

                                        if (!string.IsNullOrWhiteSpace(asDispatchDetails.PassengerInfo.RideCount))
                                            e.Notes += "\nNumber of Passengers: " + asDispatchDetails.PassengerInfo.RideCount;
                                    }

                                    e.Waypoints = e.Waypoints.OrderBy(o => o.Position).ToCollection();

                                    string addressesHuman = "";

                                    foreach (var wp in e.Waypoints)
                                    {
                                        if (!string.IsNullOrWhiteSpace(wp.Address))
                                            addressesHuman += wp.Title + ": " + wp.Address + "\n";
                                    }

                                    e.Notes = addressesHuman + "\n\n------\n" + e.Notes;

                                    if (!string.IsNullOrWhiteSpace(asDispatchDetails.ExchangeVehicleInfo?.Year))
                                    {
                                        e.Notes += "\nExchange Vehicle: " + asDispatchDetails.ExchangeVehicleInfo.ToString();

                                        if (string.IsNullOrWhiteSpace(asset.Make))
                                        {
                                            if (int.TryParse(asDispatchDetails.ExchangeVehicleInfo.Year, out int tyear))
                                                asset.Year = tyear;

                                            asset.Make = GetManufacturerByName(asDispatchDetails.ExchangeVehicleInfo.Make);
                                            asset.Model = GetModelByName(asDispatchDetails.ExchangeVehicleInfo.Model);
                                            asset.ColorId = GetColorIdByName(asDispatchDetails.ExchangeVehicleInfo.Color);

                                            if (!string.IsNullOrWhiteSpace(asDispatchDetails.ExchangeVehicleInfo.AdditionalInfo))
                                                e.Notes += "\nAdditional Info:" + asDispatchDetails.ExchangeVehicleInfo.AdditionalInfo;
                                        }
                                    }

                                    if (asDispatchDetails.JobInfo.PrimaryTask == "Rental Exchange Tow")
                                    {
                                        e.Notes = "See rental counter to determine which vehicle to pick up.\n" + e.Notes;
                                    }

                                }
                                else
                                {
                                    if (asDispatchDetails.IncAddr != null)
                                    {
                                        e.TowSource = asDispatchDetails.IncAddr.ToString();
                                        if (pickup == null)
                                        {
                                            pickup = new EntryWaypoint() { Title = "Pickup", Position = asDispatchDetails.IncAddr.OrderNoAsInt };
                                            e.Waypoints.Add(pickup);
                                        }

                                        pickup = await asDispatchDetails.IncAddr.ToWaypoint(pickup);
                                    }

                                    if (asDispatchDetails.DestAddr != null)
                                    {
                                        e.TowDestination = asDispatchDetails.DestAddr.ToString();
                                        if (dest == null && !string.IsNullOrWhiteSpace(e.TowDestination))
                                        {
                                            dest = new EntryWaypoint() { Title = "Destination", Position = asDispatchDetails.DestAddr.OrderNoAsInt };
                                            e.Waypoints.Add(dest);
                                        }
                                        dest = await asDispatchDetails.DestAddr.ToWaypoint(dest);
                                    }


                                    if (!string.IsNullOrWhiteSpace(asDispatchDetails.PassengerInfo?.Addr1))
                                    {
                                        var tst = e.Waypoints.FirstOrDefault(o => o.Title == "Passenger Dropoff");
                                        if (tst == null)
                                        {
                                            tst = new EntryWaypoint() { Position = asDispatchDetails.PassengerInfo.OrderNoAsInt, Title = "Passenger Dropoff" };
                                            e.Waypoints.Add(tst);
                                        }
                                        tst = await asDispatchDetails.PassengerInfo.ToWaypoint(tst);

                                        if (!string.IsNullOrWhiteSpace(asDispatchDetails.PassengerInfo.RideCount))
                                            e.Notes += "\nNumber of Passengers: " + asDispatchDetails.PassengerInfo.RideCount;
                                    }

                                    e.Waypoints = e.Waypoints.OrderBy(o => o.Position).ToCollection();


                                    string addressesHuman = "";

                                    if (e.Waypoints.Count > 2)
                                        foreach (var wp in e.Waypoints)
                                        {
                                            if (!string.IsNullOrWhiteSpace(wp.Address))
                                                addressesHuman += wp.Title + ": " + wp.Address + "\n";
                                        }

                                    e.Notes = addressesHuman + "------\n" + e.Notes;
                                }

                                    if (asDispatchDetails.JobInfo != null && !string.IsNullOrEmpty(asDispatchDetails.JobInfo.TimeStamp))
                                    {
                                        logger.LogEvent("{0}/{1}: Attempting to convert date of {2}",
                                            qi.CompanyId, LogLevel.Info, sourceMessage.Message.MessageId, rspMsg.DDMessageHeader.ResponseID, asDispatchDetails.JobInfo.TimeStamp);
                                        if (e.CreateDate == DateTime.MinValue)
                                            e.CreateDate = Allstate.DSPMessageJobInfo.ConvertAllstateTimestampToDateTime(asDispatchDetails.JobInfo.TimeStamp);

                                        if (asd.Eta != null)
                                            e.ArrivalETA = Allstate.DSPMessageJobInfo.ConvertAllstateTimestampToDateTime(asDispatchDetails.JobInfo.TimeStamp).AddMinutes(asd.Eta.Value);
                                        else
                                        {
                                            logger.LogEvent("{0}/{1}: ETA is missing!",
                                            qi.CompanyId, LogLevel.Error, sourceMessage.Message.MessageId, rspMsg.DDMessageHeader.ResponseID, asDispatchDetails.JobInfo.TimeStamp);
                                        }
                                    }

                                if (!string.IsNullOrWhiteSpace(asDispatchDetails?.JobInfo?.EquipmentType))
                                    e.Notes += "\nEquipment Type: " + asDispatchDetails.JobInfo.EquipmentType;

                                if (!string.IsNullOrWhiteSpace(asDispatchDetails?.JobInfo?.ServiceDetails))
                                    e.Notes += "\nService Details: " + asDispatchDetails.JobInfo.ServiceDetails;

                                if (!string.IsNullOrWhiteSpace(asDispatchDetails?.IncAddr?.BusinessName))
                                    e.Notes += $"\nPickup Address Business Name: {asDispatchDetails.IncAddr.BusinessName}";

                                if (!string.IsNullOrWhiteSpace(asDispatchDetails?.IncAddr?.BusinessContactName))
                                    e.Notes += $"\nPickup Address Business Contact Name: {asDispatchDetails.IncAddr.BusinessContactName}";

                                if (!string.IsNullOrWhiteSpace(asDispatchDetails?.IncAddr?.BusinessPhone))
                                    e.Notes += $"\nPickup Address Business Phone: {Core.FormatPhone(asDispatchDetails.IncAddr.BusinessPhone)}";

                                if (!string.IsNullOrWhiteSpace(asDispatchDetails?.DestAddr?.BusinessName))
                                    e.Notes += $"\nDestination Business Name: {asDispatchDetails.DestAddr.BusinessName}";

                                if (!string.IsNullOrWhiteSpace(asDispatchDetails?.DestAddr?.BusinessContactName))
                                    e.Notes += $"\nDestination Business Contact Name: {asDispatchDetails.DestAddr.BusinessContactName}";

                                if (!string.IsNullOrWhiteSpace(asDispatchDetails?.DestAddr?.BusinessPhone))
                                    e.Notes += $"\nDestination Business Phone: {Core.FormatPhone(asDispatchDetails.DestAddr.BusinessPhone)}";

                                if (!string.IsNullOrWhiteSpace(asDispatchDetails?.JobInfo.ReleaseCashAmount))
                                    e.Notes += $"\nRelease Cash Amount: {asDispatchDetails.JobInfo.ReleaseCashAmount}";

                                if (!string.IsNullOrWhiteSpace(asDispatchDetails?.JobInfo.ReleaseFee))
                                    e.Notes += $"\nRelease Fee: {asDispatchDetails.JobInfo.ReleaseFee}";

                                if (!string.IsNullOrWhiteSpace(asDispatchDetails?.JobInfo.LotPaidThruDate))
                                    e.Notes += $"\nStorage Lot Paid Through: {asDispatchDetails.JobInfo.LotPaidThruDate}";

                                if (!string.IsNullOrWhiteSpace(asDispatchDetails?.JobInfo.IsKeysAvailable))
                                    e.Notes += "\nKeys Available: " +
                                        (asDispatchDetails.JobInfo.IsKeysAvailable == "Y" ? "Yes" : "No");

                                if (!string.IsNullOrWhiteSpace(asDispatchDetails?.JobInfo.KeyLocation))
                                    e.Notes += "\nKey Location: " + asDispatchDetails?.JobInfo.KeyLocation;

                                if (!string.IsNullOrWhiteSpace(asDispatchDetails?.JobInfo.VehclKeyLocType))
                                    e.Notes += "\nKey Location Type: " + asDispatchDetails?.JobInfo.VehclKeyLocType;

                                if (!string.IsNullOrWhiteSpace(asDispatchDetails?.JobInfo.NeutralCapable))
                                    e.Notes += "\nNeutral Capable: " + asDispatchDetails?.JobInfo.NeutralCapable;

                                if (!string.IsNullOrWhiteSpace(asDispatchDetails?.VehicleInfo.GrossWeightNumber))
                                    e.Notes += "\nGross Vehicle Weight: " + asDispatchDetails?.VehicleInfo.GrossWeightNumber;

                                if (!string.IsNullOrWhiteSpace(asDispatchDetails?.VehicleInfo.VehicleLengthNbr))
                                    e.Notes += "\nVehicle Length: " + asDispatchDetails?.VehicleInfo.VehicleLengthNbr + "ft";

                                if (!string.IsNullOrWhiteSpace(asDispatchDetails?.VehicleInfo.VehicleHeightNbr))
                                    e.Notes += "\nVehicle Height: " + asDispatchDetails?.VehicleInfo.VehicleHeightNbr + "ft";

                                if (!string.IsNullOrWhiteSpace(asDispatchDetails?.VehicleInfo.PayloadDesc))
                                    e.Notes += "\nPayload Description: " + asDispatchDetails?.VehicleInfo.PayloadDesc;

                                if (!string.IsNullOrWhiteSpace(asDispatchDetails?.VehicleInfo.RecreatnlVehclType))
                                    e.Notes += "\nGeneric Vehicle Type: " + asDispatchDetails?.VehicleInfo.RecreatnlVehclType;

                                if (asDispatchDetails?.VehicleInfo.CarryngLoadInd == "Y")
                                    e.Notes += "\nIs Carrying Load: Yes";

                                if (!string.IsNullOrWhiteSpace(asDispatchDetails?.VehicleInfo.UpfitType))
                                    e.Notes += "\nUpfit Indicator: " + asDispatchDetails?.VehicleInfo.UpfitInd;

                                if (!string.IsNullOrWhiteSpace(asDispatchDetails?.VehicleInfo.UpfitType))
                                    e.Notes += "\nUpfit Type: " + asDispatchDetails?.VehicleInfo.UpfitType;

                                if (asDispatchDetails?.JobInfo.PullingTrailrInd == "Y")
                                    e.Notes += "\nPulling Trailer: Yes";

                                if (asDispatchDetails?.JobInfo.AllWheelsOpertnlInd != null)
                                    e.Notes += "\nAll Wheels Operational: " + (asDispatchDetails?.JobInfo.AllWheelsOpertnlInd == "Y" ? "Yes" : "No");

                                if (!string.IsNullOrWhiteSpace(asDispatchDetails?.JobInfo.DistncOffPavedSurfaceFT))
                                    e.Notes += "\nDistance Off Paved Surface: " + asDispatchDetails?.JobInfo.DistncOffPavedSurfaceFT + "ft";

                                if (!string.IsNullOrWhiteSpace(asDispatchDetails?.JobInfo.TiresFlat))
                                    e.Notes += "\nTires Flat: " + asDispatchDetails?.JobInfo.TiresFlat;

                                if (!string.IsNullOrWhiteSpace(asDispatchDetails?.JobInfo.IsGaraged))
                                    e.Notes += "\nIs Garaged: " + asDispatchDetails?.JobInfo.IsGaraged;

                                if (!string.IsNullOrWhiteSpace(asDispatchDetails?.VehicleInfo.VehicleNoseInInd))
                                    e.Notes += "\nIs Nose In: " + asDispatchDetails?.VehicleInfo.VehicleNoseInInd;

                                if (!string.IsNullOrWhiteSpace(asDispatchDetails?.JobInfo.ParkingGarageFlrNbr))
                                    e.Notes += "\nParking Garage Floor Number: " + asDispatchDetails?.JobInfo.ParkingGarageFlrNbr;

                                if (!string.IsNullOrWhiteSpace(asDispatchDetails?.JobInfo.ParkingGarageClearncIn))
                                    e.Notes += "\nParking Garage Clearance: " + asDispatchDetails?.JobInfo.ParkingGarageClearncIn + " inches";

                                if (asDispatchDetails.CallerInfo.RemainInVehicleInd == "Y")
                                    e.Notes += "\nCustomer with Vehicle: Yes";
                                else if (asDispatchDetails.CallerInfo.RemainInVehicleInd == "N")
                                    e.Notes += "\nCustomer with Vehicle: No";

                                AttachDriveTypeToCallNotesFromVin(e, asset);

                                #region DefaultTaxRate
                                if (!e.Attributes.ContainsKey(Dispatch.AttributeValue.BUILTIN_TAXRATE_OVERRIDE))
                                {
                                    var defaultTaxRateId = CompanyKeyValue.GetByCompanyId(e.CompanyId, Provider.Towbook.ProviderId, "DefaultTaxRateId").FirstOrDefault();

                                    if (defaultTaxRateId != null)
                                    {
                                        e.SetAttribute(Dispatch.AttributeValue.BUILTIN_TAXRATE_OVERRIDE, defaultTaxRateId.Value);
                                    }
                                }
                                #endregion

                                var rsp = ((Allstate.RSPMessage)rspMsg.DDContent);

                                #region Coverage Amount / Benefit Limits

                                string coverage = "";

                                coverage = asDispatchDetails.AccountInfo?.BenefitLimit;

                                var distanceUnits = "miles";
                                var fuelUnits = "gallons";

                                if (qi.CompanyId != null)
                                {
                                    var cmp = await Company.Company.GetByIdAsync(qi.CompanyId.Value);
                                    if (cmp != null)
                                    {
                                        distanceUnits = cmp.LocaleMile + "s";
                                        fuelUnits = cmp.LocaleVolume + "s";
                                    }
                                }

                                if (!string.IsNullOrWhiteSpace(rsp.BenefitDollarLimit))
                                {
                                    coverage = "$" + rsp.BenefitDollarLimit;
                                }

                                if (!String.IsNullOrWhiteSpace(rsp.BenefitMileLimit) &&
                                    rsp.BenefitMileLimit != "0")
                                {
                                    coverage = rsp.BenefitMileLimit + " " + distanceUnits;
                                }

                                if (!String.IsNullOrWhiteSpace(rsp.FuelBenefit) &&
                                    rsp.FuelBenefit != "0")
                                {
                                    coverage = (!string.IsNullOrWhiteSpace(coverage) ? coverage + ", " : "") +
                                        rsp.FuelBenefit + " " + fuelUnits;
                                }

                                if (!e.Attributes.ContainsKey(Dispatch.AttributeValue.BUILTIN_MOTORCLUB_COVERAGELIMIT))
                                    e.Attributes.Add(Dispatch.AttributeValue.BUILTIN_MOTORCLUB_COVERAGELIMIT,
                                        new Dispatch.AttributeValue(e, Dispatch.AttributeValue.BUILTIN_MOTORCLUB_COVERAGELIMIT)
                                        {
                                            Value = coverage
                                        });
                                else
                                    e.Attributes[Dispatch.AttributeValue.BUILTIN_MOTORCLUB_COVERAGELIMIT].Value = coverage;


                                #endregion

                                if (!string.IsNullOrWhiteSpace(rsp.VehicleReferenceUrl))
                                    e.SetAttribute(Dispatch.AttributeValue.BUILTIN_REFERENCE_URL, rsp.VehicleReferenceUrl);

                                if (!string.IsNullOrWhiteSpace(rsp.ChatURL))
                                    e.SetAttribute(Dispatch.AttributeValue.BUILTIN_EXTERNAL_CHAT_URL, rsp.ChatURL);

                                if(e.Account.MasterAccountId == MasterAccountTypes.OonAllstate)
                                    e.SetAttribute(Dispatch.AttributeValue.BUILTIN_EXTERNAL_TERMS_OF_SERVICE_URL, "https://app.towbook.com/security/oon-allstate-towbook.htm");

                                #region notes

                                if (asDispatchDetails.JobInfo.Boost != null &&
                                    asDispatchDetails.JobInfo.Boost.Amount > 0)
                                {
                                    var billingNotes = e.BillingNotes();
                                    var billingNoteToAdd = "Additional Boost Amount: " + asDispatchDetails.JobInfo.Boost.Amount.ToString("C") + "\n";

                                    if (!billingNotes.Contains(billingNoteToAdd))
                                        billingNotes = billingNoteToAdd + "\n" + billingNotes;

                                    e.SetAttribute(Dispatch.AttributeValue.BUILTIN_BILLING_NOTES, billingNotes);
                                }

                                if (!string.IsNullOrWhiteSpace(rsp.Overages) && rsp.Overages != "0")
                                {
                                    if (decimal.TryParse(rsp.Overages, out decimal result) && (e.Account.MasterAccountId == MasterAccountTypes.OonAllstate))
                                    {
                                        // TODO: add this as an actual InvoiceItem line so the customer doesn't have to manually do it.
                                        e.Notes = "Overage to collect from customer: " + result.ToString("C") + "\n" + e.Notes;

                                        var overage = e.InvoiceItems.FirstOrDefault(o => o.Name == "Customer Overage");

                                        if (overage == null)
                                        {
                                            e.InvoiceItems.Add(new InvoiceItem()
                                            {
                                                CustomName = "Customer Overage",
                                                Quantity = 1,
                                                CustomPrice = result
                                            });
                                        }
                                    }
                                    else
                                        e.Notes = "*** Overage to collect from Customer: $" + rsp.Overages + "***\n" + e.Notes;
                                }

                                if (e.Account.MasterAccountId == MasterAccountTypes.OonAllstate &&
                                    asDispatchDetails.JobInfo?.OfferAmount > 0)
                                {
                                    var amt = asDispatchDetails.JobInfo.OfferAmount;

                                    if (asDispatchDetails.JobInfo.CustomerPayAmt != 0)
                                        amt = asDispatchDetails.JobInfo.CoveredAmt;

                                    var oa = e.InvoiceItems.FirstOrDefault(o => o.Name == OON_LINE_ITEM_NAME);
                                    if (oa == null)
                                    {
                                        e.InvoiceItems.Add(new InvoiceItem()
                                        {
                                            CustomName = OON_LINE_ITEM_NAME,
                                            Quantity = 1,
                                            CustomPrice = amt
                                        });
                                    }

                                    const string oonNotes = @"After the call is set to Completed and Allstate confirms the service was performed, you will receive a one-time credit card number in the Billing Notes of this call.
If a GOA results, a one-time credit card number will appear upon Allstate validating that the customer is not at the scene.

Follow these tips to ensure prompt payment:
- Only assign jobs to drivers who use the Towbook mobile app
- Drivers must share their GPS
- Assigned Drivers must update statuses themselves using the Towbook mobile app
- Job must be marked Complete or have a GOA requested to receive automatic payment

Not following these steps may result in a delayed or non-payment.";

                                    e.Notes = oonNotes + "\n" + e.Notes;



                                    if (asDispatchDetails.JobInfo.CustomerPayAmt == 0)
                                    {
                                        e.SetAttribute(Dispatch.AttributeValue.BUILTIN_BILLING_NOTES,
                                            "Out of Network Offer Amount: " + asDispatchDetails.JobInfo.OfferAmount.ToString("C") + ", " +
                                            "GOA Rate: " + asDispatchDetails.JobInfo.GoaAmount.ToString("C"));
                                    }
                                    else
                                    {

                                        e.SetAttribute(Dispatch.AttributeValue.BUILTIN_BILLING_NOTES,
                                            "Out of Network Offer Amount: " + asDispatchDetails.JobInfo.OfferAmount.ToString("C") + " (" +
                                            "Allstate Pays: " + asDispatchDetails.JobInfo.CoveredAmt.ToString("C") + ", Customer Pays: " +
                                            asDispatchDetails.JobInfo.CustomerPayAmt.ToString("C") + ") " +
                                            "GOA Rate: " + asDispatchDetails.JobInfo.GoaAmount.ToString("C"));
                                    }
                                }


                                if (asDispatchDetails.AccountInfo?.ProgramName != null)
                                {
                                    var names = new string[] {
                                        "Allstate 2ndary Tow",
                                        "Esurance-2ndary",
                                        "Encompass ST",
                                        "Gerber ST",
                                        "Sixt Secondary",
                                        "AVISSECONDARY",
                                        "AVAILST",
                                        "AVISST",
                                        "M56 Merchants Fleet",
                                        "NATGEN ST",
                                        "BUDGETRENTACAR",
                                        "AVISCARRENTAL",
                                        "FLEXCAR",
                                        "PAYLESSCARRENTAL",
                                        "ZIPCAR"
                                    };

                                    if (names.Contains(asDispatchDetails.AccountInfo.ProgramName))
                                    {
                                        e.Notes = "Please capture at least one photo at both the pickup before loading, and at the destination " +
                                        "after unloading the vehicle.\n" +
                                        "For impound tows, please take a photo of the impound receipt from the impound yard.\n" +
                                        "If you cannot complete the service, please use the Digital Cancel or " +
                                        "Digital GOA option instead of completing it.\n" + e.Notes;
                                    }
                                }

                                if (!string.IsNullOrWhiteSpace(rsp.OverMileageRate))
                                    e.Notes += "\nOver Mileage Rate: " + rsp.OverMileageRate;

                                if (!string.IsNullOrWhiteSpace(rsp.CoPayAmount))
                                    e.Notes += "\nCo-Pay Amount: " + rsp.CoPayAmount;

                                if (!string.IsNullOrWhiteSpace(asDispatchDetails.AccountInfo.ProgramName))
                                    e.Notes += "\nProgram Name: " + asDispatchDetails.AccountInfo.ProgramName;

                                if (!string.IsNullOrWhiteSpace(rsp.Remarks))
                                    e.Notes += "\n" + rsp.Remarks + "\n";

                                if (!string.IsNullOrWhiteSpace(asDispatchDetails?.JobInfo?.NotesToProvider))
                                    e.Notes += "\nNotes to Provider: " + asDispatchDetails.JobInfo.NotesToProvider;

                                if (!string.IsNullOrWhiteSpace(asDispatchDetails?.VehicleInfo?.FuelType))
                                    e.Notes += "\nFuel Type: " + asDispatchDetails.VehicleInfo.FuelType;

                                if (asDispatchDetails?.VehicleInfo.EngnFuelType !=
                                    asDispatchDetails?.VehicleInfo.FuelType &&
                                    !string.IsNullOrWhiteSpace(asDispatchDetails?.VehicleInfo.EngnFuelType))
                                    e.Notes += "\nEngine Fuel Type: " + asDispatchDetails?.VehicleInfo.EngnFuelType;

                                #endregion

                                #region Reason
                                var sr = cr.ServiceNeeded;

                                if (asDispatchDetails?.VehicleInfo?.VehicleType != null &&
                                    sr != null &&
                                    sr.StartsWith(asDispatchDetails.VehicleInfo.VehicleType) &&
                                    sr.Length > asDispatchDetails.VehicleInfo.VehicleType.Length)
                                    sr = sr.Substring(asDispatchDetails.VehicleInfo.VehicleType.Length + 1);

                                e.ReasonId = await ReasonHelper.DetermineReasonId(e.Account.MasterAccountId,
                                    qi.CompanyId.Value,
                                    sr);

                                if (e.ReasonId == 1635)
                                    e.Notes += "\nService Needed: " + cr.ServiceNeeded;

                                
                                if (!string.IsNullOrEmpty(sr))
                                {
                                    if (sr.ToLowerInvariant().StartsWith("tow with passenger") ||
                                        sr.ToLowerInvariant().StartsWith("rental exchange") ||
                                        (sr.ToLowerInvariant().StartsWith("rental two")))
                                    {
                                        if (!e.Attributes.ContainsKey(Dispatch.AttributeValue.BUILTIN_ALLSTATE_JOB_INFO_PRIMARY_TASK))
                                            e.Attributes.Add(Dispatch.AttributeValue.BUILTIN_ALLSTATE_JOB_INFO_PRIMARY_TASK,
                                                new Dispatch.AttributeValue(e, Dispatch.AttributeValue.BUILTIN_ALLSTATE_JOB_INFO_PRIMARY_TASK)
                                                {
                                                    Value = sr
                                                });
                                        else
                                            e.Attributes[Dispatch.AttributeValue.BUILTIN_ALLSTATE_JOB_INFO_PRIMARY_TASK].Value =
                                                cr.ServiceNeeded;

                                        e.Notes = "Type: " + asDispatchDetails.JobInfo.PrimaryTask + "\n" + e.Notes;
                                    }
                                }

                                #endregion

                                if (cr != null)
                                {
                                    if (cr.OwnerUserId == null)
                                        cr.OwnerUserId = 1;

                                    e.CallRequestId = cr.CallRequestId;

                                    if (e.OwnerUserId < 100)
                                        e.OwnerUserId = cr.OwnerUserId.GetValueOrDefault(0);
                                }
                                else if (e.OwnerUserId < 100)
                                    e.OwnerUserId = 1;

                                await ApplyRoundRobinDispatcherLogicAsync(qi, e, jsonObj?.DispatchRequestNumber, jsonObj?.ContractorId);

                                if (e.Assets == null || e.Assets.Count == 0)
                                {
                                    e.Assets = new Collection<EntryAsset>();
                                    e.Assets.Add(asset);
                                }

                                var customerName = asDispatchDetails.CallerInfo?.FirstName ?? "";

                                //asDispatchDetails.AccountInfo.CustFirstName + " " + asDispatchDetails.AccountInfo.CustLastName;

                                if (string.IsNullOrWhiteSpace(customerName))
                                    customerName = asDispatchDetails.AccountInfo.MemFirstName + " " + asDispatchDetails.AccountInfo.MemLastName;

                                var phone = Core.FormatPhoneWithDashesOnly(asDispatchDetails.AccountInfo.CallBackPhone);

                                if (string.IsNullOrWhiteSpace(phone))
                                    phone = Core.FormatPhoneWithDashesOnly(((Allstate.RSPMessage)rspMsg.DDContent).MemberCallBackNum);

                                EntryContact c = e.Contacts.FirstOrDefault(o =>
                                    o.Name == customerName ||
                                    Core.FormatPhoneWithDashesOnly(o.Phone) == phone);

                                bool newContact = false;
                                if (c == null)
                                {
                                    c = new EntryContact() { Name = customerName };
                                    e.Contacts.Add(c);
                                    newContact = true;
                                }

                                c.Phone = phone;

                                var memberName = Core.FormatName(asDispatchDetails.AccountInfo.CustFirstName + " " + asDispatchDetails.AccountInfo.CustLastName);
                                if (!e.Contacts.Any(o => o.Name == memberName))
                                {
                                    e.Contacts.Add(new EntryContact() { Name = memberName, Phone = phone });
                                }

                                await e.Save(token: new AuthenticationToken() { UserId = 4 }, ipAddress: "127.0.0.1");

                                if (newContact)
                                    await CheckForRoadsideFeatureAndAutoInvite(e, c);

                                await AutoDispatch.AutoDispatchServiceBusHandler.Send(e);
                                try
                                {
                                    if (!string.IsNullOrWhiteSpace(((Allstate.RSPMessage)rspMsg.DDContent).DocumentsIdentifier))
                                    {
                                        var id = ((Allstate.RSPMessage)rspMsg.DDContent).DocumentsIdentifier;

                                            if (id.Contains(","))
                                                foreach (var fileGuid in id.Split(','))
                                                {
                                                    await AllstateConvertContentIdToFile(rspMsg.DDMessageHeader.ContractorID,
                                                        rspMsg.DDMessageHeader.ResponseID,
                                                        e.CompanyId, e.Id, fileGuid, qi.QueueItemId);
                                                }
                                            else
                                                await AllstateConvertContentIdToFile(rspMsg.DDMessageHeader.ContractorID,
                                                     rspMsg.DDMessageHeader.ResponseID,
                                                     e.CompanyId, e.Id, id, qi.QueueItemId);
                                        }
                                    }
                                    catch
                                    {

                                }

                                    return e;
                                },
                                alreadyLocked: async delegate ()
                                {
                                    logger.LogEvent("{0}/CR{1}: Lock already exists for {2}:{3}... pausing 250ms",
                                        qi.CompanyId, LogLevel.Warn,
                                        sourceMessage.Message.MessageId,
                                        (cr != null ? cr.CallRequestId.ToString() : "NULL"),
                                        callAccountId,
                                        raPONumber);

                                    await ServiceBusHelper.Client().CreateReceiver(DigitalDispatchService.IncomingQueueName).RenewMessageLockAsync(sourceMessage.Message);
                                    await Task.Delay(500);

                                return true;
                            });

                        if (fe == null)
                        {
                            await Task.Delay(10000);

                            var entr = await Entry.GetByPurchaseOrderNumberAsync(callAccountId, asd.PurchaseOrderNumber);
                            if (entr != null)
                            {
                                if (cr.DispatchEntryId.GetValueOrDefault() == 0)
                                {
                                    cr.DispatchEntryId = entr.Id;
                                    await cr.Save();

                                    qi.CallRequestId = cr.CallRequestId;
                                    DigitalDispatchService.LogAction(qi);

                                    await cr.UpdateStatus(
                                        CallRequestStatus.Accepted,
                                        po: fe.PurchaseOrderNumber);

                                    fe = entr;

                                        logger.Info(asd.MasterAccountId,
                                            "CallAccepted",
                                            "Creation of call failed for Allstate; associated successfully",
                                            contractorId: asd.ContractorId,
                                            dispatchId: asd.ResponseId,
                                            companyId: qi.CompanyId,
                                            data: new
                                            {
                                                json = qi.ToJson(),
                                                accountId = callAccountId
                                            },
                                            callId: fe.Id,
                                            queueItemId: qi.QueueItemId,
                                            poNumber: asd.PurchaseOrderNumber,
                                            callNumber: fe.CallNumber);
                                    }
                                }
                                else
                                {
                                    logger.Error(asd.MasterAccountId,
                                        "CallAccepted",
                                        "Creation of call FAILED",
                                        contractorId: asd.ContractorId,
                                        dispatchId: asd.ResponseId,
                                        companyId: qi.CompanyId,
                                        data: new
                                        {
                                            json = qi.ToJson(),
                                            accountId = callAccountId
                                        },
                                        queueItemId: qi.QueueItemId,
                                        poNumber: asd.PurchaseOrderNumber);
                                }
                                await sourceMessage.CompleteAsync();
                            }
                            else
                            {
                                if (cr != null)
                                {
                                    cr.DispatchEntryId = fe.Id;
                                    await cr.Save();

                                qi.CallRequestId = cr.CallRequestId;
                                DigitalDispatchService.LogAction(qi);

                                await cr.UpdateStatus(CallRequestStatus.Accepted, po: fe.PurchaseOrderNumber);
                            }
                        }
                        await fe.AssignRequestDriverToEntry();
                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);

                        logger.LogEvent("Created new towbook call for Allstate with call number {0}, " +
                            "dispatchEntryId {1} for callRequestId {2}",
                            qi?.CompanyId,
                            LogLevel.Info,
                            fe?.CallNumber,
                            fe?.Id,
                            (cr != null ? cr.CallRequestId.ToString() : "NULL"));

                        logger.Info(MasterAccountTypes.Allstate, "CallCreated",
                            "Created new call for Allstate",
                            jsonObj.ContractorId, null, jsonObj.DispatchRequestNumber, qi.CompanyId,
                            data: new
                            {
                                waypoints = fe.Waypoints.Select(o => new { o.Address, o.Latitude, o.Longitude })
                            },
                            callId: fe.Id,
                            callRequestId: cr.CallRequestId,
                            queueItemId: qi.QueueItemId,
                            poNumber: fe.PurchaseOrderNumber,
                            callNumber: fe.CallNumber);
                    }
                    else
                    {
                        logger.Error(MasterAccountTypes.Allstate, "CallAccepted", "Creation of call failed; RSP couldn't be parsed",
                            jsonObj.ContractorId, null, jsonObj.DispatchRequestNumber, qi.CompanyId, qi, queueItemId: qi.QueueItemId);

                        await sourceMessage.CompleteAsync();
                        return false;
                    }

                    break;
                }

                case DigitalDispatchActionQueueItemType.IncomingCallCancelled:
                    DDMessage cnlMsg = DDMessage.FromXml(jsonObj.Message, typeof(CNLMessage));

                    bool timedOut = false;

                    var msgText = ((CNLMessage)cnlMsg.DDContent).Response;
                    if (msgText.Contains("Timeout while waiting for RET"))
                    {
                        timedOut = true;
                    }
                    else
                        msgText = ((CNLMessage)cnlMsg.DDContent).AdditionalInfo;

                        CallRequest cancelRequest = null;
                        var asdCancel = AllstateDispatch.GetByResponseId(jsonObj.ContractorId, jsonObj.DispatchRequestNumber);
                        if (asdCancel == null)
                        {
                            logger.LogEvent("{0}: Couldn't find a AllstateDispatch with ID of {2}.  Body = {1}",
                                qi.CompanyId, LogLevel.Error, sourceMessage.Message.MessageId, qi.ToJson(), jsonObj.DispatchRequestNumber);
                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                            return false;
                        }
                        else
                        {
                            cancelRequest = CallRequest.GetById(asdCancel.CallRequestId);

                        if (timedOut)
                            await cancelRequest.UpdateStatus(CallRequestStatus.Expired);
                        else
                            await cancelRequest.UpdateStatus(CallRequestStatus.Cancelled);

                        qi.CallRequestId = cancelRequest.CallRequestId;
                        DigitalDispatchService.LogAction(qi);
                        if (cancelRequest.DispatchEntryId != null)
                        {
                            var e = Entry.GetByIdNoCache(cancelRequest.DispatchEntryId.Value);
                            if (e != null && ShouldAllowCancel(e))
                            {
                                string reason = "";

                                if (msgText != null)
                                    reason = msgText;

                                int userId = 1;
                                var name = "Motor Club";

                                var goaApproved = false;


                                const string contains = "This dispatch is not eligible for a GOA. If you feel this is in error, you may contest a GOA on the Payment Services web site after 24 hours.";
                                const string containsGoa = "The GOA Request for this dispatch has been approved. You may submit for payment on the Payment Services web site after 24 hours.";

                                if (e.Account.MasterAccountId == MasterAccountTypes.Allstate ||
                                    e.Account.MasterAccountId == MasterAccountTypes.OonAllstate)
                                {
                                    if (reason.Contains(contains))
                                    {
                                        goaApproved = false;
                                    }
                                    else if (reason.Contains(containsGoa))
                                    {
                                        goaApproved = true;
                                    }

                                    userId = 4; // external_allstate user account
                                    name = "Allstate";
                                }

                                e.SetAttribute(Dispatch.AttributeValue.BUILTIN_BILLING_NOTES,
                                    msgText + "\n" + e.BillingNotes());

                                await e.Save(false, new AuthenticationToken() { UserId = userId }, "127.0.0.1");

                                if (!goaApproved)
                                    await e.Cancel($"{name} Cancelled: {reason}",
                                        new AuthenticationToken() { UserId = userId }, "127.0.0.1");


                                logger.LogEvent("IncomingCancelEvent/Allstate/{3}/{4}: Cancelled call {0}/{1} with reason {2}",
                                    qi.CompanyId,
                                    LogLevel.Info, e.CallNumber, e.Id, reason, asdCancel.ContractorId, cancelRequest.CallRequestId);

                                if (goaApproved)
                                {
                                    var ec = await EntryCancellation.GetByDispatchEntryIdAsync(e.Id);
                                    if (ec != null)
                                    {
                                        ec.Deleted = true;
                                        await ec.Save(new AuthenticationToken() { UserId = userId }, "127.0.0.1");
                                    }
                                    e.Status = Status.Completed;
                                    e.Notes = containsGoa + "\n" + e.Notes;
                                    var goaReasonCode = (await Reason.GetByCompany(e.Company)).Where(o => o.Name.ToUpperInvariant().Contains("GOA") ||
                                        o.Name.ToLowerInvariant().Contains("gone") &&
                                        o.Name.ToLowerInvariant().Contains("on") &&
                                        o.Name.ToLowerInvariant().Contains("arrival")).FirstOrDefault();
                                    if (goaReasonCode != null)
                                        e.ReasonId = goaReasonCode.Id;
                                    e.CompletionTime = DateTime.Now;
                                    await e.Save(false, new AuthenticationToken() { UserId = userId }, "127.0.0.1");

                                    logger.LogEvent("IncomingCancelEvent/Allstate/{3}/{4}: Changed cancel to completion for call {0}/{1} with reason {2} because GOA was approved for it.",
                                        qi.CompanyId,
                                        LogLevel.Info, e.CallNumber, e.Id, reason, asdCancel.ContractorId, cancelRequest.CallRequestId);
                                }

                                Core.SetRedisValue(e.Id + ":ss_sf", "1", TimeSpan.FromDays(3));
                                Core.SetRedisValue(e.Id + ":ss_goa", "1", TimeSpan.FromDays(3));
                                Core.SetRedisValue(e.Id + ":ss_dc", "1", TimeSpan.FromDays(3));
                            }
                        }

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    }

                    break;

                case DigitalDispatchActionQueueItemType.IncomingCallGoaResponse:
                    var grd = AllstateDispatch.GetByResponseId(jsonObj.ContractorId, jsonObj.DispatchRequestNumber);
                    if (grd == null)
                    {
                        logger.LogEvent("IncomingCallGoaResponse/Allstate/{0}/{1}: Dispatch Request Number {2} doesn't exist.",
                                    qi.CompanyId,
                                    LogLevel.Error, qi.CompanyId, qi.AccountId, jsonObj.DispatchRequestNumber);
                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                        break;
                    }

                    qi.CallRequestId = grd.CallRequestId;
                    DigitalDispatchService.LogAction(qi);

                    var tcr = CallRequest.GetById(grd.CallRequestId);
                    var goaMsg = DDMessage.FromXml(jsonObj.Message, typeof(GOAMessage));
                    
                    var comments = ((GOAMessage)goaMsg.DDContent)?.Comments;

                    var forceCancel = false;
                    if (Core.GetRedisValue(tcr.DispatchEntryId + ":ss_dc") == "1")
                        forceCancel = true;

                    if (tcr?.DispatchEntryId != null)
                    {
                        var en = await Entry.GetByIdNoCacheAsync(tcr.DispatchEntryId  .Value);

                        var now = Core.OffsetDateTime(en.Company, DateTime.Now);

                        if (forceCancel)
                        {
                            await en.Cancel("Digital Cancel Request Confirmed by Allstate at " + now.ToShortTowbookTimeString(), new AuthenticationToken()
                            {
                                UserId = 4,
                                ClientVersionId = MyClientVersionId
                            });
                        }
                        else
                        {
                            var toAdd = "Allstate GOA Response received at " + now.ToShortDateString() + " " + now.ToShortTowbookTimeString() + ":  " + comments;
                            en.Notes = toAdd + "\n" + (en.Notes ?? "");

                            en.SetAttribute(Dispatch.AttributeValue.BUILTIN_BILLING_NOTES,
                                toAdd + "\n" + en.BillingNotes());

                            if (toAdd.Contains("is not eligible"))
                            {
                                foreach (var x in en.InvoiceItems)
                                {
                                    if (x.Name == "GOA" || 
                                        x.Name == "Out of Network GOA")
                                    {
                                        x.CustomPrice = 0;
                                        x.Quantity = 0;
                                    }
                                }
                            }

                            await en.Save(false, new AuthenticationToken()
                            {
                                UserId = 4,
                                ClientVersionId = MyClientVersionId
                            }, "127.0.0.1");
                        }
                    }

                    logger.LogEvent("IncomingCallGoaResponse/Allstate/{0}/{1}/{2}/{3}: Recorded GOA Response to call notes. {4}",
                        qi.CompanyId,
                        LogLevel.Info, qi.CompanyId, qi.AccountId, grd.ContractorId, grd.CallRequestId, comments);

                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    break;

                default:
                    logger.Log(LogLevel.Fatal, "Allstate/{0}: Unhandled Event Type: Type = {1}, {2}",
                        qi.QueueItemId, qi.Type, jsonObj.ToJson());
                    break;
            }

            await sourceMessage.CompleteAsync();
            return true;
        }

        public static async Async.Task AllstateConvertContentIdToFile(string contractorId, string responseId,
            int companyId, int callId, string contentId, long queueItemId)
        {

            var arity = ArityRestClient.Get();
            var filere = arity.GetResource(contentId);
            if (filere != null && filere.Results.Any())
            {
                foreach (var file in filere.Results)
                {
                    var cf = new CompanyFile() { CompanyId = companyId, OwnerUserId = 7 };
                    cf.Filename = cf.RawUrl = file.FileName;
                    cf.Size = file.FileLengthNumber;
                    cf.Description = "Allstate:" + contentId;
                    cf.DispatchEntries.Add(callId);
                    cf.Save();

                    await Storage.FileUtility.SendFile(cf.LocalLocation, Convert.FromBase64String(file.FileEncoded),
                        file.MimeTypeCode);

                    File.Delete(cf.LocalLocation);

                    logger.Info(MasterAccountTypes.Allstate, "IncomingFile",
                        "Saved File Attachment from Allstate",
                        contractorId,
                        null,
                        responseId,
                        companyId,
                        new
                        {
                            filename = file.FileName,
                            fileId = cf.Id
                        },
                        callId,
                        queueItemId: queueItemId);
                }
            }
        }

        public class StatusEventModel
        {
            public int CallRequestId { get; set; }
            public int NewStatusId { get; set; }
            public float Latitude { get; set; }
            public float Longitude { get; set; }
            public string Source { get; set; }
            public int DriverId { get; set; }
            public string DriverName { get; set; }
            public int TruckId { get; set; }
            public int CurrentWaypointId { get; set; }
        }

        [Transaction]
        public static async Async.Task HandleAllstateQueueOutgoingMessage(DigitalDispatchActionQueueItem qi, dynamic jsonObj, ProcessMessageEventArgs sourceMessage)
        {
            var returnProxy = new AllstateRestClient.RetMessageBody.Ret();
            AllstateRestClient arc = null;
            Allstate.DDMessage baseMsg = null;
            AllstateDispatch ad;

            if (disableAllstate)
                return;

            bool manualOverride = false;

            if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingLogin)
            {
                if (qi.AccountId.HasValue)
                {
                    var errors = new Dictionary<string, string>();
                    bool failed = false;

                    foreach (var x in AllstateContractor.GetByAccountId(qi.AccountId.Value))
                    {
                        // first of all, update IsLoggedIn=true so that automatic logins will log this client back in if 
                        // a LOF event is received
                        x.IsLoggedIn = true;
                        x.Save();

                        int userId = qi.OwnerUserId.GetValueOrDefault();


                        var resp = await x.AllstateLogin(userId);
                        if (!resp.Success)
                        {
                            failed = true;
                            errors.Add(x.ContractorId, "Failed:" + resp.Message);
                            x.UpdateLoginStatus(AllstateContractorLoginStatus.LoggedOut);

                            logger.Log(LogLevel.Error, "Allstate/" + qi.CompanyId + "/" + qi.AccountId + "/" + x.ContractorId + "/Login/Response/Failed: " + resp.Message);
                        }
                        else
                        {
                            errors.Add(x.ContractorId, "Logged in");
                            x.UpdateLoginStatus(AllstateContractorLoginStatus.LoggedIn);
                            logger.Log(LogLevel.Info, "Allstate/" + qi.CompanyId + "/" + qi.AccountId + "/" + x.ContractorId + "/Login/Response/Succeeded");
                        }
                    }

                    await PushNotificationProvider.BackgroundJobStatusUpdate(qi.CompanyId.Value,
                        qi.QueueItemId,
                        "digitaldispatch_login",
                        !failed,
                        "Allstate Login Status",
                        errors);
                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    await sourceMessage.CompleteAsync();
                    return;
                }
                else
                {
                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                    await sourceMessage.CompleteAsync(); // ("Attempted to Login but no accountId was passed.", "");
                    return;
                }
            }
            else if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingLogoff)
            {

                if (qi.AccountId.HasValue)
                {
                    var errors = new Dictionary<string, string>();
                    foreach (var x in AllstateContractor.GetByAccountId(qi.AccountId.Value))
                    {
                        // first of all, update IsLoggedIn=false so that automatic logins wont try to log
                        // this client back in.
                        x.IsLoggedIn = false;
                        x.Save();

                        int userId = qi.OwnerUserId.GetValueOrDefault();

                        if (!(await x.AllstateLogout(userId)).Success)
                        {
                            errors.Add(x.ContractorId, "Failed");
                        }
                        else
                        {
                            errors.Add(x.ContractorId, "Logged out");
                            x.UpdateLoginStatus(AllstateContractorLoginStatus.LoggedOut);
                        }
                    }

                    await PushNotificationProvider.BackgroundJobStatusUpdate(qi.CompanyId.Value,
                        qi.QueueItemId,
                        "digitaldispatch_logout",
                        true, // we don't care if logout failed - just let the user know the request finished.
                        "Allstate Logout Status",
                        errors);

                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    await sourceMessage.CompleteAsync();
                    return;
                }
                else
                {
                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                    await sourceMessage.CompleteAsync(); // ("Attempted to Logout but no accountId was passed.", "")
                    return;
                }
            }
            else if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingStatusUpdate)
            {
                CallRequest cr = CallRequest.GetById(Convert.ToInt32(jsonObj.CallRequestId));
                ad = AllstateDispatch.GetByCallRequestId(cr.CallRequestId);

                if (Core.GetAppSetting("Towbook.DispatchService.Allstate.DisableOutgoingStatusUpdates") == "1")
                {
                    await sourceMessage.CompleteAsync();
                    logger.Log(LogLevel.Warn, $"Allstate status updates are disabled - skipped sending status update for CR{cr.CallRequestId}, ${ad.ContractorId}/${ad.DispatchId}/${ad.PurchaseOrderNumber}");
                    return;
                }

                var args = new Integrations.MotorClubs.Allstate.Rest.PushGpsEvent();
                decimal? lat = (decimal?)jsonObj.Latitude;
                decimal? lng = (decimal?)jsonObj.Longitude;
                int newStatusId = (int)jsonObj.NewStatusId;

                string cacheKey = "allstate:" + cr.CallRequestId + ":" + newStatusId + ":" + 
                    (jsonObj.CurrentWaypointId ?? 0);

                string code = "";
                if (newStatusId == Status.Dispatched.Id)
                {
                    code = "DRIVER_ASSIGNED"; // allstate dispatched status
                    Core.DeleteRedisKey("allstate:" + cr.CallRequestId + ":" + Status.EnRoute.Id + ":0"); // make sure we allow en route to go through if we assigned.
                }
                else if (newStatusId == Status.EnRoute.Id)
                {
                    if (Core.GetRedisValue(cacheKey) == "1")
                    {
                        code = "ETA_UPDATE"; // send a breadcrumb instead.
                    }
                    else
                    {
                        code = "ETA_EVENT";
                    }
                }
                else if (newStatusId == Status.AtSite.Id)
                    code = "ON_SCENE";
                else if (newStatusId == Status.BeingTowed.Id)
                    code = "LOADED";
                else if (newStatusId == Status.DestinationArrival.Id)
                    code = "DROP_OFF";
                else if (newStatusId == Status.Completed.Id)
                    code = "SVC_COMPLETE";
                else if (newStatusId == Status.Cancelled.Id || newStatusId == Status.CancelledAcknowledgePending.Id)
                {
                    code = "PROVIDER_CANCEL";
                    lat = 0;
                    lng = 0;
                }


                Entry en = null;
                if (cr.DispatchEntryId != null)
                {
                    en = Entry.GetByIdNoCache(cr.DispatchEntryId.Value);

                    if (newStatusId == Status.Completed.Id &&
                        (Core.GetRedisValue(en.Id + ":ss_goa") == "1" ||
                        Core.GetRedisValue(en.Id + ":ss_sf") == "1" ||
                        Core.GetRedisValue(en.Id + ":ss_dc") == "1"))
                        code = "";

                }

                if (Account.GetById(cr.AccountId).MasterAccountId == MasterAccountTypes.OonAllstate)
                {
                    if (newStatusId == Status.Completed.Id)
                    {
                        var bn = en.BillingNotes() ?? "";

                        var ignore = false;
                        if (Core.GetRedisValue(en.Id + ":ss_goa") == "1" ||
                            Core.GetRedisValue(en.Id + ":ss_sf") == "1" ||
                            Core.GetRedisValue(en.Id + ":ss_dc") == "1")
                            ignore = true;

                        if (!ignore)
                        {
                            var toAdd = "Allstate Roadside is validating that the service was completed. This process should only take 1-2 minutes.";
                            if (!bn.Contains(toAdd))
                            {
                                bn = toAdd + "\n" + bn;

                                en.SetAttribute(Dispatch.AttributeValue.BUILTIN_BILLING_NOTES,
                                    bn);
                                await en.Save();
                            }
                        }
                    }
                }

                baseMsg = Allstate.DDMessage.FromXml(ad.CallXml, typeof(Allstate.DSPMessageBody));


                var asDispatchDetails =
                    (Allstate.DSPMessageBody)Allstate.DDMessage.FromXml(
                        ad.CallXml, typeof(Allstate.DSPMessageBody)).DDContent;

                int curWaypointId = 0;
                
                if (jsonObj.CurrentWaypointId != null)
                {
                    // always send en route event when a waypointId is set. 
                    if (newStatusId == Status.EnRoute.Id)
                        code = "ETA_EVENT";

                    curWaypointId = (int)jsonObj.CurrentWaypointId;

                    if (curWaypointId == 0 && en.Waypoints.Any(o => o.Title != "Pickup" && o.Title != "Destination"))
                    {
                        if (newStatusId == Status.EnRoute.Id ||
                            newStatusId == Status.AtSite.Id)
                            curWaypointId = en.Waypoints.FirstOrDefault(o => o.Position == 1)?.Id ?? 0;
                        else if (newStatusId == Status.BeingTowed.Id ||
                            newStatusId == Status.DestinationArrival.Id)
                            curWaypointId = en.Waypoints.FirstOrDefault(o => o.Position == 2)?.Id ?? 0;
                    }

                    var ew = EntryWaypoint.GetById(curWaypointId);
                    if (ew != null)
                    {
                        var primaryTask = asDispatchDetails.JobInfo.PrimaryTask;

                        var multiStep = en.Waypoints.Count() > 2
                            && !en.Waypoints.Any(o => o.Title == "Passenger Dropoff");

                        switch (ew.Title)
                        {
                            case "Exchange Pickup":
                                if (newStatusId == Status.AtSite.Id)
                                    code = "ON_SCENE_EXCHG";
                                break;

                            case "Exchange Destination":
                                if (newStatusId == Status.EnRoute.Id)
                                    code = "LOADED_EXCHG";
                                else if (newStatusId == Status.AtSite.Id)
                                    code = "ON_SCENE";
                                else if (newStatusId == Status.BeingTowed.Id)
                                    code = "LOADED_EXCHG";
                                else if (newStatusId == Status.DestinationArrival.Id)
                                    code = "ON_SCENE";

                                break;

                            case "Storage Location":
                                if (newStatusId == Status.AtSite.Id)
                                    code = "VEH_STORAGE";
                                break;

                            case "Passenger Dropoff":
                                if (newStatusId == Status.AtSite.Id)
                                    code = "DROP_PASSG";
                                // don't send a status update for passenger drop off en route.. 
                                if (!string.IsNullOrWhiteSpace(asDispatchDetails.PassengerInfo?.Addr1) && 
                                    newStatusId == Status.BeingTowed.Id)
                                    code = "";
                                break;

                            case "Pickup": // do nothing
                                if (multiStep && newStatusId == Status.EnRoute.Id)
                                    code = "ETA_EVENT_DISBLE";
                                else if (multiStep && newStatusId == Status.AtSite.Id)
                                    code = "ON_SCENE_RNTL_DSBLE";
                                break;

                            case "Destination":// do nothing
                                if (newStatusId == Status.DestinationArrival.Id)
                                    code = "DROP_OFF";
                                if (newStatusId == Status.AtSite.Id && (en.Waypoints.Count() > 2))
                                    code = "DROP_OFF";
                                if (newStatusId == Status.AtSite.Id && !string.IsNullOrWhiteSpace(asDispatchDetails.PassengerInfo?.Addr1))
                                    code = "DROP_OFF";
                                break;
                        }
                    }
                }

                if (jsonObj.DriverId != null)
                {
                    MotorClubDispatchingService.SaveDriversForCallRequest(cr.CallRequestId, Convert.ToInt32(jsonObj.DriverId));
                }

                Core.SetRedisValue(cacheKey, "1", TimeSpan.FromMinutes(60));

                var model = Newtonsoft.Json.JsonConvert.DeserializeObject<StatusEventModel>(qi.JsonObject);
                int callNumber = 0;
                if (cr.DispatchEntryId != null)
                {
                    if (en?.Driver != null)
                    {
                        if (en.Truck != null)
                            args.DriverEquipmentType = Enum.GetName(typeof(Truck.TruckType), en.Truck.Type);

                        if (model.DriverId > 0)
                            args.DriverName = model.DriverId + "-" + model.DriverName;

                        args.DriverMobileOS = model.Source;
                    }
                    callNumber = en.CallNumber;
                }

                args.Longitude = lng.GetValueOrDefault();
                args.Latitude = lat.GetValueOrDefault();
                args.AuthorizationNumber = ad.PurchaseOrderNumber;
                args.Eta = ad.Eta.GetValueOrDefault();
                args.EventTime = DateTime.Now;
                args.EventType = code;
                args.LastUpdatedInterval = 0;
                args.ReasonCode = "";
                args.ResponseId = "";
                args.AdditionalInfo = "";
                args.ReasonCode = "0";

                // force production
                arc = (baseMsg.DDMessageHeader.Key == "GETEST" ? AllstateRestClient.GetStaging() :
                    AllstateRestClient.GetProduction());

                if (args.Latitude == 0 && args.Longitude == 0 && code == "ETA_UPDATE")
                {
                    await sourceMessage.CompleteAsync();
                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                    return;
                }
                else if (code == "")
                {
                    await sourceMessage.CompleteAsync();

                    logger.Error(
                        MasterAccountTypes.Allstate,
                        "OutgoingStatusUpdate",
                        "EventType/code isn't set. Can't send.",
                        ad.ContractorId,
                        null,
                        ad.ResponseId,
                        cr.CompanyId,
                        new
                        {
                            code = code,
                            latitude = lat,
                            longitude = lng,
                        },
                        cr.DispatchEntryId,
                        cr.CallRequestId,
                        qi.QueueItemId);

                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                    return;
                }

                try
                {
                    string rv = await arc.Push(args);
                    await sourceMessage.CompleteAsync();

                    logger.Info(MasterAccountTypes.Allstate, "OutgoingStatusUpdate",
                        (lat.GetValueOrDefault() != 0 ? "Sent STATUS_UPDATE with GPS" : "Sent STATUS_UPDATE without GPS"),
                        contractorId: ad.ContractorId,
                        companyId: cr.CompanyId,
                        dispatchId: ad.ResponseId,
                        data: new
                        {
                            poNumber = ad.PurchaseOrderNumber,
                            statusSent = args.EventType,
                            currentWaypointId = curWaypointId,
                            response = rv,
                            manualOverride
                        },
                        callId: cr.DispatchEntryId,
                        callRequestId: cr.CallRequestId,
                        queueItemId: qi.QueueItemId,
                        callNumber: callNumber);

                    if (args.EventType != "ETA_UPDATE" && args.Latitude != 0)
                    {
                        args.EventType = "ETA_UPDATE";

                        var rv2 = await arc.Push(args);

                        logger.Info(MasterAccountTypes.Allstate, "OutgoingStatusUpdate",
                            (lat.GetValueOrDefault() != 0 ? "Sent extra breadcrumb via STATUS_UPDATE with GPS" : "Sent STATUS_UPDATE without GPS"),
                            ad.ContractorId,
                            null,
                            ad.ResponseId,
                            cr.CompanyId,
                            new
                            {
                                response = rv,
                                statusSent = args.EventType,
                                currentWaypointId = curWaypointId,
                            },
                            cr.DispatchEntryId,
                            cr.CallRequestId,
                            qi.QueueItemId,
                            ad.PurchaseOrderNumber,
                            callNumber);
                    }

                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                }
                catch (Exception e)
                {
                    if (e.Message.Trim() != "Application ID: GPS_TRACKING_1.0 Environment ID: ITEST Ticket ID:")
                    {
                        logger.Error(MasterAccountTypes.Allstate, "OutgoingStatusUpdate", "Error: " + e.Message,
                            ad.ContractorId, null, ad.ResponseId, cr.CompanyId,
                            data: new
                            {
                                exception = e,
                                request = args,
                            },
                            callId: cr.DispatchEntryId,
                            callRequestId: cr.CallRequestId,
                            queueItemId: qi.QueueItemId,
                            poNumber: ad.PurchaseOrderNumber,
                            callNumber: callNumber
                            );

                    }
                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                    await sourceMessage.CompleteAsync();
                }
                return;
            }
            else if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingExtendEta)
            {
                CallRequest cr = CallRequest.GetById(Convert.ToInt32(jsonObj.CallRequestId));
                if (cr == null)
                {
                    logger.Log(LogLevel.Error, "CR" + cr.CallRequestId + "/C" + cr.CompanyId +
                        "/OutgoingExtendEta: Couldn't find CallRequestId.");

                    await sourceMessage.DeadLetterAsync();
                    return;
                }

                ad = AllstateDispatch.GetByCallRequestId(cr.CallRequestId);

                if (ad == null)
                {
                    logger.Log(LogLevel.Error, "CR" + cr.CallRequestId + "/C" + cr.CompanyId + "/OutgoingExtendEta: Couldn't find AllstateDispatch.");

                    await sourceMessage.CompleteAsync();
                    return;
                }

                baseMsg = DDMessage.FromXml(ad.CallXml, typeof(Allstate.DSPMessageBody));

                arc = (baseMsg.DDMessageHeader.Key == "GETEST") ?
                    AllstateRestClient.GetStaging() :
                    AllstateRestClient.GetProduction();

                logger.Info(MasterAccountTypes.Allstate, "OutgoingExtendEta", "Sending ETA Extension Request",
                    ad.ContractorId, null, ad.ResponseId, cr.CompanyId, (object)jsonObj, cr.DispatchEntryId, cr.CallRequestId, qi.QueueItemId, ad.PurchaseOrderNumber,
                    environment: arc.Env);


                var etaReason = MasterAccountReason.GetById((int)jsonObj.ReasonId);

                var opro = new OutgoingPushRootObject();

                var originalEta = ad.Eta.GetValueOrDefault();
                var newEta = (int)jsonObj.Eta;
                var adjusted = (originalEta + newEta);
                var etaCalculated = cr.RequestDate.AddMinutes(adjusted);
                var allstateEta = Convert.ToInt32((etaCalculated - DateTime.Now).TotalMinutes);

                opro.Push.PushGPSEvents.PushGPSEvent.Add(new PushGpsEvent()
                {
                    EventType = PushEventType.NEW_ETA_EVENT,
                    AuthorizationNumber = ad.PurchaseOrderNumber,
                    ResponseId = ad.ResponseId,
                    Eta = allstateEta,
                    ReasonCode = etaReason.Code,
                    AdditionalInfo = (string)jsonObj.Comments,
                    ContactName = (await User.GetByIdAsync(qi.OwnerUserId.GetValueOrDefault()))?.FullName ?? "Dispatcher"
                });

                try
                {
                    await arc.PushAsync(opro);

                    logger.Info(MasterAccountTypes.Allstate, "OutgoingExtendEta", "Sent ETA Extension Request",
                        ad.ContractorId, null, ad.ResponseId, cr.CompanyId, null, cr.DispatchEntryId, cr.CallRequestId,
                        qi.QueueItemId, ad.PurchaseOrderNumber, environment: arc.Env);

                    DigitalDispatchService.LogAction(qi);
                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                }
                catch (Exception e)
                {
                    logger.Error(MasterAccountTypes.Allstate, "OutgoingExtendEta", "Failed: " + e.Message,
                        ad.ContractorId, null, ad.ResponseId, cr.CompanyId, new
                        {
                            purchaseOrderNumber = ad.PurchaseOrderNumber,
                            exception = e
                        }, cr.DispatchEntryId, cr.CallRequestId);

                    await sourceMessage.CompleteAsync();
                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                }

                await sourceMessage.CompleteAsync();

                return;
            }
            else if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingRequestGoa)
            {
                CallRequest cr = CallRequest.GetById(Convert.ToInt32(jsonObj.CallRequestId));
                if (cr == null)
                {
                    logger.Log(LogLevel.Error, "CR" + cr.CallRequestId + "/C" + cr.CompanyId + "/OutgoingRequestGoa: Couldn't find CallRequestId.");

                    await sourceMessage.CompleteAsync();
                    return;
                }

                ad = AllstateDispatch.GetByCallRequestId(cr.CallRequestId);

                if (ad == null)
                {
                    logger.Log(LogLevel.Error, "CR" + cr.CallRequestId + "/C" + cr.CompanyId + "/OutgoingRequestGoa: Couldn't find AllstateDispatch.");

                    await sourceMessage.CompleteAsync();
                    return;
                }

                var requestGoaMsg = JsonConvert.DeserializeObject<RequestGoaMessage>(qi.JsonObject);

                baseMsg = DDMessage.FromXml(ad.CallXml, typeof(DSPMessageBody));

                arc = (baseMsg.DDMessageHeader.Key == "GETEST") ?
                    AllstateRestClient.GetStaging() :
                    AllstateRestClient.GetProduction();

                var goaReason = MasterAccountReason.GetById(requestGoaMsg.ReasonId);
                var opro = new Integrations.MotorClubs.Allstate.Rest.OutgoingPushRootObject();

                var push = new Integrations.MotorClubs.Allstate.Rest.PushGpsEvent()
                {
                    EventType = Integrations.MotorClubs.Allstate.Rest.PushEventType.GOA_EVENT,
                    AuthorizationNumber = ad.PurchaseOrderNumber,
                    ResponseId = ad.ResponseId,
                    ReasonCode = goaReason.Code,
                    AdditionalInfo = requestGoaMsg.Comments?.ToString() ?? "",
                    ContactName = (await User.GetByIdAsync(qi.OwnerUserId.Value))?.FullName ?? "Dispatcher",
                    CustomerContacted = (requestGoaMsg.CustomerContacted) ? "T" : "F"
                };

                push.Latitude = requestGoaMsg.Latitude;
                push.Longitude = requestGoaMsg.Longitude;

                opro.Push.PushGPSEvents.PushGPSEvent.Add(push);

                try
                {
                    await arc.PushAsync(opro);
                    logger.Info(MasterAccountTypes.Allstate, "OutgoingRequestGoa",
                        "Sent Successfully",
                        null, null,
                        dispatchId: ad.ResponseId,
                        companyId: cr.CompanyId,
                        data: new
                        {
                            purchaseOrderNumber = ad.PurchaseOrderNumber,
                            reasonCode = goaReason.Code,
                            reasonName = goaReason.Name,
                            callRequestId = cr.CallRequestId,
                            request = opro.Push.PushGPSEvents.PushGPSEvent.FirstOrDefault(),
                            url = arc.UrlBase
                        });

                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    await sourceMessage.CompleteAsync();
                }
                catch (Exception e)
                {
                    logger.Error(MasterAccountTypes.Allstate, "OutgoingRequestGoa",
                        "Failed: " + e.Message,
                        companyId: cr.CompanyId,
                        dispatchId: ad.ResponseId,
                        data: new
                        {
                            exception = e,
                            purchaseOrderNumber = ad.PurchaseOrderNumber,
                            reasonCode = goaReason.Code,
                            reasonName = goaReason.Name,
                            callRequestId = cr.CallRequestId,
                            request = opro.Push.PushGPSEvents.PushGPSEvent.FirstOrDefault(),
                            url = arc.UrlBase
                        }, queueItemId: qi.QueueItemId);

                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                    await sourceMessage.CompleteAsync();
                }

                qi.CallRequestId = cr.CallRequestId;
                DigitalDispatchService.LogAction(qi);
                return;
            }
            else if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingCallCanceled)
            {
                var cr = CallRequest.GetById((int)jsonObj.CallRequestId);
                if (cr == null)
                {
                    logger.Log(LogLevel.Error, "CR" + cr.CallRequestId + "/C" + cr.CompanyId + "/OutgoingCallCanceled: Couldn't find CallRequestId.");

                    await sourceMessage.DeadLetterAsync();
                    return;
                }

                ad = AllstateDispatch.GetByCallRequestId((int)cr.CallRequestId);

                if (ad == null)
                {
                    logger.Log(LogLevel.Error, "CR" + cr.CallRequestId + "/C" + cr.CompanyId + "/OutgoingCallCanceled: Couldn't find AllstateDispatch.");

                    await sourceMessage.DeadLetterAsync();
                    return;
                }

                MasterAccountReason goaReason = MasterAccountReason.GetById(Convert.ToInt32(jsonObj.ReasonId));

                if (goaReason == null || goaReason.Type != MasterAccountReasonType.Cancel)
                {
                    logger.Log(LogLevel.Error, "CR" + cr.CallRequestId + "/C" + cr.CompanyId + "/OutgoingCallCanceled: Invalid ReasonId: " +
                        JsonExtensions.ToJson(goaReason, true));

                    await sourceMessage.DeadLetterAsync();
                    return;
                }

                baseMsg = DDMessage.FromXml(ad.CallXml, typeof(DSPMessageBody));

                arc = (baseMsg.DDMessageHeader.Key == "GETEST") ?
                    AllstateRestClient.GetStaging() :
                    AllstateRestClient.GetProduction();

                var opro = new Integrations.MotorClubs.Allstate.Rest.OutgoingPushRootObject();
                opro.Push.PushGPSEvents.PushGPSEvent.Add(new Integrations.MotorClubs.Allstate.Rest.PushGpsEvent()
                {
                    EventType = Integrations.MotorClubs.Allstate.Rest.PushEventType.ProviderCancel,
                    AuthorizationNumber = ad.PurchaseOrderNumber,
                    ResponseId = ad.ResponseId,
                    ReasonCode = goaReason.Code,
                    AdditionalInfo = jsonObj.Comments?.ToString() ?? "",
                    ContactName = (await User.GetByIdAsync(qi.OwnerUserId.Value))?.FullName ?? "Dispatcher",
                    CustomerContacted = ((bool)jsonObj.CustomerContacted) ? "T" : "F"
                });

                try
                {
                    logger.Info(MasterAccountTypes.Allstate, "OutgoingCancelCall", "Sending Cancel Call",
                        ad.ContractorId, null, ad.ResponseId, cr.CompanyId, opro.Push.PushGPSEvents.PushGPSEvent.First(), cr.DispatchEntryId, cr.CallRequestId,
                        qi.QueueItemId);

                    await arc.PushAsync(opro);
                    logger.Info(MasterAccountTypes.Allstate, "OutgoingCancelCall", "Sending Cancel Call Succeeded",
                        ad.ContractorId, null, ad.ResponseId, cr.CompanyId, null, cr.DispatchEntryId, cr.CallRequestId, qi.QueueItemId);

                    var en = Entry.GetByIdNoCache(cr.DispatchEntryId.Value);
                    if (en.Status != Status.Cancelled)
                    {
                        await en.Cancel("Cancelled", new AuthenticationToken() { UserId = qi.OwnerUserId.Value });
                    }

                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    await sourceMessage.CompleteAsync();
                }
                catch (Exception e)
                {
                    logger.Error(MasterAccountTypes.Allstate, "OutgoingCancelCall", "Failed: " + e.Message,
                        ad.ContractorId, null, ad.ResponseId, cr.CompanyId, new
                        {
                            purchaseOrderNumber = ad.PurchaseOrderNumber,
                            exception = e,
                            data = opro.Push.PushGPSEvents.PushGPSEvent.First()
                        }, cr.DispatchEntryId, cr.CallRequestId, qi.QueueItemId);

                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                    await sourceMessage.CompleteAsync();
                }

                qi.CallRequestId = cr.CallRequestId;
                DigitalDispatchService.LogAction(qi);
                return;
            }
            else if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingServiceFailure)
            {
                CallRequest cr = CallRequest.GetById(Convert.ToInt32(jsonObj.CallRequestId));
                if (cr == null)
                {
                    logger.Log(LogLevel.Error, "CR" + cr.CallRequestId + "/C" + cr.CompanyId + "/OutgoingServiceFailure: Couldn't find CallRequestId.");

                    await sourceMessage.CompleteAsync();
                    return;
                }

                ad = AllstateDispatch.GetByCallRequestId(cr.CallRequestId);

                if (ad == null)
                {
                    logger.Log(LogLevel.Error, "CR" + cr.CallRequestId + "/C" + cr.CompanyId + "/OutgoingServiceFailure: Couldn't find AllstateDispatch.");

                    await sourceMessage.DeadLetterAsync();
                    return;
                }

                var sfReason = MasterAccountReason.GetById(Convert.ToInt32(jsonObj.ReasonId));

                if (sfReason.Type != MasterAccountReasonType.ServiceFailure)
                {
                    logger.Log(LogLevel.Error, "CR" + cr.CallRequestId + "/C" + cr.CompanyId +
                        "/OutgoingCallCanceled: Invalid ReasonId: " +
                        JsonExtensions.ToJson(sfReason, true));

                    await sourceMessage.DeadLetterAsync();
                    return;
                }
                decimal locationLat = 0;
                decimal locationLong = 0;
                var en = await Entry.GetByIdNoCacheAsync(cr.DispatchEntryId.Value);

                if (en?.Driver != null && en.Account?.MasterAccountId == MasterAccountTypes.OonAllstate)
                {
                    // this can be done for regular allstate too. testing it with OON first.

                    var uli = UserLocationHistoryItem.GetCurrentByUserId(en.Driver.UserId, DateTime.Now.AddMinutes(-5), DateTime.Now);

                    if (uli != null)
                    {
                        locationLat = uli.Latitude;
                        locationLong = uli.Longitude;
                    }
                }

                baseMsg = DDMessage.FromXml(ad.CallXml, typeof(DSPMessageBody));

                arc = (baseMsg.DDMessageHeader.Key == "GETEST") ?
                    AllstateRestClient.GetStaging() :
                    AllstateRestClient.GetProduction();

                var opro = new Integrations.MotorClubs.Allstate.Rest.OutgoingPushRootObject();
                opro.Push.PushGPSEvents.PushGPSEvent.Add(new Integrations.MotorClubs.Allstate.Rest.PushGpsEvent()
                {
                    EventType = Integrations.MotorClubs.Allstate.Rest.PushEventType.ServiceFailure,
                    AuthorizationNumber = ad.PurchaseOrderNumber,
                    ResponseId = ad.ResponseId,
                    ReasonCode = sfReason.Code,
                    Latitude = locationLat,
                    Longitude = locationLong,
                    AdditionalInfo = jsonObj.Comments.ToString(),
                    ContactName = (await User.GetByIdAsync(qi.OwnerUserId.Value))?.FullName ?? "Dispatcher",
                    CustomerContacted = ((bool)jsonObj.CustomerContacted) ? "T" : "F"
                });

                try
                {
                    logger.Log(LogLevel.Info, "Allstate/" + ad.DispatchId + "/OutgoingServiceFailure... Json=" + opro.ToJson());
                    await arc.PushAsync(opro);
                    logger.Log(LogLevel.Info, "Allstate/" + ad.DispatchId + "/OutgoingServiceFailure... Success!");

                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    await sourceMessage.CompleteAsync();
                }
                catch (Exception e)
                {
                    logger.Log(LogLevel.Error, "Allstate/" + ad.DispatchId + "/OutgoingServiceFailure: ERROR == " + e.ToString());

                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                    await sourceMessage.CompleteAsync();
                }

                if (en.Account?.MasterAccountId == MasterAccountTypes.OonAllstate)
                {
                    var bn = en.BillingNotes() ?? "";
                    var toAdd = "Allstate Roadside is validating that the service was attempted. This process should only take 1-2 minutes.";

                    if (!bn.Contains(toAdd))
                    {
                        bn = toAdd + "\n" + bn;

                        en.SetAttribute(Dispatch.AttributeValue.BUILTIN_BILLING_NOTES, bn);
                        await en.Save();
                    }
                }

                qi.CallRequestId = cr.CallRequestId;
                DigitalDispatchService.LogAction(qi);
                return;
            }
            else if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingSharePhoto ||
                qi.Type == DigitalDispatchActionQueueItemType.OutgoingShareSignature)
            {
                var cr = (CallRequest)CallRequest.GetById(Convert.ToInt32(jsonObj.CallRequestId));
                if (cr == null)
                {
                    logger.Log(LogLevel.Error, "CR" + cr.CallRequestId + "/C" + cr.CompanyId + "/OutgoingServiceFailure: Couldn't find CallRequestId.");

                    await sourceMessage.CompleteAsync();
                    return;
                }

                ad = AllstateDispatch.GetByCallRequestId(cr.CallRequestId);

                if (ad == null)
                {
                    logger.Log(LogLevel.Error, "CR" + cr.CallRequestId + "/C" + cr.CompanyId + "/OutgoingServiceFailure: Couldn't find AllstateDispatch.");

                    await sourceMessage.CompleteAsync();
                    return;
                }

                baseMsg = DDMessage.FromXml(ad.CallXml, typeof(DSPMessageBody));

                arc = (baseMsg.DDMessageHeader.Key == "GETEST") ?
                    AllstateRestClient.GetStaging() :
                    AllstateRestClient.GetProduction();

                string filename = "";
                string fc = null;
                string path = null;
                string et = Integrations.MotorClubs.Allstate.Rest.PushEventType.SignatureUpload;
                decimal lat = 0;
                decimal lng = 0;
                if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingShareSignature)
                {
                    var sig = Signature.GetById((int)jsonObj.SignatureId);
                    if (sig == null)
                    {
                        throw new Exception("no such signature." + qi.JsonObject);
                    }

                    filename = sig.DispatchEntrySignatureId + ".jpg";
                    path = await FileUtility.GetFileAsync(sig.Location.Replace("%1", qi.CompanyId.ToString()));
                    lat = sig.Latitude;
                    lng = sig.Longitude;
                }
                else if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingSharePhoto)
                {
                    et = Integrations.MotorClubs.Allstate.Rest.PushEventType.PhotoUploadPickup;

                    var pho = Dispatch.Photo.GetById((int)jsonObj.PhotoId);
                    if (pho == null)
                    {
                        throw new Exception("no such photo." + qi.JsonObject);
                    }

                    filename = pho.Id + ".jpg";
                    path = await FileUtility.GetFileAsync(pho.Location.Replace("%1", qi.CompanyId.ToString()));
                    lat = pho.CameraLatitude ?? pho.Latitude;
                    lng = pho.CameraLongitude ?? pho.Longitude;
                }

                if (path != null && File.Exists(path))
                {
                    byte[] bytes = File.ReadAllBytes(path);
                    fc = Convert.ToBase64String(bytes);
                    bytes = null;

                    var opro = new Integrations.MotorClubs.Allstate.Rest.OutgoingPushRootObject();

                    opro.Push.PushGPSEvents.PushGPSEvent.Add(
                        new Integrations.MotorClubs.Allstate.Rest.PushGpsEvent()
                        {
                            EventType = et,
                            AuthorizationNumber = ad.PurchaseOrderNumber,
                            ResponseId = ad.ResponseId,
                            AdditionalInfo = "",
                            Eta = ad.Eta.GetValueOrDefault(),
                            ContactName = (await User.GetByIdAsync(qi.OwnerUserId.Value))?.FullName ?? "Dispatcher",
                            Latitude = lat,
                            Longitude = lng,
                            NameValuePair = new Integrations.MotorClubs.Allstate.Rest.NameValuePair[]
                        {
                            new Integrations.MotorClubs.Allstate.Rest.NameValuePair() { Name = "FileName", Value = filename },
                            new Integrations.MotorClubs.Allstate.Rest.NameValuePair() { Name = "DocumentType", Value = "Prolink_API_Upload" },
                            new Integrations.MotorClubs.Allstate.Rest.NameValuePair() { Name = "EncodedFileContent", Value = fc }
                        }
                        });

                    try
                    {
                        await arc.PushAsync(opro);
                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);

                        if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingSharePhoto)
                            logger.Info(ad.MasterAccountId, "OutgoingSharePhoto", "Sending of photo succeeded",
                                ad.ContractorId, null, ad.ResponseId, cr.CompanyId,
                                new
                                {
                                    photoId = jsonObj.PhotoId,
                                },
                                callId: cr.DispatchEntryId,
                                callRequestId: cr.CallRequestId,
                                queueItemId: qi.QueueItemId);
                        else
                            logger.Info(ad.MasterAccountId, "OutgoingShareSignature", "Sending of signature succeeded",
                                ad.ContractorId, null, ad.ResponseId, cr.CompanyId,
                                new
                                {
                                    signatureId = jsonObj.SignatureId,
                                },
                                callId: cr.DispatchEntryId,
                                callRequestId: cr.CallRequestId,
                                queueItemId: qi.QueueItemId);
                    }
                    catch (Exception e)
                    {
                        logger.Log(LogLevel.Error, "Allstate/" + ad.DispatchId + "/SharePhotoOrsignature: ERROR == " + e.ToString());

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                    }
                }
                await sourceMessage.CompleteAsync();
                qi.CallRequestId = cr.CallRequestId;
                DigitalDispatchService.LogAction(qi);
                return;
            }


            CallRequest request = CallRequest.GetById(Convert.ToInt32(jsonObj.Id));
            if (request == null)
            {
                await sourceMessage.DeadLetterAsync();
                return;
            }

            AllstateDispatch dispatch = AllstateDispatch.GetByCallRequestId(request.CallRequestId);
            if (dispatch == null)
            {
                if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingAcceptCall)
                {
                    await sourceMessage.DeadLetterAsync("Attempted to accept callRequestId " + request.CallRequestId + ", but no AllstateDispatch present in database for it",
                        request.ToJson(true));
                    await request.UpdateStatus(CallRequestStatus.AcceptFailed);
                }
                else if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingRejectCall)
                {
                    await sourceMessage.DeadLetterAsync("Attempted to reject callRequestId " + request.CallRequestId + ", but no AllstateDispatch present in database for it",
                        request.ToJson(true));
                    await request.UpdateStatus(CallRequestStatus.RejectFailed);
                }
                else if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingRequestPhoneCall)
                {
                    await sourceMessage.DeadLetterAsync("Attempted to phone call request callRequestId " + request.CallRequestId + ", but no AllstateDispatch present in database for it",
                    request.ToJson(true));
                    await request.UpdateStatus(CallRequestStatus.PhoneCallRequestFailed);
                }
                DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                return;
            }

            request.OwnerUserId = Convert.ToInt32(jsonObj.OwnerUserId);

            string contactName = "Dispatch";

            if (request.OwnerUserId != null)
            {
                var user = await User.GetByIdAsync(request.OwnerUserId.Value);
                contactName = user.FullName;
            }

            returnProxy.ContractorId = dispatch.ContractorId;
            returnProxy.ResponseId = dispatch.ResponseId;
            if (!string.IsNullOrWhiteSpace(dispatch.DispatchId))
                returnProxy.JobId = Convert.ToInt32(dispatch.DispatchId);
            returnProxy.ContactName = contactName;

            if (request.HasAlreadyRespondedTo() || dispatch.Eta.GetValueOrDefault() != 0)
            {
                Console.WriteLine("already done; " + request.CallRequestId);
                await sourceMessage.CompleteAsync();
                DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                return;
            }

            ad = AllstateDispatch.GetByCallRequestId(request.CallRequestId);
            baseMsg = DDMessage.FromXml(ad.CallXml, typeof(DSPMessageBody));

            arc = (baseMsg.DDMessageHeader.Key == "GETEST" ? AllstateRestClient.GetStaging() :
                AllstateRestClient.GetProduction());
            
            switch (qi.Type)
            {
                case DigitalDispatchActionQueueItemType.OutgoingAcceptCall:
                    dispatch.Eta = jsonObj.Eta;
                    dispatch.Save();

                    returnProxy.ServiceProviderResponse = 0;
                    returnProxy.Eta = dispatch.Eta.GetValueOrDefault(0);
                    returnProxy.RejectDescription = string.Empty;

                    string responseAccept = null;

                    try
                    {
                        logger.Info(dispatch.MasterAccountId,
                            "Accept",
                            "Accepting...",
                            contractorId: dispatch.ContractorId,
                            locationId: null,
                            dispatchId: dispatch.DispatchId,
                            companyId: request.CompanyId,
                            new { json = returnProxy.ToJson() },
                            callRequestId: request.CallRequestId,
                            queueItemId: qi.QueueItemId,
                            ownerUserId: qi.OwnerUserId);

                        responseAccept = arc.Ret(returnProxy);

                        logger.Info(dispatch.MasterAccountId,
                            "Accept",
                            "Sent accept",
                            contractorId: dispatch.ContractorId,
                            locationId: null,
                            dispatchId: dispatch.DispatchId,
                            companyId: request.CompanyId,
                            new { response = responseAccept },
                            callRequestId: request.CallRequestId,
                            queueItemId: qi.QueueItemId,
                            ownerUserId: qi.OwnerUserId);

                        await request.Save();
                        await request.UpdateStatus(CallRequestStatus.AcceptSent);

                        qi.CallRequestId = request.CallRequestId;
                        DigitalDispatchService.LogAction(qi);

                        await sourceMessage.CompleteAsync();
                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    }
                    catch (Exception exc)
                    {
                        logger.Log(LogLevel.Error, "Error accepting ALLSTATE call..." + exc.Message + "\n" + exc.ToJson() + "...ResponseXML=" + (responseAccept ?? ""));
                        await sourceMessage.DeadLetterAsync("Attempted to accept callRequestId " + request.CallRequestId + ", but  error occurred",
                            exc.ToJson(true));
                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                    }
                    break;

                case DigitalDispatchActionQueueItemType.OutgoingRejectCall:
                    MasterAccountReason rejectReason = MasterAccountReason.GetById(Convert.ToInt32(jsonObj.MasterAccountReasonId));
                    if (rejectReason == null)
                    {
                        await sourceMessage.DeadLetterAsync("Attempted to reject callRequestId " + request.CallRequestId +
                            ", but no AllstateDispatch present in database for it",
                            request.ToJson(true));
                        await request.UpdateStatus(CallRequestStatus.RejectFailed);
                        return;
                    }

                    dispatch.EtaReason = RejectDispatchReason.None; //  rejectReason.Code;
                    dispatch.Save();

                    returnProxy.ServiceProviderResponse = 1;
                    returnProxy.RejectDescription = rejectReason.Code;

                    logger.Info(dispatch.MasterAccountId,
                        "Reject",
                        "Rejecting...",
                        contractorId: dispatch.ContractorId,
                        locationId: null,
                        dispatchId: dispatch.DispatchId,
                        companyId: request.CompanyId,
                        new { json = returnProxy.ToJson() },
                        callRequestId: request.CallRequestId,
                        queueItemId: qi.QueueItemId,
                        ownerUserId: qi.OwnerUserId);

                    string responseReject = arc.Ret(returnProxy);

                    logger.Info(dispatch.MasterAccountId,
                        "Reject",
                        "Sent reject",
                        contractorId: dispatch.ContractorId,
                        locationId: null,
                        dispatchId: dispatch.DispatchId,
                        companyId: request.CompanyId,
                        new { response = responseReject },
                        callRequestId: request.CallRequestId,
                        queueItemId: qi.QueueItemId,
                        ownerUserId: qi.OwnerUserId);

                    // ToDo: Parse response to see if everything went ok (when we get an actual response :))

                    await request.UpdateStatus(CallRequestStatus.Rejected);
                    await request.Save();

                    qi.CallRequestId = request.CallRequestId;
                    DigitalDispatchService.LogAction(qi);

                    await sourceMessage.CompleteAsync();
                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);

                    break;

                case DigitalDispatchActionQueueItemType.OutgoingRequestPhoneCall:
                    returnProxy.ServiceProviderResponse = 2;
                    returnProxy.RejectDescription = string.Empty;

                    logger.Log(LogLevel.Info, "Allstate/" + dispatch.DispatchId + "/RequestPhoneCall... JSON=" + returnProxy.ToJson());

                    string responsePhone = arc.Ret(returnProxy);
                    logger.Log(LogLevel.Info, "Allstate/" + dispatch.DispatchId + "/RequestPhoneCall/Response=" + responsePhone);

                    await request.UpdateStatus(CallRequestStatus.PhoneCallRequested);
                    await request.Save();

                    qi.CallRequestId = request.CallRequestId;
                    DigitalDispatchService.LogAction(qi);

                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    await sourceMessage.CompleteAsync();

                    break;

                default:
                    await sourceMessage.DeadLetterAsync("No implementation written", qi.ToJson());
                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                    logger.LogEvent("Queue item doesn't have a Type set that we have a implementation written for... MessageId {0}, Body = {1}", qi.CompanyId, LogLevel.Error, sourceMessage.Message.MessageId, qi.ToJson());
                    return;

            }
        }
    }

    public static class AllstateExtensions
    {

        private static readonly Logger logger = LogManager.GetCurrentClassLogger();


        public class AllstateResponse
        {
            public bool Success { get; set; }
            public string Message { get; set; }

            public AllstateResponse(bool success, string message)
            {
                Success = success;
                Message = message;
            }

            public AllstateResponse(bool success)
            {
                Success = success;
            }
        }


        public sealed class FailureModel
        {
            public FailureFault Fault { get; set; }
            public sealed class FailureFault
            {
                public string Faultstring { get; set; }
                public FailureDetail Detail { get; set; }
            }
            public sealed class FailureDetail
            {
                public string Errorcode { get; set; }
            }
        }



        public static async Task<AllstateResponse> AllstateLogin(this AllstateContractor alc, int performerUserId)
        {
            var arc = (alc.LocationCode == "QA" ? AllstateRestClient.GetStaging() : AllstateRestClient.GetProduction());
            string resp = null;
            try
            {
                resp = (alc.LocationCode == "QA" ? await arc.LoginV3(alc.ContractorId) : await arc.Login(alc.ContractorId));

                if (!string.IsNullOrWhiteSpace(resp) &&
                    resp.StartsWith("{") &&
                    resp.EndsWith("}"))
                {
                    var da = JsonConvert.DeserializeObject<ErrContainer>(resp);

                    if (da?.ErrMessage?.ErrorCode != null)
                    {
                        if (da.ErrMessage.ErrorCode == "5001")
                            throw new Exception("Login failed. The ProviderId you tried to login does not exist.");
                        else if (da.ErrMessage.ErrorCode == "5002")
                            throw new Exception("Login failed. The provider is already logged into another software platform.");
                        else
                            throw new Exception("Login failed. " + da.ErrMessage.ErrorDescription);
                    }
                }

                logger.Info(alc.MasterAccountId,
                    "Login",
                    "Logged contractor in.",
                    alc.ContractorId, null, null,
                    alc.CompanyId,
                    new { response = resp },
                    ownerUserId: performerUserId);

                return new AllstateResponse(true, null);
            }
            catch (Exception y)
            {
                logger.Error(alc.MasterAccountId,
                    "Login",
                    "Login failed: " + y.Message,
                    alc.ContractorId, null, null,
                    alc.CompanyId,
                    new { response = resp, exception = y },
                    ownerUserId: performerUserId);

                return new AllstateResponse(false, y.Message);
            }
        }

        public static async Task<AllstateResponse> AllstateLogout(this AllstateContractor alc, int performerUserId)
        {
            var arc = (alc.LocationCode == "QA" ? AllstateRestClient.GetStaging() : AllstateRestClient.GetProduction());
            try
            {
                var resp = (alc.LocationCode == "QA" ? await arc.LogoutV3(alc.ContractorId) : await arc.Logout(alc.ContractorId));

                if (!string.IsNullOrWhiteSpace(resp) &&
                   resp.StartsWith("{") &&
                   resp.EndsWith("}"))
                {
                    var da = JsonConvert.DeserializeObject<FailureModel>(resp);

                    if (da?.Fault?.Detail?.Errorcode != null)
                    {
                        throw new Exception("Logout failed.");
                    }
                }

                logger.Info(alc.MasterAccountId,
                    "Logout",
                    "Logged contractor out.",
                    alc.ContractorId, null, null,
                    alc.CompanyId,
                    new { response = resp },
                    ownerUserId: performerUserId);


                return new AllstateResponse(true, resp);
            }
            catch (Exception y)
            {
                logger.Error(alc.MasterAccountId,
                    "Logout",
                    "Log out failed: " + y.Message,
                    alc.ContractorId, null, null,
                    alc.CompanyId,
                    new { exception = y },
                    ownerUserId: performerUserId);

                return new AllstateResponse(false, y.Message);
            }
        }

        public static async Task UpdateLoginStatus(this AllstateContractor ac, AllstateContractorLoginStatus newStatus, int userId)
        {
            var originalStatus = ac.LoginStatus;

            if (originalStatus == newStatus)
            {
                return;
            }

            ac.UpdateLoginStatus(newStatus);

            string masterAccountName = "Allstate";
            var acc = await Account.GetByIdAsync(ac.AccountId);
            if (acc != null)
            {
                var ma = await MasterAccount.GetByIdAsync(ac.MasterAccountId);

                if (ma != null)
                    masterAccountName = ma.Name;
            }


            if (newStatus == AllstateContractorLoginStatus.LoggedIn ||
                newStatus == AllstateContractorLoginStatus.LoggedOut)
            {
                await PushNotificationProvider.UpdateLoginStatus(ac.CompanyId, ac.AccountId,
                    newStatus == AllstateContractorLoginStatus.LoggedIn ? 3 : 1);
                /*
                if (await (await Company.Company.GetByIdAsync(ac.CompanyId)).HasFeatureAsync(Features.MotorClubIntegration_MobileNotifications)
                    && false)
                {
                    var usersToNotify = User.GetByCompanyId(ac.CompanyId).Where(o => o.Disabled == false && o.Deleted == false &&
                        (o.Type == User.TypeEnum.Manager || o.Type == User.TypeEnum.Dispatcher) &&
                        (!(o.Notes ?? "").Contains("DisableMobileNotifications")));

                    string message = $"You have been logged out of {masterAccountName}";

                    string title = $"{masterAccountName} Logged Out";

                    if (newStatus == AllstateContractorLoginStatus.LoggedIn)
                    {
                        title = $"Logged into {masterAccountName} Digital Dispatch";
                        message = $"You have been logged into {masterAccountName} Digital Dispatch and will now receive dispatches via Towbook from Allstate.";
                    }
                    else
                    {
                        if (userId == 0)
                        {
                            title = $"Your company has been automatically logged out of {masterAccountName} Digital Dispatch.";
                            message = $"Your digital dispatch connection with {masterAccountName} has been logged out.  You'll receive call requests from them via the phone instead while you're logged out.";
                        }
                        else
                        {
                            title = $"Logged out of {masterAccountName} Digital Dispatch";
                            message = $"Your digital dispatch connection with {masterAccountName} has been logged out.  You'll receive call requests from them via the phone instead while you're logged out.";
                        }
                    }
                    var tasks = new Collection<Task>();
                    foreach (var u in usersToNotify)
                    {
                        var values = new Dictionary<string, string>();

                        var keys = u.GetKeys();

                        values.Add("Message", message);

                        if (userId > 0)
                            values.Add("Performer", await User.GetByIdAsync(userId).FullName);

                        if (keys.Any(o => o.Key == "notificationhub_registration_id"))
                        {
                            tasks.Add(NotificationHubHelper.GeneralChannelInstance.SendNotificationMessage(u,
                                title,
                                values, true, "Connection Status"));
                        }
                    }
                    await Task.WhenAll(tasks);
                }
                */
            }
        }

        public class NsdResponse
        {
            public bool Success { get; set; }
            public string Message { get; set; }

            public NsdResponse(bool success, string message)
            {
                Success = success;
                Message = message;
            }

            public NsdResponse(bool success)
            {
                Success = success;
            }
        }

        public static async Task<NsdResponse> NsdLogin(this AllstateContractor alc, int performerUserId, string username, string password)
        {
            string providerId = alc.ContractorId;
            const string wsdl = "https://api.towbook.com/receivers/nsd?wsdl";

            using (var nsd = new Integrations.MotorClubs.Nsd.NsdService.digitaldispatchSoapClient(new BasicHttpBinding(BasicHttpSecurityMode.Transport),
                 new EndpointAddress("https://web1.nsddispatch.com/digitaldispatch/digitaldispatch.asmx")))
            {
                var login = new Allstate.LOGMessage(username, password, wsdl);

                var loginMsg = new Allstate.DDMessage("LOG");
                loginMsg.DDMessageHeader = new Allstate.DDMessageHeader(providerId, "LOG", "ACK", "1.0", "TOWBOOK", "1.1", "Y");
                loginMsg.DDContent = login;

                var xml = loginMsg.GetXmlElement();
                var xe = nsd.NSDDispatch(xml);

                var rspMsg = DDMessage.FromXml(xe.OuterXml);

                if (rspMsg.DDMessageHeader.TransType == "ERR")
                {
                    await alc.UpdateLoginStatus(AllstateContractorLoginStatus.LoggedOut, performerUserId);
                    var message = (rspMsg.DDContent as ERRMessage).ErrorDescription;
                    return new NsdResponse(false, message);
                }
                else if (rspMsg.DDMessageHeader.TransType == "ACK")
                {
                    await alc.UpdateLoginStatus(AllstateContractorLoginStatus.LoggedIn, performerUserId);
                    return new NsdResponse(true);
                }

                return new NsdResponse(false);
            }
        }

        public static async Task<bool> NsdLogout(this AllstateContractor alc, int performerUserId, string username, string password)
        {
            using (var nsd = new Integrations.MotorClubs.Nsd.NsdService.digitaldispatchSoapClient(new BasicHttpBinding(BasicHttpSecurityMode.Transport),
                 new EndpointAddress("https://web1.nsddispatch.com/digitaldispatch/digitaldispatch.asmx")))
            {
                var logoff = new DDMessage("LOF");
                logoff.DDMessageHeader = new DDMessageHeader(alc.ContractorId, "LOG", "ACK", "1.0", "TOWBOOK", "1.1", "Y");
                logoff.DDContent = new LOFMessage(username, password);

                var xml = logoff.GetXmlElement();
                var xe = nsd.NSDDispatch(xml);

                var rspMsg = DDMessage.FromXml(xe.OuterXml);

                if (rspMsg.DDMessageHeader.TransType == "ERR")
                {
                    Console.WriteLine((rspMsg.DDContent as ERRMessage).ErrorDescription);
                    return false;
                }
                else if (rspMsg.DDMessageHeader.TransType == "ACK")
                {
                    await alc.UpdateLoginStatus(AllstateContractorLoginStatus.LoggedOut, performerUserId);
                    return true;
                }

                return false;
            }
        }
    }
}
