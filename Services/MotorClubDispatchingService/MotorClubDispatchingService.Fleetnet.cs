using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using Azure.Messaging.ServiceBus;
using Extric.Towbook.Accounts;
using Extric.Towbook.Dispatch;
using Extric.Towbook.Integration;
using Extric.Towbook.Integration.MotorClubs;
using Extric.Towbook.Integration.MotorClubs.Queue;
using Extric.Towbook.Integration.MotorClubs.Services;
using Extric.Towbook.Integrations.MotorClubs.Fleetnet;
using Extric.Towbook.Storage;
using Extric.Towbook.Utility;
using Microsoft.Extensions.Hosting;
using NewRelic.Api.Agent;
using Newtonsoft.Json;
using NLog;
using Async = System.Threading.Tasks;
using Fleetnet = Extric.Towbook.Integrations.MotorClubs.Fleetnet;

namespace Extric.Towbook.Services.MotorClubDispatchingService
{
    public partial class MotorClubDispatchingService : IHostedService
    {
        [Transaction]
        private async Task<bool> HandleFleetnetIncomingMessage(DigitalDispatchActionQueueItem qi, Fleetnet.FleetnetMessage jsonObj, ProcessMessageEventArgs sourceMessage)
        {
            var account = await Account.GetByIdAsync(qi.AccountId.Value);
            var ma = await MasterAccount.GetByIdAsync(account.MasterAccountId);
            string masterAccountName = ma?.Name ?? "UNKNOWN";

            var client = account.CompanyId == 10000 || account.CompanyId == 246389 ?
                FleetnetRestClient.GetDev() : FleetnetRestClient.GetProduction();

            switch (qi.Type)
            {
                case DigitalDispatchActionQueueItemType.IncomingCallOffered:
                    var rcr = await CreateCallRequest(jsonObj, MotorClubName.Fleetnet, true, account.MasterAccountId);

                    qi.CallRequestId = rcr.CallRequestId;
                    DigitalDispatchService.LogAction(qi);

                    logger.Info(MasterAccountTypes.Fleetnet,
                        "IncomingCallOffered",
                        "New call request created for Fleetnet", 
                        jsonObj.ProviderId, null,
                        jsonObj.RequestId,  
                        companyId: rcr.CompanyId, 
                        callRequestId: rcr.CallRequestId,
                        queueItemId: qi.QueueItemId);

                    break;
                case DigitalDispatchActionQueueItemType.IncomingCallAccepted:

                    FleetnetMessage fltMessage = null;
                    try
                    {
                        fltMessage = JsonConvert.DeserializeObject<FleetnetMessage>(qi.JsonObject);

                        logger.Info(MasterAccountTypes.Fleetnet,
                            "CallAccepted",
                            "Incoming Job Offer Accepted", jsonObj.ProviderId, null,
                            jsonObj.RequestId, qi.CompanyId, fltMessage);
                    }
                    catch (Exception e)
                    {

                        logger.Info(MasterAccountTypes.Fleetnet,
                            "CallAccepted",
                            "Incoming Job Offer Accepted JSON Deserializer Error", jsonObj.ProviderId, null,
                            jsonObj.RequestId, qi.CompanyId, new
                            {
                                exception = e,
                                queueItemId = qi.QueueItemId,
                                json = qi.JsonObject
                            });

                        await sourceMessage.CompleteMessageAsync(sourceMessage.Message);
                        return false;
                    }

                    CallRequest cr = null;
                    var fltProviders = FleetnetProvider.GetByAccountId(qi.AccountId.Value);
                    var jobOffer = FleetnetJobOffer.GetByRequestId(
                        fltMessage.RequestId, fltProviders.Select(o => o.ProviderId).ToArray());

                    ServiceDispatchRequest fltRequest = JsonConvert.DeserializeObject<ServiceDispatchRequest>(fltMessage.JsonData);

                    if (jobOffer == null)
                    {
                        logger.LogEvent("{0}: Couldn't find a FleetnetJobOffer with ID of {2} - it was probably accepted via the phone.  Body = {1}",
                            qi.CompanyId, LogLevel.Warn, sourceMessage.Message.MessageId, qi.ToJson(), jsonObj.RequestId);
                    }
                    else
                    {
                        cr = await CallRequest.GetByIdAsync(jobOffer.CallRequestId);
                    }

                    string poNumber = fltRequest.ServiceDispatch?.FirstOrDefault(o => o.Field == "Event Id")?.Value ?? fltMessage.RequestId;

                    int accountId = qi.AccountId.Value;

                    if (fltMessage != null)
                    {
                        var fe = await DistributedLock.ForAsync<Entry>("Fleetnet", poNumber, 10000,
                            lockAcquired: async delegate ()
                            {
                                Entry e = await Entry.GetByPurchaseOrderNumberAsync(accountId, poNumber);

                                if (e == null)
                                {
                                    foreach (var a in await Account.GetByCompanyAsync(await Company.Company.GetByIdAsync(qi.CompanyId.Value), AccountType.MotorClub))
                                    {
                                        e = await Entry.GetByPurchaseOrderNumberAsync(a.Id, poNumber);
                                        if (e != null)
                                            break;
                                    }
                                }

                                if (e == null)
                                {
                                    e = new Entry();
                                    e.CompanyId = qi.CompanyId.Value;
                                }
                                else
                                {
                                    logger.LogEvent("{0}: Found existing towbook call for PO {1}/ResponseID {2}... Call #{3}... we're going to update this one.",
                                        qi.CompanyId, LogLevel.Warn, sourceMessage.Message.MessageId, poNumber, fltRequest.RequestId, e.CallNumber);
                                }

                                e.PurchaseOrderNumber = poNumber;
                                e.AccountId = qi.AccountId.Value;
                                EntryAsset asset = null;
                                if (e.Assets != null)
                                    asset = e.Assets.FirstOrDefault();

                                if (asset == null)
                                    asset = new EntryAsset() { BodyTypeId = 1 };

                                if (asset.BodyTypeId == 0)
                                    asset.BodyTypeId = 1;

                                // TODO: find a way to get the vehicle color?

                                string ExtractFieldValue(string name, ServiceDispatchRequest request)
                                {
                                    var fieldValue = fltRequest.ServiceDispatch.Where(o => o.Field.ToLowerInvariant() == name.ToLowerInvariant()).FirstOrDefault();
                                    if (fieldValue != null)
                                        return fieldValue.Value;

                                    return null;
                                }

                                var vehicleYearString = ExtractFieldValue("Vehicle Year", fltRequest);
                                if (!string.IsNullOrWhiteSpace(vehicleYearString))
                                {
                                    int vehicleYear;

                                    if (int.TryParse(vehicleYearString, out vehicleYear))
                                        asset.Year = vehicleYear;
                                }

                                var vehicleMake = ExtractFieldValue("Vehicle Make", fltRequest);
                                if (!string.IsNullOrWhiteSpace(vehicleMake))
                                    asset.Make = vehicleMake;

                                var vehicleModel = ExtractFieldValue("Vehicle Model", fltRequest);
                                if (!string.IsNullOrWhiteSpace(vehicleModel))
                                    asset.Model = vehicleModel;

                                var vehicleVin = ExtractFieldValue("VIN Number", fltRequest);
                                if (!string.IsNullOrWhiteSpace(vehicleVin))
                                    asset.Vin = vehicleVin;

                                var vehicleLicenseAndState = ExtractFieldValue("License Plate Number and State", fltRequest);
                                if (!string.IsNullOrWhiteSpace(vehicleLicenseAndState))
                                {
                                    string[] licenseParts = vehicleLicenseAndState.Split(' ');
                                    if (licenseParts.Count() > 0)
                                    {
                                        asset.LicenseNumber = licenseParts[0];
                                        if (licenseParts.Count() > 1)
                                            asset.LicenseState = licenseParts[1];
                                    }
                                }

                                var vehicleOdometer = ExtractFieldValue("Odometer Reading", fltRequest);
                                if (!string.IsNullOrWhiteSpace(vehicleOdometer))
                                {
                                    int odometer;
                                    if (int.TryParse(vehicleOdometer, out odometer))
                                        asset.Odometer = odometer;
                                }

                                List<string> addressParts = new List<string>();

                                var address = ExtractFieldValue("Address", fltRequest);
                                if (!string.IsNullOrWhiteSpace(address))
                                    addressParts.Add(address);

                                var eventCity = ExtractFieldValue("EventCity", fltRequest);
                                if (!string.IsNullOrWhiteSpace(eventCity))
                                    addressParts.Add(eventCity);

                                var eventState = ExtractFieldValue("EventState", fltRequest);
                                if (!string.IsNullOrWhiteSpace(eventState))
                                    addressParts.Add(eventState);

                                var eventZip = ExtractFieldValue("EventZip", fltRequest);
                                if (!string.IsNullOrWhiteSpace(eventZip))
                                    addressParts.Add(eventZip);

                                var location = ExtractFieldValue("Location", fltRequest);
                                if (!string.IsNullOrWhiteSpace(location))
                                    addressParts.Add("(" + location + ")");

                                if (addressParts.Count > 0)
                                    e.TowSource = string.Join(", ", addressParts);

                                var destination = ExtractFieldValue("Destination (Where to Tow to)", 
                                    fltRequest);

                                if (!string.IsNullOrWhiteSpace(destination))
                                    e.TowDestination = destination;

                                var dateTimeOccurred = ExtractFieldValue("DateTimeOccured", fltRequest);
                                if (!string.IsNullOrWhiteSpace(dateTimeOccurred))
                                {
                                    DateTime createDate;
                                    if (e.CreateDate == DateTime.MinValue && DateTime.TryParse(dateTimeOccurred, out createDate))
                                        e.CreateDate = createDate;
                                }

                                var arrivalETA = ExtractFieldValue("ETA (TIMESTAMP IN GMT)", fltRequest);
                                if (!string.IsNullOrWhiteSpace(arrivalETA))
                                {
                                    DateTime eta;
                                    if (DateTime.TryParse(arrivalETA, out eta))
                                        e.ArrivalETA = eta.ToLocalTime();   
                                }

                                string notes = "";

                                var trouble = ExtractFieldValue("Trouble", fltRequest);
                                if (!string.IsNullOrWhiteSpace(trouble))
                                    notes += "Trouble: " + trouble + "\n";

                                var service = ExtractFieldValue("Service Needed", fltRequest);
                                if (!string.IsNullOrWhiteSpace(service))
                                    notes += "Service Needed: " + service + "\n";

                                var unitNumber = ExtractFieldValue("Unit Number", fltRequest);
                                if (!string.IsNullOrWhiteSpace(unitNumber))
                                    notes += string.Format("Unit Number: {0}", unitNumber) + "\n";


                                
                                
                                void TryAdd(string field, string friendly = null)
                                {
                                    if (friendly == null)
                                        friendly = field;

                                    var v = ExtractFieldValue(field, fltRequest);
                                    if (!string.IsNullOrWhiteSpace(v))
                                        notes += friendly + ":" + v + "\n";
                                }

                                TryAdd("NationalAccountNumber", "National Account Number");

                                TryAdd("Notes for Tractor/Trailer Specific Information");

                                TryAdd("Trailer Loaded or Unloaded?");

                                TryAdd("customer_Client", "Client");

                                var ifLoadedTotalWeight = ExtractFieldValue("If Loaded Total Weight- (Weight to Tow)", fltRequest);
                                if (!string.IsNullOrWhiteSpace(ifLoadedTotalWeight))
                                    notes += "If Loaded Total Weight- (Weight to Tow): " + ifLoadedTotalWeight + "\n";

                                var ifDriverNotRiding = ExtractFieldValue("If driver is not riding back, where are the keys?", fltRequest);
                                if (!string.IsNullOrWhiteSpace(ifDriverNotRiding))
                                    notes += "If driver is not riding back, where are the keys?: " + ifDriverNotRiding + "\n";

                                var driverNotRidingBack = ExtractFieldValue("Driver Riding back?", fltRequest);
                                if (!string.IsNullOrWhiteSpace(driverNotRidingBack))
                                    notes += "Driver Riding back?: " + driverNotRidingBack + "\n";

                                var numberOfDrivers = ExtractFieldValue("Number of Drivers?", fltRequest);
                                if (!string.IsNullOrWhiteSpace(numberOfDrivers))
                                    notes += "Number of Drivers?: " + numberOfDrivers + "\n";

                                var driverWithUnit = ExtractFieldValue("Driver with the unit?", fltRequest);
                                if (!string.IsNullOrWhiteSpace(driverWithUnit))
                                    notes += "Driver with the unit?: " + driverWithUnit + "\n";

                                var notesForTractor = ExtractFieldValue("Notes for Tractor/Trailer Specific Information", fltRequest);
                                if (!string.IsNullOrWhiteSpace(notesForTractor))
                                    notes += "Notes for Tractor/Trailer Specific Information: " + notesForTractor + "\n";

                                var billTo = ExtractFieldValue("Bill To (Is this a Warranty Tow/To be Billed to Dealer?)", fltRequest);
                                if (!string.IsNullOrWhiteSpace(billTo))
                                    notes += "Bill To (Is this a Warranty Tow/To be Billed to Dealer?): " + billTo + "\n";

                                TryAdd("Additional Notes");


                                var winchOut = ExtractFieldValue("Winch Out, Recover or Tow?", fltRequest);
                                if (!string.IsNullOrWhiteSpace(winchOut))
                                    notes += "Winch Out, Recover or Tow?: " + winchOut + "\n";

                                e.Notes = notes;

                                if (e.Assets == null || e.Assets.Count == 0)
                                {
                                    e.Assets = new System.Collections.ObjectModel.Collection<EntryAsset>();
                                    e.Assets.Add(asset);
                                }

                                var contactName = ExtractFieldValue("Drivername", fltRequest);
                                var contactPhoneNumber = ExtractFieldValue("Driver Phone Number", fltRequest);

                                EntryContact c = e.Contacts.Where(
                                    o => o.Name == contactName || 
                                    Core.FormatPhone(o.Phone) == contactPhoneNumber)
                                    .FirstOrDefault();

                                bool newContact = false;
                                if (c == null)
                                {
                                    c = new EntryContact() { Name = contactName };
                                    e.Contacts.Add(c);
                                    newContact = true;
                                }

                                c.Phone = contactPhoneNumber;
                                c.DispatchEntryId = e.Id;

                                await e.Save();

                                if (newContact)
                                    await CheckForRoadsideFeatureAndAutoInvite(e, c);

                                await AutoDispatch.AutoDispatchServiceBusHandler.Send(e);

                                return e;
                            },
                            alreadyLocked: async delegate ()
                            {
                                logger.LogEvent("{0}/CR{1}: Lock already exists for {2}:{3}... pausing 250ms",
                                    qi.CompanyId, LogLevel.Warn,
                                    sourceMessage.Message.MessageId,
                                    (cr != null ? cr.CallRequestId.ToString() : "NULL"),
                                    qi.AccountId.Value,
                                    poNumber);


                                await System.Threading.Tasks.Task.Delay(250);

                                return true;
                            });

                        if (fe == null)
                        {
                            logger.LogEvent("{0}: Creation of call failed",
                                qi.CompanyId, LogLevel.Error, sourceMessage.Message.MessageId, qi.ToJson());
                            await sourceMessage.CompleteMessageAsync(sourceMessage.Message);
                        }
                        else
                        {
                            if (cr != null)
                            {
                                cr.DispatchEntryId = fe.Id;
                                await cr.Save();

                                qi.CallRequestId = cr.CallRequestId;
                                DigitalDispatchService.LogAction(qi);

                                await cr.UpdateStatus(CallRequestStatus.Accepted, po: fe.PurchaseOrderNumber);

                                DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);

                                await fe.AssignRequestDriverToEntry();

                                logger.Info(MasterAccountTypes.Fleetnet, "CallCreated",
                                    "Created new towbook call for Fleetnet",
                                    companyId: qi.CompanyId,
                                    data: new
                                    {
                                        waypoints = fe.Waypoints.Select(o => new { o.Address, o.Latitude, o.Longitude })
                                    },
                                    dispatchId: jsonObj.RequestId,
                                    callId: fe.Id,
                                    callRequestId: cr.CallRequestId,
                                    queueItemId: qi.QueueItemId,
                                    poNumber: fe.PurchaseOrderNumber,
                                    callNumber: fe.CallNumber);
                            }
                            
                        }

                    }

                    break;

                case DigitalDispatchActionQueueItemType.IncomingCallExpired:
                case DigitalDispatchActionQueueItemType.IncomingCallCancelled:
                    Fleetnet.FleetnetMessage cancelMessage = null;
                    try
                    {
                        cancelMessage = JsonConvert.DeserializeObject<Fleetnet.FleetnetMessage>(qi.JsonObject);
                    }
                    catch (Exception e)
                    {
                        logger.Log(LogLevel.Info, "Fleetnet/{0}: Error while deserializing json.. Exception = {2}, JSON = {1} ",
                            qi.QueueItemId,
                            jsonObj.JsonData,
                            e.Message);

                        await sourceMessage.DeadLetterMessageAsync(sourceMessage.Message, "Deadlettered because of exception ", e.ToJson() + ",\r\n" + jsonObj.JsonData);
                    }

                    CallRequest cancelRequest;
                    var cancelProvider = FleetnetProvider.GetByAccountId(qi.AccountId.Value);
                    FleetnetJobOffer cancelOffer = FleetnetJobOffer.GetByRequestId(
                        cancelMessage.RequestId, cancelProvider.Select(o => o.ProviderId).ToArray());

                    if (cancelOffer == null)
                    {
                        logger.LogEvent("{0}: Couldn't find a FleetnetMessage with ID of {2}.  Body = {1}",
                            qi.CompanyId, LogLevel.Error, sourceMessage.Message.MessageId, qi.ToJson(), jsonObj.RequestId);

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                        return false;
                    }

                    cancelRequest = CallRequest.GetById(cancelOffer.CallRequestId);
                    if (cancelRequest == null)
                    {
                        logger.LogEvent("{0}: Couldn't find the CallRequest associated with this messageId: {2}.  Body = {1}",
                            qi.CompanyId, LogLevel.Error, cancelOffer.FleetnetJobOfferId, qi.ToJson(), jsonObj.RequestId);

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                        return false;
                    }

                    // this needs to wait till api is deployed
                    //await cancelRequest.UpdateStatus(qi.Type == DigitalDispatchActionQueueItemType.IncomingCallExpired ? CallRequestStatus.Expired :
                    //     CallRequestStatus.Cancelled);

                    await cancelRequest.UpdateStatus(CallRequestStatus.Expired);


                    qi.CallRequestId = cancelRequest.CallRequestId;

                    DigitalDispatchService.LogAction(qi);
                    if (cancelRequest.DispatchEntryId.HasValue)
                    {
                        var en = Entry.GetByIdNoCache(cancelRequest.DispatchEntryId.Value);
                        if (en != null && ShouldAllowCancel(en))
                        {
                            CancelRequest fleetnetCancelRequest = JsonConvert.DeserializeObject<CancelRequest>(jsonObj.JsonData);
                            if (fleetnetCancelRequest == null)
                            {
                                logger.LogEvent("{0}: There was a problem deserializing the cancel/expired request: {2}.  Body = {1}",
                                    qi.CompanyId, LogLevel.Error, cancelOffer.FleetnetJobOfferId, cancelMessage.JsonData, jsonObj.RequestId);

                                DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                            }
                            else
                            {
                                string reason = fleetnetCancelRequest.Reason;

                                await en.Cancel($"Cancelled by Fleetnet: {reason}", AuthenticationToken.GetByUserId(1), "127.0.0.1");

                                logger.LogEvent("IncomingCancelEvent/Fleetnet/{3}/{4}: Cancelled/expired call {0}/{1} with reason {2}",
                                    qi.CompanyId,
                                    LogLevel.Info, en.CallNumber, en.Id, reason, cancelOffer.ProviderId, 
                                    cancelRequest.CallRequestId);
                            }
                        }
                    }

                    // notifies fleetnet that the cancel request has been processed
                    try
                    {
                        client.AcknowledgeDispatchCancelled(cancelOffer.RequestId);
                    }
                    catch (Exception fe)
                    {
                        await sourceMessage.DeadLetterMessageAsync(sourceMessage.Message, string.Format("Cancel request acknowledge POST failed: {0}", fe.Message));
                        return false;
                    }

                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    break;
            }

            await sourceMessage.CompleteMessageAsync(sourceMessage.Message);
            return true;
        }

        public static FleetnetRestClient GetClient(int companyId) =>
            (companyId == 10000 || companyId == 246389) ?
                FleetnetRestClient.GetDev() : 
                FleetnetRestClient.GetProduction();

        [Transaction]
        public static async Async.Task HandleFleetnetOutgoingMessage(
            DigitalDispatchActionQueueItem qi, dynamic jsonObj, 
            ProcessMessageEventArgs sourceMessage)
        {
            var client = GetClient(qi.CompanyId.Value);

            if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingLogin)
            {
                if (qi.AccountId.HasValue)
                {
                    var errors = new Dictionary<string, string>();
                    bool failed = false;

                    var providers = FleetnetProvider.GetByAccountId(qi.AccountId.Value);

                    foreach (var fleetnetProvider in providers)
                    {
                        fleetnetProvider.IsLoggedIn = true;
                        fleetnetProvider.Save();

                        try
                        {
                            var response = client.SignIn(fleetnetProvider.ProviderId);

                            if (!response.Success)
                                throw new Exception("failed to login:" + response.ToJson());

                            fleetnetProvider.UpdateLoginStatus(FleetnetProviderLoginStatus.LoggedIn);
                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                            logger.Info(MasterAccountTypes.Fleetnet,
                               "Login",
                               "Logged in",
                               fleetnetProvider.ProviderId, null, null, qi.CompanyId,
                               new
                               {
                                   json = response.ToJson()
                               },
                               queueItemId: qi.QueueItemId);

                        }
                        catch (Exception fe)
                        {
                            logger.Error(MasterAccountTypes.Fleetnet,
                               "Login",
                               fe.Message,
                               fleetnetProvider.ProviderId, null, null, qi.CompanyId,
                               new
                               {
                                   exception = fe
                               },
                               queueItemId: qi.QueueItemId);

                            failed = true;
                            errors.Add(fleetnetProvider.ProviderId, "Failed:" + fe);
                            fleetnetProvider.UpdateLoginStatus(FleetnetProviderLoginStatus.LoggedOut);
                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);

                            logger.Log(LogLevel.Error, "Fleetnet/" + qi.CompanyId + "/" + qi.AccountId + "/" + fleetnetProvider.ProviderId + "/Login/Response/Failed: " + fe);
                        }
                    }

                    await PushNotificationProvider.BackgroundJobStatusUpdate(qi.CompanyId.Value,
                        qi.QueueItemId,
                        "digitaldispatch_login",
                        !failed,
                        "Fleetnet Login Status",
                        errors);
                    
                    if (sourceMessage != null)
                        await sourceMessage.CompleteMessageAsync(sourceMessage.Message);
                }
                else
                {
                    if (sourceMessage != null)
                        await sourceMessage.DeadLetterMessageAsync(sourceMessage.Message, "Attempted to Login but no accountId was passed.", "");
                }
                return;
            }
            else if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingLogoff)
            {
                if (qi.AccountId.HasValue)
                {
                    var errors = new Dictionary<string, string>();
                    bool failed = false;

                    var providers = FleetnetProvider.GetByAccountId(qi.AccountId.Value);
                    foreach (var fleetnetProvider in providers)
                    {
                        fleetnetProvider.IsLoggedIn = false;
                        fleetnetProvider.Save();

                        try
                        {
                            client.SignOut(fleetnetProvider.ProviderId);

                            fleetnetProvider.UpdateLoginStatus(FleetnetProviderLoginStatus.LoggedOut);
                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                        }
                        catch (Exception fe)
                        {
                            failed = true;
                            errors.Add(fleetnetProvider.ProviderId, "Failed:" + fe);
                            fleetnetProvider.UpdateLoginStatus(FleetnetProviderLoginStatus.LoggedOut);
                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);

                            logger.Log(LogLevel.Error, "Fleetnet/" + qi.CompanyId + "/" + qi.AccountId + "/" + fleetnetProvider.ProviderId + "/Logoff/Response/Failed: " + fe);
                        }
                    }

                    await PushNotificationProvider.BackgroundJobStatusUpdate(qi.CompanyId.Value,
                        qi.QueueItemId,
                        "digitaldispatch_logout",
                        !failed,
                        "Fleetnet Login Status",
                        errors);
                }
                else
                {
                    // Attempted to Logoff but no accountId was passed.
                }
                if (sourceMessage != null)
                    await sourceMessage.CompleteMessageAsync(sourceMessage.Message);
                return;
            }
            else
            {
                var request = new CallRequest();
                var job = new Fleetnet.FleetnetJobOffer();

                request = CallRequest.GetById(Convert.ToInt32(jsonObj.Id ?? jsonObj.CallRequestId));
                if (request == null)
                {
                    await sourceMessage.DeadLetterMessageAsync(sourceMessage.Message);
                    return;
                }

                qi.CallRequestId = request.CallRequestId;

                job = FleetnetJobOffer.GetByCallRequestId(request.CallRequestId);
                if (job == null)
                {
                    if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingAcceptCall)
                    {
                        await sourceMessage.DeadLetterMessageAsync(sourceMessage.Message, "Attempted to accept callRequestId " + request.CallRequestId + ", but no DigitalDispatch present in database for it",
                        request.ToJson(true));
                        await request.UpdateStatus(CallRequestStatus.AcceptFailed);
                    }
                    else if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingRejectCall)
                    {
                        await sourceMessage.DeadLetterMessageAsync(sourceMessage.Message, "Attempted to reject callRequestId " + request.CallRequestId + ", but no DigitalDispatch present in database for it",
                            request.ToJson(true));
                        await request.UpdateStatus(CallRequestStatus.RejectFailed);
                    }
                    else if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingRequestPhoneCall)
                    {
                        await sourceMessage.DeadLetterMessageAsync(sourceMessage.Message, "Attempted to phone call request callRequestId " + request.CallRequestId + ", but no DigitalDispatch present in database for it",
                            request.ToJson(true));
                        await request.UpdateStatus(CallRequestStatus.PhoneCallRequestFailed);
                    }
                    else if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingCallStatusResponse)
                    {
                        await sourceMessage.DeadLetterMessageAsync(sourceMessage.Message,
                            $"Attempted to send call status update for callRequestId {request.CallRequestId}, but no DigitalDispatch present in database for it",
                            request.ToJson(true));
                    }

                    return;
                }

                switch (qi.Type)
                {
                    case DigitalDispatchActionQueueItemType.OutgoingAcceptCall:
                        DigitalDispatchService.LogAction(qi);
                        try
                        {
                            bool? tireAvailable = (bool?)jsonObj.TireAvailable;
                            if (request.Reason != "Tire")
                                tireAvailable = null;

                            if (jsonObj.DriverId != null)
                            {
                                MotorClubDispatchingService.SaveDriversForCallRequest(request.CallRequestId, Convert.ToInt32(jsonObj.DriverId));
                            }

                            client.AcceptDispatch(
                                job.RequestId,
                                (int)jsonObj.Eta,
                                (string)jsonObj.Notes,
                                "Yes", // (must always be Yes... Stupid.
                                tireAvailable == null ? null : (tireAvailable.Value ? "Yes" : "No"),
                                jsonObj.PhoneNumber.ToString()); // Valid Values: Yes/No.

                            await request.UpdateStatus(CallRequestStatus.AcceptSent);

                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                        }
                        catch (Exception fe)
                        {
                            Console.WriteLine(fe.ToString());

                            logger.Error(MasterAccountTypes.Fleetnet,
                               "AcceptCall",
                               "Sent accept failed.",
                               job.ProviderId, null, job?.RequestId, qi?.CompanyId,
                               new
                               {
                                   errorMessage = fe.Message,
                                   exception = fe
                               },
                               callRequestId: request.CallRequestId,
                               queueItemId: qi.QueueItemId);

                            await request?.UpdateStatus(CallRequestStatus.AcceptFailed);

                            await sourceMessage.DeadLetterMessageAsync(sourceMessage.Message, string.Format("Outgoing accept call request failed: {0}", fe.Message));
                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                            return;
                        }

                        break;

                    case DigitalDispatchActionQueueItemType.OutgoingRejectCall:
                        DigitalDispatchService.LogAction(qi);
                        try
                        {
                            MasterAccountReason rejectReason = MasterAccountReason.GetById(Convert.ToInt32(jsonObj.MasterAccountReasonId));

                            client.RejectDispatch(job.RequestId,
                                rejectReason?.Code);

                            await request.UpdateStatus(CallRequestStatus.Rejected);
                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                        }
                        catch //(Exception fe)
                        {
                            // TODO: handle possible exceptions
                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                            await sourceMessage.DeadLetterMessageAsync(sourceMessage.Message);
                            return;
                        }
                        break;

                    case DigitalDispatchActionQueueItemType.OutgoingRequestPhoneCall:
                        DigitalDispatchService.LogAction(qi);
                        var phoneNumber = (string)jsonObj.PhoneNumber;
                        try
                        {
                            var resp = client.AcceptDispatch(job.RequestId, 0, 
                                    notes: "",
                                    response: "Call Me", 
                                    phone: phoneNumber);

                            await request.UpdateStatus(CallRequestStatus.Rejected);
                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                        }
                        catch (Exception fe)
                        {
                            logger.Error(MasterAccountTypes.Fleetnet, 
                                "RequestPhoneCall", 
                                $"Error requesting phone call using phone number {phoneNumber}: " + fe.Message,
                                companyId: qi.CompanyId,
                                data: fe,
                                dispatchId: job.RequestId,
                                callRequestId: qi.CallRequestId, 
                                queueItemId: qi.QueueItemId);

                            await request.UpdateStatus(CallRequestStatus.PhoneCallRequestFailed);

                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                            
                            if (sourceMessage != null)
                                await sourceMessage.DeadLetterMessageAsync(sourceMessage.Message);
                            return;
                        }
                        break;

                    case DigitalDispatchActionQueueItemType.OutgoingStatusUpdate:
                        DigitalDispatchService.LogAction(qi);
                        try
                        {
                            int statusId = Convert.ToInt32(jsonObj.NewStatusId);
                            double? lat = (double?)jsonObj.Latitude;
                            double? lng = (double?)jsonObj.Longitude;
                            var statusName = (await Status.GetByIdAsync(statusId, qi.CompanyId.Value))?.Name;
                            
                            string notReadyToRollReason = null;
                            string notes = null;

                            if (statusId == Status.Completed.Id && jsonObj.CompletionReasonId != null)
                            {
                                int completionReasonId = (int)jsonObj.CompletionReasonId;

                                var marc = MasterAccountReason.GetById(completionReasonId);

                                if (marc != null && marc.Code != "SUCCESS")
                                {
                                    notReadyToRollReason = marc.Code;
                                }

                                if (jsonObj.Notes != null)
                                    notes = (string)jsonObj.Notes;
                            }

                            await client.StatusUpdate(
                                requestId: job.RequestId,
                                status: statusName,
                                lat: lat, 
                                lng: lng,
                                notReadyToRollReason: notReadyToRollReason,
                                notes: notes);

                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                        }
                        catch (Exception fe)
                        {
                            Console.WriteLine(fe.ToString());
                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                            if (sourceMessage != null)
                                await sourceMessage.DeadLetterMessageAsync(sourceMessage.Message);
                            // TODO: handle possible exceptions
                            return;
                        }
                        break;

                    case DigitalDispatchActionQueueItemType.OutgoingSharePhoto:
                    case DigitalDispatchActionQueueItemType.OutgoingShareSignature:

                        async Task SharePhotoOrSignature(int callRequestId, string json)
                        {
                            try
                            {
                                var pm = JsonConvert.DeserializeObject<SharePhotoModel>(json);

                                qi.CallRequestId = request.CallRequestId;

                                if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingShareSignature)
                                {
                                    var sig = Signature.GetById((int)jsonObj.SignatureId);
                                    // todo: send
                                }
                                else if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingSharePhoto)
                                {
                                    var pho = Dispatch.Photo.GetById((int)jsonObj.PhotoId);
                                    if (pho == null)
                                    {
                                        throw new Exception("no such photo. " + qi.JsonObject);
                                    }

                                    var en = await Entry.GetByIdNoCacheAsync(pm.DispatchEntryId);

                                    var filename = await FileUtility.GetFileAsync(pho.Location.Replace("%1", qi.CompanyId.ToString()));


                                    var fltMessage = FleetnetMessage.GetBy(Convert.ToInt32(job.ProviderId), 
                                        Convert.ToInt32(job.RequestId), 2);

                                    ServiceDispatchRequest fltRequest = JsonConvert.DeserializeObject<ServiceDispatchRequest>(fltMessage.JsonData);

                                    client.UploadEventDocuments(
                                        new FleetnetRestClient.UploadEventDocumentsBody()
                                        {
                                            FileExtension = "jpg",
                                            EventNumber = Convert.ToInt32(en.PurchaseOrderNumber),
                                            UnitNumber = fltRequest.ServiceDispatch.FirstOrDefault(r => r.Field == "Unit Number")?.Value ?? "UNKNOWN"
                                        },
                                        filename);

                                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                                }

                            }
                            catch (Exception y)
                            {
                                logger.Error(MasterAccountTypes.Fleetnet, "SharePhoto", "Error sending photo: " + y.Message,
                                    companyId: qi.CompanyId,
                                    data: y,
                                    callRequestId: callRequestId);
                            }

                            if (sourceMessage != null)
                                await sourceMessage.CompleteMessageAsync(sourceMessage.Message);

                            DigitalDispatchService.LogAction(qi);
                            return;
                        }

                        await SharePhotoOrSignature(Convert.ToInt32(jsonObj.CallRequestId), qi.JsonObject);

                        return;

                }

                if (sourceMessage != null)
                    await sourceMessage?.CompleteMessageAsync(sourceMessage.Message);
            }
        }
    }
}
