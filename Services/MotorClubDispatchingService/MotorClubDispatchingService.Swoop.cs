using Extric.Towbook.Accounts;
using Extric.Towbook.Dispatch;
using Extric.Towbook.Integration.MotorClubs.Queue;
using Extric.Towbook.Utility;
using Azure.Messaging.ServiceBus;
using Microsoft.Extensions.Hosting;
using NLog;
using System;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using static Extric.Towbook.Vehicle.VehicleUtility;
using Extric.Towbook.Integration.MotorClubs;
using Extric.Towbook.Integration.MotorClubs.Services;
using Extric.Towbook.Integration.MotorClubs.Dispatch;
using Extric.Towbook.Integrations.MotorClubs.Swoop;
using Newtonsoft.Json;
using System.Collections.Generic;
using Extric.Towbook.Storage;
using System.Diagnostics;
using NewRelic.Api.Agent;
using System.Net.Http;
using Twilio.Http;

namespace Extric.Towbook.Services.MotorClubDispatchingService
{
    public partial class MotorClubDispatchingService : IHostedService
    {
        public const string OON_LINE_ITEM_NAME = "Out of Network Job";

        public static AuthenticationToken authtokenSwoop = new AuthenticationToken()
        {
            UserId = 17,
            ClientVersionId = MyClientVersionId
        };

        [Transaction]
        public static async Task<bool> HandleSwoopIncomingMessage(
            DigitalDispatchActionQueueItem qi,
            SwoopMessage msg,
            ProcessMessageEventArgs sourceMessage)
        {

            var jsonObj = JsonConvert.DeserializeObject<Integrations.MotorClubs.Swoop.Model.SwoopJob>(msg.JsonData, 
                new JsonSerializerSettings()
                {
                    MissingMemberHandling = MissingMemberHandling.Ignore,
                    NullValueHandling = NullValueHandling.Ignore
                });
            var account = await Account.GetByIdAsync(qi.AccountId.Value);
            var ma = await MasterAccount.GetByIdAsync(account.MasterAccountId);
            string masterAccountName = ma?.Name ?? "UNKNOWN";

            switch (qi.Type)
            {
                case DigitalDispatchActionQueueItemType.IncomingCallGoaResponse:
                    var sss2 = SwoopSession.GetById(msg.SessionId);
                    SwoopDispatch sd2 = null;

                    if (jsonObj.Status == "GOARequested")
                    {
                        logger.Info(MasterAccountTypes.Swoop,
                           "IncomingCallGoaRequested",
                           "Swoop confirmed receipt of GOA Request.",
                           jsonObj.Partner?.Company?.Id,
                           null,
                           jsonObj.AuthorizationNumber.ToString(),
                           qi.CompanyId,
                           data: new
                           {
                               json = qi.JsonObject
                           },
                           queueItemId: qi.QueueItemId
                           );

                        // do nothing, this was the request. wait for the GOA approval. 
                        break;
                    }

                    if (sss2?.CompanyId == 105272)
                    {
                        var all = SwoopDispatch.OonGetByDispatchId(jsonObj.Id, sss2.SwoopSessionId);
                        foreach (var each in all)
                        {
                            var eachCr = CallRequest.GetById(each.CallRequestId);
                            if (eachCr?.DispatchEntryId != null)
                            {
                                sd2 = each;
                                break;
                            }
                        }
                        if (sd2 == null)
                        {
                            // TODO: trigger GOA warning 
                        }
                    }
                    else
                        sd2 = SwoopDispatch.GetByDispatchId(jsonObj.Id, sss2.SwoopSessionId);

                    var crr2 = CallRequest.GetById(sd2.CallRequestId);
                    await crr2.UpdateStatus(CallRequestStatus.GoaApprovedByMotorClub);

                    qi.CallRequestId = crr2.CallRequestId;
                    DigitalDispatchService.LogAction(qi);

                    var entry2 = Entry.GetByIdNoCache(crr2.DispatchEntryId.Value);
                    if (entry2 != null)
                    {
                        if (entry2.Status.Id != Status.Cancelled.Id)
                        {
                            const string newNote = "Call was marked as GOA from Swoop.\n\n";

                            if (!entry2.Notes.Contains(newNote))
                            {
                                if (entry2.CompletionTime == null)
                                    entry2.CompletionTime = DateTime.Now;

                                entry2.Notes = newNote + entry2.Notes;

                                var srca = new SwoopRestClient();

                                if (sss2.CompanyId == 10000)
                                    srca = SwoopRestClient.Staging();

                                var swoopcall = await srca.GetJobById(sd2.OfferId, sss2.AccessToken, true);

                                if (entry2.Account.MasterAccountId == MasterAccountTypes.OonSwoop)
                                {
                                    var itemName = $"{OON_LINE_ITEM_NAME} GOA";

                                    foreach (var ii in entry2.InvoiceItems.Where(o => o.CustomName == OON_LINE_ITEM_NAME))
                                    {
                                        ii.CustomPrice = 0;
                                        ii.Quantity = 0;
                                    }

                                    entry2.InvoiceItems.Add(new InvoiceItem()
                                    {
                                        CustomName = itemName,
                                        Quantity = 1,
                                        CustomPrice = swoopcall.OonProviderAmount.OonGoaAmount
                                    });

                                    var json_pay = Core.GetRedisValue("oon_swoop_pay:" + sd2.Swcid);

                                    if (json_pay != null)
                                    {
                                        var payment = JsonConvert.DeserializeObject<OonPayModel>(json_pay);

                                        entry2.SetAttribute(Dispatch.AttributeValue.BUILTIN_BILLING_NOTES,
                                            $"CCN: {payment.CCN}, Exp: {payment.Expiration}, " +
                                            $"CCV: {payment.CSC}, Billing Zip: 02155, " +
                                            $"Amount: {swoopcall.OonProviderAmount.OonGoaAmount.GetValueOrDefault().ToString("C")}");
                                    }
                                }

                                if (entry2.Status != Status.Completed)
                                    entry2.Status = Status.Completed;

                                await entry2.Save(false, authtokenSwoop);
                            }
                        }
                    }

                    break;

                case DigitalDispatchActionQueueItemType.IncomingCallServiceFailureResponse:
                    var sss3 = SwoopSession.GetById(msg.SessionId);
                    SwoopDispatch sd3 = null;
                    // this whole section would work better if we had a pending serviceFailure queue to look 
                    // for this in instead of doing this crazy oon logic

                    if (sss3?.CompanyId == 105272)
                    {
                        var sfrOonDispatch = SwoopDispatch.OonGetByDispatchId(jsonObj.Id, msg.SessionId)
                            .Where(o => o.Eta > 0)
                            .Select(o => new { cr = CallRequest.GetById(o.CallRequestId), sd = o })
                            .Where(r => r.cr.Status == CallRequestStatus.Accepted)
                            .FirstOrDefault()?.sd;

                        if (sfrOonDispatch != null)
                            sd3 = sfrOonDispatch;
                    }

                    if (sd3 == null)
                        sd3 = SwoopDispatch.GetByDispatchId(jsonObj.Id, sss3.SwoopSessionId);
                    var crr3 = CallRequest.GetById(sd3.CallRequestId);
                    await crr3.UpdateStatus(CallRequestStatus.ServiceFailureConfirmed);

                    qi.CallRequestId = crr3.CallRequestId;
                    DigitalDispatchService.LogAction(qi);

                    var entry3 = Entry.GetByIdNoCache(crr3.DispatchEntryId.Value);
                    if (entry3 != null)
                    {
                        if (entry3.Status != Status.Cancelled)
                        {
                            const string newNote = "Call was marked as unsuccessful from Swoop.\n\n";

                            if (!entry3.Notes.Contains(newNote))
                            {
                                if (entry3.CompletionTime == null)
                                    entry3.CompletionTime = DateTime.Now;

                                if (entry3.Status != Status.Completed)
                                    entry3.Status = Status.Completed;

                                entry3.Notes = newNote + entry3.Notes;

                                await entry3.Save(false, authtokenSwoop);
                            }
                        }
                    }

                    break;

                case DigitalDispatchActionQueueItemType.IncomingCallCancelled:
                case DigitalDispatchActionQueueItemType.IncomingCallRejected:
                    var sss = SwoopSession.GetById(msg.SessionId);
                    var sd = SwoopDispatch.GetByDispatchId(jsonObj.Id, msg.SessionId);

                    if (sss?.CompanyId == 105272)
                    {
                        var sfrOonDispatch = SwoopDispatch.OonGetByDispatchId(jsonObj.Id, msg.SessionId)
                            .Where(o => o.Eta > 0)
                            .Select(o => new { cr = CallRequest.GetById(o.CallRequestId), sd = o });

                        var last = sfrOonDispatch.FirstOrDefault(r => 
                            r.cr.Status == CallRequestStatus.Accepted || 
                            (r.cr.DispatchEntryId != null && r.cr.Status == CallRequestStatus.AcceptSent))?.sd;

                        if (last != null)
                            sd = last;
                    }

                    if (sd == null)
                    {
                        logger.Warn(MasterAccountTypes.Swoop,
                           "CallCancelled",
                           "Call cancelled by Swoop ignored - dispatch doesn't exist in Towbook",
                           jsonObj.Partner?.Company?.Id, null, jsonObj.AuthorizationNumber.ToString(), qi.CompanyId,
                           data: new
                           {
                               json = qi.JsonObject
                           },
                           queueItemId: qi.QueueItemId
                           );

                        break;
                    }
                    var crr = CallRequest.GetById(sd.CallRequestId);
                    int lmasterAccountId = MasterAccountTypes.Swoop;

                    if (sss.CompanyId == 105272 && qi.Type == DigitalDispatchActionQueueItemType.IncomingCallCancelled)
                    {
                        var oonDispatch = SwoopDispatch.OonGetByDispatchId(jsonObj.Id, msg.SessionId)
                            .Where(o => o.Eta > 0)
                            .Select(o => CallRequest.GetById(o.CallRequestId))
                            .FirstOrDefault(r =>
                                r.Status == CallRequestStatus.Accepted ||
                                (r.DispatchEntryId != null && r.Status == CallRequestStatus.AcceptSent));
                            

                        if (oonDispatch == null)
                        {
                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                            break;
                        }
                        crr = oonDispatch;


                        var finalAmount = (await (new Integrations.MotorClubs.Swoop.SwoopRestClient())
                            .GetJobById(jsonObj.Id, sss.AccessToken, true, true, true))
                            .OonProviderAmount.OonGoaAmount.GetValueOrDefault();

                        var json_pay = Core.GetRedisValue("oon_swoop_pay:" + sd.Swcid);

                        if (json_pay != null)
                        {
                            var payment = JsonConvert.DeserializeObject<OonPayModel>(json_pay);

                            var entry = Entry.GetByIdNoCache(crr.DispatchEntryId.Value);

                            if (finalAmount > 0)
                                entry.SetAttribute(Dispatch.AttributeValue.BUILTIN_BILLING_NOTES,
                                    $"CCN: {payment.CCN}, Exp: {payment.Expiration}, " +
                                    $"CCV: {payment.CSC}, Billing Zip: 02155, " +
                                    $"Cancelled Call Amount: {finalAmount}");
                            else
                                entry.SetAttribute(Dispatch.AttributeValue.BUILTIN_BILLING_NOTES,
                                    "Call was cancelled.");

                            foreach (var ii in entry.InvoiceItems.Where(o => o.CustomName == OON_LINE_ITEM_NAME))
                            {
                                ii.CustomPrice = 0;
                                ii.Quantity = 0;
                            }

                            entry.InvoiceItems.Add(new InvoiceItem()
                            {
                                CustomName = $"{OON_LINE_ITEM_NAME} - Motor Club Cancelled",
                                Quantity = 1,
                                CustomPrice = finalAmount
                            });

                            lmasterAccountId = MasterAccountTypes.OonSwoop;

                            await entry.Save(token: authtokenSwoop, ipAddress: "127.0.0.1");
                        }
                    }

                    var providerRejected = false;
                    if (crr.ResponseReasonId != null)
                    {
                        var reason = MasterAccountReason.GetById(crr.ResponseReasonId.Value);
                        if (reason?.Type == MasterAccountReasonType.Reject)
                            providerRejected = true;
                    }

                    if (qi.Type == DigitalDispatchActionQueueItemType.IncomingCallRejected)
                    {
                        if (providerRejected)
                            await crr.UpdateStatus(CallRequestStatus.Rejected);
                        else
                            await crr.UpdateStatus(CallRequestStatus.RejectedByMotorClub);
                    }
                    else
                    {
                        await crr.UpdateStatus(CallRequestStatus.Cancelled);
                    }

                    qi.CallRequestId = crr.CallRequestId;
                    DigitalDispatchService.LogAction(qi);
                    if (crr.DispatchEntryId != null)
                    {
                        var en = Entry.GetByIdNoCache(crr.DispatchEntryId.Value);
                        if (en != null)
                        {
                            if (en.Status.Id != Status.Cancelled.Id)
                            {
                                if (en.Status == Status.Completed)
                                {
                                    logger.Error(MasterAccountTypes.Swoop,
                                       "CallCancelled",
                                       "Call cancelled by Swoop ignored - call is already completed",
                                       jsonObj.Partner?.Company?.Id, null, jsonObj.AuthorizationNumber.ToString(), qi.CompanyId,
                                       data: new
                                       {
                                           json = qi.JsonObject
                                       },
                                       callId: en.Id,
                                       callRequestId: crr.CallRequestId,
                                       queueItemId: qi.QueueItemId,
                                       callNumber: en.CallNumber);
                                }
                                else
                                {
                                    if (Core.GetRedisValue(en.Id + ":ss_dc") == "1")
                                    {
                                        // TODO: mark the call as acknowledged initially.
                                        await en.Cancel("Cancellation Confirmed",
                                            authtokenSwoop, "127.0.0.1");
                                    }
                                    else
                                    {
                                        if (ShouldAllowCancel(en))
                                        {
                                            await en.Cancel("Cancelled by Agero (Swoop)",
                                                authtokenSwoop, "127.0.0.1");
                                        }
                                    }

                                }
                            }
                        }
                    }

                    logger.Info(lmasterAccountId,
                        "CallCancelled",
                        "Call cancelled by Swoop",
                        jsonObj.Partner?.Company?.Id, null,
                        jsonObj.Id, crr.CompanyId, new
                        {
                            json = qi.JsonObject
                        },
                        callRequestId: crr.CallRequestId,
                        queueItemId: qi.QueueItemId,
                        callId: crr.DispatchEntryId,
                        poNumber: jsonObj.AuthorizationNumber.ToString());

                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);

                    break;

                case DigitalDispatchActionQueueItemType.IncomingCallOffered:
                    if (account.MasterAccountId == MasterAccountTypes.OonSwoop ||
                        account.CompanyId == 105272)
                    {
                        await HandleOonSwoop(qi, msg, sourceMessage);
                        break;
                    }

                    var rcr = await CreateCallRequest(msg, MotorClubName.Swoop, true, account.MasterAccountId);
                    if (rcr == null)
                    {

                        logger.Warn(MasterAccountTypes.Swoop,
                            "CallOffered",
                            "New call request failed to create for Swoop",
                            jsonObj.Partner?.Company?.Id,
                            jsonObj.Partner?.Site?.Id,
                            jsonObj.Id, qi.CompanyId, new
                            {
                                json = qi.JsonObject
                            },
                            queueItemId: qi.QueueItemId,
                            poNumber: jsonObj.AuthorizationNumber.ToString());

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                        break;
                    }

                    qi.CallRequestId = rcr.CallRequestId;
                    DigitalDispatchService.LogAction(qi);

                    logger.Info(MasterAccountTypes.Swoop,
                        "CallOffered",
                        "New call request created for Swoop",
                        jsonObj.Partner?.Company?.Id,
                        jsonObj.Partner?.Site?.Id,
                        jsonObj.Id, qi.CompanyId, new
                        {
                            json = qi.JsonObject
                        },
                        callRequestId: rcr.CallRequestId,
                        queueItemId: qi.QueueItemId,
                        poNumber: jsonObj.AuthorizationNumber.ToString());

                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    break;

                case DigitalDispatchActionQueueItemType.IncomingCallAccepted:
                    async Task<bool> handleAccept()
                    {
                        try
                        {
                            logger.Info(MasterAccountTypes.Swoop,
                                "CallAccepted",
                                "Incoming Job Offer Accepted",
                                qi.CompanyId.ToString(),
                                jsonObj.Partner?.Site?.Id,
                                jsonObj.Id, qi.CompanyId, jsonObj,
                                queueItemId: qi.QueueItemId,
                                poNumber: jsonObj.AuthorizationNumber.ToString());
                        }
                        catch (Exception e)
                        {
                            logger.Info(MasterAccountTypes.Swoop,
                                "CallAccepted",
                                "Incoming Job Offer Accepted JSON Deserializer Error",
                                null, null,
                                jsonObj.Id,
                                qi.CompanyId,
                                new
                                {
                                    exception = e,
                                    json = qi.JsonObject
                                },
                                queueItemId: qi.QueueItemId);

                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                            await sourceMessage.CompleteAsync();
                            return false;
                        }

                        CallRequest cr = null;
                        var ss = SwoopSession.GetById(msg.SessionId);
                        if (ss == null)
                        {

                            var swoopSite = SwoopSite.GetByAccountId(qi.AccountId.Value);
                            if (swoopSite != null)
                                ss = SwoopSession.GetById(swoopSite.SwoopSessionId);

                            if (ss == null)
                            {
                                logger.Info(MasterAccountTypes.Swoop,
                                    "CallAccepted",
                                    "Invalid SwoopSessionId - can't find digital registration.",
                                    qi.CompanyId.ToString(), null,
                                    jsonObj.Id, qi.CompanyId, new
                                    {
                                        json = qi.JsonObject
                                    },
                                    queueItemId: qi.QueueItemId);

                                DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                                await sourceMessage.CompleteAsync();
                                return false;
                            }
                        }

                        SwoopDispatch swoopDispatch = null;
                        int masterAccountId = MasterAccountTypes.Swoop;

                        if (ss.CompanyId == 105272)
                        {
                            var oon = SwoopDispatch.OonGetByDispatchId(jsonObj.Id, ss.SwoopSessionId);
                            foreach (var x in oon)
                            {
                                var xcr = CallRequest.GetById(x.CallRequestId);

                                if (xcr.Status == CallRequestStatus.Accepting ||
                                    xcr.Status == CallRequestStatus.Accepted ||
                                    xcr.Status == CallRequestStatus.AcceptSent)
                                {
                                    swoopDispatch = x;
                                    cr = xcr;
                                    try
                                    {
                                        var src = new SwoopRestClient();

                                        var c = await Company.Company.GetByIdAsync(cr.CompanyId);
                                        var dispatcher = await User.GetByIdAsync(cr.OwnerUserId.GetValueOrDefault());
                                        var companyEmail = c.Email;

                                        if (!Core.IsEmailValid(c.Email))
                                            companyEmail = "";

                                        await src.UpdateNotes(
                                            ss.AccessToken,
                                            swoopDispatch.OfferId,
                                                c.Name + ".\n" +
                                                Core.FormatPhone(c.Phone) +
                                                ".\nDispatcher: " + dispatcher?.FullName + "\n" +
                                                companyEmail,
                                            (await src.GetAdminUser(ss)).Id);
                                    }
                                    catch (Exception yx)
                                    {
                                        Console.WriteLine(yx.ToString());
                                    }
                                    break;
                                }
                            }
                            masterAccountId = MasterAccountTypes.OonSwoop;
                        }
                        else
                        {
                            swoopDispatch = SwoopDispatch.GetByDispatchId(jsonObj.Id, ss.SwoopSessionId);
                        }

                        if (swoopDispatch == null)
                        {
                            var sStartingLocation = (await jsonObj.Location.ServiceLocation.ToWaypoint()).Address;
                            var sTowDestination = jsonObj.Location?.DropoffLocation != null ?
                                (await jsonObj.Location.DropoffLocation?.ToWaypoint()).Address : null;
                            var sVehicle = $"{jsonObj.Vehicle.Year} {jsonObj.Vehicle.Make} {jsonObj.Vehicle.Model} {jsonObj.Vehicle.Color}";

                            // create a fake call request.
                            cr = new CallRequest()
                            {
                                AccountId = ss.AccountId,
                                CompanyId = ss.CompanyId,
                                ExpirationDate = DateTime.Now.AddSeconds(1),
                                PurchaseOrderNumber = jsonObj.AuthorizationNumber.ToString(),
                                Reason = jsonObj.Service.Symptom,
                                RequestDate = DateTime.Now,
                                ServiceNeeded = jsonObj.Service.Name,
                                StartingLocation = sStartingLocation,
                                TowDestination = sTowDestination,
                                Vehicle = sVehicle,
                                MaxEta = 99,
                                ProviderId = ss.SwoopSessionId.ToString()
                            };
                            await cr.Save();

                            swoopDispatch = new SwoopDispatch()
                            {
                                CallRequestId = cr.CallRequestId,
                                Swcid = jsonObj.AuthorizationNumber,
                                OfferJson =
                                jsonObj.ToJson(),
                                SwoopSessionId = ss.SwoopSessionId,
                                OfferId = jsonObj.Id
                            };
                            swoopDispatch.Save();
                        }
                        else
                        {
                            if (cr == null)
                                cr = CallRequest.GetById(swoopDispatch.CallRequestId);
                        }

                        string poNumber = jsonObj.AuthorizationNumber.ToString();
                        int accountId = cr.AccountId;
                        int companyId = cr.CompanyId;
                        string locationId = null;

                        if (true)
                        {
                            var fe = await DistributedLock.ForAsync("Swoop", poNumber, 10000,
                                lockAcquired: async () =>
                                {
                                    var srcl = new SwoopRestClient();

                                    if (ss.CompanyId == 10000)
                                        srcl = SwoopRestClient.Staging();

                                    jsonObj = await srcl.GetJobById(jsonObj.Id, ss.AccessToken, masterAccountId == MasterAccountTypes.OonSwoop);
                                    var siteId = jsonObj.Site?.Id ?? jsonObj.Partner?.Site?.Id ?? jsonObj.Partner?.RateAgreement?.Id;
                                    var rateAgreementId = jsonObj.Partner?.RateAgreement?.Id;
                                    locationId = siteId;
                                    var site = SwoopSite.GetBySiteId(ss.SwoopSessionId, siteId);

                                    if (site != null)
                                    {
                                        companyId = site.CompanyId;
                                        accountId = site.AccountId;
                                    }
                                    else
                                    {
                                        if (masterAccountId != MasterAccountTypes.OonSwoop)
                                        {
                                            logger.Warn(masterAccountId,
                                                "MissingSite",
                                                "Swoop Site" + (rateAgreementId != null ? " (RateAgreement) " : "") + " isn't registered in Towbook. Can't route properly.",
                                                jsonObj.Partner?.Company?.Id,
                                                siteId,
                                                jsonObj.Id, qi.CompanyId, new
                                                {
                                                    json = qi.JsonObject,
                                                    humanReviewNeeded = true
                                                },
                                                callRequestId: cr?.CallRequestId,
                                                queueItemId: qi.QueueItemId,
                                                poNumber: jsonObj.AuthorizationNumber.ToString());

                                            if (jsonObj.Partner?.RateAgreement?.Id != null)
                                            {

                                                var ess = SwoopSite.GetBySiteId(
                                                    ss.SwoopSessionId,
                                                    jsonObj.Partner?.RateAgreement?.Id);

                                                if (ess != null)
                                                {
                                                    companyId = ess.CompanyId;
                                                    accountId = ess.AccountId;
                                                }
                                                else
                                                {
                                                    var sharedCompanies = Company.SharedCompany.GetByCompanyId(companyId);

                                                    var companies = (await Task.WhenAll(sharedCompanies
                                                        .Select(o => Company.Company.GetByIdAsync(o.SharedCompanyId)))).ToList();

                                                    if (companies.Count() > 1)
                                                    {
                                                        var parent = sharedCompanies.First().CompanyId;

                                                        foreach (var x in companies)
                                                        {
                                                            if (x.Latitude == 0)
                                                            {
                                                                var geocoded = await GeocodeHelper.Geocode(x.GetComposedAddress());
                                                                if (geocoded != null && geocoded.Latitude != 0)
                                                                {
                                                                    x.Latitude = geocoded.Latitude;
                                                                    x.Longitude = geocoded.Longitude;
                                                                    await x.Save();
                                                                }
                                                            }
                                                        }

                                                        var ra = jsonObj.Partner.RateAgreement;

                                                        var closest = SwoopSite.GetClosestCompanies(parent, ra.AnchorLocation.Latitude.Value, ra.AnchorLocation.Longitude.Value);

                                                        var result = closest.FirstOrDefault();

                                                        if (result != null && result.AccountId != 0)
                                                        {
                                                            var newSite = new SwoopSite()
                                                            {
                                                                SwoopSessionId = ss.SwoopSessionId,
                                                                CompanyId = result.CompanyId,
                                                                AccountId = result.AccountId,
                                                                SiteId = ra.Id,
                                                                OwnerUserId = authtokenSwoop.UserId
                                                            };

                                                            newSite.Save();
                                                            companyId = newSite.CompanyId;
                                                            accountId = newSite.AccountId;

                                                            logger.Warn(masterAccountId,
                                                                "MissingSite",
                                                                "Resolved; Added SwoopSite mapping automatically (RateAgreement).",
                                                                jsonObj.Partner?.Company?.Id,
                                                                ra.Id,
                                                                jsonObj.Id, qi.CompanyId, new
                                                                {
                                                                    json = newSite.ToJson()
                                                                },
                                                                callRequestId: cr?.CallRequestId,
                                                                queueItemId: qi.QueueItemId,
                                                                poNumber: jsonObj.AuthorizationNumber.ToString());
                                                        }
                                                    }
                                                }
                                            }
                                            else if (jsonObj.Partner?.Site?.Id != null)
                                            {
                                                var ess = SwoopSite.GetBySiteId(
                                                    ss.SwoopSessionId,
                                                    jsonObj.Partner?.RateAgreement?.Id);

                                                if (ess != null)
                                                {
                                                    companyId = ess.CompanyId;
                                                    accountId = ess.AccountId;
                                                }
                                                else
                                                {
                                                    var sharedCompanies = Company.SharedCompany.GetByCompanyId(companyId);

                                                    var companies = (await Task.WhenAll(sharedCompanies
                                                        .Select(o => Company.Company.GetByIdAsync(o.SharedCompanyId)))).ToList();

                                                    if (companies.Count() > 1)
                                                    {
                                                        var parent = sharedCompanies.First().CompanyId;

                                                        foreach (var x in companies)
                                                        {
                                                            if (x.Latitude == 0)
                                                            {
                                                                var geocoded = await GeocodeHelper.Geocode(x.GetComposedAddress());
                                                                if (geocoded != null && geocoded.Latitude != 0)
                                                                {
                                                                    x.Latitude = geocoded.Latitude;
                                                                    x.Longitude = geocoded.Longitude;
                                                                    await x.Save();
                                                                }
                                                            }
                                                        }
                                                        var ra = (await srcl.GetSites(ss.AccessToken))
                                                            .FirstOrDefault(o => o.Id == jsonObj.Partner.Site.Id);

                                                        var closest = SwoopSite.GetClosestCompanies(parent, ra.Latitude.Value, ra.Longitude.Value);

                                                        var result = closest.FirstOrDefault();

                                                        if (result != null && result.AccountId != 0)
                                                        {
                                                            var newSite = new SwoopSite()
                                                            {
                                                                SwoopSessionId = ss.SwoopSessionId,
                                                                CompanyId = result.CompanyId,
                                                                AccountId = result.AccountId,
                                                                SiteId = ra.Id,
                                                                OwnerUserId = authtokenSwoop.UserId
                                                            };

                                                            newSite.Save();
                                                            companyId = newSite.CompanyId;
                                                            accountId = newSite.AccountId;

                                                            logger.Warn(masterAccountId,
                                                                "MissingSite",
                                                                "Resolved; Added SwoopSite mapping automatically.",
                                                                jsonObj.Partner?.Company?.Id,
                                                                ra.Id,
                                                                jsonObj.Id, qi.CompanyId, new
                                                                {
                                                                    json = newSite.ToJson()
                                                                },
                                                                callRequestId: cr?.CallRequestId,
                                                                queueItemId: qi.QueueItemId,
                                                                poNumber: jsonObj.AuthorizationNumber.ToString());
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    Entry e = null;
                                    e = await Entry.GetByPurchaseOrderNumberAsync(accountId, poNumber);

                                    if (e == null)
                                    {
                                        e = new Entry() { CompanyId = companyId };
                                        await ApplyRoundRobinDispatcherLogicAsync(qi, e, jsonObj.AuthorizationNumber.ToString(), msg.SessionId.ToString(), site?.SiteId);
                                    }
                                    else
                                    {
                                        logger.LogEvent("{0}: Found existing towbook call for PO {1}/ResponseID {2}... Call #{3}... we're going to update this one.",
                                            qi.CompanyId, LogLevel.Warn, sourceMessage.Message.MessageId, poNumber, jsonObj.Id, e.CallNumber);
                                    }

                                    e.AccountId = accountId;
                                    e.PurchaseOrderNumber = poNumber;

                                    if (e.Account?.DefaultPriority == 1)
                                        e.Priority = Entry.EntryPriority.High;

                                    if (jsonObj.Service.ScheduledFor != null)
                                        e.ArrivalETA = jsonObj.Service.ScheduledFor.Value.ToLocalTime();
                                    else if (jsonObj.Eta.Current != null)
                                    {
                                        e.ArrivalETA = jsonObj.Eta.Current.Value.ToLocalTime();
                                    }
                                    else if (swoopDispatch.Eta.GetValueOrDefault() > 0)
                                        e.ArrivalETA = cr.RequestDate.AddMinutes(swoopDispatch.Eta.GetValueOrDefault());

                                    e.ReasonId = await ReasonHelper.DetermineReasonId(e.Account.MasterAccountId, companyId, cr.ServiceNeeded);

                                    void addNote(string line, bool top = false)
                                    {
                                        if (string.IsNullOrWhiteSpace(line))
                                            return;

                                        if (e.Notes == null || !e.Notes.Contains(line))
                                        {
                                            if (e.Notes == null)
                                                e.Notes = line + "\n";
                                            else
                                            {
                                                if (top)
                                                    e.Notes = line + e.Notes.Trim('\n') + "\n";
                                                else
                                                    e.Notes += "\n" + line.Trim('\n');
                                            }
                                        }
                                    }

                                    if (e.ReasonId == 1635)
                                    {
                                        var toAdd = "Service Needed: " + cr.ServiceNeeded;
                                        addNote(toAdd);
                                    }

                                    EntryAsset asset = null;
                                    if (e.Assets != null)
                                        asset = e.Assets.FirstOrDefault();

                                    if (asset == null)
                                        asset = new EntryAsset() { BodyTypeId = 1 };

                                    if (asset.BodyTypeId == 0)
                                        asset.BodyTypeId = 1;

                                    if (jsonObj.Vehicle.Year != 0)
                                        asset.Year = jsonObj.Vehicle.Year;

                                    if (jsonObj.Vehicle.Make != null)
                                        asset.Make = jsonObj.Vehicle.Make;

                                    if (jsonObj.Vehicle.Model != null)
                                        asset.Model = jsonObj.Vehicle.Model;

                                    if (!string.IsNullOrWhiteSpace(jsonObj.Vehicle.Vin))
                                        asset.Vin = jsonObj.Vehicle.Vin;

                                    if (!string.IsNullOrWhiteSpace(jsonObj.Vehicle.License))
                                        asset.LicenseNumber = jsonObj.Vehicle.License;

                                    asset.ColorId = GetColorIdByName(jsonObj.Vehicle.Color);

                                    if (!string.IsNullOrWhiteSpace(jsonObj.Vehicle.Odometer))
                                    {
                                        if (int.TryParse(jsonObj.Vehicle.Odometer, out var odometer))
                                            asset.Odometer = odometer;
                                    }

                                    e.TowSource = jsonObj.Location.ServiceLocation.Address;

                                    var pickup = e.Waypoints.FirstOrDefault(o => o.Title == "Pickup");
                                    var dest = e.Waypoints.FirstOrDefault(o => o.Title == "Destination");

                                    if (jsonObj.Location.ServiceLocation != null)
                                    {
                                        if (pickup == null)
                                        {
                                            pickup = new EntryWaypoint() { Title = "Pickup", Position = 1 };
                                            e.Waypoints.Add(pickup);
                                        }

                                        pickup = await jsonObj.Location.ServiceLocation.ToWaypoint(pickup);

                                        if (!string.IsNullOrWhiteSpace(pickup.Address))
                                            e.TowSource = pickup.Address;
                                    }

                                    if (jsonObj.Location.DropoffLocation != null)
                                    {
                                        if (dest == null)
                                        {
                                            dest = new EntryWaypoint() { Title = "Destination", Position = 2 };
                                            e.Waypoints.Add(dest);
                                        }

                                        dest = await jsonObj.Location.DropoffLocation.ToWaypoint(dest);

                                        if (!string.IsNullOrWhiteSpace(dest.Address))
                                            e.TowDestination = dest.Address;
                                    }


                                    if (e.CreateDate == DateTime.MinValue)
                                        e.CreateDate = DateTime.Now;

                                    if (ss.CompanyId == 105272)
                                    {
                                        if (!e.Contacts.Any(o => o.Name.ToLowerInvariant() == "agero (swoop) dispatch"))
                                            e.Contacts.Add(new Dispatch.EntryContact() { Name = "Agero (Swoop) Dispatch", Phone = "**********" });

                                        addNote("Your driver must have GPS enabled in Towbook to confirm that the " +
                                            (e.TowDestination != null ? "pickup and tow destination was reached." : "service location was reached. ") +
                                            "\nIf you have any questions, Agero (Swoop) can be reached at " + Core.FormatPhone("**********"));

                                        e.SetAttribute(Dispatch.AttributeValue.BUILTIN_BILLING_NOTES,
                                            $"Once this service is fulfilled and call status is set to Completed, a one-time credit card number will appear here for payment.\n " +
                                            $"If a GOA results, the amount will be {jsonObj.OonProviderAmount.OonGoaAmount.GetValueOrDefault().ToString("C")} upon Agero’s confirmation that the customer has left the scene.\n" +
                                            $"Please contact Agero (Swoop) at: {Core.FormatPhone("**********")} with any questions.");

                                        if (!e.InvoiceItems.Any(o => o.Name == OON_LINE_ITEM_NAME))
                                            e.InvoiceItems.Add(new Dispatch.InvoiceItem()
                                            {
                                                CustomName = OON_LINE_ITEM_NAME,
                                                Quantity = 1,
                                                CustomPrice = jsonObj.OonProviderAmount.OonFullAmount
                                            });
                                    }

                                    if (jsonObj.CoverageNotes != null && jsonObj.CoverageNotes.Any())
                                    {
                                        addNote("Coverage Notes: " + string.Join("\n", jsonObj.CoverageNotes).Trim('\n'));
                                    }

                                    if (jsonObj.VehicleReleasePacket != null)
                                    {
                                        if (!string.IsNullOrWhiteSpace(jsonObj.VehicleReleasePacket?.StorageYardName))
                                            addNote("Storage Yard Name: " + jsonObj.VehicleReleasePacket.StorageYardName);
                                        if (!string.IsNullOrWhiteSpace(jsonObj.VehicleReleasePacket?.PaymentTotal))
                                            addNote("Release Fee: " + jsonObj.VehicleReleasePacket.PaymentTotal);
                                        if (!string.IsNullOrWhiteSpace(jsonObj.VehicleReleasePacket?.PaymentType))
                                            addNote("Release Payment Type: " + jsonObj.VehicleReleasePacket.PaymentType);
                                    }


                                    if (!string.IsNullOrWhiteSpace(jsonObj.Notes?.Customer))
                                    {
                                        var customerNotes = jsonObj.Notes.Customer;

                                        const string toRemove = "Hey, Service Provider - Interested in making an easy $50? Refer a qualified Service Provider to join our network: https://info.agero.com/sp-referral";

                                        if (customerNotes.Contains(toRemove))
                                            customerNotes = customerNotes.Replace(toRemove, "");

                                        if (customerNotes.Trim() == "From Customer:")
                                            customerNotes = "";

                                        if (!string.IsNullOrWhiteSpace(customerNotes))
                                            addNote("Customer Notes: " + customerNotes);

                                        if (!string.IsNullOrWhiteSpace(jsonObj.Notes.Internal))
                                            addNote("Internal Notes: " + jsonObj.Notes.Internal);

                                        if (!string.IsNullOrWhiteSpace(jsonObj.Notes.Provider))
                                            addNote("Provider Notes: " + jsonObj.Notes.Provider);
                                    }

                                    if (!string.IsNullOrWhiteSpace(jsonObj.Service?.ClassType?.Name))
                                    {
                                        addNote("Class Type: " + jsonObj.Service?.ClassType?.Name);
                                    }

                                    if (!string.IsNullOrWhiteSpace(jsonObj.Location.ServiceLocation?.LocationType))
                                    {
                                        addNote("Breakdown Location Type: " + jsonObj.Location.ServiceLocation.LocationType);
                                    }

                                    if (!string.IsNullOrWhiteSpace(jsonObj.Location.DropoffLocation?.LocationType))
                                    {
                                        addNote("Destination Location Type: " + jsonObj.Location.DropoffLocation.LocationType);
                                    }

                                    if (jsonObj.Service.Answers.Edges.Any())
                                    {
                                        foreach (var answer in jsonObj.Service.Answers.Edges)
                                        {
                                            var line = answer.Node.Question + ": " + answer.Node.Answer + "\n";

                                            addNote(line);
                                        }
                                    }

                                    if (jsonObj.Partner?.RateAgreement?.AnchorLocation != null && masterAccountId == MasterAccountTypes.Swoop)
                                    {
                                        var existingBN = e.GetAttribute(Dispatch.AttributeValue.BUILTIN_BILLING_NOTES) ?? "";
                                        if (!existingBN.Contains("Rate Agreement:"))
                                            e.SetAttribute(Dispatch.AttributeValue.BUILTIN_BILLING_NOTES,
                                                "Rate Agreement: " + jsonObj.Partner.RateAgreement.ToString() +
                                                (!string.IsNullOrWhiteSpace(existingBN) ? "\n" + existingBN : ""));
                                    }

                                    if (e.Assets == null || e.Assets.Count == 0)
                                    {
                                        e.Assets = new System.Collections.ObjectModel.Collection<EntryAsset>();
                                        e.Assets.Add(asset);
                                    }

                                    var contactName = jsonObj.Location.PickupContact?.Name;
                                    var contactPhoneNumber = Core.FormatPhoneWithDashesOnly(jsonObj.Location.PickupContact?.Phone);

                                    if (string.IsNullOrWhiteSpace(contactName) && jsonObj.Customer != null)
                                    {
                                        contactName = jsonObj.Customer.Name ?? jsonObj.Customer.FullName;
                                        contactPhoneNumber = Core.FormatPhoneWithDashesOnly(jsonObj.Customer.Phone);
                                    }

                                    EntryContact c = e.Contacts
                                        .FirstOrDefault(o => o.Name == contactName ||
                                                             Core.FormatPhoneWithDashesOnly(o.Phone) == contactPhoneNumber);

                                    bool newContact = false;
                                    if (c == null)
                                    {
                                        c = new EntryContact() { Name = contactName };
                                        e.Contacts.Add(c);
                                        newContact = true;
                                    }

                                    c.Phone = contactPhoneNumber;
                                    c.DispatchEntryId = e.Id;

                                    await e.Save(false, authtokenSwoop);

                                    if (newContact)
                                        await CheckForRoadsideFeatureAndAutoInvite(e, c);

                                    await AutoDispatch.AutoDispatchServiceBusHandler.Send(e);

                                    if (DateTime.Now.Hour >= 22 ||
                                        DateTime.Now.Hour < 8)
                                    {
                                        var cdsc = CompanyDispatchStatusCurrent.GetByCompanyId(e.CompanyId);
                                        if (cdsc != null)
                                        {
                                            var statusNode = new EntryNote()
                                            {
                                                DispatchEntryId = e.Id,
                                                OwnerUserId = 1,
                                                Content = "Auto-Accepted because: " +
                                                string.Join(",", cdsc.Reasons)
                                            };

                                            await statusNode.SaveAsync();
                                        }
                                    }

                                    return e;
                                },
                                alreadyLocked: async () =>
                                {
                                    logger.Error(MasterAccountTypes.Swoop, "CallCreatedError",
                                        "Lock already exists, pausing 250ms.",
                                        locationId: jsonObj?.Site?.Id,
                                        companyId: qi.CompanyId,
                                        dispatchId: jsonObj.Id,
                                        data: new
                                        {
                                            accountId = qi.AccountId.GetValueOrDefault()
                                        },
                                        callRequestId: cr?.CallRequestId,
                                        queueItemId: qi.QueueItemId,
                                        poNumber: poNumber);

                                    await Task.Delay(250);

                                    return true;
                                });

                            if (fe == null)
                            {
                                logger.Error(masterAccountId, "CallCreatedError",
                                    "Creation of call FAILED",
                                    locationId: locationId,
                                    companyId: qi.CompanyId,
                                    dispatchId: jsonObj.Id,
                                    callRequestId: cr?.CallRequestId,
                                    queueItemId: qi.QueueItemId,
                                    poNumber: poNumber);

                                await sourceMessage.CompleteAsync();
                                return false;
                            }
                            else
                            {
                                if (cr == null)
                                    return false;

                                cr.DispatchEntryId = fe?.Id;
                                await cr.Save();

                                qi.CallRequestId = cr.CallRequestId;
                                DigitalDispatchService.LogAction(qi);

                                await cr.UpdateStatus(CallRequestStatus.Accepted, po: fe.PurchaseOrderNumber);
                                DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);

                                await fe.AssignRequestDriverToEntry();

                                logger.Info(masterAccountId,
                                    "CallCreated",
                                    "Created new towbook call for Swoop",
                                    locationId: locationId,
                                    companyId: qi.CompanyId,
                                    dispatchId: jsonObj.Id,
                                    data: new
                                    {
                                        accountId = fe?.AccountId,
                                        siteId = jsonObj?.Site?.Id,
                                        waypoints = fe?.Waypoints.Select(o => new { o.Address, o.Latitude, o.Longitude })
                                    },
                                    callId: fe?.Id,
                                    callRequestId: cr?.CallRequestId,
                                    queueItemId: qi.QueueItemId,
                                    poNumber: fe?.PurchaseOrderNumber,
                                    callNumber: fe?.CallNumber);
                            }

                            await sourceMessage.CompleteAsync();
                        }
                        return true;
                    }

                    return await handleAccept();

                case DigitalDispatchActionQueueItemType.IncomingCallExpired:
                    void handleCallExpired()
                    {
                        var ss = SwoopSession.GetById(msg.SessionId);
                        int masterAccountId = MasterAccountTypes.Swoop;

                        if (ss.CompanyId == 105272)
                            masterAccountId = MasterAccountTypes.OonSwoop;

                        logger.Info(masterAccountId,
                            "CallExpired",
                            "Call Expired event",
                            companyId: qi.CompanyId,
                            dispatchId: jsonObj.Id,
                            queueItemId: qi.QueueItemId,
                            poNumber: jsonObj.AuthorizationNumber.ToString());
                    }
                    handleCallExpired();
                    break;
            }

            await sourceMessage.CompleteAsync();
            return true;
        }

        public static async Task HandleOonSwoop(
            DigitalDispatchActionQueueItem qi,
            SwoopMessage msg,
            ProcessMessageEventArgs sourceMessage)
        {
            const int masterAccountId = MasterAccountTypes.OonSwoop;
            logger.Info(masterAccountId,
                "CallReceived",
                "Incoming Job Offer",
                msg.SessionId.ToString(), null, msg.SessionId.ToString(),
                qi.CompanyId,
                new
                {
                    request = msg
                },
                queueItemId: qi.QueueItemId);


            if (masterAccountId == MasterAccountTypes.OonSwoop)
            {
                var client = new SwoopRestClient();
                var swoopCall = JsonConvert.DeserializeObject<Integrations.MotorClubs.Swoop.Model.SwoopJob>(msg.JsonData);
                var ss = Integrations.MotorClubs.Swoop.SwoopSession.GetById(msg.SessionId);

                swoopCall = await client.GetJobById(swoopCall.Id, ss.AccessToken, true) ?? swoopCall;

                if (swoopCall.Location?.DropoffLocation?.Latitude != null &&
                    swoopCall.Location.DropoffLocation.Latitude != swoopCall.Location?.ServiceLocation?.Longitude)
                {
                    var loadedMileage = await OutOfNetworkIncoming.GetDistanceFromGoogleAsync(
                    swoopCall.Location.ServiceLocation.Latitude.GetValueOrDefault(),
                    swoopCall.Location.ServiceLocation.Longitude.GetValueOrDefault(),
                    swoopCall.Location.DropoffLocation.Latitude.GetValueOrDefault(),
                    swoopCall.Location.DropoffLocation.Longitude.GetValueOrDefault(), true); ;

                    if (loadedMileage > 0 &&
                        swoopCall.OonProviderAmount != null && 
                        swoopCall.OonProviderAmount.OonFullAmount > 0 &&
                        swoopCall.OonProviderAmount.OonFullAmount < loadedMileage)
                    {
                        await client.RejectJob(swoopCall.Id,
                            ss.AccessToken,
                            "Loaded Distance too far",
                            await FindByUserId(ss, 1));

                        logger.Info(masterAccountId,
                            "CallReceived",
                            "Incoming Job Offer Ignored due to Loaded Distance being < $1/mile",
                            msg.SessionId.ToString(), null, swoopCall.AuthorizationNumber.ToString(),
                            qi.CompanyId,
                            "{loadedMiles:" + loadedMileage + "}",
                            queueItemId: qi.QueueItemId);
                    }
                }

                // find providers
                var oon = await OutOfNetworkIncoming.FindNonSwoopProvidersByLatLong(
                    swoopCall.Location.ServiceLocation.Latitude.GetValueOrDefault(),
                    swoopCall.Location.ServiceLocation.Longitude.GetValueOrDefault(),
                    swoopCall.Service.Name);

                if (!oon.Any())
                {

                    // auto reject, no providers found.
                    await client.RejectJob(swoopCall.Id, 
                        ss.AccessToken,
                        "No Drivers Available",
                        await FindByUserId(ss, 1));

                    logger.Info(MasterAccountTypes.OonSwoop,
                        "CallReceived",
                        "Rejected - No available providers nearby",
                        swoopCall.Partner?.Company?.Id, 
                        swoopCall.Partner?.Site?.Id, swoopCall.Id, null,
                       new
                       {
                           latitude = swoopCall.Location.ServiceLocation.Latitude,
                           longitude = swoopCall.Location.ServiceLocation.Longitude
                       },
                       queueItemId: qi.QueueItemId,
                       poNumber: swoopCall.AuthorizationNumber.ToString());
                }
                else
                {
                    var requests = new List<CallRequest>();

                    foreach (var x in oon)
                    {
                        try
                        {
                            // Create or get account.
                            var account = await OutOfNetworkIncoming.OonCreateAccount(x.CompanyId, x.AccountId, MasterAccountTypes.OonSwoop,
                                qi.QueueItemId);

                            if (account != null && account.Status != AccountStatus.Inactive)
                            {
                                if (x.AccountId == 0)
                                    x.AccountId = account.Id;

                                x.AccountId = account.Id;

                                var ooncr = await OutOfNetworkIncoming.OonSwoopCreateCallRequest(account, msg, swoopCall, x);

                                if (ooncr != null)
                                    requests.Add(ooncr);
                            }
                        }
                        catch (Exception ry)
                        {
                            logger.Info(masterAccountId,
                                "CallReceived",
                                "Incoming Job Offers Creation Failure",
                                swoopCall.Partner?.Company?.Id,
                                swoopCall.Partner?.Site?.Id,
                                swoopCall.Id,
                                x.CompanyId,
                                new
                                {
                                    exception = ry
                                },
                                queueItemId: qi.QueueItemId,
                                poNumber: swoopCall.AuthorizationNumber.ToString());
                        }
                    }

                    logger.Info(masterAccountId,
                        "CallReceived",
                        "Incoming Job Offers Created",
                        "oon-incoming", null, swoopCall.Id, null,
                        new
                        {
                            callRequests = requests.Select(o => new { o.CallRequestId, o.CompanyId }).ToArray(),
                            latitude = swoopCall.Location.ServiceLocation.Latitude,
                            longitude = swoopCall.Location.ServiceLocation.Longitude,
                            providers = oon
                        },
                        queueItemId: qi.QueueItemId,
                        poNumber: swoopCall.AuthorizationNumber.ToString());
                }

                // and then fan out new service bus events for each provider offer
                // similar ot what we do in OutOfNetworkIncoming. 

                DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
            }
        }

        public static async Task DeleteSwoopUserAsync(SwoopSession ss, int towbookUserId, string swoopUserKey)
        {
            var towbookUser = await User.GetByIdAsync(towbookUserId);

            var ukv = SwoopUser.GetByUserId(ss.SwoopSessionId, towbookUserId);
            
            if (ukv != null && ukv.SwoopUserKey == swoopUserKey)
            {
                await SwoopUser.DeleteAsync(ss.SwoopSessionId, swoopUserKey);

                if (SwoopRestClient._users != null)
                    SwoopRestClient._users.TryRemove(ss.AccessToken + ":users", out _);
            }
        }

        public static async Task<string> FindByUserId(
            SwoopSession ss,
            int id,
            bool returnId = true,
            bool needDriverRole = false,
            bool needDispatcherRole = false,
            string forceRole = null)
        {
            if (id == 0)
                return null;

            var client = new SwoopRestClient();

            if (ss.CompanyId == 10000)
                client = SwoopRestClient.Staging();

            var towbookUser = await User.GetByIdAsync(id);

            var ukv = SwoopUser.GetByUserId(ss.SwoopSessionId, id);

            // TODO: store whether user has dispatcher role, dirver role, admin role, in SwoopUser table so we dont have to get the list of users from swoop. 
            if (ukv != null && !needDispatcherRole && !needDriverRole)
            {
                return returnId ? ukv.SwoopUserKey : ukv.SwoopName;
            }

            using (var configLock = await Core.GetRedisDatabaseAsync().AcquireLockAsync("swoopUser:" + ss.SwoopSessionId + ":" + id))
            {
                var swoopUsers = await client.GetUsersAsync(ss.AccessToken);
                var swoopUser = swoopUsers.Where(o => o.Name != null && towbookUser.FullName != null &&
                                                      o.Name.Replace(",", "").Replace("-", "").Replace("  ", " ").Trim().Equals(
                                                     towbookUser.FullName.Replace(",", "").Replace("-", "").Replace("  ", " ").Trim(), StringComparison.OrdinalIgnoreCase))
                    .FirstOrDefault();

                string neededRole = null;

                if (needDriverRole)
                    neededRole = "driver";

                if (needDispatcherRole)
                    neededRole = "dispatcher";


                if (forceRole != null)
                    neededRole = forceRole;

                if (swoopUser != null)
                {
                    if (neededRole != null && !swoopUser.Roles.Contains(neededRole))
                    {
                        Console.WriteLine("updated user to dispatcher");
                        await client.UpdateUser(ss.AccessToken, new SwoopRestClient.SwoopUserModel()
                        {
                            Id = swoopUser.Id,
                            Roles = swoopUser.Roles.Union(new string[] { neededRole }).ToArray()
                        }, swoopUsers.Where(o => o.Roles.Contains("admin")).DefaultIfEmpty(swoopUsers.FirstOrDefault()).FirstOrDefault().Id);
                    }

                    var newUser = SwoopUser.NewUser(ss, id, swoopUser);
                    newUser.Save();

                    if (returnId)
                        return swoopUser.Id;
                    else
                        return swoopUser.Name;
                }
                else
                {
                    var swoopRole = "driver";

                    switch (towbookUser.Type)
                    {
                        case User.TypeEnum.SystemAdministrator: swoopRole = "admin"; break;
                        case User.TypeEnum.Manager: swoopRole = "admin"; break;
                        case User.TypeEnum.Dispatcher: swoopRole = "dispatcher"; break;
                        case User.TypeEnum.Driver: swoopRole = "driver"; break;
                    }

                    if (forceRole != null)
                        swoopRole = forceRole;

                    var addedUser = await client.AddUser(ss.AccessToken,
                        new SwoopRestClient.SwoopUserModel()
                        {
                            Name = towbookUser.FullName,
                            Roles = new string[] { swoopRole }.Union(new[] { "driver" }).ToArray()
                        },
                        swoopUsers.Where(o => o.Roles.Contains("admin")).DefaultIfEmpty(swoopUsers.FirstOrDefault()).FirstOrDefault().Id,
                        towbookUser);

                    if (returnId)
                        return addedUser.Id;
                    else
                        return addedUser.Name;
                }
            }
        }

        public static async Task HandleSwoopQueueOutgoingMessage(
            DigitalDispatchActionQueueItem qi,
            dynamic jsonObj,
            ProcessMessageEventArgs sourceMessage)
        {
            CallRequest cr = CallRequest.GetById(Convert.ToInt32(jsonObj.CallRequestId ?? jsonObj.Id));
            if (cr == null)
            {
                logger.Log(LogLevel.Error, "CR" + jsonObj.Id + ": Couldn't find CallRequestId.");
                logger.Log(LogLevel.Error, "CR" + jsonObj.Id + ": " + JsonExtensions.ToJson(jsonObj));

                await sourceMessage.CompleteAsync();
                return;
            }

            var sd = SwoopDispatch.GetByCallRequestId(cr.CallRequestId);
            var ss = SwoopSession.GetById(sd.SwoopSessionId);

            if (ss == null)
            {
                var swoopSite = SwoopSite.GetByAccountId(cr.AccountId);
                if (swoopSite != null)
                {
                    ss = SwoopSession.GetById(swoopSite.SwoopSessionId);
                }
            }

            int masterAccountId = ss.CompanyId == 105272 ? MasterAccountTypes.OonSwoop : MasterAccountTypes.Swoop;

            var client = new SwoopRestClient();

            if (ss.CompanyId == 10000)
                client = SwoopRestClient.Staging();

            async Task<string> FindByTruckId(int id, bool returnId = true)
            {
                var truck = await Truck.GetByIdAsync(id);
                var sv = SwoopVehicle.GetByTruckId(ss.SwoopSessionId, id);
                if (sv != null)
                    return returnId ? sv.SwoopVehicleKey : sv.SwoopName;

                var swoopVehicles = await client.GetVehicles(ss.AccessToken);
                var swoopVehicle = swoopVehicles.Where(o => o.Name.Replace(",", "").Replace("-", "").Replace("  ", " ").Trim() == 
                truck.Name.Replace(",", "").Replace("-", "").Replace("  ", " ").Trim()).FirstOrDefault();
                if (swoopVehicle != null)
                {
                    if (returnId)
                        return swoopVehicle.Id;
                    else
                        return swoopVehicle.Name;
                }
                else
                {
                    var user = await client.GetAdminUser(ss);

                    var av = await client.AddVehicle(ss.AccessToken, new SwoopRestClient.SwoopVehicleModel()
                    {
                        Name = truck.Name
                    }, user.Id);

                    var nsv = SwoopVehicle.NewVehicle(ss, 0, truck.Id, av);
                    nsv.Save();

                    if (returnId)
                        return av.Id;
                    else
                        return av.Name;
                }
            }


            Integrations.MotorClubs.Swoop.Model.SwoopJob dispatch = null;
            if (qi.Type == DigitalDispatchActionQueueItemType.IncomingCallOffered)
            {
                dispatch = JsonConvert.DeserializeObject<Integrations.MotorClubs.Swoop.Model.SwoopJob>(sd.OfferJson);
            }
            else
            {
                var sw = SwoopDispatch.GetByCallRequestId((int)(jsonObj.Id ?? jsonObj.CallRequestId));
                if (sw == null)
                {
                    logger.Error(masterAccountId,
                        Enum.GetName(typeof(DigitalDispatchActionQueueItemType), qi.Type),
                        "Missing SwoopDispatch, cannot process.",
                        ss.SwoopSessionId.ToString(), null, null, qi.CompanyId,
                        new
                        {
                            json = JsonExtensions.ToJson(jsonObj)
                        },
                        queueItemId: qi.QueueItemId);

                    await sourceMessage.CompleteAsync();
                    return;
                }
                else
                {
                    dispatch = JsonConvert.DeserializeObject<Integrations.MotorClubs.Swoop.Model.SwoopJob>(sw.OfferJson);
                }
            }

            if (jsonObj.OwnerUserId != null)
            {
                cr.OwnerUserId = Convert.ToInt32(jsonObj.OwnerUserId);
            }
            else
            {
                cr.OwnerUserId = qi.OwnerUserId;
            }

            switch (qi.Type)
            {
                case DigitalDispatchActionQueueItemType.OutgoingAcceptCall:
                    string responseAccept = "";
                    try
                    {
                        var acceptBody = JsonConvert.DeserializeObject<CallRequestAcceptRequestBody>(qi.JsonObject);

                        sd.Eta = acceptBody.Eta;
                        sd.Save();

                        async Task<bool> doAccept()
                        {

                            logger.Info(masterAccountId,
                               "Accept",
                               "Outgoing Job Accept",
                               ss.AccessToken, null, dispatch.Id, qi.CompanyId,
                               new
                               {
                                   json = JsonExtensions.ToJson(jsonObj)
                               },
                               callRequestId: sd.CallRequestId,
                               queueItemId: qi.QueueItemId,
                               poNumber: dispatch.AuthorizationNumber.ToString());

                            // find swoop user by owneruserid. 
                            // if not exist, create and store

                            var acceptReason = MasterAccountReason.GetById(acceptBody.MasterAccountReasonId);

                            string userId = null;

                            try
                            {
                                if (ss.CompanyId == 105272)
                                {
                                    Core.RedisIncrementKey("oon_swoop_accept:" + dispatch.AuthorizationNumber);

                                    if (Convert.ToInt32(Core.GetRedisValue("oon_swoop_accept:" + dispatch.AuthorizationNumber)) > 1)
                                    {
                                        await cr.UpdateStatus(CallRequestStatus.AnotherProviderResponded);
                                        throw new Exception("OonSwoop: CRITICAL WARNING: Already Accepted by another provider in Towbook. Duplicate accept avoided.");
                                    }

                                    // keep it for 24 hours.
                                    Core.GetRedisDatabase().KeyExpire("oon_swoop_accept:" + dispatch.AuthorizationNumber, TimeSpan.FromHours(24));

                                    userId = await FindByUserId(ss, qi.OwnerUserId.Value, forceRole: "dispatcher");
                                }
                                else
                                    userId = await FindByUserId(ss, qi.OwnerUserId.Value);

                                if (jsonObj.DriverId != null)
                                {
                                    MotorClubDispatchingService.SaveDriversForCallRequest(cr.CallRequestId, Convert.ToInt32(jsonObj.DriverId));
                                }
                                var resp = await client.AcceptJob(dispatch.Id, ss.AccessToken, DateTime.UtcNow.AddMinutes(sd.Eta.GetValueOrDefault()), userId, acceptReason?.Name);

                                logger.Info(masterAccountId,
                                    "Accept",
                                    "Accepted Job",
                                    ss.AccessToken, dispatch.Site?.Id, dispatch.Id, qi.CompanyId,
                                    new
                                    {
                                        json = resp.ToJson(),
                                        userId = userId,
                                        status = resp.Status
                                    },
                                    callRequestId: sd.CallRequestId,
                                    queueItemId: qi.QueueItemId,
                                    poNumber: dispatch.AuthorizationNumber.ToString(),
                                    ownerUserId: qi.OwnerUserId);

                                // if oon, mark others as another provider responded. 
                                var others = SwoopDispatch.OonGetByDispatchId(sd.OfferId, sd.SwoopSessionId).Where(o => o.CallRequestId != sd.CallRequestId);
                                foreach (var x in others)
                                {
                                    var otherCr = CallRequest.GetById(x.CallRequestId);
                                    if (otherCr != null && (otherCr.Status == CallRequestStatus.None ||
                                        otherCr.Status == CallRequestStatus.Accepting ||
                                        otherCr.Status == CallRequestStatus.AcceptSent))
                                        await otherCr.UpdateStatus(CallRequestStatus.AnotherProviderResponded);
                                }
                            }
                            catch (SwoopException e)
                            {
                                logger.Error(masterAccountId,
                                    "Accept",
                                    "Accepting Job Failed - " + e.Message,
                                    ss.AccessToken, dispatch.Site?.Id, dispatch.Id,
                                    qi.CompanyId,
                                    new
                                    {
                                        error = e.Message,
                                        text = e.Request,
                                        userId = e.UserId
                                    },
                                    callRequestId: sd.CallRequestId,
                                    queueItemId: qi.QueueItemId,
                                    ownerUserId: qi.OwnerUserId);


                                if (e.Message == "Invalid user supplied for X-Authenticated-User")
                                {
                                    await DeleteSwoopUserAsync(ss, qi.OwnerUserId.Value, userId);
                                }

                                return false;
                            }

                            return true;
                        }

                        if (cr?.Status == CallRequestStatus.Accepted)
                        {
                            logger.Info(masterAccountId,
                               "Accept",
                               "Ignoring Outgoing Job Accept - Job is already accepted",
                               ss.AccessToken, null, dispatch.Id, qi.CompanyId,
                               new
                               {
                                   json = JsonExtensions.ToJson(jsonObj)
                               },
                               callRequestId: sd.CallRequestId,
                               queueItemId: qi.QueueItemId,
                               poNumber: dispatch.AuthorizationNumber.ToString());
                        }
                        else
                        {
                            if (!await doAccept())
                            {
                                await cr.UpdateStatus(CallRequestStatus.AcceptFailed);
                            }
                            else
                            {
                                await cr.UpdateStatus(CallRequestStatus.AcceptSent);
                            }
                        }


                        qi.CallRequestId = cr.CallRequestId;
                        DigitalDispatchService.LogAction(qi);

                        await sourceMessage.CompleteAsync();

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    }
                    catch (Exception exc)
                    {
                        logger.Log(LogLevel.Error, "Error accepting Towbook call..." + exc.Message + "\n" + exc.ToJson() + "...Response=" + (responseAccept ?? ""));
                        await sourceMessage.DeadLetterAsync("Attempted to accept callRequestId " + cr.CallRequestId + ", but  error occurred",
                            exc.ToJson(true));
                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                    }
                    break;

                case DigitalDispatchActionQueueItemType.OutgoingRejectCall:

                    async Task<bool> doRefuse()
                    {
                        logger.Info(masterAccountId,
                            "Reject",
                            "Outgoing Job Reject",
                            ss.AccessToken, null, dispatch.Id, qi.CompanyId,
                            new
                            {
                                json = JsonExtensions.ToJson(jsonObj)
                            },
                            callRequestId: sd.CallRequestId,
                            queueItemId: qi.QueueItemId,
                            poNumber: dispatch.AuthorizationNumber.ToString());

                        // find swoop user by owneruserid. 
                        // if not exist, create and store

                        MasterAccountReason rejectReason = MasterAccountReason.GetById(Convert.ToInt32(jsonObj.MasterAccountReasonId));

                        try
                        {
                            string forceRole = null;
                            if (masterAccountId == MasterAccountTypes.OonSwoop)
                            {
                                if (SwoopDispatch.OonGetByDispatchId(dispatch.Id, ss.SwoopSessionId).Where(r => r.CallRequestId != sd.CallRequestId).Any())
                                {
                                    logger.Info(masterAccountId,
                                        "Reject",
                                        "Skipped Job Reject - there other providers.",
                                        ss.AccessToken, null, dispatch.Id, qi.CompanyId,
                                        callRequestId: sd.CallRequestId,
                                        queueItemId: qi.QueueItemId,
                                        poNumber: dispatch.AuthorizationNumber.ToString());
                                    // won't send reject. 
                                    return true;
                                }

                                forceRole = "dispatcher";
                            }

                            var userId = await FindByUserId(ss, qi.OwnerUserId.Value, forceRole: forceRole);

                            var resp = await client.RejectJob(dispatch.Id, ss.AccessToken, rejectReason.Code, userId);

                            logger.Info(masterAccountId,
                                "Reject",
                                "Rejected Job",
                                ss.AccessToken, null, dispatch.Id, qi.CompanyId,
                                new
                                {
                                    response = resp
                                },
                                callRequestId: sd.CallRequestId,
                                queueItemId: qi.QueueItemId,
                                poNumber: dispatch.AuthorizationNumber.ToJson());
                        }
                        catch (Exception e)
                        {
                            logger.Error(masterAccountId,
                                "Reject",
                                "Reject Job Failed",
                                ss.AccessToken, dispatch.Partner?.Site?.Id, dispatch.Id, qi.CompanyId,
                                new
                                {
                                    error = e.Message
                                },
                                callRequestId: sd.CallRequestId,
                                queueItemId: qi.QueueItemId);

                            return false;
                        }

                        return true;
                    }

                    if (await doRefuse())
                    {
                        await cr.UpdateStatus(CallRequestStatus.Rejected);
                    }
                    else
                    {
                        await cr.UpdateStatus(CallRequestStatus.RejectFailed);
                    }

                    qi.CallRequestId = cr.CallRequestId;
                    DigitalDispatchService.LogAction(qi);

                    await sourceMessage.CompleteAsync();
                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);

                    break;

                case DigitalDispatchActionQueueItemType.OutgoingStatusUpdate:
                case DigitalDispatchActionQueueItemType.OutgoingReassignJob:
                    {
                        async Task<bool> doStatusUpdate()
                        {
                            logger.Log(LogLevel.Info, "Towbook/UpdatingStatus/CallRequestId={0}/CaseId={1}", cr.CallRequestId, cr.PurchaseOrderNumber);

                            var company = await Company.Company.GetByIdAsync(cr.CompanyId);

                            if (cr.DispatchEntryId == null)
                            {
                                // it was never accepted, so no need to cancel it.
                                return true;
                            }

                            var en = Entry.GetByIdNoCache(cr.DispatchEntryId.Value);

                            var towbookTruck = await Truck.GetByIdAsync(en.TruckId);
                            var towbookDriver = await Driver.GetByIdAsync( (int)jsonObj.DriverId);

                            if (jsonObj.TruckId != null)
                                towbookTruck = await Truck.GetByIdAsync(Convert.ToInt32(jsonObj.TruckId));

                            if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingReassignJob)
                            {
                                logger.Log(LogLevel.Info, "Towbook/ReassignDriver/CallRequestId={0}/CaseId={1} ",
                                    cr.CallRequestId, cr.PurchaseOrderNumber, Convert.ToInt32(jsonObj.NewStatusId),
                                    JsonExtensions.ToJson(jsonObj));
                            }

                            Status newStatus = await Status.GetByIdAsync(Convert.ToInt32(jsonObj.NewStatusId));

                            string status = "";
                            if (newStatus.Id == Status.Dispatched.Id)
                                status = "Dispatched";
                            else if (newStatus.Id == Status.EnRoute.Id)
                                status = "EnRoute";
                            else if (newStatus.Id == Status.AtSite.Id)
                                status = "OnSite";
                            else if (newStatus.Id == Status.BeingTowed.Id)
                                status = "Towing";
                            else if (newStatus.Id == Status.DestinationArrival.Id)
                                status = "TowDestination";
                            else if (newStatus.Id == Status.Completed.Id)
                                status = "Completed";

                            // if newStatus.Id == Completed... Trigger VCC. 

                            var currentSwoopCall = await client.GetJobById(sd.OfferId, ss.AccessToken);

                            if (currentSwoopCall?.Status != null &&
                                currentSwoopCall.Status.Equals(status, StringComparison.OrdinalIgnoreCase))
                            {
                                logger.Info(masterAccountId,
                                    "StatusUpdate",
                                    "Status Update Ignored - Status with Swoop is already set to value passed.",
                                    ss.AccessToken, null, dispatch.Id, qi.CompanyId,
                                    new
                                    {
                                        status
                                    },
                                    en.Id,
                                    cr.CallRequestId,
                                    queueItemId: qi.QueueItemId);
                                
                                await TryApplyPayment();

                                return true;
                            }

                            if (status != "")
                            {
                                var swDriver = await FindByUserId(
                                    ss, towbookDriver?.UserId ?? 0, returnId: false);
                                var swDriverId = await FindByUserId(
                                    ss, towbookDriver?.UserId ?? 0, returnId: true, needDriverRole: true);

                                try
                                {
                                    if (status == "Dispatched")
                                    {
                                        string dispatcher = null;

                                        var performer = await User.GetByIdAsync(qi.OwnerUserId.GetValueOrDefault());
                                        if (performer?.Type == User.TypeEnum.Driver)
                                        {
                                            var swoopDispatcher = await client.GetDispatcherOrAdminUser(ss);
                                                
                                            if (swoopDispatcher != null)
                                                dispatcher = swoopDispatcher.Id;
                                        }
                                        else
                                            dispatcher = await FindByUserId(ss, qi.OwnerUserId.GetValueOrDefault(), needDispatcherRole: true);

                                        if (swDriver != null)
                                        {
                                            var sw = Stopwatch.StartNew();
                                            // If this fails, the job won't register in Swoop Truck Tracking and hurt our score.
                                            await client.AssignDriverToJob(ss.AccessToken,
                                                sd.OfferId,
                                                swDriver,
                                                dispatcher);

                                            logger.Info(masterAccountId,
                                                "StatusUpdate",
                                                "Assigned call to driver",
                                                ss.AccessToken, null, dispatch.Id, qi.CompanyId,
                                                callId: en.Id,
                                                callRequestId: cr.CallRequestId,
                                                queueItemId: qi.QueueItemId,
                                                poNumber: dispatch.AuthorizationNumber.ToString(),
                                                totalMilliseconds: sw.ElapsedMilliseconds);
                                        }

                                        if (towbookTruck != null && swDriverId != null)
                                        {
                                            var vehicleName = await FindByTruckId(towbookTruck.Id, false);

                                            var sw = Stopwatch.StartNew();

                                            // If this fails, the job won't register in Swoop Truck Tracking and hurt our score.
                                            await client.UpdateUserVehicleAsync(ss.AccessToken, vehicleName, swDriverId, swDriverId);

                                            logger.Info(masterAccountId,
                                                "StatusUpdate",
                                                "Assigned vehicle to driver",
                                                ss.AccessToken, null, dispatch.Id, qi.CompanyId,
                                                new
                                                {
                                                    vehicleName,
                                                    driverId = swDriverId
                                                },
                                                callId: en.Id,
                                                callRequestId: cr.CallRequestId,
                                                queueItemId: qi.QueueItemId,
                                                poNumber: dispatch.AuthorizationNumber.ToString(),
                                                callNumber: en.CallNumber,
                                                totalMilliseconds: sw.ElapsedMilliseconds);
                                        }
                                        else
                                        {
                                            logger.Warn(masterAccountId,
                                              "StatusUpdate",
                                              "Could not assign - No Driver associated with job in Towbook. Negatively impacts LBT Score",
                                              ss.AccessToken, null, dispatch.Id, qi.CompanyId,
                                              callId: en.Id,
                                              callRequestId: cr.CallRequestId,
                                              queueItemId: qi.QueueItemId,
                                              poNumber: dispatch.AuthorizationNumber.ToString(),
                                              callNumber: en.CallNumber,
                                              ownerUserId: qi.OwnerUserId);
                                        }
                                    }
                                    else
                                    {
                                        var sw = Stopwatch.StartNew();
                                        try
                                        {
                                            var sr = await client.UpdateStatus(ss.AccessToken, sd.OfferId, status,
                                                await FindByUserId(ss, qi.OwnerUserId.GetValueOrDefault()));

                                            logger.Info(masterAccountId,
                                                "StatusUpdate",
                                                "Updated Status",
                                                ss.AccessToken, null, dispatch.Id, qi.CompanyId,
                                                new
                                                {
                                                    response = sr
                                                },
                                                callId: en.Id,
                                                callRequestId: cr.CallRequestId,
                                                queueItemId: qi.QueueItemId,
                                                poNumber: dispatch.AuthorizationNumber.ToString(),
                                                callNumber: en.CallNumber,
                                                ownerUserId: qi.OwnerUserId,
                                                totalMilliseconds: sw.ElapsedMilliseconds);
                                        }
                                        catch (Exception e)
                                        {
                                            logger.Error(masterAccountId,
                                                "StatusUpdate",
                                                "Failed to Update Status",
                                                ss.AccessToken, null, dispatch.Id, qi.CompanyId,
                                                new
                                                {
                                                    errorMessage = e.Message,
                                                    exception = e
                                                },
                                                callId: en.Id,
                                                callRequestId: cr.CallRequestId,
                                                queueItemId: qi.QueueItemId,
                                                poNumber: dispatch.AuthorizationNumber.ToString(),
                                                callNumber: en.CallNumber,
                                                ownerUserId: qi.OwnerUserId,
                                                totalMilliseconds: sw.ElapsedMilliseconds);

                                            return false;
                                        }
                                        try
                                        {
                                            // send an extra breadcrumb event with each status update
                                            var ulhi = UserLocationHistoryItem.GetCurrentByUserId(towbookDriver.UserId);

                                            if (ulhi != null)
                                            {
                                                await client.UpdateDriverGps(
                                                    ss.AccessToken,
                                                    ulhi.Latitude,
                                                    ulhi.Longitude,
                                                    swDriverId);

                                                logger.Info(masterAccountId,
                                                    "StatusUpdateBreadcrumb",
                                                    "Sent extra breadcrumb with driverGps",
                                                    ss.AccessToken, null, dispatch.Id, qi.CompanyId,
                                                    new
                                                    {
                                                        message = "sent",
                                                        json = ulhi.ToJson()
                                                    },
                                                    callId: en.Id,
                                                    callRequestId: cr.CallRequestId,
                                                    queueItemId: qi.QueueItemId,
                                                    poNumber: dispatch.AuthorizationNumber.ToString(),
                                                    callNumber: en.CallNumber);    
                                            }
                                        }
                                        catch (Exception e)
                                        {
                                            logger.Error(masterAccountId,
                                                "StatusUpdateBreadcrumb",
                                                "Failed to send extra breadcrumb",
                                                ss.AccessToken, null, dispatch.Id, qi.CompanyId,
                                                new
                                                {
                                                    errorMessage = e.Message,
                                                    exception = e
                                                },
                                                callId: en.Id,
                                                callRequestId: cr.CallRequestId,
                                                queueItemId: qi.QueueItemId,
                                                poNumber: dispatch.AuthorizationNumber.ToString(),
                                                callNumber: en.CallNumber);
                                        }
                                    }
                                }
                                catch (SwoopException y)
                                {
                                    if (y?.Message != null && y.Message.StartsWith("Partner driver not found") &&
                                        towbookDriver != null)
                                    {
                                        var sv = SwoopUser.GetByUserId(ss.SwoopSessionId, towbookDriver.UserId);
                                        if (sv != null)    
                                            await sv.DeleteAsync();
                                    }

                                    logger.Error(masterAccountId,
                                        "StatusUpdate",
                                        "Status Update Failed",
                                        ss.AccessToken, null, dispatch.Id, qi.CompanyId,
                                        new
                                        {
                                            error = y.Message,
                                            exception = y
                                        },
                                        callId: en.Id,
                                        callRequestId: cr.CallRequestId,
                                        queueItemId: qi.QueueItemId,
                                        poNumber: dispatch.AuthorizationNumber.ToString(),
                                        callNumber: en.CallNumber,
                                        ownerUserId: qi.OwnerUserId);

                                    return false;
                                }
                                catch (Exception y)
                                {
                                    logger.Error(masterAccountId,
                                        "StatusUpdate",
                                        "Status Update Failed",
                                        ss.AccessToken, null, dispatch.Id, qi.CompanyId,
                                        new
                                        {
                                            error = y.Message,
                                            exception = y
                                        },
                                        callId: en.Id,
                                        callRequestId: cr.CallRequestId,
                                        queueItemId: qi.QueueItemId,
                                        poNumber: dispatch.AuthorizationNumber.ToString(),
                                        callNumber: en.CallNumber,
                                        ownerUserId: qi.OwnerUserId);

                                    return false;
                                }
                            }

                            await TryApplyPayment();

                            async Task TryApplyPayment()
                            {
                                if (masterAccountId == MasterAccountTypes.OonSwoop &&
                                   status == "Completed")
                                {
                                    var json_pay = Core.GetRedisValue("oon_swoop_pay:" + sd.Swcid);

                                    if (json_pay != null)
                                    {
                                        var payment = JsonConvert.DeserializeObject<OonPayModel>(json_pay);

                                        en.SetAttribute(Dispatch.AttributeValue.BUILTIN_BILLING_NOTES, "");

                                        en.SetAttribute(Dispatch.AttributeValue.BUILTIN_BILLING_NOTES,
                                            $"CCN: {payment.CCN}, Exp: {payment.Expiration}, " +
                                            $"CCV: {payment.CSC}, Billing Zip: 02155, " +
                                            $"Amount: {payment.Amount}");

                                        await en.Save(token: new AuthenticationToken()
                                        {
                                            UserId = authtokenSwoop.UserId
                                        }, ipAddress: "127.0.0.1");

                                        logger.Info(masterAccountId,
                                            "HandlePayment",
                                            "Assigned payment successfully",
                                            dispatchId: sd.Swcid.ToString(),
                                            companyId: en.CompanyId,
                                            data: new
                                            {
                                                accountId = en.AccountId,
                                            },
                                            callId: en.Id,
                                            callRequestId: cr.CallRequestId,
                                            queueItemId: qi.QueueItemId,
                                            poNumber: sd.Swcid.ToString());
                                    }
                                    else
                                    {
                                        logger.Error(masterAccountId,
                                            "HandlePayment",
                                            "Could not assign payment - payment info missing - Manual intervention required.",
                                            dispatchId: sd.Swcid.ToString(),
                                            companyId: en.CompanyId,
                                            data: new
                                            {
                                                accountId = en.AccountId,
                                                humanReviewNeeded = true
                                            },
                                            callId: en.Id,
                                            callRequestId: cr.CallRequestId,
                                            queueItemId: qi.QueueItemId,
                                            poNumber: sd.Swcid.ToString());
                                    }
                                }
                            }

                            if (newStatus?.Id == Status.EnRoute.Id && en?.ArrivalETA != null)
                            {
                                // schedule EtaCheck, 10 minutes before Eta expires.
                                var checkTime = en.ArrivalETA.Value.AddMinutes(-10);
                                await ScheduleEtaCheck(checkTime, en.ArrivalETA.Value, en.CompanyId,
                                    en.AccountId, en, cr);
                            }

                            return true;
                        }
                    try
                    {
                        if (await doStatusUpdate())
                        {
                            qi.CallRequestId = cr.CallRequestId;
                            DigitalDispatchService.LogAction(qi);
                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                        }
                        else
                        {
                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                        }
                    }
                    catch(Exception y)
                    {
                        logger.Error(masterAccountId,
                            "StatusUpdate",
                            "Failed to Update Status",
                            ss.AccessToken, null, dispatch.Id, qi.CompanyId,
                            new
                            {
                                errorMessage = y.Message,
                                exception = y
                            },
                            callRequestId: cr.CallRequestId,
                            queueItemId: qi.QueueItemId,
                            poNumber: dispatch.AuthorizationNumber.ToString(),
                            ownerUserId: qi.OwnerUserId);

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                    }

                    if (sourceMessage.Message.MessageId != "MOCK")
                            await sourceMessage.CompleteAsync();
                    }
                    break;

                case DigitalDispatchActionQueueItemType.InternalEtaCheck:
                    async Task doCheck()
                    {
                        var en = Entry.GetByIdNoCache(cr.DispatchEntryId.Value);

                        var driver = await Driver.GetByIdAsync(en.DriverId);
                        
                        if (driver == null)
                        {
                            // eta is only 10 minutes away and they haven't assigned a driver to the call yet.
                            return;
                        }

                        var driverUser = await User.GetByIdAsync(driver.UserId);
                        if (driverUser == null)
                            return;

                        if (en.Status.Id >= Status.AtSite.Id)
                            return;

                        var uli = UserLocationHistoryItem.GetCurrentByUserId(driverUser.Id,
                            DateTime.Now.AddMinutes(-15), DateTime.Now);

                        if (uli == null)
                            return;

                        var pickup = en.Waypoints.FirstOrDefault(t => t.Title == "Pickup");

                        var est = await AutoDispatch.AutoDispatchServiceBusHandler.GetMatrixAsync(
                            uli.Latitude + "," + uli.Longitude,
                            pickup.Latitude + "," + pickup.Longitude);

                        if (est != null &&
                            est.Status == "OK" &&
                            est.Rows.First().Elements.First().Status == "OK")
                        {
                            var driverEta = DateTime.SpecifyKind(DateTime.Now.AddSeconds(est.Rows.FirstOrDefault().Elements.FirstOrDefault().Duration.Value), DateTimeKind.Unspecified)
                                .ToUniversalTime()
                                .ToLocalTime();

                            var driverDistanceRemaining = decimal.Round((decimal)est.Rows.FirstOrDefault().Elements.FirstOrDefault().Distance.Value / 1609.344m, 2);

                            var callEta = en.ArrivalETA.Value;
                            var differenceInMinutes = Math.Round((driverEta - callEta).TotalMinutes, 0);

                            if (differenceInMinutes > 10)
                            {
                                if (differenceInMinutes < 90)
                                {
                                    try
                                    {
                                        await client.ExtendEta(ss.AccessToken, sd.OfferId, driverEta,
                                            await FindByUserId(ss, qi.OwnerUserId.GetValueOrDefault()));

                                        en.ArrivalETA = driverEta;
                                        en.Notes = "ETA automatically updated to: " + Core.OffsetDateTime(en.Company, driverEta).ToShortTowbookTimeString() +
                                            " (net difference: " + differenceInMinutes + " minutes) based on GPS.\n" +
                                            en.Notes;

                                        await en.Save(false, authtokenSwoop, null, true);
                                        logger.Info(en.Account.MasterAccountId,
                                           "EtaCheck",
                                           "Updated ETA with Swoop automatically",
                                           ss.AccessToken, null, dispatch.Id, qi.CompanyId,
                                           new
                                           {
                                               json = new
                                               {
                                                   differenceInMinutes = Math.Round(differenceInMinutes, 2),
                                                   originalEta = callEta,
                                                   newEta = driverEta,
                                                   driverDistanceRemaining,
                                                   gpsEventUsed = new
                                                   {
                                                       uli.Latitude,
                                                       uli.Longitude,
                                                       uli.Timestamp
                                                   }
                                               }.ToJson()
                                           },
                                           callId: en.Id,
                                           callRequestId: cr.CallRequestId,
                                           queueItemId: qi.QueueItemId,
                                           poNumber: en.PurchaseOrderNumber);
                                    }
                                    catch (Exception y)
                                    {
                                        logger.Info(en.Account.MasterAccountId,
                                           "EtaCheck",
                                           "Update ETA with Swoop automatically failed",
                                           ss.AccessToken, null, dispatch.Id, qi.CompanyId,
                                           new
                                           {
                                               exception = y,
                                               newEta = driverEta
                                           },
                                           callId: en.Id,
                                           callRequestId: cr.CallRequestId,
                                           poNumber: en.PurchaseOrderNumber,
                                           queueItemId: qi.QueueItemId);
                                    }
                                }
                                else
                                {
                                    logger.Warn(en.Account.MasterAccountId,
                                          "EtaCheck",
                                          "Update ETA with Swoop skipped - new eta over 90 minutes.",
                                          ss.AccessToken, null, dispatch.Id, qi.CompanyId,
                                          data: new
                                          {
                                              originalEta = callEta,
                                              newEta = driverEta,
                                              differenceInMinutes = Math.Round(differenceInMinutes, 2),
                                              driverDistanceRemaining,
                                              gpsEventUsed = new
                                              {
                                                  uli.Latitude,
                                                  uli.Longitude,
                                                  uli.Timestamp
                                              }
                                          }.ToJson(),
                                          callId: en.Id,
                                          callRequestId: cr.CallRequestId,
                                          poNumber: en.PurchaseOrderNumber,
                                          queueItemId: qi.QueueItemId);
                                }
                            }
                        }

                    }
                    // callRequest
                    await doCheck();
                    await sourceMessage.CompleteAsync();
                    break;

                case DigitalDispatchActionQueueItemType.OutgoingCallCanceled:
                    {
                    try
                    {
                        MasterAccountReason reason = MasterAccountReason.GetById(Convert.ToInt32(jsonObj.ReasonId));
                      
                        var response = await client.CancelJob(ss.AccessToken, sd.OfferId, Convert.ToInt32(reason.Code),
                            await FindByUserId(ss, qi.OwnerUserId.GetValueOrDefault()));

                        logger.Info(MasterAccountTypes.Swoop,
                            "Cancel",
                            "Outgoing Cancel Call",
                            ss.AccessToken, null, dispatch.Id, qi.CompanyId,
                            new
                            {
                                message = response.ToJson(),
                                json = qi.JsonObject
                            },
                            queueItemId: qi.QueueItemId);
                            
                    }
                    catch (Exception e)
                    {
                        if (e.Message.Contains("Job was already in canceled state"))
                        {
                            if (cr.DispatchEntryId != null)
                            {
                                var en = await Entry.GetByIdAsync(cr.DispatchEntryId.Value);

                                if (Core.GetRedisValue(en.Id + ":ss_dc") == "1")
                                {
                                    // TODO: mark the call as acknowledged initially.
                                    await en.Cancel("Cancellation Confirmed",
                                        authtokenSwoop, "127.0.0.1");

                                    Core.DeleteRedisKey(en.Id + ":ss_dc");
                                }
                            }
                        }
                        else
                        {
                            var en = await Entry.GetByIdAsync(cr.DispatchEntryId.Value);

                            if (Core.GetRedisValue(en.Id + ":ss_dc") == "1")
                            {
                                // TODO: mark the call as acknowledged initially.
                                await en.Cancel("Digital Cancel Failed: Please contact Swoop to cancel the job.",
                                    authtokenSwoop, "127.0.0.1");

                                Core.DeleteRedisKey(en.Id + ":ss_dc");
                            }

                        }

                    }

                    await sourceMessage.CompleteAsync();

                    qi.CallRequestId = cr.CallRequestId;
                    DigitalDispatchService.LogAction(qi);
                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);

                    break;
                }

                case DigitalDispatchActionQueueItemType.OutgoingServiceFailure:
                    {
                        MasterAccountReason reason = MasterAccountReason.GetById(Convert.ToInt32(jsonObj.ReasonId));

                        var response = await client.UnsuccessfulJob(ss.AccessToken, sd.OfferId, Convert.ToInt32(reason.Code),
                            await FindByUserId(ss, qi.OwnerUserId.GetValueOrDefault()));

                        logger.Info(MasterAccountTypes.Swoop,
                            "RequestServiceFailure",
                            "Sent Unsuccessful Job Request",
                            ss.AccessToken, null, dispatch.Id, qi.CompanyId,
                            new
                            {
                                message = response.ToJson(),
                                json = qi.JsonObject
                            },
                            callRequestId: cr.CallRequestId,
                            queueItemId: qi.QueueItemId);

                        await sourceMessage.CompleteAsync();

                        qi.CallRequestId = cr.CallRequestId;
                        DigitalDispatchService.LogAction(qi);
                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);

                        break;
                    }
                case DigitalDispatchActionQueueItemType.OutgoingRequestGoa:
                    {

                        MasterAccountReason reason = MasterAccountReason.GetById(Convert.ToInt32(jsonObj.ReasonId));

                        var response = await client.GoaJob(ss.AccessToken, sd.OfferId, Convert.ToInt32(reason.Code),
                            await FindByUserId(ss, qi.OwnerUserId.GetValueOrDefault()));

                        logger.Info(MasterAccountTypes.Swoop,
                            "RequestGoa",
                            "Outgoing Request Goa",
                            ss.AccessToken, null, dispatch.Id, qi.CompanyId,
                            new
                            {
                                message = response.ToJson(),
                                json = qi.JsonObject
                            },
                            queueItemId: qi.QueueItemId);

                        await sourceMessage.CompleteAsync();
                        qi.CallRequestId = cr.CallRequestId;
                        DigitalDispatchService.LogAction(qi);
                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                        break;
                    }

                case DigitalDispatchActionQueueItemType.OutgoingSharePhoto:
                    {
                        SharePhotoModel pm = JsonConvert.DeserializeObject<SharePhotoModel>(qi.JsonObject);

                        var pho = Dispatch.Photo.GetById(pm.PhotoId);
                        var swoopUserId = await FindByUserId(ss, qi.OwnerUserId.GetValueOrDefault());

                        var presigned = await client.PresignDocumentUpload(ss.AccessToken,
                            swoopUserId);

                        var filename = await FileUtility.GetFileAsync(pho.Location.Replace("%1", qi.CompanyId.ToString()));

                        if (filename != null)
                        {
                            await FileUtility.SendFilePresigned(filename, presigned.Url);

                            try
                            {
                                System.IO.File.Delete(filename);
                            }
                            catch
                            {

                            }

                            await client.FinalizePhotoUpload(ss.AccessToken,
                                presigned.Id,  sd.OfferId,
                                pho.DispatchEntryStatusId == Status.AtSite.Id ? SwoopRestClient.PhotoType.Pickup : SwoopRestClient.PhotoType.Destination,
                                pho.Id + ".jpg", pho.ContentType, pho.CreateDate, pho.FileSize,
                                (pho.CameraLatitude ?? pho.Latitude), 
                                (pho.CameraLongitude ?? pho.Longitude), 
                                swoopUserId);

                            logger.Info(masterAccountId, 
                                "SharePhoto",
                                "Sent photo successfully.",
                                companyId: qi.CompanyId,
                                data: new
                                {
                                    id = pm.PhotoId,
                                    size = pho.FileSize
                                },
                                callId: pho.DispatchEntryId,
                                callRequestId: pm.CallRequestId,
                                queueItemId: qi.QueueItemId);
                        }

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                        await sourceMessage.CompleteAsync();
                    }
                break;

                case DigitalDispatchActionQueueItemType.OutgoingShareSignature:
                    {
                        var pm = JsonConvert.DeserializeObject<ShareSignatureModel>(qi.JsonObject);

                        var pho = Dispatch.Signature.GetById(pm.SignatureId);
                        var swoopUserId = await FindByUserId(ss, qi.OwnerUserId.GetValueOrDefault());

                        var presigned = await client.PresignDocumentUpload(ss.AccessToken,
                            swoopUserId);

                        var filename = await FileUtility.GetFileAsync(pho.Location.Replace("%1", qi.CompanyId.ToString()));

                        if (filename != null)
                        {
                            await FileUtility.SendFilePresigned(filename, presigned.Url);

                            try
                            {
                                System.IO.File.Delete(filename);
                            }
                            catch
                            {

                            }

                            var response = await client.FinalizeSignatureUpload(ss.AccessToken,
                                presigned.Id, 
                                sd.OfferId,
                                pm.StatusId == Status.AtSite.Id ? SwoopRestClient.PhotoType.Pickup : SwoopRestClient.PhotoType.Destination,
                                pho.DispatchEntrySignatureId + ".jpg", 
                                pho.ContentType, 
                                pho.CreateDate, 
                                pho.FileSize.GetValueOrDefault(),
                                pho.Latitude,
                                pho.Longitude,
                                swoopUserId);

                            logger.Info(masterAccountId,
                                "ShareSignature",
                                "Sent signature successfully.",
                                companyId: qi.CompanyId,
                                data: new
                                {
                                    id = pm.SignatureId,
                                    size = pho.FileSize,
                                    statusId = pm.StatusId,
                                    response = response.ToJson()
                                },
                                callId: pho.DispatchEntryId,
                                callRequestId: pm.CallRequestId,
                                queueItemId: qi.QueueItemId,
                                poNumber: sd.Swcid.ToString());
                        }

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                        await sourceMessage.CompleteAsync();
                    }
                    break;


                case DigitalDispatchActionQueueItemType.OutgoingExtendEta:
                    var newEta = (int)jsonObj.Eta;

                    var en2 = Entry.GetByIdNoCache(cr.DispatchEntryId.Value);
                    var newEtaTime = (DateTime)jsonObj.NewEta;

                    string predictedEta = "";

                    logger.Warn(en2.Account.MasterAccountId,
                        "ExtendETA",
                        "Outgoing Extend ETA, Predicted ETA: " + predictedEta,
                        ss.AccessToken, null, dispatch.Id, qi.CompanyId,
                        new
                        {
                            message = jsonObj,
                            eta = sd.Eta,
                            newEta = sd.Eta +newEta,
                            timestamp = newEtaTime
                        },
                        callRequestId: cr.CallRequestId,
                        queueItemId: qi.QueueItemId,
                        callId: en2.Id, 
                        poNumber: en2.PurchaseOrderNumber,
                        callNumber: en2.CallNumber);

                    await client.ExtendEta(ss.AccessToken, sd.OfferId, newEtaTime,
                        await FindByUserId(ss, qi.OwnerUserId.GetValueOrDefault()));

                    qi.CallRequestId = cr.CallRequestId;
                    DigitalDispatchService.LogAction(qi);

                    await sourceMessage.CompleteAsync();
                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    break;

                default:
                    logger.Error(masterAccountId,
                        "UnsupportedEvent",
                        "Event Type Not Implemented",
                        ss.AccessToken, null, dispatch.Id, qi.CompanyId,
                        new
                        {
                            json = JsonExtensions.ToJson(jsonObj)
                        },
                        queueItemId: qi.QueueItemId);

                    await sourceMessage.CompleteAsync();
                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                    return;
            }
        }


        public class OonPayModel
        {
            public string PO { get; set; }
            public string CCN { get; set; }
            public string Expiration { get; set; }
            public string CSC { get; set; }
            public string Amount { get; set; }
        }


    }
}
