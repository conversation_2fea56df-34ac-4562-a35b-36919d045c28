using Azure.Messaging.ServiceBus;
using Extric.Towbook.Accounts;
using Extric.Towbook.Dispatch;
using Extric.Towbook.Integration;
using Extric.Towbook.Integration.MotorClubs;
using Extric.Towbook.Integration.MotorClubs.Dispatch;
using Extric.Towbook.Integration.MotorClubs.Queue;
using Extric.Towbook.Integration.MotorClubs.Services;
using Extric.Towbook.Integrations.MotorClubs.Sykes;
using Extric.Towbook.Utility;
using Extric.Towbook.Vehicle;
using Microsoft.Extensions.Hosting;
using Newtonsoft.Json;
using NLog;
using Async = System.Threading.Tasks;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using NewRelic.Api.Agent;

namespace Extric.Towbook.Services.MotorClubDispatchingService
{
    public partial class MotorClubDispatchingService : IHostedService
    {
        public static bool disableSykes = false;
        private const int SykesUserId = 19;

        [Transaction]
        public static async Task<bool> HandleSykesIncomingMessage(DigitalDispatchActionQueueItem qi, SykesMessage jsonObj, ProcessMessageEventArgs sourceMessage)
        {
            if (!disableSykes)
            {
                switch (qi.Type)
                {
                    case DigitalDispatchActionQueueItemType.IncomingCallReceived:
                        logger.Info(MasterAccountTypes.Sykes,
                             "CallReceived",
                             "Incoming Job Offer",
                             jsonObj.ContractorId, null, jsonObj.DispatchId, qi.CompanyId,
                             new
                             {
                                 json = jsonObj.ToJson()
                             },
                             queueItemId: qi.QueueItemId);

                        var jo = JsonConvert.DeserializeObject<SykesRestClient.DispatchModel>(jsonObj.JsonData);
                        
                        var rcr = await CreateCallRequest(jsonObj, MotorClubName.Sykes);

                        qi.CallRequestId = rcr.CallRequestId;
                        DigitalDispatchService.LogAction(qi);

                        logger.Info(MasterAccountTypes.Sykes,
                            "CallReceived",
                            "Incoming Job Offer Created",
                            jsonObj.ContractorId, null, jsonObj.DispatchId, qi.CompanyId,
                            callRequestId: rcr.CallRequestId,
                            queueItemId: qi.QueueItemId);

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                        await sourceMessage.CompleteAsync();
                        break;

                    case DigitalDispatchActionQueueItemType.IncomingCallUpdate:
                        logger.Log(LogLevel.Info, "Sykes/{0}/IncomingCallUpdate: {2}",
                            qi.QueueItemId, qi.Type, jsonObj.ToJson());
                        var dmu = JsonConvert.DeserializeObject<SykesRestClient.DispatchModel>(jsonObj.JsonData);
                        var sdu = SykesDispatch.GetByContractorIdAndDispatchId(jsonObj.ContractorId, jsonObj.DispatchId);
                        var cr = CallRequest.GetById(sdu.CallRequestId);
                        if (cr?.DispatchEntryId != null)
                        {
                            var en = await Entry.GetByIdNoCacheAsync(cr.DispatchEntryId.Value);
                            if (en != null)
                            {
                                //Update pickup
                                var log = new LogEventInfo
                                {
                                    Level = LogLevel.Info,
                                    Message = "CallUpdateLocation"
                                };
                                log.Properties.Add("callId", en.Id);
                                log.Properties.Add("masterAccountName", MasterAccountTypes.GetName(MasterAccountTypes.Sykes));
                                log.Properties.Add("json", jsonObj.JsonData);
                                try
                                {

                                    en.TowSource = dmu.Location.ToString();
                                    
                                    var wp = en.Waypoints.Where(o => o.Title == "Pickup").FirstOrDefault();
                                    if (wp != null)
                                    {
                                        wp.Address = en.TowSource;
                                        wp.Latitude = Convert.ToDecimal(dmu.Location.Latitude);
                                        wp.Longitude = Convert.ToDecimal(dmu.Location.Longitude);
                                    }
                                }
                                catch (Exception e)
                                {
                                    log.Properties.Add("exception", e);
                                }
                                logger.Log(log);

                                //Update destination
                                log = new LogEventInfo
                                {
                                    Level = LogLevel.Info,
                                    Message = "CallUpdateDestination"
                                };
                                log.Properties.Add("callId", en.Id);
                                log.Properties.Add("masterAccountName", MasterAccountTypes.GetName(MasterAccountTypes.Sykes));
                                log.Properties.Add("json", jsonObj.JsonData);
                                try
                                {
                                    if (dmu.Destinations.Length > 0)
                                    {
                                        SykesRestClient.Location location = dmu.Destinations[0];
                                        en.TowDestination = location.Address;
                                        var wp = en.Waypoints.Where(o => o.Title == "Destination").FirstOrDefault();
                                        if (wp != null)
                                        {
                                            wp.Address = en.TowSource;
                                            wp.Latitude = Convert.ToDecimal(location.Latitude);
                                            wp.Longitude = Convert.ToDecimal(location.Longitude);
                                        }
                                    }
                                }
                                catch (Exception e)
                                {
                                    log.Properties.Add("exception", e);
                                }
                                logger.Log(log);


                                //Update arrivalETA
                                log = new LogEventInfo
                                {
                                    Level = LogLevel.Info,
                                    Message = "CallUpdateEtaDate"
                                };
                                log.Properties.Add("callId", en.Id);
                                log.Properties.Add("masterAccountName", MasterAccountTypes.GetName(MasterAccountTypes.Sykes));
                                log.Properties.Add("json", jsonObj.JsonData);
                                try
                                {
                                    en.ArrivalETA = DateTime.Parse(dmu.EtaDate, CultureInfo.InvariantCulture, DateTimeStyles.AssumeUniversal);
                                }
                                catch (Exception e)
                                {
                                    log.Properties.Add("exception", e);
                                }
                                logger.Log(log);

                                //Save Entry
                                log = new LogEventInfo
                                {
                                    Level = LogLevel.Info,
                                    Message = "CallSaveEntry"
                                };
                                log.Properties.Add("callId", en.Id);
                                log.Properties.Add("masterAccountName", MasterAccountTypes.GetName(MasterAccountTypes.Sykes));
                                log.Properties.Add("json", jsonObj.JsonData);
                                try
                                {
                                    await en.Save();
                                }
                                catch (Exception e)
                                {
                                    log.Properties.Add("exception", e);
                                }
                                logger.Log(log);
                            }
                        }

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                        await sourceMessage.CompleteAsync();
                        break;

                    case DigitalDispatchActionQueueItemType.IncomingCallCancelled:
                        var cc = JsonConvert.DeserializeObject<SykesRestClient.DispatchModel>(jsonObj.JsonData);
                        var ccujo = SykesDispatch.GetByContractorIdAndDispatchId(jsonObj.ContractorId, jsonObj.DispatchId);
                        if (ccujo == null)
                        {
                            logger.Error(MasterAccountTypes.Sykes,
                                "IncomingCallCancelled",
                                "Received cancel for call that doesn't exist",
                                jsonObj.ContractorId, null, jsonObj.DispatchId, qi.CompanyId,
                                new
                                {
                                    json = JsonExtensions.ToJson(cc)
                                },
                                queueItemId: qi.QueueItemId);

                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                            await sourceMessage.CompleteAsync();
                            return true;
                        }

                        var ccr = CallRequest.GetById(ccujo.CallRequestId);
                        await ccr.UpdateStatus(CallRequestStatus.Cancelled);
                       
                        bool cancelled = false;
                        if (ccr.DispatchEntryId.GetValueOrDefault() > 0)
                        {
                            var en = Entry.GetByIdNoCache(ccr.DispatchEntryId.Value);
                            if (en != null)
                            {
                                if (ShouldAllowCancel(en))
                                {
                                    await en.Cancel("Cancelled by Sykes: " + cc.CancelReason, new AuthenticationToken()
                                    {
                                        UserId = SykesUserId,
                                        ClientVersionId = MyClientVersionId
                                    }, "127.0.0.1");

                                    cancelled = true;
                                }
                                else
                                {
                                    cancelled = true;
                                }
                            }
                        }
                        qi.CallRequestId = ccr.CallRequestId;
                        DigitalDispatchService.LogAction(qi);
                        if (cancelled)
                        {
                            logger.Log(LogLevel.Info, "Sykes/{0}: Offer Cancelled. Type = {1}, CallRequestId = {2}, Reason = {3}", qi.QueueItemId, qi.Type, ccr.CallRequestId, cc.CancelReason);
                        }
                        else
                        {
                            logger.Log(LogLevel.Info, "Sykes/{0}: Offer Cancel event received but failed to be canceled. Type = {1}, CallRequestId = {2}", qi.QueueItemId, qi.Type, ccr.CallRequestId);
                        }
                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                        await sourceMessage.CompleteAsync();
                        break;

                    default:
                        logger.Error(MasterAccountTypes.Sykes,
                            "UnsupportedEvent",
                            "Event Type Not Implemented",
                            null, null, jsonObj.DispatchId, qi.CompanyId,
                            new
                            {
                                json = JsonExtensions.ToJson(jsonObj)
                            },
                            queueItemId: qi.QueueItemId);

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                        await sourceMessage.CompleteAsync();
                        return true;
                }
            }
            return true;
        }

        [Transaction]
        public static async Task HandleSykesQueueOutgoingMessage(DigitalDispatchActionQueueItem qi, dynamic jsonObj, ProcessMessageEventArgs sourceMessage)
        {
            SykesRestClient client = SykesRestClient.Get();

            if (qi?.AccountId != null)
            {
                var sc = SykesContractor.GetByAccountId(qi.AccountId.Value).FirstOrDefault();
                if (sc.EnvironmentId == 2)
                {
                    client = SykesRestClient.GetTestEnvironment();
                }
            }

            if (!disableSykes)
            {
                switch (qi.Type)
                {
                    case DigitalDispatchActionQueueItemType.OutgoingAcceptCall:
                        var x = GetCallRequestAndDispatch(jsonObj);
                        CallRequest callRequest = x.CallRequest;
                        SykesDispatch dispatch = x.SykesDispatch;
                        if (callRequest == null)
                        {
                            await sourceMessage.DeadLetterAsync();
                            return;
                        }

                        if (dispatch == null)
                        {
                            logger.Log(LogLevel.Error, "Sykes/CR" + callRequest.CallRequestId + "/C" + callRequest.CompanyId + "/OutgoingAcceptCall: Couldn't find SykesDispatches");

                            await sourceMessage.DeadLetterAsync();
                            return;
                        }

                        dispatch.Eta = jsonObj.Eta;
                        dispatch.Save();

                        try
                        {
                            logger.Info(MasterAccountTypes.Sykes,
                                "AcceptCall",
                                "Outgoing Call Accept",
                                dispatch.ContractorId, null, dispatch.DispatchId, qi.CompanyId,
                                new
                                {
                                    message = jsonObj
                                },
                                queueItemId: qi.QueueItemId);

                            bool postDispatchIsOk()
                            {
                                var data = new SykesRestClient.DispositionDto()
                                {
                                    Status = SykesRestClient.StatusDisposition.ELDACP.ToString(),
                                    Date = SykesRestClient.DateTimeToString(qi.CreateDate),
                                    Notes = "",
                                    Eta = SykesRestClient.DateTimeToString(DateTime.Now.AddMinutes(dispatch.Eta.Value))
                                };

                                var response = client.DispatchDispositionPost(dispatch.ContractorId, dispatch.DispatchId, data);

                                if (response != null)
                                {
                                    return true;
                                }

                                return false;
                            }

                            if (!postDispatchIsOk())
                            {
                                await callRequest.UpdateStatus(CallRequestStatus.AcceptFailed);
                            }
                            else
                            {
                                var sykesMessage = JsonConvert.DeserializeObject<SykesMessage>(dispatch.DispatchJson);
                                var accept = JsonConvert.DeserializeObject<SykesRestClient.DispatchModel>(sykesMessage.JsonData);

                                Entry fe = await DistributedLock.ForAsync<Entry>("Sykes", accept.ReferenceId, 10000,
                                    lockAcquired: async delegate ()
                                    {
                                        Entry call = null;
                                        call = await Entry.GetByPurchaseOrderNumberAsync(callRequest.AccountId, accept.AuthorizationId);

                                        if (call == null)
                                        {
                                            call = new Entry();
                                            call.CompanyId = callRequest.CompanyId;
                                            call.AccountId = callRequest.AccountId;
                                        }
                                        else
                                        {
                                            logger.LogEvent("Sykes/{0}: Found existing towbook call for Case {1}... Call #{2}... we're going to update this one.",
                                                qi.CompanyId, LogLevel.Info, sourceMessage.Message.MessageId, accept.ReferenceId, call.CallNumber);

                                            if (call.Status == Status.Cancelled)
                                            {
                                                logger.LogEvent("Sykes/{0}: Call is cancelled. Proceeding to uncancel and set status to waiting...",
                                                    qi.CompanyId, LogLevel.Info, sourceMessage.Message.MessageId, accept.ReferenceId, call.CallNumber);
                                                await call.Uncancel(new AuthenticationToken() { UserId = SykesUserId, ClientVersionId = MyClientVersionId });
                                            }
                                        }

                                        if (call.Account?.DefaultPriority == 1)
                                            call.Priority = Entry.EntryPriority.High;

                                        #region Vehicle

                                        EntryAsset asset = null;
                                        if (call.Assets != null)
                                            asset = call.Assets.FirstOrDefault();

                                        if (asset == null)
                                            asset = new EntryAsset() { BodyTypeId = 1 };

                                        if (asset.BodyTypeId == 0)
                                            asset.BodyTypeId = 1;

                                        var vc = VehicleUtility.GetColorIdByName(accept.Vehicle.Colour);

                                        if (accept.Vehicle.Year.HasValue && accept.Vehicle.Year.Value > 0)
                                            asset.Year = Convert.ToInt32(accept.Vehicle.Year);

                                        asset.Make = VehicleUtility.GetManufacturerByName(accept.Vehicle.Make);
                                        asset.Model = VehicleUtility.GetModelByName(accept.Vehicle.Model);

                                        if (vc != 0)
                                            asset.ColorId = vc;

                                        asset.LicenseNumber = accept.Vehicle.Plate;
                                        asset.Vin = accept.Vehicle.Vin;
                                        asset.LicenseState = accept.Vehicle.RegisterProvince ?? String.Empty;

                                        #endregion Vehicle

                                        #region Locations

                                        var pickup = call.Waypoints.FirstOrDefault(o => o.Title == "Pickup");
                                        var dest = call.Waypoints.FirstOrDefault(o => o.Title == "Destination");

                                        if (call.Notes == null)
                                            call.Notes = "";

                                        if (accept.Location != null)
                                        {
                                            if (string.IsNullOrWhiteSpace(call.TowSource) || call.Version < 3)
                                            {
                                                call.TowSource = accept.Location.ToString();
                                                if (pickup == null)
                                                {
                                                    pickup = new EntryWaypoint() { Title = "Pickup", Position = 1 };
                                                    call.Waypoints.Add(pickup);
                                                }
                                                pickup = accept.Location.ToWaypoint(pickup);
                                            }
                                        }

                                        if (accept.Destinations != null && accept.Destinations.Length > 0)
                                        {
                                            if (string.IsNullOrWhiteSpace(call.TowDestination) || call.Version < 3)
                                            {
                                                call.TowDestination = accept.Destinations != null && accept.Destinations.Count() > 0 ? accept.Destinations.FirstOrDefault().ToString() : "";
                                                if (dest == null && !string.IsNullOrWhiteSpace(call.TowDestination))
                                                {
                                                    dest = new EntryWaypoint() { Title = "Destination", Position = 2 };
                                                    call.Waypoints.Add(dest);
                                                }

                                                dest = accept.Destinations != null && accept.Destinations.Count() > 0 ? accept.Destinations.FirstOrDefault().ToWaypoint(dest) : dest;
                                            }
                                        }

                                        #endregion Locations

                                        // don't use offerExpires time, and they dont have a transaction timestamp, so we'll just use current server time.
                                        if (call.CreateDate == DateTime.MinValue)
                                            call.CreateDate = DateTime.Now.AddSeconds(-DateTime.Now.Second); // dont use accept.service.OfferExpiresLocal;

                                        if (dispatch.Eta != null)
                                        {
                                            call.ArrivalETA = call.CreateDate.AddMinutes(dispatch.Eta.Value);
                                        }

                                        #region po number
                                        call.PurchaseOrderNumber = accept.AuthorizationId;
                                        #endregion

                                        #region coverage amount
                                        if (accept.Coverage != null)
                                        {
                                            string coverage = "";

                                            coverage = accept.Coverage.Text;

                                            if (accept.Coverage.HasDetail("AUTHAMT") && accept.Coverage.GetDetail("AUTHAMT").Value > 0)
                                            {
                                                coverage = (!string.IsNullOrWhiteSpace(coverage) ? coverage + ", " : "") +
                                                    accept.Coverage.GetDetail("AUTHAMT").ToString();
                                            }

                                            if (accept.Coverage.HasDetail("AUTHKM") && accept.Coverage.GetDetail("AUTHKM").Value > 0)
                                            {
                                                coverage = (!string.IsNullOrWhiteSpace(coverage) ? coverage + ", " : "") +
                                                    accept.Coverage.GetDetail("AUTHKM").ToString();
                                            }

                                            if (!accept.Coverage.Authorized)
                                                coverage = "NO COVERAGE - COD";

                                            call.SetAttribute(Dispatch.AttributeValue.BUILTIN_MOTORCLUB_COVERAGELIMIT,
                                                coverage);
                                        }
                                        #endregion

                                        #region Reason

                                        call.ReasonId = await ReasonHelper.DetermineReasonId(call.Account.MasterAccountId, qi.CompanyId.Value, accept.ServiceType);
                                        if (call.ReasonId == 1635)
                                            call.Notes += "Service Needed: " + accept.ServiceType + "\n"; 

                                        #endregion Reason

                                        if (callRequest != null)
                                        {
                                            if (callRequest.OwnerUserId == null)
                                                callRequest.OwnerUserId = 1;

                                            call.CallRequestId = callRequest.CallRequestId;

                                            if (call.OwnerUserId < 100)
                                                call.OwnerUserId = callRequest.OwnerUserId.GetValueOrDefault(0);
                                        }
                                        else if (call.OwnerUserId < 100)
                                            call.OwnerUserId = 1;
                                        #region Notes

                                        void addNote(string line, bool top = false)
                                        {
                                            if (string.IsNullOrWhiteSpace(line))
                                                return;


                                            if (call.Notes == null || !call.Notes.Contains(line))
                                            {
                                                if (call.Notes == null)
                                                    call.Notes = line + "\n";
                                                else
                                                {
                                                    if (top)
                                                        call.Notes = line + call.Notes.Trim('\n') + "\n";
                                                    else
                                                        call.Notes += "\n" + line.Trim('\n');
                                                }
                                            }
                                        }

                                        if (!string.IsNullOrEmpty(accept.ClubName))
                                        {
                                            addNote("Club Name: " + accept.ClubName, true);
                                        }

                                        if (!string.IsNullOrEmpty(accept.EquipmentRequired))
                                        {
                                            addNote("Equipment Required: " + accept.EquipmentRequired);
                                        }

                                        if (accept.Location.Additional != null && accept.Location.Additional.Any())
                                        {
                                            accept.Location.Additional.ForEach(a => addNote($"{a.Label}: {a.Value}"));   
                                        }

                                        if (accept.Destinations != null)
                                        {
                                            foreach (var adest in accept.Destinations)
                                            {
                                                if (adest != null)
                                                    adest.Additional.ForEach(a => addNote($"Destination {a.Label}: {a.Value}"));
                                            }
                                        }

                                        #endregion Notes

                                        await ApplyRoundRobinDispatcherLogicAsync(qi, call, dispatch.DispatchId, dispatch.ContractorId);

                                        if (call.Assets == null || call.Assets.Count == 0)
                                        {
                                            call.Assets = new System.Collections.ObjectModel.Collection<EntryAsset>();
                                            call.Assets.Add(asset);
                                        }

                                        var customerName = Core.FormatName(accept.Location.Contact);
                                        var phone = Core.FormatPhoneWithDashesOnly(accept.Location.Contact.Telephone);

                                        EntryContact c = call.Contacts.FirstOrDefault(o =>
                                            o.Name?.ToLowerInvariant() == customerName?.ToLowerInvariant() ||
                                            Core.FormatPhone(o.Phone) == phone);

                                        bool newContact = false;
                                        if (c == null)
                                        {
                                            c = new EntryContact() { Name = customerName };
                                            call.Contacts.Add(c);
                                            newContact = true;
                                        }

                                        c.Phone = phone;

                                        await call.Save();

                                        if (newContact)
                                            await CheckForRoadsideFeatureAndAutoInvite(call, c);

                                        await AutoDispatch.AutoDispatchServiceBusHandler.Send(call);

                                        return call;
                                    },
                                    alreadyLocked: async delegate ()
                                    {
                                        logger.LogEvent("{0}/CR{1}: Lock already exists for {2}:{3}... pausing 250ms",
                                            qi.CompanyId, LogLevel.Warn,
                                            sourceMessage.Message.MessageId,
                                            (callRequest != null ? callRequest.CallRequestId.ToString() : "NULL"),
                                            qi.AccountId.Value,
                                            accept.ReferenceId);


                                        await System.Threading.Tasks.Task.Delay(250);

                                        return true;
                                    });

                                if (fe == null)
                                {
                                    logger.LogEvent("Sykes/{0}: Creation of call failed; {1}",
                                    qi.CompanyId, LogLevel.Error, sourceMessage.Message.MessageId, qi.ToJson());

                                    if (callRequest != null)
                                        await callRequest.UpdateStatus(CallRequestStatus.AcceptFailed);
                                }
                                else
                                {
                                    if (callRequest != null)
                                    {
                                        await callRequest.UpdateStatus(CallRequestStatus.Accepted, po: fe.PurchaseOrderNumber);

                                        qi.CallRequestId = callRequest.CallRequestId;
                                        DigitalDispatchService.LogAction(qi);

                                        callRequest.DispatchEntryId = fe.Id;
                                        await callRequest.Save();
                                        await sourceMessage.CompleteAsync();
                                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);

                                        if (jsonObj.DriverId != null)
                                        {
                                            int driverId = Convert.ToInt32(jsonObj.DriverId);
                                            if (driverId > 1)
                                            {
                                                fe.DriverId = driverId;
                                                fe.Status = Extric.Towbook.Dispatch.Status.Dispatched;
                                                await fe.Save();
                                            }
                                        }

                                        logger.Info(MasterAccountTypes.Sykes, "CallCreated",
                                            "Created new towbook call for Sykes",
                                            companyId: qi.CompanyId, 
                                            data: new
                                            {
                                                waypoints = fe.Waypoints.Select(o => new { o.Address, o.Latitude, o.Longitude })
                                            },
                                            dispatchId: sykesMessage.DispatchId,
                                            callId: fe.Id,
                                            callRequestId: callRequest.CallRequestId,
                                            queueItemId: qi.QueueItemId,
                                            poNumber: fe.PurchaseOrderNumber,
                                            callNumber: fe.CallNumber);

                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            await sourceMessage.DeadLetterAsync("Attempted to accept callRequestId " + callRequest.CallRequestId + ", but  error occurred",
                                ex.ToJson(true));

                            logger.Error(MasterAccountTypes.Sykes,
                                "AcceptCall",
                                "Sent accept failed.",
                                dispatch?.ContractorId, null, dispatch?.DispatchId, qi?.CompanyId,
                                new
                                {
                                    errorMessage = ex.Message,
                                    exception = ex
                                },
                                callRequestId: callRequest.CallRequestId,
                                queueItemId: qi.QueueItemId);

                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                        }

                        break;
                    case DigitalDispatchActionQueueItemType.OutgoingCallCanceled:
                        logger.Info("Cancelled Call");
                        break;

                    case DigitalDispatchActionQueueItemType.OutgoingRejectCall:
                        var x2 = GetCallRequestAndDispatch(jsonObj);
                        CallRequest callRequestRC = x2.CallRequest;
                        SykesDispatch dispatchRC = x2.SykesDispatch;

                        if (callRequestRC == null)
                        {
                            await sourceMessage.CompleteAsync();
                            return;
                        }

                        MasterAccountReason rejectReason = MasterAccountReason.GetById(Convert.ToInt32(jsonObj.MasterAccountReasonId));
                        if (rejectReason == null)
                        {
                            await sourceMessage.DeadLetterAsync("Attempted to reject callRequestId " + callRequestRC.CallRequestId +
                                ", but no reason is present in database for it",
                                callRequestRC.ToJson(true));
                            await callRequestRC.UpdateStatus(CallRequestStatus.RejectFailed);
                            return;
                        }
                        var dispositionPost = new SykesRestClient.DispositionDto()
                        {
                            Status = SykesRestClient.StatusDisposition.ELDREJ.ToString(),
                            Date = DateTime.UtcNow.ToString("s") + "Z",
                            Notes = rejectReason.Code ?? rejectReason.Name
                        };

                        var disposition = client.DispatchDispositionPost(dispatchRC.ContractorId, dispatchRC.DispatchId, dispositionPost);

                        //TODO: Parse response to see if everything went ok (when we get an actual response :))

                        if (disposition != null)
                        {
                            await callRequestRC.UpdateStatus(CallRequestStatus.Rejected);
                        }
                        else
                        {
                            await callRequestRC.UpdateStatus(CallRequestStatus.RejectFailed);
                        }
                        await callRequestRC.Save();

                        qi.CallRequestId = callRequestRC.CallRequestId;
                        DigitalDispatchService.LogAction(qi);

                        await sourceMessage.CompleteAsync();
                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                        break;

                    case DigitalDispatchActionQueueItemType.OutgoingStatusUpdate:
                        CallRequest callRequestSU = CallRequest.GetById(Convert.ToInt32(jsonObj.CallRequestId));
                        if (callRequestSU == null)
                        {
                            logger.Log(LogLevel.Error, "CR" + jsonObj.CallRequestId + ": Couldn't find CallRequestId.");
                            logger.Log(LogLevel.Error, "CR" + JsonExtensions.ToJson(jsonObj));
                            await sourceMessage.DeadLetterAsync();
                            return;
                        }
                        SykesDispatch dispatchSU = SykesDispatch.GetByCallRequestId(Convert.ToInt32(jsonObj.CallRequestId));

                        double? lat = (double?)jsonObj.Latitude;
                        double? lng = (double?)jsonObj.Longitude;
                        int newStatusId = (int)jsonObj.NewStatusId;

                        String statusUpdate;
                        if (newStatusId == Status.Dispatched.Id)
                        {
                            statusUpdate = SykesRestClient.StatusDisposition.ELDDLV.ToString();
                        }
                        else if (newStatusId == Status.EnRoute.Id)
                        {
                            statusUpdate = SykesRestClient.StatusDisposition.ELDRTE.ToString();
                        }
                        else if (newStatusId == Status.AtSite.Id)
                        {
                            statusUpdate = SykesRestClient.StatusDisposition.ELDSCN.ToString();
                        }
                        else if (newStatusId == Status.BeingTowed.Id)
                        {
                            statusUpdate = SykesRestClient.StatusDisposition.ELDUTW.ToString();
                        }
                        else if (newStatusId == Status.Completed.Id)
                        {
                            statusUpdate = SykesRestClient.StatusDisposition.ELDCLR.ToString();
                        }
                        else
                        {
                            await sourceMessage.CompleteAsync();
                            return;
                        }

                        var dispositionStatusUpdatePOST = new SykesRestClient.DispositionDto()
                        {
                            Status = statusUpdate,
                            Date = SykesRestClient.DateTimeToString(DateTime.Now),
                            Notes = ""
                        };


                        if (lat != null && lng != null && (Math.Abs((double)lat - 0.0) > 0.00001) && (Math.Abs((double)lng - 0.0) > 0.00001))
                        {
                            var truckPOST = new SykesRestClient.TruckPOST();

                            truckPOST.Date = SykesRestClient.DateTimeToString(DateTime.Now);
                            truckPOST.Latitude = (double)lat;
                            truckPOST.Longitude = (double)lng;
                            truckPOST.Driver = jsonObj.DriverName;

                            client.DispatchTruckPost(dispatchSU.ContractorId, dispatchSU.DispatchId, truckPOST);
                        }

                        var dispositionSU = client.DispatchDispositionPost(dispatchSU.ContractorId, dispatchSU.DispatchId, dispositionStatusUpdatePOST);
                        await sourceMessage.CompleteAsync();

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                        break;
                    case DigitalDispatchActionQueueItemType.OutgoingLogin:
                        if (qi.AccountId.HasValue)
                        {
                            foreach (var sc in SykesContractor.GetByAccountId(qi.AccountId.Value))
                            {
                                // first of all, update IsLoggedIn=false so that automatic logins wont try to log
                                // this client back in.
                                sc.IsLoggedIn = true;
                                sc.LoginStatus = 3;
                                sc.Save();

                                client.SessionLoginPost(sc.ContractorId);

                                await PushNotificationProvider.BackgroundJobStatusUpdate(qi.CompanyId.Value,
                                    qi.QueueItemId,
                                    "digitaldispatch_login",
                                    true, // we don't care if logout failed - just let the user know the request finished.
                                    "Sykes Login Status",
                                    null);

                                await sourceMessage.CompleteAsync();
                            }
                        }
                        else
                        {
                            await sourceMessage.CompleteAsync(); // ("Attempted to Logout but no accountId was passed.", "");
                            return;
                        }
                        break;

                    case DigitalDispatchActionQueueItemType.OutgoingLogoff:
                        if (qi.AccountId.HasValue)
                        {
                            foreach (var sykesContractor in SykesContractor.GetByAccountId(qi.AccountId.Value))
                            {
                                // first of all, update IsLoggedIn=false so that automatic logins wont try to log
                                // this client back in.
                                sykesContractor.IsLoggedIn = false;
                                sykesContractor.LoginStatus = 1;
                                sykesContractor.Save();

                                client.SessionLogoutPost(sykesContractor.ContractorId);

                            }

                            await PushNotificationProvider.BackgroundJobStatusUpdate(qi.CompanyId.Value,
                                qi.QueueItemId,
                                "digitaldispatch_logout",
                                true, // we don't care if logout failed - just let the user know the request finished.
                                "Sykes Logout Status",
                                null);

                            await sourceMessage.CompleteAsync();
                        }
                        else
                        {
                            await sourceMessage.CompleteAsync(); // ("Attempted to Logout but no accountId was passed.", "");
                            return;
                        }
                        break;



                    case DigitalDispatchActionQueueItemType.OutgoingExtendEta:
                        async Task extendEta()
                        {
                            var newEta = (int)jsonObj.Eta;

                            CallRequest cr = CallRequest.GetById(Convert.ToInt32(jsonObj.CallRequestId));
                            SykesDispatch sd = SykesDispatch.GetByCallRequestId(Convert.ToInt32(jsonObj.CallRequestId));

                            var entry = Entry.GetByIdNoCache(cr.DispatchEntryId.Value);
                            var newEtaTime = (DateTime)jsonObj.NewEta;

                            logger.Warn(entry.Account.MasterAccountId,
                                "ExtendETA",
                                "Outgoing Extend ETA",
                                sd.ContractorId, null, sd.DispatchId, qi.CompanyId,
                                new
                                {
                                    message = jsonObj,
                                    eta = sd.Eta,
                                    newEta = sd.Eta + newEta,
                                    timestamp = newEtaTime
                                },
                                callRequestId: cr.CallRequestId,
                                queueItemId: qi.QueueItemId,
                                callId: entry.Id,
                                poNumber: entry.PurchaseOrderNumber,
                                callNumber: entry.CallNumber);

                            client.DispatchUpdateEta(sd.ContractorId, sd.DispatchId, newEtaTime);

                            qi.CallRequestId = cr.CallRequestId;
                            DigitalDispatchService.LogAction(qi);

                            await sourceMessage.CompleteAsync();
                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                        }
                        await extendEta();
                        break;

                    default:
                        await sourceMessage.DeadLetterAsync("No implementation written", qi.ToJson());
                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                        logger.LogEvent("Queue item doesn't have a Type set that we have a implementation written for... MessageId {0}, Body = {1}", 
                            qi.CompanyId, LogLevel.Error, sourceMessage.Message.MessageId, qi.ToJson());
                        return;
                }
            }
        }

        private static dynamic GetCallRequestAndDispatch(dynamic jsonObj)
        {
            CallRequest callRequest = null;
            SykesDispatch dispatch = null;
            try
            {
                callRequest = CallRequest.GetById(Convert.ToInt32(jsonObj.Id));
                if (callRequest == null)
                {
                    logger.Log(LogLevel.Error, "CR" + jsonObj.DispatchId + ": Couldn't find CallRequestId.");
                    logger.Log(LogLevel.Error, "CR" + jsonObj.DispatchId + ": " + JsonExtensions.ToJson(jsonObj));
                }
                else
                {
                    dispatch = SykesDispatch.GetByCallRequestId(callRequest.CallRequestId);
                }
            }
            catch (Exception)
            {
                return new { CallRequest = callRequest, SykesDispatch = dispatch };
            }
            return new { CallRequest = callRequest, SykesDispatch = dispatch };
        }
    }
}
    
