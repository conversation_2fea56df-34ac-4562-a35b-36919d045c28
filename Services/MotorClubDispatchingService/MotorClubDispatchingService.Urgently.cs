using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using Azure.Messaging.ServiceBus;
using Extric.Towbook.Accounts;
using Extric.Towbook.Dispatch;
using Extric.Towbook.Integration;
using Extric.Towbook.Integration.MotorClubs;
using Extric.Towbook.Integration.MotorClubs.Dispatch;
using Extric.Towbook.Integration.MotorClubs.Queue;
using Extric.Towbook.Integration.MotorClubs.Services;
using Extric.Towbook.Integrations.MotorClubs.Urgently;
using Extric.Towbook.Storage;
using Extric.Towbook.Utility;
using Microsoft.Extensions.Hosting;
using Newtonsoft.Json;
using Extric.Towbook.Integration.MotorClubs.Model;
using NLog;
using static Extric.Towbook.Vehicle.VehicleUtility;
using NewRelic.Api.Agent;

namespace Extric.Towbook.Services.MotorClubDispatchingService
{
    public partial class MotorClubDispatchingService : IHostedService
    {
        public static bool disableUrgently = false;
        public static readonly AuthenticationToken authtokenUrgently = new AuthenticationToken()
        {
            UserId = 14,
            ClientVersionId = MyClientVersionId
        };

        public static async Task<CallRequest> OonUrgentlyCreateCallRequest(Account a, UrgentlyRestClient.JobOfferModel jo)
        {
            var ulMsg = jo;
            var ulPid = Integrations.Email.EmailAddress.GetByCompanyId(a.CompanyId).FirstOrDefault()?.Address;

            if (ulPid == null)
                return null;


            if (a.Status == AccountStatus.Inactive)
                return null;

            string startingLocation = ulMsg.serviceLocation.ToString();
            string towDestination = ulMsg.dropOffLocation?.ToString() ?? "";
            string vehicle = ulMsg.vehicle?.ToString();

            var ulRequest = new CallRequest()
            {
                AccountId = a.Id,
                CompanyId = a.CompanyId,
                ExpirationDate = ulMsg.service.OfferExpiresLocal,
                PurchaseOrderNumber = ulMsg.service.id.ToString(),
                Reason = ulMsg.service.name,
                RequestDate = DateTime.Now,
                ServiceNeeded = ulMsg.service.name,
                StartingLocation = startingLocation.Truncate(512),
                TowDestination = towDestination.Truncate(512),
                Vehicle = vehicle.Truncate(500),
                MaxEta = 120,
                ProviderId = ulPid
            };

            ulRequest.Drivers = string.Join(", ", Driver.GetByCompany(await Company.Company.GetByIdAsync(a.CompanyId)).Where(o => o.EndDate == null).Select(o => o.Id)).Truncate(500);

            var deliver = false;
            var rejectInstantly = AccountKeyValue.GetFirstValueOrNull(ulRequest.CompanyId, ulRequest.AccountId,
                Provider.Towbook.ProviderId, "DigitalAutoAcceptInstantly") == "999";

            if (rejectInstantly)
            {
                await ulRequest.Save();
            }
            else
            {
                await ulRequest.Save();
                deliver = true;
            }

            var ulDispatch = new UrgentlyJobOffer()
            {
                CallRequestId = ulRequest.CallRequestId,
                OfferJson = JsonExtensions.ToJson(ulMsg),
                ProviderId = ulPid,
                CaseId = ulMsg.context.caseId,
                JobNumber = ulMsg.service.id
            };

            ulDispatch.Save();

            if (ulRequest.ExpirationDate != null)
                await SendExpiredRequestEvent(ulRequest.CallRequestId, ulRequest.ExpirationDate.Value);

            if (deliver)
                await ulRequest.Deliver();

            return ulRequest;
        }/// <summary>
         /// Given a person's first and last name, we'll make our best guess to extract up to two initials, hopefully
         /// representing their first and last name, skipping any middle initials, Jr/Sr/III suffixes, etc. The letters 
         /// will be returned together in ALL CAPS, e.g. "TW". 
         /// 
         /// The way it parses names for many common styles:
         /// 
         /// Mason Zhwiti                -> MZ
         /// mason lowercase zhwiti      -> MZ
         /// Mason G Zhwiti              -> MZ
         /// Mason G. Zhwiti             -> MZ
         /// John Queue Public           -> JP
         /// John Q. Public, Jr.         -> JP
         /// John Q Public Jr.           -> JP
         /// Thurston Howell III         -> TH
         /// Thurston Howell, III        -> TH
         /// Malcolm X                   -> MX
         /// A Ron                       -> AR
         /// A A Ron                     -> AR
         /// Madonna                     -> M
         /// Chris O'Donnell             -> CO
         /// Malcolm McDowell            -> MM
         /// Robert "Rocky" Balboa, Sr.  -> RB
         /// 1Bobby 2Tables              -> BT
         /// �ric �gor                   -> ��
         /// ??? ???                 -> ??
         /// 
         /// </summary>
         /// <param name="name">The full name of a person.</param>
         /// <returns>One to two uppercase initials, without punctuation.</returns>
        public static string ExtractInitialsFromName(string name)
        {
            // first remove all: punctuation, separator chars, control chars, and numbers (unicode style regexes)
            string initials = Regex.Replace(name, @"[\p{P}\p{S}\p{C}\p{N}]+", "");

            // Replacing all possible whitespace/separator characters (unicode style), with a single, regular ascii space.
            initials = Regex.Replace(initials, @"\p{Z}+", " ");

            // Remove all Sr, Jr, I, II, III, IV, V, VI, VII, VIII, IX at the end of names
            initials = Regex.Replace(initials.Trim(), @"\s+(?:[JS]R|I{1,3}|I[VX]|VI{0,3})$", "", RegexOptions.IgnoreCase);

            // Extract up to 2 initials from the remaining cleaned name.
            initials = Regex.Replace(initials, @"^(\p{L})[^\s]*(?:\s+(?:\p{L}+\s+(?=\p{L}))?(?:(\p{L})\p{L}*)?)?$", "$1$2").Trim();

            if (initials.Length > 2)
            {
                // Worst case scenario, everything failed, just grab the first two letters of what we have left.
                initials = initials.Substring(0, 2);
            }

            return initials.ToUpperInvariant();
        }

        [Transaction]
        public static async Task<bool> HandleUrgentlyIncomingMessage(DigitalDispatchActionQueueItem qi, UrgentlyMessage jsonObj, ProcessMessageEventArgs sourceMessage)
        {
            var serviceCallsOnly = CompanyKeyValue.GetFirstValueOrNull(qi.CompanyId.GetValueOrDefault(), 
                Provider.Towbook.ProviderId, "ServiceCallsOnly") == "1";

            var virtualTruckMode = CompanyKeyValue.GetFirstValueOrNull(qi.CompanyId.GetValueOrDefault(),
                Provider.Towbook.ProviderId, "UrgentlyVirtualTrucks") == "1";

            if (qi?.CompanyId == 155249 &&
                qi.Type != DigitalDispatchActionQueueItemType.IncomingCallReceived)
            {
                return await GatewayForwardInboundUrgently(qi, sourceMessage);
            }

            if (!disableUrgently)
                switch (qi.Type)
                {
                    case DigitalDispatchActionQueueItemType.IncomingCallReceived:
                        var masterAccountId = MasterAccountTypes.Urgently;

                        if (jsonObj.ProviderId == "out-of-network-incoming")
                            masterAccountId = MasterAccountTypes.OonUrgently;

                        logger.Info(masterAccountId,
                            "CallReceived",
                            "Incoming Job Offer",
                            jsonObj.ProviderId, null, jsonObj.CaseId, qi.CompanyId,
                            new
                            {
                                request = jsonObj
                            },
                            queueItemId: qi.QueueItemId);

                        if (qi.AccountId != null)
                        {
                            var acc = await Account.GetByIdAsync(qi.AccountId.Value);
                            if (acc == null || acc.Status == AccountStatus.Inactive)
                            {
                                logger.Info(MasterAccountTypes.Urgently,
                                    "CallReceived",
                                    "Incoming Job Offer received for Disabled Account - Ignoring",
                                    jsonObj.ProviderId, null, jsonObj.CaseId, qi.CompanyId,
                                    queueItemId: qi.QueueItemId);

                                DigitalDispatchActionQueueItem.UpdateStatus(qi,
                                    DigitalDispatchActionQueueItemStatus.Completed);

                                break;
                            }
                        }

                        var jo = JsonConvert.DeserializeObject<UrgentlyRestClient.JobOfferModel>(jsonObj.JsonData);
                        if (masterAccountId == MasterAccountTypes.OonUrgently)
                        {
                            var client = UrgentlyRestClient.GetProduction();

                            if (jo.dropOffLocation?.Latitude != null &&
                                jo.dropOffLocation.Latitude != jo.serviceLocation?.Latitude &&
                                jo.service?.ServicePrice?.TotalOfferPrice != null)
                            {
                                var loadedMileage = await OutOfNetworkIncoming.GetDistanceFromGoogleAsync(
                                    jo.serviceLocation.Latitude,
                                    jo.serviceLocation.Longitude,
                                    jo.dropOffLocation.Latitude,
                                    jo.dropOffLocation.Longitude, true);

                                if (loadedMileage > 0 &&
                                    jo.service?.ServicePrice?.TotalOfferPrice > 0 &&
                                    jo.service.ServicePrice.TotalOfferPrice < loadedMileage)
                                {
                                    client.Reject(jsonObj.CaseId);

                                    logger.Info(masterAccountId,
                                        "CallReceived",
                                        "Incoming Job Offer Ignored due to Loaded Distance being < $1/mile",
                                        "OON", null, jo.service.id.ToString(),
                                        qi.CompanyId,
                                        "{loadedMiles:" + loadedMileage + "}",
                                        queueItemId: qi.QueueItemId);

                                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                                    break;
                                }

                                if (jo.service.name == "Mobile Mechanic" ||
                                    jo.service.name == "Dealer Tire Service")
                                {
                                    client.Reject(jsonObj.CaseId);

                                    logger.Info(masterAccountId,
                                        "CallReceived",
                                        "Incoming Job Offer Ignored due to reason of: " + jo.service.name,
                                        "OON", null, jo.service.id.ToString(),
                                        qi.CompanyId,
                                        "{loadedMiles:" + loadedMileage + "}",
                                        queueItemId: qi.QueueItemId);

                                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                                    break;
                                }
                            }
                            // handle OON

                            // find providers
                            var oon = await OutOfNetworkIncoming.FindNonUrgentlyProvidersByLatLong(
                                jo.serviceLocation.Latitude,
                                jo.serviceLocation.Longitude,
                                jo.service.name);

                            if (!oon.Any())
                            {

                                client.Reject(jsonObj.CaseId);

                                logger.Info(MasterAccountTypes.OonUrgently,
                                    "CallReceived",
                                    "Rejected - No available providers nearby",
                                    jsonObj.ProviderId, null, jsonObj.CaseId, null,
                                   new
                                   {
                                       latitude = jo.serviceLocation.Latitude,
                                       longitude = jo.serviceLocation.Longitude
                                   },
                                   queueItemId: qi.QueueItemId);
                                // auto reject, no providers found.
                            }
                            else
                            {
                                var requests = new List<CallRequest>();

                                foreach (var x in oon)
                                {
                                    try
                                    {
                                        var account = await OutOfNetworkIncoming.OonCreateAccount(x.CompanyId, x.AccountId, MasterAccountTypes.OonUrgently,
                                            qi.QueueItemId);


                                        if (account != null && account.Status != AccountStatus.Inactive)
                                        {
                                            if (x.AccountId == 0)
                                                x.AccountId = account.Id;

                                            var ema = Integrations.Email.EmailAddress.GetByCompanyId(x.CompanyId).FirstOrDefault()?.Address;
                                            if (ema == null)
                                            {
                                                var ea = new Integrations.Email.EmailAddress();
                                                ea.CompanyId = account.CompanyId;
                                                ea.Address = ExtractInitialsFromName((await Company.Company.GetByIdAsync(account.CompanyId)).Name) + x.CompanyId + "@towbook.net";
                                                ea.DomainId = 1;
                                                ea.OwnerUserId = 1;
                                                ea.Save();

                                                ema = ea.Address;
                                            }

                                            var upx = UrgentlyProvider.GetByProviderId(ema);

                                            if (upx == null)
                                            {
                                                upx = new UrgentlyProvider()
                                                {
                                                    CompanyId = x.CompanyId,
                                                    AccountId = x.AccountId,
                                                    MasterAccountId = MasterAccountTypes.OonUrgently,
                                                    ProviderId = ema
                                                };
                                                upx.Save();
                                            }

                                            x.AccountId = account.Id;
                                            var ooncr = await OonUrgentlyCreateCallRequest(account, jo);
                                            if (ooncr != null)
                                                requests.Add(ooncr);
                                        }
                                    }
                                    catch (Exception ry)
                                    {
                                        logger.Info(MasterAccountTypes.OonUrgently,
                                            "CallReceived",
                                            "Incoming Job Offers Creation Failure",
                                            jsonObj.ProviderId, null, jsonObj.CaseId, x.CompanyId,
                                           new
                                           {
                                               exception = ry
                                           },
                                           queueItemId: qi.QueueItemId);
                                    }
                                }

                                logger.Info(MasterAccountTypes.OonUrgently,
                                    "CallReceived",
                                    "Incoming Job Offers Created",
                                    jsonObj.ProviderId, null, jsonObj.CaseId, null,
                                    new
                                    {
                                       callRequests = requests.Select(o => new { o.CallRequestId, o.CompanyId }).ToArray(),
                                       latitude = jo.serviceLocation.Latitude,
                                       longitude = jo.serviceLocation.Longitude,
                                       providers = oon,
                                    },
                                    poNumber: jo.service.id.ToString(),
                                    queueItemId: qi.QueueItemId);

                                Console.WriteLine(oon.ToJson(true));
                            }


                            // and then fan out new service bus events for each provider offer
                            // similar ot what we do in OutOfNetworkIncoming. 

                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                            break;
                        }

                        bool noMatches = false;
                        try
                        {
                            if (!serviceCallsOnly && !virtualTruckMode)
                            {
                                var drivers = new System.Collections.ObjectModel.Collection<int>();
                                var urgentlyProvider = Provider.GetByName("Urgent.ly");
                                if (jo.provider?.vehicles != null)
                                {
                                    foreach (var truck in jo.provider.vehicles.Select(o => Convert.ToInt32(o.id)))
                                    {
                                        drivers = drivers.Union(DriverTruckDefault.GetByTruckId(truck).Select(o => o.DriverId)).ToCollection();
                                    }
                                }

                                var goodDrivers = new System.Collections.ObjectModel.Collection<int>();

                                foreach (var driver in drivers)
                                {
                                    var isRegistered = DriverKeyValue.GetByDriver(qi.CompanyId.GetValueOrDefault(),
                                        driver, urgentlyProvider.ProviderId, "IsRegistered").FirstOrDefault();
                                    if (isRegistered?.Value == "1")
                                        goodDrivers.Add(driver);
                                }
                                
                                // no driver, so expose everything.
                                if (!goodDrivers.Any())
                                    noMatches = true;

                                if (jo.provider.vehicles == null || !jo.provider.vehicles.Any() || noMatches)
                                {
                                    var drivers2 = (await Driver.GetByExactCompanyIdAsync(qi.CompanyId.Value))
                                        .Where(o => o.EndDate == null);

                                    var trucks = DriverTruckDefault.GetByCompanyId(qi.CompanyId.Value)
                                        .Where(o => drivers2.Any(d => d.Id == o.DriverId) &&
                                        o.TruckId > 0).Select(o => o.TruckId);

                                    jo.provider.vehicles = jo.provider.vehicles.Union(
                                        trucks.Select(o => new UrgentlyRestClient.Vehicle1()
                                        { id = o.ToString() })).ToArray();
                                }
                            }
                            else
                            {
                                if (jo.provider.vehicles == null ||
                                    !jo.provider.vehicles.Any(o => o.id.StartsWith("virtual-")))
                                {
                                    var virtualTrucks = (await Driver.GetByExactCompanyIdAsync(qi.CompanyId.Value))
                                        .Where(o => o.EndDate == null).Select(o => "virtual-" + o.Id);

                                    jo.provider.vehicles = jo.provider.vehicles.Union(
                                        virtualTrucks.Select(o => new UrgentlyRestClient.Vehicle1()
                                        { id = o.ToString() })).ToArray();
                                }
                            }
                        }
                        catch (Exception rs)
                        {
                            logger.Error(MasterAccountTypes.Urgently,
                                "CallReceived",
                                "Incoming Job Offer ERROR",
                                jsonObj.ProviderId, null, jsonObj.CaseId, qi.CompanyId,
                                new
                                {
                                    exception = rs
                                },
                                poNumber: jo.service.id.ToString(),
                                queueItemId: qi.QueueItemId);
                        }

                        var rcr = await CreateCallRequest(jo, MotorClubName.Urgently);

                        qi.CallRequestId = rcr.CallRequestId;
                        DigitalDispatchService.LogAction(qi);

                        logger.Info(MasterAccountTypes.Urgently,
                            "CallReceived",
                            "Incoming Job Offer Created",
                            jsonObj.ProviderId, null,
                            jsonObj.CaseId, qi.CompanyId,
                            queueItemId: qi.QueueItemId,
                            poNumber: jo.service.id.ToString(),
                            callRequestId: rcr.CallRequestId);

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                        break;

                    case DigitalDispatchActionQueueItemType.IncomingCallUpdate:
                        logger.Log(LogLevel.Info, "Urgent.ly/{0}/IncomingCallUpdate: {2}",
                            qi.QueueItemId, qi.Type, jsonObj.ToJson());

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);

                        break;

                    case DigitalDispatchActionQueueItemType.IncomingCallCancelled:
                        var cc = JsonConvert.DeserializeObject<UrgentlyRestClient.JobCancelModel>(jsonObj.JsonData);

                        if (jsonObj.ProviderId == "oon" && cc.reason == null)
                        {
                            var cases = UrgentlyJobOffer.GetByCaseId(cc.context.caseId);
                            foreach (var c in cases)
                            {
                                var cr = CallRequest.GetById(c.CallRequestId);
                                if (cr != null && (
                                    cr.Status != CallRequestStatus.Accepted &&
                                    cr.Status != CallRequestStatus.Rejected))
                                    await cr.UpdateStatus(CallRequestStatus.Cancelled);

                                logger.Error(MasterAccountTypes.OonUrgently,
                                    "IncomingCallCancelled",
                                    "Cancelled",
                                    c.ProviderId, null, c.CaseId, cr.CompanyId,
                                    cc.ToJson(),
                                    queueItemId: qi.QueueItemId);

                            }

                            await sourceMessage.CompleteAsync();

                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);

                            return true;
                        }
                        
                        var ccujo = UrgentlyJobOffer.GetByCaseId(cc.context.caseId, cc.provider?.id);
                        if (ccujo == null)
                        {
                            logger.Error(MasterAccountTypes.Urgently, 
                                "IncomingCallCancelled", 
                                "Received cancel for caseId that doesn't exist",
                                cc.provider?.id, null, cc?.context?.caseId, qi.CompanyId,
                                cc.ToJson(),
                                queueItemId: qi.QueueItemId);

                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                            await sourceMessage.CompleteAsync();
                            return true;
                        }

                        var ccr = CallRequest.GetById(ccujo.CallRequestId);

                        if (cc.reason != null && cc.reason.Contains("This job has been accepted by another provider."))
                        {
                            await ccr.UpdateStatus(CallRequestStatus.AnotherProviderResponded);

                            logger.Info(MasterAccountTypes.Urgently,
                                "IncomingCallCancelled",
                                "Another provider responded",
                                cc.provider.id, null, cc.context.caseId, qi.CompanyId,
                                cc.ToJson(),
                                queueItemId: qi.QueueItemId);
                        }
                        else
                        {
                            await ccr.UpdateStatus(CallRequestStatus.Cancelled);
                        }

                        bool cancelled = false;

                        if (ccr.DispatchEntryId.GetValueOrDefault() > 0)
                        {
                            var en = Entry.GetByIdNoCache(ccr.DispatchEntryId.Value);
                            if (!cc.provider.vehicles.Any() ||
                                cc.provider.vehicles.Any(o => o.id == ccujo.TruckId.ToString()) ||
                                cc.provider.vehicles.Any(o => o.id.Replace("virtual-", "") == en.DriverId.ToString()) ||
                                ccujo.DriverId == en.DriverId ||
                                ccujo.TruckId == 0)
                            {
                                
                                if (ShouldAllowCancel(en) && en != null && en.Status.Id != (int)Dispatch.Entry.EntryStatus.Canceled)
                                {
                                    await en.Cancel("Cancelled by Urgent.ly: " + cc.reason, authtokenUrgently, "127.0.0.1");

                                    // TODO: specify urgently user id
                                    cancelled = true;
                                }
                            }
                        }
                        qi.CallRequestId = ccr.CallRequestId;
                        DigitalDispatchService.LogAction(qi);
                        if (cancelled)
                        {
                            logger.Log(LogLevel.Info, "Urgent.ly/{0}: Offer Cancelled. Type = {1}, CallRequestId = {2}, Reason = {3}", qi.QueueItemId, qi.Type, ccr.CallRequestId, cc.reason);
                        }
                        else
                        {
                            logger.Log(LogLevel.Info, "Urgent.ly/{0}: Offer Cancell event received but not for the currently assigned vehicle. Type = {1}, CallRequestId = {2}, CancelledVehicle: {3}, CurrentVehicle:{4}", qi.QueueItemId, qi.Type, ccr.CallRequestId,
                                cc.provider.vehicles.Where(o => o.id == ccujo.TruckId.ToString()).ToJson(), ccujo.TruckId);
                        }

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                        break;

                    case DigitalDispatchActionQueueItemType.IncomingCallAccepted:
                        var accept = JsonConvert.DeserializeObject<UrgentlyRestClient.JobOfferModel>(jsonObj.JsonData);

                        logger.Info(MasterAccountTypes.Urgently,
                            "CallAccepted",
                            "Incoming Job Offer Accepted",
                            jsonObj.ProviderId, null, 
                            jsonObj.CaseId, 
                            qi.CompanyId,
                            new
                            {
                                message = jsonObj.JsonData
                            },
                            queueItemId: qi.QueueItemId);

                        var acceptJobOffer = UrgentlyJobOffer.GetByCaseId(accept.context.caseId, accept.provider.id);

                        if (acceptJobOffer != null &&
                            accept.provider?.vehicle?.id?.ToString() != acceptJobOffer?.TruckId.ToString() && 
                            !serviceCallsOnly && 
                            !virtualTruckMode)
                        {
                            // Update TruckId - It's different than what we have saved.

                            logger.Log(LogLevel.Info, "Urgent.ly/{0}/{1}/{2}/{3}: Call Accepted.. Truck doesn't match what we accepted with ... Towbook={4}, Urgently={5} JSON = {1} ",
                                qi.QueueItemId,
                                qi.CompanyId,
                                accept.provider.id,
                                accept.context.caseId,
                                accept.provider.vehicle?.id,
                                acceptJobOffer.TruckId);

                            if (accept.provider?.vehicle?.id != null)
                            {
                                if (int.TryParse(accept.provider.vehicle.id, out var n))
                                {
                                    acceptJobOffer.TruckId = n;
                                    acceptJobOffer.Save();
                                }
                            }
                        }

                        int callRequestId = 0;  
                        CallRequest callRequest = null; 

                        if (acceptJobOffer != null)
                        {
                            callRequestId = acceptJobOffer.CallRequestId;
                            callRequest = CallRequest.GetById(callRequestId);
                        }
                        else
                        {
                            // DUPLICATE CODE .. this should be turned into a method instead.

                            var ulMsg = accept;
                            var ulPid = ulMsg.provider.id;
                            var ulcr = UrgentlyProvider.GetByProviderId(ulPid);

                            DateTime? ulExpDate = ulMsg.service.OfferExpiresLocal;
                            if (ulExpDate < DateTime.Now && ulMsg.service.ScheduledLocal.GetValueOrDefault() > DateTime.Now)
                                ulExpDate = ulMsg.service.ScheduledLocal;

                            string startingLocation = ulMsg.serviceLocation?.ToString();
                            string towDestination = ulMsg.dropOffLocation?.ToString() ?? "";

                            // sometimes urgently sends over null vehicle...
                            string vehicle = ulMsg.vehicle?.ToString() ?? "Unknown"; 

                            var ulRequest = new CallRequest()
                            {
                                AccountId = ulcr.AccountId,
                                CompanyId = ulcr.CompanyId,
                                ExpirationDate = ulExpDate,
                                PurchaseOrderNumber = ulMsg.service.id.ToString(),
                                Reason = ulMsg.service.name,
                                RequestDate = DateTime.Now,
                                ServiceNeeded = ulMsg.service.name,
                                StartingLocation = startingLocation.Truncate(512),
                                TowDestination = towDestination.Truncate(512),
                                Vehicle = vehicle.Truncate(500),
                                MaxEta = 120,
                                ProviderId = ulPid
                            };

                            try
                            {
                                if (ulMsg.provider.vehicle != null)
                                    ulRequest.Trucks = ulMsg.provider.vehicle.id;
                                else if (ulMsg.provider.vehicles != null && !serviceCallsOnly && !virtualTruckMode)
                                {
                                    ulRequest.Trucks = string.Join(",", 
                                        ulMsg.provider.vehicles.Select(o => Convert.ToInt32(o.id)));

                                    if (ulRequest.Trucks.Length > 500)
                                    {
                                        ulRequest.Trucks = ulRequest.Trucks.Truncate(500);
                                        ulRequest.Trucks = ulRequest.Trucks.Substring(0,
                                            ulRequest.Trucks.LastIndexOf(',')).Trim(',');
                                    }
                                }

                                var goodDrivers = new System.Collections.ObjectModel.Collection<int>();

                                if (!serviceCallsOnly && !virtualTruckMode)
                                {
                                    var drivers = DriverTruckDefault.GetByTruckId(Convert.ToInt32(ulMsg.provider.vehicle.id))
                                        .Select(o => o.DriverId)
                                        .ToCollection();

                                    var urgentlyProvider = Provider.GetByName("Urgent.ly");
                                    foreach (var driver in drivers)
                                    {
                                        var isRegistered = DriverKeyValue.GetByDriver(ulcr.CompanyId, driver, urgentlyProvider.ProviderId, "IsRegistered").FirstOrDefault();
                                        if (isRegistered?.Value == "1")
                                            goodDrivers.Add(driver);
                                    }
                                }
                                else
                                {
                                    goodDrivers =
                                        ulMsg.provider.vehicles.Select(o =>
                                        Convert.ToInt32(o.id.Replace("virtual-", ""))).ToCollection();
                                }

                                ulRequest.Drivers = string.Join(",", goodDrivers);

                                if (ulRequest.Drivers.Length > 500)
                                {
                                    ulRequest.Drivers = ulRequest.Drivers.Truncate(500);
                                    ulRequest.Drivers = ulRequest.Drivers.Substring(0,
                                        ulRequest.Drivers.LastIndexOf(',')).Trim(',');
                                }
                            }
                            catch (Exception ex)
                            {
                                logger.Log(LogLevel.Error, "Urgent.ly/{1}/{2}/{3}: Urgently Error creating CallRequest: {0}",
                                    ex.ToString(), ulRequest.CompanyId, ulRequest.ProviderId, ulRequest.PurchaseOrderNumber);

                            }

                            await ulRequest.Save();

                            int truckId = 0;
                            int driverId = 0;
                            if(int.TryParse(ulMsg.provider?.vehicle?.id, out truckId))
                            {
                                var dtd = DriverTruckDefault.GetByTruckId(truckId).FirstOrDefault();
                                if (dtd != null)
                                    driverId = dtd.DriverId;
                            }

                            var ulDispatch = new UrgentlyJobOffer()
                            {
                                CallRequestId = ulRequest.CallRequestId,
                                OfferJson = ulMsg.ToJson(),
                                ProviderId = ulPid,
                                CaseId = ulMsg.context.caseId,
                                JobNumber = ulMsg.service.id,
                                TruckId = truckId,
                                DriverId = driverId
                            };

                            ulDispatch.Save();

                            callRequest = ulRequest;
                            callRequestId = ulRequest.CallRequestId;
                        }
                        
                        if (callRequest == null)
                        {
                            logger.LogEvent("Urgent.ly/{0}: Couldn't find an UrgentlyJobOffer with CaseID of {1}",
                                qi.CompanyId, LogLevel.Warn, sourceMessage.Message.MessageId, accept.context.caseId);
                        }

                        if (accept.vehicle == null || accept.service == null)
                        {
                            // replace with original offer.
                            accept = JsonConvert.DeserializeObject<UrgentlyRestClient.JobOfferModel>(acceptJobOffer.OfferJson);
                            Console.WriteLine(accept.ToJson(true));
                        }

                   
                        var fe = await DistributedLock.ForAsync("Urgently", 
                            accept.context.caseId, 10000,
                            lockAcquired: async delegate ()
                            {
                                Entry e = null;
                                e = await Entry.GetByPurchaseOrderNumberAsync(qi.AccountId.Value, accept.service.id.ToString());

                                if (e == null)
                                {
                                    e = new Entry();
                                    e.CompanyId = qi.CompanyId.Value;
                                    e.Notes = "";
                                }
                                else
                                {
                                    logger.LogEvent("Urgent.ly/{0}: Found existing towbook call for Case {1}... Call #{2}... we're going to update this one.",
                                        qi.CompanyId, LogLevel.Info, sourceMessage.Message.MessageId, accept.context.caseId, e.CallNumber);

                                    if (e.Status == Status.Cancelled)
                                    {
                                        logger.LogEvent("Urgent.ly/{0}: Call is cancelled. Proceeding to uncancel and set status to waiting...",
                                            qi.CompanyId, LogLevel.Info, sourceMessage.Message.MessageId, accept.context.caseId, e.CallNumber);
                                        await e.Uncancel(authtokenUrgently);
                                    }
                                }

                                e.AccountId = qi.AccountId.Value;

                                if (e.Account?.DefaultPriority == 1)
                                    e.Priority = Entry.EntryPriority.High;

                                EntryAsset asset = null;
                                if (e.Assets != null)
                                    asset = e.Assets.FirstOrDefault();

                                if (asset == null)
                                    asset = new EntryAsset() { BodyTypeId = 1 };

                                if (asset.BodyTypeId == 0)
                                    asset.BodyTypeId = 1;


                                if (!string.IsNullOrWhiteSpace(accept.provider?.driver?.id))
                                {
                                    if (int.TryParse(accept.provider.driver.id, out int tempDriverId))
                                    {
                                        acceptJobOffer.DriverId = tempDriverId;
                                    }
                                }

                                if (asset.Drivers.Any())
                                {
                                    var dtp = asset.Drivers.First();
                                    dtp.DriverId = acceptJobOffer.DriverId;
                                    dtp.TruckId = acceptJobOffer.TruckId;
                                }
                                else
                                {
                                    asset.Drivers.Add(new EntryAssetDriver() { DriverId = acceptJobOffer.DriverId, TruckId = acceptJobOffer.TruckId });
                                }

                                var vc = GetColorIdByName(accept.vehicle?.color);

                                if (!string.IsNullOrEmpty(accept.vehicle?.year))
                                    asset.Year = Convert.ToInt32(accept.vehicle.year);

                                asset.Make = GetManufacturerByName(accept.vehicle?.make);
                                asset.Model = GetModelByName(accept.vehicle?.model);

                                if (vc != 0)
                                    asset.ColorId = vc;

                                asset.LicenseNumber = accept.vehicle?.licensePlate;
                                asset.Vin = accept.vehicle?.vin;

                                var isExchange = 0;
                                var pickupName = "Pickup";

                                if (!string.IsNullOrWhiteSpace(accept.ExchangeVehiclePickupLocation?.Address))
                                {
                                    isExchange = 1;
                                    pickupName = "Customer Location";
                                    
                                    var tempPickup =  e.Waypoints.FirstOrDefault(o => o.Title == "Pickup");

                                    if (tempPickup != null)
                                        tempPickup.Title = pickupName;

                                    var exchange = e.Waypoints.FirstOrDefault(r => r.Title == "Replacement Pickup");
                                    if (exchange == null)
                                    {
                                        exchange = new EntryWaypoint() { Title = "Replacement Pickup" };
                                        e.Waypoints.Insert(0, exchange);
                                        exchange.Position = 1;
                                    }
                                    exchange.Address = accept.ExchangeVehiclePickupLocation.Address;
                                    exchange.Latitude = accept.ExchangeVehiclePickupLocation.Latitude;
                                    exchange.Longitude = accept.ExchangeVehiclePickupLocation.Longitude;
                                }

                                var pickup = e.Waypoints.FirstOrDefault(o => o.Title == pickupName);
                                var dest = e.Waypoints.FirstOrDefault(o => o.Title == "Destination");

                                if (e.Notes == null)
                                    e.Notes = "";

                                if (accept.serviceLocation != null)
                                {
                                    if (string.IsNullOrWhiteSpace(e.TowSource) || e.Version < 3)
                                    {
                                        e.TowSource = accept.serviceLocation.ToString();
                                        if (pickup == null)
                                        {
                                            pickup = new EntryWaypoint() { Title = pickupName };
                                            e.Waypoints.Add(pickup);
                                        }
                                        pickup = accept.serviceLocation.ToWaypoint(pickup);
                                        pickup.Position = 1 + isExchange;
                                    }
                                }

                                if (accept.dropOffLocation != null)
                                {
                                    if (string.IsNullOrWhiteSpace(e.TowDestination) || e.Version < 3)
                                    {
                                        e.TowDestination = accept.dropOffLocation.ToString();
                                        if (dest == null && !string.IsNullOrWhiteSpace(e.TowDestination))
                                        {
                                            dest = new EntryWaypoint() { Title = "Destination", Position = 2 + isExchange };
                                            e.Waypoints.Add(dest);
                                        }

                                        dest = accept.dropOffLocation.ToWaypoint(dest);
                                        dest.Position = 2 + isExchange;
                                    }
                                }

                                e.Waypoints = e.Waypoints.OrderBy(o => o.Position).ToCollection();

                                string addressesHuman = "";

                                if (e.Waypoints.Count > 2)
                                {
                                    foreach (var wp in e.Waypoints)
                                    {
                                        if (!string.IsNullOrWhiteSpace(wp.Address))
                                            addressesHuman += wp.Title + ": " + wp.Address + "\n";
                                    }
                                }

                                if (e.Notes == null || !e.Notes.Contains(addressesHuman))
                                    e.Notes = addressesHuman + "------\n" + e.Notes;

                                // don't use offerExpires time, and they dont have a transaction timestamp, so we'll just use current server time.
                                if (e.CreateDate == DateTime.MinValue)
                                    e.CreateDate = DateTime.Now.AddSeconds(-DateTime.Now.Second); // dont use accept.service.OfferExpiresLocal;

                                if (acceptJobOffer.Eta != null)
                                {
                                    e.ArrivalETA = e.CreateDate.AddMinutes(acceptJobOffer.Eta.Value);
                                }

                                if (accept.service.scheduled > 0)
                                {
                                    switch (accept.service.category)
                                    {
                                        case "RSA_SCHEDULED_SERVICE":
                                            e.ArrivalETA = accept.service.ScheduledLocal.Value;
                                            e.Notes += "** Scheduled Service Date: " + Core.OffsetDateTime(e.Company, accept.service.ScheduledLocal.Value) + "**\n";
                                            break;

                                        case "RSA_SCHEDULED_DROPOFF":
                                            e.ArrivalETA = accept.service.ScheduledLocal.Value;
                                            e.Notes += "** Vehicle must be delivered to Destination address by: " +         
                                                       Core.OffsetDateTime(e.Company, accept.service.ScheduledLocal.Value) + "**\n";
                                            break;
                                    }
                                }

                                if (accept.inspection != null)
                                {
                                    if (accept.inspection.preInspectionRequired)
                                    {
                                        e.Notes += "\n** PRE INSPECTION REQUIRED: " +
                                            (accept.inspection.preInspectionType == "PHOTO" ?
                                            accept.inspection.preInspectionPhotoCount + " photos required before servicing vehicle." :
                                            "video walking around vehicle to record current condition\n");

                                        if (accept.inspection.preInspectionSignatureRequired)
                                            e.Notes += "** PRE INSPECTION SIGNATURE REQUIRED: Please capture signature from customer confirming you took photos.\n";
                                    }

                                    if (accept.inspection.postInspectionRequired)
                                    {
                                        e.Notes += "** POST INSPECTION REQUIRED: " +
                                            (accept.inspection.postInspectionType == "PHOTO" ?
                                            accept.inspection.postInspectionPhotoCount + " photos required after job is done." :
                                            "video walking around vehicle\n");

                                        if (accept.inspection.postInspectionSignatureRequired)
                                            e.Notes += "** POST INSPECTION SIGNATURE REQUIRED: Please have customer sign after job is done.\n";
                                    }
                                }

                                #region po number
                                e.PurchaseOrderNumber = accept.service.id.ToString();
                                #endregion
                                
                                #region Reason

                                e.ReasonId = await ReasonHelper.DetermineReasonId(e.Account.MasterAccountId, qi.CompanyId.Value, accept.service.name);
                                if (e.ReasonId == 1635)
                                    e.Notes += "Service Needed: " + accept.service.name + "\n";

                                #endregion

                                if (callRequest != null)
                                {
                                    if (callRequest.OwnerUserId == null)
                                        callRequest.OwnerUserId = 1;

                                    e.CallRequestId = callRequest.CallRequestId;

                                    if (e.OwnerUserId < 100)
                                        e.OwnerUserId = callRequest.OwnerUserId.GetValueOrDefault(0);
                                }
                                else if (e.OwnerUserId < 100)
                                    e.OwnerUserId = 1;

                                void addNote(string line, bool top = false)
                                {
                                    if (string.IsNullOrWhiteSpace(line))
                                        return;


                                    if (e.Notes == null || !e.Notes.Contains(line))
                                    {
                                        if (e.Notes == null)
                                            e.Notes = line + "\n";
                                        else
                                        {
                                            if (top)
                                                e.Notes = line + e.Notes.Trim('\n') + "\n";
                                            else
                                                e.Notes += "\n" + line.Trim('\n');
                                        }
                                    }
                                }

                                if (accept.service?.ServicePrice?.TotalOfferPrice != null)
                                {
                                    var bn = e.BillingNotes();
                                    var toAdd = "Urgent.ly Job Offer Amount: " + accept.service.ServicePrice.TotalOfferPrice.Value.ToString("C");
                                    if (!bn.Contains(toAdd))
                                    {
                                        bn = toAdd + "\n" + bn;
                                        e.SetAttribute(Dispatch.AttributeValue.BUILTIN_BILLING_NOTES, bn);
                                    }
                                }
                                else
                                {
                                    addNote("Rate Card: " + Core.FormatName(accept.service?.rateCard) + " " +
                                        accept.service?.rateCardAsDollars, true);
                                }

                                if (!string.IsNullOrWhiteSpace(accept.service?.partnerName) &&
                                    e.Account?.MasterAccountId != MasterAccountTypes.OonUrgently)
                                    addNote("Partner Name: " + accept.service.partnerName);

                                if (accept.service.IsUnAttendedTow)
                                    addNote("** This is an Unattended Tow **", true);

                                if (!string.IsNullOrWhiteSpace(accept.service?.disablementReasonNote))
                                    addNote("Disablement Reason Notes: " + accept?.service?.disablementReasonNote);

                                if (!string.IsNullOrWhiteSpace(accept.service?.jobNotes))
                                {
                                    accept.service.jobNotes = accept.service.jobNotes.Replace(":\n", ": ").Replace("?\n", "? ").Replace("? \n", "? ");
                                    addNote("Job Notes: " + accept.service.jobNotes);
                                }



                                if (!string.IsNullOrWhiteSpace(accept.JobInstructionUrl))
                                    e.SetAttribute(Dispatch.AttributeValue.BUILTIN_REFERENCE_URL, accept.JobInstructionUrl);

                                await ApplyRoundRobinDispatcherLogicAsync(qi, e, jsonObj?.CaseId, jsonObj?.ProviderId);

                                if (e.Assets == null || e.Assets.Count == 0)
                                {
                                    e.Assets = new System.Collections.ObjectModel.Collection<EntryAsset>();
                                    e.Assets.Add(asset);
                                }

                                var customerName = Core.FormatName(accept.customer.name);
                                var phone = Core.FormatPhone(accept.customer.phone);

                                EntryContact c = e.Contacts.FirstOrDefault(o =>
                                    o.Name?.ToLowerInvariant() == customerName?.ToLowerInvariant() ||
                                    Core.FormatPhone(o.Phone) == phone);

                                bool newContact = false;
                                if (c == null)
                                {
                                    c = new EntryContact() { Name = customerName };
                                    e.Contacts.Add(c);
                                    newContact = true;
                                }

                                c.Phone = phone;

                                if (e.Account.MasterAccountId == MasterAccountTypes.OonUrgently &&
                                    accept.service?.ServicePrice?.TotalOfferPrice != null)
                                {
                                    var oa = e.InvoiceItems.FirstOrDefault(o => o.Name == OON_LINE_ITEM_NAME);
                                    if (oa == null)
                                    {
                                        e.InvoiceItems.Add(new InvoiceItem()
                                        {
                                            CustomName = OON_LINE_ITEM_NAME,
                                            Quantity = 1,
                                            CustomPrice = accept.service.ServicePrice.TotalOfferPrice
                                        });
                                    }

                                    if (!e.Contacts.Any(o => o.Name == "Urgently"))
                                    {
                                        e.Contacts.Add(new Dispatch.EntryContact() { Name = "Urgently", Phone = "************" });
                                    }

                                    addNote("Your driver must have GPS enabled in Towbook to confirm that the " +
                                        (e.TowDestination != null ? "pickup and tow destination was reached." : "service location was reached. ") +
                                        "\nIf you have any questions, Urgently can be contacted at " + Core.FormatPhone("************"), true);
                                }

                                await e.Save(false, authtokenUrgently);

                                if(newContact)
                                    await CheckForRoadsideFeatureAndAutoInvite(e, c);

                                await AutoDispatch.AutoDispatchServiceBusHandler.Send(e);

                                return e;
                            },
                            alreadyLocked: async delegate ()
                            {
                                logger.LogEvent("{0}/CR{1}: Lock already exists for {2}:{3}... pausing 250ms",
                                    qi.CompanyId, LogLevel.Warn,
                                    sourceMessage.Message.MessageId,
                                    (callRequest != null ? callRequest.CallRequestId.ToString() : "NULL"),
                                    qi.AccountId.Value,
                                    accept.context.caseId);

                                await Task.Delay(250);

                                return true;
                            });

                        if (fe == null)
                        {
                            logger.LogEvent("Urgent.ly/{0}: Creation of call failed; {1}",
                                qi.CompanyId, LogLevel.Error, sourceMessage.Message.MessageId, qi.ToJson());

                            await sourceMessage.CompleteAsync();
                        }
                        else
                        {
                            if (callRequest != null)
                            {
                                callRequest.DispatchEntryId = fe.Id;
                                await callRequest.Save();

                                qi.CallRequestId = callRequest.CallRequestId;
                                DigitalDispatchService.LogAction(qi);

                                await callRequest.UpdateStatus(CallRequestStatus.Accepted, po: fe.PurchaseOrderNumber);
                                DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);

                                logger.Info(fe.Account.MasterAccountId, "CallCreated",
                                   $"Created new towbook call for {MasterAccountTypes.GetName(fe.Account.MasterAccountId)}",
                                   companyId: qi.CompanyId,
                                   data: new
                                   {
                                       waypoints = fe.Waypoints.Select(o => new { o.Address, o.Latitude, o.Longitude })
                                   },
                                   dispatchId: jsonObj.CaseId,
                                   callId: fe?.Id,
                                   callRequestId: callRequest.CallRequestId,
                                   queueItemId: qi.QueueItemId,
                                   poNumber: fe?.PurchaseOrderNumber,
                                   callNumber: fe?.CallNumber);
                            }
                        }
                      
                        break;
                       
                    default:
                        logger.Log(LogLevel.Fatal, "Urgent.ly/{0}: Unhandled Event Type: Type = {1}, {2}",
                            qi.QueueItemId, qi.Type, jsonObj.ToJson());
                        break;
                }
            
            if (sourceMessage != null)
                await sourceMessage.CompleteAsync();

            return true;
        }

        [Transaction]
        public static async Task HandleUrgentlyQueueOutgoingMessage(DigitalDispatchActionQueueItem qi, dynamic jsonObj, ProcessMessageEventArgs sourceMessage)
        {
            var serviceCallsOnly = CompanyKeyValue.GetFirstValueOrNull(qi.CompanyId.GetValueOrDefault(),
                 Provider.Towbook.ProviderId, "ServiceCallsOnly") == "1";

            var virtualTruckMode = CompanyKeyValue.GetFirstValueOrNull(qi.CompanyId.GetValueOrDefault(),
                Provider.Towbook.ProviderId, "UrgentlyVirtualTrucks") == "1";

            var dev = new int[] { 10000, 100769, 100770, 100771, 154042 }.Contains(qi.CompanyId.GetValueOrDefault());

            UrgentlyRestClient client = (dev ? UrgentlyRestClient.Get() : UrgentlyRestClient.GetProduction());
            {
                if (!disableUrgently)
                {
                    if (qi.Type == DigitalDispatchActionQueueItemType.InternalSyncCompanyResources)
                    {
                        UrgentlyProvider up = null;

                        try
                        {
                            if (qi.CompanyId == 155249 || qi.CompanyId == 52117)
                            {
                                // dont do it for this account. it's not built to do a sync.
                                DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                                await sourceMessage.CompleteAsync();
                                return;
                            }
                            up = UrgentlyProvider.GetByCompanyId(qi.CompanyId.Value).FirstOrDefault();
                            await RegisterTrucksAndDrivers(up);
                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                        }
                        catch (Exception e)
                        {
                            logger.Log(LogLevel.Error, "Urgent.ly/" + up.ProviderId + "/C" + up.CompanyId + "/SyncCompanyResources: " +
                                e.ToString());
                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                        }
                        finally
                        {
                            await sourceMessage.CompleteAsync();
                        }

                        return;
                    }

                    CallRequest cr = CallRequest.GetById(Convert.ToInt32(jsonObj.CallRequestId ?? jsonObj.Id));
                    
                    cr.MasterAccountId = (await Account.GetByIdAsync(cr.AccountId)).MasterAccountId;

                    if (cr == null)
                    {
                        logger.Log(LogLevel.Error, "CR" + jsonObj.Id + ": Couldn't find CallRequestId.");
                        logger.Log(LogLevel.Error, "CR" + jsonObj.Id + ": " + JsonExtensions.ToJson(jsonObj));

                        await sourceMessage.DeadLetterAsync();
                        return;
                    }

                    var ad = UrgentlyJobOffer.GetByCallRequestId(cr.CallRequestId);

                    if (ad == null)
                    {
                        logger.Log(LogLevel.Error, "Urgent.ly/CR" + cr.CallRequestId + "/C" + cr.CompanyId + "/OutgoingCallCanceled: Couldn't find UrgentlyJobOffer");

                        await sourceMessage.DeadLetterAsync();
                        return;
                    }

                    var jo = JsonConvert.DeserializeObject<UrgentlyRestClient.JobOfferModel>(ad.OfferJson);
                    var request = cr;
                    var dispatch = ad;

                    request.OwnerUserId = Convert.ToInt32(jsonObj.OwnerUserId);

                    switch (qi.Type)
                    {

                        case DigitalDispatchActionQueueItemType.OutgoingAcceptCall:
                            dispatch.Eta = jsonObj.Eta;
                            dispatch.Save();

                            try
                            {
                                logger.Info(request.MasterAccountId,
                                    "AcceptCall",
                                    "Outgoing Job Accept",
                                    dispatch.ProviderId, null, dispatch.CaseId, qi.CompanyId,
                                    new
                                    {
                                        json = JsonExtensions.ToJson(jsonObj)
                                    },
                                    callRequestId: request.CallRequestId,
                                    queueItemId: qi.QueueItemId);

                                string error = null;

                                if (request.MasterAccountId == MasterAccountTypes.OonUrgently)
                                {
                                    // TODO: Insert a distributed lock based on the caseId so that two providers accepting at the same time
                                    // doesn't create a race condition and cancel each other out.

                                    var providerId = Integrations.Email.EmailAddress.GetByCompanyId(request.CompanyId).FirstOrDefault()?.Address;

                                    Driver driver = await Driver.GetByIdAsync((int)jsonObj.DriverId);
                                    var drn = UrgentlyRestClient.Driver.NameToFirstLast(driver.Name);

                                    var dtd = DriverTruckDefault.GetByDriverId(driver.Id);

                                    var pvd = new UrgentlyRestClient.AcceptModel.ProviderVehicleModel();

                                    if (dtd != null)
                                    {
                                        var truck = await Truck.GetByIdAsync(dtd.TruckId);
                                        pvd.Color = "Unknown";
                                        pvd.Make = truck.Manufacturer ?? "Unspecified";
                                        pvd.Model = truck.Model ?? "Unspecified";
                                        pvd.Name = truck.Name ?? "Unknown";
                                        pvd.Id = truck.Id.ToString();
                                        pvd.Type = truck.Type == Truck.TruckType.FlatBed ? "FLAT_BED" :
                                                   truck.Type == Truck.TruckType.Wrecker ? "TOW_TRUCK" :
                                                   "RSA";
                                        pvd.LicensePlate = "UNKNOWN";
                                        dispatch.TruckId = truck.Id;
                                    }
                                    else
                                    {
                                        pvd.Color = "Unknown";
                                        pvd.Id = "virtual-" + driver.Id;
                                        pvd.Make = "Generic";
                                        pvd.Model = "Generic";
                                        pvd.LicensePlate = "UNKNOWN";
                                        pvd.Name = driver.Name?.Trim() + "'s Vehicle";
                                        pvd.Type = "RSA";
                                    }

                                    var company = await Company.Company.GetByIdAsync(request.CompanyId);
                                    Console.WriteLine("company:" + company);
                                    Console.WriteLine("driver:" + drn);
                                    Console.WriteLine("up:" + providerId.ToJson());
                                    Console.WriteLine("qi:" + qi.OwnerUserId);

                                    var oonResponse = client.Interested(dispatch.CaseId, new UrgentlyRestClient.AcceptModel()
                                    {
                                        Provider = await UrgentlyRestClient.AcceptModel.ProviderAcceptModel.FromCompany(company,
                                            providerId, (await User.GetByIdAsync(qi.OwnerUserId.Value)).FullName),
                                        Driver = new UrgentlyRestClient.AcceptModel.ProviderDriverModel()
                                        {
                                            FirstName = drn.FirstName,
                                            LastName = drn.LastName,
                                            Id = driver.Id.ToString(),
                                            Phone = Core.FormatPhoneWithDashesOnly(company.Phone)
                                        },
                                        Vehicle = pvd,
                                        Eta = dispatch.Eta.Value
                                    });
                                    Console.WriteLine(oonResponse.ToJson(true));

                                    dispatch.DriverId = driver.Id;
                                    dispatch.TruckId = dtd?.TruckId ?? 0;

                                    dispatch.Save();

                                    if (!oonResponse.IsSuccessful)
                                    {
                                        logger.Error(MasterAccountTypes.OonUrgently,
                                            "AcceptCall",
                                            "Accept failed. Hopefully others will respond.",
                                            providerId, null, dispatch?.CaseId, qi?.CompanyId,
                                            new
                                            {
                                                text = oonResponse.Message,
                                                json = oonResponse.ToJson(),
                                                code = oonResponse.StatusCode
                                            },
                                            callRequestId: dispatch.CallRequestId,
                                            queueItemId: qi.QueueItemId);

                                        if (oonResponse.StatusCode == 409)
                                        {
                                            // let other providers respond.
                                        }
                                    }
                                    else
                                    {
                                        logger.Info(MasterAccountTypes.OonUrgently,
                                            "AcceptCall",
                                            "Sent accept/interest successfully.",
                                            providerId, null, dispatch.CaseId, qi.CompanyId,
                                            new
                                            {
                                                json = oonResponse.RequestJson,
                                                text = oonResponse.Message
                                            },
                                            callRequestId: dispatch.CallRequestId,
                                            queueItemId: qi.QueueItemId);

                                        foreach (var y in UrgentlyJobOffer.GetByCaseId(dispatch.CaseId))
                                        {
                                            if (y.CallRequestId == dispatch.CallRequestId)
                                                continue;

                                            var crx = CallRequest.GetById(y.CallRequestId);
                                            if (crx != null && crx.Status != CallRequestStatus.Accepted)
                                                await crx.UpdateStatus(CallRequestStatus.AnotherProviderResponded);
                                        }
                                    }
                                }
                                else
                                {
                                    async Task<bool> doAcceptAsync()
                                    {
                                        decimal? lat = 0.00m; // TODO: get from truck/user below.
                                        decimal? lng = 0.00m;

                                        var newStatus = UrgentlyRestClient.JobStatusUpdateAction.Accept;

                                        var company = await Company.Company.GetByIdAsync(request.CompanyId);

                                        int? truckId = (int?)jsonObj.TruckId;
                                        int? driverId = (int?)jsonObj.DriverId;

                                        if ((jo.provider.vehicles == null ||
                                            jo.provider.vehicles.Length == 0) &&
                                            (jo.service.category == "RSA_SCHEDULED_SERVICE" ||
                                                jo.service.category == "RSA_SCHEDULED_DROPOFF"))
                                        {
                                            // must be scheduled
                                            truckId = null;
                                            driverId = null;

                                        }
                                        else if (jo.provider.vehicles.Length == 1)
                                        {
                                            if (truckId == null)
                                                logger.Log(LogLevel.Warn, "Urgent.ly/{0}/{1}: TruckId wasn't specified; picking First().Id/{2} because there is only 1 option", dispatch.CallRequestId, dispatch.CaseId, jo.provider.vehicles.First().id);
                                            else
                                                logger.Log(LogLevel.Warn, "Urgent.ly/{0}/{1}: TruckId '{2}' was specified; but picking First() because there is only 1 option", dispatch.CallRequestId, dispatch.CaseId, truckId);

                                            if ((serviceCallsOnly || virtualTruckMode) && driverId.GetValueOrDefault() != 0)
                                                truckId = -777;
                                            else
                                            {
                                                if (jo.provider.vehicles.First().id.StartsWith("virtual-"))
                                                {
                                                    driverId = Convert.ToInt32(jo.provider.vehicles.First().id.Replace("virtual-", ""));
                                                    truckId = -777;
                                                }
                                                else
                                                    truckId = Convert.ToInt32(jo.provider.vehicles.First().id);
                                            }
                                        }
                                        else
                                        {
                                            if (serviceCallsOnly || virtualTruckMode)
                                                truckId = -777;
                                            else
                                            {
                                                truckId = DriverTruckDefault.GetByDriverId(driverId.Value)?.TruckId;

                                                if (truckId == null)
                                                {
                                                    logger.Log(LogLevel.Error, "Urgent.ly/{0}/{1}: TruckId wasn't specified and couldn't determine from TruckDefaults for DriverId {2}", dispatch.CallRequestId, dispatch.CaseId,
                                                        driverId);
                                                }
                                            }
                                        }

                                        if (driverId == null && truckId != null)
                                        {
                                            driverId = DriverTruckDefault.GetByTruckId(truckId.Value).LastOrDefault()?.DriverId;
                                        }

                                        UrgentlyRestClient.Provider.ProviderVehicle truck = null;
                                        UrgentlyRestClient.Driver driver = null;
                                        Truck towbookTruck = null;
                                        Driver towbookDriver = null;

                                        if (jo.service.category != "RSA_SCHEDULED_SERVICE" &&
                                            jo.service.category != "RSA_SCHEDULED_DROPOFF" &&
                                            truckId != null && truckId != 0 &&
                                            driverId != null && driverId != 0)
                                        {
                                            towbookTruck = await Truck.GetByIdAsync(truckId.Value);
                                            towbookDriver = await Driver.GetByIdAsync(driverId.Value);

                                            if (towbookDriver == null)
                                            {
                                                logger.Log(LogLevel.Error, "Urgent.ly/{0}/{1}: Driver is null for ID '{2}'.", dispatch.CallRequestId, dispatch.CaseId,
                                                         driverId);

                                                throw new Exception("driver " + driverId + " doesn't exist");

                                            }

                                            var location = UserLocationHistoryItem.GetCurrentByUserId(towbookDriver.UserId, DateTime.Now.AddDays(-7), DateTime.Now.AddDays(1));

                                            if (location != null)
                                            {
                                                lat = location.Latitude;
                                                lng = location.Longitude;
                                            }

                                            if (truckId == -777)
                                                truck = UrgentlyRestClient.Provider.ProviderVehicle.FromDriver(towbookDriver, virtualTruckMode);
                                            else
                                                truck = UrgentlyRestClient.Provider.ProviderVehicle.FromTowbook(towbookTruck, qi.CompanyId.Value);

                                            driver = UrgentlyRestClient.Driver.FromTowbook(towbookDriver, company.Phone);

                                            if (lat.GetValueOrDefault() == 0)
                                            {
                                                logger.Log(LogLevel.Warn, "Urgent.ly/Accepting Urgent.ly CallRequestId={0}/CaseId={1}/JobNumber={2}: WARNING: Lat/Long is missing...attempting to get from truck or company",
                                                    dispatch.CallRequestId,
                                                    dispatch.CaseId,
                                                    dispatch.JobNumber);

                                                // attempt to get from original offer.
                                                if (dispatch.TruckId != towbookTruck?.Id)
                                                {
                                                    driverId = DriverTruckDefault.GetByTruckId(dispatch.TruckId).LastOrDefault()?.DriverId;
                                                    if (driverId != null)
                                                    {
                                                        towbookDriver = await Driver.GetByIdAsync(driverId.Value);
                                                        if (towbookDriver != null)
                                                        {
                                                            location = UserLocationHistoryItem.GetCurrentByUserId(
                                                                towbookDriver.UserId, DateTime.Now.AddDays(-7), DateTime.Now);
                                                        }
                                                    }
                                                }

                                                if (location == null && towbookDriver != null)
                                                {
                                                    // try to use office address
                                                    var c = await Company.Company.GetByIdAsync(towbookDriver.CompanyId);
                                                    if (c?.Latitude != 0)
                                                    {
                                                        lat = c.Latitude;
                                                        lng = c.Longitude;
                                                    }
                                                }
                                            }
                                        }

                                        var acceptModel = new UrgentlyRestClient.JobStatusUpdateModel()
                                        {
                                            context = new UrgentlyRestClient.Context() { caseId = ad.CaseId },
                                            provider = new UrgentlyRestClient.Provider()
                                            {
                                                id = ad.ProviderId,
                                                name = company.Name,
                                                dispatchPhone = Core.FormatPhoneWithDashesOnly(company.Phone),
                                                vehicle = truck,
                                                driver = driver,
                                                driverETA = new UrgentlyRestClient.Drivereta()
                                                {
                                                    distanceInMiles = 3, // TODO
                                                    timeInMins = dispatch.Eta.Value // TODO
                                                },
                                                location = new UrgentlyRestClient.Location()
                                                {
                                                    latitude = lat.GetValueOrDefault(),
                                                    longitude = lng.GetValueOrDefault(),
                                                }
                                            },
                                            service = new UrgentlyRestClient.Service()
                                            {
                                                name = jo.service.name,
                                                typeId = jo.service.typeId,
                                                status = (int)newStatus,
                                            }
                                        };

                                        // TODO: need to handle when they return an error code, 
                                        // !IsOk and look at .error

                                        var acceptResponse = client.JobStatusUpdate(ad.ProviderId, ad.CaseId,
                                            newStatus,
                                            acceptModel);

                                        if (towbookDriver != null)
                                            dispatch.DriverId = towbookDriver.Id;

                                        if (towbookTruck != null)
                                            dispatch.TruckId = towbookTruck.Id;

                                        dispatch.Save();

                                        if (acceptResponse.IsSuccessful)
                                        {
                                            logger.Info(MasterAccountTypes.Urgently,
                                                "AcceptCall",
                                                "Sent accept successfully.",
                                                dispatch.ProviderId, null, dispatch.CaseId, qi.CompanyId,
                                                new
                                                {
                                                    json = acceptModel.ToJson(),
                                                    message = acceptResponse.Message
                                                }, callRequestId: dispatch.CallRequestId,
                                                queueItemId: qi.QueueItemId);
                                            return true;
                                        }
                                        else
                                        {
                                            logger.Error(MasterAccountTypes.Urgently,
                                                "AcceptCall",
                                                "Sending of accept failed.",
                                                dispatch.ProviderId, null, dispatch.CaseId, qi.CompanyId,
                                                new
                                                {
                                                    json = acceptModel.ToJson(),
                                                    errorMessage = acceptResponse.Error,
                                                    response = acceptResponse.ResponseBody
                                                }, 
                                                callRequestId: dispatch.CallRequestId,
                                                queueItemId: qi.QueueItemId);

                                            error = acceptResponse.Error;

                                            return false;
                                        }
                                    }

                                    if (!await doAcceptAsync())
                                    {
                                        if (error == "Driver needs to be added to company driver list. Please contact your dispatcher.")
                                        {
                                            await RegisterTrucksAndDrivers(UrgentlyProvider.GetByProviderId(ad.ProviderId));

                                            // try to repair th driver.
                                            if (!await doAcceptAsync())
                                            {
                                                await request.UpdateStatus(CallRequestStatus.AcceptFailed);
                                            }
                                            else
                                            {
                                                await request.UpdateStatus(CallRequestStatus.AcceptSent);
                                            }
                                        }
                                        else if (error != null &&
                                                error.Contains("has already been awarded to another"))
                                        {
                                            await request.UpdateStatus(CallRequestStatus.AnotherProviderResponded);
                                        }
                                        else
                                        {
                                            await request.UpdateStatus(CallRequestStatus.AcceptFailed);
                                        }
                                    }
                                    else
                                    {
                                        await request.UpdateStatus(CallRequestStatus.AcceptSent);
                                    }
                                }

                                qi.CallRequestId = request.CallRequestId;
                                DigitalDispatchService.LogAction(qi);

                                await sourceMessage.CompleteAsync();
                                DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                            }
                            catch (Exception exc)
                            {
                                await sourceMessage.DeadLetterAsync("Attempted to accept callRequestId " + request.CallRequestId + ", but  error occurred",
                                    exc.ToJson(true));

                                logger.Error(MasterAccountTypes.Urgently,
                                    "AcceptCall",
                                    "Sent accept failed.",
                                    dispatch?.ProviderId, null, dispatch?.CaseId, qi?.CompanyId,
                                    new
                                    {
                                        errorMessage = exc.Message,
                                        exception = exc
                                    }, 
                                    callRequestId: dispatch.CallRequestId,
                                    queueItemId: qi.QueueItemId);

                                DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                            }
                            break;

                        case DigitalDispatchActionQueueItemType.OutgoingRejectCall:

                            async Task<bool> doRefuse()
                            {
                                logger.Info(MasterAccountTypes.Urgently,
                                    "RejectCall",
                                    "Rejecting job offer.",
                                    dispatch?.ProviderId, null, dispatch?.CaseId, qi?.CompanyId,
                                    queueItemId: qi.QueueItemId,
                                    callRequestId: dispatch?.CallRequestId);

                                var newStatus = UrgentlyRestClient.JobStatusUpdateAction.ProviderReject;

                                var company = await Company.Company.GetByIdAsync(request.CompanyId);

                                var resp = client.JobStatusUpdate(ad.ProviderId, ad.CaseId,
                                    newStatus,
                                    new UrgentlyRestClient.JobStatusUpdateModel()
                                    {
                                        context = new UrgentlyRestClient.Context() { caseId = ad.CaseId },
                                        provider = new UrgentlyRestClient.Provider()
                                        {
                                            id = ad.ProviderId,
                                            name = company.Name,
                                            dispatchPhone = Core.FormatPhoneWithDashesOnly(company.Phone)
                                        },
                                        service = new UrgentlyRestClient.Service()
                                        {
                                            name = jo.service.name,
                                            typeId = jo.service.typeId,
                                            status = (int)newStatus,
                                        }
                                    });

                                if (resp.IsSuccessful)
                                {
                                    logger.Info(MasterAccountTypes.Urgently,
                                        "RejectCall",
                                        "Rejected job offer.",
                                        dispatch?.ProviderId, null, dispatch?.CaseId, qi?.CompanyId,
                                        new
                                        {
                                            responseJson = resp
                                        }, 
                                        callRequestId: ad.CallRequestId,
                                        queueItemId: qi.QueueItemId);

                                    return true;
                                }
                                else
                                {
                                    logger.Error(MasterAccountTypes.Urgently,
                                        "RejectCall",
                                        "Reject of job offer failed.",
                                        dispatch?.ProviderId, null, dispatch?.CaseId, qi?.CompanyId,
                                        new
                                        {
                                            responseJson = resp
                                        }, 
                                        callRequestId: ad.CallRequestId,
                                        queueItemId: qi.QueueItemId);

                                    return false;
                                }
                            }

                            if (await doRefuse())
                            {
                                await request.UpdateStatus(CallRequestStatus.Rejected);
                            }
                            else
                            {
                                await request.UpdateStatus(CallRequestStatus.RejectFailed);
                            }

                            qi.CallRequestId = request.CallRequestId;
                            DigitalDispatchService.LogAction(qi);

                            await sourceMessage.CompleteAsync();
                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);

                            break;

                        case DigitalDispatchActionQueueItemType.OutgoingCallCanceled:
                        case DigitalDispatchActionQueueItemType.OutgoingStatusUpdate:
                        case DigitalDispatchActionQueueItemType.OutgoingReassignJob:
                            async Task<bool> doStatusUpdateAsync()
                            {
                                var company = await Company.Company.GetByIdAsync(cr.CompanyId);
                                var towbookTruck = await Truck.GetByIdAsync(ad.TruckId);
                                var towbookDriver = await Driver.GetByIdAsync(ad.DriverId);
                                var newStatus = 0;


                                if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingReassignJob)
                                {
                                    var reassign = JsonConvert.DeserializeObject<ReassignPayload>(qi.JsonObject);

                                    logger.Info(MasterAccountTypes.Urgently,
                                        "ReassignDriver",
                                        "Reassigning Driver",
                                        contractorId: ad.ProviderId,
                                        companyId: cr.CompanyId,
                                        dispatchId: ad.CaseId,
                                        data: new
                                        {
                                            request = reassign
                                        }, callRequestId: cr.CallRequestId,
                                        queueItemId: qi.QueueItemId,
                                        poNumber: ad.JobNumber.ToString());

                                    newStatus = (int)UrgentlyRestClient.JobStatusUpdateAction.ReassignDriver;

                                    var preferredNewTruckId = reassign.NewTruckId;

                                    towbookTruck = await Truck.GetByIdAsync(reassign.NewTruckId);
                                    towbookDriver = await Driver.GetByIdAsync(reassign.NewDriverId);

                                    var ujo = UrgentlyJobOffer.GetByCallRequestId(cr.CallRequestId);

                                    
                                    if ((virtualTruckMode && reassign.NewDriverId != reassign.OldDriverId) ||
                                        (!virtualTruckMode && preferredNewTruckId != ujo.TruckId)) 
                                    {
                                        logger.Log(LogLevel.Info, "Urgent.ly/ReassignDriverAndTruck/CallRequestId={0}/CaseId={1} ",
                                            dispatch.CallRequestId, dispatch.CaseId, Convert.ToInt32(jsonObj.NewStatusId),
                                            JsonExtensions.ToJson(jsonObj));

                                        newStatus = (int)UrgentlyRestClient.JobStatusUpdateAction.ReassignDriverTruck;
                                        towbookTruck = await Truck.GetByIdAsync(preferredNewTruckId);
                                    }

                                    ujo.TruckId = towbookTruck.Id;
                                    ujo.DriverId = towbookDriver.Id;
                                    ujo.Save();

                                    if (reassign.OldDriverId == reassign.NewDriverId && virtualTruckMode == true)
                                    {
                                        logger.Log(LogLevel.Info, "Urgent.ly/ReassignDriverAndTruck/CallRequestId={0}/CaseId={1} Old and New driverid are the same, not continuing.",
                                            dispatch.CallRequestId, dispatch.CaseId, Convert.ToInt32(jsonObj.NewStatusId),
                                            JsonExtensions.ToJson(jsonObj));
                                        return true;
                                    }

                                }
                                else if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingCallCanceled)
                                {
                                    newStatus = (int)UrgentlyRestClient.JobStatusUpdateAction.CancelJobInProgress;
                                }
                                else
                                {
                                    switch (Convert.ToInt32(jsonObj.NewStatusId))
                                    {
                                        case 2:
                                            newStatus = (int)UrgentlyRestClient.JobStatusUpdateAction.OnWay;
                                            break;
                                        case 3:
                                            newStatus = (int)UrgentlyRestClient.JobStatusUpdateAction.OnScene;
                                            break;
                                        case 4:
                                            newStatus = (int)UrgentlyRestClient.JobStatusUpdateAction.Towing;
                                            break;
                                        case 5:
                                        {
                                            if (jsonObj.CompletionReasonId != null)
                                            {
                                                int completionReasonId = (int)jsonObj.CompletionReasonId;
                                                var marc = MasterAccountReason.GetById(completionReasonId);
                                                if (marc != null)
                                                    newStatus = (int)Convert.ToInt32(marc.Code);
                                                else
                                                    newStatus = (int)UrgentlyRestClient.JobStatusUpdateAction.Completed;
                                            }
                                            else
                                            {
                                                newStatus = (int)UrgentlyRestClient.JobStatusUpdateAction.Completed;
                                            }
                                            break;
                                        }
                                        case 255:
                                            newStatus = (int)UrgentlyRestClient.JobStatusUpdateAction.CancelJobInProgress;
                                            break;

                                        default:
                                            newStatus = -1;
                                            break;
                                    }
                                }


                                if (newStatus == -1)
                                {
                                    logger.Log(LogLevel.Info, "Urgent.ly/UpdatingStatus/CallRequestId={0}/CaseId={1} Skipping because StatusId is {2}",
                                        dispatch.CallRequestId, dispatch.CaseId, Convert.ToInt32(jsonObj.NewStatusId));
                                    return false;
                                }

                                var driver = UrgentlyRestClient.Driver.FromTowbook(towbookDriver, company.Phone);
                                var reassignedDriver = false;
                                if (driver?.id == null)
                                {
                                    var statusDriver = await Driver.GetByIdAsync((int)jsonObj.DriverId);
                                    if (statusDriver != null)
                                    {
                                        driver = UrgentlyRestClient.Driver.FromTowbook(statusDriver, company.Phone);
                                        towbookDriver = statusDriver;
                                        reassignedDriver = true;
                                    }
                                    else
                                        driver = null;
                                }
                                    

                                decimal lat = 0, lng = 0;
                                var location = towbookDriver != null ? UserLocationHistoryItem.GetCurrentByUserId(towbookDriver.UserId, DateTime.Now.AddDays(-7), DateTime.Now) : null;

                                if (location != null)
                                {
                                    lat = location.Latitude;
                                    lng = location.Longitude;
                                }

                                var vehicle = UrgentlyRestClient.Provider.ProviderVehicle.FromTowbook(towbookTruck, qi.CompanyId.Value);
                                if (vehicle == null && (serviceCallsOnly || virtualTruckMode))
                                    vehicle = UrgentlyRestClient.Provider.ProviderVehicle.FromDriver(towbookDriver, virtualTruckMode);

                                if (reassignedDriver)
                                {
                                    var rsp2 = client.JobStatusUpdate(ad.ProviderId, ad.CaseId, UrgentlyRestClient.JobStatusUpdateAction.ReassignDriverTruck,
                                        new UrgentlyRestClient.JobStatusUpdateModel()
                                        {
                                            context = new UrgentlyRestClient.Context() { caseId = ad.CaseId },
                                            provider = new UrgentlyRestClient.Provider()
                                            {
                                                id = ad.ProviderId,
                                                name = company.Name,
                                                dispatchPhone = Core.FormatPhoneWithDashesOnly(company.Phone),
                                                vehicle = vehicle,
                                                driver = driver,
                                                driverETA = driver != null ? new UrgentlyRestClient.Drivereta
                                                {
                                                    distanceInMiles = ad.Eta.GetValueOrDefault(), // TODO: calculate distance.
                                                    timeInMins = ad.Eta.GetValueOrDefault()
                                                } : null,
                                                location = driver != null ? new UrgentlyRestClient.Location()
                                                {
                                                    latitude = lat,
                                                    longitude = lng
                                                } : null
                                            },
                                            service = new UrgentlyRestClient.Service()
                                            {
                                                name = jo.service.name,
                                                typeId = jo.service.typeId,
                                                status = (int)UrgentlyRestClient.JobStatusUpdateAction.ReassignDriverTruck,
                                            }
                                        });

                                    await Task.Delay(10000);
                                    
                                    logger.Info(MasterAccountTypes.Urgently,
                                        "StatusUpdate",
                                        $"Sent {UrgentlyRestClient.JobStatusUpdateAction.ReassignDriverTruck} status update successfully",
                                        contractorId: ad.ProviderId,
                                        companyId: cr.CompanyId,
                                        dispatchId: ad.CaseId,
                                        data: new
                                        {
                                            response = rsp2
                                        },
                                        callRequestId: cr.CallRequestId,
                                        queueItemId: qi.QueueItemId,
                                        poNumber: ad.JobNumber.ToString());

                                    ad.DriverId = towbookDriver.Id;
                                    ad.Save();
                                }

                                var rsp = client.JobStatusUpdate(ad.ProviderId, ad.CaseId,
                                    (UrgentlyRestClient.JobStatusUpdateAction)newStatus,
                                    new UrgentlyRestClient.JobStatusUpdateModel()
                                    {
                                        context = new UrgentlyRestClient.Context() { caseId = ad.CaseId },
                                        provider = new UrgentlyRestClient.Provider()
                                        {
                                            id = ad.ProviderId,
                                            name = company.Name,
                                            dispatchPhone = Core.FormatPhoneWithDashesOnly(company.Phone),
                                            vehicle = vehicle,
                                            driver = driver,
                                            driverETA = driver != null ? new UrgentlyRestClient.Drivereta
                                            {
                                                distanceInMiles = ad.Eta.GetValueOrDefault(), // TODO: calculate distance.
                                                timeInMins = ad.Eta.GetValueOrDefault()
                                            } : null,
                                            location = driver != null ? new UrgentlyRestClient.Location()
                                            {
                                                latitude = lat,
                                                longitude = lng
                                            } : null
                                        },
                                        service = new UrgentlyRestClient.Service()
                                        {
                                            name = jo.service.name,
                                            typeId = jo.service.typeId,
                                            status = newStatus,
                                        }
                                    });

                                string statusUpdateName = Enum.GetName(typeof(UrgentlyRestClient.JobStatusUpdateAction), newStatus);

                                if (rsp.IsSuccessful)
                                {
                                    logger.Info(MasterAccountTypes.Urgently,
                                        "StatusUpdate",
                                        $"Sent {statusUpdateName} status update successfully",
                                        contractorId: ad.ProviderId,
                                        companyId: cr.CompanyId,
                                        dispatchId: ad.CaseId,
                                        data: new
                                        {
                                            response = rsp
                                        }, 
                                        callRequestId: cr.CallRequestId,
                                        queueItemId: qi.QueueItemId,
                                        poNumber: ad.JobNumber.ToString());
                                    return true;
                                }
                                else
                                {
                                    logger.Info(MasterAccountTypes.Urgently,
                                        "StatusUpdate",
                                        $"Sending of Status Update {statusUpdateName} Failed",
                                        contractorId: ad.ProviderId,
                                        companyId: cr.CompanyId,
                                        dispatchId: ad.CaseId,
                                        data: new
                                        {
                                            response = rsp
                                        }, 
                                        callRequestId: cr.CallRequestId,
                                        queueItemId: qi.QueueItemId,
                                        poNumber: ad.JobNumber.ToString());

                                    return false;
                                }

                            }

                            var success = await doStatusUpdateAsync();
                            qi.CallRequestId = request.CallRequestId;
                            DigitalDispatchService.LogAction(qi);

                            await sourceMessage.CompleteAsync();
                            if (success)
                                DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                            else
                                DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);

                            break;

                        case DigitalDispatchActionQueueItemType.OutgoingRequestGoa:

                            async Task<bool> doCompleteAsGoa()
                            {
                                logger.Log(LogLevel.Info, "Urgent.ly/OutgoingRequestGoa/CallRequestId={0}/CaseId={1}", dispatch.CallRequestId, dispatch.CaseId);

                                var company = await Company.Company.GetByIdAsync(cr.CompanyId);
                                var en = await Entry.GetByIdNoCacheAsync(cr.DispatchEntryId.Value);
                                var towbookTruck = en.Truck;
                                var towbookDriver = await Driver.GetByIdAsync(Convert.ToInt32(jsonObj.DriverId));

                                var model = JsonConvert.DeserializeObject<Integration.MotorClubs.Services.Models.RequestGoaMessage>(qi.JsonObject);

                                var mar = MasterAccountReason.GetById(model.ReasonId);

                                string goaReasonName = mar?.Code ?? "Other";
                                string goaReasonNotes = model?.Comments;

                                if (towbookDriver == null)
                                    towbookDriver = en.Driver;

                                var driver = new UrgentlyRestClient.Driver()
                                {
                                    id = towbookDriver.Id.ToString(),
                                    name = towbookDriver.Name,
                                    phone = Core.FormatPhoneWithDashesOnly(company.Phone),
                                    photoURL = ""
                                };

                                decimal lat = 0, lng = 0;
                                var location = UserLocationHistoryItem.GetCurrentByUserId(towbookDriver.UserId, DateTime.Now.AddMinutes(-30), DateTime.Now);

                                if (location != null)
                                {
                                    lat = location.Latitude;
                                    lng = location.Longitude;
                                }

                                var vehicle = UrgentlyRestClient.Provider.ProviderVehicle.FromTowbook(towbookTruck, qi.CompanyId.Value);
                                if (vehicle == null && (serviceCallsOnly || virtualTruckMode))
                                    vehicle = UrgentlyRestClient.Provider.ProviderVehicle.FromDriver(towbookDriver, virtualTruckMode);

                                var goaResponse = client.JobStatusUpdate(ad.ProviderId, ad.CaseId,
                                    UrgentlyRestClient.JobStatusUpdateAction.CompletedGoa,
                                    new UrgentlyRestClient.JobStatusUpdateModel()
                                    {
                                        context = new UrgentlyRestClient.Context() { caseId = ad.CaseId },
                                        provider = new UrgentlyRestClient.Provider()
                                        {
                                            id = ad.ProviderId,
                                            name = company.Name,
                                            dispatchPhone = Core.FormatPhoneWithDashesOnly(company.Phone),
                                            vehicle = vehicle,
                                            driver = driver,
                                            driverETA = new UrgentlyRestClient.Drivereta
                                            {
                                                distanceInMiles = 10,
                                                timeInMins = ad.Eta.GetValueOrDefault()
                                            },
                                            location = new UrgentlyRestClient.Location() { latitude = lat, longitude = lng }
                                        },
                                        service = new UrgentlyRestClient.Service()
                                        {
                                            name = jo.service.name,
                                            typeId = jo.service.typeId,
                                            status = (int)UrgentlyRestClient.JobStatusUpdateAction.CompletedGoa,
                                            GoaReason = goaReasonName,
                                            GoaReasonNotes = goaReasonNotes
                                        }
                                    });

                                int userId = qi.OwnerUserId ?? 1;
                                en.Status = Status.Completed;
                                en.Notes = "Call completed as GOA.\n" + en.Notes;
                                var goaReasonCode = (await Reason.GetByCompany(en.Company)).FirstOrDefault(o => o.IsGoa());
                                if (goaReasonCode != null)
                                    en.ReasonId = goaReasonCode.Id;
                                en.CompletionTime = DateTime.Now;
                                await en.Save(false, new AuthenticationToken() { UserId = userId }, "127.0.0.1");

                                if (goaResponse.IsSuccessful)
                                {
                                    logger.Info(MasterAccountTypes.Urgently,
                                        "RequestGoa",
                                        "Sent GOA request successfully.",
                                        contractorId: ad.ProviderId,
                                        companyId: cr.CompanyId,
                                        dispatchId: ad.CaseId,
                                        data: new
                                        {
                                            response = goaResponse
                                        },
                                        callRequestId: cr.CallRequestId,
                                        queueItemId: qi.QueueItemId,
                                        poNumber: ad.JobNumber.ToString());
                                }
                                else
                                {
                                    logger.Info(MasterAccountTypes.Urgently,
                                       "RequestGoa",
                                       "Sending of GOA failed.",
                                       contractorId: ad.ProviderId,
                                       companyId: cr.CompanyId,
                                       dispatchId: ad.CaseId,
                                       data: new
                                       {
                                           error = goaResponse.Error,  
                                           response = goaResponse
                                       },
                                       callRequestId: cr.CallRequestId,
                                       queueItemId: qi.QueueItemId,
                                       poNumber: ad.JobNumber.ToString());
                                }
                                logger.Log(LogLevel.Info, 
                                    "Urgent.ly/RequestGoa/Urgent.ly CallRequestId={0}/CaseId={1}: Finished Job Number {2}.", dispatch.CallRequestId, dispatch.CaseId,jo.service.id);

                                return true;
                            }

                            await doCompleteAsGoa();
                            qi.CallRequestId = request.CallRequestId;
                            DigitalDispatchService.LogAction(qi);

                            await sourceMessage.CompleteAsync();
                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                            break;


                        case DigitalDispatchActionQueueItemType.OutgoingSharePhoto:
                        case DigitalDispatchActionQueueItemType.OutgoingShareSignature:

                            async Task SharePhotoOrSignature(int callRequestId, string json)
                            {
                                try
                                {
                                    SharePhotoModel pm = JsonConvert.DeserializeObject<SharePhotoModel>(json);

                                    cr = CallRequest.GetById(callRequestId);
                                    if (cr == null)
                                    {
                                        logger.Log(LogLevel.Error, "CR" + cr.CallRequestId + "/C" + cr.CompanyId + "/SharePhoto: Couldn't find CallRequestId.");

                                        await sourceMessage.CompleteAsync();
                                        return;
                                    }

                                    qi.CallRequestId = cr.CallRequestId;

                                    if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingShareSignature)
                                    {
                                        var sig = Signature.GetById((int)jsonObj.SignatureId);

                                        var statusName = "ON_SITE";

                                        if (jsonObj.StatusId != null && ((int)jsonObj.StatusId > Status.AtSite.Id))
                                            statusName = "ON_DROP_OFF";

                                        // todo: we don't have status associated with signature.

                                        client.SharePhoto(ad.ProviderId,
                                            ad.DriverId.ToString(),
                                            ad.CaseId,
                                            FileUtility.GetPresignedUrlForDownloadFromClient(sig.Location.Replace("%1", qi.CompanyId.ToString()), sig.ContentType, 60 * 24 * 180),
                                             pm.Latitude, pm.Longitude, statusName, sig.CreateDate, "image/jpg");

                                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                                    }
                                    else if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingSharePhoto)
                                    {
                                        var pho = Dispatch.Photo.GetById((int)jsonObj.PhotoId);
                                        if (pho == null)
                                        {
                                            throw new Exception("no such photo." + qi.JsonObject);
                                        }

                                        var statusName = "ON_SITE";

                                        if (pho.DispatchEntryStatusId > Status.AtSite.Id)
                                            statusName = "ON_DROP_OFF";

                                        client.SharePhoto(ad.ProviderId,
                                            ad.DriverId.ToString(),
                                            ad.CaseId,
                                            FileUtility.GetPresignedUrlForDownloadFromClient(pho.Location.Replace("%1", qi.CompanyId.ToString()), pho.ContentType, 60 * 24 * 180),
                                             pm.Latitude, pm.Longitude, statusName, pho.CreateDate, "image/jpg");

                                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                                    }
                                }
                                catch (Exception y)
                                {
                                    logger.Error(MasterAccountTypes.Urgently, "SharePhoto", "Error sending photo: " + y.Message,
                                        companyId: qi.CompanyId,
                                        data: y,
                                        callRequestId: callRequestId,
                                        queueItemId: qi.QueueItemId);
                                }

                                await sourceMessage.CompleteAsync();
                                DigitalDispatchService.LogAction(qi);
                                return;
                            }

                            await SharePhotoOrSignature(Convert.ToInt32(jsonObj.CallRequestId), qi.JsonObject);

                            break;

                        default:
                            await sourceMessage.DeadLetterAsync("No implementation written", qi.ToJson());
                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                            logger.LogEvent("Queue item doesn't have a Type set that we have a implementation written for... MessageId {0}, Body = {1}", qi.CompanyId, LogLevel.Error, sourceMessage.Message.MessageId, qi.ToJson());
                            return;
                    }
                }
            }
        }


        public class UrgentlyGpsModel
        {
            public int CompanyId { get; set; }
            public string ProviderId { get; set; }
            public string DriverId { get; set; }
            public string TruckId { get; set; }
            public decimal Latitude { get; set; }
            public decimal Longitude { get; set; }

            public bool TruckIsRegistered { get; set; }
            public bool DriverIsRegistered { get; set; }
        }

        private static readonly Provider urgentlyProvider = Provider.GetByName("Urgent.ly");

        public static async Task RegisterTrucksAndDrivers(UrgentlyProvider p)
        {
            UrgentlyRestClient client;
            if (!new int[] { 10000, 100769, 100770, 100771, 154042 }.Contains(p.CompanyId))
                client = UrgentlyRestClient.GetProduction();
            else
                client = UrgentlyRestClient.Get();

            var comp = await Company.Company.GetByIdAsync(p.CompanyId);
            Driver.ClearCacheByCompany(comp);
            Truck.ClearCacheByCompany(comp);

           
            var allTrucks = (await Truck.GetByCompanyAsync(comp)).Where(o => o.Id > 0);
            allTrucks = Array.Empty<Truck>();
            var drivers = Driver.GetByCompany(comp);

            var tempUrgentlyDrivers = UrgentlyDriver.GetByUrgentlyProviderId(p.UrgentlyProviderId);
            if (tempUrgentlyDrivers.Any() && UrgentlyProvider.GetByCompanyId(p.CompanyId).Count() > 1)
            {
                drivers = drivers.Where(o => tempUrgentlyDrivers.Any(r => r.DriverId == o.Id)).ToList();
            }

            var users = User.GetByCompanyId(comp.Id);
            var allDrivers = drivers.Where(o => o.EndDate == null && !users.Any( r => o.UserId == r.Id && r.Disabled)).ToCollection();
            var deletedDrivers = drivers.Where(o => o.EndDate != null).ToCollection();

            var allLocations = UserLocationHistoryItem.GetCurrentByUserIds(allDrivers.Select(o => o.UserId), DateTime.Now.AddMinutes(-90), DateTime.Now);

            var driverTruckDefaults = DriverTruckDefault.GetByCompanyId(p.CompanyId);

            var serviceCallsOnly = CompanyKeyValue.GetFirstValueOrNull(comp.Id, Provider.Towbook.ProviderId, "ServiceCallsOnly") == "1";
            var virtualTruckMode = CompanyKeyValue.GetFirstValueOrNull(comp.Id, Provider.Towbook.ProviderId, "UrgentlyVirtualTrucks") == "1";

            int driverKey = DriverKey.GetByProviderId(urgentlyProvider.ProviderId)
                .First(o => o.Name == "IsRegistered").Id;

            int truckKey = TruckKey.GetByProviderId(urgentlyProvider.ProviderId)
                .First(o => o.Name == "IsRegistered").Id;

            var vehicles = client.TruckGetAll(p.ProviderId) ?? new List<UrgentlyRestClient.VehicleListItem>();

            vehicles = vehicles.Where(o => o.enabled || ((serviceCallsOnly || virtualTruckMode) && o.vehicleId.StartsWith("virtual-"))).ToList();
            foreach (var v in vehicles)
            {
                if (!v.vehicleId.StartsWith("virtual-") && v.enabled)
                {
                    Console.WriteLine("Delete:" + v.ToJson());
                    client.TruckDelete(p.ProviderId, v.vehicleId);
                }

                if (v.vehicleId.StartsWith("virtual-") && !v.enabled)
                {
                    var driver = allDrivers.FirstOrDefault(d => d.Id == v.SafeVehicleId());
                    if (driver == null || driver.EndDate != null)
                    {
                        continue;
                    }

                    client.TruckAdd(p.ProviderId,
                        UrgentlyRestClient.Vehicle.FromDriver(
                            driver, virtualTruckMode && !serviceCallsOnly
                        ));

                    client.TruckAssignDriver(p.ProviderId, v.vehicleId,
                        v.vehicleId.Replace("virtual-", ""));
                    
                    
                    Console.WriteLine("enabled:" + v.vehicleId);
                }

                if (v.vehicleId.StartsWith("virtual-") && v.enabled)
                {
                    var driverVehicleId = v.vehicleId.Replace("virtual-", "");

                    if (int.TryParse(driverVehicleId, out var driverVehicle))
                    {
                        if (allDrivers.FirstOrDefault(o => o.Id == driverVehicle && o.EndDate == null) == null)
                        {
                            client.TruckDelete(p.ProviderId, v.vehicleId);
                            continue;
                        }
                    }
                    else
                    {
                        client.TruckDelete(p.ProviderId, v.vehicleId);
                        continue;

                    }
                }

                Console.WriteLine(v.vehicleId.PadRight(20, ' ') + " " +
                    v.name?.PadRight(30, ' ') + " => " +
                    v.driverId + " / " + v.driverFirstName + " " + v.driverLastName);

                if (v.name == null)
                {
                    v.name = allTrucks.FirstOrDefault(o => o.Id.ToString() == v.vehicleId)?.Name;
                    if (v.name != null)
                    {
                        client.TruckUpdate(p.ProviderId, v.vehicleId,
                            new UrgentlyRestClient.Vehicle()
                            {
                                id = v.vehicleId,
                                name = v.name,
                                driverId = v.driverId
                            });
                    }
                }
            }

            // find any drivers that exist in urgently that dont' exist in towbook and delete them.
            var urgentlyDrivers = client.DriverGetAll(p.ProviderId).Where(o => !o.DriverId.StartsWith("del")).ToCollection();

            var driverRegistrations = 
                allDrivers.GroupBy(o => o.CompanyId).SelectMany(r => 
                    DriverKeyValue.GetByCompany(r.Key, urgentlyProvider.ProviderId)
                        .Where(o => o.KeyId == driverKey)).ToList();

            if (tempUrgentlyDrivers.Any())
                driverRegistrations = driverRegistrations.Where(o => tempUrgentlyDrivers.Any(r => r.DriverId == o.DriverId)).ToList();

            // find drivers in towbook that we THINK are registered, but aren't in urgently, so we need to clean these isRegistered bits from our DB.
            foreach (var dkv in driverRegistrations)
            {
                if (!urgentlyDrivers.Where(o => o.DriverId == dkv.DriverId.ToString()).Any())
                {
                    Console.WriteLine("Deleting orphaned IsRegistered.");
                    Console.WriteLine(dkv.ToJson());
                    dkv.Delete();
                }
            }

            var truckRegistrations = TruckKeyValue.GetByCompany(p.CompanyId).Where(o => o.KeyId == truckKey);

            var records = allDrivers.Select((o) =>
            {
                var urg = new UrgentlyGpsModel()
                {
                    DriverId = o.Id.ToString(),
                    CompanyId = o.CompanyId,
                };

                var l = allLocations.FirstOrDefault(r => r.UserId == o.UserId);
                if (l != null)
                {
                    urg.Latitude = l.Latitude;
                    urg.Longitude = l.Longitude;
                }

                var truckId = driverTruckDefaults.FirstOrDefault(r => r.DriverId == o.Id)?.TruckId ?? 0;

                urg.DriverIsRegistered = driverRegistrations.Any(r => r.DriverId == o.Id && r.Value == "1");
                urg.TruckIsRegistered = truckRegistrations.Any(r => r.TruckId == truckId && r.Value == "1");

                return urg;
            }).ToList();


            // find drivers that are in urgently assigned to trucks that are inactive in towbook
            foreach (var each in vehicles)
            {
                var deletedDriver = allDrivers.All(o => o.Id.ToString() != each.driverId);
                if (deletedDriver && each.driverId != null)
                {
                    if (int.TryParse(each.driverId, out var driverId))
                    {
                        client.TruckUpdate(p.ProviderId, each.vehicleId,
                            new UrgentlyRestClient.Vehicle() { driverId = null });

                        Console.WriteLine(p.CompanyId + "/" + each.driverId + ": Unassigned from Vehicle " + each.vehicleId);

                        if (client.DriverDelete(p.ProviderId, each.driverId))
                        {
                            var dkv = DriverKeyValue.GetByDriver(p.CompanyId, each.SafeDriverId(),
                                urgentlyProvider.ProviderId, "IsRegistered").FirstOrDefault();

                            if (dkv != null)
                            {
                                dkv.Delete();
                                Console.WriteLine(p.CompanyId + "/" + each.driverId + ": Deleted IsRegistered in towbook");
                            }
                        }
                        else
                        {
                            Console.WriteLine("FAILED TO DELETE: " + each.driverId);
                        }
                    }
                }

                if (each.driverId == null || each.driverId.StartsWith("d"))
                {
                    if (serviceCallsOnly || virtualTruckMode)
                    {
                        client.TruckAssignDriver(p.ProviderId, each.vehicleId, each.vehicleId.Replace("virtual-",""));
                        continue;
                    }
                    Console.WriteLine(each.vehicleId + ": No DriverId assigned.");
                    var assigned = driverTruckDefaults.Where(o => o.TruckId.ToString() == each.vehicleId).OrderByDescending(o => o.ModifiedDate).FirstOrDefault();

                    if (assigned != null)
                    {
                        if (urgentlyDrivers.Any(o => o.DriverId == assigned.DriverId.ToString()))
                        {
                            var location = UserLocationHistoryItem.GetByUserId(
                                allDrivers.FirstOrDefault(o => o.Id == assigned.DriverId)?.UserId ?? 0);

                            Console.WriteLine("Location:" + location.ToJson(true));
                            Console.WriteLine(each.vehicleId + ": Assigning Driver.");

                            client.TruckAssignDriver(p.ProviderId, each.vehicleId, assigned.DriverId.ToString());
                        }
                        else
                        {
                            Console.WriteLine("FATAL: We have an assigned driver, but it doesn't exist in urgently. BAD!!!");
                        }
                    }
                    else
                    {
                        Console.WriteLine(each.vehicleId + ": Marked unavailable.");
                        client.TruckMarkUnavailable(p.ProviderId, each.vehicleId);
                    }
                }
            }

            // go through our records
            foreach (var x in records)
            {

                if (!x.DriverIsRegistered)
                {
                    var driver = await Driver.GetByIdAsync(Convert.ToInt32(x.DriverId));
                    if (driver == null)
                        continue;

                    if (driver.EndDate != null)
                    {
                        Console.WriteLine(driver.Id + ": Has an EndDate set. Don't add it!");
                    }
                    else
                    {
                        string firstName = "",
                            lastName = "";

                        if (driver.Name.Contains(" "))
                        {
                            if (driver.Name.Contains("(") && driver.Name.Contains(")"))
                            {
                                // remove the word surrounded by paranethes in driver.Name

                                var si = driver.Name.IndexOf("(");
                                var ei = driver.Name.IndexOf(")");
                                if (si != -1 && ei != -1 && ei > si)
                                {
                                    driver.Name = driver.Name.Substring(0, si) + driver.Name.Substring(ei + 1).Replace(" ", "");
                                }
                            }

                            // ignore numbers
                            var tname = new String(driver.Name.Where(r => !Char.IsDigit(r)).ToArray());

                            var names = tname.Split(new char[] { ' ' }, 2, StringSplitOptions.RemoveEmptyEntries);
                            if (names.Length == 2)
                            {
                                firstName = Core.FormatName(names[0]);
                                lastName = Core.FormatName(names[1]);
                            }
                            else
                                firstName = Core.FormatName(driver.Name);
                        }
                        else
                        {
                            firstName = Core.FormatName(driver.Name);
                        }

                        var company = await Company.Company.GetByIdAsync(driver.CompanyId);
                        var driverModel = new UrgentlyRestClient.DriverModel()
                        {
                            id = x.DriverId,
                            firstName = firstName,
                            lastName = lastName,
                            phone = (Core.IsPhoneValidStandard(driver.MobilePhone) ?
                                    Core.FormatPhoneWithNumbersOnly(driver.MobilePhone) : null),
                            photoURL = null
                        };
                        Console.WriteLine(driverModel.ToJson(true));

                        if (await client.DriverAdd(p.ProviderId,
                            driverModel))
                        {
                            new DriverKeyValue()
                            {
                                DriverId = driver.Id,
                                KeyId = driverKey,
                                Value = "1"
                            }.Save();
                        }
                        else
                        {
                            Console.WriteLine("Failed to add driver: " + x.DriverId);
                        }
                    }
                }
                else
                {
                    // is registered
                    if (deletedDrivers.Where(o => o?.Id.ToString() == x.DriverId?.ToString()).Any())
                    {
                        if (client.DriverDelete(p.ProviderId, x.DriverId))
                        {
                            foreach (var irtd in DriverKeyValue.GetByDriver(p.CompanyId, Convert.ToInt32(x.DriverId))
                                .Where(o => o.KeyId == driverKey))
                            {
                                irtd.Delete();
                                Console.WriteLine("Deleted driver reference " + irtd.ToJson());
                            }
                        }
                    }
                }

                if (!x.TruckIsRegistered && (!serviceCallsOnly && !virtualTruckMode))
                {
                    var truck = await Truck.GetByIdAsync(Convert.ToInt32(x.TruckId));
                    if (truck != null && truck.IsActive && !truck.Deleted)
                    {
                        if (vehicles.Where(o => o.vehicleId == x.TruckId).Any())
                        {
                            Console.WriteLine(x.TruckId + ": Found it already exists in Urgently...Will not attempt to add.");
                            await new TruckKeyValue()
                            {
                                TruckId = truck.Id,
                                KeyId = truckKey,
                                Value = "1"
                            }.SaveAsync();
                        }
                        else
                        {
                            if (client.TruckAdd(x.ProviderId,
                                new UrgentlyRestClient.Vehicle
                                ()
                                {
                                    id = x.TruckId,
                                    name = truck.Name,
                                    color = "Unknown",
                                    licensePlate = truck.PlateNumber,
                                    driverId = x.DriverId,
                                    phone = Core.FormatPhoneWithNumbersOnly((await Company.Company.GetByIdAsync(truck.CompanyId)).Phone),
                                    type = UrgentlyRestClient.Vehicle.FromTruckType(truck.Type)
                                }) == UrgentlyRestClient.UrgentlyResponse.OK)
                            {
                                await new TruckKeyValue()
                                {
                                    TruckId = truck.Id,
                                    KeyId = truckKey,
                                    Value = "1"
                                }.SaveAsync();
                            }
                        }
                    }
                }
                
            }

            // find any drivers that exist in urgently that dont' exist in towbook and delete them.
            urgentlyDrivers = client.DriverGetAll(p.ProviderId)
                .Where(o => o != null && o.DriverId != null && !o.DriverId.StartsWith("del"))
                .ToCollection();

            if (urgentlyDrivers != null)
                foreach (var ud in urgentlyDrivers)
                {
                    if (int.TryParse(ud.DriverId, out int urgentlyDriverId))
                    {
                        var towbookDriver = allDrivers.FirstOrDefault(o => o.Id == urgentlyDriverId);
                        if (towbookDriver == null || towbookDriver.Deleted || towbookDriver.EndDate != null)
                        {
                            if (true)
                            {
                                if (client.DriverDelete(p.ProviderId, ud.DriverId))
                                {
                                    var dkv = DriverKeyValue.GetByDriver(p.CompanyId, Convert.ToInt32(urgentlyDriverId),
                                        urgentlyProvider.ProviderId, "IsRegistered").FirstOrDefault();

                                    dkv?.Delete();
                                }
                                else
                                {
                                    Console.WriteLine(p.ProviderId + ": FAILED TO DELETE ORPHANED DRIVER: " + ud.ToJson());
                                }
                            }
                        }
                        Console.WriteLine("update driver: " + towbookDriver?.Name + "/" + urgentlyDriverId);

                        if (towbookDriver == null)
                            continue;

                        string firstName = "", lastName = "";
                        if (towbookDriver.Name.Contains(" "))
                        {
                            

                            // ignore numbers
                            var tname = new String(towbookDriver.Name.Where(r => !Char.IsDigit(r)).ToArray());

                            if (tname.Contains("(") && tname.Contains(")"))
                            {
                                // remove the word surrounded by paranethes in driver.Name

                                var si = tname.IndexOf("(");
                                var ei = tname.IndexOf(")");
                                if (si != -1 && ei != -1 && ei > si)
                                {
                                    tname  = tname.Substring(0, si) + tname.Substring(ei + 1).Replace("  ", " ");
                                }
                            }


                            var names = tname.Split(new char[] { ' ' }, 2, StringSplitOptions.RemoveEmptyEntries);
                            if (names.Length == 2)
                            {
                                firstName = Core.FormatName(names[0]);
                                lastName = Core.FormatName(names[1]);
                            }
                            else
                                firstName = Core.FormatName(tname);
                        }
                        else
                            firstName = towbookDriver.Name;

                        client.DriverUpdate(p.ProviderId, ud.DriverId,
                            new UrgentlyRestClient.DriverModel()
                            {
                                id = ud.DriverId,
                                firstName = firstName,
                                lastName = lastName,
                                phone = comp.Phone
                            });
                    }
                }

            var urgentlyTrucks = vehicles.Where(o => !o.vehicleId.StartsWith("del"));
            if (urgentlyTrucks != null)
            {
                Console.WriteLine(urgentlyTrucks.ToJson(true));
                foreach (var ut in urgentlyTrucks)
                {
                    if (int.TryParse(ut.vehicleId, out int urgentlyTruckId))
                    {
                        var towbookTruck = allTrucks.FirstOrDefault(o => o.Id == urgentlyTruckId);
                        if (towbookTruck == null || !towbookTruck.IsActive)
                        {
                            if (client.TruckDelete(p.ProviderId, ut.vehicleId))
                            {
                                var tkv = TruckKeyValue.GetByTruck(p.CompanyId, Convert.ToInt32(urgentlyTruckId))
                                    .FirstOrDefault(o => o.KeyId == truckKey);

                                await tkv?.DeleteAsync();
                            }
                        }

                        if (!int.TryParse(ut.driverId, out int linkedDriverId))
                            continue;

                        var linkedDriver = allDrivers.FirstOrDefault(o => o.Id == linkedDriverId);

                        if (linkedDriver == null)
                            continue;

                        var cl = UserLocationHistoryItem.GetCurrentByUserId(linkedDriver.UserId, DateTime.Now.AddHours(-4), DateTime.Now);

                        if (cl != null &&
                            towbookTruck != null)
                        {
                            client.TruckMarkAvailable(p.ProviderId, ut.vehicleId, new UrgentlyRestClient.TruckMarkAvailableObject()
                            {
                                latitude = cl.Latitude,
                                longitude = cl.Longitude,
                                services = new int[] { 2001, 2002, 2003, 2004, 2005, 2006, 2007, 2008 }
                            });

                            Console.WriteLine("Marked available: " + ut.vehicleId);
                        }

                    }
                    else
                    {
                        if (ut?.driverId != null && ut.driverId.StartsWith("del-"))
                        {
                            client.TruckMarkUnavailable(p.ProviderId, ut.vehicleId);
                        }
                    }
                }
            }

            // make sure all the associations match
            foreach (var vehicle in vehicles)
            {
                var assignment = driverTruckDefaults.Where(o => o.TruckId.ToString() == vehicle.vehicleId &&
                    allDrivers.Where(ro => ro.Id == o.DriverId && ro.EndDate == null).Any())
                    .OrderByDescending(o => o.ModifiedDate)
                    .FirstOrDefault();

                if (assignment != null && vehicle.driverId != assignment.DriverId.ToString())
                {
                    if (!vehicles.Where(o => o.driverId == assignment.DriverId.ToString()).Any())
                    {
                        Console.WriteLine(vehicle.vehicleId + ": Assigning Driver to Vehicle with driverId of " + assignment.DriverId);

                        client.TruckAssignDriver(p.ProviderId, vehicle.vehicleId,
                            assignment.DriverId.ToString());
                    }
                }
            }

            if (serviceCallsOnly || virtualTruckMode)
            {
                // wait 5 seconds.
                Thread.Sleep(5000);

                vehicles = client.TruckGetAll(p.ProviderId) ?? new List<UrgentlyRestClient.VehicleListItem>();

                // get most recent list - there is a delay from when we add and get
                urgentlyDrivers = client.DriverGetAll(p.ProviderId)
                    .Where(o => o != null && o.DriverId != null && !o.DriverId.StartsWith("del"))
                    .ToCollection();

                var services = new int[] { 2002, 2003, 2004, 2005 };

                if (virtualTruckMode)
                    services = new int[] { 2001, 2002, 2003, 2004, 2005, 2006, 2007, 2008 };

                foreach (var driver in urgentlyDrivers)
                {
                    if (!vehicles.Any(o => o.vehicleId == "virtual-" + driver.DriverId))
                    {
                        client.TruckAdd(p.ProviderId, UrgentlyRestClient.Vehicle.FromDriver(driver, virtualTruckMode));
                    }
                    else
                    {
                        var m = UrgentlyRestClient.Vehicle.FromDriver(driver, virtualTruckMode);
                        client.TruckUpdate(p.ProviderId, m.id, m);
                    }

                    int driverId = 0;

                    if (!int.TryParse(driver.DriverId, out driverId))
                        continue;

                    var ulhi = UserLocationHistoryItem.GetCurrentByUserId(
                           (await Driver.GetByIdAsync(driverId)).UserId,
                           DateTime.Now.AddDays(-1), DateTime.Now);

                    if (ulhi != null)
                    {
                        client.TruckMarkAvailable(p.ProviderId, "virtual-" + driver.DriverId,
                            new UrgentlyRestClient.TruckMarkAvailableObject()
                            {
                                latitude = ulhi.Latitude,
                                longitude = ulhi.Longitude,
                                services = services
                            });

                        var result = await client.TruckUpdateLocationWrapper(p.ProviderId, "virtual-" + driver.DriverId,
                            ulhi.Latitude,
                            ulhi.Longitude, driverId);

                        Console.WriteLine("Updated Location with Driver location: " + driver.DriverId + " " + ulhi.Latitude + "," + ulhi.Longitude);
                    }
                    else if (comp.Latitude != 0 && comp.Longitude != 0)
                    {
                        client.TruckMarkAvailable(p.ProviderId, "virtual-" + driver.DriverId,
                            new UrgentlyRestClient.TruckMarkAvailableObject()
                            {
                                latitude = comp.Latitude,
                                longitude = comp.Longitude,
                                services = services
                            });

                        await client.TruckUpdateLocationWrapper(p.ProviderId, "virtual-" + driver.DriverId,
                            comp.Latitude,
                            comp.Longitude, driverId);


                        Console.WriteLine("Updated Location with Company location: " + driver.DriverId + " " + comp.Latitude + "," + comp.Longitude);
                    }
                }
            }
        }

        public static async Task HandleRegistration(UrgentlyProvider up)
        {
            var urgentlyProvider = Provider.GetByName("Urgent.ly");
            var c = await Company.Company.GetByIdAsync(up.CompanyId);
            var drivers = Driver.GetByCompany(c).Where(o => o.EndDate == null).ToCollection();
            var trucks = await Truck.GetByCompanyAsync(c);
            var associations = DriverTruckDefault.GetByCompanyId(up.CompanyId);

            UrgentlyRestClient client;
            if (new int[] { 10000, 100769, 100770, 100771, 154042 }.Contains(up.CompanyId))
                client = UrgentlyRestClient.Get();
            else
                client = UrgentlyRestClient.GetProduction();
            
            var urgExistingDrivers = client.DriverGetAll(up.ProviderId);
            var urgExistingTrucks = client.TruckGetAll(up.ProviderId);

            foreach (var assoc in associations)
            {
                Console.WriteLine(assoc.ToJson());

                var driverToAdd = drivers.Where(o => o.Id == assoc.DriverId).FirstOrDefault();
                var truckToAdd = trucks.Where(o => o.Id == assoc.TruckId).FirstOrDefault();

                if (driverToAdd != null)
                {
                    var urgentlyDriver = urgExistingDrivers.Where(o => o.DriverId == driverToAdd.Id.ToString()).FirstOrDefault();
                    var isRegistered = DriverKeyValue.GetByDriver(up.CompanyId, driverToAdd.Id, urgentlyProvider.ProviderId, "IsRegistered").FirstOrDefault();
                    if (urgentlyDriver == null)
                    {
                        await HandleRegistrationAsync(up, driverToAdd);
                    }
                    else
                    {
                        if (isRegistered == null)
                        {
                            new DriverKeyValue()
                            {
                                DriverId = driverToAdd.Id,
                                KeyId = DriverKey.GetByProviderId(urgentlyProvider.ProviderId)
                                    .Where(o => o.Name == "IsRegistered").First().Id,
                                Value = "1"
                            }.Save();

                            Console.WriteLine("Added DriverID " + driverToAdd.Id);
                        }
                        else
                        {
                            Console.WriteLine("Driver ID already exists:" + driverToAdd.Id);
                        }
                    }

                    if (truckToAdd != null)
                    {
                        var tk = TruckKey.GetByProviderId(urgentlyProvider.ProviderId, "IsRegistered");

                        var isRegistered2 = TruckKeyValue.GetByTruck(up.CompanyId, truckToAdd.Id).FirstOrDefault(o => o.Id == tk.Id);
                        if (isRegistered2 == null)
                        {
                            await HandleRegistrationAsync(up, truckToAdd, driverToAdd.Id);
                        }
                    }
                }
            }

            foreach (var x in drivers.Where(o => !associations.Where(ao => ao.DriverId == o.Id).Any()))
            {
                if (!urgExistingDrivers.Any(o => o.DriverId == x.Id.ToString()))
                    await HandleRegistrationAsync(up, x);
            }
        }

        private static async Task HandleRegistrationAsync(UrgentlyProvider up, Driver d)
        {
            var urgentlyProvider = Provider.GetByName("Urgent.ly");
            if (d.EndDate != null)
            {
                Console.WriteLine(up.ProviderId + "/driverId=" + d.Id + ",name=" + d.Name + ": driver has endDate. Don't sync.");
                return;
            }

            try
            {
                UrgentlyRestClient client;
                if (new int[] { 10000, 100769, 100770, 100771, 154042 }.Contains(d.CompanyId))
                    client = UrgentlyRestClient.Get();
                else
                    client = UrgentlyRestClient.GetProduction();

                var driver = await Driver.GetByIdAsync(d.Id);

                string firstName = "", lastName = "";
                if (driver.Name.Contains(" "))
                {
                    // ignore numbers
                    var tname = new String(driver.Name.Where(r => !Char.IsDigit(r)).ToArray());

                    var names = tname.Split(new char[] { ' ' }, 2, StringSplitOptions.RemoveEmptyEntries);
                    if (names.Length == 2)
                    {
                        firstName = Core.FormatName(names[0]);
                        lastName = Core.FormatName(names[1]);
                    }
                    else
                        firstName = Core.FormatName(driver.Name);
                }
                else
                    firstName = driver.Name;

                var company = await Company.Company.GetByIdAsync(up.CompanyId);
                
                if (Core.IsPhoneValidStandard(company.Phone))
                {
                    if (await client.DriverAdd(up.ProviderId,
                        new UrgentlyRestClient.DriverModel()
                        {
                            id = driver.Id.ToString(),
                            firstName = firstName,
                            lastName = lastName,
                            phone = Core.FormatPhoneWithNumbersOnly(driver.MobilePhone),
                            photoURL = null
                        }))
                    {
                        new DriverKeyValue()
                        {
                            DriverId = driver.Id,
                            KeyId = DriverKey.GetByProviderId(urgentlyProvider.ProviderId).Where(o => o.Name == "IsRegistered").First().Id,
                            Value = "1"
                        }.Save();
                    }
                    else
                    {
                        Console.WriteLine("failed to add driver:" + driver.Id);
                    }
                }

                
            }
            catch (Exception e)
            {
                Console.WriteLine(e.ToString());
            }
        }

        public async static Task HandleRegistrationAsync(UrgentlyProvider up, Truck t, int driverId)
        {
            var urgentlyProvider = Provider.GetByName("Urgent.ly");

            Console.WriteLine("HandleRegistration:Truck " + t.Id +" / " + t.Name);

            try
            {
                UrgentlyRestClient client;
                if (!new int[] { 10000, 100769, 100770, 100771, 154042 }.Contains(t.CompanyId))
                    client = UrgentlyRestClient.Get();
                else
                    client = UrgentlyRestClient.GetProduction();

                var truck = await Truck.GetByIdAsync(t.Id);

                var ta = client.TruckAdd(up.ProviderId,
                    new UrgentlyRestClient.Vehicle
                    ()
                    {
                        id = t.Id.ToString(),
                        name = truck.Name,
                        color = "Unknown",
                        licensePlate = truck.PlateNumber,
                        driverId = driverId.ToString(),
                        phone = Core.FormatPhoneWithNumbersOnly((await Company.Company.GetByIdAsync(truck.CompanyId)).Phone),
                        type = UrgentlyRestClient.Vehicle.FromTruckType(truck.Type)
                    });

                if (ta == UrgentlyRestClient.UrgentlyResponse.OK)
                {

                    await new TruckKeyValue()
                    {
                        TruckId = truck.Id,
                        KeyId = TruckKey.GetByProviderId(urgentlyProvider.ProviderId).First(o => o.Name == "IsRegistered").Id,
                        Value = "1"
                    }.SaveAsync();
                }
                else
                {
                    Console.WriteLine("Failed to add truck.");
                }
            }
            catch(Exception e)
            {
                Console.WriteLine(e.ToString());
            }
        }
    }
    
}
