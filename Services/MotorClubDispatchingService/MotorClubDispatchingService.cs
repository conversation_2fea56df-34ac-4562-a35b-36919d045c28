using Agero;
using Agero.Types;
using Azure.Messaging.ServiceBus;
using Extric.Towbook.Accounts;
using Extric.Towbook.Dispatch;
using Extric.Towbook.EventNotifications;
using Extric.Towbook.Integration;
using Extric.Towbook.Integration.MotorClubs;
using Extric.Towbook.Integration.MotorClubs.Dispatch;
using Extric.Towbook.Integration.MotorClubs.Queue;
using Extric.Towbook.Integration.MotorClubs.Services;
using Extric.Towbook.Integrations.Email;
using Extric.Towbook.Integrations.MotorClubs.Agero;
using Extric.Towbook.Integrations.MotorClubs.Allstate;
using Extric.Towbook.Integrations.MotorClubs.Issc;
using Extric.Towbook.Integrations.MotorClubs.Issc.Events;
using Extric.Towbook.Integrations.MotorClubs.Sykes;
using Extric.Towbook.Integrations.MotorClubs.Urgently;
using Extric.Towbook.Storage;
using Extric.Towbook.Utility;
using Microsoft.Extensions.Hosting;
using Newtonsoft.Json;
using NLog;
using NLog.Config;
using ProtoBuf;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Mail;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using static Extric.Towbook.Vehicle.VehicleUtility;
using Allstate = Extric.Towbook.Integrations.MotorClubs.Allstate.Proxy;
using Fleetnet = Extric.Towbook.Integrations.MotorClubs.Fleetnet;
using Gerber = Extric.Towbook.Integrations.MotorClubs.Gerber;
using Extric.Towbook.Integrations.MotorClubs.Fleetnet;
using Extric.Towbook.Integrations.MotorClubs.Honk;
using Extric.Towbook.Integrations.MotorClubs.Aaa;
using Extric.Towbook.Integrations.MotorClubs.StackThree;
using Extric.Towbook.Integrations.MotorClubs.Trx;
using Extric.Towbook.Integrations.MotorClubs.RoadsideProtect;
using Extric.Towbook.Integrations.MotorClubs.Aaa.ACG;
using Extric.Towbook.Integrations.MotorClubs.Nac;
using Extric.Towbook.Integrations.MotorClubs.Nsd;
using NewRelic.Api.Agent;
using System.ServiceModel;
using System.Net.Http;
using System.Data.SqlClient;
using Extric.Towbook.Integrations.MotorClubs.Aaa.WaNy;
using Extric.Towbook.Services.MotorClubDispatchingService.AutoDispatch;

namespace Extric.Towbook.Services.MotorClubDispatchingService
{
    public sealed class AcceptModel
    {
        public int Id { get; set; }
        public int Eta { get; set; }
        public string FullName { get; set; }
        public string PhoneNumber { get; set; }
        public int MasterAccountReasonId { get; set; }
        public int OwnerUserId { get; set; }
        public int DriverId { get; set; }
        public bool TireAvailable { get; set; }
        public string Notes { get; set; }
    }

    public partial class MotorClubDispatchingService : IHostedService
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();
        private readonly IHostApplicationLifetime _appLifetime;
        private readonly string[] _args;

        private static Dictionary<string, Consumer> consumers;

        internal static readonly int MyClientVersionId = Platform.ClientVersion.GetByGitHash("mcds").Id;
        private static readonly string commitId = Core.GetCommitId();

        public MotorClubDispatchingService(string[] args, IHostApplicationLifetime appLifetime)
        {
            try
            {
                consumers = new Dictionary<string, Consumer>();
                _args = args;
                _appLifetime = appLifetime;
                _appLifetime.ApplicationStopped.Register(OnStopped);

                Core.SetGlobalUriConnectionLimit(new Uri("https://api.towbook.com"), 32);
                Core.SetGlobalUriConnectionLimit(new Uri(Agero.AgeroRestClient.URL_BASE), 32);
                Core.SetGlobalUriConnectionLimit(new Uri(Integrations.MotorClubs.Issc.IsscConfig.GetByEnvironmentType(Integrations.MotorClubs.Issc.EnvironmentType.Live).UrlBase), 32);

                ConfigurationItemFactory.Default.Targets.RegisterDefinition("ServiceBus", typeof(Extric.Towbook.Integration.MotorClubs.Services.ServiceBusTarget));
                ConfigurationItemFactory.Default.Targets.RegisterDefinition("Slack", typeof(Extric.Towbook.Integration.MotorClubs.Services.SlackTarget));

                Integrations.MotorClubs.Allstate.AllstateRestClient.GetProduction();
                CosmosDB.Get();

                if (args.Length > 0 && args.Contains("-disableAllstate"))
                    MotorClubDispatchingService.disableAllstate = true;

                
                //NewRelic.Api.Agent.NewRelic.SetApplicationName("McDispatchingLocal");
                NewRelic.Api.Agent.NewRelic.StartAgent();
            }
            catch (Exception ex)
            {
                logger.Log(LogLevel.Info,  "Application " + ex.ToString());
            }
        }

        public async Task StartAsync(CancellationToken cancellationToken)
        {
            logger.Info("MotorClubDispatchingService Start Async ...");
            await OnStart(_args);
        }

        public async Task StopAsync(CancellationToken cancellationToken)
        {
            logger.Info("MotorClubDispatchingService Stop Async ...");
            await OnStop();
        }

        protected async Task OnStart(string[] args)
        {
            logger.Log(LogLevel.Info, "Service Started on {0}", Environment.MachineName);

            if (args.Contains("-autodispatchdisableassign") ||
                args.Contains("/autodispatchdisableassign"))
                AutoDispatch.AutoDispatchServiceBusHandler.DisableDriverAssign = true;

            if (args.Contains("/cds") ||
                args.Contains("-cds") ||
                args.Contains("-cdsonly") ||
                args.Contains("/cdsonly"))
            {
                logger.Log(LogLevel.Info, "Including Company Dispatch Status Handler");

                await AutoDispatch.CompanyDispatchStatusServiceBusHandler.Init();

                if (args.Contains("-cdsonly"))
                {
                    logger.Log(LogLevel.Info, "CDS Only mode - not watching any other queues aside form CDS queue.");
                    return;
                }
            }

            if (args.Contains("/autodispatchonly") ||
                args.Contains("-autodispatchonly") ||
                args.Contains("-autodispatch") ||
                args.Contains("/autodispatch"))
            {
                logger.Log(LogLevel.Info, "Including AutoDispatch Handler...");

                await AutoDispatch.AutoDispatchServiceBusHandler.Init();

                if (args.Contains("/autodispatchonly") || args.Contains("-autodispatchonly"))
                {
                    logger.Log(LogLevel.Info, "ADS Only mode - not watching any other queues aside form ADS queue.");
                    return;
                }
            }

            if (args.Contains("/nophotos") ||
                args.Contains("-nophotos"))
                DisablePhotoProcessing = true;

            if (args.Contains("/inbound"))
                InboundDispatchOnly = true;

            AsyncHelper.RunSync(() => DigitalDispatchAsync());

            logger.Info("MotorClubDispatching Service is running");
        }

        private bool InboundDispatchOnly = false;
        private bool DisablePhotoProcessing = false;

        protected async Task OnStop()
        {
            await AutoDispatch.AutoDispatchServiceBusHandler.Deinit();
            await AutoDispatch.CompanyDispatchStatusServiceBusHandler.Deinit();

            await DeInit();

            logger.Log(LogLevel.Info, "Service Stopping on {0}", Environment.MachineName);

        }

        private void OnStopped()
        {
            logger.Info("MotorClubDispatchingService stopped successfully");
            LogManager.Shutdown();
            Console.WriteLine("MotorClubDispatchingService Stopped finished");
        }

        public static Dictionary<object, object> ToDictionary(string key, object value)
        {
            return new Dictionary<object, object>()
            {
                [key] = value
            };
        }
        public static IEnumerable<string> Getter(ServiceBusReceivedMessage carrier, string key)
        {
            object objValue;
            bool found = carrier.ApplicationProperties.TryGetValue(key, out objValue);

            string value = null;
            if (found)
                value = objValue.ToString();

            return value == null ? null : new string[] { value };
        }

        [Trace]
        public async Task DigitalDispatchAsync()
        {
            await SubscribeToExpiredQueue();

            #region Incoming Queue
            var incomingOptions = new OnMessageOptions()
            {
                AutoCompleteMessages = false,
                MaxConcurrentCalls = 32,
                ReceiveMode = ServiceBusReceiveMode.PeekLock
            };

            var iq = await ServiceBusHelper.CreateConsumerQueueAsync(DigitalDispatchService.IncomingQueueName, incomingOptions);


            var pq = await ServiceBusHelper.CreateConsumerQueueAsync("mc-dispatch-inbound", incomingOptions);
            await pq.OnMessageAsync(
                async (r) => await processInbound(r),
                async (e) => await DigitalDispatchSync_ExceptionReceived(e));
            consumers.Add("mc-dispatch-inbound", pq);

            if (InboundDispatchOnly)
                return;

            await iq.OnMessageAsync(
                async (r) => await processInbound(r),
                async (e) => await DigitalDispatchSync_ExceptionReceived(e));

            consumers.Add(DigitalDispatchService.IncomingQueueName, iq);

            #endregion

            #region Outgoing Queue
            var outgoingOptions = new OnMessageOptions()
            {
                AutoCompleteMessages = false,
                MaxConcurrentCalls = 16,
                ReceiveMode = ServiceBusReceiveMode.PeekLock
            };

            var oq = await ServiceBusHelper.CreateConsumerQueueAsync(DigitalDispatchService.OutgoingQueueName, outgoingOptions);

            await oq.OnMessageAsync(
                async (r) => await DigitalDispatchOutProcess(r),
                async (e) => await DigitalDispatchSync_ExceptionReceived(e));

            consumers.Add(DigitalDispatchService.OutgoingQueueName, oq);

            if (!DisablePhotoProcessing)
            {
                var pq2 = await ServiceBusHelper.CreateConsumerQueueAsync("mc-photos-outbound", outgoingOptions);

                await pq2.OnMessageAsync(
                    async (r) => await DigitalDispatchOutProcess(r),
                    async (e) => await DigitalDispatchSync_ExceptionReceived(e));

                consumers.Add("mc-photos-outbound", pq2);
            }

            #endregion

            #region Synchronization Queue

            var synchronizationOptions = new OnMessageOptions()
            {
                AutoCompleteMessages = false,
                MaxConcurrentCalls = 1,
                ReceiveMode = ServiceBusReceiveMode.PeekLock
            };

            var synchronizationQConsumer = await ServiceBusHelper.CreateConsumerQueueAsync(DigitalDispatchService.SynchronizationQueueName, synchronizationOptions);

            await synchronizationQConsumer.OnMessageAsync(async (r) =>
            {
                NewRelic.Api.Agent.NewRelic.GetAgent().CurrentTransaction.AcceptDistributedTraceHeaders(r.Message, Getter, NewRelic.Api.Agent.TransportType.Queue);
                NewRelic.Api.Agent.NewRelic.SetTransactionName(null, "DigitalDispatchSyncQueue");

                var body = JsonConvert.DeserializeObject<DigitalDispatchActionQueueItem>(r.GetBody<string>()).JsonObject;
                dynamic obj = body.FromJson();

                logger.LogEvent("Synchronization Message: Id = {0}, {1}", null, LogLevel.Info, r.Message.MessageId, r.Message.Subject);

                if (r.Message.ApplicationProperties.ContainsKey("id"))
                {
                    var qi = DigitalDispatchActionQueueItem.GetById(Convert.ToInt32(r.Message.ApplicationProperties["id"]));

                    dynamic jsonObj = body.FromJson();

                    if (qi.AccountId != null)
                    {
                        var acc = await Account.GetByIdAsync(qi.AccountId.Value);
                        if (acc == null)
                        {
                            logger.Log(LogLevel.Warn, "DeadLettered " + r.Message.MessageId + " because its accountId of {0} resolved to null.", qi.AccountId.Value);

                            await r.DeadLetterAsync();
                            return;
                        }

                        var ma = await MasterAccount.GetByIdAsync(acc.MasterAccountId);
                        if (ma == null)
                        {
                            logger.Log(LogLevel.Warn, "DeadLettered " + r.Message.MessageId + " because its masterAccountId of {0} resolved to null.", acc.MasterAccountId);

                            await r.DeadLetterAsync();
                            return;
                        }

                        switch (ma.Name)
                        {
                            case "Geico":
                                //HandleIsscQueueOutgoingMessage(qi, jsonObj, r);
                                break;
                            case "Agero":
                                await HandleAgeroQueueSynchronizationMessage(qi, jsonObj, r);
                                break;
                        }
                    }
                    else
                    {
                        logger.Log(LogLevel.Warn, "DeadLettered " + r.Message.MessageId + " because it doesn't have an AccountId set.");
                        await r.DeadLetterAsync();
                    }
                }
                else
                {
                    await r.DeadLetterAsync();
                    logger.Log(LogLevel.Warn, "DeadLettered " + r.Message.MessageId + " because it doesn't contain an id property.");
                }
            }, async (e) => await DigitalDispatchSync_ExceptionReceived(e));

            consumers.Add(DigitalDispatchService.SynchronizationQueueName, synchronizationQConsumer);

            #endregion
        }

        public sealed class DriverVehicleAssignmentPayload
        {
            public int DriverId { get; set; }
            public int TruckId { get; set; }
            public int OriginalTruckId { get; set; }
        }
        
        public static async Task HandleShareDriverVehicleAssignment(DigitalDispatchActionQueueItem qi, dynamic jsonObj, ProcessMessageEventArgs r)
        {
            var pl = JsonConvert.DeserializeObject<DriverVehicleAssignmentPayload>(qi.JsonObject);
            int driverId = pl.DriverId;
            int truckId = pl.TruckId;

            foreach (var aaa in AaaContractor.GetByCompanyId(qi.CompanyId.Value)
                .Where(o => o.MasterAccountId == MasterAccountTypes.AaaAcg))
            {
                var serviceResourceId = DriverKeyValue.GetByDriver(qi.CompanyId.Value, driverId, Provider.Towbook.ProviderId, "AcgServiceResourceId").FirstOrDefault()?.Value;

                var aaaDriver = AaaDriver.GetByDriverId(aaa.AaaContractorId, driverId);
                if (aaaDriver?.AaaId != null)
                    serviceResourceId = aaaDriver.AaaId;

                var vehicleId = TruckKeyValue.GetByCompanyId(qi.CompanyId.Value, Provider.Towbook.ProviderId, "AcgVehicleId")
                    .FirstOrDefault(xr => xr.TruckId == truckId)?.Value ?? "";

                var aaaVehicle = AaaVehicle.GetByTruckId(aaa.AaaContractorId, truckId);
                if (aaaVehicle?.AaaId != null)
                    vehicleId = aaaVehicle.AaaId;

                if (!string.IsNullOrWhiteSpace(serviceResourceId))
                {
                    var client = AcgGetClient(aaa, qi);
                    var resp = await client.AssignVehicleToServiceResource(serviceResourceId, vehicleId);

                    var driver = await Driver.GetByIdAsync(driverId);
                    var truck = await Truck.GetByIdAsync(truckId);

                    var message = driver.Name + " has been assigned to " + truck.Name + " in Towbook";

                    if (!resp.Success)
                    {
                        message += ", but could not be assigned in ACG due to the following error:\n\n" + resp.Message;

                        // revert back to original truck assignment because it failed.
                        var dtd = DriverTruckDefault.GetByDriverId(pl.DriverId);
                        if (dtd != null)
                        {
                            dtd.TruckId = pl.OriginalTruckId;
                            await dtd.Save(qi.OwnerUserId ?? 1);
                        }
                    }
                    else
                        message += ".";

                    await PushNotificationProvider.BackgroundJobStatusUpdate(qi.CompanyId.Value,
                        qi.QueueItemId,
                        "assign_truck",
                        resp.Success,
                        message,
                        new
                        {
                            response = resp,
                            originalTruckId = pl.OriginalTruckId
                        });
                }
            }

            foreach (var aaa in AaaContractor.GetByCompanyId(qi.CompanyId.Value)
                .Where(o => o.MasterAccountId == MasterAccountTypes.AaaNationalFsl))
            {
                var serviceResourceId = DriverKeyValue.GetByDriver(
                    qi.CompanyId.Value, driverId, Provider.Towbook.ProviderId, "AcgServiceResourceId").FirstOrDefault()?.Value;

                var aaaDriver = AaaDriver.GetByDriverId(aaa.AaaContractorId, driverId);
                if (aaaDriver?.AaaId != null)
                    serviceResourceId = aaaDriver.AaaId;

                var vehicleId = TruckKeyValue.GetByCompanyId(qi.CompanyId.Value, Provider.Towbook.ProviderId, "AcgVehicleId")
                    .FirstOrDefault(xr => xr.TruckId == truckId)?.Value ?? "";

                var aaaVehicle = AaaVehicle.GetByTruckId(aaa.AaaContractorId, truckId);
                if (aaaVehicle?.AaaId != null)
                    vehicleId = aaaVehicle.AaaId;

                if (!string.IsNullOrWhiteSpace(serviceResourceId))
                {
                    var client = MotorClubDispatchingServiceAaaNationalFsl.AcgGetClient(aaa, qi);
                    await client.AssignVehicleToServiceResource(serviceResourceId, vehicleId);
                }
            }


            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);

            if (r != null && r.Message.MessageId != "MOCK")
                await r.CompleteAsync();
        }

        [Transaction]
        public async Task DigitalDispatchOutProcess(ProcessMessageEventArgs r)
        {
            NewRelic.Api.Agent.NewRelic.GetAgent().CurrentTransaction.AcceptDistributedTraceHeaders(r.Message, Getter, NewRelic.Api.Agent.TransportType.Queue);

            var queueItemId = r.Message.ApplicationProperties.ContainsKey("id") ? Convert.ToInt64(r.Message.ApplicationProperties["id"]) : -1;

            logger.LogEvent("Outgoing Message: Id = {0}, {1}", null, LogLevel.Info,
                new Dictionary<object, object>
                {
                    ["queueItemId"] = queueItemId,
                    ["json"] = r.Message.ApplicationProperties.ContainsKey("jsonObject") ? r.Message.ApplicationProperties["jsonObject"].ToString() : null
                },
                r.Message.MessageId,
                r.Message.Subject);
            try
            {
                if (r.Message.ApplicationProperties.ContainsKey("id"))
                {
                    var qi = DigitalDispatchActionQueueItem.GetById(Convert.ToInt64(r.Message.ApplicationProperties["id"]));
                    NewRelic.Api.Agent.NewRelic.SetTransactionName(nameof(DigitalDispatchOutProcess), Enum.GetName(qi.Type));

                    if (qi.Type == DigitalDispatchActionQueueItemType.GatewayEvent)
                    {
                        await HandleGatewayMessage(qi, r);
                        return;
                    }

                    dynamic jsonObj = r.Message.ApplicationProperties["jsonObject"] != null ? r.Message.ApplicationProperties["jsonObject"].ToString().FromJson() : qi.JsonObject.FromJson();

                    if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingConnect ||
                        qi.Type == DigitalDispatchActionQueueItemType.InternalVerifyConnection)
                    {
                        await HandleIsscQueueOutgoingMessage(qi, jsonObj, r);
                        return;
                    }

                    if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingShareDriverVehicleAssignment)
                    {
                        await HandleShareDriverVehicleAssignment(qi, jsonObj, r);
                        return;
                    }

                    // these messages shouldn't be in this queue but there's a bug elsewhere in the system causing them to be sent here
                    // so we'll redirect it to the inbound processor.
                    if (qi.Type == DigitalDispatchActionQueueItemType.IncomingCallOfferedOutOfNetwork)
                    {
                        await processInbound(r);
                        return;
                    }

                    if (qi.AccountId != null)
                    {
                        var acc = await Account.GetByIdAsync(qi.AccountId.Value);
                        if (acc == null)
                        {
                            logger.Log(LogLevel.Warn, "DeadLettered " + r.Message.MessageId + " because its accountId of {0} resolved to null.", qi.AccountId.Value);

                            await r.CompleteAsync();
                            return;
                        }


                        var ma = await MasterAccount.GetByIdAsync(r.Message.ApplicationProperties.ContainsKey("masterAccountId") ? Convert.ToInt32(r.Message.ApplicationProperties["masterAccountId"]) : acc.MasterAccountId);

                        NewRelic.Api.Agent.NewRelic.GetAgent().CurrentTransaction
                            .AddCustomAttribute("mcDispatchProcessType", qi.Type)
                            .AddCustomAttribute("mcDispatchMasterAccount", ma.Id);

                        if (ma == null)
                        {
                            logger.Log(LogLevel.Warn, "Ignored " + r.Message.MessageId + " because its masterAccountId of {0} resolved to null.", acc.MasterAccountId);

                            await PushNotificationProvider.BackgroundJobStatusUpdate(qi.CompanyId.Value,
                                qi.QueueItemId,
                                "digitaldispatch_login",
                                false,
                                "Login Status",
                                "Missing Configuration - MasterAccountId isn't set.");

                            await r.CompleteAsync();
                            return;
                        }

                        if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingRegister)
                        {
                            var rf = await RegisterContractor(qi, jsonObj, r);

                            await PushNotificationProvider.BackgroundJobStatusUpdate(qi.CompanyId.Value,
                                qi.QueueItemId,
                                "digitaldispatch_register",
                                !rf,
                                "Digital Dispatch Registration",
                                "");

                            await r.CompleteAsync();
                            return;
                        }
                        else if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingDeregister)
                        {
                            var rf = await DeregisterContractorAsync(qi, jsonObj, r);

                            await PushNotificationProvider.BackgroundJobStatusUpdate(qi.CompanyId.Value,
                               qi.QueueItemId,
                               "digitaldispatch_deregister",
                               !rf,
                               "Digital Dispatch Deletion",
                               "");

                            await r.CompleteAsync();
                            return;
                        }


                        if (qi.Type == DigitalDispatchActionQueueItemType.InternalSendVoiceDispatch)
                        {
                            logger.Log(LogLevel.Info, "VoiceDispatch/" + r.Message.MessageId + ": Making voice dispatch phone call... {1}", acc.MasterAccountId, JsonExtensions.ToJson(jsonObj));
                            CallRequest cr = CallRequest.GetById(((int)(jsonObj.CallRequestId)));
                            if (cr.Status == CallRequestStatus.None)
                            {

                                var vd = VoiceDispatchService.CreateCall(cr.CompanyId,
                                    (string)jsonObj.PhoneNumber,
                                    (int)jsonObj.CallRequestId, 1, 0);

                                logger.Log(LogLevel.Info, "VoiceDispatch/" + r.Message.MessageId + ": Response: {0}",
                                    JsonExtensions.ToJson(vd));
                            }
                            else
                            {
                                logger.Log(LogLevel.Info, "VoiceDispatch/" + r.Message.MessageId + ": Ignoring because callRequest Status is {0}",
                                    Enum.GetName(typeof(CallRequestStatus), cr.Status));
                            }

                            await r.CompleteAsync();
                            return;
                        }

                        switch (ma.Id)
                        {
                            case MasterAccountTypes.Geico:
                            case MasterAccountTypes.Tesla:
                                await HandleIsscQueueOutgoingMessage(qi, jsonObj, r);
                                break;

                            case MasterAccountTypes.RoadsideProtect:
                                if (RoadsideProtectContractor.GetByAccountId(qi.AccountId.GetValueOrDefault()).Any())
                                    await RoadsideProtectService.HandleOutgoing(ma.Id, qi, jsonObj, r);
                                else
                                    await HandleIsscQueueOutgoingMessage(qi, jsonObj, r);
                                break;

                            case MasterAccountTypes.OonRoadsideProtect:
                                await RoadsideProtectService.HandleOutgoing(ma.Id, qi, jsonObj, r);
                                break;

                            case MasterAccountTypes.AlliedDispatch:
                                if (TrxContractor.GetByAccountId(qi.AccountId.GetValueOrDefault()).Any())
                                    await TrxService.HandleOutgoing(ma.Id, qi, jsonObj, r);
                                else
                                    await HandleIsscQueueOutgoingMessage(qi, jsonObj, r);
                                break;

                            case MasterAccountTypes.OonTrx:
                                await TrxService.HandleOutgoing(ma.Id, qi, jsonObj, r);
                                break;

                            case MasterAccountTypes.Agero:
                                await HandleAgeroQueueOutgoingMessage(qi, jsonObj, r);
                                break;

                            case MasterAccountTypes.OonAllstate:
                            case MasterAccountTypes.Allstate:
                                await HandleAllstateQueueOutgoingMessage(qi, jsonObj, r);
                                break;

                            case MasterAccountTypes.OonQuest:
                            case MasterAccountTypes.Quest:
                                await HandleQuestQueueOutgoingMessage(qi, jsonObj, r);
                                break;

                            case MasterAccountTypes.Nsd:
                                await HandleNsdQueueOutgoingMessage(qi, jsonObj, r);
                                break;

                            case MasterAccountTypes.Nac:
                                await NacService.HandleOutgoing(MasterAccountTypes.Nac, qi, r);
                                break;

                            case MasterAccountTypes.Urgently:
                            case MasterAccountTypes.OonUrgently:
                                await HandleUrgentlyQueueOutgoingMessage(qi, jsonObj, r);
                                break;

                            case MasterAccountTypes.Gerber:
                                await HandleGerberQueueOutgoingMessage(qi, jsonObj, r);
                                break;

                            case MasterAccountTypes.Towbook:
                                await HandleTowbookQueueOutgoingMessage(qi, jsonObj, r);
                                break;

                            case MasterAccountTypes.Fleetnet:
                            case MasterAccountTypes.Servicase:
                                await HandleFleetnetOutgoingMessage(qi, jsonObj, r);
                                break;

                            case MasterAccountTypes.OonSwoop:
                            case MasterAccountTypes.Swoop:
                                await HandleSwoopQueueOutgoingMessage(qi, jsonObj, r);
                                break;

                            case MasterAccountTypes.Sykes:
                                await HandleSykesQueueOutgoingMessage(qi, jsonObj, r);
                                break;

                            case MasterAccountTypes.Honk:
                            case MasterAccountTypes.OonHonk:
                                await HandleHonkQueueOutgoingMessage(qi, jsonObj, r);
                                break;

                            case MasterAccountTypes.AaaAce:
                                await HandleAaaAceQueueOutgoingMessage(qi, jsonObj, r);
                                break;

                            case MasterAccountTypes.AaaNortheast:
                                await HandleAaaNortheastQueueOutgoingMessage(qi, jsonObj, r);
                                break;
                            case MasterAccountTypes.AaaAca:
                                await HandleAaaAcaQueueOutgoingMessage(qi, jsonObj, r);
                                break;

                            case MasterAccountTypes.AaaWashington:
                            case MasterAccountTypes.AaaNewYork:
                                await HandleAaaWaNyQueueOutgoingMessage(qi, jsonObj, r);
                                break;
                            case MasterAccountTypes.AaaAcg:
                                await HandleAaaAcgQueueOutgoingMessage(qi, jsonObj, r);
                                break;

                            case MasterAccountTypes.AaaNationalFsl:
                                await MotorClubDispatchingServiceAaaNationalFsl
                                    .HandleAaaFslQueueOutgoingMessage(qi, jsonObj, r);
                                break;

                            case MasterAccountTypes.AaaNational:
                                await HandleAaaNationalQueueOutgoingMessage(qi, jsonObj, r);
                                break;

                            case MasterAccountTypes.Bcaa:
                                await BcaaService.HandleBcaaQueueOutgoingMessage(qi, jsonObj, r);
                                break;

                            case MasterAccountTypes.OonAgero:
                                await OutOfNetworkOutgoing.HandleOutOfNetwork(qi, jsonObj, r);
                                break;

                            case MasterAccountTypes.StackThreeAtlas:
                            case MasterAccountTypes.DrivenSolutions:
                            case MasterAccountTypes.OonDrivenSolutions:
                                await StackThreeService.HandleOutgoing(ma.Id, qi, jsonObj, r);
                                break;

                            default:
                                logger.Fatal(ma.Id, Enum.GetName(typeof(DigitalDispatchActionQueueItemType), qi.Type),
                                    $"No implementation written for {ma.Name}",
                                    companyId: qi.CompanyId,
                                    queueItemId: qi.QueueItemId);
                                await r.CompleteAsync();
                                break;
                        }
                    }
                    else
                    {
                        logger.Log(LogLevel.Warn, "Ignored " + r.Message.MessageId + " because it doesn't have an AccountId set.");
                        await r.CompleteAsync();
                    }
                }
                else
                {
                    logger.Log(LogLevel.Warn, "Ignored " + r.Message.MessageId + " because it doesn't contain an id property.");
                    await r.CompleteAsync();
                }
            }
            catch (Exception ue)
            {
                var log = new LogEventInfo();
                log.Level = LogLevel.Error;
                log.Exception = ue;
                log.Message = "Outbound Queue Process Exception: " + ue.Message;
                log.Properties["queueItemId"] = queueItemId;
                log.Properties["messageId"] = r.Message.MessageId;
                log.Properties["subject"] = r.Message.Subject;
                log.Properties["commitId"] = commitId;

                if (r.Message.ApplicationProperties.ContainsKey("OwnerUserId"))
                {
                    var userId = Convert.ToInt32(r.Message.ApplicationProperties["OwnerUserId"]);
                    log.Properties["ownerUserId"] = userId;
                    log.Properties["userFullname"] = (await User.GetByIdAsync(userId))?.FullName;
                }

                var acq = DigitalDispatchActionQueueItem.GetById(queueItemId);
                if (acq != null)
                {
                    log.Properties["json"] = r.GetBody<string>();
                    if (acq.CompanyId != null)
                    {
                        log.Properties["companyId"] = acq.CompanyId;
                        log.Properties["companyName"] = (await Company.Company.GetByIdAsync(acq.CompanyId.Value))?.Name;
                    }
                    if (acq.AccountId != null)
                    {
                        log.Properties["accountId"] = acq.AccountId;
                    }
                }

                logger.Log(log);
            }
        }

        [Transaction]
        private async Task processInbound(ProcessMessageEventArgs r)
        {
            NewRelic.Api.Agent.NewRelic.GetAgent().CurrentTransaction.AcceptDistributedTraceHeaders(r.Message, Getter, NewRelic.Api.Agent.TransportType.Queue);
            long queueItemId = 0;

            if (r.Message.ApplicationProperties.ContainsKey("id"))
                queueItemId = Convert.ToInt64(r.Message.ApplicationProperties["id"]);

            logger.LogEvent("Processing digital dispatch.. Incoming Message: Id = {0}, Label = {1}", null, LogLevel.Info,
                properties: ToDictionary("queueItemId", queueItemId),
                r.Message.MessageId,
                r.Message.Subject);

            try
            {
                if (queueItemId > 0)
                {
                    await DigitalDispatchInProcess(queueItemId, r);
                }
                else
                {
                    await r.CompleteAsync();
                    var body = r.GetBody<string>();
                    logger.LogEvent("Ignored" + r.Message?.MessageId + " because it doesn't contain an id property. Body:" + body.Replace("\\\"", "'"),
                        properties: ToDictionary("queueItemId", queueItemId));
                }
            }
            catch (Exception ue)
            {
                var log = new LogEventInfo();
                log.Level = LogLevel.Error;
                log.Exception = ue;
                log.Message = "Inbound Queue Process Exception: " + ue.Message;
                log.Properties["queueItemId"] = queueItemId;
                log.Properties["messageId"] = r.Message.MessageId;
                log.Properties["subject"] = r.Message.Subject;
                log.Properties["commitId"] = commitId;

                if (r.Message.ApplicationProperties.ContainsKey("OwnerUserId"))
                {
                    var userId = Convert.ToInt32(r.Message.ApplicationProperties["OwnerUserId"]);
                    log.Properties["ownerUserId"] = userId;
                    log.Properties["userFullname"] = (await User.GetByIdAsync(userId))?.FullName;
                }

                var acq = DigitalDispatchActionQueueItem.GetById(queueItemId);
                if (acq != null)
                {
                    log.Properties["json"] = r.GetBody<string>();
                    if (acq.CompanyId != null)
                    {
                        log.Properties["companyId"] = acq.CompanyId;
                        log.Properties["companyName"] = (await Company.Company.GetByIdAsync(acq.CompanyId.Value))?.Name;
                    }
                    if (acq.AccountId != null)
                    {
                        log.Properties["accountId"] = acq.AccountId;
                    }
                }

                logger.Log(log);
            }
        }

        [Transaction]
        public async Task DigitalDispatchInProcess(long queueItemId, ProcessMessageEventArgs r)
        {
            if (r != null)
                NewRelic.Api.Agent.NewRelic.GetAgent().CurrentTransaction.AcceptDistributedTraceHeaders(r.Message, Getter, NewRelic.Api.Agent.TransportType.Queue);

            var qi = DigitalDispatchActionQueueItem.GetById(queueItemId);

            NewRelic.Api.Agent.NewRelic.SetTransactionName(nameof(DigitalDispatchInProcess), Enum.GetName(qi.Type));

            var json = (qi.JsonObject ?? r.Message.ApplicationProperties["jsonObject"].ToString());

            if (json.Contains("UrgentlyMessage"))
                json = json.Replace("\" H", "\\\" H")
                    .Replace("\" W", "\\\" W")
                    .Replace("\" L", "\\\" L");

            dynamic jsonObj = json.FromJson();

            if (r!= null && !r.Message.ApplicationProperties.ContainsKey("jsonObject"))
                r.Message.GetRawAmqpMessage().ApplicationProperties.Add("jsonObject", qi.JsonObject);

            if (qi.Type == DigitalDispatchActionQueueItemType.IncomingConnect ||
                qi.Type == DigitalDispatchActionQueueItemType.IncomingDisconnect)
            {
                await HandleIsscQueueIncomingMessage(qi, jsonObj, r);
                return;
            }

            if (qi.Type == DigitalDispatchActionQueueItemType.IncomingCallOfferedOutOfNetwork ||
                qi.Type == DigitalDispatchActionQueueItemType.IncomingCallAssignPONumberOutOfNetwork ||
                qi.Type == DigitalDispatchActionQueueItemType.IncomingCallCancelledOutOfNetwork ||
                qi.Type == DigitalDispatchActionQueueItemType.IncomingCallPaymentOutOfNetwork)
            {
                try
                {
                // XDANSMITH: verify
                    if ((jsonObj.providerId == "OUT_OF_NETWORK" ||
                         jsonObj.providerId.ToString().Contains("@towbook.net")) && jsonObj.honkMessageId != null)
                        r.Message.GetRawAmqpMessage().ApplicationProperties["masterAccountId"] = MasterAccountTypes.OonHonk;
                }
                catch
                {

                }

                await OutOfNetworkIncoming.HandleOutOfNetwork(qi, jsonObj, r);
                return;
            }

            if (qi.Type == DigitalDispatchActionQueueItemType.GatewayEvent)
            {
                await HandleGatewayMessage(qi, r);
                return;
            }

            var messageBody = r?.GetBody<string>() ?? qi.ToJson();
            if (string.IsNullOrWhiteSpace(messageBody))
                messageBody = qi.ToJson();

            var messageObj = JsonConvert.DeserializeObject<DigitalDispatchActionQueueItem>(messageBody);

            MasterAccount ma = null;
            if (r != null && r.Message.ApplicationProperties.ContainsKey("masterAccountId"))
                ma = await MasterAccount.GetByIdAsync(Convert.ToInt32(r.Message.ApplicationProperties["masterAccountId"]));
            else if (qi.AccountId != null)
                ma = await MasterAccount.GetByIdAsync(Account.GetById(qi.AccountId.Value).MasterAccountId);

            if (ma == null)
            {
                if (qi.AccountId != null)
                {
                    var acc = Account.GetByIdWithoutCache(qi.AccountId.Value);
                    if (acc?.MasterAccountId != null)
                    {
                        Cache.Instance.PartitionSet(acc);
                        Cache.Instance.Set(acc);
                    }

                    ma = await MasterAccount.GetByIdAsync(acc.MasterAccountId);
                }

                if (ma == null)
                    ma = await MasterAccount.GetByIdAsync(MasterAccountTypes.OonUrgently);
            }

            if (ma != null)
            {
                NewRelic.Api.Agent.NewRelic.GetAgent().CurrentTransaction
                    .AddCustomAttribute("mcDispatchProcessType", qi.Type)
                    .AddCustomAttribute("mcDispatchMasterAccount", ma.Id);

                switch (ma.Id)
                {
                    case MasterAccountTypes.AlliedDispatch:
                        if (TrxContractor.GetByAccountId(qi.AccountId.GetValueOrDefault()).Any())
                            await TrxService.HandleIncoming(ma.Id, qi, JsonConvert.DeserializeObject<TrxMessage>(qi.JsonObject), r);
                        else
                            await HandleIsscQueueIncomingMessage(qi, jsonObj, r);
                        break;

                    
                    case MasterAccountTypes.RoadsideProtect:
                        if (RoadsideProtectContractor.GetByAccountId(qi.AccountId.GetValueOrDefault()).Any())
                            await RoadsideProtectService.HandleIncoming(ma.Id, qi, JsonConvert.DeserializeObject<RoadsideProtectMessage>(qi.JsonObject), r);
                        else
                            await HandleIsscQueueIncomingMessage(qi, jsonObj, r);
                        break;

                    case MasterAccountTypes.OonRoadsideProtect:
                        await RoadsideProtectService.HandleIncoming(ma.Id, qi, JsonConvert.DeserializeObject<RoadsideProtectMessage>(qi.JsonObject), r);
                        break;

                    case MasterAccountTypes.Geico:
                    case MasterAccountTypes.Tesla:
                        await HandleIsscQueueIncomingMessage(qi, jsonObj, r);
                        break;

                    case MasterAccountTypes.Agero:
                        await HandleAgeroQueueIncomingMessage(qi, JsonConvert.DeserializeObject<AgeroMessage>(messageObj.JsonObject), r);
                        break;

                    case MasterAccountTypes.OonAllstate:
                    case MasterAccountTypes.Allstate:
                        await HandleAllstateIncomingMessage(qi, JsonConvert.DeserializeObject<AllstateMessage>(messageObj.JsonObject), r);
                        break;

                    case MasterAccountTypes.OonQuest:
                    case MasterAccountTypes.Quest:
                        await HandleQuestIncomingMessage(qi, JsonConvert.DeserializeObject<AllstateMessage>(messageObj.JsonObject), r);
                        break;

                    case MasterAccountTypes.Nsd:
                    case MasterAccountTypes.Pinnacle:
                    case MasterAccountTypes.Nac:
                        var am = JsonConvert.DeserializeObject<AllstateMessage>(messageObj.JsonObject);

                        if (ma.Id == MasterAccountTypes.Nac && am.AllstateMessageId == 0)
                        {
                            var nm = JsonConvert.DeserializeObject<NacMessage>(messageObj.JsonObject);

                            if (nm.NacMessageId > 0)
                            {
                                await NacService.HandleIncoming(MasterAccountTypes.Nac, qi, nm, r);
                                break;
                            }
                        }
                        else if (ma.Id == MasterAccountTypes.Nsd && am.AllstateMessageId == 0)
                        {
                            var nm = JsonConvert.DeserializeObject<NsdMessage>(messageObj.JsonObject);

                            if (nm.NsdMessageId > 0)
                            {
                                await NsdService.HandleIncoming(MasterAccountTypes.Nsd, qi, nm, r);
                                break;
                            }
                        }
                        await HandleDdxmlIncomingMessage(qi, am, r);
                        break;

                    case MasterAccountTypes.OonUrgently:
                    case MasterAccountTypes.Urgently:
                        await HandleUrgentlyIncomingMessage(qi, JsonConvert.DeserializeObject<UrgentlyMessage>(
                            messageObj.JsonObject.Replace("\" H", "\\\" H")
                                .Replace("\" W", "\\\" W")
                                .Replace("\" L", "\\\" L")), r);
                        break;

                    case MasterAccountTypes.Sykes:
                        await HandleSykesIncomingMessage(qi, JsonConvert.DeserializeObject<SykesMessage>(messageObj.JsonObject), r);
                        break;

                    case MasterAccountTypes.Gerber:
                        await HandleGerberIncomingMessage(qi, JsonConvert.DeserializeObject<Gerber.GerberMessage>(messageObj.JsonObject), r);
                        break;

                    case MasterAccountTypes.Fleetnet:
                    case MasterAccountTypes.Servicase:
                        await HandleFleetnetIncomingMessage(qi, JsonConvert.DeserializeObject<FleetnetMessage>(messageObj.JsonObject), r);
                        break;

                    case MasterAccountTypes.OonSwoop:
                    case MasterAccountTypes.Swoop:
                        await HandleSwoopIncomingMessage(
                            qi,
                            JsonConvert.DeserializeObject<Integrations.MotorClubs.Swoop.SwoopMessage>(messageObj.JsonObject),
                            r);
                        break;

                    case MasterAccountTypes.OonHonk:
                    case MasterAccountTypes.Honk:
                        await HandleHonkIncomingMessage(qi, JsonConvert.DeserializeObject<HonkMessage>(qi.JsonObject), r);
                        break;

                    case MasterAccountTypes.AaaAce:
                        await HandleAaaAceIncomingMessage(qi, JsonConvert.DeserializeObject<AaaMessage>(qi.JsonObject), r);
                        break;

                    case MasterAccountTypes.AaaAca:
                        await HandleAaaAcaIncomingMessage(qi, JsonConvert.DeserializeObject<AaaMessage>(qi.JsonObject), r);
                        break;

                    case MasterAccountTypes.AaaWashington:
                    case MasterAccountTypes.AaaNewYork:
                        await HandleAaaWaNyIncomingMessage(qi, JsonConvert.DeserializeObject<AaaMessage>(qi.JsonObject), r);
                        break;

                    case MasterAccountTypes.AaaAcg:
                        await HandleAaaAcgIncomingMessage(qi, JsonConvert.DeserializeObject<AaaMessage>(qi.JsonObject,
                            new JsonSerializerSettings()
                            {
                                MissingMemberHandling = MissingMemberHandling.Ignore,
                                NullValueHandling = NullValueHandling.Ignore
                            }), r);
                        break;

                    case MasterAccountTypes.AaaNortheast:
                        await HandleAaaNortheastIncomingMessage(qi, JsonConvert.DeserializeObject<AaaMessage>(qi.JsonObject), r);
                        break;

                    case MasterAccountTypes.AaaNationalFsl:
                        await MotorClubDispatchingServiceAaaNationalFsl.HandleAaaFslIncomingMessage(
                            qi, JsonConvert.DeserializeObject<AaaMessage>(qi.JsonObject), r);
                        break;

                    case MasterAccountTypes.AaaNational:
                        await HandleAaaNationalIncomingMessage(qi, JsonConvert.DeserializeObject<AaaMessage>(qi.JsonObject), r);
                        break;

                    case MasterAccountTypes.Bcaa:
                        await BcaaService.HandleBcaaIncomingMessage(qi, JsonConvert.DeserializeObject<AaaMessage>(qi.JsonObject), r);
                        break;

                    case MasterAccountTypes.StackThreeAtlas:
                    case MasterAccountTypes.DrivenSolutions:
                    case MasterAccountTypes.OonDrivenSolutions:
                        await StackThreeService.HandleIncoming(ma.Id, qi, JsonConvert.DeserializeObject<StackThreeMessage>(qi.JsonObject), r);
                        break;

                    default:
                        logger.LogEvent("Ignored " + r.Message.MessageId + " because it's MasterAccountId " + (ma.Id + ":" + ma.Name) + "doesn't have a DDService implementation written. AccountId is " + qi.AccountId.Value);
                        await r.CompleteAsync();
                        break;
                }

            }
            else
            {
                logger.LogEvent("Ignored " + r.Message.MessageId + " because it doesn't have an AccountId set.");

                await r.CompleteAsync();
            }
        }

        private async Task SubscribeToExpiredQueue()
        {
            var expiredOptions = new OnMessageOptions()
            {
                AutoCompleteMessages = false,
                MaxConcurrentCalls = 4,
                ReceiveMode = ServiceBusReceiveMode.PeekLock
            };

            var expiredRequestQConsumer = await ServiceBusHelper.CreateConsumerQueueAsync(DigitalDispatchService.ExpiredRequestsQueueName, expiredOptions);

            await expiredRequestQConsumer.OnMessageAsync(async (r) =>
            {
                logger.LogEvent("Handling {0}, {1}", null, LogLevel.Info, r.Message?.MessageId, r.Message?.Subject);

                NewRelic.Api.Agent.NewRelic.GetAgent().CurrentTransaction.AcceptDistributedTraceHeaders(r.Message, Getter, NewRelic.Api.Agent.TransportType.Queue);
                NewRelic.Api.Agent.NewRelic.SetTransactionName("ExpiredQueue", "Expired");

                if (!r.Message.ApplicationProperties.ContainsKey("callRequestId"))
                {

                    await r.CompleteAsync();
                }
                int crId = 0;

                if (!int.TryParse(r.Message.ApplicationProperties["callRequestId"].ToString(), out crId))
                {
                    await r.DeadLetterAsync("invalid callRequestId property - couldn't convert to int", "");
                    return;
                }

                var cr = CallRequest.GetById(crId);
                if (cr != null)
                {
                    if (cr.Status == CallRequestStatus.None)
                    {
                        if (!await cr.UpdateStatus(CallRequestStatus.Expired))
                        {
                            logger.Log(LogLevel.Warn, "{0}: Tried to update but the status update failed.", cr.CallRequestId);
                        }
                        else
                        {
                            logger.Log(LogLevel.Info, "{0}: Updated Status to Expired successfully.", cr.CallRequestId);

                            var ac2 = await Account.GetByIdAsync(cr.AccountId);
                            if (ac2 != null)
                            {
                                if (ac2.MasterAccountId == MasterAccountTypes.Towbook)
                                {
                                    int id = Convert.ToInt32(cr.PurchaseOrderNumber);

                                    var sourceCall = await Entry.GetByIdNoCacheAsync(id);

                                    if (sourceCall != null)
                                    {
                                        sourceCall.SetAttribute(Dispatch.AttributeValue.BUILTIN_SUBCONTRACTOR_RESPONSE_JSON,
                                            new SubcontractorResponseModel("expired", cr.CompanyId).ToJson());

                                        await sourceCall.Save();
                                        logger.Log(LogLevel.Info, "Towbook/Expired Towbook CallRequestId={0}/CaseId={1}", cr.CallRequestId, cr.PurchaseOrderNumber);

                                        var rotationMode = sourceCall.GetAttribute(Dispatch.AttributeValue.BUILTIN_SUBCONTRACTOR_ROTATION);
                                        if (rotationMode == "1")
                                        {
                                            var currentCompany = cr.CompanyId; ;

                                            if (sourceCall.CompanyId == 49039)
                                            {
                                                logger.Log(LogLevel.Info, $"Towbook/Expired Towbook CallRequestId={cr.CallRequestId}/CaseId={cr.PurchaseOrderNumber}...Not sending to next provider.");

                                                await r.CompleteAsync();
                                                return;
                                            }

                                            var nextUp = RotationStatusHandler.Pull(sourceCall.AccountId, sourceCall.BodyType?.Id ?? 1);
                                            var nextUpAccountId = nextUp.Rotation.SubcontractorAccountId;

                                            var ac = await Account.GetByIdAsync(nextUpAccountId);

                                            if (ac != null)
                                            {
                                                var companyAccount = ac.ReferenceNumber?.Split('|');
                                                int subCompanyId = 0;
                                                int subAccountId = 0;
                                                if (companyAccount != null && companyAccount.Length == 2)
                                                {
                                                    subCompanyId = Convert.ToInt32(companyAccount[0]);
                                                    subAccountId = Convert.ToInt32(companyAccount[1]);
                                                }

                                                var existing = CallRequest.GetByForeignId(subAccountId, sourceCall.Id.ToString());
                                                if (existing == null)
                                                {
                                                    // don't send it a second time
                                                    var destCr = new CallRequest()
                                                    {
                                                        CompanyId = subCompanyId,
                                                        AccountId = subAccountId,
                                                        PurchaseOrderNumber = sourceCall.Id.ToString(),
                                                        ExpirationDate = DateTime.Now.AddSeconds(91),
                                                        RequestDate = DateTime.Now,
                                                        ProviderId = sourceCall.CompanyId.ToString(),
                                                        Reason = sourceCall.Reason?.Name ?? "",
                                                        ServiceNeeded = sourceCall.Reason?.Name ?? "",
                                                        StartingLocation = sourceCall.TowSource,
                                                        TowDestination = sourceCall.TowDestination,
                                                        Vehicle = (sourceCall.Assets.FirstOrDefault()?.Year ?? sourceCall.Year) + " " + sourceCall.VehicleMake + " " + sourceCall.VehicleModel
                                                    };

                                                    await destCr.Save();
                                                    var re = destCr.Deliver().Result;

                                                    await SendExpiredRequestEvent(destCr.CallRequestId, destCr.ExpirationDate.Value);

                                                    sourceCall.SetAttribute(Dispatch.AttributeValue.BUILTIN_SUBCONTRACTOR_ID, nextUpAccountId.ToString());
                                                    await sourceCall.Save();

                                                    logger.Log(LogLevel.Info, "Towbook/Rotation/Expired: Sent to next company CallRequestId={0}/CaseId={1}," + nextUp.ToJson(), destCr.CallRequestId, destCr.PurchaseOrderNumber);
                                                }
                                                else
                                                {
                                                    logger.Log(LogLevel.Info, "Towbook/Rotation/Expired: Stopping rotation for this call; this provider was already tried once.  CallRequestId={0}/CaseId={1}," + nextUp.ToJson(), existing.CallRequestId, existing.PurchaseOrderNumber);
                                                }
                                            }
                                        }
                                        else if (rotationMode == "2")
                                        {
                                            // send to next nearest.

                                            var nearestJson = Core.GetRedisValue("dts_nearest:" + sourceCall.Id);
                                            if (nearestJson == null)
                                                throw new Exception("no dts_nearest" + sourceCall.Id);

                                            var distances = JsonConvert.DeserializeObject<List<SubcontractorDistanceModel>>(nearestJson);

                                            for (var i = 0; i < distances.Count; i++)
                                            {
                                                if (distances[i].CompanyId == cr.CompanyId)
                                                {
                                                    if (!(distances.Count > i))
                                                        break;

                                                    var next = distances[i + 1];

                                                    var existing = CallRequest.GetByForeignId(next.AccountId, sourceCall.Id.ToString());
                                                    if (existing == null)
                                                    {
                                                        // don't send it a second time
                                                        var destCr = new CallRequest()
                                                        {
                                                            CompanyId = next.CompanyId,
                                                            AccountId = next.AccountId,
                                                            PurchaseOrderNumber = sourceCall.Id.ToString(),
                                                            ExpirationDate = DateTime.Now.AddSeconds(91),
                                                            RequestDate = DateTime.Now,
                                                            ProviderId = sourceCall.CompanyId.ToString(),
                                                            Reason = sourceCall.Reason?.Name ?? "",
                                                            ServiceNeeded = sourceCall.Reason?.Name ?? "",
                                                            StartingLocation = sourceCall.TowSource,
                                                            TowDestination = sourceCall.TowDestination,
                                                            Vehicle = (sourceCall.Assets.FirstOrDefault()?.Year ?? sourceCall.Year) + " " + sourceCall.VehicleMake + " " + sourceCall.VehicleModel
                                                        };

                                                        await destCr.Save();
                                                        var re = destCr.Deliver().Result;

                                                        await SendExpiredRequestEvent(destCr.CallRequestId, destCr.ExpirationDate.Value);

                                                        sourceCall.SetAttribute(Dispatch.AttributeValue.BUILTIN_SUBCONTRACTOR_ID, next.SenderAccountId.ToString());

                                                        await sourceCall.Save();

                                                        logger.Log(LogLevel.Info, "Towbook/Rotation/Expired: Sent to next company CallRequestId={0}/CaseId={1}," +
                                                            next.ToJson(), destCr.CallRequestId, destCr.PurchaseOrderNumber);
                                                    }
                                                    else
                                                    {
                                                        logger.Log(LogLevel.Info, "Towbook/Rotation/Expired: Stopping rotation for this call; this provider was already tried once.  CallRequestId={0}/CaseId={1}," +
                                                            next.ToJson(), existing.CallRequestId, existing.PurchaseOrderNumber);
                                                    }

                                                    break;
                                                }

                                            }
                                        }
                                    }

                                }

                                if (ac2.MasterAccountId == MasterAccountTypes.AaaAcg)
                                {
                                    // send a decline 
                                    var ad = AaaDispatch.GetByCallRequestId(cr.CallRequestId);

                                    if (ad != null && ad.IsClosed)
                                    {
                                        if (r != null)
                                            await r.CompleteAsync();

                                        return;
                                    }

                                    var contractor = AaaContractor.GetById(ad.AaaContractorId);
                                    var client = AcgGetClient(contractor, new DigitalDispatchActionQueueItem()
                                    {
                                        CompanyId = cr.CompanyId,
                                        AccountId = cr.AccountId
                                    }, cr.CallRequestId, dispatchId: ad.DispatchId);

                                    var job = JsonConvert.DeserializeObject<SalesforceData<ScheduledJobPayload>>(ad.DispatchJson).Payload;

                                    if (cr.DispatchEntryId.GetValueOrDefault() == 0)
                                    {
                                        await client.AssignCall(new CallAssignmentPayload()
                                        {
                                            Status = "Spotted",
                                            CallStatus = StatusCodes.Decline,
                                            ServiceAppointmentId = job.ServiceAppointmentId,
                                            WorkOrderId = job.WorkOrderId,
                                            ServiceResourceId = job.ServiceResourceId,
                                            DeclineReason = "No response from facility"
                                        });
                                    }
                                    else
                                    {
                                        logger.Log(LogLevel.Info, "{0}: DispatchEntryId isn't set to null so we don't need to mark as expired. It's value is: {1} ",
                                            r.Message.MessageId,
                                            cr.DispatchEntryId);
                                    }
                                }

                                if (ac2.MasterAccountId == MasterAccountTypes.AaaNationalFsl)
                                {
                                    // send a decline 

                                    var ad = AaaDispatch.GetByCallRequestId(cr.CallRequestId);

                                    if (ad != null && ad.IsClosed)
                                    {
                                        if (r != null)
                                            await r.CompleteAsync();

                                        return;
                                    }

                                    var contractor = AaaContractor.GetById(ad.AaaContractorId);
                                    var client = MotorClubDispatchingServiceAaaNationalFsl.AcgGetClient(contractor, new DigitalDispatchActionQueueItem()
                                    {
                                        CompanyId = cr.CompanyId,
                                        AccountId = cr.AccountId
                                    }, cr.CallRequestId, dispatchId: ad.DispatchId);

                                    var job = JsonConvert.DeserializeObject<SalesforceData<ScheduledJobPayload>>(ad.DispatchJson).Payload;

                                    if (cr.DispatchEntryId.GetValueOrDefault() == 0)
                                    {
                                        await client.AssignCall(new CallAssignmentPayload()
                                        {
                                            Status = "Spotted",
                                            CallStatus = StatusCodes.Decline,
                                            ServiceAppointmentId = job.ServiceAppointmentId,
                                            WorkOrderId = job.WorkOrderId,
                                            ServiceResourceId = job.ServiceResourceId,
                                            DeclineReason = "No response from facility"
                                        });
                                    }
                                    else
                                    {
                                        logger.Log(LogLevel.Info, "{0}: DispatchEntryId isn't set to null so we don't need to mark as expired. It's value is: {1} ",
                                            r.Message.MessageId,
                                            cr.DispatchEntryId);
                                    }
                                }

                                if (ac2.MasterAccountId == MasterAccountTypes.AaaWashington ||
                                    ac2.MasterAccountId == MasterAccountTypes.AaaNewYork)
                                {
                                    // send a decline 
                                    var ad = AaaDispatch.GetByCallRequestId(cr.CallRequestId);

                                    if (ad != null && ad.IsClosed)
                                    {
                                        if (r != null)
                                            await r.CompleteAsync();

                                        return;
                                    }

                                    var contractor = AaaContractor.GetById(ad.AaaContractorId);
                                    var client = GetWaNyClient(contractor, new DigitalDispatchActionQueueItem()
                                    {
                                        CompanyId = cr.CompanyId,
                                        AccountId = cr.AccountId
                                    }, cr.CallRequestId, dispatchId: ad.DispatchId);

                                    if (cr.DispatchEntryId.GetValueOrDefault() == 0)
                                    {
                                        await client.RefuseAsync(new DispatchRefuseModel()
                                        {
                                            FacilityId = contractor.ContractorId,
                                            CallKey = ad.DispatchId,
                                            LocationId = contractor.LocationCode,
                                            ReasonId = 255,
                                            ReasonName = "Timeout"
                                        });
                                    }
                                    else
                                    {
                                        logger.Log(LogLevel.Info, "{0}: DispatchEntryId isn't set to null so we don't need to mark as expired. It's value is: {1} ",
                                            r.Message.MessageId,
                                            cr.DispatchEntryId);
                                    }
                                }

                                if (ac2.MasterAccountId == MasterAccountTypes.Bcaa)
                                {
                                    // send a decline 
                                    var ad = AaaDispatch.GetByCallRequestId(cr.CallRequestId);

                                    if (ad != null && ad.IsClosed)
                                    {
                                        if (r != null)
                                            await r.CompleteAsync();

                                        return;
                                    }

                                    var contractor = AaaContractor.GetById(ad.AaaContractorId);
                                    var client = BcaaService.GetClient(contractor, new DigitalDispatchActionQueueItem()
                                    {
                                        CompanyId = cr.CompanyId,
                                        AccountId = cr.AccountId
                                    }, cr.CallRequestId, dispatchId: ad.DispatchId);

                                    if (cr.DispatchEntryId.GetValueOrDefault() == 0)
                                    {
                                        client.Refuse(new Integrations.MotorClubs.Aaa.Bcaa.BcaaRestClient.DispatchRefuseModel()
                                        { 
                                            ContractorId = contractor.ContractorId,
                                            DispatchId = ad.DispatchId,
                                            ReasonId = 255,
                                            ReasonName = "Timeout"
                                        });
                                    }
                                    else
                                    {
                                        logger.Log(LogLevel.Info, "{0}: DispatchEntryId isn't set to null so we don't need to mark as expired. It's value is: {1} ",
                                            r.Message.MessageId,
                                            cr.DispatchEntryId);
                                    }
                                }
                            }
                        }
                    }
                    else
                    {
                        logger.Log(LogLevel.Info, "{0}: Status isn't set to None so we don't need to mark as expired. It's value is: {1} ",
                            r.Message.MessageId,
                            cr.Status);
                    }
                }

                await r.CompleteAsync();
            }, async (e) => await DigitalDispatchSync_ExceptionReceived(e));

            consumers.Add(DigitalDispatchService.ExpiredRequestsQueueName, expiredRequestQConsumer);
        }

        public sealed class SubcontractorDistanceModel
        {
            public int SenderAccountId { get; set; }
            public int CompanyId { get; set; }
            public int AccountId { get; set; }
            public decimal Distance { get; set; }
        }

        // TODO: duplicated from CallsController. move to shaerd library.
        private async Task SubcontractorAutoCancelQueue()
        {
            var outgoingOptions = new OnMessageOptions()
            {
                AutoCompleteMessages = false,
                MaxConcurrentCalls = 4,
                ReceiveMode = ServiceBusReceiveMode.PeekLock
            };

            var oq = await ServiceBusHelper.CreateConsumerQueueAsync("autocancel", outgoingOptions);

            await oq.OnMessageAsync(async (r) =>
            {
                NewRelic.Api.Agent.NewRelic.GetAgent().CurrentTransaction.AcceptDistributedTraceHeaders(r.Message, Getter, NewRelic.Api.Agent.TransportType.Queue);
                NewRelic.Api.Agent.NewRelic.SetTransactionName("DispatchToSubcontractor", "AutoCancel");

                logger.LogEvent("Handling {0}, {1}", null, LogLevel.Info, r.Message.MessageId, r.Message.Subject);
                var body = r.GetBody<string>();

                var acm = JsonConvert.DeserializeObject<AutoCancelModel>(body);

                logger.LogEvent("Handling AutoCancel {0}, {1}, {2}", null, LogLevel.Info, r.Message.MessageId, r.Message.Subject, body);

                var en = Entry.GetByIdNoCache(acm.CallId);
                if (en != null)
                {
                    var block = false;
                    if (en.Status.Id < Status.AtSite.Id)
                    {
                        // make sure they aren't the assigned sub contractor. if they are, don't cancel it. 
                        var cr = await CallRequest.GetByDispatchEntryId(en.Id);
                        if (cr != null)
                        {
                            var sourceCall = await Entry.GetByIdAsync(Convert.ToInt32(cr.PurchaseOrderNumber));
                            if (sourceCall != null && sourceCall.GetAttribute(Dispatch.AttributeValue.BUILTIN_SUBCONTRACTOR_ID) == acm.CompanyId.ToString())
                                block = true;
                        }

                        if (!block)
                            await en.Cancel("ETA Expired before status was marked On Scene. Automatically cancelled.",
                            new AuthenticationToken() { UserId = 1 });
                    }
                }

                await r.CompleteAsync();
            }, async (e) => await DigitalDispatchSync_ExceptionReceived(e));
        }

        #region Incoming Handlers

        private async Task<bool> HandleIsscQueueIncomingMessage(DigitalDispatchActionQueueItem qi, dynamic jsonObj, ProcessMessageEventArgs sourceMessage)
        {
            var conn = IsscConnection.Get(IsscConfig.GetById(Convert.ToInt32(sourceMessage.Message.ApplicationProperties["IsscConfigId"])));
            var conf = IsscConfig.GetById(conn.IsscConfigId);

            logger.LogEvent("Processing request for ISSC Id = {0}, Type = {1}", qi.CompanyId, LogLevel.Info,
                properties: ToDictionary("queueItemId", qi.QueueItemId),
                qi.QueueItemId, qi.Type);

            if (qi.CompanyId == 155249 && 
                qi.Type != DigitalDispatchActionQueueItemType.IncomingCallReceived)
            {
                return await GatewayForwardInboundIssc(qi, sourceMessage);
            }

            switch (qi.Type)
            {
                #region Disconnect Event
                case DigitalDispatchActionQueueItemType.IncomingDisconnect:
                    IsscConfig cfg = IsscConfig.GetById(Convert.ToInt32(sourceMessage.Message.ApplicationProperties["IsscConfigId"]));

                    if (cfg.AutoReconnectOnDisconnectEvent)
                    {
                        logger.LogEvent("{0}: Configured to auto reconnect or configId: {1}", null, LogLevel.Info, qi.QueueItemId, cfg.IsscConfigId);
                        await DigitalDispatchService.NotifyConnectEvent(qi.CompanyId, qi.JsonObject, DateTime.Now.AddSeconds(30),
                            properties: new Dictionary<string, object> { { "IsscConfigId", conn.IsscConfigId } });
                    }
                    else
                    {
                        logger.LogEvent("{0}: *NOT* Configured to auto reconnect or configId: {1}...{2}", null, LogLevel.Fatal, qi.QueueItemId, cfg.IsscConfigId, cfg.ToJson(null));
                    }

                    break;
                #endregion

                #region Connect Event
                case DigitalDispatchActionQueueItemType.IncomingConnect:
                    IsscProvider.UpdateLoggedInProvidersToLoggedOut(conf);
                    logger.Log(LogLevel.Info, "Set all ISSC Providers to Logged out due to IncomingConnect event. Config: {0}", conf.IsscConfigId);

                    IEnumerable<IsscProvider> loggedInProviders = IsscProvider.GetLogged(conf).Where(o => o.ConfigId == conf.IsscConfigId);

                    List<IsscProvider> batchRequest = new List<IsscProvider>();
                    var config = IsscConfig.GetById(loggedInProviders.FirstOrDefault()?.ConfigId ?? 1);
                    int i = 0;
                    foreach (var lp in loggedInProviders)
                    {
                        i++;
                        if (lp.Token != null)
                        {
                            logger.Log(LogLevel.Info, "Requesting login event for {0}/{1}/{2}", lp.CompanyId, lp.ClientId, lp.ContractorId);

                            /*
                            if (lp.LoginStatus != IsscProviderLoginStatus.LoggedIn && lp.LoginStatus != IsscProviderLoginStatus.LoggingIn)
                            {
                                try
                                {
                                    await DigitalDispatchService.RequestInternalEvent(lp.CompanyId,
                                        lp.AccountId,
                                        lp.ToJson(),
                                        DigitalDispatchService.InternalEventType.Login,
                                        DateTime.Now.AddSeconds(90).ToUniversalTime().AddMilliseconds(i * 30), // randomize the time a little bit so that its not ALL at once.
                                        properties: new Dictionary<string, object> { { "IsscConfigId", config.IsscConfigId } });

                                }
                                catch
                                {
                                    // todo: log failure.
                                }

                                try
                                {
                                    batchRequest.Add(lp);
                                }
                                catch
                                {
                                    // todo: log failure
                                }
                            }*/
                        }
                    }

                    try
                    {
                        logger.Log(LogLevel.Info, "Sending batch request for {0}", batchRequest.Count);
                        new IsscRestClient(config).Login(batchRequest.ToArray());
                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    }
                    catch (Exception e)
                    {
                        logger.Log(LogLevel.Fatal, "Error while sending batch login request {0}", e.ToString());
                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                    }

                    break;

                #endregion

                #region Login/Logout Events
                case DigitalDispatchActionQueueItemType.IncomingLogin:
                case DigitalDispatchActionQueueItemType.IncomingLogout:
                    string contractorId = jsonObj.ContractorID.ToString();
                    string clientId = jsonObj.ClientID.ToString();
                    string locationId = null;

                    if (jsonObj.LocationID != null)
                        locationId = jsonObj.LocationID.ToString();

                    IsscProvider provider = IsscProvider.GetByContractorId(contractorId, clientId, locationId);

                    if (provider == null)
                        throw new MotorClubException("There is no matching provider for the given clientId.");

                    if (jsonObj.Result == "Failure" &&
                        qi.Type == DigitalDispatchActionQueueItemType.IncomingLogin &&
                        jsonObj.Description != "Already logged in.")
                    {
                        logger.Log(LogLevel.Error, "ISSC/Incoming/{0}/{1}: {2}", qi.QueueItemId, qi.Type, qi.JsonObject);

                        provider.UpdateLoginStatus(IsscProviderLoginStatus.LoggedOut);

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                        break;
                    }
                    else if (jsonObj.Description == "Already logged in.")
                    {
                        provider.UpdateLoginStatus(IsscProviderLoginStatus.LoggedIn);

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                        break;
                    }

                    if (qi.Type == DigitalDispatchActionQueueItemType.IncomingLogin)
                    {
                        provider.LastLoginDate = DateTime.Now;
                        provider.Save();
                    }
                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    break;
                #endregion

                #region Incoming Call Offer (both received and offer) Event
                case DigitalDispatchActionQueueItemType.IncomingCallReceived:
                    logger.Log(LogLevel.Trace, "New call request Id = {0}, Type = {1}, Content = {2}", qi.QueueItemId, qi.Type, qi.JsonObject);

                    CallRequest ccr = await CreateCallRequest(jsonObj, MotorClubName.Geico, true);
                    qi.CallRequestId = ccr.CallRequestId;
                    DigitalDispatchService.LogAction(qi);
                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    logger.Log(LogLevel.Info, "New call request created for QueueItemId = {0}, Type = {1}, Content = {2}", qi.QueueItemId, qi.Type, ccr.CallRequestId);
                    break;

                case DigitalDispatchActionQueueItemType.IncomingCallOffered:

                    logger.Log(LogLevel.Trace, "New Call Offer Id = {0}, Type = {1}, Content = {2}", qi.QueueItemId, qi.Type, qi.JsonObject);

                    CallRequest ccr2 = await CreateCallRequest(jsonObj, MotorClubName.Geico, true);
                    qi.CallRequestId = ccr2.CallRequestId;
                    DigitalDispatchService.LogAction(qi);
                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    logger.Log(LogLevel.Info, "New call Offer Request created for QueueItemId = {0}, Type = {1}, Content = {2}", qi.QueueItemId, qi.Type, ccr2.CallRequestId);
                    break;

                #endregion

                #region Incoming Call Accepted Event
                case DigitalDispatchActionQueueItemType.IncomingCallAccepted:
                    IsscDispatch gd = IsscDispatch.GetByDispatchId(jsonObj.DispatchID.ToString());
                    CallRequest cr = CallRequest.GetById(gd.CallRequestId);

                    await IsscCreateCall(qi, jsonObj, sourceMessage, gd, cr);

                    qi.CallRequestId = cr.CallRequestId;
                    DigitalDispatchService.LogAction(qi);
                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    break;
                #endregion


                #region Incoming Call Accepted Event
                case DigitalDispatchActionQueueItemType.IncomingCallTranscript:
                    var cct = await CreateCallRequest(jsonObj, MotorClubName.Geico, false);

                    IsscDispatch gd2 = IsscDispatch.GetByDispatchId(jsonObj.DispatchID.ToString());
                    CallRequest cr2 = CallRequest.GetById(gd2.CallRequestId);
                    if (cr2.OwnerUserId.GetValueOrDefault(0) == 0)
                    {
                        // TODO: find out why we have to do this.. 
                        // when its a dispatch.transcript event... owneruserid is 0 for some reason.
                        cr2.OwnerUserId = 1;
                        await cr2.Save();
                    }
                    await IsscCreateCall(qi, jsonObj, sourceMessage, gd2, cr2);

                    qi.CallRequestId = cr2.CallRequestId;
                    DigitalDispatchService.LogAction(qi);
                    DigitalDispatchActionQueueItem.UpdateStatus(qi,
                        DigitalDispatchActionQueueItemStatus.Completed);
                    break;
                #endregion

                #region Incoming call Rejected, Cancelled, or Expired events
                case DigitalDispatchActionQueueItemType.IncomingCallRejected:
                case DigitalDispatchActionQueueItemType.IncomingCallCancelled:
                case DigitalDispatchActionQueueItemType.IncomingCallExpired:
                    IsscDispatch isscReject = IsscDispatch.GetByDispatchId(jsonObj.DispatchID.ToString());
                    if (isscReject == null)
                    {
                        await sourceMessage.CompleteAsync();

                        // ("Couldn't locate IsscDispatch object", "DispatchID doesn't exist: " + jsonObj.DispatchID);
                        logger.LogEvent("{0}: Couldn't process event for {2} event due to IsscDispatch being null. Body = {1}",
                            qi.CompanyId, LogLevel.Error, sourceMessage.Message.MessageId, qi.ToJson(), qi.Type);

                        //DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);

                        // Sometimes events come to the bus out-of-order - if it happens, delay the event by 10 seconds and retry it.
                        if (!sourceMessage.Message.ApplicationProperties.ContainsKey("isRetry"))
                        {
//                            var newMessage = sourceMessage.Clone();
                            var newMessage = new BrokeredMessage(sourceMessage.GetBody<string>());
                            foreach (var xy in sourceMessage.Message.ApplicationProperties)
                            {
                                newMessage.ApplicationProperties[xy.Key] = xy.Value;
                            }

                            newMessage.ApplicationProperties["isRetry"] = 1;
                            newMessage.ScheduledEnqueueTime = DateTime.UtcNow.AddSeconds(10);

                            await ServiceBusHelper.SendMessageAsync(DigitalDispatchService.IncomingQueueName, newMessage, false);
                        }

                        return false;
                    }

                    CallRequest isscRequest = CallRequest.GetById(isscReject.CallRequestId);

                    CallRequestStatus finalStatus = CallRequestStatus.Rejected;
                    if (qi.Type == DigitalDispatchActionQueueItemType.IncomingCallCancelled)
                        finalStatus = CallRequestStatus.Cancelled;
                    else if (qi.Type == DigitalDispatchActionQueueItemType.IncomingCallExpired)
                        finalStatus = CallRequestStatus.Expired;
                    else if (qi.Type == DigitalDispatchActionQueueItemType.IncomingCallRejected)
                        finalStatus = CallRequestStatus.RejectedByMotorClub;

                    if (isscRequest.DispatchEntryId.GetValueOrDefault() > 0)
                    {
                        if (finalStatus == CallRequestStatus.Cancelled)
                        {
                            var e = Entry.GetByIdNoCache(isscRequest.DispatchEntryId.Value);
                            if (e != null)
                            {
                                string reason = "";

                                if (jsonObj.Reason != null)
                                    reason = jsonObj.Reason;

                                int userId = 1;

                                if (e.Account.MasterAccountId == MasterAccountTypes.Geico)
                                    userId = 3; // external_geico user account

                                if (userId == 3 &&
                                    e.Notes != null &&
                                    (e.Notes.Contains("GOA Request Digitally Sent") ||
                                     e.Notes.Contains("digitally approved the GOA request.")))
                                {
                                    logger.Info(MasterAccountTypes.Geico,
                                        "CallCancelled",
                                        "GOA Workaround Hotfix - do not cancel.", isscReject.ContractorId,
                                        isscReject.LocationId,
                                        isscReject.DispatchId.ToString(),
                                        isscRequest.CompanyId,
                                        new
                                        {
                                            callRequestId = isscRequest.CallRequestId,
                                            callId = isscRequest.DispatchEntryId,
                                            queueItemId = qi.QueueItemId,
                                            reason = reason
                                        });
                                }
                                else
                                {
                                    if (ShouldAllowCancel(e))
                                    {
                                        await e.Cancel("Motor Club Cancelled" + (!string.IsNullOrWhiteSpace(reason) ? ": " + reason : ""),
                                            new AuthenticationToken() { UserId = userId }, "127.0.0.1");

                                        logger.Info(e.Account.MasterAccountId,
                                            "CallCancelled", "Motor Club Cancelled Call", isscReject.ContractorId,
                                            isscReject.LocationId,
                                            isscReject.DispatchId,
                                            isscRequest.CompanyId, new
                                            {
                                                callRequestId = isscRequest.CallRequestId,
                                                callId = isscRequest.DispatchEntryId,
                                                queueItemId = qi.QueueItemId,
                                                reason = reason,
                                                currentCallReason = e.Reason.NameMatchable()
                                            });
                                    }
                                }
                            }
                        }
                    }

                    await isscRequest.UpdateStatus(finalStatus);

                    qi.CallRequestId = isscRequest.CallRequestId;
                    DigitalDispatchService.LogAction(qi);

                    await PushNotificationProvider.UpdateCallRequestStatus(isscRequest.CompanyId, isscRequest.CallRequestId, finalStatus);
                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    break;
                #endregion



                case DigitalDispatchActionQueueItemType.IncomingChannelResponse:
                    // handle GOA and Cancel responses.
                    var chanResponse = JsonConvert.DeserializeObject<dynamic>(qi.JsonObject);

                    var tranInfo = Core.GetRedisValue("issc_channel:" + chanResponse.MessageKey.ToString());

                    if (tranInfo != null)
                    {
                        int callRequestId = (int)JsonConvert.DeserializeObject<dynamic>(tranInfo).callRequestId;

                        string type = (string)JsonConvert.DeserializeObject<dynamic>(tranInfo).type;

                        var callRequest = await CallRequest.GetByIdAsync(callRequestId);
                        if (callRequest.DispatchEntryId != null)
                        {
                            var e = await Entry.GetByIdNoCacheAsync(callRequest.DispatchEntryId.Value);
                            var ma = await MasterAccount.GetByIdAsync(e.Account.MasterAccountId);

                            if (type == "ExtendETA")
                            {
                                int eta = (int)JsonConvert.DeserializeObject<dynamic>(tranInfo).eta;
                                // it was already increased in CallsController.ExtendEta(id,model)
                                //e.ArrivalETA = e.ArrivalETA.Value.AddMinutes(eta);
                            }

                            if (ma != null)
                            {
                                if (chanResponse.MessageText != null)
                                {
                                    e.Notes = ma.Name + " digitally " + chanResponse.MessageText.ToString().ToLowerInvariant() + " the "
                                        + type + " request.\n" + e.Notes;
                                }
                                else if (chanResponse.Message != null)
                                {
                                    //v2 
                                    if (chanResponse.Message.Disposition != null)
                                    {

                                        e.Notes = ma.Name + " digitally " + chanResponse.Message.Disposition.ToString().ToLowerInvariant() +
                                            " the " + type + " request.\n" + e.Notes;
                                    }
                                }
                            }
                            await e.Save();
                        }
                        // cleanup
                        Core.DeleteRedisKey("issc_channel:" + chanResponse.MessageKey.ToString());

                        qi.CallRequestId = callRequestId;
                        if (chanResponse.MessageText.ToString() == type)
                        {
                            if (type == "ExtendETA")
                                qi.Type = DigitalDispatchActionQueueItemType.IncomingExtendEtaResponse;
                            else if (type == "GOANeeded")
                                qi.Type = DigitalDispatchActionQueueItemType.IncomingCallGoaResponse;
                            else if (type == "ProviderCancel" || type == "CustomerCancel")
                                qi.Type = DigitalDispatchActionQueueItemType.IncomingCallCancelled;
                            else if (type == "ServiceFailed")
                                qi.Type = DigitalDispatchActionQueueItemType.IncomingCallServiceFailureResponse;
                            else if (type == "AdditionalService")
                                qi.Type = DigitalDispatchActionQueueItemType.IncomingRequestAdditionalServiceResponse;
                        }
                        DigitalDispatchService.LogAction(qi);
                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                        logger.Log(LogLevel.Info, "ISSC/IncomingChannelResponse processed successfully for QueueItemId = {0}, Type = {1}, Content = {2}", qi.QueueItemId, qi.Type, callRequest.CallRequestId);
                    }
                    else
                    {
                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                    }
                    break;

                default:
                    await sourceMessage.DeadLetterAsync();

                    logger.LogEvent("Queue item has a type of {2} set that we don't have an implementation written for... MessageId {0}, Body = {1}",
                        qi.CompanyId, LogLevel.Error, sourceMessage.Message.MessageId, qi.ToJson(), qi.Type);
                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                    return false;
            }; ;

            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);

            await sourceMessage.CompleteAsync();
            return true;
        }

        [CacheKey("userSession")]
        [Serializable]
        [ProtoContract(ImplicitFields = ImplicitFields.AllFields, Name = "UserSession")]
        public class WebSession
        {
            public DateTime Expires { get; set; }
            public int UserId { get; set; }
            public int UserTypeId { get; set; }
            public int CompanyId { get; set; }
            public DateTime LastVerified { get; set; }
        }


        private static async Task<Entry> IsscCreateCall(
            DigitalDispatchActionQueueItem qi, 
            dynamic jsonObj, 
            ProcessMessageEventArgs sourceMessage,
            IsscDispatch gd,
            CallRequest cr)
        {
            var detailedDispatch = JsonConvert.DeserializeObject<DispatchNewEvent>(gd.CallJson);

            if (qi.Type == DigitalDispatchActionQueueItemType.InternalAcceptMissedCall)
            {
                if (jsonObj.ETA != null)
                    gd.ETA = Convert.ToInt32(jsonObj.ETA);

                if (jsonObj.PurchaseOrderNumber != null)
                    gd.AuthorizationNumber = jsonObj.PurchaseOrderNumber.ToString();
                else
                    gd.AuthorizationNumber = "MANUALLY_ACCEPTED_" + DateTime.Now.ToShortTowbookTimeString().Replace(" ", "");

                gd.Save();
            }
            else
            {
                gd.AuthorizationNumber = jsonObj.AuthorizationNumber.ToString();
                gd.Save();
            }

            Entry e = null;
            e = await Entry.GetByPurchaseOrderNumberAsync(cr.AccountId, gd.AuthorizationNumber);
            if (e == null)
            {
                e = new Entry();
                e.CompanyId = cr.CompanyId;
                e.AccountId = cr.AccountId;

                if (e.Account?.DefaultPriority == 1)
                    e.Priority = Entry.EntryPriority.High;
            }
            else
            {
                logger.LogEvent("{0}: Found existing towbook call for PO {1}/Dispatch Number {2}... Call #{3}... Update this one instead of ",
                    qi.CompanyId, LogLevel.Warn, sourceMessage.Message.MessageId, gd.AuthorizationNumber, gd.DispatchId, e.CallNumber);
            }

            e.PurchaseOrderNumber = gd.AuthorizationNumber;

            #region #CustomerWorkaround to send Service Calls for Quality Towing (4817) to a separate companyId 
            if (e.CompanyId == 4817)
            {
                if (String.IsNullOrWhiteSpace(cr.TowDestination))
                    e.CompanyId = 5406;
            }/*
            else if (e.CompanyId == 7992)
            {
                if (String.IsNullOrWhiteSpace(cr.TowDestination))
                    e.CompanyId = 9732;
            }*/
            #endregion


            #region Handle MoveRoadsideCallsToAccountId to move roadside calls to their own AccountId 

            if (string.IsNullOrWhiteSpace(cr.TowDestination))
            {
                var akv = AccountKeyValue.GetByAccount(e.CompanyId,
                    e.AccountId,
                    Provider.Towbook.ProviderId,
                    "MoveRoadsideCallsToAccountId"
                    ).FirstOrDefault();
                if (akv != null)
                {
                    int tempAccId = 0;
                    if (int.TryParse(akv.Value, out tempAccId))
                    {
                        var tempAcc = await Account.GetByIdAsync(tempAccId);
                        if (tempAcc != null)
                        {
                            e.AccountId = tempAcc.Id;
                        }
                    }
                }
            }
            #endregion

            #region Asset
            EntryAsset asset = null;

            if (e.Assets != null)
                asset = e.Assets.FirstOrDefault();

            if (asset == null)
                asset = new EntryAsset() { BodyTypeId = 1 };

            asset.Year = (!String.IsNullOrWhiteSpace(detailedDispatch.Vehicle.Year) ? Convert.ToInt32(detailedDispatch.Vehicle.Year) : 0);
            asset.Vin = detailedDispatch.Vehicle.VIN;



            asset.Make = GetManufacturerByName(detailedDispatch.Vehicle.Make);
            asset.Model = GetModelByName(detailedDispatch.Vehicle.Model);
            asset.ColorId = GetColorIdByName(detailedDispatch.Vehicle.Color);

            asset.LicenseNumber = detailedDispatch.Vehicle.License;
            asset.LicenseState = detailedDispatch.Vehicle.LicenseState;


            #endregion

            if (e.Notes == null)
                e.Notes = "";

            string startingLocation = string.Format("{0}, {1}, {2}, {3} {4}",
                detailedDispatch.Incident.Address1,
                detailedDispatch.Incident.Address2,
                detailedDispatch.Incident.City,
                detailedDispatch.Incident.State,
                detailedDispatch.Incident.Zip).Replace(", , ", ", ");

            if (!string.IsNullOrWhiteSpace(detailedDispatch.Incident.CrossStreet1))
                e.Notes += "Pickup Address Cross Street: " + detailedDispatch.Incident.CrossStreet1 + "\n";

            if (detailedDispatch.Destination != null &&
                !string.IsNullOrWhiteSpace(detailedDispatch.Destination.LocationInfo))
                e.Notes += "Destination Location Info:" + detailedDispatch.Destination.LocationInfo;

            var pickup = e.Waypoints.FirstOrDefault(o => o.Title == "Pickup");
            var dest = e.Waypoints.FirstOrDefault(o => o.Title == "Destination");

            e.Notes = "";

            if (!string.IsNullOrWhiteSpace(startingLocation))
            {
                e.TowSource = startingLocation;
                if (pickup == null)
                {
                    pickup = new EntryWaypoint() { Title = "Pickup", Position = 1 };
                    e.Waypoints.Add(pickup);
                }

                pickup = detailedDispatch.Incident.ToWaypoint(pickup);
            }

            if (!string.IsNullOrWhiteSpace(cr.TowDestination))
            {
                e.TowDestination = detailedDispatch.Destination.ToString();
                if (dest == null && !string.IsNullOrWhiteSpace(e.TowDestination))
                {
                    dest = new EntryWaypoint() { Title = "Destination", Position = 2 };
                    e.Waypoints.Add(dest);
                }

                dest = detailedDispatch.Destination.ToWaypoint(dest);
            }

            if (e.CreateDate == DateTime.MinValue)
                e.CreateDate = cr.RequestDate;

            if (gd.ETA != null)
                e.ArrivalETA = DateTime.Now.AddMinutes(gd.ETA.GetValueOrDefault(0));


            if (detailedDispatch.Job.RequestedFutureDateTime != null)
            {
                e.Notes = "** Scheduled Date: " + Core.OffsetDateTime(e.Company, detailedDispatch.Job.RequestedFutureDateTime.Value.ToLocalTime()) + "**\n";

                e.ArrivalETA = detailedDispatch.Job.RequestedFutureDateTime.Value.ToLocalTime();

            }

            if (gd.ClientId == "TSLA" &&
                (e.CompanyId == 4185 ||
                e.CompanyId == 4189 ||
                e.CompanyId == 4194))
            {

                if (!string.IsNullOrWhiteSpace(detailedDispatch.Job.JobDescription))
                    e.SetAttribute(Dispatch.AttributeValue.BUILTIN_BILLING_NOTES,
                         detailedDispatch.Job.JobDescription);
            }
            else
            {
                if (!string.IsNullOrWhiteSpace(detailedDispatch.Job.JobDescription))
                    e.Notes += detailedDispatch.Job.JobDescription + "\n";
            }
            e.ReasonId = await ReasonHelper.DetermineReasonId(e.Account.MasterAccountId, qi.CompanyId.Value, cr.ServiceNeeded);
            if (e.ReasonId == 1635)
                e.Notes += "Service Needed: " + cr.ServiceNeeded + "\n";

            if (!string.IsNullOrWhiteSpace(detailedDispatch.Vehicle.AdditionalInfo))
                e.Notes += "Additional Info: " + detailedDispatch.Vehicle.AdditionalInfo + "\n";

            if (!string.IsNullOrWhiteSpace(detailedDispatch.Vehicle.FuelType))
                e.Notes += "Fuel Type: " + detailedDispatch.Vehicle.FuelType + "\n";

            if (!string.IsNullOrWhiteSpace(detailedDispatch.Coverage?.CoveredServices))
                e.Notes += "Coverage Info: " + detailedDispatch.Coverage.CoveredServices + "\n";

            if (!string.IsNullOrWhiteSpace(detailedDispatch.Job?.ServiceComments))
                e.Notes += "Service Comments: " + detailedDispatch.Job.ServiceComments + "\n";

            if (!string.IsNullOrWhiteSpace(detailedDispatch.Incident?.Comments))
                e.Notes += "Incident Comments: " + detailedDispatch.Incident.Comments + "\n";

            if (detailedDispatch.Job.ServiceQuestions != null)
            {
                foreach (var x in detailedDispatch.Job.ServiceQuestions)
                {
                    e.Notes += (x.Question + ":").Replace("?:", ":") + " " +
                        String.Join(", ", x.Responses) + "\r\n";
                }
            }
            AttachDriveTypeToCallNotesFromVin(e, asset);

            #region DefaultTaxRate
            if (!e.Attributes.ContainsKey(Dispatch.AttributeValue.BUILTIN_TAXRATE_OVERRIDE))
            {
                var defaultTaxRateId = CompanyKeyValue.GetByCompanyId(e.CompanyId, Provider.Towbook.ProviderId, "DefaultTaxRateId").FirstOrDefault();

                if (defaultTaxRateId != null)
                    e.SetAttribute(Dispatch.AttributeValue.BUILTIN_TAXRATE_OVERRIDE, defaultTaxRateId.Value);
            }
            #endregion

            if (jsonObj.Payment != null)
            {
                var payment = $"CCN: {jsonObj.Payment?.Number}, Exp: {jsonObj.Payment.Expiration}, " +
                    $"CCV: {jsonObj.Payment.Verification}, Billing Zip: {jsonObj.Payment?.ZipCode}, " +
                    $"Amount: {jsonObj.Payment?.Amount?.ToString("C")} ";

                e.SetAttribute(Dispatch.AttributeValue.BUILTIN_BILLING_NOTES, payment);
            }

            if (detailedDispatch.ClientID.StartsWith("GCO"))
            {
                #region Geico
                var msg = (detailedDispatch.Job.JobDescription ?? "").ToLower().Replace("cvrd", "cvrd ").Replace(".", " ").Replace("  ", " ").Replace("  ", " ").Trim();

                string customerPaidMiles = "";
                string coveredMiles = "";

                if (msg.Contains("over mileage: "))
                {
                    var start3 = msg.IndexOf("over mileage: ");
                    if (start3 > -1)
                    {
                        var start4 = msg.IndexOf("enroute", start3);
                        if (start4 != -1)
                        {
                            var m3 = msg.Substring(start3, start4 - start3).Trim();

                            m3 = m3.Substring(m3.LastIndexOf(' ') + 1);
                            customerPaidMiles = m3;
                        }
                    }
                }

                if (msg.Contains("covered mileage: "))
                {
                    var start3 = msg.IndexOf("covered mileage: ");

                    start3 += "covered mileage: ".Length;
                    if (start3 > -1)
                    {
                        var start4 = msg.IndexOf("over", start3);
                        if (start4 != -1)
                        {
                            var m3 = msg.Substring(start3, start4 - start3).Trim();

                            m3 = m3.Substring(m3.LastIndexOf(' ') + 1).Trim();
                            coveredMiles = m3;
                        }
                    }
                }

                e.SetAttribute(Dispatch.AttributeValue.BUILTIN_GEICO_CUSTOMER_COVERED_MILES, coveredMiles);
                e.SetAttribute(Dispatch.AttributeValue.BUILTIN_GEICO_CUSTOMER_PAID_MILES, customerPaidMiles);
                #endregion
            }
            else if (detailedDispatch.ClientID.StartsWith("TSLA"))
            {
                string covg = null;
                
                try
                {
                    covg = jsonObj?.Coverage?.Description?.ToString();
                }
                catch
                {

                }

                if (!string.IsNullOrWhiteSpace(detailedDispatch?.Coverage?.BenefitLimit))
                {
                    e.SetAttribute(Dispatch.AttributeValue.BUILTIN_MOTORCLUB_COVERAGELIMIT, detailedDispatch.Coverage.BenefitLimit);
                }
                else if (!string.IsNullOrWhiteSpace(covg))
                {
                    e.SetAttribute(Dispatch.AttributeValue.BUILTIN_BILLING_NOTES, covg);
                }
            }

            if (jsonObj.OwnerUserId == null && cr.OwnerUserId == null)
            {
                if (e.OwnerUserId < 100)
                    e.OwnerUserId = 1;
            }

            e.OwnerUserId = cr.OwnerUserId.GetValueOrDefault(0);
            e.CallRequestId = cr?.CallRequestId;

            if (qi.OwnerUserId != null && e.OwnerUserId < 100)
                e.OwnerUserId = Convert.ToInt32(qi.OwnerUserId);

            await ApplyRoundRobinDispatcherLogicAsync(qi, e, detailedDispatch?.DispatchID, detailedDispatch?.ContractorID, detailedDispatch?.LocationID);

            if (cr.OwnerUserId == 0)
                cr.OwnerUserId = 1;

            if (e.Assets == null || e.Assets.Count == 0)
            {
                e.Assets = new System.Collections.ObjectModel.Collection<EntryAsset>();
                e.Assets.Add(asset);
            }

            var phone = Core.FormatPhoneWithDashesOnly(jsonObj?.MemberCallBackNumber?.ToString() ?? detailedDispatch.AccountInfo.CallBackPhone);

            var customerName = detailedDispatch.AccountInfo.Customer.FirstName + " " + detailedDispatch.AccountInfo.Customer.LastName;
            EntryContact c = e.Contacts.Where(o =>
                o.Name == customerName ||
                Core.FormatPhoneWithDashesOnly(o.Phone) == phone)
                .FirstOrDefault();

            bool newContact = false;
            if (c == null)
            {
                c = new EntryContact() { Name = customerName };
                e.Contacts.Add(c);
                newContact = true;
            }

            c.Phone = phone;

            int userId = 1;

            if (detailedDispatch.ClientID.StartsWith("GCO"))
                userId = 3;

            await e.Save(token: new AuthenticationToken() { UserId = userId }, ipAddress: "127.0.0.1");

            if (newContact)
                await CheckForRoadsideFeatureAndAutoInvite(e, c);

            cr.DispatchEntryId = e.Id;
            await cr.UpdateStatus(CallRequestStatus.Accepted, po: e.PurchaseOrderNumber);
            await cr.Save();

            // Save CallRequest "create" log event to activity logger
            await new ActivityLogging.ActivityLogItem()
            {
                ParentObjectId = cr.DispatchEntryId,
                ParentObjectTypeId = ActivityLogging.ActivityLogType.DispatchEntry,
                ObjectId = cr.CallRequestId,
                Type = ActivityLogging.ActivityLogType.CallRequest,
                ActionId = (int)ActivityLogging.ActivityLogActionType.DigitalDispatchAccepted,
                IpAddress = "0.0.0.0",
                UserId = cr.OwnerUserId.GetValueOrDefault(0),
                Details = new ActivityLogging.ActivityLogItemDetail()
                {
                    Data = new
                    {
                        Eta = gd.ETA.GetValueOrDefault(0),
                    }.ToJson(),
                    ActivityLogItemDetailTypeId = ActivityLogging.ActivityLogItemDetailType.RawJson
                }
            }.SaveAsync();

            await AutoDispatch.AutoDispatchServiceBusHandler.Send(e);

            await PushNotificationProvider.UpdateCallRequestStatus(cr.CompanyId, cr.CallRequestId, cr.Status);
            bool manual = (qi.Type == DigitalDispatchActionQueueItemType.InternalAcceptMissedCall);

            await SendIsscEmailForCall(e, gd.CallJson, manual, cr.CallRequestId);

            logger.LogEvent("{0}: Saved Call #{1} with DispatchEntryId {2} from DispatchId {3} (Manual={4})",
                    qi.CompanyId, LogLevel.Info, sourceMessage.Message.MessageId, e.CallNumber, e.Id, gd.DispatchId, manual);

            return e;
        }

        public class DispatcherObject
        {
            public int? UserId { get; set; }
            public TimeSpan? StartTime { get; set; }
            public TimeSpan? StopTime { get; set; }
            public int[] RemoveFromRoundRobin { get; set; }
        }

        internal static async Task ApplyRoundRobinDispatcherLogicAsync(DigitalDispatchActionQueueItem qi, Entry e,
            string dispatchId, string contractorId, string locationId = null)
        {
            try
            {
                var company = await Company.Company.GetByIdAsync(e.CompanyId);
                if (company == null || (company != null && !await company.HasFeatureAsync(Generated.Features.Dispatching_RoundRobinAutomaticDispatcherAssignment)))
                    return;


                if (e.OwnerUserId == 2)
                {
                    logger.Info(e?.Account?.MasterAccountId ?? 0, "RoundRobinDispatcher",
                        "Skipped round-robin dispatcher assignment; this was an agero phone call.",
                        contractorId, locationId, dispatchId, qi?.CompanyId);
                    return;
                }
                // warning: this should only be used for jack rabbit right now

                if (e.OwnerUserId < 100)
                {
                    int parentCompanyId = (await Company.SharedCompany.GetByCompanyIdAsync(company.Id)).FirstOrDefault()?.CompanyId ?? company.Id;

                    var dispatcherJson = await CompanyKeyValue.GetFirstValueOrNullAsync(e.CompanyId, Provider.Towbook.ProviderId,
                        "DispatcherRoundRobin_SetDispatcherJson");
                    int[] usersExcluded = Array.Empty<int>();

                    if (dispatcherJson != null)
                    {
                        var dj = JsonConvert.DeserializeObject<DispatcherObject>(dispatcherJson);

                        if (dj != null && dj.UserId != null)
                        {
                            if (dj.StartTime != null && dj.StopTime != null)
                            {
                                if (DateTime.Now.TimeOfDay >= dj.StartTime && DateTime.Now.TimeOfDay < dj.StopTime)
                                {
                                    e.OwnerUserId = dj.UserId.Value;
                                    logger.Info(e?.Account?.MasterAccountId ?? 0, "RoundRobinDispatcher",
                                        "Applied direct assignment from DispatcherRoundRobin_SetDispatcherJson",
                                        contractorId, locationId, dispatchId, qi?.CompanyId,
                                        dj);

                                    usersExcluded = dj.RemoveFromRoundRobin;
                                    return;
                                }
                            }
                        }
                    }

                    var availableDispatchers = Cache.Instance.HashGetAll<WebSession>(
                            "_currentUsers:" + parentCompanyId
                        ).Where(o =>
                            o.UserTypeId == (int)User.TypeEnum.Dispatcher &&
                            o.LastVerified > DateTime.Now.AddMinutes(-10) &&
                            !usersExcluded.Contains(o.UserId))
                        .OrderBy(o => User.GetById(o.UserId)?.LastLogin ?? o.LastVerified).ToArray();

                    int lastUserId = 0;

                    var x = Core.GetRedisValue("dispatch_round_robin:" + parentCompanyId);

                    if (x != null)
                    {
                        int.TryParse(x, out lastUserId);
                    }

                    availableDispatchers = availableDispatchers.Where(o => User.GetById(o.UserId).HasAccessToCompany(e.CompanyId)).ToArray();

                    if (availableDispatchers.Any())
                    {
                        WebSession nextUp = null;

                        for (int i = 0; i < availableDispatchers.Length; i++)
                        {
                            if (lastUserId != 0)
                            {
                                if (availableDispatchers[i].UserId == lastUserId)
                                {
                                    // found it.
                                    if (availableDispatchers.Length > i + 1)
                                    {
                                        nextUp = availableDispatchers[i + 1];
                                        break;
                                    }
                                    else
                                    {
                                        // start back at the beginning
                                        nextUp = availableDispatchers[0];
                                        break;
                                    }
                                }
                            }
                            else
                            {
                                // take the first one.
                                nextUp = availableDispatchers[i];
                                break;
                            }
                        }

                        if (nextUp == null && availableDispatchers.Any())
                            nextUp = availableDispatchers[0];

                        if (nextUp != null)
                        {
                            Core.SetRedisValue("dispatch_round_robin:" + parentCompanyId, nextUp.UserId.ToString());
                            e.OwnerUserId = nextUp.UserId;

                            logger.Info(e?.Account?.MasterAccountId ?? 0, "RoundRobinDispatcher", "Applied round-robin dispatcher",
                                contractorId, locationId, dispatchId, qi?.CompanyId,
                                new
                                {
                                    assignedDispatcher = nextUp,
                                    availableDispatchers = availableDispatchers
                                });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error(e?.Account?.MasterAccountId ?? 0, "RoundRobinDispatcher", "Failed to apply round-robin dispatcher: " +
                    ex.Message, contractorId, locationId, dispatchId, qi?.CompanyId,
                    new { exception = ex });
            }
        }

        private static string AttachDriveTypeToCallNotesFromVin(Entry e, EntryAsset asset)
        {
            try
            {
                var output = "";
                var lookupVin = new CarfaxUtility.CarfaxVin()
                {
                    DriveType = asset.DriveType,
                    Vin = asset.Vin
                };

                if (string.IsNullOrWhiteSpace(asset.DriveType) &&
                    string.IsNullOrWhiteSpace(asset.Vin))
                    return null;


                if (asset.DriveType == null && asset.Vin != null && asset.Vin.Length == 17)
                    lookupVin = VinDecoder.Decode(asset.Vin);

                if (lookupVin != null)
                {
                    if (lookupVin.DriveType == "AWD" || lookupVin.DriveType == "4WD" || lookupVin.DriveType == "4X4")
                    {
                        output = "*** DRIVE TYPE: " + lookupVin.DriveType + " ***\n\n";
                    }
                    else
                    {
                        output = "Drive Type: " + lookupVin.DriveType + "\n";
                    }

                    asset.DriveType = lookupVin.DriveType;

                    if (e.Notes == null)
                        e.Notes = "";

                    // only add it if it's not part of the data already.
                    if (!e.Notes.Contains(output))
                        e.Notes = output + e.Notes;

                    return lookupVin.DriveType;
                }

                return null;
            }
            catch
            {
                return null;
            }
        }

        public static async Task<bool> SendIsscEmailForCall(Entry e, string detailedDispatch, bool manual = false,
            int callRequestId = 0)
        {
            if (e == null)
                return false;

            if (detailedDispatch == null)
                return false;

            var c = Newtonsoft.Json.JsonConvert.DeserializeObject<dynamic>(detailedDispatch,
                new Newtonsoft.Json.JsonSerializerSettings()
                {
                    Formatting = Newtonsoft.Json.Formatting.Indented,
                    NullValueHandling = NullValueHandling.Ignore
                });

            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("<html><head><style>html,body,h1,p,li{{font-family:segoe ui, 'Open Sans', verdana}}</style></head><body><h3><span style='color:#2B75BE; font-weight: normal'>towbook</span> Live Dispatch</h3>");

            sb.AppendLine("<p>");
            sb.AppendFormat("<strong>{0} Job Details</strong><br />", e.Account.Company);
            sb.AppendFormat("Call #{0}<br />", e.CallNumber);
            if (manual)
                sb.AppendFormat("***MANUALLY*** Accepted by {0}<br />", e.OwnerUser.FullName);
            else
                sb.AppendFormat("Accepted by {0}<br />", e.OwnerUser?.FullName ?? e.OwnerUserId.ToString());

            if (manual)
                sb.AppendLine("<strong style='color: red'>This accept notice was manually generated by " + e.OwnerUser.FullName +
                    " choosing to mark the request as Manually Accepted. There was not an Accepted event received from the motor club for this call.</strong><br />");

            sb.AppendLine("</p>");

            sb.AppendLine("<ul>");

            if (e.PurchaseOrderNumber != null)
            {
                sb.AppendFormat("\n<li><strong>Purchase Order #</strong> {0}</li>", e.PurchaseOrderNumber);
            }

            var companyCallRequestId = e.CompanyId.ToString().PadLeft(7, '0')
                + callRequestId.ToString().PadLeft(9, '0');

            sb.AppendFormat("\n<li>Towbook Request ID: {0}</li>", companyCallRequestId);

            foreach (var key in c)
            {
                var values = key as IEnumerable<dynamic>;

                if (key.Value != null)
                {
                    if ((key.Value as IEnumerable<dynamic>).Count() > 1)
                    {
                        sb.AppendLine("<li>");

                        string zname = key.Name;

                        if (zname == "AccountInfo")
                            zname = "Customer Contact";

                        sb.AppendLine("<strong>" + zname + "</strong>");
                        sb.AppendLine("<ul>");

                        if (zname == "Vehicle")
                        {
                            sb.AppendFormat("<li>{0} {1} {2} {3}<br />{4}</li>", key.Value.Year, key.Value.Make, key.Value.Model, key.Value.Color, key.Value.AdditionalInfo);

                            if (key.Value.VIN != null)
                                sb.AppendFormat("<li>VIN: {0}</li>", key.Value.VIN);
                        }
                        else
                            foreach (var key3 in key.Value)
                            {
                                if (new string[] { "JobID", "RequiredAcknowledgeTime" }.Contains((string)key3.Name.ToString()))
                                    continue;

                                DateTime date = DateTime.MinValue;
                                if (DateTime.TryParse(key3.Value.ToString(), out date))
                                {
                                    date = Core.OffsetDateTime(e.Company, date.ToLocalTime());
                                    key3.Value = date.ToShortDateString() + " " + date.ToShortTowbookTimeString();
                                }

                                if (key3.Name == "Member")
                                {
                                    if (key3.Value.FirstName != null && key3.Value.LastName != null)
                                        sb.AppendLine(String.Format("  <li>{0}: {1} {2}</li>", key3.Name, key3.Value.FirstName, key3.Value.LastName));
                                }
                                else if (key3.Name == "Customer")
                                {
                                    var kv = key3.Value;
                                    sb.AppendLine(String.Format("  <li>Customer: {0} {1}</li>", kv.FirstName, kv.LastName));
                                }
                                else if (key3.Name == "Member")
                                {
                                    var kv = key3.Value;
                                    sb.AppendLine(String.Format("  <li>Member: {0} {1}</li>", kv.FirstName, kv.LastName));
                                }
                                else if (key3.Name == "PrimaryTasks" && key3.Value.Count == 1)
                                {
                                    sb.AppendFormat("  <li>Task: {0}</li>\r\n", key3.Value[0].Task);
                                }
                                else if (key3.Name == "ServiceQuestions")
                                {
                                    if (key3.Value != null)
                                    {
                                        foreach (var x in key3.Value)
                                        {
                                            sb.AppendFormat("<li>{0} {1}</li>\r\n", (x.Question + ":").ToString().Replace("?:", "?"),
                                                String.Join(",", x.Responses));
                                        }
                                    }
                                }
                                else if (key3.Value != null && !String.IsNullOrWhiteSpace(key3.Value.ToString()) && key3.Value.ToString() != "0")
                                {

                                    string name = key3.Name;
                                    if (name == "CallBackPhone") name = "Phone #";

                                    sb.AppendLine(String.Format("  <li>{0}: {1}</li>", name, key3.Value.ToString().Replace("False", "No").Replace("True", "Yes")));
                                }

                            }
                        sb.AppendLine("</ul></li>");
                    }
                    else
                    {
                        if (key.Name == "ClientID" || key.Name == "Event" || key.Name == "ResponseID" || key.Name == "DispatchID")
                            continue;
                        sb.AppendFormat("<li>{0}: {1}</li>\n", key.Name, key.Value.ToString().Replace("False", "No").Replace("True", "Yes"));
                    }
                }
            }
            sb.AppendLine("</ul></body></html>");

            using (var msg = new MailMessage())
            {

                var ma = await MasterAccount.GetByIdAsync(e.Account.MasterAccountId);

                msg.Subject = "Towbook: New " + ma.Name + " Call - PO # " + e.PurchaseOrderNumber + " - Call #" + e.CallNumber;

                if (manual)
                    msg.Subject += " (Manually Accepted)";

                msg.IsBodyHtml = true;
                msg.Body = sb.ToString();
                msg.From = new MailAddress("<EMAIL>", "Towbook");
                msg.ReplyToList.Add(new MailAddress("<EMAIL>", "Towbook Support"));
                msg.Headers.Add("X-Towbook-Id", companyCallRequestId);
                msg.Headers.Add("X-Towbook-CallId", e.Id.ToString());
                var ea = EmailAddress.GetByCompanyId(e.CompanyId).LastOrDefault();
                if (ea != null)
                {
                    var forwarders = EmailAddressForwarder.GetByEmailAddress(ea.Id);
                    foreach (var eaf in forwarders)
                    {
                        if (Core.IsEmailValid(eaf.Email))
                        {
                            msg.To.Add(new MailAddress(eaf.Email, e.Company.Name));
                        }
                    }
                }

                // if no forwarder addresses were found, send to the company address.
                if (msg.To.Count == 0 && Core.IsEmailValid(e.Company.Email))
                    msg.To.Add(new MailAddress(e.Company.Email, e.Company.Name));

                if (msg.To.Count == 0)
                {
                    logger.Log(LogLevel.Error, "DispatchEntryId/{0}: Couldn't send email... no email address to send notification to. ", e.Id);
                    return false;
                }

                try
                {
                    var disable = false;
                    if (CompanyKeyValue.GetFirstValueOrNull(e.CompanyId, Provider.Towbook.ProviderId,
                        "DisableIsscEmailForCallSending") == "1")
                    {
                        disable = true;
                    }

                    if (!disable)
                    {
                        var user = new User { CompanyId = e.CompanyId };

                        if (e.Account.MasterAccountId == MasterAccountTypes.Geico)
                        {
                            user = await User.GetByIdAsync(3);
                        }
                        using (var sc = new SmtpClient().Get())
                        {
                            await sc.Send(msg, user, "DigitalDispatch");
                        }
                    }
                }
                catch (SmtpException ex)
                {
                    logger.Log(LogLevel.Error, "DispatchEntryId/{0}: Couldn't send email... {1} ", e.Id, ex.ToString());
                }

                msg.To.Clear();
                try
                {
                    string eml = msg.ToEmlFile();

                    CompanyFile dispatchEntryFile = new CompanyFile();
                    dispatchEntryFile.Size = eml.Length;
                    dispatchEntryFile.Filename = "LiveDispatch_" + e.PurchaseOrderNumber + ".eml";
                    dispatchEntryFile.RawUrl = dispatchEntryFile.Filename;
                    dispatchEntryFile.OwnerUserId = e.OwnerUserId;
                    dispatchEntryFile.CompanyId = e.CompanyId;
                    dispatchEntryFile.DispatchEntries.Add(e.Id);

                    dispatchEntryFile.Save(true);

                    string fn = dispatchEntryFile.LocalLocation;

                    if (!Directory.Exists(Path.GetDirectoryName(fn)))
                    {
                        Directory.CreateDirectory(Path.GetDirectoryName(fn));
                    }

                    File.WriteAllText(fn, eml);
                    var result = await FileUtility.SendFileAsync(fn);
                    if (result.IsHttpSuccess())
                        System.IO.File.Delete(fn);
                }
                catch (Exception ex)
                {
                    logger.Log(LogLevel.Error, "DispatchEntryId/{0}: Couldn't save email... {1} ", e.Id, ex.ToString());
                }
            }

            return true;
        }

        public static bool ShouldAllowCancel(Entry en)
        {
            if (en == null)
                return false;

            if (en.Status.Id == (int)Entry.EntryStatus.Completed)
                return false;

            if (en.Status.Id == (int)Entry.EntryStatus.Canceled)
                return false;

            if ((en.Reason != null &&
                (en.Reason.NameMatchable().Contains("goa") ||
                en.Reason.NameMatchable().Contains("goneonarrival"))) ||
                (en.InvoiceItems.Where(o => o.Name != null &&
                    (o.Name.Contains("goa") ||
                    o.Name.Contains("gone on arrival"))).Any()))
            {
                return false;
            }
            else
            {
                return true;
            }
        }

        public async Task<bool> HandleAgeroQueueIncomingMessage(
            DigitalDispatchActionQueueItem qi,
            AgeroMessage jsonObj,
            ProcessMessageEventArgs sourceMessage)
        {
            var lastSession = AgeroSession.GetByVendorId(Convert.ToInt32(jsonObj.VendorId));
            if (lastSession == null)
                throw new MotorClubException("No last session found for the given vendorId: " + Convert.ToInt32(jsonObj.VendorId));

            switch (qi.Type)
            {
                #region Call Request Received
                case DigitalDispatchActionQueueItemType.IncomingCallReceived:
                    try
                    {
                        var details = new AgeroRestClient().GetDispatchDetail(lastSession.AccessToken, jsonObj.DispatchRequestNumber);

                        var acr = await CreateCallRequest(
                            new
                            {
                                CallDetails = details,
                                VendorId = jsonObj.VendorId,
                                FacilityId = jsonObj.FacilityId ?? 0
                            }, MotorClubName.Agero, true, MasterAccountTypes.Agero);

                        qi.CallRequestId = acr.CallRequestId;
                        DigitalDispatchService.LogAction(qi);
                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                        logger.Info(MasterAccountTypes.Agero, "CallReceived", "New Call Request Created", jsonObj.VendorId.ToString(),
                            jsonObj.FacilityId?.ToString(), jsonObj.DispatchRequestNumber.ToString(), acr.CompanyId, new
                            {
                                CallRequestId = acr.CallRequestId,
                                QueueItemId = qi.QueueItemId
                            });
                    }
                    catch (AgeroException ae)
                    {
                        logger.Log(LogLevel.Error, "Agero/{0}: Couldn't create CallRequest due to Agero error: {1}", qi.QueueItemId, ae.Message);
                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                    }
                    break;
                #endregion

                #region Call Accepted event 
                case DigitalDispatchActionQueueItemType.IncomingCallAccepted:
                    CallRequest cr = null;
                    AgeroDispatch ad = AgeroDispatch.GetByDispatchId(jsonObj.DispatchRequestNumber, jsonObj.VendorId);

                    var sw = Stopwatch.StartNew();

                    logger.LogEvent("{0}/{1}: Processing Agero IncomingCallAccepted event.",
                                qi.CompanyId,
                                LogLevel.Info,
                                (sourceMessage.Message != null ? sourceMessage.Message.MessageId : "messageId is null"),
                                jsonObj.DispatchRequestNumber);

                    if (ad == null)
                    {
                        if (jsonObj.DispatchSource == "LivePhone")
                            logger.LogEvent("{0}: DispatchID {1} was accepted via LivePhone.",
                                qi.CompanyId,
                                LogLevel.Info,
                                (sourceMessage.Message != null ? sourceMessage.Message.MessageId : "messageId is null"),
                                jsonObj.DispatchRequestNumber);
                        else
                            logger.LogEvent("{0}: Couldn't find a AgeroDispatch with ID of {1} - it was accepted via {2}. Debug this with Agero.",
                                qi.CompanyId,
                                LogLevel.Warn,
                                (sourceMessage.Message != null ? sourceMessage.Message.MessageId : "messageId is null"),
                                jsonObj.DispatchRequestNumber,
                                jsonObj.DispatchSource);
                        try
                        {
                            var newDetails = new AgeroRestClient().GetDispatchDetail(lastSession.AccessToken, jsonObj.DispatchRequestNumber);
                            var newAcr = await CreateCallRequest(new
                            {
                                CallDetails = newDetails,
                                VendorId = jsonObj.VendorId,
                                FacilityId = jsonObj.FacilityId
                            }, MotorClubName.Agero, false, MasterAccountTypes.Agero);

                            if (newAcr != null)
                            {
                                ad = AgeroDispatch.GetByDispatchId(jsonObj.DispatchRequestNumber, jsonObj.VendorId);
                                cr = newAcr;
                            }
                        }
                        catch (AgeroException ae)
                        {
                            logger.Log(LogLevel.Error, "Agero/{0}: Couldn't proceed to creating Entry due to Agero error: {1}", qi.QueueItemId, ae.Message);
                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                            break;
                        }
                    }
                    else
                    {
                        cr = CallRequest.GetById(ad.CallRequestId);

                        if (cr != null)
                        {
                            if (cr.CompanyId != qi.CompanyId)
                                ad = null;
                        }
                    }
                    logger.LogEvent("{0}/{1}: Passed AgeroDispatch Lookup,  {2}ms so far.",
                                qi.CompanyId,
                                LogLevel.Info,
                                (sourceMessage.Message != null ? sourceMessage.Message.MessageId : "messageId is null"),
                                jsonObj.DispatchRequestNumber,
                                sw.ElapsedMilliseconds);

                    var redirect = AccountKeyValue.GetByAccount(cr.CompanyId, cr.AccountId, Provider.Towbook.ProviderId, "RedirectByFacilityIdJson").FirstOrDefault();

                    int dCompanyId = qi.CompanyId.Value;
                    int dAccountId = qi.AccountId.GetValueOrDefault(0);

                    if (redirect != null)
                    {
                        var redirects = JsonConvert.DeserializeObject<List<AccountLocationModel>>(redirect.Value);
                        var redirectAccountId = redirects.Where(o => o.facilityId == jsonObj.FacilityId && o.accountId != 0).FirstOrDefault();
                        if (redirectAccountId != null)
                        {
                            dCompanyId = redirectAccountId.companyId;
                            dAccountId = redirectAccountId.accountId;
                        }
                    }


                    var company = await Company.Company.GetByIdAsync(dCompanyId);

                    if (company == null)
                    {
                        logger.Warn(MasterAccountTypes.Agero, "CallAccepted", "Received digital dispatch acceptance for company that has been deleted.",
                            ad.VendorId.ToString(), null, ad.DispatchId.ToString(), dCompanyId);

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);

                        await sourceMessage.CompleteAsync();

                        return false;
                    }

                    var arc = new AgeroRestClient();
                    var sess = AgeroSession.GetByAccountId(dAccountId);
                    DispatchDetails detailedDispatch = null;

                    for (int retry = 0; retry < 3; retry++)
                    {
                        try
                        {
                            detailedDispatch = arc.GetDispatchDetail(sess.AccessToken, jsonObj.DispatchRequestNumber);
                        }
                        catch (AgeroException ae)
                        {
                            logger.Error(MasterAccountTypes.Agero,
                                "CreateCall",
                                "GetDispatchDetail failed: " + ae.Message,
                                jsonObj.VendorId.ToString(),
                                jsonObj.FacilityId.ToString(),
                                jsonObj.DispatchRequestNumber.ToString(),
                                dCompanyId,
                                new
                                {
                                    queueItemId = qi.QueueItemId,
                                    accessToken = sess.AccessToken,
                                    message = ae.Message
                                });
                        }

                        if (string.IsNullOrWhiteSpace(detailedDispatch?.PurchaseOrderNumber))
                        {
                            logger.Warn(MasterAccountTypes.Agero, "CreateCall", $"PO Number is missing.",
                                jsonObj.VendorId.ToString(),
                                jsonObj.FacilityId.ToString(),
                                jsonObj.DispatchRequestNumber.ToString(),
                                dCompanyId,
                                new { retry = retry });

                            await Task.Delay(500);
                        }
                        else
                            break;
                    }

                    if (detailedDispatch == null)
                    {
                        logger.Log(LogLevel.Error, "Agero/{0}: Couldn't proceed to creating Entry due to Agero dispatchDetails being null.",
                            qi.QueueItemId);

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                        break;
                    }

                    logger.LogEvent("{0}/{1}: Passed GetDispatchDetail API Call... {2}ms so far.",
                                dCompanyId,
                                LogLevel.Info,
                                (sourceMessage.Message != null ? sourceMessage.Message.MessageId : "messageId is null"),
                                jsonObj.DispatchRequestNumber,
                                sw.ElapsedMilliseconds);

                    var lockkey = detailedDispatch.PurchaseOrderNumber;

                    if (string.IsNullOrWhiteSpace(lockkey))
                    {
                        lockkey = Guid.NewGuid().ToString("N");
                    }

                    if (detailedDispatch.PurchaseOrderNumber == "0" ||
                        string.IsNullOrWhiteSpace(detailedDispatch.PurchaseOrderNumber))
                        detailedDispatch.PurchaseOrderNumber = "NoCoverage-" + detailedDispatch.DispatchRequestNumber;

                    var fe = await DistributedLock.ForAsync<Entry>("Agero", lockkey, 10000,
                        lockAcquired: async delegate ()
                        {
                            logger.LogEvent("{0}/{1}: Entered lockAcquired delegate ()... {2}ms so far.",
                                        dCompanyId,
                                        LogLevel.Info,
                                        (sourceMessage.Message != null ? sourceMessage.Message.MessageId : "messageId is null"),
                                        jsonObj.DispatchRequestNumber,
                                        sw.ElapsedMilliseconds);
                            Entry e = null;

                            if (!string.IsNullOrWhiteSpace(detailedDispatch.PurchaseOrderNumber) &&
                                detailedDispatch.PurchaseOrderNumber != "0")

                            {
                                e = await Entry.GetByPurchaseOrderNumberAsync(
                                    qi.AccountId.Value,
                                    detailedDispatch.PurchaseOrderNumber);

                                if (e == null)
                                {
                                    foreach (var a in await Account.GetByCompanyAsync(company, AccountType.MotorClub))
                                    {
                                        e = await Entry.GetByPurchaseOrderNumberAsync(a.Id, detailedDispatch.PurchaseOrderNumber);
                                        if (e != null)
                                            break;
                                    }
                                }
                            }

                            var ignoreExtras = false;

                            if (e == null)
                            {
                                e = new Entry();
                                e.CompanyId = dCompanyId;
                            }
                            else
                            {
                                logger.LogEvent("{0}: Found existing towbook call for PO {1}/Dispatch Number {2}... Call #{3}... we're going to update this one.",
                                    dCompanyId, LogLevel.Warn, (sourceMessage.Message != null ? sourceMessage.Message.MessageId : "sourceMessage.messageId"), detailedDispatch.PurchaseOrderNumber, jsonObj.DispatchRequestNumber, e.CallNumber);

                                if (CompanyKeyValue.GetFirstValueOrNull(e.CompanyId, Provider.Towbook.ProviderId, "BlockEmailsFromUpdatingCallRequests") == "1")
                                {
                                    logger.Warn("Company {0} is set to ignore digital dispatch updates for calls that were already created via email.",
                                        e.CompanyId, cr.CallRequestId);
                                    ignoreExtras = true;
                                }
                            }


                            if (e.Id > 0)
                            {
                                if (CompanyKeyValue.GetFirstValueOrNull(e.CompanyId, Provider.Towbook.ProviderId, "BlockDigitalCallRequestUpdates") == "1")
                                {
                                    logger.Info("Ignoring Update for {1}; Company {0} is set to ignore digital dispatch updates.",
                                        cr.CompanyId, cr.CallRequestId);

                                    return null;
                                }
                            }

                            e.AccountId = dAccountId;
                            e.PurchaseOrderNumber = detailedDispatch.PurchaseOrderNumber;

                            if (jsonObj.FacilityId > 0)
                            {
                                string findKey = jsonObj.VendorId + "." + jsonObj.FacilityId;

                                var accounts = await Account.GetByCompanyAsync(await Company.Company.GetByIdAsync(dCompanyId), false, AccountType.MotorClub);

                                var foundAccount = accounts.Where(o =>
                                    (o.Company ?? "").Contains(findKey) ||
                                    o.ReferenceNumber == findKey ||
                                    (o.ReferenceNumber ?? "").Contains(findKey)).FirstOrDefault();

                                if (foundAccount != null)
                                    e.AccountId = foundAccount.Id;
                            }

                            #region Handle MoveRoadsideCallsToAccountId to move roadside calls to their own AccountId 

                            if (string.IsNullOrWhiteSpace(cr.TowDestination))
                            {
                                var akv = AccountKeyValue.GetByAccount(e.CompanyId,
                                    e.AccountId,
                                    Provider.Towbook.ProviderId,
                                    "MoveRoadsideCallsToAccountId"
                                    ).FirstOrDefault();
                                if (akv != null)
                                {
                                    int tempAccId = 0;
                                    if (int.TryParse(akv.Value, out tempAccId))
                                    {
                                        var tempAcc = await Account.GetByIdAsync(tempAccId);
                                        if (tempAcc != null)
                                        {
                                            e.AccountId = tempAcc.Id;
                                        }
                                    }
                                }
                            }
                            #endregion


                            if (e.Account?.DefaultPriority == 1)
                                e.Priority = Entry.EntryPriority.High;

                            EntryAsset asset = null;

                            if (e.Assets != null)
                            {
                                asset = e.Assets.FirstOrDefault();
                            }

                            if (asset == null)
                                asset = new EntryAsset() { BodyTypeId = 1 };

                            if (asset.BodyTypeId == 0)
                                asset.BodyTypeId = 1;


                            if (detailedDispatch.Vehicle.Year > 0)
                                asset.Year = detailedDispatch.Vehicle.Year;

                            asset.Make = GetManufacturerByName(detailedDispatch.Vehicle.Make);
                            asset.Model = GetModelByName(detailedDispatch.Vehicle.Model);

                            var colorId = GetColorIdByName(detailedDispatch.Vehicle.Color);
                            if (colorId != 0)
                                asset.ColorId = colorId;

                            asset.Vin = asset.Vin.OnlySetIfEmptyOrDifferent(detailedDispatch.Vehicle.VIN);
                            asset.LicenseNumber = asset.LicenseNumber.OnlySetIfEmpty(detailedDispatch.Vehicle.Plate);
                            asset.LicenseState = asset.LicenseState.OnlySetIfEmpty(detailedDispatch.Vehicle.State);

                            if (detailedDispatch.Equipment != null)
                            {
                                if (new string[] { "LDF", "LDWL", "WLD" }
                                    .Contains(detailedDispatch.Equipment.Id))
                                    asset.BodyTypeId = 1;
                                else if (new string[] { "MDRS", "MDF", "WMD", "M2" }
                                    .Contains(detailedDispatch.Equipment.Id))
                                    asset.BodyTypeId = 2;
                                else if (new string[] { "HDLB", "HDRS", "HDTS", "HDUR", "HDW", "WHD", "M3" }
                                    .Contains(detailedDispatch.Equipment.Id))
                                    asset.BodyTypeId = 3;
                                else
                                    asset.BodyTypeId = 1;
                            }

                            void addNote(string line, bool top = false)
                            {
                                if (string.IsNullOrWhiteSpace(line))
                                    return;

                                if (e.Notes == null || !e.Notes.Contains(line))
                                {
                                    if (e.Notes == null)
                                        e.Notes = line + "\n";
                                    else
                                    {
                                        if (top)
                                            e.Notes = line + e.Notes.Trim('\n') + "\n";
                                        else
                                            e.Notes += "\n" + line.Trim('\n');
                                    }
                                }
                            }

                            e.Notes = e.Notes ?? "";

                            e.TowSource = e.TowSource ?? detailedDispatch.DisablementLocation.ToString();

                            addNote("COVID-19 Safety Reminder: Follow best practices to keep you and your customers safe. " +
                                "We recommend wearing masks, carrying hand sanitizer in your truck, and washing your hands whenever possible.\n");

                            if (!ignoreExtras && !String.IsNullOrWhiteSpace(detailedDispatch.DisablementLocation.CrossStreet))
                            {
                                addNote("Pickup Address Cross Street: " + detailedDispatch.DisablementLocation.CrossStreet);
                            }

                            if (detailedDispatch.TowDestination != null)
                            {
                                e.TowDestination = e.TowDestination ?? detailedDispatch.TowDestination.ToString();
                            }

                            var pickup = e.Waypoints.Where(o => o.Title == "Pickup").FirstOrDefault();
                            var dest = e.Waypoints.Where(o => o.Title == "Destination").FirstOrDefault();

                            if (detailedDispatch.DisablementLocation != null)
                            {
                                if (pickup == null)
                                {
                                    pickup = new EntryWaypoint() { Title = "Pickup", Position = 1 };
                                    e.Waypoints.Add(pickup);
                                }
                                pickup = await detailedDispatch.DisablementLocation.ToWaypoint(pickup);
                            }

                            if (detailedDispatch.TowDestination != null)
                            {
                                if (dest == null)
                                {
                                    dest = new EntryWaypoint() { Title = "Destination", Position = 2 };
                                    e.Waypoints.Add(dest);
                                }
                                dest = await detailedDispatch.TowDestination.ToWaypoint(dest);
                            }

                            var ageroLatLong = CompanyKeyValue.GetByCompanyId(e.CompanyId,
                                Provider.Towbook.ProviderId, "AgeroImportPickupAddressAsLatLong")
                                .FirstOrDefault()?.Value;

                            if (ageroLatLong == "1")
                            {
                                if (pickup.Latitude != 0)
                                {
                                    addNote("Pickup Address: " + pickup.Address);
                                    e.TowSource = pickup.Address = pickup.Latitude + "," + pickup.Longitude;
                                }
                            }

                            if (e.CreateDate == DateTime.MinValue)
                                e.CreateDate = detailedDispatch.ReceivedTime.ToLocalTime();

                            if (e.ArrivalETA == null)
                                e.ArrivalETA = e.CreateDate.AddMinutes(detailedDispatch.RevisedETAInMinutes > 0 ? detailedDispatch.RevisedETAInMinutes :
                                    detailedDispatch.ETAInMinutes);

                            var reason = cr != null && cr.ServiceNeeded != null ? cr.ServiceNeeded : detailedDispatch.ServiceType;

                            if (detailedDispatch.Equipment?.Id == "PAR")
                                reason = "Accident";

                            e.ReasonId = await ReasonHelper.DetermineReasonId(MasterAccountTypes.Agero, dCompanyId, reason);
                            if (e.ReasonId == 1635)
                            {
                                if (!ignoreExtras)
                                    addNote("Service Needed: " + cr.ServiceNeeded);
                            }

                            if (detailedDispatch.Vehicle.DriveTrainType != "unknown")
                                asset.DriveType = DriveTypes.ConvertFrom(detailedDispatch.Vehicle.DriveTrainType);

                            AttachDriveTypeToCallNotesFromVin(e, asset);

                            if (!string.IsNullOrWhiteSpace(e.TowDestination))
                                addNote("Please capture at least 4 photos of the vehicle before towing it, and at least one at the destination once the vehicle has been unloaded.", true);

                            if (!ignoreExtras)
                            {
                                if (!string.IsNullOrWhiteSpace(detailedDispatch.Problem))
                                    addNote("Problem: " + detailedDispatch.Problem);

                                if (!string.IsNullOrWhiteSpace(detailedDispatch.ServiceType))
                                    addNote("Service Type: " + detailedDispatch.ServiceType);

                                if (detailedDispatch.Equipment != null && !string.IsNullOrWhiteSpace(detailedDispatch.Equipment.Type))
                                    addNote("Equipment: " + detailedDispatch.Equipment.Type);

                                if (!string.IsNullOrWhiteSpace(detailedDispatch.DisablementLocation?.LocationType))
                                    addNote("Disablement Location Type: " + detailedDispatch.Equipment.Type);

                                #region coverage amount
                                if (detailedDispatch.Coverage != null)
                                {
                                    e.SetAttribute(Dispatch.AttributeValue.BUILTIN_MOTORCLUB_COVERAGELIMIT,
                                        detailedDispatch.Coverage);
                                }
                                #endregion

                                #region notes
                                if (detailedDispatch.Comments != null)
                                {
                                    foreach (var comment in detailedDispatch.Comments)
                                    {
                                        if (!string.IsNullOrWhiteSpace(comment.CommentText))
                                        {
                                            addNote((comment.DisplayText ?? "Note") + ": " + comment.CommentText);
                                        }
                                    }
                                }
                                #endregion
                            }
                            #region DefaultTaxRate
                            if (!e.Attributes.ContainsKey(Dispatch.AttributeValue.BUILTIN_TAXRATE_OVERRIDE))
                            {
                                var defaultTaxRateId = CompanyKeyValue.GetByCompanyId(e.CompanyId, Provider.Towbook.ProviderId, "DefaultTaxRateId").FirstOrDefault();

                                if (defaultTaxRateId != null)
                                    e.SetAttribute(Dispatch.AttributeValue.BUILTIN_TAXRATE_OVERRIDE, defaultTaxRateId.Value);
                            }
                            #endregion

                            if (jsonObj.DispatchSource == "LivePhone")
                                e.OwnerUserId = 2;
                            else
                            {
                                if (cr != null)
                                {
                                    logger.LogEvent("{0}/CR{1}: Agero/OwnerUserId is set to {2}",
                                       dCompanyId, LogLevel.Warn,
                                       sourceMessage.Message.MessageId,
                                       cr.CallRequestId.ToString(),
                                       cr.OwnerUserId);

                                    if (cr.OwnerUserId == null)
                                        cr.OwnerUserId = 1; // clean

                                    e.CallRequestId = cr.CallRequestId;

                                    if (e.OwnerUserId < 100)
                                    {
                                        e.OwnerUserId = cr.OwnerUserId.GetValueOrDefault(0);
                                    }
                                }
                                else if (e.OwnerUserId < 100)
                                    e.OwnerUserId = 1;
                            }

                            await ApplyRoundRobinDispatcherLogicAsync(qi, e, jsonObj?.DispatchRequestNumber.ToString(),
                                jsonObj?.VendorId.ToString(),
                                jsonObj?.FacilityId.ToString());

                            logger.LogEvent("{0}/{1}: About to call Entry.Save... {2}ms so far.",
                                        dCompanyId,
                                        LogLevel.Info,
                                        (sourceMessage.Message != null ? sourceMessage.Message.MessageId : "messageId is null"),
                                        jsonObj.DispatchRequestNumber,
                                        sw.ElapsedMilliseconds);

                            if (e.Assets == null || e.Assets.Count == 0)
                            {
                                e.Assets = new System.Collections.ObjectModel.Collection<EntryAsset>();
                                e.Assets.Add(asset);
                            }

                            var customerName = detailedDispatch.DisablementLocation.ContactInfo.Name;
                            EntryContact c = e.Contacts.Where(o =>
                                o.Name == customerName ||
                                Core.FormatPhoneWithDashesOnly(o.Phone) == Core.FormatPhoneWithDashesOnly(detailedDispatch.DisablementLocation.ContactInfo.CallbackNumber))
                                .FirstOrDefault();

                            bool newContact = false;
                            if (c == null)
                            {
                                c = new EntryContact() { Name = customerName };
                                e.Contacts.Add(c);
                                newContact = true;
                            }

                            c.Phone = Core.FormatPhone(detailedDispatch.DisablementLocation?.ContactInfo?.CallbackNumber);

                            await e.Save(token: new AuthenticationToken()
                            {
                                UserId = jsonObj.DispatchSource == "LivePhone" ? 2 : 1
                            }, ipAddress: "127.0.0.1");

                            if (newContact)
                                await CheckForRoadsideFeatureAndAutoInvite(e, c);

                            await AutoDispatch.AutoDispatchServiceBusHandler.Send(e);
                            return e;
                        },
                        alreadyLocked: async delegate ()
                        {
                            logger.LogEvent("{0}/CR{1}: Lock already exists for {2}:{3}... pausing 250ms",
                                dCompanyId, LogLevel.Warn,
                                sourceMessage.Message.MessageId,
                                (cr != null ? cr.CallRequestId.ToString() : "NULL"),
                                qi.AccountId.Value,
                                detailedDispatch.PurchaseOrderNumber);

                            await Task.Delay(250);

                            return true;
                        });
                    sw.Stop();

                    if (fe == null)
                    {
                        logger.LogEvent("{0}: Creation of call failed; abandoning so it can be retried... this was the " +
                            sourceMessage.Message.DeliveryCount + " try... " + sw.ElapsedMilliseconds + "ms elapsed total.",
                            dCompanyId, LogLevel.Error, sourceMessage.Message.MessageId, qi.ToJson());

                        await sourceMessage.AbandonMessageAsync(sourceMessage.Message, new Dictionary<string, object>
                        {
                            ["abandonReason"] = $"Creation of call failed... Abandoned so it can be retried: {DateTime.Now.ToLongTimeString()}."
                        });

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);

                        //await sourceMessage.DeadLetterAsync("DistributedLock.For returned null.", "");
                        return false;
                    }
                    else
                    {
                        if (cr != null)
                        {
                            if (jsonObj.DispatchSource == "LivePhone")
                                cr.OwnerUserId = 2;

                            logger.LogEvent("{0}/CR{1}: After EntrySave; Agero/CallRequest.OwnerUserId is set to {2}",
                               dCompanyId, LogLevel.Warn,
                               sourceMessage.Message.MessageId,
                               cr.CallRequestId.ToString(),
                               cr.OwnerUserId);

                            cr.DispatchEntryId = fe.Id;
                            await cr.Save();

                            qi.CallRequestId = cr.CallRequestId;
                            DigitalDispatchService.LogAction(qi);

                            await cr.UpdateStatus(CallRequestStatus.Accepted, po: fe.PurchaseOrderNumber);

                            logger.Info(MasterAccountTypes.Agero,
                                "CallAccepted",
                                "Created new towbook call for Agero",
                                jsonObj.VendorId.ToString(),
                                jsonObj.FacilityId.ToString(),
                                jsonObj.DispatchRequestNumber.ToString(),
                                qi.CompanyId,
                                new
                                {
                                    waypoints = fe.Waypoints.Select(o => new { o.Address, o.Latitude, o.Longitude }),
                                },
                                callId: fe.Id,
                                callRequestId: cr?.CallRequestId,
                                queueItemId: qi.QueueItemId,
                                poNumber: fe.PurchaseOrderNumber,
                                callNumber: fe.CallNumber);
                        }
                    }


                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    break;
                #endregion

                #region Call Cancelled event


                case DigitalDispatchActionQueueItemType.IncomingCallCancelled:
                    ad = AgeroDispatch.GetByDispatchId(jsonObj.DispatchRequestNumber, jsonObj.VendorId);
                    CallRequest cancelRequest;
                    if (ad == null)
                    {
                        logger.LogEvent("Agero/{0}/{3}/IncomingCallCanceled: Couldn't find a AgeroDispatch with ID of {2} - it was probably accepted via the phone.  Body = {1}",
                            qi.CompanyId, LogLevel.Warn, sourceMessage.Message.MessageId, qi.ToJson(), jsonObj.DispatchRequestNumber, "Agero");

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                        break;
                    }
                    else
                    {
                        cancelRequest = CallRequest.GetById(ad.CallRequestId);
                    }

                    await cancelRequest.UpdateStatus(CallRequestStatus.Cancelled);

                    qi.CallRequestId = cancelRequest.CallRequestId;

                    DigitalDispatchService.LogAction(qi);

                    if (cancelRequest.DispatchEntryId.GetValueOrDefault() > 0)
                    {
                        var en = Entry.GetByIdNoCache(cancelRequest.DispatchEntryId.Value);

                        if (ShouldAllowCancel(en))
                        {
                            await en.Cancel("Cancelled by Agero digitally.",
                                new AuthenticationToken()
                                {
                                    UserId = 1,
                                    ClientVersionId = MyClientVersionId
                                }, "127.0.0.1");
                        }
                    }

                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    break;

                #endregion

                #region Call Rejected event - generally, bad ETA.
                case DigitalDispatchActionQueueItemType.IncomingCallRejected:
                    CallRequest rcr = null;
                    var rejectedDisp = AgeroDispatch.GetByDispatchId(jsonObj.DispatchRequestNumber, jsonObj.VendorId);

                    if (rejectedDisp == null)
                    {
                        logger.LogEvent("{0}: Couldn't find a AgeroDispatch with ID of {2} - it was probably accepted via the phone.  Body = {1}",
                            qi.CompanyId,
                            LogLevel.Warn,
                            (sourceMessage.Message != null ? sourceMessage.Message.MessageId : "messageId is null"),
                            qi.ToJson(), jsonObj.DispatchRequestNumber);
                    }
                    else
                    {
                        rcr = CallRequest.GetById(rejectedDisp.CallRequestId);

                        if (rcr != null)
                        {
                            if (rcr.CompanyId != qi.CompanyId)
                                rcr = null;
                        }
                    }

                    if (rcr != null)
                    {
                        if (jsonObj.NotificationCode == (int)AgeroNotificationType.JobRefused)
                            await rcr.UpdateStatus(CallRequestStatus.Rejected);
                        else if (jsonObj.NotificationCode == (int)AgeroNotificationType.EtaRejected)
                            await rcr.UpdateStatus(CallRequestStatus.RejectedByMotorClub);
                        else
                            await rcr.UpdateStatus(CallRequestStatus.UnknownError);

                        qi.CallRequestId = rcr.CallRequestId;
                        DigitalDispatchService.LogAction(qi);
                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    }
                    break;
                #endregion


                default:
                    await sourceMessage.DeadLetterAsync();

                    logger.LogEvent("Queue item doesn't have a Type set that we have a implementation written for... MessageId {0}, Body = {1}",
                        qi.CompanyId, LogLevel.Error, sourceMessage.Message.MessageId, qi.ToJson());
                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                    return false;
            }

            await sourceMessage.CompleteAsync();
            return true;
        }


        public static int ClientIdToMasterAccountId(string clientId)
        {
            switch (clientId)
            {
                case "GCO":
                case "GCOAPI":
                    return MasterAccountTypes.Geico;
                case "TSLA":
                    return MasterAccountTypes.Tesla;
                case "USAC":
                    return MasterAccountTypes.Usac;
                case "RDAM":
                    return MasterAccountTypes.RoadAmerica;
                case "RP":
                    return MasterAccountTypes.RoadsideProtect;
                case "ADS":
                    return MasterAccountTypes.AlliedDispatch;
            }

            return 0;
        }

        private async Task<bool> HandleDdxmlIncomingMessage(
            DigitalDispatchActionQueueItem qi,
            AllstateMessage jsonObj,
            ProcessMessageEventArgs sourceMessage)
        {
            if (qi?.CompanyId == 155249 &&
                qi.Type != DigitalDispatchActionQueueItemType.IncomingCallReceived)
            {
                return await GatewayForwardInboundQuest(qi, sourceMessage);
            }

            var account = await Account.GetByIdAsync(qi.AccountId.Value);
            var ma = await MasterAccount.GetByIdAsync(account.MasterAccountId);
            string masterAccountName = ma?.Name ?? "UNKNOWN";

            switch (qi.Type)
            {
                case DigitalDispatchActionQueueItemType.IncomingCallReceived:
                    var dspMsg = Allstate.DDMessage.FromXml(jsonObj.Message, typeof(Allstate.DSPMessageBody));

                    var rcr = await CreateCallRequest(dspMsg, MotorClubName.DDXML, true, account.MasterAccountId);

                    qi.CallRequestId = rcr.CallRequestId;
                    DigitalDispatchService.LogAction(qi);

                    logger.Log(LogLevel.Info, "DDXML/{3}/{0}: New call request created for . Type = {1}, CallRequestId = {2}",
                        qi.QueueItemId, qi.Type, rcr.CallRequestId, masterAccountName);
                    break;

                case DigitalDispatchActionQueueItemType.IncomingCallRejected:
                    {
                        var asd = AllstateDispatch.GetByResponseId(jsonObj.ContractorId, jsonObj.DispatchRequestNumber);
                        if (asd != null)
                        {
                            var cr = CallRequest.GetById(asd.CallRequestId);

                            var rspMsg = Allstate.DDMessage.FromXml(jsonObj.Message, typeof(Allstate.RSPMessage));

                            var rsp = ((Allstate.RSPMessage)rspMsg.DDContent);
                            if (rsp.MotorClubResponseCode == "1")
                            {
                                await cr.UpdateStatus(CallRequestStatus.RejectedByMotorClub);

                                qi.CallRequestId = cr.CallRequestId;

                                DigitalDispatchService.LogAction(qi);

                                logger.LogEvent("DDXML/{0}/{1}/RejectedByMotorClub/{2}: {0} rejected call. ",
                                    qi.CompanyId, LogLevel.Info, masterAccountName, sourceMessage.Message.MessageId, rspMsg.DDMessageHeader.ResponseID);

                                break;
                            }
                        }
                    }
                    break;

                case DigitalDispatchActionQueueItemType.IncomingCallAccepted:
                    {
                        logger.Log(LogLevel.Info, "DDXML/{0}/{2}: Incoming Call Accepted... XML = {1} ",
                            qi.QueueItemId,
                            jsonObj.Message,
                            masterAccountName);

                        Allstate.DDMessage rspMsg;
                        try
                        {
                            rspMsg = Allstate.DDMessage.FromXml(jsonObj.Message, typeof(Allstate.RSPMessage));
                        }
                        catch (Exception e)
                        {
                            logger.Log(LogLevel.Error, "DDXML/{0}/{3}/IncomingCallAccepted: Error while executing FromXml.. Exception = {2}, XML = {1} ",
                                qi.QueueItemId,
                                jsonObj.Message,
                                e.ToJson(),
                                masterAccountName);

                            await sourceMessage.DeadLetterAsync("DeadLettered because of exception", e.ToJson() + ",\n\n" + jsonObj.Message);
                            return false;
                        }

                        CallRequest cr = null;
                        AllstateDispatch asd = AllstateDispatch.GetByResponseId(jsonObj.ContractorId, jsonObj.DispatchRequestNumber);

                        if (asd == null)
                        {
                            logger.LogEvent("DDXML/{3}/{2}: Couldn't find an AllstateDispatch with ID of {2} - it was probably accepted via the phone.  Body = {1}",
                                qi.CompanyId, LogLevel.Warn, sourceMessage.Message.MessageId, qi.ToJson(), jsonObj.DispatchRequestNumber, masterAccountName);
                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                            break;
                        }
                        else
                        {
                            cr = await CallRequest.GetByIdAsync(asd.CallRequestId);
                        }

                        if (rspMsg != null)
                        {
                            var asDispatchDetails = (Allstate.DSPMessageBody)Allstate.DDMessage.FromXml(asd.CallXml,
                                    typeof(Allstate.DSPMessageBody)).DDContent;

                            var rsp = ((Allstate.RSPMessage)rspMsg.DDContent);
                            var raPONumber = rsp.AuthorizationNumber;

                            asd.PurchaseOrderNumber = raPONumber;
                            asd.Save();



                            logger.LogEvent("DDXML/{0}/{1}/IncomingAccepted/{2}/PO{3}: {4}",
                                qi.CompanyId, LogLevel.Info, masterAccountName, sourceMessage.Message.MessageId, rspMsg.DDMessageHeader.ResponseID, asd.PurchaseOrderNumber, rsp.ToJson());

                            var fe = await DistributedLock.ForAsync<Entry>("DDXML_" + masterAccountName, raPONumber, 10000,
                                lockAcquired: async delegate ()
                                {
                                    Entry e = null;
                                    e = await Entry.GetByPurchaseOrderNumberAsync(qi.AccountId.Value, raPONumber);

                                    if (e == null)
                                    {
                                        foreach (var a in await Account.GetByCompanyAsync(await Company.Company.GetByIdAsync(qi.CompanyId.Value), AccountType.MotorClub))
                                        {
                                            e = await Entry.GetByPurchaseOrderNumberAsync(a.Id, raPONumber);
                                            if (e != null)
                                                break;
                                        }
                                    }

                                    if (e == null)
                                    {
                                        e = new Entry();
                                        e.CompanyId = qi.CompanyId.Value;
                                    }
                                    else
                                    {
                                        logger.LogEvent("{0}: Found existing towbook call for PO {1}/Response ID{2}... Call #{3}... we're going to update this one.",
                                            qi.CompanyId, LogLevel.Warn, sourceMessage.Message.MessageId, asd.PurchaseOrderNumber, rspMsg.DDMessageHeader.ResponseID, e.CallNumber);
                                    }

                                    e.AccountId = qi.AccountId.Value;
                                    e.PurchaseOrderNumber = raPONumber;

                                    #region Handle MoveRoadsideCallsToAccountId to move roadside calls to their own AccountId 

                                    if (string.IsNullOrWhiteSpace(cr.TowDestination))
                                    {
                                        var akv = AccountKeyValue.GetByAccount(e.CompanyId,
                                            e.AccountId,
                                            Provider.Towbook.ProviderId,
                                            "MoveRoadsideCallsToAccountId"
                                            ).FirstOrDefault();
                                        if (akv != null)
                                        {
                                            int tempAccId = 0;
                                            if (int.TryParse(akv.Value, out tempAccId))
                                            {
                                                var tempAcc = await Account.GetByIdAsync(tempAccId);
                                                if (tempAcc != null)
                                                {
                                                    e.AccountId = tempAcc.Id;
                                                }
                                            }
                                        }
                                    }
                                    #endregion



                                    EntryAsset asset = null;
                                    if (e.Assets != null)
                                        asset = e.Assets.FirstOrDefault();

                                    if (asset == null)
                                        asset = new EntryAsset() { BodyTypeId = 1 };

                                    if (asset.BodyTypeId == 0)
                                        asset.BodyTypeId = 1;

                                    if (!string.IsNullOrEmpty(asDispatchDetails.VehicleInfo.Year))
                                        asset.Year = Convert.ToInt32(asDispatchDetails.VehicleInfo.Year);

                                    asset.Make = GetManufacturerByName(asDispatchDetails.VehicleInfo.Make);
                                    asset.Model = GetModelByName(asDispatchDetails.VehicleInfo.Model);

                                    var colorId = GetColorIdByName(asDispatchDetails.VehicleInfo.Color);
                                    if (colorId != 0)
                                        asset.ColorId = colorId;

                                    asset.Vin = asDispatchDetails.VehicleInfo.VIN;
                                    asset.LicenseNumber = asDispatchDetails.VehicleInfo.Lic;
                                    asset.LicenseState = asDispatchDetails.VehicleInfo.State;

                                    var pickup = e.Waypoints.Where(o => o.Title == "Pickup").FirstOrDefault();
                                    var dest = e.Waypoints.Where(o => o.Title == "Destination").FirstOrDefault();

                                    e.Notes = "";

                                    if (asDispatchDetails.IncAddr != null)
                                    {
                                        e.TowSource = asDispatchDetails.IncAddr.ToString();
                                        if (pickup == null)
                                        {
                                            pickup = new EntryWaypoint() { Title = "Pickup", Position = 1 };
                                            e.Waypoints.Add(pickup);
                                        }

                                        pickup = await asDispatchDetails.IncAddr.ToWaypoint(pickup);
                                    }

                                    if (asDispatchDetails.DestAddr != null)
                                    {
                                        e.TowDestination = asDispatchDetails.DestAddr.ToString();
                                        if (dest == null && !string.IsNullOrWhiteSpace(e.TowDestination))
                                        {
                                            dest = new EntryWaypoint() { Title = "Destination", Position = 2 };
                                            e.Waypoints.Add(dest);
                                        }
                                        dest = await asDispatchDetails.DestAddr.ToWaypoint(dest);
                                    }

                                    if (asDispatchDetails.JobInfo != null && !string.IsNullOrEmpty(asDispatchDetails.JobInfo.TimeStamp))
                                    {
                                        logger.LogEvent("{0}/{1}: Attempting to convert date of {2}",
                                            qi.CompanyId, LogLevel.Info, sourceMessage.Message.MessageId, rspMsg.DDMessageHeader.ResponseID, asDispatchDetails.JobInfo.TimeStamp);

                                        if (e.CreateDate == DateTime.MinValue)
                                            e.CreateDate = Allstate.DSPMessageJobInfo.ConvertAllstateTimestampToDateTime(asDispatchDetails.JobInfo.TimeStamp);

                                        if (asd.Eta != null)
                                        {
                                            e.ArrivalETA = Allstate.DSPMessageJobInfo.ConvertAllstateTimestampToDateTime(asDispatchDetails.JobInfo.TimeStamp).AddMinutes(asd.Eta.Value);
                                        }
                                        else
                                        {
                                            logger.LogEvent("{0}/{1}: ETA is missing!",
                                            qi.CompanyId, LogLevel.Error, sourceMessage.Message.MessageId, rspMsg.DDMessageHeader.ResponseID, asDispatchDetails.JobInfo.TimeStamp);
                                        }
                                    }

                                    AttachDriveTypeToCallNotesFromVin(e, asset);

                                    #region Reason
                                    e.ReasonId = await ReasonHelper.DetermineReasonId(e.Account.MasterAccountId, qi.CompanyId.Value, cr.ServiceNeeded);
                                    if (e.ReasonId == 1635)
                                        e.Notes += "Service Needed: " + cr.ServiceNeeded + "\n";
                                    #endregion

                                    // ToDo: Implement benefit parsing
                                    #region coverage amount
                                    if (rsp != null && !String.IsNullOrWhiteSpace(rsp.BenefitMileLimit))
                                    {
                                        if (!e.Attributes.ContainsKey(Extric.Towbook.Dispatch.AttributeValue.BUILTIN_MOTORCLUB_COVERAGELIMIT))
                                            e.Attributes.Add(Extric.Towbook.Dispatch.AttributeValue.BUILTIN_MOTORCLUB_COVERAGELIMIT,
                                                new Extric.Towbook.Dispatch.AttributeValue(e, Extric.Towbook.Dispatch.AttributeValue.BUILTIN_MOTORCLUB_COVERAGELIMIT)
                                                {
                                                    Value = rsp.BenefitMileLimit + " miles"
                                                });
                                        else
                                            e.Attributes[Extric.Towbook.Dispatch.AttributeValue.BUILTIN_MOTORCLUB_COVERAGELIMIT].Value = rsp.BenefitMileLimit + " miles";
                                    }

                                    if (rsp != null && !String.IsNullOrWhiteSpace(rsp.BenefitDollarLimit) && rsp.BenefitDollarLimit != "0.00")
                                    {
                                        if (!e.Attributes.ContainsKey(Extric.Towbook.Dispatch.AttributeValue.BUILTIN_MOTORCLUB_COVERAGELIMIT))
                                            e.Attributes.Add(Extric.Towbook.Dispatch.AttributeValue.BUILTIN_MOTORCLUB_COVERAGELIMIT,
                                                new Extric.Towbook.Dispatch.AttributeValue(e, Extric.Towbook.Dispatch.AttributeValue.BUILTIN_MOTORCLUB_COVERAGELIMIT)
                                                {
                                                    Value = rsp.BenefitDollarLimit + " dollars"
                                                });
                                        else
                                            e.Attributes[Extric.Towbook.Dispatch.AttributeValue.BUILTIN_MOTORCLUB_COVERAGELIMIT].Value += " | " + rsp.BenefitDollarLimit + " dollars";
                                    }





                                    if (rsp != null && !String.IsNullOrWhiteSpace(rsp.Remarks))
                                    {
                                        if (!e.Attributes.ContainsKey(Extric.Towbook.Dispatch.AttributeValue.BUILTIN_MOTORCLUB_COVERAGELIMIT))
                                            e.Attributes.Add(Extric.Towbook.Dispatch.AttributeValue.BUILTIN_MOTORCLUB_COVERAGELIMIT,
                                                new Extric.Towbook.Dispatch.AttributeValue(e, Dispatch.AttributeValue.BUILTIN_MOTORCLUB_COVERAGELIMIT)
                                                {
                                                    Value = rsp.Remarks
                                                });
                                        else
                                            e.Attributes[Extric.Towbook.Dispatch.AttributeValue.BUILTIN_MOTORCLUB_COVERAGELIMIT].Value += " | " + rsp.Remarks;
                                    }

                                    #endregion

                                    // ToDo: Implement notes parsing
                                    #region notes

                                    if (!string.IsNullOrWhiteSpace(asDispatchDetails?.JobInfo?.JobDesc))
                                    {
                                        e.Notes = (e.Notes?.Trim('\n') ?? "") + "\nJob Description: " + asDispatchDetails.JobInfo.JobDesc;
                                    }

                                    if (!String.IsNullOrWhiteSpace(asDispatchDetails.IncAddr.Landmark))
                                    {
                                        e.Notes += asDispatchDetails.IncAddr.Landmark;
                                    }

                                    if (!String.IsNullOrWhiteSpace(asDispatchDetails.VehicleInfo.AdditionalInfo))
                                    {
                                        e.Notes += "\nVehicle Notes: " + asDispatchDetails.VehicleInfo.AdditionalInfo;
                                    }

                                    #endregion

                                    if (cr != null)
                                    {
                                        if (cr.OwnerUserId == null)
                                            cr.OwnerUserId = 1;

                                        e.CallRequestId = cr.CallRequestId;

                                        e.OwnerUserId = cr.OwnerUserId.GetValueOrDefault(0);
                                    }
                                    else
                                        e.OwnerUserId = 1;

                                    if (e.Assets == null || e.Assets.Count == 0)
                                    {
                                        e.Assets = new System.Collections.ObjectModel.Collection<EntryAsset>();
                                        e.Assets.Add(asset);
                                    }

                                    await ApplyRoundRobinDispatcherLogicAsync(qi, e, jsonObj.DispatchRequestNumber, jsonObj.ContractorId, null);

                                    var customerName = asDispatchDetails.AccountInfo.CustFirstName + " " + asDispatchDetails.AccountInfo.CustLastName;
                                    EntryContact c = e.Contacts.Where(o =>
                                        o.Name == customerName ||
                                        Core.FormatPhone(o.Phone) == Core.FormatPhone(asDispatchDetails.AccountInfo.CallBackPhone))
                                        .FirstOrDefault();

                                    bool newContact = false;
                                    if (c == null)
                                    {
                                        c = new EntryContact() { Name = customerName };
                                        e.Contacts.Add(c);

                                        newContact = true;
                                    }

                                    c.Phone = Core.FormatPhone(asDispatchDetails.AccountInfo.CallBackPhone);

                                    if (ma.Id == MasterAccountTypes.Nac)
                                    {
                                        if (!string.IsNullOrEmpty(rsp.MemberCallBackNum))
                                        {
                                            if (rsp.Remarks != null)
                                            {
                                                const string ph = "Policy Holder:";
                                                if (rsp.Remarks.Contains(ph))
                                                {
                                                    var name = rsp.Remarks.Substring(rsp.Remarks.IndexOf(ph) + ph.Length);
                                                    if (!name.Contains('\n'))
                                                        c.Name = name;
                                                }
                                            }

                                            c.Phone = Core.FormatPhone(rsp.MemberCallBackNum);
                                        }
                                    }

                                    await e.Save();

                                    if (newContact)
                                        await CheckForRoadsideFeatureAndAutoInvite(e, c);

                                    await AutoDispatch.AutoDispatchServiceBusHandler.Send(e);

                                    return e;
                                },
                                alreadyLocked: async delegate ()
                                {
                                    logger.LogEvent("{0}/CR{1}: Lock already exists for {2}:{3}... pausing 250ms",
                                        qi.CompanyId, LogLevel.Warn,
                                        sourceMessage.Message.MessageId,
                                        (cr != null ? cr.CallRequestId.ToString() : "NULL"),
                                        qi.AccountId.Value,
                                        rspMsg.DDMessageHeader.ResponseID);

                                    await System.Threading.Tasks.Task.Delay(250);

                                    return true;
                                });

                            if (fe == null)
                            {
                                logger.LogEvent("{0}: Creation of call failed; deadlettering",
                                    qi.CompanyId, LogLevel.Error, sourceMessage.Message.MessageId, qi.ToJson());

                                await sourceMessage.CompleteAsync();

                                DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                            }
                            else
                            {
                                if (cr != null)
                                {
                                    cr.DispatchEntryId = fe.Id;
                                    await cr.Save();

                                    qi.CallRequestId = cr.CallRequestId;
                                    DigitalDispatchService.LogAction(qi);

                                    await cr.UpdateStatus(CallRequestStatus.Accepted, po: fe.PurchaseOrderNumber);

                                    logger.Info(fe.Account?.MasterAccountId ?? jsonObj.MasterAccountId,
                                        "CallAccepted",
                                        "Created new call for " + MasterAccountTypes.GetName(fe.Account?.MasterAccountId ?? jsonObj.MasterAccountId),
                                        jsonObj.ContractorId.ToString(), null, jsonObj.DispatchRequestNumber.ToString(), qi.CompanyId,
                                        new
                                        {
                                            waypoints = fe.Waypoints.Select(o => new { o.Address, o.Latitude, o.Longitude }),
                                        },
                                        callId: fe.Id,
                                        callRequestId: cr?.CallRequestId,
                                        queueItemId: qi.QueueItemId,
                                        poNumber: fe.PurchaseOrderNumber,
                                        callNumber: fe.CallNumber);

                                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                                }
                            }

                        }
                        else
                        {
                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);

                            logger.LogEvent("DDXML/{0}/{1}: Creation of call failed; deadlettering",
                                qi.CompanyId, LogLevel.Error, sourceMessage.Message.MessageId, qi.ToJson(), masterAccountName);

                            await sourceMessage.CompleteAsync();
                        }
                    }
                    break;

                case DigitalDispatchActionQueueItemType.IncomingCallCancelled:
                    var cnlMsg = Allstate.DDMessage.FromXml(jsonObj.Message, typeof(Allstate.CNLMessage));

                    CallRequest cancelRequest = null;
                    AllstateDispatch asdCancel = AllstateDispatch.GetByResponseId(jsonObj.ContractorId, jsonObj.DispatchRequestNumber);
                    if (asdCancel == null)
                    {
                        logger.LogEvent("DDXML/{0}/{3}/IncomingCallCanceled: Couldn't find a AllstateDispatch with ID of {2} - it was probably accepted via the phone.  Body = {1}",
                            qi.CompanyId, LogLevel.Warn, sourceMessage.Message.MessageId, qi.ToJson(), jsonObj.DispatchRequestNumber, masterAccountName);
                        break;
                    }
                    else
                    {
                        cancelRequest = CallRequest.GetById(asdCancel.CallRequestId);
                    }

                    await cancelRequest.UpdateStatus(CallRequestStatus.Cancelled);

                    qi.CallRequestId = cancelRequest.CallRequestId;

                    DigitalDispatchService.LogAction(qi);

                    if (cancelRequest.DispatchEntryId.GetValueOrDefault() > 0)
                    {
                        var msgText = ((Allstate.CNLMessage)cnlMsg.DDContent).Response;
                        var en = Entry.GetByIdNoCache(cancelRequest.DispatchEntryId.Value);

                        if (ShouldAllowCancel(en))
                        {
                            await en.Cancel("Cancelled by " +
                                (await MasterAccount.GetByIdAsync((Account.GetById(cancelRequest.AccountId).MasterAccountId)))?.Name + ": " +
                                msgText,
                                new AuthenticationToken()
                                {
                                    UserId = 1,
                                    ClientVersionId = MyClientVersionId
                                }, "127.0.0.1");
                        }

                    }


                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    break;

                case DigitalDispatchActionQueueItemType.IncomingCallStatusInquiry:
                    break;
            }

            await sourceMessage.CompleteAsync();
            return true;
        }

        #endregion

        #region Outgoing Handlers

        public static async Task<bool> HandleIsscQueueOutgoingMessage(DigitalDispatchActionQueueItem qi, dynamic jsonObj, ProcessMessageEventArgs sourceMessage)
        {
            logger.LogEvent("Processing request for ISSC Id = {0}, Type = {1}", qi.CompanyId, LogLevel.Info,
                properties: ToDictionary("queueItemId", qi.QueueItemId),
                qi.QueueItemId, qi.Type);

            int configId = 0;

            if (!sourceMessage.Message.ApplicationProperties.ContainsKey("IsscConfigId"))
            {
                CallRequest acceptRequest = null;

                if (jsonObj.Id != null)
                    acceptRequest = CallRequest.GetById(Convert.ToInt32(jsonObj.Id));
                else if (jsonObj.CallRequestId != null)
                    acceptRequest = CallRequest.GetById(Convert.ToInt32(jsonObj.CallRequestId));

                if (acceptRequest != null)
                {
                    var tempIsscDispatch = IsscDispatch.GetByCallRequestId(acceptRequest.CallRequestId);

                    if (tempIsscDispatch != null)
                    {
                        var isp = IsscProvider.GetByContractorId(tempIsscDispatch.ContractorId, tempIsscDispatch.ClientId, tempIsscDispatch.LocationId);

                        if (isp != null)
                            configId = isp.ConfigId;
                    }
                }
                else if (qi.AccountId != null)
                {
                    var isp = IsscProvider.GetByAccountId(qi.AccountId.Value).FirstOrDefault();
                    if (isp != null)
                        configId = isp.ConfigId;
                }

                if (configId == 0)
                {
                    logger.LogEvent("{0}: Missing Required Property of IsscConfigId; Forcing to Live. Body = {1}", qi.CompanyId, LogLevel.Error, 
                        properties: ToDictionary("queueItemId", qi.QueueItemId), 
                        sourceMessage.Message.MessageId, qi.ToJson(true));

                    configId = IsscConfig.GetByEnvironmentType(EnvironmentType.Live).IsscConfigId;
                }
            }
            else
            {
                configId = Convert.ToInt32(sourceMessage.Message.ApplicationProperties["IsscConfigId"]);
            }

            var conf = IsscConfig.GetById(configId);
            var conn = IsscConnection.Get(conf);

            CallRequest callRequest = null;
            IsscDispatch isscDispatch = null;

            if (qi.Type != DigitalDispatchActionQueueItemType.OutgoingConnect &&
                qi.Type != DigitalDispatchActionQueueItemType.InternalVerifyLogin &&
                qi.Type != DigitalDispatchActionQueueItemType.OutgoingLogoff &&
                qi.Type != DigitalDispatchActionQueueItemType.OutgoingRegister &&
                qi.Type != DigitalDispatchActionQueueItemType.OutgoingLogin &&
                qi.Type != DigitalDispatchActionQueueItemType.InternalVerifyConnection)
            {

                if (jsonObj.Id != null)
                {
                    // dont look up this stuff if its a connection/login event.
                    callRequest = CallRequest.GetById(Convert.ToInt32(jsonObj.Id));
                }
                else if (jsonObj.CallRequestId != null)
                {
                    callRequest = CallRequest.GetById(Convert.ToInt32(jsonObj.CallRequestId));
                }

                if (callRequest == null)
                {
                    await sourceMessage.DeadLetterMessageAsync(sourceMessage.Message, "Couldn't '" + qi.Type.ToString() + "' for ISSC call: CallRequest is null. Requested ID: " + jsonObj.Id, JsonExtensions.ToJson(jsonObj));
                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                    return false;
                }

                isscDispatch = IsscDispatch.GetByCallRequestId(callRequest.CallRequestId);

                if (isscDispatch == null)
                {
                    // randomly this returns null... but I dont know how, its in the database, when we look in the database later after reviewing the logs
                    // the row exists. 
                    Thread.Sleep(250);
                    isscDispatch = IsscDispatch.GetByCallRequestId(callRequest.CallRequestId);

                    if (isscDispatch == null)
                    {
                        logger.LogEvent("{0}: Couldn't find IsscDispatch object with CallRequestId {1}. Tried querying for it twice. [BUG]",
                            qi.CompanyId,
                            LogLevel.Fatal,
                            sourceMessage.Message.MessageId,
                            callRequest.CallRequestId);

                        await sourceMessage.DeadLetterAsync("Couldn't perform '" + qi.Type.ToString() + "' on CallRequest", "Failed to find IsscDispatch object with CallRequestId of " + callRequest.CallRequestId);
                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                        return false;
                    }
                }
            }

            if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingAcceptCall ||
                qi.Type == DigitalDispatchActionQueueItemType.OutgoingRejectCall ||
                qi.Type == DigitalDispatchActionQueueItemType.OutgoingRequestPhoneCall)
            {
                if (callRequest != null && (callRequest.HasAlreadyRespondedTo() || isscDispatch.ETA.GetValueOrDefault() != 0))
                {
                    await sourceMessage.CompleteAsync();
                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    return true;
                }
            }

            switch (qi.Type)
            {

                case DigitalDispatchActionQueueItemType.InternalAcceptMissedCall:
                    Entry missed = null;
                    try
                    {
                        missed = await IsscCreateCall(qi, jsonObj, sourceMessage, isscDispatch, callRequest);
                        await callRequest.UpdateStatus(CallRequestStatus.Accepted, po: missed.PurchaseOrderNumber);

                    }
                    catch (Exception e)
                    {
                        logger.LogEvent("{0}: InternalAcceptMissedCall  failed for CallRequestId {1}. {2}",
                        qi.CompanyId,
                        LogLevel.Fatal,
                        sourceMessage.Message.MessageId,
                        callRequest.CallRequestId,
                        e.ToString());
                    }

                    qi.CallRequestId = callRequest.CallRequestId;
                    DigitalDispatchService.LogAction(qi);

                    break;

                case DigitalDispatchActionQueueItemType.OutgoingAcceptCall:
                    MasterAccountReason acceptReason = null;
                    
                    if (jsonObj.MasterAccountReasonId != null)
                        acceptReason = MasterAccountReason.GetById(Convert.ToInt32(jsonObj.MasterAccountReasonId));

                    isscDispatch.ETA = Convert.ToInt32(jsonObj.Eta);
                    isscDispatch.Save();

                    new IsscRestClient(conf).DispatchResponse(
                        clientId: isscDispatch.ClientId,
                        contractorId: isscDispatch.ContractorId,
                        locationId: isscDispatch.LocationId,
                        dispatchId: isscDispatch.DispatchId,
                        jobId: isscDispatch.JobId,
                        serviceProviderResponse: CallRequestStatus.Accepted,
                        providerContactName: jsonObj.FullName.ToString(),
                        providerPhoneNumber: jsonObj.PhoneNumber.ToString(),
                        eta: Convert.ToInt32(jsonObj.Eta ?? 0),
                        etaExplanation: acceptReason?.Code ?? "",
                        rejectDescription: string.Empty,
                        source: IsscRestClient.DispatchSource.Manual);

                    callRequest.OwnerUserId = Convert.ToInt32(jsonObj.OwnerUserId);
                    await callRequest.Save();

                    qi.CallRequestId = callRequest.CallRequestId;
                    DigitalDispatchService.LogAction(qi);

                    break;

                case DigitalDispatchActionQueueItemType.OutgoingRejectCall:
                    var rejectReason = MasterAccountReason.GetById((int)jsonObj.MasterAccountReasonId);

                    new IsscRestClient(conf).DispatchResponseReject(
                        clientId: isscDispatch.ClientId,
                        contractorId: isscDispatch.ContractorId,
                        locationId: isscDispatch.LocationId,
                        dispatchId: isscDispatch.DispatchId,
                        jobId: isscDispatch.JobId,
                        rejectionDescription: rejectReason?.Code ?? "",
                        providerContactName: jsonObj.FullName.ToString(),
                        providerPhoneNumber: jsonObj.PhoneNumber.ToString(),
                        source: IsscRestClient.DispatchSource.Manual);

                    await callRequest.UpdateStatus(CallRequestStatus.Rejected, (int)jsonObj.OwnerUserId);

                    qi.CallRequestId = callRequest.CallRequestId;
                    DigitalDispatchService.LogAction(qi);

                    break;

                case DigitalDispatchActionQueueItemType.OutgoingRequestPhoneCall:
                    Console.WriteLine(JsonExtensions.ToJson(jsonObj, true));

                    if (callRequest.Status == CallRequestStatus.None ||
                        callRequest.Status == CallRequestStatus.RequestingPhoneCall)
                    {
                        bool automated = qi.ScheduledDate != null;

                        var phoneReason = MasterAccountReason.GetById(Convert.ToInt32(jsonObj.MasterAccountReasonId));

                        if (automated && (isscDispatch.ClientId == "GCO" || isscDispatch.ClientId == "GCOAPI" || isscDispatch.ClientId == "RDAM"))
                        {
                            await callRequest.UpdateStatus(CallRequestStatus.PhoneCallRequestFailed);
                        }
                        else
                        {
                            new IsscRestClient(conf).DispatchResponse(
                                clientId: isscDispatch.ClientId,
                                contractorId: isscDispatch.ContractorId,
                                locationId: isscDispatch.LocationId,
                                dispatchId: isscDispatch.DispatchId,
                                jobId: isscDispatch.JobId,
                                serviceProviderResponse: CallRequestStatus.PhoneCallRequested,
                                providerContactName: jsonObj.FullName.ToString(),
                                providerPhoneNumber: jsonObj.PhoneNumber.ToString(),
                                eta: 0,
                                etaExplanation: string.Empty,
                                rejectDescription: phoneReason?.Code ?? "",
                                source: (automated ? IsscRestClient.DispatchSource.Automatic : IsscRestClient.DispatchSource.Manual));

                            if (automated)
                            {
                                await callRequest.UpdateStatus(CallRequestStatus.AutomatedPhoneCallRequested);
                            }
                            else
                            {
                                await callRequest.UpdateStatus(CallRequestStatus.PhoneCallRequested);
                            }
                        }

                        qi.CallRequestId = callRequest.CallRequestId;
                        DigitalDispatchService.LogAction(qi);
                    }

                    break;

                case DigitalDispatchActionQueueItemType.OutgoingStatusUpdate:
                    if (Core.GetAppSetting("Towbook.DispatchService.Issc.DisableOutgoingStatusUpdates") == "1" ||
                        Extric.Towbook.Core.GetRedisValue("ops/mcds/issc_statusUpdates/disable") == "1")
                    {
                        logger.Log(LogLevel.Warn, $"ISSC status updates are disabled.");

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                        await sourceMessage.CompleteAsync();
                        return false;
                    }

                    IsscRestClient.DispatchStatusType newStatus = IsscRestClient.DispatchStatusType.None;
                    int newStatusId = (int)jsonObj.NewStatusId;

                    if (newStatusId == Status.Dispatched.Id)
                        newStatus = IsscRestClient.DispatchStatusType.DriverAssigned;
                    else if (newStatusId == Status.EnRoute.Id)
                        newStatus = IsscRestClient.DispatchStatusType.Enroute;
                    else if (newStatusId == Status.Completed.Id)
                        newStatus = IsscRestClient.DispatchStatusType.ServiceComplete;
                    else if (newStatusId == Status.BeingTowed.Id)
                        newStatus = IsscRestClient.DispatchStatusType.EnrouteToDestination;
                    else if (newStatusId == Status.DestinationArrival.Id)
                        newStatus = IsscRestClient.DispatchStatusType.OnSceneDestination;
                    else if (newStatusId == Status.AtSite.Id)
                        newStatus = IsscRestClient.DispatchStatusType.OnScene;

                    double? lat = (double?)jsonObj.Latitude;
                    double? lng = (double?)jsonObj.Longitude;

                    string driverId = "unknown";
                    string driverPhone = "";
                    if (jsonObj.DriverId != null)
                        driverId = jsonObj.DriverId.ToString();

                    if (driverId != "unknown")
                    {
                        int tempDriverId;
                        if (int.TryParse(driverId, out tempDriverId))
                        {
                            var tempDriver = await Driver.GetByIdAsync(tempDriverId);
                            if (!string.IsNullOrWhiteSpace(tempDriver?.MobilePhone))
                                driverPhone = Core.FormatPhone(tempDriver.MobilePhone, true);
                        }
                    }

                    if (newStatus == IsscRestClient.DispatchStatusType.None)
                    {
                        logger.Log(LogLevel.Warn, "ISSC/{0}/CR{1}/V{2}: StatusType is set to {3}. No data sent.",
                            isscDispatch.DispatchId, isscDispatch.CallRequestId, isscDispatch.ContractorId, newStatus.ToString());
                        break;
                    }

                    new IsscRestClient(conf).DispatchStatus(
                        clientId: isscDispatch.ClientId,
                        contractorId: isscDispatch.ContractorId,
                        locationId: isscDispatch.LocationId,
                        dispatchId: isscDispatch.DispatchId,
                        eventTime: DateTime.Now,
                        source: IsscRestClient.DispatchSource.Manual,
                        statusType: newStatus,
                        latitude: lat,
                        longitude: lng,
                        gpsLastUpdateTime: DateTime.Now,
                        driverId: driverId,
                        driverMobilePhone: driverPhone);

                    if (lat != null)
                    {
                        logger.Info(ClientIdToMasterAccountId(isscDispatch.ClientId), "OutgoingStatusUpdate", "Sent STATUS_UPDATE with GPS",
                                isscDispatch.ContractorId, isscDispatch.LocationId, isscDispatch.DispatchId, qi.CompanyId,
                                new
                                {
                                    statusCode = newStatus.ToString(),
                                    latitude = lat,
                                    longitude = lng,
                                    driverId = driverId
                                },
                                callRequest.DispatchEntryId, callRequest.CallRequestId);
                    }
                    else
                    {
                        logger.Warn(ClientIdToMasterAccountId(isscDispatch.ClientId), "OutgoingStatusUpdate", "Sent STATUS_UPDATE without GPS",
                                isscDispatch.ContractorId, isscDispatch.LocationId, isscDispatch.DispatchId, qi.CompanyId,
                                new
                                {
                                    statusCode = newStatus.ToString(),
                                    latitude = lat,
                                    longitude = lng,
                                    driverId = driverId
                                },
                                callRequest.DispatchEntryId, callRequest.CallRequestId);
                    }
                    break;

                case DigitalDispatchActionQueueItemType.OutgoingExtendEta:
                    async Task<bool> doExtend()
                    {
                        CallRequest cr = CallRequest.GetById(Convert.ToInt32(jsonObj.CallRequestId));
                        if (cr == null)
                        {
                            logger.Log(LogLevel.Error, "ISSC/CR" + cr.CallRequestId + "/C" + cr.CompanyId + "/OutgoingExtendEta: Couldn't find CallRequestId.");
                            return true;
                        }

                        IsscDispatch ad = IsscDispatch.GetByCallRequestId(cr.CallRequestId);

                        if (ad == null)
                        {
                            logger.Log(LogLevel.Error, "ISSC/CR" + cr.CallRequestId + "/C" + cr.CompanyId + "/OutgoingExtendEta: Couldn't find AllstateDispatch.");


                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);

                            await sourceMessage.CompleteAsync();
                            return false;
                        }

                        MasterAccountReason goaReason = MasterAccountReason.GetById(Convert.ToInt32(jsonObj.ReasonId));

                        var goa_log_id = Guid.NewGuid().ToString("N");

                        Core.SetRedisValue("issc_channel:" + goa_log_id,
                            new
                            {
                                type = "ExtendETA",
                                callRequestId = cr.CallRequestId.ToString(),
                                eta = (int)jsonObj.Eta
                            }.ToJson());

                        int timeInMinutes = ad.ETA.GetValueOrDefault() + (int)jsonObj.Eta;

                        if (ad.ClientId == "GCOAPI" &&
                            Core.GetRedisValue("disable_issc_selfservice_2") != "1")
                        {
                            new IsscRestClient(conf).ServiceProviderInitiatedRequest2(
                                ad.ClientId, ad.ContractorId, ad.LocationId,
                                goa_log_id, "ExtendETA", ad.DispatchId,
                                reason: goaReason?.Code ?? "Extend ETA",
                                ETA: timeInMinutes);
                        }
                        else
                        {
                            new IsscRestClient(conf).ServiceProviderInitiatedRequest(
                                ad.ClientId, ad.ContractorId, ad.LocationId,
                                goa_log_id, IsscRestClient.DispatchStatusType.ExtendETA, ad.DispatchId,
                                reason: goaReason?.Code ?? "Extend ETA",
                                ETA: timeInMinutes);
                        }

                        logger.Log(LogLevel.Info, "ISSC/CR" + cr.CallRequestId + "/C" + cr.CompanyId + "/OutgoingExtendEta: Extended ETA" + goa_log_id);

                        qi.CallRequestId = cr.CallRequestId;
                        DigitalDispatchService.LogAction(qi);
                        return true;
                    }
                    if (!await doExtend())
                    {
                        return false;
                    }
                    break;

                case DigitalDispatchActionQueueItemType.OutgoingRequestGoa:
                    async Task<bool> doRequestGoa()
                    {
                        CallRequest cr = CallRequest.GetById(Convert.ToInt32(jsonObj.CallRequestId));
                        if (cr == null)
                        {
                            logger.Log(LogLevel.Error, "ISSC/CR" + cr.CallRequestId + "/C" + cr.CompanyId + "/OutgoingRequestGoa: Couldn't find CallRequestId.");
                            return true;
                        }

                        IsscDispatch ad = IsscDispatch.GetByCallRequestId(cr.CallRequestId);

                        if (ad == null)
                        {
                            logger.Log(LogLevel.Error, "ISSC/CR" + cr.CallRequestId + "/C" + cr.CompanyId + "/OutgoingRequestGoa: Couldn't find AllstateDispatch.");


                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);

                            await sourceMessage.CompleteAsync();
                            return false;
                        }

                        MasterAccountReason goaReason = MasterAccountReason.GetById(Convert.ToInt32(jsonObj.ReasonId));

                        var goa_log_id = Guid.NewGuid().ToString("N");

                        Core.SetRedisValue("issc_channel:" + goa_log_id,
                            new { type = "GOA", callRequestId = cr.CallRequestId.ToString() }.ToJson());

                        new IsscRestClient(conf).ServiceProviderInitiatedRequest(
                            ad.ClientId, ad.ContractorId, ad.LocationId,
                            goa_log_id, IsscRestClient.DispatchStatusType.GOANeeded, ad.DispatchId, null, goaReason?.Name ?? "GOA", jsonObj.Comments.ToString(), "", 0);

                        logger.Log(LogLevel.Info, "ISSC/CR" + cr.CallRequestId + "/C" + cr.CompanyId + "/OutgoingRequestGoa: Requested GOA " + goa_log_id);

                        qi.CallRequestId = cr.CallRequestId;
                        DigitalDispatchService.LogAction(qi);
                        return true;
                    }
                    if (!await doRequestGoa())
                    {
                        return false;
                    }
                    break;

                case DigitalDispatchActionQueueItemType.OutgoingServiceFailure:
                    async Task<bool> doRequestServiceFailure()
                    {
                        var cr = CallRequest.GetById(Convert.ToInt32(jsonObj.CallRequestId));
                        if (cr == null)
                        {
                            logger.Log(LogLevel.Error, "ISSC/CR" + cr.CallRequestId + "/C" + cr.CompanyId + "/OutgoingRequestServiceFailure: Couldn't find CallRequestId.");
                            return true;
                        }

                        IsscDispatch ad = IsscDispatch.GetByCallRequestId(cr.CallRequestId);

                        if (ad == null)
                        {
                            logger.Log(LogLevel.Error, "ISSC/CR" + cr.CallRequestId + "/C" + cr.CompanyId + "/OutgoingRequestServiceFailure: Couldn't find AllstateDispatch.");


                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);

                            await sourceMessage.CompleteAsync();
                            return false;
                        }

                        MasterAccountReason sfReason = MasterAccountReason.GetById(Convert.ToInt32(jsonObj.ReasonId));

                        var goa_log_id = Guid.NewGuid().ToString("N");

                        Core.SetRedisValue("issc_channel:" + goa_log_id,
                            new { type = "ServiceFailed", callRequestId = cr.CallRequestId.ToString() }.ToJson(), TimeSpan.FromDays(3));

                        new IsscRestClient(conf).ServiceProviderInitiatedRequest(
                            ad.ClientId, ad.ContractorId, ad.LocationId,
                            goa_log_id, IsscRestClient.DispatchStatusType.ServiceFailed, ad.DispatchId, null, sfReason?.Name ?? "Service Failed", jsonObj.Comments.ToString(), "", 0);

                        logger.Log(LogLevel.Info, "ISSC/CR" + cr.CallRequestId + "/C" + cr.CompanyId + "/OutgoingRequestServiceFailure: Requested Service Feailure" + goa_log_id);

                        qi.CallRequestId = cr.CallRequestId;
                        DigitalDispatchService.LogAction(qi);
                        return true;
                    }
                    if (!await doRequestServiceFailure())
                    {
                        return false;
                    }
                    break;

                case DigitalDispatchActionQueueItemType.OutgoingRequestInfo:
                    async Task<bool> doInfoRequest()
                    {
                        CallRequest cr = CallRequest.GetById(Convert.ToInt32(jsonObj.CallRequestId));

                        if (cr == null)
                        {
                            logger.Log(LogLevel.Error, "ISSC/CR" + cr.CallRequestId + "/C" + cr.CompanyId + "/OutgoingRequestInfo: Couldn't find CallRequestId.");
                            return true;
                        }

                        IsscDispatch ad = IsscDispatch.GetByCallRequestId(cr.CallRequestId);

                        if (ad == null)
                        {
                            logger.Log(LogLevel.Error, "ISSC/CR" + cr.CallRequestId + "/C" + cr.CompanyId + "/OutgoingRequestInfo: Couldn't find AllstateDispatch.");

                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);

                            await sourceMessage.CompleteAsync();
                            return false;
                        }

                        var goa_log_id = Guid.NewGuid().ToString("N");

                        Core.SetRedisValue("issc_channel:" + goa_log_id,
                            new { type = "Info Request", callRequestId = cr.CallRequestId.ToString() }.ToJson(), TimeSpan.FromDays(3));

                        new IsscRestClient(conf).ServiceProviderInitiatedRequest(
                            ad.ClientId, ad.ContractorId, ad.LocationId,
                            goa_log_id, IsscRestClient.DispatchStatusType.InfoRequest, 
                            ad.DispatchId,
                            null, 
                            jsonObj.Comments.ToString());

                        logger.Log(LogLevel.Info, "ISSC/CR" + cr.CallRequestId + "/C" + cr.CompanyId + "/OutgoingRequestInfo: Requested Info" + goa_log_id);

                        qi.CallRequestId = cr.CallRequestId;
                        DigitalDispatchService.LogAction(qi);
                        return true;
                    }
                    if (!await doInfoRequest())
                    {
                        return false;
                    }
                    break;

                case DigitalDispatchActionQueueItemType.OutgoingRequestAdditionalService:
                    async Task<bool> doRequestAdditionalService()
                    {
                        var cr = (CallRequest)CallRequest.GetById(Convert.ToInt32(jsonObj.CallRequestId));
                        if (cr == null)
                        {
                            logger.Log(LogLevel.Error, "ISSC/CR" + cr.CallRequestId + "/C" + cr.CompanyId + "/OutgoingRequestAdditionalService: Couldn't find CallRequestId.");
                            return true;
                        }

                        IsscDispatch ad = IsscDispatch.GetByCallRequestId(cr.CallRequestId);

                        if (ad == null)
                        {
                            logger.Log(LogLevel.Error, "ISSC/CR" + cr.CallRequestId + "/C" + cr.CompanyId + "/OutgoingRequestAdditionalService: Couldn't find AllstateDispatch.");


                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);

                            await sourceMessage.CompleteAsync();
                            return false;
                        }

                        var goa_log_id = Guid.NewGuid().ToString("N");

                        Core.SetRedisValue("issc_channel:" + goa_log_id,
                            new
                            {
                                type = "AdditionalService",
                                callRequestId = cr.CallRequestId.ToString()
                            }.ToJson(), TimeSpan.FromDays(3));

                        var adsvcReason = (MasterAccountReason)MasterAccountReason.GetById(Convert.ToInt32(jsonObj.ReasonId));

                        var serviceNameToSend = adsvcReason.Code;

                        string comments = "";

                        if (!string.IsNullOrWhiteSpace((string)jsonObj.Comments))
                            comments = jsonObj.Comments.ToString();

                        if (ad.ClientId == "GCOAPI" &&
                            jsonObj.Category != null &&
                            jsonObj.Service != null &&
                            Core.GetRedisValue("disable_issc_selfservice_2") != "1")
                        {
                            int quantity = jsonObj.Quantity != null ? Convert.ToInt32(jsonObj.Quantity) : 0;
                            int timeInMinutes = jsonObj.TimeInMinutes != null ?
                                    Convert.ToInt32(jsonObj.TimeInMinutes) : 0;
                            decimal amount = jsonObj.Amount != null ? Convert.ToDecimal(jsonObj.Amount) : 0;
                            string category = jsonObj.Category?.ToString();
                            string name = jsonObj.Service?.ToString();

                            new IsscRestClient(conf).ServiceProviderInitiatedRequest2(
                                ad.ClientId, ad.ContractorId, ad.LocationId,
                                goa_log_id, "AdditionalService",
                                ad.DispatchId, null, comments, null, name, 0,
                                quantity,
                                category,
                                timeInMinutes,
                                amount);
                        }
                        else
                        {
                            new IsscRestClient(conf).ServiceProviderInitiatedRequest(
                                ad.ClientId, ad.ContractorId, ad.LocationId,
                                goa_log_id, IsscRestClient.DispatchStatusType.AdditionalService,
                                ad.DispatchId, null, comments, null, serviceNameToSend, 0);
                        }

                        logger.Log(LogLevel.Info, "ISSC/CR" + cr.CallRequestId + "/C" + cr.CompanyId + "/OutgoingRequestAdditionalService: " + goa_log_id);

                        qi.CallRequestId = cr.CallRequestId;
                        DigitalDispatchService.LogAction(qi);
                        return true;
                    }
                    if (!await doRequestAdditionalService())
                    {
                        return false;
                    }
                    break;

                case DigitalDispatchActionQueueItemType.OutgoingCallCanceled:
                    // both ProviderCancel and CustomerCancel are handled here.
                    CallRequest cccr = CallRequest.GetById(Convert.ToInt32(jsonObj.CallRequestId));
                    if (cccr == null)
                    {
                        logger.Log(LogLevel.Error, "ISSC/CR" + cccr.CallRequestId + "/C" + cccr.CompanyId + "/OutgoingCallCanceled: Couldn't find CallRequestId.");
                        break;
                    }

                    IsscDispatch ccad = IsscDispatch.GetByCallRequestId(cccr.CallRequestId);

                    if (ccad == null)
                    {
                        logger.Log(LogLevel.Error, "ISSC/CR" + cccr.CallRequestId + "/C" + cccr.CompanyId + "/OutgoingCallCanceled: Couldn't find AllstateDispatch.");
                        break;
                    }

                    MasterAccountReason cancelReason = MasterAccountReason.GetById(Convert.ToInt32(jsonObj.ReasonId));

                    if (cancelReason == null || cancelReason.Type != MasterAccountReasonType.Cancel)
                    {
                        logger.Log(LogLevel.Error, "ISSC/CR" + cccr.CallRequestId + "/C" + cccr.CompanyId + "/OutgoingCallCanceled: Invalid ReasonId: " +
                            JsonExtensions.ToJson(cancelReason, true));

                        await sourceMessage.CompleteAsync();
                        return false;
                    }

                    var cancel_log_id = Guid.NewGuid().ToString("N");

                    Core.SetRedisValue("issc_channel:" + cancel_log_id,
                        new { type = "Cancel", callRequestId = cccr.CallRequestId.ToString() }.ToJson());

                    var cancelType = IsscRestClient.DispatchStatusType.ProviderCancel;
                    if (cancelReason.MasterAccountReasonId == 191)
                        cancelType = IsscRestClient.DispatchStatusType.CustomerCancel;

                    new IsscRestClient(conf).ServiceProviderInitiatedRequest(
                        ccad.ClientId, ccad.ContractorId, ccad.LocationId,
                        cancel_log_id,
                        cancelType,
                        ccad.DispatchId, reason: cancelReason.Code);

                    logger.Log(LogLevel.Info, "ISSC/CR" + cccr.CallRequestId + "/C" + cccr.CompanyId + "/OutgoingCancel: Requested Cancel " + cancel_log_id);

                    qi.CallRequestId = cccr.CallRequestId;
                    DigitalDispatchService.LogAction(qi);
                    break;

                case DigitalDispatchActionQueueItemType.OutgoingConnect:
                    await IsscConnect(conf);
                    break;

                case DigitalDispatchActionQueueItemType.InternalVerifyLogin:
                    string locationId = null;

                    if (jsonObj.locationId != null)
                        locationId = Convert.ToString(jsonObj.locationId);

                    IsscProvider provider = IsscProvider.GetByContractorId(
                        Convert.ToString(jsonObj.contractorId),
                        Convert.ToString(jsonObj.clientId),
                        locationId);

                    if (provider == null)
                    {
                        await sourceMessage.DeadLetterMessageAsync(sourceMessage.Message,"Couldn't locate IsscProvider for given ContractorID", "ContractorId == " + Convert.ToString(jsonObj.contractorId) + ", ClientId == " +
                            Convert.ToString(jsonObj.clientId) + ", LocationId == " + (locationId ?? "null"));
                        return false;
                    }
                    else if (provider.ConfigId != conf.IsscConfigId)
                    {
                        logger.LogEvent("{0}: Cant verify login; bad configid. Should have been {1}, got {2}, object: {3}", qi.CompanyId, LogLevel.Error,
                            sourceMessage.Message.MessageId,
                            conf.IsscConfigId,
                            provider.ConfigId,
                            provider.ToJson());

                        await sourceMessage.DeadLetterAsync("Cant verify login... The provider returned contains ConfigId:" + provider.ConfigId + "... Should have been " + conf.IsscConfigId,
                            provider.ToJson(null));
                        return false;
                    }

                    var config = IsscConfig.GetById(provider.ConfigId);
                    var rconn = IsscConnection.Get(config);

                    if (rconn.Status != IsscConnectionStatus.Connected)
                    {
                        await sourceMessage.DeadLetterAsync("ISSC Connection Status isn't connected.", "Status == " + rconn.Status);
                        return false;
                    }

                    if (provider.LoginStatus != IsscProviderLoginStatus.LoggedIn &&
                        provider.LoginStatus != IsscProviderLoginStatus.LoggingIn)
                        new IsscRestClient(config).Login(provider.ClientId, provider.ContractorId, provider.LocationId, provider.Token);

                    break;

                case DigitalDispatchActionQueueItemType.InternalVerifyConnection:
                    //      if (conn.Status != IsscConnectionStatus.Connected)
                    //        await IsscConnect(conf);

                    break;

                case DigitalDispatchActionQueueItemType.OutgoingLogin:
                    if (qi.AccountId.HasValue)
                    {
                        var errors = new Dictionary<string, string>();
                        bool failed = false;
                        foreach (var x in IsscProvider.GetByAccountId(qi.AccountId.Value))
                        {
                            var client = new IsscRestClient(IsscConfig.GetById(x.ConfigId));

                            int userId = 0;
                            if (jsonObj.OwnerUserId != null)
                                userId = Convert.ToInt32(jsonObj.OwnerUserId);

                            try
                            {
                                client.Login((int)x.IsscProviderId);
                                errors[x.ToString()] = "Logging in...";

                                logger.LogEvent("{0}: Logging in {1}", qi.CompanyId, LogLevel.Info,
                                    sourceMessage.Message.MessageId, x.ToString());
                            }
                            catch (Exception e)
                            {
                                errors[x.ToString()] = "Failed:" + e.Message;

                                failed = true;
                                x.UpdateLoginStatus(IsscProviderLoginStatus.LoggedOut);
                            }
                        }

                        await PushNotificationProvider.BackgroundJobStatusUpdate(qi.CompanyId.Value,
                            qi.QueueItemId,
                            "digitaldispatch_login",
                            !failed,
                            "Login Status",
                            errors);

                        logger.LogEvent("{0}: Login loop finished.", qi.CompanyId, LogLevel.Info,
                            sourceMessage.Message.MessageId);

                        await sourceMessage.CompleteAsync();
                        return !failed;
                    }
                    else
                    {
                        logger.LogEvent("{0}: Cannot login. No AccountId present.", qi.CompanyId, LogLevel.Error,
                        sourceMessage.Message.MessageId);

                        await sourceMessage.DeadLetterAsync("Attempted to Login but no accountId was passed.", "");
                        return false;
                    }

                case DigitalDispatchActionQueueItemType.OutgoingLogoff:
                    if (qi.AccountId.HasValue)
                    {
                        var errors = new Dictionary<string, string>();
                        bool failed = false;
                        foreach (var x in IsscProvider.GetByAccountId(qi.AccountId.Value))
                        {
                            if (x.ClientId == "GCO")
                            {
                                logger.LogEvent("{0}: Manual GCO Legacy logout is disabled. Skipping {1}", qi.CompanyId, LogLevel.Info,
                                    sourceMessage.Message.MessageId, x.ToString());
                                continue;
                            }

                            var client = new IsscRestClient(IsscConfig.GetById(x.ConfigId));

                            int userId = 0;
                            if (jsonObj.OwnerUserId != null)
                            {
                                userId = Convert.ToInt32(jsonObj.OwnerUserId);
                            }

                            var identifier = x.ToString();

                            try
                            {
                                client.Logout((int)x.IsscProviderId);
                                errors.Add(identifier, "Logged out");

                                logger.LogEvent("{0}: Logged out {1}", qi.CompanyId, LogLevel.Info,
                                 sourceMessage.Message.MessageId, identifier);
                            }
                            catch (Exception e)
                            {
                                errors.Add(identifier, "Failed:" + e);
                                failed = true;
                            }
                        }

                        await PushNotificationProvider.BackgroundJobStatusUpdate(qi.CompanyId.Value,
                            qi.QueueItemId,
                            "digitaldispatch_logout",
                            true, // we don't care if logout failed - just let the user know the request finished.
                            "Logout Status",
                            errors);

                        logger.LogEvent("{0}: Logout loop finished.", qi.CompanyId, LogLevel.Info,
                            sourceMessage.Message.MessageId);

                        await sourceMessage.CompleteAsync();
                        return !failed;
                    }
                    else
                    {
                        await sourceMessage.DeadLetterAsync("Attempted to Logout but no accountId was passed.", "");
                        return false;
                    }

                default:
                    await sourceMessage.DeadLetterAsync("No handler written for " + qi.Type.ToString(), "Couldn't handle request; data: " + qi.ToJson());
                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                    logger.LogEvent("Queue item doesn't have a Type set that we have a implementation written for... MessageId {0}, Body = {1}", qi.CompanyId, LogLevel.Error, sourceMessage.Message.MessageId, qi.ToJson());
                    return false;
            }

            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
            await sourceMessage.CompleteAsync();
            return true;
        }

        public static async Task<bool> RegisterContractor(DigitalDispatchActionQueueItem item, dynamic data, ProcessMessageEventArgs msg = null)
        {
            int companyId = item.CompanyId.Value;
            int accountId = item.AccountId.Value;
            int userId = item.OwnerUserId.Value;
            string contractorId = data.contractorId;
            string taxId = data.taxId;
            string locationId = data.locationId;

            var acc = await Account.GetByIdAsync(accountId);
            var mc = await MasterAccount.GetByIdAsync(acc.MasterAccountId);

            contractorId = contractorId.ToUpperInvariant().Replace(" ", "");

            if (acc == null)
                throw new TowbookException($"Account {accountId} doesn't exist!");

            if (mc == null)
                throw new TowbookException($"Account {accountId} doesn't have a masterAccount set.");

            logger.LogEvent("{0}/{4}: Registering ContractorID {1} {2} for CompanyID {3}", companyId, LogLevel.Info, (msg.Message != null ? msg.Message.MessageId : "[brokerMsgNull]"),
                contractorId, locationId, companyId, mc.Name);

            var isc = new IsscRestClient(IsscConfig.GetByEnvironmentType(EnvironmentType.Live));

            switch (acc.MasterAccountId)
            {
                case MasterAccountTypes.AlliedDispatch:
                    var tcx = TrxContractor.GetByContractorId(contractorId, acc.MasterAccountId, 3);

                    if (tcx == null)
                    {
                        var tc = new TrxContractor()
                        {
                            CompanyId = companyId,
                            AccountId = accountId,
                            ContractorId = contractorId,
                            MasterAccountId = acc.MasterAccountId,
                            EnvironmentId = 3
                        };
                        tc.Save();
                        return true;
                    }

                    break;

                case MasterAccountTypes.RoadsideProtect:
                    var rpx = RoadsideProtectContractor.GetByContractorId(contractorId, acc.MasterAccountId, 3);

                    if (rpx == null)
                    {
                        //isc.RegisterProvider("RP", companyId, accountId, contractorId, taxId);

                        var rpc = new RoadsideProtectContractor()
                        {
                            CompanyId = companyId,
                            AccountId = accountId,
                            ContractorId = contractorId,
                            MasterAccountId = acc.MasterAccountId,
                            EnvironmentId = 3
                        };
                        rpc.Save();

                        return true;
                    }
                    break;

                case MasterAccountTypes.Tesla:
                    isc.RegisterProvider("TSLA", companyId, accountId, contractorId, taxId);
                    return true;

                case MasterAccountTypes.Geico:
                    isc.RegisterProvider("GCOAPI", companyId, accountId, contractorId, taxId, locationId);
                    return true;

                case MasterAccountTypes.Allstate:
                    var acx = AllstateContractor.GetByContractorId(contractorId, MasterAccountTypes.Allstate);
                    if (acx == null)
                    {
                        DigitalDispatchService.ValidateContractor(contractorId, taxId, locationId, acc, companyId);

                        acx = new AllstateContractor(MasterAccountTypes.Allstate, companyId, accountId, contractorId);
                        acx.Save();
                        return true;
                    }
                    else
                    {
                        logger.LogEvent("{0}/{4}: Registering ContractorID {1} {2} for CompanyID {3} cancelled - it is already registered.",
                            companyId, LogLevel.Warn, (msg.Message != null ? msg.Message.MessageId : "[brokerMsgNull]"),
                            contractorId, locationId, companyId, mc.Name);
                    }
                    break;

                case MasterAccountTypes.Nsd:
                    var nsd = AllstateContractor.GetByContractorId(contractorId, MasterAccountTypes.Nsd);
                    if (nsd == null)
                    {
                        DigitalDispatchService.ValidateContractor(contractorId, taxId, locationId, acc, companyId);

                        nsd = new AllstateContractor(MasterAccountTypes.Nsd, companyId, accountId, contractorId);
                        nsd.Save();
                        await NotifyNsdOfNewContractor(nsd);
                        return true;
                    }
                    else
                    {
                        logger.LogEvent("{0}/{4}: Registering ContractorID {1} {2} for CompanyID {3} cancelled - it is already registered.",
                            companyId, LogLevel.Warn, (msg.Message != null ? msg.Message.MessageId : "[brokerMsgNull]"),
                            contractorId, locationId, companyId, mc.Name);
                    }
                    break;

                case MasterAccountTypes.Sykes:
                    var sc = SykesContractor.GetByContractorId(contractorId);
                    if (sc == null)
                    {
                        DigitalDispatchService.ValidateContractor(contractorId, taxId, locationId, acc, companyId);

                        sc = new SykesContractor(companyId, accountId, contractorId);
                        sc.Save();

                        return true;
                    }
                    else
                    {
                        logger.LogEvent("{0}/{4}: Registering ContractorID {1} {2} for CompanyID {3} cancelled - it is already registered.",
                            companyId, LogLevel.Warn, (msg != null ? msg.Message.MessageId : "[brokerMsgNull]"),
                            contractorId, locationId, companyId, mc.Name);
                    }
                    break;

                case MasterAccountTypes.Honk:
                    var hp = HonkProvider.GetByProviderId(contractorId);
                    if (hp == null)
                    {
                        DigitalDispatchService.ValidateContractor(contractorId, taxId, locationId, acc, companyId);

                        hp = new HonkProvider()
                        {
                            CompanyId = companyId,
                            AccountId = accountId,
                            ProviderId = contractorId,
                            MasterAccountId = mc.Id
                        };

                        await hp.Save();

                        return true;
                    }
                    else
                    {
                        logger.LogEvent("{0}/{4}: Registering ContractorID {1} {2} for CompanyID {3} cancelled - it is already registered.",
                            companyId, LogLevel.Warn, (msg != null ? msg.Message.MessageId : "[brokerMsgNull]"),
                            contractorId, locationId, companyId, mc.Name);
                    }
                    break;


                case MasterAccountTypes.Quest:
                    var quest = AllstateContractor.GetByContractorId(contractorId, MasterAccountTypes.Quest);
                    if (quest == null)
                    {
                        DigitalDispatchService.ValidateContractor(contractorId, taxId, locationId, acc, companyId);

                        quest = new AllstateContractor(MasterAccountTypes.Quest, companyId, accountId, contractorId);
                        quest.Save();
                        await NotifyQuestOfNewContractor(quest);
                        return true;
                    }
                    else
                    {
                        logger.LogEvent("{0}/{4}: Registering ContractorID {1} {2} for CompanyID {3} cancelled - it is already registered.",
                            companyId, LogLevel.Warn, (msg.Message?.MessageId ?? "[brokerMsgNull]"),
                            contractorId, locationId, companyId, mc.Name);
                    }
                    break;

                case MasterAccountTypes.Nac:
                    var nac = AllstateContractor.GetByContractorId(contractorId, MasterAccountTypes.Nac);
                    if (nac == null)
                    {
                        DigitalDispatchService.ValidateContractor(contractorId, taxId, locationId, acc, companyId);

                        nac = new AllstateContractor(MasterAccountTypes.Nac, companyId, accountId, contractorId);
                        nac.IsLoggedIn = true;
                        nac.LoginStatus = AllstateContractorLoginStatus.LoggedIn;
                        nac.Save();
                        await NotifyNacOfNewContractor(nac);
                        return true;
                    }
                    else
                    {
                        logger.LogEvent("{0}/{4}: Registering ContractorID {1} {2} for CompanyID {3} cancelled - it is already registered.",
                            companyId, LogLevel.Warn, (msg.Message?.MessageId ?? "[brokerMsgNull]"),
                            contractorId, locationId, companyId, mc.Name);
                    }
                    break;

                case MasterAccountTypes.DrivenSolutions:
                case MasterAccountTypes.StackThreeAtlas:
                    var ds = StackThreeContractor.GetByContractorId(
                        contractorId,
                        acc.MasterAccountId,
                        3);

                    if (ds == null)
                    {
                        DigitalDispatchService.ValidateContractor(contractorId, taxId, locationId, acc, companyId);

                        ds = new StackThreeContractor()
                        {
                            MasterAccountId = MasterAccountTypes.DrivenSolutions,
                            CompanyId = companyId,
                            AccountId = accountId,
                            ContractorId = contractorId,
                            EnvironmentId = 3
                        };

                        ds.Save();

                        return true;
                    }
                    else
                    {
                        logger.LogEvent("{0}/{4}: Registering ContractorID {1} {2} for CompanyID {3} cancelled - it is already registered.",
                            companyId, LogLevel.Warn, (msg?.Message?.MessageId ?? "[brokerMsgNull]"),
                            contractorId, locationId, companyId, mc.Name);
                    }
                    break;

                case MasterAccountTypes.Fleetnet:
                case MasterAccountTypes.Servicase:
                    var fn = Fleetnet.FleetnetProvider.GetByProviderId(contractorId);
                    if (fn == null)
                    {
                        DigitalDispatchService.ValidateContractor(contractorId, taxId, locationId, acc, companyId);
                        fn = Fleetnet.FleetnetProvider.Register(companyId, accountId, contractorId);

                        var company = await Company.Company.GetByIdAsync(companyId);

                        var callProviderId = "1"; // fleetnet
                        if (acc.MasterAccountId == MasterAccountTypes.Fleetnet)
                            callProviderId = "1";
                        else if (acc.MasterAccountId == MasterAccountTypes.Servicase)
                            callProviderId = "3";

                        var email = EmailAddress.GetByCompanyId(company.Id)
                            .FirstOrDefault()?.Address ?? company.Email;

                        var reg = new FleetnetRestClient.RegisterModel()
                        {
                            Company = company.Name,
                            Address = company.Address,
                            City = company.City,
                            State = company.State,
                            Zip = company.Zip,
                            Phone = Core.FormatPhoneWithDashesOnly(company.Phone),
                            ContractorId = fn.ContractorId,
                            Latitude = company.Latitude,
                            Longitude = company.Longitude,
                            CallProviderId = callProviderId,
                            LocationId = fn.ProviderId,
                            Email = email,
                        };

                        var resp = FleetnetRestClient.GetProduction().RegisterServiceProvider(reg);

                        logger.Info(acc.MasterAccountId,
                            "RegisterContractor",
                            "Registered",
                            contractorId,
                            locationId,
                            companyId: companyId,
                            data: new
                            {
                                register = reg.ToJson(),
                                json = resp.ToJson()
                            });

                        await NotifyFleetnetOfNewContractor(fn);
                        return true;
                    }
                    else
                    {
                        logger.LogEvent("{0}/{4}: Registering ContractorID {1} {2} for CompanyID {3} cancelled - it is already registered.",
                            companyId, LogLevel.Warn, (msg.Message?.MessageId ?? "[brokerMsgNull]"),
                            contractorId, locationId, companyId, mc.Name);
                    }
                    break;

                case MasterAccountTypes.Urgently:
                    var up = UrgentlyProvider.GetByProviderId(contractorId);
                    if (up == null)
                    {
                        DigitalDispatchService.ValidateContractor(contractorId, taxId, locationId, acc, companyId);

                        up = new UrgentlyProvider()
                        {
                            AccountId = accountId,
                            CompanyId = companyId,
                            ProviderId = contractorId
                        };
                        up.Save();

                        // sync drivers
                        // sync trucks

                        return true;
                    }
                    else
                    {
                        logger.LogEvent("{0}/{4}: Registering ContractorID {1} {2} for CompanyID {3} cancelled - it is already registered.",
                            companyId, LogLevel.Warn, (msg.Message?.MessageId ?? "[brokerMsgNull]"),
                            contractorId, locationId, companyId, mc.Name);
                    }
                    break;


                default:
                    logger.LogEvent("{0}/{4}: Unknown MasterAccountId: {5}  Registering ContractorID {1} {2} for CompanyID {3} FAILED - Is this a new DDService registration that isn't implemented?",
                        companyId, LogLevel.Fatal, (msg.Message?.MessageId ?? "[brokerMsgNull]"),
                        contractorId, (locationId ?? ""), companyId, mc.Name, acc.MasterAccountId);
                    break;
            }

            return false;
        }


        public static async Task<bool> DeregisterContractorAsync(DigitalDispatchActionQueueItem item,
            dynamic data, ProcessMessageEventArgs msg = null)
        {
            int companyId = item.CompanyId.Value;
            int accountId = item.AccountId.Value;
            int userId = item.OwnerUserId.Value;
            string contractorId = data.contractorId;
            string taxId = data.taxId;
            string locationId = data.locationId;

            var acc = await Account.GetByIdAsync(accountId);
            var mc = await MasterAccount.GetByIdAsync(acc.MasterAccountId);

            contractorId = contractorId.ToUpperInvariant().Replace(" ", "");

            if (acc == null)
                throw new TowbookException($"Account {accountId} doesn't exist!");

            if (mc == null)
                throw new TowbookException($"Account {accountId} doesn't have a masterAccount set.");

            logger.Info(acc.MasterAccountId, "RemoveDigitalConnection",
                "Removing Digital Connection", contractorId, locationId, companyId: companyId,
                data: new { ownerUserId = userId },
                queueItemId: item.QueueItemId);

            var isc = new IsscRestClient(IsscConfig.GetByEnvironmentType(EnvironmentType.Live));

            void DeregisterOrDeleteIfNoToken(IsscProvider ip)
            {
                if (ip == null)
                    return;

                if (string.IsNullOrWhiteSpace(ip.Token))
                {
                    ip.Delete();
                    return;
                }

                if (ip.LoginStatus == IsscProviderLoginStatus.LoggedOut)
                {
                    try
                    {
                        isc.DeregisterProvider(ip.ClientId, ip.ContractorId, ip.LocationId, ip.Token);

                        logger.Info(acc.MasterAccountId, "RemoveDigitalConnection",
                            "Removed Digital Connection Succeeded", contractorId, locationId, companyId: companyId,
                            data: new { ownerUserId = userId },
                            queueItemId: item.QueueItemId);
                    }
                    catch (Exception y)
                    {
                        logger.Warn(acc.MasterAccountId, "RemoveDigitalConnection",
                            "Removing Digital Connection failed", contractorId, locationId, companyId: companyId,
                            data: y.Message,
                            queueItemId: item.QueueItemId);
                    }
                }
                else
                {
                    logger.Info(acc.MasterAccountId, "RemoveDigitalConnection",
                        "Removed Digital Connection couldn't proceed, connection isn't logged out", contractorId, locationId, companyId: companyId,
                        data: new { ownerUserId = userId },
                        queueItemId: item.QueueItemId);
                }
            }

            switch (acc.MasterAccountId)
            {
                case MasterAccountTypes.AlliedDispatch:
                    var tp = TrxContractor.GetByAccountId(accountId)
                        .FirstOrDefault(o => o.ContractorId.Equals(contractorId, StringComparison.InvariantCultureIgnoreCase));

                    if (tp != null)
                    {
                        tp.IsDeleted = true;
                        tp.Save();
                    }
                    return true;

                case MasterAccountTypes.RoadsideProtect:
                    var rp = RoadsideProtectContractor.GetByAccountId(accountId)
                        .FirstOrDefault(o => o.ContractorId.Equals(contractorId, StringComparison.InvariantCultureIgnoreCase));

                    if (rp != null)
                    {
                        rp.IsDeleted = true;
                        rp.Save();
                    }
                    return true;

                case MasterAccountTypes.Tesla:
                case MasterAccountTypes.Geico:
                    string clientId = null;
                    switch (acc.MasterAccountId)
                    {
                        case MasterAccountTypes.Tesla: clientId = "TSLA"; break;
                        case MasterAccountTypes.Geico: clientId = "GCOAPI"; break;
                    }

                    var ip = IsscProvider.GetByContractorId(contractorId, clientId, locationId);
                    DeregisterOrDeleteIfNoToken(ip);
                    break;

                case MasterAccountTypes.Allstate:
                    var acx = AllstateContractor.GetByContractorId(contractorId, MasterAccountTypes.Allstate, locationId);
                    if (acx != null)
                    {
                        acx.Delete();
                        return true;
                    }
                    else
                    {
                        logger.LogEvent("{0}/{4}: ContractorID {1} {2} for CompanyID {3} cancelled - it doesn't exist",
                            companyId, LogLevel.Warn, (msg.Message != null ? msg.Message.MessageId : "[brokerMsgNull]"),
                            contractorId, locationId, companyId, mc.Name);
                    }
                    break;

                case MasterAccountTypes.Nsd:
                    var nsd = AllstateContractor.GetByContractorId(contractorId, MasterAccountTypes.Nsd);
                    if (nsd != null)
                    {
                        nsd.Delete();
                        return true;
                    }
                    else
                    {
                        logger.LogEvent("{0}/{4}: Deleting ContractorID {1} {2} for CompanyID {3} cancelled - it doesn't exist",
                            companyId, LogLevel.Warn, (msg.Message != null ? msg.Message.MessageId : "[brokerMsgNull]"),
                            contractorId, locationId, companyId, mc.Name);
                    }
                    break;

                case MasterAccountTypes.Quest:
                    var quest = AllstateContractor.GetByContractorId(contractorId, MasterAccountTypes.Quest);
                    if (quest != null)
                    {
                        quest.Delete();
                        return true;
                    }
                    else
                    {
                        logger.LogEvent("{0}/{4}: Deleting ContractorID {1} {2} for CompanyID {3} cancelled - it doesn't exist",
                            companyId, LogLevel.Warn, (msg.Message?.MessageId ?? "[brokerMsgNull]"),
                            contractorId, locationId, companyId, mc.Name);
                    }
                    break;

                case MasterAccountTypes.Nac:
                    var nac = AllstateContractor.GetByContractorId(contractorId, MasterAccountTypes.Nac);
                    if (nac != null)
                    {
                        nac.Delete();
                        return true;
                    }
                    else
                    {
                        logger.LogEvent("{0}/{4}: Registering ContractorID {1} {2} for CompanyID {3} cancelled - it is already registered.",
                            companyId, LogLevel.Warn, (msg.Message?.MessageId ?? "[brokerMsgNull]"),
                            contractorId, locationId, companyId, mc.Name);
                    }
                    break;

                case MasterAccountTypes.Fleetnet:
                case MasterAccountTypes.Servicase:
                    var fn = Fleetnet.FleetnetProvider.GetByProviderId(contractorId);
                    if (fn != null)
                    {
                        fn.Delete();
                        return true;
                    }
                    else
                    {
                        logger.Warn(MasterAccountTypes.Fleetnet, "Deregister", "Deleting registration failed. ContractorId didn't exist.",
                            contractorId, companyId: item.CompanyId, data: item);
                    }
                    break;

                case MasterAccountTypes.Urgently:
                    var up = UrgentlyProvider.GetByProviderId(contractorId);
                    if (up != null)
                    {
                        // not implemented: urgently has to disable on their end.

                        // sync drivers
                        // sync trucks

                        return false;
                    }
                    else
                    {
                        logger.LogEvent("{0}/{4}: Deregistering ContractorID {1} {2} for CompanyID {3} cancelled - it doesn't exist.",
                            companyId, LogLevel.Warn, (msg.Message?.MessageId ?? "[brokerMsgNull]"),
                            contractorId, locationId, companyId, mc.Name);
                    }
                    break;

                case MasterAccountTypes.Agero:
                    var ags = AgeroSession.GetByCompanyId(companyId)
                        .Where(o => o.AccountId == accountId &&
                        o.VendorId == Convert.ToInt32(contractorId)).FirstOrDefault();
                    if (ags != null)
                    {
                        ags.Delete();
                        return true;
                    }
                    break;

                case MasterAccountTypes.Swoop:
                    var swoopSession = Integrations.MotorClubs.Swoop.SwoopSession.GetByCompanyId(companyId)
                        .Where(o => o.AccountId == accountId).FirstOrDefault();
                    if (swoopSession != null)
                    {
                        var src = new Integrations.MotorClubs.Swoop.SwoopRestClient();

                        if (swoopSession.CompanyId == 10000)
                            src = Integrations.MotorClubs.Swoop.SwoopRestClient.Staging();

                        src.OAuthRevokeAccessToken(swoopSession.AccessToken);
                        swoopSession.Delete();
                        return true;
                    }
                    break;

                case MasterAccountTypes.Sykes:
                    var sd = SykesContractor.GetByCompanyId(companyId)
                        .Where(o => o.AccountId == accountId && o.ContractorId == contractorId)
                        .FirstOrDefault();
                    if (sd != null)
                    {
                        sd.Delete();
                        return true;
                    }
                    break;


                default:
                    logger.LogEvent("{0}/{4}: Unknown MasterAccountId: {5}  Deleting ContractorID {1} {2} for CompanyID {3} FAILED - Is this a new DDService registration that isn't implemented?",
                        companyId, LogLevel.Fatal, (msg.Message?.MessageId ?? "[brokerMsgNull]"),
                        contractorId, (locationId ?? ""), companyId, mc.Name, acc.MasterAccountId);
                    break;
            }

            return false;
        }


        /// <summary>
        /// Sends a notification to Quest of a new contractor being setup in our database
        /// </summary>
        /// <param name="quest"></param>
        public static async Task NotifyQuestOfNewContractor(AllstateContractor quest)
        {
            if (quest.MasterAccountId != MasterAccountTypes.Quest)
                return;

            var comp = await Company.Company.GetByIdAsync(quest.CompanyId);

            string output = $@"<html><head><style>*,body{{font-size:12px; font-family: verdana, arial}}</style></head><body>
                A new Contractor ID has been registered in Towbook for the following company:<br /><br />

                <strong>Quest Contractor ID:</strong> {quest.ContractorId}<br />
                <strong>Company Name:</strong> {comp.Name}<br />
                <strong>Company Phone:</strong> {Core.FormatPhone(comp.Phone)}<br />
                <strong>Towbook Company ID:</strong> {comp.Id}<br /><br />
                <strong>Towbook Email Address:</strong> {EmailAddress.GetPrimaryTowbookEmailAddress(comp.Id)}<br />

                If you have any questions regarding this, please contact your primary Towbook contact. <br /><br />

                Towbook<br />
                <EMAIL><br />
                (*************<br /></body></html>";

            using (MailMessage mm = new MailMessage(
                new MailAddress("<EMAIL>", "Towbook"),
                new MailAddress("<EMAIL>", "Quest TowNetwork")))
            {

                mm.Bcc.Add(new MailAddress("<EMAIL>", "Dan Smith"));

                mm.Subject = "Towbook - New Contractor ID Registered";
                mm.IsBodyHtml = true;
                mm.Body = output;

                using (var sc = new SmtpClient().Get())
                {
                    await sc.Send(mm, await User.GetByIdAsync(1), "DDRegistration");
                }
            }
        }

        public static async Task NotifyNacOfNewContractor(AllstateContractor nac)
        {
            if (nac.MasterAccountId != MasterAccountTypes.Nac)
                return;

            var comp = await Company.Company.GetByIdAsync(nac.CompanyId);

            string output = $@"<html><head><style>*,body{{font-size:12px; font-family: verdana, arial}}</style></head><body>
                A new Contractor ID has been registered in Towbook for the following company:<br /><br />

                <strong>NAC Contractor ID:</strong> {nac.ContractorId}<br />
                <strong>Company Name:</strong> {comp.Name}<br />
                <strong>Company Phone:</strong> {Core.FormatPhone(comp.Phone)}<br />
                <strong>Towbook Email Address:</strong> {EmailAddress.GetPrimaryTowbookEmailAddress(comp.Id)}<br />
                <strong>Towbook Company ID:</strong> {comp.Id}<br /><br />

                If you have any questions regarding this, please contact your primary Towbook contact. <br /><br />

                Towbook<br />
                <EMAIL><br />
                (*************<br /></body></html>";

            using (var mm = new MailMessage(
                new MailAddress("<EMAIL>", "Towbook"),
                new MailAddress("<EMAIL>", "JC Fernandez")))
            {
                mm.Bcc.Add(new MailAddress("<EMAIL>", "Dan Smith"));
                mm.To.Add(new MailAddress("<EMAIL>"));
                mm.To.Add(new MailAddress("<EMAIL>"));

                mm.Subject = "Towbook - New Contractor ID Registered";
                mm.IsBodyHtml = true;
                mm.Body = output;

                using (var sc = new SmtpClient().Get())
                {
                    await sc.Send(mm, await User.GetByIdAsync(1), "DDRegistration");
                }
            }
        }

        public static async Task NotifyFleetnetOfNewContractor(Fleetnet.FleetnetProvider fp)
        {

            if (fp.CompanyId == 10000)
                return;

            var comp = await Company.Company.GetByIdAsync(fp.CompanyId);

            string output = $@"<html><head><style>*,body{{font-size:12px; font-family: verdana, arial}}</style></head><body>
                A new Contractor ID has been registered in Towbook for the following company:<br /><br />

                <strong>Towbook Connection Identifier:</strong> {fp.ProviderId}<br />
                <strong>Fleetnet Contractor ID:</strong> {fp.ContractorId}<br />
                <strong>Company Name:</strong> {comp.Name}<br />
                <strong>Company Phone:</strong> {Core.FormatPhone(comp.Phone)}<br />
                <strong>Towbook Email Address:</strong> {EmailAddress.GetPrimaryTowbookEmailAddress(comp.Id)}<br />

                If you have any questions regarding this, please contact your Towbook contact. <br /><br />

                Towbook<br />
                <EMAIL><br />
                (*************<br /></body></html>";


            using (MailMessage mm = new MailMessage(
                new MailAddress("<EMAIL>", "Towbook"),
                new MailAddress("<EMAIL> ", "Fleetnet")))
            {
                mm.Bcc.Add(new MailAddress("<EMAIL>", "Dan Smith"));

                mm.Subject = "Towbook - New Contractor ID Registered";
                mm.IsBodyHtml = true;
                mm.Body = output;

                using (var sc = new SmtpClient().Get())
                {
                    await sc.Send(mm, await User.GetByIdAsync(1), "DDRegistration");
                }
            }
        }


        /// <summary>
        /// Sends a notification to NSD of a new contractor being setup in our database
        /// </summary>
        /// <param name="nsd"></param>
        public static async Task NotifyNsdOfNewContractor(AllstateContractor nsd)
        {
            if (nsd.MasterAccountId != MasterAccountTypes.Nsd)
                return;

            var comp = await Company.Company.GetByIdAsync(nsd.CompanyId);

            var username = AccountKeyValue.GetByAccount(nsd.CompanyId, nsd.AccountId, Provider.Towbook.ProviderId, "McUsername").FirstOrDefault()?.Value ?? "(not yet entered)";
            var password = AccountKeyValue.GetByAccount(nsd.CompanyId, nsd.AccountId, Provider.Towbook.ProviderId, "McPassword").FirstOrDefault()?.Value ?? "(not yet entered)";

            string output = $@"<html><head><style>*,body{{font-size:12px; font-family: verdana, arial}}</style></head><body>
                A new Contractor ID has been registered in Towbook for the following company:<br /><br />

                <strong>NSD Contractor ID:</strong> {nsd.ContractorId}<br />
                <strong>NSD Username:</strong> {username}<br />
                <strong>NSD Password:</strong> {password}<br />
                <strong>Company Name:</strong> {comp.Name}<br />
                <strong>Company Phone:</strong> {Core.FormatPhone(comp.Phone)}<br />
                <strong>Towbook Email Address:</strong> {EmailAddress.GetPrimaryTowbookEmailAddress(comp.Id)}<br />
                <strong>Towbook Company ID:</strong> {comp.Id}<br /><br />

                If you have any questions regarding this, please contact your primary Towbook contact. <br /><br />

                Towbook<br />
                <EMAIL><br />
                (*************<br /></body></html>";

            using (var mm = new MailMessage(new MailAddress("<EMAIL>", "Towbook"),
                new MailAddress("<EMAIL>", "Ron Lee")))
            {

                mm.Bcc.Add(new MailAddress("<EMAIL>", "Dan Smith"));

                mm.Subject = "Towbook - New Contractor ID Registered";
                mm.IsBodyHtml = true;
                mm.Body = output;

                using (var sc = new SmtpClient().Get())
                {
                    await sc.Send(mm, await User.GetByIdAsync(1), "DDRegistration");
                }
            }
        }


        private async Task HandleAgeroQueueOutgoingMessage(DigitalDispatchActionQueueItem qi, dynamic jsonObj, ProcessMessageEventArgs sourceMessage)
        {
            CallRequest callRequest = CallRequest.GetById(Convert.ToInt32(jsonObj.Id ?? jsonObj.CallRequestId));
            AgeroDispatch ageroDispatch = AgeroDispatch.GetByCallRequestId(callRequest.CallRequestId);

            if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingAcceptCall ||
               qi.Type == DigitalDispatchActionQueueItemType.OutgoingRejectCall ||
               qi.Type == DigitalDispatchActionQueueItemType.OutgoingRequestPhoneCall)
            {
                if (callRequest != null && (callRequest.HasAlreadyRespondedTo() ||
                    (ageroDispatch?.Eta.GetValueOrDefault() ?? 0) != 0))
                {
                    await sourceMessage.CompleteAsync();
                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    return;
                }
            }


            switch (qi.Type)
            {
                case DigitalDispatchActionQueueItemType.OutgoingAcceptCall:
                    if (callRequest == null)
                    {
                        await sourceMessage.DeadLetterAsync();
                        return;
                    }
                    qi.CallRequestId = callRequest.CallRequestId;

                    var acceptReason = MasterAccountReason.GetById(Convert.ToInt32(jsonObj.MasterAccountReasonId));

                    if (ageroDispatch == null)
                    {
                        await sourceMessage.DeadLetterAsync("Attempted to accept callRequestId " + callRequest.CallRequestId + ", but no AgeroDispatch present in database for it",
                            ageroDispatch.ToJson(true));
                        await callRequest.UpdateStatus(CallRequestStatus.AcceptFailed);
                        return;
                    }


                    ageroDispatch.Eta = Convert.ToInt32(jsonObj.Eta);

                    if (acceptReason != null && !String.IsNullOrWhiteSpace(acceptReason.Code))
                        ageroDispatch.EtaReason = (AcceptDispatchETAReason)Convert.ToInt32(acceptReason.Code);
                    ageroDispatch.Save();

                    try
                    {
                        new AgeroRestClient().AcceptDispatchRequest(
                            accessToken: ageroDispatch.AccessToken,
                            dispatchNumber: ageroDispatch.DispatchId,
                            eta: ageroDispatch.Eta.GetValueOrDefault(0),
                            reason: ageroDispatch.EtaReason);

                        logger.Info(MasterAccountTypes.Agero, "AcceptCall", "Sent Accept successfully",
                            ageroDispatch.VendorId.ToString(), null,
                            ageroDispatch.DispatchId.ToString(),
                            qi.CompanyId,
                            new
                            {
                                accessToken = ageroDispatch.AccessToken,
                                dispatchNumber = ageroDispatch.DispatchId,
                                eta = ageroDispatch.Eta.GetValueOrDefault(0),
                                reason = ageroDispatch.EtaReason
                            });

                        // TODO: make acceptdispatchrequest return a status of Success/Failed so we don't have to use an exception.
                    }
                    catch (AgeroServiceUnavailable su)
                    {
                        await sourceMessage.CompleteAsync();
                        logger.Log(LogLevel.Error,
                            "Service Unavailable while trying to Accept dispatch ID " + ageroDispatch.DispatchId + "; " + su.Message, su);
                        await callRequest.UpdateStatus(CallRequestStatus.AcceptFailed);
                        DigitalDispatchService.LogAction(qi);
                        return;
                    }
                    catch (AgeroDispatchExpiredException ade)
                    {
                        await sourceMessage.DeadLetterAsync(ade.Message, ade.Json);
                        logger.Log(LogLevel.Error,
                            "Dispatch Expired exception occurred while trying to Accept dispatch ID " + ageroDispatch.DispatchId + "; " + ade.Message, ade);
                        await callRequest.UpdateStatus(CallRequestStatus.AcceptFailedExpired);
                        DigitalDispatchService.LogAction(qi);
                        return;
                    }
                    catch (AgeroDispatchAlreadyResponded are)
                    {
                        await sourceMessage.DeadLetterAsync(are.Message, are.Json);
                        logger.Log(LogLevel.Error,
                            "Another dispatcher already responded error occurred while trying to Accept Agero Dispatch ID " +
                            ageroDispatch.DispatchId + "; " +
                            are.Message,
                            are);
                        await callRequest.UpdateStatus(CallRequestStatus.AnotherDispatcherResponded);
                        DigitalDispatchService.LogAction(qi);
                        return;
                    }
                    catch (AgeroException ae)
                    {
                        logger.Error(MasterAccountTypes.Agero, "AcceptCall", "Accept Failed: " + ae.Message,
                            ageroDispatch.VendorId.ToString(), null, ageroDispatch.DispatchId.ToString(), qi.CompanyId,
                            new
                            {
                                exception = ae,
                                accessToken = ageroDispatch.AccessToken,
                                dispatchNumber = ageroDispatch.DispatchId,
                                eta = ageroDispatch.Eta.GetValueOrDefault(0),
                                reason = ageroDispatch.EtaReason
                            });

                        if (ae.Message.Contains("INVALID CLIENTID()"))
                        {
                            try
                            {
                                var oas = repairAgeroConnection(ageroDispatch, qi);

                                logger.Info(MasterAccountTypes.Agero, "AcceptCall", "Accept Failed, attempting to save it with AcceptRetry from " + ae.Message +
                                    " error. OAuthSignOut and oAuthSignIn called.",
                                    ageroDispatch.VendorId.ToString(), null, ageroDispatch.DispatchId.ToString(), qi.CompanyId,
                                    new
                                    {
                                        accessToken = ageroDispatch.AccessToken,
                                        dispatchNumber = ageroDispatch.DispatchId,
                                        eta = ageroDispatch.Eta.GetValueOrDefault(0),
                                        reason = ageroDispatch.EtaReason,
                                        signInResponse = oas
                                    });

                                new AgeroRestClient().AcceptDispatchRequest(
                                    accessToken: ageroDispatch.AccessToken,
                                    dispatchNumber: ageroDispatch.DispatchId,
                                    eta: ageroDispatch.Eta.GetValueOrDefault(0),
                                    reason: ageroDispatch.EtaReason);

                                logger.Info(MasterAccountTypes.Agero, "AcceptCall", "Sent AcceptRetry successfully", ageroDispatch.VendorId.ToString(), null,
                                    ageroDispatch.DispatchId.ToString(), qi.CompanyId,
                                    new
                                    {
                                        accessToken = ageroDispatch.AccessToken,
                                        dispatchNumber = ageroDispatch.DispatchId,
                                        eta = ageroDispatch.Eta.GetValueOrDefault(0),
                                        reason = ageroDispatch.EtaReason
                                    });

                                // we retried successfully; mark it as complete and return

                                DigitalDispatchService.LogAction(qi);
                                await sourceMessage.CompleteAsync();
                                DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);

                                return;
                            }
                            catch (Exception e2)
                            {
                                logger.Error(MasterAccountTypes.Agero, "AcceptCall", "AcceptRetry Failed: " + e2.Message,
                                    ageroDispatch.VendorId.ToString(), null, ageroDispatch.DispatchId.ToString(), qi.CompanyId,
                                    new
                                    {
                                        exception = e2,
                                        originalException = ae.Message,
                                        accessToken = ageroDispatch.AccessToken,
                                        dispatchNumber = ageroDispatch.DispatchId,
                                        eta = ageroDispatch.Eta.GetValueOrDefault(0),
                                        reason = ageroDispatch.EtaReason
                                    });
                            }
                        }

                        // If we got this far, it failed, we weren't able to retry successfully, mark it as failed.
                        await callRequest.UpdateStatus(CallRequestStatus.AcceptFailed, qi.OwnerUserId);
                        DigitalDispatchService.LogAction(qi);

                        return;
                    }

                    await callRequest.UpdateStatus(CallRequestStatus.AcceptSent, qi.OwnerUserId);

                    DigitalDispatchService.LogAction(qi);

                    await sourceMessage.CompleteAsync();
                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    break;

                case DigitalDispatchActionQueueItemType.OutgoingRejectCall:
                    if (callRequest == null)
                    {
                        await sourceMessage.DeadLetterAsync();
                        return;
                    }
                    qi.CallRequestId = callRequest.CallRequestId;
                    var rejectReason = MasterAccountReason.GetById(Convert.ToInt32(jsonObj.MasterAccountReasonId));

                    if (ageroDispatch == null)
                    {
                        await sourceMessage.DeadLetterAsync("Attempted to reject callRequestId " + callRequest.CallRequestId + ", but no AgeroDispatch present in database for it",
                            JsonExtensions.ToJson(callRequest, true));
                        await callRequest.UpdateStatus(CallRequestStatus.RejectFailed);
                        return;
                    }

                    callRequest.OwnerUserId = Convert.ToInt32(jsonObj.OwnerUserId);

                    var rci = (rejectReason != null ? (AgeroRejectReasonCode)Convert.ToInt32(rejectReason.Code) : AgeroRejectReasonCode.Other);

                    if (rci == AgeroRejectReasonCode.None)
                        rci = AgeroRejectReasonCode.Other;

                    try
                    {
                        new AgeroRestClient().RefuseDispatchRequest(
                            accessToken: ageroDispatch.AccessToken,
                            dispatchNumber: ageroDispatch.DispatchId,
                            reasonCodeId: rci);
                    }
                    catch (AgeroServiceUnavailable su)
                    {
                        await sourceMessage.CompleteAsync();
                        logger.Log(LogLevel.Error,
                            "Service Unavailable while trying to Reject dispatch ID " + ageroDispatch.DispatchId + "; " + su.Message, su);
                        await callRequest.UpdateStatus(CallRequestStatus.RejectFailed);
                        DigitalDispatchService.LogAction(qi);
                        return;
                    }
                    catch (AgeroDispatchExpiredException ade)
                    {
                        await sourceMessage.DeadLetterAsync(ade.Message, ade.Json);
                        logger.Log(LogLevel.Error, "Dispatch Expired exception occurred while trying to Reject dispatch ID " + ageroDispatch.DispatchId + "; " + ade.Message, ade);

                        await callRequest.UpdateStatus(CallRequestStatus.RejectFailedExpired);
                        return;
                    }
                    catch (AgeroException ae)
                    {
                        if (ae.Message.Contains("INVALID CLIENTID()"))
                        {
                            var oas = repairAgeroConnection(ageroDispatch, qi);

                            logger.Info(MasterAccountTypes.Agero, "RejectCall", "Reject Failed, attempting to save it with RejectRetry from " + ae.Message +
                                " error. OAuthSignOut and oAuthSignIn called.",
                                ageroDispatch.VendorId.ToString(), null, ageroDispatch.DispatchId.ToString(), qi.CompanyId,
                                new
                                {
                                    queueItemId = qi.QueueItemId,
                                    accessToken = ageroDispatch.AccessToken,
                                    dispatchNumber = ageroDispatch.DispatchId,
                                    reason = rci,
                                    signInResponse = oas
                                });

                            new AgeroRestClient().RefuseDispatchRequest(
                               accessToken: ageroDispatch.AccessToken,
                               dispatchNumber: ageroDispatch.DispatchId,
                               reasonCodeId: rci);

                            logger.Info(MasterAccountTypes.Agero, "RejectCall", "Sent RejectRetry successfully",
                                ageroDispatch.VendorId.ToString(), null, ageroDispatch.DispatchId.ToString(), qi.CompanyId,
                                new
                                {
                                    queueItemId = qi.QueueItemId,
                                    accessToken = ageroDispatch.AccessToken,
                                    dispatchNumber = ageroDispatch.DispatchId,
                                    reason = rci
                                });

                            // we retried successfully; mark it as complete and return

                            DigitalDispatchService.LogAction(qi);

                            await callRequest.UpdateStatus(CallRequestStatus.Rejected);

                            await sourceMessage.CompleteAsync();
                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);

                            return;
                        }
                        else
                        {
                            await sourceMessage.DeadLetterAsync(ae.Message, ae.Json);
                            logger.Log(LogLevel.Error, ae.Message + " <<< from dispatch Id #" + ageroDispatch.DispatchId, ae);
                            await callRequest.UpdateStatus(CallRequestStatus.RejectFailed);
                            return;
                        }
                    }

                    await callRequest.UpdateStatus(CallRequestStatus.Rejected);
                    await callRequest.Save();

                    DigitalDispatchService.LogAction(qi);

                    await sourceMessage.CompleteAsync();
                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    break;

                case DigitalDispatchActionQueueItemType.OutgoingRequestPhoneCall:
                    if (callRequest == null)
                    {
                        await sourceMessage.DeadLetterAsync();
                        return;
                    }
                    qi.CallRequestId = callRequest.CallRequestId;

                    if (ageroDispatch == null)
                    {
                        await sourceMessage.DeadLetterAsync("Attempted to request phone call for  callRequestId " + callRequest.CallRequestId +
                            ", but no AgeroDispatch present in database for it",
                               callRequest.ToJson(true));
                        await callRequest.UpdateStatus(CallRequestStatus.PhoneCallRequestFailed);
                        return;
                    }

                    try
                    {
                        new AgeroRestClient().RequestPhoneCallDispatchRequest(
                            accessToken: ageroDispatch.AccessToken,
                            dispatchNumber: ageroDispatch.DispatchId,
                            callbackNumber: jsonObj.PhoneNumber.ToString(),
                            name: jsonObj.FullName.ToString(),
                            phone: null);
                    }
                    catch (AgeroException ae)
                    {
                        await sourceMessage.DeadLetterAsync(ae.Message + " <<< from dispatch Id #" + ageroDispatch.DispatchId, ae.ToString());
                        logger.Log(LogLevel.Error, ae.Message + " <<< from dispatch Id #" + ageroDispatch.DispatchId, ae);
                        await callRequest.UpdateStatus(CallRequestStatus.PhoneCallRequestFailed);
                        return;
                    }

                    await callRequest.UpdateStatus(CallRequestStatus.PhoneCallRequested);

                    qi.CallRequestId = callRequest.CallRequestId;
                    DigitalDispatchService.LogAction(qi);

                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    break;

                case DigitalDispatchActionQueueItemType.OutgoingCallCanceled:
                    if (callRequest == null)
                    {
                        await sourceMessage.DeadLetterAsync();
                        return;
                    }
                    qi.CallRequestId = callRequest.CallRequestId;
                    MasterAccountReason cancelReason = MasterAccountReason.GetById(Convert.ToInt32(jsonObj.MasterAccountReasonId));
                    if (cancelReason != null)
                    {
                        var cancelAgeroReason = (DispatchStatusReason)Convert.ToInt32(cancelReason.Code);

                        new AgeroRestClient().CancelDispatchRequest(
                            accessToken: ageroDispatch.AccessToken,
                            dispatchNumber: ageroDispatch.DispatchId,
                            code: CancelDispatchStatusCode.SPCancel,
                            reason: cancelAgeroReason);

                        await callRequest.UpdateStatus(CallRequestStatus.Cancelled);

                        DigitalDispatchService.LogAction(qi);

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    }
                    else
                    {
                        //DigitalDispatchService.LogAction(qi);

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                    }
                    break;

                case DigitalDispatchActionQueueItemType.OutgoingStatusUpdate:
                    if (Core.GetAppSetting("Towbook.DispatchService.Agero.DisableOutgoingStatusUpdates") == "1" ||
                        Extric.Towbook.Core.GetRedisValue("ops/mcds/Agero_RefuseDispatchRequest_Disable") == "1")
                    {
                        await sourceMessage.CompleteAsync();
                        logger.Log(LogLevel.Warn, $"Agero status updates are disabled");
                        return;
                    }

                    CallRequest cr = CallRequest.GetById(Convert.ToInt32(jsonObj.CallRequestId));
                    AgeroDispatch ageroStatusDispatch = AgeroDispatch.GetByCallRequestId(cr.CallRequestId);
                    if (ageroStatusDispatch == null)
                    {
                        await sourceMessage.DeadLetterAsync();
                        return;
                    }

                    var statusClient = new AgeroRestClient();

                    var code = ChangeStatusDispatchStatusCode.Unassigned;

                    double? lat = (double?)jsonObj.Latitude;
                    double? lng = (double?)jsonObj.Longitude;

                    string src = jsonObj.Source;

                    int newStatusId = (int)jsonObj.NewStatusId;

                    if (newStatusId == Status.Dispatched.Id)
                        code = ChangeStatusDispatchStatusCode.Assigned;
                    else if (newStatusId == Status.EnRoute.Id)
                        code = ChangeStatusDispatchStatusCode.InRoute;
                    else if (newStatusId == Status.Completed.Id)
                        code = ChangeStatusDispatchStatusCode.JobCleared;
                    else if (newStatusId == Status.BeingTowed.Id)
                        code = ChangeStatusDispatchStatusCode.TowInProgress;
                    else if (newStatusId == Status.AtSite.Id)
                        code = ChangeStatusDispatchStatusCode.OnScene;

                    if (ChangeStatusDispatchStatusCode.Unassigned == code)
                    {
                        await sourceMessage.CompleteAsync();
                        logger.Log(LogLevel.Warn, $"Skipping STATUS_UPDATE Assignment. {code} isn't valid. Towbook status is set to " + newStatusId);
                        return;
                    }

                    var cds = await Entry.GetByIdNoCacheAsync(cr.DispatchEntryId.Value);

                    if (cds.Reason.IsGoa())
                    {
                        await sourceMessage.CompleteAsync();

                        logger.Log(LogLevel.Warn, $"Skipping STATUS_UPDATE Assignment. {code} isn't valid. Call reason is GOA. " + cds.Reason.Name);
                        return;
                    }

                    
                    int driverId = 0;
                    Driver driver = null;
                    if (jsonObj.DriverId != null)
                    {
                        if (!int.TryParse(jsonObj.DriverId.ToString(), out driverId))
                            driverId = cds.Drivers.FirstOrDefault();
                    }
                    if (driverId > 0)
                        driver = await Driver.GetByIdAsync(driverId);

                    try
                    {
                        statusClient.ChangeDispatchStatus(
                           accessToken: ageroStatusDispatch.AccessToken,
                           dispatchNumber: ageroStatusDispatch.DispatchId,
                           code: code,
                           reason: ageroStatusDispatch.EtaReason.ToString(),
                           eta: ageroStatusDispatch.Eta.GetValueOrDefault(),
                           extJobId: cds.CallNumber,
                           source: src,
                           latitude: lat,
                           longitude: lng,
                           statusTime: DateTime.Now,
                           externalDriverId: driverId);

                        if (lat.GetValueOrDefault() != 0)
                        {
                            logger.Info(MasterAccountTypes.Agero, "OutgoingStatusUpdate", "Sent STATUS_UPDATE with GPS",
                                ageroStatusDispatch.VendorId.ToString(), null, ageroStatusDispatch.DispatchId.ToString(), cds.CompanyId,
                                new
                                {
                                    statusCode = code,
                                    latitude = lat,
                                    longitude = lng,
                                    driverId = driverId,
                                    source = src
                                });
                        }
                        else
                        {
                            logger.Warn(MasterAccountTypes.Agero, "OutgoingStatusUpdate", "Sent STATUS_UPDATE without GPS",
                                ageroStatusDispatch.VendorId.ToString(), null, ageroStatusDispatch.DispatchId.ToString(), cds.CompanyId,
                                new
                                {
                                    statusCode = code,
                                    latitude = lat,
                                    longitude = lng,
                                    driverId = driverId,
                                    source = src
                                });
                        }
                    }
                    catch (AgeroException ae)
                    {
                        logger.Warn(MasterAccountTypes.Agero, "OutgoingStatusUpdate", "Failure: " + ae.Message,
                               ageroStatusDispatch.VendorId.ToString(), null, ageroStatusDispatch.DispatchId.ToString(), cds.CompanyId,
                               new
                               {
                                   statusCode = code,
                                   latitude = lat,
                                   longitude = lng,
                                   driverId = driverId,
                                   source = src,
                                   exception = ae
                               });
                    }

                    if (newStatusId == 5 && jsonObj.CompletionReasonId != null)
                    {
                        int CompletionReasonId = (int)jsonObj.CompletionReasonId;

                        var marc = MasterAccountReason.GetById(CompletionReasonId);

                        if (marc != null)
                        {
                            AgeroRestClient.AgeroSubStatusModel model = null;
                            try
                            {
                                model = new AgeroRestClient.AgeroSubStatusModel()
                                {
                                    Id = (-(ActivityLogging.ActivityLogCloudService.NextId())).ToString(),
                                    VendorId = ageroStatusDispatch.VendorId.ToString(),
                                    DispatchRequestNumber = ageroStatusDispatch.DispatchId.ToString(),
                                    PurchaseOrderNumber = cds.PurchaseOrderNumber,
                                    ReferenceNumber = "",
                                    CreateDate = DateTime.Now,
                                    DispatchStatus = newStatusId.ToString(),
                                    DispatchSubStatus = marc.Code,
                                    Latitude = (decimal)lat.GetValueOrDefault(),
                                    Longitude = (decimal)lng.GetValueOrDefault(),
                                };

                                statusClient.SendSubStatus(model, false);

                                logger.Info(MasterAccountTypes.Agero, "OutgoingStatusUpdate", "Sent SubStatus: ",
                                       ageroStatusDispatch.VendorId.ToString(), null, ageroStatusDispatch.DispatchId.ToString(), cds.CompanyId,
                                       new
                                       {
                                           json = model.ToJson()
                                       });
                            }
                            catch (Exception yyy)
                            {
                                logger.Error(MasterAccountTypes.Agero, "OutgoingStatusUpdate", "Sent SubStatus FAILED: ",
                                       ageroStatusDispatch.VendorId.ToString(), null, ageroStatusDispatch.DispatchId.ToString(), cds.CompanyId,
                                       new
                                       {
                                           exception = yyy,
                                           json = model.ToJson()
                                       });
                            }
                        }
                    }

                    if (code == ChangeStatusDispatchStatusCode.Assigned)
                    {
                        var e = cds;
                        if (driverId != 0)
                        {
                            try
                            {
                                statusClient.AssignExternalDriverToDispatch(
                                    accessToken: ageroStatusDispatch.AccessToken,
                                    dispatchNumber: ageroStatusDispatch.DispatchId,
                                    externalDriverId: e.DriverId,
                                    driver: new AgeroRestClient.AssignExternalDriverToDispatchModelDriverProfile()
                                    {
                                        ExternalDriverId = driverId.ToString(),
                                        Name = driver?.Name,
                                        OfficePhone = Core.FormatPhoneWithDashesOnly(e.Company.Phone),
                                        OfficeEmail = e.Company.Email,
                                        GpsTracking = true,
                                        OnDuty = true
                                    },
                                    vendorId: ageroStatusDispatch.VendorId);

                                logger.Info(MasterAccountTypes.Agero, "AssignDriver", "AssignExternalDriverToDispatch succeeded",
                                    ageroStatusDispatch.VendorId.ToString(), null, ageroStatusDispatch.DispatchId.ToString(), cds.CompanyId,
                                    new
                                    {
                                        name = driver?.Name,
                                        officePhone = Core.FormatPhoneWithDashesOnly(e.Company.Phone),
                                        officeEmail = e.Company.Email,
                                        driverId = driverId,
                                        source = src
                                    });
                            }
                            catch (AgeroException ae)
                            {
                                logger.Warn(MasterAccountTypes.Agero, "AssignDriver", "AssignExternalDriverToDispatch failed: " + ae.Message,
                                    ageroStatusDispatch.VendorId.ToString(), null, ageroStatusDispatch.DispatchId.ToString(), cds.CompanyId,
                                    new
                                    {
                                        statusCode = code,
                                        latitude = lat,
                                        longitude = lng,
                                        driverId = driver?.Id,
                                        source = src
                                    });
                            }
                        }
                    }

                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    await sourceMessage.CompleteAsync();
                    break;

                case DigitalDispatchActionQueueItemType.OutgoingSharePhoto:
                case DigitalDispatchActionQueueItemType.OutgoingShareSignature:

                    async Task SharePhotoOrSignature(int callRequestId, string json)
                    {
                        try
                        {
                            var pm = JsonConvert.DeserializeObject<Extric.Towbook.Integration.MotorClubs.Model.PhotoPayload>(json);

                            cr = CallRequest.GetById(callRequestId);
                            if (cr == null)
                            {
                                logger.Log(LogLevel.Error, "CR" + cr.CallRequestId + "/C" + cr.CompanyId + "/SharePhoto: Couldn't find CallRequestId.");

                                await sourceMessage.CompleteAsync();
                                return;
                            }

                            qi.CallRequestId = cr.CallRequestId;

                            var ad = AgeroDispatch.GetByCallRequestId(cr.CallRequestId);

                            if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingShareSignature)
                            {
                                var sig = Signature.GetById((int)jsonObj.SignatureId);
                                // todo: send
                            }
                            else if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingSharePhoto)
                            {
                                var pho = Dispatch.Photo.GetById((int)jsonObj.PhotoId);
                                if (pho == null)
                                {
                                    throw new Exception("no such signature." + qi.JsonObject);
                                }

                                var arc = new AgeroRestClient();

                                string po = null;

                                try
                                {
                                    po = JsonConvert.DeserializeObject<DispatchDetails>(ad.CallJson).PurchaseOrderNumber;
                                }
                                catch
                                {

                                }

                                arc.SharePhoto(new Agero.AgeroRestClient.AgeroPhotoModel()
                                {
                                    PurchaseOrderNumber = po,
                                    DispatchRequestNumber = ad.DispatchId.ToString(),
                                    Latitude = pm.Latitude,
                                    Longitude = pm.Longitude,
                                    Id = pho.Id,
                                    FileSize = pho.FileSize,
                                    DispatchStatus = pho.DispatchEntryStatusId.ToString(),
                                    VendorId = ad.VendorId.ToString(),
                                    Url = FileUtility.GetPresignedUrlForDownloadFromClient(pho.Location.Replace("%1", qi.CompanyId.ToString()), pho.ContentType, 60 * 24 * 180),
                                });

                                DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                            }

                        }
                        catch (Exception y)
                        {
                            logger.Error(MasterAccountTypes.Agero, "SharePhoto", "Error sending photo: " + y.Message,
                                companyId: qi.CompanyId,
                                data: y,
                                callRequestId: callRequestId);
                        }

                        await sourceMessage.CompleteAsync();
                        DigitalDispatchService.LogAction(qi);
                        return;
                    }

                    await SharePhotoOrSignature(Convert.ToInt32(jsonObj.CallRequestId), qi.JsonObject);

                    break;

                default:
                    await sourceMessage.DeadLetterAsync();

                    logger.Error(MasterAccountTypes.Agero, "UnknownMessageType",
                        "Queue item doesn't have a Type set that we have a implementation written for",
                        companyId: qi.CompanyId,
                        data: new { messageId = sourceMessage.Message.MessageId, queueItem = qi });
                    return;
            }
        }

        private OAuthSignInResponse repairAgeroConnection(AgeroDispatch dispatch, DigitalDispatchActionQueueItem qi)
        {

            var user = User.GetById(qi.OwnerUserId.Value);

            var arc = new AgeroRestClient();
            var ars = AgeroSession.GetByAccessToken(dispatch.AccessToken);
            var clientId = user.PrimaryCompanyId.ToString();

            if (ars != null)
            {
                if (ars.ClientId != null)
                {
                    clientId = ars.ClientId;
                }
                else
                {
                    if (user.Id < 100)
                    {
                        if (ars != null)
                        {
                            var sc = Company.SharedCompany.GetByCompanyId(ars.CompanyId).FirstOrDefault();
                            if (sc != null)
                            {
                                clientId = sc.CompanyId.ToString();
                            }
                        }
                    }
                }
            }

            arc.OAuthSignOut(clientId, dispatch.AccessToken);
            Thread.Sleep(5000);
            var oas = arc.OAuthSignIn(clientId, dispatch.AccessToken);

            return oas;
        }

        public static async Task HandleNsdQueueOutgoingMessage(DigitalDispatchActionQueueItem qi, dynamic jsonObj, ProcessMessageEventArgs sourceMessage)
        {
            if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingLogin)
            {
                if (qi.AccountId.HasValue)
                {
                    var errors = new Dictionary<string, string>();
                    bool failed = false;
                    int n = 0;

                    foreach (var x in AllstateContractor.GetByAccountId(qi.AccountId.Value))
                    {
                        // first of all, update IsLoggedIn=true so that automatic logins will log this client back in if 
                        // a LOF event is received
                        x.IsLoggedIn = true;
                        x.Save();

                        int userId = 0;
                        if (jsonObj.OwnerUserId != null)
                            userId = Convert.ToInt32(jsonObj.OwnerUserId);

                        string username = AccountKeyValue.GetByAccount(qi.CompanyId.Value, qi.AccountId.Value, Provider.Towbook.ProviderId, "McUsername").FirstOrDefault()?.Value;
                        string password = AccountKeyValue.GetByAccount(qi.CompanyId.Value, qi.AccountId.Value, Provider.Towbook.ProviderId, "McPassword").FirstOrDefault()?.Value;

                        if (username == null || password == null)
                        {
                            errors[x.ContractorId] = "Missing username or password";
                            continue;
                        }

                        var resp = await x.NsdLogin(userId, username, password);
                        if (!resp.Success)
                        {
                            failed = true;
                            logger.Warn(x.MasterAccountId, "Login", resp.Message,
                                x.ContractorId,
                                companyId: x.CompanyId);

                            errors.Add(n + ": " + x.ContractorId, "Failed:" + resp.Message);
                            x.UpdateLoginStatus(AllstateContractorLoginStatus.LoggedOut);
                        }
                        else
                        {
                            errors.Add(n + ": " + x.ContractorId, "Logged in");

                            x.UpdateLoginStatus(AllstateContractorLoginStatus.LoggedIn);

                            logger.Info(x.MasterAccountId, "Login", "Login succeeded",
                             x.ContractorId, companyId: x.CompanyId);
                        }

                        n++;
                    }

                    await PushNotificationProvider.BackgroundJobStatusUpdate(qi.CompanyId.Value,
                        qi.QueueItemId,
                        "digitaldispatch_login",
                        !failed,
                        "NSD Login Status",
                        errors);

                    await sourceMessage.CompleteAsync();
                    return;
                }
                else
                {
                    await sourceMessage.CompleteAsync();
                    return;
                }
            }
            else if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingLogoff)
            {
                if (qi.AccountId.HasValue)
                {
                    var errors = new Dictionary<string, string>();
                    foreach (var x in AllstateContractor.GetByAccountId(qi.AccountId.Value))
                    {
                        // first of all, update IsLoggedIn=false so that automatic logins wont try to log
                        // this client back in.
                        x.IsLoggedIn = false;
                        x.Save();

                        string username = AccountKeyValue.GetByAccount(qi.CompanyId.Value, qi.AccountId.Value, Provider.Towbook.ProviderId, "McUsername").FirstOrDefault()?.Value;
                        string password = AccountKeyValue.GetByAccount(qi.CompanyId.Value, qi.AccountId.Value, Provider.Towbook.ProviderId, "McPassword").FirstOrDefault()?.Value;

                        if (username == null || password == null)
                        {
                            errors[x.ContractorId] = "Missing username or password";
                            continue;
                        }

                        int userId = 0;

                        if (jsonObj.OwnerUserId != null)
                        {
                            userId = Convert.ToInt32(jsonObj.OwnerUserId);
                        }

                        if (!await x.NsdLogout(userId, username, password))
                        {
                            errors.Add(x.ContractorId, "Failed");
                        }
                        else
                        {
                            errors.Add(x.ContractorId, "Logged out");
                        }
                    }

                    await PushNotificationProvider.BackgroundJobStatusUpdate(qi.CompanyId.Value,
                        qi.QueueItemId,
                        "digitaldispatch_logout",
                        true, // we don't care if logout failed - just let the user know the request finished.
                        "NSD Logout Status",
                        errors);

                    await sourceMessage.CompleteAsync();
                    return;
                }
                else
                {
                    await sourceMessage.CompleteAsync();
                    return;
                }
            }
            else if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingStatusUpdate)
            {
                var outbound = new List<Integrations.MotorClubs.Nsd.RequestContext>();

                CallRequest callRequest = null;

                if (jsonObj.Id != null)
                    callRequest = CallRequest.GetById(Convert.ToInt32(jsonObj.Id));
                else if (jsonObj.CallRequestId != null)
                    callRequest = CallRequest.GetById(Convert.ToInt32(jsonObj.CallRequestId));

                var nsdDispatch = NsdDispatch.GetByCallRequestId(callRequest.CallRequestId);
                if (nsdDispatch != null)
                {
                    await NsdService.HandleOutgoing(MasterAccountTypes.Nsd, qi, sourceMessage);
                    return;
                }
                int driverId = 0;

                if (callRequest != null && callRequest.DispatchEntryId != null)
                {
                    var ad = AllstateDispatch.GetByCallRequestId(callRequest.CallRequestId);
                    var r = await Entry.GetByIdNoCacheAsync(callRequest.DispatchEntryId.Value);

                    if (r != null)
                    {
                        if (r.DriverId > 0)
                        {
                            driverId = r.DriverId;
                        }
                    }

                    var driverName = (await Driver.GetByIdAsync(driverId))?.Name;

                    string firstName = "", lastName = "";

                    if (driverName != null && driverName.IndexOf(" ") > 0)
                    {
                        firstName = JsonExtensions.Truncate(driverName.Substring(0, driverName.IndexOf(" ")).Trim(), 50);
                        lastName = JsonExtensions.Truncate(driverName.Substring(driverName.IndexOf(" ") + 1).Trim(), 50);
                    }

                    string statusId = "0";

                    int newStatusId = (int)jsonObj.NewStatusId;

                    switch (newStatusId)
                    {
                        case 2:// en route.
                            statusId = "2";
                            break;

                        case 3: // on scene
                            statusId = "3";
                            break;

                        case 5: // complete
                            statusId = "6";
                            break;
                    }

                    double? lat = (double?)jsonObj.Latitude;
                    double? lng = (double?)jsonObj.Longitude;

                    outbound.Add(new Integrations.MotorClubs.Nsd.RequestContext()
                    {
                        Key = "Towbook",
                        StatusID = statusId,
                        DriverID = driverId.ToString(),
                        Fname = firstName,
                        Lname = lastName,
                        Phone = Core.FormatPhoneWithDashesOnly(r.Company.Phone),
                        Latitude = lat.GetValueOrDefault().ToString("0.000000"),
                        Longitude = lng.GetValueOrDefault().ToString("0.000000"),
                        EquipmentType = "FB",
                        DeviceType = "Mobile",
                        Zip = "0",
                        PO = r.PurchaseOrderNumber,
                        ResponseID = ad.ResponseId,
                        Timestamp = DateTime.Now.ToString()
                    });

                    Integrations.MotorClubs.Nsd.NsdStatusSharingClient.Share(outbound);
                }

                await sourceMessage.CompleteAsync();
                return;
            }

            var request = new CallRequest();
            var dispatch = new AllstateDispatch();

            request = CallRequest.GetById(Convert.ToInt32(jsonObj.Id));
            if (request == null)
            {
                if (jsonObj.CallRequestId != null)
                    request = CallRequest.GetById(Convert.ToInt32(jsonObj.CallRequestId));

                if (request == null)
                {
                    await sourceMessage.CompleteAsync();
                    return;
                }

                
            }

            dispatch = AllstateDispatch.GetByCallRequestId(request.CallRequestId);
            if (dispatch == null)
            {
                var nsdDispatch = NsdDispatch.GetByCallRequestId(request.CallRequestId);
                if (nsdDispatch != null)
                {
                    await NsdService.HandleOutgoing(MasterAccountTypes.Nsd, qi, sourceMessage);
                    return;
                }

                if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingAcceptCall)
                {
                    await sourceMessage.DeadLetterAsync("Attempted to accept callRequestId " + request.CallRequestId + ", but no DigitalDispatch present in database for it",
                    request.ToJson(true));
                    await request.UpdateStatus(CallRequestStatus.AcceptFailed);
                }
                else if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingRejectCall)
                {
                    await sourceMessage.DeadLetterAsync("Attempted to reject callRequestId " + request.CallRequestId + ", but no DigitalDispatch present in database for it",
                    request.ToJson(true));
                    await request.UpdateStatus(CallRequestStatus.RejectFailed);
                }
                else if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingRequestPhoneCall)
                {
                    await sourceMessage.DeadLetterAsync("Attempted to phone call request callRequestId " + request.CallRequestId + ", but no DigitalDispatch present in database for it",
                    request.ToJson(true));
                    await request.UpdateStatus(CallRequestStatus.PhoneCallRequestFailed);
                }
                else if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingCallStatusResponse)
                {
                    await sourceMessage.DeadLetterAsync(
                        $"Attempted to send call status update for callRequestId {request.CallRequestId}, but no DigitalDispatch present in database for it",
                        request.ToJson(true));
                }

                return;
            }

            request.OwnerUserId = Convert.ToInt32(jsonObj.OwnerUserId);

            if (request.OwnerUserId == 0)
                request.OwnerUserId = qi.OwnerUserId;

            var returnProxy = new Allstate.RETMessage();

            using (var nsd = new Integrations.MotorClubs.Nsd.NsdService.digitaldispatchSoapClient(new BasicHttpBinding(BasicHttpSecurityMode.Transport),
                 new EndpointAddress("https://web1.nsddispatch.com/digitaldispatch/digitaldispatch.asmx")))
            {
                string contactName = "Dispatch";

                if (request.OwnerUserId != null)
                {
                    var user = await User.GetByIdAsync(request.OwnerUserId.Value);

                    if (user != null)
                        contactName = user.FullName;
                }

                var header = new Allstate.DDMessageHeader(dispatch.ContractorId, "RET", key: "TOWBOOK");
                header.ResponseID = dispatch.ResponseId; // ResponseId must be sent using the one that the original call came in with.
                returnProxy.JobID = dispatch.DispatchId;
                returnProxy.ContactName = contactName;

                var msg = new Allstate.DDMessage();
                msg.DDMessageHeader = header;
                msg.DDContent = returnProxy;
                msg.DDMessageHeader.ResponseType = "ACK";

                switch (qi.Type)
                {
                    case DigitalDispatchActionQueueItemType.OutgoingAcceptCall:
                        dispatch.Eta = jsonObj.Eta;
                        dispatch.Save();

                        returnProxy.ServiceProviderResponse = 0;
                        returnProxy.ETA = dispatch.Eta.GetValueOrDefault(0);
                        returnProxy.RejectDescription = string.Empty;

                        if (jsonObj.MasterAccountReasonId != null)
                        {
                            MasterAccountReason mar = MasterAccountReason.GetById(Convert.ToInt32(jsonObj.MasterAccountReasonId));

                            logger.Log(LogLevel.Info, "NSD/" + dispatch.DispatchId + "/Accept... ReasonId=" + jsonObj.MasterAccountReasonId);

                            if (mar != null)
                            {
                                if (mar.MasterAccountId != 7)
                                {
                                    logger.Log(LogLevel.Warn, "NSD/" + dispatch.DispatchId + "/Accept... ReasonId=" +
                                        jsonObj.MasterAccountReasonId + " belongs to " + mar.MasterAccountId + " ... should belong to 4 (Quest).");
                                }
                            }
                        }

                        try
                        {
                            logger.Log(LogLevel.Info, "NSD/Accepting callRequestId " + dispatch.DispatchId);

                            var xml = msg.GetXmlElement();
                            logger.Log(LogLevel.Info, "NSD/" + dispatch.DispatchId + "/Accept... XML=" + xml);

                            var resp4 = nsd.NSDDispatch(xml).OuterXml;

                            logger.Log(LogLevel.Info, "NSD/" + dispatch.DispatchId + "/Accept/Response=" + resp4);

                            await request.Save();
                            await sourceMessage.CompleteAsync();

                            qi.CallRequestId = request.CallRequestId;
                            DigitalDispatchService.LogAction(qi);

                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                        }
                        catch (Exception exc)
                        {
                            logger.Log(LogLevel.Error, "Error accepting NSD call..." + exc.Message + "\n" + exc.ToJson());
                            await sourceMessage.DeadLetterAsync("Attempted to accept callRequestId " + request.CallRequestId + ", but  error occurred",
                                exc.ToJson(true));
                        }
                        break;

                    case DigitalDispatchActionQueueItemType.OutgoingRejectCall:
                        MasterAccountReason rejectReason = MasterAccountReason.GetById(Convert.ToInt32(jsonObj.MasterAccountReasonId));
                        if (rejectReason == null)
                        {
                            await sourceMessage.DeadLetterAsync("Attempted to reject callRequestId " + request.CallRequestId + ", but no AllstateDispatch present in database for it",
                                request.ToJson(true));
                            await request.UpdateStatus(CallRequestStatus.RejectFailed);
                            return;
                        }

                        dispatch.EtaReason = RejectDispatchReason.None; //  rejectReason.Code;
                        dispatch.Save();

                        returnProxy.ServiceProviderResponse = 1;
                        returnProxy.RejectDescription = rejectReason.Code;

                        var xmlReject = msg.GetXmlElement();
                        logger.Log(LogLevel.Info, "NSD/" + dispatch.DispatchId + "/Reject... XML=" + xmlReject);

                        var resp = nsd.NSDDispatch(xmlReject).OuterXml;

                        logger.Log(LogLevel.Info, "NSD/" + dispatch.DispatchId + "/Reject/Response=" + resp);

                        // ToDo: Parse response to see if everything went ok (when we get an actual response :))

                        await request.UpdateStatus(CallRequestStatus.Rejected);
                        await request.Save();

                        qi.CallRequestId = request.CallRequestId;
                        DigitalDispatchService.LogAction(qi);

                        await sourceMessage.CompleteAsync();
                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);

                        break;

                    case DigitalDispatchActionQueueItemType.OutgoingRequestPhoneCall:
                        returnProxy.ServiceProviderResponse = 2;
                        returnProxy.RejectDescription = string.Empty;

                        var xmlRequestPhoneCall = msg.GetXml();

                        var resp3 = nsd.NSDDispatch(msg.GetXmlElement()).OuterXml;

                        logger.Log(LogLevel.Info, "NSD/" + dispatch.DispatchId + "/RequestPhoneCall/Response=" + resp3);

                        if (qi.ScheduledDate != null)
                        {
                            // if a scheduled date is set, then it's an automated phone call that our system sends to prevent the
                            // provider from losing the job.
                            await request.UpdateStatus(CallRequestStatus.PhoneCallRequested);
                        }
                        else
                        {
                            await request.UpdateStatus(CallRequestStatus.AutomatedPhoneCallRequested);
                        }
                        await request.Save();

                        qi.CallRequestId = request.CallRequestId;
                        DigitalDispatchService.LogAction(qi);

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                        await sourceMessage.CompleteAsync();

                        break;

                    default:
                        await sourceMessage.DeadLetterAsync("NSD: No implementation written", qi.ToJson());

                        logger.LogEvent("NSD/Queue item doesn't have a Type set that we have a implementation written for... MessageId {0}, Body = {1}", qi.CompanyId, LogLevel.Error, sourceMessage.Message.MessageId, qi.ToJson());
                        return;
                }
            }
        }

        #endregion

        #region Synchronization Handlers

        private async Task HandleAgeroQueueSynchronizationMessage(DigitalDispatchActionQueueItem qi, dynamic jsonObj, ProcessMessageEventArgs sourceMessage)
        {
            switch (qi.Type)
            {
                case DigitalDispatchActionQueueItemType.ExtSyncDrivers:

                    AgeroSession driverSession = AgeroSession.GetByAccountId(qi.AccountId.Value);
                    if (driverSession == null)
                    {
                        if (sourceMessage != null)
                            await sourceMessage.DeadLetterAsync();
                        return;
                    }

                    AgeroRestClient driverClient = new AgeroRestClient();
                    DriverKey driverKey = DriverKey.GetByProviderId(Provider.Agero.ProviderId, "AgeroDriverId");


                    ExternalDriverProfile[] ageroDrivers = new AgeroRestClient().ExternalGetDriverProfiles(driverSession.VendorId);
                    List<Driver> towbookDrivers = Driver.GetByCompany(await Company.Company.GetByIdAsync(qi.CompanyId.Value));
                    DriverKeyValue[] integratedDrivers = DriverKeyValue.GetByCompany(qi.CompanyId.Value, Provider.Agero.ProviderId).ToArray();

                    // loop through towbook drivers and check if they exist in agero
                    foreach (var tbDriver in towbookDrivers)
                    {
                        var c = await Company.Company.GetByIdAsync(tbDriver.CompanyId);
                        // find relative id for agero to match
                        var syncDriver = integratedDrivers.Where(o => o.DriverId == tbDriver.Id).FirstOrDefault();

                        // driver exists in towbook but not synced in agero -> export driver to agero
                        if (syncDriver == null)
                        {
                            try
                            {

                                int newDriverId = driverClient.ExternalExportDriverProfile(driverSession.VendorId,
                                    new ExternalDriverProfile()
                                    {
                                        Equipment = null,
                                        GpsTracking = false,
                                        Name = tbDriver.Name,
                                        OfficeEmail = c.Email,
                                        OfficePhone = Core.FormatPhone(c.Phone),
                                        ExternalDriverId = tbDriver.Id.ToString(),
                                        ProfileLastUpdatedAt = DateTime.Now,
                                        ProfilePictureUrl = string.Empty
                                    });

                                // create the relative driver on towbook integration table
                                DriverKeyValue newIntegrationDriver = new DriverKeyValue()
                                {
                                    DriverId = tbDriver.Id,
                                    KeyId = driverKey.Id,
                                    Value = newDriverId.ToString()
                                };
                                newIntegrationDriver.Save();
                            }
                            catch (AgeroResourceExistsException)
                            {
                                // create the relative driver on towbook integration table
                                var newIntegrationDriver = new DriverKeyValue()
                                {
                                    DriverId = tbDriver.Id,
                                    KeyId = driverKey.Id,
                                    Value = "pre_existing"
                                };
                                newIntegrationDriver.Save();
                            }
                        }
                        else
                        {
                            // driver exists in towbook and was already synced to agero -> update fields if needed
                            var ageroDriver = ageroDrivers.Where(o => o.DriverId == syncDriver.Value).FirstOrDefault();
                            if (ageroDriver != null &&
                                ((ageroDriver.Name != tbDriver.Name) ||
                                 (ageroDriver.OfficeEmail != c.Email) ||
                                 (Core.FormatPhone(ageroDriver.OfficePhone) != Core.FormatPhone(c.Phone))))
                            {
                                int ageroDriverId = 0;
                                if (Int32.TryParse(ageroDriver.DriverId, out ageroDriverId))
                                {
                                    driverClient.ExternalUpdateDriverProfile(driverSession.VendorId,
                                        ageroDriverId,
                                        new ExternalDriverProfile()
                                        {
                                            Equipment = null,
                                            GpsTracking = false,
                                            Name = tbDriver.Name,
                                            OfficeEmail = c.Email,
                                            OfficePhone = Core.FormatPhone(c.Phone),
                                            ProfileLastUpdatedAt = DateTime.Now,
                                            ProfilePictureUrl = string.Empty,
                                            ExternalDriverId = tbDriver.Id.ToString(),
                                        });
                                }
                            }
                        }
                    }


                    /*
                     * this might screw people up.. dont delete drivers now.
                     * 
                    foreach (var ad in ageroDrivers)
                    {
                        var syncDriver = integratedDrivers.Where(o => o.Value == ad.DriverId).FirstOrDefault();

                        // driver exists on agero but not in towbook -> delete it from agero
                        if (syncDriver == null)
                            driverClient.ExternalDeleteDriverProfile(driverSession.VendorId, Convert.ToInt32(syncDriver.Value));
                    }
                    */
                    break;


                case DigitalDispatchActionQueueItemType.ExtDispatchSignatureCreated:

                    AgeroSession signatureSession = AgeroSession.GetByAccountId(qi.AccountId.Value);
                    if (signatureSession == null)
                    {
                        if (sourceMessage != null)
                            await sourceMessage.DeadLetterAsync();
                        return;
                    }

                    int dispatchId = Convert.ToInt32(jsonObj.DispatchEntryId);
                    var photo = (await Dispatch.Photo.GetByDispatchEntryIdAsync(dispatchId)).FirstOrDefault(o => o.Description == "__INVOICE_SIGNATURE");

                    if (photo != null)
                    {
                        string localPath = photo.Location.Replace("%1", qi.CompanyId.ToString());
                        FileStream fs = new FileStream(localPath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite);
                        try
                        {
                            byte[] buffer = new byte[fs.Length];
                            fs.Read(buffer, 0, buffer.Length);

                            CallRequest cr = await CallRequest.GetByDispatchEntryId(dispatchId);
                            AgeroDispatch ad = AgeroDispatch.GetByCallRequestId(cr.CallRequestId);

                            new AgeroRestClient().AddDispatchSignature(
                                accessToken: signatureSession.AccessToken,
                                dispatchNumber: ad.DispatchId,
                                signeeName: string.Empty,
                                signeeType: string.Empty,
                                locationType: string.Empty,
                                signatureReason: string.Empty,
                                latitude: 0,
                                longitude: 0,
                                dispatchStatusCode: string.Empty,
                                signatureFileName: string.Empty,
                                signatureFile: buffer);
                        }
                        finally
                        {
                            fs.Close();
                        }
                    }

                    break;
                default:
                    if (sourceMessage != null)
                        await sourceMessage.DeadLetterAsync();

                    logger.LogEvent("Queue item doesn't have a Type set that we have a implementation written for... MessageId {0}, Body = {1}", qi.CompanyId, LogLevel.Error, (sourceMessage != null ? sourceMessage.Message.MessageId : "_INTERNAL"), qi.ToJson());
                    return;
            }
        }

        #endregion

        private static async Task IsscConnect(IsscConfig conf)
        {
            await DigitalDispatchService.RequestInternalEvent(null, null, null, DigitalDispatchService.InternalEventType.Connection,
                DateTime.Now.AddSeconds(30).ToUniversalTime(),
                new Dictionary<string, object> { { "IsscConfigId", conf.IsscConfigId } });

            new IsscRestClient(conf).Connect("https://api.towbook.com/receivers/issc/" + conf.Guid.ToString("N"));
        }

        private async Task<Task> DigitalDispatchSync_ExceptionReceived(ProcessErrorEventArgs e)
        {
            logger.LogExceptionEvent("Queue Process Unhandled Exception: " + e.Exception.Message, e.Exception);
            
            await ApplicationHelper.CheckExhaustedPortsException(e.Exception, _appLifetime, logger);

            return Task.CompletedTask;
        }

        public class AccountLocationModel
        {
            public int companyId { get; set; }
            public int accountId { get; set; }
            public int facilityId { get; set; }
        }

        [Trace]
        internal static async Task<CallRequest> CreateCallRequest(
            dynamic jsonObj,
            MotorClubName mcName,
            bool deliver = true,
            int masterAccountId = 0)
        {
            logger.Log(LogLevel.Info, "Creating Call Request for {0} ", mcName);

            switch (mcName)
            {
                case MotorClubName.Geico:
                    // i know this is bad, we deserialize before this, then we serialize, then we deserialize again.. 
                    // but this was the quickest way for now beyond refactoring this whole method and how it's called.
                    IsscCall isscCall = JsonConvert.DeserializeObject<IsscCall>(JsonExtensions.ToJson(jsonObj));
                    IsscProvider prov = IsscProvider.GetByContractorId(isscCall.ContractorID, isscCall.ClientID, isscCall.LocationID);

                    if (isscCall.Incident.CrossStreet1 != null)
                    {
                        var crossstreet = isscCall.Incident.CrossStreet1;
                        if (!String.IsNullOrWhiteSpace(crossstreet))
                            jsonObj.Incident.Address1 += " at " + crossstreet;
                    }

                    string geicoStartingLocation = string.Format("{0}, {1}, {2} {3} {4}",
                        Core.FormatName(isscCall.Incident.Address1),
                        isscCall.Incident.Address2,
                        Core.FormatName(isscCall.Incident.City),
                        isscCall.Incident.State,
                        isscCall.Incident.Zip
                        ).Replace(", , ", ", ");

                    string geicoVehicle = string.Format("{0} {1} {2} {3}",
                        jsonObj.Vehicle.Year,
                        jsonObj.Vehicle.Color,
                        GetManufacturerByName(isscCall.Vehicle.Make),
                        GetModelByName(isscCall.Vehicle.Model));

                    StringBuilder sbServices = new StringBuilder();
                    foreach (var task in jsonObj.Job.PrimaryTasks)
                        sbServices.AppendLine(task.Task.ToString());

                    string destination = null;
                    if (isscCall.Destination != null)
                    {
                        if (isscCall.Destination.Zip == "00000")
                            isscCall.Destination.Zip = null;

                        if (string.IsNullOrEmpty(isscCall.Destination.City))
                        {
                            if (!string.IsNullOrWhiteSpace(isscCall.Destination.Zip))
                            {
                                string lookupZip = isscCall.Destination.Zip.ToString();

                                logger.Log(LogLevel.Info, "GEICO: Looking up missing city for zip {0} for DispatchID {1}",
                                    isscCall.Destination.Zip,
                                    isscCall.DispatchID);

                                var zc = SqlMapper.QuerySP<dynamic>("Geo.GetCityByZip", new { @Zip = lookupZip }).FirstOrDefault();

                                if (zc != null)
                                {
                                    isscCall.Destination.City = zc.City;

                                    logger.Log(LogLevel.Info, "GEICO: City was missing for zip {0}, looked up and filling in with {1} for DispatchID {2}",
                                        isscCall.Destination.Zip,
                                        isscCall.Destination.City,
                                        isscCall.DispatchID);
                                }
                            }
                            else if (isscCall.Destination.Latitude != 0)
                            {
                                logger.Warn(masterAccountId, "IncomingOffer", "Incoming offer missing address but includes lat/long",
                                    companyId: prov.CompanyId, data: isscCall);

                                decimal lat = isscCall.Destination.Latitude;
                                decimal lng = isscCall.Destination.Longitude;

                                if (lat != 0 && lng != 0)
                                {
                                    var geo = await GeocodeHelper.Geocode(lat + "," + lng);

                                    if (geo != null)
                                    {
                                        isscCall.Destination.Address1 = geo.Address;
                                        isscCall.Destination.City = geo.City;
                                        isscCall.Destination.State = geo.State;
                                        isscCall.Destination.Zip = geo.Zip;
                                        isscCall.Destination.Latitude = geo.Latitude;
                                        isscCall.Destination.Longitude = geo.Longitude;

                                        logger.Warn(masterAccountId, "IncomingOffer",
                                            "Incoming offer missing address but includes lat/long, replaced with google",
                                            companyId: prov.CompanyId, data: isscCall.Destination);
                                    }
                                }
                            }
                        }

                        if (isscCall.Destination.Address1 == "Unknown" &&
                            isscCall.Destination.City == "Unknown" &&
                            isscCall.Destination.Zip == "00000-0000")
                        {
                            if (isscCall.Destination.LocationInfo != null && !isscCall.Destination.LocationInfo.ToString().Contains("Unknown"))
                                destination = isscCall.Destination.LocationInfo;
                        }
                        else
                        {
                            destination = string.Format("{0}, {1}, {2} {3} {4}",
                                Core.FormatName(isscCall.Destination.Address1?.ToString()),
                                isscCall.Destination.Address2,
                                Core.FormatName(isscCall.Destination.City?.ToString()),
                                isscCall.Destination.State,
                                isscCall.Destination.Zip)
                                .Replace(", , ", ", ")
                                .Trim()
                                .Trim(',');
                        }
                    }

                    DateTime timestamp = Convert.ToDateTime(jsonObj.Job.TimeStamp);
                    DateTime? expDate = null;
                    if (jsonObj.Job.RequiredAcknowledgeTime != null && jsonObj.Job.RequiredAcknowledgeTime != 0)
                        expDate = Convert.ToDateTime(jsonObj.Job.TimeStamp).AddSeconds(Convert.ToInt32(jsonObj.Job.RequiredAcknowledgeTime) - 5);

                    if (prov.ClientId.StartsWith("GCO") || prov.ClientId == "ADS")
                    {
                        if (expDate != null)
                            expDate = expDate.Value.AddSeconds(5);
                    }

                    CallRequest geicoRequest = CallRequest.GetByForeignId(prov.AccountId, jsonObj.DispatchID.ToString());

                    if (geicoRequest == null)
                        geicoRequest = new CallRequest();
                    else
                        logger.Log(LogLevel.Warn, "GEICO: CallRequest already exists for DispatchID {0} (CompanyID {1}); updating CallRequestId {2}",
                            geicoRequest.PurchaseOrderNumber,
                            geicoRequest.CompanyId,
                            geicoRequest.CallRequestId);

                    geicoRequest.AccountId = prov.AccountId;
                    geicoRequest.CompanyId = prov.CompanyId;
                    geicoRequest.Reason = jsonObj.Job.JobDescription;
                    geicoRequest.ServiceNeeded = sbServices.ToString().Trim();
                    geicoRequest.StartingLocation = geicoStartingLocation;
                    geicoRequest.Vehicle = geicoVehicle;
                    geicoRequest.RequestDate = DateTime.Now;
                    geicoRequest.ExpirationDate = (expDate != null ? (DateTime?)expDate.Value.ToLocalTime() : null);
                    geicoRequest.PurchaseOrderNumber = jsonObj.DispatchID;
                    geicoRequest.TowDestination = destination;
                    geicoRequest.ProviderId = jsonObj.ContractorID;

                    if (prov.LocationId != null)
                        geicoRequest.ProviderId += "/" + prov.LocationId;

                    geicoRequest.MaxEta = (jsonObj.Job.MaxETA != null ? Convert.ToInt32(jsonObj.Job.MaxETA) : 0);

                    logger.Log(LogLevel.Trace, "Server vs ISSC: {0} vs {1}... Expiration Scheduled for {2}",
                        DateTime.Now, Convert.ToDateTime(jsonObj.Job.TimeStamp).ToLocalTime(),
                        (expDate != null ? expDate.Value.ToLocalTime().ToString() : "NULL"));

                    await geicoRequest.Save();

                    IsscDispatch isscDispatch = new IsscDispatch()
                    {
                        ClientId = jsonObj.ClientID,
                        ContractorId = jsonObj.ContractorID,
                        DispatchId = jsonObj.DispatchID,
                        JobId = jsonObj.Job.JobID,
                        LocationId = jsonObj.LocationID,
                        CallJson = JsonExtensions.ToJson(jsonObj, null),
                        CallRequestId = geicoRequest.CallRequestId
                    };

                    if (jsonObj.Transcript != null)
                    {
                        await geicoRequest.UpdateStatus(CallRequestStatus.Accepted, 1, po: jsonObj.AuthorizationNumber.ToString());

                        isscDispatch.AuthorizationNumber = jsonObj.AuthorizationNumber;

                        isscDispatch.Save();

                        return geicoRequest;
                    }

                    isscDispatch.Save();

                    var replied = await AutoAcceptUtility.ScheduleAutomatedAccept(timestamp, expDate, prov.CompanyId, prov.AccountId, geicoRequest,
                            jsonObj?.Incident?.Zip?.ToString(),
                            prov.ContractorId,
                            prov.LocationId,
                            dispatchId: isscDispatch.DispatchId);

                    if (replied)
                        deliver = false;
                    else
                    {
                        if (prov.ClientId.StartsWith("GCO") || prov.ClientId == "ADS" || prov.ClientId == "RDAM" || prov.ClientId == "RP")
                        {
                            await SendExpiredRequestEvent(geicoRequest.CallRequestId, expDate.Value);
                        }
                        else
                        {
                            await ScheduleAutomatedPhoneCallRequest(timestamp, expDate, prov.CompanyId, prov.AccountId, geicoRequest);
                        }
                    }

                    if (deliver)
                        await geicoRequest.Deliver();

                    await ScheduleVoiceDispatch(geicoRequest);
                    GatewayForward(geicoRequest, isscDispatch);

                    return geicoRequest;

                case MotorClubName.Agero:
                    DispatchDetails callDetails = jsonObj.CallDetails;

                    AgeroSession ageroSession = AgeroSession.GetByVendorId(Convert.ToInt32(jsonObj.VendorId));

                    if (ageroSession == null)
                        throw new MotorClubException("No last session found for the given vendorId: " + Convert.ToInt32(jsonObj.VendorId));

                    var redirect = AccountKeyValue.GetByAccount(ageroSession.CompanyId, ageroSession.AccountId, Provider.Towbook.ProviderId, "RedirectByFacilityIdJson").FirstOrDefault();

                    int dCompanyId = ageroSession.CompanyId;
                    int dAccountId = ageroSession.AccountId;

                    if (redirect != null)
                    {
                        var redirects = JsonConvert.DeserializeObject<List<AccountLocationModel>>(redirect.Value);
                        var redirectAccountId = redirects.Where(o => o.facilityId == jsonObj.FacilityId && o.accountId != 0).FirstOrDefault();
                        if (redirectAccountId != null)
                        {
                            dCompanyId = redirectAccountId.companyId;
                            dAccountId = redirectAccountId.accountId;
                        }
                    }

                    var ageroRequest = new CallRequest()
                    {
                        AccountId = dAccountId,
                        CompanyId = dCompanyId,
                        Reason = callDetails.Problem,
                        ServiceNeeded = callDetails.ServiceType,
                        StartingLocation = callDetails.DisablementLocation.ToString(),
                        Vehicle = callDetails.Vehicle.ToString(),
                        PurchaseOrderNumber = callDetails.PurchaseOrderNumber,
                        RequestDate = callDetails.ReceivedTime.ToLocalTime(),
                        ExpirationDate = callDetails.ReceivedTime.ToLocalTime().AddMinutes(4),
                        ProviderId = jsonObj.VendorId.ToString() + (jsonObj.FacilityId != null ? ("." + jsonObj.FacilityId.ToString()) : ""),
                        MaxEta = 90
                    };

                    if (callDetails.DrivingInfo != null && 
                        callDetails.DrivingInfo.FacilityToDisablementDistance != 0)
                        ageroRequest.Distance = (double)callDetails.DrivingInfo.FacilityToDisablementDistance;

                    if (callDetails.TowDestination != null)
                        ageroRequest.TowDestination = callDetails.TowDestination.ToString();

                    await ageroRequest.Save();

                    if (deliver)
                        await SendExpiredRequestEvent(ageroRequest.CallRequestId, ageroRequest.ExpirationDate.Value);

                    var ageroDispatch = new AgeroDispatch()
                    {
                        AccessToken = ageroSession.AccessToken,
                        CallJson = callDetails.ToJson(),
                        CallRequestId = ageroRequest.CallRequestId,
                        VendorId = jsonObj.VendorId,
                        DispatchId = Convert.ToInt32(callDetails.DispatchRequestNumber),
                        Eta = callDetails.ETAInMinutes
                    };
                    ageroDispatch.Save();
                    string facilityId = jsonObj.FacilityId?.ToString();

                    await ScheduleVoiceDispatch(ageroRequest);

                    if (await AutoAcceptUtility.ScheduleAutomatedAccept(ageroRequest.RequestDate, ageroRequest.ExpirationDate,
                        dCompanyId, dAccountId, ageroRequest,
                        callDetails.DisablementLocation.PostalCode,
                        ageroDispatch.VendorId.ToString(),
                        facilityId,
                        dispatchId: ageroDispatch.DispatchId.ToString()))
                        deliver = false;

                    if (deliver)
                    {
                        // make sure to call deliver after the AgeroDispatch is saved.
                        await ageroRequest.Deliver();
                    }

                    return ageroRequest;

                case MotorClubName.Allstate:
                case MotorClubName.Pinnacle:
                case MotorClubName.Nsd:
                case MotorClubName.Nac:
                case MotorClubName.RoadAmerica:
                case MotorClubName.DDXML:
                    var asMsg = (Allstate.DDMessage)jsonObj;
                    var asDspMsg = (Allstate.DSPMessageBody)asMsg.DDContent;
                    var asCId = asMsg.DDMessageHeader.ContractorID;

                    if (masterAccountId == 0)
                    {
                        switch (mcName)
                        {
                            case MotorClubName.Allstate:
                                if (asCId != null && asCId.EndsWith("TB"))
                                    masterAccountId = MasterAccountTypes.OonAllstate;
                                else
                                    masterAccountId = MasterAccountTypes.Allstate;
                                break;
                            case MotorClubName.Pinnacle:
                                masterAccountId = MasterAccountTypes.Pinnacle;
                                break;
                            case MotorClubName.Nsd:
                                masterAccountId = MasterAccountTypes.Nsd;
                                break;
                            case MotorClubName.Nac:
                                masterAccountId = MasterAccountTypes.Nac;
                                break;
                        }
                    }

                    string locationCode = null;

                    if (asDspMsg.Key == "GETEST")
                        locationCode = "QA";

                    var ascr = masterAccountId > 0 ?
                        AllstateContractor.GetByContractorId(asCId, masterAccountId, locationCode) :
                        AllstateContractor.GetByContractorId(asCId);

                    DateTime? asExpDate = null;
                    if (asDspMsg.JobInfo.RequiredAcknowledgeTime != 0 &&
                        !string.IsNullOrEmpty(asDspMsg.JobInfo.TimeStamp))
                        asExpDate = Allstate.DSPMessageJobInfo
                            .ConvertAllstateTimestampToDateTime(asDspMsg.JobInfo.TimeStamp)
                            .AddSeconds(asDspMsg.JobInfo.RequiredAcknowledgeTime);

                    if (masterAccountId == MasterAccountTypes.Nac)
                        asExpDate = DateTime.Now.AddSeconds(asDspMsg.JobInfo.RequiredAcknowledgeTime);

                    string asStartingLocation = asDspMsg.IncAddr.ToString();
                    string asTowDestination = asDspMsg.DestAddr.ToString();
                    string asVehicle = asDspMsg.VehicleInfo.ToString();

                    // allstate only - for multi-step and reverse tows, use the exchange pickup address as the starting location.
                    if (asDspMsg?.ExchangePickupAddress?.OrderNoAsInt == 1)
                    {
                        asStartingLocation = asDspMsg.ExchangePickupAddress.ToString();
                    }

                    decimal unloaded = !string.IsNullOrWhiteSpace(asDspMsg.JobInfo.EstUnloadedMiles) ? Convert.ToDecimal(asDspMsg.JobInfo.EstUnloadedMiles) : 0;

                    CallRequest asRequest = new CallRequest()
                    {
                        AccountId = ascr.AccountId,
                        CompanyId = ascr.CompanyId,
                        ExpirationDate = asExpDate,
                        PurchaseOrderNumber = asDspMsg.PaymentInfo?.PONum,
                        Reason = asDspMsg.JobInfo.JobDesc.Truncate(500),
                        RequestDate = DateTime.Now,
                        ServiceNeeded = asDspMsg.JobInfo.PrimaryTask.Truncate(500),
                        StartingLocation = asStartingLocation.Truncate(512),
                        TowDestination = asTowDestination.Truncate(512),
                        Vehicle = asVehicle.Truncate(500),
                        MaxEta = asDspMsg.JobInfo.GetMaxEtaAsInt(),
                        ProviderId = asCId,
                        Distance = (double)unloaded
                    };

                    // Prepend service with light, medium, heavy.
                    if (!string.IsNullOrWhiteSpace(asDspMsg?.VehicleInfo?.VehicleType))
                        asRequest.ServiceNeeded = asDspMsg.VehicleInfo.VehicleType + " " +
                            asRequest.ServiceNeeded;

                    if (asRequest.MaxEta == 0 && mcName == MotorClubName.Nsd)
                        asRequest.MaxEta = 240;

                    await asRequest.Save();

                    var asDispatch = new AllstateDispatch()
                    {
                        CallRequestId = asRequest.CallRequestId,
                        CallXml = asMsg.GetXml(),
                        ContractorId = asMsg.DDMessageHeader.ContractorID,
                        DispatchId = asDspMsg.JobInfo.JobID ?? "",
                        PurchaseOrderNumber = asDspMsg.PaymentInfo?.PONum,
                        ResponseId = asMsg.DDMessageHeader.ResponseID,
                        MasterAccountId = ascr.MasterAccountId,
                        DisablementToDestinationDistance = !string.IsNullOrWhiteSpace(asDspMsg.JobInfo.EstLoadedMiles) ? Convert.ToDecimal(asDspMsg.JobInfo.EstLoadedMiles) : 0,
                        CompanyDistanceToDisablement = unloaded
                    };

                    var boost = false;

                    if (asDispatch.MasterAccountId == MasterAccountTypes.Allstate)
                    {
                        var extra = new CallRequestExtraModel();

                        if (asDspMsg.JobInfo.Boost != null && asDspMsg.JobInfo.Boost.Amount > 0)
                        {
                            extra.Amount = asDspMsg.JobInfo.Boost.Amount;
                            extra.Type = "Additional Boost Amt";

                            Core.SetRedisValue(asRequest.CallRequestId + ":extra", JsonConvert.SerializeObject(extra), TimeSpan.FromMinutes(5));
                            boost = true;
                        }
                    }
                    else if (asDispatch.MasterAccountId == MasterAccountTypes.OonAllstate)
                    {
                        var extra = new CallRequestExtraModel();
                        extra.Amount = asDspMsg.JobInfo.OfferAmount;

                        extra.Type = "This job pays";
                        Core.SetRedisValue(asRequest.CallRequestId + ":extra", JsonConvert.SerializeObject(extra), TimeSpan.FromMinutes(5));
                    }

                    if (asDispatch.MasterAccountId == MasterAccountTypes.OonAllstate &&
                        !string.IsNullOrWhiteSpace(asDspMsg.IncAddr.Lat) &&
                        !string.IsNullOrWhiteSpace(asDspMsg.IncAddr.Lon))
                    {
                        var row = Integrations.MotorClubs.Allstate.OON.ProviderGeolocateModel.GetByCompanyId(ascr.CompanyId,
                            Convert.ToDecimal(asDspMsg.IncAddr.Lat),
                            Convert.ToDecimal(asDspMsg.IncAddr.Lon));
                        if (row != null)
                        {
                            var driverUnloadedMatrix = await AutoDispatchServiceBusHandler.GetMatrixAsync(
                                row.Latitude.ToString() + "," + row.Longitude.ToString(),
                                asDspMsg.IncAddr.Lat + "," + asDspMsg.IncAddr.Lon);

                            if (driverUnloadedMatrix != null)
                                asDispatch.ClosestDriverDistanceToDisablement = Math.Round(driverUnloadedMatrix.Miles, 2);

                            if (!string.IsNullOrWhiteSpace(asDspMsg.DestAddr?.Lat) &&
                                !string.IsNullOrWhiteSpace(asDspMsg.DestAddr?.Lon))
                            {
                                var driverLoadedMatrix = await AutoDispatchServiceBusHandler.GetMatrixAsync(
                                    row.Latitude.ToString() + "," + row.Longitude.ToString(),
                                    asDspMsg.DestAddr.Lat + "," + asDspMsg.DestAddr.Lon);

                                if (driverLoadedMatrix != null)
                                    asDispatch.ClosestDriverDistanceToDestination = Math.Round(driverLoadedMatrix.Miles, 2);
                            }
                        }
                    }

                    asDispatch.Save();

                    if (asRequest.ExpirationDate != null)
                        await SendExpiredRequestEvent(asRequest.CallRequestId, asRequest.ExpirationDate.Value);

                    if (mcName == MotorClubName.Nsd && asExpDate != null)
                        await ScheduleAutomatedPhoneCallRequest(asExpDate.Value.AddSeconds(-asDspMsg.JobInfo.RequiredAcknowledgeTime),
                            asExpDate.Value.AddSeconds(-5), ascr.CompanyId, ascr.AccountId, asRequest);
                    await ScheduleVoiceDispatch(asRequest);

                    if (await AutoAcceptUtility.ScheduleAutomatedAccept(asRequest.RequestDate,
                        asExpDate.Value.AddSeconds(-25), ascr.CompanyId, ascr.AccountId, asRequest, asDspMsg?.IncAddr?.Zip, ascr.ContractorId,
                        dispatchId: asDispatch.ResponseId))
                        deliver = false;

                    if (deliver)
                    {
                        if (boost)
                            await asRequest.Deliver(false, " (+Boost)");
                        else 
                            await asRequest.Deliver();
                    }

                    await GatewayForward(asRequest, asDispatch);

                    return asRequest;

                case MotorClubName.Quest:
                    var qMsg = (Allstate.DDMessage)jsonObj;
                    var qDspMsg = (Allstate.DSPMessageBody)qMsg.DDContent;

                    var qCId = qMsg.DDMessageHeader.ContractorID;

                    if (masterAccountId < 1)
                        masterAccountId = MasterAccountTypes.Quest;

                    var qscr = AllstateContractor.GetByContractorId(qCId, masterAccountId);

                    DateTime? qExpDate = null;

                    if (qDspMsg.JobInfo.RequiredAcknowledgeTime != 0 && !string.IsNullOrEmpty(qDspMsg.JobInfo.TimeStamp))
                    {
                        qExpDate = Allstate.DSPMessageJobInfo.ConvertAllstateTimestampToDateTime
                            (qDspMsg.JobInfo.TimeStamp).AddSeconds(qDspMsg.JobInfo.RequiredAcknowledgeTime);
                    }

                    string qStartingLocation = qDspMsg.IncAddr.ToString();
                    string qTowDestination = qDspMsg.DestAddr.ToString();
                    string qVehicle = qDspMsg.VehicleInfo.ToString();

                    CallRequest qRequest = new CallRequest()
                    {
                        AccountId = qscr.AccountId,
                        CompanyId = qscr.CompanyId,
                        ExpirationDate = qExpDate,
                        PurchaseOrderNumber = "",
                        Reason = qDspMsg.JobInfo?.JobDesc,
                        RequestDate = DateTime.Now,
                        ServiceNeeded = qDspMsg.JobInfo.PrimaryTask,
                        StartingLocation = qStartingLocation,
                        TowDestination = qTowDestination,
                        Vehicle = qVehicle,
                        MaxEta = qDspMsg.JobInfo.GetMaxEtaAsInt(),
                        ProviderId = qCId
                    };

                    await qRequest.Save();

                    decimal op = 0;

                    decimal.TryParse(qDspMsg.PaymentInfo.QuotePrice, out op);

                    var qDispatch = new AllstateDispatch()
                    {
                        CallRequestId = qRequest.CallRequestId,
                        CallXml = qMsg.GetXml(),
                        ContractorId = qMsg.DDMessageHeader.ContractorID,
                        DispatchId = qDspMsg.JobInfo.JobID,
                        PurchaseOrderNumber = "",
                        ResponseId = qMsg.DDMessageHeader.ResponseID,
                        MasterAccountId = qscr.MasterAccountId,
                        OfferPrice = op
                    };

                    qDispatch.Save();

                    if (qRequest.ExpirationDate != null)
                        await SendExpiredRequestEvent(qRequest.CallRequestId, qRequest.ExpirationDate.Value);

                    await ScheduleVoiceDispatch(qRequest);

                    if (await AutoAcceptUtility.ScheduleAutomatedAccept(qRequest.RequestDate,
                        qRequest.ExpirationDate.Value.AddSeconds(-25), qRequest.CompanyId, qRequest.AccountId, qRequest,
                            qDspMsg.IncAddr?.Zip,
                            qDispatch.ContractorId,
                            dispatchId: qDispatch.ResponseId))
                        deliver = false;

                    if (deliver)
                        await qRequest.Deliver();

                    await GatewayForward(qRequest, qDispatch);

                    return qRequest;

                case MotorClubName.Urgently:
                    if (masterAccountId == 0)
                        masterAccountId = MasterAccountTypes.Urgently;

                    var ulMsg = (UrgentlyRestClient.JobOfferModel)jsonObj;
                    var ulPid = ulMsg.provider.id;
                    var ulcr = UrgentlyProvider.GetByProviderId(ulPid);

                    DateTime? ulExpDate = ulMsg.service.OfferExpiresLocal;

                    string startingLocation = ulMsg.serviceLocation.ToString();
                    string towDestination = ulMsg.dropOffLocation?.ToString() ?? "";
                    string vehicle = ulMsg.vehicle?.ToString();

                    var ulRequest = new CallRequest()
                    {
                        AccountId = ulcr.AccountId,
                        CompanyId = ulcr.CompanyId,
                        ExpirationDate = ulExpDate,
                        PurchaseOrderNumber = ulMsg.service.id.ToString(),
                        Reason = ulMsg.service.name,
                        RequestDate = DateTime.Now,
                        ServiceNeeded = ulMsg.service.name,
                        StartingLocation = startingLocation.Truncate(512),
                        TowDestination = towDestination.Truncate(512),
                        Vehicle = vehicle.Truncate(500),
                        MaxEta = 120,
                        ProviderId = ulPid
                    };

                    var serviceCallsOnly = CompanyKeyValue.GetFirstValueOrNull(ulcr.CompanyId,
                        Provider.Towbook.ProviderId, "ServiceCallsOnly") == "1";

                    var virtualTruckMode = CompanyKeyValue.GetFirstValueOrNull(ulcr.CompanyId,
                        Provider.Towbook.ProviderId, "UrgentlyVirtualTrucks") == "1";

                    var urgentlyProvider = Provider.GetByName("Urgent.ly");

                    try
                    {
                        var goodDrivers = new System.Collections.ObjectModel.Collection<int>();

                        if (!serviceCallsOnly && !virtualTruckMode)
                        {

                            var drivers = new System.Collections.ObjectModel.Collection<int>();
                            var dtd = DriverTruckDefault.GetByCompanyId(ulcr.CompanyId);

                            if (dtd.All(o => !ulMsg.provider.vehicles.Any(v => v.id == o.TruckId.ToString())))
                                ulMsg.provider.vehicles = new UrgentlyRestClient.Vehicle1[0];

                            if (!ulMsg.provider.vehicles.Any())
                            {
                                var associations = TruckKeyValue.GetByCompanyId(ulcr.CompanyId,
                                    urgentlyProvider.ProviderId, "IsRegistered");

                                var defaultTrucks = dtd.Where(o =>
                                    associations.Any(a => a.TruckId == o.TruckId))
                                    .Select(o => new UrgentlyRestClient.Vehicle1() { id = o.TruckId.ToString() })
                                    .Distinct()
                                    .ToArray();

                                ulMsg.provider.vehicles = defaultTrucks;
                            }

                            ulRequest.Trucks = string.Join(",", ulMsg.provider.vehicles.Select(o => Convert.ToInt32(o.id)));

                            foreach (var truck in ulMsg.provider.vehicles.Select(o => Convert.ToInt32(o.id)))
                            {
                                drivers = drivers.Union(DriverTruckDefault.GetByTruckId(truck).Select(o => o.DriverId)).ToCollection();
                            }
                       
                        
                            foreach (var driver in drivers)
                            {
                                var isRegistered = DriverKeyValue.GetByDriver(ulcr.CompanyId, driver, urgentlyProvider.ProviderId, "IsRegistered").FirstOrDefault();
                                if (isRegistered?.Value == "1")
                                    goodDrivers.Add(driver);
                            }
                            ulRequest.Drivers = string.Join(",", goodDrivers);
                        }
                        else
                        {
                            goodDrivers =
                                ulMsg.provider.vehicles.Where(o => o.id.Contains("virtual"))
                                    .Select(o =>
                                    {
                                        if (int.TryParse(o.id.Replace("virtual-", ""), out var n))
                                            return n;

                                        return -1;
                                    })
                                    .Where(o => o > 0)
                                    .ToCollection();
                        }

                        ulRequest.Drivers = string.Join(",", goodDrivers);

                        if (ulRequest.Drivers.Length > 500)
                        {
                            ulRequest.Drivers = ulRequest.Drivers.Truncate(500);
                            ulRequest.Drivers = ulRequest.Drivers.Substring(0,
                                ulRequest.Drivers.LastIndexOf(',')).Trim(',');
                        }

                        if (ulRequest.Trucks != null && ulRequest.Trucks.Length > 500)
                        {
                            ulRequest.Trucks = ulRequest.Trucks.Truncate(500);
                            ulRequest.Trucks = ulRequest.Trucks.Substring(0,
                                ulRequest.Trucks.LastIndexOf(',')).Trim(',');
                        }

                        if (ulRequest.Trucks != null && ulRequest.Trucks.Contains("virtual"))
                            ulRequest.Trucks = "";
                    }
                    catch (Exception ex)
                    {
                        logger.Error(MasterAccountTypes.Urgently, "CreateCallRequest", "Error during creation of CallRequest",
                            ulRequest.ProviderId,
                            null,
                            ulRequest.PurchaseOrderNumber,
                            ulRequest.CompanyId,
                            new
                            {
                                exception = ex
                            });
                    }

                    if (AccountKeyValue.GetFirstValueOrNull(ulRequest.CompanyId, ulRequest.AccountId,
                       Provider.Towbook.ProviderId, "CallRequestsRedirectToClosestCompany") == "1")
                    {
                        var filled = false;
                        if (ulRequest.Drivers != null)
                        {
                            var newDrivers = ulRequest.Drivers.Split(',');
                            if (newDrivers.Length > 0)
                            {
                                if (int.TryParse(newDrivers[0], out var newDriverId))
                                {
                                    var dr = Driver.GetByIdWithoutCache(newDriverId);

                                    if (dr != null)
                                    {
                                        ulRequest.CompanyId = dr.CompanyId;
                                        filled = true;
                                    }
                                }
                            }
                        }

                        if (!filled)
                        {
                            var up = UrgentlyProvider.GetClosestCompanies(ulcr.CompanyId,
                                ulMsg.serviceLocation.Latitude,
                                ulMsg.serviceLocation.Longitude);

                            if (up.Any())
                            {
                                ulRequest.CompanyId = up.First().CompanyId;
                            }
                        }
                    }

                    var rejectInstantly = AccountKeyValue.GetFirstValueOrNull(ulRequest.CompanyId, ulRequest.AccountId,
                        Provider.Towbook.ProviderId, "DigitalAutoAcceptInstantly") == "999";

                    if (rejectInstantly)
                    {
                        await ulRequest.Save();
                    }
                    else
                    {
                        await ulRequest.Save();
                        deliver = true;
                    }

                    var ulDispatch = new UrgentlyJobOffer()
                    {
                        CallRequestId = ulRequest.CallRequestId,
                        OfferJson = JsonExtensions.ToJson(ulMsg),
                        ProviderId = ulPid,
                        CaseId = ulMsg.context.caseId,
                        JobNumber = ulMsg.service.id
                    };

                    ulDispatch.Save();

                    if (ulRequest.ExpirationDate != null)
                        await SendExpiredRequestEvent(ulRequest.CallRequestId, ulRequest.ExpirationDate.Value);

                    var akv = AccountKeyValue.GetFirstValueOrNull(ulRequest.CompanyId,
                        ulRequest.AccountId, Provider.Towbook.ProviderId, "DigitalAutoAccept");

                    if (akv != null)
                    {
                        var ulZip = ulMsg.serviceLocation.ZipCode ?? ulMsg.serviceLocation.zip;

                        if (string.IsNullOrWhiteSpace(ulZip))
                            ulZip = ulMsg.serviceLocation.Address.Replace(",", "")
                            .Split(' ').Skip(1).Where(o => o.Length == 5 && Regex.IsMatch(o, @"^\d+$")).LastOrDefault();

                        if (await AutoAcceptUtility.ScheduleAutomatedAccept(ulRequest.RequestDate,
                            ulRequest.ExpirationDate.Value.AddSeconds(-25),
                            ulRequest.CompanyId, ulRequest.AccountId, ulRequest,
                            ulZip,
                            ulDispatch.ProviderId,
                            dispatchId: ulDispatch.CaseId))
                            deliver = false;
                    }

                    if (deliver)
                        await ulRequest.Deliver();


                    GatewayForward(ulRequest, ulDispatch);

                    return ulRequest;

                case MotorClubName.Honk:
                    if (masterAccountId == 0)
                        masterAccountId = MasterAccountTypes.Honk;

                    var honkMessage = (HonkMessage)jsonObj;
                    var honkProviderId = honkMessage.ProviderId;
                    var honkProvider = HonkProvider.GetByProviderId(honkProviderId);
                    var ac = await Account.GetByIdAsync(honkProvider.AccountId);

                    if (ac?.Status == AccountStatus.Inactive || ac == null)
                        return null;

                    var honkJson = JsonConvert.DeserializeObject<HonkRestClient.JobModelContainer>(honkMessage.JsonData)?.Job;

                    DateTime? honkExpDate = DateTime.Now.AddMinutes(15);

                    var hkStartingLocation = honkJson.vehicle_location_address;
                    var hkTowDestination = honkJson.destination_location_address;
                    var hkVehicle = honkJson.Vehicle;

                    var honkRequest = new CallRequest()
                    {
                        AccountId = honkProvider.AccountId,
                        CompanyId = honkProvider.CompanyId,
                        ExpirationDate = honkExpDate,
                        Reason = honkJson.service_type ?? "Unknown Service",
                        RequestDate = DateTime.Now,
                        ServiceNeeded = honkJson.service_type ?? "Unknown Service",
                        StartingLocation = hkStartingLocation.Truncate(512),
                        TowDestination = hkTowDestination.Truncate(512),
                        Vehicle = hkVehicle.Truncate(500),
                        MaxEta = 120,
                        ProviderId = honkProviderId,
                        PurchaseOrderNumber = honkJson.Id.ToString()
                    };

                    var cr = CallRequest.GetByForeignId(honkProvider.AccountId, honkJson.Id.ToString());
                    if (cr != null)
                        return cr;

                    var skRejectInstantly = AccountKeyValue.GetFirstValueOrNull(honkRequest.CompanyId, honkRequest.AccountId,
                        Provider.Towbook.ProviderId, "DigitalAutoAcceptInstantly") == "999";

                    if (skRejectInstantly)
                    {
                        await honkRequest.Save();
                    }
                    else
                    {
                        await honkRequest.SaveAsync();
                    }

                    var honkDispatch = new HonkJob()
                    {
                        CallRequestId = honkRequest.CallRequestId,
                        OfferJson = JsonExtensions.ToJson(honkMessage),
                        ProviderId = honkProviderId,
                        JobId = honkJson.Id,
                    };

                    honkDispatch.Save();

                    if (honkRequest.ExpirationDate != null)
                        await SendExpiredRequestEvent(honkRequest.CallRequestId, honkRequest.ExpirationDate.Value);

                    var hakv = AccountKeyValue.GetFirstValueOrNull(honkRequest.CompanyId,
                        honkRequest.AccountId, Provider.Towbook.ProviderId, "DigitalAutoAccept");

                    if (hakv != null)
                    {

                        var geo = await GeocodeHelper.Geocode(hkStartingLocation);
                        if (geo != null)
                        {
                            if (await AutoAcceptUtility.ScheduleAutomatedAccept(honkRequest.RequestDate, honkRequest.ExpirationDate,
                                honkProvider.CompanyId, honkProvider.AccountId, honkRequest,
                                geo.Zip,
                                honkProvider.ProviderId,
                                null,
                                dispatchId: honkJson.Id.ToString()))
                                deliver = false;
                        }
                    }

                    GatewayForward(honkRequest, honkDispatch);
                    return honkRequest;


                case MotorClubName.Sykes:
                    if (masterAccountId == 0)
                        masterAccountId = MasterAccountTypes.Sykes;

                    var sykesMessage = (SykesMessage)jsonObj;
                    var sykesContractorId = sykesMessage.ContractorId;
                    var sykesContractor = SykesContractor.GetByContractorId(sykesContractorId);

                    var sykesJson = JsonConvert.DeserializeObject<SykesRestClient.DispatchModel>(sykesMessage.JsonData);

                    var skStartingLocation = sykesJson.Location.ToString();
                    var skTowDestination = sykesJson.Destinations.FirstOrDefault()?.ToString() ?? "";
                    var skVehicle = sykesJson.Vehicle.ToString();

                    var sykesRequest = new CallRequest()
                    {
                        AccountId = sykesContractor.AccountId,
                        CompanyId = sykesContractor.CompanyId,
                        ExpirationDate = DateTime.Now.AddMinutes(15),
                        PurchaseOrderNumber = sykesJson.AuthorizationId,
                        Reason = sykesJson.Coverage.Text.Truncate(50),
                        RequestDate = DateTime.Now,
                        ServiceNeeded = sykesJson.ServiceType,
                        StartingLocation = skStartingLocation.Truncate(512),
                        TowDestination = skTowDestination.Truncate(512),
                        Vehicle = skVehicle.Truncate(500),
                        MaxEta = 120,
                        ProviderId = sykesContractorId
                    };

                    await sykesRequest.Save();

                    var sykesDispatch = new SykesDispatch()
                    {
                        CallRequestId = sykesRequest.CallRequestId,
                        DispatchJson = JsonExtensions.ToJson(sykesMessage),
                        ContractorId = sykesContractorId,
                        DispatchId = sykesJson.ReferenceId,
                        PurchaseOrderNumber = sykesJson.AuthorizationId
                    };

                    sykesDispatch.Save();

                    if (sykesRequest.ExpirationDate != null)
                        await SendExpiredRequestEvent(sykesRequest.CallRequestId, sykesRequest.ExpirationDate.Value);


                    await sykesRequest.Deliver();


                    return sykesRequest;

                case MotorClubName.Swoop:
                    // todo: create call request for swoop.
                    var swoopMsg = (Integrations.MotorClubs.Swoop.SwoopMessage)jsonObj;
                    var swoopCall = JsonConvert.DeserializeObject<Integrations.MotorClubs.Swoop.Model.SwoopJob>(swoopMsg.JsonData);
                    var ss = Integrations.MotorClubs.Swoop.SwoopSession.GetById(swoopMsg.SessionId);

                    DateTime? sExpDate = swoopCall.ExpiresAt?.ToLocalTime() ?? DateTime.Now.AddMinutes(10);

                    string sStartingLocation = (await swoopCall.Location.ServiceLocation.ToWaypoint()).Address;
                    string sTowDestination = "";
                    try
                    {
                        sTowDestination = swoopCall.Location?.DropoffLocation != null ?
                            (await swoopCall.Location?.DropoffLocation?.ToWaypoint()).Address : null;
                    }
                    catch
                    {

                    }


                    string sVehicle = $"{swoopCall.Vehicle.Year} {swoopCall.Vehicle.Make} {swoopCall.Vehicle.Model} {swoopCall.Vehicle.Color}";

                    var swcl = new Integrations.MotorClubs.Swoop.SwoopRestClient();

                    if (ss.CompanyId == 10000)
                        swcl = Integrations.MotorClubs.Swoop.SwoopRestClient.StagingVictor();

                    swoopCall = await swcl.GetJobById(swoopCall.Id, ss.AccessToken);
                    if (swoopCall == null)
                        return null;
                    int sMaxEta = 91;

                    if (!(await swcl.JobReasons(ss.AccessToken, swoopCall.Id, "Accepted")).Any())
                        sMaxEta = 300;

                    int swoopCompanyId = ss.CompanyId;
                    int swoopAccountId = ss.AccountId;

                    if (swoopCall.Site?.Id != null || swoopCall.Partner?.Site?.Id != null || swoopCall.Partner?.RateAgreement?.Id != null)
                    {
                        var swoopSite = Integrations.MotorClubs.Swoop.SwoopSite.GetBySiteId(ss.SwoopSessionId,
                            swoopCall.Site?.Id ?? swoopCall.Partner?.Site?.Id ??
                            swoopCall.Partner?.RateAgreement?.Id);

                        if (swoopSite != null && swoopSite.AccountId > 0)
                        {
                            swoopCompanyId = swoopSite.CompanyId;
                            swoopAccountId = swoopSite.AccountId;
                        }
                    }

                    var sRequest = new CallRequest()
                    {
                        AccountId = swoopAccountId,
                        CompanyId = swoopCompanyId,
                        ExpirationDate = sExpDate,
                        PurchaseOrderNumber = swoopCall.AuthorizationNumber.ToString(),
                        Reason = swoopCall.Service.Symptom,
                        RequestDate = DateTime.Now,
                        ServiceNeeded = swoopCall.Service.Name,
                        StartingLocation = sStartingLocation,
                        TowDestination = sTowDestination,
                        Vehicle = sVehicle,
                        MaxEta = sMaxEta,
                        ProviderId = ss.SwoopSessionId.ToString()
                    };

                    await sRequest.Save();

                    var sDispatch = new Integrations.MotorClubs.Swoop.SwoopDispatch()
                    {
                        CallRequestId = sRequest.CallRequestId,
                        OfferJson = swoopCall.ToJson(),
                        OfferId = swoopCall.Id,
                        Swcid = swoopCall.AuthorizationNumber,
                        SwoopSessionId = ss.SwoopSessionId
                    };
                    sDispatch.Save();

                    if (sRequest.ExpirationDate != null)
                        await SendExpiredRequestEvent(sRequest.CallRequestId, sRequest.ExpirationDate.Value);

                    await ScheduleVoiceDispatch(sRequest);
                    string zip = "UNKNOWN";

                    akv = AccountKeyValue.GetFirstValueOrNull(sRequest.CompanyId, sRequest.AccountId, Provider.Towbook.ProviderId, "DigitalAutoAccept");
                    if (akv != null)
                    {
                        zip = swoopCall.Location.ServiceLocation.PostalCode ?? sRequest.StartingLocation.Replace(",", "")
                            .Split(' ').Skip(1).Where(o => o.Length == 5 && Regex.IsMatch(o, @"^\d+$")).LastOrDefault();

                        if (await AutoAcceptUtility.ScheduleAutomatedAccept(sRequest.RequestDate,
                            sRequest.ExpirationDate.Value.AddSeconds(-25), swoopCompanyId, swoopAccountId, sRequest,
                             zip,
                             ss.SwoopSessionId.ToString(),
                             dispatchId: swoopCall.Id))
                            deliver = false;
                    }

                    if (swoopCall.Acceptable != null && swoopCall.Acceptable == false)
                    {
                        ///deliver = false;
                        // TODO:  don't do until we can see that acceptable is true on most jobs. 
                        // await sRequest.UpdateStatus(CallRequestStatus.Accepted);
                    }

                    if (deliver)
                        await sRequest.Deliver();

                    return sRequest;

                case MotorClubName.Gerber:
                    if (masterAccountId == 0)
                        masterAccountId = MasterAccountTypes.Gerber;

                    var gdMsgX = (Gerber.GerberMessage)jsonObj;
                    var gdDispX = JsonConvert.DeserializeObject<Gerber.GerberDispatch>(gdMsgX.JsonData);
                    var gdMsg = JsonConvert.DeserializeObject<Gerber.DispatchModel>(gdDispX.DispatchJson);

                    var gdcr = Gerber.GerberProvider.GetByProviderId(gdMsgX.ProviderId, gdMsgX.LocationId);

                    DateTime? gdExpDate = DateTime.Now.AddSeconds(gdMsg.Order.Expires);

                    string gdStartingLocation = gdMsg.Incident.ToString();
                    string gdTowDestination = gdMsg.Destination?.ToString() ?? "";
                    string gdVehicle = gdMsg.Vehicle.ToString();

                    var gdRequest = new CallRequest()
                    {
                        AccountId = gdcr.AccountId,
                        CompanyId = gdcr.CompanyId,
                        ExpirationDate = gdExpDate,
                        PurchaseOrderNumber = gdMsg.DispatchId,
                        Reason = gdMsg.Order.Cause,
                        RequestDate = DateTime.Now,
                        ServiceNeeded = gdMsg.Order.Cause,
                        StartingLocation = gdStartingLocation.Truncate(512),
                        TowDestination = gdTowDestination.Truncate(512),
                        Vehicle = gdVehicle.Truncate(500),
                        MaxEta = gdMsg.Order.MaxEta,
                        ProviderId = gdMsgX.ProviderId + "/" + gdMsgX.LocationId
                    };
                    Console.WriteLine("Expiration: " + gdRequest.ExpirationDate.ToString());
                    await gdRequest.SaveAsync();

                    gdDispX.CallRequestId = gdRequest.CallRequestId;
                    gdDispX.Save();

                    if (gdRequest.ExpirationDate != null)
                        await SendExpiredRequestEvent(gdRequest.CallRequestId, gdRequest.ExpirationDate.Value);

                    return gdRequest;

                case MotorClubName.Fleetnet:
                case MotorClubName.Servicase:
                    async Task<CallRequest> doCreateFleeetnetCallRequest()
                    {
                        if (masterAccountId == 0)
                            masterAccountId = MasterAccountTypes.Fleetnet;

                        var fleetnetMessage = (Fleetnet.FleetnetMessage)jsonObj;
                        var fleetnetRequest = JsonConvert.DeserializeObject<ServiceRequest>(fleetnetMessage.JsonData);

                        Fleetnet.FleetnetProvider provider = Fleetnet.FleetnetProvider.GetByProviderId(fleetnetRequest.ServiceProviderId);

                        var expirationDate = DateTime.Now.AddSeconds(600);
                        var accountId = provider.AccountId;
                        var companyId = provider.CompanyId;

                        var providerId = fleetnetRequest.ServiceProviderId;
                        var maxEta = 120;

                        var serviceRequest = (ServiceRequest)fleetnetRequest;

                        var requestId = serviceRequest.RequestId;
                        var poNumber = "";
                        var reason = serviceRequest.ServiceType;
                        var service = serviceRequest.ServiceType;
                        var fleetnetStartingLocation = serviceRequest.VehicleData.Location;
                        var fleetnetTowDestination = serviceRequest.Destination;

                        var vehicleString = serviceRequest.VehicleData.Year + " " + serviceRequest.VehicleData.Make + " " + serviceRequest.VehicleData.Model;
                        var fleetnetVehicle = vehicleString;

                        var fnRequest = new CallRequest()
                        {
                            AccountId = accountId,
                            CompanyId = companyId,
                            ExpirationDate = expirationDate,
                            PurchaseOrderNumber = poNumber,
                            Reason = reason,
                            RequestDate = DateTime.Now,
                            ServiceNeeded = service,
                            StartingLocation = fleetnetStartingLocation.Truncate(512),
                            TowDestination = fleetnetTowDestination.Truncate(512),
                            Vehicle = fleetnetVehicle.Truncate(500),
                            MaxEta = maxEta,
                            ProviderId = providerId
                        };

                        await fnRequest.Save();

                        var fnDispatch = new Fleetnet.FleetnetJobOffer()
                        {
                            ProviderId = fnRequest.ProviderId,
                            RequestId = requestId,
                            OfferJson = fleetnetRequest.ToJson(),
                            CallRequestId = fnRequest.CallRequestId
                        };

                        fnDispatch.Save();

                        await fnRequest.Deliver();

                        if (fnRequest.ExpirationDate != null)
                            await SendExpiredRequestEvent(fnRequest.CallRequestId, fnRequest.ExpirationDate.Value);

                        return fnRequest;
                    }
                    return await doCreateFleeetnetCallRequest();

                case MotorClubName.AaaAce:
                    async Task<CallRequest> doAceCreateCallRequest()
                    {
                        if (masterAccountId == 0)
                            masterAccountId = MasterAccountTypes.AaaAce;

                        var aaaMessage = (AaaMessage)jsonObj;
                        var aaaRequest = JsonConvert.DeserializeObject<Integrations.MotorClubs.Aaa.Ace.DispatchModel>(aaaMessage.JsonData);

                        AaaContractor contractor = AaaContractor.GetById(aaaMessage.AaaContractorId);


                        DateTime expirationDate = DateTime.Now;
                        
                        if (!string.IsNullOrWhiteSpace(aaaRequest.RequiredAcknowledgeTimeInSeconds))
                            expirationDate = DateTime.Now.AddSeconds(Convert.ToInt32(aaaRequest.RequiredAcknowledgeTimeInSeconds));

                        var accountId = contractor.AccountId;
                        var companyId = contractor.CompanyId;

                        var maxEta = 120;

                        var poNumber = aaaRequest.PurchaseOrderNumber;
                        string reason = "";
                        var service = "";

                        var problem = aaaRequest.Call.RequestedService.TroubleCodes.FirstOrDefault(r => r.Type == "PROBLEM");

                        if (problem != null)
                            reason = Core.FormatName(problem.Description);

                        var pacesetter = aaaRequest.Call.RequestedService.TroubleCodes.FirstOrDefault(r => r.Type == "PACESETTER");

                        if (pacesetter != null)
                            service = Core.FormatName(pacesetter.Description);

                        var aaaStartingLocation = aaaRequest.Call.BreakdownLocation.Address.ToString();
                        var aaaTowDest = aaaRequest.Call.TowDestination.Address.ToString();

                        var vehicleString = aaaRequest.Call.Vehicle.ModelYear.GetValueOrDefault() + " " +
                            aaaRequest.Call.Vehicle.MakeName + " " +
                            aaaRequest.Call.Vehicle.ModelName;

                        var aaaCallRequest = new CallRequest()
                        {
                            AccountId = accountId,
                            CompanyId = companyId,
                            ExpirationDate = expirationDate,
                            PurchaseOrderNumber = poNumber,
                            Reason = reason,
                            RequestDate = DateTime.Now,
                            ServiceNeeded = service,
                            StartingLocation = aaaStartingLocation.Truncate(512),
                            TowDestination = aaaTowDest.Truncate(512),
                            Vehicle = vehicleString.Truncate(500),
                            MaxEta = maxEta,
                            ProviderId = contractor.ContractorId
                        };

                        await aaaCallRequest.Save();

                        var fnDispatch = new AaaDispatch()
                        {
                            AaaContractorId = contractor.AaaContractorId,
                            DispatchId = aaaRequest.DispatchId,
                            DispatchJson = aaaMessage.JsonData,
                            CallRequestId = aaaCallRequest.CallRequestId
                        };

                        fnDispatch.Save();

                        if(deliver)
                            await aaaCallRequest.Deliver();

                        if (aaaCallRequest.ExpirationDate != null)
                            await SendExpiredRequestEvent(aaaCallRequest.CallRequestId, aaaCallRequest.ExpirationDate.Value);

                        return aaaCallRequest;
                    }

                    return await doAceCreateCallRequest();
                case MotorClubName.AaaNortheast:
                    async Task<CallRequest> doNortheastCreateCallRequest()
                    {
                        if (masterAccountId == 0)
                            masterAccountId = MasterAccountTypes.AaaNortheast;

                        var aaaMessage = (AaaMessage)jsonObj;
                        var aaaRequest = JsonConvert.DeserializeObject<Integrations.MotorClubs.Aaa.Ace.DispatchModel>(aaaMessage.JsonData);

                        AaaContractor contractor = AaaContractor.GetById(aaaMessage.AaaContractorId);


                        DateTime expirationDate = DateTime.Now;

                        if (!string.IsNullOrWhiteSpace(aaaRequest.RequiredAcknowledgeTimeInSeconds))
                            expirationDate = DateTime.Now.AddSeconds(Convert.ToInt32(aaaRequest.RequiredAcknowledgeTimeInSeconds));

                        var accountId = contractor.AccountId;
                        var companyId = contractor.CompanyId;

                        var maxEta = 120;

                        var poNumber = aaaRequest.PurchaseOrderNumber;
                        string reason = "";
                        var service = "";

                        var problem = aaaRequest.Call.RequestedService.TroubleCodes.FirstOrDefault(r => r.Type == "PROBLEM");

                        if (problem != null)
                            reason = Core.FormatName(problem.Description);

                        var pacesetter = aaaRequest.Call.RequestedService.TroubleCodes.FirstOrDefault(r => r.Type == "PACESETTER");

                        if (pacesetter != null)
                            service = Core.FormatName(pacesetter.Description);

                        var aaaStartingLocation = aaaRequest.Call.BreakdownLocation.Address.ToString();
                        var aaaTowDest = aaaRequest.Call.TowDestination.Address.ToString();

                        var vehicleString = aaaRequest.Call.Vehicle.ModelYear.GetValueOrDefault() + " " +
                            aaaRequest.Call.Vehicle.MakeName + " " +
                            aaaRequest.Call.Vehicle.ModelName;

                        var aaaCallRequest = new CallRequest()
                        {
                            AccountId = accountId,
                            CompanyId = companyId,
                            ExpirationDate = expirationDate,
                            PurchaseOrderNumber = poNumber,
                            Reason = reason,
                            RequestDate = DateTime.Now,
                            ServiceNeeded = service,
                            StartingLocation = aaaStartingLocation.Truncate(512),
                            TowDestination = aaaTowDest.Truncate(512),
                            Vehicle = vehicleString.Truncate(500),
                            MaxEta = maxEta,
                            ProviderId = contractor.ContractorId
                        };

                        await aaaCallRequest.Save();

                        var fnDispatch = new AaaDispatch()
                        {
                            AaaContractorId = contractor.AaaContractorId,
                            DispatchId = aaaRequest.DispatchId,
                            DispatchJson = aaaMessage.JsonData,
                            CallRequestId = aaaCallRequest.CallRequestId
                        };

                        fnDispatch.Save();

                        if (deliver)
                            await aaaCallRequest.Deliver();

                        if (aaaCallRequest.ExpirationDate != null)
                            await SendExpiredRequestEvent(aaaCallRequest.CallRequestId, aaaCallRequest.ExpirationDate.Value);

                        return aaaCallRequest;
                    }

                    return await doNortheastCreateCallRequest();

                case MotorClubName.AaaAca:
                    async Task<CallRequest> doAcaCreateCallRequest()
                    {
                        if (masterAccountId == 0)
                            masterAccountId = MasterAccountTypes.AaaAca;

                        var aaaMessage = (AaaMessage)jsonObj;
                        var aaaRequest = JsonConvert.DeserializeObject<Integrations.MotorClubs.Aaa.Ace.DispatchModel>(aaaMessage.JsonData);

                        AaaContractor contractor = AaaContractor.GetById(aaaMessage.AaaContractorId);


                        DateTime expirationDate = DateTime.Now;

                        if (!string.IsNullOrWhiteSpace(aaaRequest.RequiredAcknowledgeTimeInSeconds))
                            expirationDate = DateTime.Now.AddSeconds(Convert.ToInt32(aaaRequest.RequiredAcknowledgeTimeInSeconds));

                        var accountId = contractor.AccountId;
                        var companyId = contractor.CompanyId;

                        var maxEta = 120;

                        var poNumber = aaaRequest.PurchaseOrderNumber;
                        string reason = "";
                        var service = "";

                        var problem = aaaRequest.Call.RequestedService.TroubleCodes.FirstOrDefault(r => r.Type == "PROBLEM");

                        if (problem != null)
                            reason = Core.FormatName(problem.Description);

                        var pacesetter = aaaRequest.Call.RequestedService.TroubleCodes.FirstOrDefault(r => r.Type == "PACESETTER");

                        if (pacesetter != null)
                            service = Core.FormatName(pacesetter.Description);

                        var aaaStartingLocation = aaaRequest.Call.BreakdownLocation.Address.ToString();
                        var aaaTowDest = aaaRequest.Call.TowDestination.Address.ToString();

                        var vehicleString = aaaRequest.Call.Vehicle.ModelYear.GetValueOrDefault() + " " +
                            aaaRequest.Call.Vehicle.MakeName + " " +
                            aaaRequest.Call.Vehicle.ModelName;

                        var aaaCallRequest = new CallRequest()
                        {
                            AccountId = accountId,
                            CompanyId = companyId,
                            ExpirationDate = expirationDate,
                            PurchaseOrderNumber = poNumber,
                            Reason = reason,
                            RequestDate = DateTime.Now,
                            ServiceNeeded = service,
                            StartingLocation = aaaStartingLocation.Truncate(512),
                            TowDestination = aaaTowDest.Truncate(512),
                            Vehicle = vehicleString.Truncate(500),
                            MaxEta = maxEta,
                            ProviderId = contractor.ContractorId
                        };

                        await aaaCallRequest.Save();

                        var fnDispatch = new AaaDispatch()
                        {
                            AaaContractorId = contractor.AaaContractorId,
                            DispatchId = aaaRequest.DispatchId,
                            DispatchJson = aaaMessage.JsonData,
                            CallRequestId = aaaCallRequest.CallRequestId
                        };

                        fnDispatch.Save();

                        if (deliver)
                            await aaaCallRequest.Deliver();

                        if (aaaCallRequest.ExpirationDate != null)
                            await SendExpiredRequestEvent(aaaCallRequest.CallRequestId, aaaCallRequest.ExpirationDate.Value);

                        return aaaCallRequest;
                    }

                    return await doAcaCreateCallRequest();

                case MotorClubName.AaaAcg:
                case MotorClubName.AaaNationalFsl:
                    async Task<CallRequest> doAcgCreateCallRequest()
                    {
                        var aaaMessage = (AaaMessage)jsonObj;
                        var aaaRequest= JsonConvert.DeserializeObject<SalesforceData<ScheduledJobPayload>>(aaaMessage.JsonData).Payload;

                        AaaContractor contractor = AaaContractor.GetById(aaaMessage.AaaContractorId);

                        DateTime expirationDate = DateTime.Now.AddSeconds(720);

                        if (contractor.MasterAccountId == MasterAccountTypes.AaaNationalFsl)
                            expirationDate = DateTime.Now.AddSeconds(600);

                        var accountId = contractor.AccountId;
                        var companyId = contractor.CompanyId;

                        var maxEta = 120;

                        var poNumber = aaaRequest.AppointmentNumber;
                        string reason = "";

                        var service = aaaRequest.WorkTypeName;

                        var aaaStartingLocation = aaaRequest.Street + ", " + aaaRequest.City + " " + 
                            aaaRequest.State + " " + aaaRequest.PostalCode;

                        var aaaTowDest = aaaRequest.TowStreet + " " + aaaRequest.TowCity + " " +
                            aaaRequest.TowState + " " + aaaRequest.TowPostalCode;

                        var vehicleString = aaaRequest.VehicleYear + " " +
                            aaaRequest.VehicleMake + " " +
                            aaaRequest.VehicleModel + " " + aaaRequest.VehicleColor + " " + aaaRequest.VehicleDriveType;

                        var aaaCallRequest = new CallRequest()
                        {
                            AccountId = accountId,
                            CompanyId = companyId,
                            ExpirationDate = expirationDate,
                            PurchaseOrderNumber = poNumber,
                            Reason = reason,
                            RequestDate = DateTime.Now,
                            ServiceNeeded = service,
                            StartingLocation = aaaStartingLocation.Truncate(512),
                            TowDestination = aaaTowDest.Truncate(512),
                            Vehicle = vehicleString.Truncate(500),
                            MaxEta = maxEta,
                            ProviderId = contractor.ContractorId
                        };

                        await aaaCallRequest.Save();

                        var fnDispatch = new AaaDispatch()
                        {
                            AaaContractorId = contractor.AaaContractorId,
                            DispatchId = aaaRequest.WorkOrderId,
                            DispatchJson = aaaMessage.JsonData,
                            CallRequestId = aaaCallRequest.CallRequestId
                        };

                        fnDispatch.Save();

                        if (deliver)
                            await aaaCallRequest.Deliver();

                        if (aaaCallRequest.ExpirationDate != null)
                            await SendExpiredRequestEvent(aaaCallRequest.CallRequestId, 
                                aaaCallRequest.ExpirationDate.Value, contractor.MasterAccountId);

                        return aaaCallRequest;
                    }

                    return await doAcgCreateCallRequest();

                case MotorClubName.AaaNational:
                    async Task<CallRequest> doA3NCreateCallRequest()
                    {
                        if (masterAccountId == 0)
                            masterAccountId = MasterAccountTypes.AaaNational;

                        var aaaMessage = (AaaMessage)jsonObj;
                        var aaaRequest = JsonConvert.DeserializeObject<Integrations.MotorClubs.Aaa.National.CallCreatePayload>(aaaMessage.JsonData).Calls.FirstOrDefault();

                        AaaContractor contractor = AaaContractor.GetById(aaaMessage.AaaContractorId);

                        DateTime expirationDate = DateTime.Now;
                        expirationDate = DateTime.Now.AddSeconds(180);

                        var accountId = contractor.AccountId;
                        var companyId = contractor.CompanyId;

                        var maxEta = 120;

                        var poNumber = aaaRequest.CallKey;
                        string reason = "";
                        var service = "";

                        var problem = aaaRequest.Service.TroubleCodes.FirstOrDefault(r => r.TroubleCodeType == "PROBLEM");

                        if (problem != null)
                            reason = Core.FormatName(problem.Description);

                        var pacesetter = aaaRequest.Service.TroubleCodes.FirstOrDefault(r => r.TroubleCodeType == "PACESETTER");

                        if (pacesetter != null)
                            service = Core.FormatName(pacesetter.Description);

                        var aaaStartingLocation = aaaRequest.Service.ServiceLocations.FirstOrDefault(o => o.ServiceLocationType == "BREAKDOWN")?.Foi?.Location?.Address?.Full;
                        var aaaTowDest = aaaRequest.Service.ServiceLocations.FirstOrDefault(o => o.ServiceLocationType == "TOW_DESTINATION")?.Foi?.Location?.Address?.Full;

                        var vehicleString = aaaRequest.Vehicle.Year + " " +
                            aaaRequest.Vehicle.Make + " " +
                            aaaRequest.Vehicle.Model;

                        var aaaCallRequest = new CallRequest()
                        {
                            AccountId = accountId,
                            CompanyId = companyId,
                            ExpirationDate = expirationDate,
                            PurchaseOrderNumber = poNumber,
                            Reason = reason,
                            RequestDate = DateTime.Now,
                            ServiceNeeded = service,
                            StartingLocation = aaaStartingLocation.Truncate(512),
                            TowDestination = aaaTowDest.Truncate(512),
                            Vehicle = vehicleString.Truncate(500),
                            MaxEta = maxEta,
                            ProviderId = contractor.ContractorId
                        };

                        await aaaCallRequest.Save();

                        var fnDispatch = new AaaDispatch()
                        {
                            AaaContractorId = contractor.AaaContractorId,
                            DispatchId = aaaRequest.CallKey,
                            DispatchJson = aaaMessage.JsonData,
                            CallRequestId = aaaCallRequest.CallRequestId
                        };

                        fnDispatch.Save();

                        if (deliver)
                            await aaaCallRequest.Deliver();

                        if (aaaCallRequest.ExpirationDate != null)
                            await SendExpiredRequestEvent(aaaCallRequest.CallRequestId, aaaCallRequest.ExpirationDate.Value, contractor.MasterAccountId);

                        return aaaCallRequest;
                    }

                    return await doA3NCreateCallRequest();

                case MotorClubName.AaaWaNy:
                    async Task<CallRequest> doWaNyCreateCallRequest()
                    {
                        var aaaMessage = (AaaMessage)jsonObj;
                        var aaaRequestX = JsonConvert.DeserializeObject<SalesforceData<WorkOrderModel>>(aaaMessage.JsonData);
                        var aaaRequest = aaaRequestX.Payload;

                        var contractor = AaaContractor.GetById(aaaMessage.AaaContractorId);

                        var expirationDate = DateTime.Now.AddMinutes(10);

                        if (contractor.MasterAccountId == MasterAccountTypes.AaaWashington)
                            expirationDate = DateTime.Now.AddMinutes(15);

                        var accountId = contractor.AccountId;
                        var companyId = contractor.CompanyId;

                        var maxEta = 120;

                        var poNumber = aaaRequest.WorkOrderNumber;
                        string reason = "";
                        var service = "";

                        var problem = aaaRequest.TroubleCode;
                        service = problem;
                        if (problem != null)
                            reason = problem.TrimStart('0');

                        var mar = MasterAccountReason.GetByMasterAccountId(masterAccountId)
                            .FirstOrDefault(o => o.Type == MasterAccountReasonType.TroubleCodes &&
                                o.Code == reason);
                        
                        if (mar != null)
                            problem = service = $"{mar.Code} - {mar.Name}";
                       
                        var aaaStartingLocation = aaaRequest.Address;

                        if (string.IsNullOrWhiteSpace(aaaRequest.TowDestinationAddress))
                            aaaRequest.TowDestinationAddress = aaaRequest.TowDestinationStreet + "," +
                            aaaRequest.TowDestinationCity + " " +
                            aaaRequest.TowDestinationState + " " +
                            aaaRequest.TowDestinationPostalCode;

                        var aaaTowDest = aaaRequest.TowDestinationAddress;

                        var vehicleString = aaaRequest.VehicleYear + " " +
                            aaaRequest.VehicleMake + " " +
                            aaaRequest.VehicleModel;

                        var aaaCallRequest = new CallRequest()
                        {
                            AccountId = accountId,
                            CompanyId = companyId,
                            ExpirationDate = expirationDate,
                            PurchaseOrderNumber = poNumber,
                            Reason = reason,
                            RequestDate = DateTime.Now,
                            ServiceNeeded = service,
                            StartingLocation = aaaStartingLocation.Truncate(512),
                            TowDestination = aaaTowDest.Truncate(512),
                            Vehicle = vehicleString.Truncate(500),
                            MaxEta = maxEta,
                            ProviderId = contractor.ContractorId
                        };

                        await aaaCallRequest.Save();

                        var fnDispatch = new AaaDispatch()
                        {
                            AaaContractorId = contractor.AaaContractorId,
                            DispatchId = aaaRequest.ErsCallKey,
                            DispatchJson = aaaMessage.JsonData,
                            CallRequestId = aaaCallRequest.CallRequestId
                        };

                        fnDispatch.Save();

                        if (deliver)
                            await aaaCallRequest.Deliver();

                        if (aaaCallRequest.ExpirationDate != null)
                            await SendExpiredRequestEvent(aaaCallRequest.CallRequestId, aaaCallRequest.ExpirationDate.Value, contractor.MasterAccountId);

                        return aaaCallRequest;
                    }

                    return await doWaNyCreateCallRequest();

                case MotorClubName.Bcaa:
                    async Task<CallRequest> doBcaaCreateCallRequest()
                    {
                        var aaaMessage = (AaaMessage)jsonObj;
                        var aaaRequestX = JsonConvert.DeserializeObject<Integrations.MotorClubs.Aaa.Bcaa.DispatchModel>(aaaMessage.JsonData);
                        var aaaRequest = aaaRequestX.Job;

                        var contractor = AaaContractor.GetById(aaaMessage.AaaContractorId);

                        var expirationDate = DateTime.Now.AddMinutes(10);

                        if (contractor.MasterAccountId == MasterAccountTypes.AaaWashington)
                            expirationDate = DateTime.Now.AddMinutes(15);

                        var accountId = contractor.AccountId;
                        var companyId = contractor.CompanyId;

                        var maxEta = 120;

                        var poNumber = aaaRequest.JobId;
                        string reason = "";
                        var service = "";

                        var problem = aaaRequest.TroubleCode;
                        service = problem;
                        if (problem != null)
                            reason = problem.TrimStart('0');

                        var mar = MasterAccountReason.GetByMasterAccountId(masterAccountId)
                            .FirstOrDefault(o => o.Type == MasterAccountReasonType.TroubleCodes &&
                                o.Code == reason);

                        if (mar != null)
                            problem = service = $"{mar.Code} - {mar.Name}";

                        var aaaStartingLocation = aaaRequest.IncidentAddress.ToString();
                        var aaaTowDest = aaaRequest.DestinationAddress.ToString();
                        var vehicleString = aaaRequest.Vehicle.ToString();

                        var aaaCallRequest = new CallRequest()
                        {
                            AccountId = accountId,
                            CompanyId = companyId,
                            ExpirationDate = expirationDate,
                            PurchaseOrderNumber = poNumber,
                            Reason = reason,
                            RequestDate = DateTime.Now,
                            ServiceNeeded = service,
                            StartingLocation = aaaStartingLocation.Truncate(512),
                            TowDestination = aaaTowDest.Truncate(512),
                            Vehicle = vehicleString.Truncate(500),
                            MaxEta = maxEta,
                            ProviderId = contractor.ContractorId
                        };

                        await aaaCallRequest.Save();

                        var fnDispatch = new AaaDispatch()
                        {
                            AaaContractorId = contractor.AaaContractorId,
                            DispatchId = aaaRequestX.DispatchId,
                            DispatchJson = aaaMessage.JsonData,
                            CallRequestId = aaaCallRequest.CallRequestId
                        };

                        fnDispatch.Save();

                        if (deliver)
                            await aaaCallRequest.Deliver();

                        if (aaaCallRequest.ExpirationDate != null)
                            await SendExpiredRequestEvent(aaaCallRequest.CallRequestId, aaaCallRequest.ExpirationDate.Value, contractor.MasterAccountId);

                        return aaaCallRequest;
                    }

                    return await doBcaaCreateCallRequest();

                default:
                    throw new NotImplementedException("No implementation for: " + mcName.ToString());
            }
        }

        /// <summary>
        /// Schedules an event to request a phone call from the motor club if no response (accept, reject, or manual phone) is received by the user
        /// </summary>
        /// <param name="timestamp"></param>
        /// <param name="expDate"></param>
        /// <param name="companyId"></param>
        /// <param name="accountId"></param>
        /// <param name="request"></param>
        private static async Task ScheduleAutomatedPhoneCallRequest(DateTime timestamp, DateTime? expDate, int companyId, int accountId, CallRequest request)
        {
            // add a scheduled event to request a phone call in the given required acknowledge time
            if (expDate != null)
            {
                var cmp = await Company.Company.GetByIdAsync(companyId);
                if (cmp == null)
                    return;

                await DigitalDispatchService.NotifyCallUpdateEventAsync(
                    companyId: companyId,
                    accountId: accountId,
                    json: new
                    {
                        Id = request.CallRequestId,
                        MasterAccountReasonId = 0,
                        FullName = cmp.Name,
                        PhoneNumber = cmp.Phone,
                        OwnerUserId = 1
                    }.ToJson(null),
                    eventType: DigitalDispatchService.CallUpdateType.PhoneCall,
                    scheduledDate: expDate.Value.ToLocalTime(),
                    ownerUserId: 1);

                logger.Log(LogLevel.Info, "{3}/{4}: Scheduled automatic phone call request for {0} which is {1} away from the timestamp of {2}",
                    expDate.Value.ToLocalTime().ToLongTimeString(),
                    (expDate.Value.ToLocalTime() - timestamp.ToLocalTime()).TotalSeconds,
                    timestamp.ToLocalTime(),
                    request.CallRequestId,
                    request.PurchaseOrderNumber);
            }
        }
        /// <summary>
        /// Schedules an event to request a phone call from the motor club if no response (accept, reject, or manual phone) is received by the user
        /// </summary>
        /// <param name="timestamp"></param>
        /// <param name="expDate"></param>
        /// <param name="companyId"></param>
        /// <param name="accountId"></param>
        /// <param name="request"></param>
        private static async Task ScheduleEtaCheck(
            DateTime checkTime, DateTime eta,
            int companyId, int accountId, Entry entry, CallRequest request)
        {
            var cmp = await Company.Company.GetByIdAsync(companyId);
            if (cmp == null)
                return;

            int masterAccountId = entry.Account.MasterAccountId;

            var queueItemId = await DigitalDispatchService.NotifyCallUpdateEventAsync(
                companyId: companyId,
                accountId: accountId,
                json: new
                {
                    Id = request.CallRequestId,
                    CallId = request.DispatchEntryId,
                    MasterAccountId = masterAccountId,
                }.ToJson(null),
                eventType: DigitalDispatchService.CallUpdateType.EtaCheck,
                scheduledDate: checkTime,
                ownerUserId: 1);

            logger.Info(masterAccountId, "EtaCheck", "Scheduled ETA Check", request.ProviderId,
                companyId: companyId,
                data: new
                {
                    eta = eta,
                    scheduled = checkTime
                },
                callId: request.DispatchEntryId,
                callRequestId: request.CallRequestId,
                queueItemId: queueItemId,
                poNumber: entry.PurchaseOrderNumber,
                callNumber: entry.CallNumber);
        }



        internal static async Task ScheduleVoiceDispatch(CallRequest request)
        {

            // add a scheduled event to request a phone call in the given required acknowledge time
            if (request.ExpirationDate != null)
            {
                var cmp = await Company.Company.GetByIdAsync(request.CompanyId);
                if (cmp == null)
                    return;

                if (!await cmp.HasFeatureAsync(Generated.Features.VoiceDispatch))
                    return;

                await DigitalDispatchService.NotifyCallUpdateEventAsync(
                    companyId: request.CompanyId,
                    accountId: request.AccountId,
                    json: new
                    {
                        CallRequestId = request.CallRequestId,
                        PhoneNumber = cmp.Phone
                    }.ToJson(null),
                    eventType: DigitalDispatchService.CallUpdateType.VoiceDispatch,
                    scheduledDate: request.RequestDate.AddSeconds(38),
                    ownerUserId: 1);

                logger.Log(LogLevel.Info, "{3}/{4}: Scheduled Voice Dispatch phone call for {0} which is {1} away from the timestamp of {2}",
                    request.ExpirationDate.Value.ToLocalTime().ToLongTimeString(),
                    (request.ExpirationDate.Value.ToLocalTime() - request.RequestDate.ToLocalTime()).TotalSeconds,
                    request.RequestDate.ToLocalTime(),
                    request.CallRequestId,
                    request.PurchaseOrderNumber);
            }
        }


        internal static async Task SendExpiredRequestEvent(int callRequestId, DateTime expirationDateLocal, int masterAccountId = 0)
        {
            if (masterAccountId == 0)
            {
                logger.Log(LogLevel.Info, "{0}: Scheduled Expiration Event for {1}, current time is {2}, seconds away: {3}",
                    callRequestId,
                    expirationDateLocal.ToShortDateString() + expirationDateLocal.ToLongTimeString(),
                    DateTime.Now.ToLongTimeString(),
                    (DateTime.Now - expirationDateLocal).TotalSeconds);
            }
            else
            {
                logger.Log(LogLevel.Info, "{4} {0}: Scheduled Expiration Event for {1}, current time is {2}, seconds away: {3}",
                    callRequestId,
                    expirationDateLocal.ToShortDateString() + expirationDateLocal.ToLongTimeString(),
                    DateTime.Now.ToLongTimeString(),
                    (DateTime.Now - expirationDateLocal).TotalSeconds,
                    MasterAccountTypes.GetName(masterAccountId));
            }
            var dictionary = new Dictionary<string, object> {
                    { "callRequestId", callRequestId },

            };

            if (masterAccountId > 0)
                dictionary["masterAccountId"] = masterAccountId;

            await ServiceBusHelper.SendMessageAsync(DigitalDispatchService.ExpiredRequestsQueueName,
                callRequestId + "_expired", null,
                "Hide Expired Request for CallRequestId " + callRequestId,
                dictionary,
                expirationDateLocal.ToUniversalTime());
        }


        internal static async Task CheckForRoadsideFeatureAndAutoInvite(Entry e, EntryContact c)
        {
            if (await e.Company.HasFeatureAsync(Generated.Features.Roadside))
            {
                var setting = Roadside.RoadsideSetting.GetByCompanyId(e.CompanyId, e.AccountId);
                if (setting != null && Roadside.RoadsideSetting.IsAutoInviteEnabled(setting, e))
                {
                    await EntryContact.TriggerRoadsideUserInvite(c);
                }
            }
        }

        [Trace]
        private async Task DeInit()
        {
            var tasks = new List<Task>();
            foreach (KeyValuePair<string, Consumer> consumer in consumers)
            {
                Console.WriteLine("Stopping consumer MotorClubDispatchingService for queue" + consumer.Key);
                tasks.Add(consumer.Value.CleanAsync());
            }
            
            await Task.WhenAll(tasks.ToArray());

            await ENServiceBusHelper.Cleanup();
            await ServiceBusHelper.Cleanup();
        }    

        public static async Task<int> GetBestDriver(int companyId, int callRequestId, decimal incidentLat = 0, decimal incidentLong = 0, string incidentZip = null)
        {
            if (incidentLat == 0)
            {
                var cr = await CallRequest.GetByIdAsync(callRequestId);
                var ujo = UrgentlyJobOffer.GetByCallRequestId(cr.CallRequestId);
                var model = JsonConvert.DeserializeObject<UrgentlyRestClient.JobOfferModel>(ujo.OfferJson);

                incidentLat = model.serviceLocation.Latitude;
                incidentLong = model.serviceLocation.Longitude;
                incidentZip = model.serviceLocation.Address.Replace(",", "")
                            .Split(' ').Skip(1).Where(o => o.Length == 5 && Regex.IsMatch(o, @"^\d+$")).LastOrDefault();
            }

            var company = await Company.Company.GetByIdAsync(companyId);

            var drivers = Driver.GetByCompany(company).Where(o => o.EndDate == null).ToList();

            // Dispatchers (aka supervisors) are limited to only receive offers in their Primary location. 
            foreach (var driver in drivers.ToCollection())
            {
                if (driver.CompanyId != companyId)
                {
                    var driverUser = await User.GetByIdAsync(driver.UserId);
                    if (driverUser.Type == User.TypeEnum.Dispatcher)
                    {
                        drivers = drivers.Where(o => o != driver).ToList();
                    }
                }
            }

            var currentCalls = await Entry.GetCurrentByCompanyAsync(company, true, false, false, true);

            Console.WriteLine("This company has " + drivers.Count + " total drivers");

            var users = drivers.Select(o => o.UserId);
            var keys = DriverKeyValue.GetByDriver(companyId,
                drivers.Select(o => o.Id),
                Provider.Towbook.ProviderId,
                new string[] {
                    "Rating",
                    "BlockedZipCodesJson",
                    "ScheduleForceOff",
                    "ScheduleAlwaysOn",
                    "ScheduleJson"
                });

            // Schedule, AlwaysOn, ForceOff
            // Locations

            var locations = (users.Count() > 20 ?
                UserLocationHistoryItem.GetClosest(companyId, incidentLat, incidentLong).ToArray() : 
                UserLocationHistoryItem.GetCurrentByUserIds(
                users, DateTime.Now.AddHours(-72), DateTime.Now.AddMinutes(5)).ToArray());

            

            var driverKeys = DriverKey.GetAll().Where(o => o.ProviderId == Provider.Towbook.ProviderId).ToArray();

            var keyRating = driverKeys.First(o => o.Name == "Rating").Id;
            var keyBlockedZipCodesJson = driverKeys.First(o => o.Name == "BlockedZipCodesJson").Id;
            var keyScheduleForceOff = driverKeys.First(o => o.Name == "ScheduleForceOff").Id;
            var keyScheduleAlwaysOn = driverKeys.First(o => o.Name == "ScheduleAlwaysOn").Id;
            var keyScheduleJson = driverKeys.First(o => o.Name == "ScheduleJson").Id;

            var today = DateTime.Now.DayOfWeek.ToString().ToUpperInvariant();

            drivers = drivers.Where(d => locations.Any(l => l.UserId == d.UserId)).ToList();

            Console.WriteLine("We have " + drivers.Count + "(" +
                drivers.Select(o => o.Name).ToJson() + ") drivers available with recent gps");

            var warnings = new List<string>();

            void addWarning(List<string> lt, string warning)
            {
                //                Console.WriteLine(warning);
                lt.Add(warning);
            }

            var busyDrivers = new List<Driver>();

            var busyDriversOver3 = new List<Driver>();
            var drivers2 = new List<Driver>();

            var previousAttemptedDrivers = new List<int>();

            foreach (var driver in drivers)
            {
                var driverName = driver.Id + "/" + driver.Name;

                // step 1: ignore drivers that are forced off
                var forceOff = keys.FirstOrDefault(o => o.DriverId == driver.Id && o.KeyId == keyScheduleForceOff)?.Value;
                if (forceOff == "1")
                {
                    addWarning(warnings, "Removing " + driverName + " because they are Marked Unavailable.");
                    continue;
                }

                // step 2: make sure the call isn't in a zip that the driver won't work in
                var blockedZips = keys.FirstOrDefault(o => o.DriverId == driver.Id && o.KeyId == keyBlockedZipCodesJson)?.Value;
                if (blockedZips != null)
                {
                    // only 5 digit zip codes are valid.
                    var blockedZipList = blockedZips.Replace(",", " ")
                        .Replace(";", "")
                        .Replace("\n", " ")
                        .Replace("\r", " ")
                        .Split(' ')
                        .Where(o => o.Length == 5);

                    Console.WriteLine(driverName + ": Blocked Zips: " + blockedZipList.ToJson());

                    var blocked = false;
                    foreach (var zip in blockedZipList)
                    {
                        if (incidentZip.Contains(zip))
                        {
                            addWarning(warnings, "Removing " + driverName + " because the driver has the zip code of this call blocked");
                            blocked = true;
                            break;
                        }
                    }

                    if (blocked)
                    {
                        continue;
                    }
                }

                // step 3: check if they are always on
                var alwaysOn = keys.FirstOrDefault(o => o.DriverId == driver.Id && o.KeyId == keyScheduleAlwaysOn)?.Value;

                if (alwaysOn != "1")
                {
                    var schedule = keys.FirstOrDefault(o => o.DriverId == driver.Id && o.KeyId == keyScheduleJson)?.Value;
                    if (schedule != null)
                    {
                        Console.WriteLine(driverName + ": Schedule found for today...");

                        var sched = JsonConvert.DeserializeObject<IEnumerable<AutoDispatch.AutoDispatchServiceBusHandler.ScheduleItem>>(schedule).Where(o =>
                        string.Equals(o.DayName, today, StringComparison.InvariantCultureIgnoreCase));

                        var foundMatch = false;
                        var timeofday = DateTime.Now.TimeOfDay;
                        foreach (var slot in sched)
                        {
                            Console.WriteLine(driverName + ": " + JsonExtensions.ToJson(sched));

                            if (slot.Start == slot.Stop && slot.Start.Ticks == 0)
                            {
                                Console.WriteLine(driverName + ": MATCH FOUND! " + slot.ToJson() + " / " + DateTime.Now.TimeOfDay);

                                foundMatch = true;
                                break;
                            }

                            slot.Start = slot.Start.Add(TimeSpan.FromMinutes(-(long)company.TimezoneOffset * 60));
                            slot.Stop = slot.Stop.Add(TimeSpan.FromMinutes(-(long)company.TimezoneOffset * 60));

                            if ((timeofday >= slot.Start && ((slot.Stop.Ticks == 0 || timeofday < slot.Stop || slot.Stop < slot.Start))) ||  // handle stopping at 12:00am which is 0 ticks.
                                ((slot.Stop < slot.Start) ? timeofday < slot.Stop : false) // handle crossover (7pm-7am)
                              )
                            {
                                Console.WriteLine(driverName + ": MATCH FOUND! " + slot.ToJson() + " / " + DateTime.Now.TimeOfDay);
                                foundMatch = true;
                                break;
                            }
                            else
                            {
                                Console.WriteLine(driverName + ": NOT A MATCH *** " + slot.ToJson() + " / " + DateTime.Now.TimeOfDay);
                            }
                        }

                        if (!foundMatch)
                        {
                            addWarning(warnings, "Removing " + driverName + " because they are not scheduled to work.");
                            continue;
                        }
                    }
                }

                var alreadyWorkingOnCalls = currentCalls.Where(o => o.Status.Id > Status.Waiting.Id && o.DriverId == driver.Id &&
                    (o.Reason == null || !(o.Reason.NameMatchable().Contains("goa") || o.Reason.NameMatchable().Contains("goneonarrival")))).ToList();

                Console.WriteLine(driverName + ": already working on: " +
                    alreadyWorkingOnCalls.Select(o => new
                    {
                        callId = o.Id,
                        o.CallNumber,
                        o.PurchaseOrderNumber,
                        o.TowSource,
                        o.Status?.Name
                    }).ToJson());

                if (alreadyWorkingOnCalls.Any())
                {
                    if (alreadyWorkingOnCalls.Count() <= 3) // use busyDriver when driver is 3 calls or less deep.
                        busyDrivers.Add(driver);
                    else
                        busyDriversOver3.Add(driver);

                    if (alreadyWorkingOnCalls.Count() > 3)
                    {
                        logger.Info(MasterAccountTypes.Towbook, "AutoAcceptPickDriver", "Skipping driver, working on at least 3 (" + alreadyWorkingOnCalls.Count + " actual) other call(s)",
                            companyId: companyId, data: new
                            {
                                driverId = driver?.Id,
                                driverName = driver?.Name,
                                calls = alreadyWorkingOnCalls.Select(o => new
                                {
                                    o.CallNumber,
                                    o.PurchaseOrderNumber,
                                    o.TowSource,
                                    o.Status?.Name
                                }),
                                warnings
                            }, callRequestId: callRequestId);

                        continue;
                    }
                    else
                    {
                        // Driver is Located at X,Y (1251 Braeburn) - Real-Time Gps
                        // Because driver is currently working on other calls, calculate distance from driver's last call.

                        var lastCall = alreadyWorkingOnCalls.OrderBy(o => o.CreateDate).Last();

                        // TODO: look at DESTINATION, not just PICKUP... 

                        var lastCalLat = lastCall.Waypoints.Where(o => o.Title == "Pickup").FirstOrDefault()?.Latitude ?? 0;
                        var lastCalLng = lastCall.Waypoints.Where(o => o.Title == "Pickup").FirstOrDefault()?.Longitude ?? 0;

                        var location = locations.Where(o => o.UserId == driver.UserId).FirstOrDefault();
                        if (location != null && lastCalLat != 0)
                        {
                            logger.Info(MasterAccountTypes.Towbook, "AutoAcceptPickDriver", "Replacing driver gps with coordinates of last call",
                                companyId: companyId, data: new
                                {
                                    driverId = driver?.Id,
                                    driverName = driver?.Name,
                                    gps = new { lat = location.Latitude, lng = location.Longitude },
                                    replacedWith = new { callId = lastCall.Id, callNumber = lastCall.CallNumber, lat = lastCalLat, lastCalLng }
                                }, callRequestId: callRequestId);

                            location.Latitude = lastCalLat;
                            location.Longitude = lastCalLng;
                            location.GpsSource = "Call";
                        }
                    }
                }

                drivers2.Add(driver);
            }

            // no drivers, and no busy drivers, thus there will be no one to do the job anytime soon.
            // really should look at schedule  though, someone could be coming online in 5 minutes for instance.
            if (!drivers2.Any() && !busyDrivers.Any() && !busyDriversOver3.Any())
            {
                logger.Info(MasterAccountTypes.Towbook, "AutoAcceptPickDriver", "No drivers are available, either idle or busy. Recommending reject.",
                    companyId: companyId, callRequestId: callRequestId);

                return -1;
            }

            locations = locations.Where(o => drivers2.Where(d => d.UserId == o.UserId).Any() ||
                busyDrivers.Where(b => b.UserId == o.UserId).Any()).ToArray();

            var srcLat = incidentLat;
            var srcLng = incidentLong;

            Console.WriteLine("Contacting Google with...");
            Console.WriteLine(string.Join("|", locations.Select(o => o.Latitude + "," + o.Longitude)),
                srcLat + "," + srcLng);

            var obj = await AutoDispatchServiceBusHandler.GetMatrixAsync(string.Join("|", locations.Select(o => o.Latitude + "," + o.Longitude)),
                srcLat + "," + srcLng);

            var list = new List<AutoDispatch.LocationResult>();

            for (int i = 0; i < obj.Rows.Count; i++)
            {
                var row = obj.Rows[i];
                var cl = locations[i];
                var d = drivers2.Where(o => o.UserId == cl.UserId);

                var busyDriver = busyDrivers.Where(o => o.UserId == cl.UserId).FirstOrDefault();

                if (d != null && d.Any())
                {
                    if (row.Elements.First().Status != "ZERO_RESULTS" && row.Elements.First().Distance != null)
                    {
                        list.Add(new AutoDispatch.LocationResult
                        {
                            DriverId = d.First().Id,
                            DriverName = d.First().Name,
                            Latitude = cl.Latitude,
                            Longitude = cl.Longitude,
                            EstimatedDistanceMiles = (decimal)row.Elements.First().Distance.Value / (decimal)1609.344,
                            EstimatedTimeSeconds = row.Elements.First().Duration.Value
                        });
                    }
                }
                else if (busyDriver != null)
                {
                    if (row.Elements.First().Status != "ZERO_RESULTS" && row.Elements.First().Distance != null)
                    {
                        list.Add(new AutoDispatch.LocationResult
                        {
                            DriverId = busyDriver.Id,
                            DriverName = busyDriver.Name,
                            Latitude = cl.Latitude,
                            Longitude = cl.Longitude,
                            EstimatedDistanceMiles = (decimal)row.Elements.First().Distance.Value / (decimal)1609.344,
                            EstimatedTimeSeconds = row.Elements.First().Duration.Value,
                            IsBusy = true
                        });
                    }
                }
            }

            list = list.Where(o => o.EstimatedDistanceMiles < 80).ToList();

            var ran = new Random();

            foreach (var dr in list)
            {
                var rating = Convert.ToInt32(keys.Where(o =>
                    o.DriverId == dr.DriverId &&
                    o.KeyId == keyRating).FirstOrDefault()?.Value ?? "0");

                // randomly assign rating during testing
                //if (rating == 0)
                //    rating = ran.Next(0, 5);

                dr.Rating = rating;
                dr.EstimatedTimeSecondsWithRatingAdjustment = dr.EstimatedTimeSeconds;
                if (dr.Rating > 1)
                {
                    int modifiedRating = (rating - 1) * 15;

                    dr.EstimatedTimeSecondsWithRatingAdjustment = dr.EstimatedTimeSeconds -
                        (int)(dr.EstimatedTimeSeconds * (modifiedRating / 100.0M));
                }
            }

            int maxMinutes = 90;

            // less than 20 minutes, or rating must be 5.
            var busyOnly = list.Where(o => o.IsBusy).ToList();

            list = list.Where(o => !o.IsBusy &&
                (o.EstimatedTimeSecondsWithRatingAdjustment < (60 * maxMinutes) || o.Rating == 5)).ToList();

            list = list.OrderBy(o => o.EstimatedTimeSecondsWithRatingAdjustment).ToList();

            Console.WriteLine(JsonExtensions.ToJson(list, true));

            var driverToDispatch = list.FirstOrDefault();

            // ok, look at the driver we picked, but see if we have a busy driver that's better.
            var busyDriverToDispatch = busyOnly.FirstOrDefault(o =>
                driverToDispatch == null || o.EstimatedTimeSecondsWithRatingAdjustment < driverToDispatch.EstimatedTimeSecondsWithRatingAdjustment);

            if (busyDriverToDispatch != null)
            {
                if (driverToDispatch == null || busyDriverToDispatch.EstimatedTimeSecondsWithRatingAdjustment <
                    (driverToDispatch.EstimatedTimeSecondsWithRatingAdjustment * 0.5))
                {
                    logger.Info(MasterAccountTypes.Towbook, "AutoAcceptPickDriver",
                        "Picking a busy driver instead because of distance. ",
                        companyId: companyId, callRequestId: callRequestId,
                        data: new
                        {
                            pickedBusyDriver = busyDriverToDispatch,
                            originalDriver = driverToDispatch,
                            others = list.Where(o => o.DriverId != driverToDispatch.DriverId),
                            busyDrivers = busyOnly,
                            warnings
                        });

                    return busyDriverToDispatch.DriverId;
                }
            }

            if (driverToDispatch != null)
            {
                Console.WriteLine("We have picked " + driverToDispatch.DriverId + " " + driverToDispatch.DriverName + " to send the call to.");
                Console.WriteLine("We have picked " + driverToDispatch.ToJson());

                logger.Info(MasterAccountTypes.Towbook, "AutoAcceptPickDriver", "Determined a driver.",
                    companyId: companyId, callRequestId: callRequestId,
                    data: new
                    {
                        picked = driverToDispatch,
                        others = list.Where(o => o.DriverId != driverToDispatch.DriverId),
                        warnings
                    });
            }
            else
            {
                logger.Info(MasterAccountTypes.Towbook, "AutoAcceptPickDriver", "Couldn't determine a driver.",
                    companyId: companyId, callRequestId: callRequestId,
                    data:
                    new
                    {
                        driversToPickFrom = drivers.Select(o => new { driverId = o.Id, userId = o.UserId, name = o.Name }),
                        warnings
                    });
                return 0;
            }

            // We have the driver to dispatch now, so let's dispatch it.

            Console.WriteLine("******BestDriver: " + driverToDispatch.ToJson());
            return driverToDispatch.DriverId;
        }

        public static string GetCallRequestDriverKey(int callRequestId) => $"callRequestDriver_{callRequestId}";

        public static void SaveDriversForCallRequest(int callRequestId, int driverId)
        {
            if (driverId > 0)
                Core.SetRedisValue(GetCallRequestDriverKey(callRequestId),  driverId.ToString(), TimeSpan.FromMinutes(30));
        }
        
        public static async Task<int> GetDriverForCallRequest(int callRequestId)
        {
            string redisVal = await Core.GetRedisValueAsync(GetCallRequestDriverKey(callRequestId));

            if (redisVal == null || redisVal == "") 
                return 0;
                
            return int.Parse(redisVal);
        }
    }

    public static class Extensions
    {
        public static async Task AssignRequestDriverToEntry(this Entry entry)
        {
            if (entry == null)
                throw new ArgumentNullException(nameof(entry), $"entry passed into {nameof(AssignRequestDriverToEntry)} is null.");

            if (!entry.CallRequestId.HasValue)
                return;

            if (entry.CallRequestId == null)
                return;

            var driverId = await MotorClubDispatchingService.GetDriverForCallRequest(entry.CallRequestId.Value);
            if (driverId < 1)
                return;

            var driver = await Driver.GetByIdAsync(driverId);
            if (driver == null)
                throw new TowbookException($"Can't assign Driver to Call, driverId {driverId} doesn't exist.");

            entry.DriverId = driverId;
            entry.Status = Status.Dispatched;
            await entry.Save();

            await AutoDispatchServiceBusHandler.SendDispatchToDriver(entry, driver);
        }
    }
}
