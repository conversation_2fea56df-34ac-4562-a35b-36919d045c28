using System;
using System.Linq;
using System.Threading.Tasks;
using Agero.Types;
using Azure.Messaging.ServiceBus;
using Extric.Towbook.Accounts;
using Extric.Towbook.Integration.MotorClubs;
using Extric.Towbook.Integration.MotorClubs.Queue;
using Extric.Towbook.Integration.MotorClubs.Services;
using Newtonsoft.Json;
using OonAgeroClient;
using OonAgeroClient.Data;
using Extric.Towbook.Vehicle;
using Extric.Towbook.Integration.MotorClubs.Dispatch;
using NLog;
using Extric.Towbook.Utility;
using Extric.Towbook.Dispatch;
using Extric.Towbook.Integration.MotorClubs.Model;

namespace Extric.Towbook.Services.MotorClubDispatchingService
{
    public class OutOfNetworkOutgoing
    {
        internal static OonAgeroRestClient client = new OonAgeroRestClient();
        internal static OonAgeroRestClient devClient = new OonAgeroRestClient("DEV");

        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        internal static async System.Threading.Tasks.Task<bool> HandleOutOfNetwork(DigitalDispatchActionQueueItem qi, dynamic jsonObj, ProcessMessageEventArgs r)
        {
            if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingAcceptCall)
            {
                return await HandleAccept(qi, jsonObj, r);
            }
            else if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingRejectCall)
            {
                return await HandleReject(qi, jsonObj, r);
            }
            else if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingStatusUpdate)
            {
                return await HandleStatusUpdate(qi, r);
            }
            else if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingCallCanceled)
            {
                return await HandleCancel(qi, r);
            }
            else if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingRequestGoa)
            {
                return await HandleGoa(qi, r);
            }

            await r.CompleteAsync();

            return true;
        }

        private static async Task<bool> HandleAccept(DigitalDispatchActionQueueItem qi, dynamic jsonObj, ProcessMessageEventArgs r)
        {
            Dispatch.CallRequest callRequest = Dispatch.CallRequest.GetById(Convert.ToInt32(jsonObj.Id ?? jsonObj.CallRequestId));

            var oad = OonAgeroDispatch.GetByCallRequestId(callRequest.CallRequestId);
            oad.Eta = Convert.ToInt32(jsonObj.Eta);
            oad.Save();

            if (oad.Eta > 90)
            {
                // refuse.
            }

            logger.Info(MasterAccountTypes.OonAgero, "HandleAccept", "Accepting Dispatch",
                dispatchId: oad.DispatchId,
                data: new
                {
                    oonAgeroDispatchId = oad.Id,
                    eta = oad.Eta,
                    userId = qi.OwnerUserId,
                }, companyId: qi.CompanyId,
                callRequestId: callRequest.CallRequestId,
                queueItemId:  qi.QueueItemId);

            var company = await Company.Company.GetByIdAsync(callRequest.CompanyId);
            var user = await User.GetByIdAsync(qi.OwnerUserId.GetValueOrDefault());

            // if accepted, send, and mark all others as another provider responded. 

            // otherwise, find call requests and cancel them.
            var acceptModel = new OonAgeroRestClient.AcceptModel()
            {
                Eta = oad.Eta,
                VendorInfo = new OonAgeroRestClient.VendorInfo()
                {
                    Address = new OonAgeroRestClient.VendorInfo.VendorAddress()
                    {
                        Address1 = company.Address,
                        City = company.City,
                        State = company.State,
                        PostalCode = company.Zip,
                        Lat = company.Latitude,
                        Lng = company.Longitude
                    },
                    CompanyName = "TB-OON " + company.Name.Trim(),
                    ContactName = user.FullName ?? "Dispatch",
                    ContactPhone = Core.FormatPhoneWithDashesOnly(company.Phone),
                    TowbookVendorId = Integrations.Email.EmailAddress.GetPrimaryTowbookEmailAddress(company.Id)
                }

            };

            // TODO: distributed lock so that we don't have a race condition.
            OonAgeroRestClient.Result result;
            if (company.Id == 10000)
                result = devClient.Accept(oad.DispatchId, acceptModel);
            else 
                result = client.Accept(oad.DispatchId, acceptModel);

            if (result.IsOk)
            {
                var toCancel = OonAgeroDispatch.GetByDispatchId(oad.DispatchId).Where(o => o.CallRequestId != oad.CallRequestId);

                foreach (var t in toCancel)
                {
                    var crx = Dispatch.CallRequest.GetById(t.CallRequestId);
                    if (crx != null && crx.Status == Dispatch.CallRequestStatus.None)
                    {
                        await crx.UpdateStatus(Dispatch.CallRequestStatus.AnotherProviderResponded);

                        logger.Info(MasterAccountTypes.OonAgero, "HandleAccept", "Cancelling this dispatch for any other companies offered",
                            data: new
                            {
                                oonAgeroDispatchId = t.Id,
                                companyId = crx.CompanyId,
                                dispatchId = oad.DispatchId,
                                awardedCompanyId = qi.CompanyId
                            },
                            callRequestId: crx.CallRequestId,
                            queueItemId: qi.QueueItemId);
                    }
                }

                try
                {
                    // create call
                    var en = await CreateCall(oad, callRequest, company);
                    await en.AssignRequestDriverToEntry();
                    logger.Info(MasterAccountTypes.OonAgero, "HandleAccept", "Created Call",
                        dispatchId: oad.DispatchId,
                        companyId: qi.CompanyId,
                        data: new
                        {
                            oonAgeroDispatchId = oad.Id,
                            companyId = qi.CompanyId,
                            dispatchId = oad.DispatchId,
                            awardedCompanyId = qi.CompanyId,
                            callNumber = en.CallNumber
                        },
                        callId: en.Id,
                        callRequestId: oad.CallRequestId,
                        queueItemId: qi.QueueItemId);

                    await callRequest.UpdateStatus(CallRequestStatus.Accepted, qi.OwnerUserId, po: en.PurchaseOrderNumber);

                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                }
                catch (Exception rx)
                {
                    Console.WriteLine(rx.ToString());
                    await callRequest.UpdateStatus(Dispatch.CallRequestStatus.AcceptFailed, qi.OwnerUserId);

                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                }
            }
            else
            {
                logger.Error(MasterAccountTypes.OonAgero, "HandleAccept", "Accepting of Dispatch failed.",
                    data: new
                    {
                        oonAgeroDispatchId = oad.Id,
                        dispatchId = oad.DispatchId,
                        eta = oad.Eta,
                        userId = qi.OwnerUserId,
                        response = result.ResponseText,
                        request = acceptModel.ToJson()
                    },
                    callRequestId: callRequest.CallRequestId,
                    queueItemId: qi.QueueItemId);

                await callRequest.UpdateStatus(Dispatch.CallRequestStatus.AcceptFailed, qi.OwnerUserId);

                DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
            }

            // assign to provider.

            await r.CompleteAsync();
            return true;
        }


        public static async Task<Entry> CreateCall(OonAgeroDispatch oad, Dispatch.CallRequest cr, Company.Company company)
        {
            var disp = JsonConvert.DeserializeObject<OonDispatch>(oad.DispatchJson);

            var ne = new Dispatch.Entry()
            {
                CompanyId = company.Id,
                AccountId = cr.AccountId,
            };

            string notes = "";

            ne.ReasonId = await ReasonHelper.DetermineReasonId(ne.Account.MasterAccountId, cr.CompanyId, disp.ServiceType);
            ne.OwnerUserId = 1;

            if (ne.ReasonId == 1635)
                notes = OonDispatch.AddNote(notes, "Service Needed: " + disp.ServiceType);

            notes = OonDispatch.AddNote(notes, "Equipment Required: " + String.Join(",", disp.EquipmentList.Select(o => o.Type)));
            ne.ArrivalETA = DateTime.Now.AddMinutes(oad.Eta);

            int vehicleYear = 0;
            int.TryParse(disp.Vehicle.Year, out vehicleYear);
            var lookupVin = Utility.VinDecoder.Decode(disp.Vehicle.Vin);

            if (lookupVin != null)
            {
                if (lookupVin.DriveType == "AWD" || lookupVin.DriveType == "4WD" || lookupVin.DriveType == "4X4")
                {
                    notes = OonDispatch.AddNote(notes, "*** DRIVE TYPE: " + lookupVin.DriveType + " ***", true);
                }
                else
                {
                    notes = OonDispatch.AddNote(notes, "Drive Type: " + lookupVin.DriveType, true);
                }
            }

            ne.Assets.Add(new Dispatch.EntryAsset()
            {
                BodyTypeId = 1,
                Year = vehicleYear,
                Make = disp.Vehicle.Make,
                Model = disp.Vehicle.Model,
                LicenseNumber = disp.Vehicle.Plate,
                LicenseState = disp.Vehicle.State,
                Vin = disp.Vehicle.Vin,
                ColorId = VehicleUtility.GetColorIdByName(disp.Vehicle.Color),
                DriveType = !string.IsNullOrWhiteSpace(lookupVin?.DriveType) ? lookupVin.DriveType : null
            });

            var pickup = ne.Waypoints.Where(o => o.Title == "Pickup").FirstOrDefault();
            var dest = ne.Waypoints.Where(o => o.Title == "Destination").FirstOrDefault();

            if (disp.DisablementLocation != null)
            {
                ne.TowSource = disp.DisablementLocation.ToString();
                if (pickup == null)
                {
                    pickup = new Dispatch.EntryWaypoint() { Title = "Pickup", Position = 1 };
                    ne.Waypoints.Add(pickup);
                }
                pickup = await disp.DisablementLocation.ToWaypoint(pickup);
                notes = OonDispatch.AddNote(notes, pickup.Notes);
            }

            if (disp.TowDestination != null)
            {
                if (dest == null)
                {
                    dest = new Dispatch.EntryWaypoint() { Title = "Destination", Position = 2 };
                    ne.Waypoints.Add(dest);
                }
                ne.TowDestination = disp.TowDestination.ToString();
                dest = disp.TowDestination.ToWaypoint(dest);

                notes = OonDispatch.AddNote(notes, dest.Notes);
            }

            foreach (var c in disp.Comments)
            {
                notes = OonDispatch.AddNote(notes, c.CommentText + ":" + c.DisplayText);
            }

            if (disp.DisablementLocation != null &&
                (!string.IsNullOrWhiteSpace(disp.DisablementLocation.ContactInfo.Name) ||
                !string.IsNullOrWhiteSpace(disp.DisablementLocation.ContactInfo.Phone) ||
                !string.IsNullOrWhiteSpace(disp.DisablementLocation.ContactInfo.CallbackNumber)))
            {
                ne.Contacts.Add(new Dispatch.EntryContact()
                {
                    Name = disp.DisablementLocation.ContactInfo.Name,
                    Phone = disp.DisablementLocation.ContactInfo.Phone
                });
                if (disp.DisablementLocation.ContactInfo.Phone !=
                    disp.DisablementLocation.ContactInfo.CallbackNumber &&
                    !string.IsNullOrWhiteSpace(disp.DisablementLocation.ContactInfo.CallbackNumber))
                {
                    ne.Contacts.Add(new Dispatch.EntryContact()
                    {
                        Name = disp.DisablementLocation.ContactInfo.Name,
                        Phone = disp.DisablementLocation.ContactInfo.CallbackNumber
                    });
                }
            }

            if (!string.IsNullOrWhiteSpace(disp.AgeroCallbackNumber))
            {
                ne.Contacts.Add(new Dispatch.EntryContact() { Name = "Agero Dispatch", Phone = disp.AgeroCallbackNumber });
            }

            notes = OonDispatch.AddNote(notes, "\n\n" +
                "Your driver must have GPS enabled in Towbook to confirm that the " +
                (disp.TowDestination != null ? "pickup and tow destination was reached." : "service location was reached. ") +
                "\nIf you have any questions, Agero can be reached at " + Core.FormatPhone(disp.AgeroCallbackNumber));

            ne.Notes = notes;

            var oa = ne.InvoiceItems.FirstOrDefault(o => o.Name == MotorClubDispatchingService.OON_LINE_ITEM_NAME);
            if (oa == null)
            {
                ne.InvoiceItems.Add(new Dispatch.InvoiceItem()
                {
                    CustomName = MotorClubDispatchingService.OON_LINE_ITEM_NAME,
                    Quantity = 1,
                    CustomPrice = disp.OfferPrice
                });
            }

            ne.SetAttribute(Dispatch.AttributeValue.BUILTIN_BILLING_NOTES,
                $"Once this service is fulfilled and call status is set to Completed, a one-time credit card number will appear here for payment.\n " +
                $"If a GOA results, the amount will be {disp.GoaPrice.ToString("C")} upon Agero’s confirmation that the customer has left the scene.\n" +
                $"Please contact Agero at: {Core.FormatPhone(disp.AgeroCallbackNumber)} with any questions.");

            await ne.Save();

            cr.DispatchEntryId = ne.Id;
            await cr.Save();

            return ne;
        }

        private static async Task<bool> HandleReject(DigitalDispatchActionQueueItem qi, dynamic jsonObj, ProcessMessageEventArgs r)
        {
            CallRequest callRequest = Dispatch.CallRequest.GetById(Convert.ToInt32(jsonObj.Id ?? jsonObj.CallRequestId));

            var rejectReason = MasterAccountReason.GetById((int)jsonObj.MasterAccountReasonId);

            // store for collective response

            callRequest.OwnerUserId = Convert.ToInt32(jsonObj.OwnerUserId);
            await callRequest.UpdateStatus(Dispatch.CallRequestStatus.Rejected);
            
            qi.CallRequestId = callRequest.CallRequestId;
            DigitalDispatchService.LogAction(qi);
            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);

            // record that provider rejected it

            await r.CompleteAsync();
            return true;
        }

        private static async Task<bool> HandleStatusUpdate(DigitalDispatchActionQueueItem qi, ProcessMessageEventArgs r)
        {
            var sum = JsonConvert.DeserializeObject<StatusUpdatePayload>(
                qi.JsonObject);

            var cr = Dispatch.CallRequest.GetById(sum.CallRequestId);

            var oad = OonAgeroDispatch.GetByCallRequestId(sum.CallRequestId);

            // send status updates
            var dispatchId = oad.DispatchId;
            var code = ChangeStatusDispatchStatusCode.Unassigned;

            if (sum.NewStatusId == Dispatch.Status.Dispatched.Id)
                code = ChangeStatusDispatchStatusCode.Assigned;
            else if (sum.NewStatusId == Dispatch.Status.EnRoute.Id)
                code = ChangeStatusDispatchStatusCode.InRoute;
            else if (sum.NewStatusId == Dispatch.Status.Completed.Id)
                code = ChangeStatusDispatchStatusCode.JobCleared;
            else if (sum.NewStatusId == Dispatch.Status.BeingTowed.Id)
                code = ChangeStatusDispatchStatusCode.TowInProgress;
            else if (sum.NewStatusId == Dispatch.Status.AtSite.Id)
                code = ChangeStatusDispatchStatusCode.OnScene;


            OonAgeroRestClient clientToUse = client;
            if (qi.CompanyId == 10000)
                clientToUse = devClient;

            if (code != ChangeStatusDispatchStatusCode.Unassigned)
            {
                var en = await Dispatch.Entry.GetByIdNoCacheAsync(cr.DispatchEntryId.Value);

                if (string.IsNullOrWhiteSpace(en?.PurchaseOrderNumber))
                {
                    OutOfNetworkIncoming.AddToQueue(cr.DispatchEntryId.Value,
                        qi);

                    logger.Info(MasterAccountTypes.OonAgero, "StatusUpdate", "Deferred in queue until we get PO #.",
                        dispatchId: oad.DispatchId, 
                        companyId: cr.CompanyId,
                        data: new
                        {
                            oonAgeroDispatchId = oad.Id,
                            userId = qi.OwnerUserId,
                            code = ((int)code).ToString(),
                            statusId = sum.NewStatusId,
                            driverName = sum.DriverName,
                        },
                        callId:cr.DispatchEntryId.GetValueOrDefault(),
                        callRequestId: cr.CallRequestId,
                        queueItemId: qi.QueueItemId);
                }
                else
                {
                    var result = clientToUse.UpdateStatus(dispatchId, new OonAgeroRestClient.StatusUpdateModel()
                    {
                        Code = ((int)code).ToString(),
                        DriverName = sum.DriverName,
                        Latitude = sum.Latitude,
                        Longitude = sum.Longitude,
                        StatusTime = qi.CreateDate ?? DateTime.Now
                    });

                    if (result.IsOk)
                    {
                        logger.Info(MasterAccountTypes.OonAgero, "StatusUpdate", "Updated status successfully.",
                            dispatchId: oad.DispatchId,
                            companyId: qi.CompanyId,
                            data: new
                            {
                                oonAgeroDispatchId = oad.Id,
                                userId = qi.OwnerUserId,
                                code = ((int)code).ToString(),
                                statusId = sum.NewStatusId,
                                driverName = sum.DriverName,
                                wasDeferred = r == null
                            },
                            callId: cr.DispatchEntryId.GetValueOrDefault(),
                            callRequestId: cr.CallRequestId,
                            queueItemId: qi.QueueItemId);
                    }
                    else
                    {
                        logger.Error(MasterAccountTypes.OonAgero, "StatusUpdate", "Status update failed",
                            dispatchId: oad.DispatchId,
                            companyId: qi.CompanyId,
                            data: new
                            {
                                oonAgeroDispatchId = oad.Id,
                                userId = qi.OwnerUserId,
                                code = ((int)code).ToString(),
                                statusId = sum.NewStatusId,
                                driverName = sum.DriverName,
                                error = result.ResponseText
                            },
                            callId: cr.DispatchEntryId.GetValueOrDefault(),
                            callRequestId: cr.CallRequestId,
                            queueItemId: qi.QueueItemId);
                    }
                }
            }

            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
            
            if (r != null)
                await r.CompleteAsync();

            return true;
        }

        private static async Task<bool> HandleCancel(DigitalDispatchActionQueueItem qi, ProcessMessageEventArgs r)
        {
            var dcm = JsonConvert.DeserializeObject<CancelPayload>(JsonConvert.DeserializeObject<DigitalDispatchActionQueueItem>(r.GetBody<string>()).JsonObject);
            Console.WriteLine(dcm.ToJson(true));

            var callRequest = Dispatch.CallRequest.GetById(dcm.CallRequestId);
            var oad = OonAgeroDispatch.GetByCallRequestId(callRequest.CallRequestId);

            var mar = MasterAccountReason.GetById(dcm.ReasonId);

            OonAgeroRestClient clientToUse = client;
            if (qi.CompanyId == 10000)
                clientToUse = devClient;

            // todo: need cancel reason id to come over. 
            // send cancel event
            var result = clientToUse.Cancel(oad.DispatchId, Convert.ToInt32(mar.Code), mar.Name);

            if (result.IsOk)
            {
                logger.Info(MasterAccountTypes.OonAgero, "HandleCancel", "Cancelled dispatch successfully.",
                    dispatchId: oad.DispatchId,
                    data: new
                    {
                        oonAgeroDispatchId = oad.Id,
                        eta = oad.Eta,
                        userId = qi.OwnerUserId
                    },
                    callRequestId: callRequest.CallRequestId,
                    queueItemId: qi.QueueItemId);

                var en = await Dispatch.Entry.GetByIdNoCacheAsync(callRequest.DispatchEntryId.Value);
                if (en != null)
                {
                    if (en.Status.Id != Dispatch.Status.Cancelled.Id)
                    {
                        await en.Cancel(mar.Name, new AuthenticationToken() { UserId = qi.OwnerUserId.GetValueOrDefault(), ClientVersionId = Platform.ClientVersion.GetByGitHash("mcds").Id });
                    }
                }

                DigitalDispatchService.LogAction(qi);
                DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
            }
            else
            {
                logger.Error(MasterAccountTypes.OonAgero, "HandleCancel", "Cancelling of Dispatch failed.",
                    dispatchId: oad.DispatchId,
                    data: new
                    {
                        oonAgeroDispatchId = oad.Id,
                        eta = oad.Eta,
                        userId = qi.OwnerUserId,
                        response = result.ResponseText,
                    },
                    callRequestId: callRequest.CallRequestId,
                    queueItemId: qi.QueueItemId);
                
                var en = await Dispatch.Entry.GetByIdNoCacheAsync(callRequest.DispatchEntryId.Value);
                en.Notes = "Failed to digitally cancel call. Please call Agero to cancel the call.\n" + en.Notes;
                await en.Save();

                qi.CallRequestId = callRequest.CallRequestId;
                DigitalDispatchService.LogAction(qi);
                DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
            }
             
            await r.CompleteAsync();
            return true;
        }

        private static async Task<bool> HandleGoa(DigitalDispatchActionQueueItem qi, ProcessMessageEventArgs r)
        {
            var dgm = JsonConvert.DeserializeObject<GoaPayload>(
                JsonConvert.DeserializeObject<DigitalDispatchActionQueueItem>(r.GetBody<string>()).JsonObject);
            Console.WriteLine(dgm.ToJson(true));

            var callRequest = Dispatch.CallRequest.GetById(dgm.CallRequestId);
            var oad = OonAgeroDispatch.GetByCallRequestId(callRequest.CallRequestId);

            var mar = MasterAccountReason.GetById(dgm.ReasonId.GetValueOrDefault());

            OonAgeroRestClient clientToUse = client;
            if (qi.CompanyId == 10000)
                clientToUse = devClient;

            // todo: need cancel reason id to come over. 
            // send cancel event
            var result = clientToUse.Cancel(oad.DispatchId, Convert.ToInt32(mar.Code), mar.Name);

            if (result.IsOk)
            {
                logger.Info(MasterAccountTypes.OonAgero, "HandleGOA", "sent cancel with goa code successfully.",
                    dispatchId: oad.DispatchId,
                    data: new
                    {
                        oonAgeroDispatchId = oad.Id,
                        userId = qi.OwnerUserId,
                    },
                    callRequestId: callRequest.CallRequestId,
                    queueItemId: qi.QueueItemId);

                DigitalDispatchService.LogAction(qi);
                DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
            }
            else
            {
                logger.Error(MasterAccountTypes.OonAgero, "HandleGOA", "Sending of cancel with goa failed.",
                    dispatchId: oad.DispatchId,
                    data: new
                    {
                        oonAgeroDispatchId = oad.Id,
                        eta = oad.Eta,
                        userId = qi.OwnerUserId,    
                        response = result.ResponseText,
                    },
                    callRequestId: callRequest.CallRequestId,
                    queueItemId: qi.QueueItemId);

                var en = await Dispatch.Entry.GetByIdNoCacheAsync(callRequest.DispatchEntryId.Value);
                en.Notes = OonDispatch.AddNote(en.Notes, "Failed to digitally request GOA. Please call Agero to request the GOA.", true);
                await en.Save();

                qi.CallRequestId = callRequest.CallRequestId;
                DigitalDispatchService.LogAction(qi);
                DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
            }

            await r.CompleteAsync();
            return true;
        }
    }
}
