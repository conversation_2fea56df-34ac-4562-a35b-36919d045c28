using Extric.Towbook;
using Extric.Towbook.Accounts;
using Extric.Towbook.Dispatch;
using Extric.Towbook.Integration;
using Extric.Towbook.Integration.MotorClubs;
using Extric.Towbook.Integration.MotorClubs.Billing.Geico;
using Extric.Towbook.Utility;
using Newtonsoft.Json;
using NLog;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Diagnostics;
using System.Linq;
using System.Runtime.Versioning;
using System.Threading;
using System.Threading.Tasks;
using static MotorClubPaymentImportService.PaymentUtility;
using Async = System.Threading.Tasks;

namespace MotorClubPaymentImportService
{
    public class GeicoPaymentImporter
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();
        private static readonly User paymentUser = User.GetById(7);
        private static readonly AuthenticationToken paymentAuthToken = new AuthenticationToken() { UserId = 7 };

        [SupportedOSPlatform("windows")]
        private static void ConsoleTitleAdd(string text)
        {
            Console.Title += text;
        }

        public static async Async.Task SyncGeicoPayments(LogInfo logInfo, int accountId, string username, string password)
        {
            try
            {
                var testMode = false;

                var account = await Account.GetByIdAsync(accountId);
                if (account == null)
                {
                    Console.WriteLine("AccountId " + accountId + " doesn't exist.");
                    return;
                }

                var company = await Extric.Towbook.Company.Company.GetByIdAsync(account.CompanyId);

                GeicoConnection geico = null;
                try
                {
                    await logger.Info(MasterAccountTypes.Geico, "ImportPayments", "Logging in", accountId, account.CompanyId);
                    geico = GeicoConnection.Login(logInfo, username, password);
                    await logger.Info(MasterAccountTypes.Geico, "ImportPayments", "Logged in", accountId, account.CompanyId,
                        data: geico);
                }
                catch (InvalidLoginException ile)
                {
                    if (ile.Transient)
                    {
                        throw; // temporary failure, don't erase password.
                    }
                    else
                    {
                        var akv = AccountKeyValue.GetByAccount(account.CompanyId, accountId, Provider.Towbook.ProviderId, "McPassword").FirstOrDefault();

                        if (akv != null && !string.IsNullOrWhiteSpace(ile.Username))
                        {
                            akv.Value = "";
                            akv.Save();

                            var l = new LogEventInfo();
                            l.Message = "Cleared Password, Invalid Login";
                            l.Properties["commitId"] = Core.GetCommitId();
                            l.Properties["masterAccountId"] = MasterAccountTypes.Geico;
                            l.Properties["masterAccountName"] = MasterAccountTypes.GetName(MasterAccountTypes.Geico);
                            l.Properties["companyId"] = account.CompanyId;
                            l.Properties["invalidPassword"] = password;
                            l.Properties["exception"] = ile;
                            l.Level = LogLevel.Warn;

                            logger.Log(l);
                        }
                        return;
                    }
                }
                catch (Exception e)
                {
                    var l = new LogEventInfo();
                    l.Message = "Unknown error during login";
                    l.Properties["commitId"] = Core.GetCommitId();
                    l.Properties["masterAccountId"] = MasterAccountTypes.Geico;
                    l.Properties["masterAccountName"] = MasterAccountTypes.GetName(MasterAccountTypes.Geico);
                    l.Properties["companyId"] = account.CompanyId;
                    l.Properties["invalidPassword"] = password;
                    l.Properties["exception"] = e;
                    l.Level = LogLevel.Error;

                    logger.Log(l);
                    return;
                }

                // TODO: this should get moved further up in the process so this logic can be used with all the motor club imports - 
                // we don't need to look back 60 days for every MC as an example. If we imported payments from 2/20/19, we don't need to do it everyday for
                // the next 3 months.

                var lastImported = AccountKeyValue.GetFirstValueOrNull(account.CompanyId, accountId, Provider.Towbook.ProviderId, "ImportPaymentsLastProcessed");

                var filteredStartDate = DateTime.MinValue;

                if (lastImported != null)
                {
                    if (!DateTime.TryParse(lastImported, out filteredStartDate))
                    {
                        Console.WriteLine("Invalid ImportPaymentsLastProcessed Value: " + lastImported + "... using " + filteredStartDate + " instead");
                        await logger.Error(MasterAccountTypes.Geico, "ImportPayments", "Invalid ImportLastPaymentsProcessed Value", accountId, account.CompanyId,
                        data: new { invalidValue = lastImported });
                    }
                }


                if (filteredStartDate == DateTime.MinValue)
                {
                    var lastImport = (await Payment.GetByAccountAsync(account,
                        await Extric.Towbook.Company.Company.GetByIdAsync(account.CompanyId)))
                        .Where(o => o.OwnerUserId == 7).OrderBy(o => o.PaymentDate).LastOrDefault();

                    if (lastImport != null)
                    {
                        await logger.Info(MasterAccountTypes.Geico, "ImportPayments", "Found existing imported payment date", accountId, account.CompanyId,
                            data: new { date = lastImport.PaymentDate, paymentId = lastImport.Id });
                        filteredStartDate = lastImport.PaymentDate;
                    }
                    else
                    {
                        var recentCall = (await Entry.GetByAccountAsync(accountId, DateTime.Now.Date.AddDays(-60), DateTime.Now)).OrderBy(o => o.CreateDate).FirstOrDefault();

                        if (recentCall != null)
                        {
                            filteredStartDate = recentCall.CreateDate.Date;
                        }
                    }
                }
                if (filteredStartDate < DateTime.Now.Date.AddDays(-90))
                    filteredStartDate = DateTime.Now.Date.AddDays(-90);


                //filteredStartDate = Convert.ToDateTime("7/11/2018");

                Console.WriteLine("Using StartDate of " + filteredStartDate.Date.ToString());

                var bufferedNow = DateTime.Now.Date.AddDays(-1); // limit to 1 day ago because of issues with geico website

                for (var date = filteredStartDate; date < bufferedNow; date = date.AddDays(1))
                {
                    Console.WriteLine(username + ":" + date.ToString());
                    Console.Title = date.ToShortDateString() + " running";

                    if ((date.DayOfWeek == DayOfWeek.Saturday ||
                        date.DayOfWeek == DayOfWeek.Sunday))
                    {
                        Console.WriteLine(date.DayOfWeek + ": skipping");
                        continue;
                    }

                    var startDate = date;
                    var stopDate = date;

                    string key = "tmp-gco-payimp-" + accountId + username + startDate.ToString("MMddyyyy") + stopDate.ToString("MMddyyyy");
                    Console.WriteLine("key: " + key);

                    // try to get from redis first
                    var json = Core.GetRedisValue(key);
                    if (json == null)
                    {
                        int n = 0;
                        while (true)
                        {
                            var stopwatch = Stopwatch.StartNew();
                            try
                            {
                                var rootRecords = geico.GetPayments(logInfo, PaymentSearchBy.IssuedDate, startDate, stopDate);
                                var records = rootRecords?.Results;
                                stopwatch.Stop();
                                if (records != null)
                                {
                                    Console.WriteLine($"{accountId}: Geico : Fetched " + records.Count + " in " + stopwatch.ElapsedMilliseconds + "ms");
                                    json = records.ToJson(null);
                                    Core.SetRedisValue(key, json, TimeSpan.FromMinutes(30));
                                    break;
                                }
                                else
                                {
                                    Console.WriteLine($"{accountId}: Geico : Timed out in " + stopwatch.ElapsedMilliseconds + "ms...retrying");
                                    if (stopwatch.ElapsedMilliseconds < 10000)
                                    {
                                        Console.WriteLine("reconnecting");
                                        geico = GeicoConnection.Login(logInfo, username, password);
                                    }
                                    n++;
                                    var delay2 = 3000 + (n * 1000 + DateTime.Now.Second);
                                    Thread.Sleep(delay2);

                                    if (n > 10)
                                        break;
                                }
                            }
                            catch (Exception err)
                            {
                                n++;
                                var delay = 3000 + (n * 1000 + DateTime.Now.Second);
                                Console.WriteLine(err.ToString());
                                Console.WriteLine("pausing " + delay + "ms");
                                Thread.Sleep(delay);

                            }
                        }
                    }

                    if (json != null)
                    {
                        var replayed = JsonConvert.DeserializeObject<List<ClaimDetail>>(json);

                        var total = replayed.Sum(o => o.PaymentTotal);

                        if(OperatingSystem.IsWindows())
                        {
                            ConsoleTitleAdd(total.ToString("C"));
                        }

                        Console.WriteLine("Total:" + total);
                        if (total == 0)
                        {

                            await logger.Info(MasterAccountTypes.Geico, "ImportPayments", "No payments found for specified date.", accountId, account.CompanyId,
                                data: new
                                {
                                    day = date.DayOfWeek.ToString(),
                                    key,
                                    startDate,
                                    stopDate
                                });

                            Console.WriteLine(date.DayOfWeek.ToString() + ": No payments. Continuing.");
                            continue;
                        }

                        // Scenario 1: Find an existing AccountPayment that exists that contains this exact date and payment amount. 
                        // If we find it, then we can skip.

                        var existingAp = (await Payment.GetByAccountAsync(account, company))
                            .FirstOrDefault(o =>
                                ((o.Amount == total && o.OwnerUserId == 7 && o.PaymentDate.Date == startDate.Date) ||
                                (o.Amount == total && o.OwnerUserId != 7 &&
                                    o.PaymentDate >= startDate.AddDays(-3) &&
                                    o.PaymentDate <= startDate.AddDays(2)))
                                && !o.IsVoid);

                        if (existingAp != null)
                        {
                            Console.WriteLine("Payment Already Exists --- Id=" + existingAp.Id + ", Date=" + existingAp.PaymentDate.ToShortDateString() + ", Total=" + existingAp.Amount);
                            var accountPaymentInvoices = await InvoicePayment.GetByAccountPaymentIdAsync(existingAp.Id);
                            var accountPaymentInvoicesTotal = accountPaymentInvoices.Sum(o => o.Amount);

                            if (accountPaymentInvoicesTotal == existingAp.Amount &&
                                Math.Abs((existingAp.PaymentDate - replayed.First().PaymentDate).TotalDays) < 4)
                            {
                                Console.WriteLine($"{accountPaymentInvoicesTotal:C} found total in payments associated with this accountPaymentId. Skipping!");
                                continue;
                            }
                            else
                            {
                                existingAp = null;
                            }
                        }

                        // get the calls
                        // get any existing payments 

                        var calls = await Entry.GetByPurchaseOrderNumbersByMasterAccountIdAsync(account.Companies,
                            MasterAccountTypes.Geico,
                            replayed.Select(o => o.DispatchNumber).Distinct().ToArray());

                        Console.WriteLine(replayed.Select(o => o.DispatchNumber?.ToUpperInvariant()).Distinct<string>().Count() + " asked for");
                        Console.WriteLine(calls.Select(o => o.PurchaseOrderNumber?.ToUpperInvariant()).Distinct().Count() + " calls found");

                        // find duplicates
                        foreach (var group in calls.GroupBy(o => o.PurchaseOrderNumber.ToUpperInvariant()).Where(o => o.Count() > 1))
                        {
                            var completed = group.Where(o => o.Status == Status.Completed);
                            var cancelled = group.Where(o => o.Status == Status.Cancelled);

                            if (completed.Any() && cancelled.Any())
                            {
                                foreach (var cancelledCall in cancelled)
                                {
                                    Console.WriteLine("remove cancelled call id " + cancelledCall.Id + " from list");
                                    calls = calls.Where(o => o.Id != cancelledCall.Id);
                                }
                            }

                            if (completed.Count() > 1)
                            {
                                var keepThis = completed.First();
                                calls = calls.Where(o => o.PurchaseOrderNumber?.ToUpperInvariant() != keepThis.PurchaseOrderNumber.ToUpperInvariant() || o.Id == keepThis.Id);
                            }
                        }

                        ConcurrentBag<Task> tasks = new ConcurrentBag<Task>();

                        // create calls for payments that apply to PO's not already in towbook
                        foreach (var mc in replayed.Where(o => !calls.Any(c => c.PurchaseOrderNumber?.ToUpperInvariant() == o.DispatchNumber?.ToUpperInvariant())))
                        {
                            Console.WriteLine("**** CREATING MISSING CALL: " + mc.PurchaseOrderNumber + "***");

                            var en = new Entry()
                            {
                                CompanyId = account.CompanyId,
                                AccountId = account.Id,
                                PurchaseOrderNumber = mc.PurchaseOrderNumber,
                                CompletionTime = Convert.ToDateTime(mc.ServiceDate),
                                Status = Status.Completed
                            };

                            en.Contacts.Add(new EntryContact() { Name = mc.CustomerName });
                            en.Notes = "Call added by automatic payment import.\n" +
                                "GEICO paid you for this PO #, but it was not found in Towbook. \n" +
                                "Towbook automatically created the call based on the information in the payment import.";

                            var gi = geico.GetDispatchInformation(mc.PurchaseOrderNumber)?.InvoiceList?.LastOrDefault()?.InvoiceDetails;
                            if (gi == null)
                            {
                                await logger.Info(MasterAccountTypes.Geico, "ImportPayments", "Could not create missing call", accountId, account.CompanyId,
                                    data: new
                                    {
                                        purchaseOrderNumber = en.PurchaseOrderNumber,
                                        key,
                                        startDate,
                                        stopDate
                                    });
                                continue;
                            }
                            var pu = gi?.CurrentVehicleLocation;
                            var dest = gi?.DestinationAddress;
                            en.TowSource = (pu != null ?
                                pu.StreetAddress1 + ", " + pu.City + " " + pu.State + " " + pu.ZipCode?.Zip
                                : "(Unknown)");
                            if (dest != null)
                                en.TowDestination = dest.StreetAddress1 + ", " + dest.City + " " + dest.State + " " + dest.ZipCode?.Extension;

                            en.ReasonId = await Extric.Towbook.Integration.MotorClubs.Dispatch.ReasonHelper.DetermineReasonId(MasterAccountTypes.Geico,
                                account.CompanyId,
                                gi?.ServiceType);

                            string[] parts = mc.VehicleYMM.Split(new char[] { ' ' }, 3);
                            try { en.Year = Convert.ToInt32(parts[0]); } catch { }
                            en.VehicleMake = parts[1];
                            en.VehicleModel = parts[2];

                            en.Invoice.InvoiceItems.Add(new InvoiceItem()
                            {
                                CustomName = gi?.ServiceType ?? "Unknown",
                                Quantity = 1,
                                CustomPrice = mc.PaymentTotal
                            });

                            en.OwnerUserId = 7;

                            if (!testMode)
                            {
                                await en.Save(true, paymentAuthToken);
                                await SearchUtility.UpdateCall(en);
                                Console.WriteLine("created call number " + en.CallNumber);

                                await logger.Info(MasterAccountTypes.Geico, "ImportPayments", "Created missing call", accountId, account.CompanyId,
                                    data: new
                                    {
                                        callId = en.Id,
                                        callNumber = en.CallNumber,
                                        purchaseOrderNumber = en.PurchaseOrderNumber,
                                        key,
                                        startDate,
                                        stopDate
                                    });
                            }

                            calls = calls.Union(new Entry[] { en });
                        }

                        var existingPayments = InvoicePayment.GetByDispatchEntryIds(calls.Select(o => o.Id).ToArray(), null);
                        Console.WriteLine(existingPayments.Count + " payments");

                        var newPayments = new ConcurrentBag<InvoicePayment>();

                        // AccountPayment 

                        Parallel.ForEach(calls,
                            new ParallelOptions() { MaxDegreeOfParallelism = 2 },
                            async (Entry call) =>
                        {
                            var callPayments = existingPayments.Where(o => o.InvoiceId == call.Invoice.Id).ToCollection();
                            var paymentsToRecord = replayed.Where(o => o.DispatchNumber?.ToUpperInvariant() == call.PurchaseOrderNumber.ToUpperInvariant()).ToCollection();
                            call.Invoice.ForceRecalculate();
                            Console.WriteLine($"Working on Call# {call.CallNumber}, PO # {call.PurchaseOrderNumber}");
                            Console.WriteLine($"- Towbook Total: {call.InvoiceTotal:C}");
                            Console.WriteLine($"- Towbook Payments: {call.PaymentsApplied:C} / {callPayments.Count} payment count");
                            Console.WriteLine($"- Payments to try importing: {paymentsToRecord.Sum(o => o.PaymentTotal):C}");

                            // Scenario #1: Exact Payment already exists... Easy.
                            Console.WriteLine(call.PurchaseOrderNumber.ToUpperInvariant() + ": " + callPayments.Count + " payments already exist.");
                            Console.WriteLine(paymentsToRecord.ToJson(null));

                            var totalPaymentAmount = paymentsToRecord.Sum(o => o.PaymentTotal);

                            if (callPayments.Any())
                            {
                                // Scenario #2: Some customers record two payments on same day as one payment, find those and void them.

                                if (paymentsToRecord.Count > 1)
                                {
                                    var matchedPayment1 = callPayments.Where(o => o.Amount == totalPaymentAmount && !o.IsVoid).FirstOrDefault();

                                    if (matchedPayment1 != null)
                                    {
                                        // void combined payments, we replace with the real one below.
                                        if (matchedPayment1.AccountPaymentId > 0)
                                        {
                                            // void previosly created payment.
                                            var apMatched = await Payment.GetByIdAsync(matchedPayment1.AccountPaymentId);
                                            if (apMatched != null)
                                            {
                                                apMatched.Amount -= matchedPayment1.Amount;

                                                if (!testMode)
                                                    apMatched.Save();
                                            }

                                            if (!testMode)
                                            {
                                                await Retry(async() =>
                                                { 
                                                    await matchedPayment1.Void(paymentUser, null);

                                                    await logger.Info(MasterAccountTypes.Geico, "ImportPayments", "Voided previous individual payment", 
                                                        accountId, 
                                                        account.CompanyId,
                                                         data: new
                                                         {
                                                             accountPaymentId = matchedPayment1.AccountPaymentId,
                                                             amount = matchedPayment1.Amount,
                                                             paymentDate = matchedPayment1.PaymentDate,
                                                             key,
                                                             startDate,
                                                             stopDate
                                                         });
                                                }, 3);
                                                        
                                                Console.WriteLine("Voided PaymentID " + matchedPayment1.Id + "...Updated AccountPaymentId " + matchedPayment1.AccountPaymentId + " by subtracting " +
                                                    matchedPayment1.Amount);
                                            }
                                            else
                                            {
                                                Console.WriteLine("[WOULD HAVE, BUT IN TESTMODE] Voided PaymentID " + matchedPayment1.Id + "...Updated AccountPaymentId " + matchedPayment1.AccountPaymentId + " by subtracting " +
                                                    matchedPayment1.Amount);
                                            }

                                            call.Invoice.ForceRecalculate();
                                            callPayments = callPayments.Where(o => o.Id != matchedPayment1.Id).ToCollection();
                                        }
                                    }
                                }

                                // Scenario 3: Handle if this payment was manually added previously. If it was, we're going to move it to be part of this
                                // accountPaymentId to keep the books clean/consistent.

                                foreach (var payment in callPayments)
                                {
                                    var matchedPayment = paymentsToRecord.FirstOrDefault(o =>
                                        o.PaymentTotal == payment.Amount &&
                                        !payment.IsVoid &&
                                        (payment.PaymentType == PaymentType.Account ||
                                        payment.PaymentType == PaymentType.Check ||
                                        payment.PaymentType == PaymentType.EFT));

                                    if (matchedPayment != null)
                                    {
                                        Console.WriteLine("Found matching payment amount for this invoice: PaymentID " + payment.Id + "...");

                                        if (payment.AccountPaymentId > 0)
                                        {
                                            var accountPayment = await Payment.GetByIdAsync(payment.AccountPaymentId);
                                            if (accountPayment != null)
                                            {
                                                accountPayment.Amount -= payment.Amount;
                                                if (!testMode)
                                                {
                                                    if (accountPayment.Amount == 0)
                                                    {
                                                        await accountPayment.Void(7);
                                                    }
                                                    else
                                                    {
                                                        if (accountPayment.Amount > 0)
                                                            accountPayment.Save();
                                                    }
                                                }
                                            }
                                            payment.AccountPaymentId = 0;
                                        }

                                        payment.PaymentType = PaymentType.EFT;

                                        // place this in the new payment list, so that it will be saved with the new accountPaymentId.
                                        newPayments.Add(payment);

                                        // remove this so it doesn't get added a secondtime below. 
                                        paymentsToRecord.Remove(matchedPayment);
                                    }
                                }
                            }

                            // try to find an existing payment with this same amount

                            foreach (var payment in paymentsToRecord)
                            {
                                
                                var ip = new InvoicePayment()
                                {
                                    InvoiceId = call.Invoice.Id,
                                    PaymentDate = payment.PaymentDate,
                                    Amount = payment.PaymentTotal,
                                    ClassId = 2,
                                    OwnerUserId = 7,
                                    PaymentType = PaymentType.EFT,
                                    ReferenceNumber = payment.CheckNumber
                                };

                                if (ip.InvoiceId > 0)
                                    newPayments.Add(ip);
                            }
                        });

                        Console.WriteLine("we have " + newPayments.Count + " to add!");

                        var ap = new Payment();

                        ap.Amount = newPayments.Sum(o => o.Amount);
                        ap.PaymentDate = startDate;
                        ap.SplitType = SplitType.Multiple;
                        ap.Type = PaymentType.EFT;
                        ap.OwnerUserId = 7;
                        ap.Notes = "Imported automatically";
                        ap.AccountId = accountId;
                        ap.CompanyId = account.CompanyId;

                        // make sure we aren't creating a duplicate.

                        var duplicateKey = ap.CompanyId + ":" + ap.AccountId + ":" + ap.Amount + ":" + startDate;
                        var duplicateFound = Core.GetRedisValue(duplicateKey) != null;

                        if (!duplicateFound)
                        {
                            duplicateFound = (await Payment.GetByAccountAsync(account, company))
                                .Any(o =>
                                    o.OwnerUserId == ap.OwnerUserId &&
                                    o.Type == PaymentType.EFT &&
                                    o.PaymentDate == ap.PaymentDate &&
                                    o.Amount == ap.Amount);
                        }

                        if (duplicateFound)
                        {
                            await logger.Error(MasterAccountTypes.Geico, "ImportPayments", "Cancelled account payment; already exists",
                                accountId, account.CompanyId,
                                data: new
                                {
                                    amount = ap.Amount,
                                    paymentDate = ap.PaymentDate,
                                    key,
                                    startDate,
                                    stopDate
                                });
                        }
                        else
                        {
                            if (!testMode)
                                ap.Save();

                            Console.WriteLine(account.CompanyId + ": Created accountpayment " + ap.Id);

                            await logger.Info(MasterAccountTypes.Geico, "ImportPayments", "Created account payment", accountId, account.CompanyId,
                                data: new
                                {
                                    accountPaymentId = ap.Id,
                                    amount = ap.Amount,
                                    paymentDate = ap.PaymentDate,
                                    key,
                                    startDate,
                                    stopDate
                                });

                            var sw = Stopwatch.StartNew();
                            Parallel.ForEach(newPayments,
                                new ParallelOptions() { MaxDegreeOfParallelism = 16 },
                                async(InvoicePayment x) =>
                                {
                                    x.AccountPaymentId = ap.Id;

                                    if (!testMode)
                                        await x.Save(InvoicePayment.ResolveDifference.AdjustInvoiceWithHiddenItemIfGreaterThanTotal, tasks);
                                });

                            await Task.WhenAll(tasks);

                            SaveAccountKeyValue(ap.AccountId, ap.PaymentDate.Date.ToShortDateString(), "ImportPaymentsLastProcessed");

                            Console.WriteLine("Saved payments " + newPayments.Select(o => o.Id).ToJson());
                            sw.Stop();
                            await logger.Info(MasterAccountTypes.Geico, "ImportPayments", "Saved payments", accountId, account.CompanyId,
                                data: new
                                {
                                    accountPaymentId = ap.Id,
                                    invoicePayments = newPayments.Select(o =>
                                        new { id = o.Id, amount = o.Amount, referenceNumber = o.ReferenceNumber })
                                        .OrderBy(o => o.referenceNumber)
                                        .ToArray(),
                                    key,
                                    timeToCreate = sw.ElapsedMilliseconds,
                                    startDate,
                                    stopDate
                                });

                            Core.SetRedisValue(duplicateKey, DateTime.Now.ToJson(),
                                TimeSpan.FromDays(7));
                        }

                        if (!testMode)
                        {
                            // don't need to keep it in cache.
                            Core.DeleteRedisKey(key);
                            geico.Release();
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e.ToJson(true));
                HandleException(logger, logInfo, e, $"Importing payments for username: {username}",
                    new { accountId });
            }
        }

        private static async Task Retry( Func<Task> p, int retryCount = 3)
        {
            bool success = false;
            while (retryCount > 0 && !success)
            {
                try
                {
                    await p();
                    success = true;
                }
                catch (SqlException exception)
                {
                    if (exception.Number != 1205)
                    {
                        // a sql exception that is not a deadlock 
                        throw;
                    }
                    // Add delay here if you wish. 
                    retryCount--;
                    if (retryCount == 0) throw;
                }
            }
        }
    }
}
