<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" throwExceptions="true">
	<extensions>
		<add assembly="NLog.Targets.Loggly" />
	</extensions>
	<targets async="true">
		<target name="console" xsi:type="ColoredConsole" layout="${machinename} safeclearsynctool ${message}"/>
		<target name="loggly" xsi:type="Loggly" layout="${machinename} ${date} ${message}" />
		<target name="file" xsi:type="File"
		  layout="DateTime: ${longdate}, Logger: ${logger}, Level: ${level}, Message: ${message}, Properties: ${all-event-properties}"
		  fileName="c:/towbook/temp/safeclearsynctool-log.txt"
		  keepFileOpen="false"
		  encoding="iso-8859-1" />
	</targets>

	<rules>
		<logger name="*" minlevel="Trace" writeTo="console" enabled="true" />
		<logger name="*" minlevel="Trace" writeTo="loggly" />
		<logger name="*" minlevel="Trace" writeTo="file" enabled="true" />
	</rules>

</nlog>
