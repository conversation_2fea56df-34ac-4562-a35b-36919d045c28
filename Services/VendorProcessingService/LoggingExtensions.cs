using Extric.Towbook.Utility;
using NLog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.Services.VendorProcessingService
{
    public static class LoggingExtensions
    {
        public static async Task LogEvent(this Logger logger, string type, string message, object data, long milliseconds, Exception e, int? companyId, int? accountId, int? fileId) =>
            await Log(logger, LogLevel.Info, message, type, data, milliseconds, e, companyId, accountId, fileId);

        public static async Task LogTracingEvent(this Logger logger, string type, string message, object data, long milliseconds, Exception e, int? companyId, int? accountId, int? fileId) =>
            await Log(logger, LogLevel.Info, message, type, data, milliseconds, e, companyId, accountId, fileId);

        public static async Task LogUserEvent(this Logger logger, string type, string message, object data, long milliseconds, Exception e, int userId, int? companyId, int? accountId, int? fileId) =>
            await Log(logger, LogLevel.Info, message, type, data, milliseconds, e, companyId, accountId, fileId);

        private static async Task Log(
            this Logger logger,
            LogLevel level,
            string message,
            string type,
            object data = null,
            long milliseconds = 0,
            Exception ex = null,
            int? companyId = null,
            int? accountId = null,
            int? fileId = null)
        {
            var e = new LogEventInfo();
            e.Level = level ?? LogLevel.Info;

            if (ex != null)
            {
                e.Level = LogLevel.Error;

                e.Exception = ex;
                e.Properties["json"] = new
                {
                    ErrorMessage = e.Message,
                }.ToJson();
            }

            e.Message = message;
            e.Properties["type"] = type;

            e.Properties["commitId"] = Core.GetCommitId();
            e.Properties["totalTime"] = milliseconds;

            if (companyId != null)
            {
                e.Properties["companyId"] = companyId;
                var company = await Company.Company.GetByIdAsync(companyId.Value);
                if (company != null)
                    e.Properties["companyName"] = company.Name;
            }


            if (accountId != null)
                e.Properties["accountId"] = accountId;

            if (fileId != null)
                e.Properties["fileId"] = fileId;

            if (data != null)
            {
                if (data is string)
                    e.Properties["data"] = data;
                else if (data.GetType().IsArray)
                    e.Properties["array"] = data;
                else
                    e.Properties["object"] = data;
            }

            logger.Log(e);
        }
    }
}
