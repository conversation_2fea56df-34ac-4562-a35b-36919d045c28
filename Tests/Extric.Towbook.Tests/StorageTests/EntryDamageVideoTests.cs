using Extric.Towbook.Dispatch;

namespace Extric.Towbook.Tests.StorageTests
{
    public class EntryDamageVideoTests
    {
        [Fact]
        public async Task Location_WhenSavingAndRetrievingFilePath_ShouldResolveCorrectlyAsync()
        {
            // Arrange
            var companyId = 10;
            var entryDamageVideoId = 1;
            var expectedExtension = ".mp4";
            var entryDamageVideo = new EntryDamageVideo
            {
                DispatchEntryDamageId = 1,
            };

            EntryDamageVideoAccesor.SetId(entryDamageVideo, id: entryDamageVideoId);

            var separator = Path.DirectorySeparatorChar;
            var expectedFullPath =
                $"{Path.GetTempPath()}Towbook{separator}Storage{separator}CallDamages{separator}Videos{separator}{companyId}{separator}{entryDamageVideoId}{separator}{entryDamageVideo.DispatchEntryDamageId}{separator}{entryDamageVideo.Id}{expectedExtension}";

            // Act
            var savePath = entryDamageVideo.Location.Replace("%1", companyId.ToString()).Replace("%2", entryDamageVideoId.ToString());

            // Assert
            Assert.NotNull(entryDamageVideo);
            Assert.Equal(expectedFullPath, savePath);

            await FileStubHelper.CreateFileAsync(savePath);

            Assert.True(File.Exists(savePath));
            FileStubHelper.CleanupPathAsync(savePath);
        }

        /// <summary>
        /// Not exactly a UT, but will have to do due to the nature of the hard-coded 
        /// and coupled code-base. Will connect to S3 to get a URL for an S3 instance. This 
        /// specifically tests out if the resolved URL makes sense, mainly done to make sure
        /// EntryDamageVideo.Location didn't cause regression. This specifically is code that
        /// is called by <see cref="DamageFormDamageVideosController.Post"/>
        /// </summary>
        [Fact]
        public void LocationGetPresignedUrl_WhenTryingToGetS3Url_ShouldResolveCorrectly()
        {
            // Arrange
            var companyId = -10;
            var entryDamageVideoId = 1;
            var expectedExtension = ".mp4";
            var entryDamageVideo = new EntryDamageVideo
            {
                DispatchEntryDamageId = 1
            };
            
            EntryDamageVideoAccesor.SetId(entryDamageVideo, id: 1);

            var savePath = entryDamageVideo.Location.Replace("%1", companyId.ToString()).Replace("%2", entryDamageVideoId.ToString());
            var expectedS3Path = $"https://s3.amazonaws.com/storage.towbook.net/CallDamages/Videos/{companyId}/{entryDamageVideoId}/{entryDamageVideo.DispatchEntryDamageId}/{entryDamageVideo.Id}{expectedExtension}"; ;

            // Act
            var result = FileAccesor.GetPresignedUrl(
                new Storage.FileUtility(),
                savePath,
                contentType: "image/jpeg",
                expiresInMinutes: 10,
                verb: Amazon.S3.HttpVerb.PUT
            );

            // Assert
            Assert.NotNull(result);
            Assert.StartsWith(expectedS3Path, result);
        }

        [Fact]
        public async Task ThumbnailLocation_WhenSavingAndRetrievingFilePath_ShouldResolveCorrectlyAsync()
        {
            // Arrange
            var companyId = 10;
            var entryDamageVideoId = 1;
            var expectedExtension = ".jpg";
            var entryDamageVideo = new EntryDamageVideo
            {
                DispatchEntryDamageId = 1,
            };

            EntryDamageVideoAccesor.SetId(entryDamageVideo, id: entryDamageVideoId);

            var separator = Path.DirectorySeparatorChar;
            var expectedFullPath =
                $"{Path.GetTempPath()}Towbook{separator}Storage{separator}CallDamages{separator}Videos{separator}{companyId}{separator}{entryDamageVideoId}{separator}{entryDamageVideo.DispatchEntryDamageId}{separator}{entryDamageVideo.Id}{expectedExtension}";

            // Act
            var savePath = entryDamageVideo.ThumbnailLocation.Replace("%1", companyId.ToString()).Replace("%2", entryDamageVideoId.ToString());

            // Assert
            Assert.NotNull(entryDamageVideo);
            Assert.Equal(expectedFullPath, savePath);

            await FileStubHelper.CreateFileAsync(savePath);

            Assert.True(File.Exists(savePath));
            FileStubHelper.CleanupPathAsync(savePath);
        }

        /// <summary>
        /// Not exactly a UT, but will have to do due to the nature of the hard-coded 
        /// and coupled code-base. Will connect to S3 to get a URL for an S3 instance. This 
        /// specifically tests out if the resolved URL makes sense, mainly done to make sure
        /// EntryDamageVideo.ThumbnailLocation didn't cause regression. This specifically is code that
        /// is called by <see cref="DamageFormDamageVideosController.Post"/>
        /// </summary>
        [Fact]
        public void ThumbnailLocationGetPresignedUrl_WhenTryingToGetS3Url_ShouldResolveCorrectly()
        {
            // Arrange
            var companyId = -10;
            var entryDamageVideoId = 1;
            var expectedExtension = ".jpg";
            var entryDamageVideo = new EntryDamageVideo
            {
                DispatchEntryDamageId = 1
            };

            EntryDamageVideoAccesor.SetId(entryDamageVideo, id: 1);

            var savePath = entryDamageVideo.ThumbnailLocation.Replace("%1", companyId.ToString()).Replace("%2", entryDamageVideoId.ToString());
            var expectedS3Path = $"https://s3.amazonaws.com/storage.towbook.net/CallDamages/Videos/{companyId}/{entryDamageVideoId}/{entryDamageVideo.DispatchEntryDamageId}/{entryDamageVideo.Id}{expectedExtension}"; ;

            // Act
            var result = FileAccesor.GetPresignedUrl(
                new Storage.FileUtility(),
                savePath,
                contentType: "image/jpg",
                expiresInMinutes: 10,
                verb: Amazon.S3.HttpVerb.PUT
            );

            // Assert
            Assert.NotNull(result);
            Assert.StartsWith(expectedS3Path, result);
        }
    }
}
