namespace IdentityServer.Config
{
    using IdentityServer3.Core.Models;
    using System.Collections.Generic;

    public static class Clients
    {
        public static IEnumerable<Client> Get()
        {
            return new[]
            {
                new Client
                {
                    Enabled = true,
                    ClientName = "urgent.ly",
                    ClientId = "urgent.ly",
                    Flow = Flows.Implicit,
                    RedirectUris = new List<string>
                    {
                        "http://localhost:56668/popup.html",
                        "http://localhost:56668/silent-renew.html",
                        "https://test-towbook.urgent.ly/#/popup",
                        "https://test-towbook.urgent.ly/#/silent-renew",
                        "https://dev02.urgent.ly/#/popup",
                        "https://dev02.urgent.ly/#/silent-renew",
                        "https://dev01.urgent.ly/#/popup",
                        "https://dev01.urgent.ly/#/popup?x=1&",
                        "https://dev01.urgent.ly/#/silent-renew",
                        "https://dev02-dispatch.urgent.ly/#/towbook/popup",
                        "https://dev02-dispatch.urgent.ly/#/towbook/popup?",
                        "https://dev02-dispatch.urgent.ly/#/towbook/silent-renew",
                        "https://dev01-dispatch.urgent.ly/#/towbook/popup",
                        "https://dev01-dispatch.urgent.ly/#/towbook/popup?x=1&",
                        "https://dev01-dispatch.urgent.ly/#/towbook/silent-renew",
                        
                        // production
                        "https://dispatch.urgent.ly/#/towbook/popup",
                        "https://dispatch.urgent.ly/#/towbook/popup?",
                        "https://dispatch.urgent.ly/#/towbook/silent-renew",

                        // test
                        "http://localhost:56668/index.html#/popup",
                        "http://localhost:56668/index.html#/popup?x=1&",
                        "http://localhost:56668/index.html#/silent-renew",

                        "https://dev02-dispatch.urgent.ly/towbook/popup",
                        "https://dev02-dispatch.urgent.ly/towbook/popup?x=1&",
                        "https://dev01-dispatch.urgent.ly/towbook/popup",
                        "https://dev01-dispatch.urgent.ly/towbook/popup?x=1&",
                        "https://dispatch.urgent.ly/towbook/popup",
                        "https://dispatch.urgent.ly/towbook/popup?",
                        "https://dev02-dispatch.urgent.ly/towbook/popup.html",
                        "https://dev02-dispatch.urgent.ly/towbook/silent-renew.html",
                        "https://dev01-dispatch.urgent.ly/towbook/popup.html",
                        "https://dev01-dispatch.urgent.ly/towbook/silent-renew.html"


                    },
                    PostLogoutRedirectUris = new List<string>
                    {
                        "http://localhost:56668/index.html",
                        "https://test-towbook.urgent.ly/#/home",
                        "https://dev02-dispatch.urgent.ly/#/towbook",
                        "https://dev01-dispatch.urgent.ly/#/towbook",
                    },

                    AllowedCorsOrigins = new List<string>
                    {
                        "http://localhost:56668",
                        "https://test-towbook.urgent.ly",
                        "https://www.geturgently.com",
                        "https://www.urgent.ly",
                        "https://urgent.ly",
                        "http://dev02-dispatch.urgent.ly",
                        "https://dev02-dispatch.urgent.ly",
                        "http://dev01-dispatch.urgent.ly",
                        "http://dispatch.urgent.ly",
                        "https://dispatch.urgent.ly",
                    },
                    RequireConsent = true,
                    AllowRememberConsent = true,
                    AllowAccessToAllScopes = true,
                    AccessTokenLifetime = 3600 * 24 * 365
                }
            };
        }
    }
}