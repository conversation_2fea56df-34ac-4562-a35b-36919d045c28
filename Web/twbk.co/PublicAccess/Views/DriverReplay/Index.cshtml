@using System.Web
@using Microsoft.AspNetCore.Mvc.TagHelpers
<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>Towbook | Driver Replay</title>
    <script src="//ajax.aspnetcdn.com/ajax/jQuery/jquery-1.7.min.js" type="text/javascript"></script>
    <script src="//ajax.googleapis.com/ajax/libs/jqueryui/1.8.14/jquery-ui.min.js" type="text/javascript"></script>
    <script src="https://maps.googleapis.com/maps/api/js?client=gme-extricllc&libraries=places" type="text/javascript"></script>
    <script src="/static/maps/jquery.tmpl.min.js" type="text/javascript"></script>
    <script src="/static/maps/moment.2.29.4.min.js" type="text/javascript"></script>
    <script src="/static/maps/Towbook.js" type="text/javascript"></script>
    <script src="/static/maps/events.js"></script>
    <script src="/static/maps/map.js"></script>
    <script src="/static/maps/replay.js?v=@ViewBag.VersionNumber"></script>
    <script src="/static/maps/panel.js?v=@ViewBag.VersionNumber"></script>
    <script src="/static/maps/utils.js?v=@ViewBag.VersionNumber"></script>
    <script src="/static/maps/animate.js?v=@ViewBag.VersionNumber" type="text/javascript"></script>
    <script src="/static/maps/rangeslider.js?v=@ViewBag.VersionNumber" type="text/javascript"></script>
    <script src="/static/maps/jquery.timepicker.min.js" type="text/javascript"></script>
    <link  href="/static/maps/towbook-overrides.css?v=@ViewBag.VersionNumber" rel="stylesheet" media="screen" type="text/css" />
    <link href="/static/maps/map.css?v=@ViewBag.VersionNumber" rel="stylesheet" />
    <link href="/static/maps/rangeslider.css?v=@ViewBag.VersionNumber" rel="stylesheet" />
    <link href="/static/maps/panel.css?v=@ViewBag.VersionNumber" rel="stylesheet" />
    <link rel="stylesheet" href="https://pro.fontawesome.com/releases/v5.1.0/css/all.css" integrity="sha384-87DrmpqHRiY8hPLIr7ByqhPIywuSsjuQAfMXAE0sMUpY3BM7nXjf+mLIUSvhDArs" crossorigin="anonymous">
</head>
<main>
    <nav>
        <div id="calls-panel" class="panel list-panel">
            <div>
                <header>
                    <div>
                        <div class="title-wrap">
                            <select class="select-list">
                                <option value="0" selected>Current Calls</option>
                                <option value="1">Waiting Calls</option>
                                <option value="5">Active Calls</option>
                                <option value="2">Completed Calls</option>
                                <option value="3">Drivers</option>
                                <option value="4">Scheduled Calls</option>
                            </select>
                        </div>
                        <div class="filter-wrap">
                            <input id="calls-filter" value="" placeholder="Quick Filter" />
                            <div id="calls-filter-stop"><i class="fa fa-close"></i></div>
                        </div>
                        <div class="options-wrap"><i class="fa fa-ellipsis-v"></i></div>
                    </div>
                </header>
                <ul id="calls-list">
                    <li class="th">
                        <div class="call-number">&nbsp;#<i class="fa fa-chevron-up"></i></div>
                        <div class="account">ACCOUNT<i class="fa fa-chevron-up"></i></div>
                        <div class="call-eta">ETA<i class="fa fa-chevron-up"></i></div>
                        <div class="dot-wrap">
                            <div class="dot">&nbsp;</div>
                        </div>
                    </li>
                </ul>
                <div id="calls-none">No active calls found</div>
                <div class="calls-spinner">
                    <div class="ring-wrapper">
                        <div class="ring"></div>
                    </div>
                </div>
            </div>
        </div>
        <div id="drivers-panel" class="panel list-panel">
            <div>
                <header>
                    <div>
                        <div class="title-wrap">
                            <select class="select-list">
                                <option value="0" selected>Current Calls</option>
                                <option value="1">Waiting Calls</option>
                                <option value="5">Active Calls</option>
                                <option value="2">Completed Calls</option>
                                <option value="3">Drivers</option>
                                <option value="4">Scheduled Calls</option>
                            </select>
                        </div>
                        <div class="filter-wrap">
                            <input id="drivers-filter" value="" placeholder="Quick Filter" />
                            <div id="drivers-filter-stop"><i class="fa fa-close"></i></div>
                        </div>
                        <div class="options-wrap"><i class="fa fa-ellipsis-v"></i></div>
                    </div>
                </header>
                <ul id="drivers-list">
                    <li class="th">
                        <div class="driver-icons"></div>
                        <div class="driver">DRIVER<i class="fa fa-chevron-up"></i></div>
                        <div class="truck-icons"></div>
                        <div class="truck">TRUCK<i class="fa fa-chevron-up"></i></div>
                        <div class="dot-wrap">
                            <div class="dot">&nbsp;</div>
                        </div>
                    </li>
                </ul>
                <div id="drivers-none">No active drivers found</div>
            </div>
        </div>
        <div id="call-info-panel" class="panel info-panel">
        </div>
        <div id="driver-info-panel" class="panel info-panel">
        </div>
        <div id="generic-info-panel" class="panel info-panel">
        </div>
    </nav>
    <div id="canvas"></div>
    <div id="loading-bar">
        <div id="loading-progress"></div>
    </div>
</main>
    <script type="text/x-jquery-tmpl" id="tpl-driver-replay">
        <div class="section driver-replay">
            {{if (showTitle)}}
            <div class="title">Driver Replay</div>
            {{/if}}
            <div class="row">
                <div>
                    <input class="date-picker replay-from-date" value="${replayFromDate}" disabled />
                    <input class="time-picker replay-from-time" value="${replayFromTime}" disabled />
                </div>
                <div class="to">To</div>
                <div>
                    <input class="date-picker replay-to-date" value="${replayToDate}" disabled />
                    <input class="time-picker replay-to-time" value="${replayToTime}" disabled />
                </div>
                <input type="button" class="replay-btn standard-button" value="Start" />
            </div>
            <div class="replay-msg">
            </div>
            <div class="row">
                <div class="replay-controls">
                    <div class="row" style="padding: 5px;">
                        <input type="range" class="replay-slider" value="0" max="0" step="1" />
                        <div class="curr-info">
                            <div class="date">&nbsp;</div>
                            <div class="time">&nbsp;</div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="media-button start"><i class="fa fa-step-backward"></i></div>
                        <div class="media-button back"><i class="fa fa-backward"></i></div>
                        <div class="media-button play"><i class="fa fa-play"></i></div>
                        <div class="media-button forward"><i class="fa fa-forward"></i></div>
                        <div class="media-button end"><i class="fa fa-step-forward"></i></div>
                    </div>
                    <div class="row options speed">
                        <label for="replay-speed">Speed</label>
                        <div class="no-flex">
                            <select id="replay-speed">
                                <option value="250">1x</option>
                                <option value="125">2x</option>
                                <option value="60" selected>4x</option>
                                <option value="30">8x</option>
                            </select>
                        </div>
                        <div>&nbsp;</div>
                    </div>
                    <div class="row options">
                        <label for="replay-route-show">Route</label>
                        <div class="no-flex">
                            <input id="replay-route-show" type="checkbox" checked="checked" />
                        </div>
                        <div>&nbsp;</div>
                    </div>
                    <div class="row options">
                        <label for="replay-markers-show">Markers</label>
                        <div class="no-flex">
                            <input id="replay-markers-show" type="checkbox" checked="checked" />
                        </div>
                        <div>&nbsp;</div>
                    </div>
                </div>
                <div class="replay-spinner">
                    <div class="ring-wrapper">
                        <div class="ring"></div>
                    </div>
                </div>
            </div>
        </div>
    </script>

    <script type="text/x-jQuery-tmpl" id="tpl-call-row">
        <li class="call-row{{if (status.id == 252) }} completion-pending{{/if}}{{if (status.id == 253) }} cancelation-pending{{/if}}" data-id="${id}">
            <div class="call-number-wrap">
                <div title="Call #${callNumber}" style="${callStyle2}">Call #${callNumber}</div>
                <div class="line2" title="Invoice # ${invoiceNumber}">${invoiceNumber}</div>
                <div class="line2" title="Driver - ${driverName}">${driverInitials}</div>
            </div>
        </li>
    </script>

    <script type="text/x-jQuery-tmpl" id="tpl-driver-row">
        <li class="driver-row" data-id="${id}">
            <div class="driver-icons">
                {{if checkedIn}}<i class="fa fa-check checked-in" title="Checked in"></i> {{/if}}
            </div>
            <div class="driver truncate" title="${name}">
                ${name}
            </div>
            <div class="truck-icons">
                {{if (location && location.gpsSource == "Azuga" && location.ignitionState)}}
                <i class="fa fa-regular fa-power-off {{if location.ignitionState == 'on'}}ignition-on{{else}}ignition-off{{/if}}" title="${location.status}{{if (location.status == 'Moving' && location.speed >= 1)}} ${location.speed} MPH{{/if}}"></i>
                {{/if}}
            </div>
            <div class="truck truncate" title="${truckName} {{if (truckType)}}(${truckType}){{/if}}">
                <span class="truck-name">${truckName}</span>
                <span class="truck-type">${truckType}</span>
            </div>
            <div class="dot-wrap multi-dot">
                {{each statuses}}
                {{if (statusId < 5 || statusId == 7)}}
                <div class="dot ${getCallStatusText(statusId).replace(' ', '').toLowerCase()}"
                     onmouseover="doMouseOut2 = true; highlightCall(${callId})"
                     onmouseout="if (doMouseOut2) unHighlightCall(${callId})"
                     onclick="doMouseOut2 = false; showCall(${callId}); event.stopPropagation();"></div>
                {{/if}}
                {{/each}}
            </div>
        </li>
    </script>

<script type="text/x-jQuery-tmpl" id="tpl-call-info">
<div>
<header>
    <div>
        <div class="title-wrap">
            <div class="call-number" title="Call #${callNumber}">Call #${callNumber}</div>
            <div class="account" title="${accountName}">${accountName}</div>
            <div class="dot ${statusAbb}" title="${statusName}"></div>
        </div>
        <div class="options-wrap"><i class="fa fa-ellipsis-v"></i></div>
    </div>
</header>
<div class="info-content">
    <div class="info-bar-content">
        <div class="closed-call-wrapper" {{if isClosed}} style="display: flex;" {{else}} style="display: none;" {{/if}}>
            <div class="closed-icon">
                <i class="fa fa-minus-circle"></i>
            </div>
            <div class="closed-message">
                <div class="title">This call is closed.</div>
                <div>This call is inside of the closed accounting period. It can only be modified with an override.</div>
            </div>
        </div>

        <div class="section">
                        {{if (isService) }}
                        <div class="row">
                            <label>Location</label><div class="val">${towSource}</div>
                        </div>
                        {{else}}
                        <div class="row">
                            <label>Pickup</label><div class="val">${towSource}</div>
                        </div>
                        {{each stepWaypoints }}
                        <div class="row">
                            <label>${$value.title}</label><div class="val">${$value.address}</div>
                        </div>
                        {{/each}}
                        <div class="row">
                            <label>${destinationLabel}</label>
                            <div class="val">
                                {{if impound && !isTowOut }}
                                ${(impoundLot && impoundLot.name) || destination}
                                <div class="impound-tag">Impound</div>
                                {{else}}
                                ${destination}
                                {{/if}}
                                {{if hasTolls }}
                                <div class="has-tolls">
                                    <i class="fa fa-warning"></i> This route has tolls
                                </div>
                                {{/if}}
                            </div>
                        </div>
                        {{/if}}
                        {{each vehicles}}
                        <div class="row">
                            <label>Vehicle</label><div class="val">${$value}</div>
                        </div>
                        {{/each}}
                        {{if (notOnMap)}}
                        <div class="not-on-map">Unable to show on the map</div>
                        {{/if}}
                    </div>
                    {{if drivers.length > 0}}
                    {{each drivers}}
                    <div class="section">
                        <div class="row">
                            <label>Driver</label>
                            <div class="val">{{if driver && driver.id}}{{html getDriverLink(driver.id, driver.name) }}{{/if}}</div>
                        </div>
                        <div class="row">
                            <label>Truck</label>
                            <div class="val">{{if truck && truck.id}}${truck.name}{{/if}}</div>
                        </div>
                        {{if (showDriverReplay && driver && status.id > 1)}}
                        <div class="row">
                            <label>&nbsp;</label>
                            <input type="button" class="call-replay-btn" value="View Replay" data-driver-id="${driver.id}" />
                        </div>
                        {{/if}}
                    </div>
                    {{if (showDriverReplay && driver && status.id > 1)}}
                    <div class="call-replay replay-${driver.id}"></div>
                    {{/if}}
                    {{/each}}
                    {{/if}}
                    <div class="section">
                        <div class="title">Call Log</div>
                        <div class="row">
                            <label title="${towbook.formatDate(createDate)} ${towbook.formatAMPM(createDate)}">${getTimeFormat(createDate, 'h:mm A')}</label>
                            <div class="val">
                                Received
                                {{if (status.id > 0 && createDate && dispatchTime) }}
                                <div class="until">${getTimeDiffStr(createDate, dispatchTime)} until dispatched</div>
                                {{/if}}
                            </div>
                        </div>
                        {{if (status.id > 0 && dispatchTime) }}
                        <div class="row">
                            <label title="${towbook.formatDate(dispatchTime)} ${towbook.formatAMPM(dispatchTime)}">${getTimeFormat(dispatchTime, 'h:mm A')}</label>
                            <div class="val">
                                Dispatched
                            </div>
                        </div>
                        {{/if}}
                        {{if (acceptReject.status > 0 && acceptReject.time) }}
                            <div class="row">
                                <label title="${towbook.formatDate(acceptReject.time)} ${towbook.formatAMPM(acceptReject.time)}">${getTimeFormat(acceptReject.time, 'h:mm A')}</label>
                                <div class="val">Driver ${getAcceptRejectText(acceptReject.status)} 
                                    {{if (status.id > 1 && acceptReject.time && enrouteTime) }} 
                                        <div class="until">${getTimeDiffStr(acceptReject.time, enrouteTime)} to enroute</div> 
                                    {{/if}}
                                </div>
                            </div>
                        {{/if}}
                        {{if (status.id > 1 && enrouteTime) }}
                        <div class="row">
                            <label title="${towbook.formatDate(enrouteTime)} ${towbook.formatAMPM(enrouteTime)}">${getTimeFormat(enrouteTime, 'h:mm A')}</label>
                            <div class="val">
                                Enroute
                                {{if (status.id > 2 && enrouteTime && arrivalTime) }}
                                <div class="until">${getTimeDiffStr(enrouteTime, arrivalTime)} enroute</div>
                                {{/if}}
                            </div>
                        </div>
                        {{/if}}
                        {{if (status.id > 2 && arrivalTime) }}
                        <div class="row">
                            <label title="${towbook.formatDate(arrivalTime)} ${towbook.formatAMPM(arrivalTime)}">${getTimeFormat(arrivalTime, 'h:mm A')}</label>
                            <div class="val">
                                On Scene
                                {{if (status.id > 3 && arrivalTime && towTime) }}
                                <div class="until">${getTimeDiffStr(arrivalTime, towTime)} on scene</div>
                                {{/if}}
                            </div>
                        </div>
                        {{/if}}
                        {{if (status.id > 3 && towTime) }}
                        <div class="row">
                            <label title="${towbook.formatDate(towTime)} ${towbook.formatAMPM(towTime)}">${getTimeFormat(towTime, 'h:mm A')}</label>
                            <div class="val">
                                Towing
                                {{if (status.id > 4 && towTime && (destinationArrivalTime || completionTime)) }}
                                <div class="until">${getTimeDiffStr(towTime, (destinationArrivalTime || completionTime))} towing</div>
                                {{/if}}
                            </div>
                        </div>
                        {{/if}}
                        {{if (status.id > 4 && destinationArrivalTime) }}
                        <div class="row">
                            <label title="${towbook.formatDate(destinationArrivalTime)} ${towbook.formatAMPM(destinationArrivalTime)}">${getTimeFormat(destinationArrivalTime, 'h:mm A')}</label>
                            <div class="val">
                                Destination Arrival
                                {{if (status.id == 5 && destinationArrivalTime && completionTime) }}
                                <div class="until">${getTimeDiffStr(destinationArrivalTime, completionTime)} at destination</div>
                                {{/if}}
                            </div>
                        </div>
                        {{/if}}
                        {{if (status.id == 5 && completionTime) }}
                        <div class="row">
                            <label title="${towbook.formatDate(completionTime)} ${towbook.formatAMPM(completionTime)}">${getTimeFormat(completionTime, 'h:mm A')}</label>
                            <div class="val">
                                Completed
                                {{if (enrouteTime && completionTime) }}
                                <div class="until">${getTimeDiffStr(enrouteTime, completionTime)} from enroute to complete</div>
                                {{else (dispatchTime && completionTime) }}
                                <div class="until">${getTimeDiffStr(dispatchTime, completionTime)} from dispatch to complete</div>
                                {{/if}}
                            </div>
                        </div>
                        {{/if}}
                    </div>
                </div>
                <footer>
                </footer>
            </div>
    </script>

    <script type="text/x-jQuery-tmpl" id="tpl-driver-info">
        <div>
            <header>
                <div>
                    <div class="title-wrap">
                        <div class="driver-name" title="${name}">${name}</div>
                        <div class="dot ${statusAbb}" title="${statusName}"></div>
                    </div>
                    <div class="options-wrap"><i class="fa fa-ellipsis-v"></i></div>
                </div>
            </header>
            <div class="info-content">
                <div class="section">
                    <div class="row">
                        <label>Truck</label>
                        <div class="val">
                            ${truckName}
                            <span class="truck-type">${truckType}</span>
                        </div>
                    </div>
                    {{if (location && location.gpsSource == "Azuga" && location.speed && location.status == "Moving")}}
                    <div class="row">
                        <label>Speed</label>
                        <div class="val">${Math.round(location.speed)} MPH</div>
                    </div>
                    {{/if}}
                    {{if (location && location.gpsSource == "Azuga" && location.status)}}
                    <div class="row">
                        <label>Status</label>
                        <div class="val">${location.status}</div>
                    </div>
                    {{/if}}
                    {{if phone}}
                    <div class="row">
                        <label>Phone</label>
                        <div class="val">${phone}</div>
                    </div>
                    {{/if}}
                    {{if (timestamp)}}
                    <div class="row">
                        <label>Location Updated</label>
                        <div class="val" title="${towbook.formatDate(timestamp)} ${getTimeFormat(timestamp, 'h:mm A')}">
                            ${ getTimeLabel(timestamp) }
                            {{if (gpsSourceLabel)}} &nbsp;&nbsp;&nbsp; <i class="fa fa-${gpsSourceIcon}"></i> ${gpsSourceLabel} {{/if}}
                        </div>
                    </div>
                    {{/if}}
                    {{if notOnMap}}
                    <div class="not-on-map">Unable to show on the map. Waiting for location.</div>
                    {{/if}}
                </div>
                <div class="section">
                    <div class="title">Active Calls</div>
                    {{if (false && statuses && statuses.length > 0) }}
                    {{each statuses}}
                    <div class="row">
                        <label title="${towbook.formatDate(statusTime)}">${getTimeFormat(statusTime, 'h:mm A')}</label>
                        <div class="val">${ getDriverStatusText(statusId) } {{html getCallLink(callId, callNumber) }}</div>
                    </div>
                    {{/each}}
                    {{/if}}
                </div>
            </div>
        </div>
    </script>

    <script type="text/x-jQuery-tmpl" id="tpl-generic-info">
        <div>
            <header>
                <div>
                    <div class="title-wrap">
                        <div class="name" title="${name}">${name}</div>
                    </div>
                    <div class="options-wrap">&nbsp;</div>
                </div>
            </header>
            <div class="info-content">
                <div class="section">
                    {{each props }}
                    <div class="row">
                        <label>${key}</label><div class="val">${val}</div>
                    </div>
                    {{/each}}
                </div>
                <div>
                    {{each childProps }}
                    <div class="section">
                        <div class="title">${name}</div>
                        {{each props }}
                        <div class="row">
                            <label>${key}</label><div class="val">${val}</div>
                        </div>
                        {{/each}}
                    </div>
                    {{/each}}
                </div>
            </div>
        </div>
    </script>
    
    <script type="text/x-jQuery-tmpl" id="tpl-call-eta">
        <div class="call-eta {{if (arrived)}}arrived{{/if}} {{if (late)}}late{{/if}}" title="${title}">${text}</div>
    </script>

    <script type="text/x-jQuery-tmpl" id="tpl-call-calculated-eta">
        <div class="call-calculated-eta" title="${title}">{{if showPredictedArrival}}<i class="fa fa-clock" style="padding-right: 2px;"></i>{{/if}}${text}</div>
    </script>

    <script type="text/javascript">
    (function () {
        // Stub any undefined console methods
        var console = (window.console = window.console || {});
        var methods = [
            'assert', 'clear', 'count', 'debug', 'dir', 'dirxml', 'error',
            'exception', 'group', 'groupCollapsed', 'groupEnd', 'info', 'log',
            'markTimeline', 'profile', 'profileEnd', 'table', 'time', 'timeEnd',
            'timeStamp', 'trace', 'warn'
        ];
        methods.forEach(function (method) {
            if (!console[method]) {
                console[method] = function () { };
            }
        });

        // If Internet Explorer or Edge -- can't use colored logging
        if (navigator.userAgent.indexOf('MSIE') !== -1 ||
            navigator.userAgent.indexOf('Edge') !== -1 ||
            navigator.appVersion.indexOf('Trident/') > 0) {
            window.eventsDebug = function () { console.log.apply(console, arguments) };
            window.pusherDebug = function () { console.log.apply(console, arguments) };
            window.panelDebug = function () { console.log.apply(console, arguments) };
            window.info = function () { console.log.apply(console, arguments) };
            window.mapDebug = function () { console.log.apply(console, arguments) };
            window.warning = function () { console.log.apply(console, arguments) };
        }

        // If the browser supports bind(), use that
        else if (Function.prototype.bind) {
            window.eventsDebug = console.log.bind(console, '%cEvent   ', 'color:lightseagreen');
            window.pusherDebug = console.log.bind(console, '%cPusher  ', 'color:forestgreen');
            window.panelDebug = console.log.bind(console, '%cPanel   ', 'color:goldenrod');
            window.info = console.log.bind(console, '%cInfo    ', 'color:dodgerblue');
            window.mapDebug = console.log.bind(console, '%cTbMap   ', 'color:darkorchid');
            window.warning = console.log.bind(console, '%cWarning ', 'color:crimson');
        }

        // Else we provide an apply() fallback
        else {
            function us(a, b) { Array.prototype.unshift.call(a, b); return a; };
            window.eventsDebug = function () { console.log.apply(console, us(us(arguments, 'color:lightseagreen'), '%cEvent   '), arguments) };
            window.pusherDebug = function () { console.log.apply(console, us(us(arguments, 'color:forestgreen'), '%Pusher   '), arguments) };
            window.panelDebug = function () { console.log.apply(console, us(us(arguments, 'color:goldenrod'), '%cPanel   '), arguments) };
            window.info = function () { console.log.apply(console, us(us(arguments, 'color:dodgerblue'), '%cInfo    '), arguments) };
            window.mapDebug = function () { console.log.apply(console, us(us(arguments, 'color:darkorchid'), '%TbMap    '), arguments) };
            window.warning = function () { console.log.apply(console, us(us(arguments, 'color:crimson'), '%cWarning '), arguments) };
        }
    })();
        
    function removeEncodedAmpersand(str) {
        return str.replace(/&amp;/g, '&');
    }

    function _enc(str) {
        if (!str) return "";
        return removeEncodedAmpersand($("<a>").text(str).html());
    }

    var call = JSON.parse('@Html.Raw(HttpUtility.JavaScriptStringEncode(ViewBag.CallJson))');
    var company = JSON.parse('@Html.Raw(HttpUtility.JavaScriptStringEncode(ViewBag.CompanyJson))');
    var locHist = JSON.parse('@Html.Raw(HttpUtility.JavaScriptStringEncode(ViewBag.LocationHistory))');
    var driver = JSON.parse('@Html.Raw(HttpUtility.JavaScriptStringEncode(ViewBag.DriverJson))');
    driver.initials = getShortName(driver.name, 3);
    var drivers = [driver];
    driver.locationHistory = locHist;
    call.locationHistory = locHist;
    call.driver = driver;

    var companyId = call.companyId;
    var towbook = {
        get: function (object, id, field) {
            if (object == null || object.length == 0) return null;
            return $.grep(object, function (e) {
                return e[field || 'id'] == id
            })[0] || null;
        },
        callStatusNames: {
          0: "Waiting",
          1: "Dispatched",
          2: "Enroute",
          3: "On Scene",
          4: "Towing",
          7: "Destination Arrival",
          5: "Completed",
          252: "Pending Completion", /*Pending acknowledgment*/
          253: "Pending Cancellation", /*By MotorClub*/
          245: "Scheduled",
          255: "Cancelled",
        },
        callStatusIds: {
          "Waiting": 0,
          "Dispatched": 1,
          "Enroute": 2,
          "On Scene": 3,
          "Towing": 4,
          "Destination Arrival":7,
          "Completed": 5,
          "Pending Completion": 252,
          "Pending Cancellation": 253,
          "Scheduled": 245,
          "Cancelled": 255,
        },
        /****************************************************
            isEmpty(obj)
            Checks if an object is empty and returns true or false
        ****************************************************/
        isEmpty: function (obj) {
            // null and undefined are "empty"
            if (obj == null) return true;
            // Assume if it has a length property with a non-zero value
            // that that property is correct.
            if (obj.length > 0) return false;
            if (obj.length === 0) return true;
            // Speed up calls to hasOwnProperty
            var hasOwnProperty = Object.prototype.hasOwnProperty;
            // Otherwise, does it have any properties of its own?
            // Note that this doesn't handle
            // toString and valueOf enumeration bugs in IE < 9
            for (var key in obj) {
            if (hasOwnProperty.call(obj, key)) return false;
            }
            return true;
        },
        parseDateString: function (dateStringInRange) {
            if (dateStringInRange == "nullZ" || dateStringInRange == null || dateStringInRange == "undefinedZ")
              return null;
            var isoExp = /^\s*(\d{4})-(\d\d)-(\d\d)T(\d+):(\d+):(\d\d).*Z\s*$/,
          date = new Date(NaN),
          month,
                parts = isoExp.exec(dateStringInRange);
            if (parts) {
              month = +parts[2];
              date.setFullYear(parts[1], +parts[2] - 1, parts[3]);
              date.setHours(parts[4]);
              date.setMinutes(parts[5]);
              date.setSeconds(parts[6]);
              if (month != date.getMonth() + 1) {
                date.setTime(NaN);
              }
            }
            return date;
          },
        getDateFormat: function () {
            const formats = {
              "en-029": "m/d/yy",
              "en-AU": "d/m/yy",
              "en-BZ": "d/m/yy",
              "en-CA": "m/d/yy",
              "en-GB": "d/m/yy",
              "en-IE": "d/m/yy",
              "en-IN": "d/m/yy",
              "en-JM": "d/m/yy",
              "en-MY": "d/m/yy",
              "en-NZ": "d/m/yy",
              "en-PH": "m/d/yy",
              "en-SG": "d/m/yy",
              "en-TT": "d/m/yy",
              "en-US": "m/d/yy",
              "en-ZW": "m/d/yy",
            };
            return formats[navigator.language] || "m/d/yy";
        },
        formatDate: function (date, format, nullValue, emptyIfToday) {
            if (date == "" || date == null)
              return nullValue == null ? "" : nullValue;
            format =  format || towbook.getDateFormat();
            if (Object.prototype.toString.call(date) !== '[object Date]') {
              if (date.substring(date.length - 1) == 'Z') {
                date = this.parseISO8601(date);
              } else {
                date = this.parseDateString(date + 'Z');
              }
            }
            if (emptyIfToday == true) {
              var theDate = date.Date != null ? date : new Date(date);
              if (theDate.Date == new Date().Date)
                return "";
            }
            return $.datepicker.formatDate(format, (date.Date != null ? date : new Date(date)));
        },
        formatAMPM: function formatAMPM(date, nullValue) {
            if (date == "" || date == null)
                return nullValue == null ? "" : nullValue;

            if (Object.prototype.toString.call(date) !== '[object Date]') {
                if (date.substring(date.length - 1) == 'Z') {
                    date = towbook.parseISO8601(date);
                } else {
                    date = towbook.parseDateString(date + 'Z');
                }
            }
            
            date = new Date(date);
            var hours = date.getHours();
            var minutes = date.getMinutes();
            var ampm = hours >= 12 ? 'pm' : 'am';
            hours = hours % 12;
            hours = hours ? hours : 12; // the hour '0' should be '12'
            minutes = minutes < 10 ? '0' + minutes : minutes;
            var strTime = hours + ':' + minutes + ' ' + ampm;
            return strTime.toUpperCase();
        },
        parseISO8601: function (dateStringInRange) {
            return new Date(dateStringInRange); 
        },
        parseDateString: function (dateStringInRange) {
            if (dateStringInRange == "nullZ" || dateStringInRange == null || dateStringInRange == "undefinedZ")
                return null;

            var isoExp = /^\s*(\d{4})-(\d\d)-(\d\d)T(\d+):(\d+):(\d\d).*Z\s*$/,
                date = new Date(NaN),
                month,
                parts = isoExp.exec(dateStringInRange);
            if (parts) {
                month = +parts[2];
                date.setFullYear(parts[1], +parts[2] - 1, parts[3]);
                date.setHours(parts[4]);
                date.setMinutes(parts[5]);
                date.setSeconds(parts[6]);
                if (month != date.getMonth() + 1) {
                    date.setTime(NaN);
                }
            }
            return date;
        },
    };

    towbook.calls = [ call ];
    towbook.companies =  [ call.company ];
    var vm = {
        companies: [company],
        lots: [],
        calls: [call],
        drivers: [driver],
    };

    var tbmap = null;
    var panel = null;
    var companyFilter = 0;
    var currList = JSON.parse(localStorage.getItem('map2-curr-list') || '0');
    var callSort = null;
    var callSortDrx = null;
    var driverSort = null;
    var driverSortDrx = null;
    var displayCall = null;
    var showDriverReplay = true;
    var showingCallNumbers = JSON.parse(localStorage.getItem('map2-showing-call-numbers') || 'false') == true;
    var showingTruckLocations = JSON.parse(localStorage.getItem('map2-showing-truck-locations') || 'false') == true;
    var hidingCalls = JSON.parse(localStorage.getItem('map2-hiding-calls') || 'false') == true;
    var doMouseOut = false;
    var loadAjaxCurrentUrl = null;
    var replayOn = false;
    var replayAnimating = false;
    var replayPlaying = false;
    var replayInterval = null;
    var replayIndex = 0;
    var replayMax = 0;
    var replayValues = [];
    var replayPolyLine = null;
    var replayMarker = null;
    var replaySpeed = 60;
    var replayMarkers = [];
    var replayMarkersShow = true;
    var replayRouteShow = true;
    var callReplaying = null;
    var callReplayMarkers = [];
    var callReplayButtonRow = null;
    var dispatchingOn = false;
    var dispatchingMarkers = [];
    var dispatchingMarkersMove = false;
    var geocoder = new google.maps.Geocoder();
    var callsPanel = $('#calls-panel');
    var callInfoPanel = $('#call-info-panel');
    var driversPanel = $('#drivers-panel');
    var driverInfoPanel = $('#driver-info-panel');
    var genericInfoPanel = $('#generic-info-panel');

    //////////////////////////////////////////////////////////
    // Initialize everything
    //////////////////////////////////////////////////////////
    function initialize(company, call) {
        tbmap = new TbMap({
            targetEl: 'canvas',
            defaultLocation: { latitude: company.latitude, longitude: company.longitude },
            zoom: 11,
            minZoom: -1
        });

        panel = new Panel();

        //////////////////////////////////////////////////////////
        // Create a Traffic button on the top-right of the map
        //////////////////////////////////////////////////////////

        // Create html for it
        var controlDiv = document.createElement('DIV');
        $(controlDiv).addClass('gmap-control-container')
            .addClass('gmnoprint');

        var controlUI = document.createElement('DIV');
        $(controlUI).addClass('gmap-control');
        $(controlUI).text('Traffic');
        $(controlDiv).append(controlUI);
        
        // Wire-up click event
        var trafficLayer = new google.maps.TrafficLayer();
        google.maps.event.addDomListener(controlUI, 'click', function () {
            if (typeof trafficLayer.getMap() == 'undefined' || trafficLayer.getMap() === null) {
                $(controlUI).addClass('gmap-control-active');
                trafficLayer.setMap(tbmap.map);
            } else {
                trafficLayer.setMap(null);
                $(controlUI).removeClass('gmap-control-active');
            }
        });

        // Add it to the map
        tbmap.map.controls[google.maps.ControlPosition.TOP_RIGHT].push(controlDiv);

        // Listener for map zoom changed
        google.maps.event.addListener(tbmap.map, 'zoom_changed', function () {
            console.log('ZOOM CHANGED: ', tbmap.map.getZoom())
        });

        // Get the driver statuses
        updateDriverStatuses();


        // If we're displaying this call on load, show it
        if (displayCall) {
            showCall(displayCall.id);
        }
    }

    console.log("call", call);
    console.log("driverloc", locHist)
    initialize(company, call);

    google.maps.event.addDomListener(window, 'load', function () {
        console.log("google maps loaded")

        // Set company address to start of call if company address is not set
        var startLocation = { latitude: locHist[0].latitude, longitude: locHist[0].longitude }
        if (!company.latitude || !company.longitude) {
            company.latitude = startLocation.latitude;
            company.longitude = startLocation.longitude;
        } 
        initialize(company, call);
        panel.addCall(call);
        panel.addDriver(driver);
        panel.showCallInfo(call);
        setTimeout(() => $('.call-replay-btn').click(), 500)
    });
    </script>
</html>
