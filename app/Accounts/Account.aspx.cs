using Agero;
using Extric.Towbook;
using Extric.Towbook.Accounts;
using Extric.Towbook.Company;
using Extric.Towbook.Impounds;
using Extric.Towbook.Integration;
using Extric.Towbook.Utility;
using Extric.Towbook.WebShared;
using NLog;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data;
using System.Linq;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Threading.Tasks;

using App.Accounts;
using Extric.Towbook.API.Models.Calls;

public partial class Accounts_Account : System.Web.UI.Page
{
    public Account _account;
    protected int _accountId;
    protected int _companyId;
    protected bool SharedAccount { get; set; }
    public bool _showDispatchNumber = false;
    public bool _showMembershipNumber = false;
    public bool _showPO = false;
    public bool _showCompany = false;
    public bool RetailAccount { get; set; }
    public bool EnableMotorClubBilling { get; set; }
    public string _invoiceStatusesJson = "";
    public string _invoiceStatusWorkflowJson = "";
    public string _invoiceStatusCountsJson = "";
    public string _callsJson = "";
    public string _userJson = "";
    public string _usersJson = "";
    public string _companiesJson = "";
    //public string _attributesJson = new AttributesController().Get().ToJson();
    public string _attributesJson = ApiAccess.GetAttributes();
    public string _currentCompanyJson = "";
    public string _mapFromAddressBook = "";
    public string _statementsJson = "";
    public string _paymentsJson = "";
    public string _paymentTypesJson = "";
    public string _filesJson = "";
    public string _permitsJson = "";
    public string _permitRequestsJson = "";
    public string _permitApprovalsJson = "";
    public string _permitTypesJson = "";
    public string _permitDecalColorsJson = "";
    public string _permitSettingsJson = "";
    public string _permitUserTypesJson = "";
    public List<Impound> _impounds = new List<Impound>();
    public string _impoundsJson = "";
    public string _successRatio = "";
    public bool _hideCharges = false;
    public bool _hideDiscounts = false;
    public string _stickersJson = "";
    public string _stickerStatusesJson = "";
    public string _stickerReasonsJson = "";
    public string _replyToEmailAddress = "";
    public string _currentAccountEmail = "";
    public DateTime _initialStatementDueDate;
    public string _initialInvoiceEmailSubject = "";
    public string _initialInvoiceEmailMessage = "";

    public bool _canAuditCalls = false;
    public bool _canLockCalls = false;
    private static readonly Logger logger = LogManager.GetCurrentClassLogger();

    public bool ParkingPermitsEnabled
    {
        get
        {
            var k = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId,
                "ParkingPermitsEnabled").FirstOrDefault();
            if (k != null && k.Value == "1")
                return true;
            else
                return false;

        }
    }

    public bool StickersEnabled
    {
        get
        {
            var k = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId,
                "StickeringEnabled").FirstOrDefault();
            if (k != null && k.Value == "1")
                return true;
            else
                return false;

        }
    }

    public class QaStatus
    {
        public int StatusId { get; set; }
        public int Total { get; set; }
    }

    public string GetSubmissionStatus(int statementId)
    {
        if (!EnableMotorClubBilling)
            return "";

        var results = SqlMapper.QuerySP<QaStatus>("MCBilling.QualityAssuranceGetSubmissionStatus", new
        {
            @StatementId = statementId
        }).ToList();

        var success = results.Where(o => o.StatusId == 5).Sum(o => o.Total);
        var failed = results.Where(o => o.StatusId == 6).Sum(o => o.Total);
        var notDone = results.Where(o => o.StatusId != 5 && o.StatusId != 6).Sum(o => o.Total);

        if (success > 0 && failed == 0 && notDone == 0)
        {
            return "Submitted " + success + " invoices";
        }
        else
        {
            if (success == 0 && failed > 0)
            {
                return "Failed to submit " + failed + " invoices";
            }
            else
            {
                if (success == 0 && failed == 0 && notDone > 0)
                {
                    return "Processing " + notDone + " invoices (not yet submitted)";
                }
                else
                {
                    return "Submitted " + success + " invoices successfully, " + failed + " invoices failed";
                }
            }
        }
    }

    private void LogRedirectError(string msg) 
    {
        var logEvent = new LogEventInfo();
        logEvent.LoggerName = logger.Name;
        logEvent.Message = msg;
        logEvent.Level = LogLevel.Info;
        logEvent.TimeStamp = DateTime.Now;

        logEvent.Properties["companyId"] = WebGlobal.CurrentUser.CompanyId;
        logEvent.Properties["userId"] = WebGlobal.CurrentUser.Id;
        if(_account != null)
        {
            logEvent.Properties["accountId"] = _account.Id ;
            logEvent.Properties["json"] = (new {
                AccountCompanies = _account.Companies.ToJson(),
            }).ToJson();
        }
        logger.Log(logEvent);
    }

    protected void Page_Load(object sender, EventArgs e)
    {
        this.Master.CurrentSection = Navigation.NavigationItemEnum.Accounts;
        this.Master.InnerTitle = "Account Details";
        this.Master.UseJquery = true;

        RegisterAsyncTask(new PageAsyncTask(PageLoadAsync));
    }

    protected async Task PageLoadAsync()
    {
        var id = Request.QueryString["id"] ?? Page.RouteData.Values["id"];

        if (id != null)
        {
            _accountId = Convert.ToInt32(id);
            _companyId = Global.CurrentUser.Company.Id;

            if (_accountId < 1)
            {
                LogRedirectError("_accountId < 1");
                Response.Redirect("/Accounts/");
            }

            if (Global.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Driver)
            {
                var hideAccounts = Extric.Towbook.Integration.CompanyKeyValue.GetByCompanyId(WebGlobal.CurrentUser.Company.Id, Extric.Towbook.Integration.Provider.Towbook.ProviderId, "HideAccountDetailsFromDrivers").FirstOrDefault();
                if (Global.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Driver)
                {
                    if (hideAccounts != null && (hideAccounts.Value != "0"))
                    {
                        Response.Redirect("/");
                    }
                }

                _hideCharges = true;
            }

            if (_accountId == 1)
                RetailAccount = true;

            _account = (await Account.GetByIdAsync(_accountId));

            if (_account == null || !WebGlobal.CurrentUser.HasAccessToCompany(_account.Companies) && !RetailAccount)
            {
                LogRedirectError("Accounts page redirecting: _account == null or User does not have access to this company");
                Response.Redirect("/Accounts/");
            }

            if(!(await Company.GetByIdAsync(_account.CompanyId)).HasFeature(Extric.Towbook.Generated.Features.AccountTags))
            {
                _account.Tags = new Collection<AccountTag>();
            }

            var _masterAccount = MasterAccount.GetById(_account.MasterAccountId);
            if (_masterAccount != null)
            {
                var pn = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "ProviderId").FirstOrDefault();
                var bu = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "McUsername").FirstOrDefault();
                var bp = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "McPassword").FirstOrDefault();

                if (bu != null && bp != null)
                {
                    if (_account.MasterAccountId == 1)
                        EnableMotorClubBilling = Global.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.DirectBilling_RoadAmerica) &&
                            (!String.IsNullOrWhiteSpace(bu.Value) &&
                             !String.IsNullOrWhiteSpace(bp.Value));

                    if (_account.MasterAccountId == 2 && bp != null && bu != null && pn != null)
                        EnableMotorClubBilling = Global.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.DirectBilling_Allstate) &&
                            (!String.IsNullOrWhiteSpace(bu.Value) &&
                             !String.IsNullOrWhiteSpace(bp.Value) &&
                             !String.IsNullOrWhiteSpace(pn.Value));

                    if (_account.MasterAccountId == 3)
                    {
                        EnableMotorClubBilling = Global.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.DirectBilling_Agero) &&
                            (!String.IsNullOrWhiteSpace(bu.Value) &&
                             !String.IsNullOrWhiteSpace(bp.Value));

                        if (Extric.Towbook.Core.GetRedisValue("DisableAgeroDirectBilling") == "1")
                            EnableMotorClubBilling = false;
                    }

                    if (_account.MasterAccountId == 4 && bp != null && bu != null && pn != null)
                        EnableMotorClubBilling = Global.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.DirectBilling_Quest) &&
                            (!String.IsNullOrWhiteSpace(bu.Value) &&
                             !String.IsNullOrWhiteSpace(bp.Value) &&
                             !String.IsNullOrWhiteSpace(pn.Value));

                    if (_account.MasterAccountId == 5 && bp != null && bu != null)
                        EnableMotorClubBilling = Global.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.DirectBilling_GEICO) &&
                            (!String.IsNullOrWhiteSpace(bu.Value) &&
                             !String.IsNullOrWhiteSpace(bp.Value));

                    if (_account.MasterAccountId == 7 && bp != null && bu != null)
                        EnableMotorClubBilling = Global.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.DirectBilling_NSD) &&
                            (!String.IsNullOrWhiteSpace(bu.Value) &&
                             !String.IsNullOrWhiteSpace(bp.Value));
                }

                if (_masterAccount.Id == MasterAccountTypes.Fleetnet)
                {
                    var fp = Extric.Towbook.Integrations.MotorClubs.Fleetnet.FleetnetProvider.GetByAccountId(_account.Id).FirstOrDefault();
                    if (fp != null)
                    {
                        EnableMotorClubBilling = true;
                    }
                }
            }
            else
            {
                if (_account.Company.ToLower().Contains("allstate") && Request.QueryString["asr"] == null)
                {
                    // check if we can enable mcdispatch. 

                    var results = SqlMapper.QuerySP<dynamic>("MCDispatch.AllstateTryEnable", new
                    {
                        @AccountId = _account.Id
                    }).FirstOrDefault();

                    if (results != null)
                    {
                        Response.Redirect("/Accounts/Account.aspx?id=" + _account.Id + "&asr=1");
                    }
                }
            }


            if (Global.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Manager &&
                            Request.QueryString["forceDisconnectAgero"] == "1")
            {
                var ags = AgeroSession.GetByAccountId(_accountId);
                if (ags != null)
                {

                    ags.Delete();
                    Response.Write("Deleted Agero Connection for Vendor ID " + ags.VendorId);
                    Response.End();

                }
                else
                {
                    Response.Write("No Agero Connection exists to delete for this account.");
                    Response.End();
                }
            }

            if (Global.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Manager &&
                Request.QueryString["forceDisconnectSwoop"] == "1")
            {
                var ss = Extric.Towbook.Integrations.MotorClubs.Swoop.SwoopSession.GetByAccountId(_accountId);
                if (ss != null)
                {
                    var src = new Extric.Towbook.Integrations.MotorClubs.Swoop.SwoopRestClient();
                    src.OAuthRevokeAccessToken(ss.AccessToken);

                    ss.Delete();
                    Response.Write("Deleted Swoop Connection for " + _account.Company);
                    Response.End();
                }
                else
                {
                    Response.Write("No Swoop exists to delete for this account.");
                    Response.End();
                }
            }

            _canLockCalls = WebGlobal.CurrentUser.HasAccessToAuditCalls();
            _canAuditCalls = WebGlobal.CurrentUser.HasAccessToLockCalls();

            //_invoiceStatusesJson = JsonExtensions.ToJson(new InvoiceStatusesController().Get());
            _invoiceStatusesJson = ApiAccess.GetInvoiceStatuses();
            //_invoiceStatusWorkflowJson = JsonExtensions.ToJson(new InvoiceStatusWorkflowsController().ByAccount(_accountId));
            _invoiceStatusWorkflowJson = ApiAccess.GetInvoiceStatusWorkflows(_accountId);
            //_invoiceStatusCountsJson = JsonExtensions.ToJson(new CallsController().GetInvoiceStatusCounts(_accountId));
            _invoiceStatusCountsJson = ApiAccess.GetInvoiceStatusCounts(_accountId);
            //_callsJson = WebGlobal.GetResponseFromUrl(
            //    string.Format("/api/calls/byAccounts?accountIds={0}&invoiceStatusId=1&pageNumber=1", _accountId));
            _callsJson = ApiAccess.GetCallsByAccounts(new ByAccountsRequest()
            {
                AccountIds = new int[] { _accountId },
                InvoiceStatusId = 1,
                PageNumber = 1,
            });
            //_userJson = JsonExtensions.ToJson((new UserController().Get()));
            _userJson = ApiAccess.GetUsers();
            _usersJson = WebGlobal.GetResponseFromUrl(
                        string.Format("/api/users?accountId={0}", _accountId));
            _mapFromAddressBook = JsonExtensions.ToJson(AddressBookEntry.GetByName("MapFrom", WebGlobal.CurrentUser.Company.Id, true));
            _companiesJson = JsonExtensions.ToJson((
                WebGlobal.CurrentUser.Type == Extric.Towbook.User.TypeEnum.SystemAdministrator ? null :
                WebGlobal.GetCompanies().Select(o => Extric.Towbook.API.Models.CompanyConfigModel.Map(o))
                .OrderBy(o => o.Name)));

            _successRatio = WebGlobal.GetResponseFromUrl(
                string.Format("/api/directbilling/submissions/getSubmissionSuccessRatio?accountId={0}&companyId={1}",
                    _accountId,
                    _companyId));

            bool hidePayments = _hideCharges;
            if (Global.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Driver ||
                           Global.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Dispatcher)
            {
                var preventDriversFromViewingCharges = Extric.Towbook.Integration.CompanyKeyValue.GetByCompanyId(WebGlobal.CurrentUser.Company.Id, Extric.Towbook.Integration.Provider.Towbook.ProviderId, "PreventDriversFromViewingInvoiceItems").FirstOrDefault();
                var preventDispatchersFromViewingCharges = Extric.Towbook.Integration.CompanyKeyValue.GetByCompanyId(WebGlobal.CurrentUser.Company.Id, Extric.Towbook.Integration.Provider.Towbook.ProviderId, "PreventDispatchersFromViewingInvoiceItems").FirstOrDefault();

                if (Global.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Driver)
                {
                    if (preventDriversFromViewingCharges != null && (preventDriversFromViewingCharges.Value == "3" ||
                        preventDriversFromViewingCharges.Value == "1"))
                        hidePayments = true;
                }

                if (Global.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Dispatcher)
                {
                    if (preventDispatchersFromViewingCharges != null && (preventDispatchersFromViewingCharges.Value == "3" ||
                        preventDispatchersFromViewingCharges.Value == "1"))
                        hidePayments = true;
                }
            }

            var defaultValue = (_account.Type == AccountType.MotorClub ? "1" : "0");

            // only apply these settings if they weren't overridden in the query string. 
            if (_account.Type == AccountType.MotorClub)
            {
                if ((CompanyKeyValue.GetFirstValueOrNull(WebGlobal.CurrentUser.CompanyId,
                    Provider.Towbook.ProviderId, "HideChargesFromMotorClubInvoicesByDefault") ?? defaultValue) == "1")
                    _hideCharges = true;
            }

            var hideCharges = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "AlwaysHideCharges").FirstOrDefault();
            if (hideCharges != null)
                _hideCharges = (hideCharges.Value == "1");

            var hideDiscounts = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "AlwaysHideDiscounts").FirstOrDefault();
            if (hideDiscounts != null)
                _hideDiscounts = (hideDiscounts.Value == "1");

            if (!RetailAccount)
            {
                _statementsJson = hidePayments ? "[]" :
                    WebGlobal.GetResponseFromUrl(
                        string.Format("/api/statements/?accountId={0}", _accountId));

                var payments = WebGlobal.GetResponseFromUrl(string.Format("/api/accounts/{0}/payments", _accountId));

                _paymentsJson = hidePayments || payments.Contains("Your user doesn't have permission to execute this method") ? "[]" : payments;
                _paymentTypesJson = JsonExtensions.ToJson(
                    Extric.Towbook.Dispatch.PaymentType.GetByCompanyId(Global.CurrentUser.CompanyId).Select(o => new
                    {
                        Id = (int)o,
                        Name = o.ToString()
                    }));
                //_filesJson = JsonExtensions.ToJson(new FilesController().Get(accountId: _accountId));
                _filesJson = ApiAccess.GetFiles(_accountId);

                _permitsJson = "[]";
                _permitRequestsJson = "[]";
                _permitApprovalsJson = "[]";
                _permitTypesJson = "[]";
                _permitUserTypesJson = "[]";
                _permitDecalColorsJson = "[]";
                _permitSettingsJson = "[]";
                if (WebGlobal.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.ParkingPermits) &&
                    _accountId > 1 &&
                    _account != null &&
                    _account.Type == AccountType.PrivateProperty)
                {
                    // get permits by statusIds of Valid (0) and Extended (3)
                    _permitsJson = WebGlobal.GetResponseFromUrl(
                        string.Format("/api/accounts/{0}/parkingPermits?statusIds%5B%5D=0&statusIds%5B%5D=3&page=1&pageSize=2000",
                        _accountId));

                    _permitRequestsJson = WebGlobal.GetResponseFromUrl(
                        string.Format("/api/accounts/{0}/parkingPermitRequests?page=1",
                        _accountId));

                    _permitApprovalsJson = WebGlobal.GetResponseFromUrl(
                        string.Format("/api/accounts/{0}/parkingPermitApprovalSessions?sessionStatusId=6",
                        _accountId));

                    _permitTypesJson = WebGlobal.GetResponseFromUrl(
                        string.Format("/api/parkingPermitLists"));

                    _permitUserTypesJson = WebGlobal.GetResponseFromUrl(
                        string.Format("/api/permits/userTypes"));
                    if (_permitUserTypesJson == "") _permitUserTypesJson = "[]";

                    _permitDecalColorsJson = WebGlobal.GetResponseFromUrl(
                        string.Format("/api/parkingPermitDecalColors"));


                    _permitSettingsJson = WebGlobal.GetResponseFromUrl(
                        string.Format("/api/parkingPermits/settings?accountId={0}", _accountId));
                }

                _stickersJson = "[]";
                _stickerStatusesJson = "[]";
                _stickerReasonsJson = "[]";
                if (StickersEnabled &&
                    _accountId > 1 &&
                    _account != null &&
                    _account.Type == AccountType.PrivateProperty)
                {
                    _stickerStatusesJson = WebGlobal.GetResponseFromUrl(
                        string.Format("/api/stickering/statuses"));

                    _stickersJson = WebGlobal.GetResponseFromUrl(
                        string.Format("/api/stickering/stickers?accountId={0}",
                        _accountId));

                    _stickerReasonsJson = WebGlobal.GetResponseFromUrl(
                        string.Format("/api/stickering/reasons"));
                }


                if (Global.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Manager || Global.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Accountant)
                {
                    _impounds = await Impound.GetByCompanyAsync(new int[] { Global.CurrentUser.Company.Id }, null, true, false, new int[] { _account.Id });
                    _impoundsJson = JsonExtensions.ToJson(
                        _impounds.Select(o => new
                        {
                            Id = o.Id,
                            CallNumber = (o.DispatchEntry != null ? o.DispatchEntry.CallNumber : 0),
                            Vehicle = (o.DispatchEntry != null ? o.DispatchEntry.VehicleMake + " " + o.DispatchEntry.VehicleModel : ""),
                            ImpoundDate = o.ImpoundDate,
                            DaysHeld = o.DaysHeld,
                        })
                    );
                }
            }

            if (!WebGlobal.CurrentUser.HasAccessToCompany(_account.Companies) && _account.Id > 1)
            {
                LogRedirectError("User does not have access to account's compannies and accountId > 1");
                throw new ApplicationException("Access Denied");
            }
            else
            {
                if (_account.CompanyId != WebGlobal.CurrentUser.CompanyId && _account.Id > 1)
                    this.SharedAccount = true;
            }

            this.Master.InnerTitle = _account.Company;

            if (Request.QueryString["reset"] != null)
                Account.CacheClearById(_account.Id);

            var companies = new List<Extric.Towbook.Company.Company>();

            if (Global.CurrentUser.CompanyId == 1525)
            {
                companies = new Extric.Towbook.Company.Company[] { Global.CurrentUser.Company }.ToList();
            }
            else
            {
                companies = Extric.Towbook.WebShared.WebGlobal.GetCompanies().Where(o => o.Id != 1525).ToList();
            }

            if (_account.ReferenceNumber == "subcontractor")
            {
                companies = _account.Companies.Select(o => Extric.Towbook.Company.Company.GetById(o)).ToList();
                _showCompany = true;
            }

            #region reply-to roles
            var billingAddress = AddressBookEntry.GetByName("Billing Address", _account.CompanyId, false).FirstOrDefault();
            if (billingAddress != null && Core.IsEmailValid(billingAddress.Email))
                _replyToEmailAddress = billingAddress.Email;

            var replytoEmail = AccountKeyValue.GetFirstValueOrNull(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "ReplyToEmailAddress");
            if (Core.IsEmailValid(replytoEmail))
            {
                _replyToEmailAddress = replytoEmail;
            }
            #endregion

            #region add possible account billing contact role
            _currentAccountEmail = _account.Email;
            
            var addressBookType = "Billing Address";
            if (_account.Type == AccountType.MotorClub)
                addressBookType = "MapFrom";

            billingAddress = AddressBookEntry.GetByAccountId(_account.Id).Where(o => o.Name == addressBookType).FirstOrDefault();
            if (billingAddress != null && Core.IsEmailValid(billingAddress.Email))
            {
                _currentAccountEmail = billingAddress.Email;
            }
            #endregion


            _initialStatementDueDate = DateTime.Now.AddDays(30);
            var so = Extric.Towbook.Accounts.StatementOption.GetByCompanyId(_account.CompanyId, _account.Id);
            if(so != null)
            {
                _initialStatementDueDate = Statement.GetNextStatementDueDate(so.DefaultDueDateType, DateTime.Now);
            }

            if(Global.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.AdvancedBilling))
            {
                var ieo = Extric.Towbook.Dispatch.InvoiceEmailOption.GetByCompanyId(_account.CompanyId, _account.Id);
                if(ieo != null)
                {
                    _initialInvoiceEmailSubject = ieo.Subject;
                    _initialInvoiceEmailMessage = ieo.Message;
                }
            }
        }
        else
        {
            Response.Redirect("/Accounts/");
            LogRedirectError("Accounts redirecting because no accountId");
            return;
        }
    }
}
