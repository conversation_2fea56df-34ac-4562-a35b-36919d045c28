using System;
using System.Collections.ObjectModel;
using System.Data;
using System.Web.UI;
using System.Web.UI.WebControls;
using Extric.Towbook.Surcharges;
using Extric.Towbook.Impounds;
using Extric.Towbook.Accounts;
using System.Linq;
using Extric.Towbook.Company;
using Extric.Towbook.Utility;
using Extric.Towbook.Vehicle;
using Extric.Towbook.API.Models;
using Extric.Towbook.Integration;
using Extric.Towbook.WebShared;
using Extric.Towbook.Dispatch;
using System.Collections.Generic;
using Extric.Towbook;
using Extric.Towbook.Generated;
using System.Threading.Tasks;

public partial class Accounts_AccountEditor : System.Web.UI.Page
{
    protected int _id;
    protected Extric.Towbook.Accounts.Account _account;
    protected Extric.Towbook.Accounts.StorageRate _storageRate;
    protected bool _showAll;
    protected bool _fuelSurchargeEnabled;
    protected string RateItemsJson { get; private set; }
    protected string ReasonsJson { get; private set; }
    protected string BodyTypesJson { get; private set; }
    protected string RulesJson { get; private set; }

    protected string PredefinedRateItemsToExclude
    {
        get
        {
            return new[]
            {
                PredefinedRateItem.BUILTIN_MILEAGE_UNLOADED, PredefinedRateItem.BUILTIN_MILEAGE_LOADED,
                PredefinedRateItem.BUILTIN_MILEAGE_DEADHEAD, PredefinedRateItem.BUILTIN_AFTER_HOUR_RELEASE_FEE
            }.ToJson();
        }
    } 

    protected EntryValidationRuleSet _ruleSet { get; set; }
    protected MasterAccount _masterAccount;
    protected Collection<ListItem> _gracePeriodStartItems { get; set; }
    protected Collection<ListItem> _booleanItems { get; set; }
    protected Collection<AccountTag> _accountTags { get; set; }

    protected Collection<StickeringReasonTimeItem> rpReasonItems = new Collection<StickeringReasonTimeItem>();
    protected bool enableStickering = false;
    protected bool enablePermits = false;
    protected bool enableSiteVisits = false;
    protected bool squarePaymentsEnabled = false;
    protected ParkingPermitPublicLink _parkingPermitPublicLink;
    protected string companyReplytoEmail { get; set; }
    protected string companyReplytoEmailSource { get; set; }


    protected bool ShowCreditOptions
    {
        get { 
            return Global.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.AccountCreditLimits) ||
                Global.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.AdvancedBilling); 
        }
    }

    protected bool ShowDigitalLocations { get; set; }

    protected string DigitalLocationsJson { get; set; }
    protected bool ShowAutoAccept
    {
        get
        {
            if (new int[] { 26130,26907,26908, 26125, 26126, 26127, 26128, 26129, 27424, 27425, 27426, 27427, 27428, 27429, 27430, 27431, 27432, 27433, 27434, 27435, 27436,
                27437, 27438, 27439, 27440, 27441, 27442, 27443, 27444, 27445, 27446, 27447, 27448, 27449, 27450, 27451, 27452, 27453, 27454, 27455, 27456, 27457,
                27458, 27459, 27460, 27461, 27462, 27463, 27464, 27465, 27466, 27467, 27468, 27469, 27470,
                27471, 27472, 27473, 27474, 27475, 27476, 27477, 27478, 27479, 27480, 27481, 27482, 27483, 27484, 27485, 27486, 27487, 27488, 27489, 27490, 27491,
                27492, 27493, 27494, 27495, 26556, 28172, 28173, 28437, 211882 }.Contains(Global.CurrentUser.CompanyId) || Global.CurrentUser.PrimaryCompanyId == 26130 ||
               Global.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.AutoDispatch))
            {
                return true;
            }
            else
            {
                return false;
            }
        }
    }

    public class AutoAcceptRule
    {
        public string Zip { get; set; }
        public string Type { get; set; }
        public int Eta { get; set; }
    }

    protected string AddressBookNameMapOrBilling
    {
        get
        {
            if (_account == null || _account.Type == AccountType.MotorClub)
                return "MapFrom";
            else
                return "Billing Address";
        }
    }

    public string autoAcceptJson { get; set; }

    protected bool motorClubVisible = false;
    protected bool mcTaxIdVisible = false;
    protected bool mcLocationIdVisible = false;
    protected bool mcUsernameVisible = false;
    protected bool mcPasswordVisible = false;

    /// <summary>
    ///  12 Days Redis Key block for new required fields (see .aspx page for ShowNewFields)
    /// </summary>
    public bool ShowNewFields
    {
        get
        {
            return WebGlobal.CurrentUser.Company.HasAccessToBaseFeature("newCompletionRequirements");
        }
    }

    protected bool AllowAccess()
    {
        return (WebGlobal.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Manager ||
            WebGlobal.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Accountant ||
           WebGlobal.CurrentUser.Id == 1845 ||
           WebGlobal.CurrentUser.CompanyId == 366 ||
           (WebGlobal.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Dispatcher &&
           new int[] { 2508, 7778 }.Contains(Global.CurrentUser.CompanyId)));
    }

    protected void Page_Load(object sender, EventArgs e)
    {
        Response.Cache.SetNoStore();

        this.Master.CurrentSection = Navigation.NavigationItemEnum.Accounts;
        this.Master.InnerTitle = "Account Editor";
        this.Master.UseJquery = true;

        cbEnablePaymentImport.Visible = false;
        cbEnableStickering.Visible = false;


        if (!AllowAccess())
            Response.Redirect("/Accounts/");

        if (Request.QueryString["id"] != null)
        {

            _id = Convert.ToInt32(Request.QueryString["id"]);
            _account = Account.GetById(_id);

            if (_account == null ||
                !WebGlobal.CurrentUser.HasAccessToCompany(_account.CompanyId))
            {
                Response.Redirect("/Accounts/");
            }

            _storageRate = _account.StorageRates;


            this.Master.InnerTitle = "Modify Account - " + _account.Company;
            this.Title = "Towbook - " + this.Master.InnerTitle;

            if (_account.MasterAccountId > 0)
            {
                _masterAccount = MasterAccount.GetById(_account.MasterAccountId);
                standardAccount.Visible = true;
            }

            if (_masterAccount != null)
            {
                if (_masterAccount.AccountTypeId == (int)AccountType.MotorClub)
                {
                    motorClubVisible = true;
                    mcTaxIdVisible = new int[] { 1, 5, 19, 24, 22, 20 }.Contains(_masterAccount.Id);
                    mcLocationIdVisible = new int[] { 1, 5 }.Contains(_masterAccount.Id);

                    openingBalance.Visible = false;
                }

                if (_masterAccount.Id == 1 ||
                    _masterAccount.Id == 2 ||
                    _masterAccount.Id == 3 ||
                    _masterAccount.Id == 4 ||
                    _masterAccount.Id == 5 ||
                    _masterAccount.Id == 7)
                {
                    cbEnablePaymentImport.Visible = true;
                    mcUsernameVisible = true;
                    mcPasswordVisible = true;
                }

                if (!IsPostBack)
                {
                    if (AccountKeyValue.GetFirstValueOrNull(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "DigitalAutoAccept") != null)
                    {
                        cbDisableAutoAccept.Visible = true;
                        cbDisableAutoAccept.Checked = AccountKeyValue.GetFirstValueOrNull(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "DigitalAutoAcceptDisabled") == "1";
                    }
                }

            }
        }
        else
        {
            this.Master.InnerTitle = "Create a new account";
            this.Title = "Towbook - " + this.Master.InnerTitle;

            _account = new Account();
            _account.CompanyId = Global.CurrentUser.Company.Id;
            companyReplytoEmail = WebGlobal.CurrentUser.Company.Email;
            companyReplytoEmailSource = "company email";
            var billingAddress = AddressBookEntry.GetByName("Billing Address", _account.CompanyId, false).FirstOrDefault();
            if (billingAddress != null && Core.IsEmailValid(billingAddress.Email))
            {
                companyReplytoEmail = billingAddress.Email;
                companyReplytoEmailSource = "billing address email";
            }
        }

        RegisterAsyncTask(new PageAsyncTask(PageLoadAsync));
    }

    protected async Task PageLoadAsync()
    {
        _accountTags = await AccountTag.GetByCompanyIdAsync(_account.CompanyId);

        RulesJson = "[]";

        if (!IsPostBack)
        {
            dlType.Items.Add(new ListItem("Other", "0"));
            dlType.Items.Add(new ListItem("Motor Club", "5"));
            dlType.Items.Add(new ListItem("Automotive Service Shop", "7"));
            dlType.Items.Add(new ListItem("Body Shop", "3"));
            dlType.Items.Add(new ListItem("Insurance Company", "4"));
            dlType.Items.Add(new ListItem("Police Department", "1"));
            dlType.Items.Add(new ListItem("Municipality ", "6"));
            dlType.Items.Add(new ListItem("Individual", "2"));
            dlType.Items.Add(new ListItem("Storage Facility", "8"));
            dlType.Items.Add(new ListItem("Private Property", "9"));
            dlType.Items.Add(new ListItem("Repossession Agency", "10"));
            dlType.Items.Add(new ListItem("Dealership", "11"));
            dlType.Items.Add(new ListItem("Heavy Equipment", "12"));
            dlType.Items.Add(new ListItem("Fleet", "13"));
            dlType.Items.Add(new ListItem("Broker", "14"));
            dlType.Items.Add(new ListItem("Transport", "15"));
            dlType.Items.Add(new ListItem("Subcontractor", "16"));

            if (Global.CurrentUser.Company.HasFeature(Features.DispatchToSubcontractors_SubcontractorRotation) &&
                Request.QueryString["id"] == null)
                dlType.SelectedValue = "16";
        }

        if (Request.QueryString["id"] != null)
        {
            if (!IsPostBack)
            {
                var pn = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "ImportPayments").FirstOrDefault();

                if (pn != null && pn.Value == "1")
                    cbEnablePaymentImport.Checked = true;

                if (ShowAutoAccept)
                {
                    var aajson = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "DigitalAutoAccept").FirstOrDefault();

                    if (aajson != null)
                    {
                        autoAcceptJson = aajson.Value;
                    }
                }

                if (_masterAccount != null && 
                    (_masterAccount.Id == MasterAccountTypes.Agero ||
                    _masterAccount.Id == MasterAccountTypes.Allstate ||
                    _masterAccount.Id == MasterAccountTypes.Swoop ||
                    _masterAccount.Id == MasterAccountTypes.Urgently))
                {
                    cbPreventPhotoSharing.Checked = AccountKeyValue.GetFirstValueOrNull(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "PreventPhotoSharing") == "1";
                }

                if (_masterAccount != null && _masterAccount.Id == MasterAccountTypes.Swoop)
                {
                    ShowDigitalLocations = true;

                    DigitalLocationsJson = (await WebGlobal.GetResponseFromUrlAsync(
                        string.Format("/api/accounts/{0}/locations", _account.Id))).Content;
                }
                else
                {
                    DigitalLocationsJson = "[]";
                }

                cbAllowAccountUsersToViewFiles.Checked = AccountKeyValue.GetFirstValueOrNull(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "AllowAccountUsersToViewFiles") == "1";
            }

            if (_account.Type == AccountType.PrivateProperty)
            {
                privateProperty.Visible = true;

                cbEnableParkingPermits.Visible = Global.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.ParkingPermits);
                cbEnableStickering.Visible = Global.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.Stickering);
                cbEnableSiteVisits.Visible = Global.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.SiteVisits);

                enablePermits = Global.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.ParkingPermits);
                enableStickering = Global.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.Stickering);
                enableSiteVisits = Global.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.SiteVisits);

                if (cbEmail != null)
                {
                    cbEmail.Text = "Email Notice Automatically";
                }

                if (!IsPostBack)
                {
                    var pn = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "ParkingPermitsEnabled").FirstOrDefault();

                    if (pn != null && pn.Value == "1" || pn == null)
                    {
                        cbEnableParkingPermits.Checked = true;
                        enablePermits = true;
                    }

                    var s = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "StickeringEnabled").FirstOrDefault();

                    if (s != null && s.Value == "1" || s == null)
                    {
                        cbEnableStickering.Checked = true;
                        enableStickering = true;
                    }

                    var sv = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "SiteVisitsEnabled").FirstOrDefault();

                    if (sv != null && sv.Value == "1" || sv == null)
                    {
                        cbEnableSiteVisits.Checked = true;
                        enableSiteVisits = true;
                    }
                }
            }
            else
            {
                cbEnableParkingPermits.Visible = false;
                cbEnableStickering.Visible = false;
                cbEnableSiteVisits.Visible = false;
                enableStickering = false;
                enablePermits = false;
                enableSiteVisits = false;
            }

            RulesJson = AccountRateItemRule.GetByAccountId(_id).Select(o => AccountRateItemRuleModel.Map(o)).ToJson();
            _showAll = (await RateItemHelper.GetAllRateItems(Account.GetById(_id), false)).Count == 0;


            if (!IsPostBack)
            {
                rpImpoundLots.DataSource = await Lot.GetByCompanyAsync(Global.CurrentUser.Company,
                    _account);
                rpImpoundLots.DataBind();
                if (rpImpoundLots.Items.Count == 0)
                    rpImpoundLots.Visible = false;

                txtCompany.Text = _account.Company;
                txtContactName.Text = _account.FullName;
                txtAddress.Text = _account.Address;
                txtCity.Text = _account.City;
                txtState.Text = _account.State;
                txtZip.Text = _account.Zip;
                txtEmail.Text = _account.Email;
                txtPhone.Text = Core.FormatPhone(_account.Phone, Global.CurrentUser.Company);
                txtFax.Text = _account.Fax;
                cbTaxExempt.Checked = _account.TaxExempt;

                if (ShowCreditOptions)
                {
                    if (_account.CreditLimit != null)
                        txtCreditLimit.Text = _account.CreditLimit.Value.ToString("0.00");

                    cbCreditHold.Checked = _account.CreditHold;
                }

                var billingAddress = AddressBookEntry.GetByAccountId(this._id).Where(o => o.Name == this.AddressBookNameMapOrBilling).FirstOrDefault();

                if (billingAddress != null)
                {
                    txtBillingAddress.Text = billingAddress.Address;
                    txtBillingCity.Text = billingAddress.City;
                    txtBillingState.Text = billingAddress.State;
                    txtBillingZip.Text = billingAddress.Zip;
                    txtBillingContactEmail.Text = billingAddress.Email;
                    txtBillingContactName.Text = string.IsNullOrWhiteSpace(billingAddress.Notes) ? "" : billingAddress.Notes.Replace("RecipientDisplayName:", "");
                }

                cbInactive.Checked = _account.Status == AccountStatus.Inactive;

                txtNotes.Text = _account.Notes;

                discountRate.Text = (_account.DiscountRate * 100).ToString("0.00");

                dlType.SelectedValue = Convert.ToInt32(_account.Type).ToString();

                txtDefaultPO.Text = _account.DefaultPO;

                if (_account.DefaultPriority == 1)
                    cbDefaultPriority.Checked = true;

                var priorityCallTextAlertForManagers = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "EnablePriorityCallTextAlertToAllManagers").FirstOrDefault();
                if (priorityCallTextAlertForManagers != null)
                    cbPriorityCallTextAlertToManagers.Checked = (priorityCallTextAlertForManagers.Value == "1");

                var setting = CompanyKeyValue.GetFirstValueOrNull(_account.CompanyId,
                    Provider.Towbook.ProviderId, "HideChargesFromMotorClubInvoicesByDefault");

                var alwaysHide = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "AlwaysHideCharges").FirstOrDefault();
                if (alwaysHide != null)
                    cbAlwaysHideCharges.Checked = (alwaysHide.Value == "1");
                else
                {
                    if (_account.Type == AccountType.MotorClub)
                    {
                        var companyHideMCCharges = CompanyKeyValue.GetFirstValueOrNull(_account.CompanyId,
                           Provider.Towbook.ProviderId, "HideChargesFromMotorClubInvoicesByDefault") ?? "1";

                        cbAlwaysHideCharges.Checked = (companyHideMCCharges == "1");
                    }
                }
                
                var alwaysHidePhotos = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "AlwaysHidePhotos").FirstOrDefault();

                if (alwaysHidePhotos != null && alwaysHidePhotos.Value == "1")
                    cbAlwaysHidePhotos.Checked = true;

                var key = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "AlwaysHideDiscounts").FirstOrDefault();
                if (key != null && key.Value == "1")
                    cbAlwaysHideDiscounts.Checked = true;

                companyReplytoEmail = WebGlobal.CurrentUser.Company.Email;
                companyReplytoEmailSource = "company email";
                billingAddress = AddressBookEntry.GetByName("Billing Address", _account.CompanyId, false).FirstOrDefault();
                if (billingAddress != null && Core.IsEmailValid(billingAddress.Email))
                {
                    companyReplytoEmail = billingAddress.Email;
                    companyReplytoEmailSource = "billing address email";
                }

                var replyToEmail = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "ReplyToEmailAddress").FirstOrDefault();
                if (replyToEmail != null && !string.IsNullOrWhiteSpace(replyToEmail.Value))
                    txtReplyToEmail.Text = replyToEmail.Value;

                ddlEmailEventPreference.Items.Add(new ListItem("Call is Completed", "0"));
                ddlEmailEventPreference.Items.Add(new ListItem("Call is Marked Audited", "1"));

                var emailPreference = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "EmailInvoiceEventPreference").FirstOrDefault();
                if (emailPreference != null)
                {
                    lblEmailEventPreference.Text = "Email Invoice preference (using account setting)";
                    ddlEmailEventPreference.SelectedValue = emailPreference.Value;
                }
                else
                {
                    var companyEmailPreference = CompanyKeyValue.GetFirstValueOrNull(WebGlobal.CurrentUser.CompanyId,
                        Provider.Towbook.ProviderId, "EmailInvoiceEventPreference") ?? "0";

                    lblEmailEventPreference.Text = "Email Invoice preference (using company preference)";
                    ddlEmailEventPreference.SelectedValue = companyEmailPreference;
                }


                key = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "AccountPhysicalAddressAsDefaultLocation").FirstOrDefault();
                if (key != null && key.Value != "0")
                {
                    cbUsePhysicalAddressAs.Checked = true;

                    ddlDefaultPhysicalAddress.SelectedValue = key.Value;
                }

                ddlDefaultPhysicalAddress.Enabled = false;
                if (cbUsePhysicalAddressAs.Checked == true)
                    ddlDefaultPhysicalAddress.Enabled = true;
                

                key = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "AccountContactAddedAtCallCreation").FirstOrDefault();
                if (key != null && key.Value == "1")
                    cbAutoFillContact.Checked = true;


                if (Global.CurrentUser.Company.HasFeature(Features.PaymentIntegrations_Square))
                {
                    var companyIncludeInvoice = CompanyKeyValue.GetFirstValueOrNull(_account.CompanyId, Provider.Towbook.ProviderId, "Square_AlwaysIncludePaymentLinkOnInvoices");
                    var companyIncludeStatement = CompanyKeyValue.GetFirstValueOrNull(_account.CompanyId, Provider.Towbook.ProviderId, "Square_AlwaysIncludePaymentLinkOnStatements");
                    var companyOptOutOfSquareEmails = CompanyKeyValue.GetFirstValueOrNull(_account.CompanyId, Provider.Towbook.ProviderId, "Square_OptOutOfEmailsOnTransactions");


                    var includeInvoicePaymentLink = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "Square_AlwaysIncludePaymentLinkOnInvoices").FirstOrDefault();

                    if (includeInvoicePaymentLink != null)
                    {
                        cbIncludeInvoicePaymentLink.Text = cbIncludeInvoicePaymentLink.Text + " (using account settings)";

                        if (includeInvoicePaymentLink.Value == "1")
                            cbIncludeInvoicePaymentLink.Checked = true;
                        else
                            cbIncludeStatementPaymentLink.Checked = false;
                    }
                    else
                    {
                        cbIncludeInvoicePaymentLink.Text = cbIncludeInvoicePaymentLink.Text + " (using company settings)";
                        cbIncludeInvoicePaymentLink.Checked = companyIncludeInvoice == "1";
                    }

                    var includeStatementPaymentLink = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "Square_AlwaysIncludePaymentLinkOnStatements").FirstOrDefault();

                    if (includeStatementPaymentLink != null)
                    {
                        cbIncludeStatementPaymentLink.Text = cbIncludeStatementPaymentLink.Text + " (using account settings)";

                        if (includeStatementPaymentLink.Value == "1")
                            cbIncludeStatementPaymentLink.Checked = true;
                        else
                            cbIncludeStatementPaymentLink.Checked = false;
                    }
                    else
                    {
                        cbIncludeStatementPaymentLink.Text = cbIncludeStatementPaymentLink.Text + " (using company settings)";
                        cbIncludeStatementPaymentLink.Checked = companyIncludeStatement == "1";
                    }

                    
                    var optOutOfSquareEmails = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "Square_OptOutOfEmailsOnTransactions").FirstOrDefault();

                    if (optOutOfSquareEmails != null)
                    {
                        cbOptOutOfConfirmationEmailOnTransactions.Text = cbOptOutOfConfirmationEmailOnTransactions.Text + " (using account settings)";
                        if (optOutOfSquareEmails.Value != "1")
                            cbOptOutOfConfirmationEmailOnTransactions.Checked = true;
                        else
                            cbOptOutOfConfirmationEmailOnTransactions.Checked = false;
                    }
                    else
                    {
                        cbOptOutOfConfirmationEmailOnTransactions.Text = cbOptOutOfConfirmationEmailOnTransactions.Text + " (using company settings)";
                        cbOptOutOfConfirmationEmailOnTransactions.Checked = companyOptOutOfSquareEmails != "1";
                    }


                    var companyExcludeDispatchInvoice = CompanyKeyValue.GetFirstValueOrNull(_account.CompanyId, Provider.Towbook.ProviderId, "Square_ExcludeLinkOnPrintedDispatchInvoices");
                    var companyExcludeImpoundInvoice = CompanyKeyValue.GetFirstValueOrNull(_account.CompanyId, Provider.Towbook.ProviderId, "Square_ExcludeLinkOnPrintedImpoundInvoices");
                    var companyExcludeStatement = CompanyKeyValue.GetFirstValueOrNull(_account.CompanyId, Provider.Towbook.ProviderId, "Square_ExcludeLinkOnPrintedStatements");

                    // dispatch printed invoice
                    var excludeAccountSetting = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "Square_ExcludeLinkOnPrintedDispatchInvoices").FirstOrDefault();
                    if (excludeAccountSetting != null)
                    {
                        cbExcludeLinkOnPrintedDispatchInvoices.Text = cbExcludeLinkOnPrintedDispatchInvoices.Text + " (using account settings)";
                        if (excludeAccountSetting.Value == "1")
                            cbExcludeLinkOnPrintedDispatchInvoices.Checked = false;
                        else
                            cbExcludeLinkOnPrintedDispatchInvoices.Checked = true;
                    }
                    else
                    {
                        cbExcludeLinkOnPrintedDispatchInvoices.Text = cbExcludeLinkOnPrintedDispatchInvoices.Text + " (using company settings)";
                        cbExcludeLinkOnPrintedDispatchInvoices.Checked = companyExcludeDispatchInvoice == "1";
                    }

                    // impound printed invoice
                    excludeAccountSetting = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "Square_ExcludeLinkOnPrintedImpoundInvoices").FirstOrDefault();
                    if (excludeAccountSetting != null)
                    {
                        cbExcludeLinkOnPrintedImpoundInvoices.Text = cbExcludeLinkOnPrintedImpoundInvoices.Text + " (using account settings)";
                        if (excludeAccountSetting.Value == "1")
                            cbExcludeLinkOnPrintedImpoundInvoices.Checked = false;
                        else
                            cbExcludeLinkOnPrintedImpoundInvoices.Checked = true;
                    }
                    else
                    {
                        cbExcludeLinkOnPrintedImpoundInvoices.Text = cbExcludeLinkOnPrintedImpoundInvoices.Text + " (using company settings)";
                        cbExcludeLinkOnPrintedImpoundInvoices.Checked = companyExcludeImpoundInvoice == "1";
                    }

                    // printed statement
                    excludeAccountSetting = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "Square_ExcludeLinkOnPrintedStatements").FirstOrDefault();
                    if (excludeAccountSetting != null)
                    {
                        cbExcludeLinkOnPrintedStatements.Text = cbExcludeLinkOnPrintedStatements.Text + " (using account settings)";

                        if (excludeAccountSetting.Value == "1")
                            cbExcludeLinkOnPrintedStatements.Checked = false;
                        else
                            cbExcludeLinkOnPrintedStatements.Checked = true;
                    }
                    else
                    {
                        cbExcludeLinkOnPrintedStatements.Text = cbExcludeLinkOnPrintedStatements.Text + " (using company settings)";
                        cbExcludeLinkOnPrintedStatements.Checked = companyExcludeStatement == "1";
                    }


                    // tipping options
                    var companyTippingExcludeOnPaymentLinks = CompanyKeyValue.GetFirstValueOrNull(_account.CompanyId, Provider.Towbook.ProviderId, "SquareTipping_ExcludeOnPaymentLinks");
                    var companyTippingExcludeOnSquareReader = CompanyKeyValue.GetFirstValueOrNull(_account.CompanyId, Provider.Towbook.ProviderId, "SquareTipping_ExcludeOnSquareReader");
                    var companyTippingExcludeOnSquareTerimal = CompanyKeyValue.GetFirstValueOrNull(_account.CompanyId, Provider.Towbook.ProviderId, "SquareTipping_ExcludeOnSquareTerminal");

                    excludeAccountSetting = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "SquareTipping_ExcludeOnPaymentLinks").FirstOrDefault();
                    if (excludeAccountSetting != null)
                    {
                        cbExcludeTipsOnPaymentLinks.Text = cbExcludeTipsOnPaymentLinks.Text + " (using account settings)";

                        if (excludeAccountSetting.Value == "1")
                            cbExcludeTipsOnPaymentLinks.Checked = false;
                        else
                            cbExcludeTipsOnPaymentLinks.Checked = true;
                    }
                    else
                    {
                        cbExcludeTipsOnPaymentLinks.Text = cbExcludeTipsOnPaymentLinks.Text + " (using company settings)";
                        cbExcludeTipsOnPaymentLinks.Checked = companyTippingExcludeOnPaymentLinks != "1";
                    }

                    excludeAccountSetting = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "SquareTipping_ExcludeOnSquareReader").FirstOrDefault();
                    if (excludeAccountSetting != null)
                    {
                        cbExcludeTipsOnSquareReader.Text = cbExcludeTipsOnSquareReader.Text + " (using account settings)";

                        if (excludeAccountSetting.Value == "1")
                            cbExcludeTipsOnSquareReader.Checked = false;
                        else
                            cbExcludeTipsOnSquareReader.Checked = true;
                    }
                    else
                    {
                        cbExcludeTipsOnSquareReader.Text = cbExcludeTipsOnSquareReader.Text + " (using company settings)";
                        cbExcludeTipsOnSquareReader.Checked = companyTippingExcludeOnSquareReader != "1";
                    }

                    excludeAccountSetting = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "SquareTipping_ExcludeOnSquareTerminal").FirstOrDefault();
                    if (excludeAccountSetting != null)
                    {
                        cbExcludeTipsOnSquareTerminal.Text = cbExcludeTipsOnSquareTerminal.Text + " (using account settings)";

                        if (excludeAccountSetting.Value == "1")
                            cbExcludeTipsOnSquareTerminal.Checked = false;
                        else
                            cbExcludeTipsOnSquareTerminal.Checked = true;
                    }
                    else
                    {
                        cbExcludeTipsOnSquareTerminal.Text = cbExcludeTipsOnSquareTerminal.Text + " (using company settings)";
                        cbExcludeTipsOnSquareTerminal.Checked = companyTippingExcludeOnSquareTerimal != "1";
                    }

                }


                cbAlwaysSendRoadsideInvite.Checked = false;
                cbAlwaysSendSurvey.Checked = false;
                if (Global.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.Roadside))
                {
                    var rs = Extric.Roadside.RoadsideSetting.GetByCompanyId(_account.CompanyId, _account.Id);
                    var items = Extric.Roadside.JobProgressTextAlertItem.GetByCompanyId(_account.CompanyId, true);


                    // get defaults if company has no saved text message items saved (inherit defaults)
                    if (items.Count() == 0)
                        items = Extric.Roadside.JobProgressTextAlertItem.GetDefaults(_account.CompanyId, _account.Id);

                    bool atLeastOneProgressText = items.Where(w => new int[] {
                        Extric.Roadside.JobProgressStatusType.Created.Id,
                        Extric.Roadside.JobProgressStatusType.Enroute.Id,
                        Extric.Roadside.JobProgressStatusType.Arriving.Id }.Contains(w.StatusTypeId) && 
                        !w.IsDeleted).Any();

                    

                    var item = items.Where(w => w.StatusTypeId == Extric.Roadside.JobProgressStatusType.Completed.Id).LastOrDefault();

                    if (_account.Type == AccountType.MotorClub)
                    {
                        if(rs.EnableMotorClubAutoInvite.GetValueOrDefault() && atLeastOneProgressText)
                            cbAlwaysSendRoadsideInvite.Checked = true;

                        if(rs.EnableMotorClubAutoInvite.GetValueOrDefault() && (item == null  || !item.IsDeleted))
                            cbAlwaysSendSurvey.Checked = true;
                    }

                    if (_account.Type != AccountType.MotorClub)
                    {
                        if(rs.EnableNonMotorClubAutoInvite.GetValueOrDefault()  && atLeastOneProgressText)
                            cbAlwaysSendRoadsideInvite.Checked = true;

                        if(rs.EnableNonMotorClubAutoInvite.GetValueOrDefault() && (item == null || !item.IsDeleted))
                            cbAlwaysSendSurvey.Checked = true;
                    }

                    if (_account.Type == AccountType.Municipality || 
                        _account.Type == AccountType.PoliceDepartment || 
                        _account.Type == AccountType.PrivateProperty ||
                        (_account.Type == AccountType.MotorClub && 
                            (_account.MasterAccountId == 2 ||  // Allstate
                             _account.MasterAccountId == 3 ||  // Agero
                             _account.MasterAccountId == 28 || // Agero Spark
                             _account.MasterAccountId == 29 || // Agero (Swoop)
                             _account.MasterAccountId == 30 || // OON - Agero
                             _account.MasterAccountId == 31 || // OON - Allstate
                             _account.MasterAccountId == 36    // OON - Agero (Swoop)
                            ))) 
                    {
                        cbAlwaysSendRoadsideInvite.Visible = false;
                        cbAlwaysSendSurvey.Visible = false;
                        labelRoadside.Visible = false;
                    }

                    key = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "AutoSendRoadsideInvite").FirstOrDefault();
                    if (key != null)
                    {
                        if (key.Value == "1")
                            cbAlwaysSendRoadsideInvite.Checked = true;
                        else if (key.Value == "0")
                            cbAlwaysSendRoadsideInvite.Checked = false;

                        if(key.Value == "1" || key.Value == "0")
                            cbAlwaysSendRoadsideInvite.Text = cbAlwaysSendRoadsideInvite.Text + " (using account settings)";

                    }
                    else
                    {
                        cbAlwaysSendRoadsideInvite.Text = cbAlwaysSendRoadsideInvite.Text + " (using company settings)";
                    }


                    key = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "AlwaysSendSurvey").FirstOrDefault();
                    if (key != null)
                    {
                        if (key.Value == "1")
                            cbAlwaysSendSurvey.Checked = true;
                        else if (key.Value == "0")
                            cbAlwaysSendSurvey.Checked = false;

                        if (key.Value == "1" || key.Value == "0")
                            cbAlwaysSendSurvey.Text = cbAlwaysSendSurvey.Text + " (using account settings)";
                    }
                    else
                    {
                        cbAlwaysSendSurvey.Text = cbAlwaysSendSurvey.Text + " (using company settings)";
                    }
                }

                if (AccountKeyValue.GetFirstValueOrNull(_account.CompanyId, 
                    _account.Id, Provider.Towbook.ProviderId, "IncludeInvoicesWithCopyOfStatement") == "1")
                    cbIncludeInvoiceCopiesOnStatements.Checked = true;

                ddlSuggestedDefault.Items.Add(new ListItem("Optimal (Google Suggested Route)", "1"));
                ddlSuggestedDefault.Items.Add(new ListItem("Shortest Distance", "2"));
                ddlSuggestedDefault.Items.Add(new ListItem("Fastest Time", "3"));
                ddlSuggestedDefault.SelectedValue = AccountKeyValue.GetFirstValueOrNull(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "DefaultSuggestedMileageRoute") ?? "1";

                if(Global.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.GPS_AutomaticMileage) || Global.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.Options_AutomaticallyAddMiles))
                {
                    var companyAutoAddSetting = CompanyKeyValue.GetFirstValueOrNull(
                        _account.CompanyId,
                        Provider.Towbook.ProviderId,
                        "Towbook_Calls_AutomaticallyAddMiles") ?? "0";

                    if (Global.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.Options_AutomaticallyAddMiles))
                        companyAutoAddSetting = "1";

                    string autoMilesText = companyAutoAddSetting == "1" ? "Currently set to add automatically" : "Currently set to not add automatically";

                    var accountAutoAddSetting = AccountKeyValue.GetFirstValueOrNull(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "AutomaticallyAddMiles") ?? "0";

                    ddlAutomaticallyFillInMiles.Items.Add(new ListItem("Use Company Default - " + autoMilesText, "0"));
                    ddlAutomaticallyFillInMiles.Items.Add(new ListItem("Always add to invoice automatically", "1"));
                    ddlAutomaticallyFillInMiles.Items.Add(new ListItem("Never add to invoice automatically", "2"));

                    ddlAutomaticallyFillInMiles.SelectedValue =  accountAutoAddSetting ?? "0";
                }

                cbUnloadedMileageSetOne.Checked = AccountKeyValue.GetFirstValueOrNull(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "MCBilling_ForceUnloadedMilesToOneIfMissing") == "1";
                cbUnloadedMileageRoundOne.Checked = AccountKeyValue.GetFirstValueOrNull(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "RoundUpCalculatedMiles") == "1";

                cbUnloadedMileageRoundOne.Text = "When calculating mileage, round up to the next full " + WebGlobal.CurrentUser.Company.LocaleMile;
                cbUnloadedMileageRoundOne.ToolTip = "Towbook will round the mileage up to the next full " + WebGlobal.CurrentUser.Company.LocaleMile;

                if(Global.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.DeadheadMileage))
                    cbAlwaysAddDeadhead.Checked = AccountKeyValue.GetFirstValueOrNull(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "AutomaticallyAddDeadheadMileage") == "1";

                SurchargeRate sr = await SurchargeRate.GetBySurchargeAsync(Surcharge.SURCHARGE_FUEL, Global.CurrentUser.Company.Id);
                SurchargeAccountRate sar = null;

                if (_account.Id > 1)
                    sar = await SurchargeAccountRate.GetBySurchargeAsync(Surcharge.SURCHARGE_FUEL, _account.Id);

                if (sr != null && sar == null)
                {
                    fuelSurchargeNote.Text = "Your companies standard fuel surcharge rate of " + (sr.Rate * 100).ToString("0.00") + "% will apply to this account if you leave the box above blank. If you don't want to charge any fuel charge for this account, enter 0 in the box.<br /><br />";
                    fuelSurchargeNote.Text = "&nbsp;";
                    _fuelSurchargeEnabled = true;
                }

                if (sar != null)
                {
                    _fuelSurchargeEnabled = true;

                    fuelSurcharge.Text = (sar.Rate * 100).ToString("0.00");
                }

                switch (_account.InvoiceTerms)
                {
                    case Account.InvoiceTerm.Immediate:
                        rbPaymentImmediate.Checked = true;
                        break;

                    case Account.InvoiceTerm.Net15:
                        rbPaymentNet15.Checked = true;
                        break;

                    case Account.InvoiceTerm.Net30:
                        rbPaymentNet30.Checked = true;
                        break;
                }

                string idt = "default";

                switch (_account.ImpoundDestinationType)
                {
                    case AccountImpoundDestination.None:
                        rbStorageLotsNone.Checked = true;
                        idt = "default";
                        break;
                    case AccountImpoundDestination.Default:
                        rbStorageLotsDefault.Checked = true;
                        idt = "default";
                        break;
                    case AccountImpoundDestination.ThirdParty:
                        rbStorageLotThirdParty.Checked = true;
                        idt = "other";
                        break;
                }

                if (_account.Type == AccountType.StorageFacility)
                    Page.ClientScript.RegisterStartupScript(this.GetType(), "script", "impounds_changePg('" + idt + "')", true);

                cbEmail.Checked = _account.InvoiceDeliverByEmail;
                cbFax.Checked = _account.InvoiceDeliverByFax;
                cbMail.Checked = _account.InvoiceDeliverByMail;

                #region Storage Rates

                if (_storageRate.MaximumCharge != null && _storageRate.MaximumCharge > 0)
                    txtMaximumCharges.Text = _storageRate.MaximumCharge.Value.ToString("#0.00");

                var storageItems = Extric.Towbook.RateItem.GetByCompanyId(_account.CompanyId, _account)
                    .Where(w => (w.IsStorageItem() || w.IsTieredStorageItem()) && !w.IsTimeBasedItem() && w.ParentRateItemId == 0)
                    .OrderByDescending(b => b.Name.Contains("Daily Impound Rate"))
                    .ThenBy(b => b.Name)
                    .Select(s => new ListItem(s.Name, s.RateItemId.ToString()))
                    .ToCollection();

                if (storageItems != null)
                    storageItems.Insert(0, new ListItem("No default specified", "0"));

                foreach (var li in storageItems)
                    ddlDefaultStorage.Items.Add(li);

                if (_account.DefaultStorageRateItemId.GetValueOrDefault() != 0)
                    ddlDefaultStorage.SelectedValue = _account.DefaultStorageRateItemId.ToString();

                ddlDefaultAssetBodyType.Items.AddRange(BodyType.GetByCompanyId(_account.CompanyId)
                    .Select(s => new ListItem(s.Name, s.Id.ToString())).ToArray());

                ddlDefaultBillToAccount.Items.Insert(0, new ListItem("None", "-1"));
                ddlDefaultBillToAccount.Items.AddRange(await Account.GetByCompanyAsync(await Company.GetByIdAsync(_account.CompanyId))
                    .Select(a => new ListItem(a.Company, a.Id.ToString()))
                    .ToArray());

                var defaultBillTo = AccountKeyValue.GetFirstValueOrNull(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "DefaultBillToAccountId");
                ddlDefaultBillToAccount.SelectedValue = defaultBillTo ?? "-1";

                key = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "DefaultAssetBodyTypeId").FirstOrDefault();
                if (key != null && key.Value != "0")
                    ddlDefaultAssetBodyType.SelectedValue = key.Value;
                else
                    ddlDefaultAssetBodyType.SelectedValue = "1";

                if (Global.CurrentUser.Company.HasFeature(Features.DispatchToSubcontractors))
                {
                    ddlSubcontractors.Items.Add(new ListItem("(None)", "0"));

                    if (Global.CurrentUser.Company.HasFeature(Features.DispatchToSubcontractors_SubcontractorRotation))
                        ddlSubcontractors.Items.Add(new ListItem("Use Automatic Subcontractor Rotation", "-9"));

                    if (Global.CurrentUser.Company.Id == 96242 ||
                        Global.CurrentUser.Company.Id == 101960 ||
                        Global.CurrentUser.Company.HasFeature(Features.DispatchToSubcontractors_SendtoAllProviders))
                        ddlSubcontractors.Items.Add(new ListItem("Send to all subcontractors", "-10"));

                    if (Global.CurrentUser.Company.HasFeature(Features.DispatchToSubcontractors_SendtoNearestProviders))
                        ddlSubcontractors.Items.Add(new ListItem("Send to nearest providers", "-11"));

                    ddlSubcontractors.Items.AddRange(await Account.GetByCompanyAsync(Company.GetById(_account.CompanyId),
                        AccountType.Subcontractor).Select(o => new ListItem(o.Company, o.Id.ToString())).ToArray());

                    ddlSubcontractors.SelectedValue = AccountKeyValue.GetFirstValueOrNull(
                        _account.CompanyId, _account.Id, Provider.Towbook.ProviderId,
                        "DefaultSubcontractorAccountId") ?? "0";

                    towbookEmailAddress.Text = AccountKeyValue.GetFirstValueOrNull(
                        _account.CompanyId, _account.Id, Provider.Towbook.ProviderId,
                        "DSC_SubcontractorAddress") ?? "";

                    txtMobileNumbers.Text = AccountKeyValue.GetFirstValueOrNull(
                        _account.CompanyId, _account.Id, Provider.Towbook.ProviderId,
                        "SubcontractorMobileNumbers") ?? "";
                }

                mcTowbookEmailAddress.Text = AccountKeyValue.GetFirstValueOrNull(
                    _account.CompanyId, _account.Id, Provider.Towbook.ProviderId,
                    "DSC_SenderAddress") ?? "";

                if (Global.CurrentUser.HasAccessToCompany(119800))
                {
                    towbookReferenceNumber.Text = AccountKeyValue.GetFirstValueOrNull(
                        _account.CompanyId, _account.Id, Provider.Towbook.ProviderId,
                        "CustomReferenceNumber") ?? "";
                }

                trCompanyOverride.Visible = false;
                if (WebGlobal.Companies.Count() > 1)
                {
                    trCompanyOverride.Visible = true;

                    foreach (var li in WebGlobal.Companies.OrderBy(o => o.Name))
                        ddlCompanyOverride.Items.Add(new ListItem(li.Name, li.Id.ToString()));

                    key = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "OverrideToSharedCompanyId").FirstOrDefault();
                    int overrideCompanyId = _account.CompanyId;
                    if (key != null && int.TryParse(key.Value, out overrideCompanyId))
                        ddlCompanyOverride.SelectedValue = key.Value;
                    else
                        ddlCompanyOverride.SelectedValue = overrideCompanyId.ToString();
                }

                #endregion

                #region Motor Club

                if (_masterAccount != null)
                {
                    if (providerNumber != null)
                    {
                        var pn = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "ProviderId").FirstOrDefault();

                        if (pn != null)
                            providerNumber.Text = pn.Value;
                    }

                    if (billingUsername != null)
                    {
                        var bu = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "McUsername").FirstOrDefault();

                        if (bu != null)
                            billingUsername.Text = bu.Value;
                    }

                    if (billingPassword != null)
                    {
                        var bp = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "McPassword").FirstOrDefault();

                        if (bp != null)
                            billingPassword.Text = bp.Value;
                    }

                    if (mcTaxId != null)
                    {
                        var bp = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "TaxId").FirstOrDefault();

                        if (bp != null)
                            taxId.Text = bp.Value;
                    }

                    if (mcLocationId != null)
                    {
                        var bp = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "LocationId").FirstOrDefault();

                        if (bp != null)
                            locationId.Text = bp.Value;
                    }
                }

                #endregion

                #region Private Property
                if (gateCode != null)
                {
                    gateCode.Text = AccountKeyValue.GetFirstValueOrNull(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "PropertyGateCode");
                }

                if (contractStartDate != null)
                {
                    contractStartDate.Text = AccountKeyValue.GetFirstValueOrNull(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "PropertyContractStartDate");
                }

                if (contractEndDate != null)
                {
                    contractEndDate.Text = AccountKeyValue.GetFirstValueOrNull(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "PropertyContractEndDate");
                }

                #endregion

                #region Required Fields 
                var allRuleSets = EntryValidationRuleSet.GetAllByCompanyIds(new[] { _account.CompanyId });
                
                _ruleSet = allRuleSets.FirstOrDefault(f => f.AccountId == _account.Id);

                if(_ruleSet == null)
                    _ruleSet = new EntryValidationRuleSet();

                
                var accountTypeDefaultRuleSet = allRuleSets.FirstOrDefault(x => x.AccountTypeId == (int)_account.Type && x.AccountId == null);
                var companyDefaultRuleSet = allRuleSets.FirstOrDefault(x => x.AccountId == null && x.AccountTypeId == null);

                if (accountTypeDefaultRuleSet == null)
                    accountTypeDefaultRuleSet = new EntryValidationRuleSet();
                if (companyDefaultRuleSet == null)
                    companyDefaultRuleSet = new EntryValidationRuleSet();

                SetEntryRuleDropDownListValues(ddlReqDriverContactName, companyDefaultRuleSet.RequireContactName, accountTypeDefaultRuleSet.RequireContactName, _ruleSet.RequireContactName);
                SetEntryRuleDropDownListValues(ddlReqDispatcherContactName, companyDefaultRuleSet.RequireContactName, accountTypeDefaultRuleSet.RequireContactName, _ruleSet.RequireContactName);

                SetEntryRuleDropDownListValues(ddlReqDriverContactPhone, companyDefaultRuleSet.RequireContactPhone, accountTypeDefaultRuleSet.RequireContactPhone, _ruleSet.RequireContactPhone);
                SetEntryRuleDropDownListValues(ddlReqDispatcherContactPhone, companyDefaultRuleSet.RequireContactPhone, accountTypeDefaultRuleSet.RequireContactPhone, _ruleSet.RequireContactPhone);

                SetEntryRuleDropDownListValues(ddlReqDriverPurchaseOrderNumber, companyDefaultRuleSet.RequirePurchaseOrderNumber, accountTypeDefaultRuleSet.RequirePurchaseOrderNumber, _ruleSet.RequirePurchaseOrderNumber);
                SetEntryRuleDropDownListValues(ddlReqDispatcherPurchaseOrderNumber, companyDefaultRuleSet.RequirePurchaseOrderNumber, accountTypeDefaultRuleSet.RequirePurchaseOrderNumber, _ruleSet.RequirePurchaseOrderNumber);

                SetEntryRuleDropDownListValues(ddlReqDriverInvoiceNumber, companyDefaultRuleSet.RequireInvoiceNumber, accountTypeDefaultRuleSet.RequireInvoiceNumber, _ruleSet.RequireInvoiceNumber);
                SetEntryRuleDropDownListValues(ddlReqDispatcherInvoiceNumber, companyDefaultRuleSet.RequireInvoiceNumber, accountTypeDefaultRuleSet.RequireInvoiceNumber, _ruleSet.RequireInvoiceNumber);

                SetEntryRuleDropDownListValues(ddlReqDriverCharges, companyDefaultRuleSet.RequireCharges, accountTypeDefaultRuleSet.RequireCharges, _ruleSet.RequireCharges);
                SetEntryRuleDropDownListValues(ddlReqDispatcherCharges, companyDefaultRuleSet.RequireCharges, accountTypeDefaultRuleSet.RequireCharges, _ruleSet.RequireCharges);

                SetEntryRuleDropDownListValues(ddlReqDriverPaidInFull, companyDefaultRuleSet.RequireInvoicePaid, accountTypeDefaultRuleSet.RequireInvoicePaid, _ruleSet.RequireInvoicePaid);
                SetEntryRuleDropDownListValues(ddlReqDispatcherPaidInFull, companyDefaultRuleSet.RequireInvoicePaid, accountTypeDefaultRuleSet.RequireInvoicePaid, _ruleSet.RequireInvoicePaid);

                // Set the COD Required checkbox on RequireInvoicePaid
                var paidInFullRequired = GetEntryRuleDropDownRequirementType(ddlReqDriverPaidInFull, ddlReqDispatcherPaidInFull);
                cbCodAccount.Checked = paidInFullRequired == EntryValidationType.Required || paidInFullRequired == EntryValidationType.RequiredIncludingDispatchers;

                SetEntryRuleDropDownListValues(ddlReqDriverOverages, companyDefaultRuleSet.RequireOveragesPaid, accountTypeDefaultRuleSet.RequireOveragesPaid, _ruleSet.RequireOveragesPaid);
                SetEntryRuleDropDownListValues(ddlReqDispatcherOverages, companyDefaultRuleSet.RequireOveragesPaid, accountTypeDefaultRuleSet.RequireOveragesPaid, _ruleSet.RequireOveragesPaid);

                SetEntryRuleDropDownListValues(ddlReqDriverVin, companyDefaultRuleSet.RequireVin, accountTypeDefaultRuleSet.RequireVin, _ruleSet.RequireVin);
                SetEntryRuleDropDownListValues(ddlReqDispatcherVin, companyDefaultRuleSet.RequireVin, accountTypeDefaultRuleSet.RequireVin, _ruleSet.RequireVin);

                SetEntryRuleDropDownListValues(ddlReqDriverLicensePlate, companyDefaultRuleSet.RequirePlateNumber, accountTypeDefaultRuleSet.RequirePlateNumber, _ruleSet.RequirePlateNumber);
                SetEntryRuleDropDownListValues(ddlReqDispatcherLicensePlate, companyDefaultRuleSet.RequirePlateNumber, accountTypeDefaultRuleSet.RequirePlateNumber, _ruleSet.RequirePlateNumber);

                SetEntryRuleDropDownListValues(ddlReqDriverOdometer, companyDefaultRuleSet.RequireOdometer, accountTypeDefaultRuleSet.RequireOdometer, _ruleSet.RequireOdometer);
                SetEntryRuleDropDownListValues(ddlReqDispatcherOdometer, companyDefaultRuleSet.RequireOdometer, accountTypeDefaultRuleSet.RequireOdometer, _ruleSet.RequireOdometer);

                SetEntryRuleDropDownListValues(ddlReqDriverPhotos, companyDefaultRuleSet.RequirePhotos, accountTypeDefaultRuleSet.RequirePhotos, _ruleSet.RequirePhotos);
                SetEntryRuleDropDownListValues(ddlReqDispatcherPhotos, companyDefaultRuleSet.RequirePhotos, accountTypeDefaultRuleSet.RequirePhotos, _ruleSet.RequirePhotos);

                SetEntryRuleDropDownListValues(ddlReqDriverSignature, companyDefaultRuleSet.RequireSignature, accountTypeDefaultRuleSet.RequireSignature, _ruleSet.RequireSignature);
                SetEntryRuleDropDownListValues(ddlReqDispatcherSignature, companyDefaultRuleSet.RequireSignature, accountTypeDefaultRuleSet.RequireSignature, _ruleSet.RequireSignature);

                SetEntryRuleDropDownListValues(ddlReqDriverDamageForm, companyDefaultRuleSet.RequireDamageForm, accountTypeDefaultRuleSet.RequireDamageForm, _ruleSet.RequireDamageForm);
                SetEntryRuleDropDownListValues(ddlReqDispatcherDamageForm, companyDefaultRuleSet.RequireDamageForm, accountTypeDefaultRuleSet.RequireDamageForm, _ruleSet.RequireDamageForm);

                SetEntryRuleDropDownListValues(ddlReqDriverReason, companyDefaultRuleSet.RequireReason, accountTypeDefaultRuleSet.RequireReason, _ruleSet.RequireReason);
                SetEntryRuleDropDownListValues(ddlReqDispatcherReason, companyDefaultRuleSet.RequireReason, accountTypeDefaultRuleSet.RequireReason, _ruleSet.RequireReason);

                SetEntryRuleDropDownListValues(ddlReqDriverVehicleDestination, companyDefaultRuleSet.RequireVehicleDestination, accountTypeDefaultRuleSet.RequireVehicleDestination, _ruleSet.RequireVehicleDestination);
                SetEntryRuleDropDownListValues(ddlReqDispatcherVehicleDestination, companyDefaultRuleSet.RequireVehicleDestination, accountTypeDefaultRuleSet.RequireVehicleDestination, _ruleSet.RequireVehicleDestination);

                SetEntryRuleDropDownListValues(ddlReqDriverVehicleYear, companyDefaultRuleSet.RequireVehicleYear, accountTypeDefaultRuleSet.RequireVehicleYear, _ruleSet.RequireVehicleYear);
                SetEntryRuleDropDownListValues(ddlReqDispatcherVehicleYear, companyDefaultRuleSet.RequireVehicleYear, accountTypeDefaultRuleSet.RequireVehicleYear, _ruleSet.RequireVehicleYear);

                SetEntryRuleDropDownListValues(ddlReqDriverVehicleMake, companyDefaultRuleSet.RequireVehicleMake, accountTypeDefaultRuleSet.RequireVehicleMake, _ruleSet.RequireVehicleMake);
                SetEntryRuleDropDownListValues(ddlReqDispatcherVehicleMake, companyDefaultRuleSet.RequireVehicleMake, accountTypeDefaultRuleSet.RequireVehicleMake, _ruleSet.RequireVehicleMake);

                SetEntryRuleDropDownListValues(ddlReqDriverVehicleModel, companyDefaultRuleSet.RequireVehicleModel, accountTypeDefaultRuleSet.RequireVehicleModel, _ruleSet.RequireVehicleModel);
                SetEntryRuleDropDownListValues(ddlReqDispatcherVehicleModel, companyDefaultRuleSet.RequireVehicleModel, accountTypeDefaultRuleSet.RequireVehicleModel, _ruleSet.RequireVehicleModel);

                SetEntryRuleDropDownListValues(ddlReqDriverVehicleColor, companyDefaultRuleSet.RequireVehicleColor, accountTypeDefaultRuleSet.RequireVehicleColor, _ruleSet.RequireVehicleColor);
                SetEntryRuleDropDownListValues(ddlReqDispatcherVehicleColor, companyDefaultRuleSet.RequireVehicleColor, accountTypeDefaultRuleSet.RequireVehicleColor, _ruleSet.RequireVehicleColor);

                SetEntryRuleDropDownListValues(ddlReqDriverVehicleKeysLocation, companyDefaultRuleSet.RequireVehicleKeysLocation, accountTypeDefaultRuleSet.RequireVehicleKeysLocation, _ruleSet.RequireVehicleKeysLocation);
                SetEntryRuleDropDownListValues(ddlReqDispatcherVehicleKeysLocation, companyDefaultRuleSet.RequireVehicleKeysLocation, accountTypeDefaultRuleSet.RequireVehicleKeysLocation, _ruleSet.RequireVehicleKeysLocation);

                #endregion

                #region Stickering
                _gracePeriodStartItems = new Collection<ListItem>();
                foreach (Extric.Towbook.Stickering.GracePeriodStartFromType r in Enum.GetValues(typeof(Extric.Towbook.Stickering.GracePeriodStartFromType)))
                {
                    string name = null;
                    int id = (int)r;
                    switch (r)
                    {
                        case Extric.Towbook.Stickering.GracePeriodStartFromType.ActualTime:
                            name = "Actual Time";
                            break;

                        case Extric.Towbook.Stickering.GracePeriodStartFromType.CalendarDayMidnight:
                            name = "Calendar Day Midnight";
                            break;

                        case Extric.Towbook.Stickering.GracePeriodStartFromType.FollowingMidnight:
                            name = "Following Midnight";
                            break;
                    }

                    if (name == null)
                        continue;


                    ListItem item = new ListItem(name, id.ToString());
                    _gracePeriodStartItems.Add(item);
                }

                _booleanItems = new Collection<ListItem>();
                _booleanItems.Add(new ListItem("No", "0"));
                _booleanItems.Add(new ListItem("Yes", "1"));

                //ddlPropertyApproval.DataSource = _booleanItems;
                //ddlPropertyApproval.DataBind();

                ddlManagerApproval.DataSource = _booleanItems;
                ddlManagerApproval.DataBind();

                ddlManagerSignatureRequired.DataSource = _booleanItems;
                ddlManagerSignatureRequired.DataBind();

                ddlAllowExtensions.DataSource = _booleanItems;
                ddlAllowExtensions.DataBind();

                ddlSessionActivityEmail.DataSource = _booleanItems;
                ddlSessionActivityEmail.DataBind();

                ddlDailySummaryEmail.DataSource = _booleanItems;
                ddlDailySummaryEmail.DataBind();

                if (Global.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.Stickering))
                {
                    var companyGracePeriodSetting = new Extric.Towbook.Stickering.GracePeriodSetting();
                    var accountGracePeriodSetting = new Extric.Towbook.Stickering.GracePeriodSetting();

                    decimal defaultHours = 24;
                    var reasonTimes = Extric.Towbook.Stickering.ReasonTime.GetByAccountId(_id).ToList();
                    var stickeringReasons = Extric.Towbook.Stickering.Reason.GetByCompanyId(_account.CompanyId).OrderBy(a => a.Name);
                    var stickerSetting = Extric.Towbook.Stickering.StickerSetting.GetByAccountId(_account.CompanyId, _id);
                    
                    companyGracePeriodSetting = Extric.Towbook.Stickering.GracePeriodSetting.GetByCompanyId(_account.CompanyId, null);
                    accountGracePeriodSetting = Extric.Towbook.Stickering.GracePeriodSetting.GetByCompanyId(_account.CompanyId, _account.Id);

                    if (accountGracePeriodSetting != null)
                        defaultHours = accountGracePeriodSetting.Hours.Value;
                    else if (companyGracePeriodSetting != null)
                        defaultHours = companyGracePeriodSetting.Hours.Value;

                    foreach (Extric.Towbook.Stickering.Reason reason in stickeringReasons)
                    {
                        bool selected = reasonTimes.Count() == 0;
                        Extric.Towbook.Stickering.ReasonTime rt = null;
                        foreach (var item in _account.Reasons)
                        {
                            if (item.Id == reason.Id)
                            {
                                rt = reasonTimes.Where(w => w.StickerReasonId == item.Id).FirstOrDefault();
                                selected = true;
                                break;
                            }
                        }

                        var rItem = new StickeringReasonTimeItem();
                        rItem.Id = reason.Id;
                        rItem.Name = reason.Name;
                        rItem.Selected = selected;
                        rItem.DefaultHours = defaultHours;
                        if (rt != null)
                        {
                            if (rt.Time != null)
                                rItem.Hours = rt.Time.Value.ToString("G29");
                            else
                                rItem.Hours = "";
                        }


                        int rst = _account.GetReasonStartType(reason.Id);
                        rItem.StartFromType = Math.Max(rst, 0);

                        rpReasonItems.Add(rItem);
                    }

                    if (accountGracePeriodSetting != null && companyGracePeriodSetting != null &&
                        accountGracePeriodSetting.Hours != companyGracePeriodSetting.Hours)
                    {
                        tbWaitTime.Text = String.Format("{0:0.##}", accountGracePeriodSetting.Hours.Value);
                        tbWaitTime.Attributes.Add("placeholder", String.Format("{0:0.##}", companyGracePeriodSetting.Hours.Value));
                    }
                    else if (accountGracePeriodSetting != null)
                    {
                        tbWaitTime.Text = String.Format("{0:0.##}", accountGracePeriodSetting.Hours);
                        tbWaitTime.Attributes.Add("placeholder", String.Format("{0:0.##}", defaultHours));
                    }
                    else
                        tbWaitTime.Attributes.Add("placeholder", String.Format("{0:0.##}", defaultHours));

                    var cgs = Extric.Towbook.Stickering.StickerSetting.GetByCompanyId(_account.CompanyId, null);
                    var ags = Extric.Towbook.Stickering.StickerSetting.GetByAccountId(_account.CompanyId, _account.Id);

                    // Property Approval
                    int propertyApprovalValue = 1;
                    if (ags != null)
                        propertyApprovalValue = ags.PropertyApprovalRequired == true ? 1 : 0;
                    else if (cgs != null)
                        propertyApprovalValue = cgs.PropertyApprovalRequired == true ? 1 : 0;

                    //ddlPropertyApproval.SelectedValue = propertyApprovalValue.ToString();

                    // Tow manager approval
                    int managerApprovalValue = 1;
                    if (ags != null)
                        managerApprovalValue = ags.TowManagerApprovalRequired == true ? 1 : 0;
                    else if (cgs != null)
                        managerApprovalValue = cgs.TowManagerApprovalRequired == true ? 1 : 0;

                    ddlManagerApproval.SelectedValue = managerApprovalValue.ToString();



                    // Approval signature required
                    if (Global.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.UserSignatures))
                    {
                        int managerSignatureValue = 0;
                        if (ags != null)
                            managerSignatureValue = ags.RequireApprovalSignature == true ? 1 : 0;
                        else if (cgs != null)
                            managerSignatureValue = cgs.RequireApprovalSignature == true ? 1 : 0;

                        ddlManagerSignatureRequired.SelectedValue = managerSignatureValue.ToString();
                    }

                    // Allow Extensions
                    int allowExtension = 0;
                    if (ags != null)
                        allowExtension = ags.AllowExtensions == true ? 1 : 0;
                    else if (cgs != null)
                        allowExtension = cgs.AllowExtensions == true ? 1 : 0;

                    ddlAllowExtensions.SelectedValue = allowExtension.ToString();

                    // notification emails
                    int sendActivityEmail = 1;
                    int sendDailyEmail = 1;
                    if (ags != null)
                    {
                        sendActivityEmail = ags.SendActivitySessionEmail == true ? 1 : 0;
                        sendDailyEmail = ags.SendDailySummaryEmail == true ? 1 : 0;
                    }
                    else if (cgs != null)
                    {
                        sendActivityEmail = cgs.SendActivitySessionEmail == true ? 1 : 0;
                        sendDailyEmail = cgs.SendDailySummaryEmail == true ? 1 : 0;
                    }

                    ddlSessionActivityEmail.SelectedValue = sendActivityEmail.ToString();
                    ddlDailySummaryEmail.SelectedValue = sendDailyEmail.ToString();


                    // Sticker Expiration
                    if (ags != null && ags.ExpirationHours != null)
                        tbExpirationTime.Text = String.Format("{0:0.##}", ags.ExpirationHours.Value);
                    else if (cgs != null && cgs.ExpirationHours != null)
                        tbExpirationTime.Attributes.Add("placeholder", String.Format("{0:0.##}", cgs.ExpirationHours.Value));
                    else
                        tbExpirationTime.Attributes.Add("placeholder", "never expires");


                    rpStickerReasons.DataSource = rpReasonItems;
                    rpStickerReasons.DataBind();
                }
                #endregion

                #region Permits
                if (Global.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.ParkingPermits))
                {
                    var cppSettings = Extric.Towbook.Accounts.ParkingPermitSetting.GetByCompanyId(_account.CompanyId, null);
                    var appSettings = Extric.Towbook.Accounts.ParkingPermitSetting.GetByCompanyId(_account.CompanyId, _account.Id);

                    // parking permit public link
                    if (appSettings != null && appSettings.ParkingPermitPublicLinkId != null)
                    {
                        _parkingPermitPublicLink = ParkingPermitPublicLink.GetById(appSettings.ParkingPermitPublicLinkId.Value);
                        if (_parkingPermitPublicLink != null)
                        {
                            tbPropertyCode.Text = _parkingPermitPublicLink.PropertyCode;
                            hiddenPropertyCode.Value = _parkingPermitPublicLink.PropertyCode;
                        }
                    }

                    // Number of parking spaces
                    if (appSettings != null && cppSettings != null &&
                        appSettings.PermitsPerResident != cppSettings.PermitsPerResident)
                    {
                        tbNumberOfParkingSpaces.Text = appSettings.PermitsPerResident.ToString();
                        tbNumberOfParkingSpaces.Attributes.Add("placeholder", cppSettings.PermitsPerResident.ToString());

                    }
                    else if (cppSettings != null)
                    {
                        tbNumberOfParkingSpaces.Attributes.Add("placeholder", cppSettings.PermitsPerResident.ToString());
                        if (appSettings != null)
                            tbNumberOfParkingSpaces.Text = appSettings.PermitsPerResident.ToString();
                    }

                    // Number of guest permits per resident
                    if (appSettings != null && appSettings.GuestPermitsPerResident != 0)
                    {
                        tbNumberOfGuests.Text = appSettings.GuestPermitsPerResident.ToString();
                    }
                    else
                        tbNumberOfGuests.Attributes.Add("placeholder", "0");

                    // Expiration Type
                    if (appSettings != null)
                    {
                        ddlExpirationType.SelectedValue = ((int)appSettings.ExpirationType).ToString();
                    }

                    // Guest expiration days
                    if (appSettings != null && appSettings.GuestExpirationDays != null)
                    {
                        ddlGuestExpirationDays.SelectedValue = ((int)appSettings.GuestExpirationDays.Value).ToString();
                    }

                    // Guest require vehicle
                    if (appSettings != null)
                    {
                        if (appSettings.RequireGuestVehicleInfo == null)
                            ddlRequireGuestVehicle.SelectedValue = "1";
                        else
                            ddlRequireGuestVehicle.SelectedValue = ((int)appSettings.RequireGuestVehicleInfo.Value).ToString();
                    }

                    // Guest require vehicle
                    if (appSettings != null)
                    {
                        if (appSettings.AllowGuestPassUpdate == null)
                            ddlAllowGuestPassUpdate.SelectedValue = "true";
                        else
                            ddlAllowGuestPassUpdate.SelectedValue = appSettings.AllowGuestPassUpdate.Value == true ? "true" : "false";
                    }

                    // Require Contact Email
                    if (appSettings != null)
                        permitContactEmail.Checked = appSettings.RequireEmail;
                    else if (cppSettings != null)
                        permitContactEmail.Checked = cppSettings.RequireEmail;

                    // Require Contact Address
                    if (appSettings != null)
                        permitContactAddress.Checked = appSettings.RequireAddress;
                    else if (cppSettings != null)
                        permitContactAddress.Checked = cppSettings.RequireAddress;

                    // Require Vehicle Color
                    if (appSettings != null)
                        permitVehicleColor.Checked = appSettings.RequireColor;
                    else if (cppSettings != null)
                        permitVehicleColor.Checked = cppSettings.RequireColor;

                    // Require Vehicle Vin
                    if (appSettings != null)
                        permitVehicleVin.Checked = appSettings.RequireVin;
                    else if (cppSettings != null)
                        permitVehicleVin.Checked = cppSettings.RequireVin;

                    // Require State Registration number
                    if (appSettings != null)
                        permitStateRegistration.Checked = appSettings.RequireVehicleRegistration;
                    else if (cppSettings != null)
                        permitStateRegistration.Checked = cppSettings.RequireVehicleRegistration;

                    // Require State Registration expriation
                    if (appSettings != null)
                        permitStateRegistrationExpiration.Checked = appSettings.RequireVehicleRegistrationExpiration;
                    else if (cppSettings != null)
                        permitStateRegistrationExpiration.Checked = cppSettings.RequireVehicleRegistrationExpiration;

                    // disclaimer
                    var cDisclaimer = Extric.Towbook.Accounts.ParkingPermitDisclaimer.GetByCompanyId(_account.CompanyId, null);
                    var aDisclaimer = Extric.Towbook.Accounts.ParkingPermitDisclaimer.GetByCompanyId(_account.CompanyId, _account.Id);

                    if (aDisclaimer != null && aDisclaimer.AccountId != null)
                        tbDisclaimer.Text = Core.HtmlEncode(aDisclaimer.Content);

                    if (cDisclaimer != null)
                        tbDisclaimer.Attributes.Add("placeholder", Core.HtmlEncode(cDisclaimer.Content));
                }
                #endregion

                #region Account Disclaimer
                var accountDisclaimer = InvoiceDisclaimer.GetDefaultByCompanyId(_account.CompanyId, _account.Id, false);

                if (accountDisclaimer != null && accountDisclaimer.AccountId != null && accountDisclaimer.AccountId == _account.Id)
                    tbAccountDisclaimer.Text = accountDisclaimer.Disclaimer;

                // set placeholder to company default
                var defaultCompanyDisclaimer = InvoiceDisclaimer.GetDefaultByCompanyId(_account.CompanyId, null, false);
                if(defaultCompanyDisclaimer != null)
                    tbAccountDisclaimer.Attributes.Add("placeholder", defaultCompanyDisclaimer.Disclaimer);
                
                var defaultStatementDisclaimer = StatementDisclaimer.GetByCompanyId(_account.CompanyId, null);
                var statementDisclaimer = StatementDisclaimer.GetByCompanyId(_account.CompanyId, _account.Id);

                if(defaultStatementDisclaimer != null && !string.IsNullOrEmpty(defaultStatementDisclaimer.Disclaimer))
                    tbStatementDisclaimer.Attributes.Add("placeholder", Core.HtmlEncode(defaultStatementDisclaimer.Disclaimer));
                else
                    tbStatementDisclaimer.Attributes.Add("placeholder", "Enter a statement disclaimer for this account");

                if (statementDisclaimer != null && 
                    !string.IsNullOrEmpty(statementDisclaimer.Disclaimer) && 
                    statementDisclaimer.AccountId == _account.Id)
                    tbStatementDisclaimer.Text = Core.HtmlEncode(statementDisclaimer.Disclaimer);
                

                #endregion

                #region default statement due date 

                var dueDateItems = new Collection<ListItem>();
                foreach (var item in EnumExtensionMethods.Enum<DueDateDefaultType>.GetAllValuesAsIEnumerable()
                    .Select(s => new EnumExtensionMethods.EnumTypeDefinitionModel(s)))
                {
                    var li = new ListItem(item.Name, item.Id.ToString());
                    dueDateItems.Add(li);
                }

                ddlStatementDueDate.DataTextField = "Text";
                ddlStatementDueDate.DataValueField = "Value";
                ddlStatementDueDate.DataSource = dueDateItems;
                ddlStatementDueDate.DataBind();

                var statementOption = StatementOption.GetByCompanyId(_account.CompanyId, _account.Id);
                if (statementOption != null && statementOption.DefaultDueDateType != DueDateDefaultType.Net30)
                    ddlStatementDueDate.SelectedValue = ((int)statementOption.DefaultDueDateType).ToString();
                else
                    ddlStatementDueDate.SelectedValue = ((int)DueDateDefaultType.Net30).ToString();

                #endregion

                #region statement billing options
                var billingMethods = new Collection<ListItem>();
                foreach (var item in EnumExtensionMethods.Enum<Extric.Towbook.Accounts.Account.PreferredBillableMethod>.GetAllValuesAsIEnumerable()
                    .Where(w => w != Extric.Towbook.Accounts.Account.PreferredBillableMethod.Default)
                    .Select(s => new EnumExtensionMethods.EnumTypeDefinitionModel(s)))
                {
                    var li = new ListItem(item.Name, item.Id.ToString());
                    billingMethods.Add(li);
                }

                ddlBillingMethod.DataTextField = "Text";
                ddlBillingMethod.DataValueField = "Value";
                ddlBillingMethod.DataSource = billingMethods;
                ddlBillingMethod.DataBind();

                var pbm = AccountKeyValue.GetFirstValueOrNull(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "PreferredBillingMethod");
                Account.PreferredBillableMethod epbm = Account.PreferredBillableMethod.Default;
                if (pbm != null && Enum.TryParse(pbm, out epbm))
                {
                    ddlBillingMethod.SelectedValue = ((int)epbm).ToString();
                }
                else
                {
                    if (new[] {
                        AccountType.MotorClub,
                        AccountType.PrivateProperty,
                        AccountType.PoliceDepartment,
                        AccountType.Individual
                    }.Contains(_account.Type) || _account.Id == 1)
                        ddlBillingMethod.SelectedValue = ((int)Account.PreferredBillableMethod.Invoice).ToString();
                    else
                        ddlBillingMethod.SelectedValue = ((int)Account.PreferredBillableMethod.Statement).ToString();
                }

                var psdm = AccountKeyValue.GetFirstValueOrNull(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "PreferredStatementDeliveryMethod");

                if (psdm == null || psdm == "0")
                {
                    cbPrintedDelivery.Checked = true;
                    cbEmailedDelivery.Checked = false;
                }
                else if (psdm == "1")
                {
                    cbPrintedDelivery.Checked = false;
                    if(!string.IsNullOrEmpty(_account.Email))
                        cbEmailedDelivery.Checked = true;
                }
                else if (psdm == "2")
                {
                    cbPrintedDelivery.Checked = true;
                    if(!string.IsNullOrEmpty(_account.Email))
                        cbEmailedDelivery.Checked = true;
                }
                else if (psdm == "3")
                {
                    cbPrintedDelivery.Checked = false;
                    cbEmailedDelivery.Checked = false;
                }

                #endregion

                #region Email Default text
                if (Global.CurrentUser.Company.HasFeature(Features.AdvancedBilling))
                {
                    var dieo = InvoiceEmailOption.GetByCompanyId(_account.CompanyId);
                    var ieo = InvoiceEmailOption.GetByCompanyId(_account.CompanyId, _account.Id);

                    tbInvoiceSubject.Attributes.Add("placeholder", dieo != null && !string.IsNullOrEmpty(dieo.Subject) ? Core.HtmlEncode(dieo.Subject) : "Enter a subject");
                    tbInvoiceMessage.Attributes.Add("placeholder", dieo != null && !string.IsNullOrEmpty(dieo.Message) ? Core.HtmlEncode(dieo.Message) : "Enter a message");

                    tbInvoiceSubject.Text = ieo != null && ieo.AccountId == _account.Id && !string.IsNullOrEmpty(ieo.Subject) ? Core.HtmlEncode(ieo.Subject) : string.Empty;
                    tbInvoiceMessage.Text = ieo != null && ieo.AccountId == _account.Id && !string.IsNullOrEmpty(ieo.Message) ? Core.HtmlEncode(ieo.Message) : string.Empty;

                    var dseo = StatementEmailOption.GetByCompanyId(_account.CompanyId);
                    var seo = StatementEmailOption.GetByCompanyId(_account.CompanyId, _account.Id);

                    tbStatementSubject.Attributes.Add("placeholder", dseo != null && !string.IsNullOrEmpty(dseo.Subject) ? Core.HtmlEncode(dseo.Subject) : "Enter a subject");
                    tbStatementMessage.Attributes.Add("placeholder", dseo != null && !string.IsNullOrEmpty(dseo.Message) ? Core.HtmlEncode(dseo.Message) : "Enter a message");

                    tbStatementSubject.Text = seo != null && seo.AccountId == _account.Id && !string.IsNullOrEmpty(seo.Subject) ? Core.HtmlEncode(seo.Subject) : string.Empty;
                    tbStatementMessage.Text = seo != null && seo.AccountId == _account.Id && !string.IsNullOrEmpty(seo.Message) ? Core.HtmlEncode(seo.Message) : string.Empty;
                }
                // Only set the placeholder text if the accountDisclaimer returned is for the company not any specific account
                if (accountDisclaimer != null && accountDisclaimer.AccountId == null)
                    tbAccountDisclaimer.Attributes.Add("placeholder", accountDisclaimer.Disclaimer);
                #endregion

                #region Square payment links
                if (WebGlobal.CurrentUser.Company.HasFeature(Features.PaymentIntegrations_Square))
                {
                    var authorization = await Extric.Towbook.API.Integration.Square.SquareUtils.GetAuthorizationAsync(Global.CurrentUser.Company.Id);
                    if (authorization != null)
                    {
                        squarePaymentsEnabled = true; // square is connected and authorized
                    }
                }
                #endregion

                rpUsers.DataSource = Extric.Towbook.User.GetByAccountId(_account.Id);
                rpUsers.DataBind();

                // Get opening balance
                var openingBalance = 0m;
                var invoices = await Invoice.GetByAccountIdForEntrylessInvoicesAsync(_account.Id, new int[] { _account.CompanyId } );
                var ii = (await InvoiceItem.GetByInvoiceIdAsync(invoices.Select(i => i.Id).ToArray()))
                    .Where(i => i.CustomName == "Balance Forward" && i.Quantity == 1).FirstOrDefault();

                if (ii != null)
                {
                    hdFoundOpeningBalance.Value = "True";
                    hdFoundOpeningBalanceInvoiceItemId.Value = ii.Id.ToString();
                    openingBalance += ii.CustomPrice ?? 0;
                }
                txtOpeningBalance.Text = openingBalance.ToString("N2");
                
            }
        }
       

        if (!IsPostBack && Global.CurrentUser.Company.HasFeature(Features.Accounts_AccountManagers))
        {
            var users = Extric.Towbook.User.GetByCompanyId(_account.CompanyId);

            ddlAccountManager.Items.Add(new ListItem("Choose a manager", "0"));

            foreach (var li in users.Where(w => !w.Disabled).OrderBy(o => o.FullName))
                ddlAccountManager.Items.Add(new ListItem(li.FullName, li.Id.ToString()));

            var accountManagerUser = AccountManager.GetByAccountId(_account.Id);
            if (accountManagerUser != null && users.Any(a => a.Id == accountManagerUser.UserId))
            {
                ddlAccountManager.SelectedValue = accountManagerUser.UserId.ToString();
            }

        }

        RateItemsJson = Extric.Towbook.RateItem.GetByCompanyId(_account.CompanyId)
            .Select(o => new { Name = o.Name, Id = o.RateItemId, o.CategoryId, PredefinedRateItemId = o.Predefined != null ? o.Predefined.Id : -1 })
            .ToJson();
        ReasonsJson = (await Extric.Towbook.Dispatch.Reason.GetByCompany(Company.GetById(_account.CompanyId)))
            .Select(o => new { Name = o.Name, Id = o.Id, Active = o.IsActive })
            .ToJson();
        BodyTypesJson = BodyType.GetByCompanyId(_account.CompanyId).Select(o => new { Name = o.Name, Id = o.Id }).ToJson();
    }

    protected void btnCancel_Click(object sender, EventArgs e)
    {
        Response.Redirect("Account.aspx?id=" + _id.ToString());
        return;
    }

    protected void btnDelete_Click(object sender, EventArgs e)
    {
        RegisterAsyncTask(new PageAsyncTask(btnDeleteClickAsync));
    }

    protected void btnUndelete_Click(object sender, EventArgs e)
    {
        RegisterAsyncTask(new PageAsyncTask(btnUndeleteClickAsync));
    }

    protected async Task btnDeleteClickAsync()
    {
        await _account.Delete(Global.CurrentUser);
        await DoCacheUpdateAsync(true);
        Response.Redirect("Default.aspx");
        return;
    }

    protected async Task btnUndeleteClickAsync()
    {
        await _account.Undelete(Global.CurrentUser);
        await DoCacheUpdateAsync();
        Response.Redirect("Default.aspx");
        return;
    }

    public async Task TryConnect()
    {
        var myEmail = Extric.Towbook.Integrations.Email.EmailAddress.GetPrimaryTowbookEmailAddress(_account.CompanyId);

        if (myEmail == null)
            return;

        // are we the subcontractor or the sender ?
        Extric.Towbook.Integrations.Email.EmailAddress ea = null;
        if (Core.IsEmailValid(mcTowbookEmailAddress.Text))
        {
            // sender 
            ea = Extric.Towbook.Integrations.Email.EmailAddress.GetByEmailAddress(mcTowbookEmailAddress.Text.Trim());
            if (ea == null)
                return;

            var akv = AccountKeyValue.GetByCompany(ea.CompanyId, Provider.Towbook.ProviderId, new string[]
                    { "DSC_SubcontractorAddress" }).FirstOrDefault(o => o.Value == myEmail);
            
            if (akv == null)
            {
                var sc = SharedCompany.GetByCompanyId(ea.CompanyId).FirstOrDefault();
                if (sc != null && sc.ShareAllAccounts)
                {
                    // look at parent instead of this one
                    akv = AccountKeyValue.GetByCompany(sc.CompanyId, Provider.Towbook.ProviderId, new string[]
                        { "DSC_SenderAddress" }).FirstOrDefault(o => o.Value == myEmail);
                }
            }

            if (akv != null)
            {
                int nCompanyId = ea.CompanyId;
                int nAccountId = akv.AccountId;
                var remoteAccount = await Account.GetByIdAsync(nAccountId);
                if (remoteAccount != null &&
                    remoteAccount.Type == AccountType.Subcontractor &&
                    _account.Type == AccountType.MotorClub)
                {
                    _account.MasterAccountId = MasterAccountTypes.Towbook;
                    _account.ReferenceNumber = remoteAccount.CompanyId.ToString();
                    await _account.Save();

                    remoteAccount.ReferenceNumber = _account.CompanyId + "|" + _account.Id;
                    remoteAccount.MasterAccountId = MasterAccountTypes.Towbook;
                    await remoteAccount.Save();
                }
            }

        }
        else if (Core.IsEmailValid(towbookEmailAddress.Text))
        {
            ea = Extric.Towbook.Integrations.Email.EmailAddress.GetByEmailAddress(towbookEmailAddress.Text.Trim());
            // we are the sender. 
            if (ea != null)
            {
                var akv = AccountKeyValue.GetByCompany(ea.CompanyId, Provider.Towbook.ProviderId, new string[]
                    { "DSC_SenderAddress" }).FirstOrDefault(o => o.Value == myEmail);

                if (akv != null)
                {
                    int nCompanyId = ea.CompanyId;
                    int nAccountId = akv.AccountId;
                    var remoteAccount = await Account.GetByIdAsync(nAccountId);
                    if (remoteAccount != null &&
                        remoteAccount.Type == AccountType.MotorClub &&
                        _account.Type == AccountType.Subcontractor)
                    { 
                        remoteAccount.MasterAccountId = MasterAccountTypes.Towbook;
                        remoteAccount.ReferenceNumber = _account.CompanyId.ToString();
                        await remoteAccount.Save();

                        _account.ReferenceNumber = remoteAccount.CompanyId + "|" + remoteAccount.Id;
                        _account.MasterAccountId = MasterAccountTypes.Towbook;
                        await _account.Save();
                    }
                }
            }
        }

    }


    protected void btnSave_Click(object sender, EventArgs e)
    {
        RegisterAsyncTask(new PageAsyncTask(btnSaveClickAsync));
    }

    protected async Task btnSaveClickAsync()
    {
        await PageLoadAsync();

        bool newAccount = _account.Id == -1;
        bool addressChanged = _account.Address != txtAddress.Text || _account.City != txtCity.Text ||
            _account.State != txtState.Text || _account.Zip != txtZip.Text;

        _account.Company = txtCompany.Text;
        _account.FullName = txtContactName.Text;
        _account.Address = txtAddress.Text;
        _account.City = txtCity.Text;
        _account.State = txtState.Text;
        _account.Zip = txtZip.Text;
        _account.Email = txtEmail.Text;
        _account.Phone = txtPhone.Text;
        _account.Fax = txtFax.Text;
        _account.TaxExempt = cbTaxExempt.Checked;
        _account.Notes = txtNotes.Text;

        if (_account.Type == AccountType.PrivateProperty && (addressChanged || _account.Latitude == null || _account.Longitude == null))
        {
            await _account.UpdateLocation();
        }

        if (ShowCreditOptions)
        {
            decimal cclimit = 0;

            if (decimal.TryParse(txtCreditLimit.Text, out cclimit))
                _account.CreditLimit = cclimit;

            _account.CreditHold = cbCreditHold.Checked;
        }

        _account.Status = (cbInactive.Checked == true ?
            AccountStatus.Inactive :
            AccountStatus.Active);

        #region Discount Rate
        try
        {
            if (!String.IsNullOrEmpty(discountRate.Text.Replace("%", "")))
                _account.DiscountRate = Convert.ToDecimal(discountRate.Text.Replace("%", "")) / 100;
            else
                _account.DiscountRate = 0;
        }
        catch (FormatException)
        {
            _account.DiscountRate = 0; // error occured.. should probably notify the user somehow.
        }
        #endregion

        _account.Type = (AccountType)Convert.ToInt32(dlType.SelectedValue);

        var aceStates = new string[] { "KY", "NY", "OH", "PA",
            "WV", "AR", "IL", "MO", "IN", "LA", "MS", "MO", "TX", 
            "ME", "NH", "VT", "VA", "NM", "HI", "AL", "CO", "KS",
            "CA", "MD" };

        if (_account.Company.ToLower().StartsWith("aaa") &&
            aceStates.Contains((WebGlobal.CurrentUser.Company.State ?? "").ToUpper()))
        {
            if (_account.MasterAccountId == 0)
            {
                _account.MasterAccountId = MasterAccountTypes.AaaAce;

            }
            _masterAccount = await MasterAccount.GetByIdAsync(_account.MasterAccountId);
        }
        if (_account.Company.ToLower().StartsWith("aaa northeast"))
        {
            _account.MasterAccountId = MasterAccountTypes.AaaNortheast;
            _masterAccount = await MasterAccount.GetByIdAsync(_account.MasterAccountId);
        }
        if (_account.Company.ToLower().StartsWith("aaa aca"))
        {
            _account.MasterAccountId = 49;
            _masterAccount = await MasterAccount.GetByIdAsync(_account.MasterAccountId);
        }
        if (_account.Company.ToLower().StartsWith("aaa washington"))
        {
            _account.MasterAccountId = 50;
            _masterAccount = await MasterAccount.GetByIdAsync(_account.MasterAccountId);
        }
        if (_account.Company.ToLower().StartsWith("aaa wcny"))
        {
            _account.MasterAccountId = 51;
            _masterAccount = await MasterAccount.GetByIdAsync(_account.MasterAccountId);
        }


        if (_account.Company.ToLower().StartsWith("honk"))
        {
            _account.MasterAccountId = MasterAccountTypes.Honk;
        }

        if (rbPaymentImmediate.Checked == true)
        {
            _account.InvoiceTerms = Account.InvoiceTerm.Immediate;
        }
        else if (rbPaymentNet15.Checked == true)
        {
            _account.InvoiceTerms = Account.InvoiceTerm.Net15;
        }
        else if (rbPaymentNet30.Checked == true)
        {
            _account.InvoiceTerms = Account.InvoiceTerm.Net30;
        }

        if (rbStorageLotsNone.Checked)
        {
            _account.ImpoundDestinationType = AccountImpoundDestination.None;
            _account.ImpoundDestinationStorageLotId = Convert.ToInt32(Request.Form["dlImpoundLots_company"]);
        }
        else if (rbStorageLotsDefault.Checked)
        {
            _account.ImpoundDestinationType = AccountImpoundDestination.Default;
            _account.ImpoundDestinationStorageLotId = Convert.ToInt32(Request.Form["dlImpoundLots_company"]);
        }
        else if (rbStorageLotThirdParty.Checked)
        {
            _account.ImpoundDestinationType = AccountImpoundDestination.ThirdParty;
            _account.ImpoundDestinationStorageLotId = Convert.ToInt32(Request.Form["dlImpoundLots_other"]);
        }

        switch (_account.ImpoundDestinationType)
        {
            case AccountImpoundDestination.Default:
                rbStorageLotsDefault.Checked = true;
                break;
            case AccountImpoundDestination.ThirdParty:
                rbStorageLotThirdParty.Checked = true;
                break;
        }

        _account.InvoiceDeliverByEmail = cbEmail.Checked;
        _account.InvoiceDeliverByFax = cbFax.Checked;
        _account.InvoiceDeliverByMail = cbMail.Checked;
        _account.DefaultPriority = cbDefaultPriority.Checked ? 1 : 0;
        _account.DefaultPO = txtDefaultPO.Text;

        if (ddlDefaultStorage.SelectedValue == "0")
            _account.DefaultStorageRateItemId = null;
        else if ((ddlDefaultStorage.SelectedValue ?? "") != "")
            _account.DefaultStorageRateItemId = Convert.ToInt32(ddlDefaultStorage.SelectedValue);

        if (ddlDefaultBillToAccount.SelectedValue != "-1")
            SaveKeyValue(ddlDefaultBillToAccount.SelectedValue, "DefaultBillToAccountId");
        else
            DeleteKeyValue("DefaultBillToAccountId");

        await _account.Save(async () =>
        {

            var defaultSubcontractorAccountIdIsSet = false;
            if (Global.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.DispatchToSubcontractors_SubcontractorRotation))
            {
                var subs = RotationSubcontractor.GetByAccountId(_account.Id);

                List<KeyValuePair<int, int>> newIds = new List<KeyValuePair<int, int>>();

                foreach (string x in Request.Form.Keys)
                {
                    if (x.StartsWith("light_"))
                    {
                        newIds.Add(new KeyValuePair<int, int>(1, Convert.ToInt32(x.Replace("light_", ""))));
                    }
                    else if (x.StartsWith("heavy_"))
                    {
                        newIds.Add(new KeyValuePair<int, int>(3, Convert.ToInt32(x.Replace("heavy_", ""))));
                    }
                }

                foreach (var x in subs)
                {
                    if (!newIds.Any(o => o.Key == x.BodyTypeId && o.Value == x.SubcontractorAccountId))
                        x.Delete(WebGlobal.CurrentUser, WebGlobal.GetRequestingIp());
                    else
                    {
                        var existing = newIds.Where(o => o.Key == x.BodyTypeId && o.Value == x.SubcontractorAccountId).FirstOrDefault();
                        if (existing.Key != 0 && existing.Value != 0)
                            newIds.Remove(existing);
                    }
                }

                foreach (var kv in newIds)
                {
                    new RotationSubcontractor()
                    {
                        CompanyId = _account.CompanyId,
                        AccountId = _account.Id,
                        BodyTypeId = kv.Key,
                        SubcontractorAccountId = kv.Value,
                        OwnerUserId = WebGlobal.CurrentUser.Id
                    }.Save();
                }

                if (newIds.Any())
                {
                    SaveKeyValue("-9", "DefaultSubcontractorAccountId");
                    defaultSubcontractorAccountIdIsSet = true;
                }
            }

            #region Billing Address

            var billingAddress = await AddressBookEntry.GetByAccountId(_account.Id,
                AddressBookNameMapOrBilling);

            if (string.IsNullOrWhiteSpace(txtBillingAddress.Text) &&
                string.IsNullOrWhiteSpace(txtBillingCity.Text) &&
                string.IsNullOrWhiteSpace(txtBillingState.Text) &&
                string.IsNullOrWhiteSpace(txtBillingZip.Text) &&
                string.IsNullOrWhiteSpace(txtBillingContactName.Text) &&
                string.IsNullOrWhiteSpace(txtBillingContactEmail.Text))
            {
                if (billingAddress != null)
                    await billingAddress.Delete();
            }
            else
            {
                if (billingAddress == null)
                {
                    billingAddress = new AddressBookEntry();
                    billingAddress.Name = AddressBookNameMapOrBilling;
                    billingAddress.CompanyId = _account.CompanyId;
                    billingAddress.AccountId = _account.Id;
                }

                billingAddress.AccountId = _account.Id;
                billingAddress.Name = AddressBookNameMapOrBilling;
                billingAddress.Address = txtBillingAddress.Text;
                billingAddress.City = txtBillingCity.Text;
                billingAddress.State = txtBillingState.Text;
                billingAddress.Zip = txtBillingZip.Text;
                billingAddress.Email = txtBillingContactEmail.Text;
                if (!string.IsNullOrEmpty(txtBillingContactName.Text))
                    billingAddress.Notes = "RecipientDisplayName:" + txtBillingContactName.Text;
                else
                    billingAddress.Notes = string.Empty;

                await billingAddress.Save();
            }
            #endregion

            #region Tags

            if (Extric.Towbook.Company.Company.GetById(_account.CompanyId).HasFeature(Extric.Towbook.Generated.Features.AccountTags))
            {
                Collection<int> list = new Collection<int>();
                foreach (string x in Request.Form.Keys)
                {
                    if (x.StartsWith("tag_"))
                    {
                        int id = Convert.ToInt32(x.Substring(4));
                        list.Add(id);
                        _account.AddTag(id, Global.CurrentUser.Id);
                    }
                }

                // go through the newly created list of what's selected.
                foreach (AccountTag at in _account.Tags)
                {
                    if (!list.Contains(at.AccountTagId))
                        _account.DeleteTag(at.AccountTagId);
                }
            }

            #endregion

            #region Sticker
            if (!newAccount && _account.Type == AccountType.PrivateProperty)
                SaveStickering();
            #endregion

            #region Permits
            if (!newAccount)
                SavePermits();
            #endregion

            if (txtOpeningBalance.Text.Length > 0)
            {
                if (hdFoundOpeningBalance.Value == "True")
                {
                    // existing account....
                    // update the opening balance
                    var invoiceItem = InvoiceItem.GetById(Convert.ToInt32(hdFoundOpeningBalanceInvoiceItemId.Value));

                    decimal ob = 0;
                    if (decimal.TryParse(txtOpeningBalance.Text, out ob) && invoiceItem != null)
                    {
                        if (ob >= 0)
                        {
                            invoiceItem.CustomPrice = ob;

                            if (ob == 0)
                            {
                                invoiceItem.Quantity = 0;
                            }
                        }

                        // update entryless invoice for next account balance calcuation
                        var invoice = Invoice.GetById(invoiceItem.InvoiceId);
                        if (invoice != null)
                        {
                            var ii = invoice.InvoiceItems.FirstOrDefault(w => w.Id == invoiceItem.Id);
                            if (ii != null)
                            {
                                ii.CustomPrice = invoiceItem.CustomPrice;
                                ii.Quantity = invoiceItem.Quantity;

                                // workaround to get a recalculation of the invoice
                                // DispatchEntry is null, so ForceReclaculation() won't work.
                                var subTotal = invoice.Subtotal;

                                // save new total to the datastore
                                await invoice.SaveAsync(new AuthenticationToken()
                                {
                                    UserId = WebGlobal.CurrentUser.Id,
                                    ClientVersionId = Extric.Towbook.Platform.ClientVersion.GetByGitHash("web-app").Id
                                }, WebGlobal.GetRequestingIp());

                                // clear account balance cache
                                Account.CacheClearById(_account.Id, true);
                            }
                        }
                    }
                }
                else
                {
                    // new account....
                    // create a new invoice, and invoice item, to represent the opening balance


                    decimal ob = 0;
                    if (decimal.TryParse(txtOpeningBalance.Text, out ob))
                    {
                        if (ob > 0)
                        {
                            var inv = new Extric.Towbook.Invoice();
                            inv.AccountId = _account.Id;
                            inv.CompanyId = _account.CompanyId;

                            var invoiceItem = new Extric.Towbook.Dispatch.InvoiceItem();
                            invoiceItem.Quantity = 1;

                            invoiceItem.CustomPrice = ob;
                            invoiceItem.CustomName = "Balance Forward";
                            invoiceItem.InvoiceId = inv.Id;

                            inv.InvoiceItems.Add(invoiceItem);

                            await inv.SaveAsync(new AuthenticationToken()
                            {
                                UserId = WebGlobal.CurrentUser.Id,
                                ClientVersionId = Extric.Towbook.Platform.ClientVersion.GetByGitHash("web-app").Id
                            }, WebGlobal.GetRequestingIp());
                        }
                    }
                }
            }

            #region Fuel Surcharge

            SurchargeAccountRate sar = await SurchargeAccountRate.GetBySurchargeAsync(Surcharge.SURCHARGE_FUEL, _account.Id);

            if (fuelSurcharge.Text.Length == 0 && sar != null)
            {
                // Length is empty; It's NOT zero, so charge the default company rate. 
                await sar.Delete();
            }
            else if (fuelSurcharge.Text == "0")
            {
                if (sar == null)
                {
                    sar = new SurchargeAccountRate();
                    sar.AccountId = _account.Id;
                    sar.SurchargeId = Surcharge.SURCHARGE_FUEL;
                }
                sar.Rate = 0;
                await sar.Save();
            }
            else if (fuelSurcharge.Text.Length > 0)
            {
                if (sar == null)
                {
                    sar = new SurchargeAccountRate();
                    sar.AccountId = _account.Id;
                    sar.SurchargeId = Surcharge.SURCHARGE_FUEL;
                }
                sar.Rate = (Convert.ToDecimal(fuelSurcharge.Text.Replace("%", "")) / 100);
                await sar.Save();
            }

            #endregion

            #region Account Disclaimer

            var content = tbAccountDisclaimer.Text;
            var aDisclaimer = InvoiceDisclaimer.GetDefaultByCompanyId(_account.CompanyId, _account.Id, false);

            if (aDisclaimer == null || aDisclaimer.AccountId == null || aDisclaimer.AccountId != _account.Id)
                aDisclaimer = new InvoiceDisclaimer();

            if (string.IsNullOrEmpty(content))
            {
                if (!string.IsNullOrEmpty(aDisclaimer.Disclaimer))
                {
                    // Delete account version if user emptied text
                    await aDisclaimer.Delete();
                }
            }
            else
            {
                // save new content
                if (aDisclaimer.Disclaimer != content)
                {
                    aDisclaimer.AccountId = _account.Id;
                    aDisclaimer.CompanyId = _account.CompanyId;
                    aDisclaimer.Disclaimer = Core.HtmlEncode(content);
                    await aDisclaimer.Save();
                }
            }
            #endregion

            #region Statement Disclaimer
            content = tbStatementDisclaimer.Text;
            var sDisclaimer = StatementDisclaimer.GetByCompanyId(_account.CompanyId, _account.Id);

            if (sDisclaimer == null || sDisclaimer.AccountId == null)
                sDisclaimer = new StatementDisclaimer();

            if (string.IsNullOrEmpty(content))
            {
                if (!string.IsNullOrEmpty(sDisclaimer.Disclaimer) && sDisclaimer.AccountId == _account.Id)
                {
                    // Delete account version if user emptied text
                    await sDisclaimer.Delete();
                }
            }
            else
            {
                // save new content
                if (sDisclaimer.Disclaimer != content)
                {
                    sDisclaimer.AccountId = _account.Id;
                    sDisclaimer.CompanyId = _account.CompanyId;
                    sDisclaimer.Disclaimer = Core.HtmlEncode(content);
                    await sDisclaimer.Save();
                }
            }
            #endregion

            
            if (Global.CurrentUser.Company.HasFeature(Features.AdvancedBilling))
            {
                #region Statement due date

                var statementDefaultOption = StatementOption.GetByCompanyId(_account.CompanyId);
                var statementAccountDefaultOption = StatementOption.GetByCompanyId(_account.CompanyId, _account.Id);

                var ddv = DueDateDefaultType.Net30;
                if (ddlStatementDueDate.Items.Count > 0)
                    ddv = (DueDateDefaultType)Enum.Parse(typeof(DueDateDefaultType), ddlStatementDueDate.SelectedValue);

                if (statementAccountDefaultOption == null || statementAccountDefaultOption.AccountId == null)
                    statementAccountDefaultOption = new StatementOption();

                statementAccountDefaultOption.CompanyId = _account.CompanyId;
                statementAccountDefaultOption.AccountId = _account.Id;
                statementAccountDefaultOption.DefaultDueDateType = ddv;

                if ((statementAccountDefaultOption.StatementOptionsId == 0 && statementDefaultOption.DefaultDueDateType != ddv) || 
                    (statementAccountDefaultOption.StatementOptionsId > 0))
                {
                    statementAccountDefaultOption.Save();
                }

                #endregion

                #region Statement billing method and delivery

                var pbm = AccountKeyValue.GetFirstValueOrNull(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "PreferredBillingMethod");
                var defaultPreferredMethod = ((int)GetPreferredBillableMethod(_account)).ToString();
                if (ddlBillingMethod.SelectedValue != defaultPreferredMethod)
                    SaveKeyValue(ddlBillingMethod.SelectedValue, "PreferredBillingMethod");
                else
                    DeleteKeyValue("PreferredBillingMethod");

                var psdm = AccountKeyValue.GetFirstValueOrNull(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "PreferredStatementDeliveryMethod");
                if (cbPrintedDelivery.Checked && !cbEmailedDelivery.Checked && psdm != null)
                    DeleteKeyValue("PreferredStatementDeliveryMethod");

                if (txtEmail.Text.Length > 0)
                {
                    if (!cbPrintedDelivery.Checked && cbEmailedDelivery.Checked)
                        SaveKeyValue("1", "PreferredStatementDeliveryMethod");

                    if (cbPrintedDelivery.Checked && cbEmailedDelivery.Checked)
                        SaveKeyValue("2", "PreferredStatementDeliveryMethod");
                }

                if (!cbPrintedDelivery.Checked && !cbEmailedDelivery.Checked)
                    SaveKeyValue("3", "PreferredStatementDeliveryMethod");

                #endregion
            }


            #region Invoice and Statement Email preferences
            if (Global.CurrentUser.Company.HasFeature(Features.AdvancedBilling))
            {
                var ieo = InvoiceEmailOption.GetByCompanyId(_account.CompanyId, _account.Id);
                var seo = StatementEmailOption.GetByCompanyId(_account.CompanyId, _account.Id);

                if (ieo == null || ieo.AccountId == null)
                    ieo = new InvoiceEmailOption();

                if (seo == null || seo.AccountId == null)
                    seo = new StatementEmailOption();

                ieo.Subject = Core.HtmlEncode(tbInvoiceSubject.Text);
                ieo.Message = Core.HtmlEncode(tbInvoiceMessage.Text);
                if (string.IsNullOrEmpty(ieo.Subject) && string.IsNullOrEmpty(ieo.Message))
                {
                    if (ieo.Id > 0)
                    {
                        // Delete account version if user emptied text
                        ieo.Delete(Global.CurrentUser);
                    }
                }
                else
                {
                    ieo.CompanyId = _account.CompanyId;
                    ieo.AccountId = _account.Id;

                    ieo.Save(Global.CurrentUser);
                }

                seo.Subject = Core.HtmlEncode(tbStatementSubject.Text);
                seo.Message = Core.HtmlEncode(tbStatementMessage.Text);
                if (string.IsNullOrEmpty(seo.Subject) && string.IsNullOrEmpty(seo.Message))
                {
                    if (seo.Id > 0)
                    {
                        // Delete account version if user emptied text
                        seo.Delete(Global.CurrentUser);
                    }
                }
                else
                {
                    seo.CompanyId = _account.CompanyId;
                    seo.AccountId = _account.Id;

                    seo.Save(Global.CurrentUser);
                }
            }
            #endregion

            SaveStorageRates();

            SaveRequiredFields();

            #region Save KeyValues
            SaveKeyValue(cbAllowAccountUsersToViewFiles.Checked ? "1" : "0", "AllowAccountUsersToViewFiles");

            if (_account.MasterAccountId != 0)
            {
                if (_account.MasterAccountId == MasterAccountTypes.AaaAce ||
                    _account.MasterAccountId == MasterAccountTypes.AaaNortheast ||
                    _account.MasterAccountId == 49 ||
                    _account.MasterAccountId == 50 ||
                    _account.MasterAccountId == 51)
                {
                    SaveTextBoxKeyValue(providerNumber, "ProviderId");
                    int validProviderId = 0;
                    
                    if (int.TryParse(providerNumber.Text, out validProviderId) 
                        || _account.MasterAccountId == MasterAccountTypes.AaaNortheast
                        || _account.MasterAccountId == 49
                        || _account.MasterAccountId == 50
                        || _account.MasterAccountId == 51)
                    {

                        var prodKey = 3;

                        if (_account.MasterAccountId == 50 || _account.MasterAccountId == 51)
                            prodKey = 4;

                        var ct = Extric.Towbook.Integrations.MotorClubs.Aaa.AaaContractor.GetByContractorId(providerNumber.Text, _account.MasterAccountId, prodKey);
                        if (ct == null)
                        {
                            var acx = new Extric.Towbook.Integrations.MotorClubs.Aaa.AaaContractor()
                            {
                                CompanyId = _account.CompanyId,
                                AccountId = _account.Id,
                                EnvironmentId = 3,
                                MasterAccountId = _account.MasterAccountId,
                                ContractorId = providerNumber.Text,
                                IsValidated = false
                            };
                            if (_account.MasterAccountId == 50 || _account.MasterAccountId == 51)
                                acx.EnvironmentId = 4;

                            acx.Save();

                            var existingRateItems = Extric.Towbook.RateItem.GetByCompanyId(acx.CompanyId);

                            string[] namesToAdd = new string[] { "DL - DOLLIES",
                                "DS - DROP DRIVE SHAFT",
                                "FB - FLATBED",
                                "FD - Fuel Delivery Amount",
                                "FL - FUEL",
                                "FM - Field Manager Review",
                                "LS - LOCKSMITH AMOUNT",
                                "LT - LONG TOW",
                                "MA - Miscellaneous Amount",
                                "MD - MANAGEMENT DECISIONS",
                                "ML - MISCELLANEOUS",
                                "MT - Motorcycle Tow",
                                "MU - MEDIUM DUTY TOW",
                                "OM - ENROUTE MILES",
                                "PR - EXTRA PERSONNEL",
                                "PY - PRIORITY SERVICE",
                                "RV - RV CHARGES FOR RV EQUIPMENT",
                                "S1 - RS Rate",
                                "S2 - RSS Rate",
                                "SM - SCENE TIME MINUTES",
                                "ST - STORAGE",
                                "TL - TOLLS/PARKING FEE",
                                "TM - EXTRA TIME ON SCENE",
                                "TR - EXTRA TRUCK",
                                "TW - TOW MILEAGE" };

                            if (_account.MasterAccountId == 50)
                            {
                                namesToAdd = new string[] {
                                    "E1 - Extrication - 1st Truck",
                                    "E2 - Extrication - 2nd truck",
                                    "EM - Extra Tow Mileage",
                                    "GS - Fuel - Basic Service",
                                    "MH - Medium/Heavy Duty Vehicle Svc",
                                    "MI - Miscellaneous - AAA Approval Reqd",
                                    "PG - Plus/Premier Fuel",
                                    "TJ - TireJect",
                                    "TL - Tolls/Parking",
                                    "TR - Tire Repair"
                                };
                            }
                            else if (_account.MasterAccountId == 51)
                            {
                                namesToAdd = new string[] {
                                    "AC - Accident",
                                    "BI - Battery Install",
                                    "BW - Battery Warranty",
                                    "CH - Chains",
                                    "EK - Extra Truck",
                                    "EP - Extra Personnel",
                                    "ET - Extra Time",
                                    "FE - Ferry Ticket/Tolls",
                                    "FF - Ferry Flat Rate",
                                    "FL - Fuel",
                                    "FT - Ferry Time",
                                    "GT - Military Gate Time",
                                    "LK - Locksmith",
                                    "MR - MTCY Resp Miles",
                                    "MT - MTCY Tow Miles",
                                    "NT - Night Rate",
                                    "OA - Out of Area",
                                    "OB - Special Boundary",
                                    "OE - Special OOA",
                                    "RE - RV Extra Time",
                                    "RM - RV Resp Miles",
                                    "RT - Return Miles",
                                    "RV - RV Coverage",
                                    "TN - 1 Ton"
                                };
                            }

                            var newRates = new List<Extric.Towbook.RateItem>();
                            foreach (var name in namesToAdd)
                            {
                                if (existingRateItems.Any(o => o.Name == name))
                                    continue;

                                var newRateItem = new Extric.Towbook.RateItem();

                                newRateItem.CompanyId = acx.CompanyId;
                                newRateItem.Cost = 0;
                                newRateItem.Name = name;

                                await newRateItem.Save(null);
                                newRates.Add(newRateItem);
                            }
                            
                            await Extric.Towbook.Caching.CacheWorkerUtility.UpdateRateItems(newRates.Select(o => o.RateItemId).ToArray());
                        }
                    }
                }
                else
                {
                    SaveTextBoxKeyValue(providerNumber, "ProviderId");
                    SaveTextBoxKeyValue(taxId, "TaxId");
                    SaveTextBoxKeyValue(locationId, "LocationId");
                }

                SaveKeyValue(cbEnablePaymentImport.Checked ? "1" : "0", "ImportPayments");

                if (_masterAccount != null && 
			            (_masterAccount.Id == MasterAccountTypes.Agero ||
                    	_masterAccount.Id == MasterAccountTypes.Allstate ||
                    	_masterAccount.Id == MasterAccountTypes.Swoop ||
                    	_masterAccount.Id == MasterAccountTypes.Urgently))
                {
                    SaveKeyValue(cbPreventPhotoSharing.Checked ? "1" : "0", "PreventPhotoSharing");
                }

                if (AccountKeyValue.GetFirstValueOrNull(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "DigitalAutoAccept") != null)
                {
                    SaveKeyValue(cbDisableAutoAccept.Checked ? "1" : "0", "DigitalAutoAcceptDisabled");
                }

                if (ShowAutoAccept)
                {
                    if (Request.Form["autoAcceptJson"] != null)
                    {
                        var jsonValidate = Request.Form["autoAcceptJson"];

                        var deserialized = Newtonsoft.Json.JsonConvert.DeserializeObject<IEnumerable<AutoAcceptRule>>(jsonValidate);
                        SaveKeyValue(Request.Form["autoAcceptJson"], "DigitalAutoAccept");
                    }
                }
            }

            SaveTextBoxKeyValue(gateCode, "PropertyGateCode");
            SaveTextBoxKeyValue(contractStartDate, "PropertyContractStartDate");
            SaveTextBoxKeyValue(contractEndDate, "PropertyContractEndDate");

            SaveKeyValue(cbIncludeInvoiceCopiesOnStatements.Checked ? "1" : "0", "IncludeInvoicesWithCopyOfStatement");

            if (_account.Type == AccountType.MotorClub)
            {
                var setting = CompanyKeyValue.GetFirstValueOrNull(_account.CompanyId,
                        Provider.Towbook.ProviderId, "HideChargesFromMotorClubInvoicesByDefault");

                if (setting != null)
                {
                    if (!(setting == "1" && cbAlwaysHideCharges.Checked) &&
                        !(setting == "0" && !cbAlwaysHideCharges.Checked))
                    {
                        // only save the setting if the setting isn't the company default.
                        SaveKeyValue(cbAlwaysHideCharges.Checked ? "1" : "0", "AlwaysHideCharges");
                    }
                    else
                        DeleteKeyValue("AlwaysHideCharges");
                }
                else
                {
                    SaveKeyValue(cbAlwaysHideCharges.Checked ? "1" : "0", "AlwaysHideCharges");
                }
            }
            else
            {
                SaveKeyValue(cbAlwaysHideCharges.Checked ? "1" : "0", "AlwaysHideCharges");
            }

            SaveKeyValue(cbAlwaysHideDiscounts.Checked ? "1" : "0", "AlwaysHideDiscounts");
            SaveKeyValue(cbAlwaysHidePhotos.Checked ? "1" : "0", "AlwaysHidePhotos");
            SaveKeyValue(cbUnloadedMileageSetOne.Checked ? "1" : "0", "MCBilling_ForceUnloadedMilesToOneIfMissing");
            SaveKeyValue(cbUnloadedMileageRoundOne.Checked ? "1" : "0", "RoundUpCalculatedMiles");
            SaveKeyValue(ddlSuggestedDefault.SelectedValue, "DefaultSuggestedMileageRoute");

            if (ddlAutomaticallyFillInMiles.SelectedValue == "0")
                DeleteKeyValue("AutomaticallyAddMiles");
            else
                SaveKeyValue(ddlAutomaticallyFillInMiles.SelectedValue, "AutomaticallyAddMiles");

            if (Global.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.DeadheadMileage))
                SaveKeyValue(cbAlwaysAddDeadhead.Checked ? "1" : "0", "AutomaticallyAddDeadheadMileage");

            if (string.IsNullOrWhiteSpace(txtReplyToEmail.Text))
                DeleteKeyValue("ReplyToEmailAddress");
            else
                SaveKeyValue(txtReplyToEmail.Text, "ReplyToEmailAddress");

            var emailPreference = CompanyKeyValue.GetFirstValueOrNull(WebGlobal.CurrentUser.CompanyId,
                    Provider.Towbook.ProviderId, "EmailInvoiceEventPreference");

            if (emailPreference != null)
            {
                if (!(emailPreference == "1" && ddlEmailEventPreference.SelectedValue == "1") &&
                    !(emailPreference == "0" && ddlEmailEventPreference.SelectedValue == "0"))
                {
                    if (cbEmail.Checked)
                        SaveKeyValue(ddlEmailEventPreference.SelectedValue, "EmailInvoiceEventPreference");
                    else
                        DeleteKeyValue("EmailInvoiceEventPreference");
                }
                else
                    DeleteKeyValue("EmailInvoiceEventPreference");
            }
            else
            {
                if (cbEmail.Checked)
                    SaveKeyValue(ddlEmailEventPreference.SelectedValue, "EmailInvoiceEventPreference");
                else
                    DeleteKeyValue("EmailInvoiceEventPreference");
            }


            if (Global.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.PriorityCallTextAlert))
            {
                if (cbPriorityCallTextAlertToManagers.Checked)
                {
                    SaveKeyValue("1", "EnablePriorityCallTextAlertToAllManagers");
                }
                else
                {
                    if (AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "EnablePriorityCallTextAlertToAllManagers").FirstOrDefault() != null)
                        DeleteKeyValue("EnablePriorityCallTextAlertToAllManagers");
                }
            }

            if (_account.Type != AccountType.MotorClub)
            {
                SaveKeyValue(cbAutoFillContact.Checked ? "1" : "0", "AccountContactAddedAtCallCreation");

                if (cbUsePhysicalAddressAs.Checked)
                {
                    if (ddlDefaultPhysicalAddress.SelectedValue == "1")
                        SaveKeyValue("1", "AccountPhysicalAddressAsDefaultLocation");
                    else if (ddlDefaultPhysicalAddress.SelectedValue == "2")
                        SaveKeyValue("2", "AccountPhysicalAddressAsDefaultLocation");
                }
                else
                    DeleteKeyValue("AccountPhysicalAddressAsDefaultLocation");
            }
            else
            {
                DeleteKeyValue("AccountPhysicalAddressAsDefaultLocation");
                DeleteKeyValue("AccountContactAddedAtCallCreation");
            }

            if (ddlDefaultAssetBodyType.SelectedValue != "1")
                SaveKeyValue(ddlDefaultAssetBodyType.SelectedValue, "DefaultAssetBodyTypeId");
            else
                DeleteKeyValue("DefaultAssetBodyTypeId");

            if (Global.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.DispatchToSubcontractors))
            {
                if (!defaultSubcontractorAccountIdIsSet)
                {
                    if (ddlSubcontractors.SelectedValue != "0")
                        SaveKeyValue(ddlSubcontractors.SelectedValue, "DefaultSubcontractorAccountId");
                    else
                        DeleteKeyValue("DefaultSubcontractorAccountId");
                }

                SaveKeyValue(towbookEmailAddress.Text.Trim(), "DSC_SubcontractorAddress");
                SaveKeyValue(txtMobileNumbers.Text.Trim(), "SubcontractorMobileNumbers");
            }

            if (Global.CurrentUser.HasAccessToCompany(119800))
            {
                SaveKeyValue(towbookReferenceNumber.Text, "CustomReferenceNumber");
            }

            SaveKeyValue(mcTowbookEmailAddress.Text.Trim(), "DSC_SenderAddress");

            if (!string.IsNullOrWhiteSpace(towbookEmailAddress.Text) ||
                    !string.IsNullOrWhiteSpace(mcTowbookEmailAddress.Text))
            {
                await TryConnect();
            }

            if (WebGlobal.Companies.Count() > 1)
            {
                if (ddlCompanyOverride.SelectedValue != _account.CompanyId.ToString())
                {
                    var selectedValue = 0;
                    try
                    {
                        selectedValue = int.Parse(ddlCompanyOverride.SelectedValue);
                    }
                    catch { }

                    if (selectedValue > 0)
                    {
                        SaveKeyValue(ddlCompanyOverride.SelectedValue, "OverrideToSharedCompanyId");
                        var aUsers = Extric.Towbook.User.GetByAccountId(_account.Id);
                        foreach (var u in aUsers)
                        {
                            u.PrimaryCompanyId = selectedValue;
                            await u.Save();
                        }
                    }
                    
                }
                else
                {
                    DeleteKeyValue("OverrideToSharedCompanyId");

                    var aUsers = Extric.Towbook.User.GetByAccountId(_account.Id);
                    foreach (var u in aUsers)
                    {
                        u.PrimaryCompanyId = _account.CompanyId;
                        await u.Save();
                    }
                }
            }

            if (!newAccount && Global.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.Roadside))
            {
                var rs = Extric.Roadside.RoadsideSetting.GetByCompanyId(_account.CompanyId, _account.Id);
                bool isAutoInvoiteEnabledByDefault = _account.Type == AccountType.MotorClub ? rs.EnableMotorClubAutoInvite.GetValueOrDefault() : rs.EnableNonMotorClubAutoInvite.GetValueOrDefault();

                var key = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "AutoSendRoadsideInvite").FirstOrDefault();

                if (cbAlwaysSendRoadsideInvite.Checked != isAutoInvoiteEnabledByDefault || 
                    (key != null && key.Value == "1" && !cbAlwaysSendRoadsideInvite.Checked) || 
                    (key != null && key.Value == "0" && cbAlwaysSendRoadsideInvite.Checked))
                {
                    if (key != null)
                        DeleteKeyValue("AutoSendRoadsideInvite");

                    if(cbAlwaysSendRoadsideInvite.Visible && cbAlwaysSendRoadsideInvite.Checked != isAutoInvoiteEnabledByDefault)
                        SaveKeyValue(cbAlwaysSendRoadsideInvite.Checked ? "1" : "0", "AutoSendRoadsideInvite"); // opt-in or opt-out
                }



                var items = Extric.Roadside.JobProgressTextAlertItem.GetByCompanyId(_account.CompanyId, true);

                // get defaults if company has no saved text message items saved (inherit defaults)
                if (items.Count() == 0)
                    items = Extric.Roadside.JobProgressTextAlertItem.GetDefaults(_account.CompanyId, _account.Id);

                var completionProgressTextItem = items.Where(w => w.StatusTypeId == Extric.Roadside.JobProgressStatusType.Completed.Id && !w.IsDeleted).LastOrDefault();

                key = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "AlwaysSendSurvey").FirstOrDefault();
                if (key == null || (key.Value == "1" && !cbAlwaysSendSurvey.Checked) || (key.Value == "0" && cbAlwaysSendSurvey.Checked))
                {
                    if (key != null)
                        DeleteKeyValue("AlwaysSendSurvey");

                    if (cbAlwaysSendSurvey.Visible && (completionProgressTextItem == null && cbAlwaysSendSurvey.Checked || completionProgressTextItem != null && !cbAlwaysSendSurvey.Checked))
                        SaveKeyValue(cbAlwaysSendSurvey.Checked  ? "1" : "0", "AlwaysSendSurvey");
                }
            }

            if (_account.Type == AccountType.PrivateProperty)
            {
                SaveKeyValue(cbEnableParkingPermits.Checked ? "1" : "0", "ParkingPermitsEnabled");
                SaveKeyValue(cbEnableStickering.Checked ? "1" : "0", "StickeringEnabled");
                SaveKeyValue(cbEnableSiteVisits.Checked ? "1" : "0", "SiteVisitsEnabled");
            }


            var iplkey = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "Square_AlwaysIncludePaymentLinkOnInvoices").FirstOrDefault();
            var iplkeyDefault = (CompanyKeyValue.GetFirstValueOrNull(_account.CompanyId, Provider.Towbook.ProviderId, "Square_AlwaysIncludePaymentLinkOnInvoices") ?? "0") == "1";
            if (cbIncludeInvoicePaymentLink.Checked == iplkeyDefault)
            {
                if (iplkey != null)
                    DeleteKeyValue("Square_AlwaysIncludePaymentLinkOnInvoices");
            }
            else if ((iplkey == null) || (iplkey != null && iplkey.Value == "1" && !cbIncludeInvoicePaymentLink.Checked) || (iplkey != null && iplkey.Value == "0" && cbIncludeInvoicePaymentLink.Checked))
            {
                if (iplkey != null)
                    DeleteKeyValue("Square_AlwaysIncludePaymentLinkOnInvoices");

                SaveKeyValue(cbIncludeInvoicePaymentLink.Checked ? "1" : "0", "Square_AlwaysIncludePaymentLinkOnInvoices");
            }

            iplkey = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "Square_AlwaysIncludePaymentLinkOnStatements").FirstOrDefault();
            iplkeyDefault = (CompanyKeyValue.GetFirstValueOrNull(_account.CompanyId, Provider.Towbook.ProviderId, "Square_AlwaysIncludePaymentLinkOnStatements") ?? "0") == "1";
            if (cbIncludeStatementPaymentLink.Checked == iplkeyDefault)
            {
                if (iplkey != null)
                    DeleteKeyValue("Square_AlwaysIncludePaymentLinkOnStatements");
            }
            else if ((iplkey == null) || (iplkey != null && iplkey.Value == "1" && !cbIncludeStatementPaymentLink.Checked) || (iplkey != null && iplkey.Value == "0" && cbIncludeStatementPaymentLink.Checked))
            {
                if (iplkey != null)
                    DeleteKeyValue("Square_AlwaysIncludePaymentLinkOnStatements");

                SaveKeyValue(cbIncludeStatementPaymentLink.Checked ? "1" : "0", "Square_AlwaysIncludePaymentLinkOnStatements");
            }

            iplkey = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "Square_OptOutOfEmailsOnTransactions").FirstOrDefault();
            iplkeyDefault = (CompanyKeyValue.GetFirstValueOrNull(_account.CompanyId, Provider.Towbook.ProviderId, "Square_OptOutOfEmailsOnTransactions") ?? "0") != "1";
            if (cbOptOutOfConfirmationEmailOnTransactions.Checked == iplkeyDefault)
            {
                if (iplkey != null)
                    DeleteKeyValue("Square_OptOutOfEmailsOnTransactions");
            }
            else if ((iplkey == null) || (iplkey != null && iplkey.Value == "1" && !cbOptOutOfConfirmationEmailOnTransactions.Checked) || (iplkey != null && iplkey.Value == "0" && cbOptOutOfConfirmationEmailOnTransactions.Checked))
            {
                if (iplkey != null)
                    DeleteKeyValue("Square_OptOutOfEmailsOnTransactions");

                SaveKeyValue(cbOptOutOfConfirmationEmailOnTransactions.Checked ? "0" : "1", "Square_OptOutOfEmailsOnTransactions");
            }


            // printed dispatch invoice
            iplkey = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "Square_ExcludeLinkOnPrintedDispatchInvoices").FirstOrDefault();
            iplkeyDefault = (CompanyKeyValue.GetFirstValueOrNull(_account.CompanyId, Provider.Towbook.ProviderId, "Square_ExcludeLinkOnPrintedDispatchInvoices") ?? "0") == "1";
            if (cbExcludeLinkOnPrintedDispatchInvoices.Checked == iplkeyDefault)
            {
                if (iplkey != null)
                    DeleteKeyValue("Square_ExcludeLinkOnPrintedDispatchInvoices");
            }
            else if ((iplkey == null) || (iplkey != null && iplkey.Value == "1" && !cbExcludeLinkOnPrintedDispatchInvoices.Checked) || (iplkey != null && iplkey.Value == "0" && cbExcludeLinkOnPrintedDispatchInvoices.Checked))
            {
                if (iplkey != null)
                    DeleteKeyValue("Square_ExcludeLinkOnPrintedDispatchInvoices");

                SaveKeyValue(cbExcludeLinkOnPrintedDispatchInvoices.Checked ? "0" : "1", "Square_ExcludeLinkOnPrintedDispatchInvoices");
            }

            // printed impound invoice
            iplkey = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "Square_ExcludeLinkOnPrintedImpoundInvoices").FirstOrDefault();
            iplkeyDefault = (CompanyKeyValue.GetFirstValueOrNull(_account.CompanyId, Provider.Towbook.ProviderId, "Square_ExcludeLinkOnPrintedImpoundInvoices") ?? "0") == "1";
            if (cbExcludeLinkOnPrintedImpoundInvoices.Checked == iplkeyDefault)
            {
                if (iplkey != null)
                    DeleteKeyValue("Square_ExcludeLinkOnPrintedImpoundInvoices");
            }
            else if ((iplkey == null) || (iplkey != null && iplkey.Value == "1" && !cbExcludeLinkOnPrintedImpoundInvoices.Checked) || (iplkey != null && iplkey.Value == "0" && cbExcludeLinkOnPrintedImpoundInvoices.Checked))
            {
                if (iplkey != null)
                    DeleteKeyValue("Square_ExcludeLinkOnPrintedImpoundInvoices");

                SaveKeyValue(cbExcludeLinkOnPrintedImpoundInvoices.Checked ? "0" : "1", "Square_ExcludeLinkOnPrintedImpoundInvoices");
            }

            // printed statements
            iplkey = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "Square_ExcludeLinkOnPrintedStatements").FirstOrDefault();
            iplkeyDefault = (CompanyKeyValue.GetFirstValueOrNull(_account.CompanyId, Provider.Towbook.ProviderId, "Square_ExcludeLinkOnPrintedStatements") ?? "0") == "1";
            if (cbExcludeLinkOnPrintedStatements.Checked == iplkeyDefault)
            {
                if (iplkey != null)
                    DeleteKeyValue("Square_ExcludeLinkOnPrintedStatements");
            } 
            else if ((iplkey == null) || (iplkey != null && iplkey.Value == "1" && !cbExcludeLinkOnPrintedStatements.Checked) || (iplkey != null && iplkey.Value == "0" && cbExcludeLinkOnPrintedStatements.Checked))
            {
                if (iplkey != null)
                    DeleteKeyValue("Square_ExcludeLinkOnPrintedStatements");

                SaveKeyValue(cbExcludeLinkOnPrintedStatements.Checked ? "0" : "1", "Square_ExcludeLinkOnPrintedStatements");
            }


            // tips on payment links
            iplkey = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "SquareTipping_ExcludeOnPaymentLinks").FirstOrDefault();
            iplkeyDefault = (CompanyKeyValue.GetFirstValueOrNull(_account.CompanyId, Provider.Towbook.ProviderId, "SquareTipping_ExcludeOnPaymentLinks") ?? "0") != "1";
            if (cbExcludeTipsOnPaymentLinks.Checked == iplkeyDefault)
            {
                if (iplkey != null)
                    DeleteKeyValue("SquareTipping_ExcludeOnPaymentLinks");
            }
            else if ((iplkey == null) || (iplkey != null && iplkey.Value == "1" && !cbExcludeTipsOnPaymentLinks.Checked) || (iplkey != null && iplkey.Value == "0" && cbExcludeTipsOnPaymentLinks.Checked)) 
            {
                if (iplkey != null)
                    DeleteKeyValue("SquareTipping_ExcludeOnPaymentLinks");

                SaveKeyValue(cbExcludeTipsOnPaymentLinks.Checked ? "0" : "1", "SquareTipping_ExcludeOnPaymentLinks");
            }

            // tips on square reader
            iplkey = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "SquareTipping_ExcludeOnSquareReader").FirstOrDefault();
            iplkeyDefault = (CompanyKeyValue.GetFirstValueOrNull(_account.CompanyId, Provider.Towbook.ProviderId, "SquareTipping_ExcludeOnSquareReader") ?? "0") != "1";
            if (cbExcludeTipsOnSquareReader.Checked == iplkeyDefault)
            {

                if (iplkey != null)
                    DeleteKeyValue("SquareTipping_ExcludeOnSquareReader");
            }
            else if ((iplkey == null) || (iplkey != null && iplkey.Value == "1" && !cbExcludeTipsOnSquareReader.Checked) || (iplkey != null && iplkey.Value == "0" && cbExcludeTipsOnSquareReader.Checked))
            {
                if (iplkey != null)
                    DeleteKeyValue("SquareTipping_ExcludeOnSquareReader");

                SaveKeyValue(cbExcludeTipsOnSquareReader.Checked ? "0" : "1", "SquareTipping_ExcludeOnSquareReader");
            }

            // tips on square terminal
            iplkey = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, "SquareTipping_ExcludeOnSquareTerminal").FirstOrDefault();
            iplkeyDefault = (CompanyKeyValue.GetFirstValueOrNull(_account.CompanyId, Provider.Towbook.ProviderId, "SquareTipping_ExcludeOnSquareTerminal") ?? "0") != "1";
            if (cbExcludeTipsOnSquareTerminal.Checked == iplkeyDefault)
            {
                if (iplkey != null)
                    DeleteKeyValue("SquareTipping_ExcludeOnSquareTerminal");
            }
            else if ((iplkey == null) || (iplkey != null && iplkey.Value == "1" && !cbExcludeTipsOnSquareTerminal.Checked) || (iplkey != null && iplkey.Value == "0" && cbExcludeTipsOnSquareTerminal.Checked))
            {
                if (iplkey != null)
                    DeleteKeyValue("SquareTipping_ExcludeOnSquareTerminal");

                SaveKeyValue(cbExcludeTipsOnSquareTerminal.Checked ? "0" : "1", "SquareTipping_ExcludeOnSquareTerminal");
            }

            if (Global.CurrentUser.Company.HasFeature(Features.Accounts_AccountManagers))
            {
                int accountManagerUserId = 0;
                if (int.TryParse(ddlAccountManager.SelectedValue, out accountManagerUserId))
                {
                    var accountManager = AccountManager.GetByAccountId(_account.Id);

                    if ((accountManagerUserId == 0 && accountManager != null) ||
                            (accountManager != null && accountManager.UserId != accountManagerUserId))
                    {
                        accountManager.Delete(Global.CurrentUser);
                    }

                    if (accountManagerUserId > 0)
                    {
                        accountManager = new AccountManager()
                        {
                            AccountId = _account.Id,
                            UserId = accountManagerUserId
                        };

                        accountManager.Save(Global.CurrentUser);
                    }
                }

            }

        });

        await DoCacheUpdateAsync();

        #endregion

        Response.Redirect("Account.aspx?id=" + _account.Id);
        Response.End();
        return;
    }

    protected async Task DoCacheUpdateAsync(bool delete = false)
    {

        var cmp = GetCompaniesForRequest();
        var key = "accounts:" + string.Join(",",
            GetCompaniesForRequest().Select(o => o.Id).OrderBy(ro => ro));

        var json = Core.GetRedisValue(key);

        if (json != null)
        {
            var accountList = Newtonsoft.Json.JsonConvert.DeserializeObject<IEnumerable<Extric.Towbook.API.Models.AccountMinimalModel>>(json);

            if (delete)
                accountList = accountList.Where(o => o.Id != _account.Id);
            else
                accountList = accountList.Where(o => o.Id != _account.Id).Union(new[]
            {
                Extric.Towbook.API.Models.AccountMinimalModel.Map(await Extric.Towbook.API.Models.AccountModel.MapAsync(_account, true),
                    null,
                    null,
                    null,
                    null,
                    Global.CurrentUser)
                }).OrderBy(o => o.Name);

            json = accountList.ToJson();
            Core.SetRedisValue(key, json);
        }
    }

    public static Extric.Towbook.Company.Company[] GetCompaniesForRequest()
    {
        try
        {
            var obj = System.Web.HttpContext.Current;

            string company = "";

            if (!string.IsNullOrWhiteSpace(company))
            {
                if (company == "all")
                {
                    return WebGlobal.GetCompanies();
                }
                else
                {
                    return new Extric.Towbook.Company.Company[] {
                            WebGlobal.GetCompanies().Where(o => o.Id == Convert.ToInt32(company)).FirstOrDefault()
                        };
                }
            }
            else
            {
                return new Extric.Towbook.Company.Company[] { WebGlobal.CurrentUser.Company };
            }
        }
        finally
        {

        }
    }

    protected void OnGraceStartBound(object sender, EventArgs e)
    {
        //int itemIndex = ddlGraceStart.Items.IndexOf(itemToSelect);
    }

    protected void SaveStickering()
    {
        #region Sticker settings
        var cSetting = Extric.Towbook.Stickering.StickerSetting.GetByCompanyId(_account.CompanyId, null) 
            ?? new Extric.Towbook.Stickering.StickerSetting();

        var aSetting = Extric.Towbook.Stickering.StickerSetting.GetByAccountId(_account.CompanyId, _account.Id)
            ?? new Extric.Towbook.Stickering.StickerSetting();

        if (aSetting.AccountId == null)
            aSetting.Id = 0; // Don't override the company default

        bool isChanged = false;
        
        bool propertyApprovalValue = ddlManagerApproval.SelectedValue == "1";
        if (aSetting.Id > 0 && aSetting.PropertyApprovalRequired != propertyApprovalValue)
            isChanged = true;
        else if (aSetting.Id == 0 && cSetting.PropertyApprovalRequired != propertyApprovalValue)
            isChanged = true;

        bool managerApprovalValue = ddlManagerApproval.SelectedValue == "1";
        if (aSetting.Id > 0 && aSetting.TowManagerApprovalRequired != managerApprovalValue)
            isChanged = true;
        else if (aSetting.Id == 0 && cSetting.TowManagerApprovalRequired != managerApprovalValue)
            isChanged = true;

        if (Global.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.UserSignatures))
        {
            bool managerSignatureRequiredValue = ddlManagerSignatureRequired.SelectedValue == "1";
            if(aSetting.Id > 0 && aSetting.RequireApprovalSignature != managerSignatureRequiredValue)
                isChanged = true;
            else if (aSetting.Id == 0 && cSetting.RequireApprovalSignature != managerSignatureRequiredValue)
                isChanged = true;

            if(isChanged)
                SaveKeyValue(managerSignatureRequiredValue ? "1" : "0", "RequireSignatureForStickerApproval");
        }

        bool allowExtensions = ddlAllowExtensions.SelectedValue == "1";
        if (aSetting.Id > 0 && aSetting.AllowExtensions != allowExtensions)
            isChanged = true;
        else if (aSetting.Id == 0 && cSetting.AllowExtensions != allowExtensions)
            isChanged = true;

        decimal? stickerExtension = cSetting.ExpirationHours;

        if (aSetting.Id > 0 && aSetting.ExpirationHours != null)
            stickerExtension = aSetting.ExpirationHours;

        decimal expTime;

        if (tbExpirationTime.Text != "" && Decimal.TryParse(tbExpirationTime.Text, out expTime) && expTime != stickerExtension)
        {
            isChanged = true;
            stickerExtension = expTime;
        }

        if (isChanged)
        {
            aSetting.CompanyId = _account.CompanyId;
            aSetting.AccountId = _account.Id;
            aSetting.ExpirationHours = stickerExtension;
            aSetting.PropertyApprovalRequired = propertyApprovalValue;
            aSetting.TowManagerApprovalRequired = managerApprovalValue;
            aSetting.AllowExtensions = allowExtensions;
            aSetting.Save(Global.CurrentUser);
        }

        if (_account.Type == AccountType.PrivateProperty)
        {
            isChanged = false;

            // send notification email settings
            bool sendActivityEmail = ddlSessionActivityEmail.SelectedValue == "1";
            if (aSetting.Id > 0 && aSetting.SendActivitySessionEmail != sendActivityEmail)
                isChanged = true;
            else if (cSetting.SendActivitySessionEmail != sendActivityEmail)
                isChanged = true;

            bool sendDailyEmail = ddlSessionActivityEmail.SelectedValue == "1";
            if (aSetting.Id > 0 && aSetting.SendDailySummaryEmail != sendDailyEmail)
                isChanged = true;
            else if (cSetting.SendDailySummaryEmail != sendDailyEmail)
                isChanged = true;

            if (isChanged)
            {
                if (!aSetting.SendActivitySessionEmail && ddlSessionActivityEmail.SelectedValue == "1")
                {
                    if (cSetting.SendActivitySessionEmail)
                        DeleteKeyValue("DisableStickeringActivityReportAfter15Minutes"); // use company default
                    else
                        SaveKeyValue("0", "DisableStickeringActivityReportAfter15Minutes");  // opt-in
                }
                else if (ddlSessionActivityEmail.SelectedValue == "0")
                {
                    SaveKeyValue("1", "DisableStickeringActivityReportAfter15Minutes");
                }

                if (!aSetting.SendDailySummaryEmail && ddlDailySummaryEmail.SelectedValue == "1")
                {
                    if (cSetting.SendDailySummaryEmail)
                        DeleteKeyValue("DisableStickeringDailyWaitingForActionReport"); // use company default
                    else
                        SaveKeyValue("0", "DisableStickeringDailyWaitingForActionReport");  // opt-in
                }
                else if (ddlDailySummaryEmail.SelectedValue == "0")
                {
                    SaveKeyValue("1", "DisableStickeringDailyWaitingForActionReport");
                }
            }
        }

        // wait time
        var cgSetting = Extric.Towbook.Stickering.GracePeriodSetting.GetByCompanyId(_account.CompanyId, null)
            ?? new Extric.Towbook.Stickering.GracePeriodSetting();
        var agSetting = Extric.Towbook.Stickering.GracePeriodSetting.GetByCompanyId(_account.CompanyId, _account.Id);

        decimal? waitTime = cgSetting.Hours ?? 24;

        if (agSetting != null && agSetting.Hours != waitTime)
            waitTime = agSetting.Hours;

        decimal currentWaitTime = 0.0M;

        if ((tbWaitTime.Text == "" && agSetting != null) ||
            (tbWaitTime.Text != "" && Decimal.TryParse(tbWaitTime.Text, out currentWaitTime) && 
            currentWaitTime != waitTime))
        {
            agSetting = agSetting ?? new Extric.Towbook.Stickering.GracePeriodSetting();
            agSetting.CompanyId = _account.CompanyId;
            agSetting.AccountId = _account.Id;
            agSetting.Hours = tbWaitTime.Text == "" ? cgSetting.Hours ?? 24 : currentWaitTime;
            agSetting.OwnerUserId = Global.CurrentUser.Id;

            agSetting.Save(Global.CurrentUser);
        }

        #endregion

        #region Reasons, Reason times, etc
        Dictionary<int, int?> reasonList = new Dictionary<int, int?>();

        // delete all account start times and save with selected reasons
        Extric.Towbook.Stickering.ReasonTime.Delete(_account.Id);



        foreach (RepeaterItem i in rpStickerReasons.Items)
        {
            HiddenField id = (HiddenField)i.FindControl("hfId");
            CheckBox reason = (CheckBox)i.FindControl("cbReason");
            TextBox hours = (TextBox)i.FindControl("tbHours");
            DropDownList type = (DropDownList)i.FindControl("ddlGraceStart");

            int? reasonId = null;

            if (id != null)
                reasonId = Convert.ToInt32(id.Value);

            if (reasonId == null)
                continue;

            if (reason != null)
            {
                if (reason.Checked)
                {
                    int startType = 0;
                    if (type != null)
                        startType = Convert.ToInt32(type.SelectedValue);

                    reasonList.Add(reasonId.Value, null);

                    _account.DeleteReason(reasonId.Value);
                    _account.AddReason(reasonId.Value, Global.CurrentUser.Id, startType);

                    // reason times
                    var rt = new Extric.Towbook.Stickering.ReasonTime();

                    if (hours != null)
                    {
                        decimal decHours = 0;
                        if (decimal.TryParse(hours.Text, out decHours))
                            rt.Time = decHours;
                        else
                            rt.Time = (decimal?)null;
                    }

                    rt.AccountId = _account.Id;
                    rt.StickerReasonId = reasonId;
                    rt.CreateDate = DateTime.Now;
                    rt.Save();
                }
            }
        }

        // go through the newly created list of what's selected.
        foreach (Extric.Towbook.Stickering.Reason r in _account.Reasons)
        {
            if (!reasonList.ContainsKey(r.Id))
                _account.DeleteReason(r.Id);
        }
        #endregion
    }

    protected void SavePermits()
    {
        #region Required fields & parking spaces
        var cppSettings = Extric.Towbook.Accounts.ParkingPermitSetting.GetByCompanyId(_account.CompanyId, null);
        var appSettings = Extric.Towbook.Accounts.ParkingPermitSetting.GetByCompanyId(_account.CompanyId, _account.Id);
        bool isChanged = false;

        // always save new account settings.
        if (appSettings == null)
            appSettings = new Extric.Towbook.Accounts.ParkingPermitSetting();

        // parking spaces
        var parking = tbNumberOfParkingSpaces.Text;
        if (parking == "")
            parking = "1";

        if (appSettings.PermitsPerResident != Convert.ToInt32(parking))
            isChanged = true;

        // guest spaces
        var guests = tbNumberOfGuests.Text;
        if (guests == "")
            guests = "0";

        if (appSettings.GuestPermitsPerResident != Convert.ToInt32(guests))
            isChanged = true;

        // guest vehicle info requirement
        var guestVehicle = Convert.ToInt32(ddlRequireGuestVehicle.SelectedValue);
        if (appSettings.RequireGuestVehicleInfo == null && guestVehicle == 0)
            isChanged = true;
        else if (appSettings.RequireGuestVehicleInfo != null && appSettings.RequireGuestVehicleInfo.Value != guestVehicle)
            isChanged = true;

        // guest pass update allowance
        var allowGuestUpdate = ddlAllowGuestPassUpdate.SelectedValue == "true" ? true : false;
        if (appSettings.AllowGuestPassUpdate == null && !allowGuestUpdate)
            isChanged = true;
        else if (appSettings.AllowGuestPassUpdate != null && appSettings.AllowGuestPassUpdate.Value != allowGuestUpdate)
            isChanged = true;

        // expiration type
        var type = (PermitExpirationType)Convert.ToInt32(ddlExpirationType.SelectedValue);
        if (appSettings.ExpirationType != type)
            isChanged = true;

        // Guest expriation days
        var days = Convert.ToInt32(ddlGuestExpirationDays.SelectedValue);
        if (appSettings.GuestExpirationDays != null && appSettings.GuestExpirationDays.Value != days)
            isChanged = true;

        // require email
        bool email = permitContactEmail.Checked;
        if (appSettings.RequireEmail != email)
            isChanged = true;
        else if (cppSettings != null && cppSettings.RequireEmail != email)
            isChanged = true;

        // require color
        bool color = permitVehicleColor.Checked;
        if (appSettings != null && appSettings.RequireColor != color)
            isChanged = true;
        else if (cppSettings != null && cppSettings.RequireColor != color)
            isChanged = true;

        // require Vin
        bool vin = permitVehicleVin.Checked;
        if (appSettings.RequireVin != vin)
            isChanged = true;
        else if (cppSettings != null && cppSettings.RequireVin != vin)
            isChanged = true;

        // require registration number
        bool registrationNumber = permitStateRegistration.Checked;
        if (appSettings.RequireVehicleRegistration != registrationNumber)
            isChanged = true;
        else if (cppSettings != null && cppSettings.RequireVehicleRegistration != registrationNumber)
            isChanged = true;

        // require registration expiration
        bool registrationExpiration = permitStateRegistrationExpiration.Checked;
        if (appSettings.RequireVehicleRegistrationExpiration != registrationExpiration)
            isChanged = true;
        else if (cppSettings != null && cppSettings.RequireVehicleRegistrationExpiration != registrationExpiration)
            isChanged = true;

        // require address
        bool requireAddress = permitContactAddress.Checked;
        if (appSettings.RequireAddress != requireAddress)
            isChanged = true;
        else if (cppSettings != null && cppSettings.RequireAddress != requireAddress)
            isChanged = true;

        // parking permit public link
        if (ddlPublicLinkEnable.SelectedValue == "true")
        {
            if (appSettings.ParkingPermitPublicLinkId == null || appSettings.ParkingPermitPublicLinkId.Value == 0)
            {
                _parkingPermitPublicLink = ParkingPermitPublicLink.GetByAccountId(_account.Id);
                if (_parkingPermitPublicLink == null)
                    _parkingPermitPublicLink = new ParkingPermitPublicLink();

                _parkingPermitPublicLink.AccountId = _account.Id;
                _parkingPermitPublicLink.Disabled = false;
                _parkingPermitPublicLink.PropertyCode = tbPropertyCode.Text;
                _parkingPermitPublicLink.Save(Global.CurrentUser);

                appSettings.ParkingPermitPublicLinkId = _parkingPermitPublicLink.Id;
                isChanged = true;
            }
            else
            {
                _parkingPermitPublicLink = ParkingPermitPublicLink.GetById(appSettings.ParkingPermitPublicLinkId.Value);
                if (_parkingPermitPublicLink != null && _parkingPermitPublicLink.PropertyCode != tbPropertyCode.Text)
                {
                    _parkingPermitPublicLink.PropertyCode = tbPropertyCode.Text;
                    _parkingPermitPublicLink.Save(WebGlobal.CurrentUser);
                }
            }
        }
        else
        {
            if (appSettings.ParkingPermitPublicLinkId != null)
            {
                _parkingPermitPublicLink = ParkingPermitPublicLink.GetById(appSettings.ParkingPermitPublicLinkId.Value);
                if (_parkingPermitPublicLink != null && !_parkingPermitPublicLink.Disabled)
                {
                    _parkingPermitPublicLink.Delete();
                }

                appSettings.ParkingPermitPublicLinkId = (int?)null;
                isChanged = true;
            }
        }

        if (isChanged)
        {
            var aSetting = appSettings ?? new Extric.Towbook.Accounts.ParkingPermitSetting();
            aSetting.CompanyId = _account.CompanyId;
            aSetting.AccountId = _account.Id;
            aSetting.RequireEmail = email;
            aSetting.RequireAddress = requireAddress;
            aSetting.RequireColor = color;
            aSetting.RequireVin = vin;
            aSetting.RequireVehicleRegistration = registrationNumber;
            aSetting.RequireVehicleRegistrationExpiration = registrationExpiration;
            aSetting.PermitsPerResident = parking == "" ? 1 : Convert.ToInt32(parking);
            aSetting.GuestPermitsPerResident = guests == "" ? 0 : Convert.ToInt32(guests);
            aSetting.ExpirationType = type;
            aSetting.GuestExpirationDays = days;
            aSetting.RequireGuestVehicleInfo = guestVehicle;
            aSetting.AllowGuestPassUpdate = allowGuestUpdate;
            aSetting.Save(WebGlobal.CurrentUser);
        }
        #endregion

        #region Disclaimer
        var content = tbDisclaimer.Text;
        var aDisclaimer = Extric.Towbook.Accounts.ParkingPermitDisclaimer.GetByCompanyId(_account.CompanyId, _account.Id);

        // don't delete company disclaimer (accountId == null)
        if (aDisclaimer == null || aDisclaimer.AccountId == null)
            aDisclaimer = new ParkingPermitDisclaimer();

        if (string.IsNullOrEmpty(content))
        {
            if (!string.IsNullOrEmpty(aDisclaimer.Content))
            {
                // Delete account version if user emptied text
                aDisclaimer.Delete(Global.CurrentUser);
            }
        }
        else
        {
            // save new content
            if (aDisclaimer.Content != content)
            {
                aDisclaimer.AccountId = _account.Id;
                aDisclaimer.CompanyId = _account.CompanyId;
                aDisclaimer.Content = Core.HtmlEncode(content);
                aDisclaimer.Save(WebGlobal.CurrentUser);
            }
        }


        #endregion

    }

    protected void SaveStorageRates()
    {
        if (_storageRate == null)
        {
            _storageRate = new Extric.Towbook.Accounts.StorageRate();
            _storageRate.AccountId = _account.Id;
        }

        if (txtMaximumCharges.Text.Length > 0)
            _storageRate.MaximumCharge = Convert.ToSingle(txtMaximumCharges.Text);
        else
            _storageRate.MaximumCharge = 0;

        _storageRate.Save();
    }

    protected void SaveRequiredFields(bool useCODValue = false)
    {
        if (_account == null || _account.Id == -1)
            return;

        var allRuleSets = EntryValidationRuleSet.GetAllByCompanyIds(new[] { _account.CompanyId });

        _ruleSet = allRuleSets.FirstOrDefault(x => x.AccountId == _account.Id);

        if (_ruleSet == null)
        {
            _ruleSet = new EntryValidationRuleSet();
            _ruleSet.AccountId = _account.Id;
            _ruleSet.CompanyId = _account.CompanyId;
            _ruleSet.AccountTypeId = null;
            _ruleSet.OwnerUserId = WebGlobal.CurrentUser.Id;
        }

        _ruleSet.RequireContactName = GetEntryRuleDropDownRequirementType(ddlReqDriverContactName, ddlReqDispatcherContactName);
        _ruleSet.RequireContactPhone = GetEntryRuleDropDownRequirementType(ddlReqDriverContactPhone, ddlReqDispatcherContactPhone);
        _ruleSet.RequirePurchaseOrderNumber = GetEntryRuleDropDownRequirementType(ddlReqDriverPurchaseOrderNumber, ddlReqDispatcherPurchaseOrderNumber);
        _ruleSet.RequireInvoiceNumber = GetEntryRuleDropDownRequirementType(ddlReqDriverInvoiceNumber, ddlReqDispatcherInvoiceNumber);
        _ruleSet.RequireCharges = GetEntryRuleDropDownRequirementType(ddlReqDriverCharges, ddlReqDispatcherCharges);
        _ruleSet.RequireOveragesPaid = GetEntryRuleDropDownRequirementType(ddlReqDriverOverages, ddlReqDispatcherOverages);
        _ruleSet.RequireVin = GetEntryRuleDropDownRequirementType(ddlReqDriverVin, ddlReqDispatcherVin);
        _ruleSet.RequirePlateNumber = GetEntryRuleDropDownRequirementType(ddlReqDriverLicensePlate, ddlReqDispatcherLicensePlate);
        _ruleSet.RequireOdometer = GetEntryRuleDropDownRequirementType(ddlReqDriverOdometer, ddlReqDispatcherOdometer);
        _ruleSet.RequirePhotos = GetEntryRuleDropDownRequirementType(ddlReqDriverPhotos, ddlReqDispatcherPhotos);
        _ruleSet.RequireSignature = GetEntryRuleDropDownRequirementType(ddlReqDriverSignature, ddlReqDispatcherSignature);
        _ruleSet.RequireDamageForm = GetEntryRuleDropDownRequirementType(ddlReqDriverDamageForm, ddlReqDispatcherDamageForm);

        _ruleSet.RequireReason = GetEntryRuleDropDownRequirementType(ddlReqDriverReason, ddlReqDispatcherReason);
        _ruleSet.RequireVehicleDestination = GetEntryRuleDropDownRequirementType(ddlReqDriverVehicleDestination, ddlReqDispatcherVehicleDestination);
        _ruleSet.RequireVehicleYear = GetEntryRuleDropDownRequirementType(ddlReqDriverVehicleYear, ddlReqDispatcherVehicleYear);
        _ruleSet.RequireVehicleMake = GetEntryRuleDropDownRequirementType(ddlReqDriverVehicleMake, ddlReqDispatcherVehicleMake);
        _ruleSet.RequireVehicleModel = GetEntryRuleDropDownRequirementType(ddlReqDriverVehicleModel, ddlReqDispatcherVehicleModel);
        _ruleSet.RequireVehicleColor = GetEntryRuleDropDownRequirementType(ddlReqDriverVehicleColor, ddlReqDispatcherVehicleColor);
        _ruleSet.RequireVehicleKeysLocation = GetEntryRuleDropDownRequirementType(ddlReqDriverVehicleKeysLocation, ddlReqDispatcherVehicleKeysLocation);
        _ruleSet.RequireInvoicePaid = GetEntryRuleDropDownRequirementType(ddlReqDriverPaidInFull, ddlReqDispatcherPaidInFull);

        // ready to save.
        _ruleSet.Save();
    }

    protected EntryValidationType? GetEntryRuleDropDownRequirementType(DropDownList ddlDriver, DropDownList ddlDispatcher)
    {
        if (ddlDriver == null || ddlDispatcher == null)
            return null;

        
        var driverVal = ddlDriver.SelectedValue;
        var dispatcherVal = ddlDispatcher.SelectedValue;

        if (driverVal == "0" && dispatcherVal == "0")
            return null; // inheritance

        if (driverVal == "1")
        {
            if (dispatcherVal == "1")
                return EntryValidationType.RequiredIncludingDispatchers;

            return EntryValidationType.Required;
        }

        if (driverVal == "2")
            return EntryValidationType.NotRequired;

        return null;
    }

    protected void SetEntryRuleDropDownListValues(DropDownList ddl, EntryValidationType? companyDefault, EntryValidationType? accountTypeDefault, EntryValidationType? accountDefault)
    {
        if (ddl == null)
            return;

        
        var isDispatcherControl = ddl.ID.Contains("Dispatcher");

        if (!ddl.CssClass.Contains("required-field"))
        {
            ddl.CssClass = "required-field-selector";

            if(isDispatcherControl)
                ddl.CssClass = ddl.CssClass + " dispatcher-field";
            else 
                ddl.CssClass = ddl.CssClass + " driver-field";
        }

        string byInheritanceTypeText = "Using Company Default - Not Required";
        string selectedValue = "0";

        if (companyDefault != null) {
            byInheritanceTypeText = "Using Company Default - " +
                (!isDispatcherControl && (companyDefault == EntryValidationType.RequiredIncludingDispatchers || companyDefault == EntryValidationType.Required) ||
                    (isDispatcherControl && companyDefault == EntryValidationType.RequiredIncludingDispatchers)
                    ? "Required"
                    : "Not Required");
        }

        if (accountTypeDefault != null)
        {
            byInheritanceTypeText = "Using Account Type Default - " +
                (!isDispatcherControl && (accountTypeDefault == EntryValidationType.RequiredIncludingDispatchers || accountTypeDefault == EntryValidationType.Required) ||
                    (isDispatcherControl && accountTypeDefault == EntryValidationType.RequiredIncludingDispatchers)
                    ? "Required"
                    : "Not Required");
                    
        }

        if (accountDefault == EntryValidationType.Required)
        {
            if (isDispatcherControl)
                selectedValue = "2";
            else
                selectedValue = "1";
        }
        else if (accountDefault == EntryValidationType.RequiredIncludingDispatchers)
            selectedValue = "1";
        else if (accountDefault == EntryValidationType.NotRequired)
            selectedValue = "2";

        ddl.Items.Clear();

        if (Company.GetById(_account.CompanyId).HasAccessToBaseFeature("newCompletionRequirements"))
        {
            if (!string.IsNullOrWhiteSpace(byInheritanceTypeText))
                ddl.Items.Add(new ListItem(byInheritanceTypeText, "0"));

            ddl.Items.Add(new ListItem("Always Required for this account", "1"));
            ddl.Items.Add(new ListItem("Never Required for this account", "2"));
        }
        else
        {
            ddl.Items.Add(new ListItem("Required", "1"));
            ddl.Items.Add(new ListItem("Not Required", "2"));

            if (selectedValue == "0")
                selectedValue = "2";
        }
        

        ddl.SelectedValue = selectedValue;
    }

    protected bool SaveTextBoxKeyValue(TextBox txtBox, string key)
    {
        if (txtBox != null)
            return SaveKeyValue(txtBox.Text, key);
        else
            return false;
    }

    protected void DeleteKeyValue(string key)
    {
        var kv = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, key).FirstOrDefault();

        if (kv != null)
            kv.Delete();
    }

    protected bool SaveKeyValue(string v, string key)
    {
        var saved = false;

        if (!string.IsNullOrWhiteSpace(v))
        {
            var kv = AccountKeyValue.GetByAccount(_account.CompanyId, _account.Id, Provider.Towbook.ProviderId, key).FirstOrDefault()
                ?? new AccountKeyValue(_account.Id, Provider.Towbook.GetKey(Extric.Towbook.Integration.KeyType.Account, key).Id, "");

            if (kv.Value == "0" && key == "ImportPayments")
                kv.Delete();
            else if (kv.Value != v)
            {
                kv.Value = v;
                kv.Save();
                saved = true;
            }
        }

        return saved;
    }

    protected Account.PreferredBillableMethod GetPreferredBillableMethod(Account acc)
    {
        if (new[] {
                AccountType.MotorClub,
                AccountType.PrivateProperty,
                AccountType.PoliceDepartment,
                AccountType.Individual
            }.Contains(acc.Type))
            return Account.PreferredBillableMethod.Invoice;

        if (acc.Id == 1)
            return Account.PreferredBillableMethod.Invoice;

        return Account.PreferredBillableMethod.Statement;
    }

    public class StickeringReasonTimeItem
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Hours { get; set; }
        public decimal DefaultHours { get; set; }
        public bool Selected { get; set; }
        public int StartFromType { get; set; }
    }
}
