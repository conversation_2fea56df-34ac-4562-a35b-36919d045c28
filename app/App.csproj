<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\Microsoft.VisualStudio.Azure.Containers.Tools.Targets.1.11.1\build\Microsoft.VisualStudio.Azure.Containers.Tools.Targets.props" Condition="Exists('..\packages\Microsoft.VisualStudio.Azure.Containers.Tools.Targets.1.11.1\build\Microsoft.VisualStudio.Azure.Containers.Tools.Targets.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{75F10619-F157-405D-85E7-66214347BCE9}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>App</RootNamespace>
    <AssemblyName>App</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <UseIISExpress>true</UseIISExpress>
    <IISExpressSSLPort>44320</IISExpressSSLPort>
    <IISExpressAnonymousAuthentication>enabled</IISExpressAnonymousAuthentication>
    <IISExpressWindowsAuthentication>disabled</IISExpressWindowsAuthentication>
    <IISExpressUseClassicPipelineMode>false</IISExpressUseClassicPipelineMode>
    <SolutionDir Condition="$(SolutionDir) == '' Or $(SolutionDir) == '*Undefined*'">..\..\</SolutionDir>
    <RestorePackages>true</RestorePackages>
    <UseGlobalApplicationHostFile />
    <TargetFrameworkProfile />
    <Use64BitIISExpress />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <DockerLaunchAction>LaunchBrowser</DockerLaunchAction>
    <DockerLaunchUrl>http://{ServiceIPAddress}</DockerLaunchUrl>
    <_MigrateToProjectGuid>************************************</_MigrateToProjectGuid>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <RestoreProjectStyle>PackageReference</RestoreProjectStyle>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Data.OracleClient" />
    <Reference Include="System.IdentityModel" />
    <Reference Include="System.IO.Compression" />
    <Reference Include="System.Net" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Numerics" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.ServiceProcess" />
    <Reference Include="System.Transactions">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="WindowsBase" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Accounting\AccountCustomerMatching.aspx" />
    <Content Include="Accounting\AccountCustomerMatching.js" />
    <Content Include="Accounting\AccountCustomerMatching2.js" />
    <Content Include="Accounting\AccountManagement.aspx" />
    <Content Include="Accounting\AccountManagement.js" />
    <Content Include="Accounting\arrow.png" />
    <Content Include="Accounting\CustomerManagement.aspx" />
    <Content Include="Accounting\CustomerManagement.js" />
    <Content Include="Accounting\InvoiceManagement.aspx" />
    <Content Include="Accounting\OAuthNegociationSuccess.aspx" />
    <Content Include="Accounting\PaymentMethodMatches.tpl.html" />
    <Content Include="Accounting\PaymentMethodsManagement.aspx" />
    <Content Include="Accounting\PaymentMethodsManagement.js" />
    <Content Include="Accounting\qbAccounts.tpl.html" />
    <Content Include="Accounting\QBConnection.aspx" />
    <Content Include="Accounting\qbCustomers.tpl.html" />
    <Content Include="Accounting\QBPaymentMethods.tpl.html" />
    <Content Include="Accounting\tbCalls.tpl.html" />
    <Content Include="Accounting\twbAccounts.tpl.html" />
    <Content Include="Accounting\twbAccountsQBCustomersMatches.tpl.html" />
    <Content Include="Accounts\date-js.js" />
    <Content Include="Accounts\SubcontractorAdd.aspx" />
    <Content Include="Accounts\Account_Default.js" />
    <Content Include="Accounts\Account_StickersView.js" />
    <Content Include="Accounts\Account_PermitRequestsView.js" />
    <Content Include="Accounts\Account_PermitsView.js" />
    <Content Include="Accounts\EmailInvoices.aspx" />
    <Content Include="Accounts\SubmissionStatus.aspx" />
    <Content Include="Accounts\SubmitInvoices_MissingInvoicesView.js" />
    <Content Include="Accounts\SubmitInvoices_HistoryView.js" />
    <Content Include="Accounts\SubmitInvoices_CallsView.js" />
    <Content Include="Accounts\SubmitInvoices.aspx" />
    <Content Include="Accounts\InvoiceManager.aspx" />
    <Content Include="Accounts\Account.aspx" />
    <Content Include="Accounts\Account_Dashboard.js" />
    <Content Include="Accounts\Account_FilesView.js" />
    <Content Include="Accounts\Account_ImpoundsView.js" />
    <Content Include="Accounts\Account_PaymentsView.js" />
    <Content Include="Accounts\Account_StatementsView.js" />
    <Content Include="Accounts\AccountEditor.aspx" />
    <Content Include="Accounts\AccountStatement.aspx" />
    <Content Include="Accounts\AccountStatementCreate.aspx" />
    <Content Include="Accounts\AddNote.aspx" />
    <Content Include="Accounts\Default.aspx" />
    <Content Include="Accounts\Account_CallsView.js" />
    <Content Include="Accounts\FileUpload.aspx" />
    <Content Include="Accounts\GroupEmail.aspx" />
    <Content Include="Accounts\ImpoundLot.aspx" />
    <Content Include="Accounts\PaymentDetails.aspx" />
    <Content Include="Accounts\PrepareStatements.aspx" />
    <Content Include="Accounts\PushStatement.aspx" />
    <Content Include="Accounts\RateItem.aspx" />
    <Content Include="Accounts\RateList.aspx" />
    <Content Include="Accounts\RecordPayment.aspx" />
    <Content Include="Accounts\SendEmail.aspx" />
    <Content Include="Accounts\Statement.aspx" />
    <Content Include="Accounts\template.aspx" />
    <Content Include="Accounts\PrintInvoices.aspx" />
    <Content Include="Accounts\UnpaidInvoices.aspx" />
    <Content Include="Accounts\User.aspx" />
    <Content Include="Active.aspx" />
    <Content Include="app-dist\**\*.*" />
    <Content Include="AutoDataDirect\Default.aspx" />
    <Content Include="Dispatch10\DispatchSearchWidget.js" />
    <Content Include="Dispatch2\Invoice.aspx" />
    <Content Include="DispatchEditor\commissions.js" />
    <Content Include="Email\ReplayEmail.aspx" />
    <Content Include="Impounds\Auctions.aspx" />
    <Content Include="Impounds\ImpoundStatusUpdate.aspx" />
    <Content Include="Impounds\LetterTemplate.aspx" />
    <Content Include="Impounds\Auction.aspx" />
    <Content Include="MC\ATA\Default.aspx" />
    <Content Include="MC\Billing\ResubmitFailed.aspx" />
    <Content Include="MC\Requests\Default.aspx" />
    <Content Include="MC\Swoop\Confirmation.aspx" />
    <Content Include="Permits\Editor.aspx" />
    <Content Include="Permits\Editor.css" />
    <Content Include="Permits\History.aspx" />
    <Content Include="Permits\Extend.aspx" />
    <Content Include="Permits\Photos.aspx" />
    <Content Include="Permits\PermitViewer.aspx" />
    <Content Include="Accounts\ViewPayments.aspx" />
    <Content Include="B2B\Accounts\Default.aspx" />
    <Content Include="B2B\Accounts\Photo.aspx" />
    <Content Include="B2B\Accounts\Photos.aspx" />
    <Content Include="B2B\Accounts\Request.aspx" />
    <Content Include="B2B\Accounts\ViewInvoice.aspx" />
    <Content Include="B2B\Accounts\ViewStatement.aspx" />
    <Content Include="B2B\Messaging\Default.aspx" />
    <Content Include="B2B\Messaging\template.aspx" />
    <Content Include="B2B\Permits\Default.aspx" />
    <Content Include="B2B\Permits\permits.css" />
    <Content Include="B2B\Permits\permits.js" />
    <Content Include="Permits\PreparePermitCheckout.aspx" />
    <Content Include="Change.aspx" />
    <Content Include="Default.aspx" />
    <Content Include="Dispatch2\AccidentReport\Photos.aspx" />
    <Content Include="Dispatch2\AccidentReport\Report.aspx" />
    <Content Include="Dispatch2\AccidentReport\Default.aspx" />
    <Content Include="Dispatch2\LetterTemplate.aspx" />
    <Content Include="Impounds\PropertyRelease.aspx" />
    <Content Include="Map2\css\map-icons.css" />
    <Content Include="Map2\css\map.css">
      <DependentUpon>map.scss</DependentUpon>
    </Content>
    <Content Include="Map2\css\map.min.css">
      <DependentUpon>map.css</DependentUpon>
    </Content>
    <Content Include="Map2\css\map.scss" />
    <Content Include="Map2\css\panel.css">
      <DependentUpon>panel.scss</DependentUpon>
    </Content>
    <Content Include="Map2\css\panel.min.css">
      <DependentUpon>panel.css</DependentUpon>
    </Content>
    <Content Include="Map2\CallRequest.aspx" />
    <Content Include="Map2\Default.aspx" />
    <Content Include="Map2\fonts\map-icons.eot" />
    <Content Include="Map2\fonts\map-icons.svg" />
    <Content Include="Map2\fonts\map-icons.ttf" />
    <Content Include="Map2\fonts\map-icons.woff" />
    <Content Include="Map2\images\alert1.png" />
    <Content Include="Map2\images\alert_icon-mini.png" />
    <Content Include="Map2\images\alert_icon.png" />
    <Content Include="Map2\images\close.gif" />
    <Content Include="Map2\images\driver-mini.png" />
    <Content Include="Map2\images\driver.png" />
    <Content Include="Map2\images\flag1.png" />
    <Content Include="Map2\images\flag2.png" />
    <Content Include="Map2\images\home-mini.png" />
    <Content Include="Map2\images\home.png" />
    <Content Include="Map2\images\m1.png" />
    <Content Include="Map2\images\m2.png" />
    <Content Include="Map2\images\m3.png" />
    <Content Include="Map2\images\m4.png" />
    <Content Include="Map2\images\m5.png" />
    <Content Include="Map2\images\pin-dispatched.png" />
    <Content Include="Map2\images\pin-inroute.png" />
    <Content Include="Map2\images\pin-onscene.png" />
    <Content Include="Map2\images\pin-orange.png" />
    <Content Include="Map2\images\pin-waiting.png" />
    <Content Include="Map2\images\pin-warning.png" />
    <Content Include="Map2\images\truck-mini.png" />
    <Content Include="Map2\images\truck.png" />
    <Content Include="Map2\images\truck2.png" />
    <Content Include="Map2\images\wrench1.png" />
    <Content Include="Map2\js\panel.js" />
    <Content Include="Map2\js\replay.js" />
    <Content Include="PublicAccess\DamageVideo.aspx" />
    <Content Include="PublicAccess\SV.aspx" />
    <Content Include="PublicAccess\PrintDetails.aspx" />
    <Content Include="PublicAccess\ParkingPermit.aspx" />
    <Content Include="Reports\Auctions\Default.aspx" />
    <Content Include="Reports\CallAnalysis\DamageForms.tpl.html" />
    <Content Include="Reports\CallAnalysis\AccidentReports.tpl.html" />
    <Content Include="Reports\CallAnalysis\CancelledCall4185.tpl.html" />
    <Content Include="Reports\CallAnalysis\DriverActivity.tpl.html" />
    <Content Include="Reports\CallAnalysis\CompanyIncome.tpl.html" />
    <Content Include="Reports\CallAnalysis\MotorClubGeico.tpl.html" />
    <Content Include="Reports\CallAnalysis\PaymentVerification.tpl.html" />
    <Content Include="Reports\CallAnalysis\Performance.tpl.html" />
    <Content Include="Reports\CallAnalysis\CallWorkflow.tpl.html" />
    <Content Include="Reports\CallAnalysis\MotorClub4185.tpl.html" />
    <Content Include="Reports\CallAnalysis\DriverSurveys.tpl.html" />
    <Content Include="Reports\CallAnalysis\SubcontractorBasic.tpl.html" />
    <Content Include="Reports\CallAnalysis\UserCheckInReport.tpl.html" />
    <Content Include="Reports\CallAnalysis\StickerReport.tpl.html" />
    <Content Include="Reports\CallAnalysis\RoadsideSurveys.tpl.html" />
    <Content Include="Reports\CallAnalysis\AuctionPreview.tpl.html" />
    <Content Include="Reports\CallAnalysis\UndeliveredEmails.tpl.html" />
    <Content Include="Reports\ClosedPeriod\Default.aspx" />
    <Content Include="Reports\ReportHistory\Default.aspx" />
    <Content Include="Reports\Stickering\Default.aspx" />
    <Content Include="Reports\Subcontractor\Default.aspx" />
    <Content Include="Reports\Surveys\Default.aspx" />
    <Content Include="RequestLog\RotationStatus.aspx" />
    <Content Include="Security\License.aspx" />
    <Content Include="Security\SloCallback.aspx" />
    <Content Include="Security\SsoCallback.aspx" />
    <Content Include="Settings\MUsers\User.aspx" />
    <Content Include="Stickering\Extend.aspx" />
    <Content Include="SurveyLog\Default.aspx" />
    <Content Include="SurveyLog\reviewGrid.css" />
    <Content Include="SurveyLog\reviewGrid.js" />
    <Content Include="UI\audio\callRequest.mp3" />
    <Content Include="UI\audio\callRequest.ogg" />
    <Content Include="UI\audio\complete.mp3" />
    <Content Include="UI\audio\init.mp3" />
    <Content Include="UI\audio\init.ogg" />
    <Content Include="UI\css\chatV2.css" />
    <Content Include="UI\css\ie-notice.css" />
    <Content Include="UI\css\spectrum.css" />
    <Content Include="UI\error_html.html" />
    <Content Include="UI\fonts\fa\css\all.css" />
    <Content Include="UI\fonts\fa\css\brands.css" />
    <Content Include="UI\fonts\fa\css\fontawesome.css" />
    <Content Include="UI\fonts\fa\css\light.css" />
    <Content Include="UI\fonts\fa\css\regular.css" />
    <Content Include="UI\fonts\fa\css\solid.css" />
    <Content Include="UI\fonts\fa\css\svg-with-js.css" />
    <Content Include="UI\fonts\fa\css\v4-shims.css" />
    <Content Include="UI\fonts\fa\css\v4-shims.min.css" />
    <Content Include="UI\fonts\fa\js\all.js" />
    <Content Include="UI\fonts\fa\js\brands.js" />
    <Content Include="UI\fonts\fa\js\fontawesome.js" />
    <Content Include="UI\fonts\fa\js\light.js" />
    <Content Include="UI\fonts\fa\js\regular.js" />
    <Content Include="UI\fonts\fa\js\solid.js" />
    <Content Include="UI\fonts\fa\js\v4-shims.js" />
    <Content Include="UI\fonts\fa\LICENSE.txt" />
    <Content Include="UI\fonts\fa\sprites\brands.svg" />
    <Content Include="UI\fonts\fa\sprites\light.svg" />
    <Content Include="UI\fonts\fa\sprites\regular.svg" />
    <Content Include="UI\fonts\fa\sprites\solid.svg" />
    <Content Include="UI\fonts\fa\webfonts\fa-brands-400.svg" />
    <Content Include="UI\fonts\fa\webfonts\fa-light-300.svg" />
    <Content Include="UI\fonts\fa\webfonts\fa-regular-400.svg" />
    <Content Include="UI\fonts\fa\webfonts\fa-solid-900.svg" />
    <Content Include="UI\images\2013-R2\edit-property-32.png" />
    <Content Include="UI\images\2013-R2\list.png" />
    <Content Include="UI\images\2013-R2\_chat.png" />
    <Content Include="UI\images\browsers\chrome_512x512.png" />
    <Content Include="UI\images\browsers\edge_512x512.png" />
    <Content Include="UI\images\browsers\firefox_512x512.png" />
    <Content Include="UI\images\close.png" />
    <Content Include="UI\images\connect_quickbooks.png" />
    <Content Include="UI\images\customer.logo\4817_left.jpg" />
    <Content Include="UI\images\customer.logo\7992_left.jpg" />
    <Content Include="UI\images\driver-replay-dot.png" />
    <Content Include="UI\images\facebook.png" />
    <Content Include="UI\images\icons\arrow-up-arrow-down-solid-light.png" />
    <Content Include="UI\images\icons\arrow-up-arrow-down-solid.png" />
    <Content Include="UI\images\icons\map-marker-128-white.png" />
    <Content Include="UI\images\icons\map-marker-128.png" />
    <Content Include="UI\images\icons\map-marker-16-black.png" />
    <Content Include="UI\images\icons\map-marker-16.png" />
    <Content Include="UI\images\icons\map-marker-32.png" />
    <Content Include="UI\images\icons\map-marker-64.png" />
    <Content Include="UI\images\icons\map-marker-dark.png" />
    <Content Include="UI\images\icons\map_marker_dark.png" />
    <Content Include="UI\images\icons\map_marker_light.png" />
    <Content Include="UI\images\icons\marker_home.png" />
    <Content Include="UI\images\icons\marker_lot.png" />
    <Content Include="UI\images\icons\target_dark.png" />
    <Content Include="UI\images\icons\target_light.png" />
    <Content Include="UI\images\instagram.jpg" />
    <Content Include="UI\images\logo\towbook-light.svg" />
    <Content Include="UI\images\logo\towbook.svg" />
    <Content Include="UI\images\map-sample-1.png" />
    <Content Include="UI\images\quickbooks.png" />
    <Content Include="UI\images\replay\replay.gif" />
    <Content Include="UI\css\towbookv2.css" />
    <Content Include="UI\images\square-terminal-device.png" />
    <Content Include="UI\images\star1.png" />
    <Content Include="UI\images\twitter.png" />
    <Content Include="UI\images\vdr\Extric-3-4-door-sedan.png" />
    <Content Include="UI\images\vdr\extric_sedan.png" />
    <Content Include="UI\images\vdr\Extric_truck_1.png" />
    <Content Include="UI\images\vdr\Extric_truck_2.png" />
    <Content Include="UI\images\vdr\Extric_van.png" />
    <Content Include="UI\images\vehicle-lookup-sample.png" />
    <Content Include="UI\images\vehicle-lookup-sample2.png" />
    <Content Include="UI\js\chatv2\dist\main.js" />
    <Content Include="UI\js\chatv2\src\chatbox\chatbox.css" />
    <Content Include="UI\js\chatv2\src\chatbox\Chatbox.js" />
    <Content Include="UI\js\chatv2\src\chatbox\ChatboxContainer.js" />
    <Content Include="UI\js\chatv2\src\chatbox\ChatboxMaximized.js" />
    <Content Include="UI\js\chatv2\src\chatbox\ChatboxMinimized.js" />
    <Content Include="UI\js\chatv2\src\chatbox\Maximized.js" />
    <Content Include="UI\js\chatv2\src\chatbox\Minimized.js" />
    <Content Include="UI\js\chatv2\src\chatContacts\ChatContactList.js" />
    <Content Include="UI\js\chatv2\src\chatContacts\ChatContactListMaximized.js" />
    <Content Include="UI\js\chatv2\src\chatContacts\ChatContactListMinimized.js" />
    <Content Include="UI\js\chatv2\src\chatInbox\ChannelList.js" />
    <Content Include="UI\js\chatv2\src\chatInbox\ChannelListItem.js" />
    <Content Include="UI\js\chatv2\src\chatInbox\chatinbox.css" />
    <Content Include="UI\js\chatv2\src\chatInbox\ChatInboxContainer.js" />
    <Content Include="UI\js\chatv2\src\chatInbox\ChatInboxPanel.js" />
    <Content Include="UI\js\chatv2\src\chatInbox\CompanySelector.js" />
    <Content Include="UI\js\chatv2\src\chatInbox\Tabs.js" />
    <Content Include="UI\js\chatv2\src\common\errors.js" />
    <Content Include="UI\js\chatv2\src\components\Bubble.js" />
    <Content Include="UI\js\chatv2\src\components\common.js" />
    <Content Include="UI\js\chatv2\src\components\dialogs.js" />
    <Content Include="UI\js\chatv2\src\components\index.js" />
    <Content Include="UI\js\chatv2\src\components\Spinner.js" />
    <Content Include="UI\js\chatv2\src\index.js" />
    <Content Include="UI\js\chatv2\src\models\ChannelModel.js" />
    <Content Include="UI\js\chatv2\src\models\ChatModel.js" />
    <Content Include="UI\js\chatv2\src\models\index.js" />
    <Content Include="UI\js\chatv2\src\models\MessageModel.js" />
    <Content Include="UI\js\chatv2\src\models\Model.js" />
    <Content Include="UI\js\chatv2\src\models\UserModel.js" />
    <Content Include="UI\js\chatv2\src\types\index.js" />
    <Content Include="UI\js\chatv2\src\types\twiChannelEvent.js" />
    <Content Include="UI\js\chatv2\src\types\TwiClientEvents.js" />
    <Content Include="UI\js\chatv2\src\utils\apiClient.js" />
    <Content Include="UI\js\chatv2\src\utils\AppTwilioClient.js" />
    <Content Include="UI\js\chatv2\src\utils\callUtils.js" />
    <Content Include="UI\js\chatv2\src\utils\chatUtils.js" />
    <Content Include="UI\js\chatv2\src\utils\collectionUtils.js" />
    <Content Include="UI\js\chatv2\src\utils\commonUtils.js" />
    <Content Include="UI\js\chatv2\src\utils\Logger.js" />
    <Content Include="UI\js\chatv2\src\utils\textUtils.js" />
    <Content Include="UI\js\chatv2\src\utils\twilioUtils.js" />
    <Content Include="UI\js\chatv2\src\utils\userUtils.js" />
    <Content Include="UI\js\chatv2\webpack.common.js" />
    <Content Include="UI\js\chatv2\webpack.dev.js" />
    <Content Include="UI\js\chatv2\webpack.prod.js" />
    <Content Include="UI\js\debug.js" />
    <Content Include="UI\js\decimal.min.js" />
    <Content Include="UI\js\dispatch.js" />
    <Content Include="UI\js\firebase-messaging-sw.js" />
    <Content Include="UI\js\highcharts_src.js" />
    <Content Include="UI\js\ie-notice.js" />
    <Content Include="UI\js\jspdf.min.js" />
    <Content Include="UI\js\jspdf.plugin.autotable.js" />
    <Content Include="UI\js\md5.js" />
    <Content Include="UI\js\messaging\firebase-messaging-sw.js" />
    <Content Include="UI\js\animate.js" />
    <Content Include="Map2\js\events.js" />
    <Content Include="Map2\js\map.js" />
    <Content Include="Map2\js\markerClusterer.js" />
    <Content Include="Map2\js\markerClustererPlus.js" />
    <Content Include="Map2\js\markerwithlabel.js" />
    <Content Include="Map2\js\utils.js" />
    <Content Include="Map2\status\Default.aspx" />
    <Content Include="DS4\styles_classic_view.css">
      <DependentUpon>styles_classic_view.scss</DependentUpon>
    </Content>
    <Content Include="DS4\styles_classic_view.min.css">
      <DependentUpon>styles_classic_view.css</DependentUpon>
    </Content>
    <Content Include="Dispatch\vdr.min.css">
      <DependentUpon>vdr.css</DependentUpon>
    </Content>
    <Content Include="Map\js\markerClustererPlus.js" />
    <Content Include="PublicAccess\PreTripPhoto.aspx" />
    <Content Include="PublicAccess\SignaturePhoto.aspx" />
    <Content Include="PublicAccess\DamagePhoto.aspx" />
    <Content Include="PublicAccess\DamagePhotos.aspx" />
    <Content Include="PublicAccess\Video.aspx" />
    <Content Include="Reports\CallAnalysis\DeletedCalls.tpl.html" />
    <Content Include="UI\css\rangeslider.css" />
    <Content Include="UI\js\messaging\chatV2.js" />
    <Content Include="UI\js\jquery.datetimeentry.min.js" />
    <Content Include="UI\js\jquery.plugin.min.js" />
    <Content Include="DispatchEditor\editor.aspx" />
    <Content Include="DispatchEditor\editor.js" />
    <Content Include="DispatchEditor\editor.css" />
    <Content Include="DispatchEditor\editor.min.css" />
    <Content Include="Dispatch\callItem.js" />
    <Content Include="Dispatch\callList.js" />
    <Content Include="Dispatch\callListSearch.js" />
    <Content Include="Dispatch\Vdr.aspx" />
    <Content Include="Dispatch\Default.aspx" />
    <Content Include="Dispatch\Default.css" />
    <Content Include="Dispatch\dispatch.js" />
    <Content Include="Dispatch\dispatchMenu.js" />
    <Content Include="Dispatch\driversBar.js" />
    <Content Include="Dispatch\Editor.css" />
    <Content Include="Dispatch\JSXTransformer-0.11.0.js" />
    <Content Include="Dispatch\list.js" />
    <Content Include="Dispatch\map.js" />
    <Content Include="Dispatch\pageViewer.js" />
    <Content Include="Dispatch\pusher.js" />
    <Content Include="Dispatch\react-0.11.0.js" />
    <Content Include="Dispatch\realTime.js" />
    <Content Include="Dispatch\reusableUI.css" />
    <Content Include="Dispatch\reusableUI.js" />
    <Content Include="App_Data\itfoxtec.identity.saml2.testwebapp_Certificate.pfx" />
    <None Include=".dockerignore">
      <DependentUpon>Dockerfile</DependentUpon>
    </None>
    <None Include="compilerconfig.json" />
    <None Include="compilerconfig.json.defaults">
      <DependentUpon>compilerconfig.json</DependentUpon>
    </None>
    <Content Include="Dispatch\vdr.css" />
    <Content Include="Dispatch10\Default.css" />
    <Content Include="Dispatch10\Default.aspx" />
    <Content Include="Dispatch10\Default.aspx.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Dispatch10\Default.js" />
    <Content Include="DS4\Default.aspx" />
    <Content Include="DS4\Default.js" />
    <Content Include="DS4\DispatchSearchWidget.js" />
    <Content Include="DS4\styles.css">
      <DependentUpon>styles.scss</DependentUpon>
    </Content>
    <Content Include="DS4\styles.min.css">
      <DependentUpon>styles.css</DependentUpon>
    </Content>
    <Content Include="DS4\styles_card_view.css">
      <DependentUpon>styles_card_view.scss</DependentUpon>
    </Content>
    <Content Include="DS4\styles_card_view.min.css">
      <DependentUpon>styles_card_view.css</DependentUpon>
    </Content>
    <Content Include="DS4\styles_grid_view.css">
      <DependentUpon>styles_grid_view.scss</DependentUpon>
    </Content>
    <Content Include="DS4\styles_grid_view.min.css">
      <DependentUpon>styles_grid_view.css</DependentUpon>
    </Content>
    <Content Include="DS4\styles_one_line_view.css">
      <DependentUpon>styles_one_line_view.scss</DependentUpon>
    </Content>
    <Content Include="DS4\styles_one_line_view.min.css">
      <DependentUpon>styles_one_line_view.css</DependentUpon>
    </Content>
    <Content Include="dyreq\Home_DispatchStatistics.aspx" />
    <Content Include="dyreq\RateItemList.aspx" />
    <Content Include="dyreq\TicketExists.aspx" />
    <Content Include="dyreq\UserExists.aspx" />
    <Content Include="dyreq\VehicleModels.aspx" />
    <Content Include="dyreq\windows\accounts\SearchByTag.aspx" />
    <Content Include="dyreq\windows\dispatching\Search.aspx" />
    <Content Include="dyreq\windows\dispatching\Search2.aspx" />
    <Content Include="dyreq\windows\stickering\Search.aspx" />
    <Content Include="Global.asax" />
    <Content Include="Impounds\Default.aspx" />
    <Content Include="Impounds\GenerateReport.js" />
    <Content Include="Impounds\GenerateReportSave.js" />
    <Content Include="Impounds\InternalReport.aspx" />
    <Content Include="Management\Billing\Default.aspx" />
    <Content Include="Management\Clients\DigitalAccountAdd.aspx" />
    <Content Include="Management\Clients\DigitalAccounts.aspx" />
    <Content Include="Management\Clients\EmailForwarderAdd.aspx" />
    <Content Include="Management\Clients\FeatureAdd.aspx" />
    <Content Include="Management\Clients\SetupEmail.aspx" />
    <Content Include="Map\css\map-icons.css" />
    <Content Include="Map\js\events.js" />
    <Content Include="Map\Default.aspx" />
    <Content Include="Map\js\utils.js" />
    <Content Include="Map\js\markerwithlabel.js" />
    <Content Include="Map\images\pin-dispatched.png" />
    <Content Include="Map\images\pin-inroute.png" />
    <Content Include="Map\images\pin-onscene.png" />
    <Content Include="Map\images\pin-orange.png" />
    <Content Include="Map\images\pin-waiting.png" />
    <Content Include="Map\images\pin-warning.png" />
    <Content Include="Map\js\map.js" />
    <Content Include="Map\status\Default.aspx" />
    <Content Include="Reports\Trucks\PreTripInspection.aspx" />
    <Content Include="Reports\CallAnalysis\PreTripInspections.tpl.html" />
    <Content Include="Reports\CallAnalysis\Safeclear.tpl.html" />
    <Content Include="Reports\CallAnalysis\IncomeByUser.tpl.html" />
    <Content Include="RequestLog\css\requestLog.css" />
    <Content Include="RequestLog\Default.aspx" />
    <Content Include="Impounds\AutoDataDirect.aspx" />
    <Content Include="Impounds\AutoDataDirect.js" />
    <Content Include="Impounds\Editor.aspx" />
    <Content Include="Impounds\Email.aspx" />
    <Content Include="Impounds\GenerateReport.aspx" />
    <Content Include="Impounds\Impound.aspx" />
    <Content Include="Impounds\Impound.js" />
    <Content Include="Impounds\ImpoundDelete.aspx" />
    <Content Include="Impounds\ImpoundPrint.aspx" />
    <Content Include="Impounds\ImpoundReminderTaskDetails.aspx" />
    <Content Include="Impounds\ImpoundReminderTaskDetails.js" />
    <Content Include="Impounds\ImpoundSearchWidget.js" />
    <Content Include="Impounds\ImpoundSummaryOfCharges.aspx" />
    <Content Include="Impounds\ImpoundTasks.tpl.html" />
    <Content Include="Impounds\ImpoundTasksDashboard.tpl.html" />
    <Content Include="Impounds\ImpoundTasksDetails.tpl.html" />
    <Content Include="Impounds\Invoice.aspx" />
    <Content Include="Impounds\Note.aspx" />
    <Content Include="Impounds\Photo.aspx" />
    <Content Include="Impounds\PhotoUpload.aspx" />
    <Content Include="Impounds\Print.aspx" />
    <Content Include="Impounds\RecordPayment.aspx" />
    <Content Include="Impounds\Release.aspx" />
    <Content Include="Impounds\Release.js" />
    <Content Include="Impounds\ReleasePickedUp.aspx" />
    <Content Include="Impounds\Report.aspx" />
    <Content Include="Impounds\SummaryOfCharges.aspx" />
    <Content Include="Impounds\TemplatePdf.aspx" />
    <Content Include="Management\Clients\Client.aspx" />
    <Content Include="Management\Clients\ClientsReport.js" />
    <Content Include="Management\Clients\Companies.aspx" />
    <Content Include="Management\Clients\Default.aspx" />
    <Content Include="Management\Clients\Internalclients.tpl.html" />
    <Content Include="Management\Clients\Note.aspx" />
    <Content Include="Management\Clients\RecentLogins.aspx" />
    <Content Include="Management\Communications\Communications.js" />
    <Content Include="Management\Communications\Default.aspx" />
    <Content Include="Management\Communications\MessageTemplateDetails.aspx" />
    <Content Include="Management\Communications\MessageTemplateDetails.js" />
    <Content Include="Management\Communications\twbMessageTemplates.tpl.html" />
    <Content Include="Management\Default.aspx" />
    <Content Include="Management\Reports\Default.aspx" />
    <Content Include="Management\Tickets\Default.aspx" />
    <Content Include="MC\Agero\Confirmation.aspx" />
    <Content Include="MC\Agero\SignIn.aspx" />
    <Content Include="MC\Agero\SignOut.aspx" />
    <Content Include="PublicAccess\Impound.aspx" />
    <Content Include="PublicAccess\Invoice2.aspx" />
    <Content Include="PublicAccess\Photo.aspx" />
    <Content Include="PublicAccess\Photos.aspx" />
    <Content Include="Reports\Accounting\Default.aspx" />
    <Content Include="Reports\CallAnalysis\AccountPayments.tpl.html" />
    <Content Include="Reports\CallAnalysis\AccountPerformance.tpl.html" />
    <Content Include="Reports\CallAnalysis\AccountReceivable.tpl.html" />
    <Content Include="Reports\CallAnalysis\CallAnalysis.css" />
    <Content Include="Reports\CallAnalysis\CallAnalysis.js" />
    <Content Include="Reports\CallAnalysis\CallAnalysis.tpl.html" />
    <Content Include="Reports\CallAnalysis\CancelledCall.tpl.html" />
    <Content Include="Reports\CallAnalysis\CommissionSummary.tpl.html" />
    <Content Include="Reports\CallAnalysis\Default.aspx" />
    <Content Include="Reports\CallAnalysis\DispatchAnalysis.tpl.html" />
    <Content Include="Reports\CallAnalysis\IncomeSummary.tpl.html" />
    <Content Include="Reports\CallAnalysis\MotorClub.tpl.html" />
    <Content Include="Reports\CallAnalysis\RevenueIncome.tpl.html" />
    <Content Include="Reports\CallAnalysis\RevenuePerformance.tpl.html" />
    <Content Include="Reports\CallAnalysis\SalesTax.tpl.html" />
    <Content Include="Reports\CallAnalysis\TruckExpenses.tpl.html" />
    <Content Include="Reports\CallAnalysis\Utilization.tpl.html" />
    <Content Include="Reports\CallAnalysis\RotationActivity.tpl.html" />
    <Content Include="Reports\Custom\Default.aspx" />
    <Content Include="Reports\Default.aspx" />
    <Content Include="Reports\Dispatching\Default.aspx" />
    <Content Include="Reports\Dispatching\Income.aspx" />
    <Content Include="Reports\Dispatching\OverallIncome.aspx" />
    <Content Include="Reports\Dispatching\Viewer.aspx" />
    <Content Include="Reports\Dispatching\Viewer2.aspx" />
    <Content Include="Reports\Financial\Default.aspx" />
    <Content Include="Reports\Impounds\Default.aspx" />
    <Content Include="Reports\Payroll\Default.aspx" />
    <Content Include="Reports\Payroll\DriverCommission.aspx" />
    <Content Include="Reports\Personal\Default.aspx" />
    <Content Include="Reports\reports.css" />
    <Content Include="Reports\Revenue\RateItems.aspx" />
    <Content Include="Reports\Trucks\Default.aspx" />
    <Content Include="Security\ErrorReport.aspx" />
    <Content Include="Security\ResetPassword.aspx" />
    <Content Include="Security\SecurityQuestions.aspx" />
    <Content Include="Security\Login.aspx" />
    <Content Include="Security\Logout.aspx" />
    <Content Include="Security\Pusher.aspx" />
    <Content Include="ServiceNotifications\ServiceNotificationsDashboard.tpl.html" />
    <Content Include="Settings\Default.aspx" />
    <Content Include="Settings\MCompany\Default.aspx" />
    <Content Include="Settings\MUsers\Default.aspx" />
    <Content Include="Settings\MAddressBook\Default.aspx" />
    <Content Include="Signup\default.aspx" />
    <Content Include="Signup\Default.aspx.js" />
    <Content Include="Stickering\Default.aspx" />
    <Content Include="Stickering\Default.css" />
    <Content Include="Stickering\Default.js" />
    <Content Include="Stickering\Delete.aspx" />
    <Content Include="Stickering\Editor.aspx" />
    <Content Include="Stickering\Editor.css" />
    <Content Include="Stickering\Editor.js" />
    <Content Include="Stickering\Photos.aspx" />
    <Content Include="Stickering\Settings.aspx" />
    <Content Include="Stickering\Settings.js" />
    <Content Include="Stickering\StickerSearchWidget.js" />
    <Content Include="Stickering\Viewer.aspx" />
    <Content Include="Storage\dispatchEntries\Photos\2\24\3.jpg" />
    <Content Include="Storage\dispatchEntries\Photos\2\24\4.jpg" />
    <Content Include="Storage\Impounds\Photos\1\11\34.jpg" />
    <Content Include="Storage\Impounds\Photos\1\12\36.jpg" />
    <Content Include="Storage\Impounds\Photos\1\2\12.jpg" />
    <Content Include="Storage\Impounds\Photos\1\2\15.jpg" />
    <Content Include="Storage\Impounds\Photos\1\2\16.jpg" />
    <Content Include="Storage\Impounds\Photos\1\2\17.jpg" />
    <Content Include="Storage\Impounds\Photos\1\2\28.jpg" />
    <Content Include="Storage\Impounds\Photos\1\43\38.jpg" />
    <Content Include="Storage\Impounds\Photos\1\46\39.jpg" />
    <Content Include="Storage\Impounds\Photos\1\48\40.jpg" />
    <Content Include="Storage\Impounds\Photos\1\5\33.jpg" />
    <Content Include="Storage\Impounds\Photos\1\9\35.jpg" />
    <Content Include="Storage\Impounds\Photos\2\165\4409.jpg" />
    <Content Include="Storage\Impounds\Photos\2\165\4410.jpg" />
    <Content Include="Storage\Impounds\Photos\2\471\153.jpg" />
    <Content Include="Storage\Impounds\Photos\2\471\154.jpg" />
    <Content Include="Storage\Impounds\Photos\2\471\4381.jpg" />
    <Content Include="Storage\Impounds\Photos\2\660\152.jpg" />
    <Content Include="Support\Default.aspx" />
    <Content Include="Support\Suggestions.aspx" />
    <Content Include="UI\audio\alert.mp3" />
    <Content Include="UI\css\application-forms.css" />
    <Content Include="UI\css\application-menus.css" />
    <Content Include="UI\css\font-awesome.css" />
    <Content Include="UI\css\font-awesome.min.css" />
    <Content Include="UI\css\ibox.css" />
    <Content Include="UI\css\InvoicesForPrint.css" />
    <Content Include="UI\css\jquery-ui-demos.css" />
    <Content Include="UI\css\jquery-ui.css" />
    <Content Include="UI\css\jquery.jscrollpane.css" />
    <Content Include="UI\css\jquery.mCustomScrollbar.css" />
    <Content Include="UI\css\lightview.css" />
    <Content Include="UI\css\menu.css" />
    <Content Include="UI\css\niceforms-default.css" />
    <Content Include="UI\css\print.css" />
    <Content Include="UI\css\theme\images\ui-bg_diagonals-small_25_161d27_40x40.png" />
    <Content Include="UI\css\theme\images\ui-bg_flat_0_aaaaaa_40x100.png" />
    <Content Include="UI\css\theme\images\ui-bg_flat_0_eeeeee_40x100.png" />
    <Content Include="UI\css\theme\images\ui-bg_flat_33_172231_40x100.png" />
    <Content Include="UI\css\theme\images\ui-bg_flat_55_ffffff_40x100.png" />
    <Content Include="UI\css\theme\images\ui-bg_flat_75_ffffff_40x100.png" />
    <Content Include="UI\css\theme\images\ui-bg_glass_55_fcf0ba_1x400.png" />
    <Content Include="UI\css\theme\images\ui-bg_glass_65_ffffff_1x400.png" />
    <Content Include="UI\css\theme\images\ui-bg_gloss-wave_100_f9f6f6_500x100.png" />
    <Content Include="UI\css\theme\images\ui-bg_highlight-hard_100_ffffff_1x100.png" />
    <Content Include="UI\css\theme\images\ui-bg_highlight-hard_25_5e9433_1x100.png" />
    <Content Include="UI\css\theme\images\ui-bg_highlight-soft_100_f6f6f6_1x100.png" />
    <Content Include="UI\css\theme\images\ui-bg_highlight-soft_25_0073ea_1x100.png" />
    <Content Include="UI\css\theme\images\ui-bg_highlight-soft_25_89bc57_1x100.png" />
    <Content Include="UI\css\theme\images\ui-bg_highlight-soft_25_e8e8e8_1x100.png" />
    <Content Include="UI\css\theme\images\ui-bg_highlight-soft_50_dddddd_1x100.png" />
    <Content Include="UI\css\theme\images\ui-bg_highlight-soft_95_ffedad_1x100.png" />
    <Content Include="UI\css\theme\images\ui-icons_0073ea_256x240.png" />
    <Content Include="UI\css\theme\images\ui-icons_454545_256x240.png" />
    <Content Include="UI\css\theme\images\ui-icons_666666_256x240.png" />
    <Content Include="UI\css\theme\images\ui-icons_808080_256x240.png" />
    <Content Include="UI\css\theme\images\ui-icons_847e71_256x240.png" />
    <Content Include="UI\css\theme\images\ui-icons_8dc262_256x240.png" />
    <Content Include="UI\css\theme\images\ui-icons_cd0a0a_256x240.png" />
    <Content Include="UI\css\theme\images\ui-icons_eeeeee_256x240.png" />
    <Content Include="UI\css\theme\images\ui-icons_ff0084_256x240.png" />
    <Content Include="UI\css\theme\images\ui-icons_ffffff_256x240.png" />
    <Content Include="UI\css\theme\jquery-ui-1.8.21.custom.css" />
    <Content Include="UI\css\tipTip.css" />
    <Content Include="UI\css\towbook-home.css" />
    <Content Include="UI\css\towbook.css" />
    <Content Include="UI\css\towgrid.css" />
    <Content Include="UI\css\ui-icons_222222_256x240.png" />
    <Content Include="UI\css\ui-icons_888888_256x240.png" />
    <Content Include="UI\css\ui-icons_cd0a0a_256x240.png" />
    <Content Include="UI\fonts\fontawesome-webfont.svg" />
    <Content Include="UI\images\2013-R2\add.png" />
    <Content Include="UI\images\2013-R2\driver.png" />
    <Content Include="UI\images\2013-R2\drivers.png" />
    <Content Include="UI\images\2013-R2\edit.png" />
    <Content Include="UI\images\2013-R2\edit16.png" />
    <Content Include="UI\images\2013-R2\full.png" />
    <Content Include="UI\images\2013-R2\location.png" />
    <Content Include="UI\images\2013-R2\mail.png" />
    <Content Include="UI\images\2013-R2\search.png" />
    <Content Include="UI\images\2013-R2\settings.png" />
    <Content Include="UI\images\2013-R2\xadd.png" />
    <Content Include="UI\images\2013-R2\_alerts.png" />
    <Content Include="UI\images\2013-R2\_help.png" />
    <Content Include="UI\images\2013-R2\_logout.png" />
    <Content Include="UI\images\ac0005-48.gif" />
    <Content Include="UI\images\ac0009-48.gif" />
    <Content Include="UI\images\ac0020-48.gif" />
    <Content Include="UI\images\address-book.gif" />
    <Content Include="UI\images\ajax-loader-black.gif" />
    <Content Include="UI\images\ajax-loader.gif" />
    <Content Include="UI\images\ajax-loading1.gif" />
    <Content Include="UI\images\ajax-loading2.gif" />
    <Content Include="UI\images\black-gradient.gif" />
    <Content Include="UI\images\black-gradient.png" />
    <Content Include="UI\images\bubble.png" />
    <Content Include="UI\images\Copy of black-gradient.png" />
    <Content Include="UI\images\customer-login.jpg" />
    <Content Include="UI\images\customers.gif" />
    <Content Include="UI\images\dispatching.gif" />
    <Content Include="UI\images\forms\col-description-darkred.jpg" />
    <Content Include="UI\images\forms\col-description-hover.jpg" />
    <Content Include="UI\images\forms\col-description-red.JPG" />
    <Content Include="UI\images\forms\col-description.jpg" />
    <Content Include="UI\images\forms\Copy of input-button.jpg" />
    <Content Include="UI\images\forms\footer.jpg" />
    <Content Include="UI\images\forms\header.jpg" />
    <Content Include="UI\images\forms\input-button.jpg" />
    <Content Include="UI\images\h1-bg.jpg" />
    <Content Include="UI\images\icons.png" />
    <Content Include="UI\images\icons\accept.png" />
    <Content Include="UI\images\icons\add-plus-dark.png" />
    <Content Include="UI\images\icons\add-plus.png" />
    <Content Include="UI\images\icons\add.png" />
    <Content Include="UI\images\icons\anchor.png" />
    <Content Include="UI\images\icons\application.png" />
    <Content Include="UI\images\icons\application_add.png" />
    <Content Include="UI\images\icons\application_cascade.png" />
    <Content Include="UI\images\icons\application_delete.png" />
    <Content Include="UI\images\icons\application_double.png" />
    <Content Include="UI\images\icons\application_edit.png" />
    <Content Include="UI\images\icons\application_error.png" />
    <Content Include="UI\images\icons\application_form.png" />
    <Content Include="UI\images\icons\application_form_add.png" />
    <Content Include="UI\images\icons\application_form_delete.png" />
    <Content Include="UI\images\icons\application_form_edit.png" />
    <Content Include="UI\images\icons\application_form_magnify.png" />
    <Content Include="UI\images\icons\application_get.png" />
    <Content Include="UI\images\icons\application_go.png" />
    <Content Include="UI\images\icons\application_home.png" />
    <Content Include="UI\images\icons\application_key.png" />
    <Content Include="UI\images\icons\application_lightning.png" />
    <Content Include="UI\images\icons\application_link.png" />
    <Content Include="UI\images\icons\application_osx.png" />
    <Content Include="UI\images\icons\application_osx_terminal.png" />
    <Content Include="UI\images\icons\application_put.png" />
    <Content Include="UI\images\icons\application_side_boxes.png" />
    <Content Include="UI\images\icons\application_side_contract.png" />
    <Content Include="UI\images\icons\application_side_expand.png" />
    <Content Include="UI\images\icons\application_side_list.png" />
    <Content Include="UI\images\icons\application_side_tree.png" />
    <Content Include="UI\images\icons\application_split.png" />
    <Content Include="UI\images\icons\application_tile_horizontal.png" />
    <Content Include="UI\images\icons\application_tile_vertical.png" />
    <Content Include="UI\images\icons\application_view_columns.png" />
    <Content Include="UI\images\icons\application_view_detail.png" />
    <Content Include="UI\images\icons\application_view_gallery.png" />
    <Content Include="UI\images\icons\application_view_icons.png" />
    <Content Include="UI\images\icons\application_view_list.png" />
    <Content Include="UI\images\icons\application_view_tile.png" />
    <Content Include="UI\images\icons\application_xp.png" />
    <Content Include="UI\images\icons\application_xp_terminal.png" />
    <Content Include="UI\images\icons\arrow_branch.png" />
    <Content Include="UI\images\icons\arrow_divide.png" />
    <Content Include="UI\images\icons\arrow_down.png" />
    <Content Include="UI\images\icons\arrow_in.png" />
    <Content Include="UI\images\icons\arrow_inout.png" />
    <Content Include="UI\images\icons\arrow_join.png" />
    <Content Include="UI\images\icons\arrow_left.png" />
    <Content Include="UI\images\icons\arrow_merge.png" />
    <Content Include="UI\images\icons\arrow_out.png" />
    <Content Include="UI\images\icons\arrow_redo.png" />
    <Content Include="UI\images\icons\arrow_refresh.png" />
    <Content Include="UI\images\icons\arrow_refresh_small.png" />
    <Content Include="UI\images\icons\arrow_right.png" />
    <Content Include="UI\images\icons\arrow_rotate_anticlockwise.png" />
    <Content Include="UI\images\icons\arrow_rotate_clockwise.png" />
    <Content Include="UI\images\icons\arrow_switch.png" />
    <Content Include="UI\images\icons\arrow_turn_left.png" />
    <Content Include="UI\images\icons\arrow_turn_right.png" />
    <Content Include="UI\images\icons\arrow_undo.png" />
    <Content Include="UI\images\icons\arrow_up.png" />
    <Content Include="UI\images\icons\asterisk_orange.png" />
    <Content Include="UI\images\icons\asterisk_yellow.png" />
    <Content Include="UI\images\icons\attach.png" />
    <Content Include="UI\images\icons\award_star_add.png" />
    <Content Include="UI\images\icons\award_star_bronze_1.png" />
    <Content Include="UI\images\icons\award_star_bronze_2.png" />
    <Content Include="UI\images\icons\award_star_bronze_3.png" />
    <Content Include="UI\images\icons\award_star_delete.png" />
    <Content Include="UI\images\icons\award_star_gold_1.png" />
    <Content Include="UI\images\icons\award_star_gold_2.png" />
    <Content Include="UI\images\icons\award_star_gold_3.png" />
    <Content Include="UI\images\icons\award_star_silver_1.png" />
    <Content Include="UI\images\icons\award_star_silver_2.png" />
    <Content Include="UI\images\icons\award_star_silver_3.png" />
    <Content Include="UI\images\icons\basket.png" />
    <Content Include="UI\images\icons\basket_add.png" />
    <Content Include="UI\images\icons\basket_delete.png" />
    <Content Include="UI\images\icons\basket_edit.png" />
    <Content Include="UI\images\icons\basket_error.png" />
    <Content Include="UI\images\icons\basket_go.png" />
    <Content Include="UI\images\icons\basket_put.png" />
    <Content Include="UI\images\icons\basket_remove.png" />
    <Content Include="UI\images\icons\bell.png" />
    <Content Include="UI\images\icons\bell_add.png" />
    <Content Include="UI\images\icons\bell_delete.png" />
    <Content Include="UI\images\icons\bell_error.png" />
    <Content Include="UI\images\icons\bell_go.png" />
    <Content Include="UI\images\icons\bell_link.png" />
    <Content Include="UI\images\icons\bin.png" />
    <Content Include="UI\images\icons\bin_closed.png" />
    <Content Include="UI\images\icons\bin_empty.png" />
    <Content Include="UI\images\icons\bomb.png" />
    <Content Include="UI\images\icons\book.png" />
    <Content Include="UI\images\icons\book_add.png" />
    <Content Include="UI\images\icons\book_addresses.png" />
    <Content Include="UI\images\icons\book_delete.png" />
    <Content Include="UI\images\icons\book_edit.png" />
    <Content Include="UI\images\icons\book_error.png" />
    <Content Include="UI\images\icons\book_go.png" />
    <Content Include="UI\images\icons\book_key.png" />
    <Content Include="UI\images\icons\book_link.png" />
    <Content Include="UI\images\icons\book_next.png" />
    <Content Include="UI\images\icons\book_open.png" />
    <Content Include="UI\images\icons\book_previous.png" />
    <Content Include="UI\images\icons\box.png" />
    <Content Include="UI\images\icons\brick.png" />
    <Content Include="UI\images\icons\bricks.png" />
    <Content Include="UI\images\icons\brick_add.png" />
    <Content Include="UI\images\icons\brick_delete.png" />
    <Content Include="UI\images\icons\brick_edit.png" />
    <Content Include="UI\images\icons\brick_error.png" />
    <Content Include="UI\images\icons\brick_go.png" />
    <Content Include="UI\images\icons\brick_link.png" />
    <Content Include="UI\images\icons\briefcase.png" />
    <Content Include="UI\images\icons\bug.png" />
    <Content Include="UI\images\icons\bug_add.png" />
    <Content Include="UI\images\icons\bug_delete.png" />
    <Content Include="UI\images\icons\bug_edit.png" />
    <Content Include="UI\images\icons\bug_error.png" />
    <Content Include="UI\images\icons\bug_go.png" />
    <Content Include="UI\images\icons\bug_link.png" />
    <Content Include="UI\images\icons\building.png" />
    <Content Include="UI\images\icons\building_add.png" />
    <Content Include="UI\images\icons\building_delete.png" />
    <Content Include="UI\images\icons\building_edit.png" />
    <Content Include="UI\images\icons\building_error.png" />
    <Content Include="UI\images\icons\building_go.png" />
    <Content Include="UI\images\icons\building_key.png" />
    <Content Include="UI\images\icons\building_link.png" />
    <Content Include="UI\images\icons\bullet_add.png" />
    <Content Include="UI\images\icons\bullet_arrow_bottom.png" />
    <Content Include="UI\images\icons\bullet_arrow_down.png" />
    <Content Include="UI\images\icons\bullet_arrow_top.png" />
    <Content Include="UI\images\icons\bullet_arrow_up.png" />
    <Content Include="UI\images\icons\bullet_black.png" />
    <Content Include="UI\images\icons\bullet_blue.png" />
    <Content Include="UI\images\icons\bullet_delete.png" />
    <Content Include="UI\images\icons\bullet_disk.png" />
    <Content Include="UI\images\icons\bullet_error.png" />
    <Content Include="UI\images\icons\bullet_feed.png" />
    <Content Include="UI\images\icons\bullet_go.png" />
    <Content Include="UI\images\icons\bullet_green.png" />
    <Content Include="UI\images\icons\bullet_key.png" />
    <Content Include="UI\images\icons\bullet_orange.png" />
    <Content Include="UI\images\icons\bullet_picture.png" />
    <Content Include="UI\images\icons\bullet_pink.png" />
    <Content Include="UI\images\icons\bullet_purple.png" />
    <Content Include="UI\images\icons\bullet_red.png" />
    <Content Include="UI\images\icons\bullet_star.png" />
    <Content Include="UI\images\icons\bullet_toggle_minus.png" />
    <Content Include="UI\images\icons\bullet_toggle_plus.png" />
    <Content Include="UI\images\icons\bullet_white.png" />
    <Content Include="UI\images\icons\bullet_wrench.png" />
    <Content Include="UI\images\icons\bullet_yellow.png" />
    <Content Include="UI\images\icons\cake.png" />
    <Content Include="UI\images\icons\calculator.png" />
    <Content Include="UI\images\icons\calculator_add.png" />
    <Content Include="UI\images\icons\calculator_delete.png" />
    <Content Include="UI\images\icons\calculator_edit.png" />
    <Content Include="UI\images\icons\calculator_error.png" />
    <Content Include="UI\images\icons\calculator_link.png" />
    <Content Include="UI\images\icons\calendar.png" />
    <Content Include="UI\images\icons\calendar_add.png" />
    <Content Include="UI\images\icons\calendar_delete.png" />
    <Content Include="UI\images\icons\calendar_edit.png" />
    <Content Include="UI\images\icons\calendar_link.png" />
    <Content Include="UI\images\icons\calendar_view_day.png" />
    <Content Include="UI\images\icons\calendar_view_month.png" />
    <Content Include="UI\images\icons\calendar_view_week.png" />
    <Content Include="UI\images\icons\camera.png" />
    <Content Include="UI\images\icons\camera_add.png" />
    <Content Include="UI\images\icons\camera_delete.png" />
    <Content Include="UI\images\icons\camera_edit.png" />
    <Content Include="UI\images\icons\camera_error.png" />
    <Content Include="UI\images\icons\camera_go.png" />
    <Content Include="UI\images\icons\camera_link.png" />
    <Content Include="UI\images\icons\camera_small.png" />
    <Content Include="UI\images\icons\cancel.png" />
    <Content Include="UI\images\icons\car.png" />
    <Content Include="UI\images\icons\cart.png" />
    <Content Include="UI\images\icons\cart_add.png" />
    <Content Include="UI\images\icons\cart_delete.png" />
    <Content Include="UI\images\icons\cart_edit.png" />
    <Content Include="UI\images\icons\cart_error.png" />
    <Content Include="UI\images\icons\cart_go.png" />
    <Content Include="UI\images\icons\cart_put.png" />
    <Content Include="UI\images\icons\cart_remove.png" />
    <Content Include="UI\images\icons\car_add.png" />
    <Content Include="UI\images\icons\car_delete.png" />
    <Content Include="UI\images\icons\cd.png" />
    <Content Include="UI\images\icons\cd_add.png" />
    <Content Include="UI\images\icons\cd_burn.png" />
    <Content Include="UI\images\icons\cd_delete.png" />
    <Content Include="UI\images\icons\cd_edit.png" />
    <Content Include="UI\images\icons\cd_eject.png" />
    <Content Include="UI\images\icons\cd_go.png" />
    <Content Include="UI\images\icons\chart_bar.png" />
    <Content Include="UI\images\icons\chart_bar_add.png" />
    <Content Include="UI\images\icons\chart_bar_delete.png" />
    <Content Include="UI\images\icons\chart_bar_edit.png" />
    <Content Include="UI\images\icons\chart_bar_error.png" />
    <Content Include="UI\images\icons\chart_bar_link.png" />
    <Content Include="UI\images\icons\chart_curve.png" />
    <Content Include="UI\images\icons\chart_curve_add.png" />
    <Content Include="UI\images\icons\chart_curve_delete.png" />
    <Content Include="UI\images\icons\chart_curve_edit.png" />
    <Content Include="UI\images\icons\chart_curve_error.png" />
    <Content Include="UI\images\icons\chart_curve_go.png" />
    <Content Include="UI\images\icons\chart_curve_link.png" />
    <Content Include="UI\images\icons\chart_line.png" />
    <Content Include="UI\images\icons\chart_line_add.png" />
    <Content Include="UI\images\icons\chart_line_delete.png" />
    <Content Include="UI\images\icons\chart_line_edit.png" />
    <Content Include="UI\images\icons\chart_line_error.png" />
    <Content Include="UI\images\icons\chart_line_link.png" />
    <Content Include="UI\images\icons\chart_organisation.png" />
    <Content Include="UI\images\icons\chart_organisation_add.png" />
    <Content Include="UI\images\icons\chart_organisation_delete.png" />
    <Content Include="UI\images\icons\chart_pie.png" />
    <Content Include="UI\images\icons\chart_pie_add.png" />
    <Content Include="UI\images\icons\chart_pie_delete.png" />
    <Content Include="UI\images\icons\chart_pie_edit.png" />
    <Content Include="UI\images\icons\chart_pie_error.png" />
    <Content Include="UI\images\icons\chart_pie_link.png" />
    <Content Include="UI\images\icons\checkbox.png" />
    <Content Include="UI\images\icons\checkmark.png" />
    <Content Include="UI\images\icons\clock.png" />
    <Content Include="UI\images\icons\clock_add.png" />
    <Content Include="UI\images\icons\clock_delete.png" />
    <Content Include="UI\images\icons\clock_edit.png" />
    <Content Include="UI\images\icons\clock_error.png" />
    <Content Include="UI\images\icons\clock_go.png" />
    <Content Include="UI\images\icons\clock_link.png" />
    <Content Include="UI\images\icons\clock_pause.png" />
    <Content Include="UI\images\icons\clock_play.png" />
    <Content Include="UI\images\icons\clock_red.png" />
    <Content Include="UI\images\icons\clock_stop.png" />
    <Content Include="UI\images\icons\close.png" />
    <Content Include="UI\images\icons\cog.png" />
    <Content Include="UI\images\icons\cog_add.png" />
    <Content Include="UI\images\icons\cog_delete.png" />
    <Content Include="UI\images\icons\cog_edit.png" />
    <Content Include="UI\images\icons\cog_error.png" />
    <Content Include="UI\images\icons\cog_go.png" />
    <Content Include="UI\images\icons\coins.png" />
    <Content Include="UI\images\icons\coins_add.png" />
    <Content Include="UI\images\icons\coins_delete.png" />
    <Content Include="UI\images\icons\color_swatch.png" />
    <Content Include="UI\images\icons\color_wheel.png" />
    <Content Include="UI\images\icons\comment.png" />
    <Content Include="UI\images\icons\comments.png" />
    <Content Include="UI\images\icons\comments_add.png" />
    <Content Include="UI\images\icons\comments_delete.png" />
    <Content Include="UI\images\icons\comment_add.png" />
    <Content Include="UI\images\icons\comment_delete.png" />
    <Content Include="UI\images\icons\comment_edit.png" />
    <Content Include="UI\images\icons\compress.png" />
    <Content Include="UI\images\icons\computer.png" />
    <Content Include="UI\images\icons\computer_add.png" />
    <Content Include="UI\images\icons\computer_delete.png" />
    <Content Include="UI\images\icons\computer_edit.png" />
    <Content Include="UI\images\icons\computer_error.png" />
    <Content Include="UI\images\icons\computer_go.png" />
    <Content Include="UI\images\icons\computer_key.png" />
    <Content Include="UI\images\icons\computer_link.png" />
    <Content Include="UI\images\icons\connect.png" />
    <Content Include="UI\images\icons\connected.png" />
    <Content Include="UI\images\icons\connected48.png" />
    <Content Include="UI\images\icons\contrast.png" />
    <Content Include="UI\images\icons\contrast_decrease.png" />
    <Content Include="UI\images\icons\contrast_high.png" />
    <Content Include="UI\images\icons\contrast_increase.png" />
    <Content Include="UI\images\icons\contrast_low.png" />
    <Content Include="UI\images\icons\controller.png" />
    <Content Include="UI\images\icons\controller_add.png" />
    <Content Include="UI\images\icons\controller_delete.png" />
    <Content Include="UI\images\icons\controller_error.png" />
    <Content Include="UI\images\icons\control_eject.png" />
    <Content Include="UI\images\icons\control_eject_blue.png" />
    <Content Include="UI\images\icons\control_end.png" />
    <Content Include="UI\images\icons\control_end_blue.png" />
    <Content Include="UI\images\icons\control_equalizer.png" />
    <Content Include="UI\images\icons\control_equalizer_blue.png" />
    <Content Include="UI\images\icons\control_fastforward.png" />
    <Content Include="UI\images\icons\control_fastforward_blue.png" />
    <Content Include="UI\images\icons\control_pause.png" />
    <Content Include="UI\images\icons\control_pause_blue.png" />
    <Content Include="UI\images\icons\control_play.png" />
    <Content Include="UI\images\icons\control_play_blue.png" />
    <Content Include="UI\images\icons\control_repeat.png" />
    <Content Include="UI\images\icons\control_repeat_blue.png" />
    <Content Include="UI\images\icons\control_rewind.png" />
    <Content Include="UI\images\icons\control_rewind_blue.png" />
    <Content Include="UI\images\icons\control_start.png" />
    <Content Include="UI\images\icons\control_start_blue.png" />
    <Content Include="UI\images\icons\control_stop.png" />
    <Content Include="UI\images\icons\control_stop_blue.png" />
    <Content Include="UI\images\icons\creditcards.png" />
    <Content Include="UI\images\icons\cross.png" />
    <Content Include="UI\images\icons\css.png" />
    <Content Include="UI\images\icons\css_add.png" />
    <Content Include="UI\images\icons\css_delete.png" />
    <Content Include="UI\images\icons\css_go.png" />
    <Content Include="UI\images\icons\css_valid.png" />
    <Content Include="UI\images\icons\cup.png" />
    <Content Include="UI\images\icons\cup_add.png" />
    <Content Include="UI\images\icons\cup_delete.png" />
    <Content Include="UI\images\icons\cup_edit.png" />
    <Content Include="UI\images\icons\cup_error.png" />
    <Content Include="UI\images\icons\cup_go.png" />
    <Content Include="UI\images\icons\cup_key.png" />
    <Content Include="UI\images\icons\cup_link.png" />
    <Content Include="UI\images\icons\cursor.png" />
    <Content Include="UI\images\icons\cut.png" />
    <Content Include="UI\images\icons\cut_red.png" />
    <Content Include="UI\images\icons\database.png" />
    <Content Include="UI\images\icons\database_add.png" />
    <Content Include="UI\images\icons\database_connect.png" />
    <Content Include="UI\images\icons\database_delete.png" />
    <Content Include="UI\images\icons\database_edit.png" />
    <Content Include="UI\images\icons\database_error.png" />
    <Content Include="UI\images\icons\database_gear.png" />
    <Content Include="UI\images\icons\database_go.png" />
    <Content Include="UI\images\icons\database_key.png" />
    <Content Include="UI\images\icons\database_lightning.png" />
    <Content Include="UI\images\icons\database_link.png" />
    <Content Include="UI\images\icons\database_refresh.png" />
    <Content Include="UI\images\icons\database_save.png" />
    <Content Include="UI\images\icons\database_table.png" />
    <Content Include="UI\images\icons\date.png" />
    <Content Include="UI\images\icons\date_add.png" />
    <Content Include="UI\images\icons\date_delete.png" />
    <Content Include="UI\images\icons\date_edit.png" />
    <Content Include="UI\images\icons\date_error.png" />
    <Content Include="UI\images\icons\date_go.png" />
    <Content Include="UI\images\icons\date_link.png" />
    <Content Include="UI\images\icons\date_magnify.png" />
    <Content Include="UI\images\icons\date_next.png" />
    <Content Include="UI\images\icons\date_previous.png" />
    <Content Include="UI\images\icons\del-minus-dark.png" />
    <Content Include="UI\images\icons\del-minus.png" />
    <Content Include="UI\images\icons\delete.png" />
    <Content Include="UI\images\icons\disconnect.png" />
    <Content Include="UI\images\icons\disk.png" />
    <Content Include="UI\images\icons\disk_multiple.png" />
    <Content Include="UI\images\icons\door.png" />
    <Content Include="UI\images\icons\door_in.png" />
    <Content Include="UI\images\icons\door_open.png" />
    <Content Include="UI\images\icons\door_out.png" />
    <Content Include="UI\images\icons\down-dark.png" />
    <Content Include="UI\images\icons\down-white.png" />
    <Content Include="UI\images\icons\down.png" />
    <Content Include="UI\images\icons\drink.png" />
    <Content Include="UI\images\icons\drink_empty.png" />
    <Content Include="UI\images\icons\drive.png" />
    <Content Include="UI\images\icons\drive_add.png" />
    <Content Include="UI\images\icons\drive_burn.png" />
    <Content Include="UI\images\icons\drive_cd.png" />
    <Content Include="UI\images\icons\drive_cd_empty.png" />
    <Content Include="UI\images\icons\drive_delete.png" />
    <Content Include="UI\images\icons\drive_disk.png" />
    <Content Include="UI\images\icons\drive_edit.png" />
    <Content Include="UI\images\icons\drive_error.png" />
    <Content Include="UI\images\icons\drive_go.png" />
    <Content Include="UI\images\icons\drive_key.png" />
    <Content Include="UI\images\icons\drive_link.png" />
    <Content Include="UI\images\icons\drive_magnify.png" />
    <Content Include="UI\images\icons\drive_network.png" />
    <Content Include="UI\images\icons\drive_rename.png" />
    <Content Include="UI\images\icons\drive_user.png" />
    <Content Include="UI\images\icons\drive_web.png" />
    <Content Include="UI\images\icons\dvd.png" />
    <Content Include="UI\images\icons\dvd_add.png" />
    <Content Include="UI\images\icons\dvd_delete.png" />
    <Content Include="UI\images\icons\dvd_edit.png" />
    <Content Include="UI\images\icons\dvd_error.png" />
    <Content Include="UI\images\icons\dvd_go.png" />
    <Content Include="UI\images\icons\dvd_key.png" />
    <Content Include="UI\images\icons\dvd_link.png" />
    <Content Include="UI\images\icons\email.png" />
    <Content Include="UI\images\icons\email_add.png" />
    <Content Include="UI\images\icons\email_attach.png" />
    <Content Include="UI\images\icons\email_delete.png" />
    <Content Include="UI\images\icons\email_edit.png" />
    <Content Include="UI\images\icons\email_error.png" />
    <Content Include="UI\images\icons\email_go.png" />
    <Content Include="UI\images\icons\email_link.png" />
    <Content Include="UI\images\icons\email_open.png" />
    <Content Include="UI\images\icons\email_open_image.png" />
    <Content Include="UI\images\icons\emoticon_evilgrin.png" />
    <Content Include="UI\images\icons\emoticon_grin.png" />
    <Content Include="UI\images\icons\emoticon_happy.png" />
    <Content Include="UI\images\icons\emoticon_smile.png" />
    <Content Include="UI\images\icons\emoticon_surprised.png" />
    <Content Include="UI\images\icons\emoticon_tongue.png" />
    <Content Include="UI\images\icons\emoticon_unhappy.png" />
    <Content Include="UI\images\icons\emoticon_waii.png" />
    <Content Include="UI\images\icons\emoticon_wink.png" />
    <Content Include="UI\images\icons\error.png" />
    <Content Include="UI\images\icons\error_add.png" />
    <Content Include="UI\images\icons\error_delete.png" />
    <Content Include="UI\images\icons\error_go.png" />
    <Content Include="UI\images\icons\exclamation.png" />
    <Content Include="UI\images\icons\eye.png" />
    <Content Include="UI\images\icons\feed.png" />
    <Content Include="UI\images\icons\feed_add.png" />
    <Content Include="UI\images\icons\feed_delete.png" />
    <Content Include="UI\images\icons\feed_disk.png" />
    <Content Include="UI\images\icons\feed_edit.png" />
    <Content Include="UI\images\icons\feed_error.png" />
    <Content Include="UI\images\icons\feed_go.png" />
    <Content Include="UI\images\icons\feed_key.png" />
    <Content Include="UI\images\icons\feed_link.png" />
    <Content Include="UI\images\icons\feed_magnify.png" />
    <Content Include="UI\images\icons\female.png" />
    <Content Include="UI\images\icons\film.png" />
    <Content Include="UI\images\icons\film_add.png" />
    <Content Include="UI\images\icons\film_delete.png" />
    <Content Include="UI\images\icons\film_edit.png" />
    <Content Include="UI\images\icons\film_error.png" />
    <Content Include="UI\images\icons\film_go.png" />
    <Content Include="UI\images\icons\film_key.png" />
    <Content Include="UI\images\icons\film_link.png" />
    <Content Include="UI\images\icons\film_save.png" />
    <Content Include="UI\images\icons\find.png" />
    <Content Include="UI\images\icons\flag_blue.png" />
    <Content Include="UI\images\icons\flag_green.png" />
    <Content Include="UI\images\icons\flag_orange.png" />
    <Content Include="UI\images\icons\flag_pink.png" />
    <Content Include="UI\images\icons\flag_purple.png" />
    <Content Include="UI\images\icons\flag_red.png" />
    <Content Include="UI\images\icons\flag_yellow.png" />
    <Content Include="UI\images\icons\folder.png" />
    <Content Include="UI\images\icons\folder_add.png" />
    <Content Include="UI\images\icons\folder_bell.png" />
    <Content Include="UI\images\icons\folder_brick.png" />
    <Content Include="UI\images\icons\folder_bug.png" />
    <Content Include="UI\images\icons\folder_camera.png" />
    <Content Include="UI\images\icons\folder_database.png" />
    <Content Include="UI\images\icons\folder_delete.png" />
    <Content Include="UI\images\icons\folder_edit.png" />
    <Content Include="UI\images\icons\folder_error.png" />
    <Content Include="UI\images\icons\folder_explore.png" />
    <Content Include="UI\images\icons\folder_feed.png" />
    <Content Include="UI\images\icons\folder_find.png" />
    <Content Include="UI\images\icons\folder_go.png" />
    <Content Include="UI\images\icons\folder_heart.png" />
    <Content Include="UI\images\icons\folder_image.png" />
    <Content Include="UI\images\icons\folder_key.png" />
    <Content Include="UI\images\icons\folder_lightbulb.png" />
    <Content Include="UI\images\icons\folder_link.png" />
    <Content Include="UI\images\icons\folder_magnify.png" />
    <Content Include="UI\images\icons\folder_page.png" />
    <Content Include="UI\images\icons\folder_page_white.png" />
    <Content Include="UI\images\icons\folder_palette.png" />
    <Content Include="UI\images\icons\folder_picture.png" />
    <Content Include="UI\images\icons\folder_star.png" />
    <Content Include="UI\images\icons\folder_table.png" />
    <Content Include="UI\images\icons\folder_user.png" />
    <Content Include="UI\images\icons\folder_wrench.png" />
    <Content Include="UI\images\icons\font.png" />
    <Content Include="UI\images\icons\font_add.png" />
    <Content Include="UI\images\icons\font_delete.png" />
    <Content Include="UI\images\icons\font_go.png" />
    <Content Include="UI\images\icons\group.png" />
    <Content Include="UI\images\icons\group_add.png" />
    <Content Include="UI\images\icons\group_delete.png" />
    <Content Include="UI\images\icons\group_edit.png" />
    <Content Include="UI\images\icons\group_error.png" />
    <Content Include="UI\images\icons\group_gear.png" />
    <Content Include="UI\images\icons\group_go.png" />
    <Content Include="UI\images\icons\group_key.png" />
    <Content Include="UI\images\icons\group_link.png" />
    <Content Include="UI\images\icons\heart.png" />
    <Content Include="UI\images\icons\heart_add.png" />
    <Content Include="UI\images\icons\heart_delete.png" />
    <Content Include="UI\images\icons\help.png" />
    <Content Include="UI\images\icons\hourglass.png" />
    <Content Include="UI\images\icons\hourglass_add.png" />
    <Content Include="UI\images\icons\hourglass_delete.png" />
    <Content Include="UI\images\icons\hourglass_go.png" />
    <Content Include="UI\images\icons\hourglass_link.png" />
    <Content Include="UI\images\icons\house.png" />
    <Content Include="UI\images\icons\house_go.png" />
    <Content Include="UI\images\icons\house_link.png" />
    <Content Include="UI\images\icons\html.png" />
    <Content Include="UI\images\icons\html_add.png" />
    <Content Include="UI\images\icons\html_delete.png" />
    <Content Include="UI\images\icons\html_go.png" />
    <Content Include="UI\images\icons\html_valid.png" />
    <Content Include="UI\images\icons\image.png" />
    <Content Include="UI\images\icons\images.png" />
    <Content Include="UI\images\icons\image_add.png" />
    <Content Include="UI\images\icons\image_delete.png" />
    <Content Include="UI\images\icons\image_edit.png" />
    <Content Include="UI\images\icons\image_link.png" />
    <Content Include="UI\images\icons\information.png" />
    <Content Include="UI\images\icons\ipod.png" />
    <Content Include="UI\images\icons\ipod_cast.png" />
    <Content Include="UI\images\icons\ipod_cast_add.png" />
    <Content Include="UI\images\icons\ipod_cast_delete.png" />
    <Content Include="UI\images\icons\ipod_sound.png" />
    <Content Include="UI\images\icons\joystick.png" />
    <Content Include="UI\images\icons\joystick_add.png" />
    <Content Include="UI\images\icons\joystick_delete.png" />
    <Content Include="UI\images\icons\joystick_error.png" />
    <Content Include="UI\images\icons\key.png" />
    <Content Include="UI\images\icons\keyboard.png" />
    <Content Include="UI\images\icons\keyboard_add.png" />
    <Content Include="UI\images\icons\keyboard_delete.png" />
    <Content Include="UI\images\icons\keyboard_magnify.png" />
    <Content Include="UI\images\icons\key_add.png" />
    <Content Include="UI\images\icons\key_delete.png" />
    <Content Include="UI\images\icons\key_go.png" />
    <Content Include="UI\images\icons\layers.png" />
    <Content Include="UI\images\icons\layout.png" />
    <Content Include="UI\images\icons\layout_add.png" />
    <Content Include="UI\images\icons\layout_content.png" />
    <Content Include="UI\images\icons\layout_delete.png" />
    <Content Include="UI\images\icons\layout_edit.png" />
    <Content Include="UI\images\icons\layout_error.png" />
    <Content Include="UI\images\icons\layout_header.png" />
    <Content Include="UI\images\icons\layout_link.png" />
    <Content Include="UI\images\icons\layout_sidebar.png" />
    <Content Include="UI\images\icons\lightbulb.png" />
    <Content Include="UI\images\icons\lightbulb_add.png" />
    <Content Include="UI\images\icons\lightbulb_delete.png" />
    <Content Include="UI\images\icons\lightbulb_off.png" />
    <Content Include="UI\images\icons\lightning.png" />
    <Content Include="UI\images\icons\lightning_add.png" />
    <Content Include="UI\images\icons\lightning_delete.png" />
    <Content Include="UI\images\icons\lightning_go.png" />
    <Content Include="UI\images\icons\link.png" />
    <Content Include="UI\images\icons\link_add.png" />
    <Content Include="UI\images\icons\link_break.png" />
    <Content Include="UI\images\icons\link_delete.png" />
    <Content Include="UI\images\icons\link_edit.png" />
    <Content Include="UI\images\icons\link_error.png" />
    <Content Include="UI\images\icons\link_go.png" />
    <Content Include="UI\images\icons\lock.png" />
    <Content Include="UI\images\icons\locked.png" />
    <Content Include="UI\images\icons\lock_add.png" />
    <Content Include="UI\images\icons\lock_break.png" />
    <Content Include="UI\images\icons\lock_delete.png" />
    <Content Include="UI\images\icons\lock_edit.png" />
    <Content Include="UI\images\icons\lock_go.png" />
    <Content Include="UI\images\icons\lock_open.png" />
    <Content Include="UI\images\icons\lorry.png" />
    <Content Include="UI\images\icons\lorry_add.png" />
    <Content Include="UI\images\icons\lorry_delete.png" />
    <Content Include="UI\images\icons\lorry_error.png" />
    <Content Include="UI\images\icons\lorry_flatbed.png" />
    <Content Include="UI\images\icons\lorry_go.png" />
    <Content Include="UI\images\icons\lorry_link.png" />
    <Content Include="UI\images\icons\magifier_zoom_out.png" />
    <Content Include="UI\images\icons\magnifier.png" />
    <Content Include="UI\images\icons\magnifier_zoom_in.png" />
    <Content Include="UI\images\icons\male.png" />
    <Content Include="UI\images\icons\map.png" />
    <Content Include="UI\images\icons\map_add.png" />
    <Content Include="UI\images\icons\map_delete.png" />
    <Content Include="UI\images\icons\map_edit.png" />
    <Content Include="UI\images\icons\map_go.png" />
    <Content Include="UI\images\icons\map_magnify.png" />
    <Content Include="UI\images\icons\medal_bronze_1.png" />
    <Content Include="UI\images\icons\medal_bronze_2.png" />
    <Content Include="UI\images\icons\medal_bronze_3.png" />
    <Content Include="UI\images\icons\medal_bronze_add.png" />
    <Content Include="UI\images\icons\medal_bronze_delete.png" />
    <Content Include="UI\images\icons\medal_gold_1.png" />
    <Content Include="UI\images\icons\medal_gold_2.png" />
    <Content Include="UI\images\icons\medal_gold_3.png" />
    <Content Include="UI\images\icons\medal_gold_add.png" />
    <Content Include="UI\images\icons\medal_gold_delete.png" />
    <Content Include="UI\images\icons\medal_silver_1.png" />
    <Content Include="UI\images\icons\medal_silver_2.png" />
    <Content Include="UI\images\icons\medal_silver_3.png" />
    <Content Include="UI\images\icons\medal_silver_add.png" />
    <Content Include="UI\images\icons\medal_silver_delete.png" />
    <Content Include="UI\images\icons\money.png" />
    <Content Include="UI\images\icons\money_add.png" />
    <Content Include="UI\images\icons\money_delete.png" />
    <Content Include="UI\images\icons\money_dollar.png" />
    <Content Include="UI\images\icons\money_euro.png" />
    <Content Include="UI\images\icons\money_pound.png" />
    <Content Include="UI\images\icons\money_yen.png" />
    <Content Include="UI\images\icons\monitor.png" />
    <Content Include="UI\images\icons\monitor_add.png" />
    <Content Include="UI\images\icons\monitor_delete.png" />
    <Content Include="UI\images\icons\monitor_edit.png" />
    <Content Include="UI\images\icons\monitor_error.png" />
    <Content Include="UI\images\icons\monitor_go.png" />
    <Content Include="UI\images\icons\monitor_lightning.png" />
    <Content Include="UI\images\icons\monitor_link.png" />
    <Content Include="UI\images\icons\mouse.png" />
    <Content Include="UI\images\icons\mouse_add.png" />
    <Content Include="UI\images\icons\mouse_delete.png" />
    <Content Include="UI\images\icons\mouse_error.png" />
    <Content Include="UI\images\icons\music.png" />
    <Content Include="UI\images\icons\new.png" />
    <Content Include="UI\images\icons\newspaper.png" />
    <Content Include="UI\images\icons\newspaper_add.png" />
    <Content Include="UI\images\icons\newspaper_delete.png" />
    <Content Include="UI\images\icons\newspaper_go.png" />
    <Content Include="UI\images\icons\newspaper_link.png" />
    <Content Include="UI\images\icons\note.png" />
    <Content Include="UI\images\icons\note_add.png" />
    <Content Include="UI\images\icons\note_delete.png" />
    <Content Include="UI\images\icons\note_edit.png" />
    <Content Include="UI\images\icons\note_error.png" />
    <Content Include="UI\images\icons\note_go.png" />
    <Content Include="UI\images\icons\overlays.png" />
    <Content Include="UI\images\icons\package.png" />
    <Content Include="UI\images\icons\package_add.png" />
    <Content Include="UI\images\icons\package_delete.png" />
    <Content Include="UI\images\icons\package_go.png" />
    <Content Include="UI\images\icons\package_green.png" />
    <Content Include="UI\images\icons\package_link.png" />
    <Content Include="UI\images\icons\page.png" />
    <Content Include="UI\images\icons\page_add.png" />
    <Content Include="UI\images\icons\page_attach.png" />
    <Content Include="UI\images\icons\page_code.png" />
    <Content Include="UI\images\icons\page_copy.png" />
    <Content Include="UI\images\icons\page_delete.png" />
    <Content Include="UI\images\icons\page_edit.png" />
    <Content Include="UI\images\icons\page_error.png" />
    <Content Include="UI\images\icons\page_excel.png" />
    <Content Include="UI\images\icons\page_find.png" />
    <Content Include="UI\images\icons\page_gear.png" />
    <Content Include="UI\images\icons\page_go.png" />
    <Content Include="UI\images\icons\page_green.png" />
    <Content Include="UI\images\icons\page_key.png" />
    <Content Include="UI\images\icons\page_lightning.png" />
    <Content Include="UI\images\icons\page_link.png" />
    <Content Include="UI\images\icons\page_paintbrush.png" />
    <Content Include="UI\images\icons\page_paste.png" />
    <Content Include="UI\images\icons\page_red.png" />
    <Content Include="UI\images\icons\page_refresh.png" />
    <Content Include="UI\images\icons\page_save.png" />
    <Content Include="UI\images\icons\page_white.png" />
    <Content Include="UI\images\icons\page_white_acrobat.png" />
    <Content Include="UI\images\icons\page_white_actionscript.png" />
    <Content Include="UI\images\icons\page_white_add.png" />
    <Content Include="UI\images\icons\page_white_c.png" />
    <Content Include="UI\images\icons\page_white_camera.png" />
    <Content Include="UI\images\icons\page_white_cd.png" />
    <Content Include="UI\images\icons\page_white_code.png" />
    <Content Include="UI\images\icons\page_white_code_red.png" />
    <Content Include="UI\images\icons\page_white_coldfusion.png" />
    <Content Include="UI\images\icons\page_white_compressed.png" />
    <Content Include="UI\images\icons\page_white_copy.png" />
    <Content Include="UI\images\icons\page_white_cplusplus.png" />
    <Content Include="UI\images\icons\page_white_csharp.png" />
    <Content Include="UI\images\icons\page_white_cup.png" />
    <Content Include="UI\images\icons\page_white_database.png" />
    <Content Include="UI\images\icons\page_white_delete.png" />
    <Content Include="UI\images\icons\page_white_dvd.png" />
    <Content Include="UI\images\icons\page_white_edit.png" />
    <Content Include="UI\images\icons\page_white_error.png" />
    <Content Include="UI\images\icons\page_white_excel.png" />
    <Content Include="UI\images\icons\page_white_find.png" />
    <Content Include="UI\images\icons\page_white_flash.png" />
    <Content Include="UI\images\icons\page_white_freehand.png" />
    <Content Include="UI\images\icons\page_white_gear.png" />
    <Content Include="UI\images\icons\page_white_get.png" />
    <Content Include="UI\images\icons\page_white_go.png" />
    <Content Include="UI\images\icons\page_white_h.png" />
    <Content Include="UI\images\icons\page_white_horizontal.png" />
    <Content Include="UI\images\icons\page_white_key.png" />
    <Content Include="UI\images\icons\page_white_lightning.png" />
    <Content Include="UI\images\icons\page_white_link.png" />
    <Content Include="UI\images\icons\page_white_magnify.png" />
    <Content Include="UI\images\icons\page_white_medal.png" />
    <Content Include="UI\images\icons\page_white_office.png" />
    <Content Include="UI\images\icons\page_white_paint.png" />
    <Content Include="UI\images\icons\page_white_paintbrush.png" />
    <Content Include="UI\images\icons\page_white_paste.png" />
    <Content Include="UI\images\icons\page_white_php.png" />
    <Content Include="UI\images\icons\page_white_picture.png" />
    <Content Include="UI\images\icons\page_white_powerpoint.png" />
    <Content Include="UI\images\icons\page_white_put.png" />
    <Content Include="UI\images\icons\page_white_ruby.png" />
    <Content Include="UI\images\icons\page_white_stack.png" />
    <Content Include="UI\images\icons\page_white_star.png" />
    <Content Include="UI\images\icons\page_white_swoosh.png" />
    <Content Include="UI\images\icons\page_white_text.png" />
    <Content Include="UI\images\icons\page_white_text_width.png" />
    <Content Include="UI\images\icons\page_white_tux.png" />
    <Content Include="UI\images\icons\page_white_vector.png" />
    <Content Include="UI\images\icons\page_white_visualstudio.png" />
    <Content Include="UI\images\icons\page_white_width.png" />
    <Content Include="UI\images\icons\page_white_word.png" />
    <Content Include="UI\images\icons\page_white_world.png" />
    <Content Include="UI\images\icons\page_white_wrench.png" />
    <Content Include="UI\images\icons\page_white_zip.png" />
    <Content Include="UI\images\icons\page_word.png" />
    <Content Include="UI\images\icons\page_world.png" />
    <Content Include="UI\images\icons\paintbrush.png" />
    <Content Include="UI\images\icons\paintcan.png" />
    <Content Include="UI\images\icons\palette.png" />
    <Content Include="UI\images\icons\paste_plain.png" />
    <Content Include="UI\images\icons\paste_word.png" />
    <Content Include="UI\images\icons\pencil.png" />
    <Content Include="UI\images\icons\pencil_add.png" />
    <Content Include="UI\images\icons\pencil_delete.png" />
    <Content Include="UI\images\icons\pencil_go.png" />
    <Content Include="UI\images\icons\phone.png" />
    <Content Include="UI\images\icons\phone_add.png" />
    <Content Include="UI\images\icons\phone_delete.png" />
    <Content Include="UI\images\icons\phone_sound.png" />
    <Content Include="UI\images\icons\photo.png" />
    <Content Include="UI\images\icons\photos.png" />
    <Content Include="UI\images\icons\photo_add.png" />
    <Content Include="UI\images\icons\photo_delete.png" />
    <Content Include="UI\images\icons\photo_link.png" />
    <Content Include="UI\images\icons\picture.png" />
    <Content Include="UI\images\icons\pictures.png" />
    <Content Include="UI\images\icons\picture_add.png" />
    <Content Include="UI\images\icons\picture_delete.png" />
    <Content Include="UI\images\icons\picture_edit.png" />
    <Content Include="UI\images\icons\picture_empty.png" />
    <Content Include="UI\images\icons\picture_error.png" />
    <Content Include="UI\images\icons\picture_go.png" />
    <Content Include="UI\images\icons\picture_key.png" />
    <Content Include="UI\images\icons\picture_link.png" />
    <Content Include="UI\images\icons\picture_save.png" />
    <Content Include="UI\images\icons\pilcrow.png" />
    <Content Include="UI\images\icons\pill.png" />
    <Content Include="UI\images\icons\pill_add.png" />
    <Content Include="UI\images\icons\pill_delete.png" />
    <Content Include="UI\images\icons\pill_go.png" />
    <Content Include="UI\images\icons\plugin.png" />
    <Content Include="UI\images\icons\plugin_add.png" />
    <Content Include="UI\images\icons\plugin_delete.png" />
    <Content Include="UI\images\icons\plugin_disabled.png" />
    <Content Include="UI\images\icons\plugin_edit.png" />
    <Content Include="UI\images\icons\plugin_error.png" />
    <Content Include="UI\images\icons\plugin_go.png" />
    <Content Include="UI\images\icons\plugin_link.png" />
    <Content Include="UI\images\icons\printer.png" />
    <Content Include="UI\images\icons\printer_add.png" />
    <Content Include="UI\images\icons\printer_delete.png" />
    <Content Include="UI\images\icons\printer_empty.png" />
    <Content Include="UI\images\icons\printer_error.png" />
    <Content Include="UI\images\icons\rainbow.png" />
    <Content Include="UI\images\icons\report.png" />
    <Content Include="UI\images\icons\report_add.png" />
    <Content Include="UI\images\icons\report_delete.png" />
    <Content Include="UI\images\icons\report_disk.png" />
    <Content Include="UI\images\icons\report_edit.png" />
    <Content Include="UI\images\icons\report_go.png" />
    <Content Include="UI\images\icons\report_key.png" />
    <Content Include="UI\images\icons\report_link.png" />
    <Content Include="UI\images\icons\report_magnify.png" />
    <Content Include="UI\images\icons\report_picture.png" />
    <Content Include="UI\images\icons\report_user.png" />
    <Content Include="UI\images\icons\report_word.png" />
    <Content Include="UI\images\icons\resultset_first.png" />
    <Content Include="UI\images\icons\resultset_last.png" />
    <Content Include="UI\images\icons\resultset_next.png" />
    <Content Include="UI\images\icons\resultset_previous.png" />
    <Content Include="UI\images\icons\rosette.png" />
    <Content Include="UI\images\icons\rss.png" />
    <Content Include="UI\images\icons\rss_add.png" />
    <Content Include="UI\images\icons\rss_delete.png" />
    <Content Include="UI\images\icons\rss_go.png" />
    <Content Include="UI\images\icons\rss_valid.png" />
    <Content Include="UI\images\icons\ruby.png" />
    <Content Include="UI\images\icons\ruby_add.png" />
    <Content Include="UI\images\icons\ruby_delete.png" />
    <Content Include="UI\images\icons\ruby_gear.png" />
    <Content Include="UI\images\icons\ruby_get.png" />
    <Content Include="UI\images\icons\ruby_go.png" />
    <Content Include="UI\images\icons\ruby_key.png" />
    <Content Include="UI\images\icons\ruby_link.png" />
    <Content Include="UI\images\icons\ruby_put.png" />
    <Content Include="UI\images\icons\script.png" />
    <Content Include="UI\images\icons\script_add.png" />
    <Content Include="UI\images\icons\script_code.png" />
    <Content Include="UI\images\icons\script_code_red.png" />
    <Content Include="UI\images\icons\script_delete.png" />
    <Content Include="UI\images\icons\script_edit.png" />
    <Content Include="UI\images\icons\script_error.png" />
    <Content Include="UI\images\icons\script_gear.png" />
    <Content Include="UI\images\icons\script_go.png" />
    <Content Include="UI\images\icons\script_key.png" />
    <Content Include="UI\images\icons\script_lightning.png" />
    <Content Include="UI\images\icons\script_link.png" />
    <Content Include="UI\images\icons\script_palette.png" />
    <Content Include="UI\images\icons\script_save.png" />
    <Content Include="UI\images\icons\search.png" />
    <Content Include="UI\images\icons\server.png" />
    <Content Include="UI\images\icons\server_add.png" />
    <Content Include="UI\images\icons\server_chart.png" />
    <Content Include="UI\images\icons\server_compressed.png" />
    <Content Include="UI\images\icons\server_connect.png" />
    <Content Include="UI\images\icons\server_database.png" />
    <Content Include="UI\images\icons\server_delete.png" />
    <Content Include="UI\images\icons\server_edit.png" />
    <Content Include="UI\images\icons\server_error.png" />
    <Content Include="UI\images\icons\server_go.png" />
    <Content Include="UI\images\icons\server_key.png" />
    <Content Include="UI\images\icons\server_lightning.png" />
    <Content Include="UI\images\icons\server_link.png" />
    <Content Include="UI\images\icons\server_uncompressed.png" />
    <Content Include="UI\images\icons\shading.png" />
    <Content Include="UI\images\icons\shape_align_bottom.png" />
    <Content Include="UI\images\icons\shape_align_center.png" />
    <Content Include="UI\images\icons\shape_align_left.png" />
    <Content Include="UI\images\icons\shape_align_middle.png" />
    <Content Include="UI\images\icons\shape_align_right.png" />
    <Content Include="UI\images\icons\shape_align_top.png" />
    <Content Include="UI\images\icons\shape_flip_horizontal.png" />
    <Content Include="UI\images\icons\shape_flip_vertical.png" />
    <Content Include="UI\images\icons\shape_group.png" />
    <Content Include="UI\images\icons\shape_handles.png" />
    <Content Include="UI\images\icons\shape_move_back.png" />
    <Content Include="UI\images\icons\shape_move_backwards.png" />
    <Content Include="UI\images\icons\shape_move_forwards.png" />
    <Content Include="UI\images\icons\shape_move_front.png" />
    <Content Include="UI\images\icons\shape_rotate_anticlockwise.png" />
    <Content Include="UI\images\icons\shape_rotate_clockwise.png" />
    <Content Include="UI\images\icons\shape_square.png" />
    <Content Include="UI\images\icons\shape_square_add.png" />
    <Content Include="UI\images\icons\shape_square_delete.png" />
    <Content Include="UI\images\icons\shape_square_edit.png" />
    <Content Include="UI\images\icons\shape_square_error.png" />
    <Content Include="UI\images\icons\shape_square_go.png" />
    <Content Include="UI\images\icons\shape_square_key.png" />
    <Content Include="UI\images\icons\shape_square_link.png" />
    <Content Include="UI\images\icons\shape_ungroup.png" />
    <Content Include="UI\images\icons\shield.png" />
    <Content Include="UI\images\icons\shield_add.png" />
    <Content Include="UI\images\icons\shield_delete.png" />
    <Content Include="UI\images\icons\shield_go.png" />
    <Content Include="UI\images\icons\sitemap.png" />
    <Content Include="UI\images\icons\sitemap_color.png" />
    <Content Include="UI\images\icons\sound.png" />
    <Content Include="UI\images\icons\sound_add.png" />
    <Content Include="UI\images\icons\sound_delete.png" />
    <Content Include="UI\images\icons\sound_low.png" />
    <Content Include="UI\images\icons\sound_mute.png" />
    <Content Include="UI\images\icons\sound_none.png" />
    <Content Include="UI\images\icons\spellcheck.png" />
    <Content Include="UI\images\icons\sport_8ball.png" />
    <Content Include="UI\images\icons\sport_basketball.png" />
    <Content Include="UI\images\icons\sport_football.png" />
    <Content Include="UI\images\icons\sport_golf.png" />
    <Content Include="UI\images\icons\sport_raquet.png" />
    <Content Include="UI\images\icons\sport_shuttlecock.png" />
    <Content Include="UI\images\icons\sport_soccer.png" />
    <Content Include="UI\images\icons\sport_tennis.png" />
    <Content Include="UI\images\icons\star.png" />
    <Content Include="UI\images\icons\status_away.png" />
    <Content Include="UI\images\icons\status_busy.png" />
    <Content Include="UI\images\icons\status_offline.png" />
    <Content Include="UI\images\icons\status_online.png" />
    <Content Include="UI\images\icons\stop.png" />
    <Content Include="UI\images\icons\style.png" />
    <Content Include="UI\images\icons\style_add.png" />
    <Content Include="UI\images\icons\style_delete.png" />
    <Content Include="UI\images\icons\style_edit.png" />
    <Content Include="UI\images\icons\style_go.png" />
    <Content Include="UI\images\icons\sum.png" />
    <Content Include="UI\images\icons\tab.png" />
    <Content Include="UI\images\icons\table.png" />
    <Content Include="UI\images\icons\table_add.png" />
    <Content Include="UI\images\icons\table_delete.png" />
    <Content Include="UI\images\icons\table_edit.png" />
    <Content Include="UI\images\icons\table_error.png" />
    <Content Include="UI\images\icons\table_gear.png" />
    <Content Include="UI\images\icons\table_go.png" />
    <Content Include="UI\images\icons\table_key.png" />
    <Content Include="UI\images\icons\table_lightning.png" />
    <Content Include="UI\images\icons\table_link.png" />
    <Content Include="UI\images\icons\table_multiple.png" />
    <Content Include="UI\images\icons\table_refresh.png" />
    <Content Include="UI\images\icons\table_relationship.png" />
    <Content Include="UI\images\icons\table_row_delete.png" />
    <Content Include="UI\images\icons\table_row_insert.png" />
    <Content Include="UI\images\icons\table_save.png" />
    <Content Include="UI\images\icons\table_sort.png" />
    <Content Include="UI\images\icons\tab_add.png" />
    <Content Include="UI\images\icons\tab_delete.png" />
    <Content Include="UI\images\icons\tab_edit.png" />
    <Content Include="UI\images\icons\tab_go.png" />
    <Content Include="UI\images\icons\tag.png" />
    <Content Include="UI\images\icons\tag_blue.png" />
    <Content Include="UI\images\icons\tag_blue_add.png" />
    <Content Include="UI\images\icons\tag_blue_delete.png" />
    <Content Include="UI\images\icons\tag_blue_edit.png" />
    <Content Include="UI\images\icons\tag_green.png" />
    <Content Include="UI\images\icons\tag_orange.png" />
    <Content Include="UI\images\icons\tag_pink.png" />
    <Content Include="UI\images\icons\tag_purple.png" />
    <Content Include="UI\images\icons\tag_red.png" />
    <Content Include="UI\images\icons\tag_yellow.png" />
    <Content Include="UI\images\icons\tax.png" />
    <Content Include="UI\images\icons\telephone.png" />
    <Content Include="UI\images\icons\telephone_add.png" />
    <Content Include="UI\images\icons\telephone_delete.png" />
    <Content Include="UI\images\icons\telephone_edit.png" />
    <Content Include="UI\images\icons\telephone_error.png" />
    <Content Include="UI\images\icons\telephone_go.png" />
    <Content Include="UI\images\icons\telephone_key.png" />
    <Content Include="UI\images\icons\telephone_link.png" />
    <Content Include="UI\images\icons\television.png" />
    <Content Include="UI\images\icons\television_add.png" />
    <Content Include="UI\images\icons\television_delete.png" />
    <Content Include="UI\images\icons\textfield.png" />
    <Content Include="UI\images\icons\textfield_add.png" />
    <Content Include="UI\images\icons\textfield_delete.png" />
    <Content Include="UI\images\icons\textfield_key.png" />
    <Content Include="UI\images\icons\textfield_rename.png" />
    <Content Include="UI\images\icons\text_align_center.png" />
    <Content Include="UI\images\icons\text_align_justify.png" />
    <Content Include="UI\images\icons\text_align_left.png" />
    <Content Include="UI\images\icons\text_align_right.png" />
    <Content Include="UI\images\icons\text_allcaps.png" />
    <Content Include="UI\images\icons\text_bold.png" />
    <Content Include="UI\images\icons\text_columns.png" />
    <Content Include="UI\images\icons\text_dropcaps.png" />
    <Content Include="UI\images\icons\text_heading_1.png" />
    <Content Include="UI\images\icons\text_heading_2.png" />
    <Content Include="UI\images\icons\text_heading_3.png" />
    <Content Include="UI\images\icons\text_heading_4.png" />
    <Content Include="UI\images\icons\text_heading_5.png" />
    <Content Include="UI\images\icons\text_heading_6.png" />
    <Content Include="UI\images\icons\text_horizontalrule.png" />
    <Content Include="UI\images\icons\text_indent.png" />
    <Content Include="UI\images\icons\text_indent_remove.png" />
    <Content Include="UI\images\icons\text_italic.png" />
    <Content Include="UI\images\icons\text_kerning.png" />
    <Content Include="UI\images\icons\text_letterspacing.png" />
    <Content Include="UI\images\icons\text_letter_omega.png" />
    <Content Include="UI\images\icons\text_linespacing.png" />
    <Content Include="UI\images\icons\text_list_bullets.png" />
    <Content Include="UI\images\icons\text_list_numbers.png" />
    <Content Include="UI\images\icons\text_lowercase.png" />
    <Content Include="UI\images\icons\text_padding_bottom.png" />
    <Content Include="UI\images\icons\text_padding_left.png" />
    <Content Include="UI\images\icons\text_padding_right.png" />
    <Content Include="UI\images\icons\text_padding_top.png" />
    <Content Include="UI\images\icons\text_replace.png" />
    <Content Include="UI\images\icons\text_signature.png" />
    <Content Include="UI\images\icons\text_smallcaps.png" />
    <Content Include="UI\images\icons\text_strikethrough.png" />
    <Content Include="UI\images\icons\text_subscript.png" />
    <Content Include="UI\images\icons\text_superscript.png" />
    <Content Include="UI\images\icons\text_underline.png" />
    <Content Include="UI\images\icons\text_uppercase.png" />
    <Content Include="UI\images\icons\thumb_down.png" />
    <Content Include="UI\images\icons\thumb_up.png" />
    <Content Include="UI\images\icons\tick.png" />
    <Content Include="UI\images\icons\time.png" />
    <Content Include="UI\images\icons\timeline_marker.png" />
    <Content Include="UI\images\icons\time_add.png" />
    <Content Include="UI\images\icons\time_delete.png" />
    <Content Include="UI\images\icons\time_go.png" />
    <Content Include="UI\images\icons\towbook-truck-36.png" />
    <Content Include="UI\images\icons\towbook-truck-49.png" />
    <Content Include="UI\images\icons\towbook-truck.png" />
    <Content Include="UI\images\icons\transmit.png" />
    <Content Include="UI\images\icons\transmit_add.png" />
    <Content Include="UI\images\icons\transmit_blue.png" />
    <Content Include="UI\images\icons\transmit_delete.png" />
    <Content Include="UI\images\icons\transmit_edit.png" />
    <Content Include="UI\images\icons\transmit_error.png" />
    <Content Include="UI\images\icons\transmit_go.png" />
    <Content Include="UI\images\icons\trash.png" />
    <Content Include="UI\images\icons\tux.png" />
    <Content Include="UI\images\icons\up-dark.png" />
    <Content Include="UI\images\icons\up-white.png" />
    <Content Include="UI\images\icons\up.png" />
    <Content Include="UI\images\icons\user.png" />
    <Content Include="UI\images\icons\user_add.png" />
    <Content Include="UI\images\icons\user_comment.png" />
    <Content Include="UI\images\icons\user_delete.png" />
    <Content Include="UI\images\icons\user_edit.png" />
    <Content Include="UI\images\icons\user_female.png" />
    <Content Include="UI\images\icons\user_go.png" />
    <Content Include="UI\images\icons\user_gray.png" />
    <Content Include="UI\images\icons\user_green.png" />
    <Content Include="UI\images\icons\user_orange.png" />
    <Content Include="UI\images\icons\user_placeholder.png" />
    <Content Include="UI\images\icons\user_placeholder_small.png" />
    <Content Include="UI\images\icons\user_red.png" />
    <Content Include="UI\images\icons\user_suit.png" />
    <Content Include="UI\images\icons\vcard.png" />
    <Content Include="UI\images\icons\vcard_add.png" />
    <Content Include="UI\images\icons\vcard_delete.png" />
    <Content Include="UI\images\icons\vcard_edit.png" />
    <Content Include="UI\images\icons\vector.png" />
    <Content Include="UI\images\icons\vector_add.png" />
    <Content Include="UI\images\icons\vector_delete.png" />
    <Content Include="UI\images\icons\wand.png" />
    <Content Include="UI\images\icons\weather_clouds.png" />
    <Content Include="UI\images\icons\weather_cloudy.png" />
    <Content Include="UI\images\icons\weather_lightning.png" />
    <Content Include="UI\images\icons\weather_rain.png" />
    <Content Include="UI\images\icons\weather_snow.png" />
    <Content Include="UI\images\icons\weather_sun.png" />
    <Content Include="UI\images\icons\webcam.png" />
    <Content Include="UI\images\icons\webcam_add.png" />
    <Content Include="UI\images\icons\webcam_delete.png" />
    <Content Include="UI\images\icons\webcam_error.png" />
    <Content Include="UI\images\icons\world.png" />
    <Content Include="UI\images\icons\world_add.png" />
    <Content Include="UI\images\icons\world_delete.png" />
    <Content Include="UI\images\icons\world_edit.png" />
    <Content Include="UI\images\icons\world_go.png" />
    <Content Include="UI\images\icons\world_link.png" />
    <Content Include="UI\images\icons\wrench.png" />
    <Content Include="UI\images\icons\wrench_orange.png" />
    <Content Include="UI\images\icons\xhtml.png" />
    <Content Include="UI\images\icons\xhtml_add.png" />
    <Content Include="UI\images\icons\xhtml_delete.png" />
    <Content Include="UI\images\icons\xhtml_go.png" />
    <Content Include="UI\images\icons\xhtml_valid.png" />
    <Content Include="UI\images\icons\zoom.png" />
    <Content Include="UI\images\icons\zoom_in.png" />
    <Content Include="UI\images\icons\zoom_out.png" />
    <Content Include="UI\images\lightview\blank.gif" />
    <Content Include="UI\images\lightview\close_innertop.png" />
    <Content Include="UI\images\lightview\close_large.png" />
    <Content Include="UI\images\lightview\close_small.png" />
    <Content Include="UI\images\lightview\controller_close.png" />
    <Content Include="UI\images\lightview\controller_next.png" />
    <Content Include="UI\images\lightview\controller_prev.png" />
    <Content Include="UI\images\lightview\controller_slideshow.png" />
    <Content Include="UI\images\lightview\controller_slideshow_play.png" />
    <Content Include="UI\images\lightview\controller_slideshow_stop.png" />
    <Content Include="UI\images\lightview\controller_stop.png" />
    <Content Include="UI\images\lightview\inner_next.png" />
    <Content Include="UI\images\lightview\inner_prev.png" />
    <Content Include="UI\images\lightview\inner_slideshow_play.png" />
    <Content Include="UI\images\lightview\inner_slideshow_stop.png" />
    <Content Include="UI\images\lightview\loading.gif" />
    <Content Include="UI\images\lightview\next.png" />
    <Content Include="UI\images\lightview\overlay.png" />
    <Content Include="UI\images\lightview\prev.png" />
    <Content Include="UI\images\lightview\topclose.png" />
    <Content Include="UI\images\login-background.jpg" />
    <Content Include="UI\images\logo.png" />
    <Content Include="UI\images\moreOptions.png" />
    <Content Include="UI\images\nav-active-bg.jpg" />
    <Content Include="UI\images\nav-hover-bg.jpg" />
    <Content Include="UI\images\nav-inactive.png" />
    <Content Include="UI\images\nd0071-48.gif" />
    <Content Include="UI\images\nd0072-48.gif" />
    <Content Include="UI\images\nf-img\0.png" />
    <Content Include="UI\images\nf-img\button-left.png" />
    <Content Include="UI\images\nf-img\button-right.png" />
    <Content Include="UI\images\nf-img\button.png" />
    <Content Include="UI\images\nf-img\checkbox.png" />
    <Content Include="UI\images\nf-img\file.png" />
    <Content Include="UI\images\nf-img\input-left.png" />
    <Content Include="UI\images\nf-img\input-right.png" />
    <Content Include="UI\images\nf-img\input.png" />
    <Content Include="UI\images\nf-img\radio.png" />
    <Content Include="UI\images\nf-img\select-left.png" />
    <Content Include="UI\images\nf-img\select-right.png" />
    <Content Include="UI\images\nf-img\textarea-bl.png" />
    <Content Include="UI\images\nf-img\textarea-br.png" />
    <Content Include="UI\images\nf-img\textarea-l-off.png" />
    <Content Include="UI\images\nf-img\textarea-l-over.png" />
    <Content Include="UI\images\nf-img\textarea-r-off.png" />
    <Content Include="UI\images\nf-img\textarea-r-over.png" />
    <Content Include="UI\images\nf-img\textarea-tl.png" />
    <Content Include="UI\images\nf-img\textarea-tr.png" />
    <Content Include="UI\images\notification-delete.png" />
    <Content Include="UI\images\pg_background.jpg" />
    <Content Include="UI\images\r-miscellaneous.gif" />
    <Content Include="UI\images\shadow.png" />
    <Content Include="UI\images\spinnerDefault.png" />
    <Content Include="UI\images\subheader.jpg" />
    <Content Include="UI\images\tab_bg.png" />
    <Content Include="UI\images\ToolbarIcons\Accounts\EditAccount.png" />
    <Content Include="UI\images\ToolbarIcons\Accounts\Invoices.png" />
    <Content Include="UI\images\ToolbarIcons\Accounts\NewAccount.png" />
    <Content Include="UI\images\ToolbarIcons\Address.png" />
    <Content Include="UI\images\ToolbarIcons\background.jpg" />
    <Content Include="UI\images\ToolbarIcons\button-background.jpg" />
    <Content Include="UI\images\ToolbarIcons\Cancel.png" />
    <Content Include="UI\images\ToolbarIcons\Default.png" />
    <Content Include="UI\images\ToolbarIcons\DispatchNewCall.gif" />
    <Content Include="UI\images\ToolbarIcons\DispatchNewCall.png" />
    <Content Include="UI\images\ToolbarIcons\Drivers\NewDriver.png" />
    <Content Include="UI\images\ToolbarIcons\NewImpound.png" />
    <Content Include="UI\images\ToolbarIcons\Save.png" />
    <Content Include="UI\images\ToolbarIcons\Search.png" />
    <Content Include="UI\images\ToolbarIcons\Trucks\NewTruck.png" />
    <Content Include="UI\images\topbg.png" />
    <Content Include="UI\images\towbook-white-small.png" />
    <Content Include="UI\images\towbook-white.png" />
    <Content Include="UI\images\towbook.jpg" />
    <Content Include="UI\images\towbook.png" />
    <Content Include="UI\images\user.png" />
    <Content Include="UI\images\wi0057-48.gif" />
    <Content Include="UI\images\wi0074-48.gif" />
    <Content Include="UI\images\wi0107-48.gif" />
    <Content Include="UI\js-remote\AnyChart.js" />
    <Content Include="UI\js-remote\autocomplete.js" />
    <Content Include="UI\js-remote\builder.js" />
    <Content Include="UI\js-remote\controls.js" />
    <Content Include="UI\js-remote\dispatch.js" />
    <Content Include="UI\js-remote\dragdrop.js" />
    <Content Include="UI\js-remote\effects.js" />
    <Content Include="UI\js-remote\lightbox.js" />
    <Content Include="UI\js-remote\lightview.js" />
    <Content Include="UI\js-remote\menu.js" />
    <Content Include="UI\js-remote\mootools-1.2.1-core-yc.js" />
    <Content Include="UI\js-remote\mootools-release-1.11.js" />
    <Content Include="UI\js-remote\mootools.v1.1.js" />
    <Content Include="UI\js-remote\mootools.v1.2.1.js" />
    <Content Include="UI\js-remote\prototype.js" />
    <Content Include="UI\js-remote\scriptaculous.js" />
    <Content Include="UI\js-remote\slider.js" />
    <Content Include="UI\js-remote\sorttable.js" />
    <Content Include="UI\js-remote\sound.js" />
    <Content Include="UI\js-remote\test.htm" />
    <Content Include="UI\js-remote\Towbook.js" />
    <Content Include="UI\js-remote\TreeView.js" />
    <Content Include="UI\js-remote\unittest.js" />
    <Content Include="UI\js\accounting.js" />
    <Content Include="UI\js\AnyChart.js" />
    <Content Include="UI\js\autocomplete.js" />
    <Content Include="UI\js\builder.js" />
    <Content Include="UI\js\controls.js" />
    <Content Include="UI\js\DashboardImpounds.js" />
    <Content Include="UI\js\DashboardServiceNotifications.js" />
    <Content Include="UI\js\dragdrop.js" />
    <Content Include="UI\js\effects.js" />
    <Content Include="UI\js\highcharts.js" />
    <Content Include="UI\js\history\jquery.history.js" />
    <Content Include="UI\js\history\mootools.history.js" />
    <Content Include="UI\js\history\native.history.js" />
    <Content Include="UI\js\history\right.history.js" />
    <Content Include="UI\js\history\zepto.history.js" />
    <Content Include="dyreq\json\dispatchentries.ashx" />
    <Content Include="dyreq\json\dispatchentry_txtmsg.ashx" />
    <Content Include="dyreq\json\impoundlots.ashx" />
    <Content Include="dyreq\json\impounds.ashx" />
    <Content Include="dyreq\json\reports_revenue.ashx" />
    <Content Include="dyreq\json\reports_tax.ashx" />
    <Content Include="dyreq\json\TicketAttributes.ashx" />
    <Content Include="dyreq\json\vehiclemakes.ashx" />
    <Content Include="dyreq\windows\dispatching\Search.ashx" />
    <Content Include="Reports\Accounting\AccountPayments.ashx" />
    <Content Include="Reports\Accounting\RateItemReport.ashx" />
    <Content Include="Dispatch\Default.scss" />
    <Content Include="DS4\styles_grid_view.scss" />
    <Content Include="DS4\styles_one_line_view.scss" />
    <Content Include="UI\fonts\Open Sans\OpenSans.ttf" />
    <Content Include="UI\fonts\Open Sans\OpenSans.woff2" />
    <Content Include="DS4\styles_card_view.scss" />
    <Content Include="DS4\styles.scss" />
    <Content Include="DispatchEditor\editor.scss" />
    <Content Include="DS4\styles_classic_view.scss" />
    <Content Include="Dispatch\vdr.scss" />
    <Content Include="Map2\css\panel.scss" />
    <Content Include="UI\js\chatv2\.babelrc" />
    <Content Include="UI\js\chatv2\dist\main.js.map" />
    <Content Include="UI\js\chatv2\package-lock.json" />
    <Content Include="UI\js\chatv2\package.json" />
    <Content Include="UI\js\chatv2\src\README.md" />
    <Content Include="UI\fonts\fa\less\fa-brands.less" />
    <Content Include="UI\fonts\fa\less\fa-light.less" />
    <Content Include="UI\fonts\fa\less\fa-regular.less" />
    <Content Include="UI\fonts\fa\less\fa-solid.less" />
    <Content Include="UI\fonts\fa\less\fontawesome.less" />
    <Content Include="UI\fonts\fa\less\v4-shims.less" />
    <Content Include="UI\fonts\fa\less\_animated.less" />
    <Content Include="UI\fonts\fa\less\_bordered-pulled.less" />
    <Content Include="UI\fonts\fa\less\_core.less" />
    <Content Include="UI\fonts\fa\less\_fixed-width.less" />
    <Content Include="UI\fonts\fa\less\_icons.less" />
    <Content Include="UI\fonts\fa\less\_larger.less" />
    <Content Include="UI\fonts\fa\less\_list.less" />
    <Content Include="UI\fonts\fa\less\_mixins.less" />
    <Content Include="UI\fonts\fa\less\_rotated-flipped.less" />
    <Content Include="UI\fonts\fa\less\_screen-reader.less" />
    <Content Include="UI\fonts\fa\less\_shims.less" />
    <Content Include="UI\fonts\fa\less\_stacked.less" />
    <Content Include="UI\fonts\fa\less\_variables.less" />
    <Content Include="UI\fonts\fa\metadata\categories.yml" />
    <Content Include="UI\fonts\fa\metadata\icons.json" />
    <Content Include="UI\fonts\fa\metadata\icons.yml" />
    <Content Include="UI\fonts\fa\metadata\shims.json" />
    <Content Include="UI\fonts\fa\metadata\shims.yml" />
    <Content Include="UI\fonts\fa\metadata\sponsors.yml" />
    <Content Include="UI\fonts\fa\scss\fa-brands.scss" />
    <Content Include="UI\fonts\fa\scss\fa-light.scss" />
    <Content Include="UI\fonts\fa\scss\fa-regular.scss" />
    <Content Include="UI\fonts\fa\scss\fa-solid.scss" />
    <Content Include="UI\fonts\fa\scss\fontawesome.scss" />
    <Content Include="UI\fonts\fa\scss\v4-shims.scss" />
    <Content Include="UI\fonts\fa\scss\_animated.scss" />
    <Content Include="UI\fonts\fa\scss\_bordered-pulled.scss" />
    <Content Include="UI\fonts\fa\scss\_core.scss" />
    <Content Include="UI\fonts\fa\scss\_fixed-width.scss" />
    <Content Include="UI\fonts\fa\scss\_icons.scss" />
    <Content Include="UI\fonts\fa\scss\_larger.scss" />
    <Content Include="UI\fonts\fa\scss\_list.scss" />
    <Content Include="UI\fonts\fa\scss\_mixins.scss" />
    <Content Include="UI\fonts\fa\scss\_rotated-flipped.scss" />
    <Content Include="UI\fonts\fa\scss\_screen-reader.scss" />
    <Content Include="UI\fonts\fa\scss\_shims.scss" />
    <Content Include="UI\fonts\fa\scss\_stacked.scss" />
    <Content Include="UI\fonts\fa\scss\_variables.scss" />
    <Content Include="UI\fonts\fa\webfonts\fa-brands-400.eot" />
    <Content Include="UI\fonts\fa\webfonts\fa-brands-400.ttf" />
    <Content Include="UI\fonts\fa\webfonts\fa-brands-400.woff" />
    <Content Include="UI\fonts\fa\webfonts\fa-brands-400.woff2" />
    <Content Include="UI\fonts\fa\webfonts\fa-light-300.eot" />
    <Content Include="UI\fonts\fa\webfonts\fa-light-300.ttf" />
    <Content Include="UI\fonts\fa\webfonts\fa-light-300.woff" />
    <Content Include="UI\fonts\fa\webfonts\fa-light-300.woff2" />
    <Content Include="UI\fonts\fa\webfonts\fa-regular-400.eot" />
    <Content Include="UI\fonts\fa\webfonts\fa-regular-400.ttf" />
    <Content Include="UI\fonts\fa\webfonts\fa-regular-400.woff" />
    <Content Include="UI\fonts\fa\webfonts\fa-regular-400.woff2" />
    <Content Include="UI\fonts\fa\webfonts\fa-solid-900.eot" />
    <Content Include="UI\fonts\fa\webfonts\fa-solid-900.ttf" />
    <Content Include="UI\fonts\fa\webfonts\fa-solid-900.woff" />
    <Content Include="UI\fonts\fa\webfonts\fa-solid-900.woff2" />
    <Content Include="UI\js\decimal.min.js.map" />
    <Content Include="UI\fonts\fontawesome-webfont.woff2" />
    <Content Include="File.ashx" />
    <None Include="UI\js\jquery-1.10.2.intellisense.js" />
    <Content Include="UI\js\jquery-1.10.2.js" />
    <Content Include="UI\js\jquery-1.10.2.min.js" />
    <Content Include="UI\js\jquery.ba-resize.min.js" />
    <Content Include="UI\js\jquery.bbq-1.2.1\jquery.ba-bbq.min.js" />
    <Content Include="UI\js\jquery.blockUI.js" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\ajax.txt" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\example\10_b.jpg" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\example\10_s.jpg" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\example\11_b.jpg" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\example\11_s.jpg" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\example\12_b.jpg" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\example\12_s.jpg" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\example\1_b.jpg" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\example\1_s.jpg" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\example\2_b.jpg" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\example\2_s.jpg" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\example\3_b.jpg" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\example\3_s.jpg" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\example\4_b.jpg" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\example\4_s.jpg" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\example\5_b.jpg" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\example\5_s.jpg" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\example\6_b.jpg" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\example\6_s.jpg" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\example\7_b.jpg" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\example\7_s.jpg" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\example\8_b.jpg" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\example\8_s.jpg" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\example\9_b.jpg" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\example\9_s.jpg" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\fancybox\blank.gif" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\fancybox\fancybox-x.png" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\fancybox\fancybox-y.png" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\fancybox\fancybox.png" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\fancybox\fancy_close.png" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\fancybox\fancy_loading.png" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\fancybox\fancy_nav_left.png" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\fancybox\fancy_nav_right.png" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\fancybox\fancy_shadow_e.png" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\fancybox\fancy_shadow_n.png" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\fancybox\fancy_shadow_ne.png" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\fancybox\fancy_shadow_nw.png" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\fancybox\fancy_shadow_s.png" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\fancybox\fancy_shadow_se.png" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\fancybox\fancy_shadow_sw.png" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\fancybox\fancy_shadow_w.png" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\fancybox\fancy_title_left.png" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\fancybox\fancy_title_main.png" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\fancybox\fancy_title_over.png" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\fancybox\fancy_title_right.png" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\fancybox\jquery.easing-1.3.pack.js" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\fancybox\jquery.fancybox-1.3.4.css" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\fancybox\jquery.fancybox-1.3.4.js" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\fancybox\jquery.fancybox-1.3.4.pack.js" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\fancybox\jquery.mousewheel-3.0.4.pack.js" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\index.html" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\jquery-1.4.3.min.js" />
    <Content Include="UI\js\jquery.fancybox-1.3.4\style.css" />
    <Content Include="UI\js\jquery.growl\javascripts\jquery.growl.js" />
    <Content Include="UI\js\jquery.growl\stylesheets\jquery.growl.css" />
    <Content Include="UI\js\jquery.jscrollpane.min.js" />
    <Content Include="UI\js\jquery.maskedinput.js" />
    <Content Include="UI\js\jquery.mCustomScrollbar.js" />
    <Content Include="UI\js\jquery.mousewheel.js" />
    <Content Include="UI\js\jquery.playSound.js" />
    <Content Include="UI\js\jquery.tablesorter.min.js" />
    <Content Include="UI\js\jquery.timepicker.min.js" />
    <Content Include="UI\js\jquery.tipsy.js" />
    <Content Include="UI\js\jquery.tipTip.js" />
    <Content Include="UI\js\jquery.tipTip.minified.js" />
    <Content Include="UI\js\jquery.tmpl.min.js" />
    <Content Include="UI\js\jquery.ui.selectmenu.js" />
    <Content Include="UI\js\jquery.validate.js" />
    <Content Include="UI\js\jquery.watermark-3.1.3\changelog.txt" />
    <Content Include="UI\js\jquery.watermark-3.1.3\gpl2-license.txt" />
    <Content Include="UI\js\jquery.watermark-3.1.3\jquery.data.js" />
    <Content Include="UI\js\jquery.watermark-3.1.3\jquery.watermark.htm" />
    <Content Include="UI\js\jquery.watermark-3.1.3\jquery.watermark.js" />
    <Content Include="UI\js\jquery.watermark-3.1.3\jquery.watermark.min.js" />
    <Content Include="UI\js\jquery.watermark-3.1.3\mit-license.txt" />
    <Content Include="UI\js\jquery.watermark-3.1.3\readme.txt" />
    <Content Include="UI\js\jqueryui-1.10.3.min.js" />
    <Content Include="UI\js\jqueryui.css" />
    <Content Include="UI\js\jqueryui.min.css" />
    <Content Include="UI\js\lightbox.js" />
    <Content Include="UI\js\lightview.js" />
    <Content Include="UI\js\menu.js" />
    <Content Include="UI\js\moment.2.29.4.min.js" />
    <Content Include="UI\js\mootools-1.2.1-core-yc.js" />
    <Content Include="UI\js\mootools-release-1.11.js" />
    <Content Include="UI\js\mootools.v1.1.js" />
    <Content Include="UI\js\mootools.v1.2.1.js" />
    <Content Include="UI\js\mwheelIntent.js" />
    <Content Include="UI\js\niceforms.js" />
    <Content Include="UI\js\performance.now%28%29-polyfill.js" />
    <Content Include="UI\js\plupload\i18n\cs.js" />
    <Content Include="UI\js\plupload\i18n\da.js" />
    <Content Include="UI\js\plupload\i18n\de.js" />
    <Content Include="UI\js\plupload\i18n\el.js" />
    <Content Include="UI\js\plupload\i18n\es.js" />
    <Content Include="UI\js\plupload\i18n\et.js" />
    <Content Include="UI\js\plupload\i18n\fa.js" />
    <Content Include="UI\js\plupload\i18n\fi.js" />
    <Content Include="UI\js\plupload\i18n\fr-ca.js" />
    <Content Include="UI\js\plupload\i18n\fr.js" />
    <Content Include="UI\js\plupload\i18n\hr.js" />
    <Content Include="UI\js\plupload\i18n\hu.js" />
    <Content Include="UI\js\plupload\i18n\it.js" />
    <Content Include="UI\js\plupload\i18n\ja.js" />
    <Content Include="UI\js\plupload\i18n\ko.js" />
    <Content Include="UI\js\plupload\i18n\lv.js" />
    <Content Include="UI\js\plupload\i18n\nl.js" />
    <Content Include="UI\js\plupload\i18n\pl.js" />
    <Content Include="UI\js\plupload\i18n\pt-br.js" />
    <Content Include="UI\js\plupload\i18n\ro.js" />
    <Content Include="UI\js\plupload\i18n\ru.js" />
    <Content Include="UI\js\plupload\i18n\sr.js" />
    <Content Include="UI\js\plupload\i18n\sv.js" />
    <Content Include="UI\js\plupload\jquery.plupload.queue\css\jquery.plupload.queue.css" />
    <Content Include="UI\js\plupload\jquery.plupload.queue\img\backgrounds.gif" />
    <Content Include="UI\js\plupload\jquery.plupload.queue\img\buttons-disabled.png" />
    <Content Include="UI\js\plupload\jquery.plupload.queue\img\buttons.png" />
    <Content Include="UI\js\plupload\jquery.plupload.queue\img\delete.gif" />
    <Content Include="UI\js\plupload\jquery.plupload.queue\img\done.gif" />
    <Content Include="UI\js\plupload\jquery.plupload.queue\img\error.gif" />
    <Content Include="UI\js\plupload\jquery.plupload.queue\img\throbber.gif" />
    <Content Include="UI\js\plupload\jquery.plupload.queue\img\transp50.png" />
    <Content Include="UI\js\plupload\jquery.plupload.queue\jquery.plupload.queue-d2.js" />
    <Content Include="UI\js\plupload\jquery.plupload.queue\jquery.plupload.queue.js" />
    <Content Include="UI\js\plupload\jquery.ui.plupload\css\jquery.ui.plupload.css" />
    <Content Include="UI\js\plupload\jquery.ui.plupload\img\plupload-bw.png" />
    <Content Include="UI\js\plupload\jquery.ui.plupload\img\plupload.png" />
    <Content Include="UI\js\plupload\jquery.ui.plupload\jquery.ui.plupload.js" />
    <Content Include="UI\js\plupload\plupload.browserplus.js" />
    <Content Include="UI\js\plupload\plupload.flash.js" />
    <Content Include="UI\js\plupload\plupload.flash.swf" />
    <Content Include="UI\js\plupload\plupload.full.js" />
    <Content Include="UI\js\plupload\plupload.gears.js" />
    <Content Include="UI\js\plupload\plupload.html4.js" />
    <Content Include="UI\js\plupload\plupload.html5.js" />
    <Content Include="UI\js\plupload\plupload.js" />
    <Content Include="UI\js\plupload\plupload.silverlight.js" />
    <Content Include="UI\js\plupload\plupload.silverlight.xap" />
    <Content Include="UI\js\prototype.js" />
    <Content Include="UI\js\pusher\pusher-auth.js" />
    <Content Include="UI\js\rangeslider.js" />
    <Content Include="UI\js\SagazQuery\SagazQuery-1.1.js" />
    <Content Include="UI\js\SagazQuery\SagazQuery-1.2.js" />
    <Content Include="UI\js\SagazQuery\SagazQueryDemo-en.htm" />
    <Content Include="UI\js\SagazQuery\SagazQueryDemo-es.htm" />
    <Content Include="UI\js\scriptaculous.js" />
    <Content Include="UI\js\slider.js" />
    <Content Include="UI\js\sorttable.js" />
    <Content Include="UI\js\sound.js" />
    <Content Include="UI\js\spectrum.min.js" />
    <Content Include="UI\js\swal2\sweetalert2.common.js" />
    <Content Include="UI\js\swal2\sweetalert2.css" />
    <Content Include="UI\js\swal2\sweetalert2.js" />
    <Content Include="UI\js\swal2\sweetalert2.min.css" />
    <Content Include="UI\js\swal2\sweetalert2.min.js" />
    <Content Include="UI\js\swal\sweetalert-dev.js" />
    <Content Include="UI\js\swal\sweetalert.css" />
    <Content Include="UI\js\swal\sweetalert.min.js" />
    <Content Include="UI\js\tablednd.js" />
    <Content Include="UI\js\messaging\towbook-messaging.js" />
    <Content Include="UI\js\test.htm" />
    <Content Include="UI\js\timepicker\datepair.js" />
    <Content Include="UI\js\timepicker\glyphicons-halflings.png" />
    <Content Include="UI\js\tinymce\content.css" />
    <Content Include="UI\js\tinymce\langs\en.js" />
    <Content Include="UI\js\tinymce\license.txt" />
    <Content Include="UI\js\tinymce\plugins\advhr\css\advhr.css" />
    <Content Include="UI\js\tinymce\plugins\advhr\editor_plugin.js" />
    <Content Include="UI\js\tinymce\plugins\advhr\editor_plugin_src.js" />
    <Content Include="UI\js\tinymce\plugins\advhr\js\rule.js" />
    <Content Include="UI\js\tinymce\plugins\advhr\langs\en_dlg.js" />
    <Content Include="UI\js\tinymce\plugins\advhr\rule.htm" />
    <Content Include="UI\js\tinymce\plugins\advimage\css\advimage.css" />
    <Content Include="UI\js\tinymce\plugins\advimage\editor_plugin.js" />
    <Content Include="UI\js\tinymce\plugins\advimage\editor_plugin_src.js" />
    <Content Include="UI\js\tinymce\plugins\advimage\image.htm" />
    <Content Include="UI\js\tinymce\plugins\advimage\img\sample.gif" />
    <Content Include="UI\js\tinymce\plugins\advimage\js\image.js" />
    <Content Include="UI\js\tinymce\plugins\advimage\langs\en_dlg.js" />
    <Content Include="UI\js\tinymce\plugins\advlink\css\advlink.css" />
    <Content Include="UI\js\tinymce\plugins\advlink\editor_plugin.js" />
    <Content Include="UI\js\tinymce\plugins\advlink\editor_plugin_src.js" />
    <Content Include="UI\js\tinymce\plugins\advlink\js\advlink.js" />
    <Content Include="UI\js\tinymce\plugins\advlink\langs\en_dlg.js" />
    <Content Include="UI\js\tinymce\plugins\advlink\link.htm" />
    <Content Include="UI\js\tinymce\plugins\advlist\editor_plugin.js" />
    <Content Include="UI\js\tinymce\plugins\advlist\editor_plugin_src.js" />
    <Content Include="UI\js\tinymce\plugins\atomic\editor_plugin_src.js" />
    <Content Include="UI\js\tinymce\plugins\autolink\editor_plugin.js" />
    <Content Include="UI\js\tinymce\plugins\autolink\editor_plugin_src.js" />
    <Content Include="UI\js\tinymce\plugins\autoresize\editor_plugin.js" />
    <Content Include="UI\js\tinymce\plugins\autoresize\editor_plugin_src.js" />
    <Content Include="UI\js\tinymce\plugins\autosave\editor_plugin.js" />
    <Content Include="UI\js\tinymce\plugins\autosave\editor_plugin_src.js" />
    <Content Include="UI\js\tinymce\plugins\autosave\langs\en.js" />
    <Content Include="UI\js\tinymce\plugins\bbcode\editor_plugin.js" />
    <Content Include="UI\js\tinymce\plugins\bbcode\editor_plugin_src.js" />
    <Content Include="UI\js\tinymce\plugins\contextmenu\editor_plugin.js" />
    <Content Include="UI\js\tinymce\plugins\contextmenu\editor_plugin_src.js" />
    <Content Include="UI\js\tinymce\plugins\directionality\editor_plugin.js" />
    <Content Include="UI\js\tinymce\plugins\directionality\editor_plugin_src.js" />
    <Content Include="UI\js\tinymce\plugins\emotions\editor_plugin.js" />
    <Content Include="UI\js\tinymce\plugins\emotions\editor_plugin_src.js" />
    <Content Include="UI\js\tinymce\plugins\emotions\emotions.htm" />
    <Content Include="UI\js\tinymce\plugins\emotions\img\smiley-cool.gif" />
    <Content Include="UI\js\tinymce\plugins\emotions\img\smiley-cry.gif" />
    <Content Include="UI\js\tinymce\plugins\emotions\img\smiley-embarassed.gif" />
    <Content Include="UI\js\tinymce\plugins\emotions\img\smiley-foot-in-mouth.gif" />
    <Content Include="UI\js\tinymce\plugins\emotions\img\smiley-frown.gif" />
    <Content Include="UI\js\tinymce\plugins\emotions\img\smiley-innocent.gif" />
    <Content Include="UI\js\tinymce\plugins\emotions\img\smiley-kiss.gif" />
    <Content Include="UI\js\tinymce\plugins\emotions\img\smiley-laughing.gif" />
    <Content Include="UI\js\tinymce\plugins\emotions\img\smiley-money-mouth.gif" />
    <Content Include="UI\js\tinymce\plugins\emotions\img\smiley-sealed.gif" />
    <Content Include="UI\js\tinymce\plugins\emotions\img\smiley-smile.gif" />
    <Content Include="UI\js\tinymce\plugins\emotions\img\smiley-surprised.gif" />
    <Content Include="UI\js\tinymce\plugins\emotions\img\smiley-tongue-out.gif" />
    <Content Include="UI\js\tinymce\plugins\emotions\img\smiley-undecided.gif" />
    <Content Include="UI\js\tinymce\plugins\emotions\img\smiley-wink.gif" />
    <Content Include="UI\js\tinymce\plugins\emotions\img\smiley-yell.gif" />
    <Content Include="UI\js\tinymce\plugins\emotions\js\emotions.js" />
    <Content Include="UI\js\tinymce\plugins\emotions\langs\en_dlg.js" />
    <Content Include="UI\js\tinymce\plugins\example\dialog.htm" />
    <Content Include="UI\js\tinymce\plugins\example\editor_plugin.js" />
    <Content Include="UI\js\tinymce\plugins\example\editor_plugin_src.js" />
    <Content Include="UI\js\tinymce\plugins\example\img\example.gif" />
    <Content Include="UI\js\tinymce\plugins\example\js\dialog.js" />
    <Content Include="UI\js\tinymce\plugins\example\langs\en.js" />
    <Content Include="UI\js\tinymce\plugins\example\langs\en_dlg.js" />
    <Content Include="UI\js\tinymce\plugins\example_dependency\editor_plugin.js" />
    <Content Include="UI\js\tinymce\plugins\example_dependency\editor_plugin_src.js" />
    <Content Include="UI\js\tinymce\plugins\fullpage\css\fullpage.css" />
    <Content Include="UI\js\tinymce\plugins\fullpage\editor_plugin.js" />
    <Content Include="UI\js\tinymce\plugins\fullpage\editor_plugin_src.js" />
    <Content Include="UI\js\tinymce\plugins\fullpage\fullpage.htm" />
    <Content Include="UI\js\tinymce\plugins\fullpage\js\fullpage.js" />
    <Content Include="UI\js\tinymce\plugins\fullpage\langs\en_dlg.js" />
    <Content Include="UI\js\tinymce\plugins\fullscreen\editor_plugin.js" />
    <Content Include="UI\js\tinymce\plugins\fullscreen\editor_plugin_src.js" />
    <Content Include="UI\js\tinymce\plugins\fullscreen\fullscreen.htm" />
    <Content Include="UI\js\tinymce\plugins\iespell\editor_plugin.js" />
    <Content Include="UI\js\tinymce\plugins\iespell\editor_plugin_src.js" />
    <Content Include="UI\js\tinymce\plugins\inlinepopups\editor_plugin.js" />
    <Content Include="UI\js\tinymce\plugins\inlinepopups\editor_plugin_src.js" />
    <Content Include="UI\js\tinymce\plugins\inlinepopups\skins\clearlooks2\img\alert.gif" />
    <Content Include="UI\js\tinymce\plugins\inlinepopups\skins\clearlooks2\img\button.gif" />
    <Content Include="UI\js\tinymce\plugins\inlinepopups\skins\clearlooks2\img\buttons.gif" />
    <Content Include="UI\js\tinymce\plugins\inlinepopups\skins\clearlooks2\img\confirm.gif" />
    <Content Include="UI\js\tinymce\plugins\inlinepopups\skins\clearlooks2\img\corners.gif" />
    <Content Include="UI\js\tinymce\plugins\inlinepopups\skins\clearlooks2\img\horizontal.gif" />
    <Content Include="UI\js\tinymce\plugins\inlinepopups\skins\clearlooks2\img\vertical.gif" />
    <Content Include="UI\js\tinymce\plugins\inlinepopups\skins\clearlooks2\window.css" />
    <Content Include="UI\js\tinymce\plugins\inlinepopups\template.htm" />
    <Content Include="UI\js\tinymce\plugins\insertdatetime\editor_plugin.js" />
    <Content Include="UI\js\tinymce\plugins\insertdatetime\editor_plugin_src.js" />
    <Content Include="UI\js\tinymce\plugins\layer\editor_plugin.js" />
    <Content Include="UI\js\tinymce\plugins\layer\editor_plugin_src.js" />
    <Content Include="UI\js\tinymce\plugins\legacyoutput\editor_plugin.js" />
    <Content Include="UI\js\tinymce\plugins\legacyoutput\editor_plugin_src.js" />
    <Content Include="UI\js\tinymce\plugins\lists\editor_plugin.js" />
    <Content Include="UI\js\tinymce\plugins\lists\editor_plugin_src.js" />
    <Content Include="UI\js\tinymce\plugins\media\css\media.css" />
    <Content Include="UI\js\tinymce\plugins\media\editor_plugin.js" />
    <Content Include="UI\js\tinymce\plugins\media\editor_plugin_src.js" />
    <Content Include="UI\js\tinymce\plugins\media\js\embed.js" />
    <Content Include="UI\js\tinymce\plugins\media\js\media.js" />
    <Content Include="UI\js\tinymce\plugins\media\langs\en_dlg.js" />
    <Content Include="UI\js\tinymce\plugins\media\media.htm" />
    <Content Include="UI\js\tinymce\plugins\media\moxieplayer.swf" />
    <Content Include="UI\js\tinymce\plugins\nonbreaking\editor_plugin.js" />
    <Content Include="UI\js\tinymce\plugins\nonbreaking\editor_plugin_src.js" />
    <Content Include="UI\js\tinymce\plugins\noneditable\editor_plugin.js" />
    <Content Include="UI\js\tinymce\plugins\noneditable\editor_plugin_src.js" />
    <Content Include="UI\js\tinymce\plugins\pagebreak\editor_plugin.js" />
    <Content Include="UI\js\tinymce\plugins\pagebreak\editor_plugin_src.js" />
    <Content Include="UI\js\tinymce\plugins\paste\editor_plugin.js" />
    <Content Include="UI\js\tinymce\plugins\paste\editor_plugin_src.js" />
    <Content Include="UI\js\tinymce\plugins\paste\js\pastetext.js" />
    <Content Include="UI\js\tinymce\plugins\paste\js\pasteword.js" />
    <Content Include="UI\js\tinymce\plugins\paste\langs\en_dlg.js" />
    <Content Include="UI\js\tinymce\plugins\paste\pastetext.htm" />
    <Content Include="UI\js\tinymce\plugins\paste\pasteword.htm" />
    <Content Include="UI\js\tinymce\plugins\preview\editor_plugin.js" />
    <Content Include="UI\js\tinymce\plugins\preview\editor_plugin_src.js" />
    <Content Include="UI\js\tinymce\plugins\preview\example.html" />
    <Content Include="UI\js\tinymce\plugins\preview\jscripts\embed.js" />
    <Content Include="UI\js\tinymce\plugins\preview\preview.html" />
    <Content Include="UI\js\tinymce\plugins\print\editor_plugin.js" />
    <Content Include="UI\js\tinymce\plugins\print\editor_plugin_src.js" />
    <Content Include="UI\js\tinymce\plugins\save\editor_plugin.js" />
    <Content Include="UI\js\tinymce\plugins\save\editor_plugin_src.js" />
    <Content Include="UI\js\tinymce\plugins\searchreplace\css\searchreplace.css" />
    <Content Include="UI\js\tinymce\plugins\searchreplace\editor_plugin.js" />
    <Content Include="UI\js\tinymce\plugins\searchreplace\editor_plugin_src.js" />
    <Content Include="UI\js\tinymce\plugins\searchreplace\js\searchreplace.js" />
    <Content Include="UI\js\tinymce\plugins\searchreplace\langs\en_dlg.js" />
    <Content Include="UI\js\tinymce\plugins\searchreplace\searchreplace.htm" />
    <Content Include="UI\js\tinymce\plugins\spellchecker\css\content.css" />
    <Content Include="UI\js\tinymce\plugins\spellchecker\editor_plugin.js" />
    <Content Include="UI\js\tinymce\plugins\spellchecker\editor_plugin_src.js" />
    <Content Include="UI\js\tinymce\plugins\spellchecker\img\wline.gif" />
    <Content Include="UI\js\tinymce\plugins\style\css\props.css" />
    <Content Include="UI\js\tinymce\plugins\style\editor_plugin.js" />
    <Content Include="UI\js\tinymce\plugins\style\editor_plugin_src.js" />
    <Content Include="UI\js\tinymce\plugins\style\js\props.js" />
    <Content Include="UI\js\tinymce\plugins\style\langs\en_dlg.js" />
    <Content Include="UI\js\tinymce\plugins\style\props.htm" />
    <Content Include="UI\js\tinymce\plugins\tabfocus\editor_plugin.js" />
    <Content Include="UI\js\tinymce\plugins\tabfocus\editor_plugin_src.js" />
    <Content Include="UI\js\tinymce\plugins\table\cell.htm" />
    <Content Include="UI\js\tinymce\plugins\table\css\cell.css" />
    <Content Include="UI\js\tinymce\plugins\table\css\row.css" />
    <Content Include="UI\js\tinymce\plugins\table\css\table.css" />
    <Content Include="UI\js\tinymce\plugins\table\editor_plugin.js" />
    <Content Include="UI\js\tinymce\plugins\table\editor_plugin_src.js" />
    <Content Include="UI\js\tinymce\plugins\table\js\cell.js" />
    <Content Include="UI\js\tinymce\plugins\table\js\merge_cells.js" />
    <Content Include="UI\js\tinymce\plugins\table\js\row.js" />
    <Content Include="UI\js\tinymce\plugins\table\js\table.js" />
    <Content Include="UI\js\tinymce\plugins\table\langs\en_dlg.js" />
    <Content Include="UI\js\tinymce\plugins\table\merge_cells.htm" />
    <Content Include="UI\js\tinymce\plugins\table\row.htm" />
    <Content Include="UI\js\tinymce\plugins\table\table.htm" />
    <Content Include="UI\js\tinymce\plugins\template\blank.htm" />
    <Content Include="UI\js\tinymce\plugins\template\css\template.css" />
    <Content Include="UI\js\tinymce\plugins\template\editor_plugin.js" />
    <Content Include="UI\js\tinymce\plugins\template\editor_plugin_src.js" />
    <Content Include="UI\js\tinymce\plugins\template\js\template.js" />
    <Content Include="UI\js\tinymce\plugins\template\langs\en_dlg.js" />
    <Content Include="UI\js\tinymce\plugins\template\template.htm" />
    <Content Include="UI\js\tinymce\plugins\visualchars\editor_plugin.js" />
    <Content Include="UI\js\tinymce\plugins\visualchars\editor_plugin_src.js" />
    <Content Include="UI\js\tinymce\plugins\wordcount\editor_plugin.js" />
    <Content Include="UI\js\tinymce\plugins\wordcount\editor_plugin_src.js" />
    <Content Include="UI\js\tinymce\plugins\xhtmlxtras\abbr.htm" />
    <Content Include="UI\js\tinymce\plugins\xhtmlxtras\acronym.htm" />
    <Content Include="UI\js\tinymce\plugins\xhtmlxtras\attributes.htm" />
    <Content Include="UI\js\tinymce\plugins\xhtmlxtras\cite.htm" />
    <Content Include="UI\js\tinymce\plugins\xhtmlxtras\css\attributes.css" />
    <Content Include="UI\js\tinymce\plugins\xhtmlxtras\css\popup.css" />
    <Content Include="UI\js\tinymce\plugins\xhtmlxtras\del.htm" />
    <Content Include="UI\js\tinymce\plugins\xhtmlxtras\editor_plugin.js" />
    <Content Include="UI\js\tinymce\plugins\xhtmlxtras\editor_plugin_src.js" />
    <Content Include="UI\js\tinymce\plugins\xhtmlxtras\ins.htm" />
    <Content Include="UI\js\tinymce\plugins\xhtmlxtras\js\abbr.js" />
    <Content Include="UI\js\tinymce\plugins\xhtmlxtras\js\acronym.js" />
    <Content Include="UI\js\tinymce\plugins\xhtmlxtras\js\attributes.js" />
    <Content Include="UI\js\tinymce\plugins\xhtmlxtras\js\cite.js" />
    <Content Include="UI\js\tinymce\plugins\xhtmlxtras\js\del.js" />
    <Content Include="UI\js\tinymce\plugins\xhtmlxtras\js\element_common.js" />
    <Content Include="UI\js\tinymce\plugins\xhtmlxtras\js\ins.js" />
    <Content Include="UI\js\tinymce\plugins\xhtmlxtras\langs\en_dlg.js" />
    <Content Include="UI\js\tinymce\themes\advanced\about.htm" />
    <Content Include="UI\js\tinymce\themes\advanced\anchor.htm" />
    <Content Include="UI\js\tinymce\themes\advanced\charmap.htm" />
    <Content Include="UI\js\tinymce\themes\advanced\color_picker.htm" />
    <Content Include="UI\js\tinymce\themes\advanced\editor_template.js" />
    <Content Include="UI\js\tinymce\themes\advanced\editor_template_src.js" />
    <Content Include="UI\js\tinymce\themes\advanced\image.htm" />
    <Content Include="UI\js\tinymce\themes\advanced\img\colorpicker.jpg" />
    <Content Include="UI\js\tinymce\themes\advanced\img\flash.gif" />
    <Content Include="UI\js\tinymce\themes\advanced\img\icons.gif" />
    <Content Include="UI\js\tinymce\themes\advanced\img\iframe.gif" />
    <Content Include="UI\js\tinymce\themes\advanced\img\pagebreak.gif" />
    <Content Include="UI\js\tinymce\themes\advanced\img\quicktime.gif" />
    <Content Include="UI\js\tinymce\themes\advanced\img\realmedia.gif" />
    <Content Include="UI\js\tinymce\themes\advanced\img\shockwave.gif" />
    <Content Include="UI\js\tinymce\themes\advanced\img\trans.gif" />
    <Content Include="UI\js\tinymce\themes\advanced\img\video.gif" />
    <Content Include="UI\js\tinymce\themes\advanced\img\windowsmedia.gif" />
    <Content Include="UI\js\tinymce\themes\advanced\js\about.js" />
    <Content Include="UI\js\tinymce\themes\advanced\js\anchor.js" />
    <Content Include="UI\js\tinymce\themes\advanced\js\charmap.js" />
    <Content Include="UI\js\tinymce\themes\advanced\js\color_picker.js" />
    <Content Include="UI\js\tinymce\themes\advanced\js\image.js" />
    <Content Include="UI\js\tinymce\themes\advanced\js\link.js" />
    <Content Include="UI\js\tinymce\themes\advanced\js\source_editor.js" />
    <Content Include="UI\js\tinymce\themes\advanced\langs\en.js" />
    <Content Include="UI\js\tinymce\themes\advanced\langs\en_dlg.js" />
    <Content Include="UI\js\tinymce\themes\advanced\link.htm" />
    <Content Include="UI\js\tinymce\themes\advanced\shortcuts.htm" />
    <Content Include="UI\js\tinymce\themes\advanced\skins\default\content.css" />
    <Content Include="UI\js\tinymce\themes\advanced\skins\default\dialog.css" />
    <Content Include="UI\js\tinymce\themes\advanced\skins\default\img\buttons.png" />
    <Content Include="UI\js\tinymce\themes\advanced\skins\default\img\items.gif" />
    <Content Include="UI\js\tinymce\themes\advanced\skins\default\img\menu_arrow.gif" />
    <Content Include="UI\js\tinymce\themes\advanced\skins\default\img\menu_check.gif" />
    <Content Include="UI\js\tinymce\themes\advanced\skins\default\img\progress.gif" />
    <Content Include="UI\js\tinymce\themes\advanced\skins\default\img\tabs.gif" />
    <Content Include="UI\js\tinymce\themes\advanced\skins\default\ui.css" />
    <Content Include="UI\js\tinymce\themes\advanced\skins\highcontrast\content.css" />
    <Content Include="UI\js\tinymce\themes\advanced\skins\highcontrast\dialog.css" />
    <Content Include="UI\js\tinymce\themes\advanced\skins\highcontrast\ui.css" />
    <Content Include="UI\js\tinymce\themes\advanced\skins\o2k7\content.css" />
    <Content Include="UI\js\tinymce\themes\advanced\skins\o2k7\dialog.css" />
    <Content Include="UI\js\tinymce\themes\advanced\skins\o2k7\img\button_bg.png" />
    <Content Include="UI\js\tinymce\themes\advanced\skins\o2k7\img\button_bg_black.png" />
    <Content Include="UI\js\tinymce\themes\advanced\skins\o2k7\img\button_bg_silver.png" />
    <Content Include="UI\js\tinymce\themes\advanced\skins\o2k7\ui.css" />
    <Content Include="UI\js\tinymce\themes\advanced\skins\o2k7\ui_black.css" />
    <Content Include="UI\js\tinymce\themes\advanced\skins\o2k7\ui_silver.css" />
    <Content Include="UI\js\tinymce\themes\advanced\source_editor.htm" />
    <Content Include="UI\js\tinymce\themes\simple\editor_template.js" />
    <Content Include="UI\js\tinymce\themes\simple\editor_template_src.js" />
    <Content Include="UI\js\tinymce\themes\simple\img\icons.gif" />
    <Content Include="UI\js\tinymce\themes\simple\langs\en.js" />
    <Content Include="UI\js\tinymce\themes\simple\skins\default\content.css" />
    <Content Include="UI\js\tinymce\themes\simple\skins\default\ui.css" />
    <Content Include="UI\js\tinymce\themes\simple\skins\o2k7\content.css" />
    <Content Include="UI\js\tinymce\themes\simple\skins\o2k7\img\button_bg.png" />
    <Content Include="UI\js\tinymce\themes\simple\skins\o2k7\ui.css" />
    <Content Include="UI\js\tinymce\tiny_mce.js" />
    <Content Include="UI\js\tinymce\tiny_mce_popup.js" />
    <Content Include="UI\js\tinymce\tiny_mce_src.js" />
    <Content Include="UI\js\tinymce\utils\editable_selects.js" />
    <Content Include="UI\js\tinymce\utils\form_utils.js" />
    <Content Include="UI\js\tinymce\utils\mctabs.js" />
    <Content Include="UI\js\tinymce\utils\validate.js" />
    <Content Include="UI\js\Towbook.js" />
    <Content Include="UI\js\Towbook\callEditorWindow.js" />
    <Content Include="UI\js\Towbook\callRequests.js" />
    <Content Include="UI\js\Towbook\callRequestsv2.js" />
    <Content Include="UI\js\Towbook\chat.js" />
    <Content Include="UI\js\Towbook\dispatch.js" />
    <Content Include="UI\js\Towbook\notifications.js" />
    <Content Include="UI\js\Towbook\Towbook.js" />
    <Content Include="UI\js\Towbook\towgrid.js" />
    <Content Include="UI\js\TreeView.js" />
    <Content Include="UI\js\unittest.js" />
    <Content Include="UI\js\util-generic.js" />
    <Content Include="UI\js\w2ui\w2ui-1.4.css" />
    <Content Include="UI\js\w2ui\w2ui-1.4.js" />
    <Content Include="UI\js\w2ui\w2ui-1.4.min.css" />
    <Content Include="UI\js\w2ui\w2ui-1.4.min.js" />
    <Content Include="UI\js\xprototype.js" />
    <Content Include="UI\templates\notifications.tpl.html" />
    <Content Include="UI\scss\message-box.css">
      <DependentUpon>message-box.scss</DependentUpon>
    </Content>
    <Content Include="UI\scss\message-box.min.css">
      <DependentUpon>message-box.css</DependentUpon>
    </Content>
    <Content Include="UI\scss\towbook-overrides.css">
      <DependentUpon>towbook-overrides.scss</DependentUpon>
    </Content>
    <Content Include="UI\scss\towbook-overrides.min.css">
      <DependentUpon>towbook-overrides.css</DependentUpon>
    </Content>
    <Content Include="UI\swf\AnyChart.swf" />
    <Content Include="UI\swf\Preloader.swf" />
    <None Include="Dockerfile" />
    <Content Include="k8s\app-deployment-win.yml" />
    <Content Include="Web.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="UI\TowbookV2.master">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Reports\ClosedPeriod\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Reports\ReportHistory\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Compile Include="App_Src\AuthHeaderHelper.cs" />
    <Compile Include="App_Src\Http\CustomHttpClientHandler.cs" />
    <Compile Include="App_Src\Http\HttpCustomClientFactory.cs" />
    <Compile Include="Security\Authentication.cs" />
    <Content Include="Security\SloCallback.aspx.cs">
      <DependentUpon>SloCallback.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Security\SsoCallback.aspx.cs">
      <DependentUpon>SsoCallback.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Compile Include="Security\SOO\OIDCLogoutCallback.cs" />
    <Compile Include="Security\SOO\SAMLLogoutCallback.cs" />
    <Compile Include="Security\SOO\SLOCallbackCreator.cs" />
    <None Include="RequestLog\RotationStatus.aspx.cs">
      <DependentUpon>RotationStatus.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </None>
    <Content Include="Reports\Subcontractor\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Reports\Surveys\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Settings\MUsers\User.aspx.cs">
      <DependentUpon>User.aspx</DependentUpon>
    </Content>
    <Content Include="Accounting\AccountCustomerMatching.aspx.cs">
      <DependentUpon>AccountCustomerMatching.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Accounting\AccountManagement.aspx.cs">
      <DependentUpon>AccountManagement.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Accounting\CustomerManagement.aspx.cs">
      <DependentUpon>CustomerManagement.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Accounting\InvoiceManagement.aspx.cs">
      <DependentUpon>InvoiceManagement.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Accounting\OAuthNegociationSuccess.aspx.cs">
      <DependentUpon>OAuthNegociationSuccess.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Accounting\PaymentMethodsManagement.aspx.cs">
      <DependentUpon>PaymentMethodsManagement.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Accounting\QBConnection.aspx.cs">
      <DependentUpon>QBConnection.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Accounts\AccountEditor.aspx.cs">
      <DependentUpon>AccountEditor.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Accounts\AccountStatement.aspx.cs">
      <DependentUpon>AccountStatement.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Accounts\AccountStatementCreate.aspx.cs">
      <DependentUpon>AccountStatementCreate.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Accounts\AddNote.aspx.cs">
      <DependentUpon>AddNote.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Accounts\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Accounts\FileUpload.aspx.cs">
      <DependentUpon>FileUpload.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Accounts\GroupEmail.aspx.cs">
      <DependentUpon>GroupEmail.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Accounts\ImpoundLot.aspx.cs">
      <DependentUpon>ImpoundLot.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Accounts\PaymentDetails.aspx.cs">
      <DependentUpon>PaymentDetails.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Accounts\PrepareStatements.aspx.cs">
      <DependentUpon>PrepareStatements.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Accounts\RateItem.aspx.cs">
      <DependentUpon>RateItem.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Accounts\RateList.aspx.cs">
      <DependentUpon>RateList.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Accounts\RecordPayment.aspx.cs">
      <DependentUpon>RecordPayment.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Accounts\SendEmail.aspx.cs">
      <DependentUpon>SendEmail.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Accounts\Statement.aspx.cs">
      <DependentUpon>Statement.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Accounts\UnpaidInvoices.aspx.cs">
      <DependentUpon>UnpaidInvoices.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Accounts\User.aspx.cs">
      <DependentUpon>User.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Accounts\SubmitInvoices.aspx.cs">
      <DependentUpon>SubmitInvoices.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Accounts\InvoiceManager.aspx.cs">
      <DependentUpon>InvoiceManager.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Accounts\Account.aspx.cs">
      <DependentUpon>Account.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Accounts\ViewPayments.aspx.cs">
      <DependentUpon>ViewPayments.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Accounts\SubmissionStatus.aspx.cs">
      <DependentUpon>SubmissionStatus.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Compile Include="Accounts\ApiAccess.cs" />
    <Content Include="Accounts\SubcontractorAdd.aspx.cs">
      <DependentUpon>SubcontractorAdd.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Compile Include="Accounts\PushStatement.aspx.cs">
      <DependentUpon>PushStatement.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="App_Src\DefaultHttpClientFactory.cs" />
    <Compile Include="App_Src\Global.cs" />
    <Content Include="Permits\PermitViewer.aspx.cs">
      <DependentUpon>PermitViewer.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Accounts\PrintInvoices.aspx.cs">
      <DependentUpon>PrintInvoices.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Accounts\EmailInvoices.aspx.cs">
      <DependentUpon>EmailInvoices.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Compile Include="App_Src\ApplicationMenuItem.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="App_Src\Navigation.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="App_Src\AppExtensions.cs" />
    <Compile Include="App_Src\Settings.cs">
      <SubType>Code</SubType>
    </Compile>
    <Content Include="B2B\Accounts\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="B2B\Accounts\EntryUploadPhoto2.aspx.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="B2B\Accounts\Photo.aspx.cs">
      <DependentUpon>Photo.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="B2B\Accounts\Request.aspx.cs">
      <DependentUpon>Request.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="B2B\Accounts\ViewInvoice.aspx.cs">
      <DependentUpon>ViewInvoice.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="B2B\Accounts\ViewStatement.aspx.cs">
      <DependentUpon>ViewStatement.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="B2B\Messaging\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="B2B\Permits\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Change.aspx.cs">
      <DependentUpon>Change.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="DispatchEditor\Editor.aspx.cs">
      <DependentUpon>editor.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Dispatch\Vdr.aspx.cs">
      <DependentUpon>Vdr.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Dispatch\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="DS4\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="dyreq\Home_DispatchStatistics.aspx.cs">
      <DependentUpon>Home_DispatchStatistics.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="dyreq\RateItemList.aspx.cs">
      <DependentUpon>RateItemList.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="dyreq\TicketExists.aspx.cs">
      <DependentUpon>TicketExists.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="dyreq\UserExists.aspx.cs">
      <DependentUpon>UserExists.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="dyreq\VehicleModels.aspx.cs">
      <DependentUpon>VehicleModels.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="dyreq\windows\dispatching\Search.aspx.cs">
      <DependentUpon>Search.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="dyreq\windows\dispatching\Search2.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="dyreq\windows\stickering\Search.aspx.cs">
      <DependentUpon>Search.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Impounds\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Map\status\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Map\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Management\Billing\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Management\Clients\DigitalAccountAdd.aspx.cs">
      <DependentUpon>DigitalAccountAdd.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Management\Clients\DigitalAccounts.aspx.cs">
      <DependentUpon>DigitalAccounts.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Management\Clients\EmailForwarderAdd.aspx.cs">
      <DependentUpon>EmailForwarderAdd.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Management\Clients\FeatureAdd.aspx.cs">
      <DependentUpon>FeatureAdd.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Management\Clients\SetupEmail.aspx.cs">
      <DependentUpon>SetupEmail.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Reports\Trucks\PreTripInspection.aspx.cs">
      <DependentUpon>PreTripInspection.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="RequestLog\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Impounds\AutoDataDirect.aspx.cs">
      <DependentUpon>AutoDataDirect.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Impounds\Editor.aspx.cs">
      <DependentUpon>Editor.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Impounds\Email.aspx.cs">
      <DependentUpon>Email.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Impounds\GenerateReport.aspx.cs">
      <DependentUpon>GenerateReport.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Impounds\Impound.aspx.cs">
      <DependentUpon>Impound.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Impounds\ImpoundDelete.aspx.cs">
      <DependentUpon>ImpoundDelete.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Impounds\ImpoundPrint.aspx.cs">
      <DependentUpon>ImpoundPrint.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Impounds\ImpoundReminderTaskDetails.aspx.cs">
      <DependentUpon>ImpoundReminderTaskDetails.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Impounds\ImpoundSummaryOfCharges.aspx.cs">
      <DependentUpon>ImpoundSummaryOfCharges.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Impounds\Invoice.aspx.cs">
      <DependentUpon>Invoice.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Impounds\Note.aspx.cs">
      <DependentUpon>Note.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Impounds\Photo.aspx.cs">
      <DependentUpon>Photo.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Impounds\PhotoUpload.aspx.cs">
      <DependentUpon>PhotoUpload.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Impounds\Print.aspx.cs">
      <DependentUpon>Print.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Impounds\RecordPayment.aspx.cs">
      <DependentUpon>RecordPayment.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Impounds\Release.aspx.cs">
      <DependentUpon>Release.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Impounds\ReleasePickedUp.aspx.cs">
      <DependentUpon>ReleasePickedUp.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Impounds\Report.aspx.cs">
      <DependentUpon>Report.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Impounds\SummaryOfCharges.aspx.cs">
      <DependentUpon>SummaryOfCharges.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Management\Clients\Client.aspx.cs">
      <DependentUpon>Client.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Management\Clients\Companies.aspx.cs">
      <DependentUpon>Companies.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Management\Clients\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Management\Clients\Note.aspx.cs">
      <DependentUpon>Note.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Management\Clients\RecentLogins.aspx.cs">
      <DependentUpon>RecentLogins.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Management\Communications\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Management\Communications\MessageTemplateDetails.aspx.cs">
      <DependentUpon>MessageTemplateDetails.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Management\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Management\Reports\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Management\Tickets\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="MC\Agero\Confirmation.aspx.cs">
      <DependentUpon>Confirmation.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="MC\Agero\SignIn.aspx.cs">
      <DependentUpon>SignIn.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="MC\Agero\SignOut.aspx.cs">
      <DependentUpon>SignOut.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Dispatch2\AccidentReport\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Dispatch2\AccidentReport\Report.aspx.cs">
      <DependentUpon>Report.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Dispatch2\AccidentReport\Photos.aspx.cs">
      <DependentUpon>Photos.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Map2\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Map2\status\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Map2\CallRequest.aspx.cs">
      <DependentUpon>CallRequest.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Active.aspx.cs">
      <DependentUpon>Active.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="AutoDataDirect\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Dispatch2\Invoice.aspx.cs">
      <DependentUpon>Invoice.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Compile Include="Dispatch2\LetterTemplate.aspx.cs">
      <DependentUpon>LetterTemplate.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Content Include="Permits\Photos.aspx.cs">
      <DependentUpon>Photos.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Permits\Editor.aspx.cs">
      <DependentUpon>Editor.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Permits\Extend.aspx.cs">
      <DependentUpon>Extend.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Impounds\ImpoundStatusUpdate.aspx.cs">
      <DependentUpon>ImpoundStatusUpdate.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="MC\ATA\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="MC\Requests\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="MC\Swoop\Confirmation.aspx.cs">
      <DependentUpon>Confirmation.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Permits\History.aspx.cs">
      <DependentUpon>History.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Impounds\Auctions.aspx.cs">
      <DependentUpon>Auctions.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Compile Include="Email\ReplayEmail.aspx.cs">
      <DependentUpon>ReplayEmail.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Email\ReplayEmail.aspx.designer.cs">
      <DependentUpon>ReplayEmail.aspx</DependentUpon>
    </Compile>
    <Compile Include="Impounds\LetterTemplate.aspx.cs">
      <DependentUpon>LetterTemplate.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Content Include="File.ashx" />
    <Content Include="Impounds\Auction.aspx.cs">
      <DependentUpon>Auction.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Content Include="PublicAccess\Impound.aspx.cs">
      <DependentUpon>Impound.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="PublicAccess\Invoice2.aspx.cs">
      <DependentUpon>Invoice2.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="PublicAccess\Photo.aspx.cs">
      <DependentUpon>Photo.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="PublicAccess\Photos.aspx.cs">
      <DependentUpon>Photos.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Reports\Accounting\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Reports\CallAnalysis\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Reports\Custom\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Reports\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Reports\Dispatching\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Reports\Dispatching\Income.aspx.cs">
      <DependentUpon>Income.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Reports\Dispatching\OverallIncome.aspx.cs">
      <DependentUpon>OverallIncome.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Reports\Dispatching\Viewer.aspx.cs">
      <DependentUpon>Viewer.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Reports\Financial\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Reports\Impounds\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Reports\Payroll\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="PublicAccess\SV.aspx.cs">
      <DependentUpon>SV.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Reports\Auctions\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Reports\Payroll\DriverCommission.aspx.cs">
      <DependentUpon>DriverCommission.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Reports\Personal\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Reports\Revenue\RateItems.aspx.cs">
      <DependentUpon>RateItems.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Reports\Trucks\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Security\ErrorReport.aspx.cs">
      <DependentUpon>ErrorReport.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Security\ResetPassword.aspx.cs">
      <DependentUpon>ResetPassword.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Security\SecurityQuestions.aspx.cs">
      <DependentUpon>SecurityQuestions.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Security\Login.aspx.cs">
      <DependentUpon>Login.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Security\Logout.aspx.cs">
      <DependentUpon>Logout.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Security\Pusher.aspx.cs">
      <DependentUpon>Pusher.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Settings\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Signup\default.aspx.cs">
      <DependentUpon>default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Stickering\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Stickering\Delete.aspx.cs">
      <DependentUpon>Delete.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Stickering\Editor.aspx.cs">
      <DependentUpon>Editor.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Stickering\Photos.aspx.cs">
      <DependentUpon>Photos.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Stickering\Settings.aspx.cs">
      <DependentUpon>Settings.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Stickering\Viewer.aspx.cs">
      <DependentUpon>Viewer.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Support\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Support\Suggestions.aspx.cs">
      <DependentUpon>Suggestions.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="PublicAccess\DamagePhoto.aspx.cs">
      <DependentUpon>DamagePhoto.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="PublicAccess\DamagePhotos.aspx.cs">
      <DependentUpon>DamagePhotos.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="PublicAccess\SignaturePhoto.aspx.cs">
      <DependentUpon>SignaturePhoto.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="PublicAccess\PreTripPhoto.aspx.cs">
      <DependentUpon>PreTripPhoto.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Compile Include="PublicAccess\DamageVideo.aspx.cs">
      <DependentUpon>DamageVideo.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="PublicAccess\PrintDetails.aspx.cs">
      <DependentUpon>PrintDetails.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="PublicAccess\ParkingPermit.aspx.cs">
      <DependentUpon>ParkingPermit.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="PublicAccess\Video.aspx.cs">
      <DependentUpon>Video.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Content Include="Stickering\Extend.aspx.cs">
      <DependentUpon>Extend.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="Reports\Stickering\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="SurveyLog\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="UI\Dialog.master.cs">
      <DependentUpon>Dialog.master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
    <Content Include="UI\Towbook.master.cs">
      <DependentUpon>Towbook.master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Content Include="NLog.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="eo_web.ashx" />
    <Content Include="k8s\keyvault-towbook-secrets.yml" />
    <Content Include="UI\audio\alert.ogg" />
    <Content Include="UI\Dialog.master" />
    <Content Include="UI\fonts\fontawesome-webfont.eot" />
    <Content Include="UI\fonts\fontawesome-webfont.ttf" />
    <Content Include="UI\fonts\fontawesome-webfont.woff" />
    <Content Include="UI\fonts\FontAwesome.otf" />
    <Content Include="UI\js\jquery-1.10.2.min.map" />
    <Content Include="UI\js\w2ui\w2ui-1.4.min.js.map" />
    <Content Include="UI\Towbook.master" />
    <Content Include="UI\scss\towbook-overrides.scss" />
    <Content Include="UI\scss\message-box.scss" />
    <None Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
    <None Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="App_Code\" />
    <Folder Include="App_Start\" />
    <Folder Include="B2B\Impounds\" />
    <Folder Include="Models\" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Extric.Towbook.API.Models\Extric.Towbook.API.Models.csproj">
      <Project>{a5af3912-02a3-4317-92e4-e1a4e61da5c7}</Project>
      <Name>Extric.Towbook.API.Models</Name>
    </ProjectReference>
    <ProjectReference Include="..\Extric.Towbook.Generated.Features\Extric.Towbook.Generated.Features.csproj">
      <Project>{5d586d7f-f7fe-4db4-b0da-5a85a76a9f39}</Project>
      <Name>Extric.Towbook.Generated.Features</Name>
    </ProjectReference>
    <ProjectReference Include="..\Extric.Towbook.Integration.MotorClubs\Extric.Towbook.Integration.MotorClubs.csproj">
      <Project>{d2638757-3957-4fa0-84c1-2f17d0c63177}</Project>
      <Name>Extric.Towbook.Integration.MotorClubs</Name>
    </ProjectReference>
    <ProjectReference Include="..\Extric.Towbook.Integrations.Email\Extric.Towbook.Integrations.Email.csproj">
      <Project>{d9332002-6e9b-4d8c-98e3-15d9bc8067f8}</Project>
      <Name>Extric.Towbook.Integrations.Email</Name>
    </ProjectReference>
    <ProjectReference Include="..\Extric.Towbook.Integrations.Faxing\Extric.Towbook.Integrations.Faxing.csproj">
      <Project>{cd2f900c-d6d7-4261-a98b-eb63473b2746}</Project>
      <Name>Extric.Towbook.Integrations.Faxing</Name>
    </ProjectReference>
    <ProjectReference Include="..\Extric.Towbook.Storage\Extric.Towbook.Storage.csproj">
      <Project>{fb8973fa-4076-486f-919d-83aaa031a8e9}</Project>
      <Name>Extric.Towbook.Storage</Name>
    </ProjectReference>
    <ProjectReference Include="..\Extric.Towbook.WebShared\Extric.Towbook.WebShared.csproj">
      <Project>{02cb863e-8849-4637-b56e-b303f6f748e2}</Project>
      <Name>Extric.Towbook.WebShared</Name>
    </ProjectReference>
    <ProjectReference Include="..\Extric.Towbook.WebWrapper\Extric.Towbook.WebWrapper.csproj">
      <Project>{1033585a-049e-4331-ad57-562d24ae63a3}</Project>
      <Name>Extric.Towbook.WebWrapper</Name>
    </ProjectReference>
    <ProjectReference Include="..\Extric.Towbook\Extric.Towbook.csproj">
      <Project>{f81274d1-f72c-46f9-9221-eeea7a56ee82}</Project>
      <Name>Extric.Towbook</Name>
    </ProjectReference>
    <ProjectReference Include="..\Integrations\Data\Extric.Towbook.Integrations.AutoData\Extric.Towbook.Integrations.AutoData.csproj">
      <Project>{3945E0F4-381A-47CB-BA94-FE8642E42E8C}</Project>
      <Name>Extric.Towbook.Integrations.AutoData</Name>
    </ProjectReference>
    <ProjectReference Include="..\Integrations\MotorClubs\Aaa\Aaa.csproj">
      <Project>{1e3768b4-a050-480c-88e0-778833b82999}</Project>
      <Name>Aaa</Name>
    </ProjectReference>
    <ProjectReference Include="..\Integrations\MotorClubs\Agero\Agero.csproj">
      <Project>{1e3768b4-a050-480c-88e0-714533b82c76}</Project>
      <Name>Agero</Name>
    </ProjectReference>
    <ProjectReference Include="..\Integrations\MotorClubs\Allstate\Allstate.csproj">
      <Project>{5453b20c-2007-42d2-ab47-be294d82c856}</Project>
      <Name>Allstate</Name>
    </ProjectReference>
    <ProjectReference Include="..\Integrations\MotorClubs\Fleetnet\Fleetnet.csproj">
      <Project>{eb8443a1-94a8-4895-8a84-72265c4da22f}</Project>
      <Name>Fleetnet</Name>
    </ProjectReference>
    <ProjectReference Include="..\Integrations\MotorClubs\Geico\Issc.csproj">
      <Project>{18caf165-f57b-447b-8f0f-317a8e611aeb}</Project>
      <Name>Issc</Name>
    </ProjectReference>
    <ProjectReference Include="..\Integrations\MotorClubs\Swoop\Swoop.csproj">
      <Project>{1e3768b4-a050-480c-88e0-714533b82c78}</Project>
      <Name>Swoop</Name>
    </ProjectReference>
    <ProjectReference Include="..\Integrations\Square\Square\Extric.Towbook.Square.csproj">
      <Project>{e31a260d-8a19-494d-8aad-1bfc8428e7b2}</Project>
      <Name>Extric.Towbook.Square</Name>
    </ProjectReference>
    <ProjectReference Include="..\Roadside\Extric.Roadside\Extric.Roadside.csproj">
      <Project>{f8c766cf-a93d-455c-bdcd-e432770a617e}</Project>
      <Name>Extric.Roadside</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="AWSSDK.Core">
      <Version>3.7.400.10</Version>
    </PackageReference>
    <PackageReference Include="Azure.Core">
      <Version>1.42.0</Version>
    </PackageReference>
    <PackageReference Include="Braintree">
      <Version>5.26.0</Version>
    </PackageReference>
    <PackageReference Include="EO.Pdf">
      <Version>24.1.93</Version>
    </PackageReference>
    <PackageReference Include="EO.Web">
      <Version>24.1.93</Version>
    </PackageReference>
    <PackageReference Include="EPPlus">
      <Version>7.3.0</Version>
    </PackageReference>
    <PackageReference Include="ExifLib">
      <Version>1.7.0</Version>
    </PackageReference>
    <PackageReference Include="ITfoxtec.Identity.Saml2.Mvc">
      <Version>4.11.3</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.Azure.Cosmos">
      <Version>3.42.0</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.Bcl.AsyncInterfaces">
      <Version>9.0.0</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.Configuration.ConfigurationBuilders.Environment">
      <Version>3.0.0</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions">
      <Version>8.0.0</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.IO.RecyclableMemoryStream">
      <Version>3.0.1</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.Web.Infrastructure">
      <Version>2.0.0</Version>
    </PackageReference>
    <PackageReference Include="MimeKit">
      <Version>4.7.1</Version>
    </PackageReference>
    <PackageReference Include="MSBuild.Microsoft.VisualStudio.Web.targets">
      <Version>14.0.0.3</Version>
    </PackageReference>
    <PackageReference Include="NewRelic.Agent.Api">
      <Version>10.29.0</Version>
    </PackageReference>
    <PackageReference Include="Newtonsoft.Json">
      <Version>13.0.3</Version>
    </PackageReference>
    <PackageReference Include="NLog">
      <Version>5.3.3</Version>
    </PackageReference>
    <PackageReference Include="NLog.Targets.Loggly">
      <Version>4.8.63</Version>
    </PackageReference>
    <PackageReference Include="NVelocity">
      <Version>1.2.0</Version>
    </PackageReference>
    <PackageReference Include="protobuf-net">
      <Version>3.2.30</Version>
    </PackageReference>
    <PackageReference Include="PusherServer">
      <Version>5.0.0</Version>
    </PackageReference>
    <PackageReference Include="System.Collections.Immutable">
      <Version>8.0.0</Version>
    </PackageReference>
    <PackageReference Include="System.Configuration.ConfigurationManager">
      <Version>8.0.0</Version>
    </PackageReference>
    <PackageReference Include="System.Data.SqlClient">
      <Version>4.8.6</Version>
    </PackageReference>
    <PackageReference Include="System.Diagnostics.DiagnosticSource">
      <Version>8.0.1</Version>
    </PackageReference>
    <PackageReference Include="System.IdentityModel.Tokens.Jwt">
      <Version>8.0.2</Version>
    </PackageReference>
    <PackageReference Include="System.Text.Encodings.Web">
      <Version>9.0.0</Version>
    </PackageReference>
    <PackageReference Include="System.ValueTuple">
      <Version>4.5.0</Version>
    </PackageReference>
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>10003</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:10003/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Import Project="..\packages\Microsoft.VisualStudio.Azure.Containers.Tools.Targets.1.11.1\build\Microsoft.VisualStudio.Azure.Containers.Tools.Targets.targets" Condition="Exists('..\packages\Microsoft.VisualStudio.Azure.Containers.Tools.Targets.1.11.1\build\Microsoft.VisualStudio.Azure.Containers.Tools.Targets.targets')" />
  <PropertyGroup>
    <PreBuildEvent>git rev-parse HEAD &gt; "..\Extric.Towbook\_git_commit.txt"</PreBuildEvent>
  </PropertyGroup>
  <Import Project="..\packages\Microsoft.Extensions.Logging.Abstractions.6.0.0\build\Microsoft.Extensions.Logging.Abstractions.targets" Condition="Exists('..\packages\Microsoft.Extensions.Logging.Abstractions.6.0.0\build\Microsoft.Extensions.Logging.Abstractions.targets')" />
  <Import Project="..\packages\System.Text.Json.6.0.5\build\System.Text.Json.targets" Condition="Exists('..\packages\System.Text.Json.6.0.5\build\System.Text.Json.targets')" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
  <Target Name="AfterBuild">
    <ItemGroup>
      <MySourceFiles Include="$(OutputPath)x64\**\*.*" />
    </ItemGroup>
    <Copy SourceFiles="@(MySourceFiles)" DestinationFolder="$(OutputPath)" ContinueOnError="true" />
  </Target>
</Project>