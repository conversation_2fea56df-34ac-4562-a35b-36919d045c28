{"name": "chatv2", "version": "1.0.0", "lockfileVersion": 1, "requires": true, "dependencies": {"@babel/code-frame": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.0.0.tgz", "integrity": "sha512-OfC2uemaknXr87bdLUkWog7nYuliM9Ij5HUcajsVcMCpQrcLmtxRbVFTIqmcSkSeYRBFBRxs2FiUqFJDLdiebA==", "dev": true, "requires": {"@babel/highlight": "^7.0.0"}}, "@babel/core": {"version": "7.4.5", "resolved": "https://registry.npmjs.org/@babel/core/-/core-7.4.5.tgz", "integrity": "sha512-OvjIh6aqXtlsA8ujtGKfC7LYWksYSX8yQcM8Ay3LuvVeQ63lcOKgoZWVqcpFwkd29aYU9rVx7jxhfhiEDV9MZA==", "dev": true, "requires": {"@babel/code-frame": "^7.0.0", "@babel/generator": "^7.4.4", "@babel/helpers": "^7.4.4", "@babel/parser": "^7.4.5", "@babel/template": "^7.4.4", "@babel/traverse": "^7.4.5", "@babel/types": "^7.4.4", "convert-source-map": "^1.1.0", "debug": "^4.1.0", "json5": "^2.1.0", "lodash": "^4.17.11", "resolve": "^1.3.2", "semver": "^5.4.1", "source-map": "^0.5.0"}}, "@babel/generator": {"version": "7.4.4", "resolved": "https://registry.npmjs.org/@babel/generator/-/generator-7.4.4.tgz", "integrity": "sha512-53UOLK6TVNqKxf7RUh8NE851EHRxOOeVXKbK2bivdb+iziMyk03Sr4eaE9OELCbyZAAafAKPDwF2TPUES5QbxQ==", "dev": true, "requires": {"@babel/types": "^7.4.4", "jsesc": "^2.5.1", "lodash": "^4.17.11", "source-map": "^0.5.0", "trim-right": "^1.0.1"}}, "@babel/helper-annotate-as-pure": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.0.0.tgz", "integrity": "sha512-3UYcJUj9kvSLbLbUIfQTqzcy5VX7GRZ/CCDrnOaZorFFM01aXp1+GJwuFGV4NDDoAS+mOUyHcO6UD/RfqOks3Q==", "dev": true, "requires": {"@babel/types": "^7.0.0"}}, "@babel/helper-builder-binary-assignment-operator-visitor": {"version": "7.1.0", "resolved": "https://registry.npmjs.org/@babel/helper-builder-binary-assignment-operator-visitor/-/helper-builder-binary-assignment-operator-visitor-7.1.0.tgz", "integrity": "sha512-qNSR4jrmJ8M1VMM9tibvyRAHXQs2PmaksQF7c1CGJNipfe3D8p+wgNwgso/P2A2r2mdgBWAXljNWR0QRZAMW8w==", "dev": true, "requires": {"@babel/helper-explode-assignable-expression": "^7.1.0", "@babel/types": "^7.0.0"}}, "@babel/helper-builder-react-jsx": {"version": "7.3.0", "resolved": "https://registry.npmjs.org/@babel/helper-builder-react-jsx/-/helper-builder-react-jsx-7.3.0.tgz", "integrity": "sha512-MjA9KgwCuPEkQd9ncSXvSyJ5y+j2sICHyrI0M3L+6fnS4wMSNDc1ARXsbTfbb2cXHn17VisSnU/sHFTCxVxSMw==", "dev": true, "requires": {"@babel/types": "^7.3.0", "esutils": "^2.0.0"}}, "@babel/helper-call-delegate": {"version": "7.4.4", "resolved": "https://registry.npmjs.org/@babel/helper-call-delegate/-/helper-call-delegate-7.4.4.tgz", "integrity": "sha512-l79boDFJ8S1c5hvQvG+rc+wHw6IuH7YldmRKsYtpbawsxURu/paVy57FZMomGK22/JckepaikOkY0MoAmdyOlQ==", "dev": true, "requires": {"@babel/helper-hoist-variables": "^7.4.4", "@babel/traverse": "^7.4.4", "@babel/types": "^7.4.4"}}, "@babel/helper-create-class-features-plugin": {"version": "7.4.4", "resolved": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.4.4.tgz", "integrity": "sha512-UbBHIa2qeAGgyiNR9RszVF7bUHEdgS4JAUNT8SiqrAN6YJVxlOxeLr5pBzb5kan302dejJ9nla4RyKcR1XT6XA==", "dev": true, "requires": {"@babel/helper-function-name": "^7.1.0", "@babel/helper-member-expression-to-functions": "^7.0.0", "@babel/helper-optimise-call-expression": "^7.0.0", "@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-replace-supers": "^7.4.4", "@babel/helper-split-export-declaration": "^7.4.4"}}, "@babel/helper-define-map": {"version": "7.4.4", "resolved": "https://registry.npmjs.org/@babel/helper-define-map/-/helper-define-map-7.4.4.tgz", "integrity": "sha512-IX3Ln8gLhZpSuqHJSnTNBWGDE9kdkTEWl21A/K7PQ00tseBwbqCHTvNLHSBd9M0R5rER4h5Rsvj9vw0R5SieBg==", "dev": true, "requires": {"@babel/helper-function-name": "^7.1.0", "@babel/types": "^7.4.4", "lodash": "^4.17.11"}}, "@babel/helper-explode-assignable-expression": {"version": "7.1.0", "resolved": "https://registry.npmjs.org/@babel/helper-explode-assignable-expression/-/helper-explode-assignable-expression-7.1.0.tgz", "integrity": "sha512-NRQpfHrJ1msCHtKjbzs9YcMmJZOg6mQMmGRB+hbamEdG5PNpaSm95275VD92DvJKuyl0s2sFiDmMZ+EnnvufqA==", "dev": true, "requires": {"@babel/traverse": "^7.1.0", "@babel/types": "^7.0.0"}}, "@babel/helper-function-name": {"version": "7.1.0", "resolved": "https://registry.npmjs.org/@babel/helper-function-name/-/helper-function-name-7.1.0.tgz", "integrity": "sha512-A95XEoCpb3TO+KZzJ4S/5uW5fNe26DjBGqf1o9ucyLyCmi1dXq/B3c8iaWTfBk3VvetUxl16e8tIrd5teOCfGw==", "dev": true, "requires": {"@babel/helper-get-function-arity": "^7.0.0", "@babel/template": "^7.1.0", "@babel/types": "^7.0.0"}}, "@babel/helper-get-function-arity": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/helper-get-function-arity/-/helper-get-function-arity-7.0.0.tgz", "integrity": "sha512-r2DbJeg4svYvt3HOS74U4eWKsUAMRH01Z1ds1zx8KNTPtpTL5JAsdFv8BNyOpVqdFhHkkRDIg5B4AsxmkjAlmQ==", "dev": true, "requires": {"@babel/types": "^7.0.0"}}, "@babel/helper-hoist-variables": {"version": "7.4.4", "resolved": "https://registry.npmjs.org/@babel/helper-hoist-variables/-/helper-hoist-variables-7.4.4.tgz", "integrity": "sha512-VYk2/H/BnYbZDDg39hr3t2kKyifAm1W6zHRfhx8jGjIHpQEBv9dry7oQ2f3+J703TLu69nYdxsovl0XYfcnK4w==", "dev": true, "requires": {"@babel/types": "^7.4.4"}}, "@babel/helper-member-expression-to-functions": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.0.0.tgz", "integrity": "sha512-avo+lm/QmZlv27Zsi0xEor2fKcqWG56D5ae9dzklpIaY7cQMK5N8VSpaNVPPagiqmy7LrEjK1IWdGMOqPu5csg==", "dev": true, "requires": {"@babel/types": "^7.0.0"}}, "@babel/helper-module-imports": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.0.0.tgz", "integrity": "sha512-aP/hlLq01DWNEiDg4Jn23i+CXxW/owM4WpDLFUbpjxe4NS3BhLVZQ5i7E0ZrxuQ/vwekIeciyamgB1UIYxxM6A==", "requires": {"@babel/types": "^7.0.0"}}, "@babel/helper-module-transforms": {"version": "7.4.4", "resolved": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.4.4.tgz", "integrity": "sha512-3Z1yp8TVQf+B4ynN7WoHPKS8EkdTbgAEy0nU0rs/1Kw4pDgmvYH3rz3aI11KgxKCba2cn7N+tqzV1mY2HMN96w==", "dev": true, "requires": {"@babel/helper-module-imports": "^7.0.0", "@babel/helper-simple-access": "^7.1.0", "@babel/helper-split-export-declaration": "^7.4.4", "@babel/template": "^7.4.4", "@babel/types": "^7.4.4", "lodash": "^4.17.11"}}, "@babel/helper-optimise-call-expression": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.0.0.tgz", "integrity": "sha512-u8nd9NQePYNQV8iPWu/pLLYBqZBa4ZaY1YWRFMuxrid94wKI1QNt67NEZ7GAe5Kc/0LLScbim05xZFWkAdrj9g==", "dev": true, "requires": {"@babel/types": "^7.0.0"}}, "@babel/helper-plugin-utils": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.0.0.tgz", "integrity": "sha512-CYAOUCARwExnEixLdB6sDm2dIJ/YgEAKDM1MOeMeZu9Ld/bDgVo8aiWrXwcY7OBh+1Ea2uUcVRcxKk0GJvW7QA==", "dev": true}, "@babel/helper-regex": {"version": "7.4.4", "resolved": "https://registry.npmjs.org/@babel/helper-regex/-/helper-regex-7.4.4.tgz", "integrity": "sha512-Y5nuB/kESmR3tKjU8Nkn1wMGEx1tjJX076HBMeL3XLQCu6vA/YRzuTW0bbb+qRnXvQGn+d6Rx953yffl8vEy7Q==", "dev": true, "requires": {"lodash": "^4.17.11"}}, "@babel/helper-remap-async-to-generator": {"version": "7.1.0", "resolved": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.1.0.tgz", "integrity": "sha512-3fOK0L+Fdlg8S5al8u/hWE6vhufGSn0bN09xm2LXMy//REAF8kDCrYoOBKYmA8m5Nom+sV9LyLCwrFynA8/slg==", "dev": true, "requires": {"@babel/helper-annotate-as-pure": "^7.0.0", "@babel/helper-wrap-function": "^7.1.0", "@babel/template": "^7.1.0", "@babel/traverse": "^7.1.0", "@babel/types": "^7.0.0"}}, "@babel/helper-replace-supers": {"version": "7.4.4", "resolved": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.4.4.tgz", "integrity": "sha512-04xGEnd+s01nY1l15EuMS1rfKktNF+1CkKmHoErDppjAAZL+IUBZpzT748x262HF7fibaQPhbvWUl5HeSt1EXg==", "dev": true, "requires": {"@babel/helper-member-expression-to-functions": "^7.0.0", "@babel/helper-optimise-call-expression": "^7.0.0", "@babel/traverse": "^7.4.4", "@babel/types": "^7.4.4"}}, "@babel/helper-simple-access": {"version": "7.1.0", "resolved": "https://registry.npmjs.org/@babel/helper-simple-access/-/helper-simple-access-7.1.0.tgz", "integrity": "sha512-Vk+78hNjRbsiu49zAPALxTb+JUQCz1aolpd8osOF16BGnLtseD21nbHgLPGUwrXEurZgiCOUmvs3ExTu4F5x6w==", "dev": true, "requires": {"@babel/template": "^7.1.0", "@babel/types": "^7.0.0"}}, "@babel/helper-split-export-declaration": {"version": "7.4.4", "resolved": "https://registry.npmjs.org/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.4.4.tgz", "integrity": "sha512-Ro/XkzLf3JFITkW6b+hNxzZ1n5OQ80NvIUdmHspih1XAhtN3vPTuUFT4eQnela+2MaZ5ulH+iyP513KJrxbN7Q==", "dev": true, "requires": {"@babel/types": "^7.4.4"}}, "@babel/helper-wrap-function": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.2.0.tgz", "integrity": "sha512-o9fP1BZLLSrYlxYEYyl2aS+Flun5gtjTIG8iln+XuEzQTs0PLagAGSXUcqruJwD5fM48jzIEggCKpIfWTcR7pQ==", "dev": true, "requires": {"@babel/helper-function-name": "^7.1.0", "@babel/template": "^7.1.0", "@babel/traverse": "^7.1.0", "@babel/types": "^7.2.0"}}, "@babel/helpers": {"version": "7.4.4", "resolved": "https://registry.npmjs.org/@babel/helpers/-/helpers-7.4.4.tgz", "integrity": "sha512-igczbR/0SeuPR8RFfC7tGrbdTbFL3QTvH6D+Z6zNxnTe//GyqmtHmDkzrqDmyZ3eSwPqB/LhyKoU5DXsp+Vp2A==", "dev": true, "requires": {"@babel/template": "^7.4.4", "@babel/traverse": "^7.4.4", "@babel/types": "^7.4.4"}}, "@babel/highlight": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/highlight/-/highlight-7.0.0.tgz", "integrity": "sha512-UFMC4ZeFC48Tpvj7C8UgLvtkaUuovQX+5xNWrsIoMG8o2z+XFKjKaN9iVmS84dPwVN00W4wPmqvYoZF3EGAsfw==", "dev": true, "requires": {"chalk": "^2.0.0", "esutils": "^2.0.2", "js-tokens": "^4.0.0"}}, "@babel/parser": {"version": "7.4.5", "resolved": "https://registry.npmjs.org/@babel/parser/-/parser-7.4.5.tgz", "integrity": "sha512-9mUqkL1FF5T7f0WDFfAoDdiMVPWsdD1gZYzSnaXsxUCUqzuch/8of9G3VUSNiZmMBoRxT3neyVsqeiL/ZPcjew==", "dev": true}, "@babel/plugin-proposal-async-generator-functions": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/@babel/plugin-proposal-async-generator-functions/-/plugin-proposal-async-generator-functions-7.2.0.tgz", "integrity": "sha512-+Dfo/SCQqrwx48ptLVGLdE39YtWRuKc/Y9I5Fy0P1DDBB9lsAHpjcEJQt+4IifuSOSTLBKJObJqMvaO1pIE8LQ==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-remap-async-to-generator": "^7.1.0", "@babel/plugin-syntax-async-generators": "^7.2.0"}}, "@babel/plugin-proposal-class-properties": {"version": "7.4.4", "resolved": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.4.4.tgz", "integrity": "sha512-WjKTI8g8d5w1Bc9zgwSz2nfrsNQsXcCf9J9cdCvrJV6RF56yztwm4TmJC0MgJ9tvwO9gUA/mcYe89bLdGfiXFg==", "dev": true, "requires": {"@babel/helper-create-class-features-plugin": "^7.4.4", "@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-proposal-json-strings": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/@babel/plugin-proposal-json-strings/-/plugin-proposal-json-strings-7.2.0.tgz", "integrity": "sha512-MAFV1CA/YVmYwZG0fBQyXhmj0BHCB5egZHCKWIFVv/XCxAeVGIHfos3SwDck4LvCllENIAg7xMKOG5kH0dzyUg==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-json-strings": "^7.2.0"}}, "@babel/plugin-proposal-object-rest-spread": {"version": "7.4.4", "resolved": "https://registry.npmjs.org/@babel/plugin-proposal-object-rest-spread/-/plugin-proposal-object-rest-spread-7.4.4.tgz", "integrity": "sha512-dMBG6cSPBbHeEBdFXeQ2QLc5gUpg4Vkaz8octD4aoW/ISO+jBOcsuxYL7bsb5WSu8RLP6boxrBIALEHgoHtO9g==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-object-rest-spread": "^7.2.0"}}, "@babel/plugin-proposal-optional-catch-binding": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/@babel/plugin-proposal-optional-catch-binding/-/plugin-proposal-optional-catch-binding-7.2.0.tgz", "integrity": "sha512-mgYj3jCcxug6KUcX4OBoOJz3CMrwRfQELPQ5560F70YQUBZB7uac9fqaWamKR1iWUzGiK2t0ygzjTScZnVz75g==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-optional-catch-binding": "^7.2.0"}}, "@babel/plugin-proposal-unicode-property-regex": {"version": "7.4.4", "resolved": "https://registry.npmjs.org/@babel/plugin-proposal-unicode-property-regex/-/plugin-proposal-unicode-property-regex-7.4.4.tgz", "integrity": "sha512-j1NwnOqMG9mFUOH58JTFsA/+ZYzQLUZ/drqWUqxCYLGeu2JFZL8YrNC9hBxKmWtAuOCHPcRpgv7fhap09Fb4kA==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-regex": "^7.4.4", "regexpu-core": "^4.5.4"}}, "@babel/plugin-syntax-async-generators": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.2.0.tgz", "integrity": "sha512-1ZrIRBv2t0GSlcwVoQ6VgSLpLgiN/FVQUzt9znxo7v2Ov4jJrs8RY8tv0wvDmFN3qIdMKWrmMMW6yZ0G19MfGg==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-syntax-json-strings": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.2.0.tgz", "integrity": "sha512-5UGYnMSLRE1dqqZwug+1LISpA403HzlSfsg6P9VXU6TBjcSHeNlw4DxDx7LgpF+iKZoOG/+uzqoRHTdcUpiZNg==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-syntax-jsx": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.2.0.tgz", "integrity": "sha512-VyN4QANJkRW6lDBmENzRszvZf3/4AXaj9YR7GwrWeeN9tEBPuXbmDYVU9bYBN0D70zCWVwUy0HWq2553VCb6Hw==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-syntax-object-rest-spread": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.2.0.tgz", "integrity": "sha512-t0JKGgqk2We+9may3t0xDdmneaXmyxq0xieYcKHxIsrJO64n1OiMWNUtc5gQK1PA0NpdCRrtZp4z+IUaKugrSA==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-syntax-optional-catch-binding": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.2.0.tgz", "integrity": "sha512-bDe4xKNhb0LI7IvZHiA13kff0KEfaGX/Hv4lMA9+7TEc63hMNvfKo6ZFpXhKuEp+II/q35Gc4NoMeDZyaUbj9w==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-transform-arrow-functions": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.2.0.tgz", "integrity": "sha512-ER77Cax1+8/8jCB9fo4Ud161OZzWN5qawi4GusDuRLcDbDG+bIGYY20zb2dfAFdTRGzrfq2xZPvF0R64EHnimg==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-transform-async-to-generator": {"version": "7.4.4", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.4.4.tgz", "integrity": "sha512-YiqW2Li8TXmzgbXw+STsSqPBPFnGviiaSp6CYOq55X8GQ2SGVLrXB6pNid8HkqkZAzOH6knbai3snhP7v0fNwA==", "dev": true, "requires": {"@babel/helper-module-imports": "^7.0.0", "@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-remap-async-to-generator": "^7.1.0"}}, "@babel/plugin-transform-block-scoped-functions": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-block-scoped-functions/-/plugin-transform-block-scoped-functions-7.2.0.tgz", "integrity": "sha512-ntQPR6q1/NKuphly49+QiQiTN0O63uOwjdD6dhIjSWBI5xlrbUFh720TIpzBhpnrLfv2tNH/BXvLIab1+BAI0w==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-transform-block-scoping": {"version": "7.4.4", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.4.4.tgz", "integrity": "sha512-jkTUyWZcTrwxu5DD4rWz6rDB5Cjdmgz6z7M7RLXOJyCUkFBawssDGcGh8M/0FTSB87avyJI1HsTwUXp9nKA1PA==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0", "lodash": "^4.17.11"}}, "@babel/plugin-transform-classes": {"version": "7.4.4", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.4.4.tgz", "integrity": "sha512-/e44eFLImEGIpL9qPxSRat13I5QNRgBLu2hOQJCF7VLy/otSM/sypV1+XaIw5+502RX/+6YaSAPmldk+nhHDPw==", "dev": true, "requires": {"@babel/helper-annotate-as-pure": "^7.0.0", "@babel/helper-define-map": "^7.4.4", "@babel/helper-function-name": "^7.1.0", "@babel/helper-optimise-call-expression": "^7.0.0", "@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-replace-supers": "^7.4.4", "@babel/helper-split-export-declaration": "^7.4.4", "globals": "^11.1.0"}}, "@babel/plugin-transform-computed-properties": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-computed-properties/-/plugin-transform-computed-properties-7.2.0.tgz", "integrity": "sha512-kP/drqTxY6Xt3NNpKiMomfgkNn4o7+vKxK2DDKcBG9sHj51vHqMBGy8wbDS/J4lMxnqs153/T3+DmCEAkC5cpA==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-transform-destructuring": {"version": "7.4.4", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-destructuring/-/plugin-transform-destructuring-7.4.4.tgz", "integrity": "sha512-/aOx+nW0w8eHiEHm+BTERB2oJn5D127iye/SUQl7NjHy0lf+j7h4MKMMSOwdazGq9OxgiNADncE+SRJkCxjZpQ==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-transform-dotall-regex": {"version": "7.4.4", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.4.4.tgz", "integrity": "sha512-P05YEhRc2h53lZDjRPk/OektxCVevFzZs2Gfjd545Wde3k+yFDbXORgl2e0xpbq8mLcKJ7Idss4fAg0zORN/zg==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-regex": "^7.4.4", "regexpu-core": "^4.5.4"}}, "@babel/plugin-transform-duplicate-keys": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.2.0.tgz", "integrity": "sha512-q+yuxW4DsTjNceUiTzK0L+AfQ0zD9rWaTLiUqHA8p0gxx7lu1EylenfzjeIWNkPy6e/0VG/Wjw9uf9LueQwLOw==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-transform-exponentiation-operator": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.2.0.tgz", "integrity": "sha512-umh4hR6N7mu4Elq9GG8TOu9M0bakvlsREEC+ialrQN6ABS4oDQ69qJv1VtR3uxlKMCQMCvzk7vr17RHKcjx68A==", "dev": true, "requires": {"@babel/helper-builder-binary-assignment-operator-visitor": "^7.1.0", "@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-transform-for-of": {"version": "7.4.4", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.4.4.tgz", "integrity": "sha512-9T/5Dlr14Z9TIEXLXkt8T1DU7F24cbhwhMNUziN3hB1AXoZcdzPcTiKGRn/6iOymDqtTKWnr/BtRKN9JwbKtdQ==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-transform-function-name": {"version": "7.4.4", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.4.4.tgz", "integrity": "sha512-iU9pv7U+2jC9ANQkKeNF6DrPy4GBa4NWQtl6dHB4Pb3izX2JOEvDTFarlNsBj/63ZEzNNIAMs3Qw4fNCcSOXJA==", "dev": true, "requires": {"@babel/helper-function-name": "^7.1.0", "@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-transform-literals": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.2.0.tgz", "integrity": "sha512-2ThDhm4lI4oV7fVQ6pNNK+sx+c/GM5/SaML0w/r4ZB7sAneD/piDJtwdKlNckXeyGK7wlwg2E2w33C/Hh+VFCg==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-transform-member-expression-literals": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.2.0.tgz", "integrity": "sha512-HiU3zKkSU6scTidmnFJ0bMX8hz5ixC93b4MHMiYebmk2lUVNGOboPsqQvx5LzooihijUoLR/v7Nc1rbBtnc7FA==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-transform-modules-amd": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.2.0.tgz", "integrity": "sha512-mK2A8ucqz1qhrdqjS9VMIDfIvvT2thrEsIQzbaTdc5QFzhDjQv2CkJJ5f6BXIkgbmaoax3zBr2RyvV/8zeoUZw==", "dev": true, "requires": {"@babel/helper-module-transforms": "^7.1.0", "@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-transform-modules-commonjs": {"version": "7.4.4", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-modules-commonjs/-/plugin-transform-modules-commonjs-7.4.4.tgz", "integrity": "sha512-4sfBOJt58sEo9a2BQXnZq+Q3ZTSAUXyK3E30o36BOGnJ+tvJ6YSxF0PG6kERvbeISgProodWuI9UVG3/FMY6iw==", "dev": true, "requires": {"@babel/helper-module-transforms": "^7.4.4", "@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-simple-access": "^7.1.0"}}, "@babel/plugin-transform-modules-systemjs": {"version": "7.4.4", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.4.4.tgz", "integrity": "sha512-MSiModfILQc3/oqnG7NrP1jHaSPryO6tA2kOMmAQApz5dayPxWiHqmq4sWH2xF5LcQK56LlbKByCd8Aah/OIkQ==", "dev": true, "requires": {"@babel/helper-hoist-variables": "^7.4.4", "@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-transform-modules-umd": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.2.0.tgz", "integrity": "sha512-BV3bw6MyUH1iIsGhXlOK6sXhmSarZjtJ/vMiD9dNmpY8QXFFQTj+6v92pcfy1iqa8DeAfJFwoxcrS/TUZda6sw==", "dev": true, "requires": {"@babel/helper-module-transforms": "^7.1.0", "@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-transform-named-capturing-groups-regex": {"version": "7.4.5", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.4.5.tgz", "integrity": "sha512-z7+2IsWafTBbjNsOxU/Iv5CvTJlr5w4+HGu1HovKYTtgJ362f7kBcQglkfmlspKKZ3bgrbSGvLfNx++ZJgCWsg==", "dev": true, "requires": {"regexp-tree": "^0.1.6"}}, "@babel/plugin-transform-new-target": {"version": "7.4.4", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.4.4.tgz", "integrity": "sha512-r1z3T2DNGQwwe2vPGZMBNjioT2scgWzK9BCnDEh+46z8EEwXBq24uRzd65I7pjtugzPSj921aM15RpESgzsSuA==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-transform-object-super": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.2.0.tgz", "integrity": "sha512-VMyhPYZISFZAqAPVkiYb7dUe2AsVi2/wCT5+wZdsNO31FojQJa9ns40hzZ6U9f50Jlq4w6qwzdBB2uwqZ00ebg==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-replace-supers": "^7.1.0"}}, "@babel/plugin-transform-parameters": {"version": "7.4.4", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.4.4.tgz", "integrity": "sha512-oMh5DUO1V63nZcu/ZVLQFqiihBGo4OpxJxR1otF50GMeCLiRx5nUdtokd+u9SuVJrvvuIh9OosRFPP4pIPnwmw==", "dev": true, "requires": {"@babel/helper-call-delegate": "^7.4.4", "@babel/helper-get-function-arity": "^7.0.0", "@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-transform-property-literals": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.2.0.tgz", "integrity": "sha512-9q7Dbk4RhgcLp8ebduOpCbtjh7C0itoLYHXd9ueASKAG/is5PQtMR5VJGka9NKqGhYEGn5ITahd4h9QeBMylWQ==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-transform-react-display-name": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-react-display-name/-/plugin-transform-react-display-name-7.2.0.tgz", "integrity": "sha512-Htf/tPa5haZvRMiNSQSFifK12gtr/8vwfr+A9y69uF0QcU77AVu4K7MiHEkTxF7lQoHOL0F9ErqgfNEAKgXj7A==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-transform-react-jsx": {"version": "7.3.0", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.3.0.tgz", "integrity": "sha512-a/+aRb7R06WcKvQLOu4/TpjKOdvVEKRLWFpKcNuHhiREPgGRB4TQJxq07+EZLS8LFVYpfq1a5lDUnuMdcCpBKg==", "dev": true, "requires": {"@babel/helper-builder-react-jsx": "^7.3.0", "@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-jsx": "^7.2.0"}}, "@babel/plugin-transform-react-jsx-self": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.2.0.tgz", "integrity": "sha512-v6S5L/myicZEy+jr6ielB0OR8h+EH/1QFx/YJ7c7Ua+7lqsjj/vW6fD5FR9hB/6y7mGbfT4vAURn3xqBxsUcdg==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-jsx": "^7.2.0"}}, "@babel/plugin-transform-react-jsx-source": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.2.0.tgz", "integrity": "sha512-A32OkKTp4i5U6aE88GwwcuV4HAprUgHcTq0sSafLxjr6AW0QahrCRCjxogkbbcdtpbXkuTOlgpjophCxb6sh5g==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-jsx": "^7.2.0"}}, "@babel/plugin-transform-regenerator": {"version": "7.4.5", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.4.5.tgz", "integrity": "sha512-gBKRh5qAaCWntnd09S8QC7r3auLCqq5DI6O0DlfoyDjslSBVqBibrMdsqO+Uhmx3+BlOmE/Kw1HFxmGbv0N9dA==", "dev": true, "requires": {"regenerator-transform": "^0.14.0"}}, "@babel/plugin-transform-reserved-words": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.2.0.tgz", "integrity": "sha512-fz43fqW8E1tAB3DKF19/vxbpib1fuyCwSPE418ge5ZxILnBhWyhtPgz8eh1RCGGJlwvksHkyxMxh0eenFi+kFw==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-transform-runtime": {"version": "7.4.4", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-runtime/-/plugin-transform-runtime-7.4.4.tgz", "integrity": "sha512-aMVojEjPszvau3NRg+TIH14ynZLvPewH4xhlCW1w6A3rkxTS1m4uwzRclYR9oS+rl/dr+kT+pzbfHuAWP/lc7Q==", "dev": true, "requires": {"@babel/helper-module-imports": "^7.0.0", "@babel/helper-plugin-utils": "^7.0.0", "resolve": "^1.8.1", "semver": "^5.5.1"}}, "@babel/plugin-transform-shorthand-properties": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.2.0.tgz", "integrity": "sha512-QP4eUM83ha9zmYtpbnyjTLAGKQritA5XW/iG9cjtuOI8s1RuL/3V6a3DeSHfKutJQ+ayUfeZJPcnCYEQzaPQqg==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-transform-spread": {"version": "7.2.2", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.2.2.tgz", "integrity": "sha512-KWfky/58vubwtS0hLqEnrWJjsMGaOeSBn90Ezn5Jeg9Z8KKHmELbP1yGylMlm5N6TPKeY9A2+UaSYLdxahg01w==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-transform-sticky-regex": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.2.0.tgz", "integrity": "sha512-KKYCoGaRAf+ckH8gEL3JHUaFVyNHKe3ASNsZ+AlktgHevvxGigoIttrEJb8iKN03Q7Eazlv1s6cx2B2cQ3Jabw==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-regex": "^7.0.0"}}, "@babel/plugin-transform-template-literals": {"version": "7.4.4", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-template-literals/-/plugin-transform-template-literals-7.4.4.tgz", "integrity": "sha512-mQrEC4TWkhLN0z8ygIvEL9ZEToPhG5K7KDW3pzGqOfIGZ28Jb0POUkeWcoz8HnHvhFy6dwAT1j8OzqN8s804+g==", "dev": true, "requires": {"@babel/helper-annotate-as-pure": "^7.0.0", "@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-transform-typeof-symbol": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.2.0.tgz", "integrity": "sha512-2LNhETWYxiYysBtrBTqL8+La0jIoQQnIScUJc74OYvUGRmkskNY4EzLCnjHBzdmb38wqtTaixpo1NctEcvMDZw==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-transform-unicode-regex": {"version": "7.4.4", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.4.4.tgz", "integrity": "sha512-il+/XdNw01i93+M9J9u4T7/e/Ue/vWfNZE4IRUQjplu2Mqb/AFTDimkw2tdEdSH50wuQXZAbXSql0UphQke+vA==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-regex": "^7.4.4", "regexpu-core": "^4.5.4"}}, "@babel/polyfill": {"version": "7.4.4", "resolved": "https://registry.npmjs.org/@babel/polyfill/-/polyfill-7.4.4.tgz", "integrity": "sha512-WlthFLfhQQhh+A2Gn5NSFl0Huxz36x86Jn+E9OW7ibK8edKPq+KLy4apM1yDpQ8kJOVi1OVjpP4vSDLdrI04dg==", "dev": true, "requires": {"core-js": "^2.6.5", "regenerator-runtime": "^0.13.2"}}, "@babel/preset-env": {"version": "7.4.5", "resolved": "https://registry.npmjs.org/@babel/preset-env/-/preset-env-7.4.5.tgz", "integrity": "sha512-f2yNVXM+FsR5V8UwcFeIHzHWgnhXg3NpRmy0ADvALpnhB0SLbCvrCRr4BLOUYbQNLS+Z0Yer46x9dJXpXewI7w==", "dev": true, "requires": {"@babel/helper-module-imports": "^7.0.0", "@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-proposal-async-generator-functions": "^7.2.0", "@babel/plugin-proposal-json-strings": "^7.2.0", "@babel/plugin-proposal-object-rest-spread": "^7.4.4", "@babel/plugin-proposal-optional-catch-binding": "^7.2.0", "@babel/plugin-proposal-unicode-property-regex": "^7.4.4", "@babel/plugin-syntax-async-generators": "^7.2.0", "@babel/plugin-syntax-json-strings": "^7.2.0", "@babel/plugin-syntax-object-rest-spread": "^7.2.0", "@babel/plugin-syntax-optional-catch-binding": "^7.2.0", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-transform-async-to-generator": "^7.4.4", "@babel/plugin-transform-block-scoped-functions": "^7.2.0", "@babel/plugin-transform-block-scoping": "^7.4.4", "@babel/plugin-transform-classes": "^7.4.4", "@babel/plugin-transform-computed-properties": "^7.2.0", "@babel/plugin-transform-destructuring": "^7.4.4", "@babel/plugin-transform-dotall-regex": "^7.4.4", "@babel/plugin-transform-duplicate-keys": "^7.2.0", "@babel/plugin-transform-exponentiation-operator": "^7.2.0", "@babel/plugin-transform-for-of": "^7.4.4", "@babel/plugin-transform-function-name": "^7.4.4", "@babel/plugin-transform-literals": "^7.2.0", "@babel/plugin-transform-member-expression-literals": "^7.2.0", "@babel/plugin-transform-modules-amd": "^7.2.0", "@babel/plugin-transform-modules-commonjs": "^7.4.4", "@babel/plugin-transform-modules-systemjs": "^7.4.4", "@babel/plugin-transform-modules-umd": "^7.2.0", "@babel/plugin-transform-named-capturing-groups-regex": "^7.4.5", "@babel/plugin-transform-new-target": "^7.4.4", "@babel/plugin-transform-object-super": "^7.2.0", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-property-literals": "^7.2.0", "@babel/plugin-transform-regenerator": "^7.4.5", "@babel/plugin-transform-reserved-words": "^7.2.0", "@babel/plugin-transform-shorthand-properties": "^7.2.0", "@babel/plugin-transform-spread": "^7.2.0", "@babel/plugin-transform-sticky-regex": "^7.2.0", "@babel/plugin-transform-template-literals": "^7.4.4", "@babel/plugin-transform-typeof-symbol": "^7.2.0", "@babel/plugin-transform-unicode-regex": "^7.4.4", "@babel/types": "^7.4.4", "browserslist": "^4.6.0", "core-js-compat": "^3.1.1", "invariant": "^2.2.2", "js-levenshtein": "^1.1.3", "semver": "^5.5.0"}}, "@babel/preset-react": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.0.0.tgz", "integrity": "sha512-oayxyPS4Zj+hF6Et11BwuBkmpgT/zMxyuZgFrMeZID6Hdh3dGlk4sHCAhdBCpuCKW2ppBfl2uCCetlrUIJRY3w==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-transform-react-display-name": "^7.0.0", "@babel/plugin-transform-react-jsx": "^7.0.0", "@babel/plugin-transform-react-jsx-self": "^7.0.0", "@babel/plugin-transform-react-jsx-source": "^7.0.0"}}, "@babel/runtime": {"version": "7.4.5", "resolved": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.4.5.tgz", "integrity": "sha512-TuI4qpWZP6lGOGIuGWtp9sPluqYICmbk8T/1vpSysqJxRPkudh/ofFWyqdcMsDf2s7KvDL4/YHgKyvcS3g9CJQ==", "requires": {"regenerator-runtime": "^0.13.2"}}, "@babel/template": {"version": "7.4.4", "resolved": "https://registry.npmjs.org/@babel/template/-/template-7.4.4.tgz", "integrity": "sha512-CiGzLN9KgAvgZsnivND7rkA+AeJ9JB0ciPOD4U59GKbQP2iQl+olF1l76kJOupqidozfZ32ghwBEJDhnk9MEcw==", "dev": true, "requires": {"@babel/code-frame": "^7.0.0", "@babel/parser": "^7.4.4", "@babel/types": "^7.4.4"}}, "@babel/traverse": {"version": "7.4.5", "resolved": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.4.5.tgz", "integrity": "sha512-Vc+qjynwkjRmIFGxy0KYoPj4FdVDxLej89kMHFsWScq999uX+pwcX4v9mWRjW0KcAYTPAuVQl2LKP1wEVLsp+A==", "dev": true, "requires": {"@babel/code-frame": "^7.0.0", "@babel/generator": "^7.4.4", "@babel/helper-function-name": "^7.1.0", "@babel/helper-split-export-declaration": "^7.4.4", "@babel/parser": "^7.4.5", "@babel/types": "^7.4.4", "debug": "^4.1.0", "globals": "^11.1.0", "lodash": "^4.17.11"}}, "@babel/types": {"version": "7.4.4", "resolved": "https://registry.npmjs.org/@babel/types/-/types-7.4.4.tgz", "integrity": "sha512-dOllgYdnEFOebhkKCjzSVFqw/PmmB8pH6RGOWkY4GsboQNd47b1fBThBSwlHAq9alF9vc1M3+6oqR47R50L0tQ==", "requires": {"esutils": "^2.0.2", "lodash": "^4.17.11", "to-fast-properties": "^2.0.0"}}, "@emotion/cache": {"version": "10.0.15", "resolved": "https://registry.npmjs.org/@emotion/cache/-/cache-10.0.15.tgz", "integrity": "sha512-8VthgeKhlGeTXSW1JN7I14AnAaiFPbOrqNqg3dPoGCZ3bnMjkrmRU0zrx0BtBw9esBaPaQgDB9y0tVgAGT2Mrg==", "requires": {"@emotion/sheet": "0.9.3", "@emotion/stylis": "0.8.4", "@emotion/utils": "0.11.2", "@emotion/weak-memoize": "0.2.3"}, "dependencies": {"@emotion/stylis": {"version": "0.8.4", "resolved": "https://registry.npmjs.org/@emotion/stylis/-/stylis-0.8.4.tgz", "integrity": "sha512-TLmkCVm8f8gH0oLv+HWKiu7e8xmBIaokhxcEKPh1m8pXiV/akCiq50FvYgOwY42rjejck8nsdQxZlXZ7pmyBUQ=="}}}, "@emotion/core": {"version": "10.0.15", "resolved": "https://registry.npmjs.org/@emotion/core/-/core-10.0.15.tgz", "integrity": "sha512-VHwwl3k/ddMfQOHYgOJryXOs2rGJ5AfKLQGm5AVolNonnr6tkmDI4nzIMNaPpveoXVs7sP0OrF24UunIPxveQw==", "requires": {"@babel/runtime": "^7.4.3", "@emotion/cache": "^10.0.15", "@emotion/css": "^10.0.14", "@emotion/serialize": "^0.11.9", "@emotion/sheet": "0.9.3", "@emotion/utils": "0.11.2"}}, "@emotion/css": {"version": "10.0.14", "resolved": "https://registry.npmjs.org/@emotion/css/-/css-10.0.14.tgz", "integrity": "sha512-MozgPkBEWvorcdpqHZE5x1D/PLEHUitALQCQYt2wayf4UNhpgQs2tN0UwHYS4FMy5ROBH+0ALyCFVYJ/ywmwlg==", "requires": {"@emotion/serialize": "^0.11.8", "@emotion/utils": "0.11.2", "babel-plugin-emotion": "^10.0.14"}}, "@emotion/hash": {"version": "0.6.6", "resolved": "https://registry.npmjs.org/@emotion/hash/-/hash-0.6.6.tgz", "integrity": "sha512-ojhgxzUHZ7am3D2jHkMzPpsBAiB005GF5YU4ea+8DNPybMk01JJUM9V9YRlF/GE95tcOm8DxQvWA2jq19bGalQ=="}, "@emotion/is-prop-valid": {"version": "0.6.8", "resolved": "https://registry.npmjs.org/@emotion/is-prop-valid/-/is-prop-valid-0.6.8.tgz", "integrity": "sha512-IMSL7ekYhmFlILXcouA6ket3vV7u9BqStlXzbKOF9HBtpUPMMlHU+bBxrLOa2NvleVwNIxeq/zL8LafLbeUXcA==", "requires": {"@emotion/memoize": "^0.6.6"}}, "@emotion/memoize": {"version": "0.6.6", "resolved": "https://registry.npmjs.org/@emotion/memoize/-/memoize-0.6.6.tgz", "integrity": "sha512-h4t4jFjtm1YV7UirAFuSuFGyLa+NNxjdkq6DpFLANNQY5rHueFZHVY+8Cu1HYVP6DrheB0kv4m5xPjo7eKT7yQ=="}, "@emotion/serialize": {"version": "0.11.9", "resolved": "https://registry.npmjs.org/@emotion/serialize/-/serialize-0.11.9.tgz", "integrity": "sha512-/Cn4V81z3ZyFiDQRw8nhGFaHkxHtmCSSBUit4vgTuLA1BqxfJUYiqSq97tq/vV8z9LfIoqs6a9v6QrUFWZpK7A==", "requires": {"@emotion/hash": "0.7.2", "@emotion/memoize": "0.7.2", "@emotion/unitless": "0.7.4", "@emotion/utils": "0.11.2", "csstype": "^2.5.7"}, "dependencies": {"@emotion/hash": {"version": "0.7.2", "resolved": "https://registry.npmjs.org/@emotion/hash/-/hash-0.7.2.tgz", "integrity": "sha512-RMtr1i6E8MXaBWwhXL3yeOU8JXRnz8GNxHvaUfVvwxokvayUY0zoBeWbKw1S9XkufmGEEdQd228pSZXFkAln8Q=="}, "@emotion/memoize": {"version": "0.7.2", "resolved": "https://registry.npmjs.org/@emotion/memoize/-/memoize-0.7.2.tgz", "integrity": "sha512-hnHhwQzvPCW1QjBWFyBtsETdllOM92BfrKWbUTmh9aeOlcVOiXvlPsK4104xH8NsaKfg86PTFsWkueQeUfMA/w=="}, "@emotion/unitless": {"version": "0.7.4", "resolved": "https://registry.npmjs.org/@emotion/unitless/-/unitless-0.7.4.tgz", "integrity": "sha512-kBa+cDHOR9jpRJ+kcGMsysrls0leukrm68DmFQoMIWQcXdr2cZvyvypWuGYT7U+9kAExUE7+T7r6G3C3A6L8MQ=="}}}, "@emotion/sheet": {"version": "0.9.3", "resolved": "https://registry.npmjs.org/@emotion/sheet/-/sheet-0.9.3.tgz", "integrity": "sha512-c3Q6V7Df7jfwSq5AzQWbXHa5soeE4F5cbqi40xn0CzXxWW9/6Mxq48WJEtqfWzbZtW9odZdnRAkwCQwN12ob4A=="}, "@emotion/stylis": {"version": "0.7.1", "resolved": "https://registry.npmjs.org/@emotion/stylis/-/stylis-0.7.1.tgz", "integrity": "sha512-/SLmSIkN13M//53TtNxgxo57mcJk/UJIDFRKwOiLIBEyBHEcipgR6hNMQ/59Sl4VjCJ0Z/3zeAZyvnSLPG/1HQ=="}, "@emotion/unitless": {"version": "0.6.7", "resolved": "https://registry.npmjs.org/@emotion/unitless/-/unitless-0.6.7.tgz", "integrity": "sha512-Arj1hncvEVqQ2p7Ega08uHLr1JuRYBuO5cIvcA+WWEQ5+VmkOE3ZXzl04NbQxeQpWX78G7u6MqxKuNX3wvYZxg=="}, "@emotion/utils": {"version": "0.11.2", "resolved": "https://registry.npmjs.org/@emotion/utils/-/utils-0.11.2.tgz", "integrity": "sha512-UHX2XklLl3sIaP6oiMmlVzT0J+2ATTVpf0dHQVyPJHTkOITvXfaSqnRk6mdDhV9pR8T/tHc3cex78IKXssmzrA=="}, "@emotion/weak-memoize": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/@emotion/weak-memoize/-/weak-memoize-0.2.3.tgz", "integrity": "sha512-zVgvPwGK7c1aVdUVc9Qv7SqepOGRDrqCw7KZPSZziWGxSlbII3gmvGLPzLX4d0n0BMbamBacUrN22zOMyFFEkQ=="}, "@livechat/data-utils": {"version": "0.2.7", "resolved": "https://registry.npmjs.org/@livechat/data-utils/-/data-utils-0.2.7.tgz", "integrity": "sha512-gF+5qQTy3UMn+0goU32qgbwAffTMQQIii2GZL3x1BzC0VmBK2SLVbYcdciiIsXvcdwnXLwLmxG+/HgM6eDr/1Q=="}, "@livechat/ui-kit": {"version": "0.4.0-5", "resolved": "https://registry.npmjs.org/@livechat/ui-kit/-/ui-kit-0.4.0-5.tgz", "integrity": "sha512-N70HLVxDNLDpwK1llpCa5a/xQg1SJp4LQqvQjM37JMcISbjBzpcyg/iI6TcX2jXEorGx4EYZjXTCwWRg2ZfsBw==", "requires": {"@babel/runtime": "^7.2.0", "@emotion/is-prop-valid": "^0.6.8", "@livechat/data-utils": "^0.2.7", "@popmotion/easing": "^1.0.1", "callbag-duration-progress": "^1.0.0", "callbag-for-each": "^1.0.1", "callbag-map": "^1.0.1", "callbag-pipe": "^1.1.1", "create-emotion": "^9.2.6", "create-emotion-styled": "^9.2.6", "emotion-theming": "^9.2.6", "polished": "1.9.0", "prop-types": "^15.5.8", "react-textarea-autosize": "^7.1.0", "react-toggled": "^1.2.7", "react-transition-group": "^4.0.1", "recompose": "^0.23.4", "resize-observer-polyfill": "^1.5.0", "stylis-custom-properties": "0.0.3"}}, "@popmotion/easing": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/@popmotion/easing/-/easing-1.0.2.tgz", "integrity": "sha512-IkdW0TNmRnWTeWI7aGQIVDbKXPWHVEYdGgd5ZR4SH/Ty/61p63jCjrPxX1XrR7IGkl08bjhJROStD7j+RKgoIw=="}, "@webassemblyjs/ast": {"version": "1.8.5", "resolved": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.8.5.tgz", "integrity": "sha512-aJMfngIZ65+t71C3y2nBBg5FFG0Okt9m0XEgWZ7Ywgn1oMAT8cNwx00Uv1cQyHtidq0Xn94R4TAywO+LCQ+ZAQ==", "dev": true, "requires": {"@webassemblyjs/helper-module-context": "1.8.5", "@webassemblyjs/helper-wasm-bytecode": "1.8.5", "@webassemblyjs/wast-parser": "1.8.5"}}, "@webassemblyjs/floating-point-hex-parser": {"version": "1.8.5", "resolved": "https://registry.npmjs.org/@webassemblyjs/floating-point-hex-parser/-/floating-point-hex-parser-1.8.5.tgz", "integrity": "sha512-9p+79WHru1oqBh9ewP9zW95E3XAo+90oth7S5Re3eQnECGq59ly1Ri5tsIipKGpiStHsUYmY3zMLqtk3gTcOtQ==", "dev": true}, "@webassemblyjs/helper-api-error": {"version": "1.8.5", "resolved": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.8.5.tgz", "integrity": "sha512-Za/tnzsvnqdaSPOUXHyKJ2XI7PDX64kWtURyGiJJZKVEdFOsdKUCPTNEVFZq3zJ2R0G5wc2PZ5gvdTRFgm81zA==", "dev": true}, "@webassemblyjs/helper-buffer": {"version": "1.8.5", "resolved": "https://registry.npmjs.org/@webassemblyjs/helper-buffer/-/helper-buffer-1.8.5.tgz", "integrity": "sha512-Ri2R8nOS0U6G49Q86goFIPNgjyl6+oE1abW1pS84BuhP1Qcr5JqMwRFT3Ah3ADDDYGEgGs1iyb1DGX+kAi/c/Q==", "dev": true}, "@webassemblyjs/helper-code-frame": {"version": "1.8.5", "resolved": "https://registry.npmjs.org/@webassemblyjs/helper-code-frame/-/helper-code-frame-1.8.5.tgz", "integrity": "sha512-VQAadSubZIhNpH46IR3yWO4kZZjMxN1opDrzePLdVKAZ+DFjkGD/rf4v1jap744uPVU6yjL/smZbRIIJTOUnKQ==", "dev": true, "requires": {"@webassemblyjs/wast-printer": "1.8.5"}}, "@webassemblyjs/helper-fsm": {"version": "1.8.5", "resolved": "https://registry.npmjs.org/@webassemblyjs/helper-fsm/-/helper-fsm-1.8.5.tgz", "integrity": "sha512-kRuX/saORcg8se/ft6Q2UbRpZwP4y7YrWsLXPbbmtepKr22i8Z4O3V5QE9DbZK908dh5Xya4Un57SDIKwB9eow==", "dev": true}, "@webassemblyjs/helper-module-context": {"version": "1.8.5", "resolved": "https://registry.npmjs.org/@webassemblyjs/helper-module-context/-/helper-module-context-1.8.5.tgz", "integrity": "sha512-/O1B236mN7UNEU4t9X7Pj38i4VoU8CcMHyy3l2cV/kIF4U5KoHXDVqcDuOs1ltkac90IM4vZdHc52t1x8Yfs3g==", "dev": true, "requires": {"@webassemblyjs/ast": "1.8.5", "mamacro": "^0.0.3"}}, "@webassemblyjs/helper-wasm-bytecode": {"version": "1.8.5", "resolved": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-bytecode/-/helper-wasm-bytecode-1.8.5.tgz", "integrity": "sha512-Cu4YMYG3Ddl72CbmpjU/wbP6SACcOPVbHN1dI4VJNJVgFwaKf1ppeFJrwydOG3NDHxVGuCfPlLZNyEdIYlQ6QQ==", "dev": true}, "@webassemblyjs/helper-wasm-section": {"version": "1.8.5", "resolved": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.8.5.tgz", "integrity": "sha512-VV083zwR+VTrIWWtgIUpqfvVdK4ff38loRmrdDBgBT8ADXYsEZ5mPQ4Nde90N3UYatHdYoDIFb7oHzMncI02tA==", "dev": true, "requires": {"@webassemblyjs/ast": "1.8.5", "@webassemblyjs/helper-buffer": "1.8.5", "@webassemblyjs/helper-wasm-bytecode": "1.8.5", "@webassemblyjs/wasm-gen": "1.8.5"}}, "@webassemblyjs/ieee754": {"version": "1.8.5", "resolved": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.8.5.tgz", "integrity": "sha512-aaCvQYrvKbY/n6wKHb/ylAJr27GglahUO89CcGXMItrOBqRarUMxWLJgxm9PJNuKULwN5n1csT9bYoMeZOGF3g==", "dev": true, "requires": {"@xtuc/ieee754": "^1.2.0"}}, "@webassemblyjs/leb128": {"version": "1.8.5", "resolved": "https://registry.npmjs.org/@webassemblyjs/leb128/-/leb128-1.8.5.tgz", "integrity": "sha512-plYUuUwleLIziknvlP8VpTgO4kqNaH57Y3JnNa6DLpu/sGcP6hbVdfdX5aHAV716pQBKrfuU26BJK29qY37J7A==", "dev": true, "requires": {"@xtuc/long": "4.2.2"}}, "@webassemblyjs/utf8": {"version": "1.8.5", "resolved": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.8.5.tgz", "integrity": "sha512-U7zgftmQriw37tfD934UNInokz6yTmn29inT2cAetAsaU9YeVCveWEwhKL1Mg4yS7q//NGdzy79nlXh3bT8Kjw==", "dev": true}, "@webassemblyjs/wasm-edit": {"version": "1.8.5", "resolved": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.8.5.tgz", "integrity": "sha512-A41EMy8MWw5yvqj7MQzkDjU29K7UJq1VrX2vWLzfpRHt3ISftOXqrtojn7nlPsZ9Ijhp5NwuODuycSvfAO/26Q==", "dev": true, "requires": {"@webassemblyjs/ast": "1.8.5", "@webassemblyjs/helper-buffer": "1.8.5", "@webassemblyjs/helper-wasm-bytecode": "1.8.5", "@webassemblyjs/helper-wasm-section": "1.8.5", "@webassemblyjs/wasm-gen": "1.8.5", "@webassemblyjs/wasm-opt": "1.8.5", "@webassemblyjs/wasm-parser": "1.8.5", "@webassemblyjs/wast-printer": "1.8.5"}}, "@webassemblyjs/wasm-gen": {"version": "1.8.5", "resolved": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.8.5.tgz", "integrity": "sha512-BCZBT0LURC0CXDzj5FXSc2FPTsxwp3nWcqXQdOZE4U7h7i8FqtFK5Egia6f9raQLpEKT1VL7zr4r3+QX6zArWg==", "dev": true, "requires": {"@webassemblyjs/ast": "1.8.5", "@webassemblyjs/helper-wasm-bytecode": "1.8.5", "@webassemblyjs/ieee754": "1.8.5", "@webassemblyjs/leb128": "1.8.5", "@webassemblyjs/utf8": "1.8.5"}}, "@webassemblyjs/wasm-opt": {"version": "1.8.5", "resolved": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.8.5.tgz", "integrity": "sha512-HKo2mO/Uh9A6ojzu7cjslGaHaUU14LdLbGEKqTR7PBKwT6LdPtLLh9fPY33rmr5wcOMrsWDbbdCHq4hQUdd37Q==", "dev": true, "requires": {"@webassemblyjs/ast": "1.8.5", "@webassemblyjs/helper-buffer": "1.8.5", "@webassemblyjs/wasm-gen": "1.8.5", "@webassemblyjs/wasm-parser": "1.8.5"}}, "@webassemblyjs/wasm-parser": {"version": "1.8.5", "resolved": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.8.5.tgz", "integrity": "sha512-pi0SYE9T6tfcMkthwcgCpL0cM9nRYr6/6fjgDtL6q/ZqKHdMWvxitRi5JcZ7RI4SNJJYnYNaWy5UUrHQy998lw==", "dev": true, "requires": {"@webassemblyjs/ast": "1.8.5", "@webassemblyjs/helper-api-error": "1.8.5", "@webassemblyjs/helper-wasm-bytecode": "1.8.5", "@webassemblyjs/ieee754": "1.8.5", "@webassemblyjs/leb128": "1.8.5", "@webassemblyjs/utf8": "1.8.5"}}, "@webassemblyjs/wast-parser": {"version": "1.8.5", "resolved": "https://registry.npmjs.org/@webassemblyjs/wast-parser/-/wast-parser-1.8.5.tgz", "integrity": "sha512-daXC1FyKWHF1i11obK086QRlsMsY4+tIOKgBqI1lxAnkp9xe9YMcgOxm9kLe+ttjs5aWV2KKE1TWJCN57/Btsg==", "dev": true, "requires": {"@webassemblyjs/ast": "1.8.5", "@webassemblyjs/floating-point-hex-parser": "1.8.5", "@webassemblyjs/helper-api-error": "1.8.5", "@webassemblyjs/helper-code-frame": "1.8.5", "@webassemblyjs/helper-fsm": "1.8.5", "@xtuc/long": "4.2.2"}}, "@webassemblyjs/wast-printer": {"version": "1.8.5", "resolved": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.8.5.tgz", "integrity": "sha512-w0U0pD4EhlnvRyeJzBqaVSJAo9w/ce7/WPogeXLzGkO6hzhr4GnQIZ4W4uUt5b9ooAaXPtnXlj0gzsXEOUNYMg==", "dev": true, "requires": {"@webassemblyjs/ast": "1.8.5", "@webassemblyjs/wast-parser": "1.8.5", "@xtuc/long": "4.2.2"}}, "@xtuc/ieee754": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/@xtuc/ieee754/-/ieee754-1.2.0.tgz", "integrity": "sha512-DX8nKgqcGwsc0eJSqYt5lwP4DH5FlHnmuWWBRy7X0NcaGR0ZtuyeESgMwTYVEtxmsNGY+qit4QYT/MIYTOTPeA==", "dev": true}, "@xtuc/long": {"version": "4.2.2", "resolved": "https://registry.npmjs.org/@xtuc/long/-/long-4.2.2.tgz", "integrity": "sha512-NuHqBY1PB/D8xU6s/thBgOAiAP7HOYDQ32+BFZILJ8ivkUkAHQnWfn6WhL79Owj1qmUnoN/YPhktdIoucipkAQ==", "dev": true}, "acorn": {"version": "6.1.1", "resolved": "https://registry.npmjs.org/acorn/-/acorn-6.1.1.tgz", "integrity": "sha512-jPTiwtOxaHNaAPg/dmrJ/beuzLRnXtB0kQPQ8JpotKJgTB6rX6c8mlf315941pyjBSaPg8NHXS9fhP4u17DpGA==", "dev": true}, "acorn-dynamic-import": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/acorn-dynamic-import/-/acorn-dynamic-import-4.0.0.tgz", "integrity": "sha512-d3OEjQV4ROpoflsnUA8HozoIR504TFxNivYEUi6uwz0IYhBkTDXGuWlNdMtybRt3nqVx/L6XqMt0FxkXuWKZhw==", "dev": true}, "ajv": {"version": "6.10.0", "resolved": "https://registry.npmjs.org/ajv/-/ajv-6.10.0.tgz", "integrity": "sha512-nffhOpkymDECQyR0mnsUtoCE8RlX38G0rYP+wgLWFyZuUyuuojSSvi/+euOiQBIn63whYwYVIIH1TvE3tu4OEg==", "dev": true, "requires": {"fast-deep-equal": "^2.0.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}}, "ajv-errors": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/ajv-errors/-/ajv-errors-1.0.1.tgz", "integrity": "sha512-DCRfO/4nQ+89p/RK43i8Ezd41EqdGIU4ld7nGF8OQ14oc/we5rEntLCUa7+jrn3nn83BosfwZA0wb4pon2o8iQ==", "dev": true}, "ajv-keywords": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-3.4.0.tgz", "integrity": "sha512-aUjdRFISbuFOl0EIZc+9e4FfZp0bDZgAdOOf30bJmw8VM9v84SHyVyxDfbWxpGYbdZD/9XoKxfHVNmxPkhwyGw==", "dev": true}, "align-text": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/align-text/-/align-text-0.1.4.tgz", "integrity": "sha1-DNkKVhCT810KmSVsIrcGlDP60Rc=", "requires": {"kind-of": "^3.0.2", "longest": "^1.0.1", "repeat-string": "^1.5.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}}}, "ansi-bgblack": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/ansi-bgblack/-/ansi-bgblack-0.1.1.tgz", "integrity": "sha1-poulAHiHcBtqr74/oNrf36juPKI=", "requires": {"ansi-wrap": "0.1.0"}}, "ansi-bgblue": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/ansi-bgblue/-/ansi-bgblue-0.1.1.tgz", "integrity": "sha1-Z73ATtybm1J4lp2hlt6j11yMNhM=", "requires": {"ansi-wrap": "0.1.0"}}, "ansi-bgcyan": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/ansi-bgcyan/-/ansi-bgcyan-0.1.1.tgz", "integrity": "sha1-WEiUJWAL3p9VBwaN2Wnr/bUP52g=", "requires": {"ansi-wrap": "0.1.0"}}, "ansi-bggreen": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/ansi-bggreen/-/ansi-bggreen-0.1.1.tgz", "integrity": "sha1-TjGRJIUplD9DIelr8THRwTgWr0k=", "requires": {"ansi-wrap": "0.1.0"}}, "ansi-bgmagenta": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/ansi-bgmagenta/-/ansi-bgmagenta-0.1.1.tgz", "integrity": "sha1-myhDLAduqpmUGGcqPvvhk5HCx6E=", "requires": {"ansi-wrap": "0.1.0"}}, "ansi-bgred": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/ansi-bgred/-/ansi-bgred-0.1.1.tgz", "integrity": "sha1-p2+Sg4OCukMpCmwXeEJPmE1vEEE=", "requires": {"ansi-wrap": "0.1.0"}}, "ansi-bgwhite": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/ansi-bgwhite/-/ansi-bgwhite-0.1.1.tgz", "integrity": "sha1-ZQRlE3elim7OzQMxmU5IAljhG6g=", "requires": {"ansi-wrap": "0.1.0"}}, "ansi-bgyellow": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/ansi-bgyellow/-/ansi-bgyellow-0.1.1.tgz", "integrity": "sha1-w/4usIzUdmSAKeaHTRWgs49h1E8=", "requires": {"ansi-wrap": "0.1.0"}}, "ansi-black": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/ansi-black/-/ansi-black-0.1.1.tgz", "integrity": "sha1-9hheiJNgslRaHsUMC/Bj/EMDJFM=", "requires": {"ansi-wrap": "0.1.0"}}, "ansi-blue": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/ansi-blue/-/ansi-blue-0.1.1.tgz", "integrity": "sha1-FbgEmQ6S/JyoxUds6PaZd3wh7b8=", "requires": {"ansi-wrap": "0.1.0"}}, "ansi-bold": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/ansi-bold/-/ansi-bold-0.1.1.tgz", "integrity": "sha1-PmOVCvWswq4uZw5vZ96xFdGl9QU=", "requires": {"ansi-wrap": "0.1.0"}}, "ansi-colors": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/ansi-colors/-/ansi-colors-0.2.0.tgz", "integrity": "sha1-csMd4qDZoszQysMMyYI+6y9kNLU=", "requires": {"ansi-bgblack": "^0.1.1", "ansi-bgblue": "^0.1.1", "ansi-bgcyan": "^0.1.1", "ansi-bggreen": "^0.1.1", "ansi-bgmagenta": "^0.1.1", "ansi-bgred": "^0.1.1", "ansi-bgwhite": "^0.1.1", "ansi-bgyellow": "^0.1.1", "ansi-black": "^0.1.1", "ansi-blue": "^0.1.1", "ansi-bold": "^0.1.1", "ansi-cyan": "^0.1.1", "ansi-dim": "^0.1.1", "ansi-gray": "^0.1.1", "ansi-green": "^0.1.1", "ansi-grey": "^0.1.1", "ansi-hidden": "^0.1.1", "ansi-inverse": "^0.1.1", "ansi-italic": "^0.1.1", "ansi-magenta": "^0.1.1", "ansi-red": "^0.1.1", "ansi-reset": "^0.1.1", "ansi-strikethrough": "^0.1.1", "ansi-underline": "^0.1.1", "ansi-white": "^0.1.1", "ansi-yellow": "^0.1.1", "lazy-cache": "^2.0.1"}}, "ansi-cyan": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/ansi-cyan/-/ansi-cyan-0.1.1.tgz", "integrity": "sha1-U4rlKK+JgvKK4w2G8vF0VtJgmHM=", "requires": {"ansi-wrap": "0.1.0"}}, "ansi-dim": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/ansi-dim/-/ansi-dim-0.1.1.tgz", "integrity": "sha1-QN5MYDqoCG2Oeoa4/5mNXDbu/Ww=", "requires": {"ansi-wrap": "0.1.0"}}, "ansi-escapes": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-1.4.0.tgz", "integrity": "sha1-06ioOzGapneTZisT52HHkRQiMG4="}, "ansi-gray": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/ansi-gray/-/ansi-gray-0.1.1.tgz", "integrity": "sha1-KWLPVOyXksSFEKPetSRDaGHvclE=", "requires": {"ansi-wrap": "0.1.0"}}, "ansi-green": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/ansi-green/-/ansi-green-0.1.1.tgz", "integrity": "sha1-il2al55FjVfEDjNYCzc5C44Q0Pc=", "requires": {"ansi-wrap": "0.1.0"}}, "ansi-grey": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/ansi-grey/-/ansi-grey-0.1.1.tgz", "integrity": "sha1-WdmLasK6GfilF5jphT+6eDOaM8E=", "requires": {"ansi-wrap": "0.1.0"}}, "ansi-hidden": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/ansi-hidden/-/ansi-hidden-0.1.1.tgz", "integrity": "sha1-7WpMSY0rt8uyidvyqNHcyFZ/rg8=", "requires": {"ansi-wrap": "0.1.0"}}, "ansi-inverse": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/ansi-inverse/-/ansi-inverse-0.1.1.tgz", "integrity": "sha1-tq9Fgm/oJr+1KKbHmIV5Q1XM0mk=", "requires": {"ansi-wrap": "0.1.0"}}, "ansi-italic": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/ansi-italic/-/ansi-italic-0.1.1.tgz", "integrity": "sha1-EEdDRj9iXBQqA2c5z4XtpoiYbyM=", "requires": {"ansi-wrap": "0.1.0"}}, "ansi-magenta": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/ansi-magenta/-/ansi-magenta-0.1.1.tgz", "integrity": "sha1-BjtboW+z8j4c/aKwfAqJ3hHkMK4=", "requires": {"ansi-wrap": "0.1.0"}}, "ansi-red": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/ansi-red/-/ansi-red-0.1.1.tgz", "integrity": "sha1-jGOPnRCAgAo1PJwoyKgcpHBdlGw=", "requires": {"ansi-wrap": "0.1.0"}}, "ansi-regex": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-4.1.0.tgz", "integrity": "sha512-1apePfXM1UOSqw0o9IiFAovVz9M5S1Dg+4TrDwfMewQ6p/rmMueb7tWZjQ1rx4Loy1ArBggoqGpfqqdI4rondg==", "dev": true}, "ansi-reset": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/ansi-reset/-/ansi-reset-0.1.1.tgz", "integrity": "sha1-5+cSksPH3c1NYu9KbHwFmAkRw7c=", "requires": {"ansi-wrap": "0.1.0"}}, "ansi-strikethrough": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/ansi-strikethrough/-/ansi-strikethrough-0.1.1.tgz", "integrity": "sha1-2Eh3FAss/wfRyT685pkE9oiF5Wg=", "requires": {"ansi-wrap": "0.1.0"}}, "ansi-styles": {"version": "3.2.1", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz", "integrity": "sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==", "dev": true, "requires": {"color-convert": "^1.9.0"}}, "ansi-underline": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/ansi-underline/-/ansi-underline-0.1.1.tgz", "integrity": "sha1-38kg9Ml7WXfqFi34/7mIMIqqcaQ=", "requires": {"ansi-wrap": "0.1.0"}}, "ansi-white": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/ansi-white/-/ansi-white-0.1.1.tgz", "integrity": "sha1-nHe3wZPF7pkuYBHTbsTJIbRXiUQ=", "requires": {"ansi-wrap": "0.1.0"}}, "ansi-wrap": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/ansi-wrap/-/ansi-wrap-0.1.0.tgz", "integrity": "sha1-qCJQ3bABXponyoLoLqYDu/pF768="}, "ansi-yellow": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/ansi-yellow/-/ansi-yellow-0.1.1.tgz", "integrity": "sha1-y5NW8vRscy8OMZnmEClVp32oPB0=", "requires": {"ansi-wrap": "0.1.0"}}, "anymatch": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/anymatch/-/anymatch-2.0.0.tgz", "integrity": "sha512-5teOsQWABXHHBFP9y3skS5P3d/WfWXpv3FUpy+LorMrNYaT9pI4oLMQX7jzQ2KklNpGpWHzdCXTDT2Y3XGlZBw==", "dev": true, "requires": {"micromatch": "^3.1.4", "normalize-path": "^2.1.1"}, "dependencies": {"normalize-path": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/normalize-path/-/normalize-path-2.1.1.tgz", "integrity": "sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=", "dev": true, "requires": {"remove-trailing-separator": "^1.0.1"}}}}, "aproba": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/aproba/-/aproba-1.2.0.tgz", "integrity": "sha512-Y9J6ZjXtoYh8RnXVCMOU/ttDmk1aBjunq9vO0ta5x85WDQiQfUF9sIPBITdbiiIVcBo03Hi3jMxigBtsddlXRw==", "dev": true}, "argparse": {"version": "1.0.10", "resolved": "https://registry.npmjs.org/argparse/-/argparse-1.0.10.tgz", "integrity": "sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==", "requires": {"sprintf-js": "~1.0.2"}}, "arr-diff": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/arr-diff/-/arr-diff-4.0.0.tgz", "integrity": "sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA=", "dev": true}, "arr-flatten": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/arr-flatten/-/arr-flatten-1.1.0.tgz", "integrity": "sha512-L3hKV5R/p5o81R7O02IGnwpDmkp6E982XhtbuwSe3O4qOtMMMtodicASA1Cny2U+aCXcNpml+m4dPsvsJ3jatg=="}, "arr-map": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/arr-map/-/arr-map-2.0.2.tgz", "integrity": "sha1-Onc0X/wc814qkYJWAfnljy4kysQ=", "requires": {"make-iterator": "^1.0.0"}}, "arr-pluck": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/arr-pluck/-/arr-pluck-0.1.0.tgz", "integrity": "sha1-+K1tcI+HkAiB4jr9gw1SKQp2Z3U=", "requires": {"arr-map": "^2.0.0"}}, "arr-union": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/arr-union/-/arr-union-3.1.0.tgz", "integrity": "sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ="}, "array-sort": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/array-sort/-/array-sort-0.1.4.tgz", "integrity": "sha512-BNcM+RXxndPxiZ2rd76k6nyQLRZr2/B/sdi8pQ+Joafr5AH279L40dfokSUTp8O+AaqYjXWhblBWa2st2nc4fQ==", "requires": {"default-compare": "^1.0.0", "get-value": "^2.0.6", "kind-of": "^5.0.2"}, "dependencies": {"kind-of": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-5.1.0.tgz", "integrity": "sha512-NGEErnH6F2vUuXDh+OlbcKW7/wOcfdRHaZ7VWtqCztfHri/++YKmP51OdWeGPuqCOba6kk2OTe5d02VmTB80Pw=="}}}, "array-unique": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/array-unique/-/array-unique-0.3.2.tgz", "integrity": "sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg="}, "arrayify-compact": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/arrayify-compact/-/arrayify-compact-0.2.0.tgz", "integrity": "sha1-RZFw4VXKErtRRISDnJ1xUHyA7E0=", "requires": {"arr-flatten": "^1.0.1"}}, "asap": {"version": "2.0.6", "resolved": "https://registry.npmjs.org/asap/-/asap-2.0.6.tgz", "integrity": "sha1-5QNHYR1+aQlDIIu9r+vLwvuGbUY="}, "asn1.js": {"version": "4.10.1", "resolved": "https://registry.npmjs.org/asn1.js/-/asn1.js-4.10.1.tgz", "integrity": "sha512-p32cOF5q0Zqs9uBiONKYLm6BClCoBCM5O9JfeUSlnQLBTxYdTK+pW+nXflm8UkKd2UYlEbYz5qEi0JuZR9ckSw==", "dev": true, "requires": {"bn.js": "^4.0.0", "inherits": "^2.0.1", "minimalistic-assert": "^1.0.0"}}, "assemble-core": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/assemble-core/-/assemble-core-0.25.0.tgz", "integrity": "sha1-ZZF7/K+c1rFNm5HQMaDdmar0OWQ=", "requires": {"assemble-fs": "^0.6.0", "assemble-render-file": "^0.7.1", "assemble-streams": "^0.6.0", "base-task": "^0.6.1", "define-property": "^0.2.5", "lazy-cache": "^2.0.1", "templates": "^0.24.0"}, "dependencies": {"define-property": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "requires": {"is-descriptor": "^0.1.0"}}}}, "assemble-fs": {"version": "0.6.0", "resolved": "https://registry.npmjs.org/assemble-fs/-/assemble-fs-0.6.0.tgz", "integrity": "sha1-uky+t0tdG97m1SipZa07fZbe8Og=", "requires": {"assemble-handle": "^0.1.2", "extend-shallow": "^2.0.1", "is-valid-app": "^0.2.0", "lazy-cache": "^2.0.1", "stream-combiner": "^0.2.2", "through2": "^2.0.1", "vinyl-fs": "^2.4.3"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}}}, "assemble-handle": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/assemble-handle/-/assemble-handle-0.1.4.tgz", "integrity": "sha1-6De1uyPnXJsFJX2AfhYvaSzOIW4=", "requires": {"through2": "^2.0.3"}}, "assemble-loader": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/assemble-loader/-/assemble-loader-0.6.1.tgz", "integrity": "sha1-0GmqZBhOFzKEP+HsGAghI1dpVdg=", "requires": {"extend-shallow": "^2.0.1", "file-contents": "^0.2.4", "fs-exists-sync": "^0.1.0", "has-glob": "^0.1.1", "is-registered": "^0.1.5", "is-valid-glob": "^0.3.0", "is-valid-instance": "^0.1.0", "isobject": "^2.1.0", "lazy-cache": "^2.0.1", "load-templates": "^0.11.3"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}, "is-valid-instance": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/is-valid-instance/-/is-valid-instance-0.1.0.tgz", "integrity": "sha1-etXGo4ht/ffZzHgEnO/yFxqZB7M=", "requires": {"isobject": "^2.1.0", "pascalcase": "^0.1.1"}}, "isobject": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "requires": {"isarray": "1.0.0"}}}}, "assemble-render-file": {"version": "0.7.2", "resolved": "https://registry.npmjs.org/assemble-render-file/-/assemble-render-file-0.7.2.tgz", "integrity": "sha1-g6qV9e131ctK6oq8dPIkoVRVccY=", "requires": {"debug": "^2.2.0", "is-valid-app": "^0.1.2", "lazy-cache": "^2.0.1", "mixin-deep": "^1.1.3", "through2": "^2.0.1"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}}, "is-valid-app": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/is-valid-app/-/is-valid-app-0.1.2.tgz", "integrity": "sha1-L2fLs7r2TWWccNBD/JETm1qLlZA=", "requires": {"debug": "^2.2.0", "is-registered": "^0.1.5", "is-valid-instance": "^0.1.0", "lazy-cache": "^2.0.1"}}, "is-valid-instance": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/is-valid-instance/-/is-valid-instance-0.1.0.tgz", "integrity": "sha1-etXGo4ht/ffZzHgEnO/yFxqZB7M=", "requires": {"isobject": "^2.1.0", "pascalcase": "^0.1.1"}}, "isobject": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "requires": {"isarray": "1.0.0"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}}}, "assemble-streams": {"version": "0.6.0", "resolved": "https://registry.npmjs.org/assemble-streams/-/assemble-streams-0.6.0.tgz", "integrity": "sha1-kOkhaoNpltJoNwvtrHG7MdjJq18=", "requires": {"assemble-handle": "^0.1.2", "is-registered": "^0.1.4", "is-valid-instance": "^0.1.0", "lazy-cache": "^2.0.1", "match-file": "^0.2.0", "src-stream": "^0.1.1", "through2": "^2.0.1"}, "dependencies": {"is-valid-instance": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/is-valid-instance/-/is-valid-instance-0.1.0.tgz", "integrity": "sha1-etXGo4ht/ffZzHgEnO/yFxqZB7M=", "requires": {"isobject": "^2.1.0", "pascalcase": "^0.1.1"}}, "isobject": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "requires": {"isarray": "1.0.0"}}}}, "assert": {"version": "1.5.0", "resolved": "https://registry.npmjs.org/assert/-/assert-1.5.0.tgz", "integrity": "sha512-EDsgawzwoun2CZkCgtxJbv392v4nbk9XDD06zI+kQYoBM/3RBWLlEyJARDOmhAAosBjWACEkKL6S+lIZtcAubA==", "dev": true, "requires": {"object-assign": "^4.1.1", "util": "0.10.3"}, "dependencies": {"inherits": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/inherits/-/inherits-2.0.1.tgz", "integrity": "sha1-sX0I0ya0Qj5Wjv9xn5GwscvfafE=", "dev": true}, "util": {"version": "0.10.3", "resolved": "https://registry.npmjs.org/util/-/util-0.10.3.tgz", "integrity": "sha1-evsa/lCAUkZInj23/g7TeTNqwPk=", "dev": true, "requires": {"inherits": "2.0.1"}}}}, "assign-deep": {"version": "0.4.8", "resolved": "https://registry.npmjs.org/assign-deep/-/assign-deep-0.4.8.tgz", "integrity": "sha512-uxqXJCnNZDEjPnsaLKVzmh/ST5+Pqoz0wi06HDfHKx1ASNpSbbvz2qW2Gl8ZyHwr5jnm11X2S5eMQaP1lMZmCg==", "requires": {"assign-symbols": "^0.1.1", "is-primitive": "^2.0.0", "kind-of": "^5.0.2"}, "dependencies": {"assign-symbols": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/assign-symbols/-/assign-symbols-0.1.1.tgz", "integrity": "sha1-ywJZRO9OyKNpPwhunhEsdOOg/tk="}, "kind-of": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-5.1.0.tgz", "integrity": "sha512-NGEErnH6F2vUuXDh+OlbcKW7/wOcfdRHaZ7VWtqCztfHri/++YKmP51OdWeGPuqCOba6kk2OTe5d02VmTB80Pw=="}}}, "assign-symbols": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/assign-symbols/-/assign-symbols-1.0.0.tgz", "integrity": "sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c="}, "async": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/async/-/async-2.6.2.tgz", "integrity": "sha512-H1qVYh1MYhEEFLsP97cVKqCGo7KfCyTt6uEWqsTBr9SO84oK9Uwbyd/yCW+6rKJLHksBNUVWZDAjfS+Ccx0Bbg==", "requires": {"lodash": "^4.17.11"}}, "async-array-reduce": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/async-array-reduce/-/async-array-reduce-0.2.1.tgz", "integrity": "sha1-yL4BCitc0A3qlsgRFgNGk9/dgtE="}, "async-done": {"version": "1.3.2", "resolved": "https://registry.npmjs.org/async-done/-/async-done-1.3.2.tgz", "integrity": "sha512-uYkTP8dw2og1tu1nmza1n1CMW0qb8gWWlwqMmLb7MhBVs4BXrFziT6HXUd+/RlRA/i4H9AkofYloUbs1fwMqlw==", "requires": {"end-of-stream": "^1.1.0", "once": "^1.3.2", "process-nextick-args": "^2.0.0", "stream-exhaust": "^1.0.1"}}, "async-each": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/async-each/-/async-each-1.0.3.tgz", "integrity": "sha512-z/WhQ5FPySLdvREByI2vZiTWwCnF0moMJ1hK9YQwDTHKh6I7/uSckMetoRGb5UBZPC1z0jlw+n/XCgjeH7y1AQ=="}, "async-each-series": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/async-each-series/-/async-each-series-1.1.0.tgz", "integrity": "sha1-9C/YFV048hpbjqB8KOBj7RcAsTg="}, "async-helpers": {"version": "0.3.17", "resolved": "https://registry.npmjs.org/async-helpers/-/async-helpers-0.3.17.tgz", "integrity": "sha512-LfgCyvmK6ZiC7pyqOgli2zfkWL4HYbEb+HXvGgdmqVBgsOOtQz5rSF8Ii/H/1cNNtrfj1KsdZE/lUMeIY3Qcwg==", "requires": {"co": "^4.6.0", "kind-of": "^6.0.0"}}, "async-limiter": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/async-limiter/-/async-limiter-1.0.0.tgz", "integrity": "sha512-jp/uFnooOiO+L211eZOoSyzpOITMXx1rBITauYykG3BRYPu8h0UcxsPNB04RR5vo4Tyz3+ay17tR6JVf9qzYWg=="}, "async-settle": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/async-settle/-/async-settle-0.2.1.tgz", "integrity": "sha1-dnRi1XOACNx16sQkYiNSjyE3E5Y=", "requires": {"async-done": "^0.4.0"}, "dependencies": {"async-done": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/async-done/-/async-done-0.4.0.tgz", "integrity": "sha1-q4BT9fYikPi/xY83zZtzBwszB7k=", "requires": {"end-of-stream": "^0.1.4", "next-tick": "^0.2.2", "once": "^1.3.0", "stream-exhaust": "^1.0.0"}}, "end-of-stream": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/end-of-stream/-/end-of-stream-0.1.5.tgz", "integrity": "sha1-jhdyBsPICDfYVjLouTWd/osvbq8=", "requires": {"once": "~1.3.0"}, "dependencies": {"once": {"version": "1.3.3", "resolved": "https://registry.npmjs.org/once/-/once-1.3.3.tgz", "integrity": "sha1-suJhVXzkwxTsgwTz+oJmPkKXyiA=", "requires": {"wrappy": "1"}}}}}}, "asynckit": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz", "integrity": "sha1-x57Zf380y48robyXkLzDZkdLS3k="}, "atob": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/atob/-/atob-2.1.2.tgz", "integrity": "sha512-Wm6ukoaOGJi/73p/cl2GvLjTI5JM1k/O14isD73YML8StrH/7/lRFgmg8nICZgD3bZZvjwCGxtMOD3wWNAu8cg==", "dev": true}, "babel-loader": {"version": "8.0.6", "resolved": "https://registry.npmjs.org/babel-loader/-/babel-loader-8.0.6.tgz", "integrity": "sha512-4BmWKtBOBm13uoUwd08UwjZlaw3O9GWf456R9j+5YykFZ6LUIjIKLc0zEZf+hauxPOJs96C8k6FvYD09vWzhYw==", "dev": true, "requires": {"find-cache-dir": "^2.0.0", "loader-utils": "^1.0.2", "mkdirp": "^0.5.1", "pify": "^4.0.1"}}, "babel-plugin-emotion": {"version": "10.0.15", "resolved": "https://registry.npmjs.org/babel-plugin-emotion/-/babel-plugin-emotion-10.0.15.tgz", "integrity": "sha512-E3W68Zk8EcKpRUDW2tsFKi4gsavapMRjfr2/KKgG3l7l2zZpiKk0BxB59Ul9C0w0ekv6my/ylGOk2WroaqKXPw==", "requires": {"@babel/helper-module-imports": "^7.0.0", "@emotion/hash": "0.7.2", "@emotion/memoize": "0.7.2", "@emotion/serialize": "^0.11.9", "babel-plugin-macros": "^2.0.0", "babel-plugin-syntax-jsx": "^6.18.0", "convert-source-map": "^1.5.0", "escape-string-regexp": "^1.0.5", "find-root": "^1.1.0", "source-map": "^0.5.7"}, "dependencies": {"@emotion/hash": {"version": "0.7.2", "resolved": "https://registry.npmjs.org/@emotion/hash/-/hash-0.7.2.tgz", "integrity": "sha512-RMtr1i6E8MXaBWwhXL3yeOU8JXRnz8GNxHvaUfVvwxokvayUY0zoBeWbKw1S9XkufmGEEdQd228pSZXFkAln8Q=="}, "@emotion/memoize": {"version": "0.7.2", "resolved": "https://registry.npmjs.org/@emotion/memoize/-/memoize-0.7.2.tgz", "integrity": "sha512-hnHhwQzvPCW1QjBWFyBtsETdllOM92BfrKWbUTmh9aeOlcVOiXvlPsK4104xH8NsaKfg86PTFsWkueQeUfMA/w=="}}}, "babel-plugin-macros": {"version": "2.6.1", "resolved": "https://registry.npmjs.org/babel-plugin-macros/-/babel-plugin-macros-2.6.1.tgz", "integrity": "sha512-6W2nwiXme6j1n2erPOnmRiWfObUhWH7Qw1LMi9XZy8cj+KtESu3T6asZvtk5bMQQjX8te35o7CFueiSdL/2NmQ==", "requires": {"@babel/runtime": "^7.4.2", "cosmiconfig": "^5.2.0", "resolve": "^1.10.0"}}, "babel-plugin-syntax-jsx": {"version": "6.18.0", "resolved": "https://registry.npmjs.org/babel-plugin-syntax-jsx/-/babel-plugin-syntax-jsx-6.18.0.tgz", "integrity": "sha1-CvMqmm4Tyno/1QaeYtew9Y0NiUY="}, "babel-runtime": {"version": "6.26.0", "resolved": "https://registry.npmjs.org/babel-runtime/-/babel-runtime-6.26.0.tgz", "integrity": "sha1-llxwWGaOgrVde/4E/yM3vItWR/4=", "dev": true, "requires": {"core-js": "^2.4.0", "regenerator-runtime": "^0.11.0"}, "dependencies": {"regenerator-runtime": {"version": "0.11.1", "resolved": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.11.1.tgz", "integrity": "sha512-MguG95oij0fC3QV3URf4V2SDYGJhJnJGqvIIgdECeODCT98wSWDAJ94SSuVpYQUoTcGUIL6L4yNB7j1DFFHSBg==", "dev": true}}}, "bach": {"version": "0.5.0", "resolved": "https://registry.npmjs.org/bach/-/bach-0.5.0.tgz", "integrity": "sha1-P/pqN0F3PrwNJL5f2kvF6FtbHaE=", "requires": {"async-done": "^1.1.1", "async-settle": "^0.2.1", "lodash.filter": "^4.1.0", "lodash.flatten": "^4.0.0", "lodash.foreach": "^4.0.0", "lodash.initial": "^4.0.1", "lodash.last": "^3.0.0", "lodash.map": "^4.1.0", "now-and-later": "0.0.6"}}, "balanced-match": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.0.tgz", "integrity": "sha1-ibTRmasr7kneFk6gK4nORi1xt2c="}, "base": {"version": "0.11.2", "resolved": "https://registry.npmjs.org/base/-/base-0.11.2.tgz", "integrity": "sha512-5T6P4xPgpp0YDFvSWwEZ4NoE3aM4QBQXDzmVbraCkFj8zHM+mba8SyqB5DbZWyR7mYHo6Y7BdQo3MoA4m0TeQg==", "requires": {"cache-base": "^1.0.1", "class-utils": "^0.3.5", "component-emitter": "^1.2.1", "define-property": "^1.0.0", "isobject": "^3.0.1", "mixin-deep": "^1.2.0", "pascalcase": "^0.1.1"}, "dependencies": {"define-property": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/define-property/-/define-property-1.0.0.tgz", "integrity": "sha1-dp66rz9KY6rTr56NMEybvnm/sOY=", "requires": {"is-descriptor": "^1.0.0"}}, "is-accessor-descriptor": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-accessor-descriptor/-/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha512-m5hnHTkcVsPfqx3AKlyttIPb7J+XykHvJP2B9bZDjlhLIoEq4XoK64Vg7boZlVWYK6LUY94dYPEE7Lh0ZkZKcQ==", "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz", "integrity": "sha512-jbRXy1FmtAoCjQkVmIVYwuuqDFUbaOeDjmed1tOGPrsMhtJA4rD9tkgA0F1qJ3gRFRXcHYVkdeaP50Q5rE/jLQ==", "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/is-descriptor/-/is-descriptor-1.0.2.tgz", "integrity": "sha512-2eis5WqQGV7peooDyLmNEPUrps9+SXX5c9pL3xEB+4e9HnGuDa7mB7kHxHw4CbqS9k1T2hOH3miL8n8WtiYVtg==", "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}}}, "base-argv": {"version": "0.4.5", "resolved": "https://registry.npmjs.org/base-argv/-/base-argv-0.4.5.tgz", "integrity": "sha1-BalXHNwnaUDeGW/8h07uuJnLED0=", "requires": {"arr-diff": "^2.0.0", "arr-union": "^3.1.0", "debug": "^2.2.0", "define-property": "^0.2.5", "expand-args": "^0.4.1", "extend-shallow": "^2.0.1", "lazy-cache": "^1.0.3"}, "dependencies": {"arr-diff": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/arr-diff/-/arr-diff-2.0.0.tgz", "integrity": "sha1-jzuCf5Vai9ZpaX5KQlasPOrjVs8=", "requires": {"arr-flatten": "^1.0.1"}}, "debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}}, "define-property": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "requires": {"is-descriptor": "^0.1.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}, "lazy-cache": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/lazy-cache/-/lazy-cache-1.0.4.tgz", "integrity": "sha1-odePw6UEdMuAhF07O24dpJpEbo4="}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}}}, "base-cli": {"version": "0.5.0", "resolved": "https://registry.npmjs.org/base-cli/-/base-cli-0.5.0.tgz", "integrity": "sha1-U+Zdjg9bKKoRBo/sjdTpXXLvPOg=", "requires": {"base-argv": "^0.4.2", "base-config": "^0.5.2"}}, "base-cli-process": {"version": "0.1.19", "resolved": "https://registry.npmjs.org/base-cli-process/-/base-cli-process-0.1.19.tgz", "integrity": "sha1-Mg08gVTfcQltSBgY52/m1+R5NjY=", "requires": {"arr-union": "^3.1.0", "arrayify-compact": "^0.2.0", "base-cli": "^0.5.0", "base-cli-schema": "^0.1.19", "base-config-process": "^0.1.9", "base-cwd": "^0.3.4", "base-option": "^0.8.4", "base-pkg": "^0.2.4", "debug": "^2.6.2", "export-files": "^2.1.1", "fs-exists-sync": "^0.1.0", "is-valid-app": "^0.2.1", "kind-of": "^3.1.0", "lazy-cache": "^2.0.2", "log-utils": "^0.2.1", "merge-deep": "^3.0.0", "mixin-deep": "^1.2.0", "object.pick": "^1.2.0", "pad-right": "^0.2.2", "union-value": "^1.0.0"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}}, "kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}}}, "base-cli-schema": {"version": "0.1.19", "resolved": "https://registry.npmjs.org/base-cli-schema/-/base-cli-schema-0.1.19.tgz", "integrity": "sha1-gfQYL0zwu4NnHxF2PknLBbkugkE=", "requires": {"arr-flatten": "^1.0.1", "array-unique": "^0.2.1", "debug": "^2.2.0", "define-property": "^0.2.5", "export-files": "^2.1.1", "extend-shallow": "^2.0.1", "falsey": "^0.3.0", "fs-exists-sync": "^0.1.0", "has-glob": "^0.1.1", "has-value": "^0.3.1", "kind-of": "^3.0.3", "lazy-cache": "^2.0.1", "map-schema": "^0.2.3", "merge-deep": "^3.0.0", "mixin-deep": "^1.1.3", "resolve": "^1.1.7", "tableize-object": "^0.1.0"}, "dependencies": {"array-unique": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/array-unique/-/array-unique-0.2.1.tgz", "integrity": "sha1-odl8yvy8JiXMcPrc6zalDFiwGlM="}, "debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}}, "define-property": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "requires": {"is-descriptor": "^0.1.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}, "has-value": {"version": "0.3.1", "resolved": "https://registry.npmjs.org/has-value/-/has-value-0.3.1.tgz", "integrity": "sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=", "requires": {"get-value": "^2.0.3", "has-values": "^0.1.4", "isobject": "^2.0.0"}}, "has-values": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/has-values/-/has-values-0.1.4.tgz", "integrity": "sha1-bWHeldkd/Km5oCCJrThL/49it3E="}, "isobject": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "requires": {"isarray": "1.0.0"}}, "kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}}}, "base-compose": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/base-compose/-/base-compose-0.2.1.tgz", "integrity": "sha1-reSal/WiRIvVa8s0C090aMb74tc=", "requires": {"copy-task": "^0.1.0", "lazy-cache": "^2.0.1", "mixin-deep": "^1.1.3"}}, "base-config": {"version": "0.5.2", "resolved": "https://registry.npmjs.org/base-config/-/base-config-0.5.2.tgz", "integrity": "sha1-q2A8AdExWL4uYux3/7Ix4o9Ijh8=", "requires": {"isobject": "^2.0.0", "lazy-cache": "^1.0.3", "map-config": "^0.5.0", "resolve-dir": "^0.1.0"}, "dependencies": {"expand-tilde": {"version": "1.2.2", "resolved": "https://registry.npmjs.org/expand-tilde/-/expand-tilde-1.2.2.tgz", "integrity": "sha1-C4HrqJflo9MdHD0QL48BRB5VlEk=", "requires": {"os-homedir": "^1.0.1"}}, "global-modules": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/global-modules/-/global-modules-0.2.3.tgz", "integrity": "sha1-6lo77ULG1s6ZWk+KEmm12uIjgo0=", "requires": {"global-prefix": "^0.1.4", "is-windows": "^0.2.0"}}, "global-prefix": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/global-prefix/-/global-prefix-0.1.5.tgz", "integrity": "sha1-jTvGuNo8qBEqFg2NSW/wRiv+948=", "requires": {"homedir-polyfill": "^1.0.0", "ini": "^1.3.4", "is-windows": "^0.2.0", "which": "^1.2.12"}}, "is-windows": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/is-windows/-/is-windows-0.2.0.tgz", "integrity": "sha1-3hqm1j6indJIc3tp8f+LgALSEIw="}, "isobject": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "requires": {"isarray": "1.0.0"}}, "lazy-cache": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/lazy-cache/-/lazy-cache-1.0.4.tgz", "integrity": "sha1-odePw6UEdMuAhF07O24dpJpEbo4="}, "resolve-dir": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/resolve-dir/-/resolve-dir-0.1.1.tgz", "integrity": "sha1-shklmlYC+sXFxJatiUpujMQwJh4=", "requires": {"expand-tilde": "^1.2.2", "global-modules": "^0.2.3"}}}}, "base-config-process": {"version": "0.1.9", "resolved": "https://registry.npmjs.org/base-config-process/-/base-config-process-0.1.9.tgz", "integrity": "sha1-imOmGYnuY1UMyM/cP2wCdf2gtG4=", "requires": {"base-config": "^0.5.2", "base-config-schema": "^0.1.18", "base-cwd": "^0.3.4", "base-option": "^0.8.4", "debug": "^2.2.0", "export-files": "^2.1.1", "is-valid-app": "^0.2.0", "lazy-cache": "^2.0.1", "micromatch": "^2.3.10", "mixin-deep": "^1.1.3"}, "dependencies": {"arr-diff": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/arr-diff/-/arr-diff-2.0.0.tgz", "integrity": "sha1-jzuCf5Vai9ZpaX5KQlasPOrjVs8=", "requires": {"arr-flatten": "^1.0.1"}}, "array-unique": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/array-unique/-/array-unique-0.2.1.tgz", "integrity": "sha1-odl8yvy8JiXMcPrc6zalDFiwGlM="}, "braces": {"version": "1.8.5", "resolved": "https://registry.npmjs.org/braces/-/braces-1.8.5.tgz", "integrity": "sha1-uneWLhLf+WnWt2cR6RS3N4V79qc=", "requires": {"expand-range": "^1.8.1", "preserve": "^0.2.0", "repeat-element": "^1.1.2"}}, "debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}}, "expand-brackets": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/expand-brackets/-/expand-brackets-0.1.5.tgz", "integrity": "sha1-3wcoTjQqgHzXM6xa9yQR5YHRF3s=", "requires": {"is-posix-bracket": "^0.1.0"}}, "extglob": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/extglob/-/extglob-0.3.2.tgz", "integrity": "sha1-Lhj/PS9JqydlzskCPwEdqo2DSaE=", "requires": {"is-extglob": "^1.0.0"}}, "is-extglob": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-extglob/-/is-extglob-1.0.0.tgz", "integrity": "sha1-rEaBd8SUNAWgkvyPKXYMb/xiBsA="}, "is-glob": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/is-glob/-/is-glob-2.0.1.tgz", "integrity": "sha1-0Jb5JqPe1WAPP9/ZEZjLCIjC2GM=", "requires": {"is-extglob": "^1.0.0"}}, "kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}, "micromatch": {"version": "2.3.11", "resolved": "https://registry.npmjs.org/micromatch/-/micromatch-2.3.11.tgz", "integrity": "sha1-hmd8l9FyCzY0MdBNDRUpO9OMFWU=", "requires": {"arr-diff": "^2.0.0", "array-unique": "^0.2.1", "braces": "^1.8.2", "expand-brackets": "^0.1.4", "extglob": "^0.3.1", "filename-regex": "^2.0.0", "is-extglob": "^1.0.0", "is-glob": "^2.0.1", "kind-of": "^3.0.2", "normalize-path": "^2.0.1", "object.omit": "^2.0.0", "parse-glob": "^3.0.4", "regex-cache": "^0.4.2"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}, "normalize-path": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/normalize-path/-/normalize-path-2.1.1.tgz", "integrity": "sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=", "requires": {"remove-trailing-separator": "^1.0.1"}}}}, "base-config-schema": {"version": "0.1.24", "resolved": "https://registry.npmjs.org/base-config-schema/-/base-config-schema-0.1.24.tgz", "integrity": "sha1-T74UvsVtwa7ef+3QaSjpGfhyH6k=", "requires": {"arr-flatten": "^1.0.3", "array-unique": "^0.3.2", "base-pkg": "^0.2.4", "camel-case": "^3.0.0", "debug": "^2.6.6", "define-property": "^1.0.0", "export-files": "^2.1.1", "extend-shallow": "^2.0.1", "has-glob": "^1.0.0", "has-value": "^0.3.1", "inflection": "^1.12.0", "kind-of": "^3.2.0", "lazy-cache": "^2.0.2", "load-templates": "^1.0.2", "map-schema": "^0.2.4", "matched": "^0.4.4", "mixin-deep": "^1.2.0", "resolve": "^1.3.3"}, "dependencies": {"clone": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/clone/-/clone-2.1.2.tgz", "integrity": "sha1-G39Ln1kfHo+DZwQBYANFoCiHQ18="}, "clone-stats": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/clone-stats/-/clone-stats-1.0.0.tgz", "integrity": "sha1-s3gt/4u1R04Yuba/D9/ngvh3doA="}, "debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}}, "define-property": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/define-property/-/define-property-1.0.0.tgz", "integrity": "sha1-dp66rz9KY6rTr56NMEybvnm/sOY=", "requires": {"is-descriptor": "^1.0.0"}}, "expand-tilde": {"version": "1.2.2", "resolved": "https://registry.npmjs.org/expand-tilde/-/expand-tilde-1.2.2.tgz", "integrity": "sha1-C4HrqJflo9MdHD0QL48BRB5VlEk=", "requires": {"os-homedir": "^1.0.1"}}, "extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}, "file-contents": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/file-contents/-/file-contents-1.0.1.tgz", "integrity": "sha1-ryW7/T00RjhPrYBmSdiAi8/uHsg=", "requires": {"define-property": "^0.2.5", "extend-shallow": "^2.0.1", "is-buffer": "^1.1.4", "kind-of": "^3.1.0", "lazy-cache": "^2.0.2", "strip-bom-buffer": "^0.1.1", "strip-bom-string": "^0.1.2", "through2": "^2.0.3"}, "dependencies": {"define-property": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "requires": {"is-descriptor": "^0.1.0"}}, "is-accessor-descriptor": {"version": "0.1.6", "resolved": "https://registry.npmjs.org/is-accessor-descriptor/-/is-accessor-descriptor-0.1.6.tgz", "integrity": "sha1-qeEss66Nh2cn7u84Q/igiXtcmNY=", "requires": {"kind-of": "^3.0.2"}}, "is-data-descriptor": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/is-data-descriptor/-/is-data-descriptor-0.1.4.tgz", "integrity": "sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y=", "requires": {"kind-of": "^3.0.2"}}, "is-descriptor": {"version": "0.1.6", "resolved": "https://registry.npmjs.org/is-descriptor/-/is-descriptor-0.1.6.tgz", "integrity": "sha512-avDYr0SB3DwO9zsMov0gKCESFYqCnE4hq/4z3TdUlukEy5t9C0YRq7HLrsN52NAcqXKaepeCD0n+B0arnVG3Hg==", "requires": {"is-accessor-descriptor": "^0.1.6", "is-data-descriptor": "^0.1.4", "kind-of": "^5.0.0"}, "dependencies": {"kind-of": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-5.1.0.tgz", "integrity": "sha512-NGEErnH6F2vUuXDh+OlbcKW7/wOcfdRHaZ7VWtqCztfHri/++YKmP51OdWeGPuqCOba6kk2OTe5d02VmTB80Pw=="}}}}}, "global-modules": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/global-modules/-/global-modules-0.2.3.tgz", "integrity": "sha1-6lo77ULG1s6ZWk+KEmm12uIjgo0=", "requires": {"global-prefix": "^0.1.4", "is-windows": "^0.2.0"}}, "global-prefix": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/global-prefix/-/global-prefix-0.1.5.tgz", "integrity": "sha1-jTvGuNo8qBEqFg2NSW/wRiv+948=", "requires": {"homedir-polyfill": "^1.0.0", "ini": "^1.3.4", "is-windows": "^0.2.0", "which": "^1.2.12"}}, "has-glob": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/has-glob/-/has-glob-1.0.0.tgz", "integrity": "sha1-mqqe7b/7G6OZCnsAEPtnjuAIEgc=", "requires": {"is-glob": "^3.0.0"}}, "has-value": {"version": "0.3.1", "resolved": "https://registry.npmjs.org/has-value/-/has-value-0.3.1.tgz", "integrity": "sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=", "requires": {"get-value": "^2.0.3", "has-values": "^0.1.4", "isobject": "^2.0.0"}}, "has-values": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/has-values/-/has-values-0.1.4.tgz", "integrity": "sha1-bWHeldkd/Km5oCCJrThL/49it3E="}, "is-accessor-descriptor": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-accessor-descriptor/-/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha512-m5hnHTkcVsPfqx3AKlyttIPb7J+XykHvJP2B9bZDjlhLIoEq4XoK64Vg7boZlVWYK6LUY94dYPEE7Lh0ZkZKcQ==", "requires": {"kind-of": "^6.0.0"}, "dependencies": {"kind-of": {"version": "6.0.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-6.0.2.tgz", "integrity": "sha512-s5kLOcnH0XqDO+FvuaLX8DDjZ18CGFk7VygH40QoKPUQhW4e2rvM0rwUq0t8IQDOwYSeLK01U90OjzBTme2QqA=="}}}, "is-data-descriptor": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz", "integrity": "sha512-jbRXy1FmtAoCjQkVmIVYwuuqDFUbaOeDjmed1tOGPrsMhtJA4rD9tkgA0F1qJ3gRFRXcHYVkdeaP50Q5rE/jLQ==", "requires": {"kind-of": "^6.0.0"}, "dependencies": {"kind-of": {"version": "6.0.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-6.0.2.tgz", "integrity": "sha512-s5kLOcnH0XqDO+FvuaLX8DDjZ18CGFk7VygH40QoKPUQhW4e2rvM0rwUq0t8IQDOwYSeLK01U90OjzBTme2QqA=="}}}, "is-descriptor": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/is-descriptor/-/is-descriptor-1.0.2.tgz", "integrity": "sha512-2eis5WqQGV7peooDyLmNEPUrps9+SXX5c9pL3xEB+4e9HnGuDa7mB7kHxHw4CbqS9k1T2hOH3miL8n8WtiYVtg==", "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}, "dependencies": {"kind-of": {"version": "6.0.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-6.0.2.tgz", "integrity": "sha512-s5kLOcnH0XqDO+FvuaLX8DDjZ18CGFk7VygH40QoKPUQhW4e2rvM0rwUq0t8IQDOwYSeLK01U90OjzBTme2QqA=="}}}, "is-glob": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/is-glob/-/is-glob-3.1.0.tgz", "integrity": "sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo=", "requires": {"is-extglob": "^2.1.0"}}, "is-windows": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/is-windows/-/is-windows-0.2.0.tgz", "integrity": "sha1-3hqm1j6indJIc3tp8f+LgALSEIw="}, "isobject": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "requires": {"isarray": "1.0.0"}}, "kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}, "load-templates": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/load-templates/-/load-templates-1.0.2.tgz", "integrity": "sha1-CfOOlcjvS/t4W9f8qOv9MrIwvIc=", "requires": {"extend-shallow": "^2.0.1", "file-contents": "^1.0.0", "glob-parent": "^3.1.0", "is-glob": "^3.1.0", "kind-of": "^3.1.0", "lazy-cache": "^2.0.2", "matched": "^0.4.4", "vinyl": "^2.0.1"}}, "matched": {"version": "0.4.4", "resolved": "https://registry.npmjs.org/matched/-/matched-0.4.4.tgz", "integrity": "sha1-Vte36xgDPwz5vFLrIJD6x9weifo=", "requires": {"arr-union": "^3.1.0", "async-array-reduce": "^0.2.0", "extend-shallow": "^2.0.1", "fs-exists-sync": "^0.1.0", "glob": "^7.0.5", "has-glob": "^0.1.1", "is-valid-glob": "^0.3.0", "lazy-cache": "^2.0.1", "resolve-dir": "^0.1.0"}, "dependencies": {"has-glob": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/has-glob/-/has-glob-0.1.1.tgz", "integrity": "sha1-omHEwqbGZ+DHe3AKfyl8Oe86pYk=", "requires": {"is-glob": "^2.0.1"}}, "is-extglob": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-extglob/-/is-extglob-1.0.0.tgz", "integrity": "sha1-rEaBd8SUNAWgkvyPKXYMb/xiBsA="}, "is-glob": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/is-glob/-/is-glob-2.0.1.tgz", "integrity": "sha1-0Jb5JqPe1WAPP9/ZEZjLCIjC2GM=", "requires": {"is-extglob": "^1.0.0"}}}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}, "replace-ext": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/replace-ext/-/replace-ext-1.0.0.tgz", "integrity": "sha1-3mMSg3P8v3w8z6TeWkgMRaZ5WOs="}, "resolve-dir": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/resolve-dir/-/resolve-dir-0.1.1.tgz", "integrity": "sha1-shklmlYC+sXFxJatiUpujMQwJh4=", "requires": {"expand-tilde": "^1.2.2", "global-modules": "^0.2.3"}}, "vinyl": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/vinyl/-/vinyl-2.2.0.tgz", "integrity": "sha512-MBH+yP0kC/GQ5GwBqrTPTzEfiiLjta7hTtvQtbxBgTeSXsmKQRQecjibMbxIXzVT3Y9KJK+drOz1/k+vsu8Nkg==", "requires": {"clone": "^2.1.1", "clone-buffer": "^1.0.0", "clone-stats": "^1.0.0", "cloneable-readable": "^1.0.0", "remove-trailing-separator": "^1.0.1", "replace-ext": "^1.0.0"}}}}, "base-cwd": {"version": "0.3.4", "resolved": "https://registry.npmjs.org/base-cwd/-/base-cwd-0.3.4.tgz", "integrity": "sha1-TQCrY1CgRuGtSrnCMm2heUs+TwE=", "requires": {"empty-dir": "^0.2.0", "find-pkg": "^0.1.2", "is-valid-app": "^0.2.0"}}, "base-data": {"version": "0.6.2", "resolved": "https://registry.npmjs.org/base-data/-/base-data-0.6.2.tgz", "integrity": "sha512-wH2ViG6CUO2AaeHSEt6fJTyQAk5gl0oY456DoSC5h8mnHrWUbvdctMCuF53CXgBmi0oalZQppKNH0iamG5+uqw==", "requires": {"arr-flatten": "^1.1.0", "cache-base": "^1.0.0", "extend-shallow": "^2.0.1", "get-value": "^2.0.6", "has-glob": "^1.0.0", "has-value": "^1.0.0", "is-registered": "^0.1.5", "is-valid-app": "^0.3.0", "kind-of": "^5.0.0", "lazy-cache": "^2.0.2", "merge-value": "^1.0.0", "mixin-deep": "^1.2.0", "read-file": "^0.2.0", "resolve-glob": "^1.0.0", "set-value": "^2.0.0", "union-value": "^1.0.0"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}, "has-glob": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/has-glob/-/has-glob-1.0.0.tgz", "integrity": "sha1-mqqe7b/7G6OZCnsAEPtnjuAIEgc=", "requires": {"is-glob": "^3.0.0"}}, "is-glob": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/is-glob/-/is-glob-3.1.0.tgz", "integrity": "sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo=", "requires": {"is-extglob": "^2.1.0"}}, "is-valid-app": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/is-valid-app/-/is-valid-app-0.3.0.tgz", "integrity": "sha1-eBBrdR88oyOF+0VJK/KUF7WZPIA=", "requires": {"debug": "^2.6.3", "is-registered": "^0.1.5", "is-valid-instance": "^0.3.0", "lazy-cache": "^2.0.2"}}, "is-valid-instance": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/is-valid-instance/-/is-valid-instance-0.3.0.tgz", "integrity": "sha1-9KxzAjxNTYubw7PsPmZjBRbijp4=", "requires": {"isobject": "^3.0.0", "pascalcase": "^0.1.1"}}, "kind-of": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-5.1.0.tgz", "integrity": "sha512-NGEErnH6F2vUuXDh+OlbcKW7/wOcfdRHaZ7VWtqCztfHri/++YKmP51OdWeGPuqCOba6kk2OTe5d02VmTB80Pw=="}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}}}, "base-engines": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/base-engines/-/base-engines-0.2.1.tgz", "integrity": "sha1-aXgAyoq4iKM3iXONv6zLgYoqWns=", "requires": {"debug": "^2.2.0", "define-property": "^0.2.5", "engine-cache": "^0.19.0", "is-valid-app": "^0.1.2", "lazy-cache": "^2.0.1"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}}, "define-property": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "requires": {"is-descriptor": "^0.1.0"}}, "is-valid-app": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/is-valid-app/-/is-valid-app-0.1.2.tgz", "integrity": "sha1-L2fLs7r2TWWccNBD/JETm1qLlZA=", "requires": {"debug": "^2.2.0", "is-registered": "^0.1.5", "is-valid-instance": "^0.1.0", "lazy-cache": "^2.0.1"}}, "is-valid-instance": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/is-valid-instance/-/is-valid-instance-0.1.0.tgz", "integrity": "sha1-etXGo4ht/ffZzHgEnO/yFxqZB7M=", "requires": {"isobject": "^2.1.0", "pascalcase": "^0.1.1"}}, "isobject": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "requires": {"isarray": "1.0.0"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}}}, "base-env": {"version": "0.3.1", "resolved": "https://registry.npmjs.org/base-env/-/base-env-0.3.1.tgz", "integrity": "sha512-/HxC8QV1m/bWqvjcu4WZl4Um1HRpTAjuY31uiFUEukXsXge4WIvNvGKG/gCs2PrpBFPCybowA406V/ivdPknpQ==", "requires": {"base-namespace": "^0.2.0", "contains-path": "^0.1.0", "debug": "^2.2.0", "extend-shallow": "^2.0.1", "fs-exists-sync": "^0.1.0", "global-modules": "^0.2.2", "is-absolute": "^0.2.5", "is-valid-app": "^0.1.0", "is-valid-instance": "^0.1.0", "kind-of": "^3.0.3", "os-homedir": "^1.0.1", "resolve-file": "^0.3.0"}, "dependencies": {"cwd": {"version": "0.10.0", "resolved": "https://registry.npmjs.org/cwd/-/cwd-0.10.0.tgz", "integrity": "sha1-FyQAaUBXwioTsM8WFix+S3p/5Wc=", "requires": {"find-pkg": "^0.1.2", "fs-exists-sync": "^0.1.0"}}, "debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}, "global-modules": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/global-modules/-/global-modules-0.2.3.tgz", "integrity": "sha1-6lo77ULG1s6ZWk+KEmm12uIjgo0=", "requires": {"global-prefix": "^0.1.4", "is-windows": "^0.2.0"}}, "global-prefix": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/global-prefix/-/global-prefix-0.1.5.tgz", "integrity": "sha1-jTvGuNo8qBEqFg2NSW/wRiv+948=", "requires": {"homedir-polyfill": "^1.0.0", "ini": "^1.3.4", "is-windows": "^0.2.0", "which": "^1.2.12"}}, "is-valid-app": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/is-valid-app/-/is-valid-app-0.1.2.tgz", "integrity": "sha1-L2fLs7r2TWWccNBD/JETm1qLlZA=", "requires": {"debug": "^2.2.0", "is-registered": "^0.1.5", "is-valid-instance": "^0.1.0", "lazy-cache": "^2.0.1"}}, "is-valid-instance": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/is-valid-instance/-/is-valid-instance-0.1.0.tgz", "integrity": "sha1-etXGo4ht/ffZzHgEnO/yFxqZB7M=", "requires": {"isobject": "^2.1.0", "pascalcase": "^0.1.1"}}, "is-windows": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/is-windows/-/is-windows-0.2.0.tgz", "integrity": "sha1-3hqm1j6indJIc3tp8f+LgALSEIw="}, "isobject": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "requires": {"isarray": "1.0.0"}}, "kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}, "resolve-file": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/resolve-file/-/resolve-file-0.3.0.tgz", "integrity": "sha1-EeH7RkVm06fFAMt+lIHo8LAKFO8=", "requires": {"cwd": "^0.10.0", "expand-tilde": "^2.0.2", "extend-shallow": "^2.0.1", "fs-exists-sync": "^0.1.0", "homedir-polyfill": "^1.0.1", "lazy-cache": "^2.0.2", "resolve": "^1.2.0"}}}}, "base-generators": {"version": "0.4.6", "resolved": "https://registry.npmjs.org/base-generators/-/base-generators-0.4.6.tgz", "integrity": "sha1-4amTYh5bRCr44MgRMVoyb5h8nqY=", "requires": {"async-each-series": "^1.1.0", "base-compose": "^0.2.1", "base-cwd": "^0.3.1", "base-data": "^0.6.0", "base-env": "^0.3.0", "base-option": "^0.8.4", "base-pkg": "^0.2.4", "base-plugins": "^0.4.13", "base-task": "^0.6.1", "debug": "^2.2.0", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "global-modules": "^0.2.2", "is-valid-app": "^0.2.0", "is-valid-instance": "^0.2.0", "kind-of": "^3.0.3", "lazy-cache": "^2.0.1", "mixin-deep": "^1.1.3"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}}, "define-property": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "requires": {"is-descriptor": "^0.1.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}, "global-modules": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/global-modules/-/global-modules-0.2.3.tgz", "integrity": "sha1-6lo77ULG1s6ZWk+KEmm12uIjgo0=", "requires": {"global-prefix": "^0.1.4", "is-windows": "^0.2.0"}}, "global-prefix": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/global-prefix/-/global-prefix-0.1.5.tgz", "integrity": "sha1-jTvGuNo8qBEqFg2NSW/wRiv+948=", "requires": {"homedir-polyfill": "^1.0.0", "ini": "^1.3.4", "is-windows": "^0.2.0", "which": "^1.2.12"}}, "is-windows": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/is-windows/-/is-windows-0.2.0.tgz", "integrity": "sha1-3hqm1j6indJIc3tp8f+LgALSEIw="}, "kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}}}, "base-helpers": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/base-helpers/-/base-helpers-0.1.1.tgz", "integrity": "sha1-2k4eKy+ACOzc6T8R79223gYzP7M=", "requires": {"debug": "^2.2.0", "define-property": "^0.2.5", "is-valid-app": "^0.1.0", "lazy-cache": "^2.0.1", "load-helpers": "^0.2.11"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}}, "define-property": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "requires": {"is-descriptor": "^0.1.0"}}, "is-valid-app": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/is-valid-app/-/is-valid-app-0.1.2.tgz", "integrity": "sha1-L2fLs7r2TWWccNBD/JETm1qLlZA=", "requires": {"debug": "^2.2.0", "is-registered": "^0.1.5", "is-valid-instance": "^0.1.0", "lazy-cache": "^2.0.1"}}, "is-valid-instance": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/is-valid-instance/-/is-valid-instance-0.1.0.tgz", "integrity": "sha1-etXGo4ht/ffZzHgEnO/yFxqZB7M=", "requires": {"isobject": "^2.1.0", "pascalcase": "^0.1.1"}}, "isobject": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "requires": {"isarray": "1.0.0"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}}}, "base-namespace": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/base-namespace/-/base-namespace-0.2.0.tgz", "integrity": "sha1-RLLLumZ1Y8xE5trrTv5AO7CrPaA=", "requires": {"is-valid-app": "^0.1.0"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}}, "is-valid-app": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/is-valid-app/-/is-valid-app-0.1.2.tgz", "integrity": "sha1-L2fLs7r2TWWccNBD/JETm1qLlZA=", "requires": {"debug": "^2.2.0", "is-registered": "^0.1.5", "is-valid-instance": "^0.1.0", "lazy-cache": "^2.0.1"}}, "is-valid-instance": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/is-valid-instance/-/is-valid-instance-0.1.0.tgz", "integrity": "sha1-etXGo4ht/ffZzHgEnO/yFxqZB7M=", "requires": {"isobject": "^2.1.0", "pascalcase": "^0.1.1"}}, "isobject": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "requires": {"isarray": "1.0.0"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}}}, "base-option": {"version": "0.8.4", "resolved": "https://registry.npmjs.org/base-option/-/base-option-0.8.4.tgz", "integrity": "sha1-EUF/qSRPInpNU3tNKRcjRieH1cc=", "requires": {"define-property": "^0.2.5", "get-value": "^2.0.6", "is-valid-app": "^0.2.0", "isobject": "^2.1.0", "lazy-cache": "^2.0.1", "mixin-deep": "^1.1.3", "option-cache": "^3.4.0", "set-value": "^0.3.3"}, "dependencies": {"define-property": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "requires": {"is-descriptor": "^0.1.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}, "isobject": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "requires": {"isarray": "1.0.0"}}, "set-value": {"version": "0.3.3", "resolved": "https://registry.npmjs.org/set-value/-/set-value-0.3.3.tgz", "integrity": "sha1-uBIjaBY4oQiP2IpDW4qdMtro2bo=", "requires": {"extend-shallow": "^2.0.1", "isobject": "^2.0.0", "to-object-path": "^0.2.0"}}, "to-object-path": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/to-object-path/-/to-object-path-0.2.0.tgz", "integrity": "sha1-FjThtSqIugDjlJYZ/ACB3Jo7B8o=", "requires": {"arr-flatten": "^1.0.1", "is-arguments": "^1.0.2"}}}}, "base-pkg": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/base-pkg/-/base-pkg-0.2.5.tgz", "integrity": "sha512-/POxajlgBhVsknwLXnqnbp//bAMh7SkDgHF+z/uoYnFqk46e05c3MxSEmn5vFCB8g4rHHKxAPLKrU/4Yb3vUdA==", "requires": {"cache-base": "^1.0.0", "debug": "^2.6.8", "define-property": "^1.0.0", "expand-pkg": "^0.1.8", "extend-shallow": "^2.0.1", "is-valid-app": "^0.3.0", "log-utils": "^0.2.1", "pkg-store": "^0.2.2"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}}, "define-property": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/define-property/-/define-property-1.0.0.tgz", "integrity": "sha1-dp66rz9KY6rTr56NMEybvnm/sOY=", "requires": {"is-descriptor": "^1.0.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}, "is-accessor-descriptor": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-accessor-descriptor/-/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha512-m5hnHTkcVsPfqx3AKlyttIPb7J+XykHvJP2B9bZDjlhLIoEq4XoK64Vg7boZlVWYK6LUY94dYPEE7Lh0ZkZKcQ==", "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz", "integrity": "sha512-jbRXy1FmtAoCjQkVmIVYwuuqDFUbaOeDjmed1tOGPrsMhtJA4rD9tkgA0F1qJ3gRFRXcHYVkdeaP50Q5rE/jLQ==", "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/is-descriptor/-/is-descriptor-1.0.2.tgz", "integrity": "sha512-2eis5WqQGV7peooDyLmNEPUrps9+SXX5c9pL3xEB+4e9HnGuDa7mB7kHxHw4CbqS9k1T2hOH3miL8n8WtiYVtg==", "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}, "is-valid-app": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/is-valid-app/-/is-valid-app-0.3.0.tgz", "integrity": "sha1-eBBrdR88oyOF+0VJK/KUF7WZPIA=", "requires": {"debug": "^2.6.3", "is-registered": "^0.1.5", "is-valid-instance": "^0.3.0", "lazy-cache": "^2.0.2"}}, "is-valid-instance": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/is-valid-instance/-/is-valid-instance-0.3.0.tgz", "integrity": "sha1-9KxzAjxNTYubw7PsPmZjBRbijp4=", "requires": {"isobject": "^3.0.0", "pascalcase": "^0.1.1"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}}}, "base-plugins": {"version": "0.4.13", "resolved": "https://registry.npmjs.org/base-plugins/-/base-plugins-0.4.13.tgz", "integrity": "sha1-kd8XjcN/hoQt6ihteeSPuGtarD0=", "requires": {"define-property": "^0.2.5", "is-registered": "^0.1.5", "isobject": "^2.1.0"}, "dependencies": {"define-property": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "requires": {"is-descriptor": "^0.1.0"}}, "isobject": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "requires": {"isarray": "1.0.0"}}}}, "base-questions": {"version": "0.7.4", "resolved": "https://registry.npmjs.org/base-questions/-/base-questions-0.7.4.tgz", "integrity": "sha1-9k+EgmHtbIKPSYPXgS9A0wN4IUY=", "requires": {"base-store": "^0.4.4", "clone-deep": "^0.2.4", "debug": "^2.2.0", "define-property": "^0.2.5", "is-valid-app": "^0.2.0", "isobject": "^2.1.0", "lazy-cache": "^2.0.1", "mixin-deep": "^1.1.3", "question-store": "^0.11.0"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}}, "define-property": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "requires": {"is-descriptor": "^0.1.0"}}, "isobject": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "requires": {"isarray": "1.0.0"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}}}, "base-routes": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/base-routes/-/base-routes-0.2.2.tgz", "integrity": "sha1-CmFNFy1JBF2Mk4dxP4YN88QFNB4=", "requires": {"debug": "^2.2.0", "en-route": "^0.7.5", "is-valid-app": "^0.2.0", "lazy-cache": "^2.0.1", "template-error": "^0.1.2"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}}}, "base-runtimes": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/base-runtimes/-/base-runtimes-0.2.0.tgz", "integrity": "sha1-GI4+ZoJMyxWYsyh7TqW5NaG4UEU=", "requires": {"extend-shallow": "^2.0.1", "is-valid-app": "^0.2.0", "lazy-cache": "^2.0.1", "log-utils": "^0.1.4", "micromatch": "^2.3.10", "time-diff": "^0.3.1"}, "dependencies": {"ansi-colors": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/ansi-colors/-/ansi-colors-0.1.0.tgz", "integrity": "sha1-M0rDbNPq1wjeXGnhmpjRhkImtD8=", "requires": {"ansi-bgblack": "^0.1.1", "ansi-bgblue": "^0.1.1", "ansi-bgcyan": "^0.1.1", "ansi-bggreen": "^0.1.1", "ansi-bgmagenta": "^0.1.1", "ansi-bgred": "^0.1.1", "ansi-bgwhite": "^0.1.1", "ansi-bgyellow": "^0.1.1", "ansi-black": "^0.1.1", "ansi-blue": "^0.1.1", "ansi-bold": "^0.1.1", "ansi-cyan": "^0.1.1", "ansi-dim": "^0.1.1", "ansi-gray": "^0.1.1", "ansi-green": "^0.1.1", "ansi-grey": "^0.1.1", "ansi-hidden": "^0.1.1", "ansi-inverse": "^0.1.1", "ansi-italic": "^0.1.1", "ansi-magenta": "^0.1.1", "ansi-red": "^0.1.1", "ansi-reset": "^0.1.1", "ansi-strikethrough": "^0.1.1", "ansi-underline": "^0.1.1", "ansi-white": "^0.1.1", "ansi-yellow": "^0.1.1", "lazy-cache": "^0.2.4"}, "dependencies": {"lazy-cache": {"version": "0.2.7", "resolved": "https://registry.npmjs.org/lazy-cache/-/lazy-cache-0.2.7.tgz", "integrity": "sha1-f+3fLctu23fRHvHRF6tf/fCrG2U="}}}, "arr-diff": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/arr-diff/-/arr-diff-2.0.0.tgz", "integrity": "sha1-jzuCf5Vai9ZpaX5KQlasPOrjVs8=", "requires": {"arr-flatten": "^1.0.1"}}, "array-unique": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/array-unique/-/array-unique-0.2.1.tgz", "integrity": "sha1-odl8yvy8JiXMcPrc6zalDFiwGlM="}, "braces": {"version": "1.8.5", "resolved": "https://registry.npmjs.org/braces/-/braces-1.8.5.tgz", "integrity": "sha1-uneWLhLf+WnWt2cR6RS3N4V79qc=", "requires": {"expand-range": "^1.8.1", "preserve": "^0.2.0", "repeat-element": "^1.1.2"}}, "expand-brackets": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/expand-brackets/-/expand-brackets-0.1.5.tgz", "integrity": "sha1-3wcoTjQqgHzXM6xa9yQR5YHRF3s=", "requires": {"is-posix-bracket": "^0.1.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}, "extglob": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/extglob/-/extglob-0.3.2.tgz", "integrity": "sha1-Lhj/PS9JqydlzskCPwEdqo2DSaE=", "requires": {"is-extglob": "^1.0.0"}}, "is-extglob": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-extglob/-/is-extglob-1.0.0.tgz", "integrity": "sha1-rEaBd8SUNAWgkvyPKXYMb/xiBsA="}, "is-glob": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/is-glob/-/is-glob-2.0.1.tgz", "integrity": "sha1-0Jb5JqPe1WAPP9/ZEZjLCIjC2GM=", "requires": {"is-extglob": "^1.0.0"}}, "kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}, "log-utils": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/log-utils/-/log-utils-0.1.5.tgz", "integrity": "sha1-3g84+Vf0zW69Xctoddijua4HT3c=", "requires": {"ansi-colors": "^0.1.0", "error-symbol": "^0.1.0", "info-symbol": "^0.1.0", "log-ok": "^0.1.1", "success-symbol": "^0.1.0", "time-stamp": "^1.0.1", "warning-symbol": "^0.1.0"}}, "micromatch": {"version": "2.3.11", "resolved": "https://registry.npmjs.org/micromatch/-/micromatch-2.3.11.tgz", "integrity": "sha1-hmd8l9FyCzY0MdBNDRUpO9OMFWU=", "requires": {"arr-diff": "^2.0.0", "array-unique": "^0.2.1", "braces": "^1.8.2", "expand-brackets": "^0.1.4", "extglob": "^0.3.1", "filename-regex": "^2.0.0", "is-extglob": "^1.0.0", "is-glob": "^2.0.1", "kind-of": "^3.0.2", "normalize-path": "^2.0.1", "object.omit": "^2.0.0", "parse-glob": "^3.0.4", "regex-cache": "^0.4.2"}}, "normalize-path": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/normalize-path/-/normalize-path-2.1.1.tgz", "integrity": "sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=", "requires": {"remove-trailing-separator": "^1.0.1"}}}}, "base-store": {"version": "0.4.4", "resolved": "https://registry.npmjs.org/base-store/-/base-store-0.4.4.tgz", "integrity": "sha1-JY32uKYu4G/xUADJSdD9fCi68mY=", "requires": {"data-store": "^0.16.0", "debug": "^2.2.0", "extend-shallow": "^2.0.1", "is-registered": "^0.1.4", "is-valid-instance": "^0.1.0", "lazy-cache": "^2.0.1", "project-name": "^0.2.5"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}, "is-valid-instance": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/is-valid-instance/-/is-valid-instance-0.1.0.tgz", "integrity": "sha1-etXGo4ht/ffZzHgEnO/yFxqZB7M=", "requires": {"isobject": "^2.1.0", "pascalcase": "^0.1.1"}}, "isobject": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "requires": {"isarray": "1.0.0"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}}}, "base-task": {"version": "0.6.2", "resolved": "https://registry.npmjs.org/base-task/-/base-task-0.6.2.tgz", "integrity": "sha1-Rn1guuBzezuJab/1f6RElJiZgcA=", "requires": {"composer": "^0.13.0", "is-valid-app": "^0.1.0"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}}, "is-valid-app": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/is-valid-app/-/is-valid-app-0.1.2.tgz", "integrity": "sha1-L2fLs7r2TWWccNBD/JETm1qLlZA=", "requires": {"debug": "^2.2.0", "is-registered": "^0.1.5", "is-valid-instance": "^0.1.0", "lazy-cache": "^2.0.1"}}, "is-valid-instance": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/is-valid-instance/-/is-valid-instance-0.1.0.tgz", "integrity": "sha1-etXGo4ht/ffZzHgEnO/yFxqZB7M=", "requires": {"isobject": "^2.1.0", "pascalcase": "^0.1.1"}}, "isobject": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "requires": {"isarray": "1.0.0"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}}}, "base64-js": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/base64-js/-/base64-js-1.3.0.tgz", "integrity": "sha512-ccav/yGvoa80BQDljCxsmmQ3Xvx60/UpBIij5QN21W3wBi/hhIC9OoO+KLpu9IJTS9j4DRVJ3aDDF9cMSoa2lw==", "dev": true}, "big.js": {"version": "5.2.2", "resolved": "https://registry.npmjs.org/big.js/-/big.js-5.2.2.tgz", "integrity": "sha512-vyL2OymJxmarO8gxMr0mhChsO9QGwhynfuu4+MHTAW6czfq9humCB7rKpUjDd9YUiDPU4mzpyupFSvOClAwbmQ==", "dev": true}, "binary-extensions": {"version": "1.13.1", "resolved": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-1.13.1.tgz", "integrity": "sha512-Un7MIEDdUC5gNpcGDV97op1Ywk748MpHcFTHoYs6qnj1Z3j7I53VG3nwZhKzoBZmbdRNnb6WRdFlwl7tSDuZGw==", "dev": true}, "bluebird": {"version": "3.5.5", "resolved": "https://registry.npmjs.org/bluebird/-/bluebird-3.5.5.tgz", "integrity": "sha512-5am6HnnfN+urzt4yfg7IgTbotDjIT/u8AJpEt0sIU9FtXfVeezXAPKswrG+xKUCOYAINpSdgZVDU6QFh+cuH3w==", "dev": true}, "bn.js": {"version": "4.11.8", "resolved": "https://registry.npmjs.org/bn.js/-/bn.js-4.11.8.tgz", "integrity": "sha512-ItfYfPLkWHUjckQCk8xC+LwxgK8NYcXywGigJgSwOP8Y2iyWT4f2vsZnoOXTTbo+o5yXmIUJ4gn5538SO5S3gA==", "dev": true}, "brace-expansion": {"version": "1.1.11", "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz", "integrity": "sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==", "requires": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "braces": {"version": "2.3.2", "resolved": "https://registry.npmjs.org/braces/-/braces-2.3.2.tgz", "integrity": "sha512-aNdbnj9P8PjdXU4ybaWLK2IF3jc/EoDYbC7AazW6to3TRsfXxscC9UXOB5iDiEQrkyIbWp2SLQda4+QAa7nc3w==", "dev": true, "requires": {"arr-flatten": "^1.1.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fill-range": "^4.0.0", "isobject": "^3.0.1", "repeat-element": "^1.1.2", "snapdragon": "^0.8.1", "snapdragon-node": "^2.0.1", "split-string": "^3.0.2", "to-regex": "^3.0.1"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "requires": {"is-extendable": "^0.1.0"}}}}, "brorand": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/brorand/-/brorand-1.1.0.tgz", "integrity": "sha1-EsJe/kCkXjwyPrhnWgoM5XsiNx8=", "dev": true}, "browserify-aes": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/browserify-aes/-/browserify-aes-1.2.0.tgz", "integrity": "sha512-+7CHXqGuspUn/Sl5aO7Ea0xWGAtETPXNSAjHo48JfLdPWcMng33Xe4znFvQweqc/uzk5zSOI3H52CYnjCfb5hA==", "dev": true, "requires": {"buffer-xor": "^1.0.3", "cipher-base": "^1.0.0", "create-hash": "^1.1.0", "evp_bytestokey": "^1.0.3", "inherits": "^2.0.1", "safe-buffer": "^5.0.1"}}, "browserify-cipher": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/browserify-cipher/-/browserify-cipher-1.0.1.tgz", "integrity": "sha512-sPhkz0ARKbf4rRQt2hTpAHqn47X3llLkUGn+xEJzLjwY8LRs2p0v7ljvI5EyoRO/mexrNunNECisZs+gw2zz1w==", "dev": true, "requires": {"browserify-aes": "^1.0.4", "browserify-des": "^1.0.0", "evp_bytestokey": "^1.0.0"}}, "browserify-des": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/browserify-des/-/browserify-des-1.0.2.tgz", "integrity": "sha512-BioO1xf3hFwz4kc6iBhI3ieDFompMhrMlnDFC4/0/vd5MokpuAc3R+LYbwTA9A5Yc9pq9UYPqffKpW2ObuwX5A==", "dev": true, "requires": {"cipher-base": "^1.0.1", "des.js": "^1.0.0", "inherits": "^2.0.1", "safe-buffer": "^5.1.2"}}, "browserify-rsa": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/browserify-rsa/-/browserify-rsa-4.0.1.tgz", "integrity": "sha1-IeCr+vbyApzy+vsTNWenAdQTVSQ=", "dev": true, "requires": {"bn.js": "^4.1.0", "randombytes": "^2.0.1"}}, "browserify-sign": {"version": "4.0.4", "resolved": "https://registry.npmjs.org/browserify-sign/-/browserify-sign-4.0.4.tgz", "integrity": "sha1-qk62jl17ZYuqa/alfmMMvXqT0pg=", "dev": true, "requires": {"bn.js": "^4.1.1", "browserify-rsa": "^4.0.0", "create-hash": "^1.1.0", "create-hmac": "^1.1.2", "elliptic": "^6.0.0", "inherits": "^2.0.1", "parse-asn1": "^5.0.0"}}, "browserify-zlib": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/browserify-zlib/-/browserify-zlib-0.2.0.tgz", "integrity": "sha512-Z942RysHXmJrhqk88FmKBVq/v5tqmSkDz7p54G/MGyjMnCFFnC79XWNbg+Vta8W6Wb2qtSZTSxIGkJrRpCFEiA==", "dev": true, "requires": {"pako": "~1.0.5"}}, "browserslist": {"version": "4.6.3", "resolved": "https://registry.npmjs.org/browserslist/-/browserslist-4.6.3.tgz", "integrity": "sha512-CNBqTCq22RKM8wKJNowcqihHJ4SkI8CGeK7KOR9tPboXUuS5Zk5lQgzzTbs4oxD8x+6HUshZUa2OyNI9lR93bQ==", "dev": true, "requires": {"caniuse-lite": "^1.0.30000975", "electron-to-chromium": "^1.3.164", "node-releases": "^1.1.23"}}, "buffer": {"version": "4.9.1", "resolved": "https://registry.npmjs.org/buffer/-/buffer-4.9.1.tgz", "integrity": "sha1-bRu2AbB6TvztlwlBMgkwJ8lbwpg=", "dev": true, "requires": {"base64-js": "^1.0.2", "ieee754": "^1.1.4", "isarray": "^1.0.0"}}, "buffer-from": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.1.tgz", "integrity": "sha512-MQcXEUbCKtEo7bhqEs6560Hyd4XaovZlO/k9V3hjVUF/zwW7KBVdSK4gIt/bzwS9MbR5qob+F5jusZsb0YQK2A==", "dev": true}, "buffer-xor": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/buffer-xor/-/buffer-xor-1.0.3.tgz", "integrity": "sha1-JuYe0UIvtw3ULm42cp7VHYVf6Nk=", "dev": true}, "builtin-status-codes": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/builtin-status-codes/-/builtin-status-codes-3.0.0.tgz", "integrity": "sha1-hZgoeOIbmOHGZCXgPQF0eI9Wnug=", "dev": true}, "cacache": {"version": "11.3.3", "resolved": "https://registry.npmjs.org/cacache/-/cacache-11.3.3.tgz", "integrity": "sha512-p8WcneCytvzPxhDvYp31PD039vi77I12W+/KfR9S8AZbaiARFBCpsPJS+9uhWfeBfeAtW7o/4vt3MUqLkbY6nA==", "dev": true, "requires": {"bluebird": "^3.5.5", "chownr": "^1.1.1", "figgy-pudding": "^3.5.1", "glob": "^7.1.4", "graceful-fs": "^4.1.15", "lru-cache": "^5.1.1", "mississippi": "^3.0.0", "mkdirp": "^0.5.1", "move-concurrently": "^1.0.1", "promise-inflight": "^1.0.1", "rimraf": "^2.6.3", "ssri": "^6.0.1", "unique-filename": "^1.1.1", "y18n": "^4.0.0"}}, "cache-base": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/cache-base/-/cache-base-1.0.1.tgz", "integrity": "sha512-AKcdTnFSWATd5/GCPRxr2ChwIJ85CeyrEyjRHlKxQ56d4XJMGym0uAiKn0xbLOGOl3+yRpOTi484dVCEc5AUzQ==", "requires": {"collection-visit": "^1.0.0", "component-emitter": "^1.2.1", "get-value": "^2.0.6", "has-value": "^1.0.0", "isobject": "^3.0.1", "set-value": "^2.0.0", "to-object-path": "^0.3.0", "union-value": "^1.0.0", "unset-value": "^1.0.0"}}, "callbag": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/callbag/-/callbag-1.2.0.tgz", "integrity": "sha512-RMcVOk2oyA8CyCt/HsLEvnvvttKlaGVaG0uOo5IY7RpdiPLhStjxv5oYre33VJfT8kJE+cuKLSNHooz7yCUdhQ=="}, "callbag-animation-frames": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/callbag-animation-frames/-/callbag-animation-frames-2.1.0.tgz", "integrity": "sha512-mRvpE47uySluq2ioOVMY1fqS//6bxuiok8hNEMdPwa2JPYBJIQxkdpTpOXuAzyfxd+U1Yysp3I37PrnaH0ziPg==", "requires": {"callbag": "^1.1.0", "callbag-share": "^1.1.1"}}, "callbag-concat": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/callbag-concat/-/callbag-concat-1.2.1.tgz", "integrity": "sha512-iIC4FCUF7sKnt4gDKHNjMRHekebdaA1/udjON/mCLg31EzzTO/dE9nfxpcj+G/63KopFXVQdZKQDsu7UOYGpPg==", "requires": {"callbag": "^1.2.0"}}, "callbag-concat-with": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/callbag-concat-with/-/callbag-concat-with-1.2.0.tgz", "integrity": "sha512-F2BHaSHMdKHsdQ01dtgUzcGWOEzE5MxpneN5TVlcVleVZb3BUnTcIuI40mLdd8xfYAADYhDGk+SaoRsq8VH1Kw==", "requires": {"callbag": "^1.1.0", "callbag-concat": "^1.2.0", "callbag-of": "^2.0.0"}}, "callbag-defer": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/callbag-defer/-/callbag-defer-1.1.0.tgz", "integrity": "sha512-hRW/2CiLZjaKy5IOT1htcZOgwQut+E2gwjVRXDdoLgTrzce9WNK5PUmNhG/oZDEo2DFASiKU3tPBJ6A4iDzjXQ==", "requires": {"callbag": "^1.2.0", "callbag-flatten": "^1.4.0", "callbag-lazy": "^2.0.1"}}, "callbag-duration-progress": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/callbag-duration-progress/-/callbag-duration-progress-1.0.3.tgz", "integrity": "sha512-XN+rdxvQjmMEXkHecZqX6AMN8TqL0Pf/EC0tjXjtRcGHkks7iCk6yzdK6lDjPES4sPBxFyIF80sunu3tKuVb5g==", "requires": {"callbag-concat-with": "^1.1.0", "callbag-map": "^1.0.1", "callbag-ms-elapsed": "^1.1.1", "callbag-take-while": "^1.0.0"}}, "callbag-flatten": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/callbag-flatten/-/callbag-flatten-1.4.0.tgz", "integrity": "sha512-2JM/jA0Ijnuu15kqcdepVd8Iv/L3VytTFOjP0rk/BKc4/yvUUPkQfR3U2QM6Uq+DO/roOWMfrVKaMAxsZ8ciww==", "requires": {"callbag": "^1.1.0"}}, "callbag-for-each": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/callbag-for-each/-/callbag-for-each-1.1.0.tgz", "integrity": "sha512-/FKC/dd3hnw/M1G5mlZrz/+UbtHYPz5PsmmBvpthNb67v6XuQlT93BzxQDxRHAalAjsOci8ZqoYFgmmJxwGhvg==", "requires": {"callbag": "^1.1.0"}}, "callbag-lazy": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/callbag-lazy/-/callbag-lazy-2.0.1.tgz", "integrity": "sha512-NGusLZnRL6F26wxi6ydJl1kPB2zXU+s3f3u8dYW/8SFq9Z0WZVYrkMfnWbvX3hJLMf9I+XSL1RCj504Re1m0Vg==", "requires": {"callbag": "^1.1.0"}}, "callbag-map": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/callbag-map/-/callbag-map-1.1.0.tgz", "integrity": "sha512-/xFzvJCtx1xulZpFeI4wq+ZD7tkg417XOeJ3wS9v8bHLLpt+fHKXgbZ8TBnnIxywJN6Uu/ZhL+c1DzUG6KXYFQ==", "requires": {"callbag": "^1.1.0"}}, "callbag-ms-elapsed": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/callbag-ms-elapsed/-/callbag-ms-elapsed-1.1.1.tgz", "integrity": "sha512-DrCb7LVBcl7gsic4fhiEUOxrWjus6mKtlGd2WWm0SPkAqDo6Baa39fWE1MHlXv14JlT+wn1ejK3nULXEetYOkg==", "requires": {"callbag-animation-frames": "^2.0.1", "callbag-defer": "^1.0.0", "callbag-map": "^1.0.1"}}, "callbag-of": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/callbag-of/-/callbag-of-2.0.0.tgz", "integrity": "sha512-eqOUTQn2P/gzlPlRKo/PY/bVRASDFiFNI2+kfjyd5YHJERET4u6WP4xHNmKJlWcDaFQtaJdigzryu+Ja+W6L9A=="}, "callbag-pipe": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/callbag-pipe/-/callbag-pipe-1.2.0.tgz", "integrity": "sha512-M+LdHUK0qJkm4CRe+I870ZzD/SnzcpTsfJRzFoB2hOkNXlrVdoj002KMkfdS4pHMfV6egzZFd8j+xJu6C4kwEQ=="}, "callbag-share": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/callbag-share/-/callbag-share-1.2.0.tgz", "integrity": "sha512-Gt+D5lfJ/j3X5/3PiMPL8hHDPBmeQILZl2QYu+YJtSFz6PlNGQSL45aD4e4uoxMvhMZY9NpMHh7KRWia9PZA+g==", "requires": {"callbag": "^1.1.0"}}, "callbag-take-while": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/callbag-take-while/-/callbag-take-while-1.0.0.tgz", "integrity": "sha512-OfKid0exYr0i2rMTvZytP/QhNRTerdKhQpwUfHepeyypm7J+I90uRpSfEnjEwsjBd3xghU52xV6f5aOFe9VsTA=="}, "caller-callsite": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/caller-callsite/-/caller-callsite-2.0.0.tgz", "integrity": "sha1-hH4PzgoiN1CpoCfFSzNzGtMVQTQ=", "requires": {"callsites": "^2.0.0"}}, "caller-path": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/caller-path/-/caller-path-2.0.0.tgz", "integrity": "sha1-Ro+DBE42mrIBD6xfBs7uFbsssfQ=", "requires": {"caller-callsite": "^2.0.0"}}, "callsites": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/callsites/-/callsites-2.0.0.tgz", "integrity": "sha1-BuuE8A7qQT2oav/vrL/7Ngk7PFA="}, "camel-case": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/camel-case/-/camel-case-3.0.0.tgz", "integrity": "sha1-yjw2iKTpzzpM2nd9xNy8cTJJz3M=", "requires": {"no-case": "^2.2.0", "upper-case": "^1.1.1"}}, "camelcase": {"version": "5.3.1", "resolved": "https://registry.npmjs.org/camelcase/-/camelcase-5.3.1.tgz", "integrity": "sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg==", "dev": true}, "caniuse-lite": {"version": "1.0.30000978", "resolved": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30000978.tgz", "integrity": "sha512-H6gK6kxUzG6oAwg/Jal279z8pHw0BzrpZfwo/CA9FFm/vA0l8IhDfkZtepyJNE2Y4V6Dp3P3ubz6czby1/Mgsw==", "dev": true}, "chalk": {"version": "2.4.2", "resolved": "https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz", "integrity": "sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==", "dev": true, "requires": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}}, "change-emitter": {"version": "0.1.6", "resolved": "https://registry.npmjs.org/change-emitter/-/change-emitter-0.1.6.tgz", "integrity": "sha1-6LL+PX8at9aaMhma/5HqaTFAlRU="}, "chokidar": {"version": "2.1.6", "resolved": "https://registry.npmjs.org/chokidar/-/chokidar-2.1.6.tgz", "integrity": "sha512-V2jUo67OKkc6ySiRpJrjlpJKl9kDuG+Xb8VgsGzb+aEouhgS1D0weyPU4lEzdAcsCAvrih2J2BqyXqHWvVLw5g==", "dev": true, "requires": {"anymatch": "^2.0.0", "async-each": "^1.0.1", "braces": "^2.3.2", "fsevents": "^1.2.7", "glob-parent": "^3.1.0", "inherits": "^2.0.3", "is-binary-path": "^1.0.0", "is-glob": "^4.0.0", "normalize-path": "^3.0.0", "path-is-absolute": "^1.0.0", "readdirp": "^2.2.1", "upath": "^1.1.1"}}, "chownr": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/chownr/-/chownr-1.1.1.tgz", "integrity": "sha512-j38EvO5+LHX84jlo6h4UzmOwi0UgW61WRyPtJz4qaadK5eY3BTS5TY/S1Stc3Uk2lIM6TPevAlULiEJwie860g==", "dev": true}, "chrome-trace-event": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/chrome-trace-event/-/chrome-trace-event-1.0.2.tgz", "integrity": "sha512-9e/zx1jw7B4CO+c/RXoCsfg/x1AfUBioy4owYH0bJprEYAx5hRFLRhWBqHAG57D0ZM4H7vxbP7bPe0VwhQRYDQ==", "dev": true, "requires": {"tslib": "^1.9.0"}}, "cipher-base": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/cipher-base/-/cipher-base-1.0.4.tgz", "integrity": "sha512-Kkht5ye6ZGmwv40uUDZztayT2ThLQGfnj/T71N/XzeZeo3nf8foyW7zGTsPYkEya3m5f3cAypH+qe7YOrM1U2Q==", "dev": true, "requires": {"inherits": "^2.0.1", "safe-buffer": "^5.0.1"}}, "class-utils": {"version": "0.3.6", "resolved": "https://registry.npmjs.org/class-utils/-/class-utils-0.3.6.tgz", "integrity": "sha512-qOhPa/Fj7s6TY8H8esGu5QNpMMQxz79h+urzrNYN6mn+9BnxlDGf5QZ+XeCDsxSjPqsSR56XOZOJmpeurnLMeg==", "requires": {"arr-union": "^3.1.0", "define-property": "^0.2.5", "isobject": "^3.0.0", "static-extend": "^0.1.1"}, "dependencies": {"define-property": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "requires": {"is-descriptor": "^0.1.0"}}}}, "classnames": {"version": "2.2.6", "resolved": "https://registry.npmjs.org/classnames/-/classnames-2.2.6.tgz", "integrity": "sha512-JR/iSQOSt+LQIWwrwEzJ9uk0xfN3mTVYMwt1Ir5mUcSN6pU+V4zQFFaJsclJbPuAUQH+yfWef6tm7l1quW3C8Q=="}, "cli-cursor": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/cli-cursor/-/cli-cursor-1.0.2.tgz", "integrity": "sha1-ZNo/fValRBLll5S9Ytw1KV6PKYc=", "requires": {"restore-cursor": "^1.0.1"}}, "cli-width": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/cli-width/-/cli-width-1.1.1.tgz", "integrity": "sha1-pNKT72frt7iNSk1CwMzwDE0eNm0="}, "cliui": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/cliui/-/cliui-5.0.0.tgz", "integrity": "sha512-PYeGSEmmHM6zvoef2w8TPzlrnNpXIjTipYK780YswmIP9vjxmd6Y2a3CB2Ks6/AU8NHjZugXvo8w3oWM2qnwXA==", "dev": true, "requires": {"string-width": "^3.1.0", "strip-ansi": "^5.2.0", "wrap-ansi": "^5.1.0"}}, "clone": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/clone/-/clone-1.0.4.tgz", "integrity": "sha1-2jCcwmPfFZlMaIypAheco8fNfH4="}, "clone-buffer": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/clone-buffer/-/clone-buffer-1.0.0.tgz", "integrity": "sha1-4+JbIHrE5wGvch4staFnksrD3Fg="}, "clone-deep": {"version": "0.2.4", "resolved": "https://registry.npmjs.org/clone-deep/-/clone-deep-0.2.4.tgz", "integrity": "sha1-TnPdCen7lxzDhnDF3O2cGJZIHMY=", "requires": {"for-own": "^0.1.3", "is-plain-object": "^2.0.1", "kind-of": "^3.0.2", "lazy-cache": "^1.0.3", "shallow-clone": "^0.1.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}, "lazy-cache": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/lazy-cache/-/lazy-cache-1.0.4.tgz", "integrity": "sha1-odePw6UEdMuAhF07O24dpJpEbo4="}}}, "clone-stats": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/clone-stats/-/clone-stats-0.0.1.tgz", "integrity": "sha1-uI+UqCzzi4eR1YBG6kAprYjKmdE="}, "cloneable-readable": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/cloneable-readable/-/cloneable-readable-1.1.3.tgz", "integrity": "sha512-2EF8zTQOxYq70Y4XKtorQupqF0m49MBz2/yf5Bj+MHjvpG3Hy7sImifnqD6UA+TKYxeSV+u6qqQPawN5UvnpKQ==", "requires": {"inherits": "^2.0.1", "process-nextick-args": "^2.0.0", "readable-stream": "^2.3.5"}}, "co": {"version": "4.6.0", "resolved": "https://registry.npmjs.org/co/-/co-4.6.0.tgz", "integrity": "sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ="}, "code-point-at": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/code-point-at/-/code-point-at-1.1.0.tgz", "integrity": "sha1-DQcLTQQ6W+ozovGkDi7bPZpMz3c="}, "collection-visit": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/collection-visit/-/collection-visit-1.0.0.tgz", "integrity": "sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA=", "requires": {"map-visit": "^1.0.0", "object-visit": "^1.0.0"}}, "color-convert": {"version": "1.9.3", "resolved": "https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz", "integrity": "sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==", "dev": true, "requires": {"color-name": "1.1.3"}}, "color-name": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz", "integrity": "sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=", "dev": true}, "combined-stream": {"version": "1.0.8", "resolved": "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz", "integrity": "sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==", "requires": {"delayed-stream": "~1.0.0"}}, "commander": {"version": "2.20.0", "resolved": "https://registry.npmjs.org/commander/-/commander-2.20.0.tgz", "integrity": "sha512-7j2y+40w61zy6YC2iRNpUe/NwhNyoXrYpHMrSunaMG64nRnaf96zO/KMQR4OyN/UnE5KLyEBnKHd4aG3rskjpQ==", "dev": true}, "common-config": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/common-config/-/common-config-0.1.0.tgz", "integrity": "sha1-0fGnQa+gy/al7wl1K9/C5nfYtO8=", "requires": {"composer": "^0.13.0", "data-store": "^0.16.1", "get-value": "^2.0.6", "lazy-cache": "^2.0.1", "log-utils": "^0.2.0", "object.pick": "^1.1.2", "omit-empty": "^0.4.1", "question-cache": "^0.4.0", "set-value": "^0.3.3", "strip-color": "^0.1.0", "tableize-object": "^0.1.0", "text-table": "^0.2.0", "yargs-parser": "^2.4.0"}, "dependencies": {"async": {"version": "1.5.2", "resolved": "https://registry.npmjs.org/async/-/async-1.5.2.tgz", "integrity": "sha1-7GphrlZIDAw8skHJVhjiCJL5Zyo="}, "camelcase": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/camelcase/-/camelcase-3.0.0.tgz", "integrity": "sha1-MvxLn82vhF/N9+c7uXysImHwqwo="}, "debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}}, "define-property": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "requires": {"is-descriptor": "^0.1.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}, "has-value": {"version": "0.3.1", "resolved": "https://registry.npmjs.org/has-value/-/has-value-0.3.1.tgz", "integrity": "sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=", "requires": {"get-value": "^2.0.3", "has-values": "^0.1.4", "isobject": "^2.0.0"}}, "has-values": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/has-values/-/has-values-0.1.4.tgz", "integrity": "sha1-bWHeldkd/Km5oCCJrThL/49it3E="}, "isobject": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "requires": {"isarray": "1.0.0"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}, "question-cache": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/question-cache/-/question-cache-0.4.0.tgz", "integrity": "sha1-4rmTf8X7fcYPu58QXx+iVLM96n0=", "requires": {"arr-flatten": "^1.0.1", "arr-union": "^3.1.0", "async": "1.5.2", "debug": "^2.2.0", "define-property": "^0.2.5", "get-value": "^2.0.5", "has-value": "^0.3.1", "inquirer2": "^0.1.1", "is-answer": "^0.1.0", "isobject": "^2.0.0", "lazy-cache": "^1.0.3", "mixin-deep": "^1.1.3", "omit-empty": "^0.3.6", "option-cache": "^3.3.5", "os-homedir": "^1.0.1", "project-name": "^0.2.4", "set-value": "^0.3.3", "to-choices": "^0.2.0", "use": "^1.1.2"}, "dependencies": {"lazy-cache": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/lazy-cache/-/lazy-cache-1.0.4.tgz", "integrity": "sha1-odePw6UEdMuAhF07O24dpJpEbo4="}, "omit-empty": {"version": "0.3.6", "resolved": "https://registry.npmjs.org/omit-empty/-/omit-empty-0.3.6.tgz", "integrity": "sha1-bThAXyqmHJEetQT+aIBcVm2FwxY=", "requires": {"has-values": "^0.1.4", "is-date-object": "^1.0.1", "isobject": "^2.0.0", "reduce-object": "^0.1.3"}}}}, "set-value": {"version": "0.3.3", "resolved": "https://registry.npmjs.org/set-value/-/set-value-0.3.3.tgz", "integrity": "sha1-uBIjaBY4oQiP2IpDW4qdMtro2bo=", "requires": {"extend-shallow": "^2.0.1", "isobject": "^2.0.0", "to-object-path": "^0.2.0"}}, "to-object-path": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/to-object-path/-/to-object-path-0.2.0.tgz", "integrity": "sha1-FjThtSqIugDjlJYZ/ACB3Jo7B8o=", "requires": {"arr-flatten": "^1.0.1", "is-arguments": "^1.0.2"}}, "use": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/use/-/use-1.1.2.tgz", "integrity": "sha1-bjgy/rholXNJSsanrLX++zd7LNE=", "requires": {"define-property": "^0.2.5", "isobject": "^2.0.0"}}, "yargs-parser": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-2.4.1.tgz", "integrity": "sha1-hVaN488VD/SfpRgl8DqMiA3cxcQ=", "requires": {"camelcase": "^3.0.0", "lodash.assign": "^4.0.6"}}}}, "commondir": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/commondir/-/commondir-1.0.1.tgz", "integrity": "sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs=", "dev": true}, "component-emitter": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/component-emitter/-/component-emitter-1.3.0.tgz", "integrity": "sha512-Rd3se6QB+sO1TwqZjscQrurpEPIfO0/yYnSin6Q/rD3mOutHvUrCAhJub3r90uNb+SESBuE0QYoB90YdfatsRg=="}, "composer": {"version": "0.13.0", "resolved": "https://registry.npmjs.org/composer/-/composer-0.13.0.tgz", "integrity": "sha1-HbyxXxmpBt7uSanD0TfmVLvG0OI=", "requires": {"array-unique": "^0.2.1", "bach": "^0.5.0", "co": "^4.6.0", "component-emitter": "^1.2.1", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "is-generator": "^1.0.3", "is-glob": "^2.0.1", "isobject": "^2.1.0", "lazy-cache": "^2.0.1", "micromatch": "^2.3.8", "nanoseconds": "^0.1.0"}, "dependencies": {"arr-diff": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/arr-diff/-/arr-diff-2.0.0.tgz", "integrity": "sha1-jzuCf5Vai9ZpaX5KQlasPOrjVs8=", "requires": {"arr-flatten": "^1.0.1"}}, "array-unique": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/array-unique/-/array-unique-0.2.1.tgz", "integrity": "sha1-odl8yvy8JiXMcPrc6zalDFiwGlM="}, "braces": {"version": "1.8.5", "resolved": "https://registry.npmjs.org/braces/-/braces-1.8.5.tgz", "integrity": "sha1-uneWLhLf+WnWt2cR6RS3N4V79qc=", "requires": {"expand-range": "^1.8.1", "preserve": "^0.2.0", "repeat-element": "^1.1.2"}}, "define-property": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "requires": {"is-descriptor": "^0.1.0"}}, "expand-brackets": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/expand-brackets/-/expand-brackets-0.1.5.tgz", "integrity": "sha1-3wcoTjQqgHzXM6xa9yQR5YHRF3s=", "requires": {"is-posix-bracket": "^0.1.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}, "extglob": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/extglob/-/extglob-0.3.2.tgz", "integrity": "sha1-Lhj/PS9JqydlzskCPwEdqo2DSaE=", "requires": {"is-extglob": "^1.0.0"}}, "is-extglob": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-extglob/-/is-extglob-1.0.0.tgz", "integrity": "sha1-rEaBd8SUNAWgkvyPKXYMb/xiBsA="}, "is-glob": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/is-glob/-/is-glob-2.0.1.tgz", "integrity": "sha1-0Jb5JqPe1WAPP9/ZEZjLCIjC2GM=", "requires": {"is-extglob": "^1.0.0"}}, "isobject": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "requires": {"isarray": "1.0.0"}}, "kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}, "micromatch": {"version": "2.3.11", "resolved": "https://registry.npmjs.org/micromatch/-/micromatch-2.3.11.tgz", "integrity": "sha1-hmd8l9FyCzY0MdBNDRUpO9OMFWU=", "requires": {"arr-diff": "^2.0.0", "array-unique": "^0.2.1", "braces": "^1.8.2", "expand-brackets": "^0.1.4", "extglob": "^0.3.1", "filename-regex": "^2.0.0", "is-extglob": "^1.0.0", "is-glob": "^2.0.1", "kind-of": "^3.0.2", "normalize-path": "^2.0.1", "object.omit": "^2.0.0", "parse-glob": "^3.0.4", "regex-cache": "^0.4.2"}}, "normalize-path": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/normalize-path/-/normalize-path-2.1.1.tgz", "integrity": "sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=", "requires": {"remove-trailing-separator": "^1.0.1"}}}}, "concat-map": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz", "integrity": "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s="}, "concat-stream": {"version": "1.6.2", "resolved": "https://registry.npmjs.org/concat-stream/-/concat-stream-1.6.2.tgz", "integrity": "sha512-27HBghJxjiZtIk3Ycvn/4kbJk/1uZuJFfuPEns6LaEvpvG1f0hTea8lilrouyo9mVc2GWdcEZ8OLoGmSADlrCw==", "dev": true, "requires": {"buffer-from": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^2.2.2", "typedarray": "^0.0.6"}}, "console-browserify": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/console-browserify/-/console-browserify-1.1.0.tgz", "integrity": "sha1-8CQcRXMKn8YyOyBtvzjtx0HQuxA=", "dev": true, "requires": {"date-now": "^0.1.4"}}, "constants-browserify": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/constants-browserify/-/constants-browserify-1.0.0.tgz", "integrity": "sha1-wguW2MYXdIqvHBYCF2DNJ/y4y3U=", "dev": true}, "contains-path": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/contains-path/-/contains-path-0.1.0.tgz", "integrity": "sha1-/ozxhP9mcLa67wGp1IYaXL7EEgo="}, "convert-source-map": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/convert-source-map/-/convert-source-map-1.6.0.tgz", "integrity": "sha512-eFu7XigvxdZ1ETfbgPBohgyQ/Z++C0eEhTor0qRwBw9unw+L0/6V8wkSuGgzdThkiS5lSpdptOQPD8Ak40a+7A==", "requires": {"safe-buffer": "~5.1.1"}}, "copy-concurrently": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/copy-concurrently/-/copy-concurrently-1.0.5.tgz", "integrity": "sha512-f2domd9fsVDFtaFcbaRZuYXwtdmnzqbADSwhSWYxYB/Q8zsdUUFMXVRwXGDMWmbEzAn1kdRrtI1T/KTFOL4X2A==", "dev": true, "requires": {"aproba": "^1.1.1", "fs-write-stream-atomic": "^1.0.8", "iferr": "^0.1.5", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "run-queue": "^1.0.0"}}, "copy-descriptor": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/copy-descriptor/-/copy-descriptor-0.1.1.tgz", "integrity": "sha1-Z29us8OZl8LuGsOpJP1hJHSPV40="}, "copy-task": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/copy-task/-/copy-task-0.1.0.tgz", "integrity": "sha1-TDT+muVPKq9gntMvhbj3l6H0arY="}, "core-js": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/core-js/-/core-js-2.6.9.tgz", "integrity": "sha512-HOpZf6eXmnl7la+cUdMnLvUxKNqLUzJvgIziQ0DiF3JwSImNphIqdGqzj6hIKyX04MmV0poclQ7+wjWvxQyR2A==", "dev": true}, "core-js-compat": {"version": "3.1.4", "resolved": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.1.4.tgz", "integrity": "sha512-Z5zbO9f1d0YrJdoaQhphVAnKPimX92D6z8lCGphH89MNRxlL1prI9ExJPqVwP0/kgkQCv8c4GJGT8X16yUncOg==", "dev": true, "requires": {"browserslist": "^4.6.2", "core-js-pure": "3.1.4", "semver": "^6.1.1"}, "dependencies": {"semver": {"version": "6.1.2", "resolved": "https://registry.npmjs.org/semver/-/semver-6.1.2.tgz", "integrity": "sha512-z4PqiCpomGtWj8633oeAdXm1Kn1W++3T8epkZYnwiVgIYIJ0QHszhInYSJTYxebByQH7KVCEAn8R9duzZW2PhQ==", "dev": true}}}, "core-js-pure": {"version": "3.1.4", "resolved": "https://registry.npmjs.org/core-js-pure/-/core-js-pure-3.1.4.tgz", "integrity": "sha512-uJ4Z7iPNwiu1foygbcZYJsJs1jiXrTTCvxfLDXNhI/I+NHbSIEyr548y4fcsCEyWY0XgfAG/qqaunJ1SThHenA==", "dev": true}, "core-util-is": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.2.tgz", "integrity": "sha1-tf1UIgqivFq1eqtxQMlAdUUDwac="}, "cosmiconfig": {"version": "5.2.1", "resolved": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-5.2.1.tgz", "integrity": "sha512-H65gsXo1SKjf8zmrJ67eJk8aIRKV5ff2D4uKZIBZShbhGSpEmsQOPW/SKMKYhSTrqR7ufy6RP69rPogdaPh/kA==", "requires": {"import-fresh": "^2.0.0", "is-directory": "^0.3.1", "js-yaml": "^3.13.1", "parse-json": "^4.0.0"}}, "create-ecdh": {"version": "4.0.3", "resolved": "https://registry.npmjs.org/create-ecdh/-/create-ecdh-4.0.3.tgz", "integrity": "sha512-GbEHQPMOswGpKXM9kCWVrremUcBmjteUaQ01T9rkKCPDXfUHX0IoP9LpHYo2NPFampa4e+/pFDc3jQdxrxQLaw==", "dev": true, "requires": {"bn.js": "^4.1.0", "elliptic": "^6.0.0"}}, "create-emotion": {"version": "9.2.12", "resolved": "https://registry.npmjs.org/create-emotion/-/create-emotion-9.2.12.tgz", "integrity": "sha512-P57uOF9NL2y98Xrbl2OuiDQUZ30GVmASsv5fbsjF4Hlraip2kyAvMm+2PoYUvFFw03Fhgtxk3RqZSm2/qHL9hA==", "requires": {"@emotion/hash": "^0.6.2", "@emotion/memoize": "^0.6.1", "@emotion/stylis": "^0.7.0", "@emotion/unitless": "^0.6.2", "csstype": "^2.5.2", "stylis": "^3.5.0", "stylis-rule-sheet": "^0.0.10"}}, "create-emotion-styled": {"version": "9.2.8", "resolved": "https://registry.npmjs.org/create-emotion-styled/-/create-emotion-styled-9.2.8.tgz", "integrity": "sha512-2LrNM5MREWzI5hZK+LyiBHglwE18WE3AEbBQgpHQ1+zmyLSm/dJsUZBeFAwuIMb+TjNZP0KsMZlV776ufOtFdg==", "requires": {"@emotion/is-prop-valid": "^0.6.1"}}, "create-hash": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/create-hash/-/create-hash-1.2.0.tgz", "integrity": "sha512-z00bCGNHDG8mHAkP7CtT1qVu+bFQUPjYq/4Iv3C3kWjTFV10zIjfSoeqXo9Asws8gwSHDGj/hl2u4OGIjapeCg==", "dev": true, "requires": {"cipher-base": "^1.0.1", "inherits": "^2.0.1", "md5.js": "^1.3.4", "ripemd160": "^2.0.1", "sha.js": "^2.4.0"}}, "create-hmac": {"version": "1.1.7", "resolved": "https://registry.npmjs.org/create-hmac/-/create-hmac-1.1.7.tgz", "integrity": "sha512-MJG9liiZ+ogc4TzUwuvbER1JRdgvUFSB5+VR/g5h82fGaIRWMWddtKBHi7/sVhfjQZ6SehlyhvQYrcYkaUIpLg==", "dev": true, "requires": {"cipher-base": "^1.0.3", "create-hash": "^1.1.0", "inherits": "^2.0.1", "ripemd160": "^2.0.0", "safe-buffer": "^5.0.1", "sha.js": "^2.4.8"}}, "cross-spawn": {"version": "6.0.5", "resolved": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-6.0.5.tgz", "integrity": "sha512-eTVLrBSt7fjbDygz805pMnstIs2VTBNkRm0qxZd+M7A5XDdxVRWO5MxGBXZhjY4cqLYLdtrGqRf8mBPmzwSpWQ==", "dev": true, "requires": {"nice-try": "^1.0.4", "path-key": "^2.0.1", "semver": "^5.5.0", "shebang-command": "^1.2.0", "which": "^1.2.9"}}, "crypto-browserify": {"version": "3.12.0", "resolved": "https://registry.npmjs.org/crypto-browserify/-/crypto-browserify-3.12.0.tgz", "integrity": "sha512-fz4spIh+znjO2VjL+IdhEpRJ3YN6sMzITSBijk6FK2UvTqruSQW+/cCZTSNsMiZNvUeq0CqurF+dAbyiGOY6Wg==", "dev": true, "requires": {"browserify-cipher": "^1.0.0", "browserify-sign": "^4.0.0", "create-ecdh": "^4.0.0", "create-hash": "^1.1.0", "create-hmac": "^1.1.0", "diffie-hellman": "^5.0.0", "inherits": "^2.0.1", "pbkdf2": "^3.0.3", "public-encrypt": "^4.0.0", "randombytes": "^2.0.0", "randomfill": "^1.0.3"}}, "crypto-js": {"version": "3.1.9-1", "resolved": "https://registry.npmjs.org/crypto-js/-/crypto-js-3.1.9-1.tgz", "integrity": "sha1-/aGedh/Ad+Af+/3G6f38WeiAbNg="}, "css-loader": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/css-loader/-/css-loader-3.0.0.tgz", "integrity": "sha512-WR6KZuCkFbnMhRrGPlkwAA7SSCtwqPwpyXJAPhotYkYsc0mKU9n/fu5wufy4jl2WhBw9Ia8gUQMIp/1w98DuPw==", "dev": true, "requires": {"camelcase": "^5.3.1", "cssesc": "^3.0.0", "icss-utils": "^4.1.1", "loader-utils": "^1.2.3", "normalize-path": "^3.0.0", "postcss": "^7.0.17", "postcss-modules-extract-imports": "^2.0.0", "postcss-modules-local-by-default": "^3.0.2", "postcss-modules-scope": "^2.1.0", "postcss-modules-values": "^3.0.0", "postcss-value-parser": "^4.0.0", "schema-utils": "^1.0.0"}}, "cssesc": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/cssesc/-/cssesc-3.0.0.tgz", "integrity": "sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/*****************************/Vg==", "dev": true}, "csstype": {"version": "2.6.5", "resolved": "https://registry.npmjs.org/csstype/-/csstype-2.6.5.tgz", "integrity": "sha512-JsTaiksRsel5n7XwqPAfB0l3TFKdpjW/kgAELf9vrb5adGA7UCPLajKK5s3nFrcFm3Rkyp/Qkgl73ENc1UY3cA=="}, "cwd": {"version": "0.9.1", "resolved": "https://registry.npmjs.org/cwd/-/cwd-0.9.1.tgz", "integrity": "sha1-QeEKfhq4M9xZwuyoOBTH3ne1pP0=", "requires": {"find-pkg": "^0.1.0"}}, "cyclist": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/cyclist/-/cyclist-0.2.2.tgz", "integrity": "sha1-GzN5LhHpFKL9bW7WRHRkRE5fpkA=", "dev": true}, "data-store": {"version": "0.16.1", "resolved": "https://registry.npmjs.org/data-store/-/data-store-0.16.1.tgz", "integrity": "sha1-5pwDpcrBXR/zPwJUyWeDZT5ogwQ=", "requires": {"cache-base": "^0.8.4", "clone-deep": "^0.2.4", "debug": "^2.2.0", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "graceful-fs": "^4.1.4", "has-own-deep": "^0.1.4", "lazy-cache": "^2.0.1", "mkdirp": "^0.5.1", "project-name": "^0.2.5", "resolve-dir": "^0.1.0", "rimraf": "^2.5.3", "union-value": "^0.2.3"}, "dependencies": {"cache-base": {"version": "0.8.5", "resolved": "https://registry.npmjs.org/cache-base/-/cache-base-0.8.5.tgz", "integrity": "sha1-YM6zUEAh7O7HAR/TOEt/TpVym/o=", "requires": {"collection-visit": "^0.2.1", "component-emitter": "^1.2.1", "get-value": "^2.0.5", "has-value": "^0.3.1", "isobject": "^3.0.0", "lazy-cache": "^2.0.1", "set-value": "^0.4.2", "to-object-path": "^0.3.0", "union-value": "^0.2.3", "unset-value": "^0.1.1"}}, "collection-visit": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/collection-visit/-/collection-visit-0.2.3.tgz", "integrity": "sha1-L2JIPK7MlfCDuaRUo+6eYTmteVc=", "requires": {"lazy-cache": "^2.0.1", "map-visit": "^0.1.5", "object-visit": "^0.3.4"}}, "debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}}, "define-property": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "requires": {"is-descriptor": "^0.1.0"}}, "expand-tilde": {"version": "1.2.2", "resolved": "https://registry.npmjs.org/expand-tilde/-/expand-tilde-1.2.2.tgz", "integrity": "sha1-C4HrqJflo9MdHD0QL48BRB5VlEk=", "requires": {"os-homedir": "^1.0.1"}}, "extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}, "global-modules": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/global-modules/-/global-modules-0.2.3.tgz", "integrity": "sha1-6lo77ULG1s6ZWk+KEmm12uIjgo0=", "requires": {"global-prefix": "^0.1.4", "is-windows": "^0.2.0"}}, "global-prefix": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/global-prefix/-/global-prefix-0.1.5.tgz", "integrity": "sha1-jTvGuNo8qBEqFg2NSW/wRiv+948=", "requires": {"homedir-polyfill": "^1.0.0", "ini": "^1.3.4", "is-windows": "^0.2.0", "which": "^1.2.12"}}, "has-value": {"version": "0.3.1", "resolved": "https://registry.npmjs.org/has-value/-/has-value-0.3.1.tgz", "integrity": "sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=", "requires": {"get-value": "^2.0.3", "has-values": "^0.1.4", "isobject": "^2.0.0"}, "dependencies": {"isobject": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "requires": {"isarray": "1.0.0"}}}}, "has-values": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/has-values/-/has-values-0.1.4.tgz", "integrity": "sha1-bWHeldkd/Km5oCCJrThL/49it3E="}, "is-windows": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/is-windows/-/is-windows-0.2.0.tgz", "integrity": "sha1-3hqm1j6indJIc3tp8f+LgALSEIw="}, "map-visit": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/map-visit/-/map-visit-0.1.5.tgz", "integrity": "sha1-2+Q5J85VJbgN/BVzpE1oxR8mgWs=", "requires": {"lazy-cache": "^2.0.1", "object-visit": "^0.3.4"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}, "object-visit": {"version": "0.3.4", "resolved": "https://registry.npmjs.org/object-visit/-/object-visit-0.3.4.tgz", "integrity": "sha1-rhXPhvCy/dVRdxY2RIRSxUw9qCk=", "requires": {"isobject": "^2.0.0"}, "dependencies": {"isobject": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "requires": {"isarray": "1.0.0"}}}}, "resolve-dir": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/resolve-dir/-/resolve-dir-0.1.1.tgz", "integrity": "sha1-shklmlYC+sXFxJatiUpujMQwJh4=", "requires": {"expand-tilde": "^1.2.2", "global-modules": "^0.2.3"}}, "set-value": {"version": "0.4.3", "resolved": "https://registry.npmjs.org/set-value/-/set-value-0.4.3.tgz", "integrity": "sha1-fbCPnT0i3H945Trzw79GZuzfzPE=", "requires": {"extend-shallow": "^2.0.1", "is-extendable": "^0.1.1", "is-plain-object": "^2.0.1", "to-object-path": "^0.3.0"}}, "union-value": {"version": "0.2.4", "resolved": "https://registry.npmjs.org/union-value/-/union-value-0.2.4.tgz", "integrity": "sha1-c3UVJ4ZnkFfns3qmdug0aPwCdPA=", "requires": {"arr-union": "^3.1.0", "get-value": "^2.0.6", "is-extendable": "^0.1.1", "set-value": "^0.4.3"}}, "unset-value": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/unset-value/-/unset-value-0.1.2.tgz", "integrity": "sha1-UGgQuGfyfCpabpsEgzYx9t5Y0xA=", "requires": {"has-value": "^0.3.1", "isobject": "^3.0.0"}}}}, "date-now": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/date-now/-/date-now-0.1.4.tgz", "integrity": "sha1-6vQ5/U1ISK105cx9vvIAZyueNFs=", "dev": true}, "debug": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/debug/-/debug-4.1.1.tgz", "integrity": "sha512-pYA<PERSON>zeRo8J6KPEaJ0VWOh5Pzkbw/RetuzehGM7QRRX5he4fPHx2rdKMB256ehJCkX+XRQm16eZLqLNS8RSZXZw==", "dev": true, "requires": {"ms": "^2.1.1"}}, "decamelize": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/decamelize/-/decamelize-1.2.0.tgz", "integrity": "sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=", "dev": true}, "decode-uri-component": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/decode-uri-component/-/decode-uri-component-0.2.0.tgz", "integrity": "sha1-6zkTMzRYd1y4TNGh+uBiEGu4dUU=", "dev": true}, "deep-bind": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/deep-bind/-/deep-bind-0.3.0.tgz", "integrity": "sha1-lcMd2Eoc0bOBEZosQu25DbSFvDM=", "requires": {"mixin-deep": "^1.1.3"}}, "default-compare": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/default-compare/-/default-compare-1.0.0.tgz", "integrity": "sha512-QWfXlM0EkAbqOCbD/6HjdwT19j7WCkMyiRhWilc4H9/5h/RzTF9gv5LYh1+CmDV5d1rki6KAWLtQale0xt20eQ==", "requires": {"kind-of": "^5.0.2"}, "dependencies": {"kind-of": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-5.1.0.tgz", "integrity": "sha512-NGEErnH6F2vUuXDh+OlbcKW7/wOcfdRHaZ7VWtqCztfHri/++YKmP51OdWeGPuqCOba6kk2OTe5d02VmTB80Pw=="}}}, "defaults-deep": {"version": "0.2.4", "resolved": "https://registry.npmjs.org/defaults-deep/-/defaults-deep-0.2.4.tgz", "integrity": "sha512-V6BtqzcMvn0EPOy7f+SfMhfmTawq+7UQdt9yZH0EBK89+IHo5f+Hse/qzTorAXOBrQpxpwb6cB/8OgtaMrT+Fg==", "requires": {"for-own": "^0.1.3", "is-extendable": "^0.1.1", "lazy-cache": "^0.2.3"}, "dependencies": {"lazy-cache": {"version": "0.2.7", "resolved": "https://registry.npmjs.org/lazy-cache/-/lazy-cache-0.2.7.tgz", "integrity": "sha1-f+3fLctu23fRHvHRF6tf/fCrG2U="}}}, "define-property": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/define-property/-/define-property-2.0.2.tgz", "integrity": "sha512-jwK2UV4cnPpbcG7+VRARKTZPUWowwXA8bzH5NP6ud0oeAxyYPuGZUAC7hMugpCdz4BeSZl2Dl9k66CHJ/46ZYQ==", "dev": true, "requires": {"is-descriptor": "^1.0.2", "isobject": "^3.0.1"}, "dependencies": {"is-accessor-descriptor": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-accessor-descriptor/-/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha512-m5hnHTkcVsPfqx3AKlyttIPb7J+XykHvJP2B9bZDjlhLIoEq4XoK64Vg7boZlVWYK6LUY94dYPEE7Lh0ZkZKcQ==", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz", "integrity": "sha512-jbRXy1FmtAoCjQkVmIVYwuuqDFUbaOeDjmed1tOGPrsMhtJA4rD9tkgA0F1qJ3gRFRXcHYVkdeaP50Q5rE/jLQ==", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/is-descriptor/-/is-descriptor-1.0.2.tgz", "integrity": "sha512-2eis5WqQGV7peooDyLmNEPUrps9+SXX5c9pL3xEB+4e9HnGuDa7mB7kHxHw4CbqS9k1T2hOH3miL8n8WtiYVtg==", "dev": true, "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}}}, "delayed-stream": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz", "integrity": "sha1-3zrhmayt+31ECqrgsp4icrJOxhk="}, "delimiter-regex": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/delimiter-regex/-/delimiter-regex-2.0.0.tgz", "integrity": "sha1-DQ9vYdmRVZH9Qwh6jpWF0+IRWnU=", "requires": {"extend-shallow": "^1.1.2", "isobject": "^2.1.0"}, "dependencies": {"extend-shallow": {"version": "1.1.4", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-1.1.4.tgz", "integrity": "sha1-Gda/lN/AnXa6cR85uHLSH/TdkHE=", "requires": {"kind-of": "^1.1.0"}}, "isobject": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "requires": {"isarray": "1.0.0"}}, "kind-of": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-1.1.0.tgz", "integrity": "sha1-FAo9LUGjbS78+pN3tiwk+ElaXEQ="}}}, "des.js": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/des.js/-/des.js-1.0.0.tgz", "integrity": "sha1-wHTS4qpqipoH29YfmhXCzYPsjsw=", "dev": true, "requires": {"inherits": "^2.0.1", "minimalistic-assert": "^1.0.0"}}, "detect-file": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/detect-file/-/detect-file-1.0.0.tgz", "integrity": "sha1-8NZtA2cqglyxtzvbP+YjEMjlUrc=", "dev": true}, "diffie-hellman": {"version": "5.0.3", "resolved": "https://registry.npmjs.org/diffie-hellman/-/diffie-hellman-5.0.3.tgz", "integrity": "sha512-kqag/Nl+f3GwyK25fhUMYj81BUOrZ9IuJsjIcDE5icNM9FJHAVm3VcUDxdLPoQtTuUylWm6ZIknYJwwaPxsUzg==", "dev": true, "requires": {"bn.js": "^4.1.0", "miller-rabin": "^4.0.0", "randombytes": "^2.0.0"}}, "dom-helpers": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/dom-helpers/-/dom-helpers-3.4.0.tgz", "integrity": "sha512-LnuPJ+dwqKDIyotW1VzmOZ5TONUN7CwkCR5hrgawTUbkBGYdeoNLZo6nNfGkCrjtE1nXXaj7iMMpDa8/d9WoIA==", "requires": {"@babel/runtime": "^7.1.2"}}, "domain-browser": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/domain-browser/-/domain-browser-1.2.0.tgz", "integrity": "sha512-jnjyiM6eRyZl2H+W8Q/zLMA481hzi0eszAaBUzIVnmYVDBbnLxVNnfu1HgEBvCbL+71FrxMl3E6lpKH7Ge3OXA==", "dev": true}, "duplexer": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/duplexer/-/duplexer-0.1.1.tgz", "integrity": "sha1-rOb/gIwc5mtX0ev5eXessCM0z8E="}, "duplexify": {"version": "3.7.1", "resolved": "https://registry.npmjs.org/duplexify/-/duplexify-3.7.1.tgz", "integrity": "sha512-07z8uv2wMyS51kKhD1KsdXJg5WQ6t93RneqRxUHnskXVtlYYkLqM0gqStQZ3pj073g687jPCHrqNfCzawLYh5g==", "requires": {"end-of-stream": "^1.0.0", "inherits": "^2.0.1", "readable-stream": "^2.0.0", "stream-shift": "^1.0.0"}}, "electron-to-chromium": {"version": "1.3.175", "resolved": "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.3.175.tgz", "integrity": "sha512-cQ0o7phcPA0EYkN4juZy/rq4XVxl/1ywQnytDT9hZTC0cHfaOBqWK2XlWp9Mk8xRGntgnmxlHTOux4HLb2ZNnA==", "dev": true}, "elliptic": {"version": "6.5.0", "resolved": "https://registry.npmjs.org/elliptic/-/elliptic-6.5.0.tgz", "integrity": "sha512-eFOJTMyCYb7xtE/caJ6JJu+bhi67WCYNbkGSknu20pmM8Ke/bqOfdnZWxyoGN26JgfxTbXrsCkEw4KheCT/KGg==", "dev": true, "requires": {"bn.js": "^4.4.0", "brorand": "^1.0.1", "hash.js": "^1.0.0", "hmac-drbg": "^1.0.0", "inherits": "^2.0.1", "minimalistic-assert": "^1.0.0", "minimalistic-crypto-utils": "^1.0.0"}}, "emoji-regex": {"version": "7.0.3", "resolved": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-7.0.3.tgz", "integrity": "sha512-CwBLREIQ7LvYFB0WyRvwhq5N5qPhc6PMjD6bYggFlI5YyDgl+0vxq5VHbMOFqLg7hfWzmu8T5Z1QofhmTIhItA==", "dev": true}, "emojis-list": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/emojis-list/-/emojis-list-2.1.0.tgz", "integrity": "sha1-TapNnbAPmBmIDHn6RXrlsJof04k=", "dev": true}, "emotion-theming": {"version": "9.2.9", "resolved": "https://registry.npmjs.org/emotion-theming/-/emotion-theming-9.2.9.tgz", "integrity": "sha512-Ncyr1WocmDDrTbuYAzklIUC5iKiGtHy3e5ymoFXcka6SuvZl/EDMawegk4wVp72Agrcm1xemab3QOHfnOkpoMA==", "requires": {"hoist-non-react-statics": "^2.3.1"}}, "empty-dir": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/empty-dir/-/empty-dir-0.2.1.tgz", "integrity": "sha1-gJ7kih60rRy1EMJXLWb9DthNAas=", "requires": {"fs-exists-sync": "^0.1.0"}}, "en-route": {"version": "0.7.5", "resolved": "https://registry.npmjs.org/en-route/-/en-route-0.7.5.tgz", "integrity": "sha1-6CMOc4NsXpXGdX4EQtPBExJL3Zg=", "requires": {"arr-flatten": "^1.0.1", "debug": "^2.2.0", "extend-shallow": "^2.0.1", "kind-of": "^3.0.2", "lazy-cache": "^1.0.3", "path-to-regexp": "^1.2.1"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}, "kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}, "lazy-cache": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/lazy-cache/-/lazy-cache-1.0.4.tgz", "integrity": "sha1-odePw6UEdMuAhF07O24dpJpEbo4="}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}}}, "encoding": {"version": "0.1.12", "resolved": "https://registry.npmjs.org/encoding/-/encoding-0.1.12.tgz", "integrity": "sha1-U4tm8+5izRq1HsMjgp0flIDHS+s=", "requires": {"iconv-lite": "~0.4.13"}}, "end-of-stream": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/end-of-stream/-/end-of-stream-1.4.1.tgz", "integrity": "sha512-1MkrZNvWTKCaigbn+W15elq2BB/L22nqrSY5DKlo3X6+vclJm8Bb5djXJBmEX6fS3+zCh/F4VBK5Z2KxJt4s2Q==", "requires": {"once": "^1.4.0"}}, "engine": {"version": "0.1.12", "resolved": "https://registry.npmjs.org/engine/-/engine-0.1.12.tgz", "integrity": "sha1-+H6MkLuAzT9YWXrFaVk+5G2idC0=", "requires": {"assign-deep": "^0.4.3", "collection-visit": "^0.2.0", "get-value": "^1.2.1", "kind-of": "^2.0.1", "lazy-cache": "^0.2.3", "object.omit": "^2.0.0", "set-value": "^0.2.0"}, "dependencies": {"collection-visit": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/collection-visit/-/collection-visit-0.2.3.tgz", "integrity": "sha1-L2JIPK7MlfCDuaRUo+6eYTmteVc=", "requires": {"lazy-cache": "^2.0.1", "map-visit": "^0.1.5", "object-visit": "^0.3.4"}, "dependencies": {"lazy-cache": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/lazy-cache/-/lazy-cache-2.0.2.tgz", "integrity": "sha1-uRkKT5EzVGlIQIWfio9whNiCImQ=", "requires": {"set-getter": "^0.1.0"}}}}, "get-value": {"version": "1.3.1", "resolved": "https://registry.npmjs.org/get-value/-/get-value-1.3.1.tgz", "integrity": "sha1-isfvTyA4I5KyZGVI+bmtLcbIlkI=", "requires": {"arr-flatten": "^1.0.1", "is-extendable": "^0.1.1", "lazy-cache": "^0.2.4", "noncharacters": "^1.1.0"}}, "isobject": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "requires": {"isarray": "1.0.0"}}, "kind-of": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-2.0.1.tgz", "integrity": "sha1-AY7HpM5+OobLkUG+UZ0kyPqpgbU=", "requires": {"is-buffer": "^1.0.2"}}, "lazy-cache": {"version": "0.2.7", "resolved": "https://registry.npmjs.org/lazy-cache/-/lazy-cache-0.2.7.tgz", "integrity": "sha1-f+3fLctu23fRHvHRF6tf/fCrG2U="}, "map-visit": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/map-visit/-/map-visit-0.1.5.tgz", "integrity": "sha1-2+Q5J85VJbgN/BVzpE1oxR8mgWs=", "requires": {"lazy-cache": "^2.0.1", "object-visit": "^0.3.4"}, "dependencies": {"lazy-cache": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/lazy-cache/-/lazy-cache-2.0.2.tgz", "integrity": "sha1-uRkKT5EzVGlIQIWfio9whNiCImQ=", "requires": {"set-getter": "^0.1.0"}}}}, "object-visit": {"version": "0.3.4", "resolved": "https://registry.npmjs.org/object-visit/-/object-visit-0.3.4.tgz", "integrity": "sha1-rhXPhvCy/dVRdxY2RIRSxUw9qCk=", "requires": {"isobject": "^2.0.0"}}, "set-value": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/set-value/-/set-value-0.2.0.tgz", "integrity": "sha1-c7CmglwVjGoWqCu9yVd1vyqCX6s=", "requires": {"isobject": "^1.0.0", "noncharacters": "^1.1.0"}, "dependencies": {"isobject": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/isobject/-/isobject-1.0.2.tgz", "integrity": "sha1-8Pm4zpLdVA+gdAiC44NaLgIux4o="}}}}}, "engine-base": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/engine-base/-/engine-base-0.1.3.tgz", "integrity": "sha1-1ZycxS591t0rSa579ftEmU9wFqU=", "requires": {"component-emitter": "^1.2.1", "delimiter-regex": "^2.0.0", "engine": "^0.1.12", "engine-utils": "^0.1.1", "lazy-cache": "^2.0.2", "mixin-deep": "^1.1.3", "object.omit": "^2.0.1", "object.pick": "^1.2.0"}}, "engine-cache": {"version": "0.19.4", "resolved": "https://registry.npmjs.org/engine-cache/-/engine-cache-0.19.4.tgz", "integrity": "sha1-giSWb732pl54Dsed+HtrLLgjlbI=", "requires": {"async-helpers": "^0.3.9", "extend-shallow": "^2.0.1", "helper-cache": "^0.7.2", "isobject": "^3.0.0", "lazy-cache": "^2.0.2", "mixin-deep": "^1.1.3"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}}}, "engine-utils": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/engine-utils/-/engine-utils-0.1.1.tgz", "integrity": "sha1-rd9HCN2FoFoyF6l3l+q4oBPE+A4="}, "enhanced-resolve": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-4.1.0.tgz", "integrity": "sha512-F/7vkyTtyc/llOIn8oWclcB25KdRaiPBpZYDgJHgh/UHtpgT2p2eldQgtQnLtUvfMKPKxbRaQM/hHkvLHt1Vng==", "dev": true, "requires": {"graceful-fs": "^4.1.2", "memory-fs": "^0.4.0", "tapable": "^1.0.0"}}, "errno": {"version": "0.1.7", "resolved": "https://registry.npmjs.org/errno/-/errno-0.1.7.tgz", "integrity": "sha512-MfrRBDWzIWifgq6tJj60gkAwtLNb6sQPlcFrSOflcP1aFmmruKQ2wRnze/8V6kgyz7H3FF8Npzv78mZ7XLLflg==", "dev": true, "requires": {"prr": "~1.0.1"}}, "error-ex": {"version": "1.3.2", "resolved": "https://registry.npmjs.org/error-ex/-/error-ex-1.3.2.tgz", "integrity": "sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==", "requires": {"is-arrayish": "^0.2.1"}}, "error-symbol": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/error-symbol/-/error-symbol-0.1.0.tgz", "integrity": "sha1-Ck2uN9YA0VopukU9jvkg8YRDM/Y="}, "escape-string-regexp": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "integrity": "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ="}, "eslint-scope": {"version": "4.0.3", "resolved": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-4.0.3.tgz", "integrity": "sha512-p7VutNr1O/QrxysMo3E45FjYDTeXBy0iTltPFNSqKAIfjDSXC+4dj+qfyuD8bfAXrW/y6lW3O76VaYNPKfpKrg==", "dev": true, "requires": {"esrecurse": "^4.1.0", "estraverse": "^4.1.1"}}, "esprima": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/esprima/-/esprima-4.0.1.tgz", "integrity": "sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A=="}, "esrecurse": {"version": "4.2.1", "resolved": "https://registry.npmjs.org/esrecurse/-/esrecurse-4.2.1.tgz", "integrity": "sha512-64RBB++fIOAXPw3P9cy89qfMlvZEXZkqqJkjqqXIvzP5ezRZjW+lPWjw35UX/3EhUPFYbg5ER4JYgDw4007/DQ==", "dev": true, "requires": {"estraverse": "^4.1.0"}}, "estraverse": {"version": "4.2.0", "resolved": "https://registry.npmjs.org/estraverse/-/estraverse-4.2.0.tgz", "integrity": "sha1-De4/7TH81GlhjOc0IJn8GvoL2xM=", "dev": true}, "esutils": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/esutils/-/esutils-2.0.2.tgz", "integrity": "sha1-Cr9PHKpbyx96nYrMbepPqqBLrJs="}, "events": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/events/-/events-3.0.0.tgz", "integrity": "sha512-Dc381HFWJzEOhQ+d8pkNon++bk9h6cdAoAj4iE6Q4y6xgTzySWXlKn05/TVNpjnfRqi/X0EpJEJohPjNI3zpVA==", "dev": true}, "evp_bytestokey": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/evp_bytestokey/-/evp_bytestokey-1.0.3.tgz", "integrity": "sha512-/f2Go4TognH/KvCISP7OUsHn85hT9nUkxxA9BEWxFn+Oj9o8ZNLm/40hdlgSLyuOimsrTKLUMEorQexp/aPQeA==", "dev": true, "requires": {"md5.js": "^1.3.4", "safe-buffer": "^5.1.1"}}, "execa": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/execa/-/execa-1.0.0.tgz", "integrity": "sha512-adbxcyWV46qiHyvSp50TKt05tB4tK3HcmF7/nxfAdhnox83seTDbwnaqKO4sXRy7roHAIFqJP/Rw/AuEbX61LA==", "dev": true, "requires": {"cross-spawn": "^6.0.0", "get-stream": "^4.0.0", "is-stream": "^1.1.0", "npm-run-path": "^2.0.0", "p-finally": "^1.0.0", "signal-exit": "^3.0.0", "strip-eof": "^1.0.0"}}, "exit-hook": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/exit-hook/-/exit-hook-1.1.1.tgz", "integrity": "sha1-8FyiM7SMBdVP/wd2XfhQfpXAL/g="}, "expand-args": {"version": "0.4.3", "resolved": "https://registry.npmjs.org/expand-args/-/expand-args-0.4.3.tgz", "integrity": "sha1-OoZiJBxYF1fIzTf7d2d6xgL/nZg=", "requires": {"expand-object": "^0.4.2", "kind-of": "^3.0.3", "lazy-cache": "^2.0.1", "minimist": "^1.2.0", "mixin-deep": "^1.1.3", "omit-empty": "^0.4.1", "set-value": "^0.3.3"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}, "isobject": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "requires": {"isarray": "1.0.0"}}, "kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}, "set-value": {"version": "0.3.3", "resolved": "https://registry.npmjs.org/set-value/-/set-value-0.3.3.tgz", "integrity": "sha1-uBIjaBY4oQiP2IpDW4qdMtro2bo=", "requires": {"extend-shallow": "^2.0.1", "isobject": "^2.0.0", "to-object-path": "^0.2.0"}}, "to-object-path": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/to-object-path/-/to-object-path-0.2.0.tgz", "integrity": "sha1-FjThtSqIugDjlJYZ/ACB3Jo7B8o=", "requires": {"arr-flatten": "^1.0.1", "is-arguments": "^1.0.2"}}}}, "expand-brackets": {"version": "2.1.4", "resolved": "https://registry.npmjs.org/expand-brackets/-/expand-brackets-2.1.4.tgz", "integrity": "sha1-t3c14xXOMPa27/D4OwQVGiJEliI=", "dev": true, "requires": {"debug": "^2.3.3", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "posix-character-classes": "^0.1.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "dev": true, "requires": {"ms": "2.0.0"}}, "define-property": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "dev": true, "requires": {"is-descriptor": "^0.1.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "requires": {"is-extendable": "^0.1.0"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}}}, "expand-object": {"version": "0.4.2", "resolved": "https://registry.npmjs.org/expand-object/-/expand-object-0.4.2.tgz", "integrity": "sha1-t/J+9pwv3MYrD5OQwMtHvAa7Buo=", "requires": {"get-stdin": "^5.0.1", "is-number": "^2.1.0", "minimist": "^1.2.0", "set-value": "^0.3.3"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}, "is-number": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/is-number/-/is-number-2.1.0.tgz", "integrity": "sha1-Afy7s5NGOlSPL0ZszhbezknbkI8=", "requires": {"kind-of": "^3.0.2"}}, "isobject": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "requires": {"isarray": "1.0.0"}}, "kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}, "set-value": {"version": "0.3.3", "resolved": "https://registry.npmjs.org/set-value/-/set-value-0.3.3.tgz", "integrity": "sha1-uBIjaBY4oQiP2IpDW4qdMtro2bo=", "requires": {"extend-shallow": "^2.0.1", "isobject": "^2.0.0", "to-object-path": "^0.2.0"}}, "to-object-path": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/to-object-path/-/to-object-path-0.2.0.tgz", "integrity": "sha1-FjThtSqIugDjlJYZ/ACB3Jo7B8o=", "requires": {"arr-flatten": "^1.0.1", "is-arguments": "^1.0.2"}}}}, "expand-pkg": {"version": "0.1.9", "resolved": "https://registry.npmjs.org/expand-pkg/-/expand-pkg-0.1.9.tgz", "integrity": "sha512-Qqtqzx/e8tODrDr0H8HtO7+nftN0wH9bsk3948KpKBZLrc86Cm3/8mRKJmDfNSDWWcuKsilMmFlKPhYx5gHYuA==", "requires": {"component-emitter": "^1.2.1", "debug": "^2.4.1", "defaults-deep": "^0.2.4", "export-files": "^2.1.1", "get-value": "^2.0.6", "kind-of": "^3.1.0", "lazy-cache": "^2.0.2", "load-pkg": "^3.0.1", "mixin-deep": "^1.1.3", "normalize-pkg": "^0.3.20", "omit-empty": "^0.4.1", "parse-author": "^1.0.0", "parse-git-config": "^1.1.1", "repo-utils": "^0.3.7"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}}, "kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}}}, "expand-range": {"version": "1.8.2", "resolved": "https://registry.npmjs.org/expand-range/-/expand-range-1.8.2.tgz", "integrity": "sha1-opnv/TNf4nIeuujiV+x5ZE/IUzc=", "requires": {"fill-range": "^2.1.0"}, "dependencies": {"fill-range": {"version": "2.2.4", "resolved": "https://registry.npmjs.org/fill-range/-/fill-range-2.2.4.tgz", "integrity": "sha512-cnrcCbj01+j2gTG921VZPnHbjmdAf8oQV/iGeV2kZxGSyfYjjTyY79ErsK1WJWMpw6DaApEX72binqJE+/d+5Q==", "requires": {"is-number": "^2.1.0", "isobject": "^2.0.0", "randomatic": "^3.0.0", "repeat-element": "^1.1.2", "repeat-string": "^1.5.2"}}, "is-number": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/is-number/-/is-number-2.1.0.tgz", "integrity": "sha1-Afy7s5NGOlSPL0ZszhbezknbkI8=", "requires": {"kind-of": "^3.0.2"}}, "isobject": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "requires": {"isarray": "1.0.0"}}, "kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}}}, "expand-tilde": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/expand-tilde/-/expand-tilde-2.0.2.tgz", "integrity": "sha1-l+gBqgUt8CRU3kawK/YhZCzchQI=", "requires": {"homedir-polyfill": "^1.0.1"}}, "export-files": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/export-files/-/export-files-2.1.1.tgz", "integrity": "sha1-u/ZFdAU6CeTrmOX0NQHVcrLDzn8=", "requires": {"lazy-cache": "^1.0.3"}, "dependencies": {"lazy-cache": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/lazy-cache/-/lazy-cache-1.0.4.tgz", "integrity": "sha1-odePw6UEdMuAhF07O24dpJpEbo4="}}}, "extend": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/extend/-/extend-3.0.2.tgz", "integrity": "sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g=="}, "extend-shallow": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-3.0.2.tgz", "integrity": "sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg=", "requires": {"assign-symbols": "^1.0.0", "is-extendable": "^1.0.1"}, "dependencies": {"is-extendable": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/is-extendable/-/is-extendable-1.0.1.tgz", "integrity": "sha512-arnXMxT1hhoKo9k1LZdmlNyJdDDfy2v0fXjFlmok4+i8ul/6WlbVge9bhM74OpNPQPMGUToDtz+KXa1PneJxOA==", "requires": {"is-plain-object": "^2.0.4"}}}}, "extglob": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/extglob/-/extglob-2.0.4.tgz", "integrity": "sha512-Nmb6QXkELsuBr24CJSkilo6UHHgbekK5UiZgfE6UHD3Eb27YC6oD+bhcT+tJ6cl8dmsgdQxnWlcry8ksBIBLpw==", "dev": true, "requires": {"array-unique": "^0.3.2", "define-property": "^1.0.0", "expand-brackets": "^2.1.4", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "dependencies": {"define-property": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/define-property/-/define-property-1.0.0.tgz", "integrity": "sha1-dp66rz9KY6rTr56NMEybvnm/sOY=", "dev": true, "requires": {"is-descriptor": "^1.0.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "requires": {"is-extendable": "^0.1.0"}}, "is-accessor-descriptor": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-accessor-descriptor/-/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha512-m5hnHTkcVsPfqx3AKlyttIPb7J+XykHvJP2B9bZDjlhLIoEq4XoK64Vg7boZlVWYK6LUY94dYPEE7Lh0ZkZKcQ==", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz", "integrity": "sha512-jbRXy1FmtAoCjQkVmIVYwuuqDFUbaOeDjmed1tOGPrsMhtJA4rD9tkgA0F1qJ3gRFRXcHYVkdeaP50Q5rE/jLQ==", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/is-descriptor/-/is-descriptor-1.0.2.tgz", "integrity": "sha512-2eis5WqQGV7peooDyLmNEPUrps9+SXX5c9pL3xEB+4e9HnGuDa7mB7kHxHw4CbqS9k1T2hOH3miL8n8WtiYVtg==", "dev": true, "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}}}, "falsey": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/falsey/-/falsey-0.3.2.tgz", "integrity": "sha512-lxEuefF5MBIVDmE6XeqCdM4BWk1+vYmGZtkbKZ/VFcg6uBBw6fXNEbWmxCjDdQlFc9hy450nkiWwM3VAW6G1qg==", "requires": {"kind-of": "^5.0.2"}, "dependencies": {"kind-of": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-5.1.0.tgz", "integrity": "sha512-NGEErnH6F2vUuXDh+OlbcKW7/wOcfdRHaZ7VWtqCztfHri/++YKmP51OdWeGPuqCOba6kk2OTe5d02VmTB80Pw=="}}}, "fast-deep-equal": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-2.0.1.tgz", "integrity": "sha1-ewUhjd+WZ79/Nwv3/bLLFf3Qqkk=", "dev": true}, "fast-json-stable-stringify": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.0.0.tgz", "integrity": "sha1-1RQsDK7msRifh9OnYREGT4bIu/I=", "dev": true}, "fbjs": {"version": "0.8.17", "resolved": "https://registry.npmjs.org/fbjs/-/fbjs-0.8.17.tgz", "integrity": "sha1-xNWY6taUkRJlPWWIsBpc3Nn5D90=", "requires": {"core-js": "^1.0.0", "isomorphic-fetch": "^2.1.1", "loose-envify": "^1.0.0", "object-assign": "^4.1.0", "promise": "^7.1.1", "setimmediate": "^1.0.5", "ua-parser-js": "^0.7.18"}, "dependencies": {"core-js": {"version": "1.2.7", "resolved": "https://registry.npmjs.org/core-js/-/core-js-1.2.7.tgz", "integrity": "sha1-ZSKUwUZR2yj6k70tX/KYOk8IxjY="}}}, "figgy-pudding": {"version": "3.5.1", "resolved": "https://registry.npmjs.org/figgy-pudding/-/figgy-pudding-3.5.1.tgz", "integrity": "sha512-vNKxJHTEKNThjfrdJwHc7brvM6eVevuO5nTj6ez8ZQ1qbXTvGthucRF7S4vf2cr71QVnT70V34v0S1DyQsti0w==", "dev": true}, "figures": {"version": "1.7.0", "resolved": "https://registry.npmjs.org/figures/-/figures-1.7.0.tgz", "integrity": "sha1-y+Hjr/zxzUS4DK3+0o3Hk6lwHS4=", "requires": {"escape-string-regexp": "^1.0.5", "object-assign": "^4.1.0"}}, "file-contents": {"version": "0.2.4", "resolved": "https://registry.npmjs.org/file-contents/-/file-contents-0.2.4.tgz", "integrity": "sha1-BQb3uO/2KvpFrkXaTfnp1H30U8s=", "requires": {"extend-shallow": "^2.0.0", "file-stat": "^0.1.0", "graceful-fs": "^4.1.2", "is-buffer": "^1.1.0", "is-utf8": "^0.2.0", "lazy-cache": "^0.2.3", "through2": "^2.0.0"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}, "lazy-cache": {"version": "0.2.7", "resolved": "https://registry.npmjs.org/lazy-cache/-/lazy-cache-0.2.7.tgz", "integrity": "sha1-f+3fLctu23fRHvHRF6tf/fCrG2U="}}}, "file-is-binary": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/file-is-binary/-/file-is-binary-1.0.0.tgz", "integrity": "sha1-XkGAbRvK5FjI/sMv484SLbu8Q1Y=", "requires": {"is-binary-buffer": "^1.0.0", "isobject": "^3.0.0"}}, "file-name": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/file-name/-/file-name-0.1.0.tgz", "integrity": "sha1-ErEi8SD5w028F2wauBpUis7W3vc="}, "file-stat": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/file-stat/-/file-stat-0.1.3.tgz", "integrity": "sha1-0PGWHX0QcykoEgpuaVVHHCpbVBE=", "requires": {"graceful-fs": "^4.1.2", "lazy-cache": "^0.2.3", "through2": "^2.0.0"}, "dependencies": {"lazy-cache": {"version": "0.2.7", "resolved": "https://registry.npmjs.org/lazy-cache/-/lazy-cache-0.2.7.tgz", "integrity": "sha1-f+3fLctu23fRHvHRF6tf/fCrG2U="}}}, "filename-regex": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/filename-regex/-/filename-regex-2.0.1.tgz", "integrity": "sha1-wcS5vuPglyXdsQa3XB4wH+LxiyY="}, "fill-range": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/fill-range/-/fill-range-4.0.0.tgz", "integrity": "sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc=", "dev": true, "requires": {"extend-shallow": "^2.0.1", "is-number": "^3.0.0", "repeat-string": "^1.6.1", "to-regex-range": "^2.1.0"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "requires": {"is-extendable": "^0.1.0"}}}}, "find-cache-dir": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/find-cache-dir/-/find-cache-dir-2.1.0.tgz", "integrity": "sha512-Tq6PixE0w/VMFfCgbONnkiQIVol/JJL7nRMi20fqzA4NRs9AfeqMGeRdPi3wIhYkxjeBaWh2rxwapn5Tu3IqOQ==", "dev": true, "requires": {"commondir": "^1.0.1", "make-dir": "^2.0.0", "pkg-dir": "^3.0.0"}}, "find-file-up": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/find-file-up/-/find-file-up-0.1.3.tgz", "integrity": "sha1-z2gJG8+fMApA2kEbN9pczlovvqA=", "requires": {"fs-exists-sync": "^0.1.0", "resolve-dir": "^0.1.0"}, "dependencies": {"expand-tilde": {"version": "1.2.2", "resolved": "https://registry.npmjs.org/expand-tilde/-/expand-tilde-1.2.2.tgz", "integrity": "sha1-C4HrqJflo9MdHD0QL48BRB5VlEk=", "requires": {"os-homedir": "^1.0.1"}}, "global-modules": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/global-modules/-/global-modules-0.2.3.tgz", "integrity": "sha1-6lo77ULG1s6ZWk+KEmm12uIjgo0=", "requires": {"global-prefix": "^0.1.4", "is-windows": "^0.2.0"}}, "global-prefix": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/global-prefix/-/global-prefix-0.1.5.tgz", "integrity": "sha1-jTvGuNo8qBEqFg2NSW/wRiv+948=", "requires": {"homedir-polyfill": "^1.0.0", "ini": "^1.3.4", "is-windows": "^0.2.0", "which": "^1.2.12"}}, "is-windows": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/is-windows/-/is-windows-0.2.0.tgz", "integrity": "sha1-3hqm1j6indJIc3tp8f+LgALSEIw="}, "resolve-dir": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/resolve-dir/-/resolve-dir-0.1.1.tgz", "integrity": "sha1-shklmlYC+sXFxJatiUpujMQwJh4=", "requires": {"expand-tilde": "^1.2.2", "global-modules": "^0.2.3"}}}}, "find-pkg": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/find-pkg/-/find-pkg-0.1.2.tgz", "integrity": "sha1-G9wiwG42NlUy4qJIBGhUuXiNpVc=", "requires": {"find-file-up": "^0.1.2"}}, "find-root": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/find-root/-/find-root-1.1.0.tgz", "integrity": "sha512-NKfW6bec6GfKc0SGx1e07QZY9PE99u0Bft/0rzSD5k3sO/vwkVUpDUKVm5Gpp5Ue3YfShPFTX2070tDs5kB9Ng=="}, "find-up": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/find-up/-/find-up-3.0.0.tgz", "integrity": "sha512-1yD6RmLI1XBfxugvORwlck6f75tYL+iR0jqwsOrOxMZyGYqUuDhJ0l4AXdO1iX/FTs9cBAMEk1gWSEx1kSbylg==", "dev": true, "requires": {"locate-path": "^3.0.0"}}, "findup-sync": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/findup-sync/-/findup-sync-3.0.0.tgz", "integrity": "sha512-<PERSON><PERSON>ffarhcicEhOrm4CtrwdKBdCuz576RLdhJDsIfvNtxUuhdRet1qZcsMjqbePtAseKdAnDyM/IyXbu7PRPRLYg==", "dev": true, "requires": {"detect-file": "^1.0.0", "is-glob": "^4.0.0", "micromatch": "^3.0.4", "resolve-dir": "^1.0.1"}}, "first-chunk-stream": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/first-chunk-stream/-/first-chunk-stream-1.0.0.tgz", "integrity": "sha1-Wb+1DNkF9g18OUzT2ayqtOatk04="}, "flush-write-stream": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/flush-write-stream/-/flush-write-stream-1.1.1.tgz", "integrity": "sha512-3Z4XhFZ3992uIq0XOqb9AreonueSYphE6oYbpt5+3u06JWklbsPkNv3ZKkP9Bz/r+1MWCaMoSQ28P85+1Yc77w==", "dev": true, "requires": {"inherits": "^2.0.3", "readable-stream": "^2.3.6"}}, "for-in": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/for-in/-/for-in-1.0.2.tgz", "integrity": "sha1-gQaNKVqBQuwKxybG4iAMMPttXoA="}, "for-own": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/for-own/-/for-own-0.1.5.tgz", "integrity": "sha1-UmXGgaTylNq78XyVCbZ2OqhFEM4=", "requires": {"for-in": "^1.0.1"}}, "form-data": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/form-data/-/form-data-2.4.0.tgz", "integrity": "sha512-4FinE8RfqYnNim20xDwZZE0V2kOs/AuElIjFUbPuegQSaoZM+vUT5FnwSl10KPugH4voTg1bEQlcbCG9ka75TA==", "requires": {"asynckit": "^0.4.0", "combined-stream": "^1.0.6", "mime-types": "^2.1.12"}}, "fragment-cache": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/fragment-cache/-/fragment-cache-0.2.1.tgz", "integrity": "sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk=", "dev": true, "requires": {"map-cache": "^0.2.2"}}, "from2": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/from2/-/from2-2.3.0.tgz", "integrity": "sha1-i/tVAr3kpNNs/e6gB/zKIdfjgq8=", "dev": true, "requires": {"inherits": "^2.0.1", "readable-stream": "^2.0.0"}}, "fs-exists-sync": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/fs-exists-sync/-/fs-exists-sync-0.1.0.tgz", "integrity": "sha1-mC1ok6+RjnLQjeyehnP/K1qNat0="}, "fs-write-stream-atomic": {"version": "1.0.10", "resolved": "https://registry.npmjs.org/fs-write-stream-atomic/-/fs-write-stream-atomic-1.0.10.tgz", "integrity": "sha1-tH31NJPvkR33VzHnCp3tAYnbQMk=", "dev": true, "requires": {"graceful-fs": "^4.1.2", "iferr": "^0.1.5", "imurmurhash": "^0.1.4", "readable-stream": "1 || 2"}}, "fs.realpath": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz", "integrity": "sha1-FQStJSMVjKpA20onh8sBQRmU6k8="}, "fsevents": {"version": "1.2.9", "resolved": "https://registry.npmjs.org/fsevents/-/fsevents-1.2.9.tgz", "integrity": "sha512-oeyj2H3EjjonWcFjD5NvZNE9Rqe4UW+nQBU2HNeKw0koVLEFIhtyETyAakeAM3de7Z/SW5kcA+fZUait9EApnw==", "dev": true, "optional": true, "requires": {"nan": "^2.12.1", "node-pre-gyp": "^0.12.0"}, "dependencies": {"abbrev": {"version": "1.1.1", "bundled": true, "dev": true, "optional": true}, "ansi-regex": {"version": "2.1.1", "bundled": true, "dev": true, "optional": true}, "aproba": {"version": "1.2.0", "bundled": true, "dev": true, "optional": true}, "are-we-there-yet": {"version": "1.1.5", "bundled": true, "dev": true, "optional": true, "requires": {"delegates": "^1.0.0", "readable-stream": "^2.0.6"}}, "balanced-match": {"version": "1.0.0", "bundled": true, "dev": true, "optional": true}, "brace-expansion": {"version": "1.1.11", "bundled": true, "dev": true, "optional": true, "requires": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "chownr": {"version": "1.1.1", "bundled": true, "dev": true, "optional": true}, "code-point-at": {"version": "1.1.0", "bundled": true, "dev": true, "optional": true}, "concat-map": {"version": "0.0.1", "bundled": true, "dev": true, "optional": true}, "console-control-strings": {"version": "1.1.0", "bundled": true, "dev": true, "optional": true}, "core-util-is": {"version": "1.0.2", "bundled": true, "dev": true, "optional": true}, "debug": {"version": "4.1.1", "bundled": true, "dev": true, "optional": true, "requires": {"ms": "^2.1.1"}}, "deep-extend": {"version": "0.6.0", "bundled": true, "dev": true, "optional": true}, "delegates": {"version": "1.0.0", "bundled": true, "dev": true, "optional": true}, "detect-libc": {"version": "1.0.3", "bundled": true, "dev": true, "optional": true}, "fs-minipass": {"version": "1.2.5", "bundled": true, "dev": true, "optional": true, "requires": {"minipass": "^2.2.1"}}, "fs.realpath": {"version": "1.0.0", "bundled": true, "dev": true, "optional": true}, "gauge": {"version": "2.7.4", "bundled": true, "dev": true, "optional": true, "requires": {"aproba": "^1.0.3", "console-control-strings": "^1.0.0", "has-unicode": "^2.0.0", "object-assign": "^4.1.0", "signal-exit": "^3.0.0", "string-width": "^1.0.1", "strip-ansi": "^3.0.1", "wide-align": "^1.1.0"}}, "glob": {"version": "7.1.3", "bundled": true, "dev": true, "optional": true, "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}, "has-unicode": {"version": "2.0.1", "bundled": true, "dev": true, "optional": true}, "iconv-lite": {"version": "0.4.24", "bundled": true, "dev": true, "optional": true, "requires": {"safer-buffer": ">= 2.1.2 < 3"}}, "ignore-walk": {"version": "3.0.1", "bundled": true, "dev": true, "optional": true, "requires": {"minimatch": "^3.0.4"}}, "inflight": {"version": "1.0.6", "bundled": true, "dev": true, "optional": true, "requires": {"once": "^1.3.0", "wrappy": "1"}}, "inherits": {"version": "2.0.3", "bundled": true, "dev": true, "optional": true}, "ini": {"version": "1.3.5", "bundled": true, "dev": true, "optional": true}, "is-fullwidth-code-point": {"version": "1.0.0", "bundled": true, "dev": true, "optional": true, "requires": {"number-is-nan": "^1.0.0"}}, "isarray": {"version": "1.0.0", "bundled": true, "dev": true, "optional": true}, "minimatch": {"version": "3.0.4", "bundled": true, "dev": true, "optional": true, "requires": {"brace-expansion": "^1.1.7"}}, "minimist": {"version": "0.0.8", "bundled": true, "dev": true, "optional": true}, "minipass": {"version": "2.3.5", "bundled": true, "dev": true, "optional": true, "requires": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}}, "minizlib": {"version": "1.2.1", "bundled": true, "dev": true, "optional": true, "requires": {"minipass": "^2.2.1"}}, "mkdirp": {"version": "0.5.1", "bundled": true, "dev": true, "optional": true, "requires": {"minimist": "0.0.8"}}, "ms": {"version": "2.1.1", "bundled": true, "dev": true, "optional": true}, "needle": {"version": "2.3.0", "bundled": true, "dev": true, "optional": true, "requires": {"debug": "^4.1.0", "iconv-lite": "^0.4.4", "sax": "^1.2.4"}}, "node-pre-gyp": {"version": "0.12.0", "bundled": true, "dev": true, "optional": true, "requires": {"detect-libc": "^1.0.2", "mkdirp": "^0.5.1", "needle": "^2.2.1", "nopt": "^4.0.1", "npm-packlist": "^1.1.6", "npmlog": "^4.0.2", "rc": "^1.2.7", "rimraf": "^2.6.1", "semver": "^5.3.0", "tar": "^4"}}, "nopt": {"version": "4.0.1", "bundled": true, "dev": true, "optional": true, "requires": {"abbrev": "1", "osenv": "^0.1.4"}}, "npm-bundled": {"version": "1.0.6", "bundled": true, "dev": true, "optional": true}, "npm-packlist": {"version": "1.4.1", "bundled": true, "dev": true, "optional": true, "requires": {"ignore-walk": "^3.0.1", "npm-bundled": "^1.0.1"}}, "npmlog": {"version": "4.1.2", "bundled": true, "dev": true, "optional": true, "requires": {"are-we-there-yet": "~1.1.2", "console-control-strings": "~1.1.0", "gauge": "~2.7.3", "set-blocking": "~2.0.0"}}, "number-is-nan": {"version": "1.0.1", "bundled": true, "dev": true, "optional": true}, "object-assign": {"version": "4.1.1", "bundled": true, "dev": true, "optional": true}, "once": {"version": "1.4.0", "bundled": true, "dev": true, "optional": true, "requires": {"wrappy": "1"}}, "os-homedir": {"version": "1.0.2", "bundled": true, "dev": true, "optional": true}, "os-tmpdir": {"version": "1.0.2", "bundled": true, "dev": true, "optional": true}, "osenv": {"version": "0.1.5", "bundled": true, "dev": true, "optional": true, "requires": {"os-homedir": "^1.0.0", "os-tmpdir": "^1.0.0"}}, "path-is-absolute": {"version": "1.0.1", "bundled": true, "dev": true, "optional": true}, "process-nextick-args": {"version": "2.0.0", "bundled": true, "dev": true, "optional": true}, "rc": {"version": "1.2.8", "bundled": true, "dev": true, "optional": true, "requires": {"deep-extend": "^0.6.0", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~2.0.1"}, "dependencies": {"minimist": {"version": "1.2.0", "bundled": true, "dev": true, "optional": true}}}, "readable-stream": {"version": "2.3.6", "bundled": true, "dev": true, "optional": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "rimraf": {"version": "2.6.3", "bundled": true, "dev": true, "optional": true, "requires": {"glob": "^7.1.3"}}, "safe-buffer": {"version": "5.1.2", "bundled": true, "dev": true, "optional": true}, "safer-buffer": {"version": "2.1.2", "bundled": true, "dev": true, "optional": true}, "sax": {"version": "1.2.4", "bundled": true, "dev": true, "optional": true}, "semver": {"version": "5.7.0", "bundled": true, "dev": true, "optional": true}, "set-blocking": {"version": "2.0.0", "bundled": true, "dev": true, "optional": true}, "signal-exit": {"version": "3.0.2", "bundled": true, "dev": true, "optional": true}, "string-width": {"version": "1.0.2", "bundled": true, "dev": true, "optional": true, "requires": {"code-point-at": "^1.0.0", "is-fullwidth-code-point": "^1.0.0", "strip-ansi": "^3.0.0"}}, "string_decoder": {"version": "1.1.1", "bundled": true, "dev": true, "optional": true, "requires": {"safe-buffer": "~5.1.0"}}, "strip-ansi": {"version": "3.0.1", "bundled": true, "dev": true, "optional": true, "requires": {"ansi-regex": "^2.0.0"}}, "strip-json-comments": {"version": "2.0.1", "bundled": true, "dev": true, "optional": true}, "tar": {"version": "4.4.8", "bundled": true, "dev": true, "optional": true, "requires": {"chownr": "^1.1.1", "fs-minipass": "^1.2.5", "minipass": "^2.3.4", "minizlib": "^1.1.1", "mkdirp": "^0.5.0", "safe-buffer": "^5.1.2", "yallist": "^3.0.2"}}, "util-deprecate": {"version": "1.0.2", "bundled": true, "dev": true, "optional": true}, "wide-align": {"version": "1.1.3", "bundled": true, "dev": true, "optional": true, "requires": {"string-width": "^1.0.2 || 2"}}, "wrappy": {"version": "1.0.2", "bundled": true, "dev": true, "optional": true}, "yallist": {"version": "3.0.3", "bundled": true, "dev": true, "optional": true}}}, "get-caller-file": {"version": "2.0.5", "resolved": "https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz", "integrity": "sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==", "dev": true}, "get-stdin": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/get-stdin/-/get-stdin-5.0.1.tgz", "integrity": "sha1-Ei4WFZHiH/TFJTAwVpPyDmOTo5g="}, "get-stream": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/get-stream/-/get-stream-4.1.0.tgz", "integrity": "sha512-GMat4EJ5161kIy2HevLlr4luNjBgvmj413KaQA7jt4V8B4RDsfpHk7WQ9GVqfYyyx8OS/L66Kox+rJRNklLK7w==", "dev": true, "requires": {"pump": "^3.0.0"}}, "get-value": {"version": "2.0.6", "resolved": "https://registry.npmjs.org/get-value/-/get-value-2.0.6.tgz", "integrity": "sha1-3BXKHGcjh8p2vTesCjlbogQqLCg="}, "get-view": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/get-view/-/get-view-0.1.3.tgz", "integrity": "sha1-NmCsBYuhPfl0nKvKpry5bUGqDqA=", "requires": {"isobject": "^3.0.0", "match-file": "^0.2.1"}}, "git-config-path": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/git-config-path/-/git-config-path-1.0.1.tgz", "integrity": "sha1-bTP37WPbDQ4RgTFQO6s6ykfVRmQ=", "requires": {"extend-shallow": "^2.0.1", "fs-exists-sync": "^0.1.0", "homedir-polyfill": "^1.0.0"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}}}, "git-repo-name": {"version": "0.6.0", "resolved": "https://registry.npmjs.org/git-repo-name/-/git-repo-name-0.6.0.tgz", "integrity": "sha1-rwmIRlaqU37GJccIcAgXXNYSKP8=", "requires": {"cwd": "^0.9.1", "file-name": "^0.1.0", "lazy-cache": "^1.0.4", "remote-origin-url": "^0.5.1"}, "dependencies": {"lazy-cache": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/lazy-cache/-/lazy-cache-1.0.4.tgz", "integrity": "sha1-odePw6UEdMuAhF07O24dpJpEbo4="}}}, "glob": {"version": "7.1.4", "resolved": "https://registry.npmjs.org/glob/-/glob-7.1.4.tgz", "integrity": "sha512-hkLPepehmnKk41pUGm3sYxoFs/umurYfYJCerbXEyFIWcAzvpipAgVkBqqT9RBKMGjnq6kMuyYwha6csxbiM1A==", "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}, "glob-base": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/glob-base/-/glob-base-0.3.0.tgz", "integrity": "sha1-27Fk9iIbHAscz4Kuoyi0l98Oo8Q=", "requires": {"glob-parent": "^2.0.0", "is-glob": "^2.0.0"}, "dependencies": {"glob-parent": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/glob-parent/-/glob-parent-2.0.0.tgz", "integrity": "sha1-gTg9ctsFT8zPUzbaqQLxgvbtuyg=", "requires": {"is-glob": "^2.0.0"}}, "is-extglob": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-extglob/-/is-extglob-1.0.0.tgz", "integrity": "sha1-rEaBd8SUNAWgkvyPKXYMb/xiBsA="}, "is-glob": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/is-glob/-/is-glob-2.0.1.tgz", "integrity": "sha1-0Jb5JqPe1WAPP9/ZEZjLCIjC2GM=", "requires": {"is-extglob": "^1.0.0"}}}}, "glob-parent": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/glob-parent/-/glob-parent-3.1.0.tgz", "integrity": "sha1-nmr2KZ2NO9K9QEMIMr0RPfkGxa4=", "requires": {"is-glob": "^3.1.0", "path-dirname": "^1.0.0"}, "dependencies": {"is-glob": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/is-glob/-/is-glob-3.1.0.tgz", "integrity": "sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo=", "requires": {"is-extglob": "^2.1.0"}}}}, "glob-stream": {"version": "5.3.5", "resolved": "https://registry.npmjs.org/glob-stream/-/glob-stream-5.3.5.tgz", "integrity": "sha1-pVZlqajM3EGRWofHAeMtTgFvrSI=", "requires": {"extend": "^3.0.0", "glob": "^5.0.3", "glob-parent": "^3.0.0", "micromatch": "^2.3.7", "ordered-read-streams": "^0.3.0", "through2": "^0.6.0", "to-absolute-glob": "^0.1.1", "unique-stream": "^2.0.2"}, "dependencies": {"arr-diff": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/arr-diff/-/arr-diff-2.0.0.tgz", "integrity": "sha1-jzuCf5Vai9ZpaX5KQlasPOrjVs8=", "requires": {"arr-flatten": "^1.0.1"}}, "array-unique": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/array-unique/-/array-unique-0.2.1.tgz", "integrity": "sha1-odl8yvy8JiXMcPrc6zalDFiwGlM="}, "braces": {"version": "1.8.5", "resolved": "https://registry.npmjs.org/braces/-/braces-1.8.5.tgz", "integrity": "sha1-uneWLhLf+WnWt2cR6RS3N4V79qc=", "requires": {"expand-range": "^1.8.1", "preserve": "^0.2.0", "repeat-element": "^1.1.2"}}, "expand-brackets": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/expand-brackets/-/expand-brackets-0.1.5.tgz", "integrity": "sha1-3wcoTjQqgHzXM6xa9yQR5YHRF3s=", "requires": {"is-posix-bracket": "^0.1.0"}}, "extglob": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/extglob/-/extglob-0.3.2.tgz", "integrity": "sha1-Lhj/PS9JqydlzskCPwEdqo2DSaE=", "requires": {"is-extglob": "^1.0.0"}}, "glob": {"version": "5.0.15", "resolved": "https://registry.npmjs.org/glob/-/glob-5.0.15.tgz", "integrity": "sha1-G8k2ueAvSmA/zCIuz3Yz0wuLk7E=", "requires": {"inflight": "^1.0.4", "inherits": "2", "minimatch": "2 || 3", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}, "is-extglob": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-extglob/-/is-extglob-1.0.0.tgz", "integrity": "sha1-rEaBd8SUNAWgkvyPKXYMb/xiBsA="}, "is-glob": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/is-glob/-/is-glob-2.0.1.tgz", "integrity": "sha1-0Jb5JqPe1WAPP9/ZEZjLCIjC2GM=", "requires": {"is-extglob": "^1.0.0"}}, "isarray": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/isarray/-/isarray-0.0.1.tgz", "integrity": "sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8="}, "kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}, "micromatch": {"version": "2.3.11", "resolved": "https://registry.npmjs.org/micromatch/-/micromatch-2.3.11.tgz", "integrity": "sha1-hmd8l9FyCzY0MdBNDRUpO9OMFWU=", "requires": {"arr-diff": "^2.0.0", "array-unique": "^0.2.1", "braces": "^1.8.2", "expand-brackets": "^0.1.4", "extglob": "^0.3.1", "filename-regex": "^2.0.0", "is-extglob": "^1.0.0", "is-glob": "^2.0.1", "kind-of": "^3.0.2", "normalize-path": "^2.0.1", "object.omit": "^2.0.0", "parse-glob": "^3.0.4", "regex-cache": "^0.4.2"}}, "normalize-path": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/normalize-path/-/normalize-path-2.1.1.tgz", "integrity": "sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=", "requires": {"remove-trailing-separator": "^1.0.1"}}, "readable-stream": {"version": "1.0.34", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.0.34.tgz", "integrity": "sha1-Elgg40vIQtLyqq+v5MKRbuMsFXw=", "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "string_decoder": {"version": "0.10.31", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-0.10.31.tgz", "integrity": "sha1-YuIDvEF2bGwoyfyEMB2rHFMQ+pQ="}, "through2": {"version": "0.6.5", "resolved": "https://registry.npmjs.org/through2/-/through2-0.6.5.tgz", "integrity": "sha1-QaucZ7KdVyCQcUEOHXp6lozTrUg=", "requires": {"readable-stream": ">=1.0.33-1 <1.1.0-0", "xtend": ">=4.0.0 <4.1.0-0"}}}}, "global-modules": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/global-modules/-/global-modules-2.0.0.tgz", "integrity": "sha512-NGbfmJBp9x8IxyJSd1P+otYK8vonoJactOogrVfFRIAEY1ukil8RSKDz2Yo7wh1oihl51l/r6W4epkeKJHqL8A==", "dev": true, "requires": {"global-prefix": "^3.0.0"}, "dependencies": {"global-prefix": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/global-prefix/-/global-prefix-3.0.0.tgz", "integrity": "sha512-awConJSVCHVGND6x3tmMaKcQvwXLhjdkmomy2W+Goaui8YPgYgXJZewhg3fWC+DlfqqQuWg8AwqjGTD2nAPVWg==", "dev": true, "requires": {"ini": "^1.3.5", "kind-of": "^6.0.2", "which": "^1.3.1"}}}}, "global-prefix": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/global-prefix/-/global-prefix-1.0.2.tgz", "integrity": "sha1-2/dDxsFJklk8ZVVoy2btMsASLr4=", "requires": {"expand-tilde": "^2.0.2", "homedir-polyfill": "^1.0.1", "ini": "^1.3.4", "is-windows": "^1.0.1", "which": "^1.2.14"}}, "globals": {"version": "11.12.0", "resolved": "https://registry.npmjs.org/globals/-/globals-11.12.0.tgz", "integrity": "sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==", "dev": true}, "graceful-fs": {"version": "4.2.0", "resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.0.tgz", "integrity": "sha512-jpSvDPV4Cq/bgtpndIWbI5hmYxhQGHPC4d4cqBPb4DLniCfhJokdXhwhaDuLBGLQdvvRum/UiX6ECVIPvDXqdg=="}, "gray-matter": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/gray-matter/-/gray-matter-3.1.1.tgz", "integrity": "sha512-nZ1qjLmayEv0/wt3sHig7I0s3/sJO0dkAaKYQ5YAOApUtYEOonXSFdWvL1khvnZMTvov4UufkqlFsilPnejEXA==", "requires": {"extend-shallow": "^2.0.1", "js-yaml": "^3.10.0", "kind-of": "^5.0.2", "strip-bom-string": "^1.0.0"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}, "kind-of": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-5.1.0.tgz", "integrity": "sha512-NGEErnH6F2vUuXDh+OlbcKW7/wOcfdRHaZ7VWtqCztfHri/++YKmP51OdWeGPuqCOba6kk2OTe5d02VmTB80Pw=="}, "strip-bom-string": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/strip-bom-string/-/strip-bom-string-1.0.0.tgz", "integrity": "sha1-5SEekiQ2n7uB1jOi8ABE3IztrZI="}}}, "group-array": {"version": "0.3.3", "resolved": "https://registry.npmjs.org/group-array/-/group-array-0.3.3.tgz", "integrity": "sha1-u9nS9xjfS+M/D7kEMqrxtDYOSY8=", "requires": {"arr-flatten": "^1.0.1", "for-own": "^0.1.4", "get-value": "^2.0.6", "kind-of": "^3.1.0", "split-string": "^1.0.1", "union-value": "^0.2.3"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}, "kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}, "set-value": {"version": "0.4.3", "resolved": "https://registry.npmjs.org/set-value/-/set-value-0.4.3.tgz", "integrity": "sha1-fbCPnT0i3H945Trzw79GZuzfzPE=", "requires": {"extend-shallow": "^2.0.1", "is-extendable": "^0.1.1", "is-plain-object": "^2.0.1", "to-object-path": "^0.3.0"}}, "split-string": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/split-string/-/split-string-1.0.1.tgz", "integrity": "sha1-vLqz9BUqzuOg1qskecDSh5w9s84=", "requires": {"extend-shallow": "^2.0.1"}}, "union-value": {"version": "0.2.4", "resolved": "https://registry.npmjs.org/union-value/-/union-value-0.2.4.tgz", "integrity": "sha1-c3UVJ4ZnkFfns3qmdug0aPwCdPA=", "requires": {"arr-union": "^3.1.0", "get-value": "^2.0.6", "is-extendable": "^0.1.1", "set-value": "^0.4.3"}}}}, "gulp-choose-files": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/gulp-choose-files/-/gulp-choose-files-0.1.3.tgz", "integrity": "sha1-hrFfBjAHOrZz1XJb7sY+qhSFUPk=", "requires": {"extend-shallow": "^2.0.1", "question-cache": "^0.5.1", "through2": "^2.0.1"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}}}, "gulp-sourcemaps": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/gulp-sourcemaps/-/gulp-sourcemaps-1.6.0.tgz", "integrity": "sha1-uG/zSdgBzrVuHZ59x7vLS33uYAw=", "requires": {"convert-source-map": "^1.1.1", "graceful-fs": "^4.1.2", "strip-bom": "^2.0.0", "through2": "^2.0.0", "vinyl": "^1.0.0"}}, "has-ansi": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/has-ansi/-/has-ansi-2.0.0.tgz", "integrity": "sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE=", "requires": {"ansi-regex": "^2.0.0"}, "dependencies": {"ansi-regex": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-2.1.1.tgz", "integrity": "sha1-w7M6te42DYbg5ijwRorn7yfWVN8="}}}, "has-flag": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz", "integrity": "sha1-tdRU3CGZriJWmfNGfloH87lVuv0=", "dev": true}, "has-glob": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/has-glob/-/has-glob-0.1.1.tgz", "integrity": "sha1-omHEwqbGZ+DHe3AKfyl8Oe86pYk=", "requires": {"is-glob": "^2.0.1"}, "dependencies": {"is-extglob": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-extglob/-/is-extglob-1.0.0.tgz", "integrity": "sha1-rEaBd8SUNAWgkvyPKXYMb/xiBsA="}, "is-glob": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/is-glob/-/is-glob-2.0.1.tgz", "integrity": "sha1-0Jb5JqPe1WAPP9/ZEZjLCIjC2GM=", "requires": {"is-extglob": "^1.0.0"}}}}, "has-own-deep": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/has-own-deep/-/has-own-deep-0.1.4.tgz", "integrity": "sha1-kesM2ieAgxWPgEKigxZDTpr+eHY="}, "has-value": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/has-value/-/has-value-1.0.0.tgz", "integrity": "sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc=", "requires": {"get-value": "^2.0.6", "has-values": "^1.0.0", "isobject": "^3.0.0"}}, "has-values": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/has-values/-/has-values-1.0.0.tgz", "integrity": "sha1-lbC2P+whRmGab+V/51Yo1aOe/k8=", "requires": {"is-number": "^3.0.0", "kind-of": "^4.0.0"}, "dependencies": {"kind-of": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-4.0.0.tgz", "integrity": "sha1-IIE989cSkosgc3hpGkUGb65y3Vc=", "requires": {"is-buffer": "^1.1.5"}}}}, "hash-base": {"version": "3.0.4", "resolved": "https://registry.npmjs.org/hash-base/-/hash-base-3.0.4.tgz", "integrity": "sha1-X8hoaEfs1zSZQDMZprCj8/auSRg=", "dev": true, "requires": {"inherits": "^2.0.1", "safe-buffer": "^5.0.1"}}, "hash.js": {"version": "1.1.7", "resolved": "https://registry.npmjs.org/hash.js/-/hash.js-1.1.7.tgz", "integrity": "sha512-taOaskGt4z4SOANNseOviYDvjEJinIkRgmp7LbKP2YTTmVxWBl87s/uzK9r+44BclBSp2X7K1hqeNfz9JbBeXA==", "dev": true, "requires": {"inherits": "^2.0.3", "minimalistic-assert": "^1.0.1"}}, "helper-cache": {"version": "0.7.2", "resolved": "https://registry.npmjs.org/helper-cache/-/helper-cache-0.7.2.tgz", "integrity": "sha1-AkVixLS4sqsqtTHQC+FuxJZRi5A=", "requires": {"extend-shallow": "^2.0.1", "lazy-cache": "^0.2.3", "lodash.bind": "^3.1.0"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}, "lazy-cache": {"version": "0.2.7", "resolved": "https://registry.npmjs.org/lazy-cache/-/lazy-cache-0.2.7.tgz", "integrity": "sha1-f+3fLctu23fRHvHRF6tf/fCrG2U="}}}, "hmac-drbg": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/hmac-drbg/-/hmac-drbg-1.0.1.tgz", "integrity": "sha1-0nRXAQJabHdabFRXk+1QL8DGSaE=", "dev": true, "requires": {"hash.js": "^1.0.3", "minimalistic-assert": "^1.0.0", "minimalistic-crypto-utils": "^1.0.1"}}, "hoist-non-react-statics": {"version": "2.5.5", "resolved": "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-2.5.5.tgz", "integrity": "sha512-rqcy4pJo55FTTLWt+bU8ukscqHeE/e9KWvsOW2b/a3afxQZhwkQdT1rPPCJ0rYXdj4vNcasY8zHTH+jF/qStxw=="}, "homedir-polyfill": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/homedir-polyfill/-/homedir-polyfill-1.0.3.tgz", "integrity": "sha512-eSmmWE5bZTK2Nou4g0AI3zZ9rswp7GRKoKXS1BLUkvPviOqs4YTN1djQIqrXy9k5gEtdLPy86JjRwsNM9tnDcA==", "requires": {"parse-passwd": "^1.0.0"}}, "https-browserify": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/https-browserify/-/https-browserify-1.0.0.tgz", "integrity": "sha1-7AbBDgo0wPL68Zn3/X/Hj//QPHM=", "dev": true}, "iconv-lite": {"version": "0.4.24", "resolved": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.24.tgz", "integrity": "sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==", "requires": {"safer-buffer": ">= 2.1.2 < 3"}}, "icss-utils": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/icss-utils/-/icss-utils-4.1.1.tgz", "integrity": "sha512-4aFq7wvWyMHKgxsH8QQtGpvbASCf+eM3wPRLI6R+MgAnTCZ6STYsRvttLvRWK0Nfif5piF394St3HeJDaljGPA==", "dev": true, "requires": {"postcss": "^7.0.14"}}, "ieee754": {"version": "1.1.13", "resolved": "https://registry.npmjs.org/ieee754/-/ieee754-1.1.13.tgz", "integrity": "sha512-4vf7I2LYV/HaWerSo3XmlMkp5eZ83i+/CDluXi/IGTs/O1sejBNhTtnxzmRZfvOUqj7lZjqHkeTvpgSFDlWZTg==", "dev": true}, "iferr": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/iferr/-/iferr-0.1.5.tgz", "integrity": "sha1-xg7taebY/bazEEofy8ocGS3FtQE=", "dev": true}, "import-fresh": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/import-fresh/-/import-fresh-2.0.0.tgz", "integrity": "sha1-2BNVwVYS04bGH53dOSLUMEgipUY=", "requires": {"caller-path": "^2.0.0", "resolve-from": "^3.0.0"}}, "import-local": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/import-local/-/import-local-2.0.0.tgz", "integrity": "sha512-b6s04m3O+s3CGSbqDIyP4R6aAwAeYlVq9+WUWep6iHa8ETRf9yei1U48C5MmfJmV9AiLYYBKPMq/W+/WRpQmCQ==", "dev": true, "requires": {"pkg-dir": "^3.0.0", "resolve-cwd": "^2.0.0"}}, "imurmurhash": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz", "integrity": "sha1-khi5srkoojixPcT7a21XbyMUU+o=", "dev": true}, "indexes-of": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/indexes-of/-/indexes-of-1.0.1.tgz", "integrity": "sha1-8w9xbI4r00bHtn0985FVZqfAVgc=", "dev": true}, "inflection": {"version": "1.12.0", "resolved": "https://registry.npmjs.org/inflection/-/inflection-1.12.0.tgz", "integrity": "sha1-ogCTVlbW9fa8TcdQLhrstwMihBY="}, "inflight": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz", "integrity": "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=", "requires": {"once": "^1.3.0", "wrappy": "1"}}, "info-symbol": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/info-symbol/-/info-symbol-0.1.0.tgz", "integrity": "sha1-J4QdcoZ920JCzWEtecEGM4gcang="}, "inherits": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "integrity": "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ=="}, "ini": {"version": "1.3.5", "resolved": "https://registry.npmjs.org/ini/-/ini-1.3.5.tgz", "integrity": "sha512-RZY5huIKCMRWDUqZlEi72f/lmXKMvuszcMBduliQ3nnWbx9X/ZBQO7DijMEYS9EhHBb2qacRUMtC7svLwe0lcw=="}, "inquirer2": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/inquirer2/-/inquirer2-0.1.1.tgz", "integrity": "sha1-vFQkqBQ1fEHmXi6Vf+U2ruqb8fY=", "requires": {"ansi-escapes": "^1.1.1", "ansi-regex": "^2.0.0", "arr-flatten": "^1.0.1", "arr-pluck": "^0.1.0", "array-unique": "^0.2.1", "chalk": "^1.1.1", "cli-cursor": "^1.0.2", "cli-width": "^1.1.0", "extend-shallow": "^2.0.1", "figures": "^1.4.0", "is-number": "^2.1.0", "is-plain-object": "^2.0.1", "lazy-cache": "^1.0.3", "lodash.where": "^3.1.0", "readline2": "^1.0.1", "run-async": "^0.1.0", "rx-lite": "^4.0.7", "strip-color": "^0.1.0", "through2": "^2.0.0"}, "dependencies": {"ansi-regex": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-2.1.1.tgz", "integrity": "sha1-w7M6te42DYbg5ijwRorn7yfWVN8="}, "ansi-styles": {"version": "2.2.1", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-2.2.1.tgz", "integrity": "sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4="}, "array-unique": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/array-unique/-/array-unique-0.2.1.tgz", "integrity": "sha1-odl8yvy8JiXMcPrc6zalDFiwGlM="}, "chalk": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/chalk/-/chalk-1.1.3.tgz", "integrity": "sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg=", "requires": {"ansi-styles": "^2.2.1", "escape-string-regexp": "^1.0.2", "has-ansi": "^2.0.0", "strip-ansi": "^3.0.0", "supports-color": "^2.0.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}, "is-number": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/is-number/-/is-number-2.1.0.tgz", "integrity": "sha1-Afy7s5NGOlSPL0ZszhbezknbkI8=", "requires": {"kind-of": "^3.0.2"}}, "kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}, "lazy-cache": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/lazy-cache/-/lazy-cache-1.0.4.tgz", "integrity": "sha1-odePw6UEdMuAhF07O24dpJpEbo4="}, "strip-ansi": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-3.0.1.tgz", "integrity": "sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=", "requires": {"ansi-regex": "^2.0.0"}}, "supports-color": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-2.0.0.tgz", "integrity": "sha1-U10EXOa2Nj+kARcIRimZXp3zJMc="}}}, "interpret": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/interpret/-/interpret-1.2.0.tgz", "integrity": "sha512-mT34yGKMNceBQUoVn7iCDKDntA7SC6gycMAWzGx1z/CMCTV7b2AAtXlo3nRyHZ1FelRkQbQjprHSYGwzLtkVbw==", "dev": true}, "invariant": {"version": "2.2.4", "resolved": "https://registry.npmjs.org/invariant/-/invariant-2.2.4.tgz", "integrity": "sha512-phJfQVBuaJM5raOpJjSfkiD6BpbCE4Ns//LaXl6wGYtUBY83nWS6Rf9tXm2e8VaK60JEjYldbPif/A2B1C2gNA==", "dev": true, "requires": {"loose-envify": "^1.0.0"}}, "invert-kv": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/invert-kv/-/invert-kv-2.0.0.tgz", "integrity": "sha512-wPVv/y/QQ/Uiirj/vh3oP+1Ww+AWehmi1g5fFWGPF6IpCBCDVrhgHRMvrLfdYcwDh3QJbGXDW4JAuzxElLSqKA==", "dev": true}, "is-absolute": {"version": "0.2.6", "resolved": "https://registry.npmjs.org/is-absolute/-/is-absolute-0.2.6.tgz", "integrity": "sha1-IN5p89uULvLYe5wto28XIjWxtes=", "requires": {"is-relative": "^0.2.1", "is-windows": "^0.2.0"}, "dependencies": {"is-windows": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/is-windows/-/is-windows-0.2.0.tgz", "integrity": "sha1-3hqm1j6indJIc3tp8f+LgALSEIw="}}}, "is-accessor-descriptor": {"version": "0.1.6", "resolved": "https://registry.npmjs.org/is-accessor-descriptor/-/is-accessor-descriptor-0.1.6.tgz", "integrity": "sha1-qeEss66Nh2cn7u84Q/igiXtcmNY=", "requires": {"kind-of": "^3.0.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}}}, "is-answer": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/is-answer/-/is-answer-0.1.1.tgz", "integrity": "sha1-zBwvGG+FzyZQIgveNZ2GIYfUnLY=", "requires": {"has-values": "^0.1.4", "is-primitive": "^2.0.0", "omit-empty": "^0.4.1"}, "dependencies": {"has-values": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/has-values/-/has-values-0.1.4.tgz", "integrity": "sha1-bWHeldkd/Km5oCCJrThL/49it3E="}}}, "is-arguments": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/is-arguments/-/is-arguments-1.0.4.tgz", "integrity": "sha512-xPh0Rmt8NE65sNzvyUmWgI1tz3mKq74lGA0mL8LYZcoIzKOzDh6HmrYm3d18k60nHerC8A9Km8kYu87zfSFnLA=="}, "is-arrayish": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.2.1.tgz", "integrity": "sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0="}, "is-binary-buffer": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-binary-buffer/-/is-binary-buffer-1.0.0.tgz", "integrity": "sha1-vGAxKQtly/eZudlQK1D9U3VSQAc=", "requires": {"is-buffer": "^1.1.5"}}, "is-binary-path": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/is-binary-path/-/is-binary-path-1.0.1.tgz", "integrity": "sha1-dfFmQrSA8YenEcgUFh/TpKdlWJg=", "dev": true, "requires": {"binary-extensions": "^1.0.0"}}, "is-buffer": {"version": "1.1.6", "resolved": "https://registry.npmjs.org/is-buffer/-/is-buffer-1.1.6.tgz", "integrity": "sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w=="}, "is-data-descriptor": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/is-data-descriptor/-/is-data-descriptor-0.1.4.tgz", "integrity": "sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y=", "requires": {"kind-of": "^3.0.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}}}, "is-date-object": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/is-date-object/-/is-date-object-1.0.1.tgz", "integrity": "sha1-mqIOtq7rv/d/vTPnTKAbM1gdOhY="}, "is-descriptor": {"version": "0.1.6", "resolved": "https://registry.npmjs.org/is-descriptor/-/is-descriptor-0.1.6.tgz", "integrity": "sha512-avDYr0SB3DwO9zsMov0gKCESFYqCnE4hq/4z3TdUlukEy5t9C0YRq7HLrsN52NAcqXKaepeCD0n+B0arnVG3Hg==", "requires": {"is-accessor-descriptor": "^0.1.6", "is-data-descriptor": "^0.1.4", "kind-of": "^5.0.0"}, "dependencies": {"kind-of": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-5.1.0.tgz", "integrity": "sha512-NGEErnH6F2vUuXDh+OlbcKW7/wOcfdRHaZ7VWtqCztfHri/++YKmP51OdWeGPuqCOba6kk2OTe5d02VmTB80Pw=="}}}, "is-directory": {"version": "0.3.1", "resolved": "https://registry.npmjs.org/is-directory/-/is-directory-0.3.1.tgz", "integrity": "sha1-YTObbyR1/Hcv2cnYP1yFddwVSuE="}, "is-dotfile": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/is-dotfile/-/is-dotfile-1.0.3.tgz", "integrity": "sha1-pqLzL/0t+wT1yiXs0Pa4PPeYoeE="}, "is-equal-shallow": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/is-equal-shallow/-/is-equal-shallow-0.1.3.tgz", "integrity": "sha1-IjgJj8Ih3gvPpdnqxMRdY4qhxTQ=", "requires": {"is-primitive": "^2.0.0"}}, "is-extendable": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/is-extendable/-/is-extendable-0.1.1.tgz", "integrity": "sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik="}, "is-extglob": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz", "integrity": "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI="}, "is-fullwidth-code-point": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-2.0.0.tgz", "integrity": "sha1-o7MKXE8ZkYMWeqq5O+764937ZU8=", "dev": true}, "is-generator": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/is-generator/-/is-generator-1.0.3.tgz", "integrity": "sha1-wUwhBX7TbjKNuANHlmxpP4hjifM="}, "is-glob": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/is-glob/-/is-glob-4.0.1.tgz", "integrity": "sha512-5G0tKtBTFImOqDnLB2hG6Bp2qcKEFduo4tZu9MT/H6NQv/ghhy30o55ufafxJ/LdH79LLs2Kfrn85TLKyA7BUg==", "dev": true, "requires": {"is-extglob": "^2.1.1"}}, "is-number": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/is-number/-/is-number-3.0.0.tgz", "integrity": "sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU=", "requires": {"kind-of": "^3.0.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}}}, "is-plain-object": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/is-plain-object/-/is-plain-object-2.0.4.tgz", "integrity": "sha512-h5PpgXkWitc38BBMYawTYMWJHFZJVnBquFE57xFpjB8pJFiF6gZ+bU+WyI/yqXiFR5mdLsgYNaPe8uao6Uv9Og==", "requires": {"isobject": "^3.0.1"}}, "is-posix-bracket": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/is-posix-bracket/-/is-posix-bracket-0.1.1.tgz", "integrity": "sha1-MzTceXdDaOkvAW5vvAqI9c1ua8Q="}, "is-primitive": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/is-primitive/-/is-primitive-2.0.0.tgz", "integrity": "sha1-IHurkWOEmcB7Kt8kCkGochADRXU="}, "is-registered": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/is-registered/-/is-registered-0.1.5.tgz", "integrity": "sha1-HTRpd0GdZl4qxshAE1NWheb3b38=", "requires": {"define-property": "^0.2.5", "isobject": "^2.1.0"}, "dependencies": {"define-property": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "requires": {"is-descriptor": "^0.1.0"}}, "isobject": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "requires": {"isarray": "1.0.0"}}}}, "is-relative": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/is-relative/-/is-relative-0.2.1.tgz", "integrity": "sha1-0n9MfVFtF1+2ENuEu+7yPDvJeqU=", "requires": {"is-unc-path": "^0.1.1"}}, "is-stream": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/is-stream/-/is-stream-1.1.0.tgz", "integrity": "sha1-EtSj3U5o4Lec6428hBc66A2RykQ="}, "is-unc-path": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/is-unc-path/-/is-unc-path-0.1.2.tgz", "integrity": "sha1-arBTpyVzwQJQ/0FqOBTDUXivObk=", "requires": {"unc-path-regex": "^0.1.0"}}, "is-utf8": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/is-utf8/-/is-utf8-0.2.1.tgz", "integrity": "sha1-Sw2hRCEE0bM2NA6AeX6GXPOffXI="}, "is-valid-app": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/is-valid-app/-/is-valid-app-0.2.1.tgz", "integrity": "sha1-Zc8ZW71xvXdssWGZHGhCSNZd/4k=", "requires": {"debug": "^2.2.0", "is-registered": "^0.1.5", "is-valid-instance": "^0.2.0", "lazy-cache": "^2.0.1"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}}}, "is-valid-glob": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/is-valid-glob/-/is-valid-glob-0.3.0.tgz", "integrity": "sha1-1LVcafUYhvm2XHDWwmItN+KfSP4="}, "is-valid-instance": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/is-valid-instance/-/is-valid-instance-0.2.0.tgz", "integrity": "sha1-4an/EQa4y64AB+pqIPidVGoqWg8=", "requires": {"isobject": "^2.1.0", "pascalcase": "^0.1.1"}, "dependencies": {"isobject": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "requires": {"isarray": "1.0.0"}}}}, "is-whitespace": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/is-whitespace/-/is-whitespace-0.3.0.tgz", "integrity": "sha1-Fjnssb4DauxppUy7QBz77XEUq38="}, "is-windows": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/is-windows/-/is-windows-1.0.2.tgz", "integrity": "sha512-eXK1UInq2bPmjyX6e3VHIzMLobc4J94i4AWn+Hpq3OU5KkrRC96OAcR3PRJ/pGu6m8TRnBHP9dkXQVsT/COVIA=="}, "is-wsl": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/is-wsl/-/is-wsl-1.1.0.tgz", "integrity": "sha1-HxbkqiKwTRM2tmGIpmrzxgDDpm0=", "dev": true}, "isarray": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE="}, "isexe": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz", "integrity": "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA="}, "iso8601-duration": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/iso8601-duration/-/iso8601-duration-1.2.0.tgz", "integrity": "sha512-ErTBd++b17E8nmWII1K1uZtBgD1E8RjyvwmxlCjPHNqHMD7gmcMHOw0E8Ro/6+QT4PhHRSnnMo7bxa1vFPkwhg=="}, "isobject": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/isobject/-/isobject-3.0.1.tgz", "integrity": "sha1-TkMekrEalzFjaqH5yNHMvP2reN8="}, "isomorphic-fetch": {"version": "2.2.1", "resolved": "https://registry.npmjs.org/isomorphic-fetch/-/isomorphic-fetch-2.2.1.tgz", "integrity": "sha1-YRrhrPFPXoH3KVB0coGf6XM1WKk=", "requires": {"node-fetch": "^1.0.1", "whatwg-fetch": ">=0.10.0"}}, "isomorphic-form-data": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/isomorphic-form-data/-/isomorphic-form-data-2.0.0.tgz", "integrity": "sha512-TYgVnXWeESVmQSg4GLVbalmQ+B4NPi/H4eWxqALKj63KsUrcu301YDjBqaOw3h+cbak7Na4Xyps3BiptHtxTfg==", "requires": {"form-data": "^2.3.2"}}, "javascript-state-machine": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/javascript-state-machine/-/javascript-state-machine-3.1.0.tgz", "integrity": "sha512-BwhYxQ1OPenBPXC735RgfB+ZUG8H3kjsx8hrYTgWnoy6TPipEy4fiicyhT2lxRKAXq9pG7CfFT8a2HLr6Hmwxg=="}, "js-levenshtein": {"version": "1.1.6", "resolved": "https://registry.npmjs.org/js-levenshtein/-/js-levenshtein-1.1.6.tgz", "integrity": "sha512-X2BB11YZtrRqY4EnQcLX5Rh373zbK4alC1FW7D7MBhL2gtcC17cTnr6DmfHZeS0s2rTHjUTMMHfG7gO8SSdw+g==", "dev": true}, "js-tokens": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz", "integrity": "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ=="}, "js-yaml": {"version": "3.13.1", "resolved": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.13.1.tgz", "integrity": "sha512-YfbcO7jXDdyj0DGxYVSlSeQNHbD7XPWvrVWeVUujrQEoZzWJIRrCPoyk6kL6IAjAG2IolMK4T0hNUe0HOUs5Jw==", "requires": {"argparse": "^1.0.7", "esprima": "^4.0.0"}}, "jsesc": {"version": "2.5.2", "resolved": "https://registry.npmjs.org/jsesc/-/jsesc-2.5.2.tgz", "integrity": "sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA==", "dev": true}, "json-parse-better-errors": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/json-parse-better-errors/-/json-parse-better-errors-1.0.2.tgz", "integrity": "sha512-mrqyZKfX5EhL7hvqcV6WG1yYjnjeuYDzDhhcAAUrq8Po85NBQBJP+ZDUT75qZQ98IkUoBqdkExkukOU7Ts2wrw=="}, "json-schema-traverse": {"version": "0.4.1", "resolved": "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz", "integrity": "sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==", "dev": true}, "json-stable-stringify-without-jsonify": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz", "integrity": "sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE="}, "json5": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/json5/-/json5-2.1.0.tgz", "integrity": "sha512-8Mh9h6xViijj36g7Dxi+Y4S6hNGV96vcJZr/SrlHh1LR/pEn/8j/+qIBbs44YKl69Lrfctp4QD+AdWLTMqEZAQ==", "dev": true, "requires": {"minimist": "^1.2.0"}}, "kind-of": {"version": "6.0.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-6.0.2.tgz", "integrity": "sha512-s5kLOcnH0XqDO+FvuaLX8DDjZ18CGFk7VygH40QoKPUQhW4e2rvM0rwUq0t8IQDOwYSeLK01U90OjzBTme2QqA=="}, "layouts": {"version": "0.11.0", "resolved": "https://registry.npmjs.org/layouts/-/layouts-0.11.0.tgz", "integrity": "sha1-xiDos8uI/IxJLbRTin3VQKTffyI=", "requires": {"delimiter-regex": "^1.3.1", "falsey": "^0.3.0", "get-view": "^0.1.1", "lazy-cache": "^1.0.3"}, "dependencies": {"delimiter-regex": {"version": "1.3.1", "resolved": "https://registry.npmjs.org/delimiter-regex/-/delimiter-regex-1.3.1.tgz", "integrity": "sha1-Y4XK4UAE28DBzY3//+uGPVGZnv8=", "requires": {"extend-shallow": "^1.1.2"}}, "extend-shallow": {"version": "1.1.4", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-1.1.4.tgz", "integrity": "sha1-Gda/lN/AnXa6cR85uHLSH/TdkHE=", "requires": {"kind-of": "^1.1.0"}}, "kind-of": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-1.1.0.tgz", "integrity": "sha1-FAo9LUGjbS78+pN3tiwk+ElaXEQ="}, "lazy-cache": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/lazy-cache/-/lazy-cache-1.0.4.tgz", "integrity": "sha1-odePw6UEdMuAhF07O24dpJpEbo4="}}}, "lazy-cache": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/lazy-cache/-/lazy-cache-2.0.2.tgz", "integrity": "sha1-uRkKT5EzVGlIQIWfio9whNiCImQ=", "requires": {"set-getter": "^0.1.0"}}, "lazystream": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/lazystream/-/lazystream-1.0.0.tgz", "integrity": "sha1-9plf4PggOS9hOWvolGJAe7dxaOQ=", "requires": {"readable-stream": "^2.0.5"}}, "lcid": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/lcid/-/lcid-2.0.0.tgz", "integrity": "sha512-avPEb8P8EGnwXKClwsNUgryVjllcRqtMYa49NTsbQagYuT1DcXnl1915oxWjoyGrXR6zH/Y0Zc96xWsPcoDKeA==", "dev": true, "requires": {"invert-kv": "^2.0.0"}}, "load-helpers": {"version": "0.2.11", "resolved": "https://registry.npmjs.org/load-helpers/-/load-helpers-0.2.11.tgz", "integrity": "sha1-9L2LIYQ1wFLl4536dxMinVcepCM=", "requires": {"extend-shallow": "^2.0.1", "is-valid-glob": "^0.3.0", "lazy-cache": "^2.0.1", "matched": "^0.4.1", "resolve-dir": "^0.1.0"}, "dependencies": {"expand-tilde": {"version": "1.2.2", "resolved": "https://registry.npmjs.org/expand-tilde/-/expand-tilde-1.2.2.tgz", "integrity": "sha1-C4HrqJflo9MdHD0QL48BRB5VlEk=", "requires": {"os-homedir": "^1.0.1"}}, "extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}, "global-modules": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/global-modules/-/global-modules-0.2.3.tgz", "integrity": "sha1-6lo77ULG1s6ZWk+KEmm12uIjgo0=", "requires": {"global-prefix": "^0.1.4", "is-windows": "^0.2.0"}}, "global-prefix": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/global-prefix/-/global-prefix-0.1.5.tgz", "integrity": "sha1-jTvGuNo8qBEqFg2NSW/wRiv+948=", "requires": {"homedir-polyfill": "^1.0.0", "ini": "^1.3.4", "is-windows": "^0.2.0", "which": "^1.2.12"}}, "is-windows": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/is-windows/-/is-windows-0.2.0.tgz", "integrity": "sha1-3hqm1j6indJIc3tp8f+LgALSEIw="}, "matched": {"version": "0.4.4", "resolved": "https://registry.npmjs.org/matched/-/matched-0.4.4.tgz", "integrity": "sha1-Vte36xgDPwz5vFLrIJD6x9weifo=", "requires": {"arr-union": "^3.1.0", "async-array-reduce": "^0.2.0", "extend-shallow": "^2.0.1", "fs-exists-sync": "^0.1.0", "glob": "^7.0.5", "has-glob": "^0.1.1", "is-valid-glob": "^0.3.0", "lazy-cache": "^2.0.1", "resolve-dir": "^0.1.0"}}, "resolve-dir": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/resolve-dir/-/resolve-dir-0.1.1.tgz", "integrity": "sha1-shklmlYC+sXFxJatiUpujMQwJh4=", "requires": {"expand-tilde": "^1.2.2", "global-modules": "^0.2.3"}}}}, "load-pkg": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/load-pkg/-/load-pkg-3.0.1.tgz", "integrity": "sha1-kjCzfsBOVpADBgvFiVHj7VCNWU8=", "requires": {"find-pkg": "^0.1.0"}}, "load-templates": {"version": "0.11.4", "resolved": "https://registry.npmjs.org/load-templates/-/load-templates-0.11.4.tgz", "integrity": "sha1-zyk977a1hg/1uMRJ2qHAx7tyjek=", "requires": {"define-property": "^0.2.5", "extend-shallow": "^2.0.1", "glob-parent": "^2.0.0", "has-glob": "^0.1.1", "is-valid-glob": "^0.3.0", "lazy-cache": "^2.0.1", "matched": "^0.4.1", "to-file": "^0.2.0"}, "dependencies": {"define-property": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "requires": {"is-descriptor": "^0.1.0"}}, "expand-tilde": {"version": "1.2.2", "resolved": "https://registry.npmjs.org/expand-tilde/-/expand-tilde-1.2.2.tgz", "integrity": "sha1-C4HrqJflo9MdHD0QL48BRB5VlEk=", "requires": {"os-homedir": "^1.0.1"}}, "extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}, "glob-parent": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/glob-parent/-/glob-parent-2.0.0.tgz", "integrity": "sha1-gTg9ctsFT8zPUzbaqQLxgvbtuyg=", "requires": {"is-glob": "^2.0.0"}}, "global-modules": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/global-modules/-/global-modules-0.2.3.tgz", "integrity": "sha1-6lo77ULG1s6ZWk+KEmm12uIjgo0=", "requires": {"global-prefix": "^0.1.4", "is-windows": "^0.2.0"}}, "global-prefix": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/global-prefix/-/global-prefix-0.1.5.tgz", "integrity": "sha1-jTvGuNo8qBEqFg2NSW/wRiv+948=", "requires": {"homedir-polyfill": "^1.0.0", "ini": "^1.3.4", "is-windows": "^0.2.0", "which": "^1.2.12"}}, "is-extglob": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-extglob/-/is-extglob-1.0.0.tgz", "integrity": "sha1-rEaBd8SUNAWgkvyPKXYMb/xiBsA="}, "is-glob": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/is-glob/-/is-glob-2.0.1.tgz", "integrity": "sha1-0Jb5JqPe1WAPP9/ZEZjLCIjC2GM=", "requires": {"is-extglob": "^1.0.0"}}, "is-windows": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/is-windows/-/is-windows-0.2.0.tgz", "integrity": "sha1-3hqm1j6indJIc3tp8f+LgALSEIw="}, "matched": {"version": "0.4.4", "resolved": "https://registry.npmjs.org/matched/-/matched-0.4.4.tgz", "integrity": "sha1-Vte36xgDPwz5vFLrIJD6x9weifo=", "requires": {"arr-union": "^3.1.0", "async-array-reduce": "^0.2.0", "extend-shallow": "^2.0.1", "fs-exists-sync": "^0.1.0", "glob": "^7.0.5", "has-glob": "^0.1.1", "is-valid-glob": "^0.3.0", "lazy-cache": "^2.0.1", "resolve-dir": "^0.1.0"}}, "resolve-dir": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/resolve-dir/-/resolve-dir-0.1.1.tgz", "integrity": "sha1-shklmlYC+sXFxJatiUpujMQwJh4=", "requires": {"expand-tilde": "^1.2.2", "global-modules": "^0.2.3"}}}}, "loader-runner": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/loader-runner/-/loader-runner-2.4.0.tgz", "integrity": "sha512-Jsmr89RcXGIwivFY21FcRrisYZfvLMTWx5kOLc+JTxtpBOG6xML0vzbc6SEQG2FO9/4Fc3wW4LVcB5DmGflaRw==", "dev": true}, "loader-utils": {"version": "1.2.3", "resolved": "https://registry.npmjs.org/loader-utils/-/loader-utils-1.2.3.tgz", "integrity": "sha512-fkpz8ejdnEMG3s37wGL07iSBDg99O9D5yflE9RGNH3hRdx9SOwYfnGYdZOUIZitN8E+E2vkq3MUMYMvPYl5ZZA==", "dev": true, "requires": {"big.js": "^5.2.2", "emojis-list": "^2.0.0", "json5": "^1.0.1"}, "dependencies": {"json5": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/json5/-/json5-1.0.1.tgz", "integrity": "sha512-aKS4WQjPenRxiQsC93MNfjx+nbF4PAdYzmd/1JIj8HYzqfbu86beTuNgXDzPknWk0n0uARlyewZo4s++ES36Ow==", "dev": true, "requires": {"minimist": "^1.2.0"}}}}, "locate-path": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/locate-path/-/locate-path-3.0.0.tgz", "integrity": "sha512-7AO748wWnIhNqAuaty2ZWHkQHRSNfPVIsPIfwEOWO22AmaoVrWavlOcMR5nzTLNYvp36X220/maaRsrec1G65A==", "dev": true, "requires": {"p-locate": "^3.0.0", "path-exists": "^3.0.0"}}, "lodash": {"version": "4.17.11", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.17.11.tgz", "integrity": "sha512-cQKh8igo5QUhZ7lg38DYWAxMvjSAKG0A8wGSVimP07SIUEK2UO+arSRKbRZWtelMtN5V0Hkwh5ryOto/SshYIg=="}, "lodash._arrayfilter": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/lodash._arrayfilter/-/lodash._arrayfilter-3.0.0.tgz", "integrity": "sha1-LevhHuxp5dzG9LhhNxKKSPFSQjc="}, "lodash._basecallback": {"version": "3.3.1", "resolved": "https://registry.npmjs.org/lodash._basecallback/-/lodash._basecallback-3.3.1.tgz", "integrity": "sha1-t7K7Q9whYEJKIczybFfkQ3cqjic=", "requires": {"lodash._baseisequal": "^3.0.0", "lodash._bindcallback": "^3.0.0", "lodash.isarray": "^3.0.0", "lodash.pairs": "^3.0.0"}}, "lodash._baseeach": {"version": "3.0.4", "resolved": "https://registry.npmjs.org/lodash._baseeach/-/lodash._baseeach-3.0.4.tgz", "integrity": "sha1-z4cGVyyhROjZ11InyZDamC+TKvM=", "requires": {"lodash.keys": "^3.0.0"}}, "lodash._basefilter": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/lodash._basefilter/-/lodash._basefilter-3.0.0.tgz", "integrity": "sha1-S3ZAPfDihtA9Xg9yle00QeEB0SE=", "requires": {"lodash._baseeach": "^3.0.0"}}, "lodash._baseisequal": {"version": "3.0.7", "resolved": "https://registry.npmjs.org/lodash._baseisequal/-/lodash._baseisequal-3.0.7.tgz", "integrity": "sha1-2AJfdjOdKTQnZ9zIh85cuVpbUfE=", "requires": {"lodash.isarray": "^3.0.0", "lodash.istypedarray": "^3.0.0", "lodash.keys": "^3.0.0"}}, "lodash._baseismatch": {"version": "3.1.3", "resolved": "https://registry.npmjs.org/lodash._baseismatch/-/lodash._baseismatch-3.1.3.tgz", "integrity": "sha1-Byj8SO+hFpnT1fLXMEnyqxPED9U=", "requires": {"lodash._baseisequal": "^3.0.0"}}, "lodash._basematches": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/lodash._basematches/-/lodash._basematches-3.2.0.tgz", "integrity": "sha1-9H4D8H7CB4SrCWjQy2y1l+IQEVg=", "requires": {"lodash._baseismatch": "^3.0.0", "lodash.pairs": "^3.0.0"}}, "lodash._bindcallback": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/lodash._bindcallback/-/lodash._bindcallback-3.0.1.tgz", "integrity": "sha1-5THCdkTPi1epnhftlbNcdIeJOS4="}, "lodash._createwrapper": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/lodash._createwrapper/-/lodash._createwrapper-3.2.0.tgz", "integrity": "sha1-30U+ZkFjIXuJWkVAZa8cR6DqPE0=", "requires": {"lodash._root": "^3.0.0"}}, "lodash._getnative": {"version": "3.9.1", "resolved": "https://registry.npmjs.org/lodash._getnative/-/lodash._getnative-3.9.1.tgz", "integrity": "sha1-VwvH3t5G1hzc3mh9ZdPuy6o6r/U="}, "lodash._replaceholders": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/lodash._replaceholders/-/lodash._replaceholders-3.0.0.tgz", "integrity": "sha1-iru3EmxDH37XRPe6rznwi8m9nVg="}, "lodash._root": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/lodash._root/-/lodash._root-3.0.1.tgz", "integrity": "sha1-+6HEUkwZ7ppfgTa0YJ8BfPTe1pI="}, "lodash.assign": {"version": "4.2.0", "resolved": "https://registry.npmjs.org/lodash.assign/-/lodash.assign-4.2.0.tgz", "integrity": "sha1-DZnzzNem0mHRm9rrkkUAXShYCOc="}, "lodash.bind": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/lodash.bind/-/lodash.bind-3.1.0.tgz", "integrity": "sha1-+V9IY419i7tYVPkIJmUnmZ+/pLs=", "requires": {"lodash._createwrapper": "^3.0.0", "lodash._replaceholders": "^3.0.0", "lodash.restparam": "^3.0.0"}}, "lodash.filter": {"version": "4.6.0", "resolved": "https://registry.npmjs.org/lodash.filter/-/lodash.filter-4.6.0.tgz", "integrity": "sha1-ZosdSYFgOuHMWm+nYBQ+SAtMSs4="}, "lodash.flatten": {"version": "4.4.0", "resolved": "https://registry.npmjs.org/lodash.flatten/-/lodash.flatten-4.4.0.tgz", "integrity": "sha1-8xwiIlqWMtK7+OSt2+8kCqdlph8="}, "lodash.foreach": {"version": "4.5.0", "resolved": "https://registry.npmjs.org/lodash.foreach/-/lodash.foreach-4.5.0.tgz", "integrity": "sha1-Gmo16s5AEoDH8G3d7DUWWrJ+PlM="}, "lodash.initial": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/lodash.initial/-/lodash.initial-4.1.1.tgz", "integrity": "sha1-5T9kiRJl3cQE6YbSwo93vtlDWRo="}, "lodash.isarguments": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/lodash.isarguments/-/lodash.isarguments-3.1.0.tgz", "integrity": "sha1-L1c9hcaiQon/AGY7SRwdM4/zRYo="}, "lodash.isarray": {"version": "3.0.4", "resolved": "https://registry.npmjs.org/lodash.isarray/-/lodash.isarray-3.0.4.tgz", "integrity": "sha1-eeTriMNqgSKvhvhEqpvNhRtfu1U="}, "lodash.isequal": {"version": "4.5.0", "resolved": "https://registry.npmjs.org/lodash.isequal/-/lodash.isequal-4.5.0.tgz", "integrity": "sha1-QVxEePK8wwEgwizhDtMib30+GOA="}, "lodash.istypedarray": {"version": "3.0.6", "resolved": "https://registry.npmjs.org/lodash.istypedarray/-/lodash.istypedarray-3.0.6.tgz", "integrity": "sha1-yaR3SYYHUB2OhJTSg7h8OSgc72I="}, "lodash.keys": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/lodash.keys/-/lodash.keys-3.1.2.tgz", "integrity": "sha1-TbwEcrFWvlCgsoaFXRvQsMZWCYo=", "requires": {"lodash._getnative": "^3.0.0", "lodash.isarguments": "^3.0.0", "lodash.isarray": "^3.0.0"}}, "lodash.last": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/lodash.last/-/lodash.last-3.0.0.tgz", "integrity": "sha1-JC9mMRLdTG5jcoxgo8kJ0b2tvUw="}, "lodash.map": {"version": "4.6.0", "resolved": "https://registry.npmjs.org/lodash.map/-/lodash.map-4.6.0.tgz", "integrity": "sha1-dx7Hg540c9nEzeKLGTlMNWL09tM="}, "lodash.pairs": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/lodash.pairs/-/lodash.pairs-3.0.1.tgz", "integrity": "sha1-u+CNV4bu6qCaFckevw3LfSvjJqk=", "requires": {"lodash.keys": "^3.0.0"}}, "lodash.restparam": {"version": "3.6.1", "resolved": "https://registry.npmjs.org/lodash.restparam/-/lodash.restparam-3.6.1.tgz", "integrity": "sha1-k2pOMJ7zMKdkXtQUWYbIWuWyCAU="}, "lodash.where": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/lodash.where/-/lodash.where-3.1.0.tgz", "integrity": "sha1-LnhLnJM2jV11qu4zLOF2Ai8rlVM=", "requires": {"lodash._arrayfilter": "^3.0.0", "lodash._basecallback": "^3.0.0", "lodash._basefilter": "^3.0.0", "lodash._basematches": "^3.0.0", "lodash.isarray": "^3.0.0"}}, "log-ok": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/log-ok/-/log-ok-0.1.1.tgz", "integrity": "sha1-vqPdNqzQuKckDXhza1uXxlREozQ=", "requires": {"ansi-green": "^0.1.1", "success-symbol": "^0.1.0"}}, "log-utils": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/log-utils/-/log-utils-0.2.1.tgz", "integrity": "sha1-pMIXoN2aUFFdm5ICBgkas9TgMc8=", "requires": {"ansi-colors": "^0.2.0", "error-symbol": "^0.1.0", "info-symbol": "^0.1.0", "log-ok": "^0.1.1", "success-symbol": "^0.1.0", "time-stamp": "^1.0.1", "warning-symbol": "^0.1.0"}}, "loglevel": {"version": "1.6.3", "resolved": "https://registry.npmjs.org/loglevel/-/loglevel-1.6.3.tgz", "integrity": "sha512-LoEDv5pgpvWgPF4kNYuIp0qqSJVWak/dML0RY74xlzMZiT9w77teNAwKYKWBTYjlokMirg+o3jBwp+vlLrcfAA=="}, "longest": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/longest/-/longest-1.0.1.tgz", "integrity": "sha1-MKCy2jj3N3DoKUoNIuZiXtd9AJc="}, "loose-envify": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz", "integrity": "sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==", "requires": {"js-tokens": "^3.0.0 || ^4.0.0"}}, "lower-case": {"version": "1.1.4", "resolved": "https://registry.npmjs.org/lower-case/-/lower-case-1.1.4.tgz", "integrity": "sha1-miyr0bno4K6ZOkv31YdcOcQujqw="}, "lru-cache": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz", "integrity": "sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==", "dev": true, "requires": {"yallist": "^3.0.2"}}, "make-dir": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/make-dir/-/make-dir-2.1.0.tgz", "integrity": "sha512-LS9X+dc8KLxXCb8dni79fLIIUA5VyZoyjSMCwTluaXA0o27cCK0bhXkpgw+sTXVpPy/lSO57ilRixqk0vDmtRA==", "dev": true, "requires": {"pify": "^4.0.1", "semver": "^5.6.0"}}, "make-iterator": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/make-iterator/-/make-iterator-1.0.1.tgz", "integrity": "sha512-pxiuXh0iVEq7VM7KMIhs5gxsfxCux2URptUQaXo4iZZJxBAzTPOLE2BumO5dbfVYq/hBJFBR/a1mFDmOx5AGmw==", "requires": {"kind-of": "^6.0.2"}}, "mamacro": {"version": "0.0.3", "resolved": "https://registry.npmjs.org/mamacro/-/mamacro-0.0.3.tgz", "integrity": "sha512-qMEwh+UujcQ+kbz3T6V+wAmO2U8veoq2w+3wY8MquqwVA3jChfwY+Tk52GZKDfACEPjuZ7r2oJLejwpt8jtwTA==", "dev": true}, "map-age-cleaner": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/map-age-cleaner/-/map-age-cleaner-0.1.3.tgz", "integrity": "sha512-bJzx6nMoP6PDLPBFmg7+xRKeFZvFboMrGlxmNj9ClvX53KrmvM5bXFXEWjbz4cz1AFn+jWJ9z/DJSz7hrs0w3w==", "dev": true, "requires": {"p-defer": "^1.0.0"}}, "map-cache": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/map-cache/-/map-cache-0.2.2.tgz", "integrity": "sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8=", "dev": true}, "map-config": {"version": "0.5.0", "resolved": "https://registry.npmjs.org/map-config/-/map-config-0.5.0.tgz", "integrity": "sha1-FwJgfiZ696NwyKnQxiumUk/rb+U=", "requires": {"array-unique": "^0.2.1", "async": "^1.5.2"}, "dependencies": {"array-unique": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/array-unique/-/array-unique-0.2.1.tgz", "integrity": "sha1-odl8yvy8JiXMcPrc6zalDFiwGlM="}, "async": {"version": "1.5.2", "resolved": "https://registry.npmjs.org/async/-/async-1.5.2.tgz", "integrity": "sha1-7GphrlZIDAw8skHJVhjiCJL5Zyo="}}}, "map-schema": {"version": "0.2.4", "resolved": "https://registry.npmjs.org/map-schema/-/map-schema-0.2.4.tgz", "integrity": "sha1-wZVRg0/DwHoEWXt6WvtEpHWvlbQ=", "requires": {"arr-union": "^3.1.0", "collection-visit": "^0.2.3", "component-emitter": "^1.2.1", "debug": "^2.6.0", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "get-value": "^2.0.6", "is-primitive": "^2.0.0", "kind-of": "^3.1.0", "lazy-cache": "^2.0.2", "log-utils": "^0.2.1", "longest": "^1.0.1", "mixin-deep": "^1.1.3", "object.omit": "^2.0.1", "object.pick": "^1.2.0", "omit-empty": "^0.4.1", "pad-right": "^0.2.2", "set-value": "^0.4.0", "sort-object-arrays": "^0.1.1", "union-value": "^0.2.3"}, "dependencies": {"collection-visit": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/collection-visit/-/collection-visit-0.2.3.tgz", "integrity": "sha1-L2JIPK7MlfCDuaRUo+6eYTmteVc=", "requires": {"lazy-cache": "^2.0.1", "map-visit": "^0.1.5", "object-visit": "^0.3.4"}}, "debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}}, "define-property": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "requires": {"is-descriptor": "^0.1.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}, "isobject": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "requires": {"isarray": "1.0.0"}}, "kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}, "map-visit": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/map-visit/-/map-visit-0.1.5.tgz", "integrity": "sha1-2+Q5J85VJbgN/BVzpE1oxR8mgWs=", "requires": {"lazy-cache": "^2.0.1", "object-visit": "^0.3.4"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}, "object-visit": {"version": "0.3.4", "resolved": "https://registry.npmjs.org/object-visit/-/object-visit-0.3.4.tgz", "integrity": "sha1-rhXPhvCy/dVRdxY2RIRSxUw9qCk=", "requires": {"isobject": "^2.0.0"}}, "set-value": {"version": "0.4.3", "resolved": "https://registry.npmjs.org/set-value/-/set-value-0.4.3.tgz", "integrity": "sha1-fbCPnT0i3H945Trzw79GZuzfzPE=", "requires": {"extend-shallow": "^2.0.1", "is-extendable": "^0.1.1", "is-plain-object": "^2.0.1", "to-object-path": "^0.3.0"}}, "union-value": {"version": "0.2.4", "resolved": "https://registry.npmjs.org/union-value/-/union-value-0.2.4.tgz", "integrity": "sha1-c3UVJ4ZnkFfns3qmdug0aPwCdPA=", "requires": {"arr-union": "^3.1.0", "get-value": "^2.0.6", "is-extendable": "^0.1.1", "set-value": "^0.4.3"}}}}, "map-visit": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/map-visit/-/map-visit-1.0.0.tgz", "integrity": "sha1-7Nyo8TFE5mDxtb1B8S80edmN+48=", "requires": {"object-visit": "^1.0.0"}}, "match-file": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/match-file/-/match-file-0.2.2.tgz", "integrity": "sha1-Jua88bOQpmH2Em+visUB4z7M+uk=", "requires": {"is-glob": "^3.1.0", "isobject": "^3.0.0", "micromatch": "^2.3.11"}, "dependencies": {"arr-diff": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/arr-diff/-/arr-diff-2.0.0.tgz", "integrity": "sha1-jzuCf5Vai9ZpaX5KQlasPOrjVs8=", "requires": {"arr-flatten": "^1.0.1"}}, "array-unique": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/array-unique/-/array-unique-0.2.1.tgz", "integrity": "sha1-odl8yvy8JiXMcPrc6zalDFiwGlM="}, "braces": {"version": "1.8.5", "resolved": "https://registry.npmjs.org/braces/-/braces-1.8.5.tgz", "integrity": "sha1-uneWLhLf+WnWt2cR6RS3N4V79qc=", "requires": {"expand-range": "^1.8.1", "preserve": "^0.2.0", "repeat-element": "^1.1.2"}}, "expand-brackets": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/expand-brackets/-/expand-brackets-0.1.5.tgz", "integrity": "sha1-3wcoTjQqgHzXM6xa9yQR5YHRF3s=", "requires": {"is-posix-bracket": "^0.1.0"}}, "extglob": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/extglob/-/extglob-0.3.2.tgz", "integrity": "sha1-Lhj/PS9JqydlzskCPwEdqo2DSaE=", "requires": {"is-extglob": "^1.0.0"}, "dependencies": {"is-extglob": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-extglob/-/is-extglob-1.0.0.tgz", "integrity": "sha1-rEaBd8SUNAWgkvyPKXYMb/xiBsA="}}}, "is-glob": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/is-glob/-/is-glob-3.1.0.tgz", "integrity": "sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo=", "requires": {"is-extglob": "^2.1.0"}}, "kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}, "micromatch": {"version": "2.3.11", "resolved": "https://registry.npmjs.org/micromatch/-/micromatch-2.3.11.tgz", "integrity": "sha1-hmd8l9FyCzY0MdBNDRUpO9OMFWU=", "requires": {"arr-diff": "^2.0.0", "array-unique": "^0.2.1", "braces": "^1.8.2", "expand-brackets": "^0.1.4", "extglob": "^0.3.1", "filename-regex": "^2.0.0", "is-extglob": "^1.0.0", "is-glob": "^2.0.1", "kind-of": "^3.0.2", "normalize-path": "^2.0.1", "object.omit": "^2.0.0", "parse-glob": "^3.0.4", "regex-cache": "^0.4.2"}, "dependencies": {"is-extglob": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-extglob/-/is-extglob-1.0.0.tgz", "integrity": "sha1-rEaBd8SUNAWgkvyPKXYMb/xiBsA="}, "is-glob": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/is-glob/-/is-glob-2.0.1.tgz", "integrity": "sha1-0Jb5JqPe1WAPP9/ZEZjLCIjC2GM=", "requires": {"is-extglob": "^1.0.0"}}}}, "normalize-path": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/normalize-path/-/normalize-path-2.1.1.tgz", "integrity": "sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=", "requires": {"remove-trailing-separator": "^1.0.1"}}}}, "matched": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/matched/-/matched-1.0.2.tgz", "integrity": "sha512-7ivM1jFZVTOOS77QsR+TtYHH0ecdLclMkqbf5qiJdX2RorqfhsL65QHySPZgDE0ZjHoh+mQUNHTanNXIlzXd0Q==", "requires": {"arr-union": "^3.1.0", "async-array-reduce": "^0.2.1", "glob": "^7.1.2", "has-glob": "^1.0.0", "is-valid-glob": "^1.0.0", "resolve-dir": "^1.0.0"}, "dependencies": {"has-glob": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/has-glob/-/has-glob-1.0.0.tgz", "integrity": "sha1-mqqe7b/7G6OZCnsAEPtnjuAIEgc=", "requires": {"is-glob": "^3.0.0"}}, "is-glob": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/is-glob/-/is-glob-3.1.0.tgz", "integrity": "sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo=", "requires": {"is-extglob": "^2.1.0"}}, "is-valid-glob": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-valid-glob/-/is-valid-glob-1.0.0.tgz", "integrity": "sha1-Kb8+/3Ab4tTTFdusw5vDn+j2Aao="}}}, "math-random": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/math-random/-/math-random-1.0.4.tgz", "integrity": "sha512-rUxjysqif/BZQH2yhd5Aaq7vXMSx9NdEsQcyA07uEzIvxgI7zIr33gGsh+RU0/XjmQpCW7RsVof1vlkvQVCK5A=="}, "md5.js": {"version": "1.3.5", "resolved": "https://registry.npmjs.org/md5.js/-/md5.js-1.3.5.tgz", "integrity": "sha512-xitP+WxNPcTTOgnTJcrhM0xvdPepipPSf3I8EIpGKeFLjt3PlJLIDG3u8EX53ZIubkb+5U2+3rELYpEhHhzdkg==", "dev": true, "requires": {"hash-base": "^3.0.0", "inherits": "^2.0.1", "safe-buffer": "^5.1.2"}}, "mem": {"version": "4.3.0", "resolved": "https://registry.npmjs.org/mem/-/mem-4.3.0.tgz", "integrity": "sha512-qX2bG48pTqYRVmDB37rn/6PT7LcR8T7oAX3bf99u1Tt1nzxYfxkgqDwUwolPlXweM0XzBOBFzSx4kfp7KP1s/w==", "dev": true, "requires": {"map-age-cleaner": "^0.1.1", "mimic-fn": "^2.0.0", "p-is-promise": "^2.0.0"}}, "memoize-one": {"version": "5.0.5", "resolved": "https://registry.npmjs.org/memoize-one/-/memoize-one-5.0.5.tgz", "integrity": "sha512-ey6EpYv0tEaIbM/nTDOpHciXUvd+ackQrJgEzBwemhZZIWZjcyodqEcrmqDy2BKRTM3a65kKBV4WtLXJDt26SQ=="}, "memory-fs": {"version": "0.4.1", "resolved": "https://registry.npmjs.org/memory-fs/-/memory-fs-0.4.1.tgz", "integrity": "sha1-OpoguEYlI+RHz7x+i7gO1me/xVI=", "dev": true, "requires": {"errno": "^0.1.3", "readable-stream": "^2.0.1"}}, "merge-deep": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/merge-deep/-/merge-deep-3.0.2.tgz", "integrity": "sha512-T7qC8kg4Zoti1cFd8Cr0M+qaZfOwjlPDEdZIIPPB2JZctjaPM4fX+i7HOId69tAti2fvO6X5ldfYUONDODsrkA==", "requires": {"arr-union": "^3.1.0", "clone-deep": "^0.2.4", "kind-of": "^3.0.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}}}, "merge-stream": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/merge-stream/-/merge-stream-1.0.1.tgz", "integrity": "sha1-QEEgLVCKNCugAXQAjfDCUbjBNeE=", "requires": {"readable-stream": "^2.0.1"}}, "merge-value": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/merge-value/-/merge-value-1.0.0.tgz", "integrity": "sha512-fJMmvat4NeKz63Uv9iHWcPDjCWcCkoiRoajRTEO8hlhUC6rwaHg0QCF9hBOTjZmm4JuglPckPSTtcuJL5kp0TQ==", "requires": {"get-value": "^2.0.6", "is-extendable": "^1.0.0", "mixin-deep": "^1.2.0", "set-value": "^2.0.0"}, "dependencies": {"is-extendable": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/is-extendable/-/is-extendable-1.0.1.tgz", "integrity": "sha512-arnXMxT1hhoKo9k1LZdmlNyJdDDfy2v0fXjFlmok4+i8ul/6WlbVge9bhM74OpNPQPMGUToDtz+KXa1PneJxOA==", "requires": {"is-plain-object": "^2.0.4"}}}}, "micromatch": {"version": "3.1.10", "resolved": "https://registry.npmjs.org/micromatch/-/micromatch-3.1.10.tgz", "integrity": "sha512-MWikgl9n9M3w+bpsY3He8L+w9eF9338xRl8IAO5viDizwSzziFEyUzo2xrrloB64ADbTf8uA8vRqqttDTOmccg==", "dev": true, "requires": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "braces": "^2.3.1", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "extglob": "^2.0.4", "fragment-cache": "^0.2.1", "kind-of": "^6.0.2", "nanomatch": "^1.2.9", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.2"}}, "miller-rabin": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/miller-rabin/-/miller-rabin-4.0.1.tgz", "integrity": "sha512-115fLhvZVqWwHPbClyntxEVfVDfl9DLLTuJvq3g2O/Oxi8AiNouAHvDSzHS0viUJc+V5vm3eq91Xwqn9dp4jRA==", "dev": true, "requires": {"bn.js": "^4.0.0", "brorand": "^1.0.1"}}, "mime-db": {"version": "1.40.0", "resolved": "https://registry.npmjs.org/mime-db/-/mime-db-1.40.0.tgz", "integrity": "sha512-jYdeOMPy9vnxEqFRRo6ZvTZ8d9oPb+k18PKoYNYUe2stVEBPPwsln/qWzdbmaIvnhZ9v2P+CuecK+fpUfsV2mA=="}, "mime-types": {"version": "2.1.24", "resolved": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.24.tgz", "integrity": "sha512-Wa<PERSON><PERSON>3MCl5fapm3oLxU4eYDw77IQM2ACcxQ9RIxfaC3ooc6PFuBMGZZsYpvoXS5D5QTWPieo1jjLdAm3TBP3cQ==", "requires": {"mime-db": "1.40.0"}}, "mimic-fn": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/mimic-fn/-/mimic-fn-2.1.0.tgz", "integrity": "sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==", "dev": true}, "minimalistic-assert": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/minimalistic-assert/-/minimalistic-assert-1.0.1.tgz", "integrity": "sha512-UtJcAD4yEaGtjPezWuO9wC4nwUnVH/8/Im3yEHQP4b67cXlD/Qr9hdITCU1xDbSEXg2XKNaP8jsReV7vQd00/A==", "dev": true}, "minimalistic-crypto-utils": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/minimalistic-crypto-utils/-/minimalistic-crypto-utils-1.0.1.tgz", "integrity": "sha1-9sAMHAsIIkblxNmd+4x8CDsrWCo=", "dev": true}, "minimatch": {"version": "3.0.4", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.0.4.tgz", "integrity": "sha512-yJHVQEhyqPLUTgt9B83PXu6W3rx4MvvHvSUvToogpwoGDOUQ+yDrR0HRot+yOCdCO7u4hX3pWft6kWBBcqh0UA==", "requires": {"brace-expansion": "^1.1.7"}}, "minimist": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/minimist/-/minimist-1.2.0.tgz", "integrity": "sha1-o1AIsg9BOD7sH7kU9M1d95omQoQ="}, "mississippi": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/mississippi/-/mississippi-3.0.0.tgz", "integrity": "sha512-x471SsVjUtBRtcvd4BzKE9kFC+/2TeWgKCgw0bZcw1b9l2X3QX5vCWgF+KaZaYm87Ss//rHnWryupDrgLvmSkA==", "dev": true, "requires": {"concat-stream": "^1.5.0", "duplexify": "^3.4.2", "end-of-stream": "^1.1.0", "flush-write-stream": "^1.0.0", "from2": "^2.1.0", "parallel-transform": "^1.1.0", "pump": "^3.0.0", "pumpify": "^1.3.3", "stream-each": "^1.1.0", "through2": "^2.0.0"}}, "mixin-deep": {"version": "1.3.2", "resolved": "https://registry.npmjs.org/mixin-deep/-/mixin-deep-1.3.2.tgz", "integrity": "sha512-WRoDn//mXBiJ1H40rqa3vH0toePwSsGb45iInWlTySa+Uu4k3tYUSxa2v1KqAiLtvlrSzaExqS1gtk96A9zvEA==", "requires": {"for-in": "^1.0.2", "is-extendable": "^1.0.1"}, "dependencies": {"is-extendable": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/is-extendable/-/is-extendable-1.0.1.tgz", "integrity": "sha512-arnXMxT1hhoKo9k1LZdmlNyJdDDfy2v0fXjFlmok4+i8ul/6WlbVge9bhM74OpNPQPMGUToDtz+KXa1PneJxOA==", "requires": {"is-plain-object": "^2.0.4"}}}}, "mixin-object": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/mixin-object/-/mixin-object-2.0.1.tgz", "integrity": "sha1-T7lJRB2rGCVA8f4DW6YOGUel5X4=", "requires": {"for-in": "^0.1.3", "is-extendable": "^0.1.1"}, "dependencies": {"for-in": {"version": "0.1.8", "resolved": "https://registry.npmjs.org/for-in/-/for-in-0.1.8.tgz", "integrity": "sha1-2Hc5COMSVhCZUrH9ubP6hn0ndeE="}}}, "mkdirp": {"version": "0.5.1", "resolved": "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.1.tgz", "integrity": "sha1-MAV0OOrGz3+MR2fzhkjWaX11yQM=", "requires": {"minimist": "0.0.8"}, "dependencies": {"minimist": {"version": "0.0.8", "resolved": "https://registry.npmjs.org/minimist/-/minimist-0.0.8.tgz", "integrity": "sha1-hX/Kv8M5fSYluCKCYuhqp6ARsF0="}}}, "move-concurrently": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/move-concurrently/-/move-concurrently-1.0.1.tgz", "integrity": "sha1-viwAX9oy4LKa8fBdfEszIUxwH5I=", "dev": true, "requires": {"aproba": "^1.1.1", "copy-concurrently": "^1.0.0", "fs-write-stream-atomic": "^1.0.8", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "run-queue": "^1.0.3"}}, "ms": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz", "integrity": "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==", "dev": true}, "mute-stream": {"version": "0.0.5", "resolved": "https://registry.npmjs.org/mute-stream/-/mute-stream-0.0.5.tgz", "integrity": "sha1-j7+rsKmKJT0xhDMfno3rc3L6xsA="}, "nan": {"version": "2.14.0", "resolved": "https://registry.npmjs.org/nan/-/nan-2.14.0.tgz", "integrity": "sha512-INOFj37C7k3AfaNTtX8RhsTw7qRy7eLET14cROi9+5HAVbbHuIWUHEauBv5qT4Av2tWasiTY1Jw6puUNqRJXQg==", "dev": true, "optional": true}, "nanomatch": {"version": "1.2.13", "resolved": "https://registry.npmjs.org/nanomatch/-/nanomatch-1.2.13.tgz", "integrity": "sha512-fpoe2T0RbHwBTBUOftAfBPaDEi06ufaUai0mE6Yn1kacc3SnTErfb/h+X94VXzI64rKFHYImXSvdwGGCmwOqCA==", "dev": true, "requires": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "fragment-cache": "^0.2.1", "is-windows": "^1.0.2", "kind-of": "^6.0.2", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}}, "nanoseconds": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/nanoseconds/-/nanoseconds-0.1.0.tgz", "integrity": "sha1-aew5/NAOd6s6ct4KQzQoJM15Izo="}, "neo-async": {"version": "2.6.1", "resolved": "https://registry.npmjs.org/neo-async/-/neo-async-2.6.1.tgz", "integrity": "sha512-iyam8fBuCUpWeKPGpaNMetEocMt364qkCsfL9JuhjXX6dRnguRVOfk2GZaDpPjcOKiiXCPINZC1GczQ7iTq3Zw==", "dev": true}, "next-tick": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/next-tick/-/next-tick-0.2.2.tgz", "integrity": "sha1-ddpKkn7liH45BliABltzNkE7MQ0="}, "nice-try": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/nice-try/-/nice-try-1.0.5.tgz", "integrity": "sha512-1nh45deeb5olNY7eX82BkPO7SSxR5SSYJiPTrTdFUVYwAl8CKMA5N9PjTYkHiRjisVcxcQ1HXdLhx2qxxJzLNQ==", "dev": true}, "no-case": {"version": "2.3.2", "resolved": "https://registry.npmjs.org/no-case/-/no-case-2.3.2.tgz", "integrity": "sha512-rmTZ9kz+f3rCvK2TD1Ue/oZlns7OGoIWP4fc3llxxRXlOkHKoWPPWJOfFYpITabSow43QJbRIoHQXtt10VldyQ==", "requires": {"lower-case": "^1.1.1"}}, "node-fetch": {"version": "1.7.3", "resolved": "https://registry.npmjs.org/node-fetch/-/node-fetch-1.7.3.tgz", "integrity": "sha512-NhZ4CsKx7cYm2vSrBAr2PvFOe6sWDf0UYLRqA6svUYg7+/TSfVAu49jYC4BvQ4Sms9SZgdqGBgroqfDhJdTyKQ==", "requires": {"encoding": "^0.1.11", "is-stream": "^1.0.1"}}, "node-libs-browser": {"version": "2.2.1", "resolved": "https://registry.npmjs.org/node-libs-browser/-/node-libs-browser-2.2.1.tgz", "integrity": "sha512-h/zcD8H9kaDZ9ALUWwlBUDo6TKF8a7qBSCSEGfjTVIYeqsioSKaAX+BN7NgiMGp6iSIXZ3PxgCu8KS3b71YK5Q==", "dev": true, "requires": {"assert": "^1.1.1", "browserify-zlib": "^0.2.0", "buffer": "^4.3.0", "console-browserify": "^1.1.0", "constants-browserify": "^1.0.0", "crypto-browserify": "^3.11.0", "domain-browser": "^1.1.1", "events": "^3.0.0", "https-browserify": "^1.0.0", "os-browserify": "^0.3.0", "path-browserify": "0.0.1", "process": "^0.11.10", "punycode": "^1.2.4", "querystring-es3": "^0.2.0", "readable-stream": "^2.3.3", "stream-browserify": "^2.0.1", "stream-http": "^2.7.2", "string_decoder": "^1.0.0", "timers-browserify": "^2.0.4", "tty-browserify": "0.0.0", "url": "^0.11.0", "util": "^0.11.0", "vm-browserify": "^1.0.1"}, "dependencies": {"punycode": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/punycode/-/punycode-1.4.1.tgz", "integrity": "sha1-wNWmOycYgArY4esPpSachN1BhF4=", "dev": true}}}, "node-releases": {"version": "1.1.23", "resolved": "https://registry.npmjs.org/node-releases/-/node-releases-1.1.23.tgz", "integrity": "sha512-uq1iL79YjfYC0WXoHbC/z28q/9pOl8kSHaXdWmAAc8No+bDwqkZbzIJz55g/MUsPgSGm9LZ7QSUbzTcH5tz47w==", "dev": true, "requires": {"semver": "^5.3.0"}}, "noncharacters": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/noncharacters/-/noncharacters-1.1.0.tgz", "integrity": "sha1-rzPfMP1Q7TxTzSAiWPJa2pC1QNI="}, "normalize-path": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz", "integrity": "sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==", "dev": true}, "normalize-pkg": {"version": "0.3.20", "resolved": "https://registry.npmjs.org/normalize-pkg/-/normalize-pkg-0.3.20.tgz", "integrity": "sha1-Luc3FJUXhQ2c7/WmI0r174nFFag=", "requires": {"arr-union": "^3.1.0", "array-unique": "^0.3.2", "component-emitter": "^1.2.1", "export-files": "^2.1.1", "extend-shallow": "^2.0.1", "fs-exists-sync": "^0.1.0", "get-value": "^2.0.6", "kind-of": "^3.0.4", "lazy-cache": "^2.0.1", "map-schema": "^0.2.3", "minimist": "^1.2.0", "mixin-deep": "^1.1.3", "omit-empty": "^0.4.1", "parse-git-config": "^1.0.2", "repo-utils": "^0.3.6", "semver": "^5.3.0", "stringify-author": "^0.1.3", "write-json": "^0.2.2"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}, "kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}}}, "now-and-later": {"version": "0.0.6", "resolved": "https://registry.npmjs.org/now-and-later/-/now-and-later-0.0.6.tgz", "integrity": "sha1-GKFNw/xJXcBs++Ao8AvhbdrE+uo=", "requires": {"once": "^1.3.0"}}, "npm-run-path": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/npm-run-path/-/npm-run-path-2.0.2.tgz", "integrity": "sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8=", "dev": true, "requires": {"path-key": "^2.0.0"}}, "number-is-nan": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/number-is-nan/-/number-is-nan-1.0.1.tgz", "integrity": "sha1-CXtgK1NCKlIsGvuHkDGDNpQaAR0="}, "object-assign": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM="}, "object-copy": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/object-copy/-/object-copy-0.1.0.tgz", "integrity": "sha1-fn2Fi3gb18mRpBupde04EnVOmYw=", "requires": {"copy-descriptor": "^0.1.0", "define-property": "^0.2.5", "kind-of": "^3.0.3"}, "dependencies": {"define-property": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "requires": {"is-descriptor": "^0.1.0"}}, "kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}}}, "object-visit": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/object-visit/-/object-visit-1.0.1.tgz", "integrity": "sha1-95xEk68MU3e1n+OdOV5BBC3QRbs=", "requires": {"isobject": "^3.0.0"}}, "object.omit": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/object.omit/-/object.omit-2.0.1.tgz", "integrity": "sha1-Gpx0SCnznbuFjHbKNXmuKlTr0fo=", "requires": {"for-own": "^0.1.4", "is-extendable": "^0.1.1"}}, "object.pick": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/object.pick/-/object.pick-1.3.0.tgz", "integrity": "sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c=", "requires": {"isobject": "^3.0.1"}}, "omit-empty": {"version": "0.4.1", "resolved": "https://registry.npmjs.org/omit-empty/-/omit-empty-0.4.1.tgz", "integrity": "sha1-KUo3gvLLIMdJfEEitiN8ncwMY6s=", "requires": {"has-values": "^0.1.4", "kind-of": "^3.0.3", "reduce-object": "^0.1.3"}, "dependencies": {"has-values": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/has-values/-/has-values-0.1.4.tgz", "integrity": "sha1-bWHeldkd/Km5oCCJrThL/49it3E="}, "kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}}}, "once": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/once/-/once-1.4.0.tgz", "integrity": "sha1-WDsap3WWHUsROsF9nFC6753Xa9E=", "requires": {"wrappy": "1"}}, "onetime": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/onetime/-/onetime-1.1.0.tgz", "integrity": "sha1-ofeDj4MUxRbwXs78vEzP4EtO14k="}, "operation-retrier": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/operation-retrier/-/operation-retrier-3.0.1.tgz", "integrity": "sha512-lmrISisi5nbu0WNXBCMagrdJFwLUYFnaas87PgOMM3aNi+Z2YFvyC5K7/cAJuNUtpljztwJPzvp8bho02sAMAg=="}, "option-cache": {"version": "3.5.0", "resolved": "https://registry.npmjs.org/option-cache/-/option-cache-3.5.0.tgz", "integrity": "sha1-y3ZRVboqhhwRCf8m4qIOqgZhKys=", "requires": {"arr-flatten": "^1.0.3", "collection-visit": "^1.0.0", "component-emitter": "^1.2.1", "get-value": "^2.0.6", "has-value": "^0.3.1", "kind-of": "^3.2.2", "lazy-cache": "^2.0.2", "set-value": "^0.4.3", "to-object-path": "^0.3.0"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}, "has-value": {"version": "0.3.1", "resolved": "https://registry.npmjs.org/has-value/-/has-value-0.3.1.tgz", "integrity": "sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=", "requires": {"get-value": "^2.0.3", "has-values": "^0.1.4", "isobject": "^2.0.0"}}, "has-values": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/has-values/-/has-values-0.1.4.tgz", "integrity": "sha1-bWHeldkd/Km5oCCJrThL/49it3E="}, "isobject": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "requires": {"isarray": "1.0.0"}}, "kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}, "set-value": {"version": "0.4.3", "resolved": "https://registry.npmjs.org/set-value/-/set-value-0.4.3.tgz", "integrity": "sha1-fbCPnT0i3H945Trzw79GZuzfzPE=", "requires": {"extend-shallow": "^2.0.1", "is-extendable": "^0.1.1", "is-plain-object": "^2.0.1", "to-object-path": "^0.3.0"}}}}, "ordered-read-streams": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/ordered-read-streams/-/ordered-read-streams-0.3.0.tgz", "integrity": "sha1-cTfmmzKYuzQiR6G77jiByA4v14s=", "requires": {"is-stream": "^1.0.1", "readable-stream": "^2.0.1"}}, "os-browserify": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/os-browserify/-/os-browserify-0.3.0.tgz", "integrity": "sha1-hUNzx/XCMVkU/Jv8a9gjj92h7Cc=", "dev": true}, "os-homedir": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/os-homedir/-/os-homedir-1.0.2.tgz", "integrity": "sha1-/7xJiDNuDoM94MFox+8VISGqf7M="}, "os-locale": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/os-locale/-/os-locale-3.1.0.tgz", "integrity": "sha512-Z8l3R4wYWM40/52Z+S265okfFj8Kt2cC2MKY+xNi3kFs+XGI7WXu/I309QQQYbRW4ijiZ+yxs9pqEhJh0DqW3Q==", "dev": true, "requires": {"execa": "^1.0.0", "lcid": "^2.0.0", "mem": "^4.0.0"}}, "p-defer": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/p-defer/-/p-defer-1.0.0.tgz", "integrity": "sha1-n26xgvbJqozXQwBKfU+WsZaw+ww=", "dev": true}, "p-finally": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/p-finally/-/p-finally-1.0.0.tgz", "integrity": "sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4=", "dev": true}, "p-is-promise": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/p-is-promise/-/p-is-promise-2.1.0.tgz", "integrity": "sha512-Y3W0wlRPK8ZMRbNq97l4M5otioeA5lm1z7bkNkxCka8HSPjR0xRWmpCmc9utiaLP9Jb1eD8BgeIxTW4AIF45Pg==", "dev": true}, "p-limit": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/p-limit/-/p-limit-2.2.0.tgz", "integrity": "sha512-pZbTJpoUsCzV48Mc9Nh51VbwO0X9cuPFE8gYwx9BTCt9SF8/b7Zljd2fVgOxhIF/HDTKgpVzs+GPhyKfjLLFRQ==", "dev": true, "requires": {"p-try": "^2.0.0"}}, "p-locate": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/p-locate/-/p-locate-3.0.0.tgz", "integrity": "sha512-x+12w/To+4GFfgJhBEpiDcLozRJGegY+Ei7/z0tSLkMmxGZNybVMSfWj9aJn8Z5Fc7dBUNJOOVgPv2H7IwulSQ==", "dev": true, "requires": {"p-limit": "^2.0.0"}}, "p-try": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/p-try/-/p-try-2.2.0.tgz", "integrity": "sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==", "dev": true}, "pad-right": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/pad-right/-/pad-right-0.2.2.tgz", "integrity": "sha1-b7ySQEXSRPKiokRQMGDTv8YAl3Q=", "requires": {"repeat-string": "^1.5.2"}}, "paginationator": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/paginationator/-/paginationator-0.1.4.tgz", "integrity": "sha1-hHht04UKrh8Ru7kRsMHghRtTgQY="}, "pako": {"version": "1.0.10", "resolved": "https://registry.npmjs.org/pako/-/pako-1.0.10.tgz", "integrity": "sha512-0DTvPVU3ed8+HNXOu5Bs+o//Mbdj9VNQMUOe9oKCwh8l0GNwpTDMKCWbRjgtD291AWnkAgkqA/LOnQS8AmS1tw==", "dev": true}, "parallel-transform": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/parallel-transform/-/parallel-transform-1.1.0.tgz", "integrity": "sha1-1BDwZbBdojCB/NEPKIVMKb2jOwY=", "dev": true, "requires": {"cyclist": "~0.2.2", "inherits": "^2.0.3", "readable-stream": "^2.1.5"}}, "parse-asn1": {"version": "5.1.4", "resolved": "https://registry.npmjs.org/parse-asn1/-/parse-asn1-5.1.4.tgz", "integrity": "sha512-Qs5duJcuvNExRfFZ99HDD3z4mAi3r9Wl/FOjEOijlxwCZs7E7mW2vjTpgQ4J8LpTF8x5v+1Vn5UQFejmWT11aw==", "dev": true, "requires": {"asn1.js": "^4.0.0", "browserify-aes": "^1.0.0", "create-hash": "^1.1.0", "evp_bytestokey": "^1.0.0", "pbkdf2": "^3.0.3", "safe-buffer": "^5.1.1"}}, "parse-author": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/parse-author/-/parse-author-1.0.0.tgz", "integrity": "sha1-XsFZAGKXe9nLOWLpFzuHWGQ39d8="}, "parse-git-config": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/parse-git-config/-/parse-git-config-1.1.1.tgz", "integrity": "sha1-06mYQxcTL1c5hxK7pDjhKVkN34w=", "requires": {"extend-shallow": "^2.0.1", "fs-exists-sync": "^0.1.0", "git-config-path": "^1.0.1", "ini": "^1.3.4"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}}}, "parse-github-url": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/parse-github-url/-/parse-github-url-0.3.2.tgz", "integrity": "sha1-du8B6/4LHpwPSTZylSzGpM2csmA="}, "parse-glob": {"version": "3.0.4", "resolved": "https://registry.npmjs.org/parse-glob/-/parse-glob-3.0.4.tgz", "integrity": "sha1-ssN2z7EfNVE7rdFz7wu246OIORw=", "requires": {"glob-base": "^0.3.0", "is-dotfile": "^1.0.0", "is-extglob": "^1.0.0", "is-glob": "^2.0.0"}, "dependencies": {"is-extglob": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-extglob/-/is-extglob-1.0.0.tgz", "integrity": "sha1-rEaBd8SUNAWgkvyPKXYMb/xiBsA="}, "is-glob": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/is-glob/-/is-glob-2.0.1.tgz", "integrity": "sha1-0Jb5JqPe1WAPP9/ZEZjLCIjC2GM=", "requires": {"is-extglob": "^1.0.0"}}}}, "parse-json": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/parse-json/-/parse-json-4.0.0.tgz", "integrity": "sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA=", "requires": {"error-ex": "^1.3.1", "json-parse-better-errors": "^1.0.1"}}, "parse-passwd": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/parse-passwd/-/parse-passwd-1.0.0.tgz", "integrity": "sha1-bVuTSkVpk7I9N/QKOC1vFmao5cY="}, "parser-front-matter": {"version": "1.6.4", "resolved": "https://registry.npmjs.org/parser-front-matter/-/parser-front-matter-1.6.4.tgz", "integrity": "sha512-eqtUnI5+COkf1CQOYo8FmykN5Zs+5Yr60f/7GcPgQDZEEjdE/VZ4WMaMo9g37foof8h64t/TH2Uvk2Sq0fDy/g==", "requires": {"extend-shallow": "^2.0.1", "file-is-binary": "^1.0.0", "gray-matter": "^3.0.2", "isobject": "^3.0.1", "lazy-cache": "^2.0.2", "mixin-deep": "^1.2.0", "trim-leading-lines": "^0.1.1"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}}}, "pascalcase": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/pascalcase/-/pascalcase-0.1.1.tgz", "integrity": "sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ="}, "path-browserify": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/path-browserify/-/path-browserify-0.0.1.tgz", "integrity": "sha512-BapA40NHICOS+USX9SN4tyhq+A2RrN/Ws5F0Z5aMHDp98Fl86lX8Oti8B7uN93L4Ifv4fHOEA+pQw87gmMO/lQ==", "dev": true}, "path-dirname": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/path-dirname/-/path-dirname-1.0.2.tgz", "integrity": "sha1-zDPSTVJeCZpTiMAzbG4yuRYGCeA="}, "path-exists": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/path-exists/-/path-exists-3.0.0.tgz", "integrity": "sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=", "dev": true}, "path-is-absolute": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "integrity": "sha1-F0uSaHNVNP+8es5r9TpanhtcX18="}, "path-key": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/path-key/-/path-key-2.0.1.tgz", "integrity": "sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A=", "dev": true}, "path-parse": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/path-parse/-/path-parse-1.0.6.tgz", "integrity": "sha512-GSmOT2EbHrINBf9SR7CDELwlJ8AENk3Qn7OikK4nFYAu3Ote2+JYNVvkpAEQm3/TLNEJFD/xZJjzyxg3KBWOzw=="}, "path-to-regexp": {"version": "1.7.0", "resolved": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-1.7.0.tgz", "integrity": "sha1-Wf3g9DW62suhA6hOnTvGTpa5k30=", "requires": {"isarray": "0.0.1"}, "dependencies": {"isarray": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/isarray/-/isarray-0.0.1.tgz", "integrity": "sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8="}}}, "pbkdf2": {"version": "3.0.17", "resolved": "https://registry.npmjs.org/pbkdf2/-/pbkdf2-3.0.17.tgz", "integrity": "sha512-U/il5MsrZp7mGg3mSQfn742na2T+1/vHDCG5/iTI3X9MKUuYUZVLQhyRsg06mCgDBTd57TxzgZt7P+fYfjRLtA==", "dev": true, "requires": {"create-hash": "^1.1.2", "create-hmac": "^1.1.4", "ripemd160": "^2.0.1", "safe-buffer": "^5.0.1", "sha.js": "^2.4.8"}}, "performance-now": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/performance-now/-/performance-now-2.1.0.tgz", "integrity": "sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns="}, "pify": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/pify/-/pify-4.0.1.tgz", "integrity": "sha512-uB80kBFb/tfd68bVleG9T5GGsGPjJrLAUpR5PZIrhBnIaRTQRjqdJSsIKkOP6OAIFbj7GOrcudc5pNjZ+geV2g==", "dev": true}, "pkg-dir": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/pkg-dir/-/pkg-dir-3.0.0.tgz", "integrity": "sha512-/E57AYkoeQ25qkxMj5PBOVgF8Kiu/h7cYS30Z5+R7WaiCCBfLq58ZI/dSeaEKb9WVJV5n/03QwrN3IeWIFllvw==", "dev": true, "requires": {"find-up": "^3.0.0"}}, "pkg-store": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/pkg-store/-/pkg-store-0.2.2.tgz", "integrity": "sha1-sfXA+GIKWf1mWGrMXiVvTCw3oNg=", "requires": {"cache-base": "^0.8.2", "kind-of": "^3.0.2", "lazy-cache": "^1.0.3", "union-value": "^0.2.3", "write-json": "^0.2.2"}, "dependencies": {"cache-base": {"version": "0.8.5", "resolved": "https://registry.npmjs.org/cache-base/-/cache-base-0.8.5.tgz", "integrity": "sha1-YM6zUEAh7O7HAR/TOEt/TpVym/o=", "requires": {"collection-visit": "^0.2.1", "component-emitter": "^1.2.1", "get-value": "^2.0.5", "has-value": "^0.3.1", "isobject": "^3.0.0", "lazy-cache": "^2.0.1", "set-value": "^0.4.2", "to-object-path": "^0.3.0", "union-value": "^0.2.3", "unset-value": "^0.1.1"}, "dependencies": {"lazy-cache": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/lazy-cache/-/lazy-cache-2.0.2.tgz", "integrity": "sha1-uRkKT5EzVGlIQIWfio9whNiCImQ=", "requires": {"set-getter": "^0.1.0"}}}}, "collection-visit": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/collection-visit/-/collection-visit-0.2.3.tgz", "integrity": "sha1-L2JIPK7MlfCDuaRUo+6eYTmteVc=", "requires": {"lazy-cache": "^2.0.1", "map-visit": "^0.1.5", "object-visit": "^0.3.4"}, "dependencies": {"lazy-cache": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/lazy-cache/-/lazy-cache-2.0.2.tgz", "integrity": "sha1-uRkKT5EzVGlIQIWfio9whNiCImQ=", "requires": {"set-getter": "^0.1.0"}}}}, "extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}, "has-value": {"version": "0.3.1", "resolved": "https://registry.npmjs.org/has-value/-/has-value-0.3.1.tgz", "integrity": "sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=", "requires": {"get-value": "^2.0.3", "has-values": "^0.1.4", "isobject": "^2.0.0"}, "dependencies": {"isobject": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "requires": {"isarray": "1.0.0"}}}}, "has-values": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/has-values/-/has-values-0.1.4.tgz", "integrity": "sha1-bWHeldkd/Km5oCCJrThL/49it3E="}, "kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}, "lazy-cache": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/lazy-cache/-/lazy-cache-1.0.4.tgz", "integrity": "sha1-odePw6UEdMuAhF07O24dpJpEbo4="}, "map-visit": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/map-visit/-/map-visit-0.1.5.tgz", "integrity": "sha1-2+Q5J85VJbgN/BVzpE1oxR8mgWs=", "requires": {"lazy-cache": "^2.0.1", "object-visit": "^0.3.4"}, "dependencies": {"lazy-cache": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/lazy-cache/-/lazy-cache-2.0.2.tgz", "integrity": "sha1-uRkKT5EzVGlIQIWfio9whNiCImQ=", "requires": {"set-getter": "^0.1.0"}}}}, "object-visit": {"version": "0.3.4", "resolved": "https://registry.npmjs.org/object-visit/-/object-visit-0.3.4.tgz", "integrity": "sha1-rhXPhvCy/dVRdxY2RIRSxUw9qCk=", "requires": {"isobject": "^2.0.0"}, "dependencies": {"isobject": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "requires": {"isarray": "1.0.0"}}}}, "set-value": {"version": "0.4.3", "resolved": "https://registry.npmjs.org/set-value/-/set-value-0.4.3.tgz", "integrity": "sha1-fbCPnT0i3H945Trzw79GZuzfzPE=", "requires": {"extend-shallow": "^2.0.1", "is-extendable": "^0.1.1", "is-plain-object": "^2.0.1", "to-object-path": "^0.3.0"}}, "union-value": {"version": "0.2.4", "resolved": "https://registry.npmjs.org/union-value/-/union-value-0.2.4.tgz", "integrity": "sha1-c3UVJ4ZnkFfns3qmdug0aPwCdPA=", "requires": {"arr-union": "^3.1.0", "get-value": "^2.0.6", "is-extendable": "^0.1.1", "set-value": "^0.4.3"}}, "unset-value": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/unset-value/-/unset-value-0.1.2.tgz", "integrity": "sha1-UGgQuGfyfCpabpsEgzYx9t5Y0xA=", "requires": {"has-value": "^0.3.1", "isobject": "^3.0.0"}}}}, "platform": {"version": "1.3.5", "resolved": "https://registry.npmjs.org/platform/-/platform-1.3.5.tgz", "integrity": "sha512-TuvHS8AOIZNAlE77WUDiR4rySV/VMptyMfcfeoMgs4P8apaZM3JrnbzBiixKUv+XR6i+BXrQh8WAnjaSPFO65Q=="}, "polished": {"version": "1.9.0", "resolved": "https://registry.npmjs.org/polished/-/polished-1.9.0.tgz", "integrity": "sha512-xCteonGa+5eDt2lcbVg0jzh7GLRoejljfh0bP3XBymCHz1uAsi1IjW5T0XrDGRbUzb/f7vXUS+Tz2Ipw3S/w8A=="}, "posix-character-classes": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/posix-character-classes/-/posix-character-classes-0.1.1.tgz", "integrity": "sha1-AerA/jta9xoqbAL+q7jB/vfgDqs=", "dev": true}, "postcss": {"version": "7.0.17", "resolved": "https://registry.npmjs.org/postcss/-/postcss-7.0.17.tgz", "integrity": "sha512-546ZowA+KZ3OasvQZHsbuEpysvwTZNGJv9EfyCQdsIDltPSWHAeTQ5fQy/Npi2ZDtLI3zs7Ps/p6wThErhm9fQ==", "dev": true, "requires": {"chalk": "^2.4.2", "source-map": "^0.6.1", "supports-color": "^6.1.0"}, "dependencies": {"source-map": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz", "integrity": "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==", "dev": true}, "supports-color": {"version": "6.1.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-6.1.0.tgz", "integrity": "sha512-qe1jfm1Mg7Nq/NSh6XE24gPXROEVsWHxC1LIx//XNlD9iw7YZQGjZNjYN7xGaEG6iKdA8EtNFW6R0gjnVXp+wQ==", "dev": true, "requires": {"has-flag": "^3.0.0"}}}}, "postcss-modules-extract-imports": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/postcss-modules-extract-imports/-/postcss-modules-extract-imports-2.0.0.tgz", "integrity": "sha512-LaYLDNS4SG8Q5WAWqIJgdHPJrDDr/Lv775rMBFUbgjTz6j34lUznACHcdRWroPvXANP2Vj7yNK57vp9eFqzLWQ==", "dev": true, "requires": {"postcss": "^7.0.5"}}, "postcss-modules-local-by-default": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/postcss-modules-local-by-default/-/postcss-modules-local-by-default-3.0.2.tgz", "integrity": "sha512-jM/V8eqM4oJ/22j0gx4jrp63GSvDH6v86OqyTHHUvk4/k1vceipZsaymiZ5PvocqZOl5SFHiFJqjs3la0wnfIQ==", "dev": true, "requires": {"icss-utils": "^4.1.1", "postcss": "^7.0.16", "postcss-selector-parser": "^6.0.2", "postcss-value-parser": "^4.0.0"}}, "postcss-modules-scope": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/postcss-modules-scope/-/postcss-modules-scope-2.1.0.tgz", "integrity": "sha512-91Rjps0JnmtUB0cujlc8KIKCsJXWjzuxGeT/+Q2i2HXKZ7nBUeF9YQTZZTNvHVoNYj1AthsjnGLtqDUE0Op79A==", "dev": true, "requires": {"postcss": "^7.0.6", "postcss-selector-parser": "^6.0.0"}}, "postcss-modules-values": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/postcss-modules-values/-/postcss-modules-values-3.0.0.tgz", "integrity": "sha512-1//E5jCBrZ9DmRX+zCtmQtRSV6PV42Ix7Bzj9GbwJceduuf7IqP8MgeTXuRDHOWj2m0VzZD5+roFWDuU8RQjcg==", "dev": true, "requires": {"icss-utils": "^4.0.0", "postcss": "^7.0.6"}}, "postcss-selector-parser": {"version": "6.0.2", "resolved": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.0.2.tgz", "integrity": "sha512-36P2QR59jDTOAiIkqEprfJDsoNrvwFei3eCqKd1Y0tUsBimsq39BLp7RD+JWny3WgB1zGhJX8XVePwm9k4wdBg==", "dev": true, "requires": {"cssesc": "^3.0.0", "indexes-of": "^1.0.1", "uniq": "^1.0.1"}}, "postcss-value-parser": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.0.0.tgz", "integrity": "sha512-ESPktioptiSUchCKgggAkzdmkgzKfmp0EU8jXH+5kbIUB+unr0Y4CY9SRMvibuvYUBjNh1ACLbxqYNpdTQOteQ==", "dev": true}, "preserve": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/preserve/-/preserve-0.2.0.tgz", "integrity": "sha1-gV7R9uvGWSb4ZbMQwHE7yzMVzks="}, "pretty-time": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/pretty-time/-/pretty-time-0.2.0.tgz", "integrity": "sha1-ejvexAScYgzXxCt/NCt01W5z104=", "requires": {"is-number": "^2.0.2", "nanoseconds": "^0.1.0"}, "dependencies": {"is-number": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/is-number/-/is-number-2.1.0.tgz", "integrity": "sha1-Afy7s5NGOlSPL0ZszhbezknbkI8=", "requires": {"kind-of": "^3.0.2"}}, "kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}}}, "private": {"version": "0.1.8", "resolved": "https://registry.npmjs.org/private/-/private-0.1.8.tgz", "integrity": "sha512-VvivMrbvd2nKkiG38qjULzlc+4Vx4wm/whI9pQD35YrARNnhxeiRktSOhSukRLFNlzg6Br/cJPet5J/u19r/mg==", "dev": true}, "process": {"version": "0.11.10", "resolved": "https://registry.npmjs.org/process/-/process-0.11.10.tgz", "integrity": "sha1-czIwDoQBYb2j5podHZGn1LwW8YI=", "dev": true}, "process-nextick-args": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-2.0.1.tgz", "integrity": "sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag=="}, "project-name": {"version": "0.2.6", "resolved": "https://registry.npmjs.org/project-name/-/project-name-0.2.6.tgz", "integrity": "sha1-Pk94H+HulLB4apuuU1BjdsN5r2k=", "requires": {"find-pkg": "^0.1.2", "git-repo-name": "^0.6.0", "minimist": "^1.2.0"}}, "promise": {"version": "7.3.1", "resolved": "https://registry.npmjs.org/promise/-/promise-7.3.1.tgz", "integrity": "sha512-nolQXZ/4L+bP/UGlkfaIujX9BKxGwmQ9OT4mOt5yvy8iK1h3wqTEJCijzGANTCCl9nWjY41juyAn2K3Q1hLLTg==", "requires": {"asap": "~2.0.3"}}, "promise-inflight": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/promise-inflight/-/promise-inflight-1.0.1.tgz", "integrity": "sha1-mEcocL8igTL8vdhoEputEsPAKeM=", "dev": true}, "prop-types": {"version": "15.7.2", "resolved": "https://registry.npmjs.org/prop-types/-/prop-types-15.7.2.tgz", "integrity": "sha512-8QQikdH7//R2vurIJSutZ1smHYTcLpRWEOlHnzcWHmBYrOGUysKwSsrC89BCiFj3CbrfJ/nXFdJepOVrY1GCHQ==", "requires": {"loose-envify": "^1.4.0", "object-assign": "^4.1.1", "react-is": "^16.8.1"}}, "prr": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/prr/-/prr-1.0.1.tgz", "integrity": "sha1-0/wRS6BplaRexok/SEzrHXj19HY=", "dev": true}, "public-encrypt": {"version": "4.0.3", "resolved": "https://registry.npmjs.org/public-encrypt/-/public-encrypt-4.0.3.tgz", "integrity": "sha512-zVpa8oKZSz5bTMTFClc1fQOnyyEzpl5ozpi1B5YcvBrdohMjH2rfsBtyXcuNuwjsDIXmBYlF2N5FlJYhR29t8Q==", "dev": true, "requires": {"bn.js": "^4.1.0", "browserify-rsa": "^4.0.0", "create-hash": "^1.1.0", "parse-asn1": "^5.0.0", "randombytes": "^2.0.1", "safe-buffer": "^5.1.2"}}, "pump": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/pump/-/pump-3.0.0.tgz", "integrity": "sha512-LwZy+p3SFs1Pytd/jYct4wpv49HiYCqd9Rlc5ZVdk0V+8Yzv6jR5Blk3TRmPL1ft69TxP0IMZGJ+WPFU2BFhww==", "dev": true, "requires": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "pumpify": {"version": "1.5.1", "resolved": "https://registry.npmjs.org/pumpify/-/pumpify-1.5.1.tgz", "integrity": "sha512-oClZI37HvuUJJxSKKrC17bZ9Cu0ZYhEAGPsPUy9KlMUmv9dKX2o77RUmq7f3XjIxbwyGwYzbzQ1L2Ks8sIradQ==", "dev": true, "requires": {"duplexify": "^3.6.0", "inherits": "^2.0.3", "pump": "^2.0.0"}, "dependencies": {"pump": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/pump/-/pump-2.0.1.tgz", "integrity": "sha512-ruPMNRkN3MHP1cWJc9OWr+T/xDP0jhXYCLfJcBuX54hhfIBnaQmAUMfDcG4DM5UMWByBbJY69QSphm3jtDKIkA==", "dev": true, "requires": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}}}, "punycode": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/punycode/-/punycode-2.1.1.tgz", "integrity": "sha512-XRsRjdf+j5ml+y/6GKHPZbrF/8p2Yga0JPtdqTIY2Xe5ohJPD9saDJJLPvp9+NSBprVvevdXZybnj2cv8OEd0A==", "dev": true}, "querystring": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/querystring/-/querystring-0.2.0.tgz", "integrity": "sha1-sgmEkgO7Jd+CDadW50cAWHhSFiA=", "dev": true}, "querystring-es3": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/querystring-es3/-/querystring-es3-0.2.1.tgz", "integrity": "sha1-nsYfeQSYdXB9aUFFlv2Qek1xHnM=", "dev": true}, "question-cache": {"version": "0.5.1", "resolved": "https://registry.npmjs.org/question-cache/-/question-cache-0.5.1.tgz", "integrity": "sha1-C8JzKRdTQXB99azTHvLd9nApFo0=", "requires": {"arr-flatten": "^1.0.1", "arr-union": "^3.1.0", "async-each-series": "^1.1.0", "debug": "^2.2.0", "define-property": "^0.2.5", "get-value": "^2.0.6", "has-value": "^0.3.1", "inquirer2": "^0.1.1", "is-answer": "^0.1.0", "isobject": "^2.1.0", "lazy-cache": "^2.0.1", "mixin-deep": "^1.1.3", "omit-empty": "^0.4.1", "option-cache": "^3.4.0", "os-homedir": "^1.0.1", "project-name": "^0.2.5", "set-value": "^0.3.3", "to-choices": "^0.2.0", "use": "^2.0.0"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}}, "define-property": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "requires": {"is-descriptor": "^0.1.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}, "has-value": {"version": "0.3.1", "resolved": "https://registry.npmjs.org/has-value/-/has-value-0.3.1.tgz", "integrity": "sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=", "requires": {"get-value": "^2.0.3", "has-values": "^0.1.4", "isobject": "^2.0.0"}}, "has-values": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/has-values/-/has-values-0.1.4.tgz", "integrity": "sha1-bWHeldkd/Km5oCCJrThL/49it3E="}, "isobject": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "requires": {"isarray": "1.0.0"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}, "set-value": {"version": "0.3.3", "resolved": "https://registry.npmjs.org/set-value/-/set-value-0.3.3.tgz", "integrity": "sha1-uBIjaBY4oQiP2IpDW4qdMtro2bo=", "requires": {"extend-shallow": "^2.0.1", "isobject": "^2.0.0", "to-object-path": "^0.2.0"}}, "to-object-path": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/to-object-path/-/to-object-path-0.2.0.tgz", "integrity": "sha1-FjThtSqIugDjlJYZ/ACB3Jo7B8o=", "requires": {"arr-flatten": "^1.0.1", "is-arguments": "^1.0.2"}}, "use": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/use/-/use-2.0.2.tgz", "integrity": "sha1-riig1y+TvyJCKhii43mZMRLeyOg=", "requires": {"define-property": "^0.2.5", "isobject": "^3.0.0", "lazy-cache": "^2.0.2"}, "dependencies": {"isobject": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/isobject/-/isobject-3.0.1.tgz", "integrity": "sha1-TkMekrEalzFjaqH5yNHMvP2reN8="}}}}}, "question-store": {"version": "0.11.1", "resolved": "https://registry.npmjs.org/question-store/-/question-store-0.11.1.tgz", "integrity": "sha1-gf1NRF9NWtwqYiPCUj+nEj4E/X0=", "requires": {"common-config": "^0.1.0", "data-store": "^0.16.1", "debug": "^2.2.0", "is-answer": "^0.1.0", "lazy-cache": "^2.0.1", "project-name": "^0.2.6", "question-cache": "^0.5.1"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}}}, "raf": {"version": "3.4.1", "resolved": "https://registry.npmjs.org/raf/-/raf-3.4.1.tgz", "integrity": "sha512-Sq4CW4QhwOHE8ucn6J34MqtZCeWFP2aQSmrlroYgqAV1PjStIhJXxYuTgUIfkEk7zTLjmIjLmU5q+fbD1NnOJA==", "requires": {"performance-now": "^2.1.0"}}, "randomatic": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/randomatic/-/randomatic-3.1.1.tgz", "integrity": "sha512-TuDE5KxZ0J461RVjrJZCJc+J+zCkTb1MbH9AQUq68sMhOMcy9jLcb3BrZKgp9q9Ncltdg4QVqWrH02W2EFFVYw==", "requires": {"is-number": "^4.0.0", "kind-of": "^6.0.0", "math-random": "^1.0.1"}, "dependencies": {"is-number": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/is-number/-/is-number-4.0.0.tgz", "integrity": "sha512-rSklcAIlf1OmFdyAqbnWTLVelsQ58uvZ66S/ZyawjWqIviTWCjg2PzVGw8WUA+nNuPTqb4wgA+NszrJ+08LlgQ=="}}}, "randombytes": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/randombytes/-/randombytes-2.1.0.tgz", "integrity": "sha512-vYl3iOX+4CKUWuxGi9Ukhie6fsqXqS9FE2Zaic4tNFD2N2QQaXOMFbuKK4QmDHC0JO6B1Zp41J0LpT0oR68amQ==", "dev": true, "requires": {"safe-buffer": "^5.1.0"}}, "randomfill": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/randomfill/-/randomfill-1.0.4.tgz", "integrity": "sha512-87lcbR8+MhcWcUiQ+9e+Rwx8MyR2P7qnt15ynUlbm3TU/fjbgz4GsvfSUDTemtCCtVCqb4ZcEFlyPNTh9bBTLw==", "dev": true, "requires": {"randombytes": "^2.0.5", "safe-buffer": "^5.1.0"}}, "react": {"version": "16.8.6", "resolved": "https://registry.npmjs.org/react/-/react-16.8.6.tgz", "integrity": "sha512-pC0uMkhLaHm11ZSJULfOBqV4tIZkx87ZLvbbQYunNixAAvjnC+snJCg0XQXn9VIsttVsbZP/H/ewzgsd5fxKXw==", "requires": {"loose-envify": "^1.1.0", "object-assign": "^4.1.1", "prop-types": "^15.6.2", "scheduler": "^0.13.6"}}, "react-dom": {"version": "16.8.6", "resolved": "https://registry.npmjs.org/react-dom/-/react-dom-16.8.6.tgz", "integrity": "sha512-1nL7PIq9LTL3fthPqwkvr2zY7phIPjYrT0jp4HjyEQrEROnw4dG41VVwi/wfoCneoleqrNX7iAD+pXebJZwrwA==", "requires": {"loose-envify": "^1.1.0", "object-assign": "^4.1.1", "prop-types": "^15.6.2", "scheduler": "^0.13.6"}}, "react-input-autosize": {"version": "2.2.1", "resolved": "https://registry.npmjs.org/react-input-autosize/-/react-input-autosize-2.2.1.tgz", "integrity": "sha512-3+K4CD13iE4lQQ2WlF8PuV5htfmTRLH6MDnfndHM6LuBRszuXnuyIfE7nhSKt8AzRBZ50bu0sAhkNMeS5pxQQA==", "requires": {"prop-types": "^15.5.8"}}, "react-is": {"version": "16.8.6", "resolved": "https://registry.npmjs.org/react-is/-/react-is-16.8.6.tgz", "integrity": "sha512-aUk3bHfZ2bRSVFFbbeVS4i+lNPZr3/WM5jT2J5omUVV1zzcs1nAaf3l51ctA5FFvCRbhrH0bdAsRRQddFJZPtA=="}, "react-lifecycles-compat": {"version": "3.0.4", "resolved": "https://registry.npmjs.org/react-lifecycles-compat/-/react-lifecycles-compat-3.0.4.tgz", "integrity": "sha512-fBASbA6LnOU9dOU2eW7aQ8xmYBSXUIWr+UmF9b1efZBazGNO+rcXT/icdKnYm2pTwcRylVUYwW7H1PHfLekVzA=="}, "react-select": {"version": "3.0.4", "resolved": "https://registry.npmjs.org/react-select/-/react-select-3.0.4.tgz", "integrity": "sha512-fbVISKa/lSUlLsltuatfUiKcWCNvdLXxFFyrzVQCBUsjxJZH/m7UMPdw/ywmRixAmwXAP++MdbNNZypOsiDEfA==", "requires": {"@babel/runtime": "^7.4.4", "@emotion/cache": "^10.0.9", "@emotion/core": "^10.0.9", "@emotion/css": "^10.0.9", "classnames": "^2.2.5", "memoize-one": "^5.0.0", "prop-types": "^15.6.0", "raf": "^3.4.0", "react-input-autosize": "^2.2.1", "react-transition-group": "^2.2.1"}, "dependencies": {"react-transition-group": {"version": "2.9.0", "resolved": "https://registry.npmjs.org/react-transition-group/-/react-transition-group-2.9.0.tgz", "integrity": "sha512-+HzNTCHpeQyl4MJ/bdE0u6XRMe9+XG/+aL4mCxVN4DnPBQ0/5bfHWPDuOZUzYdMj94daZaZdCCc1Dzt9R/xSSg==", "requires": {"dom-helpers": "^3.4.0", "loose-envify": "^1.4.0", "prop-types": "^15.6.2", "react-lifecycles-compat": "^3.0.4"}}}}, "react-textarea-autosize": {"version": "7.1.0", "resolved": "https://registry.npmjs.org/react-textarea-autosize/-/react-textarea-autosize-7.1.0.tgz", "integrity": "sha512-c2FlR/fP0qbxmlrW96SdrbgP/v0XZMTupqB90zybvmDVDutytUgPl7beU35klwcTeMepUIQEpQUn3P3bdshGPg==", "requires": {"@babel/runtime": "^7.1.2", "prop-types": "^15.6.0"}}, "react-toggled": {"version": "1.2.7", "resolved": "https://registry.npmjs.org/react-toggled/-/react-toggled-1.2.7.tgz", "integrity": "sha512-3am1uA5ZzDwUkReEuUkK+fJ0DAYcGiLraWEPqXfL1kKD/NHbbB7fB/t+5FflMGd+FA6n9hih1es4pui1yzKi0w=="}, "react-transition-group": {"version": "4.2.0", "resolved": "https://registry.npmjs.org/react-transition-group/-/react-transition-group-4.2.0.tgz", "integrity": "sha512-EKfLKbqdrbrTQFxCOFCJ+wW+d461QhPJTLbARN5WKrNz3jxTnom4cPsq4xaBagQAF/7Qs5+XpG4zrtDRJH0bpA==", "requires": {"@babel/runtime": "^7.4.5", "dom-helpers": "^3.4.0", "loose-envify": "^1.4.0", "prop-types": "^15.6.2"}}, "read-file": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/read-file/-/read-file-0.2.0.tgz", "integrity": "sha1-cMa6+IQux9FUD5gf0Oau1Mgb1UU="}, "readable-stream": {"version": "2.3.6", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.6.tgz", "integrity": "sha512-tQtKA9WIAhBF3+VLAseyMqZeBjW0AHJoxOtYqSUZNJxauErmLbVm2FW1y+J/YA9dUrAC39ITejlZWhVIwawkKw==", "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "readdirp": {"version": "2.2.1", "resolved": "https://registry.npmjs.org/readdirp/-/readdirp-2.2.1.tgz", "integrity": "sha512-1JU/8q+VgFZyxwrJ+SVIOsh+KywWGpds3NTqikiKpDMZWScmAYyKIgqkO+ARvNWJfXeXR1zxz7aHF4u4CyH6vQ==", "dev": true, "requires": {"graceful-fs": "^4.1.11", "micromatch": "^3.1.10", "readable-stream": "^2.0.2"}}, "readline2": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/readline2/-/readline2-1.0.1.tgz", "integrity": "sha1-QQWWCP/BVHV7cV2ZidGZ/783LjU=", "requires": {"code-point-at": "^1.0.0", "is-fullwidth-code-point": "^1.0.0", "mute-stream": "0.0.5"}, "dependencies": {"is-fullwidth-code-point": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-1.0.0.tgz", "integrity": "sha1-754xOG8DGn8NZDr4L95QxFfvAMs=", "requires": {"number-is-nan": "^1.0.0"}}}}, "recompose": {"version": "0.23.5", "resolved": "https://registry.npmjs.org/recompose/-/recompose-0.23.5.tgz", "integrity": "sha1-cqyCYSRr7DeCNdGHRn0CpyHosd4=", "requires": {"change-emitter": "^0.1.2", "fbjs": "^0.8.1", "hoist-non-react-statics": "^1.0.0", "symbol-observable": "^1.0.4"}, "dependencies": {"hoist-non-react-statics": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-1.2.0.tgz", "integrity": "sha1-qkSM8JhtVcxAdzsXF0t90GbLfPs="}}}, "reduce-object": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/reduce-object/-/reduce-object-0.1.3.tgz", "integrity": "sha1-1UnUCmwpNvpOPpt4yonJMxRZQhg=", "requires": {"for-own": "^0.1.1"}}, "regenerate": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/regenerate/-/regenerate-1.4.0.tgz", "integrity": "sha512-1G6jJVDWrt0rK99kBjvEtziZNCICAuvIPkSiUFIQxVP06RCVpq3dmDo2oi6ABpYaDYaTRr67BEhL8r1wgEZZKg==", "dev": true}, "regenerate-unicode-properties": {"version": "8.1.0", "resolved": "https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-8.1.0.tgz", "integrity": "sha512-LGZzkgtLY79GeXLm8Dp0BVLdQlWICzBnJz/ipWUgo59qBaZ+BHtq51P2q1uVZlppMuUAT37SDk39qUbjTWB7bA==", "dev": true, "requires": {"regenerate": "^1.4.0"}}, "regenerator-runtime": {"version": "0.13.2", "resolved": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.13.2.tgz", "integrity": "sha512-S/TQAZJO+D3m9xeN1WTI8dLKBBiRgXBlTJvbWjCThHWZj9EvHK70Ff50/tYj2J/fvBY6JtFVwRuazHN2E7M9BA=="}, "regenerator-transform": {"version": "0.14.0", "resolved": "https://registry.npmjs.org/regenerator-transform/-/regenerator-transform-0.14.0.tgz", "integrity": "sha512-rtOelq4Cawlbmq9xuMR5gdFmv7ku/sFoB7sRiywx7aq53bc52b4j6zvH7Te1Vt/X2YveDKnCGUbioieU7FEL3w==", "dev": true, "requires": {"private": "^0.1.6"}}, "regex-cache": {"version": "0.4.4", "resolved": "https://registry.npmjs.org/regex-cache/-/regex-cache-0.4.4.tgz", "integrity": "sha512-nVIZwtCjkC9YgvWkpM55B5rBhBYRZhAaJbgcFYXXsHnbZ9UZI9nnVWYZpBlCqv9ho2eZryPnWrZGsOdPwVWXWQ==", "requires": {"is-equal-shallow": "^0.1.3"}}, "regex-not": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/regex-not/-/regex-not-1.0.2.tgz", "integrity": "sha512-J6SDjUgDxQj5NusnOtdFxDwN/+HWykR8GELwctJ7mdqhcyy1xEc4SRFHUXvxTp661YaVKAjfRLZ9cCqS6tn32A==", "dev": true, "requires": {"extend-shallow": "^3.0.2", "safe-regex": "^1.1.0"}}, "regexp-tree": {"version": "0.1.10", "resolved": "https://registry.npmjs.org/regexp-tree/-/regexp-tree-0.1.10.tgz", "integrity": "sha512-K1qVSbcedffwuIslMwpe6vGlj+ZXRnGkvjAtFHfDZZZuEdA/h0dxljAPu9vhUo6Rrx2U2AwJ+nSQ6hK+lrP5MQ==", "dev": true}, "regexpu-core": {"version": "4.5.4", "resolved": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-4.5.4.tgz", "integrity": "sha512-BtizvGtFQKGPUcTy56o3nk1bGRp4SZOTYrDtGNlqCQufptV5IkkLN6Emw+yunAJjzf+C9FQFtvq7IoA3+oMYHQ==", "dev": true, "requires": {"regenerate": "^1.4.0", "regenerate-unicode-properties": "^8.0.2", "regjsgen": "^0.5.0", "regjsparser": "^0.6.0", "unicode-match-property-ecmascript": "^1.0.4", "unicode-match-property-value-ecmascript": "^1.1.0"}}, "regjsgen": {"version": "0.5.0", "resolved": "https://registry.npmjs.org/regjsgen/-/regjsgen-0.5.0.tgz", "integrity": "sha512-RnIrLhrXCX5ow/E5/Mh2O4e/oa1/jW0eaBKTSy3LaCj+M3Bqvm97GWDp2yUtzIs4LEn65zR2yiYGFqb2ApnzDA==", "dev": true}, "regjsparser": {"version": "0.6.0", "resolved": "https://registry.npmjs.org/regjsparser/-/regjsparser-0.6.0.tgz", "integrity": "sha512-RQ7YyokLiQBomUJuUG8iGVvkgOLxwyZM8k6d3q5SAXpg4r5TZJZigKFvC6PpD+qQ98bCDC5YelPeA3EucDoNeQ==", "dev": true, "requires": {"jsesc": "~0.5.0"}, "dependencies": {"jsesc": {"version": "0.5.0", "resolved": "https://registry.npmjs.org/jsesc/-/jsesc-0.5.0.tgz", "integrity": "sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0=", "dev": true}}}, "relative": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/relative/-/relative-3.0.2.tgz", "integrity": "sha1-Dc2OxUpdNaPBXhBFA9ZTdbWlNn8=", "requires": {"isobject": "^2.0.0"}, "dependencies": {"isobject": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "requires": {"isarray": "1.0.0"}}}}, "remote-origin-url": {"version": "0.5.3", "resolved": "https://registry.npmjs.org/remote-origin-url/-/remote-origin-url-0.5.3.tgz", "integrity": "sha512-crQ7Xk1m/F2IiwBx5oTqk/c0hjoumrEz+a36+ZoVupskQRE/q7pAwHKsTNeiZ31sbSTELvVlVv4h1W0Xo5szKg==", "requires": {"parse-git-config": "^1.1.1"}}, "remove-trailing-separator": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/remove-trailing-separator/-/remove-trailing-separator-1.1.0.tgz", "integrity": "sha1-wkvOKig62tW8P1jg1IJJuSN52O8="}, "repeat-element": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/repeat-element/-/repeat-element-1.1.3.tgz", "integrity": "sha512-ahGq0ZnV5m5XtZLMb+vP76kcAM5nkLqk0lpqAuojSKGgQtn4eRi4ZZGm2olo2zKFH+sMsWaqOCW1dqAnOru72g=="}, "repeat-string": {"version": "1.6.1", "resolved": "https://registry.npmjs.org/repeat-string/-/repeat-string-1.6.1.tgz", "integrity": "sha1-jcrkcOHIirwtYA//Sndihtp15jc="}, "replace-ext": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/replace-ext/-/replace-ext-0.0.1.tgz", "integrity": "sha1-KbvZIHinOfC8zitO5B6DeVNSKSQ="}, "repo-utils": {"version": "0.3.7", "resolved": "https://registry.npmjs.org/repo-utils/-/repo-utils-0.3.7.tgz", "integrity": "sha1-SrZq80DLEfp+XPgFgekr6Xwb964=", "requires": {"extend-shallow": "^2.0.1", "get-value": "^2.0.6", "git-config-path": "^1.0.1", "is-absolute": "^0.2.6", "kind-of": "^3.0.4", "lazy-cache": "^2.0.1", "mixin-deep": "^1.1.3", "omit-empty": "^0.4.1", "parse-author": "^1.0.0", "parse-git-config": "^1.0.2", "parse-github-url": "^0.3.2", "project-name": "^0.2.6"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}, "kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}}}, "require-directory": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz", "integrity": "sha1-jGStX9MNqxyXbiNE/+f3kqam30I=", "dev": true}, "require-main-filename": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/require-main-filename/-/require-main-filename-2.0.0.tgz", "integrity": "sha512-NKN5kMDylKuldxYLSUfrbo5Tuzh4hd+2E8NPPX02mZtn1VuREQToYe/ZdlJy+J3uCpfaiGF05e7B8W0iXbQHmg==", "dev": true}, "resize-observer-polyfill": {"version": "1.5.1", "resolved": "https://registry.npmjs.org/resize-observer-polyfill/-/resize-observer-polyfill-1.5.1.tgz", "integrity": "sha512-LwZrotdHOo12nQuZlHEmtuXdqGoOD0OhaxopaNFxWzInpEgaLWoVuAMbTzixuosCx2nEG58ngzW3vxdWoxIgdg=="}, "resolve": {"version": "1.11.1", "resolved": "https://registry.npmjs.org/resolve/-/resolve-1.11.1.tgz", "integrity": "sha512-vIpgF6wfuJOZI7KKKSP+HmiKggadPQAdsp5HiC1mvqnfp0gF1vdwgBWZIdrVft9pgqoMFQN+R7BSWZiBxx+BBw==", "requires": {"path-parse": "^1.0.6"}}, "resolve-cwd": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/resolve-cwd/-/resolve-cwd-2.0.0.tgz", "integrity": "sha1-AKn3OHVW4nA46uIyyqNypqWbZlo=", "dev": true, "requires": {"resolve-from": "^3.0.0"}}, "resolve-dir": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/resolve-dir/-/resolve-dir-1.0.1.tgz", "integrity": "sha1-eaQGRMNivoLybv/nOcm7U4IEb0M=", "requires": {"expand-tilde": "^2.0.0", "global-modules": "^1.0.0"}, "dependencies": {"global-modules": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/global-modules/-/global-modules-1.0.0.tgz", "integrity": "sha512-sKzpEkf11GpOFuw0Zzjzmt4B4UZwjOcG757PPvrfhxcLFbq0wpsgpOqxpxtxFiCG4DtG93M6XRVbF2oGdev7bg==", "requires": {"global-prefix": "^1.0.1", "is-windows": "^1.0.1", "resolve-dir": "^1.0.0"}}}}, "resolve-file": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/resolve-file/-/resolve-file-0.2.2.tgz", "integrity": "sha1-FNvsWhnThPXW3GSin9ZigV0xdpY=", "requires": {"cwd": "^0.10.0", "expand-tilde": "^2.0.1", "extend-shallow": "^2.0.1", "fs-exists-sync": "^0.1.0", "global-modules": "^0.2.3", "homedir-polyfill": "^1.0.0", "lazy-cache": "^2.0.1", "resolve": "^1.1.7"}, "dependencies": {"cwd": {"version": "0.10.0", "resolved": "https://registry.npmjs.org/cwd/-/cwd-0.10.0.tgz", "integrity": "sha1-FyQAaUBXwioTsM8WFix+S3p/5Wc=", "requires": {"find-pkg": "^0.1.2", "fs-exists-sync": "^0.1.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}, "global-modules": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/global-modules/-/global-modules-0.2.3.tgz", "integrity": "sha1-6lo77ULG1s6ZWk+KEmm12uIjgo0=", "requires": {"global-prefix": "^0.1.4", "is-windows": "^0.2.0"}}, "global-prefix": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/global-prefix/-/global-prefix-0.1.5.tgz", "integrity": "sha1-jTvGuNo8qBEqFg2NSW/wRiv+948=", "requires": {"homedir-polyfill": "^1.0.0", "ini": "^1.3.4", "is-windows": "^0.2.0", "which": "^1.2.12"}}, "is-windows": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/is-windows/-/is-windows-0.2.0.tgz", "integrity": "sha1-3hqm1j6indJIc3tp8f+LgALSEIw="}}}, "resolve-from": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/resolve-from/-/resolve-from-3.0.0.tgz", "integrity": "sha1-six699nWiBvItuZTM17rywoYh0g="}, "resolve-glob": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/resolve-glob/-/resolve-glob-1.0.0.tgz", "integrity": "sha512-wSW9pVGJRs89k0wEXhM7C6+va9998NsDhgc0Y+6Nv8hrHsu0hUS7Ug10J1EiVtU6N2tKlSNvx9wLihL8Ao22Lg==", "requires": {"extend-shallow": "^2.0.1", "is-valid-glob": "^1.0.0", "matched": "^1.0.2", "relative": "^3.0.2", "resolve-dir": "^1.0.0"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}, "is-valid-glob": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-valid-glob/-/is-valid-glob-1.0.0.tgz", "integrity": "sha1-Kb8+/3Ab4tTTFdusw5vDn+j2Aao="}}}, "resolve-url": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/resolve-url/-/resolve-url-0.2.1.tgz", "integrity": "sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo=", "dev": true}, "restore-cursor": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/restore-cursor/-/restore-cursor-1.0.1.tgz", "integrity": "sha1-NGYfRohjJ/7SmRR5FSJS35LapUE=", "requires": {"exit-hook": "^1.0.0", "onetime": "^1.0.0"}}, "ret": {"version": "0.1.15", "resolved": "https://registry.npmjs.org/ret/-/ret-0.1.15.tgz", "integrity": "sha512-TTlYpa+OL+vMMNG24xSlQGEJ3B/RzEfUlLct7b5G/ytav+wPrplCpVMFuwzXbkecJrb6IYo1iFb0S9v37754mg==", "dev": true}, "rethrow": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/rethrow/-/rethrow-0.2.3.tgz", "integrity": "sha1-xVKPGQ6J7HU1iJRSob5omWtfZhY=", "requires": {"ansi-bgred": "^0.1.1", "ansi-red": "^0.1.1", "ansi-yellow": "^0.1.1", "extend-shallow": "^1.1.4", "lazy-cache": "^0.2.3", "right-align": "^0.1.3"}, "dependencies": {"extend-shallow": {"version": "1.1.4", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-1.1.4.tgz", "integrity": "sha1-Gda/lN/AnXa6cR85uHLSH/TdkHE=", "requires": {"kind-of": "^1.1.0"}}, "kind-of": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-1.1.0.tgz", "integrity": "sha1-FAo9LUGjbS78+pN3tiwk+ElaXEQ="}, "lazy-cache": {"version": "0.2.7", "resolved": "https://registry.npmjs.org/lazy-cache/-/lazy-cache-0.2.7.tgz", "integrity": "sha1-f+3fLctu23fRHvHRF6tf/fCrG2U="}}}, "rfc6902": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/rfc6902/-/rfc6902-3.0.2.tgz", "integrity": "sha512-Ky6zSlAL5HVu28/BQ+R8vgrKxgTKYBygww9D+TSms7Mylg+mSFdFVtGpHr+XZ8TFiAWslsqaK6qHbrl7XvYjWA=="}, "right-align": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/right-align/-/right-align-0.1.3.tgz", "integrity": "sha1-YTObci/mo1FWiSENJOFMlhSGE+8=", "requires": {"align-text": "^0.1.1"}}, "rimraf": {"version": "2.6.3", "resolved": "https://registry.npmjs.org/rimraf/-/rimraf-2.6.3.tgz", "integrity": "sha512-mwqeW5XsA2qAejG46gYdENaxXjx9onRNCfn7L0duuP4hCuTIi/QO7PDK07KJfp1d+izWPrzEJDcSqBa0OZQriA==", "requires": {"glob": "^7.1.3"}}, "ripemd160": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/ripemd160/-/ripemd160-2.0.2.tgz", "integrity": "sha512-ii4iagi25WusVoiC4B4lq7pbXfAp3D9v5CwfkY33vffw2+pkDjY1D8GaN7spsxvCSx8dkPqOZCEZyfxcmJG2IA==", "dev": true, "requires": {"hash-base": "^3.0.0", "inherits": "^2.0.1"}}, "run-async": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/run-async/-/run-async-0.1.0.tgz", "integrity": "sha1-yK1KXhEGYeQCp9IbUw4AnyX444k=", "requires": {"once": "^1.3.0"}}, "run-queue": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/run-queue/-/run-queue-1.0.3.tgz", "integrity": "sha1-6Eg5bwV9Ij8kOGkkYY4laUFh7Ec=", "dev": true, "requires": {"aproba": "^1.1.1"}}, "rx-lite": {"version": "4.0.8", "resolved": "https://registry.npmjs.org/rx-lite/-/rx-lite-4.0.8.tgz", "integrity": "sha1-Cx4Rr4vESDbwSmQH6S2kJGe3lEQ="}, "safe-buffer": {"version": "5.1.2", "resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz", "integrity": "sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g=="}, "safe-regex": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/safe-regex/-/safe-regex-1.1.0.tgz", "integrity": "sha1-QKNmnzsHfR6UPURinhV91IAjvy4=", "dev": true, "requires": {"ret": "~0.1.10"}}, "safer-buffer": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz", "integrity": "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg=="}, "scheduler": {"version": "0.13.6", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.13.6.tgz", "integrity": "sha512-IWnObHt413ucAYKsD9J1QShUKkbKLQQHdxRyw73sw4FN26iWr3DY/H34xGPe4nmL1DwXyWmSWmMrA9TfQbE/XQ==", "requires": {"loose-envify": "^1.1.0", "object-assign": "^4.1.1"}}, "schema-utils": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/schema-utils/-/schema-utils-1.0.0.tgz", "integrity": "sha512-i27Mic4KovM/lnGsy8whRCHhc7VicJajAjTrYg11K9zfZXnYIt4k5F+kZkwjnrhKzLic/HLU4j11mjsz2G/75g==", "dev": true, "requires": {"ajv": "^6.1.0", "ajv-errors": "^1.0.0", "ajv-keywords": "^3.1.0"}}, "semver": {"version": "5.7.0", "resolved": "https://registry.npmjs.org/semver/-/semver-5.7.0.tgz", "integrity": "sha512-Ya52jSX2u7QKghxeoFGpLwCtGlt7j0oY9DYb5apt9nPlJ42ID+ulTXESnt/qAQcoSERyZ5sl3LDIOw0nAn/5DA=="}, "serialize-javascript": {"version": "1.7.0", "resolved": "https://registry.npmjs.org/serialize-javascript/-/serialize-javascript-1.7.0.tgz", "integrity": "sha512-ke8UG8ulpFOxO8f8gRYabHQe/ZntKlcig2Mp+8+URDP1D8vJZ0KUt7LYo07q25Z/+JVSgpr/cui9PIp5H6/+nA==", "dev": true}, "set-blocking": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/set-blocking/-/set-blocking-2.0.0.tgz", "integrity": "sha1-BF+XgtARrppoA93TgrJDkrPYkPc="}, "set-getter": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/set-getter/-/set-getter-0.1.0.tgz", "integrity": "sha1-12nBgsnVpR9AkUXy+6guXoboA3Y=", "requires": {"to-object-path": "^0.3.0"}}, "set-value": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/set-value/-/set-value-2.0.1.tgz", "integrity": "sha512-JxHc1weCN68wRY0fhCoXpyK55m/XPHafOmK4UWD7m2CI14GMcFypt4w/0+NV5f/ZMby2F6S2wwA7fgynh9gWSw==", "requires": {"extend-shallow": "^2.0.1", "is-extendable": "^0.1.1", "is-plain-object": "^2.0.3", "split-string": "^3.0.1"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}}}, "setimmediate": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/setimmediate/-/setimmediate-1.0.5.tgz", "integrity": "sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU="}, "sha.js": {"version": "2.4.11", "resolved": "https://registry.npmjs.org/sha.js/-/sha.js-2.4.11.tgz", "integrity": "sha512-QMEp5B7cftE7APOjk5Y6xgrbWu+WkLVQwk8JNjZ8nKRciZaByEW6MubieAiToS7+dwvrjGhH8jRXz3MVd0AYqQ==", "dev": true, "requires": {"inherits": "^2.0.1", "safe-buffer": "^5.0.1"}}, "shallow-clone": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/shallow-clone/-/shallow-clone-0.1.2.tgz", "integrity": "sha1-WQnodLp3EG1zrEFM/sH/yofZcGA=", "requires": {"is-extendable": "^0.1.1", "kind-of": "^2.0.1", "lazy-cache": "^0.2.3", "mixin-object": "^2.0.1"}, "dependencies": {"kind-of": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-2.0.1.tgz", "integrity": "sha1-AY7HpM5+OobLkUG+UZ0kyPqpgbU=", "requires": {"is-buffer": "^1.0.2"}}, "lazy-cache": {"version": "0.2.7", "resolved": "https://registry.npmjs.org/lazy-cache/-/lazy-cache-0.2.7.tgz", "integrity": "sha1-f+3fLctu23fRHvHRF6tf/fCrG2U="}}}, "shebang-command": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/shebang-command/-/shebang-command-1.2.0.tgz", "integrity": "sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=", "dev": true, "requires": {"shebang-regex": "^1.0.0"}}, "shebang-regex": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/shebang-regex/-/shebang-regex-1.0.0.tgz", "integrity": "sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM=", "dev": true}, "signal-exit": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.2.tgz", "integrity": "sha1-tf3AjxKH6hF4Yo5BXiUTK3NkbG0=", "dev": true}, "snapdragon": {"version": "0.8.2", "resolved": "https://registry.npmjs.org/snapdragon/-/snapdragon-0.8.2.tgz", "integrity": "sha512-FtyOnWN/wCHTVXOMwvSv26d+ko5vWlIDD6zoUJ7LW8vh+ZBC8QdljveRP+crNrtBwioEUWy/4dMtbBjA4ioNlg==", "dev": true, "requires": {"base": "^0.11.1", "debug": "^2.2.0", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "map-cache": "^0.2.2", "source-map": "^0.5.6", "source-map-resolve": "^0.5.0", "use": "^3.1.0"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "dev": true, "requires": {"ms": "2.0.0"}}, "define-property": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "dev": true, "requires": {"is-descriptor": "^0.1.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "requires": {"is-extendable": "^0.1.0"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}}}, "snapdragon-node": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/snapdragon-node/-/snapdragon-node-2.1.1.tgz", "integrity": "sha512-O27l4xaMYt/RSQ5TR3vpWCAB5Kb/czIcqUFOM/C4fYcLnbZUc1PkjTAMjof2pBWaSTwOUd6qUHcFGVGj7aIwnw==", "dev": true, "requires": {"define-property": "^1.0.0", "isobject": "^3.0.0", "snapdragon-util": "^3.0.1"}, "dependencies": {"define-property": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/define-property/-/define-property-1.0.0.tgz", "integrity": "sha1-dp66rz9KY6rTr56NMEybvnm/sOY=", "dev": true, "requires": {"is-descriptor": "^1.0.0"}}, "is-accessor-descriptor": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-accessor-descriptor/-/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha512-m5hnHTkcVsPfqx3AKlyttIPb7J+XykHvJP2B9bZDjlhLIoEq4XoK64Vg7boZlVWYK6LUY94dYPEE7Lh0ZkZKcQ==", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz", "integrity": "sha512-jbRXy1FmtAoCjQkVmIVYwuuqDFUbaOeDjmed1tOGPrsMhtJA4rD9tkgA0F1qJ3gRFRXcHYVkdeaP50Q5rE/jLQ==", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/is-descriptor/-/is-descriptor-1.0.2.tgz", "integrity": "sha512-2eis5WqQGV7peooDyLmNEPUrps9+SXX5c9pL3xEB+4e9HnGuDa7mB7kHxHw4CbqS9k1T2hOH3miL8n8WtiYVtg==", "dev": true, "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}}}, "snapdragon-util": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/snapdragon-util/-/snapdragon-util-3.0.1.tgz", "integrity": "sha512-mbKkMdQKsjX4BAL4bRYTj21edOf8cN7XHdYUJEe+Zn99hVEYcMvKPct1IqNe7+AZPirn8BCDOQBHQZknqmKlZQ==", "dev": true, "requires": {"kind-of": "^3.2.0"}, "dependencies": {"kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "sort-object-arrays": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/sort-object-arrays/-/sort-object-arrays-0.1.1.tgz", "integrity": "sha1-mfVc8gWkkd3h9S8Jajaiawm0gy8=", "requires": {"kind-of": "^3.0.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}}}, "source-list-map": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/source-list-map/-/source-list-map-2.0.1.tgz", "integrity": "sha512-qnQ7gVMxGNxsiL4lEuJwe/To8UnK7fAnmbGEEH8RpLouuKbeEm0lhbQVFIrNSuB+G7tVrAlVsZgETT5nljf+Iw==", "dev": true}, "source-map": {"version": "0.5.7", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz", "integrity": "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w="}, "source-map-resolve": {"version": "0.5.2", "resolved": "https://registry.npmjs.org/source-map-resolve/-/source-map-resolve-0.5.2.tgz", "integrity": "sha512-MjqsvNwyz1s0k81Goz/9vRBe9SZdB09Bdw+/zYyO+3CuPk6fouTaxscHkgtE8jKvf01kVfl8riHzERQ/kefaSA==", "dev": true, "requires": {"atob": "^2.1.1", "decode-uri-component": "^0.2.0", "resolve-url": "^0.2.1", "source-map-url": "^0.4.0", "urix": "^0.1.0"}}, "source-map-support": {"version": "0.5.12", "resolved": "https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.12.tgz", "integrity": "sha512-4h2Pbvyy15EE02G+JOZpUCmqWJuqrs+sEkzewTm++BPi7Hvn/HwcqLAcNxYAyI0x13CpPPn+kMjl+hplXMHITQ==", "dev": true, "requires": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}, "dependencies": {"source-map": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz", "integrity": "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==", "dev": true}}}, "source-map-url": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/source-map-url/-/source-map-url-0.4.0.tgz", "integrity": "sha1-PpNdfd1zYxuXZZlW1VEo6HtQhKM=", "dev": true}, "split-string": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/split-string/-/split-string-3.1.0.tgz", "integrity": "sha512-NzNVhJDYpwceVVii8/Hu6DKfD2G+NrQHlS/V/qgv763EYudVwEcMQNxd2lh+0VrUByXN/oJkl5grOhYWvQUYiw==", "requires": {"extend-shallow": "^3.0.0"}}, "sprintf-js": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.0.3.tgz", "integrity": "sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw="}, "src-stream": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/src-stream/-/src-stream-0.1.1.tgz", "integrity": "sha1-2T9G0oGjcAKB7A8wszoDFDiUpoE=", "requires": {"duplexify": "^3.4.2", "merge-stream": "^0.1.8", "through2": "^2.0.0"}, "dependencies": {"isarray": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/isarray/-/isarray-0.0.1.tgz", "integrity": "sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8="}, "merge-stream": {"version": "0.1.8", "resolved": "https://registry.npmjs.org/merge-stream/-/merge-stream-0.1.8.tgz", "integrity": "sha1-SKB7O0oSHXSj7b/c20sIrb8CQLE=", "requires": {"through2": "^0.6.1"}, "dependencies": {"through2": {"version": "0.6.5", "resolved": "https://registry.npmjs.org/through2/-/through2-0.6.5.tgz", "integrity": "sha1-QaucZ7KdVyCQcUEOHXp6lozTrUg=", "requires": {"readable-stream": ">=1.0.33-1 <1.1.0-0", "xtend": ">=4.0.0 <4.1.0-0"}}}}, "readable-stream": {"version": "1.0.34", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.0.34.tgz", "integrity": "sha1-Elgg40vIQtLyqq+v5MKRbuMsFXw=", "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "string_decoder": {"version": "0.10.31", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-0.10.31.tgz", "integrity": "sha1-YuIDvEF2bGwoyfyEMB2rHFMQ+pQ="}}}, "ssri": {"version": "6.0.1", "resolved": "https://registry.npmjs.org/ssri/-/ssri-6.0.1.tgz", "integrity": "sha512-3Wge10hNcT1Kur4PDFwEieXSCMCJs/7WvSACcrMYrNp+b8kDL1/0wJch5Ni2WrtwEa2IO8OsVfeKIciKCDx/QA==", "dev": true, "requires": {"figgy-pudding": "^3.5.1"}}, "static-extend": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/static-extend/-/static-extend-0.1.2.tgz", "integrity": "sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY=", "requires": {"define-property": "^0.2.5", "object-copy": "^0.1.0"}, "dependencies": {"define-property": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "requires": {"is-descriptor": "^0.1.0"}}}}, "stream-browserify": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/stream-browserify/-/stream-browserify-2.0.2.tgz", "integrity": "sha512-nX6hmklHs/gr2FuxYDltq8fJA1GDlxKQCz8O/IM4atRqBH8OORmBNgfvW5gG10GT/qQ9u0CzIvr2X5Pkt6ntqg==", "dev": true, "requires": {"inherits": "~2.0.1", "readable-stream": "^2.0.2"}}, "stream-combiner": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/stream-combiner/-/stream-combiner-0.2.2.tgz", "integrity": "sha1-rsjLrBd7Vrb0+kec7YwZEs7lKFg=", "requires": {"duplexer": "~0.1.1", "through": "~2.3.4"}}, "stream-each": {"version": "1.2.3", "resolved": "https://registry.npmjs.org/stream-each/-/stream-each-1.2.3.tgz", "integrity": "sha512-vlMC2f8I2u/bZGqkdfLQW/13Zihpej/7PmSiMQsbYddxuTsJp8vRe2x2FvVExZg7FaOds43ROAuFJwPR4MTZLw==", "dev": true, "requires": {"end-of-stream": "^1.1.0", "stream-shift": "^1.0.0"}}, "stream-exhaust": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/stream-exhaust/-/stream-exhaust-1.0.2.tgz", "integrity": "sha512-b/qaq/GlBK5xaq1yrK9/zFcyRSTNxmcZwFLGSTG0mXgZl/4Z6GgiyYOXOvY7N3eEvFRAG1bkDRz5EPGSvPYQlw=="}, "stream-http": {"version": "2.8.3", "resolved": "https://registry.npmjs.org/stream-http/-/stream-http-2.8.3.tgz", "integrity": "sha512-+TSkfINHDo4J+ZobQLWiMouQYB+UVYFttRA94FpEzzJ7ZdqcL4uUUQ7WkdkI4DSozGmgBUE/a47L+38PenXhUw==", "dev": true, "requires": {"builtin-status-codes": "^3.0.0", "inherits": "^2.0.1", "readable-stream": "^2.3.6", "to-arraybuffer": "^1.0.0", "xtend": "^4.0.0"}}, "stream-shift": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/stream-shift/-/stream-shift-1.0.0.tgz", "integrity": "sha1-1cdSgl5TZ+eG944Y5EXqIjoVWVI="}, "string-width": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/string-width/-/string-width-3.1.0.tgz", "integrity": "sha512-vafcv6KjVZKSgz06oM/H6GDBrAtz8vdhQakGjFIvNrHA6y3HCF1CInLy+QLq8dTJPQ1b+KDUqDFctkdRW44e1w==", "dev": true, "requires": {"emoji-regex": "^7.0.1", "is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^5.1.0"}}, "string_decoder": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.1.1.tgz", "integrity": "sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==", "requires": {"safe-buffer": "~5.1.0"}}, "stringify-author": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/stringify-author/-/stringify-author-0.1.3.tgz", "integrity": "sha1-1YHgLOC1XNo8lT5irdIR+uSw72Y="}, "strip-ansi": {"version": "5.2.0", "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-5.2.0.tgz", "integrity": "sha512-DuRs1gKbBqsMKIZlrffwlug8MHkcnpjs5VPmL1PAh+mA30U0DTotfDZ0d2UUsXpPmPmMMJ6W773MaA3J+lbiWA==", "dev": true, "requires": {"ansi-regex": "^4.1.0"}}, "strip-bom": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/strip-bom/-/strip-bom-2.0.0.tgz", "integrity": "sha1-YhmoVhZSBJHzV4i9vxRHqZx+aw4=", "requires": {"is-utf8": "^0.2.0"}}, "strip-bom-buffer": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/strip-bom-buffer/-/strip-bom-buffer-0.1.1.tgz", "integrity": "sha1-yj3cSRnBP5/d8wsd/xAKmDUki00=", "requires": {"is-buffer": "^1.1.0", "is-utf8": "^0.2.0"}}, "strip-bom-stream": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/strip-bom-stream/-/strip-bom-stream-1.0.0.tgz", "integrity": "sha1-5xRDmFd9Uaa+0PoZlPoF9D/ZiO4=", "requires": {"first-chunk-stream": "^1.0.0", "strip-bom": "^2.0.0"}}, "strip-bom-string": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/strip-bom-string/-/strip-bom-string-0.1.2.tgz", "integrity": "sha1-nG5yCjE7qYNliVGEBcz7iKX0G5w="}, "strip-color": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/strip-color/-/strip-color-0.1.0.tgz", "integrity": "sha1-EG9l09PmotlAHKwOsM6LinArT3s="}, "strip-eof": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/strip-eof/-/strip-eof-1.0.0.tgz", "integrity": "sha1-u0P/VZim6wXYm1n80SnJgzE2Br8=", "dev": true}, "style-loader": {"version": "0.23.1", "resolved": "https://registry.npmjs.org/style-loader/-/style-loader-0.23.1.tgz", "integrity": "sha512-XK+uv9kWwhZMZ1y7mysB+zoihsEj4wneFWAS5qoiLwzW0WzSqMrrsIy+a3zkQJq0ipFtBpX5W3MqyRIBF/WFGg==", "dev": true, "requires": {"loader-utils": "^1.1.0", "schema-utils": "^1.0.0"}}, "stylis": {"version": "3.5.4", "resolved": "https://registry.npmjs.org/stylis/-/stylis-3.5.4.tgz", "integrity": "sha512-8/3pSmthWM7lsPBKv7NXkzn2Uc9W7NotcwGNpJaa3k7WMM1XDCA4MgT5k/8BIexd5ydZdboXtU90XH9Ec4Bv/Q=="}, "stylis-custom-properties": {"version": "0.0.3", "resolved": "https://registry.npmjs.org/stylis-custom-properties/-/stylis-custom-properties-0.0.3.tgz", "integrity": "sha1-Ei4Tuwb1mAU5U34iYMxULksFvFE="}, "stylis-rule-sheet": {"version": "0.0.10", "resolved": "https://registry.npmjs.org/stylis-rule-sheet/-/stylis-rule-sheet-0.0.10.tgz", "integrity": "sha512-nTbZoaqoBnmK+ptANthb10ZRZOGC+EmTLLUxeYIuHNkEKcmKgXX1XWKkUBT2Ac4es3NybooPe0SmvKdhKJZAuw=="}, "success-symbol": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/success-symbol/-/success-symbol-0.1.0.tgz", "integrity": "sha1-JAIuSG878c3KCUKDt2nEctO3KJc="}, "supports-color": {"version": "5.5.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz", "integrity": "sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==", "dev": true, "requires": {"has-flag": "^3.0.0"}}, "symbol-observable": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/symbol-observable/-/symbol-observable-1.2.0.tgz", "integrity": "sha512-e900nM8RRtGhlV36KGEU9k65K3mPb1WV70OdjfxlG2EAuM1noi/E/BaW/uMhL7bPEssK8QV57vN3esixjUvcXQ=="}, "tableize-object": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/tableize-object/-/tableize-object-0.1.0.tgz", "integrity": "sha1-fCngEzsn1ItWuedtOijSQd8bOiQ=", "requires": {"isobject": "^2.0.0"}, "dependencies": {"isobject": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "requires": {"isarray": "1.0.0"}}}}, "tapable": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/tapable/-/tapable-1.1.3.tgz", "integrity": "sha512-4WK/bYZmj8xLr+HUCODHGF1ZFzsYffasLUgEiMBY4fgtltdO6B4WJtlSbPaDTLpYTcGVwM2qLnFTICEcNxs3kA==", "dev": true}, "template-error": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/template-error/-/template-error-0.1.2.tgz", "integrity": "sha1-GMn2ANkPLz37oIM+N/fLb0E1QtQ=", "requires": {"engine": "^0.1.5", "kind-of": "^2.0.1", "lazy-cache": "^0.2.3", "rethrow": "^0.2.3"}, "dependencies": {"kind-of": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-2.0.1.tgz", "integrity": "sha1-AY7HpM5+OobLkUG+UZ0kyPqpgbU=", "requires": {"is-buffer": "^1.0.2"}}, "lazy-cache": {"version": "0.2.7", "resolved": "https://registry.npmjs.org/lazy-cache/-/lazy-cache-0.2.7.tgz", "integrity": "sha1-f+3fLctu23fRHvHRF6tf/fCrG2U="}}}, "templates": {"version": "0.24.3", "resolved": "https://registry.npmjs.org/templates/-/templates-0.24.3.tgz", "integrity": "sha1-i6uicOGlcnR022hXX4Ic4bT+TQU=", "requires": {"array-sort": "^0.1.2", "async-each": "^1.0.0", "base": "^0.11.1", "base-data": "^0.6.0", "base-engines": "^0.2.0", "base-helpers": "^0.1.1", "base-option": "^0.8.3", "base-plugins": "^0.4.13", "base-routes": "^0.2.1", "debug": "^2.2.0", "deep-bind": "^0.3.0", "define-property": "^0.2.5", "engine-base": "^0.1.2", "export-files": "^2.1.1", "extend-shallow": "^2.0.1", "falsey": "^0.3.0", "get-value": "^2.0.6", "get-view": "^0.1.1", "group-array": "^0.3.0", "has-glob": "^0.1.1", "has-value": "^0.3.1", "inflection": "^1.10.0", "is-valid-app": "^0.2.0", "layouts": "^0.11.0", "lazy-cache": "^2.0.1", "match-file": "^0.2.0", "mixin-deep": "^1.1.3", "paginationator": "^0.1.3", "pascalcase": "^0.1.1", "set-value": "^0.3.3", "template-error": "^0.1.2", "vinyl-item": "^0.1.0", "vinyl-view": "^0.1.2"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}}, "define-property": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "requires": {"is-descriptor": "^0.1.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}, "has-value": {"version": "0.3.1", "resolved": "https://registry.npmjs.org/has-value/-/has-value-0.3.1.tgz", "integrity": "sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=", "requires": {"get-value": "^2.0.3", "has-values": "^0.1.4", "isobject": "^2.0.0"}}, "has-values": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/has-values/-/has-values-0.1.4.tgz", "integrity": "sha1-bWHeldkd/Km5oCCJrThL/49it3E="}, "isobject": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "requires": {"isarray": "1.0.0"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}, "set-value": {"version": "0.3.3", "resolved": "https://registry.npmjs.org/set-value/-/set-value-0.3.3.tgz", "integrity": "sha1-uBIjaBY4oQiP2IpDW4qdMtro2bo=", "requires": {"extend-shallow": "^2.0.1", "isobject": "^2.0.0", "to-object-path": "^0.2.0"}}, "to-object-path": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/to-object-path/-/to-object-path-0.2.0.tgz", "integrity": "sha1-FjThtSqIugDjlJYZ/ACB3Jo7B8o=", "requires": {"arr-flatten": "^1.0.1", "is-arguments": "^1.0.2"}}}}, "terser": {"version": "4.1.2", "resolved": "https://registry.npmjs.org/terser/-/terser-4.1.2.tgz", "integrity": "sha512-jvNoEQSPXJdssFwqPSgWjsOrb+ELoE+ILpHPKXC83tIxOlh2U75F1KuB2luLD/3a6/7K3Vw5pDn+hvu0C4AzSw==", "dev": true, "requires": {"commander": "^2.20.0", "source-map": "~0.6.1", "source-map-support": "~0.5.12"}, "dependencies": {"source-map": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz", "integrity": "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==", "dev": true}}}, "terser-webpack-plugin": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-1.3.0.tgz", "integrity": "sha512-W2YWmxPjjkUcOWa4pBEv4OP4er1aeQJlSo2UhtCFQCuRXEHjOFscO8VyWHj9JLlA0RzQb8Y2/Ta78XZvT54uGg==", "dev": true, "requires": {"cacache": "^11.3.2", "find-cache-dir": "^2.0.0", "is-wsl": "^1.1.0", "loader-utils": "^1.2.3", "schema-utils": "^1.0.0", "serialize-javascript": "^1.7.0", "source-map": "^0.6.1", "terser": "^4.0.0", "webpack-sources": "^1.3.0", "worker-farm": "^1.7.0"}, "dependencies": {"source-map": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz", "integrity": "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==", "dev": true}}}, "text-table": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/text-table/-/text-table-0.2.0.tgz", "integrity": "sha1-f17oI66AUgfACvLfSoTsP8+lcLQ="}, "through": {"version": "2.3.8", "resolved": "https://registry.npmjs.org/through/-/through-2.3.8.tgz", "integrity": "sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU="}, "through2": {"version": "2.0.5", "resolved": "https://registry.npmjs.org/through2/-/through2-2.0.5.tgz", "integrity": "sha512-/mrRod8xqpA+IHSLyGCQ2s8SPHiCDEeQJSep1jqLYeEUClOFG2Qsh+4FU6G9VeqpZnGW/Su8LQGc4YKni5rYSQ==", "requires": {"readable-stream": "~2.3.6", "xtend": "~4.0.1"}}, "through2-filter": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/through2-filter/-/through2-filter-2.0.0.tgz", "integrity": "sha1-YLxVoNrLdghdsfna6Zq0P4PWIuw=", "requires": {"through2": "~2.0.0", "xtend": "~4.0.0"}}, "time-diff": {"version": "0.3.1", "resolved": "https://registry.npmjs.org/time-diff/-/time-diff-0.3.1.tgz", "integrity": "sha1-Jej7c07qnmy15LA5TwWBC5yHwtg=", "requires": {"extend-shallow": "^2.0.1", "is-number": "^2.1.0", "log-utils": "^0.1.0", "pretty-time": "^0.2.0"}, "dependencies": {"ansi-colors": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/ansi-colors/-/ansi-colors-0.1.0.tgz", "integrity": "sha1-M0rDbNPq1wjeXGnhmpjRhkImtD8=", "requires": {"ansi-bgblack": "^0.1.1", "ansi-bgblue": "^0.1.1", "ansi-bgcyan": "^0.1.1", "ansi-bggreen": "^0.1.1", "ansi-bgmagenta": "^0.1.1", "ansi-bgred": "^0.1.1", "ansi-bgwhite": "^0.1.1", "ansi-bgyellow": "^0.1.1", "ansi-black": "^0.1.1", "ansi-blue": "^0.1.1", "ansi-bold": "^0.1.1", "ansi-cyan": "^0.1.1", "ansi-dim": "^0.1.1", "ansi-gray": "^0.1.1", "ansi-green": "^0.1.1", "ansi-grey": "^0.1.1", "ansi-hidden": "^0.1.1", "ansi-inverse": "^0.1.1", "ansi-italic": "^0.1.1", "ansi-magenta": "^0.1.1", "ansi-red": "^0.1.1", "ansi-reset": "^0.1.1", "ansi-strikethrough": "^0.1.1", "ansi-underline": "^0.1.1", "ansi-white": "^0.1.1", "ansi-yellow": "^0.1.1", "lazy-cache": "^0.2.4"}}, "extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}, "is-number": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/is-number/-/is-number-2.1.0.tgz", "integrity": "sha1-Afy7s5NGOlSPL0ZszhbezknbkI8=", "requires": {"kind-of": "^3.0.2"}}, "kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}, "lazy-cache": {"version": "0.2.7", "resolved": "https://registry.npmjs.org/lazy-cache/-/lazy-cache-0.2.7.tgz", "integrity": "sha1-f+3fLctu23fRHvHRF6tf/fCrG2U="}, "log-utils": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/log-utils/-/log-utils-0.1.5.tgz", "integrity": "sha1-3g84+Vf0zW69Xctoddijua4HT3c=", "requires": {"ansi-colors": "^0.1.0", "error-symbol": "^0.1.0", "info-symbol": "^0.1.0", "log-ok": "^0.1.1", "success-symbol": "^0.1.0", "time-stamp": "^1.0.1", "warning-symbol": "^0.1.0"}}}}, "time-stamp": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/time-stamp/-/time-stamp-1.1.0.tgz", "integrity": "sha1-dkpaEa9QVhkhsTPztE5hhofg9cM="}, "timers-browserify": {"version": "2.0.10", "resolved": "https://registry.npmjs.org/timers-browserify/-/timers-browserify-2.0.10.tgz", "integrity": "sha512-YvC1SV1XdOUaL6gx5CoGroT3Gu49pK9+TZ38ErPldOWW4j49GI1HKs9DV+KGq/w6y+LZ72W1c8cKz2vzY+qpzg==", "dev": true, "requires": {"setimmediate": "^1.0.4"}}, "to-absolute-glob": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/to-absolute-glob/-/to-absolute-glob-0.1.1.tgz", "integrity": "sha1-HN+kcqnvUMI57maZm2YsoOs5k38=", "requires": {"extend-shallow": "^2.0.1"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}}}, "to-arraybuffer": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/to-arraybuffer/-/to-arraybuffer-1.0.1.tgz", "integrity": "sha1-fSKbH8xjfkZsoIEYCDanqr/4P0M=", "dev": true}, "to-choices": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/to-choices/-/to-choices-0.2.0.tgz", "integrity": "sha1-IufnWgfWl9fkzsvVaxvwPBVlTXM=", "requires": {"ansi-gray": "^0.1.1", "mixin-deep": "^1.1.3"}}, "to-fast-properties": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/to-fast-properties/-/to-fast-properties-2.0.0.tgz", "integrity": "sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4="}, "to-file": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/to-file/-/to-file-0.2.0.tgz", "integrity": "sha1-I2xsCIBl5XDe+9Fc9LTlZb5G6pM=", "requires": {"define-property": "^0.2.5", "extend-shallow": "^2.0.1", "file-contents": "^0.2.4", "glob-parent": "^2.0.0", "is-valid-glob": "^0.3.0", "isobject": "^2.1.0", "lazy-cache": "^2.0.1", "vinyl": "^1.1.1"}, "dependencies": {"define-property": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "requires": {"is-descriptor": "^0.1.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}, "glob-parent": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/glob-parent/-/glob-parent-2.0.0.tgz", "integrity": "sha1-gTg9ctsFT8zPUzbaqQLxgvbtuyg=", "requires": {"is-glob": "^2.0.0"}}, "is-extglob": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-extglob/-/is-extglob-1.0.0.tgz", "integrity": "sha1-rEaBd8SUNAWgkvyPKXYMb/xiBsA="}, "is-glob": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/is-glob/-/is-glob-2.0.1.tgz", "integrity": "sha1-0Jb5JqPe1WAPP9/ZEZjLCIjC2GM=", "requires": {"is-extglob": "^1.0.0"}}, "isobject": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "requires": {"isarray": "1.0.0"}}}}, "to-object-path": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/to-object-path/-/to-object-path-0.3.0.tgz", "integrity": "sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68=", "requires": {"kind-of": "^3.0.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}}}, "to-regex": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/to-regex/-/to-regex-3.0.2.tgz", "integrity": "sha512-FWtleNAtZ/Ki2qtqej2CXTOayOH9bHDQF+Q48VpWyDXjbYxA4Yz8iDB31zXOBUlOHHKidDbqGVrTUvQMPmBGBw==", "dev": true, "requires": {"define-property": "^2.0.2", "extend-shallow": "^3.0.2", "regex-not": "^1.0.2", "safe-regex": "^1.1.0"}}, "to-regex-range": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/to-regex-range/-/to-regex-range-2.1.1.tgz", "integrity": "sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg=", "dev": true, "requires": {"is-number": "^3.0.0", "repeat-string": "^1.6.1"}}, "trim-leading-lines": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/trim-leading-lines/-/trim-leading-lines-0.1.1.tgz", "integrity": "sha1-DnysPoMELc+Vp07TaWbxd0TVwWk=", "requires": {"is-whitespace": "^0.3.0"}}, "trim-right": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/trim-right/-/trim-right-1.0.1.tgz", "integrity": "sha1-yy4SAwZ+DI3h9hQJS5/kVwTqYAM=", "dev": true}, "tslib": {"version": "1.10.0", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.10.0.tgz", "integrity": "sha512-qOebF53frne81cf0S9B41ByenJ3/IuH8yJKngAX35CmiZySA0khhkovshKK+jGCaMnVomla7gVlIcc3EvKPbTQ==", "dev": true}, "tty-browserify": {"version": "0.0.0", "resolved": "https://registry.npmjs.org/tty-browserify/-/tty-browserify-0.0.0.tgz", "integrity": "sha1-oVe6QC2iTpv5V/mqadUk7tQpAaY=", "dev": true}, "twilio-chat": {"version": "3.3.0", "resolved": "https://registry.npmjs.org/twilio-chat/-/twilio-chat-3.3.0.tgz", "integrity": "sha512-XL97DlW7a/XYvdc0QGgBgqvYUX3OxSUhJoow4F8YrACMB81BaEYtHBpLAdgYSpDR4B+KC0AcVsnGeKgScW4rcA==", "requires": {"iso8601-duration": "^1.2.0", "isomorphic-form-data": "^2.0.0", "loglevel": "^1.6.3", "operation-retrier": "^3.0.0", "platform": "^1.3.5", "rfc6902": "^3.0.2", "twilio-mcs-client": "^0.2.4", "twilio-notifications": "^0.5.7", "twilio-sync": "^0.11.2", "twilsock": "^0.5.10", "uuid": "^3.3.2"}}, "twilio-mcs-client": {"version": "0.2.4", "resolved": "https://registry.npmjs.org/twilio-mcs-client/-/twilio-mcs-client-0.2.4.tgz", "integrity": "sha512-oNB4B3am62syzcsfMPKL/KyXufKmGlschjZvAw+MdTnEEtwYR6EFyq2vF2RawJQQCk2cdLC1OUWQU3FU9dtiwQ==", "requires": {"isomorphic-form-data": "^1.0.0", "loglevel": "^1.6.3", "operation-retrier": "^3.0.0", "twilio-transport": "^0.3.5"}, "dependencies": {"form-data": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/form-data/-/form-data-1.0.1.tgz", "integrity": "sha1-rjFduaSQf6BlUCMEpm13M0de43w=", "requires": {"async": "^2.0.1", "combined-stream": "^1.0.5", "mime-types": "^2.1.11"}}, "isomorphic-form-data": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/isomorphic-form-data/-/isomorphic-form-data-1.0.0.tgz", "integrity": "sha1-BEe+fg9rht7z05MDbPdmSpiRkQA=", "requires": {"form-data": "^1.0.0-rc3"}}}}, "twilio-notifications": {"version": "0.5.7", "resolved": "https://registry.npmjs.org/twilio-notifications/-/twilio-notifications-0.5.7.tgz", "integrity": "sha512-F+a8zJmrMz4PYzXv6EHQOpA73WFWWXQ2ugZVdup4Ck8upDBl+sefGkFOOn+Z7KTNRt3qbpIxHQzrzVVeu7d1kg==", "requires": {"loglevel": "^1.6.3", "operation-retrier": "^3.0.0", "twilsock": "^0.5.10", "uuid": "^3.2.1"}}, "twilio-sync": {"version": "0.11.2", "resolved": "https://registry.npmjs.org/twilio-sync/-/twilio-sync-0.11.2.tgz", "integrity": "sha512-HkiuvmO/9UmFLWaRsPiRvcO0OQN2uA2lDkVl8Rrezl251/31IbNM1IM9L/VVGjhG9Rb2UQfE/K5ilDgQCtGEBg==", "requires": {"loglevel": "^1.6.3", "operation-retrier": "^3.0.0", "platform": "^1.3.5", "twilio-notifications": "^0.5.7", "twilsock": "^0.5.10", "update": "^0.7.4", "uuid": "^3.3.2"}}, "twilio-transport": {"version": "0.3.5", "resolved": "https://registry.npmjs.org/twilio-transport/-/twilio-transport-0.3.5.tgz", "integrity": "sha512-Uug8y+U2im7LbUOpHy3AIUqjsPReLSgMPYOOwkISHT7rU0utkyEWQkssV4gN+FDAGRVCCWW8e43Gl3Bcb1hEFA==", "requires": {"loglevel": "^1.6.3", "xmlhttprequest": "^1.8.0"}}, "twilsock": {"version": "0.5.10", "resolved": "https://registry.npmjs.org/twilsock/-/twilsock-0.5.10.tgz", "integrity": "sha512-F7NqwKU5aXWJmuZPFyBWtoKsU48/i5PCTmaF6isy5Zk9m7brXG6zfKqyggHH0WHHx8xJNJaesHHeixPiPm6wGw==", "requires": {"javascript-state-machine": "^3.0.1", "loglevel": "^1.6.3", "operation-retrier": "^3.0.0", "platform": "^1.3.5", "uuid": "^3.2.1", "ws": "^5.1.0"}}, "typedarray": {"version": "0.0.6", "resolved": "https://registry.npmjs.org/typedarray/-/typedarray-0.0.6.tgz", "integrity": "sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c=", "dev": true}, "ua-parser-js": {"version": "0.7.20", "resolved": "https://registry.npmjs.org/ua-parser-js/-/ua-parser-js-0.7.20.tgz", "integrity": "sha512-8OaIKfzL5cpx8eCMAhhvTlft8GYF8b2eQr6JkCyVdrgjcytyOmPCXrqXFcUnhonRpLlh5yxEZVohm6mzaowUOw=="}, "unc-path-regex": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/unc-path-regex/-/unc-path-regex-0.1.2.tgz", "integrity": "sha1-5z3T17DXxe2G+6xrCufYxqadUPo="}, "unicode-canonical-property-names-ecmascript": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-1.0.4.tgz", "integrity": "sha512-jDrNnXWHd4oHiTZnx/ZG7gtUTVp+gCcTTKr8L0HjlwphROEW3+Him+IpvC+xcJEFegapiMZyZe02CyuOnRmbnQ==", "dev": true}, "unicode-match-property-ecmascript": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/unicode-match-property-ecmascript/-/unicode-match-property-ecmascript-1.0.4.tgz", "integrity": "sha512-L4Qoh15vTfntsn4P1zqnHulG0LdXgjSO035fEpdtp6YxXhMT51Q6vgM5lYdG/5X3MjS+k/Y9Xw4SFCY9IkR0rg==", "dev": true, "requires": {"unicode-canonical-property-names-ecmascript": "^1.0.4", "unicode-property-aliases-ecmascript": "^1.0.4"}}, "unicode-match-property-value-ecmascript": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/unicode-match-property-value-ecmascript/-/unicode-match-property-value-ecmascript-1.1.0.tgz", "integrity": "sha512-hDTHvaBk3RmFzvSl0UVrUmC3PuW9wKVnpoUDYH0JDkSIovzw+J5viQmeYHxVSBptubnr7PbH2e0fnpDRQnQl5g==", "dev": true}, "unicode-property-aliases-ecmascript": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-1.0.5.tgz", "integrity": "sha512-L5RAqCfXqAwR3RriF8pM0lU0w4Ryf/GgzONwi6KnL1taJQa7x1TCxdJnILX59WIGOwR57IVxn7Nej0fz1Ny6fw==", "dev": true}, "union-value": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/union-value/-/union-value-1.0.1.tgz", "integrity": "sha512-tJfXmxMeWYnczCVs7XAEvIV7ieppALdyepWMkHkwciRpZraG/xwT+s2JN8+pr1+8jCRf80FFzvr+MpQeeoF4Xg==", "requires": {"arr-union": "^3.1.0", "get-value": "^2.0.6", "is-extendable": "^0.1.1", "set-value": "^2.0.1"}}, "uniq": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/uniq/-/uniq-1.0.1.tgz", "integrity": "sha1-sxxa6CVIRKOoKBVBzisEuGWnNP8=", "dev": true}, "unique-filename": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/unique-filename/-/unique-filename-1.1.1.tgz", "integrity": "sha512-Vmp0jIp2ln35UTXuryvjzkjGdRyf9b2lTXuSYUiPmzRcl3FDtYqAwOnTJkAngD9SWhnoJzDbTKwaOrZ+STtxNQ==", "dev": true, "requires": {"unique-slug": "^2.0.0"}}, "unique-slug": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/unique-slug/-/unique-slug-2.0.2.tgz", "integrity": "sha512-zoWr9ObaxALD3DOPfjPSqxt4fnZiWblxHIgeWqW8x7UqDzEtHEQLzji2cuJYQFCU6KmoJikOYAZlrTHHebjx2w==", "dev": true, "requires": {"imurmurhash": "^0.1.4"}}, "unique-stream": {"version": "2.3.1", "resolved": "https://registry.npmjs.org/unique-stream/-/unique-stream-2.3.1.tgz", "integrity": "sha512-2nY4TnBE70yoxHkDli7DMazpWiP7xMdCYqU2nBRO0UB+ZpEkGsSija7MvmvnZFUeC+mrgiUfcHSr3LmRFIg4+A==", "requires": {"json-stable-stringify-without-jsonify": "^1.0.1", "through2-filter": "^3.0.0"}, "dependencies": {"through2-filter": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/through2-filter/-/through2-filter-3.0.0.tgz", "integrity": "sha512-jaRjI2WxN3W1V8/FMZ9HKIBXixtiqs3SQSX4/YGIiP3gL6djW48VoZq9tDqeCWs3MT8YY5wb/zli8VW8snY1CA==", "requires": {"through2": "~2.0.0", "xtend": "~4.0.0"}}}}, "unset-value": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/unset-value/-/unset-value-1.0.0.tgz", "integrity": "sha1-g3aHP30jNRef+x5vw6jtDfyKtVk=", "requires": {"has-value": "^0.3.1", "isobject": "^3.0.0"}, "dependencies": {"has-value": {"version": "0.3.1", "resolved": "https://registry.npmjs.org/has-value/-/has-value-0.3.1.tgz", "integrity": "sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=", "requires": {"get-value": "^2.0.3", "has-values": "^0.1.4", "isobject": "^2.0.0"}, "dependencies": {"isobject": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "requires": {"isarray": "1.0.0"}}}}, "has-values": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/has-values/-/has-values-0.1.4.tgz", "integrity": "sha1-bWHeldkd/Km5oCCJrThL/49it3E="}}}, "upath": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/upath/-/upath-1.1.2.tgz", "integrity": "sha512-kXpym8nmDmlCBr7nKdIx8P2jNBa+pBpIUFRnKJ4dr8htyYGJFokkr2ZvERRtUN+9SY+JqXouNgUPtv6JQva/2Q==", "dev": true}, "update": {"version": "0.7.4", "resolved": "https://registry.npmjs.org/update/-/update-0.7.4.tgz", "integrity": "sha1-saCRwRo+KK4xui7vWLcRuCzpixQ=", "requires": {"arr-union": "^3.1.0", "assemble-core": "^0.25.0", "assemble-loader": "^0.6.1", "base-cli-process": "^0.1.18", "base-config-process": "^0.1.9", "base-generators": "^0.4.5", "base-questions": "^0.7.3", "base-runtimes": "^0.2.0", "base-store": "^0.4.4", "common-config": "^0.1.0", "data-store": "^0.16.1", "export-files": "^2.1.1", "extend-shallow": "^2.0.1", "find-pkg": "^0.1.2", "fs-exists-sync": "^0.1.0", "global-modules": "^0.2.2", "gulp-choose-files": "^0.1.3", "is-valid-app": "^0.2.0", "isobject": "^2.1.0", "lazy-cache": "^2.0.1", "log-utils": "^0.2.1", "parser-front-matter": "^1.4.1", "resolve-dir": "^0.1.0", "resolve-file": "^0.2.0", "set-blocking": "^2.0.0", "strip-color": "^0.1.0", "text-table": "^0.2.0", "through2": "^2.0.1", "yargs-parser": "^2.4.1"}, "dependencies": {"camelcase": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/camelcase/-/camelcase-3.0.0.tgz", "integrity": "sha1-MvxLn82vhF/N9+c7uXysImHwqwo="}, "expand-tilde": {"version": "1.2.2", "resolved": "https://registry.npmjs.org/expand-tilde/-/expand-tilde-1.2.2.tgz", "integrity": "sha1-C4HrqJflo9MdHD0QL48BRB5VlEk=", "requires": {"os-homedir": "^1.0.1"}}, "extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}, "global-modules": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/global-modules/-/global-modules-0.2.3.tgz", "integrity": "sha1-6lo77ULG1s6ZWk+KEmm12uIjgo0=", "requires": {"global-prefix": "^0.1.4", "is-windows": "^0.2.0"}}, "global-prefix": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/global-prefix/-/global-prefix-0.1.5.tgz", "integrity": "sha1-jTvGuNo8qBEqFg2NSW/wRiv+948=", "requires": {"homedir-polyfill": "^1.0.0", "ini": "^1.3.4", "is-windows": "^0.2.0", "which": "^1.2.12"}}, "is-windows": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/is-windows/-/is-windows-0.2.0.tgz", "integrity": "sha1-3hqm1j6indJIc3tp8f+LgALSEIw="}, "isobject": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "requires": {"isarray": "1.0.0"}}, "resolve-dir": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/resolve-dir/-/resolve-dir-0.1.1.tgz", "integrity": "sha1-shklmlYC+sXFxJatiUpujMQwJh4=", "requires": {"expand-tilde": "^1.2.2", "global-modules": "^0.2.3"}}, "yargs-parser": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-2.4.1.tgz", "integrity": "sha1-hVaN488VD/SfpRgl8DqMiA3cxcQ=", "requires": {"camelcase": "^3.0.0", "lodash.assign": "^4.0.6"}}}}, "upper-case": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/upper-case/-/upper-case-1.1.3.tgz", "integrity": "sha1-9rRQHC7EzdJrp4vnIilh3ndiFZg="}, "uri-js": {"version": "4.2.2", "resolved": "https://registry.npmjs.org/uri-js/-/uri-js-4.2.2.tgz", "integrity": "sha512-KY9Frmirql91X2Qgjry0Wd4Y+YTdrdZheS8TFwvkbLWf/G5KNJDCh6pKL5OZctEW4+0Baa5idK2ZQuELRwPznQ==", "dev": true, "requires": {"punycode": "^2.1.0"}}, "urix": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/urix/-/urix-0.1.0.tgz", "integrity": "sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI=", "dev": true}, "url": {"version": "0.11.0", "resolved": "https://registry.npmjs.org/url/-/url-0.11.0.tgz", "integrity": "sha1-ODjpfPxgUh63PFJajlW/3Z4uKPE=", "dev": true, "requires": {"punycode": "1.3.2", "querystring": "0.2.0"}, "dependencies": {"punycode": {"version": "1.3.2", "resolved": "https://registry.npmjs.org/punycode/-/punycode-1.3.2.tgz", "integrity": "sha1-llOgNvt8HuQjQvIyXM7v6jkmxI0=", "dev": true}}}, "use": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/use/-/use-3.1.1.tgz", "integrity": "sha512-cwESVXlO3url9YWlFW/TA9cshCEhtu7IKJ/p5soJ/gGpj7vbvFrAY/eIioQ6Dw23KjZhYgiIo8HOs1nQ2vr/oQ==", "dev": true}, "util": {"version": "0.11.1", "resolved": "https://registry.npmjs.org/util/-/util-0.11.1.tgz", "integrity": "sha512-HShAsny+zS2TZfaXxD9tYj4HQGlBezXZMZuM/S5PKLLoZkShZiGk9o5CzukI1LVHZvjdvZ2Sj1aW/Ndn2NB/HQ==", "dev": true, "requires": {"inherits": "2.0.3"}, "dependencies": {"inherits": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/inherits/-/inherits-2.0.3.tgz", "integrity": "sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=", "dev": true}}}, "util-deprecate": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz", "integrity": "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8="}, "uuid": {"version": "3.3.2", "resolved": "https://registry.npmjs.org/uuid/-/uuid-3.3.2.tgz", "integrity": "sha512-yXJmeNaw3DnnKAOKJE51sL/ZaYfWJRl1pK9dr19YFCu0ObS231AB1/LbqTKRAQ5kw8A90rA6fr4riOUpTZvQZA=="}, "v8-compile-cache": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/v8-compile-cache/-/v8-compile-cache-2.0.3.tgz", "integrity": "sha512-CNmdbwQMBjwr9Gsmohvm0pbL954tJrNzf6gWL3K+QMQf00PF7ERGrEiLgjuU3mKreLC2MeGhUsNV9ybTbLgd3w==", "dev": true}, "vali-date": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/vali-date/-/vali-date-1.0.0.tgz", "integrity": "sha1-G5BKWWCfsyjvB4E4Qgk09rhnCaY="}, "vinyl": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/vinyl/-/vinyl-1.2.0.tgz", "integrity": "sha1-XIgDbPVl5d8FVYv8kR+GVt8hiIQ=", "requires": {"clone": "^1.0.0", "clone-stats": "^0.0.1", "replace-ext": "0.0.1"}}, "vinyl-fs": {"version": "2.4.4", "resolved": "https://registry.npmjs.org/vinyl-fs/-/vinyl-fs-2.4.4.tgz", "integrity": "sha1-vm/zJwy1Xf19MGNkDegfJddTIjk=", "requires": {"duplexify": "^3.2.0", "glob-stream": "^5.3.2", "graceful-fs": "^4.0.0", "gulp-sourcemaps": "1.6.0", "is-valid-glob": "^0.3.0", "lazystream": "^1.0.0", "lodash.isequal": "^4.0.0", "merge-stream": "^1.0.0", "mkdirp": "^0.5.0", "object-assign": "^4.0.0", "readable-stream": "^2.0.4", "strip-bom": "^2.0.0", "strip-bom-stream": "^1.0.0", "through2": "^2.0.0", "through2-filter": "^2.0.0", "vali-date": "^1.0.0", "vinyl": "^1.0.0"}}, "vinyl-item": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/vinyl-item/-/vinyl-item-0.1.0.tgz", "integrity": "sha1-8ngTyBFC66ScpYSd5PQvb6Dl4Jg=", "requires": {"base": "^0.8.1", "base-option": "^0.8.2", "base-plugins": "^0.4.12", "clone": "^1.0.2", "clone-stats": "^1.0.0", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "isobject": "^2.1.0", "lazy-cache": "^2.0.1", "vinyl": "^1.1.1"}, "dependencies": {"base": {"version": "0.8.1", "resolved": "https://registry.npmjs.org/base/-/base-0.8.1.tgz", "integrity": "sha1-aQC7MA8sdZbJnz2DurhyLYGLdI8=", "requires": {"arr-union": "^3.1.0", "cache-base": "^0.8.2", "class-utils": "^0.3.2", "component-emitter": "^1.2.0", "debug": "^2.2.0", "define-property": "^0.2.5", "lazy-cache": "^1.0.3", "mixin-deep": "^1.1.3"}, "dependencies": {"lazy-cache": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/lazy-cache/-/lazy-cache-1.0.4.tgz", "integrity": "sha1-odePw6UEdMuAhF07O24dpJpEbo4="}}}, "cache-base": {"version": "0.8.5", "resolved": "https://registry.npmjs.org/cache-base/-/cache-base-0.8.5.tgz", "integrity": "sha1-YM6zUEAh7O7HAR/TOEt/TpVym/o=", "requires": {"collection-visit": "^0.2.1", "component-emitter": "^1.2.1", "get-value": "^2.0.5", "has-value": "^0.3.1", "isobject": "^3.0.0", "lazy-cache": "^2.0.1", "set-value": "^0.4.2", "to-object-path": "^0.3.0", "union-value": "^0.2.3", "unset-value": "^0.1.1"}, "dependencies": {"isobject": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/isobject/-/isobject-3.0.1.tgz", "integrity": "sha1-TkMekrEalzFjaqH5yNHMvP2reN8="}}}, "clone-stats": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/clone-stats/-/clone-stats-1.0.0.tgz", "integrity": "sha1-s3gt/4u1R04Yuba/D9/ngvh3doA="}, "collection-visit": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/collection-visit/-/collection-visit-0.2.3.tgz", "integrity": "sha1-L2JIPK7MlfCDuaRUo+6eYTmteVc=", "requires": {"lazy-cache": "^2.0.1", "map-visit": "^0.1.5", "object-visit": "^0.3.4"}}, "debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}}, "define-property": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "requires": {"is-descriptor": "^0.1.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}, "has-value": {"version": "0.3.1", "resolved": "https://registry.npmjs.org/has-value/-/has-value-0.3.1.tgz", "integrity": "sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=", "requires": {"get-value": "^2.0.3", "has-values": "^0.1.4", "isobject": "^2.0.0"}}, "has-values": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/has-values/-/has-values-0.1.4.tgz", "integrity": "sha1-bWHeldkd/Km5oCCJrThL/49it3E="}, "isobject": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "requires": {"isarray": "1.0.0"}}, "map-visit": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/map-visit/-/map-visit-0.1.5.tgz", "integrity": "sha1-2+Q5J85VJbgN/BVzpE1oxR8mgWs=", "requires": {"lazy-cache": "^2.0.1", "object-visit": "^0.3.4"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}, "object-visit": {"version": "0.3.4", "resolved": "https://registry.npmjs.org/object-visit/-/object-visit-0.3.4.tgz", "integrity": "sha1-rhXPhvCy/dVRdxY2RIRSxUw9qCk=", "requires": {"isobject": "^2.0.0"}}, "set-value": {"version": "0.4.3", "resolved": "https://registry.npmjs.org/set-value/-/set-value-0.4.3.tgz", "integrity": "sha1-fbCPnT0i3H945Trzw79GZuzfzPE=", "requires": {"extend-shallow": "^2.0.1", "is-extendable": "^0.1.1", "is-plain-object": "^2.0.1", "to-object-path": "^0.3.0"}}, "union-value": {"version": "0.2.4", "resolved": "https://registry.npmjs.org/union-value/-/union-value-0.2.4.tgz", "integrity": "sha1-c3UVJ4ZnkFfns3qmdug0aPwCdPA=", "requires": {"arr-union": "^3.1.0", "get-value": "^2.0.6", "is-extendable": "^0.1.1", "set-value": "^0.4.3"}}, "unset-value": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/unset-value/-/unset-value-0.1.2.tgz", "integrity": "sha1-UGgQuGfyfCpabpsEgzYx9t5Y0xA=", "requires": {"has-value": "^0.3.1", "isobject": "^3.0.0"}, "dependencies": {"isobject": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/isobject/-/isobject-3.0.1.tgz", "integrity": "sha1-TkMekrEalzFjaqH5yNHMvP2reN8="}}}}}, "vinyl-view": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/vinyl-view/-/vinyl-view-0.1.2.tgz", "integrity": "sha1-CaxtfIASEr8JJr2dQQb0XmxPyXc=", "requires": {"arr-union": "^3.1.0", "define-property": "^0.2.5", "engine-base": "^0.1.2", "isobject": "^2.1.0", "lazy-cache": "^2.0.1", "mixin-deep": "^1.1.3", "vinyl-item": "^0.1.0"}, "dependencies": {"define-property": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "requires": {"is-descriptor": "^0.1.0"}}, "isobject": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "requires": {"isarray": "1.0.0"}}}}, "vm-browserify": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/vm-browserify/-/vm-browserify-1.1.0.tgz", "integrity": "sha512-iq+S7vZJE60yejDYM0ek6zg308+UZsdtPExWP9VZoCFCz1zkJoXFnAX7aZfd/ZwrkidzdUZL0C/ryW+JwAiIGw==", "dev": true}, "warning-symbol": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/warning-symbol/-/warning-symbol-0.1.0.tgz", "integrity": "sha1-uzHdEbeg+dZ6su2V9Fe2WCW7rSE="}, "watchpack": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/watchpack/-/watchpack-1.6.0.tgz", "integrity": "sha512-i6dHe3EyLjMmDlU1/bGQpEw25XSjkJULPuAVKCbNRefQVq48yXKUpwg538F7AZTf9kyr57zj++pQFltUa5H7yA==", "dev": true, "requires": {"chokidar": "^2.0.2", "graceful-fs": "^4.1.2", "neo-async": "^2.5.0"}}, "webpack": {"version": "4.35.0", "resolved": "https://registry.npmjs.org/webpack/-/webpack-4.35.0.tgz", "integrity": "sha512-M5hL3qpVvtr8d4YaJANbAQBc4uT01G33eDpl/psRTBCfjxFTihdhin1NtAKB1ruDwzeVdcsHHV3NX+QsAgOosw==", "dev": true, "requires": {"@webassemblyjs/ast": "1.8.5", "@webassemblyjs/helper-module-context": "1.8.5", "@webassemblyjs/wasm-edit": "1.8.5", "@webassemblyjs/wasm-parser": "1.8.5", "acorn": "^6.0.5", "acorn-dynamic-import": "^4.0.0", "ajv": "^6.1.0", "ajv-keywords": "^3.1.0", "chrome-trace-event": "^1.0.0", "enhanced-resolve": "^4.1.0", "eslint-scope": "^4.0.0", "json-parse-better-errors": "^1.0.2", "loader-runner": "^2.3.0", "loader-utils": "^1.1.0", "memory-fs": "~0.4.1", "micromatch": "^3.1.8", "mkdirp": "~0.5.0", "neo-async": "^2.5.0", "node-libs-browser": "^2.0.0", "schema-utils": "^1.0.0", "tapable": "^1.1.0", "terser-webpack-plugin": "^1.1.0", "watchpack": "^1.5.0", "webpack-sources": "^1.3.0"}}, "webpack-cli": {"version": "3.3.5", "resolved": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-3.3.5.tgz", "integrity": "sha512-w0j/s42c5UhchwTmV/45MLQnTVwRoaUTu9fM5LuyOd/8lFoCNCELDogFoecx5NzRUndO0yD/gF2b02XKMnmAWQ==", "dev": true, "requires": {"chalk": "2.4.2", "cross-spawn": "6.0.5", "enhanced-resolve": "4.1.0", "findup-sync": "3.0.0", "global-modules": "2.0.0", "import-local": "2.0.0", "interpret": "1.2.0", "loader-utils": "1.2.3", "supports-color": "6.1.0", "v8-compile-cache": "2.0.3", "yargs": "13.2.4"}, "dependencies": {"supports-color": {"version": "6.1.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-6.1.0.tgz", "integrity": "sha512-qe1jfm1Mg7Nq/NSh6XE24gPXROEVsWHxC1LIx//XNlD9iw7YZQGjZNjYN7xGaEG6iKdA8EtNFW6R0gjnVXp+wQ==", "dev": true, "requires": {"has-flag": "^3.0.0"}}}}, "webpack-merge": {"version": "4.2.1", "resolved": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-4.2.1.tgz", "integrity": "sha512-4p8WQyS98bUJcCvFMbdGZyZmsKuWjWVnVHnAS3FFg0HDaRVrPbkivx2RYCre8UiemD67RsiFFLfn4JhLAin8Vw==", "dev": true, "requires": {"lodash": "^4.17.5"}}, "webpack-sources": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-1.3.0.tgz", "integrity": "sha512-OiVgSrbGu7NEnEvQJJgdSFPl2qWKkWq5lHMhgiToIiN9w34EBnjYzSYs+VbL5KoYiLNtFFa7BZIKxRED3I32pA==", "dev": true, "requires": {"source-list-map": "^2.0.0", "source-map": "~0.6.1"}, "dependencies": {"source-map": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz", "integrity": "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==", "dev": true}}}, "whatwg-fetch": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/whatwg-fetch/-/whatwg-fetch-3.0.0.tgz", "integrity": "sha512-9GSJUgz1D4MfyKU7KRqwOjXCXTqWdFNvEr7eUBYchQiVc744mqK/MzXPNR2WsPkmkOa4ywfg8C2n8h+13Bey1Q=="}, "which": {"version": "1.3.1", "resolved": "https://registry.npmjs.org/which/-/which-1.3.1.tgz", "integrity": "sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ==", "requires": {"isexe": "^2.0.0"}}, "which-module": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/which-module/-/which-module-2.0.0.tgz", "integrity": "sha1-2e8H3Od7mQK4o6j6SzHD4/fm6Ho=", "dev": true}, "worker-farm": {"version": "1.7.0", "resolved": "https://registry.npmjs.org/worker-farm/-/worker-farm-1.7.0.tgz", "integrity": "sha512-rvw3QTZc8lAxyVrqcSGVm5yP/IJ2UcB3U0graE3LCFoZ0Yn2x4EoVSqJKdB/T5M+FLcRPjz4TDacRf3OCfNUzw==", "dev": true, "requires": {"errno": "~0.1.7"}}, "wrap-ansi": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-5.1.0.tgz", "integrity": "sha512-QC1/iN/2/RPVJ5jYK8BGttj5z83LmSKmvbvrXPNCLZSEb32KKVDJDl/MOt2N01qU2H/FkzEa9PKto1BqDjtd7Q==", "dev": true, "requires": {"ansi-styles": "^3.2.0", "string-width": "^3.0.0", "strip-ansi": "^5.0.0"}}, "wrappy": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz", "integrity": "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8="}, "write": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/write/-/write-0.2.1.tgz", "integrity": "sha1-X8A4KOJkzqP+kUVUdvejxWbLB1c=", "requires": {"mkdirp": "^0.5.1"}}, "write-json": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/write-json/-/write-json-0.2.2.tgz", "integrity": "sha1-+k4VKennY6T5LwfZhBMX49JI2vM=", "requires": {"write": "^0.2.1"}}, "ws": {"version": "5.2.2", "resolved": "https://registry.npmjs.org/ws/-/ws-5.2.2.tgz", "integrity": "sha512-jaHFD6PFv6UgoIVda6qZllptQsMlDEJkTQcybzzXDYM1XO9Y8em691FGMPmM46WGyLU4z9KMgQN+qrux/nhlHA==", "requires": {"async-limiter": "~1.0.0"}}, "xmlhttprequest": {"version": "1.8.0", "resolved": "https://registry.npmjs.org/xmlhttprequest/-/xmlhttprequest-1.8.0.tgz", "integrity": "sha1-Z/4HXFwk/vOfnWX197f+dRcZaPw="}, "xtend": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/xtend/-/xtend-4.0.1.tgz", "integrity": "sha1-pcbVMr5lbiPbgg77lDofBJmNY68="}, "y18n": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/y18n/-/y18n-4.0.0.tgz", "integrity": "sha512-r9S/ZyXu/Xu9q1tYlpsLIsa3EeLXXk0VwlxqTcFRfg9EhMW+17kbt9G0NrgCmhGb5vT2hyhJZLfDGx+7+5Uj/w==", "dev": true}, "yallist": {"version": "3.0.3", "resolved": "https://registry.npmjs.org/yallist/-/yallist-3.0.3.tgz", "integrity": "sha512-S+Zk8DEWE6oKpV+vI3qWkaK+jSbIK86pCwe2IF/xwIpQ8jEuxpw9NyaGjmp9+BoJv5FV2piqCDcoCtStppiq2A==", "dev": true}, "yargs": {"version": "13.2.4", "resolved": "https://registry.npmjs.org/yargs/-/yargs-13.2.4.tgz", "integrity": "sha512-HG/DWAJa1PAnHT9JAhNa8AbAv3FPaiLzioSjCcmuXXhP8MlpHO5vwls4g4j6n30Z74GVQj8Xa62dWVx1QCGklg==", "dev": true, "requires": {"cliui": "^5.0.0", "find-up": "^3.0.0", "get-caller-file": "^2.0.1", "os-locale": "^3.1.0", "require-directory": "^2.1.1", "require-main-filename": "^2.0.0", "set-blocking": "^2.0.0", "string-width": "^3.0.0", "which-module": "^2.0.0", "y18n": "^4.0.0", "yargs-parser": "^13.1.0"}}, "yargs-parser": {"version": "13.1.1", "resolved": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-13.1.1.tgz", "integrity": "sha512-oVAVsHz6uFrg3XQheFII8ESO2ssAf9luWuAd6Wexsu4F3OtIW0o8IribPXYrD4WC24LWtPrJlGy87y5udK+dxQ==", "dev": true, "requires": {"camelcase": "^5.0.0", "decamelize": "^1.2.0"}}}}